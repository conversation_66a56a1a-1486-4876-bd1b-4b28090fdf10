package com.huitu.cloud.api.datamanager.rule.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 雨量规则配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-06
 */
@TableName("BSN_RAINRULE_B")
@ApiModel(value="BsnRainruleB对象", description="雨量规则配置表")
public class BsnRainruleb extends Model<BsnRainruleb> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "站码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "分钟数")
    @TableField("INV")
    private Integer inv;

    @ApiModelProperty(value = "瞬时阈值")
    @TableField("SNV")
    private Double snv;

    @ApiModelProperty(value = "5分钟数据量阈值")
    @TableField("CTPM5")
    private Integer ctpm5;

    @ApiModelProperty(value = "备注")
    @TableField("NT")
    private String nt;

    @ApiModelProperty(value = "时间戳 自动获取无需传值")
    @TableField("MODITIME")
    private Date moditime;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }


    public Integer getInv() {
        return inv;
    }

    public void setInv(Integer inv) {
        this.inv = inv;
    }

    public Double getSnv() {
        return snv;
    }

    public void setSnv(Double snv) {
        this.snv = snv;
    }

    public Integer getCtpm5() {
        return ctpm5;
    }

    public void setCtpm5(Integer ctpm5) {
        this.ctpm5 = ctpm5;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }

    public Date getModitime() {
        return moditime;
    }

    public void setModitime(Date moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "BsnRainruleB{" +
        "stcd=" + stcd +
        ", mncnt=" + inv +
        ", snv=" + snv +
        ", ctpm=" + ctpm5 +
        ", nt=" + nt +
        ", moditime=" + moditime +
        "}";
    }
}
