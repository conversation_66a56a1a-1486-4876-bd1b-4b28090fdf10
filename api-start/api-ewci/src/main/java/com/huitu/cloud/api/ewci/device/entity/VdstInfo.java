package com.huitu.cloud.api.ewci.device.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.api.ewci.device.util.TypeConverter;
import com.huitu.cloud.validation.constraints.StringLength;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 视频监测站
 *
 * <AUTHOR>
 */

@TableName("BSN_VDSTINFO_B")
@ApiModel(value = "视频监测站-设备信息维护")
@Data
public class VdstInfo implements Serializable {

    @ExcelProperty("序号") // 让它出现在 Excel 里
    @TableField(exist = false)
    private Integer serialNumber;

    @ExcelIgnore
    @ApiModelProperty(value = "视频站编码")
    @TableField(value = "VDSTCD")
    private String vdstcd;

    @ExcelProperty(value = "视频站名称")
    @ApiModelProperty(value = "视频站名称")
    @TableField(value = "VDSTNM")
    @StringLength(max = 50, message = "参数[视频站名称]的长度不能超过50个字符")
    private String vdstnm;

    @ExcelProperty(value = "政区")
    @ApiModelProperty(value = "政区名称")
    @TableField(exist = false)
    private String adnm;

    @ExcelProperty(value = "类别", converter = TypeConverter.class)
    @ApiModelProperty(value = "类型(2-主要江河、3-山洪沟、4-防洪城市、5-中小河流、11-大型水库、12-中型水库、13-小型水库)")
    @TableField(value = "VDSTTP")
    @StringLength(max = 2, message = "参数[类别]的长度不能超过2个字符")
    private String vdsttp;

    @ExcelProperty(value = "管理单位")
    @ApiModelProperty(value = "管理单位")
    @TableField(value = "ADMAUTH")
    private String admauth;

    @ExcelProperty(value = "站址")
    @ApiModelProperty(value = "站址")
    @TableField(value = "ADDRESS")
    @StringLength(max = 100, message = "参数[站址]的长度不能超过100个字符")
    private String address;

    @ExcelProperty(value = "建站时间")
    @ApiModelProperty(value = "建站时间")
    @TableField(value = "ESTABLISHMENTTIME")
    private LocalDateTime establishmenttime;

    @ExcelProperty(value = {"主要设备品牌", "智能球机"})
    @ApiModelProperty(value = "智能球机品牌")
    @TableField(value = "QJBD")
    @StringLength(max = 50, message = "参数[智能球机品牌]的长度不能超过50个字符")
    private String qjbd;

    @ExcelIgnore
    @ApiModelProperty(value = "充电控制器品牌")
    @TableField(value = "CHARGEBD")
    @StringLength(max = 50, message = "参数[充电控制器品牌]的长度不能超过50个字符")
    private String chargebd;

    @ExcelProperty(value = {"最近一次改造或重建时间", "智能球机"})
    @ApiModelProperty(value = "智能球机改造年份")
    @TableField(value = "QJAR")
    @StringLength(max = 6, message = "参数[智能球机改造年份]的长度不能超过6个字符")
    private String qjar;

    @ExcelProperty(value = {"最近一次改造或重建时间", "蓄电池"})
    @ApiModelProperty(value = "蓄电池改造年份")
    @TableField(value = "DCAR")
    @StringLength(max = 6, message = "参数[蓄电池改造年份]的长度不能超过6个字符")
    private String dcar;

    @ExcelProperty(value = {"最近一次改造或重建时间", "太阳能板"})
    @ApiModelProperty(value = "太阳能板改造年份")
    @TableField(value = "SOLARENERGYAR")
    @StringLength(max = 6, message = "参数[太阳能板改造年份]的长度不能超过6个字符")
    private String solarenergyar;

    @ExcelProperty(value = {"最近一次改造或重建时间", "路由器"})
    @ApiModelProperty(value = "路由器改造年份")
    @TableField(value = "ROUTERAR")
    @StringLength(max = 6, message = "参数[路由器改造年份]的长度不能超过6个字符")
    private String routerar;

    @ExcelProperty(value = {"最近一次改造或重建时间", "充电控制器"})
    @ApiModelProperty(value = "充电控制器改造年份")
    @TableField(value = "CHARGEAR")
    @StringLength(max = 6, message = "参数[充电控制器改造年份]的长度不能超过6个字符")
    private String chargear;

    @ExcelProperty(value = {"最近一次改造或重建时间", "信号避雷器"})
    @ApiModelProperty(value = "信号避雷器改造年份")
    @TableField(value = "SPDAR")
    @StringLength(max = 6, message = "参数[信号避雷器改造年份]的长度不能超过6个字符")
    private String spdar;

    @ExcelProperty(value = {"最近一次改造或重建时间", "室外设备防雨箱"})
    @ApiModelProperty(value = "室外设备防雨箱改造年份")
    @TableField(value = "RAINPROOFAR")
    @StringLength(max = 6, message = "参数[室外设备防雨箱改造年份]的长度不能超过6个字符")
    private String rainproofar;

    @ExcelProperty(value = {"最近一次改造或重建时间", "监控立杆"})
    @ApiModelProperty(value = "监控立杆改造年份")
    @TableField(value = "VERTICALPOLEAR")
    @StringLength(max = 6, message = "参数[监控立杆改造年份]的长度不能超过6个字符")
    private String verticalpolear;

    @ExcelProperty(value = {"最近一次改造或重建时间", "基础及避雷系统"})
    @ApiModelProperty(value = "基础及避雷系统改造年份")
    @TableField(value = "BASICRENOVATIONAR")
    @StringLength(max = 6, message = "参数[基础及避雷系统改造年份]的长度不能超过6个字符")
    private String basicrenovationar;

    @ExcelProperty(value = {"最近一次改造或重建时间", "数据填写时间"})
    @ApiModelProperty(value = "时间戳")
    @TableField(value = "MODITIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime moditime;

    @ExcelIgnore
    @ApiModelProperty(value = "经度")
    @TableField(value = "LGTD")
    private BigDecimal lgtd;

    @ExcelIgnore
    @ApiModelProperty(value = "纬度")
    @TableField(value = "LTTD")
    private BigDecimal lttd;

    @ExcelIgnore
    @ApiModelProperty(value = "服务商名称")
    @TableField(value = "SERVICENM")
    private String servicenm;

    @ExcelIgnore
    @ApiModelProperty(value = "关联测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ExcelIgnore
    @ApiModelProperty(value = "行政区划码")
    @TableField(value = "ADCD")
    @NotBlank(message = "参数[行政区划代码]不能为空")
    @Size(min = 0, max = 15, message = "参数[行政区划代码]的长度不能超过15个字符")
    private String adcd;

    @ExcelIgnore
    @ApiModelProperty(value = "视频IP地址")
    @TableField(value = "VDIP")
    private String vdip;

    @ExcelIgnore
    @ApiModelProperty(value = "视频端口")
    @TableField(value = "VDPORT")
    private String vdport;

    @ExcelIgnore
    @ApiModelProperty(value = "视频用户名")
    @TableField(value = "VDUSER")
    private String vduser;

    @ExcelIgnore
    @ApiModelProperty(value = "视频密码")
    @TableField(value = "VDPWD")
    private String vdpwd;

    @ExcelIgnore
    @ApiModelProperty(value = "测站管理员")
    @TableField(value = "VDMGER")
    private String vdmger;

    @ExcelIgnore
    @ApiModelProperty(value = "管理员电话")
    @TableField(value = "VDMTEL")
    private String vdmtel;

    @ExcelIgnore
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UDTM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime udtm;


    @ExcelIgnore
    @ApiModelProperty(value = "备注")
    @TableField(value = "NT")
    private String nt;

    @ExcelIgnore
    @ApiModelProperty(value = "工程编码")
    @TableField(value = "ENNMCD")
    private String ennmcd;

    @ExcelIgnore
    @ApiModelProperty(value = "连接类型")
    @TableField(value = "LKTP")
    private String lktp;

    @ExcelIgnore
    @ApiModelProperty(value = "启用标志：1=正常，2=废弃")
    @TableField(value = "USFL")
    private String usfl;

    @ExcelIgnore
    @ApiModelProperty(value = "来源ID")
    @TableField(value = "SOURCEID")
    private String sourceid;

    @ExcelIgnore
    @ApiModelProperty(value = "提交人")
    @TableField(value = "CREATOR")
    private String creator;

    @ExcelIgnore
    @ApiModelProperty(value = "提交时间")
    @TableField(value = "CREATED")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime created;

    @ExcelIgnore
    @ApiModelProperty(value = "审核时间")
    @TableField(value = "AUDITED")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime audited;

    @ExcelIgnore
    @ApiModelProperty(value = "审核人")
    @TableField(value = "AUDITOR")
    private Integer auditor;

    @ExcelIgnore
    @ApiModelProperty(value = "状态")
    @TableField(exist = false)
    private String status;

    @ExcelIgnore
    @ApiModelProperty(value = "审核不通过原因")
    @TableField(exist = false)
    private String audreason;

}
