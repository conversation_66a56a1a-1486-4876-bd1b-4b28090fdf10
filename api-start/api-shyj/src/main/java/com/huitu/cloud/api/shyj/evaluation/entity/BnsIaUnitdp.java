package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 计算单元(防灾对象)信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("BNS_IA_UNITDP")
@ApiModel(value="BnsIaUnitdp对象", description="计算单元(防灾对象)信息表")
public class BnsIaUnitdp extends Model<BnsIaUnitdp> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableId(value = "ADCD", type = IdType.NONE)
    private String adcd;

    @ApiModelProperty(value = "集雨面积(k㎡)")
    @TableField("WAT_SHED_AREA")
    private Double watShedArea;

    @ApiModelProperty(value = "断面代码")
    @TableField("HSCD")
    private String hscd;

    @ApiModelProperty(value = "比降")
    @TableField("SLP")
    private Double slp;

    @ApiModelProperty(value = "糙率-左岸")
    @TableField("AVEROUL")
    private Double averoul;

    @ApiModelProperty(value = "糙率-主槽")
    @TableField("AVEROUC")
    private Double averouc;

    @ApiModelProperty(value = "糙率-右岸")
    @TableField("AVEROUR")
    private Double averour;

    @ApiModelProperty(value = "成灾水位(m)")
    @TableField("CZZ")
    private Double czz;

    @ApiModelProperty(value = "预警时段(h)")
    @TableField("STDT")
    private String stdt;

    @ApiModelProperty(value = "流域土壤含水量(Wm)")
    @TableField("LWATER")
    private String lwater;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("TS")
    private LocalDateTime ts;
    @ApiModelProperty(value = "政区名称")
    private String adnm;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getWatShedArea() {
        return watShedArea;
    }

    public void setWatShedArea(Double watShedArea) {
        this.watShedArea = watShedArea;
    }

    public String getHscd() {
        return hscd;
    }

    public void setHscd(String hscd) {
        this.hscd = hscd;
    }

    public Double getSlp() {
        return slp;
    }

    public void setSlp(Double slp) {
        this.slp = slp;
    }

    public Double getAveroul() {
        return averoul;
    }

    public void setAveroul(Double averoul) {
        this.averoul = averoul;
    }

    public Double getAverouc() {
        return averouc;
    }

    public void setAverouc(Double averouc) {
        this.averouc = averouc;
    }

    public Double getAverour() {
        return averour;
    }

    public void setAverour(Double averour) {
        this.averour = averour;
    }

    public Double getCzz() {
        return czz;
    }

    public void setCzz(Double czz) {
        this.czz = czz;
    }

    public String getStdt() {
        return stdt;
    }

    public void setStdt(String stdt) {
        this.stdt = stdt;
    }

    public String getLwater() {
        return lwater;
    }

    public void setLwater(String lwater) {
        this.lwater = lwater;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getTs() {
        return ts;
    }

    public void setTs(LocalDateTime ts) {
        this.ts = ts;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    @Override
    protected Serializable pkVal() {
        return this.adcd;
    }

    @Override
    public String toString() {
        return "BnsIaUnitdp{" +
                "adcd='" + adcd + '\'' +
                ", watShedArea=" + watShedArea +
                ", hscd='" + hscd + '\'' +
                ", slp=" + slp +
                ", averoul=" + averoul +
                ", averouc=" + averouc +
                ", averour=" + averour +
                ", czz=" + czz +
                ", stdt='" + stdt + '\'' +
                ", lwater='" + lwater + '\'' +
                ", remark='" + remark + '\'' +
                ", ts=" + ts +
                ", adnm='" + adnm + '\'' +
                '}';
    }
}
