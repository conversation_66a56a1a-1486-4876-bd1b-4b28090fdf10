package com.huitu.cloud.api.datamanager.ststop.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

public class StstopVo implements Serializable {
    @ApiModelProperty(value = "测站编码")
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "测站基础类型名称")
    private String stadtpnm;

    @ApiModelProperty(value = "县级政区名称")
    private String xadnm;

    @ApiModelProperty(value = "测站联系人")
    private String  officer;

    @ApiModelProperty(value = "移动电话号码")
    private String  mphone;

    @ApiModelProperty(value = "站点屏蔽类型  1：计划型 0：非计划型")
    private String ststtp;
    @ApiModelProperty(value = "用户")
    private String usernm;
    @ApiModelProperty(value = "屏蔽开始时间 yyyy-MM-dd HH:mm:ss")
    private Date stm;
    @ApiModelProperty(value = "屏蔽结束时间 yyyy-MM-dd HH:mm:ss")
    private Date etm;

    @ApiModelProperty(value = "状态：0停用，1启用")
    @TableField("STSTA")
    private String ststa;

    @ApiModelProperty(value = "屏蔽要素 雨量：st_pptn_r , 河道水位：st_river_r, 水库水位：st_rsvr_r")
    private String tbnm;

    @ApiModelProperty(value = "是否显示")
    @TableField("IS_SHOW")
    private String isShow;

    @ApiModelProperty(value = "理由")
    @TableField("REASON")
    private String reason;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getOfficer() {
        return officer;
    }

    public void setOfficer(String officer) {
        this.officer = officer;
    }

    public String getMphone() {
        return mphone;
    }

    public void setMphone(String mphone) {
        this.mphone = mphone;
    }

    public String getStsttp() {
        return ststtp;
    }

    public void setStsttp(String ststtp) {
        this.ststtp = ststtp;
    }

    public String getUsernm() {
        return usernm;
    }

    public void setUsernm(String usernm) {
        this.usernm = usernm;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public String getStsta() {
        return ststa;
    }

    public void setStsta(String ststa) {
        this.ststa = ststa;
    }

    public String getTbnm() {
        return tbnm;
    }

    public void setTbnm(String tbnm) {
        this.tbnm = tbnm;
    }

    public String getIsShow() {
        return isShow;
    }

    public void setIsShow(String isShow) {
        this.isShow = isShow;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
