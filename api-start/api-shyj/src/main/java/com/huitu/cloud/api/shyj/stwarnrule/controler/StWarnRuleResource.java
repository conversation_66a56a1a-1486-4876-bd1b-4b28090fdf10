package com.huitu.cloud.api.shyj.stwarnrule.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;

import com.huitu.cloud.api.shyj.stwarnrule.entity.QuetyStWarnRule;
import com.huitu.cloud.api.shyj.stwarnrule.entity.RainWarnRule;
import com.huitu.cloud.api.shyj.stwarnrule.entity.SwWarnRule;
import com.huitu.cloud.api.shyj.stwarnrule.service.StWarnRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 测站预警规则表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-27
 */

@RestController
@Api(tags = "山洪普查预警指标查询")
@RequestMapping("/api/shyj/stwarnrule")
public class StWarnRuleResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "afb1cda5-4f7d-4791-b6e2-603019953a1b";
    }
    @Override
    public String getVersion() {
        return "1.0";
    }
    @Autowired
    private StWarnRuleService stWarnRuleService;


    @ApiOperation(value = "查询雨量预警规则列表",notes="查询雨量预警规则列表")
    @PostMapping(value = "select-rain-rule-list")
    public ResponseEntity<SuccessResponse<Page<RainWarnRule>>> getRainWarnrule(@RequestBody QuetyStWarnRule baseDao) throws Exception {
        IPage<RainWarnRule> iPage = stWarnRuleService.getRainWarnrule(baseDao.getAdcd(),baseDao.getWarnGradeId(),baseDao.getPageNum(),baseDao.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "查询水位预警规则列表",notes="查询水位预警规则列表")
    @PostMapping(value = "select-z-rule-list")
    public ResponseEntity<SuccessResponse<Page<SwWarnRule>>> getSwWarnrule(@RequestBody QuetyStWarnRule baseDao) throws Exception {
        IPage<SwWarnRule> iPage = stWarnRuleService.getSwWarnrule(baseDao.getAdcd(),baseDao.getWarnGradeId(),baseDao.getPageNum(),baseDao.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

}
