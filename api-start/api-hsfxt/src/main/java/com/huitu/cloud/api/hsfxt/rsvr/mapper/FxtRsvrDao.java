package com.huitu.cloud.api.hsfxt.rsvr.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.hsfxt.rsvr.entity.KbRvsanalyG;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmInfo;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmPic;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrObjinfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 风险图-小水库 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
public interface FxtRsvrDao {
    /**
     * 查询小水库风险信息
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return
     */
    IPage<KbRvsanalyG> getFxtRsvrList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询小水库河道断面信息
     *
     * @param cid 成果编码
     * @return
     */
    List<RsvrDmInfo> getFxtRsvrDmList(@Param("cid") String cid);

    /**
     * 小水库河道断面图
     *
     * @param cid 成果编码
     * @return
     */
    List<RsvrDmPic> getFxtRsvrDmPic(@Param("cid") String cid);

    /**
     * 查询测站对应的风险信息（测站编码是否可以对应上cid，如果对应则返回水库信息）
     * @param stcd 测站编码
     * @return
     */
    List<KbRvsanalyG> getFxtRsvrListByStcd(@Param("stcd") String stcd);

    List<RsvrObjinfo> getFzqRsvrListByStcd(@Param("stcd") String stcd);
}
