package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value = "短信描述信息")
public class SmsDesInfo {
    @ApiModelProperty(value = "政区平均降雨量")
    private String adavg;
    @ApiModelProperty(value = "下级政区平均降雨量")
    private List<AdAvgRain> lowAvg;
    @ApiModelProperty(value = "县级政区平均降雨量")
    private List<AdAvgRain> xianAvg;
    @ApiModelProperty(value = "累计降雨测站集合")
    private List<Rain> stRain;

    public String getAdavg() {
        return adavg;
    }

    public void setAdavg(String adavg) {
        this.adavg = adavg;
    }

    public List<AdAvgRain> getLowAvg() {
        return lowAvg;
    }

    public void setLowAvg(List<AdAvgRain> lowAvg) {
        this.lowAvg = lowAvg;
    }

    public List<AdAvgRain> getXianAvg() {
        return xianAvg;
    }

    public void setXianAvg(List<AdAvgRain> xianAvg) {
        this.xianAvg = xianAvg;
    }

    public List<Rain> getStRain() {
        return stRain;
    }

    public void setStRain(List<Rain> stRain) {
        this.stRain = stRain;
    }
}
