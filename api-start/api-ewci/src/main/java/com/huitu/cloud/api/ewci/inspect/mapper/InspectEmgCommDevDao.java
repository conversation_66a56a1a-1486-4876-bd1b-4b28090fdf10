package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectEmgCommDev;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 应急通信设备
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectEmgCommDevDao {

    int deleteInspectEmgCommDev(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);

    List<BnsInspectEmgCommDev> getInspectEmgCommDevList(@Param("adcd") String adcd, @Param("year") String year);

    int updateInspectEmgCommDev(BnsInspectEmgCommDev entity);

    int insertInspectEmgCommDevDao(BnsInspectEmgCommDev entity);
}

