package com.huitu.cloud.api.ew.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.base.EwDisasterFile;
import com.huitu.cloud.api.ew.entity.base.EwDisasterRecord;
import com.huitu.cloud.api.ew.entity.common.FileType;
import com.huitu.cloud.api.ew.entity.ext.EwDisasterFileDTO;
import com.huitu.cloud.api.ew.entity.request.EwDisasterQueryRequest;
import com.huitu.cloud.api.ew.entity.request.EwDisasterSaveRequest;
import com.huitu.cloud.api.ew.entity.response.EwDisasterRecordResponse;
import com.huitu.cloud.api.ew.exception.WarningException;
import com.huitu.cloud.api.ew.mapper.EwDisasterDao;
import com.huitu.cloud.api.ew.service.EwDisasterService;
import com.huitu.cloud.util.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.OutputStream;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 灾情反馈服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class EwDisasterServiceImpl implements EwDisasterService {

    private final EwDisasterDao baseDao;

    public EwDisasterServiceImpl(EwDisasterDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public IPage<EwDisasterRecordResponse> getPageList(EwDisasterQueryRequest request) {
        IPage<EwDisasterRecordResponse> page = baseDao.getPageList(request.toPage(), request.toQuery());
        if (!CollectionUtils.isEmpty(page.getRecords()) && request.isLoadFile()) {
            // 获取灾情记录集合
            List<EwDisasterRecordResponse> records = page.getRecords();
            // 获取灾情ID集合
            List<List<String>> disasterIdLists = ListUtils.splitList(
                    records.stream().map(EwDisasterRecord::getDisasterId).collect(Collectors.toList()), 500);
            for (List<String> disasterIdList : disasterIdLists) {
                // 获取文件集合
                List<EwDisasterFile> fileList = baseDao.getFileList(disasterIdList);
                if (!CollectionUtils.isEmpty(fileList)) {
                    for (EwDisasterRecordResponse record : records) {
                        List<EwDisasterFile> curFileList = fileList.stream()
                                .filter(itm -> itm.getDisasterId().equals(record.getDisasterId()))
                                .collect(Collectors.toList());
                        fillFileList(record, curFileList);
                    }
                }
            }
        }
        return page;
    }

    @Override
    public void dataExport(EwDisasterQueryRequest query, OutputStream output) {
        IPage<EwDisasterRecordResponse> page = baseDao.getPageList(query.toPage(), query.toQuery());
        List<EwDisasterRecordResponse> records = page.getRecords();
        for (int i = 0; i < records.size(); i++) {
            records.get(i).setSortno(i + 1);
        }
        EasyExcel.write(output, EwDisasterRecordResponse.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("险情（灾情）信息")
                .doWrite(records);
    }

    @Override
    public EwDisasterRecordResponse getRecord(String disasterId) {
        EwDisasterRecordResponse record = baseDao.getRecord(disasterId);
        if (null != record) {
            try {
                fillFileList(record, baseDao.getFileList(Collections.singletonList(disasterId)));
            } catch (Exception ignored) {
            }
        }
        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveDisaster(EwDisasterSaveRequest request) {
        if (baseDao.exists(request)) {
            throw new WarningException("当前政区已存在相同场次的灾情记录，请检查后重试");
        }
        int result;
        try {
            if (StringUtils.isBlank(request.getDisasterId())) {
                // 新增
                request.setDisasterId(IdUtil.randomUUID().toUpperCase());
                result = baseDao.insertRecord(request);
            } else {
                // 修改
                result = baseDao.updateRecord(request);
                if (result > 0) {
                    baseDao.deleteFileByDisasterId(request.getDisasterId());
                }
            }
        } catch (DuplicateKeyException e) {
            throw new WarningException("当前政区已存在相同场次的灾情记录，请检查后重试", e);
        }
        if (result > 0) {
            if (!CollectionUtils.isEmpty(request.getPictureList())) {
                baseDao.saveFileList(new EwDisasterFileDTO(request.getDisasterId(), FileType.PICTURE.getCode(), request.getPictureList()));
            }
            if (!CollectionUtils.isEmpty(request.getVideoList())) {
                baseDao.saveFileList(new EwDisasterFileDTO(request.getDisasterId(), FileType.VIDEO.getCode(), request.getVideoList()));
            }
        }
        return result;
    }

    @Override
    @Transactional
    public int deleteDisaster(String disasterId) {
        int result = baseDao.deleteRecord(disasterId);
        if (result > 0) {
            baseDao.deleteFileByDisasterId(disasterId);
        }
        return result;
    }

    private void fillFileList(EwDisasterRecordResponse record, List<EwDisasterFile> fileList) {
        if (CollectionUtils.isEmpty(fileList)) {
            return;
        }
        record.setPictureList(fileList.stream().filter(file -> FileType.PICTURE.equalsCode(file.getFileType())).collect(Collectors.toList()));
        record.setVideoList(fileList.stream().filter(file -> FileType.VIDEO.equalsCode(file.getFileType())).collect(Collectors.toList()));
    }
}
