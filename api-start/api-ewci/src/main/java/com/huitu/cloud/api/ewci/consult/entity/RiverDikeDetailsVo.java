package com.huitu.cloud.api.ewci.consult.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 提防统计详细数据堤防
 */
@ApiModel(value = "提防统计详细数据堤防")
public class RiverDikeDetailsVo implements Serializable {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "长度")
    private Double reachLen;

    @ApiModelProperty(value = "子集")
    private List<RiverDikeDetailsChildVo> children;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Double getReachLen() {
        return reachLen;
    }

    public void setReachLen(Double reachLen) {
        this.reachLen = reachLen;
    }

    public List<RiverDikeDetailsChildVo> getChildren() {
        return children;
    }

    public void setChildren(List<RiverDikeDetailsChildVo> children) {
        this.children = children;
    }
}

