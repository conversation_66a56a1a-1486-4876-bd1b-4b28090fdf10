package com.huitu.cloud.api.screen.fhjz.utils;

import com.huitu.cloud.api.screen.fhjz.Constants;
import org.apache.commons.lang3.time.DateUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DateTimeUtils {

    /**
     * 雨量获取 开始及结束时间 方法
     *
     * @param type
     * @param hour
     * @return
     */
    public static List<String> queryRainTimeBy8(String type, int hour) throws ParseException {
        List<String> queryTimes = new ArrayList<String>();
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH");//定义新的日期格式
        DateFormat queryformatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//定义查询日期格式
        String currentTimeString = formatter.format(new Date());
        Date currentDate =  queryformatter.parse(currentTimeString+":00:00");
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        LocalDateTime localDateTime = LocalDateTime.now(zoneId);
        int currentHour = localDateTime.getHour();
        if(Constants.RAIN_REAL_TYPE.equals(type)){
            //实时降雨
            switch (hour){
                case 24:
                    if(currentHour < 8){
                        //当前时间小于8，应该从昨天8:00统计
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, currentHour - 24)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                    }else{
                        //当前时间大于8，应该从今天8:00统计
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 8-currentHour)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                    }
                    break;
                case 48:
                    if(currentHour < 8){
                        //当前时间小于8，应该从昨天8:00统计
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, currentHour - 48)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                    }else{
                        //当前时间大于8，应该从今天8:00统计
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, -16-currentHour)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                    }
                    break;
                case 72:
                    if(currentHour < 8){
                        //当前时间小于8，应该从昨天8:00统计
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, currentHour - 72)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                    }else{
                        //当前时间大于8，应该从今天8:00统计
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, -40-currentHour)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                    }
                    break;
            }
        }else if(Constants.RAIN_FORECAST_TYPE.equals(type)){
            //预测降雨
            switch (hour){
                case 24:
                    if(currentHour < 8){
                        //当前时间小于8，应该统计到今天8:00
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 8 - currentHour)));
                    }else{
                        //当前时间大于8，应该统计到明天8:00
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 24-currentHour+8)));
                    }
                    break;
                case 48:
                    if(currentHour < 8){
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 8 - currentHour + 24)));
                    }else{
                        //当前时间大于8，应该从今天8:00统计
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 24-currentHour+8+24)));
                    }
                    break;
                case 72:
                    if(currentHour < 8){
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 8 - currentHour + 48)));
                    }else{
                        //当前时间大于8，应该从今天8:00统计
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
                        queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 24-currentHour+8+48)));
                    }
                    break;
            }
        }
        return queryTimes;
    }

    /**
     * 雨量获取 开始及结束时间 方法
     *
     * @param type
     * @param hour
     * @return
     */
    public static List<String> queryRainTime(String type, int hour) throws ParseException {
        List<String> queryTimes = new ArrayList<String>();
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH");//定义新的日期格式
        DateFormat queryformatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//定义查询日期格式
        String currentTimeString = formatter.format(new Date());
        Date currentDate =  queryformatter.parse(currentTimeString+":00:00");
        if(Constants.RAIN_REAL_TYPE.equals(type)){
            //实时降雨
            queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, -hour+1)));
            queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 1)));
        }else if(Constants.RAIN_FORECAST_TYPE.equals(type)){
            //预测降雨
            queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, 0)));
            queryTimes.add(queryformatter.format(DateUtils.addHours(currentDate, hour)));
        }
        return queryTimes;
    }

    /**
     * 获取查询水库的开始及结束时间
     *
     * @return
     */
    public static List<String> queryReservoirTime(){
        List<String> queryTimes = new ArrayList<String>();
        DateFormat queryformatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");//定义查询日期格式
        Date currentDate = new Date();
        queryTimes.add(queryformatter.format( DateUtils.addHours(currentDate, -3*24)));
        queryTimes.add(queryformatter.format(currentDate));
        return queryTimes;
    }

}
