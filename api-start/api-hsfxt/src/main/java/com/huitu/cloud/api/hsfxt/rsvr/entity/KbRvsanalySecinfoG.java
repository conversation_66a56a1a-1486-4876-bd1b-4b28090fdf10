package com.huitu.cloud.api.hsfxt.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 风险图分析成果断面基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-28
 */
@TableName("KB_RVSANALY_SECINFO_G")
@ApiModel(value="KbRvsanalySecinfoG对象", description="风险图分析成果断面基本信息表")
public class KbRvsanalySecinfoG extends Model<KbRvsanalySecinfoG> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "成果编号")
    @TableId(value = "CID", type = IdType.NONE)
    private String cid;

    @ApiModelProperty(value = "断面编号")
    @TableField("RSCD")
    private Double rscd;

    @ApiModelProperty(value = "断面名称")
    @TableField("RSNM")
    private String rsnm;

    @ApiModelProperty(value = "距大坝距离")
    @TableField("CDIST")
    private Double cdist;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private Double lttd;

    @ApiModelProperty(value = "备注")
    @TableField("RM")
    private String rm;


    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Double getRscd() {
        return rscd;
    }

    public void setRscd(Double rscd) {
        this.rscd = rscd;
    }

    public String getRsnm() {
        return rsnm;
    }

    public void setRsnm(String rsnm) {
        this.rsnm = rsnm;
    }

    public Double getCdist() {
        return cdist;
    }

    public void setCdist(Double cdist) {
        this.cdist = cdist;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public String getRm() {
        return rm;
    }

    public void setRm(String rm) {
        this.rm = rm;
    }

    @Override
    protected Serializable pkVal() {
        return this.cid;
    }

    @Override
    public String toString() {
        return "KbRvsanalySecinfoG{" +
        "cid=" + cid +
        ", rscd=" + rscd +
        ", rsnm=" + rsnm +
        ", cdist=" + cdist +
        ", lgtd=" + lgtd +
        ", lttd=" + lttd +
        ", rm=" + rm +
        "}";
    }
}
