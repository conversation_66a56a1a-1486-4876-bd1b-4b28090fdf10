package com.huitu.cloud.api.syq.skdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * <p>
 * 水库上下游河道水情信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@ApiModel
public class RsvrRiverInfo implements Serializable {

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "水位")
    @TableField("Z")
    private String z;

    @ApiModelProperty(value = "流量")
    @TableField("Q")
    private BigDecimal q;

    @ApiModelProperty(value = "水势")
    @TableField("WPTN")
    private String wptn;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private BigDecimal lttd;
    @ApiModelProperty(value = "警戒水位")
    @TableField("WRZ")
    private BigDecimal wrz;
    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;
    @ApiModelProperty(value = "保证水位")
    @TableField("GRZ")
    private BigDecimal grz;
    @ApiModelProperty(value = "超警戒水位")
    private BigDecimal zwrz;
    @ApiModelProperty(value = "超保证水位")
    private BigDecimal zgrz;

    @ApiModelProperty(value = "雨量报讯标志 1：报讯 ")
    private String pfl;
    @ApiModelProperty(value = "上下游标志类型 1、上游 2、下游 ")
    private String type;

    @ApiModelProperty(value = "累计雨量")
    private String drps;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getZ() {
        return z;
    }

    public void setZ(String z) {
        this.z = z;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public String getWptn() {
        return wptn;
    }

    public void setWptn(String wptn) {
        this.wptn = wptn;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public BigDecimal getWrz() {
        return wrz;
    }

    public void setWrz(BigDecimal wrz) {
        this.wrz = wrz;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public BigDecimal getGrz() {
        return grz;
    }

    public void setGrz(BigDecimal grz) {
        this.grz = grz;
    }

    public BigDecimal getZwrz() {
        return zwrz;
    }

    public void setZwrz(BigDecimal zwrz) {
        this.zwrz = zwrz;
    }

    public BigDecimal getZgrz() {
        return zgrz;
    }

    public void setZgrz(BigDecimal zgrz) {
        this.zgrz = zgrz;
    }

    public String getPfl() {
        return pfl;
    }

    public void setPfl(String pfl) {
        this.pfl = pfl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDrps() {
        return drps;
    }

    public void setDrps(String drps) {
        this.drps = drps;
    }
}

