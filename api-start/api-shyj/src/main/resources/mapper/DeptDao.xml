<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.shyj.dept.mapper.DeptDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <update id="updateDept">
        update DEPT_B
        set pid=#{pid},
            deptnm=#{deptnm},
            faxcode=#{faxcode},
            broadcast=#{broadcast},
            duty=#{duty},
            remark=#{remark}
        where adcd=#{adcd} and deptcd=#{deptcd}
    </update>

    <update id="delDept">
      update DEPT_B set ISDEL='1' where ADCD=#{adcd} and DEPTCD=#{deptcd}
    </update>


    <select id="getDeptListByPage" resultType="com.huitu.cloud.api.shyj.dept.entity.RelevantDeptInfo">
        select a.adcd, a.deptcd, a.deptnm, a.faxcode, a.broadcast, a.duty, a.remark, a.pid, a.sortno,b.DeptNM pdeptnm,C.ADNM
        from DEPT_B a left join DEPT_B b on a.PID=b.DeptCD and a.ADCD=b.ADCD
        LEFT JOIN BSN_ADCD_B C ON A.ADCD=C.ADCD
        WHERE  1=1 and isnull(a.isdel,0) !='1'
        <if test="map.adcd !=null and map.adcd !=''" >
            and  a.ADCD=#{map.adcd}
        </if>
        <if test="map.deptnm !=null and map.deptnm !=''" >
            and  CHARINDEX(#{map.deptnm},a.deptnm) >0
        </if>
        <if test="map.pid !=null and map.pid !=''" >
            and   a.pid=#{map.pid}
        </if>
        order by a.deptcd
    </select>
    <select id="getDeptList" resultType="com.huitu.cloud.api.shyj.dept.entity.RelevantDeptInfo">
        select a.adcd, a.deptcd, a.deptnm, a.faxcode, a.broadcast, a.duty, a.remark, convert(varchar,isnull(a.pid,0))pid , a.sortno,b.DeptNM pdeptnm,C.ADNM
        from DEPT_B a left join DEPT_B b on a.PID=b.DeptCD and a.ADCD=b.ADCD
        LEFT JOIN BSN_ADCD_B C ON A.ADCD=C.ADCD
        WHERE  1=1 and isnull(a.isdel,0) !='1'
        <if test="adcd !=null and adcd !=''" >
            and  a.ADCD=#{adcd}
        </if>
        <if test="deptnm !=null and deptnm !=''" >
            and   CHARINDEX(#{deptnm,a.deptnm}) >0
        </if>
        <if test="checkNm !=null and checkNm !=''" >
            and   a.deptnm=#{checkNm}
        </if>
        <if test="pid !=null and pid !=''" >
            and   a.pid=#{pid}
        </if>
        order by a.deptcd
    </select>
    <select id="selectMaxId" resultType="com.huitu.cloud.api.shyj.dept.entity.DeptB">
        select convert(varchar,isnull(max(cast(deptcd as int )),0)+1) deptcd
        from DEPT_B
    </select>


</mapper>
