package com.huitu.cloud.api.ewci.hydfcst.entity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 返回水库 xml 映射字段类
 *
 * <AUTHOR>
 */
public class StReglat implements Serializable {
    private static final long serialVersionUID = 1L;

    private String stcd;   // 站点编码
    private BigDecimal rz; // 水位（对应SQL查询中的z列，别名为rz）
    private BigDecimal w;  // 流量
    private BigDecimal otq; // 出库流量
    private String ymdh;   // 时间戳（年月日时）
    private String fsltdw;   // 调度方式（来自ST_RSVRFSR_B）
    private BigDecimal normz; // 正常蓄水位（来自ST_RSVRFCCH_B）
    private BigDecimal actz;  // 实际水位（来自ST_RSVRFCCH_B）
    private BigDecimal dsflz; // 下游控制水位（来自ST_RSVRFCCH_B）
    private BigDecimal ckflz; // 出库水位（来自ST_RSVRFCCH_B）
    private BigDecimal damel; // 大坝高程（来自ST_RSVRFCCH_B）
    private BigDecimal ttcp;  // 总调度能力（来自ST_RSVRFCCH_B）
    private String bgmd;      // 调度开始日期（月日）（来自ST_RSVRFSR_B）
    private String edmd;      // 调度结束日期（月日）（来自ST_RSVRFSR_B）
    private BigDecimal fsltdz; // 调度水位（来自ST_RSVRFSR_B）
    private String stnm;   // 站点名称（来自BSN_STBPRP_V）
    private String rvnm;   // 河流名称（来自BSN_STBPRP_V）
    private String hnnm;   // 水文节点名称（来自BSN_STBPRP_V）
    private String bsnm;   // 流域名称（来自BSN_STBPRP_V）
    private String adcd;   // 行政区划代码（来自BSN_STBPRP_V）
    private String adnm;   // 行政区划名称（来自BSN_STBPRP_V）
    private BigDecimal lgtd;  // 经度（来自BSN_STBPRP_V）
    private BigDecimal lttd;  // 纬度（来自BSN_STBPRP_V）
    private BigDecimal plgtd;  // 纠偏后经度（来自BSN_STBPRP_V）
    private BigDecimal plttd;  // 纠偏后纬度（来自BSN_STBPRP_V）

    private String tmYmdh;
    private BigDecimal inq;
    private BigDecimal rzfsltdz;
    private BigDecimal rznormz;
    private BigDecimal wfsltdw;
    private String stlc;
    private String sttp;
    private String frgrd;
    private String xadcd;
    private String xadnm;
    private String rsvrtp;
    private String fldcp;
    private String actcp;

    private String resCode;
    private String resName;


    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getTmYmdh() {
        return tmYmdh;
    }

    public BigDecimal getInq() {
        return inq;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public BigDecimal getRznormz() {
        return rznormz;
    }

    public BigDecimal getWfsltdw() {
        return wfsltdw;
    }

    public String getStlc() {
        return stlc;
    }

    public String getSttp() {
        return sttp;
    }

    public String getFrgrd() {
        return frgrd;
    }


    public String getXadcd() {
        return xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public String getRsvrtp() {
        return rsvrtp;
    }

    public String getFldcp() {
        return fldcp;
    }

    public String getActcp() {
        return actcp;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    public void setTmYmdh(String tmYmdh) {
        this.tmYmdh = tmYmdh;
    }

    public void setInq(BigDecimal inq) {
        this.inq = inq;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public void setRznormz(BigDecimal rznormz) {
        this.rznormz = rznormz;
    }

    public void setWfsltdw(BigDecimal wfsltdw) {
        this.wfsltdw = wfsltdw;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public void setFrgrd(String frgrd) {
        this.frgrd = frgrd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public void setRsvrtp(String rsvrtp) {
        this.rsvrtp = rsvrtp;
    }

    public void setFldcp(String fldcp) {
        this.fldcp = fldcp;
    }

    public void setActcp(String actcp) {
        this.actcp = actcp;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public BigDecimal getRz() {
        return rz;
    }

    public void setRz(BigDecimal rz) {
        this.rz = rz;
    }

    public BigDecimal getW() {
        return w;
    }

    public void setW(BigDecimal w) {
        this.w = w;
    }

    public BigDecimal getOtq() {
        return otq;
    }

    public void setOtq(BigDecimal otq) {
        this.otq = otq;
    }

    public String getYmdh() {
        return ymdh;
    }

    public void setYmdh(String ymdh) {
        this.ymdh = ymdh;
    }

    public String getFsltdw() {
        return fsltdw;
    }

    public void setFsltdw(String fsltdw) {
        this.fsltdw = fsltdw;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }

    public BigDecimal getActz() {
        return actz;
    }

    public void setActz(BigDecimal actz) {
        this.actz = actz;
    }

    public BigDecimal getDsflz() {
        return dsflz;
    }

    public void setDsflz(BigDecimal dsflz) {
        this.dsflz = dsflz;
    }

    public BigDecimal getCkflz() {
        return ckflz;
    }

    public void setCkflz(BigDecimal ckflz) {
        this.ckflz = ckflz;
    }

    public BigDecimal getDamel() {
        return damel;
    }

    public void setDamel(BigDecimal damel) {
        this.damel = damel;
    }

    public BigDecimal getTtcp() {
        return ttcp;
    }

    public void setTtcp(BigDecimal ttcp) {
        this.ttcp = ttcp;
    }

    public String getBgmd() {
        return bgmd;
    }

    public void setBgmd(String bgmd) {
        this.bgmd = bgmd;
    }

    public String getEdmd() {
        return edmd;
    }

    public void setEdmd(String edmd) {
        this.edmd = edmd;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getHnnm() {
        return hnnm;
    }

    public void setHnnm(String hnnm) {
        this.hnnm = hnnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }
}
