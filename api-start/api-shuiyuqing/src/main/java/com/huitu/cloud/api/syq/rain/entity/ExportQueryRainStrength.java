package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 降雨强度统计导出
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-29
 */
@ApiModel(value="ExportQueryRainStrength对象", description="降雨强度统计导出")
public class ExportQueryRainStrength {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "测站归属类型 1、水文 2、山洪 3、气象")
    private List<String> stType;
    @ApiModelProperty(value = "政区编码",required = true)
    private String adcd;
    @ApiModelProperty(value = "流域编码")
    private String bscd;
    @ApiModelProperty(value = "流域名称")
    private String bsnm;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "统计时段")
    private int intv;

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public int getIntv() {
        return intv;
    }

    public void setIntv(int intv) {
        this.intv = intv;
    }
}
