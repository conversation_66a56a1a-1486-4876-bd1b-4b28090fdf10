package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * <p>
 * 控制断面水位- 流量- 人口关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@TableName("IA_A_SWLLRKTB")
@ApiModel(value="IaASwllrktb对象", description="控制断面水位- 流量- 人口关系表")
public class IaSwllrktb extends Model<IaSwllrktb> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableId(value = "ADCD", type = IdType.NONE)
    private String adcd;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "断面代码")
    @TableField("HSCD")
    private String hscd;

    @ApiModelProperty(value = "水位")
    @TableField("Z")
    private Double z;

    @ApiModelProperty(value = "流量（m³/s）")
    @TableField("SJHSHF")
    private Double sjhshf;

    @ApiModelProperty(value = "重现期（年）")
    @TableField("CXQ")
    private String cxq;

    @ApiModelProperty(value = "人口(人)")
    @TableField("PCOUNT")
    private Double pcount;

    @ApiModelProperty(value = "户数（户）")
    @TableField("HCOUNT")
    private Double hcount;

    @ApiModelProperty(value = "房屋数（座）")
    @TableField("HNCOUNT")
    private Double hncount;

    @ApiModelProperty(value = "导入者姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalTime moditime;
    @ApiModelProperty(value = "流域名称")
    private String wsnm;
    @ApiModelProperty(value = "政区名称")
    private String adnm;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getHscd() {
        return hscd;
    }

    public void setHscd(String hscd) {
        this.hscd = hscd;
    }

    public Double getZ() {
        return z;
    }

    public void setZ(Double z) {
        this.z = z;
    }

    public Double getSjhshf() {
        return sjhshf;
    }

    public void setSjhshf(Double sjhshf) {
        this.sjhshf = sjhshf;
    }

    public String getCxq() {
        return cxq;
    }

    public void setCxq(String cxq) {
        this.cxq = cxq;
    }

    public Double getPcount() {
        return pcount;
    }

    public void setPcount(Double pcount) {
        this.pcount = pcount;
    }

    public Double getHcount() {
        return hcount;
    }

    public void setHcount(Double hcount) {
        this.hcount = hcount;
    }

    public Double getHncount() {
        return hncount;
    }

    public void setHncount(Double hncount) {
        this.hncount = hncount;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.adcd;
    }

    @Override
    public String toString() {
        return "IaSwllrktb{" +
        "adcd=" + adcd +
        ", wscd=" + wscd +
        ", hscd=" + hscd +
        ", z=" + z +
        ", sjhshf=" + sjhshf +
        ", cxq=" + cxq +
        ", pcount=" + pcount +
        ", hcount=" + hcount +
        ", hncount=" + hncount +
        ", signer=" + signer +
        ", audid=" + audid +
        ", status=" + status +
        ", remark=" + remark +
        ", moditime=" + moditime +
        "}";
    }
}
