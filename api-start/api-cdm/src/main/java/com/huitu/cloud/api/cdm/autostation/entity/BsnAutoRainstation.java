package com.huitu.cloud.api.cdm.autostation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 自动站点信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
@TableName("BSN_AUTO_RAINSTATION")
@ApiModel(value="BsnAutoRainstation对象", description="自动站点信息表")
public class BsnAutoRainstation extends Model<BsnAutoRainstation> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "站点编号")
    @TableId(value = "ID", type = IdType.NONE)
    private String id;

    @ApiModelProperty(value = "行政区划码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "站点编码")
    @TableField("CODE")
    private String code;

    @ApiModelProperty(value = "站点名称")
    @TableField("PROJECT_NAME")
    private String projectName;

    @ApiModelProperty(value = "建站时间")
    @TableField("CREATE_TM")
    private String createTm;

    @ApiModelProperty(value = "乡镇")
    @TableField("COUNTY")
    private String county;

    @ApiModelProperty(value = "行政村")
    @TableField("XZC")
    private String xzc;

    @ApiModelProperty(value = "屯组")
    @TableField("TZ")
    private String tz;

    @ApiModelProperty(value = "村屯数")
    @TableField("CTS")
    private String cts;

    @ApiModelProperty(value = "户数")
    @TableField("HS")
    private String hs;

    @ApiModelProperty(value = "人口")
    @TableField("PEOPLE")
    private String people;

    @ApiModelProperty(value = "所属河流")
    @TableField("RIVER")
    private String river;

    @ApiModelProperty(value = "所属流域")
    @TableField("HNM")
    private String hnm;

    @ApiModelProperty(value = "经度")
    @TableField("LTTD")
    private String lttd;

    @ApiModelProperty(value = "纬度")
    @TableField("LDTD")
    private String ldtd;

    @ApiModelProperty(value = "施工单位")
    @TableField("COMPANY")
    private String company;

    @ApiModelProperty(value = "RTU品牌（初建）")
    @TableField("RTU_BUILD_BRAND")
    private String rtuBuildBrand;

    @ApiModelProperty(value = "RTU型号（初建）")
    @TableField("RTU_BUILD_MODEL")
    private String rtuBuildModel;

    @ApiModelProperty(value = "RTU生产厂家(初建）")
    @TableField("RTU_BUILD_COMPANY")
    private String rtuBuildCompany;

    @ApiModelProperty(value = "RTU品牌（更新）")
    @TableField("RTU_UPDATE_BRAND")
    private String rtuUpdateBrand;

    @ApiModelProperty(value = "RTU型号（更新）")
    @TableField("RTU_UPDATE_MODEL")
    private String rtuUpdateModel;

    @ApiModelProperty(value = "RTU生产厂家(更新）")
    @TableField("RTU_UPDATE_COMPANY")
    private String rtuUpdateCompany;

    @ApiModelProperty(value = "RTU更新时间")
    @TableField("RTU_UPDATE_TM")
    private String rtuUpdateTm;

    @ApiModelProperty(value = "雨量筒品牌（初建）")
    @TableField("YLT_BUILD_BRAND")
    private String yltBuildBrand;

    @ApiModelProperty(value = "雨量筒型号（初建）")
    @TableField("YLT_BUILD_MODEL")
    private String yltBuildModel;

    @ApiModelProperty(value = "雨量筒生产厂家（初建）")
    @TableField("YLT_BUILD_COMPANY")
    private String yltBuildCompany;

    @ApiModelProperty(value = "雨量筒品牌（更新）")
    @TableField("YLT_UPDATE_BRAND")
    private String yltUpdateBrand;

    @ApiModelProperty(value = "雨量筒型号（更新）")
    @TableField("YLT_UPDATE_MODEL")
    private String yltUpdateModel;

    @ApiModelProperty(value = "雨量筒生产厂家（更新）")
    @TableField("YLT_UPDATE_COMPANY")
    private String yltUpdateCompany;

    @ApiModelProperty(value = "雨量筒更新时间")
    @TableField("YLT_UPDATE_TM")
    private String yltUpdateTm;

    @ApiModelProperty(value = "电池品牌（初建）")
    @TableField("DC_BUILD_BRAND")
    private String dcBuildBrand;

    @ApiModelProperty(value = "电池型号（初建）")
    @TableField("DC_BUILD_MODEL")
    private String dcBuildModel;

    @ApiModelProperty(value = "电池类型（初建）")
    @TableField("DC_BUILD_TYPE")
    private String dcBuildType;

    @ApiModelProperty(value = "电池生产厂家（初建）")
    @TableField("DC_BUILD_COMPANY")
    private String dcBuildCompany;

    @ApiModelProperty(value = "电池品牌（更新）")
    @TableField("DC_UPDATE_BRAND")
    private String dcUpdateBrand;

    @ApiModelProperty(value = "电池型号（更新）")
    @TableField("DC_UPDATE_MODEL")
    private String dcUpdateModel;

    @ApiModelProperty(value = "电池类型（更新）")
    @TableField("DC_UPDATE_TYPE")
    private String dcUpdateType;

    @ApiModelProperty(value = "电池生产厂家（更新）")
    @TableField("DC_UPDATE_COMPANY")
    private String dcUpdateCompany;

    @ApiModelProperty(value = "电池更新时间")
    @TableField("DC_UPDATE_TM")
    private String dcUpdateTm;

    @ApiModelProperty(value = "时间")
    @TableField("TM")
    private LocalDateTime tm;

    @ApiModelProperty(value = "类型 ")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = "水位计品牌（初建）")
    @TableField("SWJ_BUILD_BRAND")
    private String swjBuildBrand;

    @ApiModelProperty(value = "水位计型号（初建）")
    @TableField("SWJ_BUILD_MODEL")
    private String swjBuildModel;

    @ApiModelProperty(value = "水位计生产厂家（初建）")
    @TableField("SWJ_BUILD_COMPANY")
    private String swjBuildCompany;

    @ApiModelProperty(value = "水位计品牌（更新）")
    @TableField("SWJ_UPDATE_BRAND")
    private String swjUpdateBrand;

    @ApiModelProperty(value = "水位计型号（更新）")
    @TableField("SWJ_UPDATE_MODEL")
    private String swjUpdateModel;

    @ApiModelProperty(value = "水位计生产厂家（更新）")
    @TableField("SWJ_UPDATE_COMPANY")
    private String swjUpdateCompany;

    @ApiModelProperty(value = "水位计更新时间")
    @TableField("SWJ_UPDATE_TM")
    private String swjUpdateTm;

    @ApiModelProperty(value = "预警负责人")
    @TableField("YJ_FZR")
    private String yjFzr;

    @ApiModelProperty(value = "广播机品牌（初建）")
    @TableField("GBJ_BUILD_BRAND")
    private String gbjBuildBrand;

    @ApiModelProperty(value = "广播机型号（初建）")
    @TableField("GBJ_BUILD_MODEL")
    private String gbjBuildModel;

    @ApiModelProperty(value = "广播机生产厂家（初建）")
    @TableField("GBJ_BUILD_COMPANY")
    private String gbjBuildCompany;

    @ApiModelProperty(value = "广播机品牌（更新）")
    @TableField("GBJ_UPDATE_BRAND")
    private String gbjUpdateBrand;

    @ApiModelProperty(value = "广播机型号（更新）")
    @TableField("GBJ_UPDATE_MODEL")
    private String gbjUpdateModel;

    @ApiModelProperty(value = "广播机生产厂家（更新）")
    @TableField("GBJ_UPDATE_COMPANY")
    private String gbjUpdateCompany;

    @ApiModelProperty(value = "广播机更新时间")
    @TableField("GBJ_UPDATE_TM")
    private String gbjUpdateTm;

    @ApiModelProperty(value = "水位报警器品牌（初建）")
    @TableField("BJQ_BUILD_BRAND")
    private String bjqBuildBrand;

    @ApiModelProperty(value = "水位报警器型号（初建）")
    @TableField("BJQ_BUILD_MODEL")
    private String bjqBuildModel;

    @ApiModelProperty(value = "水位报警器生产厂家（初建）")
    @TableField("BJQ_BUILD_COMPANY")
    private String bjqBuildCompany;

    @ApiModelProperty(value = "水位报警器品牌（更新）")
    @TableField("BJQ_UPDATE_BRAND")
    private String bjqUpdateBrand;

    @ApiModelProperty(value = "水位报警器型号（更新）")
    @TableField("BJQ_UPDATE_MODEL")
    private String bjqUpdateModel;

    @ApiModelProperty(value = "水位报警器生产厂家（更新）")
    @TableField("BJQ_UPDATE_COMPANY")
    private String bjqUpdateCompany;

    @ApiModelProperty(value = "水位报警器更新时间")
    @TableField("BJQ_UPDATE_TM")
    private String bjqUpdateTm;

    @ApiModelProperty(value = "山洪沟")
    @TableField("SHG")
    private String shg;

    @ApiModelProperty(value = "摄像头品牌（初建）")
    @TableField("SXT_BUILD_BRAND")
    private String sxtBuildBrand;

    @ApiModelProperty(value = "摄像头型号（初建）")
    @TableField("SXT_BUILD_MODEL")
    private String sxtBuildModel;

    @ApiModelProperty(value = "摄像头生产厂家（初建）")
    @TableField("SXT_BUILD_COMPANY")
    private String sxtBuildCompany;

    @ApiModelProperty(value = "摄像头品牌（更新）")
    @TableField("SXT_UPDATE_BRAND")
    private String sxtUpdateBrand;

    @ApiModelProperty(value = "摄像头型号（更新）")
    @TableField("SXT_UPDATE_MODEL")
    private String sxtUpdateModel;

    @ApiModelProperty(value = "摄像头生产厂家（更新）")
    @TableField("SXT_UPDATE_COMPANY")
    private String sxtUpdateCompany;

    @ApiModelProperty(value = "摄像头更新时间")
    @TableField("SXT_UPDATE_TM")
    private String sxtUpdateTm;

    @ApiModelProperty(value = "路由器品牌（初建）")
    @TableField("LYQ_BUILD_BRAND")
    private String lyqBuildBrand;

    @ApiModelProperty(value = "路由器型号（初建）")
    @TableField("LYQ_BUILD_MODEL")
    private String lyqBuildModel;

    @ApiModelProperty(value = "路由器生产厂家（初建）")
    @TableField("LYQ_BUILD_COMPANY")
    private String lyqBuildCompany;

    @ApiModelProperty(value = "路由器品牌（更新）")
    @TableField("LYQ_UPDATE_BRAND")
    private String lyqUpdateBrand;

    @ApiModelProperty(value = "路由器型号（更新）")
    @TableField("LYQ_UPDATE_MODEL")
    private String lyqUpdateModel;

    @ApiModelProperty(value = "路由器生产厂家（更新）")
    @TableField("LYQ_UPDATE_COMPANY")
    private String lyqUpdateCompany;

    @ApiModelProperty(value = "路由器更新时间")
    @TableField("LYQ_UPDATE_TM")
    private String lyqUpdateTm;

    @ApiModelProperty(value = "是否接市电")
    @TableField("IS_SD")
    private String isSd;

    @ApiModelProperty(value = "是否接宽带")
    @TableField("IS_KD")
    private String isKd;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCreateTm() {
        return createTm;
    }

    public void setCreateTm(String createTm) {
        this.createTm = createTm;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getXzc() {
        return xzc;
    }

    public void setXzc(String xzc) {
        this.xzc = xzc;
    }

    public String getTz() {
        return tz;
    }

    public void setTz(String tz) {
        this.tz = tz;
    }

    public String getCts() {
        return cts;
    }

    public void setCts(String cts) {
        this.cts = cts;
    }

    public String getHs() {
        return hs;
    }

    public void setHs(String hs) {
        this.hs = hs;
    }

    public String getPeople() {
        return people;
    }

    public void setPeople(String people) {
        this.people = people;
    }

    public String getRiver() {
        return river;
    }

    public void setRiver(String river) {
        this.river = river;
    }

    public String getHnm() {
        return hnm;
    }

    public void setHnm(String hnm) {
        this.hnm = hnm;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }

    public String getLdtd() {
        return ldtd;
    }

    public void setLdtd(String ldtd) {
        this.ldtd = ldtd;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getRtuBuildBrand() {
        return rtuBuildBrand;
    }

    public void setRtuBuildBrand(String rtuBuildBrand) {
        this.rtuBuildBrand = rtuBuildBrand;
    }

    public String getRtuBuildModel() {
        return rtuBuildModel;
    }

    public void setRtuBuildModel(String rtuBuildModel) {
        this.rtuBuildModel = rtuBuildModel;
    }

    public String getRtuBuildCompany() {
        return rtuBuildCompany;
    }

    public void setRtuBuildCompany(String rtuBuildCompany) {
        this.rtuBuildCompany = rtuBuildCompany;
    }

    public String getRtuUpdateBrand() {
        return rtuUpdateBrand;
    }

    public void setRtuUpdateBrand(String rtuUpdateBrand) {
        this.rtuUpdateBrand = rtuUpdateBrand;
    }

    public String getRtuUpdateModel() {
        return rtuUpdateModel;
    }

    public void setRtuUpdateModel(String rtuUpdateModel) {
        this.rtuUpdateModel = rtuUpdateModel;
    }

    public String getRtuUpdateCompany() {
        return rtuUpdateCompany;
    }

    public void setRtuUpdateCompany(String rtuUpdateCompany) {
        this.rtuUpdateCompany = rtuUpdateCompany;
    }

    public String getRtuUpdateTm() {
        return rtuUpdateTm;
    }

    public void setRtuUpdateTm(String rtuUpdateTm) {
        this.rtuUpdateTm = rtuUpdateTm;
    }

    public String getYltBuildBrand() {
        return yltBuildBrand;
    }

    public void setYltBuildBrand(String yltBuildBrand) {
        this.yltBuildBrand = yltBuildBrand;
    }

    public String getYltBuildModel() {
        return yltBuildModel;
    }

    public void setYltBuildModel(String yltBuildModel) {
        this.yltBuildModel = yltBuildModel;
    }

    public String getYltBuildCompany() {
        return yltBuildCompany;
    }

    public void setYltBuildCompany(String yltBuildCompany) {
        this.yltBuildCompany = yltBuildCompany;
    }

    public String getYltUpdateBrand() {
        return yltUpdateBrand;
    }

    public void setYltUpdateBrand(String yltUpdateBrand) {
        this.yltUpdateBrand = yltUpdateBrand;
    }

    public String getYltUpdateModel() {
        return yltUpdateModel;
    }

    public void setYltUpdateModel(String yltUpdateModel) {
        this.yltUpdateModel = yltUpdateModel;
    }

    public String getYltUpdateCompany() {
        return yltUpdateCompany;
    }

    public void setYltUpdateCompany(String yltUpdateCompany) {
        this.yltUpdateCompany = yltUpdateCompany;
    }

    public String getYltUpdateTm() {
        return yltUpdateTm;
    }

    public void setYltUpdateTm(String yltUpdateTm) {
        this.yltUpdateTm = yltUpdateTm;
    }

    public String getDcBuildBrand() {
        return dcBuildBrand;
    }

    public void setDcBuildBrand(String dcBuildBrand) {
        this.dcBuildBrand = dcBuildBrand;
    }

    public String getDcBuildModel() {
        return dcBuildModel;
    }

    public void setDcBuildModel(String dcBuildModel) {
        this.dcBuildModel = dcBuildModel;
    }

    public String getDcBuildType() {
        return dcBuildType;
    }

    public void setDcBuildType(String dcBuildType) {
        this.dcBuildType = dcBuildType;
    }

    public String getDcBuildCompany() {
        return dcBuildCompany;
    }

    public void setDcBuildCompany(String dcBuildCompany) {
        this.dcBuildCompany = dcBuildCompany;
    }

    public String getDcUpdateBrand() {
        return dcUpdateBrand;
    }

    public void setDcUpdateBrand(String dcUpdateBrand) {
        this.dcUpdateBrand = dcUpdateBrand;
    }

    public String getDcUpdateModel() {
        return dcUpdateModel;
    }

    public void setDcUpdateModel(String dcUpdateModel) {
        this.dcUpdateModel = dcUpdateModel;
    }

    public String getDcUpdateType() {
        return dcUpdateType;
    }

    public void setDcUpdateType(String dcUpdateType) {
        this.dcUpdateType = dcUpdateType;
    }

    public String getDcUpdateCompany() {
        return dcUpdateCompany;
    }

    public void setDcUpdateCompany(String dcUpdateCompany) {
        this.dcUpdateCompany = dcUpdateCompany;
    }

    public String getDcUpdateTm() {
        return dcUpdateTm;
    }

    public void setDcUpdateTm(String dcUpdateTm) {
        this.dcUpdateTm = dcUpdateTm;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSwjBuildBrand() {
        return swjBuildBrand;
    }

    public void setSwjBuildBrand(String swjBuildBrand) {
        this.swjBuildBrand = swjBuildBrand;
    }

    public String getSwjBuildModel() {
        return swjBuildModel;
    }

    public void setSwjBuildModel(String swjBuildModel) {
        this.swjBuildModel = swjBuildModel;
    }

    public String getSwjBuildCompany() {
        return swjBuildCompany;
    }

    public void setSwjBuildCompany(String swjBuildCompany) {
        this.swjBuildCompany = swjBuildCompany;
    }

    public String getSwjUpdateBrand() {
        return swjUpdateBrand;
    }

    public void setSwjUpdateBrand(String swjUpdateBrand) {
        this.swjUpdateBrand = swjUpdateBrand;
    }

    public String getSwjUpdateModel() {
        return swjUpdateModel;
    }

    public void setSwjUpdateModel(String swjUpdateModel) {
        this.swjUpdateModel = swjUpdateModel;
    }

    public String getSwjUpdateCompany() {
        return swjUpdateCompany;
    }

    public void setSwjUpdateCompany(String swjUpdateCompany) {
        this.swjUpdateCompany = swjUpdateCompany;
    }

    public String getSwjUpdateTm() {
        return swjUpdateTm;
    }

    public void setSwjUpdateTm(String swjUpdateTm) {
        this.swjUpdateTm = swjUpdateTm;
    }

    public String getYjFzr() {
        return yjFzr;
    }

    public void setYjFzr(String yjFzr) {
        this.yjFzr = yjFzr;
    }

    public String getGbjBuildBrand() {
        return gbjBuildBrand;
    }

    public void setGbjBuildBrand(String gbjBuildBrand) {
        this.gbjBuildBrand = gbjBuildBrand;
    }

    public String getGbjBuildModel() {
        return gbjBuildModel;
    }

    public void setGbjBuildModel(String gbjBuildModel) {
        this.gbjBuildModel = gbjBuildModel;
    }

    public String getGbjBuildCompany() {
        return gbjBuildCompany;
    }

    public void setGbjBuildCompany(String gbjBuildCompany) {
        this.gbjBuildCompany = gbjBuildCompany;
    }

    public String getGbjUpdateBrand() {
        return gbjUpdateBrand;
    }

    public void setGbjUpdateBrand(String gbjUpdateBrand) {
        this.gbjUpdateBrand = gbjUpdateBrand;
    }

    public String getGbjUpdateModel() {
        return gbjUpdateModel;
    }

    public void setGbjUpdateModel(String gbjUpdateModel) {
        this.gbjUpdateModel = gbjUpdateModel;
    }

    public String getGbjUpdateCompany() {
        return gbjUpdateCompany;
    }

    public void setGbjUpdateCompany(String gbjUpdateCompany) {
        this.gbjUpdateCompany = gbjUpdateCompany;
    }

    public String getGbjUpdateTm() {
        return gbjUpdateTm;
    }

    public void setGbjUpdateTm(String gbjUpdateTm) {
        this.gbjUpdateTm = gbjUpdateTm;
    }

    public String getBjqBuildBrand() {
        return bjqBuildBrand;
    }

    public void setBjqBuildBrand(String bjqBuildBrand) {
        this.bjqBuildBrand = bjqBuildBrand;
    }

    public String getBjqBuildModel() {
        return bjqBuildModel;
    }

    public void setBjqBuildModel(String bjqBuildModel) {
        this.bjqBuildModel = bjqBuildModel;
    }

    public String getBjqBuildCompany() {
        return bjqBuildCompany;
    }

    public void setBjqBuildCompany(String bjqBuildCompany) {
        this.bjqBuildCompany = bjqBuildCompany;
    }

    public String getBjqUpdateBrand() {
        return bjqUpdateBrand;
    }

    public void setBjqUpdateBrand(String bjqUpdateBrand) {
        this.bjqUpdateBrand = bjqUpdateBrand;
    }

    public String getBjqUpdateModel() {
        return bjqUpdateModel;
    }

    public void setBjqUpdateModel(String bjqUpdateModel) {
        this.bjqUpdateModel = bjqUpdateModel;
    }

    public String getBjqUpdateCompany() {
        return bjqUpdateCompany;
    }

    public void setBjqUpdateCompany(String bjqUpdateCompany) {
        this.bjqUpdateCompany = bjqUpdateCompany;
    }

    public String getBjqUpdateTm() {
        return bjqUpdateTm;
    }

    public void setBjqUpdateTm(String bjqUpdateTm) {
        this.bjqUpdateTm = bjqUpdateTm;
    }

    public String getShg() {
        return shg;
    }

    public void setShg(String shg) {
        this.shg = shg;
    }

    public String getSxtBuildBrand() {
        return sxtBuildBrand;
    }

    public void setSxtBuildBrand(String sxtBuildBrand) {
        this.sxtBuildBrand = sxtBuildBrand;
    }

    public String getSxtBuildModel() {
        return sxtBuildModel;
    }

    public void setSxtBuildModel(String sxtBuildModel) {
        this.sxtBuildModel = sxtBuildModel;
    }

    public String getSxtBuildCompany() {
        return sxtBuildCompany;
    }

    public void setSxtBuildCompany(String sxtBuildCompany) {
        this.sxtBuildCompany = sxtBuildCompany;
    }

    public String getSxtUpdateBrand() {
        return sxtUpdateBrand;
    }

    public void setSxtUpdateBrand(String sxtUpdateBrand) {
        this.sxtUpdateBrand = sxtUpdateBrand;
    }

    public String getSxtUpdateModel() {
        return sxtUpdateModel;
    }

    public void setSxtUpdateModel(String sxtUpdateModel) {
        this.sxtUpdateModel = sxtUpdateModel;
    }

    public String getSxtUpdateCompany() {
        return sxtUpdateCompany;
    }

    public void setSxtUpdateCompany(String sxtUpdateCompany) {
        this.sxtUpdateCompany = sxtUpdateCompany;
    }

    public String getSxtUpdateTm() {
        return sxtUpdateTm;
    }

    public void setSxtUpdateTm(String sxtUpdateTm) {
        this.sxtUpdateTm = sxtUpdateTm;
    }

    public String getLyqBuildBrand() {
        return lyqBuildBrand;
    }

    public void setLyqBuildBrand(String lyqBuildBrand) {
        this.lyqBuildBrand = lyqBuildBrand;
    }

    public String getLyqBuildModel() {
        return lyqBuildModel;
    }

    public void setLyqBuildModel(String lyqBuildModel) {
        this.lyqBuildModel = lyqBuildModel;
    }

    public String getLyqBuildCompany() {
        return lyqBuildCompany;
    }

    public void setLyqBuildCompany(String lyqBuildCompany) {
        this.lyqBuildCompany = lyqBuildCompany;
    }

    public String getLyqUpdateBrand() {
        return lyqUpdateBrand;
    }

    public void setLyqUpdateBrand(String lyqUpdateBrand) {
        this.lyqUpdateBrand = lyqUpdateBrand;
    }

    public String getLyqUpdateModel() {
        return lyqUpdateModel;
    }

    public void setLyqUpdateModel(String lyqUpdateModel) {
        this.lyqUpdateModel = lyqUpdateModel;
    }

    public String getLyqUpdateCompany() {
        return lyqUpdateCompany;
    }

    public void setLyqUpdateCompany(String lyqUpdateCompany) {
        this.lyqUpdateCompany = lyqUpdateCompany;
    }

    public String getLyqUpdateTm() {
        return lyqUpdateTm;
    }

    public void setLyqUpdateTm(String lyqUpdateTm) {
        this.lyqUpdateTm = lyqUpdateTm;
    }

    public String getIsSd() {
        return isSd;
    }

    public void setIsSd(String isSd) {
        this.isSd = isSd;
    }

    public String getIsKd() {
        return isKd;
    }

    public void setIsKd(String isKd) {
        this.isKd = isKd;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "BsnAutoRainstation{" +
        "id=" + id +
        ", adcd=" + adcd +
        ", code=" + code +
        ", projectName=" + projectName +
        ", createTm=" + createTm +
        ", county=" + county +
        ", xzc=" + xzc +
        ", tz=" + tz +
        ", cts=" + cts +
        ", hs=" + hs +
        ", people=" + people +
        ", river=" + river +
        ", hnm=" + hnm +
        ", lttd=" + lttd +
        ", ldtd=" + ldtd +
        ", company=" + company +
        ", rtuBuildBrand=" + rtuBuildBrand +
        ", rtuBuildModel=" + rtuBuildModel +
        ", rtuBuildCompany=" + rtuBuildCompany +
        ", rtuUpdateBrand=" + rtuUpdateBrand +
        ", rtuUpdateModel=" + rtuUpdateModel +
        ", rtuUpdateCompany=" + rtuUpdateCompany +
        ", rtuUpdateTm=" + rtuUpdateTm +
        ", yltBuildBrand=" + yltBuildBrand +
        ", yltBuildModel=" + yltBuildModel +
        ", yltBuildCompany=" + yltBuildCompany +
        ", yltUpdateBrand=" + yltUpdateBrand +
        ", yltUpdateModel=" + yltUpdateModel +
        ", yltUpdateCompany=" + yltUpdateCompany +
        ", yltUpdateTm=" + yltUpdateTm +
        ", dcBuildBrand=" + dcBuildBrand +
        ", dcBuildModel=" + dcBuildModel +
        ", dcBuildType=" + dcBuildType +
        ", dcBuildCompany=" + dcBuildCompany +
        ", dcUpdateBrand=" + dcUpdateBrand +
        ", dcUpdateModel=" + dcUpdateModel +
        ", dcUpdateType=" + dcUpdateType +
        ", dcUpdateCompany=" + dcUpdateCompany +
        ", dcUpdateTm=" + dcUpdateTm +
        ", tm=" + tm +
        ", type=" + type +
        ", swjBuildBrand=" + swjBuildBrand +
        ", swjBuildModel=" + swjBuildModel +
        ", swjBuildCompany=" + swjBuildCompany +
        ", swjUpdateBrand=" + swjUpdateBrand +
        ", swjUpdateModel=" + swjUpdateModel +
        ", swjUpdateCompany=" + swjUpdateCompany +
        ", swjUpdateTm=" + swjUpdateTm +
        ", yjFzr=" + yjFzr +
        ", gbjBuildBrand=" + gbjBuildBrand +
        ", gbjBuildModel=" + gbjBuildModel +
        ", gbjBuildCompany=" + gbjBuildCompany +
        ", gbjUpdateBrand=" + gbjUpdateBrand +
        ", gbjUpdateModel=" + gbjUpdateModel +
        ", gbjUpdateCompany=" + gbjUpdateCompany +
        ", gbjUpdateTm=" + gbjUpdateTm +
        ", bjqBuildBrand=" + bjqBuildBrand +
        ", bjqBuildModel=" + bjqBuildModel +
        ", bjqBuildCompany=" + bjqBuildCompany +
        ", bjqUpdateBrand=" + bjqUpdateBrand +
        ", bjqUpdateModel=" + bjqUpdateModel +
        ", bjqUpdateCompany=" + bjqUpdateCompany +
        ", bjqUpdateTm=" + bjqUpdateTm +
        ", shg=" + shg +
        ", sxtBuildBrand=" + sxtBuildBrand +
        ", sxtBuildModel=" + sxtBuildModel +
        ", sxtBuildCompany=" + sxtBuildCompany +
        ", sxtUpdateBrand=" + sxtUpdateBrand +
        ", sxtUpdateModel=" + sxtUpdateModel +
        ", sxtUpdateCompany=" + sxtUpdateCompany +
        ", sxtUpdateTm=" + sxtUpdateTm +
        ", lyqBuildBrand=" + lyqBuildBrand +
        ", lyqBuildModel=" + lyqBuildModel +
        ", lyqBuildCompany=" + lyqBuildCompany +
        ", lyqUpdateBrand=" + lyqUpdateBrand +
        ", lyqUpdateModel=" + lyqUpdateModel +
        ", lyqUpdateCompany=" + lyqUpdateCompany +
        ", lyqUpdateTm=" + lyqUpdateTm +
        ", isSd=" + isSd +
        ", isKd=" + isKd +
        "}";
    }
}
