package com.huitu.cloud.api.fusion.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 人工通知配置
 */
@ApiModel(value = "人工通知配置")
public class FusionManualConf implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "叫应类型，注：0=全部，其他同叫应反馈表")
    @TableField(value = "CALL_TYPE")
    private String callType;
    @ApiModelProperty(value = "人工通知标识，注：1=启用、0=禁用")
    @TableField(value = "MANUAL_FLAG")
    private String manualFlag;
    @ApiModelProperty(value = "人工通知延时，注：人工通知的延迟时间，达到或超过该时间，开始人工通知")
    @TableField(value = "MANUAL_DELAY")
    private Short manualDelay;
    @ApiModelProperty(value = "人工通知期限，注：基于延迟后的时间，超过期限不再人工通知")
    @TableField(value = "MANUAL_EXPIRED")
    private Short manualExpired;

    public String getCallType() {
        return callType;
    }

    public void setCallType(String callType) {
        this.callType = callType;
    }

    public String getManualFlag() {
        return manualFlag;
    }

    public void setManualFlag(String manualFlag) {
        this.manualFlag = manualFlag;
    }

    public Short getManualDelay() {
        return manualDelay;
    }

    public void setManualDelay(Short manualDelay) {
        this.manualDelay = manualDelay;
    }

    public Short getManualExpired() {
        return manualExpired;
    }

    public void setManualExpired(Short manualExpired) {
        this.manualExpired = manualExpired;
    }
}
