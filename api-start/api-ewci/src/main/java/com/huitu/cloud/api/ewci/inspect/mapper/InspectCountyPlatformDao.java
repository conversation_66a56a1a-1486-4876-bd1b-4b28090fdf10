package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectCountyPlatform;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 责任人落实
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectCountyPlatformDao {

    /**
     * 判断是否添加 县级山洪灾害防御平台
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectCountyPlatform> getInspectCountyPlatformList(@Param("adcd") String adcd, @Param("year") String year);

    /**
     * 添加 县级山洪灾害防御平台
     *
     * @param entity
     * @return
     */
    int insertInspectCountyPlatform(BnsInspectCountyPlatform entity);

    /**
     * 修改 县级山洪灾害防御平台
     *
     * @param entity
     * @return
     */
    int updateInspectCountyPlatform(BnsInspectCountyPlatform entity);

    /**
     * 删除 县级山洪灾害防御平台
     *
     * @return
     */
    int deleteInspectCountyPlatform(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

