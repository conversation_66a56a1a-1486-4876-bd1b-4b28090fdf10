<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.hsfxt.rsvr.mapper.FxtRsvrDao">

    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getFxtRsvrList" resultType="com.huitu.cloud.api.hsfxt.rsvr.entity.KbRvsanalyG">
        SELECT  A.<PERSON>, A.CNM, A.ENNMCD, A.<PERSON>, A.<PERSON>, A.DMTPEL, A.DMTPST, A.BWBOTEL, A.BWBOTST, A.RSINT, A.RSNUM, A.DEMFILE, A.RCSHPFILE, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, A.<PERSON>, A<PERSON>,
                B.DMSTA<PERSON>, B.DSCD, B.RVNM, B.ENCL, B.LVBSLV, B.RSCCI, B.RSLG, B.RSLT,C.ENNM,ADNM
        FROM    KB_RVSANALY_G A LEFT JOIN KB_RSCMIN_B B ON A.ENNMCD=B.ENNMCD
                                LEFT JOIN KB_CPRNMSR_TEMP C ON A.ENNMCD=C.ENNMCD
                                LEFT JOIN  BSN_ADCD_B D ON B.DSCD=D.ADCD
        WHERE 1=1
        <if test="map.ad != null and map.ad != ''">
            AND LEFT(B.DSCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.name != null and map.name != ''">
            AND CHARINDEX(#{map.name}, C.ENNM)>0
        </if>
        ORDER BY A.CID
    </select>
    <select id="getFxtRsvrDmList" resultType="com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmInfo">
        SELECT A.RSCD,A.CDIST,B.Z,B.Q,B.VR
        FROM KB_RVSANALY_SECINFO_G A LEFT JOIN KB_RVSANALY_SECRW_B B ON A.CID=B.CID AND A.RSCD=B.RSCD
        WHERE A.CID=#{cid}
    </select>
    <select id="getFxtRsvrDmPic" resultType="com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmPic">
        SELECT A.CDIST,B.AMEL,C.Z
		FROM KB_RVSANALY_SECINFO_G A LEFT JOIN KB_RVSANALY_SECELEV_G B ON A.CID=B.CID AND A.RSCD=B.RSCD AND B.PTNO='1'
		                             LEFT JOIN KB_RVSANALY_SECRW_B C ON A.CID=C.CID AND A.RSCD=C.RSCD
		WHERE A.CID=#{cid}
    </select>

    <select id="getFxtRsvrListByStcd" resultType="com.huitu.cloud.api.hsfxt.rsvr.entity.KbRvsanalyG">
        SELECT  A.CID, A.CNM, A.ENNMCD, A.BWWIDTH, A.BWDEPTH, A.DMTPEL, A.DMTPST, A.BWBOTEL, A.BWBOTST, A.RSINT, A.RSNUM, A.DEMFILE, A.RCSHPFILE, A.RSSHPFILE, A.RSIMGFILE, A.RSDEMFILE, A.EMINX, A.EMINY, A.EMAXX, A.EMAXY, A.RSAREA, A.BWQ, A.RM, A.TM,
                B.DMSTATPL, B.DSCD, B.RVNM, B.ENCL, B.LVBSLV, B.RSCCI, B.RSLG, B.RSLT,C.ENNM,ADNM
        from KB_RVSANALY_G A inner join (select b.objcd from bsn_objonly_b a inner join bsn_objonly_b b on a.objid = b.objid and b.objtp = '30'
              where a.objcd = #{stcd}
            )t2 on A.Ennmcd = t2.objcd
            LEFT JOIN KB_RSCMIN_B B ON A.ENNMCD=B.ENNMCD
            LEFT JOIN KB_CPRNMSR_TEMP C ON A.ENNMCD=C.ENNMCD
            LEFT JOIN  BSN_ADCD_B D ON B.DSCD=D.ADCD
    </select>
    <select id="getFzqRsvrListByStcd" resultType="com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrObjinfo">
        SELECT A.objnms,
               A.objid,
               A.PTCOUNT,
               A.ETCOUNT,
               A.ADCD,
               P1.REALNM XZREALNM,
               P1.MOBILE XZMOBILE,
               P2.REALNM CTREALNM,
               P2.MOBILE CTMOBILE
        FROM (select B.ADCD, b.adnm objnms, a.objid, b.PCOUNT PTCOUNT, b.HTCOUNT ETCOUNT
              from bsn_objonly_b a
                       left join IA_C_ADINFO b on a.objcd = b.ADCD
                       left join bsn_objonly_b d on a.objid = d.objid
              where a.objtp = '31'
                and d.objcd = #{stcd}) A
                 LEFT JOIN BNS_VILLAGEPERSON_B P1 ON A.ADCD = P1.ADCD AND P1.PERTP = '1'
                 LEFT JOIN BNS_VILLAGEPERSON_B P2 ON A.ADCD = P2.ADCD AND P2.PERTP = '2'
    </select>
</mapper>