package com.huitu.cloud.api.datamanager.rule.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
@ApiModel(value = "ZrruleVo",description = "水位规则查询结果类")
public class ZrruleVo extends BsnZrruleb {
    @ApiModelProperty(value = "测站名称")
    private String stnm;

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }
}
