package com.huitu.cloud.api.syq.warn.service;

import com.huitu.cloud.api.syq.warn.entity.*;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SyqWarnService {
    /**
     * 查询预警的
     * @param adcd 政区
     * @param stm
     * @param etm
     * @return
     */
    WarnCountVo  getWarnCount(String adcd,String stm,String etm);

    WarnInfoVo getWarnCountAndInfo(String adcd, String stm,String stms, String etm, String rsvrStType); // stms:针对app河道修改
    /**
     * 生成强降雨告警通告
     * @param query 通告类
     * @return
     */
    Notice crezateHeavyRainfallNotice(QueryNotice query);
    /**
     * 强降雨告警通告期数查询
     *
     * @return
     */
    int getHeavyRainfallNoticeNo();

    /**
     * 发送强降雨文件后标记备注
     * @param stcd 测站名称
     * @param rightFillStr 备注填充 [1]或[2]
     * @return
     */
    Boolean signHeavyRainfallNotice(String stcd, String rightFillStr);

    /**
     * 风险预警
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    RiskWarningsVo getRisks(String adcd, String stm, String etm);

    /**
     * 风险预警-村庄
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<EwVillageVo> getRiskVillage(String adcd, String stm, String etm);

    /**
     * 风险预警-河道
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<EwRivelVo> getRiskRiver(String adcd, String stm, String etm);

    /**
     * 风险预警-水库
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<EwReserVo> getRiskRsvr(String adcd, String stm, String etm);

    /**
     * 未来一小时风险预警
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<EarlyWarningVo> getRiskEarly1(String adcd, String stm, String etm, String type);

    /**
     * 未来三小时风险预警
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<EarlyWarningVo> getRiskEarly3(String adcd, String stm, String etm, String type);

    /**
     * 未来六小时风险预警
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<EarlyWarningVo> getRiskEarly6(String adcd, String stm, String etm, String type);

    /**
     * 未来一三六小时风险预警
     * @param adcd
     * @param stm
     * @param etm
     * @param tmType 未来几小时：1：一小时 3：三小时 6：六小时
     * @param riskType 风险类型。1：村庄 2：水库 3：河道
     * @return
     */
    List<EarlyWarningVo> getRiskEarly(String adcd, String stm, String etm, String tmType, String riskType);

    /**
     * 山洪快报文件生成
     * @param adcd
     * @param stm
     * @param etm
     * @throws Exception
     */
    void generateShNews(String adcd, String stm, String etm, String orgfilename,String orgfileurl) throws Exception;
}
