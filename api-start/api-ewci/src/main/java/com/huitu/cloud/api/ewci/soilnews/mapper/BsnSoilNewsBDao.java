package com.huitu.cloud.api.ewci.soilnews.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.soilnews.entity.BsnSoilNewsB;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 墒情专报Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface BsnSoilNewsBDao {

    /**
     * 获取墒情专报信息列表
     *
     * @param params 查询条件
     * @return 墒情专报信息列表
     **/
    IPage<BsnSoilNewsB> getSoilNewsList(Page page, @Param("map") Map<String, Object> params);

}
