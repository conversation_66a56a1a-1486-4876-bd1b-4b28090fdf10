package com.huitu.cloud.api.datamanager.anmrecord.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmStatistics;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmVo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.BsnAnmrecordlogr;
import com.huitu.cloud.api.datamanager.anmrecord.entity.BsnAnmrecordr;
import com.huitu.cloud.api.datamanager.anmrecord.entity.RainStInfo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.River;
import com.huitu.cloud.api.datamanager.anmrecord.entity.Rsvr;
import com.huitu.cloud.api.datamanager.anmrecord.entity.RzStInfo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.StSwVo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.ZrStInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 异常数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-07
 */
public interface AnmrecordDao extends BaseMapper<BsnAnmrecordr> {
    /**
     * 查询最新异常记录信息
     *
     * @param anmtp
     * @param staflgs
     * @param stm
     * @param etm
     * @param filterCnt
     * @return
     */
    List<AnmVo> getBsnAnmrecordr(@Param("anmtp") String anmtp, @Param("staflgs") String staflgs, @Param("stm") String stm, @Param("etm") String etm, @Param("filterCnt") Integer filterCnt);

    /**
     * 查询异常雨量站关联测站信息以及规则信息
     *
     * @param param
     * @return
     */
    List<RainStInfo> getRainStInfo(Map<String, Object> param);

    /**
     * 查询异常水位关联测站信息以及规则信息
     *
     * @param param
     * @return
     */
    List<ZrStInfo> getZrStInfo(Map<String, Object> param);

    /**
     * 插入雨量数据
     *
     * @param data
     * @return
     */
    int insertRain(Map<String, Object> data);

    /**
     * 插入河道水位数据
     *
     * @param data
     * @return
     */
    int insertRiver(River data);

    /**
     * 插入水库水位数据
     *
     * @param data
     * @return
     */
    int insertRsvr(Rsvr data);

    /**
     * 插入异常数据操作日志信息
     *
     * @param bsnAnmrecordlogr
     * @return
     */
    int insertAnmLog(BsnAnmrecordlogr bsnAnmrecordlogr);

    /**
     * 根据测站编码查询河道最新5条最新水位
     *
     * @param stcd
     * @return
     */
    List<StSwVo> getRiverSw(@Param("stcd") String stcd);

    /**
     * 根据测站编码查询水库最新5条最新水位
     *
     * @param stcd
     * @return
     */
    List<StSwVo> getRsvrSw(@Param("stcd") String stcd);

    /**
     * 某个时间点的政区平均降雨
     *
     * @param adcd 政区编码
     * @param tm
     * @return
     */
    String getAdAvg(@Param("adcd") String adcd, @Param("tm") String tm);

    /**
     * 根据测站批量忽略异常数据
     * @return
     */
    /**
     * 根据测站与异常数据类型批量忽略异常数据
     *
     * @param stcd
     * @param anmtp
     * @return
     */
    int updateAnmrecordrStaflg(@Param("stcd") String stcd, @Param("anmtp") String anmtp);

    /**
     * 根据测站编码查询河道48小时内最新1条最新水位
     *
     * @param stcd
     * @return
     */
    StSwVo getRiverSwByTm(@Param("stcd") String stcd, @Param("tm") Date tm);

    /**
     * 根据测站编码查询水库8小时内最新1条最新水位
     *
     * @param stcd
     * @return
     */
    StSwVo getRsvrSwByTm(@Param("stcd") String stcd, @Param("tm") Date tm);

    IPage<AnmVo> getAnmrecordrByPage(Page page, @Param("map") Map<String, Object> map);

    IPage<AnmVo> getAnmrecordrStcdByPage(Page page, @Param("map") Map<String, Object> map);

    /**
     * 查询异常站点信息
     *
     * @param map
     * @return
     */
    List<AnmVo> getAnmrecordrByExpt(@Param("map") Map<String, Object> map);

    /**
     * 查询政区的异常数据的数量
     *
     * @param param
     * @return
     */
    int getCountByAd(Map<String, Object> param);

    /**
     * 根据政区分页查询水位异常站点列表
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return
     */
    IPage<RzStInfo> getAnmRzSt(Page page, @Param("map") Map<String, Object> param);

    /**
     * 根据测站编码和表名查询异常数据记录
     *
     * @param stcd 测站编码
     * @param tbnm 表名
     * @return
     */
    List<BsnAnmrecordr> getAnmrecordrs(@Param("stcd") String stcd, @Param("tbnm") String tbnm);

    /**
     * 删除临时表
     *
     * @param tableName 表名
     * @return
     */
    @Update({"IF OBJECT_ID(N'tempdb..[${tableName}]') is not null begin drop  table  ${tableName} end"})
    void dropTemoraryTable(@Param("tableName") String tableName);

    /**
     * 创建临时表
     *
     * @param tableName 表名
     * @return
     */
    @Update({"IF OBJECT_ID(N'tempdb..[${tableName}]') is not null drop  table  ${tableName}  select * into ${tableName} from BSN_ANMRECORD_R where 1=2"})
    void createTemoraryTable(@Param("tableName") String tableName);

    /**
     * 数据批量插入临时表
     *
     * @param tableName 表名
     * @param list      插入数据集合
     * @return
     */
    void batchInsertBsnAnmrecordr(@Param("list") List<BsnAnmrecordr> list, @Param("tableName") String tableName);

    void batchUpdateBsnAnmrecordr(@Param("list") List<BsnAnmrecordr> list);

    /**
     * 临时表的数据更新至原表
     *
     * @param tableName 表名
     * @return
     */
    void updateBsnAnmrecordr(@Param("tableName") String tableName);

    /**
     * 异常数据批量插入日志表
     *
     * @param list 日志数据集合
     * @return
     */
    void batchInsertAnmLog(@Param("list") List<BsnAnmrecordlogr> list);

    /**
     * 数据批量插入雨情表
     *
     * @param list 雨情数据集合
     * @return
     */
    void batchInsertRain(@Param("list") List<Map<String, Object>> list);

    /**
     * 数据批量插入河道水清表
     *
     * @param list 雨情数据集合
     * @return
     */
    void batchInsertRiver(@Param("list") List<River> list);

    /**
     * 数据批量插入水库水清表
     *
     * @param list 雨情数据集合
     * @return
     */
    void batchInsertRsvr(@Param("list") List<Rsvr> list);

    /**
     * 获取异常站数列表
     *
     * @param adcd  政区编码
     * @param bgtm  开始时间
     * @param endtm 结束时间
     * @return java.util.List<com.huitu.cloud.api.datamanager.anmrecord.entity.AnmStatistics>
     **/
    List<AnmStatistics> getAnmStatisticsList(@Param("adcd") String adcd, @Param("bgtm") Date bgtm, @Param("endtm") Date endtm);
}
