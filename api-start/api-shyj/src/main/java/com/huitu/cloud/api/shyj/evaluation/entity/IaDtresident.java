package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 重要城（集）镇居民调查成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-18
 */
@TableName("IA_C_DTRESIDENT")
@ApiModel(value="IaCDtresident对象", description="重要城（集）镇居民调查成果表")
public class IaDtresident extends  RelevantInfo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "重要城（集）镇居民户编码")
    @TableId(value = "IURCD", type = IdType.NONE)
    private String iurcd;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "城（集）镇名称")
    @TableField("VNAME")
    private String vname;

    @ApiModelProperty(value = "基准点经度")
    @TableField("BLGTD")
    private Double blgtd;

    @ApiModelProperty(value = "基准点纬度")
    @TableField("BLTTD")
    private Double blttd;

    @ApiModelProperty(value = "基准点高程（m）")
    @TableField("BELE")
    private Double bele;

    @ApiModelProperty(value = "地址（门牌号）")
    @TableField("ADDRESS")
    private String address;

    @ApiModelProperty(value = "楼房号")
    @TableField("BCODE")
    private String bcode;

    @ApiModelProperty(value = "户数（户）")
    @TableField("HTCOUNT")
    private Double htcount;

    @ApiModelProperty(value = "总人数（人）")
    @TableField("PTCOUNT")
    private Double ptcount;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private Double lttd;

    @ApiModelProperty(value = "宅基高程（m）")
    @TableField("HELE")
    private Double hele;

    @ApiModelProperty(value = "建筑面积（m2）")
    @TableField("AREA")
    private Double area;

    @ApiModelProperty(value = "临水")
    @TableField("BWATER")
    private String bwater;

    @ApiModelProperty(value = "切坡")
    @TableField("BHILL")
    private String bhill;

    @ApiModelProperty(value = "建筑类型")
    @TableField("BTYPE")
    private String btype;

    @ApiModelProperty(value = "结构形式")
    @TableField("STYPE")
    private String stype;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getIurcd() {
        return iurcd;
    }

    public void setIurcd(String iurcd) {
        this.iurcd = iurcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getVname() {
        return vname;
    }

    public void setVname(String vname) {
        this.vname = vname;
    }

    public Double getBlgtd() {
        return blgtd;
    }

    public void setBlgtd(Double blgtd) {
        this.blgtd = blgtd;
    }

    public Double getBlttd() {
        return blttd;
    }

    public void setBlttd(Double blttd) {
        this.blttd = blttd;
    }

    public Double getBele() {
        return bele;
    }

    public void setBele(Double bele) {
        this.bele = bele;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBcode() {
        return bcode;
    }

    public void setBcode(String bcode) {
        this.bcode = bcode;
    }

    public Double getHtcount() {
        return htcount;
    }

    public void setHtcount(Double htcount) {
        this.htcount = htcount;
    }

    public Double getPtcount() {
        return ptcount;
    }

    public void setPtcount(Double ptcount) {
        this.ptcount = ptcount;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public Double getHele() {
        return hele;
    }

    public void setHele(Double hele) {
        this.hele = hele;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getBwater() {
        return bwater;
    }

    public void setBwater(String bwater) {
        this.bwater = bwater;
    }

    public String getBhill() {
        return bhill;
    }

    public void setBhill(String bhill) {
        this.bhill = bhill;
    }

    public String getBtype() {
        return btype;
    }

    public void setBtype(String btype) {
        this.btype = btype;
    }

    public String getStype() {
        return stype;
    }

    public void setStype(String stype) {
        this.stype = stype;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }


}
