<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.shyj.person.mapper.PersonDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <update id="delPerson">
        update PERSON_B set ISDEL='1' where PERSONCD=#{personcd}
    </update>


    <select id="getPersionListByPage" resultType="com.huitu.cloud.api.shyj.person.entity.RelevantPersionInfo">
        select A.personcd, name, A.deptcd, ishead, A.duty, sex, company, position, adress, mobile, officetel, email, A.remark, A.sortno,A.type, B.deptNm,b.adcd,AD.adnm
        from PERSON_B  A left join DEPT_B  B ON A.DeptCD=B.DeptCD
        left join BSN_ADCD_B AD ON AD.ADCD=B.ADCD
        WHERE  1=1 and isnull(a.isdel,0) !='1'
        <if test="map.adcd !=null and map.adcd !=''" >
            and  b.ADCD=#{map.adcd}
        </if>
        <if test="map.name !=null and map.name !=''" >
            and   CHARINDEX(#{map.name},name) >0
        </if>
        <if test="map.type !=null and map.type !=''" >
            and a.type=#{map.type}
        </if>
        <if test="map.depts !=null and map.depts.size() >0" >
            and A.deptcd in
            <foreach collection="map.depts" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="map.mobile !=null and map.mobile !=''" >
            and a.mobile=#{map.mobile}
        </if>
        order by A.deptcd,A.personcd
    </select>

    <select id="getPersionList" resultType="com.huitu.cloud.api.shyj.person.entity.RelevantPersionInfo">
        select A.personcd, name, A.deptcd, ishead, A.duty, sex, company, position, adress, mobile, officetel, email, A.remark, A.sortno,A.type, B.deptNm,b.adcd,AD.adnm
        from PERSON_B  A left join DEPT_B  B ON A.DeptCD=B.DeptCD
        left join AD_CD_B AD ON AD.ADCD=B.ADCD
        WHERE  1=1 and isnull(a.isdel,0) !='1'
        <if test="adcd !=null and adcd !=''" >
            and  b.ADCD=#{adcd}
        </if>
        <if test="name !=null and name !=''" >
            and CHARINDEX(#{name},name) >0
        </if>
        <if test="type !=null and type !=''" >
            and a.type=#{type}
        </if>
        <if test="depts !=null and depts.size() >0" >
            and A.deptcd in
            <foreach collection="depts" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="checkNm !=null and checkNm !=''" >
            and a.name=#{checkNm}
        </if>
        <if test="mobile !=null and mobile !=''" >
            and a.mobile=#{mobile}
        </if>
        <if test="deptcd !=null and deptcd !=''" >
            and a.deptcd=#{deptcd}
        </if>
        order by A.personcd
    </select>
    <select id="selectMaxId" resultType="com.huitu.cloud.api.shyj.person.entity.Person">
        select convert(varchar,(isnull(max(cast(personcd as int)),0)+1)) personcd
        from PERSON_B
    </select>


</mapper>