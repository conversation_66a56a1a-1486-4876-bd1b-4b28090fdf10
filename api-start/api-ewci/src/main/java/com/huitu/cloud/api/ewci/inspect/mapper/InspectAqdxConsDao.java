package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectAqdxCons;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 安全度汛工程建设
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectAqdxConsDao {

    /**
     * 判断是否添加 安全度汛工程建设
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectAqdxCons> getInspectAqdxConsList(@Param("adcd") String adcd, @Param("year") String year, @Param("enCode") String enCode);

    /**
     * 添加 安全度汛工程建设
     *
     * @param entity
     * @return
     */
    int insertInspectAqdxCons(BnsInspectAqdxCons entity);

    /**
     * 修改 安全度汛工程建设
     *
     * @param entity
     * @return
     */
    int updateInspectAqdxCons(BnsInspectAqdxCons entity);

    /**
     * 删除 安全度汛工程建设
     *
     * @return
     */
    int deleteInspectAqdxCons(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

