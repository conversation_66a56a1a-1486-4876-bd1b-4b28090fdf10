package com.huitu.cloud.api.syq.ad.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 政区基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */

public class AdcdB implements Serializable {

    private String adcd;


    private String adnm;


    private String padcd;

    private BigDecimal lgtd;


    private BigDecimal lttd;


    private String adlvl;


    private List<AdcdB> children;


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getAdlvl() {
        return adlvl;
    }

    public void setAdlvl(String adlvl) {
        this.adlvl = adlvl;
    }


    public void setChildren(List<AdcdB> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return "BsnAdcdB{" +
        "adcd=" + adcd +
        ", adnm=" + adnm +
        ", padcd=" + padcd +
        ", lgtd=" + lgtd +
        ", lttd=" + lttd +
        ", adlvl=" + adlvl +
        "}";
    }
}
