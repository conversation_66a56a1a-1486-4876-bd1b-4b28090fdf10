package com.huitu.cloud.api.syq.rsvr.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.syq.river.entity.RiverZuoZhanTuVo;
import com.huitu.cloud.api.syq.rsvr.entity.*;
import com.huitu.cloud.api.syq.skdt.entity.BsnSkdtRvEx;
import com.huitu.cloud.api.xxjh.smallreser.entity.BsnRsvrrecorderfileR;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 水库水情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-09
 */
public interface RsvrDao extends BaseMapper<StRsvrR> {
    /**
     * 最新水库水情
     *
     * @param page 分页对象
     * @param map  查询条件集合
     * @return
     */
    IPage<StRsvrVo> getLatestCondition(Page page, @Param("map") Map<String, Object> map);

    /**
     * 存储过程生成最新水库水情数据
     *
     * @param stm 开始时间
     * @param etm 结束时间
     */
    IPage<StRsvrVo> getRsvrLatestData(Page page, @Param("stm") String stm, @Param("etm") String etm, @Param("whereSql") String whereSql);

    /**
     * 查询实时水库水位列表
     *
     * @param map
     * @return
     */

    List<StRsvrTmVo> getTmList(Map<String, Object> map);

    /**
     * 根据测站编码查询 库（湖）容曲线表用于存储水库（湖）的施策时间
     *
     * @param stcd 测站编码
     * @return
     */
    List<String> getStZvarLbByStcd(String stcd);

    /**
     * 根据测站编码以及施策时间 查询 库（湖）容曲线表用于存储水库（湖）的List
     *
     * @param map 查询条件集合
     * @return
     */
    List<StZvarlB> getStZvarLbByStcdAndMstm(@Param("map") Map<String, Object> map);

    /**
     * 查询 库（湖）容曲线表用于存储水库（湖）的List
     *
     * @return
     */
    List<StZvarlB> getStZvarLbLastList();

    /**
     * 查询单站水库防洪指标
     *
     * @param stcd
     * @return
     */
    StRsvrfcchB selectStRsvrfcchbByStcd(String stcd);

    /**
     * 查询单站水库汛限水位列表信息
     *
     * @param stcd
     * @return
     */
    List<StRsvrfsrB> selectStRsvrfsrbByStcd(String stcd);


    /**
     * 多站水库水情预报
     *
     * @param map
     * @param page
     * @return
     */
    IPage<StReglatfSqyb> getStReglaTbyInfo(Page page, @Param("map") Map<String, Object> map);

    /**
     * 分页查询一段时间内水库日旬月均值
     *
     * @param page
     * @param param
     * @return
     */
    IPage<RsvrDayAvg> getDayAvgRsvrByPage(Page page, @Param("map") Map<String, Object> param);

    /**
     * 单站水库水情预报
     *
     * @param stcd
     * @param ymdh
     * @return
     */
    List<StReglatfSqybd> getStReglaTbyAdcd(@Param("stcd") String stcd, @Param("ymdh") String ymdh);

    /**
     * 查询水库告警信息
     *
     * @param map
     * @return
     */
    List<StRsvrMax> getStRsvrVoOfMaxRzfsltdz(@Param("map") Map<String, Object> map);

    /**
     * 查询水库调度涉及水库的水库最新水库水情信息
     *
     * @param stm 开始时间
     * @param etm 结束时间
     * @return
     */
    List<BsnSkdtRvEx> getSkdtRsvrInfo(@Param("stm") String stm, @Param("etm") String etm, @Param("stcd") String stcd);

    /**
     * 获取水库单站的最新洪峰记录
     *
     * @param stcd
     * @return
     */
    StRsvrR getHfRsvr(@Param("stcd") String stcd);

    /**
     * 根据测站编码集合查询实时水库水情信息
     *
     * @param stcds
     * @param stm
     * @param etm
     * @return
     */
    List<StRsvrR> getRsvrTmListByStcds(@Param("stcds") List<String> stcds, @Param("stm") String stm, @Param("etm") String etm);

    /**
     * 查询水库关联对应关系表
     *
     * @return
     */
    List<BsnObjonlyB> getBsnObjOnly();

    /**
     * 查询水库关联对应关系表
     *
     * @return
     */
    List<BsnObjonlyB> getBsnRsvrObjOnly();

    /**
     * 查询水库工程信息
     *
     * @return
     */
    List<ResBase> getAttResBase();

    /**
     * 根据多个测站字符串查询小水库风险图和调查评价数据
     *
     * @param stcdStr 要查的多个测站字符串。如：10814603,10813282,10813285
     * @return
     */
    List<ObjOnlyIACAdinfo> getObjOnlyIACAdinfoByStcds(@Param("stcds") String stcdStr);

    /**
     * 根据测站字符串查询小水库风险图和调查评价数据
     *
     * @param stcd 测站名称
     * @return
     */
    List<ObjOnlyIACAdinfo> getObjOnlyIACAdinfoByStcd(@Param("stcd") String stcd);

    /**
     * 查询超汛限站点时段内最大入库流量数据
     *
     * @param map
     * @return
     */
    List<StRsvrMax> getStRsvrVoOfMaxInqfsltdz(@Param("map") Map<String, Object> map);

    /**
     * 查询超汛限站点时段内最大出库流量数据
     *
     * @param map
     * @return
     */
    List<StRsvrMax> getStRsvrVoOfMaxOtqfsltdz(@Param("map") Map<String, Object> map);

    /**
     * 水库水情查看
     *
     * @param param
     * @return
     */
    List<ReservoirWaterinfo> getReservoirWaterRegimePage(@Param("map") Map<String, Object> param);

    /**
     * 水库水情查看查询照片
     *
     * @param tm   时间
     * @param stcd 测站编码
     * @return
     */
    List<ReservoirWaterinfo> getReservoirWaterRegimePhoto(@Param("tm") String tm, @Param("stcd") String stcd);

    /**
     * 水库水情查看照片对比
     *
     * @param stcd 测站编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    List<BsnRsvrrecorderfileR> getPhotoComparison(@Param("stcd") String stcd, @Param("stm") String stm, @Param("etm") String etm);

    List<RsvrContinueAlermVo> getRsvrContinueAlerm(@Param("ad") String ad, @Param("adlevel") int adlevel, @Param("stm") String stm, @Param("etm") String etm, @Param("stTypes") String stTypes, @Param("rvTypes") String rvTypes);

    /**
     * 统计政区下时间段内测站超警戒情况
     *
     * @param param
     * @return
     */
    List<RsvrStWarnByAdcdTmVo> selectWarnStatisticsByAdcdTm(@Param("map") Map<String, Object> param);

    /**
     * 多站水库水情最新预报
     *
     * @param param
     * @return
     */
    List<StReglatfSqyb> getForecastLatestTime(@Param("map") Map<String, Object> param);

    /**
     * 纳雨能力分析
     * @param resCode 水库工程编码
     * @return
     */
    List<ReservoirRain> getContainRainList(@Param("resCode") String resCode);

    /**
     * 查询测站对应水库集水面积范围内测站的平均雨量
     * @param param
     * @return
     */
    List<RsvrAvgDrnaVo> getRsvrAvgDrnaByStcd(@Param("map") Map<String, Object> param);

    /**
     * 水库集水面积内测站降雨列表
     * @param param
     * @return
     */
    List<RsvrRainList> getStRsvrRainBaseList(@Param("map") Map<String, Object> param);

    /**
     * 水库集水面积内测站降雨列表
     * @param param
     * @return
     */
    List<RsvrRainList> getStRsvrRainList(@Param("map") Map<String, Object> param);

    /**
     * 水库集水面积内测站降雨列表
     * @param param
     * @return
     */
    List<RsvrRainList> getStRsvrRcList(@Param("map") Map<String, Object> param);

    /**
     * 水库集水面积内测站降雨子集列表
     * @param param
     * @return
     */
    List<RsvrRainChildList> getStRsvrRainChildList(@Param("map") Map<String, Object> param);

    /**
     * 告警-临界告警
     *
     * @param param
     * @return
     */
    List<RsvrNearWarnVo> selectNearWarnTemp(@Param("map") Map<String, Object> param);

    /**
     * 防洪作战图批量查询水库水位
     * @param page
     * @param param
     * @return
     */
    IPage<RsvrZuoZhanTuVo> getRsvrZuoZhanTuList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 防洪作战图根据水库测站编码查询水库信息
     * @param param
     * @return
     */
    RsvrZuoZhanTuVo getRsvrZuoZhanTu(@Param("map") Map<String, Object> param);

}
