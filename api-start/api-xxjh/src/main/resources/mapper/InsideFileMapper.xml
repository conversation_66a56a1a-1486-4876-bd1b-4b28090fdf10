<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.xxjh.insidefile.mapper.InsideFileDao">
<!--    <cache type="com.huitu.cloud.config.RedisCache"/>-->
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.xxjh.insidefile.entity.BnsFilesR">
        <id column="filecd" property="filecd" />
        <result column="filetitle" property="filetitle" />
        <result column="fileno" property="fileno" />
        <result column="filetype" property="filetype" />
        <result column="fileflg" property="fileflg" />
        <result column="ctm" property="ctm" />
        <result column="cer" property="cer" />
        <result column="cher" property="cher" />
        <result column="nt" property="nt" />
        <result column="adcd" property="adcd" />
        <result column="filedesc" property="filedesc" />
        <result column="ser" property="ser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        filecd, filetitle, fileno, filetype, fileflg, ctm, cer, cher, nt, adcd, filedesc, ser,senddept
    </sql>
    <insert id="insertBnsFileurlsr">
        insert into BNS_FILEURLS_R (  filecd, fileurl, filenewnm)
        values
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (#{item.filecd},#{item.fileurl},#{item.filenewnm})
        </foreach>
    </insert>
    <insert id="insertBnsFilegetsendr">
        insert into BNS_FILEGETSEND_R (  filecd, stm, ser,reer,retm,filesta,wser,wreer,adcd)
        values
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (#{item.filecd},#{item.stm}, #{item.ser},#{item.reer},#{item.retm},#{item.filesta},#{item.wser},#{item.wreer},#{item.adcd})
        </foreach>
    </insert>
    <insert id="insertWeather" parameterType="java.util.Map" >
         insert into BSN_QXDESC_R(recid, tm, qxdesc, picurl, moditime)
        values (#{recid}, #{tm}, #{qxdesc}, #{picurl}, getdate())
    </insert>
    <insert id="insertMsgInfo">
        insert into MESSAGEINFO_R (msgId,WarnID,MsgTypeID,Msgcontent,SENDER,MEDIAID,SENDTM,REMARK,SENDTYPE,ADCD) values
     (#{msgId},#{warnId},#{msgTypeId},#{msgContent},#{sender},#{mediaId},getdate(),#{remark,jdbcType=VARCHAR},#{sendType,jdbcType=VARCHAR},#{adcd,jdbcType=VARCHAR})
    </insert>
    <insert id="bathInsertMsgSend">
        insert into  MESSAGESEND_R(MSGID,CODE,SMSID,SID,UNAME,ObjectCD,OBJECTTYPE,SENDRESULT,RECTM,REMARK,ADCD,ISSEND) values
        <foreach collection="msg" item="item" index="index"  separator="," >
            (#{item.msgId},CONVERT(varchar(4),getdate(),120),#{item.smsId},#{item.sid},#{item.uname},#{item.msgId},10,#{item.sendResult,jdbcType=VARCHAR},getdate(),#{item.remark,jdbcType=VARCHAR},#{item.adcd,jdbcType=VARCHAR},'1')
        </foreach>
    </insert>

    <select id="getBnsFilesrByDispatch" resultType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsFileByDispatch">
        SELECT
            a.filecd,
            a.filetitle,
            a.fileno,
            a.filetype,
            a.fileflg,
            CONVERT (varchar(20), a.ctm,120) ctm,
            a.cer,
            a.cher,
            a.nt,
            a.adcd,
            a.filedesc,
            a.ser,
            a.senddept
        FROM
            BNS_FILES_R a
            where 1=1
        <if test="map.filetitle !=null and map.filetitle !=''">
            and CHARINDEX(#{map.filetitle},a.filetitle)>0
        </if>
        <if test="map.filetype !=null and map.filetype !=''">
            and a.filetype=#{map.filetype}
        </if>
        <if test="map.fileno !=null and map.fileno !=''">
            and CHARINDEX(#{map.fileno} ,a.fileno)>0
        </if>
        <if test="map.ser !=null and map.ser !=''">
            and CHARINDEX(#{map.ser} ,a.ser)>0
        </if>
        <if test="map.stm !=null and map.stm !=''">
            and convert(varchar(16),a.ctm,120) >=#{map.stm}
        </if>
        <if test="map.etm !=null and map.etm !=''">
            and convert(varchar(16),a.ctm,120)&lt;=#{map.etm}
        </if>
        <if test="map.adcd !=null and map.adcd !=''">
            and a.adcd=#{map.adcd}
        </if>
        <if test=" map.deptId !=null and map.deptId !=''">
            and a.senddept = #{map.deptId}
        </if>
        <if test="map.reer!=null and map.reer.size() >0">
            and exists (select 1 from BNS_FILEGETSEND_R b where a.filecd=b.filecd and b.reer in
            <foreach item="item" index="index" collection="map.reer" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        order by a.ctm desc
    </select>
    <select id="getBnsFileBySgin" resultType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsFileBySgin">
        SELECT
            a.reer,
            b.deptnm,
            a.wreer,
            a.filesta,
            case when a.retm = '1900-01-01' then null else a.retm end as retm,
            CONVERT (varchar(20),a.stm,120) stm,
            a.filecd
        FROM
            BNS_FILEGETSEND_R a
        LEFT JOIN BNS_DEPTINFO_B b on a.reer=b.deptid
        where 1=1
        <if test="map.stm !=null and map.stm !=''">
            and a.stm>=#{map.stm}
        </if>
        <if test="map.etm !=null and map.etm !=''">
            and a.stm&lt;#{map.etm}
        </if>
        <if test="map.filecd !=null and map.filecd !=''">
            and  a.filecd=#{map.filecd}
        </if>
        <if test="map.tm !=null and map.tm !=''">
            and CONVERT (datetime,a.stm,120 )=CONVERT (datetime, #{map.tm},120)
        </if>
        <if test="map.fileList !=null and map.fileList.size() >0 " >
            and a.filecd IN
            <foreach item="item" index="index" collection="map.fileList" open="(" separator="," close=")">
                #{item.filecd}
            </foreach>
        </if>
    </select>
    <select id="getBnsFileByReceiver" resultType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsFileByReceiver">
        SELECT
            a.reer,
            a.wser,
            (select realnm from BNS_USERINFO_B where userid=a.wser) wserName ,
            b.filecd,
            b.filetitle,
            b.fileno,
            b.filetype,
            a.ser,
            a.stm,
            c.deptnm,
            case when a.retm = '1900-01-01' then null else a.retm end as retm,
            a.wreer,
            a.filesta,
            d.realnm wreerName
        FROM
            BNS_FILEGETSEND_R a
        LEFT JOIN BNS_FILES_R b ON a.filecd = b.filecd
        LEFT JOIN BNS_DEPTINFO_B c ON b.senddept=c.deptid
        LEFT JOIN BNS_USERINFO_B d ON a.wreer=d.userid
        where 1=1
        <if test="map.filetitle !=null and map.filetitle !=''">
            and CHARINDEX(#{map.filetitle},b.filetitle)>0
        </if>
        <if test="map.filetype !=null and map.filetype !='' ">
            and b.filetype=#{map.filetype}
        </if>
        <if test="map.fileno !=null and map.fileno !='' ">
            and CHARINDEX(#{map.fileno},b.fileno)>0
        </if>
        <if test="map.ser !=null and map.ser !='' ">
            and CHARINDEX(#{map.ser},a.ser)>0
        </if>
        <if test="map.stm !=null and map.stm !='' ">
            and CONVERT(datetime,stm) >= CONVERT(datetime, #{map.stm})
        </if>
        <if test="map.etm !=null and map.etm !='' ">
            and CONVERT(datetime,stm) &lt;= CONVERT(datetime, #{map.etm})
        </if>
        <if test="map.adcd !=null and map.adcd !=''">
            and a.adcd=#{map.adcd}
        </if>
        <if test="map.redept !=null and map.redept !=''">
            and a.reer=#{map.redept}
        </if>
        <if test="map.senddept !=null and map.senddept.size() >0 " >
           and b.senddept IN
            <foreach item="item" index="index" collection="map.senddept" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by a.stm desc
    </select>
    <select id="getBnsFilegetsendr" resultType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsFilegetsendR">
        SELECT filecd,
				stm,
				ser,
				reer,
                case when retm = '1900-01-01' then null else retm end as retm,
				filesta,
				wser,
				wreer,
				adcd
              FROM
	    BNS_FILEGETSEND_R where 1=1
        <if test="map.filecd !=null and map.filecd !=''">
            and filecd=#{map.filecd}
        </if>
        <if test="map.reer !=null and map.reer !=''">
            and reer=#{map.reer}
        </if>
        <if test="map.stm !=null and map.stm !=''">
            and CONVERT (datetime,stm,120)=CONVERT (datetime, #{map.stm},120)
        </if>
    </select>
    <update id="updateBnsFilegetsendR" parameterType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsFilegetsendR">
        update BNS_FILEGETSEND_R
          set
				retm=GETDATE(),
				filesta='1',
				wreer=#{wreer}
          where
               filecd=#{filecd}
                 and reer=#{reer}
                and CONVERT (datetime,stm,120)=CONVERT (datetime, #{stm},120)
    </update>
    <select id="getBnsFileurlsrByFilecd" resultType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsFileurlsR">
        SELECT  distinct
                a.filecd,
                a.fileurl,
                a.filenewnm,
                b.ctm,
                b.ser
            FROM
                BNS_FILEURLS_R a
            LEFT JOIN BNS_FILES_R b ON a.filecd = b.filecd
             where
             a.filecd=#{filecd}
    </select>
    <select id="getBnsFileSendByIdAdcd" resultType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsFilegetsendR">
        SELECT *
        FROM
        BNS_FILEGETSEND_R where 1=1
        <if test="map.filecd !=null and map.filecd !=''">
            and filecd=#{map.filecd}
        </if>
        <if test="map.adcd !=null and map.adcd !=''">
            and adcd=#{map.adcd}
        </if>

    </select>
    <select id="getCountByFileUrl" resultType="java.lang.Integer">
        select count(*) from BNS_FILEURLS_R where fileurl =#{fileurl}
    </select>
    <select id="getMsgSender" resultType="com.huitu.cloud.api.xxjh.insidefile.entity.MsgSender">
        select distinct b.realnm name,b.mobile
        from  BNS_USERDEPT_B a  LEFT JOIN  BNS_USERINFO_B b ON a.userid = b.userid
        where a.issend=1
        <if test="list !=null and list.size()>0 ">
            and a.deptid in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item.reer}
            </foreach>
        </if>
    </select>
    <select id="selectMaxMsgId" resultType="integer" parameterType="java.lang.String" >
    select isnull(MAX(MSGID),0)+1 from MESSAGEINFO_R where adcd=#{adcd}
  </select>
    <select id="selectMaxSmsId" resultType="integer" parameterType="java.lang.String">
    select isnull(MAX(SMSID),0)+1 from MESSAGESEND_R where code=CONVERT(varchar(4),getdate(),120) and  adcd=#{adcd}
  </select>
    <select id="getLastestBnsFileurlsrByFiletitle"
            resultType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsFilesAndUrls">
        select top 1 a.filecd, a.filetitle, a.fileno, a.filetype, a.fileflg, a.ctm, a.cer, a.cher, a.nt, a.adcd, a.filedesc, a.ser, a.senddept, b.filenewnm, b.fileurl
        from BNS_FILES_R a inner join BNS_FILEURLS_R b on a.filecd = b.filecd
        <where>
            <if test="filetitle !=null and filetitle !=''">
                and (charindex(#{filetitle}, filetitle) > 0 or charindex(#{filetitle}, filenewnm) > 0)
            </if>
        </where>
        order by ctm desc
    </select>
    <delete id="deleteBnsFileurlsrById">
            delete  from BNS_FILEURLS_R where filecd=#{filecd}
    </delete>
    <delete id="deleteBnsFilegetsendrById">
            delete  from BNS_FILEGETSEND_R where filecd=#{filecd}
    </delete>
    <select id="getRiskFileNoByAdcdFiletp" resultType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsRiskFileNo">
        select isnull(max(periods), 0) periods, isnull(max(CASE WHEN YEAR(tm) = year(getdate()) THEN yearPeriods ELSE NULL END), 0) yearPeriods from BNS_RISKFILESEXT_R
        <where>
            adcd = #{adcd} and filedetailtype = #{filetp}
        </where>
        group by adcd, filedetailtype
    </select>
    <insert id="saveRiskFileExt">
        INSERT INTO BNS_RISKFILESEXT_R (filecd, adcd, filedetailtype, periods, yearperiods)
        VALUES (#{filecd}, #{adcd}, #{filedetailtype}, #{periods}, #{yearperiods})
    </insert>
    <insert id="insertRiskReminder">
        INSERT INTO BSN_RISKREMINDER_R (TP, NO, TOTAL_NO, YEAR, CREATE_TIME) VALUES (#{tp}, #{no}, #{totalNo}, YEAR(GETDATE()), GETDATE())
    </insert>
    <select id="getBnsRiskFilesrByDispatch"
            resultType="com.huitu.cloud.api.xxjh.insidefile.entity.BnsRiskFileByDispatch">

        SELECT
        a.filecd,
        b.filedetailtype,
        b.periods,
        b.yearperiods,
        a.filetitle,
        a.filetype,
        a.fileflg,
        CONVERT (varchar(20), a.ctm,120) ctm,
        a.cer,
        a.cher,
        a.nt,
        a.adcd,
        a.filedesc,
        a.ser,
        a.senddept
        FROM
        BNS_FILES_R a
        left join BNS_RISKFILESEXT_R b on a.adcd = b.adcd and a.filecd = b.filecd
        where 1=1
        <if test="map.filecd !=null and map.filecd !=''">
            and a.filecd=#{map.filecd}
        </if>
        <if test="map.filetitle !=null and map.filetitle !=''">
            and CHARINDEX(#{map.filetitle},a.filetitle)>0
        </if>
        <if test="map.filetype !=null and map.filetype !='' ">
            and a.filetype=#{map.filetype}
        </if>
        <if test="map.filedetailtype !=null and map.filedetailtype !=''">
            and b.filedetailtype=#{map.filedetailtype}
        </if>
        <if test="map.ser !=null and map.ser !=''">
            and CHARINDEX(#{map.ser} ,a.ser)>0
        </if>
        <if test="map.stm !=null and map.stm !=''">
            and convert(varchar(19),a.ctm,120) >=#{map.stm}
        </if>
        <if test="map.etm !=null and map.etm !=''">
            and convert(varchar(19),a.ctm,120)&lt;=#{map.etm}
        </if>
        <if test="map.smstm !=null and map.smstm !=''">
            and a.ctm >= dateadd(minute,#{map.smstm},getdate())
        </if>
        <if test="map.smetm !=null and map.smetm !=''">
            and a.ctm &lt;= dateadd(minute,#{map.smetm},getdate())
        </if>
        <if test="map.adcd !=null and map.adcd !=''">
            and a.adcd=#{map.adcd}
        </if>
        <if test=" map.deptId !=null and map.deptId !=''">
            and a.senddept = #{map.deptId}
        </if>
        <if test="map.reer!=null and map.reer.size() >0">
            and exists (select 1 from BNS_FILEGETSEND_R b where a.filecd=b.filecd and b.reer in
            <foreach item="item" index="index" collection="map.reer" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        order by a.ctm desc

    </select>
    <select id="selectRiskReminderNo" resultType="java.util.Map">
        SELECT
            ISNULL(MAX(CASE WHEN TP = #{tp} THEN NO ELSE NULL END), 0) AS no,
            ISNULL(MAX(TOTAL_NO), 0) AS totalNo
        FROM BSN_RISKREMINDER_R
        WHERE YEAR = YEAR(GETDATE())
    </select>
    <delete id="deleteBnsRiskFileById">
        delete  from BNS_RISKFILESEXT_R where filecd=#{filecd}
    </delete>
</mapper>
