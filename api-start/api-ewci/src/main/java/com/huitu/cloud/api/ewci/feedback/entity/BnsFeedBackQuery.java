package com.huitu.cloud.api.ewci.feedback.entity;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 意见反馈信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@ApiModel(value = "BnsFeedBack对象", description = "意见反馈信息")
public class BnsFeedBackQuery extends PageBean {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主模块")
    private String mModule;

    @ApiModelProperty(value = "子模块")
    private String cModule;

    @ApiModelProperty(value = "处理状态(0未处理1已处理)")
    private String status;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private String stm;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private String etm;

    public String getmModule() {
        return mModule;
    }

    public void setmModule(String mModule) {
        this.mModule = mModule;
    }

    public String getcModule() {
        return cModule;
    }

    public void setcModule(String cModule) {
        this.cModule = cModule;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public Map<String, Object> toQueryParam() {
        Map<String, Object> params = new HashMap<>();
        params.put("mModule", getmModule());
        params.put("cModule", getcModule());
        params.put("status", getStatus());
        params.put("stm", getStm());
        params.put("etm", getEtm());
        return params;
    }
}