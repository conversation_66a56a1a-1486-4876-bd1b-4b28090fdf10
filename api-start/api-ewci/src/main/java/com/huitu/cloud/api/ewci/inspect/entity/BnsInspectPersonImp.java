package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 责任人落实
 */
@Data
@ApiModel(value = "责任人落实")
public class BnsInspectPersonImp extends BnsInspectReportForm {

    @ApiModelProperty(value = "是否落实(0否 1是)")
    private String iorp;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;
}
