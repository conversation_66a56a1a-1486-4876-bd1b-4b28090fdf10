package com.huitu.cloud.api.syq.river.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 大断面测验成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-07
 */
@TableName("ST_RVSECT_B")
@ApiModel(value="StRvsectB对象", description="大断面测验成果表")
public class StRvsectB extends Model<StRvsectB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "施测时间")
    @TableField("MSTM")
    private LocalDateTime mstm;

    @ApiModelProperty(value = "起测岸别")
    @TableField("BGBK")
    private String bgbk;

    @ApiModelProperty(value = "垂线号")
    @TableField("VTNO")
    private Integer vtno;

    @ApiModelProperty(value = "起点距")
    @TableField("DI")
    private BigDecimal di;

    @ApiModelProperty(value = "河底高程")
    @TableField("ZB")
    private BigDecimal zb;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getMstm() {
        return mstm;
    }

    public void setMstm(LocalDateTime mstm) {
        this.mstm = mstm;
    }

    public String getBgbk() {
        return bgbk;
    }

    public void setBgbk(String bgbk) {
        this.bgbk = bgbk;
    }

    public Integer getVtno() {
        return vtno;
    }

    public void setVtno(Integer vtno) {
        this.vtno = vtno;
    }

    public BigDecimal getDi() {
        return di;
    }

    public void setDi(BigDecimal di) {
        this.di = di;
    }

    public BigDecimal getZb() {
        return zb;
    }

    public void setZb(BigDecimal zb) {
        this.zb = zb;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "StRvsectB{" +
        "stcd=" + stcd +
        ", mstm=" + mstm +
        ", bgbk=" + bgbk +
        ", vtno=" + vtno +
        ", di=" + di +
        ", zb=" + zb +
        ", comments=" + comments +
        ", moditime=" + moditime +
        "}";
    }
}
