package com.huitu.cloud.api.ewci.weChat.entity;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class WeChatRainByDutyRecort {
    @ApiModelProperty(value = "省平均降雨")
    private String provinceAvgRain;
    @ApiModelProperty(value = "市平均降雨")
    private List<WeChatAdAvgRain> cityAvgRain;
    @ApiModelProperty(value = "降雨量站")
    private List<WeChatRain> rainList;
    @ApiModelProperty(value = "降雨站总数")
    private Integer  rainCount;
    @ApiModelProperty(value = "水文最大降雨站")
    private WeChatRain swMaxRain;
    @ApiModelProperty(value = "山洪最大降雨站")
    private WeChatRain shMaxRain;
    @ApiModelProperty(value = "气象最大降雨站")
    private WeChatRain qxMaxRain;

    public Integer getRainCount() {
        return rainCount;
    }

    public void setRainCount(Integer rainCount) {
        this.rainCount = rainCount;
    }

    public List<WeChatRain> getRainList() {
        return rainList;
    }

    public void setRainList(List<WeChatRain> rainList) {
        this.rainList = rainList;
    }

    public String getProvinceAvgRain() {
        return provinceAvgRain;
    }

    public void setProvinceAvgRain(String provinceAvgRain) {
        this.provinceAvgRain = provinceAvgRain;
    }

    public List<WeChatAdAvgRain> getCityAvgRain() {
        return cityAvgRain;
    }

    public void setCityAvgRain(List<WeChatAdAvgRain> cityAvgRain) {
        this.cityAvgRain = cityAvgRain;
    }

    public WeChatRain getSwMaxRain() {
        return swMaxRain;
    }

    public void setSwMaxRain(WeChatRain swMaxRain) {
        this.swMaxRain = swMaxRain;
    }

    public WeChatRain getShMaxRain() {
        return shMaxRain;
    }

    public void setShMaxRain(WeChatRain shMaxRain) {
        this.shMaxRain = shMaxRain;
    }

    public WeChatRain getQxMaxRain() {
        return qxMaxRain;
    }

    public void setQxMaxRain(WeChatRain qxMaxRain) {
        this.qxMaxRain = qxMaxRain;
    }
}
