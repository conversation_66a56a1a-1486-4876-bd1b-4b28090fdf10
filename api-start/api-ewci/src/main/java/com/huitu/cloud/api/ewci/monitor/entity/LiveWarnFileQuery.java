package com.huitu.cloud.api.ewci.monitor.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 生成预警文件参数
 *
 * <AUTHOR>
 */
@ApiModel(value = "生成预警文件参数")
public class LiveWarnFileQuery {

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date etm;

    @ApiModelProperty(value = "文件名称")
    @NotNull(message = "文件名称不能为空")
    private String orgFileName;

    @ApiModelProperty(value = "文件路径")
    @NotNull(message = "文件路径不能为空")
    private String orgFileUrl;

    @ApiModelProperty(value = "行政区划代码")
    @NotBlank(message = "行政区划代码不能为空")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @NotBlank(message = "行政区划名称不能为空")
    private String adnm;

    @ApiModelProperty(value = "等值分析-最小降雨层级")
    private Integer maxDrpLevel;

    @ApiModelProperty(value = "降雨最高值")
    private Integer maxDrps;

    @ApiModelProperty(value = "雨情信息列表")
    private List<LiveRain> rainList;

    @ApiModelProperty(value = "防治区信息列表")
    private List<IaCPrevad> prevadList;

    @ApiModelProperty(value = "危险区信息列表")
    private List<IaCDanad> danadList;

    @ApiModelProperty(value = "责任人信息列表")
    private List<FloodPerson> floodPersonList;

    @ApiModelProperty(value = "防洪工程信息-水库列表")
    private List<ReservoirData> reservoirList;

    @ApiModelProperty(value = "防洪工程信息-堤防列表")
    private List<DikeData> dikeList;

    @ApiModelProperty(value = "风险隐患点信息列表")
    private List<RiskInfoPointData> riskInfoPointsList;

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public String getOrgFileName() {
        return orgFileName;
    }

    public void setOrgFileName(String orgFileName) {
        this.orgFileName = orgFileName;
    }

    public String getOrgFileUrl() {
        return orgFileUrl;
    }

    public void setOrgFileUrl(String orgFileUrl) {
        this.orgFileUrl = orgFileUrl;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getMaxDrpLevel() {
        return maxDrpLevel;
    }

    public void setMaxDrpLevel(Integer maxDrpLevel) {
        this.maxDrpLevel = maxDrpLevel;
    }

    public Integer getMaxDrps() {
        return maxDrps;
    }

    public void setMaxDrps(Integer maxDrps) {
        this.maxDrps = maxDrps;
    }

    public List<LiveRain> getRainList() {
        return rainList;
    }

    public void setRainList(List<LiveRain> rainList) {
        this.rainList = rainList;
    }

    public List<IaCPrevad> getPrevadList() {
        return prevadList;
    }

    public void setPrevadList(List<IaCPrevad> prevadList) {
        this.prevadList = prevadList;
    }

    public List<IaCDanad> getDanadList() {
        return danadList;
    }

    public void setDanadList(List<IaCDanad> danadList) {
        this.danadList = danadList;
    }

    public List<FloodPerson> getFloodPersonList() {
        return floodPersonList;
    }

    public void setFloodPersonList(List<FloodPerson> floodPersonList) {
        this.floodPersonList = floodPersonList;
    }

    public List<ReservoirData> getReservoirList() {
        return reservoirList;
    }

    public void setReservoirList(List<ReservoirData> reservoirList) {
        this.reservoirList = reservoirList;
    }

    public List<DikeData> getDikeList() {
        return dikeList;
    }

    public void setDikeList(List<DikeData> dikeList) {
        this.dikeList = dikeList;
    }

    public List<RiskInfoPointData> getRiskInfoPointsList() {
        return riskInfoPointsList;
    }

    public void setRiskInfoPointsList(List<RiskInfoPointData> riskInfoPointsList) {
        this.riskInfoPointsList = riskInfoPointsList;
    }

    @Override
    public String toString() {
        return "LiveWarnFileQuery{" +
                "stm=" + stm +
                ", etm=" + etm +
                ", orgFileName='" + orgFileName + '\'' +
                ", orgFileUrl='" + orgFileUrl + '\'' +
                ", adcd='" + adcd + '\'' +
                ", adnm='" + adnm + '\'' +
                '}';
    }
}
