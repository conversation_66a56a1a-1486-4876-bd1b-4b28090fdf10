package com.huitu.cloud.api.screen.fhjz.mapper;


import com.huitu.cloud.api.screen.fhjz.entity.RainCountVo;
import com.huitu.cloud.api.screen.fhjz.entity.RainVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
public interface RainScreenDao {


    RainCountVo staticRainRealCount(@Param("startTime")String startTime,@Param("endTime") String endTime,@Param("code") String code);

    RainCountVo staicRainForecastCount(@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("code")String code);

    RainCountVo staticProRainRealCount(@Param("startTime")String startTime, @Param("endTime")String endTime);

    RainCountVo staticProRainForecastCount(@Param("startTime")String startTime, @Param("endTime")String endTime);

    List<Map<String,Object>> statisticsRealRainValueByPro(@Param("startTime")String startTime, @Param("endTime")String endTime);

    List<Map<String,Object>> statisticsRealRainValueByLy(@Param("startTime")String startTime, @Param("endTime")String endTime);

    List<Map<String,Object>> statisticsForecastRainValueByPro(@Param("startTime")String startTime, @Param("endTime")String endTime);

    List<Map<String,Object>> statisticsForecastRainValueByLy(@Param("startTime")String startTime, @Param("endTime")String endTime);

    List<RainVo> queryProRainList(@Param("rStartTime")String rStartTime, @Param("rEndtTime")String rEndtTime, @Param("fStartTime")String fStartTime, @Param("fEndtTime")String fEndtTime,@Param("stType") String stType,@Param("isAnalys") Boolean isAnalys);

    List<RainVo> querylyRainList(@Param("code")String code,@Param("rStartTime")String rStartTime, @Param("rEndtTime")String rEndtTime, @Param("fStartTime")String fStartTime, @Param("fEndtTime")String fEndtTime,@Param("stType") String stType,@Param("isAnalys") Boolean isAnalys);

    List<Map<String,Object>> statisticSoilValue(@Param("startTime")String startTime, @Param("endTime")String endTime);
}
