package com.huitu.cloud.api.ewci.soilnews.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.soilnews.entity.BsnSoilNewsB;
import com.huitu.cloud.api.ewci.soilnews.entity.BsnSoilNewsQo;
import com.huitu.cloud.api.ewci.soilnews.service.SoilNewsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 墒情专报信息服务
 *
 * <AUTHOR>
 */

@Api(tags = "墒情专报信息")
@RestController
@RequestMapping("/api/ewci/soilnews")
public class SoilNewsResource  extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "46686c95-5efd-48bf-9b9e-619b65cfef15";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }
    private final SoilNewsService baseService;

    public SoilNewsResource(SoilNewsService baseService) {
        this.baseService = baseService;
    }

    @ApiOperation(value = "获取墒情专报", notes = "作者：jiangjy")
    @PostMapping("select-soilnews-list")
    public ResponseEntity<SuccessResponse<IPage<BsnSoilNewsB>>> getUnreadList(@RequestBody BsnSoilNewsQo soilNewsQo) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSoilNewsList(soilNewsQo.getPeriods(), soilNewsQo.getPageNum(), soilNewsQo.getPageSize())));
    }

}
