package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel
public class StRsvrMax implements Serializable {

    @ApiModelProperty(value = "排序")
    private Integer sortNo;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "库上水位")
    @TableField("RZ")
    private String rz;

    @ApiModelProperty(value = "入库流量")
    @TableField("INQ")
    private BigDecimal inq;

    @ApiModelProperty(value = "蓄水量")
    @TableField("W")
    private BigDecimal w;

    @ApiModelProperty(value = "出库流量")
    @TableField("OTQ")
    private BigDecimal otq;
    @ApiModelProperty(value = "水库类型")
    @TableField("RSVRTP")
    private String rsvrtp;

    @ApiModelProperty(value = "坝顶高程")
    @TableField("DAMEL")
    private BigDecimal damel;

    @ApiModelProperty(value = "校核洪水位")
    @TableField("CKFLZ")
    private BigDecimal ckflz;

    @ApiModelProperty(value = "设计洪水位")
    @TableField("DSFLZ")
    private BigDecimal dsflz;

    @ApiModelProperty(value = "正常高水位")
    @TableField("NORMZ")
    private BigDecimal normz;

    @ApiModelProperty(value = "超讯限水位")
    @TableField("RZFSLTDZ")
    private BigDecimal rzfsltdz;

    @ApiModelProperty(value = "超讯限库容")
    @TableField("WFSLTDW")
    private BigDecimal wfsltdw;

    @ApiModelProperty(value = "超正常高水位")
    @TableField("RZNORMZ")
    private BigDecimal rznormz;

    @ApiModelProperty(value = "汛限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @ApiModelProperty(value = "汛限库容")
    @TableField("FSLTDW")
    private BigDecimal fsltdw;

    @ApiModelProperty(value = "是否是开敞式溢洪道")
    @TableField("OSFLG")
    private String osflg;

    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    @TableField("RVNM")
    private String rvnm;

    @ApiModelProperty(value = "水系名称")
    @TableField("HNNM")
    private String hnnm;

    @ApiModelProperty(value = "流域名称")
    @TableField("BSNM")
    private String bsnm;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private BigDecimal lttd;

    @ApiModelProperty(value = "站址")
    @TableField("STLC")
    private String stlc;
    @ApiModelProperty(value = "行政区划码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划码")
    @TableField("ADCD")
    private String adnm;


    @ApiModelProperty(value = "县级行政区划码")
    private String xadcd;

    @ApiModelProperty(value = "县级行政区划名称")
    private String xadnm;

    @ApiModelProperty(value = "平均入流量")
    private BigDecimal avinq;

    @ApiModelProperty(value = "平均出流量")
    private BigDecimal avotq;

    @ApiModelProperty(value = "最大入流量")
    private BigDecimal maxinq;

    @ApiModelProperty(value = "最大入流时间")
    private LocalDateTime maxinqtm;

    @ApiModelProperty(value = "最大出流量")
    private BigDecimal maxotq;

    @ApiModelProperty(value = "最大出流时间")
    private LocalDateTime maxotqtm;

    @ApiModelProperty(value = "测站归属类型")
    private String stadtp;

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getRz() {
        return rz;
    }

    public void setRz(String rz) {
        this.rz = rz;
    }

    public BigDecimal getInq() {
        return inq;
    }

    public void setInq(BigDecimal inq) {
        this.inq = inq;
    }

    public BigDecimal getW() {
        return w;
    }

    public void setW(BigDecimal w) {
        this.w = w;
    }

    public BigDecimal getOtq() {
        return otq;
    }

    public void setOtq(BigDecimal otq) {
        this.otq = otq;
    }

    public String getRsvrtp() {
        return rsvrtp;
    }

    public void setRsvrtp(String rsvrtp) {
        this.rsvrtp = rsvrtp;
    }

    public BigDecimal getDamel() {
        return damel;
    }

    public void setDamel(BigDecimal damel) {
        this.damel = damel;
    }

    public BigDecimal getCkflz() {
        return ckflz;
    }

    public void setCkflz(BigDecimal ckflz) {
        this.ckflz = ckflz;
    }

    public BigDecimal getDsflz() {
        return dsflz;
    }

    public void setDsflz(BigDecimal dsflz) {
        this.dsflz = dsflz;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public BigDecimal getWfsltdw() {
        return wfsltdw;
    }

    public void setWfsltdw(BigDecimal wfsltdw) {
        this.wfsltdw = wfsltdw;
    }

    public BigDecimal getRznormz() {
        return rznormz;
    }

    public void setRznormz(BigDecimal rznormz) {
        this.rznormz = rznormz;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public String getOsflg() {
        return osflg;
    }

    public void setOsflg(String osflg) {
        this.osflg = osflg;
    }

    public BigDecimal getFsltdw() {
        return fsltdw;
    }

    public void setFsltdw(BigDecimal fsltdw) {
        this.fsltdw = fsltdw;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getHnnm() {
        return hnnm;
    }

    public void setHnnm(String hnnm) {
        this.hnnm = hnnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public BigDecimal getAvinq() {
        return avinq;
    }

    public void setAvinq(BigDecimal avinq) {
        this.avinq = avinq;
    }

    public BigDecimal getAvotq() {
        return avotq;
    }

    public void setAvotq(BigDecimal avotq) {
        this.avotq = avotq;
    }

    public BigDecimal getMaxinq() {
        return maxinq;
    }

    public void setMaxinq(BigDecimal maxinq) {
        this.maxinq = maxinq;
    }

    public BigDecimal getMaxotq() {
        return maxotq;
    }

    public void setMaxotq(BigDecimal maxotq) {
        this.maxotq = maxotq;
    }

    public LocalDateTime getMaxinqtm() {
        return maxinqtm;
    }

    public void setMaxinqtm(LocalDateTime maxinqtm) {
        this.maxinqtm = maxinqtm;
    }

    public LocalDateTime getMaxotqtm() {
        return maxotqtm;
    }

    public void setMaxotqtm(LocalDateTime maxotqtm) {
        this.maxotqtm = maxotqtm;
    }

    public String getStadtp() {
        return stadtp;
    }

    public void setStadtp(String stadtp) {
        this.stadtp = stadtp;
    }
}
