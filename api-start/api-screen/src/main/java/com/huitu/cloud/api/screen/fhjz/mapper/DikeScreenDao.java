package com.huitu.cloud.api.screen.fhjz.mapper;


import com.huitu.cloud.api.screen.fhjz.entity.DikeDetailVo;
import com.huitu.cloud.api.screen.fhjz.entity.DikeVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
public interface DikeScreenDao {

    List<DikeVo> queryDikeList(@Param("dikeGrad") String dikeGrad,@Param("dikeName") String dikeName);

    Map<String, Object> queryDikeInfo(@Param("dikeCode") String dikeCode);

    List<DikeDetailVo> queryDikeDetailList(@Param("dikeGrad") String dikeGrad, @Param("dikeName") String dikeName);

    List<Map> statisticDikeDetail(@Param("adcd")String adcd,@Param("level") int level,@Param("lowLevel") int lowLevel,@Param("suffix") String suffix);
}
