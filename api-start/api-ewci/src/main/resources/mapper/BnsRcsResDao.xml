<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rcs.mapper.BnsRcsResDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getRcsResList" resultType="com.huitu.cloud.api.ewci.rcs.entity.BnsRcsResVo" useCache="false">
        select a.RES_CODE, a.ENG_CODE, a.RES_NAME, a.ADCD, a.RES_LOC, a.LGTD, a.LTTD, a.MWRDAM_TYPE, a.COMPLETED, a.COMPLETED_DATE, a.CONCLUSION, a.REINFORCED, a.TS, b.adnm from BNS_RCS_RES a left join BSN_ADCD_B b on a.adcd = b.adcd
        <where>
            <if test="map.ad!=null and map.ad!='' and map.level!=null and map.level!=''">
                AND substring(a.ADCD,1,#{map.level})=#{map.ad}
            </if>
            <if test="map.resName!=null and map.resName!=''">
                and CHARINDEX(#{map.resName},a.RES_NAME) >0
            </if>
            <if test="map.mwrdamType !=null and map.mwrdamType !=''">
                AND A.MWRDAM_TYPE = #{map.mwrdamType}
            </if>
            <if test="map.completed !=null and map.completed !=''">
                AND A.COMPLETED = #{map.completed}
            </if>
            <if test="map.conclusion !=null and map.conclusion !='' and map.conclusion != 0">
                AND A.CONCLUSION = #{map.conclusion}
            </if>
            <if test="map.conclusion !=null and map.conclusion !='' and map.conclusion == 0">
                AND A.CONCLUSION is null
            </if>
            <if test="map.reinforced !=null and map.reinforced !='' and map.reinforced != 2">
                AND A.REINFORCED = #{map.reinforced}
            </if>
            <if test="map.reinforced !=null and map.reinforced !='' and map.reinforced == 2">
                AND A.REINFORCED is null
            </if>
            <if test="map.level != null and map.level == '4'.toString()">
                and substring(a.ADCD,1,6) != '220581'
            </if>
        </where>
        order by a.adcd
    </select>
    <select id="queryExport" resultType="com.huitu.cloud.api.ewci.rcs.entity.BnsRcsResVo" useCache="false">
        select a.RES_CODE, a.ENG_CODE, a.RES_NAME, a.ADCD, a.RES_LOC, a.LGTD, a.LTTD, CASE
        a.MWRDAM_TYPE
        WHEN '0' THEN
        '未知'
        WHEN '1' THEN
        '重力坝'
        WHEN '2' THEN
        '拱坝'
        WHEN '3' THEN
        '支墩坝'
        WHEN '4' THEN
        '均质坝'
        WHEN '5' THEN
        '心墙坝'
        WHEN '6' THEN
        '斜墙坝'
        WHEN '7' THEN
        '面板坝'
        WHEN '9' THEN
        '其他'
        END AS MWRDAM_TYPE,
        CASE
        a.COMPLETED
        WHEN '0' THEN
        '未开展'
        WHEN '1' THEN
        '开展过'
        END AS COMPLETED, a.COMPLETED_DATE,
        CASE
        a.CONCLUSION
        WHEN '0' THEN
        '未知'
        WHEN '1' THEN
        '一类坝'
        WHEN '2' THEN
        '二类坝'
        WHEN '3' THEN
        '三类坝'
        END AS CONCLUSION,
        CASE
        a.REINFORCED
        WHEN '0' THEN
        '否'
        WHEN '1' THEN
        '是'
        END AS REINFORCED, a.TS, b.adnm from BNS_RCS_RES a left join BSN_ADCD_B b on a.adcd = b.adcd
        <where>
            <if test="map.ad!=null and map.ad!='' and map.level!=null and map.level!=''">
                AND substring(a.ADCD,1,#{map.level})=#{map.ad}
            </if>
            <if test="map.resName!=null and map.resName!=''">
                and CHARINDEX(#{map.resName},a.RES_NAME) >0
            </if>
            <if test="map.mwrdamType !=null and map.mwrdamType !=''">
                AND A.MWRDAM_TYPE = #{map.mwrdamType}
            </if>
            <if test="map.completed !=null and map.completed !=''">
                AND A.COMPLETED = #{map.completed}
            </if>
            <if test="map.conclusion !=null and map.conclusion !=''">
                AND A.CONCLUSION = #{map.conclusion}
            </if>
            <if test="map.reinforced !=null and map.reinforced !=''">
                AND A.REINFORCED = #{map.reinforced}
            </if>
            <if test="map.level != null and map.level == '4'.toString()">
                and substring(a.ADCD,1,6) != '220581'
            </if>
        </where>
        order by a.adcd
    </select>
    <select id="getSonAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        select adcd, adnm from BSN_ADCD_B
        <where>
            <if test="adcd != null and adcd != ''">
                and padcd = #{adcd}
            </if>
        </where>
        order by adcd
    </select>
    <select id="getStatByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT A.ADCD, A.ADNM, '03' ENTYPE, D.TOTAL_COUNT
        FROM BSN_ADCD_B A,
        (SELECT COUNT(RES_CODE) TOTAL_COUNT FROM BNS_RCS_RES
        WHERE LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) D
        WHERE A.ADCD = #{map.adcd}
    </select>
    <select id="getCountListWithMwrdamTypeStat" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(MWRDAM_TYPE, '0') PROP_CODE, COUNT(RES_CODE) PROP_COUNT
        FROM BNS_RCS_RES
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY MWRDAM_TYPE
        ORDER BY MWRDAM_TYPE ASC
    </select>
    <select id="getCountListWithCompletedStat" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(COMPLETED, '0') PROP_CODE, COUNT(RES_CODE) PROP_COUNT
        FROM BNS_RCS_RES
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY COMPLETED
        ORDER BY COMPLETED ASC
    </select>
    <select id="getCountListWithConclusionStat" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(CONCLUSION, '0') PROP_CODE, COUNT(RES_CODE) PROP_COUNT
        FROM BNS_RCS_RES
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY CONCLUSION
        ORDER BY CONCLUSION ASC
    </select>
    <select id="getCountListWithReinforcedStat" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(REINFORCED, '2') PROP_CODE, COUNT(RES_CODE) PROP_COUNT
        FROM BNS_RCS_RES
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY REINFORCED
        ORDER BY REINFORCED ASC
    </select>

</mapper>
