package com.huitu.cloud.api.common.msg.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@ApiModel
public class Message {
    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "手机号码")
    private String sid;

    @ApiModelProperty(value = " 接收人姓名")
    private String uname;

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getUname() {
        return uname;
    }

    public void setUname(String uname) {
        this.uname = uname;
    }
}
