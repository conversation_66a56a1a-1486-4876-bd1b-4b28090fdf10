<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.common.msg.mapper.MessageInfoDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                ID, MSGID, WARNID, MSGTYPEID, MSGCONTENT, SENDER, MEDIAID, SENDTM, REMARK, SENDTYPE, ADCD
    </sql>
    <update id="updateSendResult">
        UPDATE A
        SET A.sendresult='2',
            A.rectm= getdate()
        FROM MESSAGESEND_R A,
             ${tableName} B
        WHERE A.code = B.code
          and a.smsid = b.smsid
          and a.adcd = b.adcd
          and a.issend = '1'
    </update>
    <update id="updateCommonMsgBatches">
        UPDATE BNS_MESSAGE_R SET flag = '1' WHERE id in
        <foreach collection="ids" index="index" item="item"
                 separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <select id="selectMaxMsgId" resultType="integer" parameterType="java.lang.String">
        select isnull(MAX(MSGID), 0) + 1
        from MESSAGEINFO_R
        where adcd = #{adcd}
    </select>
    <select id="selectMaxSmsId" resultType="integer" parameterType="java.lang.String">
        select isnull(MAX(SMSID), 0) + 1
        from MESSAGESEND_R
        where code = CONVERT(varchar(4), getdate(), 120)
          and adcd = #{adcd}
    </select>
    <insert id="insertMsgInfo" parameterType="com.huitu.cloud.api.common.msg.entity.MessageInfo">
        insert into MESSAGEINFO_R (msgId, WarnID, MsgTypeID, Msgcontent, SENDER, MEDIAID, SENDTM, REMARK, SENDTYPE,
                                   ADCD)
        values (#{msgId}, #{warnId}, #{msgTypeId}, #{msgContent}, #{sender}, #{mediaId}, #{sendTm},
                #{remark,jdbcType=VARCHAR}, #{sendType,jdbcType=VARCHAR}, #{adcd,jdbcType=VARCHAR})
    </insert>
    <insert id="bathInsertMsgSend">
        insert into MESSAGESEND_R(MSGID,CODE,SMSID,SID,UNAME,ObjectCD,OBJECTTYPE,SENDRESULT,RECTM,REMARK,ADCD,ISSEND)
        values
        <foreach collection="msg" item="item" index="index" separator=",">
            (#{item.msgId},CONVERT(varchar(4),getdate(),120),#{item.smsId},#{item.sid},#{item.uname},#{item.msgId},10,#{item.sendResult,jdbcType=VARCHAR},getdate(),#{item.remark,jdbcType=VARCHAR},#{item.adcd,jdbcType=VARCHAR},'1')
        </foreach>
    </insert>
    <insert id="bathInsertFailMassage">
        insert into ${tableName}(code,smsid,adcd)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.code}, #{item.smsid}, #{item.adcd})
        </foreach>
    </insert>
    <insert id="insertCommonMsg">
        insert into BNS_MESSAGE_R (type, tm, adcd, deptid, message, flag)
        values (#{type}, #{tm}, #{adcd}, #{deptid}, #{message}, #{flag})
    </insert>
    <!-- 查询预警短信广播发送记录情况-->
    <select id="selectWarnMsg" resultType="com.huitu.cloud.api.common.msg.entity.MessageVo">
        select
        A.MSGID,A.CODE,A.SMSID,A.SID,B.REMARK,A.SENDRESULT,A.UNAME,B.SENDTM,B.Msgcontent,B.MsgTypeID,B.MEDIAID,B.ADCD,B.SENDER,
        (case when A.SENDRESULT ='1' then '发送成功' when A.SENDRESULT ='0' then '发送失败' else '请求发送' end) SENDRESULTNM from
        MESSAGESEND_R A
        LEFT JOIN MESSAGEINFO_R B ON A.MSGID=B.MSGID and a.adcd=b.adcd
        WHERE 1=1
        <if test="warnId !=null and warnId !='' ">
            AND B.warnID=#{warnId}
        </if>
        <if test="ad !=null and ad !='' ">
            AND A.ADCD LIKE '${ad}%'
        </if>
        <if test="msgTypeId !=null and msgTypeId !='' ">
            AND B.MsgTypeID=#{msgTypeId}
        </if>
        <if test="mediaId !=null and mediaId !='' ">
            AND B.MEDIAID= #{mediaId}
        </if>
        <if test="status !=null and status !='' ">
            AND A.SENDRESULT=#{status}
        </if>
        <if test="stm != null and stm !=''">
            and CONVERT(DATE,B.SENDTM) >=CONVERT(DATE,#{stm})
        </if>
        <if test="etm != null and etm !=''">
            and CONVERT(DATE,B.SENDTM) &lt;= CONVERT(DATE,#{etm})
        </if>
        order by B.SENDTM desc
    </select>
    <select id="getMsgStatistics" resultType="com.huitu.cloud.api.common.msg.entity.MessageStatistics">
        SELECT ADCD,ADNM,MSGCT0,MSGCT1,MSGCT2,MSGCT0+MSGCT1+MSGCT2 MSGCT, REALCT
        FROM (
        SELECT ADCD,ADNM,MAX(CASE WHEN SENDRESULT='0' THEN TOTAL ELSE 0 END) MSGCT0 ,
        MAX(CASE WHEN SENDRESULT='1' THEN TOTAL ELSE 0 END) MSGCT1,MAX(CASE WHEN SENDRESULT='2' THEN TOTAL ELSE 0 END)
        MSGCT2,
        MAX(CASE WHEN SENDRESULT='1' THEN REALCT ELSE 0 END) REALCT
        FROM
        (SELECT ADCD,ADNM,SENDRESULT,COUNT(*) TOTAL,SUM(REALCT)REALCT FROM (select
        AD.ADCD,AD.ADNM,B.SMSID,B.SENDRESULT,CEILING(ROUND(CAST(ISNULL(LEN(A.MSGCONTENT),0) AS FLOAT)/70, 2)) REALCT
        from MESSAGEINFO_R A LEFT JOIN MESSAGESEND_R B
        ON A.MSGID=B.MSGID AND A.ADCD=B.ADCD

        LEFT JOIN BSN_ADCD_B AD ON LEFT(LEFT(A.ADCD, #{size}) + '000000000000000', 15)=AD.ADCD
        where left(A.ADCD,#{level})=#{ad}
        <if test="stm != null and stm !=''">
            and CONVERT(DATE,SENDTM)>=CONVERT(DATE,#{stm})
        </if>
        <if test="etm != null and etm !=''">
            and CONVERT(DATE,SENDTM) &lt;= CONVERT(DATE,#{etm})
        </if>
        ) T GROUP BY ADCD,ADNM,SENDRESULT) M GROUP BY ADCD,ADNM) MSG ORDER BY ADCD
    </select>
    <select id="getWarnMsgInfoByPage" resultType="com.huitu.cloud.api.common.msg.entity.MessageVo">
        select
        A.MSGID,A.CODE,A.SMSID,A.SID,B.REMARK,A.SENDRESULT,A.UNAME,B.SENDTM,B.Msgcontent,B.MsgTypeID,B.MEDIAID,B.ADCD,B.SENDER,
        (case when A.SENDRESULT ='1' then '发送成功' when A.SENDRESULT ='0' then '发送失败' else '请求发送' end) SENDRESULTNM from
        MESSAGESEND_R A
        LEFT JOIN MESSAGEINFO_R B ON A.MSGID=B.MSGID and a.adcd=b.adcd
        <if test="map.warnId =='all'">
            INNER JOIN WARNRECORD_R C ON C.WARNID = B.WARNID
        </if>
        WHERE 1=1
        <if test="map.ad !=null and map.ad !='' ">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        <if test="map.msgid !=null and map.msgid !='' ">
            AND A.MSGID=#{map.msgid}
        </if>
        <if test="map.msgTypeId !=null ">
            AND B.MsgTypeID=#{map.msgTypeId}
        </if>
        <if test="map.mediaId !=null and map.mediaId !='' ">
            AND B.MEDIAID= #{map.mediaId}
        </if>
        <if test="map.status !=null and map.status !='' ">
            AND A.SENDRESULT=#{map.status}
        </if>
        <if test="map.stm != null and map.stm !='' and map.warnId !='all'">
            and CONVERT(DATE,A.RECTM) >= CONVERT(DATE,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !='' and map.warnId !='all'">
            and CONVERT(DATE,A.RECTM) &lt;= CONVERT(DATE,#{map.etm})
        </if>
        <if test="map.warnId !=null and map.warnId !='' and map.warnId != 'all'">
            AND B.warnID=#{map.warnId}
        </if>
        <if test="map.warnId =='all'">
            <if test="map.stm != null and map.stm !=''">
                and C.WARNSTM >= CONVERT(DATE,#{map.stm})
            </if>
            <if test="map.etm != null and map.etm !=''">
                and C.WARNSTM &lt;= CONVERT(DATE,#{map.etm})
            </if>
        </if>
        order by B.SENDTM desc
    </select>
    <select id="getWarnMsgInfoList" resultType="com.huitu.cloud.api.common.msg.entity.MessageList">
        SELECT ADCD,ADNM,MSGID,TOTAL,SUCCOUNT,MSGCONTENT, SENDER, MEDIAID, SENDTM, SENDTYPE, MSGTYPEID from (
        SELECT AD.ADCD,AD.ADNM,A.MSGID,
        (SELECT COUNT(*) FROM MESSAGESEND_R C WHERE C.ADCD = A.ADCD AND C.MSGID = A.MSGID AND C.CODE=YEAR(A.SENDTM)) AS TOTAL,
        (SELECT COUNT(*) FROM MESSAGESEND_R C WHERE C.ADCD = A.ADCD AND C.MSGID = A.MSGID AND C.CODE=YEAR(A.SENDTM) AND C.SENDRESULT = '1') AS
        SUCCOUNT,
        A.MSGCONTENT, A.SENDER, A.MEDIAID, A.SENDTM, A.SENDTYPE, A.MSGTYPEID
        FROM MESSAGEINFO_R A LEFT JOIN BSN_ADCD_B AD ON substring (A.ADCD,1,#{map.size})+'${map.zero}'=AD.ADCD
        WHERE left(A.ADCD,#{map.level})=#{map.ad}
        <if test="map.stm != null and map.stm !=''">
            and CONVERT(DATETIME,SENDTM)>=CONVERT(DATETIME,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and CONVERT(DATETIME,SENDTM) &lt;= CONVERT(DATETIME,#{map.etm})
        </if>
        <if test="map.msgTypeId !=null">
            AND A.MsgTypeID=#{map.msgTypeId}
        </if>
        <if test="map.mediaId !=null and map.mediaId !='' ">
            AND A.MEDIAID= #{map.mediaId}
        </if>
        ) a order by a.MSGID desc,a.ADCD
    </select>
    <select id="getFailMessage" resultType="com.huitu.cloud.api.common.msg.entity.FailMessage">
        SELECT CODE,SMSID,ADCD FROM MESSAGESEND_R
        <where>
            <if test="adcd != null and adcd !=''">
                and ADCD = #{adcd}
            </if>
            <if test="msgid != null and msgid !=''">
                and MSGID = #{msgid}
            </if>
            AND SENDRESULT = '0'
        </where>
    </select>
    <select id="getCommonMsg" resultType="com.huitu.cloud.api.common.msg.entity.BnsMessageR">
        select id ,type ,tm ,adcd ,deptid ,message ,flag from BNS_MESSAGE_R
        <where>
            <if test="ad !=null and ad !=''">
                and left(ADCD,#{level})=#{ad}
            </if>
            <if test="stm != null and stm !=''">
                and tm> CONVERT(datetime,#{stm})
            </if>
            <if test="etm != null and etm !=''">
                and TM &lt;= CONVERT(datetime,#{etm})
            </if>
            <if test="id != null and id !=''">
                and ID = #{id}
            </if>
            <if test="type != null and type !=''">
                and TYPE = #{type}
            </if>
            <if test="deptid != null and deptid !=''">
                and DEPTID = #{deptid}
            </if>
            <if test="flag != null and flag !=''">
                and FLAG = #{flag}
            </if>
        </where>
        order by tm desc

    </select>
    <select id="getMobilePerTp" resultType="com.huitu.cloud.api.common.msg.entity.MessageVo">
        select a.mobile                               sid,
               STUFF((select distinct ',(山洪)' +
                                      case
                                          when rytp = '1' then '县市责任人'
                                          when rytp = '2' then '乡镇责任人'
                                          when rytp = '3' then '行政村责任人'
                                          when rytp = '4' then '自然村责任人'
                                          when rytp = '5' then '监测责任人'
                                          when rytp = '6' then '预警责任人'
                                          when rytp = '7' then '转移责任人'
                                          when rytp = '8' then '管理责任人'
                                          else ''
                                          end
                      from BSN_SH_Person_B
                      where mobile = a.mobile
                      for xml path('')), 1, 1, '') as rytps
        from BSN_SH_Person_B a
        where a.mobile is not null
          and a.mobile != ''
        group by a.mobile

        union all

        select a.mobile                               sid,
               STUFF((select distinct ',(水库)' +
                                      case
                                          when RESPERTP = '1' then '安全度汛行政责任人'
                                          when RESPERTP = '2' then '抢险技术责任人'
                                          when RESPERTP = '3' then '主管部门责任人'
                                          when RESPERTP = '4' then '管理单位责任人'
                                          when RESPERTP = '5' then '巡查值守责任人'
                                          else ''
                                          end
                      from BNS_RSVRPERSON_B
                      where mobile = a.mobile
                      for xml path('')), 1, 1, '') as rytps
        from BNS_RSVRPERSON_B a
        where a.mobile is not null
          and a.mobile != ''
        group by a.mobile

        union all

        select a.MOBILE_PHONE                         sid,
               STUFF((select distinct ',(江河)' +
                                      case
                                          when PERTP = '1' then '行政责任人'
                                          when PERTP = '2' then '技术责任人'
                                          else ''
                                          end
                      from BNS_RIVERPERSON_B
                      where MOBILE_PHONE = a.MOBILE_PHONE
                      for xml path('')), 1, 1, '') as rytps
        from BNS_RIVERPERSON_B a
        where a.MOBILE_PHONE is not null
          and a.MOBILE_PHONE != ''
        group by a.MOBILE_PHONE

        union all

        select a.MOBILE                               sid,
               STUFF((select distinct ',(堤防)' +
                                      case
                                          when PERTP = '1' then '行政责任人'
                                          when PERTP = '2' then '技术责任人'
                                          when PERTP = '3' then '村级责任人'
                                          else ''
                                          end
                      from BNS_DIKEPERSON_B
                      where MOBILE = a.MOBILE
                      for xml path('')), 1, 1, '') as rytps
        from BNS_DIKEPERSON_B a
        where a.MOBILE is not null
          and a.mobile != ''
        group by a.MOBILE

        union all

        select a.MOBILE                               sid,
               STUFF((select distinct ',(险工险段)' +
                                      case
                                          when PERTP = '1' then '行政责任人'
                                          when PERTP = '2' then '技术责任人'
                                          else ''
                                          end
                      from BNS_DPDSPERSON_B
                      where MOBILE = a.MOBILE
                      for xml path('')), 1, 1, '') as rytps
        from BNS_DPDSPERSON_B a
        where a.MOBILE is not null
          and a.mobile != ''
        group by a.MOBILE

        union all

        select MOBILE sid, '危险区责任人' rytps
        from BNS_IA_DANDFOBJ a
        where a.MOBILE is not null
          and a.mobile != ''
        group by a.MOBILE
    </select>
    <select id="getWarnMessagePage" resultType="com.huitu.cloud.api.common.msg.entity.WarnMessage" useCache="false">
        SELECT A.UNAME RECEIVER, A.[SID] MOBILENO, B.SENDTM, A.SENDRESULT, C.FEEDBACK, C.CONFIRM_TIME
        FROM MESSAGESEND_R A
        LEFT JOIN MESSAGEINFO_R B ON B.MSGID = A.MSGID AND B.ADCD = A.ADCD AND CODE = YEAR(B.SENDTM)
        LEFT JOIN (SELECT MOBILE_NO, MAX([STATUS]) FEEDBACK, MAX([CONFIRM_TIME]) CONFIRM_TIME FROM WARN_CALL_FEEDBACK
        WHERE WARN_ID = #{map.warnId} GROUP BY MOBILE_NO) C ON C.MOBILE_NO = A.[SID]
        WHERE B.WARNID = #{map.warnId} AND B.MEDIAID = #{map.mediaId}
        <if test="map.status != null and map.status != ''">
            AND A.SENDRESULT = #{map.status}
        </if>
        ORDER BY C.FEEDBACK DESC, B.SENDTM ASC, A.UNAME ASC
    </select>
</mapper>
