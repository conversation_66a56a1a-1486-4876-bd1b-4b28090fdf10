package com.huitu.cloud.api.ewci.weChat.entity;

import io.swagger.annotations.ApiModelProperty;

public class WeChatRainByYwmh extends WeChatRainByDutyRecort{
    @ApiModelProperty(value = "雨量告警总数量")
    private int ylTotal;
    @ApiModelProperty(value = "雨量超警戒数量")
    private int ylCjTotal;
    @ApiModelProperty(value = "雨量超危险数量")
    private int ylCwTotal;

    public int getYlTotal() {
        return ylTotal;
    }

    public void setYlTotal(int ylTotal) {
        this.ylTotal = ylTotal;
    }

    public int getYlCjTotal() {
        return ylCjTotal;
    }

    public void setYlCjTotal(int ylCjTotal) {
        this.ylCjTotal = ylCjTotal;
    }

    public int getYlCwTotal() {
        return ylCwTotal;
    }

    public void setYlCwTotal(int ylCwTotal) {
        this.ylCwTotal = ylCwTotal;
    }
}
