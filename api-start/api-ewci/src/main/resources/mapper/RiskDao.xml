<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.monitor.mapper.RiskDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <!--  根据测站编码查询政区、经度、纬度-->
    <select id="getLocation" resultType="com.huitu.cloud.api.ewci.monitor.entity.RiskLocation">
        SELECT ADCD,
               PLGTD AS LGTD,
               PLTTD AS LTTD
        FROM BSN_STBPRP_V
        WHERE STCD = #{stcd}
    </select>
    <select id="getRainfallByAd" resultType="com.huitu.cloud.api.ewci.monitor.entity.RainData">
        SELECT T.STCD, B.STNM, B.STTP, T.ACCP, R1.RAINFALL RAINFALL1, R3.RAINFALL RAINFALL3, R6.RAINFALL RAINFALL6
        FROM (SELECT A.STCD, SUM(A.DRP) ACCP
              FROM ST_PPTN_R A
                       RIGHT JOIN (SELECT DISTINCT STCD
                                   FROM BSN_STBPRP_V
                                   WHERE FRGRD != '8' AND STTP IN ('PP','RQ','ZQ') AND LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}
                                       , #{map.level})) B ON B.STCD = A.STCD
              WHERE A.INTV = 1
                AND A.TM >= #{map.stm}
                AND A.TM &lt;= #{map.etm}
              GROUP BY A.STCD) T
                 LEFT JOIN BSN_STBPRP_V B ON T.STCD = B.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm1}
                            GROUP BY STCD) R1 ON R1.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm3}
                            GROUP BY STCD) R3 ON R3.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm6}
                            GROUP BY STCD) R6 ON R6.STCD = T.STCD
        ORDER BY T.ACCP DESC, T.STCD ASC
    </select>
    <select id="getRainfallByWs" resultType="com.huitu.cloud.api.ewci.monitor.entity.RainData">
        SELECT T.STCD, B.STNM, B.STTP, T.ACCP, R1.RAINFALL RAINFALL1, R3.RAINFALL RAINFALL3, R6.RAINFALL RAINFALL6
        FROM (SELECT A.STCD, SUM(A.DRP) ACCP
              FROM ST_PPTN_R A
                       RIGHT JOIN (SELECT DISTINCT V.STCD
                                   FROM BSN_STBPRP_V V
                                            left join
                                        (SELECT DISTINCT Q.ADCD
                                         FROM IA_C_WSADCD Q
                                                  LEFT JOIN IA_C_WSADCD E ON Q.WSCD = E.WSCD
                                         WHERE LEFT (E.ADCD
                                             , #{map.level}) = LEFT (#{map.adcd}
                                             , #{map.level})) W on V.ADCD = W.ADCD
                                   WHERE V.FRGRD != '8' AND V.STTP IN ('PP','RQ','ZQ') AND LEFT (V.ADCD , 12) = LEFT (W.ADCD , 12)) B
                                  ON B.STCD = A.STCD
              WHERE A.INTV = 1
                AND A.TM >= #{map.stm}
                AND A.TM &lt;= #{map.etm}
              GROUP BY A.STCD) T
                 LEFT JOIN BSN_STBPRP_V B ON T.STCD = B.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm1}
                            GROUP BY STCD) R1 ON R1.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm3}
                            GROUP BY STCD) R3 ON R3.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm6}
                            GROUP BY STCD) R6 ON R6.STCD = T.STCD
        ORDER BY T.ACCP DESC, T.STCD ASC
    </select>
    <select id="getRainfallByRange" resultType="com.huitu.cloud.api.ewci.monitor.entity.RainData">
        SELECT T.STCD, B.STNM, B.STTP, T.ACCP, R1.RAINFALL RAINFALL1, R3.RAINFALL RAINFALL3, R6.RAINFALL RAINFALL6
        FROM (SELECT A.STCD, SUM(A.DRP) ACCP
              FROM ST_PPTN_R A
                       RIGHT JOIN (SELECT DISTINCT STCD
                                   FROM BSN_STBPRP_V
                                   WHERE FRGRD != '8' AND STTP IN ('PP','RQ','ZQ') AND dbo.fnGetDistance(#{map.lgtd}
                                       , #{map.lttd},LGTD, LTTD) &lt;= #{map.range}) B ON B.STCD = A.STCD
              WHERE A.INTV = 1
                AND A.TM >= #{map.stm}
                AND A.TM &lt;= #{map.etm}
              GROUP BY A.STCD) T
                 LEFT JOIN BSN_STBPRP_V B ON T.STCD = B.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm1}
                            GROUP BY STCD) R1 ON R1.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm3}
                            GROUP BY STCD) R3 ON R3.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm6}
                            GROUP BY STCD) R6 ON R6.STCD = T.STCD
        ORDER BY T.ACCP DESC, T.STCD ASC
    </select>

</mapper>
