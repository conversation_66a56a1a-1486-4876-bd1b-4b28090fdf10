package com.huitu.cloud.api.datamanager.rule.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.datamanager.rule.entity.*;
import com.huitu.cloud.api.datamanager.rule.mapper.BsnZrrulebDao;
import com.huitu.cloud.api.datamanager.rule.mapper.RainRuleDao;
import com.huitu.cloud.api.datamanager.rule.service.RuleService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 雨量规则配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-06
 */
@Service
public class RuleServiceImpl implements RuleService {
    @Autowired
    private RainRuleDao rainRuleDao;
    @Autowired
    private BsnZrrulebDao bsnZrrulebDao;

    @Override
    public boolean updateById(BsnRainruleb entity) {
        entity.setModitime(new Date());
        rainRuleDao.updateById(entity);
        return true;
    }

    @Override
    public boolean save(BsnRainruleb entity) {
        entity.setModitime(new Date());
        rainRuleDao.insert(entity) ;
        return true;
    }

    @Override
    @Transactional
    public boolean saveAll(RainruleEx entity) {
        int adLevel= AdcdUtil.getAdLevel(entity.getAdcd());
        String ad="";
        if(adLevel>0){
            ad=entity.getAdcd().substring(0,adLevel);
        }
        //先删除原来规则
        rainRuleDao.deleteAll(ad,adLevel);
        entity.setAdcd(ad);
        //插入新的全部的数据
        rainRuleDao.batchInsertAll(entity,adLevel);
        return true;
    }

    @Override
    public boolean del(String stcd) {
        rainRuleDao.deleteById(stcd);
        return true;
    }

    @Override
    public IPage<RainRuleVo> getRainRule(String stnm,String adcd, int pageNum, int pageSize) {
        Page page=new Page(pageNum,pageSize);
        int adLevel= AdcdUtil.getAdLevel(adcd);
        String ad="";
        if(adLevel>0){
            ad=adcd.substring(0,adLevel);
        }
        return rainRuleDao.getRainRule(page,stnm,ad,adLevel);
    }

    @Override
    public boolean updateZrById(BsnZrruleb entity) {
        entity.setModitime(new Date());
        bsnZrrulebDao.updateById(entity);
        return true;
    }

    @Override
    public boolean saveZr(BsnZrruleb entity) {
        entity.setModitime(new Date());
        bsnZrrulebDao.insert(entity);
        return true;
    }

    @Override
    public boolean delZr(String stcd) {
        bsnZrrulebDao.deleteById(stcd);
        return true;
    }

    @Override
    public IPage<ZrruleVo> getZrRule(String sttp, String stnm,String adcd, int pageNum, int pageSize) {
        Page page=new Page(pageNum,pageSize);
        int adLevel= AdcdUtil.getAdLevel(adcd);
        String ad="";
        if(adLevel>0){
            ad=adcd.substring(0,adLevel);
        }
        return bsnZrrulebDao.getZrRule(page,stnm,sttp,ad);
    }

    @Override
    public void exportRainRule(String stnm,String adcd, HttpServletResponse response) {
        Page page=new Page(1,-1);
        int adLevel= AdcdUtil.getAdLevel(adcd);
        String ad="";
        if(adLevel>0){
            ad=adcd.substring(0,adLevel);
        }
        IPage<RainRuleVo>  iPage=rainRuleDao.getRainRule(page,stnm,ad,adLevel);
        List<RainRuleVo> list=iPage.getRecords();
        List<RainruleModel> data=new ArrayList<>();
        list.forEach(x->{
            RainruleModel model=new RainruleModel();
            model.setStcd(x.getStcd());
            model.setStnm(x.getStnm());
            model.setMncnt(x.getInv());
            model.setCtpm(x.getCtpm5());
            model.setSnv(x.getSnv());
            model.setNt(x.getNt());
            data.add(model);
        });
        ExcelExportUtil.excleDownloadModel(data,RainruleModel.class,"雨量规则",response);
    }

    @Override
    public void exportZrRule(String sttp, String stnm, String adcd,HttpServletResponse response) {
        IPage<ZrruleVo> iPage=getZrRule(sttp,stnm,adcd,1,-1);
        List<ZrruleVo> list=iPage.getRecords();
        List<BsnZrrulebModel> data=new ArrayList<>();
        list.forEach(x->{
            BsnZrrulebModel model=new BsnZrrulebModel();
            model.setStcd(x.getStcd());
            model.setStnm(x.getStnm());
            model.setUpv(x.getUpv());
            model.setDwv(x.getDwv());
            model.setChv(x.getChv());
            model.setNt(x.getNt());
            data.add(model);
        });
        ExcelExportUtil.excleDownloadModel(data,BsnZrrulebModel.class,"水位规则",response);
    }

    @Override
    public String checkRainRuleIsExist(String stcd) {
        BsnRainruleb rainruleb=rainRuleDao.selectById(stcd);
        if(rainruleb !=null){
            return "1";
        }
        return "0";
    }

    @Override
    public String checkZrRuleIsExist(String stcd) {
        BsnZrruleb zrruleb=bsnZrrulebDao.selectById(stcd);
        if(zrruleb!=null){
            return "1";
        }
        return "0";
    }
}
