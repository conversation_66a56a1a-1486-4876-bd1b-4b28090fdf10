package com.huitu.cloud.api.syq.river.entity;

import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
/**
 * <AUTHOR>
 * @since 2019-09-11
 */
public class RiverQo extends PageBean {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "测站归属类型 1、水文 2、山洪  ")
    private List<String> stType;
    @ApiModelProperty(value = "政区编码",required = true)
    @SqlInjection
    private String adcd;
    @ApiModelProperty(value = "流域编码")
    private String bscd;
    @ApiModelProperty(value = "流域名称")
    private String bsnm;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "超警戒标志  true： 超警戒 false 全部数据")
    private boolean warnFlag;
    @ApiModelProperty(value = "显示数据控制 true：只显示有数据的 false 显示全部 (暂不使用！！！)")
    private boolean dataFlag;

    @ApiModelProperty(value = "是否是辖区外 1：辖区内，2：辖区外")
    private List<String> isOut;
    @ApiModelProperty(value = "是否关注 1：关注，2：未关注")
    private List<String> isFollow;
    @ApiModelProperty(value = "仅有视频标志  true： 仅有视频")
    private boolean videoFlag;

    public List<String> getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(List<String> isFollow) {
        this.isFollow = isFollow;
    }

    public List<String> getIsOut() {
        return isOut;
    }

    public void setIsOut(List<String> isOut) {
        this.isOut = isOut;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public boolean isWarnFlag() {
        return warnFlag;
    }

    public void setWarnFlag(boolean warnFlag) {
        this.warnFlag = warnFlag;
    }

    public boolean isDataFlag() {
        return dataFlag;
    }

    public void setDataFlag(boolean dataFlag) {
        this.dataFlag = dataFlag;
    }

    public boolean isVideoFlag() {
        return videoFlag;
    }

    public void setVideoFlag(boolean videoFlag) {
        this.videoFlag = videoFlag;
    }
}
