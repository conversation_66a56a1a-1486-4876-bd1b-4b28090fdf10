package com.huitu.cloud.api.sms.task;

import com.huitu.cloud.api.sms.entity.MessageSend;
import com.huitu.cloud.api.sms.entity.MsgResponse;
import com.huitu.cloud.api.sms.entity.SendMsgResult;
import com.huitu.cloud.api.sms.service.SmsService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
public class SmsMsgTask {

    private static final Logger logger = LoggerFactory.getLogger(SmsMsgTask.class);

    private static final String DEFAULT_PATTERN = "yyyyMMddHHmmss";
    private static final int TM_SIZE_MAX = 1440;

    private SmsService smsService;

    @Value("${sms.sendMsgTmSize:30}")
    private int sendMsgTmSize;

    @Value("${sms.sendMsgLimit:200}")
    private int sendMsgLimit;

    @Value("${sms.sendMsgEnabled:true}")
    private boolean sendMsgEnabled;

    @Value("${sms.getMsgTmSize:1440}")
    private int getMsgTmSize;

    @Value("${sms.getMsgEnabled:true}")
    private boolean getMsgEnabled;

    @Value("${sms.getMsgType:intv}")
    private String getMsgType;

    @Resource
    private ThreadPoolExecutor sendMsgTaskExecutor;

    @Resource
    private ThreadPoolExecutor getMsgResultTaskExecutor;

    @Autowired
    public void setSmsService(SmsService smsService) {
        this.smsService = smsService;
    }

    public int getSendMsgTmSize() {
        return Math.min(Math.max(sendMsgTmSize, 30), TM_SIZE_MAX);
    }

    public int getSendMsgLimit() {
        return Math.min(Math.max(sendMsgLimit, 10), 10000);
    }

    public int getGetMsgTmSize() {
        return Math.min(Math.max(getMsgTmSize, 60), TM_SIZE_MAX);
    }

    /**
     * 发送短信定时任务
     */
    @Scheduled(fixedDelayString = "${sms.sendMsgInterval:60000}")
    public void sendMsgTask() {
        if (!checkEnabled(sendMsgEnabled, "短信发送", "sms.sendMsgEnabled")) {
            return;
        }

        String batchCode = UUID.randomUUID().toString().toUpperCase();
        logger.info("【短信发送】任务<{}>开始", batchCode);

        LocalTime beginTm = LocalTime.now();
        try {
            int tmSize = getSendMsgTmSize();
            // 查询需要发送记录信息
            List<MessageSend> msList = smsService.getSendList(DateUtils.addMinutes(new Date(), -tmSize), getSendMsgLimit());
            if (logger.isInfoEnabled()) {
                logger.info("【短信发送】待发送短信{}条", msList.size());
            }
            if (!msList.isEmpty()) {
                List<SendMsgResult.Result> results = Collections.synchronizedList(new ArrayList<>());
                CountDownLatch cdl = new CountDownLatch(msList.size());
                for (MessageSend ms : msList) {
                    try {
                        sendMsgTaskExecutor.execute(() -> {
                            try {
                                results.addAll(smsService.sendMsg(ms, batchCode).getData());
                            } catch (Exception ex) {
                                logger.error("【短信发送】未知异常", ex);
                            } finally {
                                cdl.countDown();
                            }
                        });
                    } catch (Exception ex) {
                        cdl.countDown();

                        logger.error("【短信发送】线程池异常", ex);
                    }
                }
                // 等待返回结果
                if (!cdl.await((tmSize / 3) * 2, TimeUnit.MINUTES)) {
                    logger.warn("【短信发送】线程池同步等待超时，剩余未响应线程{}个", cdl.getCount());
                }
                if (logger.isInfoEnabled()) {
                    int total = results.size();
                    try {
                        int success = (int) results.stream()
                                .filter(SendMsgResult.Result::isSuccess).count();
                        logger.info("【短信发送】实际发送{}条，成功{}条，失败{}条", total, success, total - success);
                    } catch (Exception ex) {
                        logger.warn("【短信发送】汇总统计失败，实际发送{}条，发送情况未知", total);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("【短信发送】任务<" + batchCode + ">失败", ex);
        } finally {
            if (logger.isInfoEnabled()) {
                logger.info("【短信发送】任务<{}>结束，耗时->{}s", batchCode, ChronoUnit.SECONDS.between(beginTm, LocalTime.now()));
            }
        }
    }

    /**
     * 获取短信发送的反馈信息(10分钟一执行)
     */
    @Scheduled(fixedDelayString = "${sms.getMsgInterval:600000}")
    public void getMsgResultTask() {
        if (!"intv".equals(getMsgType) || !checkEnabled(getMsgEnabled, "短信反馈", "sms.getMsgEnabled")) {
            return;
        }

        String batchCode = UUID.randomUUID().toString().toUpperCase();
        logger.info("【短信反馈】任务<{}>开始", batchCode);

        LocalTime beginTm = LocalTime.now();
        try {
            int tmSize = getGetMsgTmSize();
            Date endDate = new Date();
            Date startDate = DateUtils.addMinutes(endDate, -tmSize);
            List<String> phoneNoList = smsService.getPhoneNoList(startDate, endDate);
            if (logger.isInfoEnabled()) {
                logger.info("【短信反馈】{}小时内发送但未反馈的手机号{}个", tmSize / 60, phoneNoList.size());
            }
            executeMsgResult(phoneNoList, startDate, endDate, tmSize);
        } catch (Exception ex) {
            logger.error("【短信反馈】任务<" + batchCode + ">失败", ex);
        }
        if (logger.isInfoEnabled()) {
            logger.info("【短信反馈】任务<{}>结束，耗时->{}s", batchCode, ChronoUnit.SECONDS.between(beginTm, LocalTime.now()));
        }
    }

    /**
     * 获取短信发送的反馈信息(每天6时、18时执行)
     */
    @Scheduled(cron = "${sms.getMsgCron:0 0 6,18 * * ?}")
    public void getMsgResultCronTask() {
        if (!"cron".equals(getMsgType) || !checkEnabled(getMsgEnabled, "短信反馈", "sms.getMsgEnabled")) {
            return;
        }

        String batchCode = UUID.randomUUID().toString().toUpperCase();
        logger.info("【短信反馈】任务<{}>开始", batchCode);

        LocalTime beginTm = LocalTime.now();
        try {
            Date endDate = DateUtils.truncate(new Date(), Calendar.DATE);
            Date startDate = DateUtils.addDays(endDate, -1);
            List<String> phoneNoList = smsService.getPhoneNoList(startDate, endDate);
            if (logger.isInfoEnabled()) {
                logger.info("【短信反馈】{}发送但未反馈的手机号{}个",
                        DateFormatUtils.format(startDate, "yyyy年MM月dd日"), phoneNoList.size());
            }
            executeMsgResult(phoneNoList, startDate, DateUtils.addSeconds(endDate, -1), 1440);
        } catch (Exception ex) {
            logger.error("【短信反馈】任务<" + batchCode + ">失败", ex);
        }
        if (logger.isInfoEnabled()) {
            logger.info("【短信反馈】任务<{}>结束，耗时->{}s", batchCode, ChronoUnit.SECONDS.between(beginTm, LocalTime.now()));
        }
    }

    private Map<String, String> getTmRanges(Date startDate, Date endDate) {
        Map<String, String> map = new HashMap<>();
        if (DateUtils.truncatedCompareTo(startDate, endDate, Calendar.MONTH) != 0) {
            map.put(DateFormatUtils.format(startDate, DEFAULT_PATTERN), DateFormatUtils.format(startDate, "yyyyMMdd235959"));
            map.put(DateFormatUtils.format(endDate, "yyyyMMdd000000"), DateFormatUtils.format(endDate, DEFAULT_PATTERN));
        } else {
            map.put(DateFormatUtils.format(startDate, DEFAULT_PATTERN), DateFormatUtils.format(endDate, DEFAULT_PATTERN));
        }
        return map;
    }

    private boolean checkEnabled(boolean enabled, String taskName, String propName) {
        if (!enabled) {
            logger.info("【{}】任务已停用，如需启用，请手动修改配置文件application.properties中的{}选项为true，然后重启服务", taskName, propName);
        }
        return enabled;
    }

    private void executeMsgResult(List<String> phoneNoList, Date startDate, Date endDate, int tmSize) throws InterruptedException {
        if (phoneNoList.isEmpty()) {
            return;
        }
        Map<String, String> tmRanges = getTmRanges(startDate, endDate);
        CountDownLatch cdl = new CountDownLatch(tmRanges.size() * phoneNoList.size());

        List<MsgResponse> resList = Collections.synchronizedList(new ArrayList<>());
        tmRanges.forEach((stm, etm) -> {
            logger.info("【短信反馈】查询时间范围：{}～{}", stm, etm);
            for (String phoneNo : phoneNoList) {
                try {
                    getMsgResultTaskExecutor.execute(() -> {
                        try {
                            List<MsgResponse> tmpList = smsService.getResult(phoneNo, stm, etm);
                            if (!tmpList.isEmpty()) {
                                resList.addAll(tmpList);
                            }
                        } catch (Exception ex) {
                            logger.error("【短信反馈】未知异常", ex);
                        } finally {
                            cdl.countDown();
                        }
                    });
                } catch (Exception ex) {
                    cdl.countDown();

                    logger.error("【短信反馈】线程池异常", ex);
                }
            }
        });
        // 等待返回结果
        if (!cdl.await((tmSize / 3) * 2, TimeUnit.MINUTES)) {
            logger.warn("【短信反馈】线程池同步等待超时，剩余未响应线程{}个", cdl.getCount());
        }
        if (logger.isInfoEnabled()) {
            logger.info("【短信反馈】本次查询到{}条反馈记录", resList.size());
        }
        if (!resList.isEmpty()) {
            try {
                int validCount = smsService.updateResult(resList, startDate, endDate);
                logger.info("【短信反馈】本次更新了{}条反馈记录", validCount);
            } catch (Exception ex) {
                logger.error("【短信反馈】数据库操作异常", ex);
            }
        }
    }
}
