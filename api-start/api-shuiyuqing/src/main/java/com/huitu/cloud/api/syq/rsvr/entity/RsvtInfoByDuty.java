package com.huitu.cloud.api.syq.rsvr.entity;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class RsvtInfoByDuty {

    @ApiModelProperty(value = "大型水库总数")
    private int bigRsvrCount;
    @ApiModelProperty(value = "中型水库总数")
    private int middleRsvrCount;
    @ApiModelProperty(value = "小型水库总数")
    private int smallRsvrCount;
    @ApiModelProperty(value = "大型水库超汛线范围")
    private String bigRsvr;
    @ApiModelProperty(value = "中型水库超汛线范围")
    private String middleRsvr;
    @ApiModelProperty(value = "小型水库超汛线范围")
    private String smallRsvr;
    @ApiModelProperty(value = "中型水库前三名称")
    private String middleRsvrName;
    @ApiModelProperty(value = "大型水库超汛限集合")
    private List<StRsvrVo> bigList;

    @ApiModelProperty(value = "水库中型前三集合")
    private List<StRsvrVo> middleList;

    @ApiModelProperty(value = "中型水库超汛限集合")
    private List<StRsvrVo> middleAllList;
    @ApiModelProperty(value = "小型水库超汛限集合")
    private List<StRsvrVo> smallList;

    @ApiModelProperty(value = "水库总数")
    private int total;

    public List<StRsvrVo> getBigList() {
        return bigList;
    }

    public void setBigList(List<StRsvrVo> bigList) {
        this.bigList = bigList;
    }

    public int getBigRsvrCount() {
        return bigRsvrCount;
    }

    public void setBigRsvrCount(int bigRsvrCount) {
        this.bigRsvrCount = bigRsvrCount;
    }

    public int getMiddleRsvrCount() {
        return middleRsvrCount;
    }

    public void setMiddleRsvrCount(int middleRsvrCount) {
        this.middleRsvrCount = middleRsvrCount;
    }

    public int getSmallRsvrCount() {
        return smallRsvrCount;
    }

    public void setSmallRsvrCount(int smallRsvrCount) {
        this.smallRsvrCount = smallRsvrCount;
    }

    public String getBigRsvr() {
        return bigRsvr;
    }

    public void setBigRsvr(String bigRsvr) {
        this.bigRsvr = bigRsvr;
    }

    public String getMiddleRsvr() {
        return middleRsvr;
    }

    public void setMiddleRsvr(String middleRsvr) {
        this.middleRsvr = middleRsvr;
    }

    public String getSmallRsvr() {
        return smallRsvr;
    }

    public void setSmallRsvr(String smallRsvr) {
        this.smallRsvr = smallRsvr;
    }

    public String getMiddleRsvrName() {
        return middleRsvrName;
    }

    public void setMiddleRsvrName(String middleRsvrName) {
        this.middleRsvrName = middleRsvrName;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<StRsvrVo> getMiddleList() {
        return middleList;
    }

    public void setMiddleList(List<StRsvrVo> middleList) {
        this.middleList = middleList;
    }

    public List<StRsvrVo> getMiddleAllList() {
        return middleAllList;
    }

    public void setMiddleAllList(List<StRsvrVo> middleAllList) {
        this.middleAllList = middleAllList;
    }

    public List<StRsvrVo> getSmallList() {
        return smallList;
    }

    public void setSmallList(List<StRsvrVo> smallList) {
        this.smallList = smallList;
    }
}
