package com.huitu.cloud.api.ew.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.base.EwWarningGrade;
import com.huitu.cloud.api.ew.entity.base.EwWarningStatus;
import com.huitu.cloud.api.ew.entity.ext.EwMessageReceiver;
import com.huitu.cloud.api.ew.entity.ext.EwWarningFlag;
import com.huitu.cloud.api.ew.entity.ext.EwWarningSending;
import com.huitu.cloud.api.ew.entity.request.*;
import com.huitu.cloud.api.ew.entity.response.*;
import com.huitu.cloud.api.usif.user.entity.UserInfos;

import java.util.List;

/**
 * 山洪预警服务
 */
public interface EwWarningService {

    /**
     * 获取预警等级列表
     *
     * @return 预警等级列表
     */
    List<EwWarningGrade> getGradeList();

    /**
     * 获取预警状态列表
     *
     * @return 预警状态列表
     */
    List<EwWarningStatus> getStatusList();

    /**
     * 获取完整政区名称
     *
     * @param request 请求参数
     * @return 完整政区名称
     */
    String getFullAdnm(EwFullAdnmRequest request);

    /**
     * 获取预警分页列表
     *
     * @param request 请求参数
     * @return 预警分页列表
     **/
    IPage<EwWarningRecordResponse> getPageList(EwWarningRecordRequest request);

    /**
     * 获取最新的山洪预警分页列表
     *
     * @param request 请求参数
     * @return 预警分页列表
     **/
    IPage<EwWarningRecordResponse> getLatestPageList(EwWarningRecordRequest request);

    /**
     * 根据预警ID集合，获取预警摘要列表
     *
     * @param warnIds 预警ID集合
     * @return 摘要列表
     */
    List<EwWarningDigestResponse> getDigestList(List<String> warnIds);

    /**
     * 根据预警ID和预警等级，获取预警记录
     *
     * @param warnId      预警ID
     * @param warnGradeId 预警等级ID
     * @return 预警记录
     */
    EwWarningRecordResponse getRecord(String warnId, Integer warnGradeId);

    /**
     * 获取预警消息接收人列表
     *
     * @param villageAdcd 村级行政区划代码
     * @param userAdcd    用户行政区划代码
     * @return 接收人列表
     */
    List<EwMessageReceiver> getMessageReceiverList(String villageAdcd, String userAdcd);

    /**
     * 判断平台是否启用了预警
     *
     * @param platformId 平台ID
     * @return true：启用，false：禁用
     */
    boolean isEnabled(String platformId);

    /**
     * 创建突发预警
     *
     * @param request 请求参数
     * @param user    用户信息
     * @return 受影响的行数
     */
    int create(EwWarningCreateRequest request, UserInfos user);

    /**
     * 关闭（解除）预警
     *
     * @param request 请求参数
     * @param user    用户信息
     * @return 受影响的行数
     */
    int close(EwWarningCloseRequest request, UserInfos user);

    /**
     * 根据预警ID，获取预警过程列表
     *
     * @param warnId 预警ID
     * @return 过程列表
     */
    List<EwWarningProcessResponse> getProcessList(String warnId);

    /**
     * 根据预警ID，获取预警流程列表
     *
     * @param warnId 预警ID
     * @return 流程列表
     */
    List<EwWarningFlowResponse> getFlowList(String warnId);

    /**
     * 根据预警ID，获取预警消息列表
     *
     * @param warnId 预警ID
     * @return 消息列表
     */
    List<EwWarningMessageResponse<?>> getMessageList(String warnId);

    /**
     * 根据预警ID，获取对应的预警指标快照列表
     *
     * @param warnId 预警ID
     * @return 指标快照列表
     */
    List<EwSnapshotIndexResponse> getSnapshotIndexList(String warnId);

    /**
     * 根据预警ID和预警等级，获取对应的预警指标快照
     *
     * @param warnId      预警ID
     * @param warnGradeId 预警等级ID
     * @return 指标快照
     */
    EwSnapshotIndexResponse getSnapshotIndex(String warnId, Integer warnGradeId);

    /**
     * 根据预警ID和预警等级，获取对应的预警监测数据快照
     *
     * @param warnId      预警ID
     * @param warnGradeId 预警等级ID
     * @return 监测数据快照
     */
    EwSnapshotMonitorResponse getSnapshotMonitor(String warnId, Integer warnGradeId);

    /**
     * 获取预警发送记录分页列表
     *
     * @param request 请求参数
     * @return 发送记录分页列表
     */
    IPage<EwWarningSending> getSendingPageList(EwWarningSendingRequest<EwWarningSending> request);

    /**
     * 获取预警标识
     *
     * @param msgId 消息ID
     * @return 预警标识
     */
    EwWarningFlag getWarningFlag(String msgId);

    /**
     * 获取待更正的预警记录列表
     *
     * @param warnId 当前预警ID
     * @return 预警记录列表
     */
    List<EwWarningCorrectResponse> getCorrectList(String warnId);

    /**
     * 更正预警
     *
     * @param request 请求参数
     * @param user    用户信息
     * @return 受影响的行数
     */
    int correct(EwWarningCorrectRequest request, UserInfos user);

    /**
     * 根据消息ID，获取对应的预警监测数据
     *
     * @param msgId 消息ID
     * @return 监测数据
     */
    EwShMonitorDataResponse getMonitorData(String msgId);

    /**
     * 根据消息ID，获取对应的预警影响村屯
     *
     * @param msgId 消息ID
     * @return 影响村屯
     */
    List<EwShVillageDataResponse> getVillageList(String msgId);

    /**
     * 根据消息ID，获取对应的预警降雨过程
     *
     * @param msgId 消息ID
     * @return 降雨过程
     */
    List<EwShPptnDataResponse> getPptnList(String msgId);
}
