package com.huitu.cloud.api.ewci.person.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

/**
 * 险工险段责任人管理服务
 *
 * <AUTHOR>
 */
public interface DpdsPersonService {

    /**
     * 分页获取险工险段责任人信息列表
     *
     * @param query 查询条件
     * @return 分页后的信息列表
     **/
    IPage<DpdsPerson> getPageList(DpdsPersonQuery query);

    /**
     * 险工险段责任人汇总统计
     *
     * @param query
     * @return
     */
    List<DpdsSummaryVo> getDpdsSummaryList(DpdsSummaryQuery query);

    /**
     * 数据导出
     *
     * @param query  查询条件
     * @param output 输出流
     **/
    void dataExport(DpdsPersonQuery query, OutputStream output);

    /**
     * 数据导入
     *
     * @param xadcd 县级行政区划代码
     * @param input 输入流
     * @return 导入结果
     **/
    ImportResult<BnsDpdspersonTmp> dataImport(String xadcd, InputStream input);

    /**
     * 编辑险工险段责任人
     * @param entity
     * @return
     */
    int updateDpds(DpdsPerson entity);


}
