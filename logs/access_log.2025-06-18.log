************ - - [18/Jun/2025:14:48:31 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [18/Jun/2025:14:48:31 +0800] "GET /login HTTP/1.1" 302 -
************ - - [18/Jun/2025:14:48:32 +0800] "GET /login?code=6QkbcX&state=393y3b HTTP/1.1" 302 -
************ - - [18/Jun/2025:14:48:32 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [18/Jun/2025:14:48:33 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750229313486 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750229313486 HTTP/1.1" 200 552
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750229317694 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750229317694 HTTP/1.1" 200 61649
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750229317789 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750229317789 HTTP/1.1" 200 10388
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750229317809 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750229317809 HTTP/1.1" 200 2009
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750229317978 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+14:48:37&etm=&_timer304=1750229317980 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750229317980 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+15:00&filterCnt=6&_timer304=1750229317980 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750229317980 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750229317980 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:37 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:38 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750229317978 HTTP/1.1" 200 1482
************ - - [18/Jun/2025:14:48:38 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+15:00&filterCnt=6&_timer304=1750229317980 HTTP/1.1" 200 164
************ - - [18/Jun/2025:14:48:38 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750229317980 HTTP/1.1" 200 166
************ - - [18/Jun/2025:14:48:38 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+14:48:37&etm=&_timer304=1750229317980 HTTP/1.1" 200 156
************ - - [18/Jun/2025:14:48:38 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:14:48:38 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:14:48:38 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [18/Jun/2025:14:48:38 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [18/Jun/2025:14:48:38 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750229317980 HTTP/1.1" 200 169
************ - - [18/Jun/2025:14:48:38 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750229317980 HTTP/1.1" 200 13016
************ - - [18/Jun/2025:14:48:38 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:38 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-18&_timer304=1750229318795 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:38 +0800] "OPTIONS /api/base/saas/token?_timer304=1750229318795 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:38 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:38 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:38 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:38 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:38 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:38 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:39 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:39 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750229318813 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:39 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:39 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:39 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:43 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750229323175 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750229323181 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:48:44 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [18/Jun/2025:14:48:47 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:14:48:47 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2517
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 2881
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [18/Jun/2025:14:48:48 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750229318813 HTTP/1.1" 200 2009
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [18/Jun/2025:14:48:48 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 302
************ - - [18/Jun/2025:14:48:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750229323175 HTTP/1.1" 200 160
************ - - [18/Jun/2025:14:48:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750229323181 HTTP/1.1" 200 159
************ - - [18/Jun/2025:14:48:51 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [18/Jun/2025:14:48:52 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-18&_timer304=1750229318795 HTTP/1.1" 200 348
************ - - [18/Jun/2025:14:48:53 +0800] "GET /api/base/saas/token?_timer304=1750229318795 HTTP/1.1" 200 411
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750229373996 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750229374196 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+14:49:34&etm=&_timer304=1750229374196 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750229374196 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+15:00&filterCnt=6&_timer304=1750229374196 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750229374196 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750229374197 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750229374201 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:14:49:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:14:49:34 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [18/Jun/2025:14:49:34 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [18/Jun/2025:14:49:34 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+14:49:34&etm=&_timer304=1750229374196 HTTP/1.1" 200 156
************ - - [18/Jun/2025:14:49:34 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+15:00&filterCnt=6&_timer304=1750229374196 HTTP/1.1" 200 164
************ - - [18/Jun/2025:14:49:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750229374196 HTTP/1.1" 200 166
************ - - [18/Jun/2025:14:49:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750229374197 HTTP/1.1" 200 161
************ - - [18/Jun/2025:14:49:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750229374196 HTTP/1.1" 200 169
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750229374344 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750229374366 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:34 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750229374196 HTTP/1.1" 200 13016
************ - - [18/Jun/2025:14:49:34 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750229374201 HTTP/1.1" 200 159491
************ - - [18/Jun/2025:14:49:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [18/Jun/2025:14:49:34 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [18/Jun/2025:14:49:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [18/Jun/2025:14:49:34 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750229374366 HTTP/1.1" 200 155
************ - - [18/Jun/2025:14:49:34 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [18/Jun/2025:14:49:34 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750229373996 HTTP/1.1" 200 144
************ - - [18/Jun/2025:14:49:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [18/Jun/2025:14:49:35 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:14:49:35 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750229375445 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:35 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750229375445 HTTP/1.1" 200 155
************ - - [18/Jun/2025:14:49:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750229374344 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:14:49:36 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:14:49:36 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3671
************ - - [18/Jun/2025:14:49:37 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750229377015 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:37 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:37 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:14:49:37 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750229377015 HTTP/1.1" 200 232
************ - - [18/Jun/2025:14:49:38 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:38 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:49:38 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 93185
************ - - [18/Jun/2025:14:49:38 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 93185
************ - - [18/Jun/2025:14:50:02 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812745&_timer304=1750229402283 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:50:02 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220523107200100&_timer304=1750229402283 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:50:02 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10812745&_timer304=1750229402283 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:50:02 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10812745&stm=2025-06-18+08:00&etm=2025-06-18+14:48&_timer304=1750229402299 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:50:02 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10812745&stm=2025-06-18+08:00&etm=2025-06-18+14:48&_timer304=1750229402299 HTTP/1.1" 200 160
************ - - [18/Jun/2025:14:50:02 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812745&_timer304=1750229402283 HTTP/1.1" 200 814
************ - - [18/Jun/2025:14:50:02 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10812745&_timer304=1750229402283 HTTP/1.1" 200 509
************ - - [18/Jun/2025:14:50:02 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220523107200100&_timer304=1750229402283 HTTP/1.1" 200 510
************ - - [18/Jun/2025:14:50:10 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750229410583 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:50:10 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750229410583 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:50:10 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750229410583 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:50:10 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+14:48&_timer304=1750229410583 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:50:10 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750229410583 HTTP/1.1" 200 831
************ - - [18/Jun/2025:14:50:10 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+14:48&_timer304=1750229410583 HTTP/1.1" 200 160
************ - - [18/Jun/2025:14:50:10 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750229410583 HTTP/1.1" 200 519
************ - - [18/Jun/2025:14:50:10 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750229410583 HTTP/1.1" 200 520
************ - - [18/Jun/2025:14:50:46 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:50:46 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 93185
************ - - [18/Jun/2025:14:53:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750229613183 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:53:33 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750229613183 HTTP/1.1" 200 160
************ - - [18/Jun/2025:14:53:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750229629299 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:53:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750229629299 HTTP/1.1" 200 159
************ - - [18/Jun/2025:14:54:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750229674023 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:54:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750229674023 HTTP/1.1" 200 161
************ - - [18/Jun/2025:14:54:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:54:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:54:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750229674269 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:54:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:54:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:14:54:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:14:54:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750229674269 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:14:54:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:14:58:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750229913174 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:58:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750229913174 HTTP/1.1" 200 160
************ - - [18/Jun/2025:14:58:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750229929336 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:58:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750229929336 HTTP/1.1" 200 159
************ - - [18/Jun/2025:14:59:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750229974023 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:59:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750229974023 HTTP/1.1" 200 161
************ - - [18/Jun/2025:14:59:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:59:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:59:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750229974283 HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:59:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:14:59:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:14:59:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:14:59:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750229974283 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:14:59:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:03:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750230213186 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:03:33 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750230213186 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:03:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750230229956 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:03:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750230229956 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:04:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750230274023 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:04:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750230274023 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:04:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:04:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:04:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750230274269 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:04:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:04:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:04:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:04:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750230274269 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:04:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:07:39 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:08:26 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 8206
************ - - [18/Jun/2025:15:08:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750230513185 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:08:33 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750230513185 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:08:35 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:08:37 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 93185
************ - - [18/Jun/2025:15:08:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750230530787 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:08:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750230530787 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:09:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750230574786 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:09:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:09:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:09:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750230574789 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:09:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:09:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750230574786 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:09:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:09:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:09:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750230574789 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:09:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:13:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750230813174 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:13:33 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750230813174 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:13:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750230830836 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:13:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750230830836 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:14:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750230874025 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:14:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750230874025 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:14:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:14:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:14:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750230874278 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:14:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:14:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:14:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:14:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750230874278 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:14:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750230969272 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750230969278 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750230969272 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:16:09 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750230969278 HTTP/1.1" 200 159491
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+15:16:09&etm=&_timer304=1750230969352 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750230969352 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+16:00&filterCnt=6&_timer304=1750230969352 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750230969352 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750230969352 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750230969471 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+15:16:09&etm=&_timer304=1750230969352 HTTP/1.1" 200 156
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750230969505 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:15:16:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:15:16:09 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+16:00&filterCnt=6&_timer304=1750230969352 HTTP/1.1" 200 164
************ - - [18/Jun/2025:15:16:09 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750230969352 HTTP/1.1" 200 166
************ - - [18/Jun/2025:15:16:09 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [18/Jun/2025:15:16:09 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [18/Jun/2025:15:16:09 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [18/Jun/2025:15:16:09 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750230969352 HTTP/1.1" 200 169
************ - - [18/Jun/2025:15:16:09 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [18/Jun/2025:15:16:09 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750230969505 HTTP/1.1" 200 155
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [18/Jun/2025:15:16:09 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:09 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [18/Jun/2025:15:16:10 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750230969352 HTTP/1.1" 200 13016
************ - - [18/Jun/2025:15:16:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [18/Jun/2025:15:16:10 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:15:16:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750230969471 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:16:11 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750230971415 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:11 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750230971415 HTTP/1.1" 200 155
************ - - [18/Jun/2025:15:16:11 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:12 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:12 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:16:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:15:16:12 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3671
************ - - [18/Jun/2025:15:16:13 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:13 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:15 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750230974138 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:15 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 93185
************ - - [18/Jun/2025:15:16:15 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 93185
************ - - [18/Jun/2025:15:16:15 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750230974138 HTTP/1.1" 200 232
************ - - [18/Jun/2025:15:16:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750230978935 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750230978935 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:16:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750230978984 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:16:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750230978984 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750231113584 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750231113591 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750231113584 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:18:33 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750231113591 HTTP/1.1" 200 159491
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+15:18:33&etm=&_timer304=1750231113713 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750231113713 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+16:00&filterCnt=6&_timer304=1750231113713 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750231113713 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750231113713 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750231113875 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+15:18:33&etm=&_timer304=1750231113713 HTTP/1.1" 200 156
************ - - [18/Jun/2025:15:18:33 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [18/Jun/2025:15:18:33 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750231113713 HTTP/1.1" 200 166
************ - - [18/Jun/2025:15:18:33 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [18/Jun/2025:15:18:33 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+16:00&filterCnt=6&_timer304=1750231113713 HTTP/1.1" 200 164
************ - - [18/Jun/2025:15:18:33 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:15:18:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [18/Jun/2025:15:18:33 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:15:18:33 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [18/Jun/2025:15:18:33 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750231113713 HTTP/1.1" 200 169
************ - - [18/Jun/2025:15:18:33 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750231113946 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:33 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750231113946 HTTP/1.1" 200 155
************ - - [18/Jun/2025:15:18:33 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750231113713 HTTP/1.1" 200 13016
************ - - [18/Jun/2025:15:18:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [18/Jun/2025:15:18:34 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:34 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:34 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [18/Jun/2025:15:18:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [18/Jun/2025:15:18:34 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:15:18:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750231113875 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:18:36 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750231116442 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:36 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750231116442 HTTP/1.1" 200 155
************ - - [18/Jun/2025:15:18:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:15:18:38 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3671
************ - - [18/Jun/2025:15:18:41 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750231121540 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:41 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750231121540 HTTP/1.1" 200 232
************ - - [18/Jun/2025:15:18:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750231122959 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750231122962 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750231122959 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:18:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750231122962 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:18:49 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:49 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:18:52 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:52 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:18:54 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 93185
************ - - [18/Jun/2025:15:18:54 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 93185
************ - - [18/Jun/2025:15:23:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750231412690 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:23:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750231412690 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:23:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750231412901 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:23:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750231412901 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:23:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:23:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:23:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750231413738 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:23:33 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:23:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:23:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:23:34 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750231413738 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:23:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:23:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750231423044 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:23:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750231423044 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:23:43 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:24:03 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 93185
************ - - [18/Jun/2025:15:28:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750231712690 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:28:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750231712690 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:28:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750231712888 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:28:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750231712888 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:28:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:28:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:28:33 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:28:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750231713737 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:28:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:28:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:28:34 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750231713737 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:28:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:28:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750231723080 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:28:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750231723080 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:33:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750232012690 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:33:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750232012690 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:33:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750232012888 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:33:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750232012888 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:33:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:33:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750232013737 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:33:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:33:33 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:33:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:33:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:33:34 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750232013737 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:33:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:33:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750232023121 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:33:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750232023121 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:38:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750232312785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:38:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750232312785 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:38:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750232313785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:38:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:38:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:38:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750232313787 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:38:33 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:38:33 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750232313785 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:38:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:38:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:38:34 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750232313787 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:38:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:38:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750232323786 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:38:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750232323786 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:43:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750232612786 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:43:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750232612786 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:43:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750232613785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:43:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:43:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:43:33 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:43:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750232613787 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:43:33 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750232613785 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:43:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:43:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:43:34 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750232613787 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:43:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:43:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750232624786 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:43:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750232624786 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:48:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750232913786 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:48:33 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750232913786 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:48:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750232924785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:48:44 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:48:44 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:48:44 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750232924787 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:48:44 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:48:44 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:48:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750232924785 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:48:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:48:45 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750232924787 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:48:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750232925785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:48:45 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:48:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750232925785 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:52:18 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:52:22 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94572
************ - - [18/Jun/2025:15:54:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750233267799 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:54:28 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750233267798 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:54:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:54:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:54:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:54:29 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:54:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:15:54:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:54:36 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:15:54:37 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:15:54:37 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750233267799 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:54:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750233267798 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:54:41 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:54:44 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750233497839 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750233497851 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750233497839 HTTP/1.1" 200 161
************ - - [18/Jun/2025:15:58:18 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750233497851 HTTP/1.1" 200 159491
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+15:58:18&etm=&_timer304=1750233498019 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750233498019 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+16:00&filterCnt=6&_timer304=1750233498019 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750233498019 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750233498019 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750233498202 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+15:58:18&etm=&_timer304=1750233498019 HTTP/1.1" 200 156
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750233498252 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [18/Jun/2025:15:58:18 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+16:00&filterCnt=6&_timer304=1750233498019 HTTP/1.1" 200 164
************ - - [18/Jun/2025:15:58:18 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750233498019 HTTP/1.1" 200 166
************ - - [18/Jun/2025:15:58:18 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750233498019 HTTP/1.1" 200 169
************ - - [18/Jun/2025:15:58:18 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [18/Jun/2025:15:58:18 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:15:58:18 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:15:58:18 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [18/Jun/2025:15:58:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [18/Jun/2025:15:58:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750233498252 HTTP/1.1" 200 155
************ - - [18/Jun/2025:15:58:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [18/Jun/2025:15:58:18 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750233498019 HTTP/1.1" 200 13016
************ - - [18/Jun/2025:15:58:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [18/Jun/2025:15:58:19 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750233498202 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:15:58:19 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:15:58:20 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:20 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [18/Jun/2025:15:58:20 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750233500913 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:21 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750233500913 HTTP/1.1" 200 232
************ - - [18/Jun/2025:15:58:27 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750233507244 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:27 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750233507244 HTTP/1.1" 200 160
************ - - [18/Jun/2025:15:58:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750233507312 HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750233507312 HTTP/1.1" 200 159
************ - - [18/Jun/2025:15:58:30 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:31 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:15:58:32 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:32 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:15:58:34 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:15:58:34 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:16:02:33 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750233753519 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:33 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750233753519 HTTP/1.1" 200 155
************ - - [18/Jun/2025:16:02:33 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:33 +0800] "OPTIONS /api/syq/rsvr/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750233753586 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:33 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:33 +0800] "GET /api/syq/rsvr/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750233753586 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:02:33 +0800] "OPTIONS /api/syq/river/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750233753742 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:33 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 200618
************ - - [18/Jun/2025:16:02:33 +0800] "GET /api/syq/river/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750233753742 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:02:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:02:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:02:34 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:16:02:34 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:35 +0800] "OPTIONS /api/ewci/soil/select-soil-disposi-by-etm HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:35 +0800] "POST /api/ewci/soil/select-soil-disposi-by-etm HTTP/1.1" 200 207
************ - - [18/Jun/2025:16:02:35 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:35 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [18/Jun/2025:16:02:35 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:16:02:35 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1750233755895 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:35 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:36 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1750233755895 HTTP/1.1" 200 427
************ - - [18/Jun/2025:16:02:36 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 953731
************ - - [18/Jun/2025:16:02:37 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:37 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [18/Jun/2025:16:02:38 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:02:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [18/Jun/2025:16:02:40 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:40 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:16:02:40 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750233760892 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:41 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750233760892 HTTP/1.1" 200 232
************ - - [18/Jun/2025:16:02:41 +0800] "OPTIONS /api/syq/video/select-video-count-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:41 +0800] "OPTIONS /api/syq/video/select-video-info-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:41 +0800] "POST /api/syq/video/select-video-info-by-page HTTP/1.1" 200 63831
************ - - [18/Jun/2025:16:02:41 +0800] "POST /api/syq/video/select-video-count-info HTTP/1.1" 200 304
************ - - [18/Jun/2025:16:02:42 +0800] "OPTIONS /api/ewci/basin/select-basin-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:42 +0800] "POST /api/ewci/basin/select-basin-list HTTP/1.1" 200 5658
************ - - [18/Jun/2025:16:02:43 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:43 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:02:43 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:43 +0800] "OPTIONS /api/syq/rsvr/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750233763466 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:43 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750233763466 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:43 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:43 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750233763466 HTTP/1.1" 200 155
************ - - [18/Jun/2025:16:02:43 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:43 +0800] "GET /api/syq/rsvr/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750233763466 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:02:43 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 200618
************ - - [18/Jun/2025:16:02:43 +0800] "OPTIONS /api/syq/river/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750233763609 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:43 +0800] "GET /api/syq/river/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750233763609 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:02:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:02:43 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:44 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:02:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:16:02:44 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:44 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1750233764420 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:44 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:44 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1750233764420 HTTP/1.1" 200 427
************ - - [18/Jun/2025:16:02:44 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 953731
************ - - [18/Jun/2025:16:02:45 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:16:02:45 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:45 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:02:47 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:47 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:02:49 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750233807392 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750233798047 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750234097416 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750234097246 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750234098050 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750234127400 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750234392262 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750234392262 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/ewci/soil/select-soil-disposi-by-etm HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1750234394088 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750234397408 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750234397245 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750234398046 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750233797410 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750233797254 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/ewci/soil/select-soil-disposi-by-etm HTTP/1.1" 200 207
************ - - [18/Jun/2025:16:13:22 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750234392262 HTTP/1.1" 200 155
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 8206
************ - - [18/Jun/2025:16:13:22 +0800] "GET /api/syq/rsvr/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750234392262 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/river/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750234402207 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [18/Jun/2025:16:13:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750234397245 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 200618
************ - - [18/Jun/2025:16:13:22 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750234397408 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 953731
************ - - [18/Jun/2025:16:13:22 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1750234394088 HTTP/1.1" 200 427
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:13:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:13:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:13:23 +0800] "GET /api/syq/river/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1750234402207 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:13:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:16:13:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:16:13:23 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:13:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750234398046 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:13:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:13:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:16:14:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750234447410 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:14:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750234447410 HTTP/1.1" 200 159
************ - - [18/Jun/2025:16:14:21 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:14:21 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:14:22 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:14:22 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:14:24 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:16:14:24 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:16:15:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:15:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:15:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [18/Jun/2025:16:15:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:15:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:15:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:18:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750234697245 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:18:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750234697245 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:18:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750234697409 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:18:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750234697409 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:18:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:18:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:18:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:18:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750234698046 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:18:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:18:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:18:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750234698046 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:18:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:16:19:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750234747452 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:19:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750234747452 HTTP/1.1" 200 159
************ - - [18/Jun/2025:16:20:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:20:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:20:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [18/Jun/2025:16:20:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:20:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:20:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:23:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750234997245 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:23:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750234997245 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:23:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750234997409 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:23:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750234997409 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:23:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:23:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:23:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750234998046 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:23:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:23:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:23:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:23:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750234998046 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:23:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:16:24:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750235047489 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:24:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750235047489 HTTP/1.1" 200 159
************ - - [18/Jun/2025:16:25:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:25:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:25:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [18/Jun/2025:16:25:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:25:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:25:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:27:39 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:27:42 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:16:28:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750235297250 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750235297250 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:28:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750235297409 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750235297409 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:28:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750235298051 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:28:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:28:19 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750235298051 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:28:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:16:28:28 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:28 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1750235308493 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:28 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1750235308493 HTTP/1.1" 200 427
************ - - [18/Jun/2025:16:28:28 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 953731
************ - - [18/Jun/2025:16:28:29 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:29 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:29 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [18/Jun/2025:16:28:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:28:35 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:35 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:28:36 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:36 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:28:41 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:16:28:41 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:16:29:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750235347528 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:29:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750235347528 HTTP/1.1" 200 159
************ - - [18/Jun/2025:16:30:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [18/Jun/2025:16:30:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:30:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:30:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [18/Jun/2025:16:30:48 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235448960 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:48 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235448960 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:48 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235448960 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:48 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235448980 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:49 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235448980 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:30:49 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235448960 HTTP/1.1" 200 831
************ - - [18/Jun/2025:16:30:49 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235448960 HTTP/1.1" 200 520
************ - - [18/Jun/2025:16:30:49 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235448960 HTTP/1.1" 200 519
************ - - [18/Jun/2025:16:30:54 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200007&_timer304=1750235454299 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:54 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902443&_timer304=1750235454299 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:54 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902443&_timer304=1750235454299 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:54 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902443&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235454299 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:54 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902443&_timer304=1750235454299 HTTP/1.1" 200 829
************ - - [18/Jun/2025:16:30:54 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902443&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235454299 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:30:54 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200007&_timer304=1750235454299 HTTP/1.1" 200 520
************ - - [18/Jun/2025:16:30:54 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902443&_timer304=1750235454299 HTTP/1.1" 200 519
************ - - [18/Jun/2025:16:30:55 +0800] "OPTIONS /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:55 +0800] "OPTIONS /api/ewci/bia/keyArea/select-basic-info/220221100200007?_timer304=1750235455586 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:55 +0800] "POST /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 658
************ - - [18/Jun/2025:16:30:55 +0800] "GET /api/ewci/bia/keyArea/select-basic-info/220221100200007?_timer304=1750235455586 HTTP/1.1" 200 393
************ - - [18/Jun/2025:16:30:56 +0800] "OPTIONS /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:56 +0800] "POST /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 658
************ - - [18/Jun/2025:16:30:56 +0800] "OPTIONS /api/ewci/bia/keyArea/select-shperson-list/220221100200007?_timer304=1750235456505 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:56 +0800] "GET /api/ewci/bia/keyArea/select-shperson-list/220221100200007?_timer304=1750235456505 HTTP/1.1" 200 811
************ - - [18/Jun/2025:16:30:57 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=10902443&stm=2025-06-13+16:30&etm=2025-06-18+16:30&drps=&type=1&_timer304=1750235457362 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:30:57 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=10902443&stm=2025-06-13+16:30&etm=2025-06-18+16:30&drps=&type=1&_timer304=1750235457362 HTTP/1.1" 200 147
************ - - [18/Jun/2025:16:31:06 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235466723 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:31:06 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235466723 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:31:06 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235466723 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:31:06 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235466723 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:31:06 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235466723 HTTP/1.1" 200 831
************ - - [18/Jun/2025:16:31:06 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235466723 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:31:06 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235466723 HTTP/1.1" 200 519
************ - - [18/Jun/2025:16:31:06 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235466723 HTTP/1.1" 200 520
************ - - [18/Jun/2025:16:31:59 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235519367 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:31:59 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235519367 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:31:59 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235519367 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:31:59 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235519367 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:31:59 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235519367 HTTP/1.1" 200 831
************ - - [18/Jun/2025:16:31:59 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235519367 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:31:59 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235519367 HTTP/1.1" 200 519
************ - - [18/Jun/2025:16:31:59 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235519367 HTTP/1.1" 200 520
************ - - [18/Jun/2025:16:32:05 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235525715 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:05 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235525715 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:05 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235525715 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:05 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235525715 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:05 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235525715 HTTP/1.1" 200 831
************ - - [18/Jun/2025:16:32:05 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235525715 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:32:05 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235525715 HTTP/1.1" 200 519
************ - - [18/Jun/2025:16:32:05 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235525715 HTTP/1.1" 200 520
************ - - [18/Jun/2025:16:32:18 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200007&_timer304=1750235538062 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:18 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902443&_timer304=1750235538062 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:18 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902443&_timer304=1750235538062 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:18 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902443&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235538062 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:18 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902443&_timer304=1750235538062 HTTP/1.1" 200 829
************ - - [18/Jun/2025:16:32:18 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902443&stm=2025-06-18+08:00&etm=2025-06-18+15:58&_timer304=1750235538062 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:32:18 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902443&_timer304=1750235538062 HTTP/1.1" 200 519
************ - - [18/Jun/2025:16:32:18 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200007&_timer304=1750235538062 HTTP/1.1" 200 520
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750235555572 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750235555559 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750235555559 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:32:35 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750235555572 HTTP/1.1" 200 159491
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+16:32:35&etm=&_timer304=1750235555651 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750235555651 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+17:00&filterCnt=6&_timer304=1750235555651 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750235555651 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750235555651 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750235555742 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+16:32:35&etm=&_timer304=1750235555651 HTTP/1.1" 200 156
************ - - [18/Jun/2025:16:32:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750235555651 HTTP/1.1" 200 166
************ - - [18/Jun/2025:16:32:35 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [18/Jun/2025:16:32:35 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+17:00&filterCnt=6&_timer304=1750235555651 HTTP/1.1" 200 164
************ - - [18/Jun/2025:16:32:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750235555772 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:16:32:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:16:32:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [18/Jun/2025:16:32:35 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [18/Jun/2025:16:32:35 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750235555772 HTTP/1.1" 200 155
************ - - [18/Jun/2025:16:32:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750235555651 HTTP/1.1" 200 169
************ - - [18/Jun/2025:16:32:35 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750235555651 HTTP/1.1" 200 13016
************ - - [18/Jun/2025:16:32:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:35 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [18/Jun/2025:16:32:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [18/Jun/2025:16:32:36 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:16:32:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750235555742 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:32:37 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750235557603 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:37 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750235557603 HTTP/1.1" 200 155
************ - - [18/Jun/2025:16:32:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:16:32:38 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:16:32:40 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750235560499 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:40 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750235560499 HTTP/1.1" 200 232
************ - - [18/Jun/2025:16:32:41 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:41 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:32:44 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:44 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:44 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:16:32:44 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:16:32:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750235565204 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750235565204 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:32:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750235565262 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750235565262 HTTP/1.1" 200 159
************ - - [18/Jun/2025:16:32:53 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235573015 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:53 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235573015 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:53 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235573015 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:53 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750235573015 HTTP/1.1" 200 831
************ - - [18/Jun/2025:16:32:53 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+16:32&_timer304=1750235573031 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:32:53 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750235573015 HTTP/1.1" 200 519
************ - - [18/Jun/2025:16:32:53 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750235573015 HTTP/1.1" 200 520
************ - - [18/Jun/2025:16:32:53 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+16:32&_timer304=1750235573031 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:37:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750235855207 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:37:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750235855207 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:37:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750235855323 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:37:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750235855323 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:37:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:37:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:37:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750235855671 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:37:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:37:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:37:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:37:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750235855671 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:37:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:16:37:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750235865305 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:37:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750235865305 HTTP/1.1" 200 159
************ - - [18/Jun/2025:16:42:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750236155203 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:42:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750236155203 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:42:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750236155327 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:42:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750236155327 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:42:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:42:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:42:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750236155669 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:42:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:42:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:42:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:42:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750236155669 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:42:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:16:42:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750236165351 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:42:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750236165351 HTTP/1.1" 200 159
************ - - [18/Jun/2025:16:47:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750236455205 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:47:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750236455205 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:47:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750236455328 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:47:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750236455328 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:47:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:47:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:47:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750236455671 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:47:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:47:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:47:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:47:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750236455671 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:47:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:16:47:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750236465397 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:47:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750236465397 HTTP/1.1" 200 159
************ - - [18/Jun/2025:16:52:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750236755208 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:52:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750236755208 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:52:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750236755317 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:52:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750236755317 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:52:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:52:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:52:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750236755674 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:52:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:52:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:52:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:52:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750236755674 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:52:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:16:52:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750236765438 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:52:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750236765438 HTTP/1.1" 200 159
************ - - [18/Jun/2025:16:57:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750237055203 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:57:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750237055203 HTTP/1.1" 200 160
************ - - [18/Jun/2025:16:57:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750237055326 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:57:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750237055326 HTTP/1.1" 200 161
************ - - [18/Jun/2025:16:57:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:57:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:57:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:57:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750237055667 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:57:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:16:57:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:16:57:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750237055667 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:16:57:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:16:57:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750237065482 HTTP/1.1" 200 -
************ - - [18/Jun/2025:16:57:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750237065482 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:02:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750237355208 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:02:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750237355208 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:02:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750237355328 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:02:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750237355328 HTTP/1.1" 200 161
************ - - [18/Jun/2025:17:02:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:02:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:02:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:02:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750237355674 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:02:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:17:02:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:17:02:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750237355674 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:17:02:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:17:02:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750237365526 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:02:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750237365526 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:07:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750237664796 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:07:44 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750237664796 HTTP/1.1" 200 161
************ - - [18/Jun/2025:17:07:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750237665799 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:07:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750237665799 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:08:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750237724796 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:08:44 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:08:44 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750237724798 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:08:44 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:08:44 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:08:44 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:17:08:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750237724796 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:08:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:17:08:45 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:17:08:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750237724798 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:17:12:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750237955208 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:12:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750237955208 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:12:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750237955321 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:12:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750237955321 HTTP/1.1" 200 161
************ - - [18/Jun/2025:17:12:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:12:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:12:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:12:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750237955684 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:12:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:17:12:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:17:12:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750237955684 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:17:12:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:17:12:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750237965845 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:12:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750237965845 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:17:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750238264788 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:17:44 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750238264788 HTTP/1.1" 200 161
************ - - [18/Jun/2025:17:17:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750238266790 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:17:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750238266790 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:17:58 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750238278801 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:17:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:17:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750238278803 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:17:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:17:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:17:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:17:17:58 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750238278801 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:17:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:17:17:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750238278803 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:17:18:00 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+17:20:27&etm=&_timer304=1750238427061 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750238426971 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750238427061 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750238426965 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750238427061 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+18:00&filterCnt=6&_timer304=1750238427061 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750238427061 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238427224 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750238427190 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:31 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750238427061 HTTP/1.1" 200 166
************ - - [18/Jun/2025:17:20:31 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [18/Jun/2025:17:20:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750238426965 HTTP/1.1" 200 161
************ - - [18/Jun/2025:17:20:31 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+17:20:27&etm=&_timer304=1750238427061 HTTP/1.1" 200 156
************ - - [18/Jun/2025:17:20:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [18/Jun/2025:17:20:31 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750238426971 HTTP/1.1" 200 159491
************ - - [18/Jun/2025:17:20:31 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+18:00&filterCnt=6&_timer304=1750238427061 HTTP/1.1" 200 164
************ - - [18/Jun/2025:17:20:31 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:17:20:31 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:17:20:31 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750238427061 HTTP/1.1" 200 169
************ - - [18/Jun/2025:17:20:31 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [18/Jun/2025:17:20:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [18/Jun/2025:17:20:31 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238427224 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:20:31 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:31 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:31 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [18/Jun/2025:17:20:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [18/Jun/2025:17:20:31 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750238427061 HTTP/1.1" 200 13016
************ - - [18/Jun/2025:17:20:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [18/Jun/2025:17:20:33 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:20:33 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238433213 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:33 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238433213 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:20:33 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750238427190 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:17:20:33 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:20:33 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:34 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:20:34 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238434780 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:34 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238434780 HTTP/1.1" 200 232
************ - - [18/Jun/2025:17:20:36 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750238436552 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750238436590 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750238436552 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:20:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750238436590 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:20:39 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:39 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [18/Jun/2025:17:20:42 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:42 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:43 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:17:20:43 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [18/Jun/2025:17:20:49 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750238449782 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:49 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750238449782 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:49 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750238449782 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:49 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+17:20&_timer304=1750238449800 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:20:49 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-18+08:00&etm=2025-06-18+17:20&_timer304=1750238449800 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:20:49 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750238449782 HTTP/1.1" 200 831
************ - - [18/Jun/2025:17:20:49 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750238449782 HTTP/1.1" 200 519
************ - - [18/Jun/2025:17:20:49 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750238449782 HTTP/1.1" 200 520
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750238521587 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750238521593 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750238521587 HTTP/1.1" 200 161
************ - - [18/Jun/2025:17:22:01 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750238521593 HTTP/1.1" 200 159491
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+17:22:01&etm=&_timer304=1750238521691 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750238521691 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750238521691 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+18:00&filterCnt=6&_timer304=1750238521691 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750238521691 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750238521831 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-15+17:22:01&etm=&_timer304=1750238521691 HTTP/1.1" 200 156
************ - - [18/Jun/2025:17:22:01 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [18/Jun/2025:17:22:01 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [18/Jun/2025:17:22:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:17:22:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750238521691 HTTP/1.1" 200 166
************ - - [18/Jun/2025:17:22:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [18/Jun/2025:17:22:01 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-18+08:00&etm=2025-06-18+18:00&filterCnt=6&_timer304=1750238521691 HTTP/1.1" 200 164
************ - - [18/Jun/2025:17:22:01 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238521886 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:01 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [18/Jun/2025:17:22:01 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [18/Jun/2025:17:22:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750238521691 HTTP/1.1" 200 169
************ - - [18/Jun/2025:17:22:01 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238521886 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:22:01 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750238521691 HTTP/1.1" 200 13016
************ - - [18/Jun/2025:17:22:02 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [18/Jun/2025:17:22:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [18/Jun/2025:17:22:02 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [18/Jun/2025:17:22:03 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:22:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750238521831 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:17:22:04 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238524014 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:04 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238524014 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:22:04 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:05 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:22:05 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:06 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:22:07 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238527353 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:07 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238527353 HTTP/1.1" 200 232
************ - - [18/Jun/2025:17:22:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750238531171 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750238531171 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:22:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750238531222 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:22:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750238531222 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:24:01 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:24:02 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:24:02 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238642590 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:24:02 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238642590 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:24:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:24:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:24:03 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:24:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:24:04 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238644183 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:24:04 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238644183 HTTP/1.1" 200 232
************ - - [18/Jun/2025:17:26:02 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:26:03 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:26:03 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238763675 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:26:03 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238763675 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:26:03 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:26:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:26:04 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:26:05 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:26:05 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238765253 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:26:05 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238765253 HTTP/1.1" 200 232
************ - - [18/Jun/2025:17:27:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750238821786 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:27:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750238821785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:27:01 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750238821788 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:27:01 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:27:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:27:01 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:27:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750238821785 HTTP/1.1" 200 161
************ - - [18/Jun/2025:17:27:01 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:17:27:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750238821786 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:27:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:17:27:03 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:17:27:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750238821788 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:17:27:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750238831785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:27:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750238831785 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:28:02 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:28:03 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:28:03 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238883660 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:28:03 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750238883660 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:28:03 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:28:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:28:04 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:28:05 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:28:05 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238885200 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:28:05 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750238885200 HTTP/1.1" 200 232
************ - - [18/Jun/2025:17:30:03 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:30:03 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239003681 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:30:03 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239003681 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:30:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:30:05 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:30:05 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239005224 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:30:05 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239005224 HTTP/1.1" 200 232
************ - - [18/Jun/2025:17:32:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750239121785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:32:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750239121785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:32:01 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750239121786 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:32:01 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:17:32:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750239121785 HTTP/1.1" 200 161
************ - - [18/Jun/2025:17:32:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750239121785 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:32:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:17:32:02 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750239121786 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:17:32:02 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:17:32:03 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:32:03 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239123748 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:32:03 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239123748 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:32:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:32:05 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:32:05 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239125330 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:32:05 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239125330 HTTP/1.1" 200 232
************ - - [18/Jun/2025:17:32:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750239132784 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:32:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750239132784 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:34:45 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:34:45 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239285722 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:34:45 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239285722 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:34:46 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:34:47 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:34:47 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239287287 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:34:47 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239287287 HTTP/1.1" 200 232
************ - - [18/Jun/2025:17:37:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750239421785 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:37:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750239421786 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:37:01 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750239421786 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:37:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750239421785 HTTP/1.1" 200 161
************ - - [18/Jun/2025:17:37:01 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [18/Jun/2025:17:37:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750239421786 HTTP/1.1" 200 160
************ - - [18/Jun/2025:17:37:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [18/Jun/2025:17:37:02 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54644
************ - - [18/Jun/2025:17:37:02 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-18+08:00&etm=2025-06-18+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750239421786 HTTP/1.1" 200 441073
************ - - [18/Jun/2025:17:37:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750239433784 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:37:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750239433784 HTTP/1.1" 200 159
************ - - [18/Jun/2025:17:37:45 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:37:45 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239465688 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:37:45 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239465688 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:37:46 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:37:47 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:37:47 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239467265 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:37:47 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239467265 HTTP/1.1" 200 232
************ - - [18/Jun/2025:17:39:45 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [18/Jun/2025:17:39:45 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239585695 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:39:45 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750239585695 HTTP/1.1" 200 155
************ - - [18/Jun/2025:17:39:46 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:39:47 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 2525
************ - - [18/Jun/2025:17:39:47 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239587312 HTTP/1.1" 200 -
************ - - [18/Jun/2025:17:39:47 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750239587312 HTTP/1.1" 200 232
