<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.screen.ly.mapper.SvgDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="queryRsvrInfo" resultType="map">
        select t1.RES_CODE,
        convert(decimal(18,4),t1.TOT_CAP/10000)   TOT_CAP,
                     t1.FL_LOW_LIM_LEV,
                     t1.FL_LOW_LIM_LEV_CAP,
                     t2.DFLMD,
                     t3.CHFLLV,
                     CASE WHEN t1.ENG_SCAL in ('1','2','3') then (
                     select top 1
                                    case when t.rwptn = '4' then convert(varchar(30),convert(decimal(18,2),t.rz)) + '↓'
                                    when t.rwptn = '5' then convert(varchar(30),convert(decimal(18,2),t.rz)) + '↑'
                                    when t.rwptn = '6' then convert(varchar(30),convert(decimal(18,2),t.rz)) + '-'
                                    else convert(varchar(30),convert(decimal(18,2),t.rz)) end RZ_TXT
                    from st_rsvr_r t where t.stcd in (
                            select top 1 t2.stcd
                            from (select * from BSN_OBJONLY_B t where t.objid in (select t.objid from BSN_OBJONLY_B t where t.objcd = t1.RES_CODE )  and objtp='1') t1
                            left join BSN_STADTP_B t2 on t1.objcd = t2.stcd
                            order by t2.stadtp asc
                        )
                     )
                     WHEN t1.ENG_SCAL in ('4','5') then (
                         select top 1
                                        case when t.rwptn = '4' then convert(varchar(30),convert(decimal(18,2),t.rz)) + '↓'
                                        when t.rwptn = '5' then convert(varchar(30),convert(decimal(18,2),t.rz)) + '↑'
                                        when t.rwptn = '6' then convert(varchar(30),convert(decimal(18,2),t.rz)) + '-'
                                        else convert(varchar(30),convert(decimal(18,2),t.rz)) end RZ_TXT
                            from st_rsvr_r t where t.stcd in (
                                select top 1 t2.stcd
                                from (select * from BSN_OBJONLY_B t where t.objid in (select t.objid from BSN_OBJONLY_B t where t.objcd = t1.RES_CODE )  and objtp='1') t1
                                left join BSN_STADTP_B t2 on t1.objcd = t2.stcd
                                order by t2.stadtp desc
                            )
                     )
                     end RZ
        from ATT_RES_BASE t1
        left join FHGC_RSHYPR t2 on t1.res_code = t2.res_code
        left join FHGC_RSPP t3 on t1.res_code = t3.res_code
        where t1.res_code in
                <foreach collection="resCodeList"  item="item" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
    </select>

    <select id="queryRiverInfo" resultType="map">
        select  t2.stcd RES_CODE,
                t3.WRZ,t3.GRZ,t3.GRQ,
                case when t3.LDKEL > t3.RDKEL then t3.RDKEL
                else t3.LDKEL end DKEL,
                case when t2.WPTN = '4' then convert(varchar(30),convert(decimal(18,2),t2.z)) + '↓'
                                when t2.WPTN = '5' then convert(varchar(30),convert(decimal(18,2),t2.z)) + '↑'
                                when t2.WPTN = '6' then convert(varchar(30),convert(decimal(18,2),t2.z)) + '-'
                                else convert(varchar(30),convert(decimal(18,2),t2.z)) end RZ
        from (
        select t.stcd,max(t.tm) tm
        from st_river_r t where t.stcd in
        <foreach collection="resCodeList"  item="item" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        and t.tm &gt;= getdate()-12 and t.tm &lt;= getdate()
        group by t.stcd
        ) t1
        inner join st_river_r t2 on t1.stcd=t2.stcd and t1.tm=t2.tm
        left join ST_RVFCCH_B t3 on t1.stcd=t3.stcd
    </select>

    <select id="queryDikeInfo" resultType="map">
        select t1.dike_code RES_CODE,
               t4.ADNM,
               t2.PRAR,
               t2.PTPP,
               t2.PRINAR
        from ATT_DIKE_BASE t1
                 left join FHGC_DBIABT t2 on t1.dike_code = t2.dike_code
                 left join REL_DIKE_AD t3 on t1.dike_code = t3.dike_code
                 left join MDT_ADCDINFO_B t4 on t3.ad_code = t4.adcd
        where t1.dike_code in
        <foreach collection="resCodeList"  item="item" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>

    <select id="queryFloodControlList" resultType="map">
        select t.term,t.content from BNS_RV_FLOODCONTROL t where t.RES_CODE = #{rvCode} and t.type=#{type} order by t.order_by asc
    </select>
</mapper>
