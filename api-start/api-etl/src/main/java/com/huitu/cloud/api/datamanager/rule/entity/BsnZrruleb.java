package com.huitu.cloud.api.datamanager.rule.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 水位规则配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-07
 */
@TableName("BSN_ZRRULE_B")
@ApiModel(value="BsnZrruleB对象", description="水位规则配置表")
public class BsnZrruleb extends Model<BsnZrruleb> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "站码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "水位上限")
    @TableField("UPV")
    private Double upv;

    @ApiModelProperty(value = "水位下限")
    @TableField("DWV")
    private Double dwv;

    @ApiModelProperty(value = "变幅")
    @TableField("CHV")
    private Double chv;

    @ApiModelProperty(value = "备注")
    @TableField("NT")
    private String nt;

    @ApiModelProperty(value = "时间戳 自动获取无需传值")
    @TableField("MODITIME")
    private Date moditime;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Double getUpv() {
        return upv;
    }

    public void setUpv(Double upv) {
        this.upv = upv;
    }

    public Double getDwv() {
        return dwv;
    }

    public void setDwv(Double dwv) {
        this.dwv = dwv;
    }

    public Double getChv() {
        return chv;
    }

    public void setChv(Double chv) {
        this.chv = chv;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }

    public Date getModitime() {
        return moditime;
    }

    public void setModitime(Date moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "BsnZrruleB{" +
        "stcd=" + stcd +
        ", upv=" + upv +
        ", dwv=" + dwv +
        ", chv=" + chv +
        ", nt=" + nt +
        ", moditime=" + moditime +
        "}";
    }
}
