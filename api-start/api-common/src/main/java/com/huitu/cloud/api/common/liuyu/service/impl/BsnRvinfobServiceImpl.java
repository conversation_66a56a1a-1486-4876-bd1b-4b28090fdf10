package com.huitu.cloud.api.common.liuyu.service.impl;

import com.huitu.cloud.api.common.liuyu.entity.BsnBasB;
import com.huitu.cloud.api.common.liuyu.entity.BsnRvinfoB;
import com.huitu.cloud.api.common.liuyu.mapper.BsnRvinfobDao;
import com.huitu.cloud.api.common.liuyu.service.BsnRvinfobService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.util.AdcdUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 河流流域常量表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
@Service
public class BsnRvinfobServiceImpl extends ServiceImpl<BsnRvinfobDao, BsnBasB> implements BsnRvinfobService {

    @Override
    public BsnBasB getRiverTree() {
        List<BsnBasB> list = baseMapper.selectRiver();
        // 主要江河过滤 - 父级为空 或 主要江河标志
        List<BsnBasB> mainList = list.stream().filter(item -> StringUtils.isEmpty(item.getPbasCode()) || (item.getIsZyjh() != null && item.getIsZyjh().equals("1"))).collect(Collectors.toList());
        mainList.forEach(item -> {
            List<BsnBasB> childList = getChildren(mainList, item.getBasCode());
            item.setChildren(childList);
        });
        List<BsnBasB> finalList = list.stream().filter(item -> "".equals(item.getPbasCode())).collect(Collectors.toList());
        BsnBasB bsnBasB = new BsnBasB();
        bsnBasB.setBasName("全部");
        bsnBasB.setBasCode("0");
        bsnBasB.setChildren(finalList);
        return bsnBasB;
    }

    private List<BsnBasB> getChildren(List<BsnBasB> list, String pcode) {
        // 通过父级编码子类
        List<BsnBasB> childList = list.stream().filter(x -> x.getPbasCode().equals(pcode)).collect(Collectors.toList());
        return childList;
    }

    @Override
    public BsnBasB getRiverAndStTree(String adcd, String sttp) {
        List<BsnBasB> list = baseMapper.selectRiver();
        List<BsnBasB> level12list = list.stream().filter(item -> item.getBasLevel() < 3).collect(Collectors.toList());
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("level", level);
        param.put("ad", adcd.substring(0, level));
        param.put("sttp", sttp);
//        List<BsnBasB> stList=baseMapper.selectSt(param);
//        param.put("bas_code", "");
//        List<BsnBasB> stList=baseMapper.getStbyBscd(param);
        /*list.forEach(x->{
            List<BsnBasB> chilerenList=getChildStcd(stList,x.getRvnm());
            x.setChildren(chilerenList);
        });*/
        List<BsnBasB> stList;
        for (BsnBasB item : level12list) {
            param.put("basCode", item.getBasCode());
            stList = baseMapper.getStbyBscd(param);
//            List<BsnBasB> childList=getChildren(stList,item.getBasCode());
            item.setChildren(stList);
        }
        level12list.forEach(item -> {
            if (item.getBasLevel() == 1) {
                List<BsnBasB> childList = getChildren(level12list, item.getBasCode());
                if (childList.size() != 0) {
                    item.setChildren(childList);
                }
            } else {
                item.setDisabled(item.getChildren().size() > 0 ? false : true);
            }
        });
        List<BsnBasB> finalList = list.stream().filter(item -> "".equals(item.getPbasCode())).collect(Collectors.toList());
        BsnBasB bsnBasB = new BsnBasB();
        bsnBasB.setBasName("全部");
        bsnBasB.setBasCode("0");
        bsnBasB.setChildren(finalList);
        return bsnBasB;
    }

    private List<BsnBasB> getChildrenStcd(List<BsnBasB> list, String code) {
        // 通过父级编码子类
        List<BsnBasB> childList = list.stream().filter(x -> x.getBasCode().equals(code)).collect(Collectors.toList());
        return childList;
    }

}
