package com.huitu.cloud.api.ew.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ew.entity.request.EwDisasterQueryRequest;
import com.huitu.cloud.api.ew.entity.request.EwDisasterSaveRequest;
import com.huitu.cloud.api.ew.entity.response.EwDisasterRecordResponse;
import com.huitu.cloud.api.ew.exception.WarningException;
import com.huitu.cloud.api.ew.service.EwDisasterService;
import com.huitu.cloud.api.usif.util.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;

/**
 * 灾情反馈服务
 *
 * <AUTHOR>
 */
@Api(tags = "灾情反馈服务")
@RestController
@RequestMapping("/api/ew/disaster")
public class EwDisasterResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "382038aa-80f2-bdc0-20c9-d52488fc042a";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final LoginUtils loginUtils;
    private final EwDisasterService baseService;

    public EwDisasterResource(LoginUtils loginUtils, EwDisasterService baseService) {
        this.loginUtils = loginUtils;
        this.baseService = baseService;
    }

    @ApiOperation(value = "获取灾情记录分页列表", notes = "作者：曹宝金")
    @PostMapping("page-list")
    public ResponseEntity<SuccessResponse<IPage<EwDisasterRecordResponse>>> getPageList(@RequestBody @Validated EwDisasterQueryRequest request) {
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(request)));
        } catch (Exception e) {
            throw new WarningException("获取灾情记录分页列表失败", e);
        }
    }

    @ApiOperation(value = "导出险情（灾情）信息", notes = "作者：曹宝金")
    @PostMapping(value = "export")
    public void dataExport(@Validated @RequestBody EwDisasterQueryRequest query, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("险情（灾情）信息", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.dataExport(query, response.getOutputStream());
        } catch (Exception e) {
            throw new WarningException("险情（灾情）信息导出失败", e);
        }
    }

    @ApiOperation(value = "获取灾情记录", notes = "作者：曹宝金")
    @GetMapping("record")
    public ResponseEntity<SuccessResponse<EwDisasterRecordResponse>> getRecord(@RequestParam String disasterId) {
        Assert.hasText(disasterId, "参数[灾情ID]不能为空");
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRecord(disasterId)));
        } catch (Exception e) {
            throw new WarningException("获取灾情记录失败", e);
        }
    }

    @ApiOperation(value = "保存灾情记录", notes = "作者：曹宝金")
    @PostMapping("save")
    public ResponseEntity<SuccessResponse<Integer>> saveDisaster(@RequestBody @Validated EwDisasterSaveRequest request) {
        try {
            Long userId = loginUtils.getCurrentLoginUserID();
            if (StringUtils.isBlank(request.getDisasterId())) {
                // 新增，记录创建人
                request.setCreateBy(userId);
            } else {
                // 修改，记录最后更新人
                request.setLatestBy(userId);
            }
            return ResponseEntity.ok(
                    new SuccessResponse<>(this, "OK", baseService.saveDisaster(request)));
        } catch (WarningException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("保存灾情记录失败", ex);
        }
    }

    @ApiOperation(value = "删除灾情记录", notes = "作者：曹宝金")
    @GetMapping("delete")
    public ResponseEntity<SuccessResponse<Integer>> deleteDisaster(@RequestParam String disasterId) {
        Assert.hasText(disasterId, "参数[灾情ID]不能为空");
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteDisaster(disasterId)));
        } catch (Exception ex) {
            throw new WarningException("删除灾情记录失败", ex);
        }
    }
}
