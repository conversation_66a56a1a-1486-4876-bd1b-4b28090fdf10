<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rcs.mapper.BnsRcsDikeDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.rcs.entity.BnsRcsDike" useCache="false">
        SELECT D.DIKE_CODE, D.ENG_CODE, D.DIKE_NAME, D.ADCD, A.ADNM, D.RV_OR_LE_CODE, D.RV_OR_LE_NAME, D.START_LGTD,
        D.START_LTTD, D.END_LGTD, D.END_LTTD, D.DIKE_GRAD, D.DIKE_PATT, <PERSON><PERSON>DIKE_LEN, D.BANK, D.<PERSON>, D<PERSON>CT<PERSON>,
        D.COMPLIANCED, D.REMARK, D.TS
        FROM BNS_RCS_DIKE D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE LEFT(D.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.dikeName!= null and map.dikeName !=''">
            AND CHARINDEX(#{map.dikeName}, D.DIKE_NAME) > 0
        </if>
        <if test="map.dikeGrad !=null and map.dikeGrad !=''">
            AND D.DIKE_GRAD = #{map.dikeGrad}
        </if>
        <if test="map.bank !=null and map.bank !=''">
            AND D.BANK = #{map.bank}
        </if>
        <if test="map.complianced !=null and map.complianced !=''">
            AND D.COMPLIANCED = #{map.complianced}
        </if>
        ORDER BY D.ADCD, D.DIKE_CODE ASC
    </select>
    <select id="export" resultType="com.huitu.cloud.api.ewci.rcs.entity.BnsRcsDike" useCache="false">
        SELECT D.DIKE_CODE, D.ENG_CODE, D.DIKE_NAME, D.ADCD, A.ADNM, D.RV_OR_LE_CODE, D.RV_OR_LE_NAME, D.START_LGTD,
        D.START_LTTD, D.END_LGTD, D.END_LTTD, CASE
        D.DIKE_GRAD
        WHEN '0' THEN
        '未知'
        WHEN '1' THEN
        '1级'
        WHEN '2' THEN
        '2级'
        WHEN '3' THEN
        '3级'
        WHEN '4' THEN
        '4级'
        WHEN '5' THEN
        '5级'
        WHEN '9' THEN
        '5级以下'
        END AS DIKE_GRAD,
        CASE
        D.DIKE_PATT
        WHEN '1' THEN
        '土堤'
        WHEN '2' THEN
        '砌石堤'
        WHEN '3' THEN
        '土石混合堤'
        WHEN '4' THEN
        '钢筋混凝土防洪墙'
        WHEN '5' THEN
        '其他'
        END AS DIKE_PATT, D.DIKE_LEN,
        CASE
        D.BANK
        WHEN '0' THEN
        '不分'
        WHEN '1' THEN
        '左岸'
        WHEN '2' THEN
        '右岸'
        WHEN '3' THEN
        '海堤/围堤'
        END AS BANK, D.PLFLCTSD, D.ACFLCTSD,
        CASE
        D.COMPLIANCED
        WHEN '0' THEN
        '待评价'
        WHEN '1' THEN
        '达标'
        WHEN '2' THEN
        '未达标'
        END AS COMPLIANCED,
        D.REMARK, D.TS
        FROM BNS_RCS_DIKE D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE LEFT(D.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.dikeName!= null and map.dikeName !=''">
            AND CHARINDEX(#{map.dikeName}, D.DIKE_NAME) > 0
        </if>
        <if test="map.dikeGrad !=null and map.dikeGrad !=''">
            AND D.DIKE_GRAD = #{map.dikeGrad}
        </if>
        <if test="map.bank !=null and map.bank !=''">
            AND D.BANK = #{map.bank}
        </if>
        <if test="map.complianced !=null and map.complianced !=''">
            AND D.COMPLIANCED = #{map.complianced}
        </if>
        ORDER BY D.ADCD, D.DIKE_CODE ASC
    </select>
    <select id="getStatListByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT D.ADCD, A.ADNM, '06' ENTYPE, COUNT(D.DIKE_CODE) TOTAL_COUNT
        FROM (
        SELECT DIKE_CODE, LEFT(LEFT(ADCD, #{map.lowerLevel}) + '***************', 15) ADCD FROM BNS_RCS_DIKE
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT DIKE_CODE, '***************' ADCD FROM BNS_RCS_DIKE WHERE LEFT (ADCD, 6) = '220581'
        </if>
        ) D
        LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        GROUP BY D.ADCD, A.ADNM
        ORDER BY D.ADCD ASC
    </select>
    <select id="getStatByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT A.ADCD, A.ADNM, '06' ENTYPE, D.TOTAL_COUNT FROM BSN_ADCD_B A,
        (SELECT COUNT(DIKE_CODE) TOTAL_COUNT FROM BNS_RCS_DIKE
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) D
        WHERE A.ADCD = #{map.adcd}
    </select>
    <select id="getCountListWithBank" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(BANK, '0') PROP_CODE, COUNT(DIKE_CODE) PROP_COUNT
        FROM BNS_RCS_DIKE
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY BANK
        ORDER BY BANK ASC
    </select>
    <select id="getCountListWithGrad" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(DIKE_GRAD, '0') PROP_CODE, COUNT(DIKE_CODE) PROP_COUNT
        FROM BNS_RCS_DIKE
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY DIKE_GRAD
        ORDER BY DIKE_GRAD ASC
    </select>
    <select id="getCountListWithComplianced" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(COMPLIANCED, '0') PROP_CODE, COUNT(DIKE_CODE) PROP_COUNT
        FROM BNS_RCS_DIKE
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY COMPLIANCED
        ORDER BY COMPLIANCED ASC
    </select>
</mapper>
