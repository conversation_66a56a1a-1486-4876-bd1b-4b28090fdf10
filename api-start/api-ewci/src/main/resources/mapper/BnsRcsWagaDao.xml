<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rcs.mapper.BnsRcsWagaDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.rcs.entity.BnsRcsWagaVo" useCache="false">
        SELECT D.WAGA_CODE, D.ENG_CODE, D.WAGA_NAME, D.ADCD, D.WAGA_LOC, D.WAGA_TYPE, D.ENG_GRAD, D.LGTD, D.LTTD, D.COMPLETED, D.COMPLETED_DATE, D.CONCLUSION, D.REINFORCED, D.TS, A.ADNM
        FROM BNS_RCS_WAGA D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE LEFT(D.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wagaName!= null and map.wagaName !=''">
            AND CHARINDEX(#{map.wagaName}, D.WAGA_NAME) > 0
        </if>
        <if test="map.wagaType !=null and map.wagaType !=''">
            AND D.WAGA_TYPE = #{map.wagaType}
        </if>
        <if test="map.engGrad !=null and map.engGrad !=''">
            AND D.ENG_GRAD = #{map.engGrad}
        </if>
        <if test="map.completed !=null and map.completed !=''">
            AND D.COMPLETED = #{map.completed}
        </if>
        <if test="map.conclusion !=null and map.conclusion !='' and map.conclusion != 0">
            AND D.CONCLUSION = #{map.conclusion}
        </if>
        <if test="map.conclusion !=null and map.conclusion !='' and map.conclusion == 0">
            AND D.CONCLUSION is null
        </if>
        <if test="map.reinforced !=null and map.reinforced !='' and map.reinforced != 2">
            AND D.REINFORCED = #{map.reinforced}
        </if>
        <if test="map.reinforced !=null and map.reinforced !='' and map.reinforced == 2">
            AND D.REINFORCED is null
        </if>
        ORDER BY D.ADCD, D.WAGA_CODE ASC
    </select>
    <select id="getList" resultType="com.huitu.cloud.api.ewci.rcs.entity.BnsRcsWagaVo" useCache="false">
        SELECT D.WAGA_CODE, D.ENG_CODE, D.WAGA_NAME, D.ADCD, D.WAGA_LOC,
        CASE
        D.WAGA_TYPE
        WHEN '0' THEN
        '未知'
        WHEN '1' THEN
        '分(泄)洪闸'
        WHEN '2' THEN
        '节制闸'
        WHEN '3' THEN
        '排(退)水闸'
        WHEN '4' THEN
        '引(进)水闸'
        WHEN '5' THEN
        '挡潮闸'
        WHEN '6' THEN
        '船闸'
        WHEN '7' THEN
        '橡胶坝'
        WHEN '9' THEN
        '其他'
        END AS WAGA_TYPE,
        CASE
        D.ENG_GRAD
        WHEN '0' THEN
        '未知'
        WHEN '1' THEN
        'Ⅰ'
        WHEN '2' THEN
        'Ⅱ'
        WHEN '3' THEN
        'Ⅲ'
        WHEN '4' THEN
        'Ⅳ'
        WHEN '5' THEN
        'Ⅴ'
        END AS ENG_GRAD,
        D.LGTD,
        D.LTTD,
        CASE
        D.COMPLETED
        WHEN '0' THEN
        '未开展'
        WHEN '1' THEN
        '开展过'
        END AS COMPLETED,
        D.COMPLETED_DATE,
        CASE
        D.CONCLUSION
        WHEN '0' THEN
        '未知'
        WHEN '1' THEN
        '一类闸'
        WHEN '2' THEN
        '二类闸'
        WHEN '3' THEN
        '三类闸'
        WHEN '4' THEN
        '四类闸'
        END AS CONCLUSION,
        CASE
        D.REINFORCED
        WHEN '0' THEN
        '否'
        WHEN '1' THEN
        '是'
        WHEN '2' THEN
        '二类闸'
        WHEN '3' THEN
        '三类闸'
        WHEN '4' THEN
        '四类闸'
        END AS REINFORCED,
        D.TS, D.TS, A.ADNM
        FROM BNS_RCS_WAGA D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE LEFT(D.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wagaName!= null and map.wagaName !=''">
            AND CHARINDEX(#{map.wagaName}, D.WAGA_NAME) > 0
        </if>
        <if test="map.wagaType !=null and map.wagaType !=''">
            AND D.WAGA_TYPE = #{map.wagaType}
        </if>
        <if test="map.engGrad !=null and map.engGrad !=''">
            AND D.ENG_GRAD = #{map.engGrad}
        </if>
        <if test="map.completed !=null and map.completed !=''">
            AND D.COMPLETED = #{map.completed}
        </if>
        <if test="map.conclusion !=null and map.conclusion !=''">
            AND D.CONCLUSION = #{map.conclusion}
        </if>
        <if test="map.reinforced !=null and map.reinforced !=''">
            AND D.REINFORCED = #{map.reinforced}
        </if>
        ORDER BY D.ADCD, D.WAGA_CODE ASC
    </select>
    <select id="getStatListByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT D.ADCD,
        A.ADNM,
        '11' ENTYPE,
        COUNT(D.WAGA_CODE) TOTAL_COUNT
        FROM (
        SELECT WAGA_CODE, LEFT(LEFT(ADCD, #{map.lowerLevel}) + '000000000000000', 15) ADCD FROM BNS_RCS_WAGA
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT WAGA_CODE, '220581000000000' ADCD FROM BNS_RCS_WAGA WHERE LEFT (ADCD, 6) = '220581'
        </if>
        ) D
        LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        GROUP BY D.ADCD, A.ADNM
        ORDER BY D.ADCD ASC
    </select>
    <select id="getStatByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT A.ADCD, A.ADNM, '11' ENTYPE, D.TOTAL_COUNT
        FROM BSN_ADCD_B A,
        (SELECT COUNT(WAGA_CODE) TOTAL_COUNT FROM BNS_RCS_WAGA
        WHERE LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) D
        WHERE A.ADCD = #{map.adcd}
    </select>
    <select id="getCountListWithWagaField" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(${field}, '0') PROP_CODE, COUNT(WAGA_CODE) PROP_COUNT
        FROM BNS_RCS_WAGA
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY ${field}
        ORDER BY ${field} ASC
    </select>
    <select id="getCountListWithWagaType" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(WAGA_TYPE, '0') PROP_CODE, COUNT(WAGA_CODE) PROP_COUNT
        FROM BNS_RCS_WAGA
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY WAGA_TYPE
        ORDER BY WAGA_TYPE ASC
    </select>
    <select id="getCountListWithEngGrad" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(ENG_GRAD, '0') PROP_CODE, COUNT(WAGA_CODE) PROP_COUNT
        FROM BNS_RCS_WAGA
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY ENG_GRAD
        ORDER BY ENG_GRAD ASC
    </select>
    <select id="getCountListWithCompleted" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(COMPLETED, '0') PROP_CODE, COUNT(WAGA_CODE) PROP_COUNT
        FROM BNS_RCS_WAGA
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY COMPLETED
        ORDER BY COMPLETED ASC
    </select>
    <select id="getCountListWithConclusion" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(CONCLUSION, '0') PROP_CODE, COUNT(WAGA_CODE) PROP_COUNT
        FROM BNS_RCS_WAGA
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY CONCLUSION
        ORDER BY CONCLUSION ASC
    </select>
    <select id="getCountListWithReinforced" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(REINFORCED, '2') PROP_CODE, COUNT(WAGA_CODE) PROP_COUNT
        FROM BNS_RCS_WAGA
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY REINFORCED
        ORDER BY REINFORCED ASC
    </select>
</mapper>
