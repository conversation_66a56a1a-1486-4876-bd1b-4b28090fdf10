package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 预警指标
 * </p>
 *
 * <AUTHOR>
 * @since 2022年5月26日
 */
@ApiModel(value="IaDfwruleVo对象", description="预警指标")
public class IaDfwruleVo extends IaDfwrule{
    @ApiModelProperty(value = "准备转移雨量阈值")
    private Integer drpt30;
    @ApiModelProperty(value = "立即转移雨量阈值")
    private Integer drpt31;

    public Integer getDrpt30() {
        return drpt30;
    }

    public void setDrpt30(Integer drpt30) {
        this.drpt30 = drpt30;
    }

    public Integer getDrpt31() {
        return drpt31;
    }

    public void setDrpt31(Integer drpt31) {
        this.drpt31 = drpt31;
    }
}
