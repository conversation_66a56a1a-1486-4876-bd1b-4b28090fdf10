package com.huitu.cloud.api.screen.fhjz.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value="雨量测站数量统计", description="雨量测站数量统计")
public class RainCountVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "0-10mm 测试数量")
    private String leve1;

    @ApiModelProperty(value = "10-25mm 测试数量")
    private String leve2;

    @ApiModelProperty(value = "25-50mm 测试数量")
    private String leve3;

    @ApiModelProperty(value = "50-100mm 测试数量")
    private String leve4;

    @ApiModelProperty(value = "100-200mm 测试数量")
    private String leve5;

    @ApiModelProperty(value = ">250mm 测试数量")
    private String leve6;

    public String getLeve1() {
        return leve1;
    }

    public void setLeve1(String leve1) {
        this.leve1 = leve1;
    }

    public String getLeve2() {
        return leve2;
    }

    public void setLeve2(String leve2) {
        this.leve2 = leve2;
    }

    public String getLeve3() {
        return leve3;
    }

    public void setLeve3(String leve3) {
        this.leve3 = leve3;
    }

    public String getLeve4() {
        return leve4;
    }

    public void setLeve4(String leve4) {
        this.leve4 = leve4;
    }

    public String getLeve5() {
        return leve5;
    }

    public void setLeve5(String leve5) {
        this.leve5 = leve5;
    }

    public String getLeve6() {
        return leve6;
    }

    public void setLeve6(String leve6) {
        this.leve6 = leve6;
    }
}
