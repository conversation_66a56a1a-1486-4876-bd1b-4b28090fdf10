package com.huitu.cloud.api.hsfxt.river.service;

import com.huitu.cloud.api.hsfxt.river.entity.JiangHeXiangQing;
import com.huitu.cloud.api.hsfxt.river.entity.RiverTree;
import com.huitu.cloud.api.hsfxt.river.entity.SheHuiJingJi;
import com.huitu.cloud.api.hsfxt.river.entity.TunXiangQing;
import com.huitu.cloud.validation.constraints.SqlInjection;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <p>
 * 风险图-河流  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
@Validated
public interface FxtRiverService {
    /**
     * 江河列表信息查询
     *
     * @param key   查询条件组合
     * @param name  最小编制区单位名称
     * @param hl    洪水量级
     * @param bzqid 编制区编码
     * @return
     */
    List<RiverTree> getRiverTreeInfo(@SqlInjection String key, String name, String hl, String bzqid);

    /**
     * 江河社会经济统计查询
     *
     * @param key 查询条件组合
     * @return
     */
    List<SheHuiJingJi> getRiverShjjTreeInfo(@SqlInjection String key);

    /**
     * 江河列表详情信息查询
     *
     * @param key   查询条件组合
     * @param adcd  政区编码
     * @param bzqid 编制区id
     * @return
     */
    JiangHeXiangQing getRiverShjjDetailInfo(String key, String adcd, String bzqid);

    /**
     * 屯详情信息查询
     *
     * @param key   查询条件组合
     * @param hl    编制区id
     * @param adcd  政区编码
     * @param bzqid 编制区id
     * @return
     */
    TunXiangQing getTunShjjDetailInfo(String key, String hl, String adcd, String bzqid);
}
