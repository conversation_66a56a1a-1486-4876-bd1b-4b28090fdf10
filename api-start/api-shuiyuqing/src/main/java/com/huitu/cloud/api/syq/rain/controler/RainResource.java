package com.huitu.cloud.api.syq.rain.controler;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.syq.rain.entity.*;
import com.huitu.cloud.api.syq.rain.service.RainService;
import com.huitu.cloud.api.syq.rsvr.entity.RsvrRainList;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-02
 */

@RestController
@Api(tags = "雨情信息查询接口")
@Validated
@RequestMapping("/api/syq/rain")
public class RainResource extends AbstractApiResource implements ApiResource {
    private static final Logger logger = LoggerFactory.getLogger(RainResource.class);

    @Override
    public String getUuid() {
        return "00000000-0000-0000-0000-000000000000";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private RainService baseService;

    @ApiOperation(value = "累计雨量统计", notes = "分页查询一段时间内的每个测站累计雨量")
    @PostMapping(value = "select-by-page")
    public ResponseEntity<SuccessResponse<Page<Rain>>> getInfoByPage(@Validated @RequestBody QueryRain baseDao) throws Exception {
        IPage<Rain> list = baseService.getRainByCondition(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBscd(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getRainShowType(), baseDao.getPageNum(), baseDao.getPageSize(), baseDao.getIsOut(), baseDao.getIsFollow(), baseDao.getForecastHour(), baseDao.getIsVideo());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "累计雨量对比", notes = "列表查询一段时间内的每个测站累计雨量对比")
    @PostMapping(value = "listRainDiff")
    public ResponseEntity<SuccessResponse<List<Rain>>> listRainDiff(@Validated @RequestBody QueryRain baseDao) throws Exception {
        List<Rain> list = baseService.listRainDiff(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBscd());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "政区累计雨量对比", notes = "列表查询一段时间内的每个政区累计雨量对比")
    @PostMapping(value = "listAdRainDiff")
    public ResponseEntity<SuccessResponse<List<AdAvgRain>>> listAdRainDiff(@Validated @RequestBody QueryRain baseDao) throws Exception {
        List<AdAvgRain> list = baseService.listAdRainDiff(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "雨情摘要", notes = "雨情摘要")
    @GetMapping(value = "get-rain-summary")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tm", value = "时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<RainSummary>> getRainSummary(@RequestParam String tm, @RequestParam String adcd) throws Exception {
        RainSummary rainSummary = baseService.getRainSummary(tm, adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", rainSummary));
    }


    @ApiOperation(value = "切换流域 中小河流测站列表查询")
    @PostMapping(value = "select-by-page-by-bas")
    public ResponseEntity<SuccessResponse<List<RainByBas>>> getRainByBasCondition(@Validated @RequestBody QueryRain baseDao) throws Exception {
        List<RainByBas> list = baseService.getRainByBasCondition(baseDao);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }


    @ApiOperation(value = "导出 切换流域 中小河流测站列表查询")
    @PostMapping(value = "export-select-by-page-by-bas")
    public void exportRainByBasCondition(@Validated @RequestBody QueryRain baseDao) throws Exception {
        baseService.exportRainByBasCondition(baseDao);
    }

    @ApiOperation(value = "切换流域 主要江河测站列表查询")
    @PostMapping(value = "select-by-page-by-bas-main")
    public ResponseEntity<SuccessResponse<List<RainByBas>>> getRainByBasMainCondition(@Validated @RequestBody QueryRain baseDao) throws Exception {
        List<RainByBas> list = baseService.getRainByBasMainCondition(baseDao);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "切换流域 主要江河测站列表查询（移动端）")
    @PostMapping(value = "select-by-page-by-bas-main-mobile")
    public ResponseEntity<SuccessResponse<List<RainByBas>>> getRainByBasMainConditionMobile(@Validated @RequestBody QueryRain baseDao) throws Exception {
        List<RainByBas> list = baseService.getRainByBasMainConditionMobile(baseDao);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出 切换流域 主要江河测站列表查询")
    @PostMapping(value = "export-select-by-page-by-bas-main")
    public void exportRainByBasMainCondition(@Validated @RequestBody QueryRain baseDao) throws Exception {
        baseService.exportRainByBasMainCondition(baseDao);
    }

    @ApiOperation(value = "累计雨量导出", notes = "累计雨量导出")
    @PostMapping(value = "export-select-by-page")
    public void exportRainByCondition(@RequestBody ExportQueryRain exportQueryRain) throws Exception {
        baseService.exportRainByCondition(exportQueryRain.getType(), exportQueryRain.getStm(), exportQueryRain.getEtm(), exportQueryRain.getStType(), exportQueryRain.getAdcd(), exportQueryRain.getBscd(),
                exportQueryRain.getStnm(), exportQueryRain.getThreshold(), exportQueryRain.getRainShowType(), exportQueryRain.getIsOut());
    }

    @ApiOperation(value = "查询所有测站累计雨量统计", notes = "查询一段时间内的每个测站累计雨量 ")
    @PostMapping(value = "select-all")
    public ResponseEntity<SuccessResponse<List<Rain>>> getInfoAll(@Validated @RequestBody QueryRain baseDao) throws Exception {
        List<Rain> list = baseService.getRainByConditionAll(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBscd(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getRainShowType(), baseDao.getIsOut(), null, baseDao.getForecastHour(), null);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }


    @ApiOperation(value = "单站雨量时段列表", notes = "查询一段时间内单站的降雨时段列表")
    @GetMapping(value = "get-rain-by-tm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "时分", required = false, dataType = "String", defaultValue = "1")
    })
    public ResponseEntity<SuccessResponse<List<StPptnR>>> getRainTmList(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm, @RequestParam(required = false, defaultValue = "1") String type) throws Exception {
        List<StPptnR> list = baseService.getRainListByTm(stm, etm, stcd, type);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "单站雨量时段列表导出", notes = "单站雨量时段列表导出")
    @PostMapping(value = "export-get-rain-by-tm")
    public void exportRainTmList(@RequestBody ExportQueryRainOneSt queryRainOneSt) throws Exception {
        baseService.exportRainTmList(queryRainOneSt.getStm(), queryRainOneSt.getEtm(), queryRainOneSt.getStcd(), queryRainOneSt.getType());
    }


    @ApiOperation(value = "政区面平均雨量树", notes = "查询一段时间内政区面平均雨量树")
    @PostMapping(value = "get-avg-rain-tree-by-tm")
    public ResponseEntity<SuccessResponse<AdAvgRain>> getAvgRainTmTree(@Validated @RequestBody QueryAdAvgRain queryAdAvgRain) throws Exception {
        AdAvgRain result = baseService.getAvgRainTreeByTm(queryAdAvgRain.getStm(), queryAdAvgRain.getEtm(), queryAdAvgRain.getAdcd(), queryAdAvgRain.getStType(), queryAdAvgRain.getShowLevel());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "政区面平均雨量树", notes = "查询一段时间内政区面平均雨量树")
    @PostMapping(value = "get-avg-rain-tree-by-tm-new")
    public ResponseEntity<SuccessResponse<List<AdAvgRain>>> getAvgRainTmTreeNew(@Validated @RequestBody QueryAdAvgRain queryAdAvgRain) throws Exception {
        List<AdAvgRain> result = baseService.getAvgRainTreeByTmNew(queryAdAvgRain.getStm(), queryAdAvgRain.getEtm(), queryAdAvgRain.getStType(), queryAdAvgRain.getAdcd(), queryAdAvgRain.getIsself());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "政区面平均雨量列表导出", notes = "政区面平均雨量列表导出")
    @PostMapping(value = "export-get-avg-rain-tree-by-tm-new")
    public void exportAvgRainTmTreeNew(@RequestBody QueryAdAvgRain queryAdAvgRain) throws Exception {
        baseService.exportAvgRainTmTreeNew(queryAdAvgRain.getStm(), queryAdAvgRain.getEtm(), queryAdAvgRain.getStType(), queryAdAvgRain.getAdcd());
    }

    @ApiOperation(value = "累计雨量最大值", notes = "查询一段时间内累计雨量最大值")
    @PostMapping(value = "get-max-rain-by-tm")
    public ResponseEntity<SuccessResponse<List<Rain>>> getMaxRainTm(@RequestBody QueryAdAvgRain queryAdAvgRain) throws Exception {
        List<Rain> result = baseService.getMaxRainByTm(queryAdAvgRain.getStm(), queryAdAvgRain.getEtm(), queryAdAvgRain.getAdcd(), queryAdAvgRain.getStType(), queryAdAvgRain.getIsOut());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "统计不同雨量级别的测站数", notes = "查询一段时间内各个雨量级别的测站数")
    @PostMapping(value = "get-rain-level-st-num-by-tm")
    public ResponseEntity<SuccessResponse<List<RainLevelStNum>>> getRainLevelStNumTm(@Validated @RequestBody QueryRainLevelStNum queryRainLevelStNum) throws Exception {
        List<RainLevelStNum> result = baseService.getRainLevelStNumByTm(
                queryRainLevelStNum.getStType(), queryRainLevelStNum.getAdcd(), queryRainLevelStNum.getEtm(), queryRainLevelStNum.getStm(),
                queryRainLevelStNum.getRainRule0(), queryRainLevelStNum.getRainRule1(), queryRainLevelStNum.getRainRule2(), queryRainLevelStNum.getRainRule3(),
                queryRainLevelStNum.getRainRule4(), queryRainLevelStNum.getRainRule5());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "降雨强度统计", notes = "分页查询一段时间内的各个测站的降雨强度")
    @PostMapping(value = "select-rain-strength-by-page")
    public ResponseEntity<SuccessResponse<Page<RainStrength>>> getRainStrengthByPage(@RequestBody QueryRainStrength rainStrength) throws Exception {
        IPage<RainStrength> list = baseService.getRainStrength(rainStrength.getStm(), rainStrength.getEtm(), rainStrength.getStType(), rainStrength.getIntv(), rainStrength.getAdcd(), rainStrength.getBscd(), rainStrength.getStnm(), rainStrength.getPageNum(), rainStrength.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "降雨强度统计导出", notes = "降雨强度统计导出")
    @PostMapping(value = "export-select-rain-strength")
    public void exportRainStrength(@RequestBody ExportQueryRainStrength rainStrength) throws Exception {
        baseService.exportRainStrength(rainStrength.getStm(), rainStrength.getEtm(), rainStrength.getStType(), rainStrength.getIntv(), rainStrength.getAdcd(), rainStrength.getBscd(), rainStrength.getStnm());
    }

    @ApiOperation(value = "降雨测站归属类型统计", notes = "查询一段时间内降雨测站归属类型统计")
    @PostMapping(value = "select-ascription-type-count")
    public ResponseEntity<SuccessResponse<AscriptionTypeCount>> getAscriptionTypeCount(@Validated @RequestBody QueryAscriptionType ascriptionType) throws Exception {
        AscriptionTypeCount result = baseService.getAscriptionTypeCount(ascriptionType.getAdcd(), ascriptionType.getEtm(), ascriptionType.getStm(), ascriptionType.getStType(), ascriptionType.getIsOut());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "降雨测站归属类型统计（新）", notes = "降雨测站归属类型统计（新）")
    @GetMapping(value = "select-ascription-type-count-new")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "bscd", value = "流域", dataType = "String"),
            @ApiImplicitParam(name = "stType", value = "测站归属类型  1、水文，2、山洪  3、气象  6、运管 多个值以逗号隔开", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<AscriptionTypeCount>> getAscriptionTypeCountNew(@RequestParam String adcd, @RequestParam String bscd, @RequestParam String stType) throws Exception {
        AscriptionTypeCount result = baseService.getAscriptionTypeCountNew(adcd, bscd, stType);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "降雨告警指标", notes = "降雨告警指标")
    @GetMapping(value = "select-bsnrainalarmb")
    public ResponseEntity<SuccessResponse<BsnRainalarmB>> getBsnRainalarmb() throws Exception {
        BsnRainalarmB param = baseService.getBsnRainalarmb();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", param));
    }

    @ApiOperation(value = "降雨告警指标设置", notes = "降雨告警指标设置")
    @PostMapping(value = "update-bsnrainalarmb")
    public ResponseEntity<SuccessResponse<String>> updateBsnRainalarmb(@RequestBody BsnRainalarmB param) throws Exception {
        Boolean flag = baseService.updateBsnRainalarmb(param.getId(), param.getMovedrp1h(), param.getMovedrp3h(),
                param.getMovedrp6h(), param.getMovedrp12h(), param.getMovedrp24h(), param.getWarndrp1h(), param.getWarndrp3h(),
                param.getWarndrp6h(), param.getWarndrp12h(), param.getWarndrp24h());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }


    @ApiOperation(value = "查询历史雨量信息", notes = "查询历史雨量信息列表")
    @PostMapping(value = "select-rain-history-list-by-page")
    public ResponseEntity<SuccessResponse<IPage<HisRain>>> getRainHisListByPage(@Validated @RequestBody QueryHisRain queryHisRain) throws Exception {
        IPage<HisRain> list = baseService.getRainHisListByPage(queryHisRain.getAdcd(), queryHisRain.getStm(), queryHisRain.getEtm(), queryHisRain.getStType(), queryHisRain.getTmType(), queryHisRain.getStnm(), queryHisRain.getThreshold(), queryHisRain.getPageNum(), queryHisRain.getPageSize(), queryHisRain.getIsOut());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "历史雨量信息导出", notes = "历史雨量信息导出")
    @PostMapping(value = "export-select-rain-history-list")
    public void exportRainHisList(@Validated @RequestBody ExportQueryHisRain queryHisRain) throws Exception {
        baseService.exportRainHisList(queryHisRain.getAdcd(), queryHisRain.getStm(), queryHisRain.getEtm(), queryHisRain.getStType(), queryHisRain.getTmType(), queryHisRain.getStnm(), queryHisRain.getThreshold(), queryHisRain.getIsOut());
    }

    @ApiOperation(value = "流域降雨统计", notes = "可根据流域、时间范围查询流域雨情。")
    @GetMapping(value = "select-rainfall-statistics")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stTypes", value = "测站归属类型  1、水文，2、山洪  3、气象 多个值以逗号隔开", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<BsnBasStBTo>> getRainfallStatistics(@RequestParam String stTypes, @RequestParam String stm, @RequestParam String etm) throws Exception {
        BsnBasStBTo list = baseService.getRainfallStatistics(stTypes, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "流域降雨汇总", notes = "可根据流域、时间范围查询流域雨情。")
    @GetMapping(value = "select-bas-rain-summary-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "basCode", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "stTypes", value = "测站归属类型  1、水文，2、山洪  3、气象 多个值以逗号隔开", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", dataType = "String"),
            @ApiImplicitParam(name = "isOut", value = "测站显示 1、辖区内，2、辖区外 多个值以逗号隔开", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<BasRainSummary>>> getBasRainSummaryList(@RequestParam String basCode, @RequestParam String stTypes, @RequestParam String stm, @RequestParam String etm, @RequestParam String isOut) {
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", baseService.getBasRainSummaryList(basCode, stTypes, stm, etm, isOut)));
    }

    @ApiOperation(value = "流域降雨统计导出", notes = "流域降雨统计导出")
    @PostMapping(value = "export-select-rainfall-statistics")
    public void exportRainfallStatistics(@RequestBody ExportQueryRainfallStatistics statistics) throws Exception {
        baseService.exportRainfallStatistics(statistics.getStTypes(), statistics.getStm(), statistics.getEtm());
    }

    @ApiOperation(value = "值班报告降雨情况", notes = "值班报告降雨情况。")
    @GetMapping(value = "select-rain-duty")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<RainByDutyRecort>> getRainInfoByDuty(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        RainByDutyRecort list = baseService.getRainInfoByDuty(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "业务门户降雨统计", notes = "业务门户降雨统计。")
    @GetMapping(value = "select-rain-ywmh")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<RainByYwmh>> getRainInfoByYwmh(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        RainByYwmh list = baseService.getRainInfoByYwmh(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "大型水库降雨统计", notes = "可根据时间范围，测站类型查询大型水库的降雨汇总")
    @GetMapping(value = "select-res-rain-statistics")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stTypes", value = "测站归属类型  1、水文，2、山洪  3、气象 多个值以逗号隔开", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrRainStatisticsVo>>> getRsvrStatistics(@RequestParam String stTypes, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<RsvrRainStatisticsVo> list = baseService.getRsvrRainStatistics(stTypes, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "大型水库降雨统计导出", notes = "大型水库降雨统计导出")
    @GetMapping(value = "export-res-rain-statistics")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stTypes", value = "测站归属类型  1、水文，2、山洪  3、气象 多个值以逗号隔开", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public void exportRsvrStatistics(@RequestParam String stTypes, @RequestParam String stm, @RequestParam String etm) throws Exception {
        baseService.exportRsvrStatistics(stTypes, stm, etm);
    }

    @ApiOperation(value = "按照时间查询政区的场次降雨", notes = "按照时间查询政区的场次降雨")
    @GetMapping(value = "select-rain-times")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<BsnTimesR>>> getRainTimes(@RequestParam String stm, @RequestParam String etm, @RequestParam String adcd) throws Exception {
        List<BsnTimesR> list = baseService.getRainTimes(stm, etm, adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "根据政区场次统计测站累计雨量", notes = "根据政区场次统计测站累计雨量")
    @PostMapping(value = "select-by-raintms")
    public ResponseEntity<SuccessResponse<Page<TmsRainVo>>> getRainCountByRainTms(@RequestBody RainTmsQo baseDao) throws Exception {
        IPage<TmsRainVo> iPage = baseService.getTmsRainByCondition(baseDao.getTmsid(), baseDao.getTmsad(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBsnm(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getPageNum(), baseDao.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "根据政区场次统计测站累计雨量导出", notes = "根据政区场次统计测站累计雨量导出")
    @PostMapping(value = "export-select-by-raintms")
    public void exportRainCountByRainTms(@RequestBody RainTmsQo baseDao) throws Exception {
        baseService.exportRainCountByRainTms(baseDao.getTmsid(), baseDao.getTmsad(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBsnm(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getPageNum(), baseDao.getPageSize());
    }

    @ApiOperation(value = "按照时间查询短信描述", notes = "按照时间查询短信描述")
    @GetMapping(value = "select-sms-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<SmsDesInfo>> getSmsInfo(@RequestParam String stm, @RequestParam String etm, @RequestParam String adcd) throws Exception {
        SmsDesInfo result = baseService.getSmsInfo(stm, etm, adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "墒情分析累计雨量统计", notes = "分页查询一段时间内的每个测站累计雨量")
    @PostMapping(value = "select-by-page-sqfx")
    public ResponseEntity<SuccessResponse<Page<Rain>>> getInfoByPageForSqFx(@RequestBody QueryRain baseDao) throws Exception {
        IPage<Rain> list = baseService.getRainByConditionForSqfx(baseDao.getStm(), baseDao.getEtm(), baseDao.getAdcd(), baseDao.getPageNum(), baseDao.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "墒情分析累计雨量导出", notes = "墒情分析累计雨量导出")
    @PostMapping(value = "export-select-by-page-sqfx")
    public void exportRainByConditionForSqfx(@RequestBody ExportQueryRain exportQueryRain) throws Exception {
        baseService.exportRainByConditionForSqfx(exportQueryRain.getStm(), exportQueryRain.getEtm(), exportQueryRain.getAdcd());
    }

    @ApiOperation(value = "逐站24小时降雨信息", notes = "逐站24小时降雨信息")
    @PostMapping(value = "select-one-day-tm-merge")
    public ResponseEntity<SuccessResponse<Page<Rain>>> getRainListTmMerge(@RequestBody OneDayRainQo baseDao) throws Exception {
        IPage<OneDayRain> iPage = baseService.getRainListTmMerge(baseDao.getDate(), baseDao.getAdcd(), baseDao.getStType(), baseDao.getStnm(), baseDao.getAdmauth(), baseDao.getPageNum(), baseDao.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "墒情分析逐站24小时降雨信息", notes = "墒情分析逐站24小时降雨信息")
    @PostMapping(value = "select-one-day-tm-merge-sqfx")
    public ResponseEntity<SuccessResponse<Page<Rain>>> getRainListTmMergeForSqfx(@RequestBody OneDayRainQo baseDao) throws Exception {
        IPage<OneDayRain> iPage = baseService.getRainListTmMergeForSqfx(baseDao.getDate(), baseDao.getAdcd(), baseDao.getPageNum(), baseDao.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "导出逐站24小时降雨信息", notes = "导出逐站24小时降雨信息")
    @PostMapping(value = "export-one-day-tm-merge")
    public void exportRainListTmMerge(@RequestBody OneDayRainQo baseDao) throws Exception {
        baseService.exportRainListTmMerge(baseDao.getDate(), baseDao.getAdcd(), baseDao.getStType(), baseDao.getStnm(), baseDao.getAdmauth());

    }

    @ApiOperation(value = "墒情分析导出逐站24小时降雨信息", notes = "墒情分析导出逐站24小时降雨信息")
    @PostMapping(value = "export-one-day-tm-merge-sqfx")
    public void exportRainListTmMergeForSqfx(@RequestBody OneDayRainQo baseDao) throws Exception {
        baseService.exportRainListTmMergeForSqfx(baseDao.getDate(), baseDao.getAdcd());

    }

    @ApiOperation(value = "墒情气象监测查询", notes = "墒情气象监测查询")
    @PostMapping(value = "select-moisture-sqfx")
    public ResponseEntity<SuccessResponse<Page<MoistureMeteorologyVo>>> getMoistureForSqfx(@RequestBody MoistureMeteorologyQo baseDao) throws Exception {
        IPage<MoistureMeteorologyVo> iPage = baseService.getMoistureForSqfx(baseDao.getStm(), baseDao.getEtm(), baseDao.getQuery(), baseDao.getPageNum(), baseDao.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "墒情气象监测导出", notes = "墒情气象监测导出")
    @PostMapping(value = "export-moisture-sqfx")
    public void exportMoistureForSqfx(@RequestBody MoistureMeteorologyQo baseDao) throws Exception {
        baseService.exportMoistureForSqfx(baseDao.getStm(), baseDao.getEtm(), baseDao.getQuery(), baseDao.getPageNum(), baseDao.getPageSize());
    }

    @ApiOperation(value = "最近24小时累计降雨统计", notes = "最近24小时累计降雨统计")
    @PostMapping(value = "select-time-interval")
    public ResponseEntity<SuccessResponse<Page<RainTimeIntervalInfo>>> getTimeInterval(@RequestBody QueryRainTimeInterval baseDao) throws Exception {
        IPage<RainTimeIntervalInfo> iPage = baseService.getTimeInterval(baseDao.getAdcd(), baseDao.getStType(), baseDao.getTm(), baseDao.getStnm(), baseDao.getSort(), baseDao.getPageNum(), baseDao.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "导出最近24小时累计降雨统计", notes = "作者：赵英捷")
    @PostMapping(value = "export-time-interval")
    public void exportTimeInterval(@Validated @RequestBody QueryRainTimeInterval query, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("时间维度累计降雨信息", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.exportTimeInterval(query, response.getOutputStream());
        } catch (Exception ex) {
            logger.error("时间维度累计降雨信息导出失败，参数：{}", JSON.toJSONString(query));
            logger.error(ex.getMessage(), ex);
        }
    }


    @ApiOperation(value = "最近24小时累计降雨统计", notes = "最近24小时累计降雨统计")
    @PostMapping(value = "select-time-interval-list")
    public ResponseEntity<SuccessResponse<List<RainTimeIntervalInfo>>> getTimeIntervalList(@RequestBody QueryRainTimeInterval baseDao) throws Exception {
        List<RainTimeIntervalInfo> iPage = baseService.getTimeIntervalList(baseDao.getAdcd(), baseDao.getStType(), baseDao.getTm(), baseDao.getStnm(), baseDao.getSort());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "测站雨量数据对比", notes = "测站雨量数据对比")
    @GetMapping(value = "select-stcd-tm-contrast")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tp", value = "颗粒度（1：年， 2：月）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tms", value = "基准时间和对比时间的字符串，用,分割。如：年（2022,2021,2020）月（202201,202202,202203）", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<Map<String, String>>>> getStcdContrastBytm(@RequestParam String stcd, @RequestParam String tp, @RequestParam String tms) throws Exception {
        List<Map<String, String>> list = baseService.getStcdContrastBytm(stcd, tp, tms);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "政区雨量数据对比", notes = "政区雨量数据对比")
    @GetMapping(value = "select-adcd-tm-contrast")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tp", value = "颗粒度（1：年， 2：月）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tms", value = "基准时间和对比时间的字符串，用,分割。如：年（2022,2021,2020）月（202201,202202,202203）", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<Map<String, String>>>> getAdcdContrastBytm(@SqlInjection @RequestParam String adcd, @RequestParam String tp, @RequestParam String tms) throws Exception {
        List<Map<String, String>> list = baseService.getAdcdContrastBytm(adcd, tp, tms);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "流域雨量数据对比", notes = "流域雨量数据对比")
    @GetMapping(value = "select-bscd-tm-contrast")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bscd", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "tp", value = "颗粒度（1：年， 2：月）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tms", value = "基准时间和对比时间的字符串，用,分割。如：年（2022,2021,2020）月（202201,202202,202203）", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<Map<String, String>>>> getBscdContrastBytm(@SqlInjection @RequestParam String bscd, @RequestParam String tp, @RequestParam String tms) throws Exception {
        List<Map<String, String>> list = baseService.getBscdContrastBytm(bscd, tp, tms);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询测站未来各小时降雨量", notes = "查询测站未来各小时降雨量")
    @GetMapping(value = "select-forecast-hour-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站代码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "预报开始时间", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<RainForecastHour>>> getForecastHourByStcd(@RequestParam String stcd, @RequestParam String stm) throws Exception {
        List<RainForecastHour> list = baseService.getForecastHourByStcd(stcd, stm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询预报降雨测站累计雨量", notes = "分页查询预报降雨测站累计雨量")
    @PostMapping(value = "select-forecast-by-stcd")
    public ResponseEntity<SuccessResponse<List<Rain>>> getForecastByStcd(@Validated @RequestBody QueryRain baseDao) throws Exception {
        List<Rain> list = baseService.getForecastByStcd(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询未来降雨量", notes = "查询未来降雨量")
    @PostMapping(value = "select-forecast-list")
    public ResponseEntity<SuccessResponse<List<Rain>>> getForecastList(@RequestBody QueryRain baseDao) throws Exception {
        List<Rain> list = baseService.getForecastList(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "下级政区实际降雨和预报降雨平均雨量列表", notes = "下级政区实际降雨和预报降雨平均雨量列表")
    @GetMapping(value = "get-next-avg-rain-forecast-list-by-tm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "测站代码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<AdAvgRainForecast>>> getAvgRainForecastListByAdcdTm(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<AdAvgRainForecast> list = baseService.getAvgRainForecastListByAdcdTm(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询测站未来各小时降雨量", notes = "查询测站未来各小时降雨量")
    @GetMapping(value = "select-forecast-3hours")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站代码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "预报开始时间", required = false, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "预报结束时间", required = false, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<StPptnF>>> getForecast3Hours(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<StPptnF> list = baseService.getForecast3Hours(stcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询测站未来各小时降雨量（未来3小时逐6分钟或未来72小时逐1小时）", notes = "查询测站未来各小时降雨量（未来3小时逐6分钟或未来72小时逐1小时）")
    @GetMapping(value = "select-forecast-3or24hours")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站代码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "预报开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "预报结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tp", value = "类型（3：未来3小时逐6分钟，72：未来72小时逐1小时）", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<StPptnFVo>>> getForecast3Or72List(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String tp) throws Exception {
        List<StPptnFVo> list = baseService.getForecast3Or72List(stcd, stm, etm, tp);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "雨情测站关联村", notes = "赵英捷")
    @GetMapping(value = "get-rain-pointer-list/{stcd}")
    public ResponseEntity<SuccessResponse<List<RainPointerAD>>> getRainPointerADList(@PathVariable("stcd") String stcd) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.getRainPointerADList(stcd)));
    }

    @ApiOperation(value = "雨情村关联测站", notes = "赵英捷")
    @GetMapping(value = "get-rain-pointer-st-list/{adcd}")
    public ResponseEntity<SuccessResponse<List<RainPointerST>>> getRainPointerSTList(@PathVariable("adcd") String adcd) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.getRainPointerSTList(adcd)));
    }

    @ApiOperation(value = "雨情笼罩面降雨", notes = "作者：jiangjy")
    @PostMapping("iso-rain-area-ad")
    public ResponseEntity<SuccessResponse<List<RainArea>>> getRainAreaAd(@Validated @RequestBody QueryRain baseDao) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRainAreaAd(baseDao)));
    }

    @ApiOperation(value = "雨情笼罩面降雨", notes = "作者：jiangjy")
    @PostMapping("iso-rain-area-bas")
    public ResponseEntity<SuccessResponse<List<RainArea>>> getRainAreaBas(@Validated @RequestBody QueryRain baseDao) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRainAreaBas(baseDao)));
    }

    @ApiOperation(value = "雨情笼罩面降雨导出", notes = "雨情笼罩面降雨导出")
    @PostMapping(value = "export-iso-rain-area-ad")
    public void exportRainArea(@Validated @RequestBody QueryRain baseDao) throws Exception {
        baseService.exportRainArea(baseDao);
    }

    @ApiOperation(value = "工作台降雨统计", notes = "赵英捷")
    @PostMapping(value = "select-map-by-work")
    public ResponseEntity<SuccessResponse<List<Map<String, Object>>>> getMapByWork(@Validated @RequestBody QueryRain baseDao) throws Exception {
        IPage<Rain> page = baseService.getRainByCondition(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBscd(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getRainShowType(), baseDao.getPageNum(), baseDao.getPageSize(), baseDao.getIsOut(), baseDao.getIsFollow(), baseDao.getForecastHour(), baseDao.getIsVideo());
        List<Map<String, Object>> list = new ArrayList<>();

        Map<String, Object> tdby = new HashMap<>();
        tdby.put("type", "特大暴雨（250mm以上）");
        List<Rain> tdbyList = page.getRecords().stream().filter(x -> StringUtils.isNotBlank(x.getDrps()) && Double.parseDouble(x.getDrps()) >= 250).collect(Collectors.toList());
        List<String> tdbyxadList = tdbyList.stream().map(x -> x.getAdcd().substring(0, 6)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> tdbyzadList = tdbyList.stream().map(x -> x.getAdcd().substring(0, 9)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> tdbycadList = tdbyList.stream().map(x -> x.getAdcd().substring(0, 12)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        tdby.put("list", tdbyList);
        tdby.put("xad", tdbyxadList.size());
        tdby.put("zad", tdbyzadList.size());
        tdby.put("cad", tdbycadList.size());
        tdby.put("st", tdbyList.size());
        list.add(tdby);

        Map<String, Object> dby = new HashMap<>();
        dby.put("type", "大暴雨（100~250mm）");
        List<Rain> dbyList = page.getRecords().stream().filter(x -> StringUtils.isNotBlank(x.getDrps()) && Double.parseDouble(x.getDrps()) >= 100 && Double.parseDouble(x.getDrps()) < 250).collect(Collectors.toList());
        List<String> dbyxadList = dbyList.stream().map(x -> x.getAdcd().substring(0, 6)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> dbyzadList = dbyList.stream().map(x -> x.getAdcd().substring(0, 9)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> dbycadList = dbyList.stream().map(x -> x.getAdcd().substring(0, 12)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        dby.put("list", dbyList);
        dby.put("xad", dbyxadList.size());
        dby.put("zad", dbyzadList.size());
        dby.put("cad", dbycadList.size());
        dby.put("st", dbyList.size());
        list.add(dby);

        Map<String, Object> by = new HashMap<>();
        by.put("type", "暴雨（50~100mm）");
        List<Rain> byList = page.getRecords().stream().filter(x -> StringUtils.isNotBlank(x.getDrps()) && Double.parseDouble(x.getDrps()) >= 50 && Double.parseDouble(x.getDrps()) < 100).collect(Collectors.toList());
        List<String> byxadList = byList.stream().map(x -> x.getAdcd().substring(0, 6)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> byzadList = byList.stream().map(x -> x.getAdcd().substring(0, 9)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> bycadList = byList.stream().map(x -> x.getAdcd().substring(0, 12)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        by.put("list", byList);
        by.put("xad", byxadList.size());
        by.put("zad", byzadList.size());
        by.put("cad", bycadList.size());
        by.put("st", byList.size());
        list.add(by);

        Map<String, Object> dy = new HashMap<>();
        dy.put("type", "大雨（25~50mm）");
        List<Rain> dyList = page.getRecords().stream().filter(x -> StringUtils.isNotBlank(x.getDrps()) && Double.parseDouble(x.getDrps()) >= 25 && Double.parseDouble(x.getDrps()) < 50).collect(Collectors.toList());
        List<String> dyadList = dyList.stream().map(x -> x.getAdcd().substring(0, 6)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> dyzadList = dyList.stream().map(x -> x.getAdcd().substring(0, 9)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> dycadList = dyList.stream().map(x -> x.getAdcd().substring(0, 12)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        dy.put("list", dyList);
        dy.put("xad", dyadList.size());
        dy.put("zad", dyzadList.size());
        dy.put("cad", dycadList.size());
        dy.put("st", dyList.size());
        list.add(dy);

        Map<String, Object> zy = new HashMap<>();
        zy.put("type", "中雨（10~25mm）");
        List<Rain> zyList = page.getRecords().stream().filter(x -> StringUtils.isNotBlank(x.getDrps()) && Double.parseDouble(x.getDrps()) >= 10 && Double.parseDouble(x.getDrps()) < 25).collect(Collectors.toList());
        List<String> zyadList = zyList.stream().map(x -> x.getAdcd().substring(0, 6)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> zyzadList = zyList.stream().map(x -> x.getAdcd().substring(0, 9)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> zycadList = zyList.stream().map(x -> x.getAdcd().substring(0, 12)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        zy.put("list", zyList);
        zy.put("xad", zyadList.size());
        zy.put("zad", zyzadList.size());
        zy.put("cad", zycadList.size());
        zy.put("st", zyList.size());
        list.add(zy);

        Map<String, Object> xy = new HashMap<>();
        xy.put("type", "小雨（0~10mm）");
        List<Rain> xyList = page.getRecords().stream().filter(x -> StringUtils.isNotBlank(x.getDrps()) && Double.parseDouble(x.getDrps()) >= 0.1 && Double.parseDouble(x.getDrps()) < 10).collect(Collectors.toList());
        List<String> xyadList = xyList.stream().map(x -> x.getAdcd().substring(0, 6)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> xyzadList = xyList.stream().map(x -> x.getAdcd().substring(0, 9)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<String> xycadList = xyList.stream().map(x -> x.getAdcd().substring(0, 12)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        xy.put("list", xyList);
        xy.put("xad", xyadList.size());
        xy.put("zad", xyzadList.size());
        xy.put("cad", xycadList.size());
        xy.put("st", xyList.size());
        list.add(xy);

//        Map<String, Object> wy = new HashMap<>();
//        wy.put("type", "无雨");
//        List<Rain> wyList = page.getRecords().stream().filter(x -> StringUtils.isEmpty(x.getDrps()) || Double.parseDouble(x.getDrps()) < 0.1).collect(Collectors.toList());
//        List<String> wyadList = wyList.stream().map(x -> x.getAdcd().substring(0, 6)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
//        List<String> wyzadList = wyList.stream().map(x -> x.getAdcd().substring(0, 9)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
//        List<String> wycadList = wyList.stream().map(x -> x.getAdcd().substring(0, 12)).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
//        wy.put("list", wyList);
//        wy.put("xad", wyadList.size());
//        wy.put("zad", wyzadList.size());
//        wy.put("cad", wycadList.size());
//        wy.put("st", wyList.size());
//        list.add(wy);

        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "雨情信息水库降雨列表", notes = "雨情信息水库降雨列表")
    @GetMapping(value = "select-bas-rain-reservoir-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "basCode", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "resName", value = "水库名称", dataType = "String"),
            @ApiImplicitParam(name = "engScal", value = "水库规模", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<RsvrRainList>>> getRainReservoirList(@RequestParam String basCode, @RequestParam String adcd, @RequestParam String resName, @RequestParam String engScal, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<RsvrRainList> param = baseService.getRainReservoirList(basCode, adcd, resName, engScal, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", param));
    }

}







