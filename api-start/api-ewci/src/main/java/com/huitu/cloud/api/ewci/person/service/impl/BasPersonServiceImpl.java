package com.huitu.cloud.api.ewci.person.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.mapper.BasPersonDao;
import com.huitu.cloud.api.ewci.person.service.BasPersonService;
import com.huitu.cloud.util.AdcdUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 中小河流责任人 实现类
 *
 * <AUTHOR>
 */
@Service
public class BasPersonServiceImpl implements BasPersonService {

    private static final Logger logger = LoggerFactory.getLogger(BasPersonServiceImpl.class);

    private BasPersonDao baseDao;

    public BasPersonServiceImpl(BasPersonDao baseDao) {
        this.baseDao = baseDao;
    }


    @Override
    public IPage<BasPersonVo> getPageList(BasPersonQuery query) {
        return baseDao.getPageList(query.toPageParam(), query.toQueryParam());
    }

    @Override
    public List<BasSummaryVo> getBasSummaryList(BasSummaryQuery query) {
        if (query.getLevel() > 6) {
            throw new RuntimeException("行政区划代码无效，仅支持到县以上级别（含县）");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", query.getAdcd());
        params.put("level", query.getLevel());
        params.put("include", query.isInclude() ? 1 : 0);
        params.put("lowLevel", query.getLowLevel());
        List<BasSummaryVo> source = baseDao.getBasSummaryList(params);

        if (CollectionUtils.isEmpty(source)) {
            return new ArrayList<>();
        }
        if (!query.isInclude()) {
            // 不包含下级，无需创建树
            return source;
        }
        Map<String, BasSummaryVo> center = new LinkedHashMap<>(source.size());
        source.forEach(ad -> center.put(ad.getAdcd(), ad));
        List<BasSummaryVo> target = new ArrayList<>();
        for (BasSummaryVo node : source) {
            if (center.containsKey(node.getPadcd())) {
                BasSummaryVo parent = center.get(node.getPadcd());
                if (null == parent.getChildren()) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            } else {
                target.add(node);
            }
        }
        return target;
    }

    @Override
    public void dataExport(BasPersonQuery query, OutputStream output) {
        IPage<BasPersonVo> page = baseDao.getPageList(query.toPageParam(), query.toQueryParam());
        EasyExcel.write(output, BasPersonVo.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("中小河流责任人信息")
                .doWrite(page.getRecords());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int basPersonEdit(BasPersonVo entity) {
        List<BasPersonB> list = new ArrayList<>();
        // 县级行政责任人
        if (StringUtils.isNotBlank(entity.getXjRealnm()) && StringUtils.isNotBlank(entity.getXjMobilePhone())) {
            BasPersonB xjPerson = new BasPersonB();
            xjPerson.setAdcd(entity.getAdcd());
            xjPerson.setPertp("1");
            xjPerson.setBasCode(entity.getBasCode());
            xjPerson.setSno(entity.getSno());
            xjPerson.setRealnm(entity.getXjRealnm());
            xjPerson.setPost(entity.getXjPost());
            xjPerson.setOfficePhone(entity.getXjOfficePhone());
            xjPerson.setMobilePhone(entity.getXjMobilePhone());
            xjPerson.setTs(LocalDateTime.now());
            list.add(xjPerson);
        }

        // 乡镇行政责任人
        if (StringUtils.isNotBlank(entity.getXzRealnm()) && StringUtils.isNotBlank(entity.getXzMobilePhone())) {
            BasPersonB xzPerson = new BasPersonB();
            xzPerson.setAdcd(entity.getAdcd());
            xzPerson.setBasCode(entity.getBasCode());
            xzPerson.setPertp("2");
            xzPerson.setSno(entity.getSno());
            xzPerson.setRealnm(entity.getXzRealnm());
            xzPerson.setPost(entity.getXzPost());
            xzPerson.setOfficePhone(entity.getXzOfficePhone());
            xzPerson.setMobilePhone(entity.getXzMobilePhone());
            xzPerson.setTs(LocalDateTime.now());
            list.add(xzPerson);
        }
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", entity.getAdcd());
        param.put("basCode", entity.getBasCode());
        param.put("sno", entity.getSno());
        int insertCode = 0;
        int delCode = baseDao.delete(param);
        if (delCode > 0) {
            insertCode = baseDao.insertList(list);
        }
        return insertCode;
    }

    @Override
    public List<String> getBasCodeList(String adcd) {
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        return baseDao.getBasCodeList(param);
    }

    @Override
    public List<BasFailReportVo> getBasFailReportList(String adcd) {
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        return baseDao.getBasFailReportList(param);
    }

    @Override
    public void exportBasFailReport(BasPersonQuery query, OutputStream output) {
        List<BasFailReportVo> list = getBasFailReportList(query.getAdcd());
        EasyExcel.write(output, BasFailReportVo.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("未上报中小河流责任人信息")
                .doWrite(list);
    }
}
