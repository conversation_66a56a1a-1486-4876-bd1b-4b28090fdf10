package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 预警反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@TableName("EXAM_PROVINCE_SCORE")
@ApiModel(value="ExamProvinceScore对象", description="与省级部门配合得分")
public class ExamProvinceScore implements Serializable {
    @ApiModelProperty(value = "政区编码")
    @TableId(value = "ADCD", type = IdType.NONE)
    private String adcd;

    @ApiModelProperty(value = "年月")
    @TableField("YM")
    private String ym;

    @ApiModelProperty(value = "得分")
    @TableField("SCORE")
    private double score;

    @ApiModelProperty(value = "最后修改人ID")
    @TableField("OCCURUSER")
    private String occurUser;

    @ApiModelProperty(value = "最后修改时间")
    @TableField("OCCURTM")
    private Date occurTM;

    @ApiModelProperty(value = "UUID版本标识")
    @TableField("VCODE")
    private String vcode;

    public String getAdcd() {
        return adcd;
    }
    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getYm() {
        return ym;
    }
    public void setYm(String ym) {
        this.ym = ym;
    }

    public double getScore() {
        return score;
    }
    public void setScore(double score) {
        this.score = score;
    }

    public String getOccurUser() {
        return occurUser;
    }
    public void setOccurUser(String occurUser) {
        this.occurUser = occurUser;
    }

    public Date getOccurTM() {
        return occurTM;
    }
    public void setOccurTM(Date occurTM) {
        this.occurTM = occurTM;
    }

    public String getVcode() {
        return vcode;
    }
    public void setVcode(String vcode) {
        this.vcode = vcode;
    }
}