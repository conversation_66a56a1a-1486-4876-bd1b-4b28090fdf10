package com.huitu.cloud.api.ewci.consult.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description: 实时水情-水情统计-流域-水库实体
 * @author: jiangjy
 * @create: 2024-4-17
 **/
@ApiModel(value = "实时水情-水情统计-流域-水库实体")
public class ConsultWaterBasRsvrVo {

    @ApiModelProperty(value = "中小河流的流域名称")
    private String basName;

    @ApiModelProperty(value = "中小河流的流域编码")
    private String basCode;

    @ApiModelProperty(value = "正常水位站数量")
    private Integer rz;

    @ApiModelProperty(value = "超讯限水位站数量")
    private Integer rzfsltdz;

    @ApiModelProperty(value = "超设计水位站数量")
    private Integer rzDsflz;

    public String getBasName() {
        return basName;
    }

    public void setBasName(String basName) {
        this.basName = basName;
    }

    public String getBasCode() {
        return basCode;
    }

    public void setBasCode(String basCode) {
        this.basCode = basCode;
    }

    public Integer getRz() {
        return rz;
    }

    public void setRz(Integer rz) {
        this.rz = rz;
    }

    public Integer getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(Integer rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public Integer getRzDsflz() {
        return rzDsflz;
    }

    public void setRzDsflz(Integer rzDsflz) {
        this.rzDsflz = rzDsflz;
    }
}
