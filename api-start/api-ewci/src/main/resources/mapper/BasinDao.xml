<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.basin.mapper.BasinDao">
    <!--    <cache type="com.huitu.cloud.config.RedisCache"/>-->

    <select id="getBasinList" resultType="com.huitu.cloud.api.ewci.basin.entity.BasinListVo">
        select
        a.BAS_CODE, a.BAS_NAME,a.BAS_SHORT_NAME, a.PREV_BAS_NAME, a.GWYSB, a.PBAS_CODE, a.VLAR, a.SYFLAG, a.IMPFLAG, a.URL, h.BAS_NAME
        bbas_name,
        STUFF((SELECT ',' + c.adnm FROM BSN_SBF_AD_B b, BSN_ADCD_B c WHERE a.BAS_CODE = b.BAS_CODE and b.ADCD = c.adcd FOR XML PATH('')) ,
        1, 1, '') AS adnms
        from BSN_SMALL_BAS_FZD_B a
        left join BSN_SBF_AD_B b on a.BAS_CODE = b.BAS_CODE
        left join BSN_ADCD_B c on c.ADCD = b.ADCD
        left join BSN_BAS_B h on a.GWYSB = h.BAS_CODE
        <where>
            <if test="map.adcd != null and map.adcd != ''">
                AND (LEFT(c.adcd, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
                <if test="map.level == '4'.toString()">
                    AND LEFT(c.adcd, 6) NOT IN ('220581')
                </if>)
            </if>
            <if test="map.types != null and map.types != ''">
                <if test="map.types == '1'.toString()">
                    and a.syflag = '1'
                </if>
                <if test="map.types == '2'.toString()">
                    and a.impflag = '1'
                </if>
                <if test="map.types == '3'.toString()">
                    and a.impflag = '0'
                </if>
            </if>
        </where>
        group by a.BAS_CODE, a.BAS_NAME,a.BAS_SHORT_NAME, a.PREV_BAS_NAME, a.GWYSB, a.PBAS_CODE, a.VLAR, a.SYFLAG, a.IMPFLAG, a.URL,
        h.BAS_NAME
        order by a.BAS_NAME
    </select>
    <select id="getBasinPrevadList" resultType="com.huitu.cloud.api.ewci.basin.entity.BasinPrevadVo">
        select e.ADCD, e.PTCOUNT, e.LDAREA, e.PLAREA, e.ETCOUNT, e.ECOUNT1, e.ECOUNT2, e.ECOUNT3, e.ECOUNT4, e.HTCOUNT, e.HCOUNT1,
        e.HCOUNT2, e.HCOUNT3, e.HCOUNT4, e.SIGNER, e.AUDID, e.STATUS, e.COMMENTS, e.MODITIME
        from BSN_SBF_PREVAD_B d
        inner join IA_C_PREVAD e on d.FCD = e.ADCD
        <where>
            <if test="basCode != null and basCode != ''">
                and d.BAS_CODE = #{basCode}
            </if>
        </where>
    </select>
    <select id="getBasinWawaList" resultType="com.huitu.cloud.api.ewci.basin.entity.BasinWawaVo">
        select g.WSCD, g.WSNM, g.NADDRESS, g.NLGTD, g.NLTTD, g.MODITIME
        from BSN_SBF_WATA_B f
        inner join IA_C_WATA g on f.WSCD = g.WSCD
        <where>
            <if test="basCode != null and basCode != ''">
                and f.BAS_CODE = #{basCode}
            </if>
        </where>
    </select>
    <select id="getBasCodeList" resultType="java.lang.String">
        WITH SUBQRY(BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE) AS (
        　　SELECT BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE FROM BSN_BAS_B
        <if test="bscd != null and bscd !=''">
            WHERE BAS_CODE = #{bscd}
        </if>
        <if test="bscd == null or bscd ==''">
            WHERE PBAS_CODE = ''
        </if>
        　　UNION ALL
        　　SELECT BSN_BAS_B.BAS_CODE,BSN_BAS_B.BAS_NAME,BSN_BAS_B.BAS_LEVEL,BSN_BAS_B.PBAS_CODE FROM BSN_BAS_B,SUBQRY
        WHERE BSN_BAS_B.PBAS_CODE = SUBQRY.BAS_CODE
        )
        SELECT DISTINCT B.FZDY_CODE FROM SUBQRY A INNER JOIN BSN_BAS_FZDY B
        ON A.BAS_CODE = B.BAS_CODE
    </select>
    <select id="getPrevadSummary" resultType="com.huitu.cloud.api.ewci.basin.entity.PrevadSummaryVo">
        SELECT COUNT(ADCD)  TCOUNT,
               SUM(PTCOUNT) PTCOUNT,
               SUM(LDAREA)  LDAREA,
               SUM(PLAREA)  PLAREA,
               SUM(ETCOUNT) ETCOUNT,
               SUM(ECOUNT1) ECOUNT1,
               SUM(ECOUNT2) ECOUNT2,
               SUM(ECOUNT3) ECOUNT3,
               SUM(ECOUNT4) ECOUNT4,
               SUM(HTCOUNT) HTCOUNT,
               SUM(HCOUNT1) HCOUNT1,
               SUM(HCOUNT2) HCOUNT2,
               SUM(HCOUNT3) HCOUNT3,
               SUM(HCOUNT4) HCOUNT4
        FROM IA_C_PREVAD A
                 LEFT JOIN BSN_SBF_PREVAD_B B ON B.FCD = A.ADCD
        WHERE BAS_CODE = #{basCode}
    </select>
    <select id="getPrevadList" resultType="com.huitu.cloud.api.ewci.basin.entity.PrevadInfoVo">
        SELECT A.ADCD,
               C.ADNM,
               C.PREVTP,
               PTCOUNT,
               A.LDAREA,
               A.PLAREA,
               ETCOUNT,
               ECOUNT1,
               ECOUNT2,
               ECOUNT3,
               ECOUNT4,
               A.HTCOUNT,
               HCOUNT1,
               HCOUNT2,
               HCOUNT3,
               HCOUNT4
        FROM IA_C_PREVAD A
                 LEFT JOIN BSN_SBF_PREVAD_B B ON B.FCD = A.ADCD
                 LEFT JOIN IA_C_ADINFO C ON C.ADCD = A.ADCD
        WHERE BAS_CODE = #{basCode}
        ORDER BY PREVTP DESC, ADCD ASC
    </select>
    <select id="getRainfallList" resultType="com.huitu.cloud.api.ewci.basin.entity.RainfallDetailVo">
        SELECT A.STCD,
               B.STNM,
               B.STTP,
               D.STADTP,
               E.STADTPNM,
               F.ADCD,
               F.ADNM,
               RR.DRPS,
               RF2HTY.DRPS DRPS_F3H,
               RF2HCY.DRPS DRPS_F24H,
               RB24H.DRPS  DRPS_B24H
        FROM BSN_SMALL_BAS_ST A
                 LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
                 LEFT JOIN ST_STSMTASK_B C ON C.STCD = A.STCD
                 LEFT JOIN BSN_STADTP_B D ON D.STCD = A.STCD
                 LEFT JOIN BSN_ADTPINFO_B E ON E.STADTP = D.STADTP
                 LEFT JOIN MDT_ADCDINFO_B F ON F.ADCD = LEFT(D.ADCD, 6) + '000000000'
                 LEFT JOIN (SELECT STCD, SUM(DRP) DRPS
                            FROM ST_PPTN_R
                            WHERE INTV = 1
                              AND TM > #{map.stm}
                              AND TM &lt;= #{map.etm}
                            GROUP BY STCD) RR ON RR.STCD = A.STCD
                 LEFT JOIN (SELECT STCD, SUM(DRP) DRPS
                            FROM ST_PPTN_QX_3H6M
                            WHERE TM > GETDATE()
                              AND TM &lt;= DATEADD(HH, 3, GETDATE())
                            GROUP BY STCD) RF2HTY ON RF2HTY.STCD = A.STCD
                 LEFT JOIN (SELECT STCD, SUM(DRP) DRPS
                            FROM ST_PPTN_QX_72H1H
                            WHERE TM > GETDATE()
                              AND TM &lt;= DATEADD(HH, 24, GETDATE())
                            GROUP BY STCD) RF2HCY ON RF2HCY.STCD = A.STCD
                 LEFT JOIN (SELECT STCD, SUM(DRP) DRPS
                            FROM ST_PPTN_R
                            WHERE INTV = 1
                              AND TM > DATEADD(HH, -24, #{map.stm})
                              AND TM &lt;= #{map.stm}
                            GROUP BY STCD) RB24H ON RB24H.STCD = A.STCD
        WHERE A.BAS_CODE = #{map.basCode}
          AND C.PFL = '1'
        ORDER BY RR.DRPS DESC, D.ADCD, A.STCD ASC
    </select>
</mapper>