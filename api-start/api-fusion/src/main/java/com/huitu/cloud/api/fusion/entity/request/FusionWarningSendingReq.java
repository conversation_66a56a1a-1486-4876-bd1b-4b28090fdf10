package com.huitu.cloud.api.fusion.entity.request;

import com.huitu.cloud.api.fusion.entity.response.FusionWarningSendingResp;
import com.huitu.cloud.entity.EntityRequest;
import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.validation.constraints.Option;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 山洪雨量升级预警-预警发送情况请求对象
 */
@ApiModel(value = "山洪雨量升级预警-预警发送情况请求对象")
@Data
public class FusionWarningSendingReq extends PageBean<FusionWarningSendingResp> implements EntityRequest, Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @NotBlank(message = "参数[预警ID]不能为空")
    private String warnId;

    @ApiModelProperty(value = "短信发送状态，注：0=等待发送、1=发送成功、2=发送失败、9=正在发送")
    @Option(value = {"0", "1", "2", "9"}, message = "参数[短信发送状态]的值无效")
    private List<String> smsSendStatusList;

    @ApiModelProperty(value = "叫应反馈状态，注：0=未确认、1=已确认")
    @Option(value = {"0", "1"}, message = "参数[叫应反馈状态]的值无效")
    private List<String> feedbackStatusList;

    @Override
    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("warnId", getWarnId());
        if (!CollectionUtils.isEmpty(getSmsSendStatusList())) {
            params.put("smsSendStatusList", String.join(",", getSmsSendStatusList()));
        }
        if (!CollectionUtils.isEmpty(getFeedbackStatusList())) {
            params.put("feedbackStatusList", String.join(",", getFeedbackStatusList()));
        }
        return params;
    }
}
