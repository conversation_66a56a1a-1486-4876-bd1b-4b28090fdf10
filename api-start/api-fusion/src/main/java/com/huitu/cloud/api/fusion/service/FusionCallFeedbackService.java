package com.huitu.cloud.api.fusion.service;

import com.huitu.cloud.api.fusion.entity.request.FusionCallStatisticsRequest;
import com.huitu.cloud.api.fusion.entity.request.FusionManualConfirmRequest;
import com.huitu.cloud.api.fusion.entity.request.FusionNoticeReceiverRequest;
import com.huitu.cloud.api.fusion.entity.response.FusionCallStatisticsResponse;
import com.huitu.cloud.api.fusion.entity.response.FusionNoticeReceiverResponse;

import java.util.List;

/**
 * 叫应反馈服务
 */
public interface FusionCallFeedbackService {

    /**
     * 获取等待人工通知的预警接收人列表
     *
     * @param platformId 平台标识
     * @return 接收人列表
     */
    List<FusionNoticeReceiverResponse> getNotNoticeReceiverList(String platformId);

    /**
     * 获取已人工通知的预警接收人列表
     *
     * @param request 请求参数
     * @return 接收人列表
     */
    List<FusionNoticeReceiverResponse> getNotifiedReceiverList(FusionNoticeReceiverRequest request);

    /**
     * 预警人工确认
     *
     * @param request 请求参数
     * @return 受影响的行数
     */
    int manualConfirm(FusionManualConfirmRequest request);

    /**
     * 预警叫应失败统计列表
     *
     * @param request 请求参数
     * @return 统计列表
     */
    List<FusionCallStatisticsResponse> getCallFailStatsList(FusionCallStatisticsRequest request);
}
