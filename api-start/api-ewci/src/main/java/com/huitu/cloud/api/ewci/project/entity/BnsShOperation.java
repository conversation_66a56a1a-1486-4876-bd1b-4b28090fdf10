package com.huitu.cloud.api.ewci.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <p>
 * 运行效益情况统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@TableName("BNS_SH_OPERATION")
@ApiModel(value="BnsShOperation对象", description="山洪灾害运行效益情况表")
public class BnsShOperation  extends Model<BnsShOperation> {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "行政区划名称")
    @TableField("ADCD")
    private String adnm;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "代码")
    @TableField("ADCDS")
    private String adcds;

    @ApiModelProperty(value = "年度")
    @TableField("YEAR")
    private String year;

    @ApiModelProperty(value = "批次编码，生成规则：yyyyMMdd，默认：00000000")
    @TableField("BNO")
    private String bno;

    @ApiModelProperty(value = "发布过预警的县数，单位：个")
    @TableField("XCOUNT")
    private int xcount;

    @ApiModelProperty(value = "县级发布预警次数，单位：次")
    @TableField("WCOUNT")
    private int wcount;

    @ApiModelProperty(value = "县级发布预警短信条数，单位：万条")
    @TableField("SCOUNT")
    private Double scount;

    @ApiModelProperty(value = "县级发布预警涉及相关防汛责任人数量，单位：人")
    @TableField("DPCOUNT")
    private int dpcount;

    @ApiModelProperty(value = "启动预警广播站次，单位：次")
    @TableField("RSTCOUNT")
    private int rstcount;

    @ApiModelProperty(value = "转移人数，单位：人")
    @TableField("TPCOUNT")
    private int tpcount;

    @ApiModelProperty(value = "避免伤亡人数，单位：人")
    @TableField("ACPCOUNT")
    private int acpcount;

    @ApiModelProperty(value = "倒塌房屋，单位：间")
    @TableField("CHCOUNT")
    private int chcount;

    @ApiModelProperty(value = "重要设施毁坏，单位：处")
    @TableField("IFDCOUNT")
    private int ifdcount;

    @ApiModelProperty(value = "气象预警服务发布期数，单位：期")
    @TableField("RCOUNT")
    private int rcount;

    @ApiModelProperty(value = "气象预警服务向责任人发送预警短信，单位：条")
    @TableField("MDPSCOUNT")
    private int mdpscount;

    @ApiModelProperty(value = "气象预警服务向社会公众发送预警短信，单位：条")
    @TableField("MPPSCOUNT")
    private int mppscount;

    @ApiModelProperty(value = "通过“三大运营商”向责任人发送预警短信，单位：条")
    @TableField("CDPSCOUNT")
    private int cdpscount;

    @ApiModelProperty(value = "通过“三大运营商”向社会公众发送预警短信，单位：条")
    @TableField("CPPSCOUNT")
    private int cppscount;

    @ApiModelProperty(value = "通过其它方式向责任人发送预警短信，单位：条")
    @TableField("ODPSCOUNT")
    private int odpscount;

    @ApiModelProperty(value = "通过其它方式向社会公众发送预警短信，单位：条")
    @TableField("OPPSCOUNT")
    private int oppscount;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED")
    private Date created;

    @ApiModelProperty(value = "统计时间")
    @TableField("COMPLETED")
    private Date completed;

    @ApiModelProperty(value = "时间戳")
    @TableField("TS")
    private Date ts;

    public String getAdcds() {
        return adcds;
    }

    public void setAdcds(String adcds) {
        this.adcds = adcds;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBno() {
        return bno;
    }

    public void setBno(String bno) {
        this.bno = bno;
    }

    public int getXcount() {
        return xcount;
    }

    public void setXcount(int xcount) {
        this.xcount = xcount;
    }

    public int getWcount() {
        return wcount;
    }

    public void setWcount(int wcount) {
        this.wcount = wcount;
    }

    public Double getScount() {
        return scount;
    }

    public void setScount(Double scount) {
        this.scount = scount;
    }

    public int getDpcount() {
        return dpcount;
    }

    public void setDpcount(int dpcount) {
        this.dpcount = dpcount;
    }

    public int getRstcount() {
        return rstcount;
    }

    public void setRstcount(int rstcount) {
        this.rstcount = rstcount;
    }

    public int getTpcount() {
        return tpcount;
    }

    public void setTpcount(int tpcount) {
        this.tpcount = tpcount;
    }

    public int getAcpcount() {
        return acpcount;
    }

    public void setAcpcount(int acpcount) {
        this.acpcount = acpcount;
    }

    public int getChcount() {
        return chcount;
    }

    public void setChcount(int chcount) {
        this.chcount = chcount;
    }

    public int getIfdcount() {
        return ifdcount;
    }

    public void setIfdcount(int ifdcount) {
        this.ifdcount = ifdcount;
    }

    public int getRcount() {
        return rcount;
    }

    public void setRcount(int rcount) {
        this.rcount = rcount;
    }

    public int getMdpscount() {
        return mdpscount;
    }

    public void setMdpscount(int mdpscount) {
        this.mdpscount = mdpscount;
    }

    public int getMppscount() {
        return mppscount;
    }

    public void setMppscount(int mppscount) {
        this.mppscount = mppscount;
    }

    public int getCdpscount() {
        return cdpscount;
    }

    public void setCdpscount(int cdpscount) {
        this.cdpscount = cdpscount;
    }

    public int getCppscount() {
        return cppscount;
    }

    public void setCppscount(int cppscount) {
        this.cppscount = cppscount;
    }

    public int getOdpscount() {
        return odpscount;
    }

    public void setOdpscount(int odpscount) {
        this.odpscount = odpscount;
    }

    public int getOppscount() {
        return oppscount;
    }

    public void setOppscount(int oppscount) {
        this.oppscount = oppscount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getCompleted() {
        return completed;
    }

    public void setCompleted(Date completed) {
        this.completed = completed;
    }

    public Date getTs() {
        return ts;
    }

    public void setTs(Date ts) {
        this.ts = ts;
    }

    @Override
    public String toString() {
        return "BnsShOperation{" +
                "adnm='" + adnm + '\'' +
                ", adcd='" + adcd + '\'' +
                ", bno='" + bno + '\'' +
                ", xcount=" + xcount +
                ", wcount=" + wcount +
                ", scount=" + scount +
                ", dpcount=" + dpcount +
                ", rstcount=" + rstcount +
                ", tpcount=" + tpcount +
                ", acpcount=" + acpcount +
                ", chcount=" + chcount +
                ", ifdcount=" + ifdcount +
                ", rcount=" + rcount +
                ", mdpscount=" + mdpscount +
                ", mppscount=" + mppscount +
                ", cdpscount=" + cdpscount +
                ", cppscount=" + cppscount +
                ", odpscount=" + odpscount +
                ", oppscount=" + oppscount +
                ", remark='" + remark + '\'' +
                ", created=" + created +
                ", completed=" + completed +
                ", ts=" + ts +
                '}';
    }
}
