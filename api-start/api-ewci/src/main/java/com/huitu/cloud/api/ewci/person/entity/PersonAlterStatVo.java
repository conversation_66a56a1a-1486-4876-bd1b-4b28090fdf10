package com.huitu.cloud.api.ewci.person.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 各类责任人信息
 *
 * <AUTHOR>
 */
@ApiModel(value = "各类责任人信息")
public class PersonAlterStatVo implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "政区编码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "上级编码")
    @TableField(value = "PADCD")
    private String padcd;

    @ApiModelProperty(value = "政区名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "水库责任人-今年是否更新")
    @TableField(value = "SKWHETHER")
    private String skWhether;

    @ApiModelProperty(value = "水库责任人-最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "SKTM")
    private LocalDateTime skTm;

    @ApiModelProperty(value = "山洪责任人-今年是否更新")
    @TableField(value = "SHWHETHER")
    private String shWhether;

    @ApiModelProperty(value = "山洪责任人-最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "SHTM")
    private LocalDateTime shTm;

    @ApiModelProperty(value = "江河责任人-今年是否更新")
    @TableField(value = "JHWHETHER")
    private String jhWhether;

    @ApiModelProperty(value = "江河责任人-最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "JHTM")
    private LocalDateTime jhTm;

    @ApiModelProperty(value = "堤防责任人-今年是否更新")
    @TableField(value = "DFWHETHER")
    private String dfWhether;

    @ApiModelProperty(value = "堤防责任人-最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "DFTM")
    private LocalDateTime dfTm;

    @ApiModelProperty(value = "险工险段责任人-今年是否更新")
    @TableField(value = "XGXDWHETHER")
    private String xgxdWhether;

    @ApiModelProperty(value = "险工险段责任人-最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "XGXDTM")
    private LocalDateTime xgxdTm;

    @ApiModelProperty(value = "蓄滞洪区责任人-今年是否更新")
    @TableField(value = "XZHQWHETHER")
    private String xzhqWhether;

    @ApiModelProperty(value = "蓄滞洪区责任人-最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "XZHQTM")
    private LocalDateTime xzhqTm;

    @ApiModelProperty(value = "水库淹没范围责任人-今年是否更新")
    @TableField(value = "YMFWWHETHER")
    private String ymfwWhether;

    @ApiModelProperty(value = "水库淹没范围责任人-最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "YMFWTM")
    private LocalDateTime ymfwTm;

    @ApiModelProperty(value = "中小河流责任人-今年是否更新")
    @TableField(value = "ZXHLWHETHER")
    private String zxhlWhether;

    @ApiModelProperty(value = "中小河流责任人-最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "ZXHLTM")
    private LocalDateTime zxhlTm;

    private List<PersonAlterStatVo> children;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getSkWhether() {
        return skWhether;
    }

    public void setSkWhether(String skWhether) {
        this.skWhether = skWhether;
    }

    public LocalDateTime getSkTm() {
        return skTm;
    }

    public void setSkTm(LocalDateTime skTm) {
        this.skTm = skTm;
    }

    public String getShWhether() {
        return shWhether;
    }

    public void setShWhether(String shWhether) {
        this.shWhether = shWhether;
    }

    public LocalDateTime getShTm() {
        return shTm;
    }

    public void setShTm(LocalDateTime shTm) {
        this.shTm = shTm;
    }

    public String getJhWhether() {
        return jhWhether;
    }

    public void setJhWhether(String jhWhether) {
        this.jhWhether = jhWhether;
    }

    public LocalDateTime getJhTm() {
        return jhTm;
    }

    public void setJhTm(LocalDateTime jhTm) {
        this.jhTm = jhTm;
    }

    public String getDfWhether() {
        return dfWhether;
    }

    public void setDfWhether(String dfWhether) {
        this.dfWhether = dfWhether;
    }

    public LocalDateTime getDfTm() {
        return dfTm;
    }

    public void setDfTm(LocalDateTime dfTm) {
        this.dfTm = dfTm;
    }

    public String getXgxdWhether() {
        return xgxdWhether;
    }

    public void setXgxdWhether(String xgxdWhether) {
        this.xgxdWhether = xgxdWhether;
    }

    public LocalDateTime getXgxdTm() {
        return xgxdTm;
    }

    public void setXgxdTm(LocalDateTime xgxdTm) {
        this.xgxdTm = xgxdTm;
    }

    public String getXzhqWhether() {
        return xzhqWhether;
    }

    public void setXzhqWhether(String xzhqWhether) {
        this.xzhqWhether = xzhqWhether;
    }

    public LocalDateTime getXzhqTm() {
        return xzhqTm;
    }

    public void setXzhqTm(LocalDateTime xzhqTm) {
        this.xzhqTm = xzhqTm;
    }

    public String getYmfwWhether() {
        return ymfwWhether;
    }

    public void setYmfwWhether(String ymfwWhether) {
        this.ymfwWhether = ymfwWhether;
    }

    public LocalDateTime getYmfwTm() {
        return ymfwTm;
    }

    public void setYmfwTm(LocalDateTime ymfwTm) {
        this.ymfwTm = ymfwTm;
    }

    public List<PersonAlterStatVo> getChildren() {
        return children;
    }

    public void setChildren(List<PersonAlterStatVo> children) {
        this.children = children;
    }

    public String getZxhlWhether() {
        return zxhlWhether;
    }

    public void setZxhlWhether(String zxhlWhether) {
        this.zxhlWhether = zxhlWhether;
    }

    public LocalDateTime getZxhlTm() {
        return zxhlTm;
    }

    public void setZxhlTm(LocalDateTime zxhlTm) {
        this.zxhlTm = zxhlTm;
    }
}
