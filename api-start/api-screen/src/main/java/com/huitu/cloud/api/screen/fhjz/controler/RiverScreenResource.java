package com.huitu.cloud.api.screen.fhjz.controler;


import com.huitu.api.SuccessResponse;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.screen.fhjz.entity.DikeVo;
import com.huitu.cloud.api.screen.fhjz.entity.RiverDetailVo;
import com.huitu.cloud.api.screen.fhjz.entity.RiverVo;
import com.huitu.cloud.api.screen.fhjz.service.DikeScreenService;
import com.huitu.cloud.api.screen.fhjz.service.RiverScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 河流 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-16
 */

@RestController
@Api(tags = "一体机大屏（防洪减灾-河流工程） ")
@RequestMapping("/api/screen/river")
public class RiverScreenResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "F661C064-1554-4164-80CA-B52A17E42148";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private RiverScreenService baseService;

    @ApiOperation(value = "河流列表查询",notes="河流列表查询")
    @GetMapping(value = "river-list")
    public ResponseEntity<SuccessResponse<List>> riverList(@RequestParam("rvName") String rvName){
        List<RiverVo> result= baseService.queryRiverList(rvName);
            return ResponseEntity.ok(
                new SuccessResponse(result));
     }

    @ApiOperation(value = "河流防洪工程详细列表查询",notes="河流防洪工程详细列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvName", value = "河流名称", required = false, dataType = "string"),
    })
    @GetMapping(value = "river-detail-list")
    public ResponseEntity<SuccessResponse<List>> riverDetailList(@RequestParam("rvName") String rvName){
        List<RiverDetailVo> result= baseService.riverDetailList(rvName);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }


    @ApiOperation(value = "河道站列表查询",notes="河道站列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码", required = false, dataType = "String"),
    })
    @GetMapping(value = "river-station-list")
    public ResponseEntity<SuccessResponse<List>> riverStationList(@RequestParam("rvCode") String rvCode,@RequestParam("stm") String stm,@RequestParam("etm") String etm){
        List<Map> result= baseService.riverStationList(rvCode,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "查询河道站信息",notes="查询河道站信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
    })
    @GetMapping(value = "queryRiverStationInfo")
    public ResponseEntity<SuccessResponse<Map<String,Object>>> queryRiverStationInfo(@RequestParam("stcd") String stcd){
        Map<String,Object> result= baseService.queryRiverStationInfo(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "查询河流信息",notes="查询河流信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码", required = true, dataType = "String"),
    })
    @GetMapping(value = "queryRiverInfo")
    public ResponseEntity<SuccessResponse<Map<String,Object>>> queryRiverInfo(@RequestParam("rvCode") String rvCode){
        Map<String,Object> result= baseService.queryRiverInfo(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "查询工程数量统计",notes="查询工程数量统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
    })
    @GetMapping(value = "queryProjectTotal")
    public ResponseEntity<SuccessResponse<String>> queryProjectTotal(@RequestParam("adcd") String adcd){
        String result= baseService.queryProjectTotal(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "统计河流数量详情",notes="统计河流数量详情")
    @GetMapping(value = "statisticRiverDetail")
    public ResponseEntity<SuccessResponse<String>> statisticRiverDetail(){
        String result= baseService.statisticRiverDetail();
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

}







