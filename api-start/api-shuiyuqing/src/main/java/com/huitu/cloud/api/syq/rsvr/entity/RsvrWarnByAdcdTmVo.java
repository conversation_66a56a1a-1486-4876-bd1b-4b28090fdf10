package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.api.syq.rsvr.entity.RsvrStWarnByAdcdTmVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "RsvrWarnByAdcdTmVo",description = "水库告警时间段统计类")
public class RsvrWarnByAdcdTmVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "测站编码")
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "最大动态水位")
    private BigDecimal maxRz;
    @ApiModelProperty(value = "最小动态水位")
    private BigDecimal minRz;
    @ApiModelProperty(value = "汛限水位")
    private BigDecimal fsltdz;
    @ApiModelProperty(value = "超汛限集合")
    private List<RsvrStWarnByAdcdTmVo> warnWrzList;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public BigDecimal getMaxRz() {
        return maxRz;
    }

    public void setMaxRz(BigDecimal maxRz) {
        this.maxRz = maxRz;
    }

    public BigDecimal getMinRz() {
        return minRz;
    }

    public void setMinRz(BigDecimal minRz) {
        this.minRz = minRz;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public List<RsvrStWarnByAdcdTmVo> getWarnWrzList() {
        return warnWrzList;
    }

    public void setWarnWrzList(List<RsvrStWarnByAdcdTmVo> warnWrzList) {
        this.warnWrzList = warnWrzList;
    }
}
