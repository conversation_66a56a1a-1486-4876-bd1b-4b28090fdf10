package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 涉水工程统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-15
 */
@TableName("DT_WADING_S")
@ApiModel(value="DtWadingS对象", description="涉水工程统计表")
public class DtWading extends DtExtendInfo {

    @ApiModelProperty(value = "政区编码 ")
    private String adcd;

    @ApiModelProperty(value = "水库个数 ")
    @TableField("RSST")
    private Double rsst;

    @ApiModelProperty(value = "水闸个数 ")
    @TableField("SLUST")
    private Double slust;

    @ApiModelProperty(value = "堤防个数 ")
    @TableField("DIKST")
    private Double dikst;

    @ApiModelProperty(value = "塘坝个数")
    @TableField("DAMST")
    private Double damst;

    @ApiModelProperty(value = "路涵个数")
    @TableField("CULST")
    private Double culst;

    @ApiModelProperty(value = "桥梁个数 ")
    @TableField("BRIST")
    private Double brist;


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getRsst() {
        return rsst;
    }

    public void setRsst(Double rsst) {
        this.rsst = rsst;
    }

    public Double getSlust() {
        return slust;
    }

    public void setSlust(Double slust) {
        this.slust = slust;
    }

    public Double getDikst() {
        return dikst;
    }

    public void setDikst(Double dikst) {
        this.dikst = dikst;
    }

    public Double getDamst() {
        return damst;
    }

    public void setDamst(Double damst) {
        this.damst = damst;
    }

    public Double getCulst() {
        return culst;
    }

    public void setCulst(Double culst) {
        this.culst = culst;
    }

    public Double getBrist() {
        return brist;
    }

    public void setBrist(Double brist) {
        this.brist = brist;
    }

}
