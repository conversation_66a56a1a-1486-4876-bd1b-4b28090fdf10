package com.huitu.cloud.api.ewci.hydrology.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.exception.HyWarnException;
import com.huitu.cloud.api.ewci.hydrology.entity.query.HyWarnIndexQuery;
import com.huitu.cloud.api.ewci.hydrology.entity.query.HyWarnRecordQuery;
import com.huitu.cloud.api.ewci.hydrology.entity.request.HyWarnFileRequest;
import com.huitu.cloud.api.ewci.hydrology.entity.request.HyWarnIndexRequest;
import com.huitu.cloud.api.ewci.hydrology.entity.response.*;
import com.huitu.cloud.api.ewci.hydrology.service.HyWarnIndexService;
import com.huitu.cloud.api.ewci.hydrology.service.HyWarnReleaseService;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import com.huitu.cloud.api.usif.util.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 洪水预警发布服务
 *
 * <AUTHOR>
 */
@Api(tags = "洪水预警发布服务")
@RestController
@RequestMapping("/api/ewci/hydrology/warn")
public class HyWarnResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "cfcbb92f-0cc6-4682-bbe3-e5fdb43324bf";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final LoginUtils loginUtils;
    private HyWarnReleaseService releaseService;
    private HyWarnIndexService indexService;

    public HyWarnResource(LoginUtils loginUtils, HyWarnReleaseService releaseService, HyWarnIndexService indexService) {
        this.loginUtils = loginUtils;
        this.releaseService = releaseService;
        this.indexService = indexService;
    }

    @ApiOperation(value = "分页获取预警记录列表", notes = "作者：曹宝金")
    @PostMapping("page")
    public ResponseEntity<SuccessResponse<IPage<HyWarnRecordResponse>>> getPageList(@Validated @RequestBody HyWarnRecordQuery query) {
        try {
            UserInfos user = loginUtils.getCurrentLoginUser();
            query.setSendDept(user.getDeptid());
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", releaseService.getPageList(query)));
        } catch (Exception ex) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "Error", new Page<>()));
        }
    }

    @ApiOperation(value = "获取预警记录信息", notes = "作者：曹宝金")
    @GetMapping("record-info")
    public ResponseEntity<SuccessResponse<HyWarnRecordResponse>> getRecordInfo(String warnId) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", releaseService.getRecordInfo(warnId)));
    }

    @ApiOperation(value = "获取预警文件信息", notes = "作者：曹宝金")
    @GetMapping("file-info")
    public ResponseEntity<SuccessResponse<HyWarnFileResponse>> getFileInfo(String warnId) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", releaseService.getFileInfo(warnId)));
    }

    @ApiOperation(value = "获取最新的发文期数", notes = "作者：曹宝金")
    @GetMapping("new-file-no")
    public ResponseEntity<SuccessResponse<Integer>> getNewFileNo(String yr) {
        if (StringUtils.isBlank(yr)) {
            yr = DateFormatUtils.format(new Date(), "yyyy");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", releaseService.getNewFileNo(yr)));
    }

    @ApiOperation(value = "获取预警人员列表", notes = "作者：曹宝金")
    @GetMapping("user-list")
    public ResponseEntity<SuccessResponse<List<HyWarnUserResponse>>> getUserList() {
        try {
            UserInfos user = loginUtils.getCurrentLoginUser();
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", releaseService.getUserList(user.getDeptid())));
        } catch (Exception ex) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "Error", new ArrayList<>()));
        }
    }

    @ApiOperation(value = "获取洪水预警文档", notes = "作者：曹宝金")
    @GetMapping("document")
    public ResponseEntity<SuccessResponse<HyWarnDocumentResponse>> getDocument(String warnId) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", releaseService.getDocument(warnId)));
    }

    @ApiOperation(value = "保存洪水预警文件", notes = "作者：曹宝金")
    @PostMapping("save-file")
    public ResponseEntity<SuccessResponse<Integer>> saveFile(@Validated @RequestBody HyWarnFileRequest request) {
        try {
            UserInfos user = loginUtils.getCurrentLoginUser();
            request.setSendDept(user.getDeptid());
            try {
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", releaseService.saveFile(request)));
            } catch (HyWarnException ex) {
                throw ex;
            } catch (Exception ex) {
                throw new HyWarnException("保存失败", ex);
            }
        } catch (HyWarnException ex) {
            throw ex;
        } catch (Exception ex) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "Error", -1));
        }
    }

    @ApiOperation(value = "忽略预警", notes = "作者：曹宝金")
    @GetMapping("ignore")
    public ResponseEntity<SuccessResponse<Integer>> ignore(String warnId) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", releaseService.ignore(warnId)));
    }

    @ApiOperation(value = "发布预警", notes = "作者：曹宝金")
    @GetMapping("release")
    public ResponseEntity<SuccessResponse<HyWarnFileResponse>> release(String warnId) {
        try {
            UserInfos user = loginUtils.getCurrentLoginUser();
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", releaseService.release(warnId, user)));
        } catch (Exception ex) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "Error", null));
        }
    }

    @ApiOperation(value = "获取预警站列表", notes = "作者：曹宝金")
    @GetMapping("station-list/{adcd}")
    public ResponseEntity<SuccessResponse<List<HyWarnStationResponse>>> getStationList(@PathVariable("adcd") String adcd, Boolean include) {
        if (StringUtils.isBlank(adcd)) {
            throw new IllegalArgumentException("参数[行政区划代码]不能为空");
        }
        if (adcd.length() != 15) {
            throw new IllegalArgumentException("参数[行政区划代码]的长度应为15个字符");
        }
        if (null == include) {
            include = true;
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", indexService.getStationList(adcd, include)));
    }

    @ApiOperation(value = "获取预警单位列表", notes = "作者：曹宝金")
    @GetMapping("unit-list")
    public ResponseEntity<SuccessResponse<List<HyWarnUnitResponse>>> getUnitList() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", indexService.getUnitList()));
    }

    @ApiOperation(value = "分页查询预警指标列表", notes = "作者：曹宝金")
    @PostMapping("index/page")
    public ResponseEntity<SuccessResponse<IPage<HyWarnIndexResponse>>> getIndexPageList(@Validated @RequestBody HyWarnIndexQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", indexService.getPageList(query)));
    }

    @ApiOperation(value = "保存洪水预警指标", notes = "作者：曹宝金")
    @PostMapping("index/save")
    public ResponseEntity<SuccessResponse<Integer>> saveIndex(@Validated @RequestBody HyWarnIndexRequest request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", indexService.save(request)));
    }

    @ApiOperation(value = "删除洪水预警指标", notes = "作者：曹宝金")
    @GetMapping("index/delete/{stcd}")
    public ResponseEntity<SuccessResponse<Integer>> deleteIndex(@PathVariable("stcd") String stcd) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", indexService.delete(checkArgument(stcd))));
    }

    @ApiOperation(value = "启用洪水预警指标", notes = "作者：曹宝金")
    @GetMapping("index/enable/{stcd}")
    public ResponseEntity<SuccessResponse<Integer>> enableIndex(@PathVariable("stcd") String stcd) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", indexService.updateUseFlag(checkArgument(stcd), "1")));
    }

    @ApiOperation(value = "禁用洪水预警指标", notes = "作者：曹宝金")
    @GetMapping("index/disable/{stcd}")
    public ResponseEntity<SuccessResponse<Integer>> disableIndex(@PathVariable("stcd") String stcd) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", indexService.updateUseFlag(checkArgument(stcd), "0")));
    }

    private String checkArgument(String stcd) {
        if (StringUtils.isBlank(stcd)) {
            throw new IllegalArgumentException("参数[测站编码]不能为空");
        }
        if (stcd.length() != 8) {
            throw new IllegalArgumentException("参数[测站编码]的长度应为8个字符");
        }
        return stcd;
    }
}
