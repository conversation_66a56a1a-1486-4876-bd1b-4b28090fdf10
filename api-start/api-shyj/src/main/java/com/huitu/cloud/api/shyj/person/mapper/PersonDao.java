package com.huitu.cloud.api.shyj.person.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.person.entity.Person;
import com.huitu.cloud.api.shyj.person.entity.RelevantPersionInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人员信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
public interface PersonDao extends BaseMapper<Person> {
    /**
     * 分页查询人员信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<RelevantPersionInfo> getPersionListByPage(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询人员信息
     *
     * @param param
     * @return
     */
    List<RelevantPersionInfo> getPersionList(Map<String, Object> param);
    /**
     * 查询新增id
     *
     * @return
     */
    Person selectMaxId();
    /**
     * 删除人员
     *
     * @return
     * @param personcd
     */
    void delPerson(String personcd);
}
