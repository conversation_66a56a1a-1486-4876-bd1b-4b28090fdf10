package com.huitu.cloud.api.ewci.person.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.service.BasPersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * 中小河流责任人
 *
 * <AUTHOR>
 */
@Api(tags = "中小河流责任人")
@RestController
@RequestMapping("/api/ewci/person/bas")
public class BasPersonResource extends AbstractApiResource implements ApiResource {

    private static final Logger logger = LoggerFactory.getLogger(BasPersonResource.class);

    public BasPersonResource(BasPersonService baseService) {
        this.baseService = baseService;
    }

    @Override
    public String getUuid() {
        return "d4af531f-6330-495e-f34b-33167c33d7f4";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final BasPersonService baseService;

    @ApiOperation(value = "分页获取中小河流责任人列表", notes = "作者：赵英捷")
    @PostMapping("select-page-list")
    public ResponseEntity<SuccessResponse<IPage<BasPersonVo>>> getPageList(@Validated @RequestBody BasPersonQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(query)));
    }


    @ApiOperation(value = "中小河流责任人统计", notes = "作者：赵英捷")
    @PostMapping("select-bas-per-sum-list")
    public ResponseEntity<SuccessResponse<List<BasSummaryVo>>> getBasSummaryList(@RequestBody BasSummaryQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBasSummaryList(query)));
    }

    @ApiOperation(value = "导出中小河流责任人信息", notes = "作者：赵英捷")
    @PostMapping(value = "export")
    public void dataExport(@Validated @RequestBody BasPersonQuery query, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("中小河流责任人信息", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.dataExport(query, response.getOutputStream());
        } catch (Exception ex) {
            logger.error("中小河流责任人信息失败，参数：{}", JSON.toJSONString(query));
            logger.error(ex.getMessage(), ex);
        }
    }

    @ApiOperation(value = "编辑中小河流责任人", notes = "赵英捷")
    @PostMapping(value = "bas-person-edit")
    public ResponseEntity<SuccessResponse<Integer>> basPersonEdit(@RequestBody BasPersonVo entity) throws Exception {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.basPersonEdit(entity)));
    }

    @ApiOperation(value = "根据政区编码查询河流编码", notes = "作者：zyj")
    @GetMapping("select-bas-code-list/{adcd}")
    public ResponseEntity<SuccessResponse<List<String>>> getBasCodeList(@PathVariable("adcd") String adcd) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBasCodeList(adcd)));
    }

    @ApiOperation(value = "未上报", notes = "作者：zyj")
    @GetMapping("select-bas-fail-report-list/{adcd}")
    public ResponseEntity<SuccessResponse<List<BasFailReportVo>>> getBasFailReportList(@PathVariable("adcd") String adcd) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBasFailReportList(adcd)));
    }

    @ApiOperation(value = "导出未上报中小河流责任人信息", notes = "作者：赵英捷")
    @PostMapping(value = "export-bas-fail-report")
    public void exportBasFailReport(@Validated @RequestBody BasPersonQuery query, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("未上报中小河流责任人信息", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.exportBasFailReport(query, response.getOutputStream());
        } catch (Exception ex) {
            logger.error("未上报中小河流责任人信息失败，参数：{}", JSON.toJSONString(query));
            logger.error(ex.getMessage(), ex);
        }
    }
}
