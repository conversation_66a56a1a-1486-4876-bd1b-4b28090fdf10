package com.huitu.cloud.api.shyj.warn.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.warn.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 现地预警记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface LocWarnDao extends BaseMapper<LocWarnVo> {

    /**
     * 分页查询现地预警信息
     *
     * @param queryForm
     * @param page
     * @return
     */
    IPage<LocWarnVo> pageLocWarn(Page page, @Param("form") LocWarnQueryForm queryForm);

    /**
     * 列表查询现地预警信息
     *
     * @param queryForm
     * @return
     */
    List<LocWarnVo> listLocWarnOld(@Param("form") LocWarnQueryForm queryForm);

    /**
     * 列表查询现地预警信息
     *
     * @param queryForm
     * @return
     */
    List<StwarnrecordXdRVo> listLocWarn(@Param("form") LocWarnQueryForm queryForm);

    /**
     * 列表查询现地预警设备信息
     *
     * @param queryForm
     * @return
     */
    List<LocWarnDeviceVo> listLocWarnDevice(@Param("form") LocWarnQueryForm queryForm);

    /**
     * 查询现地预警详情信息
     *
     * @param warnid 预警id
     * @return
     */
    LocWarnVo getLocWarnById(@Param("warnid") String warnid);

    /**
     * 查询现地预警详情信息
     *
     * @param
     * @return
     */
    List<StwarnrecordXdRVo> LocWarnDetailData(@Param("stcd") String stcd, @Param("stm") String stm, @Param("etm") String etm);

    /**
     * 查询现地预警状态列表
     *
     * @param
     * @return
     */
    List<WarnRwmStatusR> LocWarnDetailStatusList(@Param("stcd") String stcd, @Param("idList") List<String> idList);
}
