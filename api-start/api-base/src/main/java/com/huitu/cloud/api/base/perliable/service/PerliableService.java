package com.huitu.cloud.api.base.perliable.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.base.perliable.entity.*;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

/**
 * <p>
 * 责任人预警设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-3-13
 */
public interface PerliableService extends IService<BsnReswcB> {
    /**
     * 添加水库配置信息
     *
     * @param entity 角色实体类
     * @return OK 成功
     */
    String addRes(BsnReswcB entity);

    /**
     * 提醒时间间隔设置
     *
     * @param confiltm  预警时间间隔
     * @param pushapptm 推送时间间隔
     * @return OK 成功
     */
    String setTime(int confiltm, int pushapptm);

    /**
     * 按水库类型设置短信发送配置
     *
     * @param list 类型配置集合
     * @return OK 成功
     */
    String setSendByType(List<QuerySetByType> list);

    /**
     * 查询水库责任人配置信息
     *
     * @param scal     水库类型
     * @param name     水库名称
     * @param isSend   是否发送
     * @param isPush   是否推送
     * @param adcd     政区编码
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return
     */
    IPage<ResInfo> getRsvrYjByPage(List<String> scal, String name, List<String> isSend, List<String> isPush, String adcd, int pageNum, int pageSize);

    /**
     * 查询水库的相关责任人信息
     *
     * @param adcd     政区编码
     * @param name     水库名称
     * @param pernm    责任人名称
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return
     */
    IPage<Rsvrper> getRsvrPerByPage(String adcd, String name, String pernm, List<String> scals, int pageNum, int pageSize);

    /**
     * 查询水库的相关责任人信息
     *
     * @param adcd     政区编码
     * @param code     水库编码
     * @param name     水库名称
     * @param pernm    责任人名称
     * @param mobile   手机号
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return
     */
    IPage<BnsRsvrpersonBVo> getRsvrPerNewByPage(String adcd, String code, String name, String pernm, List<String> scals, String mobile, String resCodes, String types, String webFlag,String udtm, int pageNum, int pageSize);

    /**
     * 水库防汛责任人汇总统计
     *
     * @param query
     * @return
     */
    List<RsvrpersonSummaryVo> getRsvrpersonSummaryList(RsvrpersonSummaryQuery query);

    /**
     * 根据人员批量添加水库责任人信息
     *
     * @param entity
     * @return
     */
    boolean addRsvrPers(BnsRsvrpersonBQo entity);

    /**
     * 根据人员批量添加水库责任人信息
     *
     * @param entity
     * @return
     */
    boolean addRsvrPer(BnsRsvrpersonB entity);

    /**
     * 更新水库责任人信息
     *
     * @param entity
     * @return
     */
    boolean editRsvrPer(BnsRsvrpersonB entity);

    /**
     * 根据水库编码删除水库责任人
     *
     * @param rescode 水库编码
     * @param adcd    行政代码
     * @return
     */
    boolean delRsvrPer(String rescode, String adcd);

    /**
     * 导出水库责任人信息
     *
     * @param adcd   政区编码
     * @param name   水库名称
     * @param pernm  责任人名称
     * @param mobile 手机号
     * @return
     */
    void exportRsvrPerNew(String adcd, String name, String pernm, List<String> scals,String mobile, String web22ExpFlag,String udtm, HttpServletResponse response);

    /**
     * 导入水库责任人
     *
     * @param inputStream 文件流
     * @param adcd        县级政区编码 15位
     * @return
     */
    RsvrPerImportVo importRsvrPer(InputStream inputStream, String adcd);

    /**
     * 添加或更新水库责任人信息
     *
     * @param entity
     * @return
     */
    boolean editRsvrPerson(RsvrPerEdit entity);

    /**
     * 根据水库编码删除水库责任人
     *
     * @param rsvrcd
     * @return
     */
    boolean delRsvrperson(String rsvrcd);

    /**
     * 根据用户添加水库责任人信息
     *
     * @param entity
     * @return
     */
    boolean addRsvrPersonByUserId(PerEdit entity);

    /**
     * 根据水库编码查询水库责任人信息
     *
     * @param rsvrcd 水库编码
     * @return
     */
    Rsvrper getResPersonByRscd(String rsvrcd);

    /**
     * 导出水库责任人信息
     *
     * @param adcd     政区编码
     * @param name     水库名称
     * @param pernm    责任人名称
     * @param response
     */
    void exportRsvrPer(String adcd, String name, String pernm, List<String> scals, HttpServletResponse response);

    /**
     * 查询水库预警统计
     *
     * @param adcd     政区编码
     * @param resName  水库名称
     * @param scals    水库规模
     * @param stm      开始时间
     * @param etm      结束时间
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return
     */
    IPage<RsvrWarnTj> getRsvrWarnTjByPage(String adcd, String resName, List<String> scals, String stm, String etm, int pageNum, int pageSize);

    /**
     * 查询水库预警记录列表
     *
     * @param adcd     政区编码
     * @param resName  水库名称
     * @param resCode  水库编码
     * @param scals    水库规模
     * @param stm      开始时间
     * @param etm      结束时间
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return
     */
    IPage<RsvrWarnInfo> getRsvrWarnInfoByPage(String adcd, String resName, String resCode, List<String> scals, String stm, String etm, int pageNum, int pageSize);

    /**
     * 查询水库预警发送的短信列表
     *
     * @param resCode  水库编码
     * @param stm      开始时间
     * @param etm      结束时间
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return
     */
    IPage<RsvrWarnMsg> getRsvrWarnSendMsgByPage(String resCode, String stm, String etm, int pageNum, int pageSize);

    /**
     * 查询水库责任人列表信息
     *
     * @param adcd
     * @param resCodes
     * @param types
     * @return
     */
    IPage<BnsRsvrpersonB> getRsvrPerList(String adcd, String resCodes, String types, int pageNum, int pageSize);

    /**
     * 查询当前政区下的水库列表
     *
     * @param adcd
     * @return
     */
    List<ResBaseInfo> getAttResBaseByAdcd(String adcd);

    /**
     * 编辑水库责任人
     * @param entity
     * @return
     */
    int rsvrPersonEdit(BnsRsvrpersonBVo entity);

    /**
     * 查询未上报水库
     * @param adcd
     * @return
     */
    List<RsvrFailReportVo> getRsvrFailReportList(String adcd);

    /**
     * 根据政区查询全部水库
     * @param adcd
     * @return
     */
    List<ResInfoVo> getResInfoList(String adcd);

    /**
     * 未上报水库导出
     *
     * @param adcd  查询条件
     * @param output 输出流
     **/
    void failReportExport(String adcd, OutputStream output);
}
