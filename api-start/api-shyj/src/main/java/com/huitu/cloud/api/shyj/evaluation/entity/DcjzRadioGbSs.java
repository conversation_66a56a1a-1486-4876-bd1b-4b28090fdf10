package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <p>
 * 重点城(集)镇调查评价设备信息-无线预警广播
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@ApiModel(value="DcjzRadioGbSs对象", description="重点城(集)镇调查评价设备信息-无线预警广播")
public class DcjzRadioGbSs extends Model<DcjzRadioGbSs> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "站点位置")
    @TableId(value = "ADDRESS", type = IdType.NONE)
    private String address;

    @ApiModelProperty(value = "经度")
    @TableId(value = "LGTD", type = IdType.NONE)
    private String lgtd;

    @ApiModelProperty(value = "纬度")
    @TableId(value = "LTTD", type = IdType.NONE)
    private String lttd;

    @ApiModelProperty(value = "设备类型")
    @TableId(value = "TYPE", type = IdType.NONE)
    private String type;

    @ApiModelProperty(value = "所在流域")
    @TableId(value = "WSNM", type = IdType.NONE)
    private String wsnm;

    @ApiModelProperty(value = "建设日期")
    @TableId(value = "BDATE", type = IdType.NONE)
    private Date bdate;

    @ApiModelProperty(value = "描述")
    @TableId(value = "COMMENTS", type = IdType.NONE)
    private String comments;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getBdate() {
        return bdate;
    }

    public void setBdate(Date bdate) {
        this.bdate = bdate;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    @Override
    public String toString() {
        return "DcjzRadioGbSs{" +
                "adnm='" + adnm + '\'' +
                ", address='" + address + '\'' +
                ", lgtd='" + lgtd + '\'' +
                ", lttd='" + lttd + '\'' +
                ", type='" + type + '\'' +
                ", wsnm='" + wsnm + '\'' +
                ", bdate=" + bdate +
                ", comments='" + comments + '\'' +
                '}';
    }
}
