package com.huitu.cloud.api.hsfxt.riverrisk.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@TableName("RV_RISKMAP")
@ApiModel(value="RvRiskmap对象", description="河道风险表")
public class RvRiskmap extends Model<RvRiskmap> {

    private static final long serialVersionUID=1L;

    @TableField("RV_CODE")
    @ApiModelProperty(value = "河道风险编码")
    private String rvCode;

    @TableField("DEPTH")
    @ApiModelProperty(value = "水深")
    private BigDecimal depth;

    @TableField("RV_NAME")
    @ApiModelProperty(value = "河道风险名称")
    private String rvName;

    @TableField("AREA")
    @ApiModelProperty(value = "面积")
    private BigDecimal area;

    @TableField("RV_LEVEL")
    @ApiModelProperty(value = "河流级别码")
    private Integer rvLevel;


    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public BigDecimal getDepth() {
        return depth;
    }

    public void setDepth(BigDecimal depth) {
        this.depth = depth;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public BigDecimal getArea() {
        return area;
    }

    public void setArea(BigDecimal area) {
        this.area = area;
    }

    @Override
    protected Serializable pkVal() {
        return null;
    }

    public Integer getRvLevel() {
        return rvLevel;
    }

    public void setRvLevel(Integer rvLevel) {
        this.rvLevel = rvLevel;
    }

    @Override
    public String toString() {
        return "RvRiskmap{" +
                "rvCode='" + rvCode + '\'' +
                ", depth=" + depth +
                ", rvName='" + rvName + '\'' +
                ", area=" + area +
                ", rvLevel=" + rvLevel +
                '}';
    }
}
