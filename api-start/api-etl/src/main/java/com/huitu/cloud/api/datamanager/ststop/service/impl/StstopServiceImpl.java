package com.huitu.cloud.api.datamanager.ststop.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.datamanager.anmrecord.service.AnmrecordService;
import com.huitu.cloud.api.datamanager.manualreport.mapper.ManualReportDao;
import com.huitu.cloud.api.datamanager.ststop.entity.*;
import com.huitu.cloud.api.datamanager.ststop.mapper.StstopDao;
import com.huitu.cloud.api.datamanager.ststop.service.StstopService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.DateFormatUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>
 * 站点屏蔽记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@Service
public class StstopServiceImpl extends ServiceImpl<StstopDao, BsnStstopr> implements StstopService {
    @Autowired
    private AnmrecordService anmrecordService;
    @Autowired
    private ManualReportDao reportDao;
    @Override
    @Transactional
    public String saveStStop(StStopQo stStopQo) {
        Date date=new Date();
        String tb=stStopQo.getTbnm();
        List<BsnStStopTab> tbTypes=baseMapper.getTabByType(tb);
        //String[] tbTypes=stStopQo.getTbnm().split(",");
        if(stStopQo.getStsttp().equals("0")){
            for(BsnStStopTab item:tbTypes){
                //非计划型
                BsnStstopr bsnStstopr=new BsnStstopr();
                bsnStstopr.setStcd(stStopQo.getStcd());
                bsnStstopr.setStsttp(stStopQo.getStsttp());
                bsnStstopr.setUsernm(stStopQo.getUsernm());
                bsnStstopr.setTm(date);
                bsnStstopr.setStsta("1");
                bsnStstopr.setTbnm(item.getTbnm());
                baseMapper.insert(bsnStstopr);;
            }
            //将产生的异常数据 设为忽略
            anmrecordService.updateStaflg(stStopQo.getStcd(),tb);
            //修改测站信息扩展表中的该测站类型为负值
            //查询测站扩展信息表中的测站类型
            String type=baseMapper.getStType(stStopQo.getStcd());
            type=0-Integer.parseInt(type)+"";
            baseMapper.updateStType(type,stStopQo.getStcd());
        }else{
            for(BsnStStopTab item:tbTypes){
                //计划型 插入开始与结束两条记录
                BsnStstopr bsnStstopr=new BsnStstopr();
                bsnStstopr.setStcd(stStopQo.getStcd());
                bsnStstopr.setStsttp(stStopQo.getStsttp());
                bsnStstopr.setUsernm(stStopQo.getUsernm());
                bsnStstopr.setTbnm(item.getTbnm());
                bsnStstopr.setTm(DateFormatUtil.formatStringToDate(stStopQo.getStm(),"yyyy-MM-dd HH:mm:ss"));
                bsnStstopr.setStsta("1");
                //插入开始
                baseMapper.insert(bsnStstopr);
                //设置屏蔽
                bsnStstopr.setStsta("0");
                bsnStstopr.setTm(DateFormatUtil.formatStringToDate(stStopQo.getEtm(),"yyyy-MM-dd HH:mm:ss"));
                //插入结束
                baseMapper.insert(bsnStstopr);
            }
        }
        BsnStstopexr bsnStstopexr = new BsnStstopexr();
        List<String> strList = Arrays.asList(stStopQo.getTbnm().split(","));
        for (int i = 0; i < strList.size(); i++) {
//            0-雨量  1-水位
//            屏蔽要素 雨量：st_pptn_r , 河道水位：st_river_r, 水库水位：st_rsvr_r
            bsnStstopexr.setStcd(stStopQo.getStcd());
            bsnStstopexr.setIsshow(stStopQo.getIsShow());
            if(strList.get(i).equals("st_pptn_r")){
                bsnStstopexr.setReason(stStopQo.getRainfallReason()); // 雨量屏蔽原因
                bsnStstopexr.setFactor("st_pptn_r");
            }else if(strList.get(i).equals("st_river_r") || strList.get(i).equals("st_rsvr_r")){
                bsnStstopexr.setReason(stStopQo.getWaterReason()); // 水位屏蔽原因
                if(strList.get(i).equals("st_river_r") ){
                    bsnStstopexr.setFactor("st_river_r");
                }else{
                    bsnStstopexr.setFactor("st_rsvr_r");
                }
            }
            baseMapper.insertBsnStstopexr(bsnStstopexr);
        }
        return "OK";
    }

    @Override
    @Transactional
    public List<StstopVo> getStStopInfos(String adcd,String isAll,String isShow) {
        Map<String,Object> param=new HashMap<>();
        int level= AdcdUtil.getAdLevel(adcd);
        if(level>0){
            param.put("ad",adcd.substring(0,level));
            param.put("level",level);
        }
        if(isAll!=null&&isAll.equals("1")){
            //查询所有有效的屏蔽站点 包含未来计划的
            param.put("isCurrent",false);
        }else {
            //截止到当前
            param.put("isCurrent",true);
        }
        param.put("isShow", isShow);
        List<StstopVo> list=baseMapper.getInfoByParam(param);
        return list;
    }

    @Override
    @Transactional
    public String closeStStop(String stcd) {
        List<BsnStstoprEx> list=baseMapper.getOneStstop(stcd,null);
        if(list.size()>0){
            //变更测站所有屏蔽的关闭状态
            baseMapper.colseALLStStop(stcd);
        }
        list.forEach(x->{
            //计划性
            if(x.getStsttp().equals("1")){
                baseMapper.updateEtm(stcd,x.getEtm());
            }
        });
        //修改测站信息扩展表中的该测站类型为正值
        //查询测站扩展信息表中的测站类型
        String type=baseMapper.getStType(stcd);
        String flag="-";
        if (type.contains(flag)){
            type=0-Integer.parseInt(type)+"";
        }
        baseMapper.updateStType(type,stcd);
        baseMapper.deleteFactor(stcd);

        //关闭人工报讯
        reportDao.close(stcd,null);
        return "OK";
    }

    @Override
    public String closeStStopByTbnm(String stcd, String tbnm) {
        List<BsnStstoprEx> list=baseMapper.getOneStstop(stcd,tbnm);
        if(list.size()>0){
            //变更测站某个要素的所有屏蔽的关闭状态
            baseMapper.colseStStop(stcd,tbnm);
        }
        list.forEach(x->{
            //计划性
            if(x.getStsttp().equals("1")){
                baseMapper.updateEtmByTbnm(stcd,x.getEtm(),x.getTbnm());
            }
        });
        //修改测站信息扩展表中的该测站类型为正值
        //查询测站扩展信息表中的测站类型
        String type=baseMapper.getStType(stcd);
        String flag="-";
        if (type.contains(flag)){
            type=0-Integer.parseInt(type)+"";
        }
        baseMapper.updateStType(type,stcd);
        baseMapper.deleteStcdFactor(stcd,tbnm);
        //关闭人工报讯
        reportDao.close(stcd,tbnm);
        return "OK";
    }

    @Override
    public String checkStStopByTbnmAndStcd(String stcd, String tbnm) {
        int count=baseMapper.getCountByStcdAndTbnm(stcd,tbnm);
        if(count>0){
            //存在
            return "1";
        }else{
            return "0";
        }
    }
}
