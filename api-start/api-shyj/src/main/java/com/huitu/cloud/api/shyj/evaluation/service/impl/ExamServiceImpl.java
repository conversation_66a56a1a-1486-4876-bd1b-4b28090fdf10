package com.huitu.cloud.api.shyj.evaluation.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import com.huitu.cloud.api.shyj.evaluation.mapper.ExamDao;
import com.huitu.cloud.api.shyj.evaluation.service.ExamService;
import com.huitu.cloud.api.shyj.warn.entity.WarnFeedback;
import com.huitu.cloud.util.AdcdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 考核评价
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Service
public class ExamServiceImpl implements ExamService {
    @Autowired
    private ExamDao examDao;
    @Override
    public List<ExamIndex> getExamIndexList() {
        List<ExamIndex> list = examDao.getExamIndexList();
        return list;
    }

    @Override
    public List<ExamCityScore> getExamCityScoreListByYm(String month) {
        List<ExamCityScore> list = examDao.getExamCityScoreListByYm(month);
        return list;
    }

    @Override
    public Integer addOrUpdateExamCityScoreByAdcdAndMonth(ExamCityScore examCityScore) {
        if(examCityScore.getVcode()==null || "".equals(examCityScore.getVcode())){
            examCityScore.setVcode(UUID.randomUUID().toString().replaceAll("-",""));
            return examDao.addExamCityScoreByAdcd(examCityScore);
        }else{
            return examDao.updateExamCityScoreByAdcd(examCityScore);
        }
    }

    @Override
    public boolean saveExamIndex(ExamIndex entity) {
        boolean flag = examDao.saveExamIndex(entity);
        return flag;
    }

    @Override
    public List<ExamIndex> getExamIndexById(ExamIndex entity) {
        List<ExamIndex> list = examDao.getExamIndexById(entity);
        return list;
    }

    @Override
    public boolean updateExamIndex(ExamIndex entity) {
        boolean flag = examDao.updateExamIndex(entity);
        return flag;
    }

    @Override
    public boolean deleteExamIndex(String examType, String examOption) {
        boolean flag = examDao.deleteExamIndex(examType, examOption);
        return flag;
    }

    @Override
    public IPage<ExamIndex> getExamIndexListPage(String type, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        IPage<ExamIndex> result = examDao.getExamIndexListPage(page, param);
        return result;
    }

    @Override
    public List<ExamProvinceScore> getExamProvinceScoreListByAdcd(String adcd, String month) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ym", month);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<ExamProvinceScore> list = examDao.getExamProvinceScoreListByAdcd(param);
        return list;
    }

    @Override
    public Integer addOrUpdateExamProvinceScoreByAdcdAndMonth(ExamProvinceScore examProvinceScore) {
        if(examProvinceScore.getVcode()==null || "".equals(examProvinceScore.getVcode())){
            examProvinceScore.setVcode(UUID.randomUUID().toString().replaceAll("-",""));
            return examDao.addExamProvinceScoreByAdcd(examProvinceScore);
        }else{
            return examDao.updateExamProvinceScoreByAdcd(examProvinceScore);
        }
    }

}
