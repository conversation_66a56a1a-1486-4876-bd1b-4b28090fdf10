package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2019-09-11
 */
public class RiverNearWarnVo extends BsnRiverNearWarnR {

    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "行政区划码")
    private String adcd;

    @ApiModelProperty(value = "行政区名称")
    private String adnm;

    @ApiModelProperty(value = "警戒水位")
    @TableField("WRZ")
    private BigDecimal wrz;

    @ApiModelProperty(value = "警戒流量")
    @TableField("WRQ")
    private BigDecimal wrq;

    @ApiModelProperty(value = "保证水位")
    @TableField("GRZ")
    private BigDecimal grz;

    @ApiModelProperty(value = "保证流量")
    @TableField("GRQ")
    private BigDecimal grq;

    @ApiModelProperty(value = "超警戒水位")
    private BigDecimal zwrz;

    @ApiModelProperty(value = "超警戒流量")
    private BigDecimal zwrq;

    @ApiModelProperty(value = "超保证水位")
    private BigDecimal zgrz;

    @ApiModelProperty(value = "超保证流量")
    private BigDecimal zgrq;

    @ApiModelProperty(value = "偏移经度")
    private BigDecimal plgtd;

    @ApiModelProperty(value = "偏移纬度")
    private BigDecimal plttd;

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public BigDecimal getWrz() {
        return wrz;
    }

    public void setWrz(BigDecimal wrz) {
        this.wrz = wrz;
    }

    public BigDecimal getWrq() {
        return wrq;
    }

    public void setWrq(BigDecimal wrq) {
        this.wrq = wrq;
    }

    public BigDecimal getGrz() {
        return grz;
    }

    public void setGrz(BigDecimal grz) {
        this.grz = grz;
    }

    public BigDecimal getGrq() {
        return grq;
    }

    public void setGrq(BigDecimal grq) {
        this.grq = grq;
    }

    public BigDecimal getZwrz() {
        return zwrz;
    }

    public void setZwrz(BigDecimal zwrz) {
        this.zwrz = zwrz;
    }

    public BigDecimal getZwrq() {
        return zwrq;
    }

    public void setZwrq(BigDecimal zwrq) {
        this.zwrq = zwrq;
    }

    public BigDecimal getZgrz() {
        return zgrz;
    }

    public void setZgrz(BigDecimal zgrz) {
        this.zgrz = zgrz;
    }

    public BigDecimal getZgrq() {
        return zgrq;
    }

    public void setZgrq(BigDecimal zgrq) {
        this.zgrq = zgrq;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }
}
