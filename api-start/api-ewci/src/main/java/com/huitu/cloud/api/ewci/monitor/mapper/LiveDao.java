package com.huitu.cloud.api.ewci.monitor.mapper;

import com.huitu.cloud.api.ewci.monitor.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 实况信息Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface LiveDao {

    /**
     * 获取雨情信息
     *
     * @param params
     * @return
     **/
    List<LiveRain> getRainList(@Param("map") Map<String, Object> params);

    /**
     * 防治区信息列表
     *
     * @param list
     * @return
     */
    List<IaCPrevad> getPrevadList(@Param("list") List<String> list);

    /**
     * 危险区信息列表
     *
     * @param list
     * @return
     */
    List<IaCDanad> getDanadList(@Param("list") List<String> list);

    /**
     * 责任人信息列表
     *
     * @param list
     * @return
     */
    List<FloodPerson> getFloodPersonList(@Param("list") List<String> list);

    /**
     * 防洪工程信息-水库列表
     *
     * @param list
     * @return
     */
    List<ReservoirData> getReservoirList(@Param("list") List<String> list);

    /**
     * 防洪工程信息-水闸列表
     *
     * @param list
     * @return
     */
    List<SluiceData> getSluiceList(@Param("list") List<String> list);

    /**
     * 防洪工程信息-堤防列表
     *
     * @param list
     * @return
     */
    List<DikeData> getDikeList(@Param("list") List<String> list);

    /**
     * 防洪工程信息-涵洞列表
     *
     * @param list
     * @return
     */
    List<CulvertData> getCulvertList(@Param("list") List<String> list);

    /**
     * 防洪工程信息-桥梁列表
     *
     * @param list
     * @return
     */
    List<BridgeData> getBridgeList(@Param("list") List<String> list);

    /**
     * 防洪工程信息-塘堰列表
     *
     * @param list
     * @return
     */
    List<DaminfoData> getDaminfoList(@Param("list") List<String> list);

    /**
     * 获取实况预警规则
     * @param params
     * @return
     */
    List<MessageWarnStatusB> getLiveWarnStatusList(@Param("map") Map<String, Object> params);

    /**
     * 水库责任人信息列表
     *
     * @param list
     * @return
     */
    List<RsvrPersonVo> getRsvrPersonList(@Param("list") List<String> list);
}
