************ - - [17/Jun/2025:14:37:56 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [17/Jun/2025:14:37:56 +0800] "GET /login HTTP/1.1" 302 -
************ - - [17/Jun/2025:14:38:30 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [17/Jun/2025:14:38:30 +0800] "GET /login HTTP/1.1" 302 -
************ - - [17/Jun/2025:14:38:36 +0800] "GET /login?code=ngejQf&state=t3usaC HTTP/1.1" 302 -
************ - - [17/Jun/2025:14:38:36 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [17/Jun/2025:14:38:38 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750142318176 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750142318176 HTTP/1.1" 200 552
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750142322495 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750142322495 HTTP/1.1" 200 61649
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750142322585 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750142322585 HTTP/1.1" 200 10388
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750142322602 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750142322602 HTTP/1.1" 200 2009
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750142322750 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-14+14:38:42&etm=&_timer304=1750142322751 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750142322752 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-17+08:00&etm=2025-06-17+15:00&filterCnt=6&_timer304=1750142322752 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750142322752 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750142322752 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:42 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750142322750 HTTP/1.1" 200 1482
************ - - [17/Jun/2025:14:38:42 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-17+08:00&etm=2025-06-17+15:00&filterCnt=6&_timer304=1750142322752 HTTP/1.1" 200 164
************ - - [17/Jun/2025:14:38:42 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750142322752 HTTP/1.1" 200 166
************ - - [17/Jun/2025:14:38:42 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-14+14:38:42&etm=&_timer304=1750142322751 HTTP/1.1" 200 156
************ - - [17/Jun/2025:14:38:42 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [17/Jun/2025:14:38:43 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [17/Jun/2025:14:38:43 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [17/Jun/2025:14:38:43 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [17/Jun/2025:14:38:43 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750142322752 HTTP/1.1" 200 169
************ - - [17/Jun/2025:14:38:43 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750142322752 HTTP/1.1" 200 13016
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/base/saas/token?_timer304=1750142323528 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-17&_timer304=1750142323528 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750142323540 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:43 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:44 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [17/Jun/2025:14:38:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [17/Jun/2025:14:38:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [17/Jun/2025:14:38:46 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 6053
************ - - [17/Jun/2025:14:38:46 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 6414
************ - - [17/Jun/2025:14:38:47 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [17/Jun/2025:14:38:47 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [17/Jun/2025:14:38:47 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [17/Jun/2025:14:38:47 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [17/Jun/2025:14:38:47 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750142323540 HTTP/1.1" 200 2009
************ - - [17/Jun/2025:14:38:47 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [17/Jun/2025:14:38:47 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 302
************ - - [17/Jun/2025:14:38:47 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 302
************ - - [17/Jun/2025:14:38:47 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750142327899 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750142327906 HTTP/1.1" 200 -
************ - - [17/Jun/2025:14:38:48 +0800] "GET /api/base/saas/token?_timer304=1750142323528 HTTP/1.1" 200 411
************ - - [17/Jun/2025:14:38:48 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [17/Jun/2025:14:38:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750142327906 HTTP/1.1" 200 159
************ - - [17/Jun/2025:14:38:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750142327899 HTTP/1.1" 200 160
************ - - [17/Jun/2025:14:38:51 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [17/Jun/2025:14:38:52 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-17&_timer304=1750142323528 HTTP/1.1" 200 348
************ - - [17/Jun/2025:17:09:58 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [17/Jun/2025:17:09:58 +0800] "GET /login HTTP/1.1" 302 -
************ - - [17/Jun/2025:17:09:58 +0800] "GET /login?code=ukxEzF&state=vuxKtl HTTP/1.1" 302 -
************ - - [17/Jun/2025:17:09:58 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750151399089 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750151399089 HTTP/1.1" 200 552
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750151399363 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750151399363 HTTP/1.1" 200 61649
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750151399504 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750151399504 HTTP/1.1" 200 10388
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750151399594 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750151399594 HTTP/1.1" 200 2009
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750151399779 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-14+17:09:59&etm=&_timer304=1750151399779 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-17+08:00&etm=2025-06-17+18:00&filterCnt=6&_timer304=1750151399779 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750151399779 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750151399779 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750151399779 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:09:59 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-14+17:09:59&etm=&_timer304=1750151399779 HTTP/1.1" 200 156
************ - - [17/Jun/2025:17:09:59 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [17/Jun/2025:17:09:59 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-17+08:00&etm=2025-06-17+18:00&filterCnt=6&_timer304=1750151399779 HTTP/1.1" 200 164
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750151399779 HTTP/1.1" 200 1482
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750151399779 HTTP/1.1" 200 166
************ - - [17/Jun/2025:17:09:59 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750151399779 HTTP/1.1" 200 169
************ - - [17/Jun/2025:17:09:59 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750151399779 HTTP/1.1" 200 13016
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/base/saas/token?_timer304=1750151400423 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-17&_timer304=1750151400423 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750151400436 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 302
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 6053
************ - - [17/Jun/2025:17:10:01 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750151400436 HTTP/1.1" 200 2009
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 302
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 6414
************ - - [17/Jun/2025:17:10:01 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [17/Jun/2025:17:10:01 +0800] "GET /api/base/saas/token?_timer304=1750151400423 HTTP/1.1" 200 411
************ - - [17/Jun/2025:17:10:02 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-17&_timer304=1750151400423 HTTP/1.1" 200 348
************ - - [17/Jun/2025:17:10:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750151408803 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750151408810 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750151408803 HTTP/1.1" 200 160
************ - - [17/Jun/2025:17:10:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750151408810 HTTP/1.1" 200 159
************ - - [17/Jun/2025:17:10:22 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750151422671 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:22 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-14+17:10:22&etm=&_timer304=1750151422850 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750151422850 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-17+08:00&etm=2025-06-17+18:00&filterCnt=6&_timer304=1750151422850 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:22 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750151422850 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750151422850 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750151422851 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:22 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750151422855 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [17/Jun/2025:17:10:22 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [17/Jun/2025:17:10:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [17/Jun/2025:17:10:22 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [17/Jun/2025:17:10:22 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-14+17:10:22&etm=&_timer304=1750151422850 HTTP/1.1" 200 156
************ - - [17/Jun/2025:17:10:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750151422850 HTTP/1.1" 200 166
************ - - [17/Jun/2025:17:10:22 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-17+08:00&etm=2025-06-17+18:00&filterCnt=6&_timer304=1750151422850 HTTP/1.1" 200 164
************ - - [17/Jun/2025:17:10:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750151422850 HTTP/1.1" 200 169
************ - - [17/Jun/2025:17:10:22 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750151422851 HTTP/1.1" 200 161
************ - - [17/Jun/2025:17:10:23 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750151422995 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:23 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:23 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151423017 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:23 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750151422850 HTTP/1.1" 200 13016
************ - - [17/Jun/2025:17:10:23 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750151422855 HTTP/1.1" 200 159491
************ - - [17/Jun/2025:17:10:23 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [17/Jun/2025:17:10:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [17/Jun/2025:17:10:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [17/Jun/2025:17:10:23 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151423017 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:10:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [17/Jun/2025:17:10:23 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750151422671 HTTP/1.1" 200 144
************ - - [17/Jun/2025:17:10:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [17/Jun/2025:17:10:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:10:24 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151424147 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:24 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151424147 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:10:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750151422995 HTTP/1.1" 200 444620
************ - - [17/Jun/2025:17:10:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 6082
************ - - [17/Jun/2025:17:10:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 6082
************ - - [17/Jun/2025:17:10:25 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151425889 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:26 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151425889 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:10:28 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:10:28 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151428954 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:28 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151428954 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:10:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:10:30 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:10:31 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151431161 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:10:31 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151431161 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:12:23 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:12:23 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151543780 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:12:23 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151543780 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:12:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:12:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:12:25 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151545681 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:12:25 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151545681 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:14:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:14:24 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151664364 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:14:24 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151664364 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:14:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:14:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:14:26 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151666631 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:14:26 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151666631 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:14:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750151699599 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:14:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750151699599 HTTP/1.1" 200 160
************ - - [17/Jun/2025:17:15:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750151709588 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:15:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750151709588 HTTP/1.1" 200 159
************ - - [17/Jun/2025:17:15:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750151723597 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:15:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750151723599 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:15:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750151723597 HTTP/1.1" 200 161
************ - - [17/Jun/2025:17:15:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [17/Jun/2025:17:15:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [17/Jun/2025:17:15:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750151723599 HTTP/1.1" 200 444620
************ - - [17/Jun/2025:17:15:24 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57082
************ - - [17/Jun/2025:17:16:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:16:24 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151784497 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:16:24 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750151784497 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:16:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:16:26 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:16:26 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151786939 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:16:27 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750151786939 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:19:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750151999585 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:19:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750151999585 HTTP/1.1" 200 160
************ - - [17/Jun/2025:17:20:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750152010595 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:20:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750152010595 HTTP/1.1" 200 159
************ - - [17/Jun/2025:17:20:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750152023593 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:20:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750152023594 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:20:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [17/Jun/2025:17:20:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750152023593 HTTP/1.1" 200 161
************ - - [17/Jun/2025:17:20:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [17/Jun/2025:17:20:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:20:24 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152024528 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:20:24 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152024528 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:20:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750152023594 HTTP/1.1" 200 444620
************ - - [17/Jun/2025:17:20:24 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57082
************ - - [17/Jun/2025:17:20:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:20:26 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732295
************ - - [17/Jun/2025:17:20:26 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152026790 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:20:26 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152026790 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:22:48 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:22:48 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152168461 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:22:48 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152168461 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:22:49 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732298
************ - - [17/Jun/2025:17:22:50 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732298
************ - - [17/Jun/2025:17:22:50 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152170755 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:22:50 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152170755 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:23:46 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:23:46 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152226723 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:23:46 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152226723 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:23:47 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732298
************ - - [17/Jun/2025:17:23:48 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732298
************ - - [17/Jun/2025:17:23:48 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152228810 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:23:48 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152228810 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:23:58 +0800] "OPTIONS /api/syq/rain/get-rain-pointer-list/E9512QXZ?_timer304=1750152238946 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:23:58 +0800] "GET /api/syq/rain/get-rain-pointer-list/E9512QXZ?_timer304=1750152238946 HTTP/1.1" 200 461
************ - - [17/Jun/2025:17:24:00 +0800] "OPTIONS /api/syq/rain/get-rain-pointer-list/E9590QXZ?_timer304=1750152240375 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:24:00 +0800] "GET /api/syq/rain/get-rain-pointer-list/E9590QXZ?_timer304=1750152240375 HTTP/1.1" 200 163
************ - - [17/Jun/2025:17:24:23 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:24:23 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152263751 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:24:23 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152263751 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:24:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732298
************ - - [17/Jun/2025:17:24:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732298
************ - - [17/Jun/2025:17:24:25 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152265808 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:24:25 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152265808 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:24:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750152299695 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:24:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750152299695 HTTP/1.1" 200 160
************ - - [17/Jun/2025:17:25:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750152311697 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:25:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750152311697 HTTP/1.1" 200 159
************ - - [17/Jun/2025:17:25:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750152323588 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:25:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750152323590 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:25:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750152323588 HTTP/1.1" 200 161
************ - - [17/Jun/2025:17:25:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [17/Jun/2025:17:25:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [17/Jun/2025:17:25:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750152323590 HTTP/1.1" 200 445226
************ - - [17/Jun/2025:17:25:24 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57694
************ - - [17/Jun/2025:17:27:48 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:27:48 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152468455 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:27:48 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152468455 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:27:49 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:27:50 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:27:51 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152471143 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:27:51 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152471143 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:29:48 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:29:48 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152588471 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:29:48 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152588471 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:29:49 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:29:50 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:29:51 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152591080 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:29:51 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152591080 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:29:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750152599592 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:29:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750152599592 HTTP/1.1" 200 160
************ - - [17/Jun/2025:17:30:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750152612588 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:30:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750152612588 HTTP/1.1" 200 159
************ - - [17/Jun/2025:17:30:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750152623597 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:30:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750152623598 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:30:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [17/Jun/2025:17:30:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750152623597 HTTP/1.1" 200 161
************ - - [17/Jun/2025:17:30:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [17/Jun/2025:17:30:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750152623598 HTTP/1.1" 200 446371
************ - - [17/Jun/2025:17:30:24 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 58865
************ - - [17/Jun/2025:17:31:02 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:31:02 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152662636 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:31:02 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152662636 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:31:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:31:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:31:04 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152664685 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:31:04 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152664685 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:31:20 +0800] "OPTIONS /api/syq/rain/get-rain-pointer-list/E9513QXZ?_timer304=1750152680039 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:31:20 +0800] "GET /api/syq/rain/get-rain-pointer-list/E9513QXZ?_timer304=1750152680039 HTTP/1.1" 200 163
************ - - [17/Jun/2025:17:31:21 +0800] "OPTIONS /api/syq/rain/get-rain-pointer-list/E9590QXZ?_timer304=1750152681424 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:31:21 +0800] "GET /api/syq/rain/get-rain-pointer-list/E9590QXZ?_timer304=1750152681424 HTTP/1.1" 200 163
************ - - [17/Jun/2025:17:32:25 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:32:25 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152745368 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:32:25 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152745368 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:32:26 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:32:26 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:32:28 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152748134 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:32:28 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152748134 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:34:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750152899693 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:34:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750152899693 HTTP/1.1" 200 160
************ - - [17/Jun/2025:17:35:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750152913599 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:35:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750152913599 HTTP/1.1" 200 159
************ - - [17/Jun/2025:17:35:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750152923589 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:35:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750152923590 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:35:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750152923589 HTTP/1.1" 200 161
************ - - [17/Jun/2025:17:35:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [17/Jun/2025:17:35:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [17/Jun/2025:17:35:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750152923590 HTTP/1.1" 200 446371
************ - - [17/Jun/2025:17:35:24 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 58865
************ - - [17/Jun/2025:17:35:48 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:35:48 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152948480 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:35:48 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750152948480 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:35:49 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:35:50 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:35:51 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152951189 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:35:51 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750152951189 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:37:48 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:37:48 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153068481 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:37:48 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153068481 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:37:49 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:37:50 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:37:51 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153071206 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:37:51 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153071206 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:39:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-17+08:00&etm=2025-06-17+18:00&filterCnt=6&_timer304=1750153172340 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:33 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-14+17:39:32&etm=&_timer304=1750153172340 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750153172340 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750153172262 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:33 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750153172340 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:33 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750153172265 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750153172340 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:33 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153172472 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750153172445 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [17/Jun/2025:17:39:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [17/Jun/2025:17:39:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [17/Jun/2025:17:39:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [17/Jun/2025:17:39:35 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [17/Jun/2025:17:39:35 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [17/Jun/2025:17:39:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [17/Jun/2025:17:39:35 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-14+17:39:32&etm=&_timer304=1750153172340 HTTP/1.1" 200 156
************ - - [17/Jun/2025:17:39:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750153172262 HTTP/1.1" 200 161
************ - - [17/Jun/2025:17:39:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750153172340 HTTP/1.1" 200 166
************ - - [17/Jun/2025:17:39:35 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-17+08:00&etm=2025-06-17+18:00&filterCnt=6&_timer304=1750153172340 HTTP/1.1" 200 164
************ - - [17/Jun/2025:17:39:35 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153172472 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:39:35 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750153172265 HTTP/1.1" 200 159491
************ - - [17/Jun/2025:17:39:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750153172340 HTTP/1.1" 200 169
************ - - [17/Jun/2025:17:39:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [17/Jun/2025:17:39:35 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750153172340 HTTP/1.1" 200 13016
************ - - [17/Jun/2025:17:39:36 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [17/Jun/2025:17:39:36 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:39:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-17+08:00&etm=2025-06-17+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750153172445 HTTP/1.1" 200 446371
************ - - [17/Jun/2025:17:39:37 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153177294 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:37 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153177294 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:39:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 7820
************ - - [17/Jun/2025:17:39:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 7820
************ - - [17/Jun/2025:17:39:38 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153178953 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:39 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153178953 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:39:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750153182550 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750153182550 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750153182550 HTTP/1.1" 200 160
************ - - [17/Jun/2025:17:39:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750153182550 HTTP/1.1" 200 159
************ - - [17/Jun/2025:17:39:49 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:39:49 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153189159 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:49 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153189159 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:39:49 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 7820
************ - - [17/Jun/2025:17:39:50 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 7820
************ - - [17/Jun/2025:17:39:50 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153190651 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:50 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153190651 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:39:55 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:39:55 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153195008 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:55 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153195008 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:39:55 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:39:56 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:39:57 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153197616 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:39:57 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153197616 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:40:02 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:40:02 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153202201 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:40:02 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153202201 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:40:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:40:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:40:04 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153204846 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:40:04 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153204846 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:40:15 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:40:15 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153215085 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:40:15 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153215085 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:40:20 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:40:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:40:24 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153224255 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:40:24 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153224255 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:40:31 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:40:31 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153231680 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:40:31 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153231680 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:40:52 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153251786 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:40:52 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 8206
************ - - [17/Jun/2025:17:40:52 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153251786 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:40:55 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:40:55 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153255949 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:40:55 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153255949 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:41:00 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:41:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:41:02 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153262077 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:41:02 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153262077 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:41:11 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:41:11 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153271559 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:41:11 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153271559 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:41:34 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153291571 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:41:36 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153291571 HTTP/1.1" 200 232
************ - - [17/Jun/2025:17:41:37 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [17/Jun/2025:17:41:37 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153297441 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:41:37 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750153297441 HTTP/1.1" 200 155
************ - - [17/Jun/2025:17:41:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 8206
************ - - [17/Jun/2025:17:41:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:41:41 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3732302
************ - - [17/Jun/2025:17:41:42 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153302321 HTTP/1.1" 200 -
************ - - [17/Jun/2025:17:41:42 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750153302321 HTTP/1.1" 200 232
