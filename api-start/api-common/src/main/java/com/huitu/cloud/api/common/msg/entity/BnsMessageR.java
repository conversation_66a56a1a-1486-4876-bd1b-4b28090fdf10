package com.huitu.cloud.api.common.msg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 预警消息记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("BNS_MESSAGE_R")
@ApiModel(value="websocket消息表", description="websocket消息表")
public class BnsMessageR extends Model<BnsMessageR> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "编号 发送无需传值")
    @TableId(value = "id", type = IdType.NONE)
    private BigDecimal id;

    @ApiModelProperty(value = "消息类型（对应BSN_USERPUSHMSG_B中tp类型，1-6对应UserPushMsgQo，检查问题-待填报：7， 检查问题-待确认：8，检查问题-待复核：9，警示通报：10）")
    @TableField("type")
    private Integer type;

    @ApiModelProperty(value = "消息生成时间 无需传值默认当前时间")
    @TableField("tm")
    private LocalDateTime tm;

    @ApiModelProperty(value = "政区编码")
    @TableField("adcd")
    private String adcd;

    @ApiModelProperty(value = "组织机构编码")
    @TableField("deptid")
    private Integer deptid;

    @ApiModelProperty(value = "短信内容")
    @TableField("message")
    private String message;

    @ApiModelProperty(value = "是否处理标识 无需传值")
    @TableField("flag")
    private String flag;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getDeptid() {
        return deptid;
    }

    public void setDeptid(Integer deptid) {
        this.deptid = deptid;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "BnsMessageR{" +
                "id=" + id +
                ", type=" + type +
                ", tm=" + tm +
                ", adcd='" + adcd + '\'' +
                ", deptid=" + deptid +
                ", message='" + message + '\'' +
                ", flag='" + flag + '\'' +
                '}';
    }
}
