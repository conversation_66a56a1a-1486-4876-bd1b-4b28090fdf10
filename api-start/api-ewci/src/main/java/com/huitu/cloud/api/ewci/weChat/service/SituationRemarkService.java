package com.huitu.cloud.api.ewci.weChat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.bia.entity.StRiverVos;
import com.huitu.cloud.api.ewci.weChat.entity.*;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SituationRemarkService {
    /**
     * 累计雨量统计
     *
     * @return
     */
    Map<String, Object> getRainByCondition();

    /**
     * 查询全部累计雨量信息
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @return
     */
    List<WeChatRain> getRainByConditionAll(String stm, String etm, List<String> stType, String adcd, String bsnm, String stnm, String threshold, String rainShowType, List<String> isOut, List<String> isFollow, List<Integer> forecastHour);

    /**
     * 根据政区编码查询
     *
     * @param adcd
     * @param adLevl
     * @return
     */
    Map<String, WeChatAdcdB> getAdInfoList(String adcd, String adLevl);

    /**
     * 业务门户降雨统计
     *
     * @return
     */
    WeChatRainByYwmh getRainInfoByYwmh();

    /**
     * 值班报告降雨情况
     *
     * @param adcd 政区编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    WeChatRainByDutyRecort getRainInfoByDuty(String adcd, String stm, String etm);

    /**
     * 降雨告警列表
     *
     * @param adcd     政区编码
     * @param bsnm     流域名称
     * @param stnm     测站名称
     * @param stType   测站归属类型 1、山洪 2、水文 3、气象
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */

    IPage<WeChatBsnRainAlarm> getRainWarn(String adcd, String bsnm, String stnm, String warnType, List<String> stType, int pageNum, int pageSize);

    /**
     * 最新水库水情查询
     *
     * @return
     */
    IPage<WeChatStRsvrVo> getRsvrLatestByConditon(@Validated WeChatRsvrQuery baseDao);

    /**
     * 最新水库水情查询 无参数
     *
     * @return
     */
    IPage<WeChatStRsvrVo> getNoRsvrLatest();

    /**
     * 最新水库水情查询类型参数入口
     *
     * @return
     */
    IPage<WeChatStRsvrVo> getTyRsvrByConditon(List<String> rvType);

    /**
     * 查询最新河道水情类型参数入口
     *
     * @return
     */
    IPage<StRiverVos> getTyRiver(List<String> stType);

    /**
     * 查询最新河道水情信息
     *
     * @return
     */
    IPage<StRiverVos> getRiverByConditon(WeChatRiverQo baseDao);

    /**
     * 查询最新河道水情无参数入口
     *
     * @return
     */
    IPage<StRiverVos> getNoRiver();

    String getTime(String tm);

    /**
     * 根据文件标题获取文件最新的一个文件信息
     * @return
     */
    List<WeChatBnsFilesAndUrls> getLastestBnsFileurlsr();

    /**
     * 查询最新的天气预报
     * @return
     */
    WeChatWeatherVo getWeatherForecast();

    List<WeChatQxpicgetRVo> getWxytList(int tms,String picType);
}