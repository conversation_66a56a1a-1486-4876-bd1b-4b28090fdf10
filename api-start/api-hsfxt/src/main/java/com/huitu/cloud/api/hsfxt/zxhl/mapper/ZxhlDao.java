package com.huitu.cloud.api.hsfxt.zxhl.mapper;

import com.huitu.cloud.api.hsfxt.city.entity.DicHongshuiliangji;
import com.huitu.cloud.api.hsfxt.zxhl.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 风险图-中小河流 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
public interface ZxhlDao {
    /**
     * 查询屯信息需要的洪水量级参数
     *
     * @param param 查询参数
     * @return
     */
    DicHongshuiliangji getZxhlParam(Map<String, Object> param);
    /**
     * 查询县和乡镇信息
     *
     * @param param 查询参数
     * @return
     */
    List<ZxhlTree> getZxhlTreeInfo(Map<String, Object> param);
    /**
     * 查询屯信息
     *
     * @param param 查询参数
     * @return
     */
    List<ZxhlTree> getTunZxhlTreeInfo(Map<String, Object> param);
    /**
     * 中小河流社会经济统计查询
     *
     * @param key 查询条件组合
     * @return
     */
    List<ZxhlShehuijingji> getZxhlShjjTreeInfo(@Param("key") String key);
    /**
     * 查询县区乡镇信息
     *
     * @param adcd  政区编码
     * @param bzqid 编制区编码
     * @return
     */
    ZxhlXiangQing getXianQuXiangZhen(@Param("adcd")String adcd, @Param("bzqid")String bzqid);
    /**
     * 查询灾害损失信息
     *
     * @param adcd 政区编码
     * @param key  查询条件组合
     * @return
     */
    ZxhlXiangQing getZaiHaiSunShi(@Param("adcd")String adcd, @Param("key")String key);
    /**
     * 查询中小河流屯详细信息
     *
     * @param newParam2 查询参数
     * @return
     */
    ZxhlTunXiangqing getZxhlTunShjjDetailInfo(Map<String, Object> newParam2);
    /**
     * 查询中小河流转移方案信息
     *
     * @param adcd  政区编码
     * @param bzqid 编制区编码
     * @return
     */
    List<ZxhlZhuanYiFangAn> getZxhlZhuanYiFangAn(@Param("adcd")String adcd, @Param("bzqid")String bzqid);
}
