package com.huitu.cloud.api.shyj.msg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huitu.cloud.api.shyj.msg.entity.MsgVo;
import com.huitu.cloud.api.shyj.warn.entity.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 消息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-16
 */
public interface MsgDao extends BaseMapper<WarnrecordR> {
    /**
     * 短信预警统计
     * @return
     */
    List<MsgVo> getMsgSendReport1(Map<String, Object> param);
    /**
     * 短信预警统计
     * @return
     */
    List<MsgVo> getMsgSendReport2(Map<String, Object> param);
}
