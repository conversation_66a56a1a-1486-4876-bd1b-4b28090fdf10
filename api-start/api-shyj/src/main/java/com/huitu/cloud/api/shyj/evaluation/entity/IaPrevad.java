package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 防治区基本情况调查成果汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@TableName("IA_C_PREVAD")
@ApiModel(value="IaCPrevad对象", description="防治区基本情况调查成果汇总表")
public class IaPrevad extends Model<IaPrevad> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableId(value = "ADCD", type = IdType.NONE)
    private String adcd;

    @ApiModelProperty(value = "行政区名称")
    private String adnm;

    @ApiModelProperty(value = "防治区类型(1：一般防治区  2：重点防治区)")
    private String prevtp;

    @ApiModelProperty(value = "总人口")
    @TableField("PTCOUNT")
    private Double ptcount;

    @ApiModelProperty(value = "土地面积")
    @TableField("LDAREA")
    private Double ldarea;

    @ApiModelProperty(value = "耕地面积")
    @TableField("PLAREA")
    private Double plarea;

    @ApiModelProperty(value = "总户数")
    @TableField("ETCOUNT")
    private Double etcount;

    @ApiModelProperty(value = "Ⅰ类经济户数")
    @TableField("ECOUNT1")
    private Double ecount1;

    @ApiModelProperty(value = "Ⅱ类经济户数")
    @TableField("ECOUNT2")
    private Double ecount2;

    @ApiModelProperty(value = "Ⅲ类经济户数")
    @TableField("ECOUNT3")
    private Double ecount3;

    @ApiModelProperty(value = "Ⅳ类经济户数")
    @TableField("ECOUNT4")
    private Double ecount4;

    @ApiModelProperty(value = "总房屋数")
    @TableField("HTCOUNT")
    private Double htcount;

    @ApiModelProperty(value = "Ⅰ类房屋数")
    @TableField("HCOUNT1")
    private Double hcount1;

    @ApiModelProperty(value = "Ⅱ类房屋数")
    @TableField("HCOUNT2")
    private Double hcount2;

    @ApiModelProperty(value = "Ⅲ类房屋数")
    @TableField("HCOUNT3")
    private Double hcount3;

    @ApiModelProperty(value = "Ⅳ类房屋数")
    @TableField("HCOUNT4")
    private Double hcount4;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String SIGNER;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    public String getPrevtp() {
        return prevtp;
    }

    public void setPrevtp(String prevtp) {
        this.prevtp = prevtp;
    }

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")

    private LocalDateTime moditime;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getPtcount() {
        return ptcount;
    }

    public void setPtcount(Double ptcount) {
        this.ptcount = ptcount;
    }

    public Double getLdarea() {
        return ldarea;
    }

    public void setLdarea(Double ldarea) {
        this.ldarea = ldarea;
    }

    public Double getPlarea() {
        return plarea;
    }

    public void setPlarea(Double plarea) {
        this.plarea = plarea;
    }

    public Double getEtcount() {
        return etcount;
    }

    public void setEtcount(Double etcount) {
        this.etcount = etcount;
    }

    public Double getEcount1() {
        return ecount1;
    }

    public void setEcount1(Double ecount1) {
        this.ecount1 = ecount1;
    }

    public Double getEcount2() {
        return ecount2;
    }

    public void setEcount2(Double ecount2) {
        this.ecount2 = ecount2;
    }

    public Double getEcount3() {
        return ecount3;
    }

    public void setEcount3(Double ecount3) {
        this.ecount3 = ecount3;
    }

    public Double getEcount4() {
        return ecount4;
    }

    public void setEcount4(Double ecount4) {
        this.ecount4 = ecount4;
    }

    public Double getHtcount() {
        return htcount;
    }

    public void setHtcount(Double htcount) {
        this.htcount = htcount;
    }

    public Double getHcount1() {
        return hcount1;
    }

    public void setHcount1(Double hcount1) {
        this.hcount1 = hcount1;
    }

    public Double getHcount2() {
        return hcount2;
    }

    public void setHcount2(Double hcount2) {
        this.hcount2 = hcount2;
    }

    public Double getHcount3() {
        return hcount3;
    }

    public void setHcount3(Double hcount3) {
        this.hcount3 = hcount3;
    }

    public Double getHcount4() {
        return hcount4;
    }

    public void setHcount4(Double hcount4) {
        this.hcount4 = hcount4;
    }

    public String getSIGNER() {
        return SIGNER;
    }

    public void setSIGNER(String SIGNER) {
        this.SIGNER = SIGNER;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }


}
