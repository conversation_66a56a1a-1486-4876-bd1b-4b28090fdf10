package com.huitu.cloud.api.hsfxt.river.entity;

import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqZhuanYiFangAn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 河流树形列表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@ApiModel(value = "RiverTree对象", description = "河流树形列表")
public class RiverTree implements Serializable {
    @ApiModelProperty(value = "行政区编码")
    private String adcd;
    @ApiModelProperty(value = "行政区名称")
    private String adnm;
    @ApiModelProperty(value = "上级编码")
    private String padcd;
    @ApiModelProperty(value = "下级节点集合")
    private List<RiverTree> children;
    @ApiModelProperty(value = "影响屯总数")
    private int childrenCount;
    @ApiModelProperty(value = "查询详情参数组合")
    private String keyindex;
    @ApiModelProperty(value = "转移方案")
    private List<ZhuanYiFangAn> zyfa;

    public List<ZhuanYiFangAn> getZyfa() {
        return zyfa;
    }

    public void setZyfa(List<ZhuanYiFangAn> zyfa) {
        this.zyfa = zyfa;
    }

    public String getKeyindex() {
        return keyindex;
    }

    public void setKeyindex(String keyindex) {
        this.keyindex = keyindex;
    }


    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public List<RiverTree> getChildren() {
        return children;
    }

    public void setChildren(List<RiverTree> children) {
        this.children = children;
    }

    public int getChildrenCount() {
        return childrenCount;
    }

    public void setChildrenCount(int childrenCount) {
        this.childrenCount = childrenCount;
    }
}
