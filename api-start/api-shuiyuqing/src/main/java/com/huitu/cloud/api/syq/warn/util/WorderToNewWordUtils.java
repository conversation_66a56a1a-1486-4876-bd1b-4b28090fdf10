package com.huitu.cloud.api.syq.warn.util;


import org.apache.poi.xwpf.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * 通过word模板生成新的word工具类
 *
 * <AUTHOR>
 *
 */
public class WorderToNewWordUtils {
    /**
     * 根据模板生成新word文档
     * 判断表格是需要替换还是需要插入，判断逻辑有$为替换，表格无$为插入
     * @param inputUrl 模板存放地址
     * @param textMap 需要替换的信息集合
     * @return 成功返回true,失败返回false
     */
    public static String changWord(HttpServletResponse response,String inputUrl, String outputUrl,
                                    Map<String, String> textMap) {

        //模板转换默认成功
        String path = "";
        try {
            ClassPathResource  resource = new ClassPathResource("template/heavy_rainfall_notice.docx");
            String fileName="d:/强降雨通告"+textMap.get("hno")+"期.docx";
            //获取docx解析对象
            //XWPFDocument document = new XWPFDocument(POIXMLDocument.openPackage(sourceFile.getPath()));
            XWPFDocument document = new XWPFDocument(resource.getInputStream());
            //解析替换文本段落对象
            WorderToNewWordUtils.changeText(document, textMap);
            //解析替换表格对象
            WorderToNewWordUtils.changeTable(document, textMap);

            //生成新的word
            File file = new File(fileName);
            FileOutputStream stream = new FileOutputStream(file);
            document.write(stream);
            exportDoc(response,fileName,file);
            path=fileName;
            stream.close();

        } catch (Exception e) {
            e.printStackTrace();
            return path;
        }

        return path;

    }
    public static void exportDoc(HttpServletResponse response, String fileName, File outFile){
        //缓冲输入流
        InputStream inputStream = null;
        //缓冲输出流
        BufferedOutputStream bos = null;
        try {
            String fileName2 = new String(fileName.getBytes("gb2312"), "ISO8859-1");
            //设置字符集编码和响应体类型
//            response.setContentType("application/msword;charset=utf-8");
//            //response.setHeader("Access-Control-Allow-Origin", "*");
//            response.setCharacterEncoding("utf-8");
//            response.setHeader("Content-disposition", "attachment;filename=" +  URLEncoder.encode(fileName, "utf-8"));
            File file = new File(fileName);
            inputStream = new FileInputStream(file);
            //FileCopyUtils.copy(inputStream, response.getOutputStream());
            //file.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                if(inputStream!=null){
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if(bos!=null){
                    bos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    /**
     * 替换段落文本
     * @param document docx解析对象
     * @param textMap 需要替换的信息集合
     */
    public static void changeText(XWPFDocument document, Map<String, String> textMap){
        //获取段落集合
        List<XWPFParagraph> paragraphs = document.getParagraphs();

        for (XWPFParagraph paragraph : paragraphs) {
            //判断此段落时候需要进行替换
            String text = paragraph.getText();
            if(checkText(text)){
                List<XWPFRun> runs = paragraph.getRuns();
                for (XWPFRun run : runs) {
                    //替换模板原来位置
                    run.setText(changeValue(run.toString(), textMap),0);
                }
            }
        }

    }

    /**
     * 替换表格对象方法
     * @param document docx解析对象
     * @param textMap 需要替换的信息集合
     */
    public static void changeTable(XWPFDocument document, Map<String, String> textMap){
        //获取表格对象集合
        List<XWPFTable> tables = document.getTables();
        for (int i = 0; i < tables.size(); i++) {
            //只处理行数大于等于2的表格，且不循环表头
            XWPFTable table = tables.get(i);
            if(table.getRows().size()>=1){
                //判断表格是需要替换还是需要插入，判断逻辑有$为替换，表格无$为插入
                if(checkText(table.getText())){
                    List<XWPFTableRow> rows = table.getRows();
                    //遍历表格,并替换模板
                    eachTable(rows, textMap);
                }
            }
        }
    }





    /**
     * 遍历表格
     * @param rows 表格行对象
     * @param textMap 需要替换的信息集合
     */
    public static void eachTable(List<XWPFTableRow> rows ,Map<String, String> textMap){
        for (XWPFTableRow row : rows) {
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                //判断单元格是否需要替换
                if(checkText(cell.getText())){
                    List<XWPFParagraph> paragraphs = cell.getParagraphs();
                    for (XWPFParagraph paragraph : paragraphs) {
                        List<XWPFRun> runs = paragraph.getRuns();
                        for (XWPFRun run : runs) {
                            run.setText(changeValue(run.toString(), textMap),0);
                        }
                    }
                }
            }
        }
    }

    /**
     * 为表格插入数据，行数不够添加新行
     * @param table 需要插入数据的表格
     * @param tableList 插入数据集合
     */
    public static void insertTable(XWPFTable table, List<String[]> tableList){
        //创建行,根据需要插入的数据添加新行，不处理表头
        for(int i = 1; i < tableList.size(); i++){
            XWPFTableRow row =table.createRow();
        }
        //遍历表格插入数据
        List<XWPFTableRow> rows = table.getRows();
        for(int i = 1; i < rows.size(); i++){
            XWPFTableRow newRow = table.getRow(i);
            List<XWPFTableCell> cells = newRow.getTableCells();
            for(int j = 0; j < cells.size(); j++){
                XWPFTableCell cell = cells.get(j);
                cell.setText(tableList.get(i-1)[j]);
            }
        }

    }



    /**
     * 判断文本中时候包含$
     * @param text 文本
     * @return 包含返回true,不包含返回false
     */
    public static boolean checkText(String text){
        boolean check  =  false;
        if(text.indexOf("$")!= -1){
            check = true;
        }
        return check;

    }

    /**
     * 匹配传入信息集合与模板
     * @param value 模板需要替换的区域
     * @param textMap 传入信息集合
     * @return 模板需要替换区域信息集合对应值
     */
    public static String changeValue(String value, Map<String, String> textMap){
        Set<Entry<String, String>> textSets = textMap.entrySet();
        for (Entry<String, String> textSet : textSets) {
            //匹配模板与替换值 格式${key}
            String key = "${"+textSet.getKey()+"}";
            if(value.indexOf(key)!= -1){
                value = textSet.getValue();
            }
        }
        //模板未匹配到区域替换为空
        if(checkText(value)){
            value = "";
        }
        return value;
    }




    public static void main(String[] args) {
        //模板文件地址
        String inputUrl = "D:\\work\\值班信息第41期.docx";
        //新生产的模板文件
        String outputUrl = "D:\\work\\值班信息第45期.docx";

        Map<String, String> dataMap = new HashMap<String, String>();
        //编号
        dataMap.put("zbno", "51");
        //年份
        dataMap.put("y", "2019");
        //月份
        dataMap.put("m", "7");
        //日
        dataMap.put("d", "28");
        //撰稿人
        dataMap.put("zg", "满一才");
        //签发人
        dataMap.put("qf", "胡毅哥");
        //雨量
        dataMap.put("raindesc", "22日23时至24日20时，全省平均降雨3.2毫米，其中,松原16毫米，长春5.1毫米，公主岭4.2毫米，吉林3.2毫米，白城3.2毫米，延边州2.6毫米，辽源市2.3毫米，四平市2.2毫米，梅河口1.6毫米，其余地区均不足1毫米。降雨量前十位为：乾安县仙字村100.1毫米、前郭县前郭尔罗斯71.1毫米，乾安县鹿场50.6毫米，前郭县韩家店46.7毫米，前郭县白依拉嘎乡45.0毫米，前郭县额如39.5毫米，蛟河市天岗管委会38.9毫米，和龙市大洞34.0毫米，扶余市黄花村31.1毫米");
//        //水情 工情概况
        dataMap.put("sqdesc", "据24日8时统计，全省有1座大型水库超汛限，为亮甲山水库，超汛限水位0.48米；有3座中型水库超汛限，其中，舒兰市太平水库超汛限水位0.28米，榆树市石塘水库超汛限0.15米，九台区牛头山水库超汛限0.12米，均在有序泄洪。\r\n"+"  主要江河水势平稳，各监测断面均在警戒水位以下运行。");
        //工作部署
        dataMap.put("report", "24日下午省水利厅与省应急管理厅、省气象局就新一轮强降雨天气预报进行了视频会商。" +(char)11+
                "   24日18时，省防指总指挥、常务副省长吴靖平组织召开会商会。会上省防指副总指挥、省水利厅厅长韩沐恩介绍了当前全省水情、工情及水旱灾害防御工作开展情况。会后，韩沐恩同志立即组织省水利厅水旱灾害防御领导小组相关成员单位负责同志进行会商研判，并做出进一步安排部署。目前，全省各有关市县相关责任人均已上岗到位。" +(char)11+
                "省水利厅副厅长孙富岭同志在省水利厅值班室调度情况。省水利厅加强值班，密切监测雨情、水情和工程运行情况。");
        dataMap.put("report","23日下午省水利厅与省应急管理厅、省气象局就新一轮强降雨天气预报进行了视频会商。\n" +
                "    23日18时，省防指总指挥、常务副省长吴靖平组织召开会商会。会上省防指副总指挥、省水利厅厅长韩沐恩介绍了当前全省水情、工情及水旱灾害防御工作开展情况。会后，韩沐恩同志立即组织省水利厅水旱灾害防御领导小组相关成员单位负责同志进行会商研判，并做出进一步安排部署。目前，全省各有关市县相关责任人均已上岗到位。\n" +
                "    省水利厅副厅长孙富岭同志在省水利厅值班室调度情况。省水利厅加强值班，密切监测雨情、水情和工程运行情况。");
        String desc="24日下午省水利厅与省应急管理厅、省气象局就新一轮强降雨天气预报进行了视频会商。\n" +
                "    24日18时，省防指总指挥、常务副省长吴靖平组织召开会商会。会上省防指副总指挥、省水利厅厅长韩沐恩介绍了当前全省水情、工情及水旱灾害防御工作开展情况。会后，韩沐恩同志立即组织省水利厅水旱灾害防御领导小组相关成员单位负责同志进行会商研判，并做出进一步安排部署。目前，全省各有关市县相关责任人均已上岗到位。\n" +
                "    省水利厅副厅长孙富岭同志在省水利厅值班室调度情况。省水利厅加强值班，密切监测雨情、水情和工程运行情况。";
        String report =desc.replaceAll("\n","\r\n");
        dataMap.put("report",report);
        //险情
        dataMap.put("zqdesc", "目前，全省水利工程运行良好，无险情、灾情报告。");


       // WorderToNewWordUtils.changWord(inputUrl, outputUrl, dataMap);
    }
}

