<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ast.mapper.AstStationDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getStationList" resultType="com.huitu.cloud.api.ewci.ast.entity.AstStationInfo">
        <if test="map.froms != null and map.froms.size() > 0 and (map.froms.contains('1'.toString()) or map.froms.contains('2'.toString()) or map.froms.contains('3'.toString()) or map.froms.contains('4'.toString()))">
            SELECT A.STCD, STNM, RVNM, HNNM, BSNM, A.LGTD, A<PERSON>LTTD, STLC, ADDVCD, C.ADNM ADDVNM, DTMNM, DTMEL, DTPR, STTP, C.ADCD adcd_order,
            FRGRD, ESSTYM, BGFRYM, ATCUNIT, ADMAUTH, LOCALITY, STBK, STAZT, DSTRVM, DRNA, PHCD, USFL, COMMENTS, B.ADCD,
            CASE WHEN FRGRD = '5' THEN 1 WHEN FRGRD IN ('1', '2', '4', '9') OR (FRGRD = '3' AND STTP IN ('PP', 'ZZ', 'ZQ')) THEN 2 WHEN
            FRGRD = '6' THEN 3 WHEN FRGRD = '8' THEN 4 ELSE 6 END FRGRD_FROM, D.ADNM, B.PLGTD, B.PLTTD
            FROM ST_STBPRP_B A
            INNER JOIN BSN_STADTP_B B ON B.STCD = A.STCD
            LEFT OUTER JOIN MDT_ADCDINFO_B C ON C.ADCD = left(B.ADCD, 6) + '000000000'
            LEFT OUTER JOIN MDT_ADCDINFO_B D ON D.ADCD = B.ADCD
            <where>
                <if test="map.adcd != null and map.adcd !=''">
                    LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
                </if>
                <if test="map.level != null and map.level == '4'.toString()">
                    AND LEFT(B.ADCD, 6) != '220581'
                </if>
                <foreach collection="map.froms" item="from" separator=" OR " open="AND (" close=")">
                    <if test="from == '1'.toString()">
                        FRGRD = '5'
                    </if>
                    <if test="from == '2'.toString()">
                        FRGRD IN ('1', '2', '4', '9') OR (FRGRD = '3' AND STTP IN ('PP', 'ZZ', 'ZQ'))
                    </if>
                    <if test="from == '3'.toString()">
                        FRGRD = '6'
                    </if>
                    <if test="from == '4'.toString()">
                        FRGRD = '8'
                    </if>
                </foreach>
                <if test="map.froms.contains('1'.toString()) or map.froms.contains('2'.toString()) or map.froms.contains('3'.toString())">
                    AND STTP IN (<foreach collection="map.sttps" item="sttp" separator=",">#{sttp}</foreach>)
                </if>
            </where>
        </if>
        <if test="map.sttps != null and map.sttps.size() > 0 and map.sttps.contains('VIDEO'.toString())">
            UNION ALL
            SELECT A.STCD, STNM, RVNM, HNNM, BSNM, A.LGTD, A.LTTD, STLC, ADDVCD, C.ADNM ADDVNM, DTMNM, DTMEL, DTPR, 'VIDEO' STTP,
            C.ADCD adcd_order, FRGRD, ESSTYM, BGFRYM, ATCUNIT, ADMAUTH, LOCALITY, STBK, STAZT, DSTRVM, DRNA, PHCD, USFL, COMMENTS, B.ADCD,
            '5' FRGRD_FROM, D.ADNM, B.PLGTD, B.PLTTD
            FROM ST_STBPRP_B A
            INNER JOIN BSN_VDSTINFO_B E ON E.STCD = A.STCD
            INNER JOIN BSN_STADTP_B B ON B.STCD = A.STCD
            LEFT OUTER JOIN MDT_ADCDINFO_B C ON C.ADCD = left(B.ADCD, 6) + '000000000'
            LEFT OUTER JOIN MDT_ADCDINFO_B D ON D.ADCD = B.ADCD
            <where>
                <if test="map.adcd != null and map.adcd !=''">
                    LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
                </if>
                <if test="map.level != null and map.level == '4'.toString()">
                    AND LEFT(B.ADCD, 6) != '220581'
                </if>
            </where>
        </if>
        ORDER BY adcd_order, FRGRD_FROM ASC
    </select>
    <select id="getStcdTreeListByBscd" resultType="com.huitu.cloud.api.ewci.bia.entity.BsnBasStBTos">
        WITH SUBQRY(BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE) AS (
        　　SELECT BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE FROM BSN_BAS_B
        <if test="bscd != null and bscd !=''">
            WHERE BAS_CODE = #{bscd}
        </if>
        <if test="bscd == null or bscd ==''">
            WHERE PBAS_CODE = ''
        </if>
        　　UNION ALL
        　　SELECT BSN_BAS_B.BAS_CODE,BSN_BAS_B.BAS_NAME,BSN_BAS_B.BAS_LEVEL,BSN_BAS_B.PBAS_CODE FROM BSN_BAS_B,SUBQRY
        WHERE BSN_BAS_B.PBAS_CODE = SUBQRY.BAS_CODE
        )
        SELECT A.BAS_CODE,A.BAS_NAME,A.BAS_LEVEL,A.PBAS_CODE, B.STCD FROM SUBQRY A INNER JOIN BSN_BAS_ST B
        ON A.BAS_CODE = B.BAS_CODE
    </select>
    <select id="getStationInfo" resultType="com.huitu.cloud.api.ewci.ast.entity.AstStationInfo">
        SELECT A.STCD,
               STNM,
               RVNM,
               HNNM,
               BSNM,
               A.LGTD,
               A.LTTD,
               STLC,
               ADDVCD,
               dbo.fnGetAdnmPath(ADDVCD, 1, ' / ') ADDVNM,
               DTMNM,
               DTMEL,
               DTPR,
               STTP,
               FRGRD,
               ESSTYM,
               BGFRYM,
               ATCUNIT,
               ADMAUTH,
               LOCALITY,
               STBK,
               STAZT,
               DSTRVM,
               DRNA,
               PHCD,
               USFL,
               COMMENTS,
               B.ADCD,
               dbo.fnGetAdnmPath(B.ADCD, 1, ' / ') ADNM,
               B.PLGTD,
               B.PLTTD
        FROM ST_STBPRP_B A
                 INNER JOIN BSN_STADTP_B B ON B.STCD = A.STCD
        WHERE A.STCD = #{stcd}
    </select>
    <update id="updateStationCoordinates">
        <foreach collection="coordinates" item="coordinate" separator=";">
            UPDATE ST_STBPRP_B
            SET LGTD = #{coordinate.lgtd}, LTTD = #{coordinate.lttd}
            WHERE STCD = #{coordinate.stcd};
            UPDATE BSN_STADTP_B
            SET PLGTD = #{coordinate.lgtd}, PLTTD = #{coordinate.lttd}
            WHERE STCD = #{coordinate.stcd}
        </foreach>
    </update>
    <update id="updateStationBase">
        UPDATE ST_STBPRP_B
        SET STNM=#{stnm},
            RVNM=#{rvnm},
            HNNM=#{hnnm},
            BSNM=#{bsnm},
            LGTD=#{lgtd},
            LTTD=#{lttd},
            STLC=#{stlc},
            ADDVCD=#{addvcd},
            DTMNM=#{dtmnm},
            DTMEL=#{dtmel},
            DTPR=#{dtpr},
            STTP=#{sttp},
            FRGRD=#{frgrd},
            ESSTYM=#{esstym},
            BGFRYM=#{bgfrym},
            ATCUNIT=#{atcunit},
            ADMAUTH=#{admauth},
            LOCALITY=#{locality},
            STBK=#{stbk},
            STAZT=#{stazt},
            DSTRVM=#{dstrvm},
            DRNA=#{drna},
            PHCD=#{phcd},
            USFL=#{usfl},
            COMMENTS=#{comments},
            MODITIME=GETDATE()
        WHERE STCD = #{stcd}
    </update>
    <update id="updateStationExtend">
        UPDATE BSN_STADTP_B
        SET ADCD=#{adcd},
            PLGTD=#{lgtd},
            PLTTD=#{lttd},
            UPDATETM=GETDATE()
        WHERE STCD = #{stcd}
    </update>
    <select id="getPictureList" resultType="com.huitu.cloud.api.ewci.ast.entity.AstPictureInfo">
        SELECT ID, KEYID, FILENAME, FILESIZE, FILEPATH
        FROM BSN_STFILE_B
        WHERE KEYID = #{stcd}
          AND TYPE = '测站信息'
        ORDER BY ID ASC
    </select>
    <select id="getMaxPictureId" resultType="java.lang.Integer">
        SELECT ISNULL(MAX(ID), 0)
        FROM BSN_STFILE_B
    </select>
    <delete id="deletePictures">
        DELETE
        FROM BSN_STFILE_B
        WHERE KEYID = #{stcd}
          AND TYPE = '测站信息'
    </delete>
    <insert id="insertPictures">
        INSERT INTO BSN_STFILE_B (ID, KEYID, FILENAME, FILESIZE, FILEPATH, TYPE) VALUES
        <foreach collection="pictures" item="picture" index="index" separator=", ">
            (#{picture.id}, #{picture.keyid}, #{picture.filename}, #{picture.filesize}, #{picture.filepath}, '测站信息')
        </foreach>
    </insert>
</mapper>
