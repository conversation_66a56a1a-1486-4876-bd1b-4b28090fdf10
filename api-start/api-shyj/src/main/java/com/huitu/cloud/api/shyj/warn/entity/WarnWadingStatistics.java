package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2019-09-17
 */
@ApiModel(value="预警关联涉水工程统计信息", description="预警关联涉水工程统计信息")
public class WarnWadingStatistics implements Serializable {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "水库总数")
    private String rsst;
    @ApiModelProperty(value = "水闸总数")
    private String slust;
    @ApiModelProperty(value = "堤防总数")
    private String dikst;
    @ApiModelProperty(value = "塘坝总数")
    private String damst;
    @ApiModelProperty(value = "路涵总数")
    private String culst;
    @ApiModelProperty(value = "桥梁总数")
    private String brist;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getRsst() {
        return rsst;
    }

    public void setRsst(String rsst) {
        this.rsst = rsst;
    }

    public String getSlust() {
        return slust;
    }

    public void setSlust(String slust) {
        this.slust = slust;
    }

    public String getDikst() {
        return dikst;
    }

    public void setDikst(String dikst) {
        this.dikst = dikst;
    }

    public String getDamst() {
        return damst;
    }

    public void setDamst(String damst) {
        this.damst = damst;
    }

    public String getCulst() {
        return culst;
    }

    public void setCulst(String culst) {
        this.culst = culst;
    }

    public String getBrist() {
        return brist;
    }

    public void setBrist(String brist) {
        this.brist = brist;
    }
}
