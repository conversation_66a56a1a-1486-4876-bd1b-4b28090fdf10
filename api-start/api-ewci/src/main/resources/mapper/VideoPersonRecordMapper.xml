<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.tencent.mapper.VideoPersonRecordDao">

    <!-- 查询用户 部门 部门ID 政区 政区ID -->
    <select id="selectExtInfo" resultType="com.huitu.cloud.api.ewci.tencent.entity.VideoPersonRecord">
        SELECT
            userId as memberId,
            a.deptId as deptid,
            c.deptnm ,
            c.adcd,
            d.adnm
        FROM
            BSN_UGRELATION a
                LEFT JOIN BNS_DEPTINFO_B c ON a.deptid= c.deptid
                LEFT JOIN BSN_ADCD_B D ON c.ADCD= D.ADCD
        WHERE
            userid = #{memberId}
    </select>

</mapper>
