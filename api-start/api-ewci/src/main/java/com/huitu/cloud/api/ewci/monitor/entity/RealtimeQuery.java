package com.huitu.cloud.api.ewci.monitor.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 实时数据查询条件
 *
 * <AUTHOR>
 */
@ApiModel(value = "实时数据查询条件")
public class RealtimeQuery {

    @ApiModelProperty(value = "类型，ad=按行政村，ws=按小流域，空=按当前政区代码")
    @NotBlank(message = "类型不能为空")
    private String type;

    @ApiModelProperty(value = "测站编码")
    @NotBlank(message = "测站编码不能为空")
    @SqlInjection
    private String stcd;

    @ApiModelProperty(value = "行政区划代码")
    @NotBlank(message = "行政区划代码不能为空")
    @Size(min = 15, max = 15, message = "行政区划代码的长度应为15")
    @SqlInjection
    private String adcd;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date etm;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }
}
