package com.huitu.cloud.api.fusion.entity.common;

/**
 * 叫应状态
 */
public enum FusionCallStatus {
    CONFIRMED("1", "已确认"),
    UNCONFIRMED("0", "未确认");

    /**
     * 代码
     */
    private String code;
    /**
     * 别名
     */
    private String alias;

    FusionCallStatus(String code, String alias) {
        this.code = code;
        this.alias = alias;
    }

    public String getCode() {
        return code;
    }

    public String getAlias() {
        return alias;
    }

    /**
     * 判断代码是否相等
     *
     * @param code 代码
     * @return true: 相等, false: 不相等
     */
    public boolean equalsCode(String code) {
        return this.code.equalsIgnoreCase(code);
    }
}
