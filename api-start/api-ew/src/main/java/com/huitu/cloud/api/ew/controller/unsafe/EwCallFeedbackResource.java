package com.huitu.cloud.api.ew.controller.unsafe;

import cn.hutool.core.net.url.UrlBuilder;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ew.entity.base.EwCallFeedback;
import com.huitu.cloud.api.ew.entity.ext.EwWarningFlag;
import com.huitu.cloud.api.ew.entity.request.EwCallFeedbackRequest;
import com.huitu.cloud.api.ew.entity.request.EwFullAdnmRequest;
import com.huitu.cloud.api.ew.entity.response.*;
import com.huitu.cloud.api.ew.exception.WarningException;
import com.huitu.cloud.api.ew.service.EwCallFeedbackService;
import com.huitu.cloud.api.ew.service.EwRiverWarningService;
import com.huitu.cloud.api.ew.service.EwRsvrWarningService;
import com.huitu.cloud.api.ew.service.EwWarningService;
import com.huitu.cloud.util.ServletUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 叫应反馈服务
 *
 * <AUTHOR>
 */
@Api(tags = "叫应反馈服务")
@RestController
@RequestMapping("/api/unsafe/ew/call/feedback")
@EnableConfigurationProperties(EwCallFeedbackResource.Properties.class)
public class EwCallFeedbackResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "53fa9551-4bec-f62c-7006-f6a17857f24b";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private Properties properties;
    private EwCallFeedbackService feedbackService;
    private EwWarningService shWarningService;
    private EwRsvrWarningService rsvrWarningService;
    private EwRiverWarningService riverWarningService;

    @Autowired
    public void setProperties(Properties properties) {
        this.properties = properties;
    }

    @Autowired
    public void setFeedbackService(EwCallFeedbackService feedbackService) {
        this.feedbackService = feedbackService;
    }

    @Autowired
    public void setShWarningService(EwWarningService shWarningService) {
        this.shWarningService = shWarningService;
    }

    @Autowired
    public void setRsvrWarningService(EwRsvrWarningService rsvrWarningService) {
        this.rsvrWarningService = rsvrWarningService;
    }

    @Autowired
    public void setRiverWarningService(EwRiverWarningService riverWarningService) {
        this.riverWarningService = riverWarningService;
    }

    /**
     * 反馈地址重定向
     *
     * @param callId 叫应标识
     **/
    @GetMapping("/{callId}")
    public void redirect(HttpServletResponse response, @PathVariable("callId") String callId) {
        if (StringUtils.isBlank(properties.getUrl())) {
            ServletUtils.renderString(response, HttpStatus.INTERNAL_SERVER_ERROR, "服务器重要参数缺失，请联系管理员");
        } else if (StringUtils.isBlank(callId)) {
            ServletUtils.renderString(response, HttpStatus.BAD_REQUEST, "参数[叫应标识]不能为空");
        } else {
            EwCallFeedback feedback = feedbackService.getFeedback(callId);
            if (null == feedback) {
                ServletUtils.renderString(response, HttpStatus.NOT_FOUND, "目标地址无效，请检查后重试");
            } else {
                try {
                    if ("0".equals(feedback.getStatus())) {
                        feedbackService.confirm(new EwCallFeedbackRequest(callId, properties.getExpired()));
                    }

                    String targetPath = UrlBuilder.of(properties.getUrl())
                            .addQuery("callId", feedback.getCallId())
                            .addQuery("callType", feedback.getCallType())
                            .addQuery("msgId", feedback.getSmsWarnMsgId())
                            .addQuery("phoneNo", feedback.getPhoneNo()).build();
                    ServletUtils.redirect(response, targetPath);
                } catch (Exception e) {
                    ServletUtils.renderString(response, HttpStatus.INTERNAL_SERVER_ERROR, "服务器内部错误，请联系管理员");
                }
            }
        }
    }

    @ApiOperation(value = "获取预警叫应反馈状态", notes = "作者：曹宝金")
    @GetMapping("/status/{callId}")
    public ResponseEntity<SuccessResponse<String>> getStatus(@PathVariable("callId") String callId) {
        String status = "0";
        try {
            EwCallFeedback feedback = feedbackService.getFeedback(callId);
            if (null != feedback) {
                status = feedback.getStatus();
            }
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", status));
    }

    @ApiOperation(value = "预警叫应反馈", notes = "作者：曹宝金")
    @PostMapping("/confirm")
    public ResponseEntity<SuccessResponse<Integer>> confirm(@RequestBody @Validated EwCallFeedbackRequest request) {
        try {
            request.setExpired(properties.getExpired());
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", feedbackService.confirm(request)));
        } catch (Exception ex) {
            throw new WarningException("预警叫应反馈失败", ex);
        }
    }

    @ApiOperation(value = "获取山洪预警记录", notes = "作者：曹宝金")
    @GetMapping("/sh/record/{msgId}")
    public ResponseEntity<SuccessResponse<EwWarningRecordResponse>> getShRecord(@PathVariable("msgId") String msgId) {
        Assert.hasText(msgId, "参数[消息ID]不能为空");
        try {
            EwWarningFlag flag = shWarningService.getWarningFlag(msgId);
            if (null == flag) {
                throw new WarningException("参数[消息ID]的值无效");
            }
            EwWarningRecordResponse record = shWarningService.getRecord(flag.getWarnId(), flag.getWarnGradeId());
            if (null != record && "1".equals(record.getWarnMode())) {
                String fulladnm = shWarningService.getFullAdnm(new EwFullAdnmRequest(record.getAdcd(), 4));
                if (StringUtils.isNotBlank(fulladnm)) {
                    record.setAdnm(fulladnm);
                }
            }
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", record));
        } catch (WarningException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("获取山洪预警记录失败", ex);
        }
    }

    // 预警降雨过程

    @ApiOperation(value = "获取山洪预警监测数据", notes = "作者：曹宝金")
    @GetMapping("/sh/monitor/{msgId}")
    public ResponseEntity<SuccessResponse<EwShMonitorDataResponse>> getShMonitorData(@PathVariable("msgId") String msgId) {
        Assert.hasText(msgId, "参数[消息ID]不能为空");
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", shWarningService.getMonitorData(msgId)));
        } catch (Exception ex) {
            throw new WarningException("获取山洪预警监测数据失败", ex);
        }
    }

    @ApiOperation(value = "获取山洪预警影响村屯", notes = "作者：曹宝金")
    @GetMapping("/sh/village-list/{msgId}")
    public ResponseEntity<SuccessResponse<List<EwShVillageDataResponse>>> getShVillageList(@PathVariable("msgId") String msgId) {
        Assert.hasText(msgId, "参数[消息ID]不能为空");
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", shWarningService.getVillageList(msgId)));
        } catch (Exception ex) {
            throw new WarningException("获取山洪预警影响村屯失败", ex);
        }
    }

    @ApiOperation(value = "获取山洪预警降雨过程", notes = "作者：曹宝金")
    @GetMapping("/sh/pptn-list/{msgId}")
    public ResponseEntity<SuccessResponse<List<EwShPptnDataResponse>>> getShPptnList(@PathVariable("msgId") String msgId) {
        Assert.hasText(msgId, "参数[消息ID]不能为空");
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", shWarningService.getPptnList(msgId)));
        } catch (Exception ex) {
            throw new WarningException("获取山洪预警降雨过程失败", ex);
        }
    }

    @ApiOperation(value = "获取水库预警记录", notes = "作者：曹宝金")
    @GetMapping("/rsvr/record/{msgId}")
    public ResponseEntity<SuccessResponse<EwRsvrWarningRecordResponse>> getRsvrRecord(@PathVariable("msgId") String msgId) {
        Assert.hasText(msgId, "参数[消息ID]不能为空");
        try {
            String warnId = rsvrWarningService.getWarningFlag(msgId);
            if (StringUtils.isBlank(warnId)) {
                throw new WarningException("参数[消息ID]的值无效");
            }
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", rsvrWarningService.getRecord(warnId)));
        } catch (WarningException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("获取水库预警记录失败", ex);
        }
    }

    @ApiOperation(value = "获取河道预警记录", notes = "作者：曹宝金")
    @GetMapping("/river/record/{msgId}")
    public ResponseEntity<SuccessResponse<EwRiverWarningRecordResponse>> getRiverRecord(@PathVariable("msgId") String msgId) {
        Assert.hasText(msgId, "参数[消息ID]不能为空");
        try {
            String warnId = riverWarningService.getWarningFlag(msgId);
            if (StringUtils.isBlank(warnId)) {
                throw new WarningException("参数[消息ID]的值无效");
            }
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", riverWarningService.getRecord(warnId)));
        } catch (WarningException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("获取河道预警记录失败", ex);
        }
    }

    @ConfigurationProperties(prefix = Properties.PREFIX, ignoreInvalidFields = true)
    public static class Properties {
        public static final String PREFIX = "huitu.call-feedback";

        /**
         * 叫应反馈确认页面地址，注：重定向的地址
         */
        private String url;
        /**
         * 叫应反馈有效期，为0时，忽略，单位：分钟，默认：120
         */
        private int expired = 120;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public int getExpired() {
            return expired;
        }

        public void setExpired(int expired) {
            this.expired = expired;
        }
    }
}
