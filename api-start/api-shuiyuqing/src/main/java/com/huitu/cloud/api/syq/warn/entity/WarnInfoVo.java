package com.huitu.cloud.api.syq.warn.entity;

import com.huitu.cloud.api.syq.rain.entity.BsnRainAlarm;
import com.huitu.cloud.api.syq.river.entity.StRiverVo;
import com.huitu.cloud.api.syq.rsvr.entity.StRsvrVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel
public class WarnInfoVo {
    @ApiModelProperty(value = "水库超汛限告警总数量")
    private int skTotal;
    @ApiModelProperty(value = "大型水库超汛限总数")
    private int bigRsvrCount;
    @ApiModelProperty(value = "中型水库超汛限总数")
    private int middleRsvrCount;
    @ApiModelProperty(value = "小型水库超汛限总数")
    private int smallRsvrCount;
    @ApiModelProperty(value = "大型水库超汛限集合")
    private List<StRsvrVo> bigList;
    @ApiModelProperty(value = "中型水库超汛限集合")
    private List<StRsvrVo> middleList;
    @ApiModelProperty(value = "小型水库超汛限集合")
    private List<StRsvrVo> smallList;
    @ApiModelProperty(value = "河道超警戒总数量")
    private int hdTotal;
    @ApiModelProperty(value = "河道超警戒集合")
    List<StRiverVo> riverList;
    @ApiModelProperty(value = "雨量告警总数量")
    private int ylTotal;
    @ApiModelProperty(value = "雨量超警戒数量")
    private int ylCjTotal;
    @ApiModelProperty(value = "雨量超危险数量")
    private int ylCwTotal;
    @ApiModelProperty(value = "雨量超警戒集合")
    private List<BsnRainAlarm> ylCjList;
    @ApiModelProperty(value = "雨量超危险集合")
    private List<BsnRainAlarm>  ylCwList;

    public int getSkTotal() {
        return skTotal;
    }

    public void setSkTotal(int skTotal) {
        this.skTotal = skTotal;
    }

    public int getBigRsvrCount() {
        return bigRsvrCount;
    }

    public void setBigRsvrCount(int bigRsvrCount) {
        this.bigRsvrCount = bigRsvrCount;
    }

    public int getMiddleRsvrCount() {
        return middleRsvrCount;
    }

    public void setMiddleRsvrCount(int middleRsvrCount) {
        this.middleRsvrCount = middleRsvrCount;
    }

    public int getSmallRsvrCount() {
        return smallRsvrCount;
    }

    public void setSmallRsvrCount(int smallRsvrCount) {
        this.smallRsvrCount = smallRsvrCount;
    }

    public List<StRsvrVo> getBigList() {
        return bigList;
    }

    public void setBigList(List<StRsvrVo> bigList) {
        this.bigList = bigList;
    }

    public List<StRsvrVo> getMiddleList() {
        return middleList;
    }

    public void setMiddleList(List<StRsvrVo> middleList) {
        this.middleList = middleList;
    }

    public List<StRsvrVo> getSmallList() {
        return smallList;
    }

    public void setSmallList(List<StRsvrVo> smallList) {
        this.smallList = smallList;
    }

    public int getHdTotal() {
        return hdTotal;
    }

    public void setHdTotal(int hdTotal) {
        this.hdTotal = hdTotal;
    }

    public List<StRiverVo> getRiverList() {
        return riverList;
    }

    public void setRiverList(List<StRiverVo> riverList) {
        this.riverList = riverList;
    }

    public int getYlTotal() {
        return ylTotal;
    }

    public void setYlTotal(int ylTotal) {
        this.ylTotal = ylTotal;
    }

    public int getYlCjTotal() {
        return ylCjTotal;
    }

    public void setYlCjTotal(int ylCjTotal) {
        this.ylCjTotal = ylCjTotal;
    }

    public int getYlCwTotal() {
        return ylCwTotal;
    }

    public void setYlCwTotal(int ylCwTotal) {
        this.ylCwTotal = ylCwTotal;
    }

    public List<BsnRainAlarm> getYlCjList() {
        return ylCjList;
    }

    public void setYlCjList(List<BsnRainAlarm> ylCjList) {
        this.ylCjList = ylCjList;
    }

    public List<BsnRainAlarm> getYlCwList() {
        return ylCwList;
    }

    public void setYlCwList(List<BsnRainAlarm> ylCwList) {
        this.ylCwList = ylCwList;
    }
}
