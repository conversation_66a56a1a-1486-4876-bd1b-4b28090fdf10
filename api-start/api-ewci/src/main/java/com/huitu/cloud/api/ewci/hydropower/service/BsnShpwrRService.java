package com.huitu.cloud.api.ewci.hydropower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.ewci.hydropower.entity.BsnShpwrR;
import com.huitu.cloud.api.ewci.hydropower.entity.ShpwrRVo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-08
 */
public interface BsnShpwrRService extends IService<BsnShpwrR> {

    /**
     * 获取小水电生态放流监测列表信息
     * @param adcd
     * @param stnm
     * @param pageNum
     * @param pageSize
     * @return
     */
    IPage<ShpwrRVo> selectByPage(String adcd, String stnm, int pageNum, int pageSize);
}
