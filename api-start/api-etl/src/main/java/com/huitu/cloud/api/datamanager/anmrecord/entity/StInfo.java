package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class StInfo implements Serializable {
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "站类")
    private String sttp;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "测站基础类型")
    private String stadtp;

    @ApiModelProperty(value = "测站基础类型名称")
    private String stadtpnm;

    @ApiModelProperty(value = "县级政区编码")
    private String xadcd;

    @ApiModelProperty(value = "县级政区名称")
    private String xadnm;

    @ApiModelProperty(value = "测站联系人")
    private String  officer;

    @ApiModelProperty(value = "移动电话号码")
    private String  mphone;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getStadtp() {
        return stadtp;
    }

    public void setStadtp(String stadtp) {
        this.stadtp = stadtp;
    }

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getOfficer() {
        return officer;
    }

    public void setOfficer(String officer) {
        this.officer = officer;
    }

    public String getMphone() {
        return mphone;
    }

    public void setMphone(String mphone) {
        this.mphone = mphone;
    }
}
