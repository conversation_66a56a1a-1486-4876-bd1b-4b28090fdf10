package com.huitu.cloud.api.syq.rain.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.geotools.data.shapefile.ShapefileDataStore;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Polygon;
import org.opengis.feature.simple.SimpleFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.HashMap;
import java.util.Map;

@Component
public class RainHelper {
    private static final Logger logger = LoggerFactory.getLogger(RainHelper.class);

    public static final String MYAV_YEAR = "8888";
    public static final String MYAV_KEY = "myav";

    private static final double EARTH_RADIUS = 6378.137;

    public static final int KIND_CNTY = 1;
    public static final int KIND_BAS = 2;

    private int kind;

    private String cntyShpFile;
    private ShapefileDataStore cntyDataStore;
    private SimpleFeatureCollection cntyFeatureCollection;

    private String basShpFile;
    private ShapefileDataStore basDataStore;
    private SimpleFeatureCollection basFeatureCollection;


    public RainHelper() {
    }

    public RainHelper(String shpFile) {
        this.kind = RainHelper.KIND_CNTY;
        this.cntyShpFile = shpFile;
    }

    public RainHelper(String shpFile, int kind) {
        this.kind = kind;
        if (this.kind == RainHelper.KIND_BAS) {
            this.basShpFile = shpFile;
        } else {
            this.cntyShpFile = shpFile;
        }
    }

    /**
     * 加载县级政区数据
     *
     * @return
     */
    private SimpleFeatureCollection loadCntyFeatureCollection() {
        if (cntyFeatureCollection == null) {
            if (cntyDataStore != null) {
                try {
                    cntyDataStore.dispose();
                } catch (Exception ex) {
                    logger.error("雨情笼罩面降雨 RainHelper：loadCntyFeatureCollection", ex);
                }
            }

            try {
                File file = new File(this.cntyShpFile);
                cntyDataStore = new ShapefileDataStore(file.toURI().toURL());

                SimpleFeatureSource featureSource = cntyDataStore.getFeatureSource();
                cntyFeatureCollection = featureSource.getFeatures();
            } catch (Exception ex) {
                logger.error("雨情笼罩面降雨 RainHelper：loadCntyFeatureCollection", ex);
            }
        }
        return cntyFeatureCollection;
    }

    /**
     * 加载流域数据
     *
     * @return
     */
    private SimpleFeatureCollection loadBasFeatureCollection() {
        if (basFeatureCollection == null) {
            if (basDataStore != null) {
                try {
                    basDataStore.dispose();
                } catch (Exception ex) {
                    logger.error("雨情笼罩面降雨 RainHelper：loadBasFeatureCollection", ex);
                }
            }

            try {
                File file = new File(this.basShpFile);
                basDataStore = new ShapefileDataStore(file.toURI().toURL());

                SimpleFeatureSource featureSource = basDataStore.getFeatureSource();
                basFeatureCollection = featureSource.getFeatures();
            } catch (Exception ex) {
                logger.error("雨情笼罩面降雨 RainHelper：loadBasFeatureCollection", ex);
            }
        }
        return basFeatureCollection;
    }


    /**
     * 加载雨量等值分析json数据
     *
     * @return
     */
    private String loadRainJson(String alyResult) {
        /*String sbRain = "";
        try {
            sbRain = isoService.analyse(query);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }*/
        /*try (BufferedReader br = new BufferedReader(new FileReader("E:\\workspace\\huitu\\sy-test\\src\\main\\resources\\jilin_geojson.json"))) {
            String line;
            while ((line = br.readLine()) != null) {
                sbRain.append(line);
            }
        } catch (Exception ex) {
        }*/
        return this.fixRainJson(alyResult);
    }


    /**
     * 调用等值分析服务获取json数据
     *
     * @return
     */
    private String loadRainFileJson() {
        StringBuffer sbRain = new StringBuffer();
        try (BufferedReader br = new BufferedReader(new FileReader("E:\\workspace\\huitu\\sy-test\\src\\main\\resources\\jilin_geojson.json"))) {
            String line;
            while ((line = br.readLine()) != null) {
                sbRain.append(line);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return this.fixRainJson(sbRain.toString());
    }

    /**
     * 空间数据处理
     *
     * @param json
     * @return
     */
    private String fixRainJson(String json) {
        JSONObject geoJson = JSONObject.parseObject(json);
        JSONArray features = geoJson.getJSONArray("features");
        if (features != null) {
            for (int i = 0; i < features.size(); i++) {
                JSONObject geometry = features.getJSONObject(i).getJSONObject("geometry");
                if (geometry == null) {
                    continue;
                }

                try {
                    JSONArray coordinates = geometry.getJSONArray("coordinates");
                    if ("Polygon".equals(geometry.getString("type"))) {
                        JSONArray polygon = new JSONArray();
                        for (int m = 0; m < coordinates.size(); m++) {
                            JSONArray ring = coordinates.getJSONArray(m);
                            ring = this.fixRainRing(ring);
                            polygon.add(ring);
                        }
                        geometry.put("coordinates", polygon);
                    } else if ("MultiPolygon".equals(geometry.getString("type"))) {
                        JSONArray multiPolygon = new JSONArray();
                        for (int j = 0; j < coordinates.size(); j++) {
                            JSONArray coords = coordinates.getJSONArray(j);
                            if (coords == null || coords.isEmpty()) {
                                continue;
                            }

                            JSONArray polygon = new JSONArray();
                            for (int m = 0; m < coords.size(); m++) {
                                JSONArray ring = coords.getJSONArray(m);
                                ring = this.fixRainRing(ring);
                                polygon.add(ring);
                            }
                            multiPolygon.add(polygon);
                        }
                        geometry.put("coordinates", multiPolygon);
                    }
                } catch (Exception ex) {
                    if (logger.isWarnEnabled()) {
                        logger.warn("雨情笼罩面降雨  RainHelper fixRainJson", ex);
                    }
                }
            }
        }
        return geoJson.toJSONString();
    }

    /**
     * 空间数据处理(抽稀+补端点)
     *
     * @param ring
     * @return
     */
    private JSONArray fixRainRing(JSONArray ring) {
        if (ring == null || ring.isEmpty()) {
            return ring;
        }

        // 取消抽稀
        int step = 1;
        /*int step = ring.size() / this.pointCnt;
        if (step <= 1 || ring.size() / step <= 4) {
            ring.add(ring.get(0));
            return ring;
        }*/

        JSONArray newRing = new JSONArray();
        for (int i = 0; i < ring.size(); i = i + step) {
            newRing.add(ring.get(i));
        }
        JSONArray firstPoint = (JSONArray) newRing.get(0);
        JSONArray lastPoint = (JSONArray) newRing.get(newRing.size() - 1);
        if (firstPoint.getDoubleValue(0) != lastPoint.getDoubleValue(0)
                || firstPoint.getDoubleValue(1) != lastPoint.getDoubleValue(1)) {
            newRing.add(firstPoint);
        }
        return newRing;
    }

    /**
     * 解析雨量等值分析json面积
     *
     * @return
     */
    public Map<String, RainArea> parseRainJsonArea(String alyResult) {
        Map<String, RainArea> rainAreaMap = new HashMap<>();

        try {
            // 单位经纬度面积
            double ll = RainHelper.getDistance(126, 42.5, 127, 42.5);
            double ss = ll * ll;

            SimpleFeatureCollection featureCollection = null;
            // 流域 || 县级政区
            if (this.kind == RainHelper.KIND_BAS) {
                featureCollection = this.loadBasFeatureCollection();
            } else {
                featureCollection = this.loadCntyFeatureCollection();
            }

            // GeometryFactory
            GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory();

            // JSONObject rainJson = JSONObject.parseObject(this.loadRainFileJson());
            JSONObject rainJson = JSONObject.parseObject(this.loadRainJson(alyResult));
            JSONArray rainFeatures = rainJson.getJSONArray("features");
            for (int i = 0; i < rainFeatures.size(); i++) {
                // geometry
                JSONObject geometry = rainFeatures.getJSONObject(i).getJSONObject("geometry");
                if (geometry == null) {
                    continue;
                }

                // accp
                JSONObject propertis = rainFeatures.getJSONObject(i).getJSONObject("properties");
                double minAccp = propertis.getDouble("minvalue");
                if (minAccp < 25) {
                    continue;
                }

                // geometry
                try {
                    Geometry rainGeometry = null;
                    JSONArray coordinates = geometry.getJSONArray("coordinates");
                    if (geometry.getString("type").equals("Polygon")) {
                        JSONArray polygon = coordinates.getJSONArray(0);
                        Coordinate[] rainCoordinates = new Coordinate[polygon.size()];
                        for (int m = 0; m < polygon.size(); m++) {
                            rainCoordinates[m] = new Coordinate(polygon.getJSONArray(m).getDouble(0), polygon.getJSONArray(m).getDouble(1));
                        }
                        rainGeometry = geometryFactory.createPolygon(rainCoordinates);
                    } else if (geometry.getString("type").equals("MultiPolygon")) {
                        Polygon[] rainPolygon = new Polygon[coordinates.size()];
                        for (int j = 0; j < coordinates.size(); j++) {
                            JSONArray polygonShell = coordinates.getJSONArray(j);
                            JSONArray polygon = polygonShell.getJSONArray(0);
                            Coordinate[] rainCoordinates = new Coordinate[polygon.size()];
                            for (int m = 0; m < polygon.size(); m++) {
                                rainCoordinates[m] = new Coordinate(polygon.getJSONArray(m).getDouble(0), polygon.getJSONArray(m).getDouble(1));
                            }
                            rainPolygon[j] = geometryFactory.createPolygon(rainCoordinates);
                        }
                        rainGeometry = geometryFactory.createMultiPolygon(rainPolygon);
                    }
                    if (rainGeometry == null) {
                        continue;
                    }

                    // 相交面积
                    SimpleFeatureIterator cntyIt = featureCollection.features();
                    while (cntyIt.hasNext()) {
                        SimpleFeature cntyFeature = cntyIt.next();
                        Geometry cntyGeometry = (Geometry) cntyFeature.getDefaultGeometry();

                        if (rainGeometry.intersects(cntyGeometry)) {
                            try {
                                Geometry rsltGeometry = rainGeometry.intersection(cntyGeometry);
                                double area = rsltGeometry.getArea() * ss;
                                if (this.kind == RainHelper.KIND_BAS) {
                                    String bascd = cntyFeature.getAttributes().get(2).toString();
                                    this.fillBasArea(rainAreaMap, bascd, area, minAccp);
                                } else {
                                    String adcd = cntyFeature.getAttributes().get(4).toString();
                                    this.fillAdArea(rainAreaMap, adcd, area, minAccp);
                                }
                            } catch (Exception ex) {
                                if (logger.isWarnEnabled()) {
                                    logger.warn("雨情笼罩面降雨  RainHelper parseRainJsonArea = 2", ex);
                                }
                            }
                        }
                    }
                    cntyIt.close();
                } catch (Exception ex) {
                    if (logger.isWarnEnabled()) {
                        logger.warn("雨情笼罩面降雨  RainHelper parseRainJsonArea = 1", ex);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("雨情笼罩面降雨  RainHelper parseRainJsonArea = 0", ex);
        }
        return rainAreaMap;
    }


    /**
     * 累加政区雨量面积数据
     *
     * @param rainAreaMap
     * @param adcd
     * @param area
     * @param accp
     */
    private void fillAdArea(Map<String, RainArea> rainAreaMap, String adcd, double area, double accp) {
        if (StringUtils.isEmpty(adcd) || adcd.length() < 6) {
            return;
        }

        // cnty
        String cntyAdcd = adcd.substring(0, 6);
        this.fillAdArea25(rainAreaMap, cntyAdcd, area, accp);
        this.fillAdArea50(rainAreaMap, cntyAdcd, area, accp);
        this.fillAdArea100(rainAreaMap, cntyAdcd, area, accp);
        // city
        String cityAdcd = adcd.substring(0, 4);
        this.fillAdArea25(rainAreaMap, cityAdcd, area, accp);
        this.fillAdArea50(rainAreaMap, cityAdcd, area, accp);
        this.fillAdArea100(rainAreaMap, cityAdcd, area, accp);
        // prov
        String provAdcd = adcd.substring(0, 2);
        this.fillAdArea25(rainAreaMap, provAdcd, area, accp);
        this.fillAdArea50(rainAreaMap, provAdcd, area, accp);
        this.fillAdArea100(rainAreaMap, provAdcd, area, accp);
    }

    private void fillAdArea25(Map<String, RainArea> rainAreaMap, String adcd, double area, double accp) {
        if (accp < 24.9999 || rainAreaMap == null || StringUtils.isEmpty(adcd)) {
            return;
        }

        RainArea rainArea = rainAreaMap.get(adcd);
        if (rainArea == null) {
            rainArea = new RainArea();
            rainAreaMap.put(adcd, rainArea);
        }
        rainArea.setArea25(rainArea.getArea25() + area);
    }

    private void fillAdArea50(Map<String, RainArea> rainAreaMap, String adcd, double area, double accp) {
        if (accp < 49.9999 || rainAreaMap == null || StringUtils.isEmpty(adcd) || accp < 50) {
            return;
        }

        RainArea rainArea = rainAreaMap.get(adcd);
        if (rainArea == null) {
            rainArea = new RainArea();
            rainAreaMap.put(adcd, rainArea);
        }
        rainArea.setArea50(rainArea.getArea50() + area);
    }

    private void fillAdArea100(Map<String, RainArea> rainAreaMap, String adcd, double area, double accp) {
        if (accp < 99.9999 || rainAreaMap == null || StringUtils.isEmpty(adcd) || accp < 100) {
            return;
        }

        RainArea rainArea = rainAreaMap.get(adcd);
        if (rainArea == null) {
            rainArea = new RainArea();
            rainAreaMap.put(adcd, rainArea);
        }
        rainArea.setArea100(rainArea.getArea100() + area);
    }

    /**
     * 累加流域雨量面积数据
     *
     * @param rainAreaMap
     * @param bascd
     * @param area
     * @param accp
     */
    private void fillBasArea(Map<String, RainArea> rainAreaMap, String bascd, double area, double accp) {
        if (StringUtils.isEmpty(bascd)) {
            return;
        }

        // bas
        this.fillBasArea25(rainAreaMap, bascd, area, accp);
        this.fillBasArea50(rainAreaMap, bascd, area, accp);
        this.fillBasArea100(rainAreaMap, bascd, area, accp);
    }

    private void fillBasArea25(Map<String, RainArea> rainAreaMap, String bascd, double area, double accp) {
        if (accp < 24.9999 || rainAreaMap == null || StringUtils.isEmpty(bascd)) {
            return;
        }

        RainArea rainArea = rainAreaMap.get(bascd);
        if (rainArea == null) {
            rainArea = new RainArea();
            rainAreaMap.put(bascd, rainArea);
        }
        rainArea.setArea25(rainArea.getArea25() + area);
    }

    private void fillBasArea50(Map<String, RainArea> rainAreaMap, String bascd, double area, double accp) {
        if (accp < 49.9999 || rainAreaMap == null || StringUtils.isEmpty(bascd) || accp < 50) {
            return;
        }

        RainArea rainArea = rainAreaMap.get(bascd);
        if (rainArea == null) {
            rainArea = new RainArea();
            rainAreaMap.put(bascd, rainArea);
        }
        rainArea.setArea50(rainArea.getArea50() + area);
    }

    private void fillBasArea100(Map<String, RainArea> rainAreaMap, String bascd, double area, double accp) {
        if (accp < 99.9999 || rainAreaMap == null || StringUtils.isEmpty(bascd) || accp < 100) {
            return;
        }

        RainArea rainArea = rainAreaMap.get(bascd);
        if (rainArea == null) {
            rainArea = new RainArea();
            rainAreaMap.put(bascd, rainArea);
        }
        rainArea.setArea100(rainArea.getArea100() + area);
    }


    /**
     * 计算两个经纬度之间的距离
     *
     * @param lon1 第一个点的经度
     * @param lat1 第一个点的纬度
     * @param lon2 第二个点的经度
     * @param lat2 第二个点的纬度
     * @return 距离（单位：公里）
     */
    public static double getDistance(double lon1, double lat1, double lon2, double lat2) {
        double radLat1 = Math.toRadians(lat1);
        double radLat2 = Math.toRadians(lat2);
        double a = radLat1 - radLat2;
        double b = Math.toRadians(lon1) - Math.toRadians(lon2);

        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = (double) Math.round(s * 10000) / 10000;
        return s;
    }


    @PreDestroy
    private void preDestroy() {
        if (cntyDataStore != null) {
            try {
                cntyDataStore.dispose();
            } catch (Exception ex) {
                logger.error("雨情笼罩面降雨  RainHelper preDestroy ", ex);
            }
        }
    }


    /**
     * 政区不同雨量等级对应面积实体类
     */
    public static class RainArea {
        private double area25;
        private double area50;
        private double area100;

        public RainArea() {
            this.area25 = 0;
            this.area50 = 0;
            this.area100 = 0;
        }

        @Override
        public String toString() {
            JSONObject areaObj = new JSONObject();
            areaObj.put("s25", area25);
            areaObj.put("s50", area50);
            areaObj.put("s100", area100);
            return areaObj.toJSONString();
        }

        public double getArea25() {
            return area25;
        }

        public void setArea25(double area25) {
            this.area25 = area25;
        }

        public double getArea50() {
            return area50;
        }

        public void setArea50(double area50) {
            this.area50 = area50;
        }

        public double getArea100() {
            return area100;
        }

        public void setArea100(double area100) {
            this.area100 = area100;
        }
    }

}
