package com.huitu.cloud.api.fusion.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import static com.huitu.cloud.api.fusion.entity.common.FusionConfirmResult.MANUAL;

/**
 * 山洪雨量升级预警-预警发送情况响应对象
 */
@ApiModel(value = "山洪雨量升级预警-预警发送情况响应对象")
@Data
public class FusionWarningSendingResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "叫应ID")
    @TableField(value = "CALL_ID")
    private String callId;

    @ApiModelProperty(value = "预警ID")
    private String warnId;

    @ApiModelProperty(value = "接收人电话")
    private String phoneNo;

    @ApiModelProperty(value = "接收人姓名")
    private String receiverName;

    @ApiModelProperty(value = "接收人职务")
    private String position;

    @ApiModelProperty(value = "接收人类型：A=县级领导、B=水旱灾害防御工作人员、1=县级责任人、2=乡镇责任人、3=行政村责任人、4=自然村责任人、5=监测责任人、6=预警责任人、7=转移责任人")
    private String receiverType;

    @ApiModelProperty(value = "短信发送状态：0=待发送、1=发送成功、2=发送失败、9=处理中")
    private String smsStatus;

    @ApiModelProperty(value = "短信发送失败原因")
    private String smsFailCause;

    @ApiModelProperty(value = "短信发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date smsSendTime;

    @ApiModelProperty(value = "语音呼叫状态：0=待发送、1=发送成功、2=发送失败")
    private String voiceStatus;

    @ApiModelProperty(value = "语音呼叫失败原因")
    private String xccFailCause;

    @ApiModelProperty(value = "语音呼叫外部发送ID")
    @TableField(value = "XCC_EXT_SEND_ID")
    private String xccExtSendId;

    @ApiModelProperty(value = "语音通话结果，注：详见枚举值表")
    private Integer xccCallResult;

    @ApiModelProperty(value = "语音通话结果文本")
    @TableField(value = "XCC_CALL_RESULT_TEXT")
    private String xccCallResultText;

    @ApiModelProperty(value = "语音呼叫时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date voiceSendTime;

    @ApiModelProperty(value = "确认结果，注：1=正常、2=超时、3=语音")
    @TableField(value = "CONFIRM_RESULT")
    private String confirmResult;

    @ApiModelProperty(value = "确认时间")
    @TableField(value = "CONFIRM_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    @ApiModelProperty(value = "叫应反馈状态，注：0=未读、1=已读")
    @TableField(value = "FEEDBACK_STATUS")
    private String feedbackStatus;

    @ApiModelProperty(value = "人工通知状态，注：0=未通知、1=已通知")
    public String getManualStatus() {
        return MANUAL.equalsCode(getConfirmResult()) ? "1" : null;
    }

    @ApiModelProperty(value = "人工通知时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date getManualTime() {
        return MANUAL.equalsCode(getConfirmResult()) ? getConfirmTime() : null;
    }

}
