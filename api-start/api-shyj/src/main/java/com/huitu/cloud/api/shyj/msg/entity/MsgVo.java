package com.huitu.cloud.api.shyj.msg.entity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 预警短信发送记录统计
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
public class MsgVo implements Serializable {
    @ApiModelProperty(value = " 政区编码 ")
    private String adcd;
    @ApiModelProperty(value = " 所属政区 ")
    private String adnm;
    @ApiModelProperty(value = " 层级 ")
    private String adlvl;
    @ApiModelProperty(value = " 发送失败 ")
    private int failed;
    @ApiModelProperty(value = " 发送成功 ")
    private int success;
    @ApiModelProperty(value = " 下级城市集合 ")
    private List<MsgVo> list;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdlvl() {
        return adlvl;
    }

    public void setAdlvl(String adlvl) {
        this.adlvl = adlvl;
    }

    public List<MsgVo> getList() {
        return list;
    }

    public void setList(List<MsgVo> list) {
        this.list = list;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public int getFailed() {
        return failed;
    }

    public void setFailed(int failed) {
        this.failed = failed;
    }

    public int getSuccess() {
        return success;
    }

    public void setSuccess(int success) {
        this.success = success;
    }
}
