package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
/**
 * <AUTHOR>
 */
/**
 * <AUTHOR>
 */
public class QueryRainWarn extends PageBean {
    @ApiModelProperty(value = "政区编码",required = true)
    private String adcd;
    @ApiModelProperty(value = "流域编码")
    private String bscd;
    @ApiModelProperty(value = "流域名称")
    private String bsnm;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "测站归属类型 1、水文 2、山洪 3、气象")
    private List<String> stType;
    @ApiModelProperty(value = "告警类型 1、超警 2、超危 多值逗号隔开 ")
    private String  warnType;
    @ApiModelProperty(value = "预警时段(h)")
    private String stdt;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public String getWarnType() {
        return warnType;
    }

    public void setWarnType(String warnType) {
        this.warnType = warnType;
    }

    public String getStdt() {
        return stdt;
    }

    public void setStdt(String stdt) {
        this.stdt = stdt;
    }
}
