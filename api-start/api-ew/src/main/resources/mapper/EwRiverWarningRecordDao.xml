<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwRiverWarningRecordDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ew.entity.response.EwRiverWarningRecordResponse" useCache="false">
        SELECT A.WARN_ID, E.ADCD XADCD, E.ADNM XADNM, C.ADCD, D.ADNM, A.STCD, B.STNM, B.RVNM, A.WARN_TIME, A.WARN_DESC, A.Z,
        A.WARN_GRADE_ID, A<PERSON>RN_INDEX, A.EXCEED_WARN_INDEX, A<PERSON>[STATE], C.PLGTD LGTD, C.PLTTD LTTD, A.LATEST_TIME
        FROM EW_RIVER_WARNING_RECORD A
        LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
        WHERE LEFT(C.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">AND LEFT(C.ADCD, 6) NOT IN ('220581')</if>
        <if test="map.bscd != null and map.bscd != ''">
            AND EXISTS(SELECT 8 FROM BSN_BAS_ST WHERE STCD = A.STCD AND BAS_CODE = #{map.bscd})
        </if>
        AND A.WARN_TIME >= #{map.stm}
        <if test="map.etm != null">AND A.WARN_TIME &lt;= #{map.etm}</if>
        <if test="map.stadtps != null and map.stadtps != ''">AND CHARINDEX(C.STADTP, #{map.stadtps}) > 0</if>
        <if test="map.states != null and map.states != ''">AND CHARINDEX(A.[STATE], #{map.states}) > 0</if>
        ORDER BY A.WARN_TIME DESC, A.WARN_GRADE_ID ASC
    </select>
    <select id="getRecord" resultType="com.huitu.cloud.api.ew.entity.response.EwRiverWarningRecordResponse">
        SELECT A.WARN_ID,
               E.ADCD  XADCD,
               E.ADNM  XADNM,
               C.ADCD,
               D.ADNM,
               A.STCD,
               B.STNM,
               B.RVNM,
               A.WARN_TIME,
               A.WARN_DESC,
               A.Z,
               A.WARN_GRADE_ID,
               A.WARN_INDEX,
               A.EXCEED_WARN_INDEX,
               A.[STATE],
               C.PLGTD LGTD,
               C.PLTTD LTTD,
               A.LATEST_TIME
        FROM EW_RIVER_WARNING_RECORD A
                 LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
                 LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
                 LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
                 LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
        WHERE A.WARN_ID = #{warnId}
    </select>
    <select id="getRiverWarningGradeSummary"
            resultType="com.huitu.cloud.api.ew.entity.response.EwWarningGradeSummaryResponse">
        SELECT A.ADCD, A.ADNM, ISNULL(C.ONE, 0) ONE, ISNULL(C.TWO, 0) TWO
        FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.ONE) ONE, SUM(T.TWO) TWO FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, A.WARN_ID, ONE ONE,TWO TWO FROM (
        SELECT WARN_ID, B.ADCD, WARN_GRADE_ID, A.WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        <where>
            <if test="map.stadtps != null and map.stadtps != ''">AND CHARINDEX(B.STADTP, #{map.stadtps}) > 0</if>
            <if test="map.states != null and map.states != ''">AND CHARINDEX(A.[STATE], #{map.states}) > 0</if>
        </where>
        ) A
        LEFT JOIN (SELECT WARN_ID, 1 ONE FROM EW_RIVER_WARNING_RECORD WHERE WARN_GRADE_ID = 1 AND [STATE] != '3') B
        ON B.WARN_ID = A.WARN_ID
        LEFT JOIN (SELECT WARN_ID, 1 TWO FROM EW_RIVER_WARNING_RECORD WHERE WARN_GRADE_ID = 2 AND [STATE] != '3') C
        ON C.WARN_ID = A.WARN_ID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, SUM(ONE) ONE, SUM(TWO) TWO FROM (
            SELECT WARN_ID, B.ADCD, WARN_GRADE_ID,A.WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A
            LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
            WHERE LEFT(B.ADCD, 6) = '220581'
            <if test="map.stadtps != null and map.stadtps != ''">AND CHARINDEX(B.STADTP, #{map.stadtps}) > 0</if>
            <if test="map.states != null and map.states != ''">AND CHARINDEX(A.[STATE], #{map.states}) > 0</if>
            ) A
            LEFT JOIN (SELECT WARN_ID, 1 ONE FROM EW_RIVER_WARNING_RECORD WHERE WARN_GRADE_ID = 1 AND [STATE] != '3') B
            ON B.WARN_ID = A.WARN_ID
            LEFT JOIN (SELECT WARN_ID, 1 TWO FROM EW_RIVER_WARNING_RECORD WHERE WARN_GRADE_ID = 2 AND [STATE] != '3') C
            ON C.WARN_ID = A.WARN_ID
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getLatestZ" resultType="java.math.BigDecimal" useCache="false">
        SELECT TOP (1) Z
        FROM ST_RIVER_R
        WHERE TM >= DATEADD(HOUR, -24, GETDATE())
          AND STCD = #{stcd}
        ORDER BY TM DESC
    </select>
    <select id="getCorrectMessage" resultType="com.huitu.cloud.api.ew.entity.sms.SmsMessage">
        SELECT NEWID()                                     MSG_ID,
               A.WARN_ID                                   BUSINESS_KEY,
               '0'                                         SMS_TYPE,
               ('adnm=' + LTRIM(RTRIM(D.ADNM)) +
                '&amp;rvnm=' + LTRIM(RTRIM(B.RVNM)) +
                '&amp;stnm=' + LTRIM(RTRIM(B.STNM)) +
                '&amp;z=' + FORMAT(#{map.latestZ}, '###0.##') +
                '&amp;grade=' + (CASE
                                     WHEN WARN_GRADE_ID = 1 THEN '保证水位'
                                     ELSE '警戒水位' END)) SMS_CONTENT
        FROM EW_RIVER_WARNING_RECORD A
                 LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
                 LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
                 LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = LEFT(C.ADCD, 6) + '000000000'
        WHERE A.WARN_ID = #{map.warnId}
    </select>
    <select id="getCorrectReceivers" resultType="com.huitu.cloud.api.ew.entity.sms.SmsReceiver" useCache="false">
        SELECT PHONE_NO, RECEIVER
        FROM SMS_SENDING A
                 LEFT JOIN SMS_MESSAGE B ON B.MSG_ID = A.MSG_ID
        WHERE A.[STATUS] = '1'
          AND EXISTS(SELECT 8
                     FROM EW_RIVER_WARNING_MESSAGE
                     WHERE PUSH_MODE = '1'
                       AND WARN_ID = #{warnId}
                       AND MSG_ID = B.BUSINESS_KEY)
        ORDER BY PHONE_NO ASC
    </select>
    <update id="correct">
        UPDATE EW_RIVER_WARNING_RECORD
        SET [STATE]     = '3',
            LATEST_TIME = GETDATE()
        WHERE [STATE] IN ('0', '1')
          AND WARN_ID = #{map.warnId}
    </update>
</mapper>