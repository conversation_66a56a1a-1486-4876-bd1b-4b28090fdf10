package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 预警反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@TableName("EXAM_CITY_SCORE")
@ApiModel(value="ExamCityScore对象", description="市县满意度得分")
public class ExamCityScore implements Serializable {
    @ApiModelProperty(value = "运营商ID")
    @TableId(value = "YYID", type = IdType.NONE)
    private String yyid;

    @ApiModelProperty(value = "年月")
    @TableField("YM")
    private String ym;

    @ApiModelProperty(value = "得分")
    @TableField("SCORE")
    private double score;

    @ApiModelProperty(value = "最后修改人ID")
    @TableField("OCCURUSER")
    private String occurUser;

    @ApiModelProperty(value = "最后修改时间")
    @TableField("OCCURTM")
    private Date occurTM;

    @ApiModelProperty(value = "UUID版本标识")
    @TableField("VCODE")
    private String vcode;

    public String getYyid() {
        return yyid;
    }
    public void setYyid(String yyid) {
        this.yyid = yyid;
    }

    public String getYm() {
        return ym;
    }
    public void setYm(String ym) {
        this.ym = ym;
    }

    public double getScore() {
        return score;
    }
    public void setScore(double score) {
        this.score = score;
    }

    public String getOccurUser() {
        return occurUser;
    }
    public void setOccurUser(String occurUser) {
        this.occurUser = occurUser;
    }

    public Date getOccurTM() {
        return occurTM;
    }
    public void setOccurTM(Date occurTM) {
        this.occurTM = occurTM;
    }

    public String getVcode() {
        return vcode;
    }
    public void setVcode(String vcode) {
        this.vcode = vcode;
    }
}