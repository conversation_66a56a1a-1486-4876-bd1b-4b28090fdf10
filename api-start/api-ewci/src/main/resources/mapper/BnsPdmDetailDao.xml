<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.pdm.mapper.BnsPdmDetailDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getNewMessageId" resultType="java.lang.Integer">
        SELECT ISNULL(MAX(MSGID), 0) + 1
        FROM MESSAGEINFO_R
        WHERE ADCD = #{adcd}
    </select>
    <select id="getNewSendId" resultType="java.lang.Integer">
        SELECT ISNULL(MAX(SMSID), 0) + 1
        FROM MESSAGESEND_R
        WHERE CODE = #{code}
          AND ADCD = #{adcd}
    </select>

    <insert id="batchInsertDetailList">
        INSERT INTO BNS_PDM_DETAIL (SID, MID, PERTP, REALNM, MOBILE, TS)
        SELECT SID, MID, PERTP, REALNM, MOBILE, TS FROM (VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.sid}, #{item.mid}, #{item.pertp}, #{item.realnm}, #{item.mobile}, #{item.ts})
        </foreach>) AS T(SID, MID, PERTP, REALNM, MOBILE, TS)
    </insert>

    <insert id="batchInsertMessageList">
        INSERT INTO MESSAGEINFO_R (MSGID, WARNID, MSGTYPEID, MSGCONTENT, SENDER, MEDIAID, SENDTM, SENDTYPE, ADCD)
        SELECT MSGID, WARNID, MSGTYPEID, MSGCONTENT, SENDER, MEDIAID, SENDTM, SENDTYPE, ADCD FROM (VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.msgId}, #{item.warnId}, 0, #{item.msgContent}, #{item.sender}, '10', GETDATE(), 1, #{item.adcd})
        </foreach>) AS T(MSGID, WARNID, MSGTYPEID, MSGCONTENT, SENDER, MEDIAID, SENDTM, SENDTYPE, ADCD)
    </insert>

    <insert id="batchInsertSendList">
        INSERT INTO MESSAGESEND_R (MSGID, CODE, SMSID, [SID], UNAME, OBJECTCD, OBJECTTYPE, SENDRESULT, RECTM, ADCD,
        ISSEND)
        SELECT MSGID, CODE, SMSID, [SID], UNAME, OBJECTCD, OBJECTTYPE, SENDRESULT, RECTM, ADCD, ISSEND FROM (VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.msgId}, #{item.code}, #{item.smsId}, #{item.mobile}, #{item.realnm}, #{item.msgId}, 10, 2,
            GETDATE(), #{item.adcd}, 1)
        </foreach>) AS T(MSGID, CODE, SMSID, [SID], UNAME, OBJECTCD, OBJECTTYPE, SENDRESULT, RECTM, ADCD, ISSEND)
    </insert>

    <select id="getDetailList" resultType="com.huitu.cloud.api.ewci.pdm.entity.BnsPdmDetail">
        SELECT SID, MID, PERTP, REALNM, MOBILE, TS
        FROM BNS_PDM_DETAIL
        WHERE MID = #{mid}
        ORDER BY PERTP ASC, TS ASC
    </select>
</mapper>