package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 自动监测站下拉
 */
@ApiModel(value = "自动监测站下拉")
public class BnsAutoMonitorB implements Serializable {

    @ApiModelProperty(value = "站点编码")
    private String stcd;

    @ApiModelProperty(value = "站点名称")
    private String stnm;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }
}
