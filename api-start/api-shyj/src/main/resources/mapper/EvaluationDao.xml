<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.shyj.evaluation.mapper.EvaluationDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!-- 通用查询结果列 -->
    <sql id="1">
        RACD
        , RADT, LOG_TP, IN_NUM, CONN, SYSTM
    </sql>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RACD
        , RADT, RA_ST, BATV, CHAV, SIG_ST, AMP_ST, SYSTM
    </sql>
    <select id="getWbrinfoList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.Wbrinfo">
        SELECT A.WBRCD, A.ADDRESS, A.WSC<PERSON>, <PERSON>.<PERSON>, A.<PERSON>, A.<PERSON>, A.<PERSON>G<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, A.<PERSON>, A.ST<PERSON>US,
        A.<PERSON>MMENTS, A.MODITIME,A.WBRNM,A.SIMID,A.ADMIN,A.ADMINSIM,A.DEVICEID,ADNM,WSNM,A.DENM
        FROM BSN_WIRE_DEVICE_V a LEFT join IA_C_ADINFO B on A.ADCD=B.ADCD left join
        IA_C_WATA C ON A.WSCD=C.WSCD
        <where>1=1
            <if test="map.ad !=null and map.ad !=''">
                AND left(A.ADCD,#{map.level})=#{map.ad}
            </if>
            <if test="map.key!= null and map.key !=''">
                AND A.WBRCD=#{map.key}
            </if>
            <if test="map.wsnm !=null and map.wsnm !=''">
                and CHARINDEX(#{map.wsnm},WSNM) >0
            </if>
            <if test="map.wscd !=null and map.wscd !=''">
                AND A.WSCD = #{map.wscd}
            </if>
        </where>
        ORDER BY A.WBRCD
    </select>
    <select id="getWirelessInfo" resultType="com.huitu.cloud.api.shyj.evaluation.entity.BsnWirelessinfo">
        select RACD,
               RADT,
               RA_ST,
               BATV,
               CHAV,
               SIG_ST,
               AMP_ST,
               SYSTM
        from BSN_WIRELESSINFO_R
        where RACD = #{racd}
    </select>
    <select id="getWirelessLogInfo" resultType="com.huitu.cloud.api.shyj.evaluation.entity.BsnWirelessinfoLog">
        select RACD, RADT, LOG_TP, IN_NUM, CONN, SYSTM
        from BSN_WIRELESSINFO_LOG
        where 1=1
        <if test="racd != null and racd !=''">
            AND RACD=#{racd}
        </if>
        <if test="type != null and type !=''">
            AND LOG_TP=#{type}
        </if>
        <if test="stm != null and stm !=''">
            AND convert(date,RADT)>=convert(date,#{stm})
        </if>
        <if test="etm != null and etm !=''">
            AND convert(date,RADT)&lt;=convert(date,#{etm})
        </if>
    </select>
    <select id="getDanList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaDanad">
        SELECT A.DAND, A.NAME, A.WSCD, A.ADCD, A.PTCOUNT, ETCOUNT, ECOUNT1, ECOUNT2, ECOUNT3, ECOUNT4, A.HTCOUNT,
        HCOUNT1, HCOUNT2, HCOUNT3, HCOUNT4, A.SIGNER, A.AUDID, A.STATUS, A.COMMENTS,A.MODITIME,ADNM ,WSNM
        FROM IA_C_DANAD A LEFT join IA_C_ADINFO B on A.ADCD=B.ADCD left join IA_C_WATA C ON A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.DAND=#{map.key}
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.name !=null and map.name !=''">
            and CHARINDEX(#{map.name},A.NAME) >0
        </if>
        <if test="map.adnm !=null and map.adnm !=''">
            and CHARINDEX(#{map.adnm},ADNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        <if test="map.prevtp!= null and map.prevtp !=''">
            and CHARINDEX(B.PREVTP,#{map.prevtp}) > 0
        </if>
        <if test="map.level != null and map.level == '4'.toString()">
            and substring(A.ADCD,1,6) not in ( '220581','220381' )
        </if>
        order by DAND
    </select>
    <select id="getHsfwaterList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaHsfwater">
        SELECT A.MTCD, A.WSCD, A.ADCD, A.OTIME, A.ADDRESS, A.LGTD, A.LTTD, A.PFRAIN, A.DPCOUNT, A.MPCOUNT, A.CHCOUNT,
        A.SPCOUNT, A.ELOSE, A.DDSCRIB, A.SIGNER, A.AUDID, A.STATUS, A.COMMENTS, A.MODITIME,ADNM,WSNM
        FROM IA_C_HSFWATER a left join IA_C_ADINFO B on A.ADCD=B.ADCD left join IA_C_WATA C ON A.WSCD=C.WSCD
        WHERE 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.MTCD=#{map.key}
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.address !=null and map.address !=''">
            and CHARINDEX(#{map.address},A.ADDRESS) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        <if test="map.prevtp!= null and map.prevtp !=''">
            and CHARINDEX(B.PREVTP,#{map.prevtp}) > 0
        </if>
        <if test="map.level != null and map.level == '4'.toString()">
            and substring(A.ADCD,1,6) not in ( '220581','220381' )
        </if>
        ORDER BY A.MTCD
    </select>
    <select id="getGullyList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaGully">
        SELECT A.GULLYCD, A.NAME, A.ADCD, A.WSCD, A.CAREA, A.CHLENGTH, A.CHPERCENT, A.FCATION, A.FSTAND, A.DIKELEN,
        A.RTLEN, A.TOWNS, A.XZC, A.ZRC, A.PCOUNT, A.LAND, A.PFCOUNT, A.FCOUNT, A.DCOUNT, A.CPROGRAM, A.SIGNER, A.AUDID,
        A.STATUS, A.COMMENTS, A.MODITIME,
        A.BTLVLWS_1, A.BTLVLWS_2, A.BTLVLWS_3, A.IMPACTHTCOUNT, A.IMPACTHCOUNT, A.PICOFC, A.PICPH, A.PIOHTCOUNT,
        A.PIOHCOUNT, A.PIOHCCOUNT, A.PIOHTCOUNT_2M, A.PIOHCOUNT_2M, A.PIOHCCOUNT_2M,ADNM
        FROM IA_C_GULLY a left join IA_C_ADINFO B on A.ADCD=B.ADCD
        WHERE 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.GULLYCD=#{map.key}
        </if>
        <if test="map.name !=null and map.name !=''">
            and CHARINDEX(#{map.name},A.NAME) >0
        </if>
        <if test="map.adnm !=null and map.adnm !=''">
            and CHARINDEX(#{map.adnm},ADNM) >0
        </if>
        ORDER BY A.GULLYCD
    </select>
    <select id="getGully" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaGully">
        SELECT A.GULLYCD, A.NAME, A.ADCD,LEFT(A.ADCD, 9) + '000000' AS PADCD, A.WSCD, A.CAREA, A.CHLENGTH, A.CHPERCENT,
        A.FCATION, A.FSTAND, A.DIKELEN, A.RTLEN, A.TOWNS, A.XZC, A.ZRC, A.PCOUNT, A.LAND, A.PFCOUNT, A.FCOUNT, A.DCOUNT,
        A.CPROGRAM, A.SIGNER, A.AUDID, A.STATUS, A.COMMENTS, A.MODITIME,
        A.BTLVLWS_1, A.BTLVLWS_2, A.BTLVLWS_3, A.IMPACTHTCOUNT, A.IMPACTHCOUNT, A.PICOFC, A.PICPH, A.PIOHTCOUNT,
        A.PIOHCOUNT, A.PIOHCCOUNT, A.PIOHTCOUNT_2M, A.PIOHCOUNT_2M, A.PIOHCCOUNT_2M,ADNM
        FROM IA_C_GULLY a left join IA_C_ADINFO B on A.ADCD=B.ADCD
        WHERE 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        ORDER BY A.GULLYCD
    </select>
    <!-- 行政区划基本情况  统计 -->
    <select id="getDtAdInfoList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtAdInfo">
        select b.ADCD,c.ADNM,c.PADCD,PTCOUNT,HTCOUNT,PREVCNT,IMPPEVCNT,LDAREA,PLAREA
        from (select a.ADCD,sum(PTCOUNT) PTCOUNT,sum(HTCOUNT) HTCOUNT,sum(PREVCNT) PREVCNT,sum(IMPPEVCNT)
        IMPPEVCNT,sum(LDAREA)LDAREA,sum(PLAREA)PLAREA
        from (select (left(i.CODE,#{level2})+'${zero}') ADCD,i.PTCOUNT,i.HTCOUNT,PREVCNT,IMPPEVCNT,i.LDAREA,i.PLAREA
        from DT_ADSURVEYCNT_S i left join DT_PREVCNT_S j on i.CODE=j.CODE
        where 1=1
        <if test="flag !=null and flag !=''">
            and SUBSTRING(i.CODE,7,3)&lt;>'000'
        </if>
        <if test="ad !=null and ad !=''">
            AND left(i.CODE,#{level}) =#{ad}
        </if>
        <if test="level != null and level == '4'.toString()">
            and substring(i.CODE,1,6) not in ( '220581','220381' )
        </if>
        ) a
        group by a.ADCD)b inner join BSN_ADCD_B c on b.ADCD=c.ADCD
        order by c.ADCD
    </select>
    <!-- 重点区基本情况  统计 -->
    <select id="getDtKeyAdList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtKeyAd">
        select b.ADCD,c.ADNM,c.PADCD,PTCOUNT,PREVCNT,IMPPEVCNT,c.LGTD,c.LTTD
        from (select a.ADCD,sum(PTCOUNT) PTCOUNT,sum(PREVCNT) PREVCNT, sum(IMPPEVCNT) IMPPEVCNT
        from (select (left(CODE,#{level2})+'${zero}') ADCD,PTCOUNT,PREVCNT,IMPPEVCNT
        from DT_PREVCNT_S
        where 1=1
        <if test="flag !=null and flag !=''">
            and SUBSTRING(CODE,7,3)&lt;>'000'
        </if>
        <if test="level2 != null and level2 == '4'.toString()">
            and substring(CODE,1,6) not in ( '220581','220381' )
        </if>
        <if test="level2 != null and level2 == '6'.toString() and ad == '2205'">
            and substring(CODE,1,6) not in ( '220581','220381' )
        </if>
        <if test="ad !=null and ad !=''">
            AND left(CODE,#{level}) =#{ad}
        </if>
        ) a
        group by a.ADCD)b inner join BSN_ADCD_B c on b.ADCD=c.ADCD
        order by c.ADCD
    </select>
    <!-- 调查成果  统计 -->
    <select id="getDtDccgInfoList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtSurvey">
        select b.ADCD,c.ADNM,c.PADCD,FLRVVLGCT,DANADCT,DTRESIDENTCT,BSNSSINFOCT,HSFWATERCT,HSURFACECT,VSURFACECT,GULLYCT
        from (select a.ADCD,sum(FLRVVLGCT) FLRVVLGCT,sum(DANADCT) DANADCT,sum(DTRESIDENTCT)
        DTRESIDENTCT,sum(BSNSSINFOCT) BSNSSINFOCT,sum(HSFWATERCT)HSFWATERCT,
        sum(HSURFACECT)HSURFACECT,sum(VSURFACECT)VSURFACECT,sum(GULLYCT)GULLYCT
        from (select (left(CODE,#{level2})+'${zero}')
        ADCD,FLRVVLGCT,DANADCT,DTRESIDENTCT,BSNSSINFOCT,HSFWATERCT,HSURFACECT,VSURFACECT,GULLYCT
        from DT_SURVEY_S
        where ISAD='1'
        <if test="flag !=null and flag !=''">
            and SUBSTRING(CODE,7,3)&lt;>'000'
        </if>
        <if test="ad !=null and ad !=''">
            AND left(CODE,#{level}) =#{ad}
        </if>
        <if test="level != null and level == '4'.toString() or level != null and level2 == '4'.toString()">
            and substring(CODE,1,6) not in ( '220581','220381' )
        </if>
        ) a
        group by a.ADCD)b inner join BSN_ADCD_B c on b.ADCD=c.ADCD
        order by c.ADCD
    </select>
    <!-- 涉水工程  统计 -->
    <select id="getDtWadingInfoList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtWading">
        select c.ADCD,c.ADNM,c.PADCD,RSST,SLUST,DIKST,DAMST,CULST,BRIST
        from (select a.ADCD,sum(RSST) RSST,sum(SLUST) SLUST,sum(DIKST) DIKST,sum(DAMST) DAMST,sum(CULST)
        CULST,sum(BRIST) BRIST
        from (select (left(ID,#{level2})+'${zero}') ADCD,RSST,SLUST,DIKST,DAMST,CULST,BRIST
        from DT_WADING_S
        where ISADCD='1'
        <if test="flag !=null and flag !=''">
            and SUBSTRING(ID,7,3)&lt;>'000'
        </if>
        <if test="ad !=null and ad !=''">
            AND left(ID,#{level}) =#{ad}
        </if>
        <if test="level != null and level == '4'.toString()">
            and substring(ID,1,6) not in ( '220581','220381' )
        </if>
        ) a
        group by a.ADCD)b inner join BSN_ADCD_B c on c.ADCD=b.ADCD
        order by b.ADCD
    </select>
    <!-- 分析评价对象  统计 -->
    <select id="getDtFxpjInfoList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtAnalysis">
        select
        b.ADCD,c.ADNM,c.PADCD,DFWRULECT,WLWRULECT,YJICRCT,NOWFHTBCT,FXCGTBCT,JYGSTBCT,YJCJTBCT,SDTDTBCT,SWLLRKTBCT
        from (select a.ADCD,sum(DFWRULECT) DFWRULECT,sum(WLWRULECT) WLWRULECT,sum(YJICRCT) YJICRCT,sum(NOWFHTBCT)
        NOWFHTBCT,sum(FXCGTBCT)FXCGTBCT,
        sum(JYGSTBCT)JYGSTBCT,sum(YJCJTBCT)YJCJTBCT,sum(SDTDTBCT)SDTDTBCT,sum(SWLLRKTBCT)SWLLRKTBCT
        from (select (left(CODE,#{level2})+'${zero}')
        ADCD,DFWRULECT,WLWRULECT,YJICRCT,NOWFHTBCT,FXCGTBCT,JYGSTBCT,YJCJTBCT,SDTDTBCT,SWLLRKTBCT
        from DT_ANALYSIS_S
        where ISAD='1'
        <if test="flag !=null and flag !=''">
            and SUBSTRING(CODE,7,3)&lt;>'000'
        </if>
        <if test="ad !=null and ad !=''">
            AND left(CODE,#{level}) =#{ad}
        </if>
        <if test="level != null and level == '4'.toString()">
            and substring(CODE,1,6) not in ( '220581','220381' )
        </if>
        ) a
        group by a.ADCD)b inner join BSN_ADCD_B c on b.ADCD=c.ADCD
        order by c.ADCD
    </select>
    <select id="getAdInfo" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtAllEvaluationInfo">
        select ADCD,ADNM,PADCD,ADLVL FROM BSN_ADCD_B WHERE 1=1
        <if test="ad !=null and ad !=''">
            and LEFT(ADCD,#{level})=#{ad}
        </if>
        <if test="adlvl !=null and adlvl !=''">
            and ADLVL &lt;= #{adlvl}
        </if>
        <if test="level != null and level == '4'.toString()">
            and substring(ADCD,1,6) not in ( '220581','220381' )
        </if>
        order by adcd
    </select>
    <select id="getDtDanInfo" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtIaDanad">
        select c.ADCD,DANCT,PTCOUNT,ETCOUNT,ECOUNT1,ECOUNT2,ECOUNT3,ECOUNT4,
        HTCOUNT,HCOUNT1,HCOUNT2,HCOUNT3,HCOUNT4,(select count(0)
        from IA_C_BSNSSINFO a inner join IA_C_DANAD b on a.DAND=b.DAND
        where left(b.ADCD,#{level2})=left(c.ADCD,#{level2}))BSCT
        from (select
        b.ADCD,count(0)DANCT,sum(PTCOUNT)PTCOUNT,sum(ETCOUNT)ETCOUNT,sum(ECOUNT1)ECOUNT1,sum(ECOUNT2)ECOUNT2,
        sum(ECOUNT3)ECOUNT3,sum(ECOUNT4)ECOUNT4,sum(HTCOUNT)HTCOUNT,sum(HCOUNT1)HCOUNT1,sum(HCOUNT2)HCOUNT2,sum(HCOUNT3)HCOUNT3,sum(HCOUNT4)HCOUNT4
        from (select (left(ADCD,#{level2})+'${zero}')
        ADCD,e.DAND,PTCOUNT,ETCOUNT,ECOUNT1,ECOUNT2,ECOUNT3,ECOUNT4,HTCOUNT,HCOUNT1,HCOUNT2,HCOUNT3,HCOUNT4
        from IA_C_DANAD e
        where 1=1
        <if test="flag !=null and flag !=''">
            and SUBSTRING(ADCD,7,3)&lt;>'000'
        </if>
        <if test="ad !=null and ad !=''">
            AND left(ADCD,#{level}) =#{ad}
        </if>
        <if test="level != null and level == '4'.toString()">
            and substring(ADCD,1,6) not in ( '220581','220381' )
        </if>
        ) b
        group by b.ADCD
        ) c
        order by c.ADCD
    </select>
    <select id="getHsfwaterInfo" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtIaHsfwater">
        select b.ADCD,HSFCT,DPCOUNT,MPCOUNT,CHCOUNT,SPCOUNT,ELOSE
        from (select
        ADCD,count(0)HSFCT,sum(DPCOUNT)DPCOUNT,sum(MPCOUNT)MPCOUNT,sum(CHCOUNT)CHCOUNT,sum(SPCOUNT)SPCOUNT,sum(ELOSE)ELOSE
        from (select (left(ADCD,#{level2})+'${zero}') ADCD,DPCOUNT,MPCOUNT,CHCOUNT,SPCOUNT,ELOSE
        from IA_C_HSFWATER where 1=1
        <if test="flag !=null and flag !=''">
            and SUBSTRING(ADCD,7,3)&lt;>'000'
        </if>
        <if test="ad !=null and ad !=''">
            AND left(ADCD,#{level}) =#{ad}
        </if>
        <if test="level != null and level == '4'.toString()">
            and substring(ADCD,1,6) not in ( '220581','220381' )
        </if>
        )a
        where 1=1
        group by ADCD)b
        order by b.ADCD
    </select>
    <select id="getGullyInfo" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtIaGully">
        select b.ADCD,GULLYCT,CHLENGTH,DIKELEN,RTLEN,FCOUNT,DCOUNT
        from (select
        ADCD,count(0)GULLYCT,sum(CHLENGTH)CHLENGTH,sum(DIKELEN)DIKELEN,sum(RTLEN)RTLEN,sum(FCOUNT)FCOUNT,sum(DCOUNT)DCOUNT
        from (select (left(ADCD,#{level2})+'${zero}') ADCD,CHLENGTH,DIKELEN,RTLEN,FCOUNT,DCOUNT
        from IA_C_GULLY where 1=1
        <if test="flag !=null and flag !=''">
            and SUBSTRING(ADCD,7,3)&lt;>'000'
        </if>
        <if test="ad !=null and ad !=''">
            AND left(ADCD,#{level}) =#{ad}
        </if>
        <if test="level2 != null and level2 == '4'.toString()">
            and substring(ADCD,1,6) not in ( '220581','220381' )
        </if>
        )a
        where 1=1
        group by ADCD)b
        order by b.ADCD
    </select>
    <select id="getVlgestatInfo" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtIaVlgestat">
        SELECT B.ADCD,LDAREA,XZCOUNT,CMWYH ,NMHS,XCHS ,NMZRK,XCRK ,NMDWCY ,XCCY ,LLMY ,LYJX ,PHONEN ,DYCY ,DRCY ,CZYS
        ,CZZC,CXCK ,JRDK ,
        NSCL ,MHCL ,ULCL,RLCL ,GYQYN ,GYQYCZ ,GDZCTZ ,ZXZXS ,XXZXS,YYCWS ,FLDWS ,FLCWS
        FROM (select ADCD,sum(LDAREA) LDAREA,sum(XZCOUNT) XZCOUNT,sum(CMWYH) CMWYH ,sum(NMHS) NMHS,sum(XCHS) XCHS
        ,sum(NMZRK) NMZRK,sum(XCRK) XCRK ,sum(NMDWCY) NMDWCY ,
        sum(XCCY) XCCY ,sum(LLMY) LLMY , sum(LYJX) LYJX ,sum(LYJX) PHONEN ,sum(DYCY) DYCY ,sum(DRCY) DRCY ,sum(CZYS)
        CZYS ,sum(CZZC) CZZC,sum(CXCK) CXCK ,sum(JRDK) JRDK ,
        sum(NSCL) NSCL ,sum(MHCL) MHCL ,sum(ULCL) ULCL,sum(RLCL) RLCL ,sum(GYQYN) GYQYN ,sum(GYQYCZ) GYQYCZ ,sum(GDZCTZ)
        GDZCTZ ,sum(ZXZXS) ZXZXS ,sum(XXZXS) XXZXS,sum(YYCWS) YYCWS ,
        sum(FLDWS) FLDWS ,sum(FLCWS) FLCWS
        from (select LEFT(ADCD,#{level2})+'${zero}' ADCD ,LDAREA,XZCOUNT,CMWYH ,NMHS,XCHS ,NMZRK,XCRK ,NMDWCY ,XCCY
        ,LLMY ,LYJX ,PHONEN ,DYCY ,DRCY ,CZYS ,CZZC,CXCK ,JRDK ,
        NSCL ,MHCL ,ULCL,RLCL ,GYQYN ,GYQYCZ ,GDZCTZ ,ZXZXS ,XXZXS,YYCWS ,FLDWS ,FLCWS ,SIGNER ,AUDID ,STATUS ,COMMENTS
        ,MODITIME
        from IA_C_VLGESTAT
        WHERE LEFT(ADCD,#{level})=#{ad}
        <if test="flag !=null and flag !=''">
            and SUBSTRING(ADCD,3,2)&lt;>'00'
        </if>
        <if test="level != null and level == '4'.toString()">
            and substring(ADCD,1,6) not in ( '220581','220381' )
        </if>
        ) a
        GROUP BY ADCD) B
        order by B.ADCD
    </select>
    <select id="getDamList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaDaminfo">
        select A.DAMCD, A.DAMNAME, A.WSCD, A.ADCD, A.PICID, A.LGTD, A.LTTD, A.XHST, A.HEIGHT, A.WIDTH, A.MT, A.SIGNER,
        A.AUDID, A.STATUS, A.COMMENTS, A.MODITIME,ADNM,WSNM
        from IA_C_DAMINFO A LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join IA_C_WATA C ON A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.DAMCD=#{map.key}
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        ORDER BY A.DAMCD
    </select>
    <select id="getFlrvvlgList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaFlrvvlg">
        SELECT A.AVRCD, A.NAME, A.ADCD, A.WSCD, A.BLGTD, A.BLTTD, A.BELE, A.PTCOUNT, A.AREA, A.BTYPE, A.STYPE, A.LGTD,
        A.LTTD, A.HELE, A.BWATER, A.BHILL, A.SIGNER, A.AUDID, A.STATUS, A.COMMENTS, A.MODITIME,ADNM,WSNM
        FROM IA_C_FLRVVLG a left join IA_C_ADINFO B on A.ADCD=B.ADCD left join IA_C_WATA C ON A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.AVRCD=#{map.key}
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.name !=null and map.name !=''">
            and CHARINDEX(#{map.name},A.NAME) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        <if test="map.prevtp!= null and map.prevtp !=''">
            and CHARINDEX(B.PREVTP,#{map.prevtp}) > 0
        </if>
        <if test="map.level != null and map.level == '4'.toString()">
            and substring(A.ADCD,1,6) not in ( '220581','220381' )
        </if>
        ORDER BY A.AVRCD
    </select>
    <select id="getSrstList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaSrstinfo">
        select A.SRSTCD, A.ADDRESS, A.WSCD, A.ADCD, A.BDATE, A.MRAIN, A.ALVOICE, A.LIGHT, A.WVALUE, A.PRAIN, A.LGTD,
        A.LTTD, A.SIGNER, A.AUDID, A.STATUS, A.COMMENTS, A.MODITIME,ADNM,WSNM
        from IA_C_SRSTINFO A LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join IA_C_WATA C ON A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.SRSTCD=#{map.key}
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        ORDER BY A.SRSTCD
    </select>
    <select id="getSwstList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaSwstinfo">
        select A.SWSTCD, A.ADDRESS, A.WSCD, A.ADCD, A.BDATE, A.LGTD, A.LTTD, A.MWARTER, A.ALVOICE, A.ALIGHT, A.SIGNER,
        A.AUDID, A.STATUS, A.COMMENTS, A.MODITIME,ADNM,WSNM
        from IA_C_SWSTINFO A LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join IA_C_WATA C ON A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.SWSTCD=#{map.key}
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        ORDER BY A.SWSTCD
    </select>
    <select id="getBsnsstList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaBsnssinfo">
        SELECT A.EICD, A.NAME, A.WSCD, A.ADCD, A.DAND, A.LGTD, A.LTTD, A.TYPE, A.OCODE, A.ADDRESS, A.AREA, A.PCOUNT,
        A.HCOUNT, A.AVALUE, A.OVALUE, A.SIGNER, A.AUDID, A.STATUS, A.COMMENTS, A.MODITIME,ADNM,WSNM,D.NAME DANDNM
        FROM IA_C_BSNSSINFO a left join IA_C_ADINFO B on A.ADCD=B.ADCD left join IA_C_WATA C ON A.WSCD=C.WSCD
        left join IA_C_DANAD D ON A.DAND=D.DAND
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.EICD=#{map.key}
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        order by a.EICD
    </select>
    <select id="getDtresidentList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaDtresident">
        SELECT A.IURCD, A.WSCD, A.ADCD, A.VNAME, A.BLGTD, A.BLTTD, A.BELE, A.ADDRESS, A.BCODE, A.HTCOUNT, A.PTCOUNT,
        A.LGTD, A.LTTD, A.HELE, A.AREA, A.BWATER, A.BHILL, A.BTYPE, A.STYPE, A.SIGNER, A.AUDID, A.STATUS, A.COMMENTS,
        A.MODITIME,ADNM,WSNM
        FROM IA_C_DTRESIDENT a left join IA_C_ADINFO B on A.ADCD=B.ADCD left join IA_C_WATA C ON A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.IURCD=#{map.key}
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        order by a.IURCD
    </select>
    <select id="getIaHsfmList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaHsfm">
        SELECT A.FMCD, A.CHANNEL, A.ADCD, A.METHOD, A.ISCTOWN, A.LGTD, A.LTTD, A.ELE, A.FLOODM, A.SIGNER, A.AUDID,
        A.STATUS, A.REMARK, A.MODITIME,B.ADNM
        FROM IA_M_HSFM a LEFT join IA_C_ADINFO B on A.ADCD=B.ADCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.FMCD=#{map.key}
        </if>
        ORDER BY A.FMCD
    </select>
    <select id="getHsurfaceList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaHsurface">
        SELECT A.HECD, A.ADCD, A.CHANNEL, A.ADDRESS, A.ISCTOWN, A.DMIDENTIT, A.DMFORM, A.TEXTURE, A.COORDINATE,
        A.ELETYPE, A.BASEELE, A.BASELGTD, A.BASELTTD, A.AZIMUTH, A.HMZ, A.CZZ, A.METHOD, A.VECD, A.SIGNER, A.AUDID,
        A.STATUS, A.REMARK, A.MODITIME,ADNM
        FROM IA_M_HSURFACE a LEFT join IA_C_ADINFO B on A.ADCD=B.ADCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.HECD=#{map.key}
        </if>
        ORDER BY A.HECD
    </select>
    <select id="getVsurfaceList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaVsurface">
        SELECT A.VECD, A.ADCD, A.CHANNEL, A.ADDRESS, A.ISCTOWN, A.CELE, A.CLGTD, A.CLTTD, A.ELETYPE, A.METHOD, A.SIGNER,
        A.AUDID, A.STATUS, A.REMARK, A.MODITIME,ADNM
        FROM IA_M_VSURFACE a LEFT join IA_C_ADINFO B on A.ADCD=B.ADCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.VECD=#{map.key}
        </if>
        ORDER BY A.VECD
    </select>
    <select id="getPrevadList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaAdinfo">
        select A.ADCD, A.ADNM, A.PCOUNT, A.HTCOUNT, A.LDAREA, A.PLAREA, A.PREVTP, A.LGTD, A.LTTD, A.SIGNER, A.AUDID,
        A.STATUS, A.COMMENTS, A.MODITIME from IA_C_ADINFO A
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND left(A.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.key!= null and map.key !=''">
            AND A.ADCD=#{map.key}
        </if>
        <if test="map.prevtp!= null and map.prevtp !=''">
            and CHARINDEX(A.PREVTP,#{map.prevtp}) > 0
        </if>
        <if test="map.adnm !=null and map.adnm !=''">
            and CHARINDEX(#{map.adnm},A.ADNM) >0
        </if>
        <if test="map.level != null and map.level == '4'.toString()">
            and substring(A.ADCD,1,6) not in ( '220581','220381' )
        </if>
        ORDER BY A.ADCD
    </select>
    <select id="getDtKeyAdListNew" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtKeyAd">
        SELECT C.ADNM, B.ADCD, C.PADCD, B.prevcnt, B.ptcount,C.LGTD,C.LTTD
        FROM (SELECT A.ADCD, COUNT(A.ADCD) prevcnt, SUM(A.PCOUNT) ptcount
        FROM (select (left(ADCD,#{level2})+'${zero}') ADCD, PCOUNT from IA_C_ADINFO
        where 1=1
        <if test="ad !=null and ad !=''">
            AND left(ADCD,#{level}) =#{ad}
        </if>
        <if test="level2 != null and level2 == '4'.toString()">
            and substring(ADCD,1,6) not in ( '220581','220381' )
        </if>
        <if test="level2 != null and level2 == '6'.toString() and ad == '2205'">
            and substring(ADCD,1,6) not in ( '220581','220381' )
        </if>
        <if test="fzqType !=null and fzqType.size() >0 ">
            and PREVTP in
            <foreach item="item" index="index" collection="fzqType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) A
        GROUP BY A.ADCD) B
        LEFT JOIN MDT_ADCDINFO_B C ON B.ADCD = C.ADCD
        WHERE C.PADCD IS NOT NULL
        ORDER BY B.ADCD
    </select>
    <select id="getDtDccgInfoListNew" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DtSurvey">
        SELECT
        A.ADCD,
        COUNT(DISTINCT A.DAND) danadct,
        COUNT(DISTINCT A.AVRCD) flrvvlgct,
        COUNT(DISTINCT A.MTCD) hsfwaterct
        FROM
        (
        SELECT
        ( LEFT ( A.ADCD, #{level2} ) + '${zero}' ) ADCD,
        B.DAND+B.NAME DAND,
        C.AVRCD+C.NAME AVRCD,
        D.MTCD MTCD
        FROM
        IA_C_ADINFO A
        LEFT JOIN IA_C_DANAD B ON A.ADCD = B.ADCD AND B.DAND IS NOT NULL
        LEFT JOIN IA_C_FLRVVLG C ON A.ADCD = C.ADCD AND C.AVRCD IS NOT NULL
        LEFT JOIN IA_C_HSFWATER D ON A.ADCD = D.ADCD AND D.MTCD IS NOT NULL
        WHERE 1=1
        <if test="ad !=null and ad !=''">
            AND left(A.ADCD,#{level}) =#{ad}
        </if>
        <if test="level2 != null and level2 == '4'.toString()">
            and substring(A.ADCD,1,6) not in ( '220581','220381' )
        </if>
        <if test="level2 != null and level2 == '6'.toString() and ad == '2205'">
            and substring(A.ADCD,1,6) not in ( '220581','220381' )
        </if>
        <if test="fzqType !=null and fzqType.size() >0 ">
            and A.PREVTP in
            <foreach item="item" index="index" collection="fzqType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) A
        GROUP BY
        A.ADCD
    </select>

</mapper>
