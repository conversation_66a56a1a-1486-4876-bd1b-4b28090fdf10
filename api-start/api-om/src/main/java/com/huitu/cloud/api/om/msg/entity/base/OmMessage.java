package com.huitu.cloud.api.om.msg.entity.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.huitu.cloud.api.om.constant.DateFormatConst.DEFAULT_TIMEZONE;
import static com.huitu.cloud.api.om.constant.DateFormatConst.PATTERN_YYYYMMDD_HHMM;

/**
 * 运管信息表
 *
 * <AUTHOR>
 */
@ApiModel(value = "OmMessage对象", description = "运管信息表")
public class OmMessage implements Serializable {
    private static final long serialVersionUID = -3004213086907511253L;

    /**
     * 信息类型有效值
     **/
    public static final List<String> MSG_TYPE_VALID_VALUE = Arrays.asList("1", "2", "3");
    /**
     * 重要程度有效值
     **/
    public static final List<String> MSG_DEGREE_VALID_VALUE = Arrays.asList("1", "2", "3");

    @ApiModelProperty(value = "信息ID")
    private String sid;

    @ApiModelProperty(value = "信息类型（1=通知，2=公告，3=预警）")
    @Size(max = 1, message = "信息类型的长度应为1个字符")
    private String msgtype;

    @ApiModelProperty(value = "信息标题")
    @NotBlank(message = "信息标题不能为空")
    @Size(max = 50, message = "信息标题的长度不能超过50个字符")
    private String msgtitle;

    @ApiModelProperty(value = "信息内容")
    @NotBlank(message = "信息内容不能为空")
    @Size(max = 1000, message = "信息内容的长度不能超过1000个字符")
    private String msgcontent;

    @ApiModelProperty(value = "重要程度（1=一般，2=重要，3=紧急）")
    @Size(max = 1, message = "重要程度的长度应为1个字符")
    private String msgdegree;

    @ApiModelProperty(value = "状态（0=待发布，1=已发布）")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String status;

    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = PATTERN_YYYYMMDD_HHMM, timezone = DEFAULT_TIMEZONE)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Date released;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = PATTERN_YYYYMMDD_HHMM, timezone = DEFAULT_TIMEZONE)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Date created;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = PATTERN_YYYYMMDD_HHMM, timezone = DEFAULT_TIMEZONE)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Date updated;

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getMsgtype() {
        return msgtype;
    }

    public void setMsgtype(String msgtype) {
        this.msgtype = msgtype;
    }

    public String getMsgtitle() {
        return msgtitle;
    }

    public void setMsgtitle(String msgtitle) {
        this.msgtitle = msgtitle;
    }

    public String getMsgcontent() {
        return msgcontent;
    }

    public void setMsgcontent(String msgcontent) {
        this.msgcontent = msgcontent;
    }

    public String getMsgdegree() {
        return msgdegree;
    }

    public void setMsgdegree(String msgdegree) {
        this.msgdegree = msgdegree;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getReleased() {
        return released;
    }

    public void setReleased(Date released) {
        this.released = released;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getUpdated() {
        return updated;
    }

    public void setUpdated(Date updated) {
        this.updated = updated;
    }
}
