package com.huitu.cloud.api.ewci.person.service.impl;

import com.huitu.cloud.api.ewci.person.entity.PersonAlterStatVo;
import com.huitu.cloud.api.ewci.person.entity.PersonInfoStat;
import com.huitu.cloud.api.ewci.person.entity.PersonStatAdcdInfo;
import com.huitu.cloud.api.ewci.person.entity.PersonStatVo;
import com.huitu.cloud.api.ewci.person.mapper.PersonStatDao;
import com.huitu.cloud.api.ewci.person.service.PersonStatService;
import com.huitu.cloud.constants.CommConstants;
import com.huitu.cloud.util.AdcdUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 责任人统计 实现类
 *
 * <AUTHOR>
 */
@Service
public class PersonStatServiceImpl implements PersonStatService {

    private PersonStatDao baseDao;

    public PersonStatServiceImpl(PersonStatDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public List<PersonStatVo> getPersonStatList(String adcd) {
        List<PersonStatVo> list = new ArrayList<>();
        //获取各类责任人信息
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", adcd);
        params.put("level", AdcdUtil.getAdLevel(adcd));
        params.put("lowLevel", AdcdUtil.getAdLevel(adcd) + 2);
        try {
            List<PersonInfoStat> personList = baseDao.getPersonInfoList(params);
            if (CollectionUtils.isEmpty(personList)) {
                throw new RuntimeException("没有获取到责任人信息！");
            }
            // 获取政区
            List<PersonStatAdcdInfo> adcdlist = baseDao.getAdcdInfoList(adcd);
            // 责任人类别
            List<String> chargeTpList = Arrays.asList("水库责任人", "山洪责任人", "江河责任人", "堤防责任人", "险工险段责任人", "蓄滞洪区责任人", "水库淹没范围责任人","中小河流责任人");
            adcdlist.forEach(a -> {
                if (a.getAdcd().equals(adcd)) {
                    // 合计
                    PersonStatVo total = new PersonStatVo();
                    total.setType("合计");
                    total.setId("合计");
                    total.setAdcd(a.getAdcd());
                    total.setPerDue(personList.size());
                    //去重之后
                    List<PersonInfoStat> listLater = personList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(n -> n.getRealnm() + n.getMobile()))), ArrayList::new));
                    total.setPerReal(listLater.size());
                    list.add(total);
                    // 责任人大类
                    chargeTpList.forEach(x -> {
                        PersonStatVo per = new PersonStatVo();
                        per.setType(x);
                        per.setAdcd(a.getAdcd());
                        per.setId(x);
                        per.setPtype("合计");
                        //根据责任人类别筛选数据
                        List<PersonInfoStat> listByChargeTp = personList.stream().filter(i -> x.equals(i.getChargeTp())).collect(Collectors.toList());
                        per.setPerDue(listByChargeTp.size());
                        if (CollectionUtils.isEmpty(listByChargeTp)) {
                            per.setPerReal(0);
                        } else {
                            //去重之后
                            List<PersonInfoStat> listByChargeTpLater = listByChargeTp.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                                    new TreeSet<>(Comparator.comparing(n -> n.getRealnm() + n.getMobile()))), ArrayList::new));
                            per.setPerReal(listByChargeTpLater.size());
                        }
                        list.add(per);

                        // 责任人小类
                        // 人员类型
                        List<String> perTpList = listByChargeTp.stream().map(PersonInfoStat::getPertp).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
                        perTpList.forEach(c -> {
                            PersonStatVo perChildren = new PersonStatVo();
                            perChildren.setType(c);
                            perChildren.setAdcd(a.getAdcd());
                            perChildren.setPtype(x);
                            perChildren.setId(x.concat("_").concat(c));
                            List<PersonInfoStat> listBypertp = listByChargeTp.stream().filter(i -> c.equals(i.getPertp())).collect(Collectors.toList());
                            perChildren.setPerDue(listBypertp.size());
                            if (CollectionUtils.isEmpty(listBypertp)) {
                                perChildren.setPerReal(0);
                            } else {
                                //去重之后
                                List<PersonInfoStat> listBypertpLater = listBypertp.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                                        new TreeSet<>(Comparator.comparing(n -> n.getRealnm() + n.getMobile()))), ArrayList::new));
                                perChildren.setPerReal(listBypertpLater.size());
                            }
                            list.add(perChildren);
                        });
                    });
                } else {
                    // 合计
                    PersonStatVo total = new PersonStatVo();
                    total.setType("合计");
                    total.setId("合计");
                    total.setAdcd(a.getAdcd());
                    List<PersonInfoStat> listByAdcd = personList.stream().filter(i -> a.getAdcd().equals(i.getAdcd())).collect(Collectors.toList());
                    total.setPerDue(listByAdcd.size());
                    //去重之后
                    if (CollectionUtils.isEmpty(listByAdcd)) {
                        total.setPerReal(0);
                    } else {
                        //去重之后
                        List<PersonInfoStat> listByAdcdLater = listByAdcd.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                                new TreeSet<>(Comparator.comparing(n -> n.getRealnm() + n.getMobile()))), ArrayList::new));
                        total.setPerReal(listByAdcdLater.size());
                    }
                    list.add(total);
                    // 责任人大类
                    chargeTpList.forEach(x -> {
                        PersonStatVo per = new PersonStatVo();
                        per.setType(x);
                        per.setAdcd(a.getAdcd());
                        per.setId(x);
                        per.setPtype("合计");
                        //根据责任人类别筛选数据
                        List<PersonInfoStat> listByChargeTp = personList.stream().filter(i -> x.equals(i.getChargeTp())).collect(Collectors.toList());
                        List<PersonInfoStat> listByChargeTpAndAdcd = listByChargeTp.stream().filter(i -> i.getAdcd().equals(a.getAdcd())).collect(Collectors.toList());
                        per.setPerDue(listByChargeTpAndAdcd.size());
                        if (CollectionUtils.isEmpty(listByChargeTpAndAdcd)) {
                            per.setPerReal(0);
                        } else {
                            //去重之后
                            List<PersonInfoStat> listByChargeTpAndAdcdLater = listByChargeTpAndAdcd.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                                    new TreeSet<>(Comparator.comparing(n -> n.getRealnm() + n.getMobile()))), ArrayList::new));
                            per.setPerReal(listByChargeTpAndAdcdLater.size());
                        }
                        list.add(per);

                        // 责任人小类
                        // 人员类型
                        List<String> perTpList = listByChargeTp.stream().map(PersonInfoStat::getPertp).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
                        perTpList.forEach(c -> {
                            PersonStatVo perChildren = new PersonStatVo();
                            perChildren.setType(c);
                            perChildren.setAdcd(a.getAdcd());
                            perChildren.setPtype(x);
                            perChildren.setId(x.concat("_").concat(c));
                            List<PersonInfoStat> listBypertp = listByChargeTpAndAdcd.stream().filter(i -> c.equals(i.getPertp())).collect(Collectors.toList());
                            perChildren.setPerDue(listBypertp.size());
                            if (CollectionUtils.isEmpty(listBypertp)) {
                                perChildren.setPerReal(0);
                            } else {
                                //去重之后
                                List<PersonInfoStat> listBypertpLater = listBypertp.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                                        new TreeSet<>(Comparator.comparing(n -> n.getRealnm() + n.getMobile()))), ArrayList::new));
                                perChildren.setPerReal(listBypertpLater.size());
                            }
                            list.add(perChildren);
                        });
                    });
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public List<PersonStatAdcdInfo> getAdcdInfoList(String adcd) {
        List<PersonStatAdcdInfo> list = baseDao.getAdcdInfoList(adcd);
        list = sortAdLevel(list);
        return list;
    }

    @Override
    public List<PersonAlterStatVo> getPersonAlterStatList(String adcd) {
        List<PersonAlterStatVo> list = baseDao.getPersonAlterStatList(adcd, AdcdUtil.getAdLevel(adcd));
        List<PersonAlterStatVo> slist = list.stream().filter(x -> "220000000000000".equals(x.getPadcd())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(slist)) {
            slist = sortPersonAlterStat(slist);
            slist.forEach(x -> {
                List<PersonAlterStatVo> childList = getChildren(list, x.getAdcd());
                if (!CollectionUtils.isEmpty(childList)){
                    x.setChildren(childList);
                }
            });
        }
        return slist;
    }

    private List<PersonStatAdcdInfo> sortAdLevel(List<PersonStatAdcdInfo> list) {
        //市集合
        List<PersonStatAdcdInfo> cityList = new ArrayList<>();
        //公主岭市与梅河口市
        List<PersonStatAdcdInfo> gmList = new ArrayList<>();
        list.forEach(x -> {
            //公主岭市与梅河口市
            if (x.getAdcd().equals(CommConstants.Public.GONGZHULING_ADCD) || x.getAdcd().equals(CommConstants.Public.MEIHEKOU_ADCD)) {
                gmList.add(x);
            } else {
                cityList.add(x);
            }
        });
        //公主岭市与梅河口市 放到列表最后
        cityList.addAll(gmList);
        return cityList;
    }

    private List<PersonAlterStatVo> sortPersonAlterStat(List<PersonAlterStatVo> list) {
        //市集合
        List<PersonAlterStatVo> cityList = new ArrayList<>();
        //公主岭市与梅河口市
        List<PersonAlterStatVo> gmList = new ArrayList<>();
        list.forEach(x -> {
            //公主岭市与梅河口市
            if (x.getAdcd().equals(CommConstants.Public.GONGZHULING_ADCD) || x.getAdcd().equals(CommConstants.Public.MEIHEKOU_ADCD)) {
                gmList.add(x);
            } else {
                cityList.add(x);
            }
        });
        //公主岭市与梅河口市 放到列表最后
        cityList.addAll(gmList);
        return cityList;
    }

    private List<PersonAlterStatVo> getChildren(List<PersonAlterStatVo> list, String padcd) {
        // 通过父级编码子类
        List<PersonAlterStatVo> childList = list.stream().filter(x -> x.getPadcd().equals(padcd)).collect(Collectors.toList());
        return childList;
    }

}
