package com.huitu.cloud.api.ewci.inspect.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.inspect.constants.InspectConstants;
import com.huitu.cloud.api.ewci.inspect.entity.*;
import com.huitu.cloud.api.ewci.inspect.mapper.*;
import com.huitu.cloud.api.ewci.inspect.service.InspectReportService;
import com.huitu.cloud.api.ewci.remote.OssRemoteService;
import com.huitu.cloud.api.usif.ad.entity.BsnAdcdB;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import com.huitu.cloud.api.usif.util.LoginUtils;
import com.huitu.cloud.constants.CommConstants;
import com.huitu.cloud.util.AdcdUtil;
import feign.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.Year;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 检查上报 实现类
 *
 * <AUTHOR>
 */
@Service
public class InspectReportServiceImpl implements InspectReportService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InspectReportServiceImpl.class);

    @Autowired
    private InspectReportDao baseDao;
    @Autowired
    private InspectPersonImpDao inspectPersonImpDao;
    @Autowired
    private InspectPlanReviseDao inspectPlanReviseDao;
    @Autowired
    private InspectEmgCommDevDao inspectEmgCommDevDao;
    @Autowired
    private InspectRsvrRunDao inspectRsvrRunDao;
    @Autowired
    private InspectIllRsvrDao inspectIllRsvrDao;
    @Autowired
    private InspectRiverDikeDao inspectRiverDikeDao;
    @Autowired
    private InspectWagaRunDao inspectWagaRunDao;
    @Autowired
    private InspectConEnDao inspectConEnDao;
    @Autowired
    private InspectAutoMonitorDao inspectAutoMonitorDao;
    @Autowired
    private InspectSimpleRainDao inspectSimpleRainDao;
    @Autowired
    private InspectWarnBroadDao inspectWarnBroadDao;
    @Autowired
    private InspectCountyPlatformDao inspectCountyPlatformDao;
    @Autowired
    private InspectMainExpDao inspectMainExpDao;
    @Autowired
    private InspectShgcConsDao inspectShgcConsDao;
    @Autowired
    private InspectAqdxConsDao inspectAqdxConsDao;
    @Autowired
    private OssRemoteService ossService;
    @Autowired
    private LoginUtils loginUtils;

    private static Map<String, List<String>> reportSettings;

    @PostConstruct
    private void initSettings() {
        try {
            String settings = ResourceUtil.readUtf8Str("inspect-settings.json");
            reportSettings = JSON.parseObject(settings, new TypeReference<LinkedHashMap<String, List<String>>>() {
            });
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize the inspect settings", e);
        }
    }

    @Override
    public List<BnsInspectTypeInfo> getBnsInspectTypeInfoList() {
        return baseDao.getBnsInspectTypeInfoList("1");
    }

    @Override
    public List<BnsInspectPointInfo> getBnsInspectPointInfoList(BnsInspectReportQuery query) {
        return baseDao.getBnsInspectPointInfoList(query.getInspectCode());
    }

    @Override
    public List<BnsInspectRecords> getBnsInspectRecordsList(BnsInspectRecordsQuery query) {
        return baseDao.getBnsInspectRecordsList(query.toQuery());
    }

    @Override
    public IPage<BnsInspectRecordsChild> getBnsInspectRecordsChildList(BnsInspectRecordsQuery query) {
        return baseDao.getBnsInspectRecordsChildList(query.toPageParam(), query.toQuery());
    }

    @Override
    public List<BnsInspectRsvr> getRsvrRunList(BnsInspectRsvrQuery query) {
        return baseDao.getRsvrRunList(query.toQuery());
    }

    @Override
    public List<BnsInspectRsvr> getIllRsvrList(BnsInspectRsvrQuery query) {
        return baseDao.getIllRsvrList(query.toQuery());
    }

    @Override
    public List<BnsEngB> getConstructEngList(BnsEngBQuery query) {
        return baseDao.getConstructEngList(query.toQuery());
    }

    @Override
    public List<BnsEngB> getShgcConstructList(BnsEngBQuery query) {
        return baseDao.getShgcConstructList(query.toQuery());
    }

    @Override
    public List<BnsEngB> getAqdxConstructList(BnsEngBQuery query) {
        return baseDao.getAqdxConstructList(query.toQuery());
    }

    @Override
    public List<BnsAutoMonitorB> getAutoMonitorList(BnsAutoMonitorQuery query) {
        return baseDao.getAutoMonitorList(query.toQuery());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectPersonImp(BnsInspectPersonImp entity) {
        try {
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectPersonImp> lists = inspectPersonImpDao.getInspectPersonImpList(entity.getAdcd(), entity.getYear());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加责任人落实
                        return 0;
                    } else {
                        return addInspectPersonImp(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectPersonImpDao.deleteInspectPersonImp(lists.get(0).getInspectId());
                    return addInspectPersonImp(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectPersonImp(BnsInspectPersonImp entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectPersonImp> lists = inspectPersonImpDao.getInspectPersonImpList(entity.getAdcd(), entity.getYear());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectPersonImpDao.updateInspectPersonImp(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectPlanRevise(BnsInspectPlanRevise entity) {
        try {
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectPlanRevise> lists = inspectPlanReviseDao.getInspectPlanReviseList(entity.getAdcd(), entity.getYear());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加方案/预案修订
                        return 0;
                    } else {
                        return addInspectPlanRevise(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectPlanReviseDao.deleteInspectPlanRevise(lists.get(0).getInspectId());
                    return addInspectPlanRevise(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectPlanRevise(BnsInspectPlanRevise entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectPlanRevise> lists = inspectPlanReviseDao.getInspectPlanReviseList(entity.getAdcd(), entity.getYear());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectPlanReviseDao.updateInspectPlanRevise(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectRsvrRun(BnsInspectRsvrRun entity) {
        try {
            if (Objects.isNull(entity.getResCode()) || Objects.equals(entity.getResCode(), "")) {
                throw new IllegalArgumentException("参数[水库编码]不能为空");
            }
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectRsvrRun> lists = inspectRsvrRunDao.getInspectRsvrRunList(entity.getAdcd(), entity.getYear(), entity.getResCode());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加水库(水电站)运行
                        return 0;
                    } else {
                        return addInspectRsvrRun(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectRsvrRunDao.deleteInspectRsvrRun(lists.get(0).getInspectId());
                    return addInspectRsvrRun(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectRsvrRun(BnsInspectRsvrRun entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectRsvrRun> lists = inspectRsvrRunDao.getInspectRsvrRunList(entity.getAdcd(), entity.getYear(), entity.getResCode());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectRsvrRunDao.updateInspectRsvrRun(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectIllRsvr(BnsInspectIllRsvr entity) {
        try {
            if (Objects.isNull(entity.getResCode()) || Objects.equals(entity.getResCode(), "")) {
                throw new IllegalArgumentException("参数[水库编码]不能为空");
            }
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectIllRsvr> lists = inspectIllRsvrDao.getInspectIllRsvrList(entity.getAdcd(), entity.getYear(), entity.getResCode());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加 病险水库
                        return 0;
                    } else {
                        return addInspectIllRsvr(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectIllRsvrDao.deleteInspectIllRsvr(lists.get(0).getInspectId());
                    return addInspectIllRsvr(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectIllRsvr(BnsInspectIllRsvr entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectIllRsvr> lists = inspectIllRsvrDao.getInspectIllRsvrList(entity.getAdcd(), entity.getYear(), entity.getResCode());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectIllRsvrDao.updateInspectIllRsvr(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectRiverDike(BnsInspectRiverDike entity) {
        try {
            if (Objects.isNull(entity.getRiverDikeName()) || Objects.equals(entity.getRiverDikeName(), "")) {
                throw new IllegalArgumentException("参数[河流及堤防名称]不能为空");
            }
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectRiverDike> lists = inspectRiverDikeDao.getInspectRiverDikeList(entity.getAdcd(), entity.getYear(), entity.getRiverDikeName());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加 河流及堤防名称
                        return 0;
                    } else {
                        return addInspectRiverDike(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectRiverDikeDao.deleteInspectRiverDike(lists.get(0).getInspectId());
                    return addInspectRiverDike(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectRiverDike(BnsInspectRiverDike entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectRiverDike> lists = inspectRiverDikeDao.getInspectRiverDikeList(entity.getAdcd(), entity.getYear(), entity.getRiverDikeName());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectRiverDikeDao.updateInspectRiverDike(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectWagaRun(BnsInspectWagaRun entity) {
        try {
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                return addInspectWagaRun(entity);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectWagaRun(BnsInspectWagaRun entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectWagaRunDao.updateInspectWagaRun(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectConEn(BnsInspectConEn entity) {
        try {
            if (Objects.isNull(entity.getEnCode()) || Objects.equals(entity.getEnCode(), "")) {
                throw new IllegalArgumentException("参数[在建工程编码]不能为空");
            }
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectConEn> lists = inspectConEnDao.getInspectConEnList(entity.getAdcd(), entity.getYear(), entity.getEnCode());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加 在建工程
                        return 0;
                    } else {
                        return addInspectConEn(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectConEnDao.deleteInspectConEn(lists.get(0).getInspectId());
                    return addInspectConEn(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectConEn(BnsInspectConEn entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectConEn> lists = inspectConEnDao.getInspectConEnList(entity.getAdcd(), entity.getYear(), entity.getEnCode());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectConEnDao.updateInspectConEn(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectAutoMonitor(BnsInspectAutoMonitor entity) {
        try {
            if (Objects.isNull(entity.getStcd()) || Objects.equals(entity.getStcd(), "")) {
                throw new IllegalArgumentException("参数[站点编码]不能为空");
            }
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectAutoMonitor> lists = inspectAutoMonitorDao.getInspectAutoMonitorList(entity.getAdcd(), entity.getYear(), entity.getStcd());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加 自动监测站
                        return 0;
                    } else {
                        return addInspectAutoMonitor(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectAutoMonitorDao.deleteInspectAutoMonitor(lists.get(0).getInspectId());
                    return addInspectAutoMonitor(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectAutoMonitor(BnsInspectAutoMonitor entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectAutoMonitor> lists = inspectAutoMonitorDao.getInspectAutoMonitorList(entity.getAdcd(), entity.getYear(), entity.getStcd());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectAutoMonitorDao.updateInspectAutoMonitor(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectSimpleRain(BnsInspectSimpleRain entity) {
        try {
            if (Objects.isNull(entity.getSrstnm()) || Objects.equals(entity.getSrstnm(), "")) {
                throw new IllegalArgumentException("参数[简易雨量站名称]不能为空");
            }
            if (Objects.isNull(entity.getTadcd()) || Objects.equals(entity.getTadcd(), "")) {
                throw new IllegalArgumentException("参数[村屯政区编码]不能为空");
            }
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            }
            // 验证政区后三位不是000，代表是屯，一个屯只能有一个简易雨量站
            if (!entity.getTadcd().endsWith("000")) {
                List<BnsInspectSimpleRain> lists = inspectSimpleRainDao.getInspectSimpleRainList(entity.getYear(), entity.getTadcd());
                if (!lists.isEmpty() && Objects.equals(entity.getIsCover(), "0")) {
                    return 0;
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectSimpleRainDao.deleteInspectSimpleRain(lists.get(0).getInspectId());
                    return addInspectSimpleRain(entity);
                }
            }
            return addInspectSimpleRain(entity);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectSimpleRain(BnsInspectSimpleRain entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                if (!entity.getTadcd().endsWith("000")) {
                    List<BnsInspectSimpleRain> lists = inspectSimpleRainDao.getInspectSimpleRainList(entity.getYear(), entity.getTadcd());
                    if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                        return 0;
                    }
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectSimpleRainDao.updateInspectSimpleRain(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectWarnBroad(BnsInspectWarnBroad entity) {
        try {
            if (Objects.isNull(entity.getWbrnm()) || Objects.equals(entity.getWbrnm(), "")) {
                throw new IllegalArgumentException("参数[无线预警广播站名称]不能为空");
            }
            if (Objects.isNull(entity.getTadcd()) || Objects.equals(entity.getTadcd(), "")) {
                throw new IllegalArgumentException("参数[村屯政区编码]不能为空");
            }
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            }
            // 验证政区后三位不是000，代表是屯，一个屯只能有一个简易雨量站
            if (!entity.getTadcd().endsWith("000")) {
                List<BnsInspectWarnBroad> lists = inspectWarnBroadDao.getInspectWarnBroadList(entity.getYear(), entity.getTadcd());
                if (!lists.isEmpty() && Objects.equals(entity.getIsCover(), "0")) {
                    return 0;
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectWarnBroadDao.deleteInspectWarnBroad(lists.get(0).getInspectId());
                    return addInspectWarnBroad(entity);
                }

            }
            return addInspectWarnBroad(entity);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectWarnBroad(BnsInspectWarnBroad entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                if (Objects.nonNull(entity.getTadcd()) && !Objects.equals(entity.getTadcd(), "") && !entity.getTadcd().endsWith("000")) {
                    List<BnsInspectWarnBroad> lists = inspectWarnBroadDao.getInspectWarnBroadList(entity.getYear(), entity.getTadcd());
                    if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                        return 0;
                    }
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectWarnBroadDao.updateInspectWarnBroad(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectCountyPlatform(BnsInspectCountyPlatform entity) {
        try {
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectCountyPlatform> lists = inspectCountyPlatformDao.getInspectCountyPlatformList(entity.getAdcd(), entity.getYear());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加 县级山洪灾害防御平台
                        return 0;
                    } else {
                        return addInspectCountyPlatform(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectCountyPlatformDao.deleteInspectCountyPlatform(lists.get(0).getInspectId());
                    return addInspectCountyPlatform(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectCountyPlatform(BnsInspectCountyPlatform entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectCountyPlatform> lists = inspectCountyPlatformDao.getInspectCountyPlatformList(entity.getAdcd(), entity.getYear());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectCountyPlatformDao.updateInspectCountyPlatform(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectMainExp(BnsInspectMainExp entity) {
        try {
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectMainExp> lists = inspectMainExpDao.getInspectMainExpList(entity.getAdcd(), entity.getYear());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加 运行维护经费
                        return 0;
                    } else {
                        return addInspectMainExp(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectMainExpDao.deleteInspectMainExp(lists.get(0).getInspectId());
                    return addInspectMainExp(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectMainExp(BnsInspectMainExp entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectMainExp> lists = inspectMainExpDao.getInspectMainExpList(entity.getAdcd(), entity.getYear());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectMainExpDao.updateInspectMainExp(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectShgcCons(BnsInspectShgcCons entity) {
        try {
            if (Objects.isNull(entity.getEnCode()) || Objects.equals(entity.getEnCode(), "")) {
                throw new IllegalArgumentException("参数[水毁修复工程编码]不能为空");
            }
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectShgcCons> lists = inspectShgcConsDao.getInspectShgcConsList(entity.getAdcd(), entity.getYear(), entity.getEnCode());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加 水毁工程修复建设
                        return 0;
                    } else {
                        return addInspectShgcCons(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectShgcConsDao.deleteInspectShgcCons(lists.get(0).getInspectId());
                    return addInspectShgcCons(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectShgcCons(BnsInspectShgcCons entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectShgcCons> lists = inspectShgcConsDao.getInspectShgcConsList(entity.getAdcd(), entity.getYear(), entity.getEnCode());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectShgcConsDao.updateInspectShgcCons(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectAqdxCons(BnsInspectAqdxCons entity) {
        try {
            if (Objects.isNull(entity.getEnCode()) || Objects.equals(entity.getEnCode(), "")) {
                throw new IllegalArgumentException("参数[安全度汛工程编码]不能为空");
            }
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectAqdxCons> lists = inspectAqdxConsDao.getInspectAqdxConsList(entity.getAdcd(), entity.getYear(), entity.getEnCode());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加 安全度汛工程建设
                        return 0;
                    } else {
                        return addInspectAqdxCons(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectAqdxConsDao.deleteInspectAqdxCons(lists.get(0).getInspectId());
                    return addInspectAqdxCons(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectAqdxCons(BnsInspectAqdxCons entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectAqdxCons> lists = inspectAqdxConsDao.getInspectAqdxConsList(entity.getAdcd(), entity.getYear(), entity.getEnCode());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectAqdxConsDao.updateInspectAqdxCons(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectPersonImp(BnsInspectPersonImp entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectPersonImpDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectPersonImpDao.deleteInspectPersonImp(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectPlanRevise(BnsInspectPlanRevise entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectPlanReviseDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectPlanReviseDao.deleteInspectPlanRevise(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectRsvrRun(BnsInspectRsvrRun entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectRsvrRunDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectRsvrRunDao.deleteInspectRsvrRun(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectIllRsvr(BnsInspectIllRsvr entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectIllRsvrDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectIllRsvrDao.deleteInspectIllRsvr(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectRiverDike(BnsInspectRiverDike entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectRiverDikeDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectRiverDikeDao.deleteInspectRiverDike(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectWagaRun(BnsInspectWagaRun entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectWagaRunDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectWagaRunDao.deleteInspectWagaRun(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectConEn(BnsInspectConEn entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectConEnDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectConEnDao.deleteInspectConEn(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectAutoMonitor(BnsInspectAutoMonitor entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectAutoMonitorDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectAutoMonitorDao.deleteInspectAutoMonitor(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectSimpleRain(BnsInspectSimpleRain entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectSimpleRainDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectSimpleRainDao.deleteInspectSimpleRain(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectWarnBroad(BnsInspectWarnBroad entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectWarnBroadDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectWarnBroadDao.deleteInspectWarnBroad(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectCountyPlatform(BnsInspectCountyPlatform entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectCountyPlatformDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectCountyPlatformDao.deleteInspectCountyPlatform(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectMainExp(BnsInspectMainExp entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectMainExpDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectMainExpDao.deleteInspectMainExp(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectShgcCons(BnsInspectShgcCons entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectShgcConsDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectShgcConsDao.deleteInspectShgcCons(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectAqdxCons(BnsInspectAqdxCons entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectAqdxConsDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectAqdxConsDao.deleteInspectAqdxCons(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public int addInspectPersonImp(BnsInspectPersonImp entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectPersonImpDao.insertInspectPersonImp(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectPlanRevise(BnsInspectPlanRevise entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectPlanReviseDao.insertInspectPlanRevise(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectRsvrRun(BnsInspectRsvrRun entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectRsvrRunDao.insertInspectRsvrRun(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectIllRsvr(BnsInspectIllRsvr entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectIllRsvrDao.insertInspectIllRsvr(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectRiverDike(BnsInspectRiverDike entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectRiverDikeDao.insertInspectRiverDike(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectWagaRun(BnsInspectWagaRun entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectWagaRunDao.insertInspectWagaRun(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectConEn(BnsInspectConEn entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectConEnDao.insertInspectConEn(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectAutoMonitor(BnsInspectAutoMonitor entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectAutoMonitorDao.insertInspectAutoMonitor(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectSimpleRain(BnsInspectSimpleRain entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectSimpleRainDao.insertInspectSimpleRain(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectWarnBroad(BnsInspectWarnBroad entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectWarnBroadDao.insertInspectWarnBroad(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectCountyPlatform(BnsInspectCountyPlatform entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectCountyPlatformDao.insertInspectCountyPlatform(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectMainExp(BnsInspectMainExp entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectMainExpDao.insertInspectMainExp(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectShgcCons(BnsInspectShgcCons entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectShgcConsDao.insertInspectShgcCons(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public int addInspectAqdxCons(BnsInspectAqdxCons entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectAqdxConsDao.insertInspectAqdxCons(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    public void addFile(String inspectId, List<BnsInspectFile> photos, List<BnsInspectFile> videos, List<BnsInspectFile> audios) {
        // 添加文件
        List<BnsInspectFile> fileList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(photos)) { //图片
            List<BnsInspectFile> photo = photos.stream().map(item -> {
                        item.setBusinessKey(inspectId);
                        item.setFileType("1");
                        return item;
                    }
            ).collect(Collectors.toList());
            fileList.addAll(photo);
        }
        if (!CollectionUtils.isEmpty(videos)) { //视频
            List<BnsInspectFile> video = videos.stream().map(item -> {
                        item.setBusinessKey(inspectId);
                        item.setFileType("2");
                        return item;
                    }
            ).collect(Collectors.toList());
            fileList.addAll(video);
        }
        if (!CollectionUtils.isEmpty(audios)) { //音频
            List<BnsInspectFile> audio = audios.stream().map(item -> {
                        item.setBusinessKey(inspectId);
                        item.setFileType("3");
                        return item;
                    }
            ).collect(Collectors.toList());
            fileList.addAll(audio);
        }
        if (!CollectionUtils.isEmpty(fileList)) {
            baseDao.addFile(fileList);

        }
    }

    @Override
    public BsnAdcdB getAdTree(String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adLvl", level + 2);
        List<BsnAdcdB> adList = baseDao.selectByAdLevel(param);
        adList.forEach(x -> {
            List<BsnAdcdB> childList = getChildren(adList, x.getAdcd());
            //对市重新排序 公主岭市与梅河口市 放到最后
            if (x.getAdcd().equals(CommConstants.Public.PROVINCE_ADCD)) {
                childList = sortAdLevel(childList);
            }

            x.setChildren(childList);
        });
        //获取等于当前政区编码节点
        BsnAdcdB adBean = adList.stream().filter(x -> x.getAdcd().equals(adcd)).collect(Collectors.toList()).get(0);
        return adBean;
    }

    private List<BsnAdcdB> getChildren(List<BsnAdcdB> list, String pcode) {
        // 通过父级编码子类
        List<BsnAdcdB> childList = list.stream().filter(x -> x.getPadcd().equals(pcode)).collect(Collectors.toList());
        return childList;
    }

    private List<BsnAdcdB> sortAdLevel(List<BsnAdcdB> list) {
        //市集合
        List<BsnAdcdB> cityList = new ArrayList<>();
        //公主岭市与梅河口市
        List<BsnAdcdB> gmList = new ArrayList<>();
        list.forEach(x -> {
            //公主岭市与梅河口市
            if (x.getAdcd().equals(CommConstants.Public.GONGZHULING_ADCD) || x.getAdcd().equals(CommConstants.Public.MEIHEKOU_ADCD)) {
                gmList.add(x);
            } else {
                cityList.add(x);
            }
        });
        //公主岭市与梅河口市 放到列表最后
        cityList.addAll(gmList);
        return cityList;
    }

    @Override
    public String getAdnmByAdcd(String adcd) {
        return baseDao.getAdnmByAdcd(adcd);
    }

    private <T> void setFiles(List<BnsInspectFile> files, T inspectRecord) {
        // 获取文件类型分组
        Map<String, List<BnsInspectFile>> filesByType = files.stream()
                .collect(Collectors.groupingBy(BnsInspectFile::getFileType));

        // 通过反射调用 setPhotos, setVideos, setAudios 方法
        try {
            // 获取 class 对象
            Class<?> clazz = inspectRecord.getClass();

            // 获取对应的 set 方法
            Method setPhotos = clazz.getMethod("setPhotos", List.class);
            Method setVideos = clazz.getMethod("setVideos", List.class);
            Method setAudios = clazz.getMethod("setAudios", List.class);

            // 调用 set 方法来设置值
            setPhotos.invoke(inspectRecord, filesByType.getOrDefault("1", Collections.emptyList()));
            setVideos.invoke(inspectRecord, filesByType.getOrDefault("2", Collections.emptyList()));
            setAudios.invoke(inspectRecord, filesByType.getOrDefault("3", Collections.emptyList()));

        } catch (Exception e) {
            e.printStackTrace();  // 处理异常，例如没有找到方法等
        }
    }


    @Override
    public <T> T getBnsInspectRecordInfo(BnsInspectRecordInfoQuery query) {
        String inspectCode = query.getInspectCode();
        String inspectId = query.getInspectId();
        List<BnsInspectFile> file = baseDao.getFile(inspectId);
        switch (inspectCode) {
            case InspectConstants.PERSON_IMP:  // 责任人落实
                BnsInspectPersonImp personImpInfo = baseDao.getPersonImpInfo(inspectId, inspectCode);
                personImpInfo.setAdnm(baseDao.getAdnmByAdcd(personImpInfo.getAdcd()));
                setFiles(file, personImpInfo);
                return (T) personImpInfo;
            case InspectConstants.PLAN_REVISE:  // 方案/预案修订
                BnsInspectPlanRevise inspectPlanRevise = baseDao.getPlanReviseInfo(inspectId, inspectCode);
                inspectPlanRevise.setAdnm(baseDao.getAdnmByAdcd(inspectPlanRevise.getAdcd()));
                setFiles(file, inspectPlanRevise);
                return (T) inspectPlanRevise;
            case InspectConstants.EMG_COMM_DEV:  // 应急通讯设备
                BnsInspectEmgCommDev inspectEmgCommDev = baseDao.getEmgCommDevInfo(inspectId, inspectCode);
                inspectEmgCommDev.setAdnm(baseDao.getAdnmByAdcd(inspectEmgCommDev.getAdcd()));
                setFiles(file, inspectEmgCommDev);
                return (T) inspectEmgCommDev;
            case InspectConstants.RSVR_RUN:  // 水库(水电站)运行
                BnsInspectRsvrRun inspectRsvrRun = baseDao.getRsvrRunInfo(inspectId, inspectCode);
                inspectRsvrRun.setAdnm(baseDao.getAdnmByAdcd(inspectRsvrRun.getAdcd()));
                setFiles(file, inspectRsvrRun);
                return (T) inspectRsvrRun;
            case InspectConstants.ILL_RSVR:  // 病险水库
                BnsInspectIllRsvr inspectIllRsvr = baseDao.getInspectIllRsvrInfo(inspectId, inspectCode);
                inspectIllRsvr.setAdnm(baseDao.getAdnmByAdcd(inspectIllRsvr.getAdcd()));
                setFiles(file, inspectIllRsvr);
                return (T) inspectIllRsvr;
            case InspectConstants.RIVER_DIKE:  // 河道及堤防运行
                BnsInspectRiverDike inspectRiverDike = baseDao.getInspectRiverDikeInfo(inspectId, inspectCode);
                inspectRiverDike.setAdnm(baseDao.getAdnmByAdcd(inspectRiverDike.getAdcd()));
                setFiles(file, inspectRiverDike);
                return (T) inspectRiverDike;
            case InspectConstants.CON_EN:  // 在建工程
                BnsInspectConEn inspectConEn = baseDao.getInspectConEnInfo(inspectId, inspectCode);
                inspectConEn.setAdnm(baseDao.getAdnmByAdcd(inspectConEn.getAdcd()));
                setFiles(file, inspectConEn);
                return (T) inspectConEn;
            case InspectConstants.WAGA_RUN:  // 水闸工程运行
                BnsInspectWagaRun inspectWagaRun = baseDao.getInspectWagaRunInfo(inspectId, inspectCode);
                inspectWagaRun.setAdnm(baseDao.getAdnmByAdcd(inspectWagaRun.getAdcd()));
                inspectWagaRun.setTadnm(baseDao.getTadnmByAdcd(inspectWagaRun.getTadcd()));
                setFiles(file, inspectWagaRun);
                return (T) inspectWagaRun;
            case InspectConstants.AUTO_MONITOR:  // 自动监测站/预案修订
                BnsInspectAutoMonitor inspectAutoMonitor = baseDao.getInspectAutoMonitorInfo(inspectId, inspectCode);
                inspectAutoMonitor.setAdnm(baseDao.getAdnmByAdcd(inspectAutoMonitor.getAdcd()));
                setFiles(file, inspectAutoMonitor);
                return (T) inspectAutoMonitor;
            case InspectConstants.SIMPLE_RAIN:  // 简易雨量站
                BnsInspectSimpleRain inspectSimpleRain = baseDao.getInspectSimpleRainInfo(inspectId, inspectCode);
                inspectSimpleRain.setAdnm(baseDao.getAdnmByAdcd(inspectSimpleRain.getAdcd()));
                inspectSimpleRain.setTadnm(baseDao.getTadnmByAdcd(inspectSimpleRain.getTadcd()));
                setFiles(file, inspectSimpleRain);
                return (T) inspectSimpleRain;
            case InspectConstants.WARN_BROAD:  // 无线预警广播站(水电站)运行
                BnsInspectWarnBroad inspectWarnBroad = baseDao.getInspectWarnBroadInfo(inspectId, inspectCode);
                inspectWarnBroad.setAdnm(baseDao.getAdnmByAdcd(inspectWarnBroad.getAdcd()));
                inspectWarnBroad.setTadnm(baseDao.getTadnmByAdcd(inspectWarnBroad.getTadcd()));
                setFiles(file, inspectWarnBroad);
                return (T) inspectWarnBroad;
            case InspectConstants.COUNTY_PLATFORM:  // 县级山洪灾害防御平台
                BnsInspectCountyPlatform inspectCountyPlatform = baseDao.getInspectCountyPlatformInfo(inspectId, inspectCode);
                inspectCountyPlatform.setAdnm(baseDao.getAdnmByAdcd(inspectCountyPlatform.getAdcd()));
                setFiles(file, inspectCountyPlatform);
                return (T) inspectCountyPlatform;
            case InspectConstants.MAIN_EXP:  // 运行维护经费
                BnsInspectMainExp inspectMainExp = baseDao.getInspectMainExpInfo(inspectId, inspectCode);
                inspectMainExp.setAdnm(baseDao.getAdnmByAdcd(inspectMainExp.getAdcd()));
                setFiles(file, inspectMainExp);
                return (T) inspectMainExp;
            case InspectConstants.SHGC_CONS:  // 水毁工程修复建设
                BnsInspectShgcCons inspectShgcCons = baseDao.getInspectShgcConsInfo(inspectId, inspectCode);
                inspectShgcCons.setAdnm(baseDao.getAdnmByAdcd(inspectShgcCons.getAdcd()));
                setFiles(file, inspectShgcCons);
                return (T) inspectShgcCons;
            case InspectConstants.AQDX_CONS:  // 安全度汛工程建设
                BnsInspectAqdxCons inspectAqdxCons = baseDao.getInspectAqdxConsInfo(inspectId, inspectCode);
                inspectAqdxCons.setAdnm(baseDao.getAdnmByAdcd(inspectAqdxCons.getAdcd()));
                setFiles(file, inspectAqdxCons);
                return (T) inspectAqdxCons;
        }
        return null;
    }

    @Override
    public List<BnsInspectFile> getFile(String businessKey) {
        return baseDao.getFile(businessKey);
    }

    @Override
    public Integer selectReportCount(BnsInspectReportAllQuery entity) {
        return baseDao.selectReportCount(entity.toQueryParam());
    }

    @Override
    public Integer insertReport(BnsInspectReportAllQuery entity) {
        if (baseDao.selectReportCount(entity.toQueryParam()) > 0) {
            return 0;
        }
        UserInfos loginUser = loginUtils.getCurrentLoginUser();
        Map<String, Object> param = entity.toQueryParam();
        entity.setReportId(UUID.randomUUID().toString());
        param.put("reportId", entity.getReportId());
        param.put("createDate", LocalDateTime.now());
        param.put("confirmResult", "0");
        param.put("status", "1");
        param.put("latestTime", LocalDateTime.now());
        param.put("creator", loginUser.getUserid());
        return baseDao.insertReport(param);
    }

    @Override
    public int rebuildReportDocument(String reportId) {
        return baseDao.updateReportStatus(reportId, "1");
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rebuildReportDocumentFileAsync(String reportId) {
        BnsInspectReportsChild report = baseDao.getInspectReportById(reportId);

        String adcd = report.getAdcd(), year = report.getYear();
        String reportPath = StringUtils.EMPTY, reportStatus = "3";
        try {
            LOGGER.info("[汛前检查报告]文档[adcd={}, year={}]开始生成", adcd, year);
            reportPath = buildReportDocument(adcd, year);
            LOGGER.info("[汛前检查报告]文档[adcd={}, year={}]已生成，准备更新报告状态", adcd, year);
        } catch (Exception e) {
            LOGGER.warn("[汛前检查报告]文档[adcd={}, year={}]生成失败，准备更新报告状态", adcd, year);

            LOGGER.error(String.format("[汛前检查报告]文档[adcd=%s, year=%s]生成失败", adcd, year), e);
        }
        try {
            if (StringUtils.isNotBlank(reportPath)) {
                reportStatus = "2";
                // 保存报告文件
                BnsInspectFile file = new BnsInspectFile();
                file.setFileId(UUID.randomUUID().toString());
                file.setBusinessKey(reportId);
                file.setFilePath(reportPath);
                file.setFileType("4");
                // 删除文件
                baseDao.deleteFileByType(file.getBusinessKey(), file.getFileType());
                // 新增文件
                baseDao.addFile(Collections.singletonList(file));
            }
            // 更新报告状态
            baseDao.updateReportStatus(reportId, reportStatus);

            LOGGER.info("[汛前检查报告]报告[adcd={}, year={}]状态已更新", adcd, year);
        } catch (Exception e) {
            LOGGER.warn("[汛前检查报告]报告[adcd={}, year={}]状态更新失败", adcd, year);

            LOGGER.error(String.format("[汛前检查报告]报告[adcd=%s, year=%s]状态更新失败", adcd, year), e);

            // 抛出异常回滚事务
            throw new RuntimeException(e);
        }
    }

    @Override
    public IPage<BnsInspectReportsChild> getBnsInspectReportsChildList(BnsInspectReportsQuery query) {
        return baseDao.getBnsInspectReportsChildList(query.toPageParam(), query.toQuery());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer submitInspectReport(BnsInspectSubmitReports entity) {
        String reportId = entity.getReportId();
        if (baseDao.getReportConfirmResult(reportId) > 0) {
            return 0;
        }
        if (entity.getPhotos().isEmpty()) {
            throw new IllegalArgumentException("报告确认单照片不能为空");
        }
        entity.getPhotos().forEach(item -> {
            item.setBusinessKey(reportId);
            item.setFileType("1");
        });
        baseDao.addFile(entity.getPhotos());
        return baseDao.updateReportConfirm(reportId);
    }

    @Override
    public Integer deleteInspectReport(String reportId) {
        int row = baseDao.deleteInspectReport(reportId);
        if (row > 0) {
            baseDao.deleteFile(reportId);
        }
        return row;
    }

    @Override
    public BnsInspectReportsChild getInspectReportById(String reportId) throws Exception {
        BnsInspectReportsChild bnsInspectReport = baseDao.getInspectReportById(reportId);
        if (Objects.isNull(bnsInspectReport)) {
            throw new Exception("检查报告不存在，请检查");
        }
        bnsInspectReport.setAdnm(baseDao.getTadnmByAdcd(bnsInspectReport.getAdcd()));
        bnsInspectReport.setPhotos(baseDao.getFile(reportId)
                .stream()
                .filter(s -> Objects.equals(s.getFileType(), "1"))
                .collect(Collectors.toList())
        );
        return bnsInspectReport;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInspectEmgCommDev(BnsInspectEmgCommDev entity) {
        try {
            String inspectId = entity.getInspectId();
            Date inspDate = inspectEmgCommDevDao.selectYearByInspectId(inspectId);
            entity.setYear(String.valueOf(Year.from(inspDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                int code = inspectEmgCommDevDao.deleteInspectEmgCommDev(inspectId);
                if (code > 0) {
                    baseDao.deleteFile(inspectId);
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInspectEmgCommDev(BnsInspectEmgCommDev entity) {
        try {
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectEmgCommDev> lists = inspectEmgCommDevDao.getInspectEmgCommDevList(entity.getAdcd(), entity.getYear());
                if (!lists.isEmpty() && !Objects.equals(entity.getInspectId(), lists.get(0).getInspectId())) {  // 表示和其他人提交的数据有重复
                    return 0;
                }
                UserInfos loginUser = loginUtils.getCurrentLoginUser();
                entity.setCreator(loginUser.getUserid());
                int code = inspectEmgCommDevDao.updateInspectEmgCommDev(entity);
                if (code == 0) {
                    return 3;
                }
                if (code > 0) {
                    baseDao.deleteFile(entity.getInspectId());
                    addFile(entity.getInspectId(), entity.getPhotos(), entity.getVideos(), entity.getAudios());
                }
                return code;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertInspectEmgCommDev(BnsInspectEmgCommDev entity) {
        try {
            UserInfos loginUser = loginUtils.getCurrentLoginUser();
            entity.setCreator(loginUser.getUserid());
            entity.setYear(String.valueOf(Year.from(entity.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
            if (baseDao.getBnsInspectReportCount(entity.getAdcd(), entity.getYear()) > 0) { // 判断是否生成报告
                return 2;
            } else {
                List<BnsInspectEmgCommDev> lists = inspectEmgCommDevDao.getInspectEmgCommDevList(entity.getAdcd(), entity.getYear());
                //判断是否覆盖
                if ("0".equals(entity.getIsCover())) {
                    if (!lists.isEmpty()) { // 判断是否添加方案/预案修订
                        return 0;
                    } else {
                        return addInspectEmgCommDev(entity);
                    }
                }
                if ("1".equals(entity.getIsCover())) {
                    // 根据检查ID删除文件
                    baseDao.deleteFile(lists.get(0).getInspectId());
                    inspectEmgCommDevDao.deleteInspectEmgCommDev(lists.get(0).getInspectId());
                    return addInspectEmgCommDev(entity);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    private Integer addInspectEmgCommDev(BnsInspectEmgCommDev entity) {
        String uuid = UUID.randomUUID().toString();
        entity.setInspectId(uuid);
        int code = inspectEmgCommDevDao.insertInspectEmgCommDevDao(entity);
        if (code > 0) {
            // 添加文件
            addFile(uuid, entity.getPhotos(), entity.getVideos(), entity.getAudios());
        }
        return code;
    }

    private String buildReportDocument(String adcd, String year) throws IOException {
        String adnm = baseDao.getCurrentAdnmByAdcd(adcd);
        ResponseEntity<SuccessResponse<Map<String, String>>> response = ossService.copyFile(
                "template/inspect_report.docx", String.format("%s%s年汛前检查及存在问题处理情况表.docx", adnm, year));
        Map<String, String> map = Objects.requireNonNull(response.getBody()).getData();
        String filename = map.get("filename"), filepath = map.get("filepath");

        Map<String, Object> params = new HashMap<>();
        params.put("adcd", adcd);
        params.put("adnm", null == adnm ? "未知" : adnm);
        params.put("year", year);

        Response fileResponse = ossService.getFile(filepath, filename);
        XWPFDocument document = new XWPFDocument(fileResponse.body().asInputStream());

        String patternString = "\\$\\{(" + StringUtils.join(params.keySet(), "|") + ")\\}";
        Pattern pattern = Pattern.compile(patternString);

        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            String text = paragraph.getText();
            Matcher matcher = pattern.matcher(text);

            StringBuffer sb = new StringBuffer();
            while (matcher.find()) {
                matcher.appendReplacement(sb, params.get(matcher.group(1)).toString());
            }
            matcher.appendTail(sb);

            String valueStr = sb.toString();
            List<XWPFRun> runs = paragraph.getRuns();
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                if (i == 0) {
                    run.setText(valueStr, 0);
                } else {
                    run.setText("", 0);
                }
            }
        }

        List<XWPFTable> tables = document.getTables();
        if (!CollectionUtils.isEmpty(tables)) {
            // 获取检查记录列表
            List<BnsInspectRecordsChild> records = baseDao.getBnsInspectRecordsChildList(new Page<>(1, -1), params).getRecords();
            Map<String, String> typeMap = baseDao.getBnsInspectTypeInfoList(null).stream().collect(Collectors.toMap(BnsInspectTypeInfo::getInspectCode, BnsInspectTypeInfo::getInspectName));

            XWPFTable table = tables.get(0);
            int startRow = 1;
            for (Map.Entry<String, List<String>> settings : reportSettings.entrySet()) {
                List<String> codes = settings.getValue();
                int fromRow = startRow;
                for (String code : codes) {
                    List<BnsInspectRecordsChild> children = records.stream().filter(itm -> code.equals(itm.getTypeCode())).collect(Collectors.toList());
                    if (children.isEmpty()) {
                        XWPFTableRow row = table.insertNewTableRow(startRow);
                        createCenteredCell(row).setText(settings.getKey());
                        String typeName = typeMap.get(code);
                        createCenteredCell(row).setText(StringUtils.isNotBlank(typeName) ? typeName : "无");
                        createCenteredCell(row).setText("无");
                        createCenteredCell(row).setText("无");
                        createCenteredCell(row).setText("无");
                        startRow = startRow + 1;
                    } else {
                        for (BnsInspectRecordsChild child : children) {
                            XWPFTableRow row = table.insertNewTableRow(startRow);
                            createCenteredCell(row).setText(settings.getKey());
                            if (StringUtils.isNotBlank(child.getName())) {
                                if (codes.size() > 1) {
                                    createCenteredCell(row).setText(child.getTypeName() + " - " + child.getName());
                                } else {
                                    createCenteredCell(row).setText(child.getName());
                                }
                            } else {
                                createCenteredCell(row).setText(child.getTypeName());
                            }
                            createCenteredCell(row).setText(child.getExistProblems());
                            createCenteredCell(row).setText(child.getRecAsk());
                            createCenteredCell(row).setText(DateUtil.format(child.getRecDate(), "yyyy/MM/dd"));
                            startRow = startRow + 1;
                        }
                    }
                }
                mergeCellsVertical(table, 0, fromRow, startRow - 1);
            }
        }

        // 创建字节流
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        // 将文档输出至流
        document.write(output);
        // 更新原始文件
        ossService.putFileToConvert(filepath.replace("/", "HTJLUP"), output.toByteArray());

        return filepath + ".pdf";
    }

    /**
     * 创建一个横向纵向均居中的单元格
     *
     * @param row 单元格所在行
     * @return 单元格
     */
    private XWPFTableCell createCenteredCell(XWPFTableRow row) {
        XWPFTableCell cell = row.createCell();
        List<XWPFParagraph> paragraphs = cell.getParagraphs();
        XWPFParagraph paragraph;
        if (!CollectionUtils.isEmpty(paragraphs)) {
            paragraph = paragraphs.get(0);
        } else {
            paragraph = cell.addParagraph();
        }
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        return cell;
    }

    /**
     * 纵向合并单元格
     *
     * @param table    表格
     * @param colIndex 列索引
     * @param fromRow  从第n行，n从零开始
     * @param toRow    到第n行，n从零开始
     */
    private void mergeCellsVertical(XWPFTable table, int colIndex, int fromRow, int toRow) {
        if (fromRow == toRow || fromRow > toRow) {
            return;
        }
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(colIndex);
            if (rowIndex == fromRow) {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }
}
