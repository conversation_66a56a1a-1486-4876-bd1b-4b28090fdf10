package com.huitu.cloud.api.ewci.hydropower.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.ewci.hydropower.entity.BsnShpwrR;
import com.huitu.cloud.api.ewci.hydropower.entity.ShpwrRVo;
import com.huitu.cloud.api.ewci.hydropower.mapper.BsnShpwrRDao;
import com.huitu.cloud.api.ewci.hydropower.service.BsnShpwrRService;
import com.huitu.cloud.util.AdcdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-08
 */
@Service
public class BsnShpwrRServiceImpl extends ServiceImpl<BsnShpwrRDao, BsnShpwrR> implements BsnShpwrRService {

    @Autowired
    private BsnShpwrRDao baseDao;

    @Override
    public IPage<ShpwrRVo> selectByPage(String adcd, String stnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        int nextLevel = level;
        if (level==2){
            nextLevel = 4;
        }else if(level==4){
            nextLevel = 6;
        }else if(level==6){
            nextLevel = 9;
        }else if(level==9){
            nextLevel = 12;
        }else if(level==12){
            nextLevel = 15;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("stnm", stnm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("nextLevel", nextLevel);
        IPage<ShpwrRVo> result = baseDao.selectByPage(page, param);
        return result;
    }
}
