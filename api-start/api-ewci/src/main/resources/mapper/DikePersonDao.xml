<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.DikePersonDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getListByStcd" resultType="com.huitu.cloud.api.ewci.person.entity.DikePerson">
        SELECT R.RV_NAME,
               T.DIKE_NAME,
               P1.REALNM XZ_REALNM,
               P1.POST   XZ_POST,
               P1.MOBILE XZ_MOBILE,
               P2.REALNM JS_REALNM,
               P2.POST   JS_POST,
               P2.MOBILE JS_MOBILE,
               P3.REALNM CJ_REALNM,
               P3.POST   CJ_POST,
               P3.MOBILE CJ_MOBILE,
               T.TCOUNT
        FROM (SELECT DISTINCT DIKE_CODE, RV_CODE, DIKE_NAME, TCOUNT
              FROM BNS_DIKEPERSON_B
              WHERE ADCD IN (SELECT DISTINCT (LEFT(ADCD, 9) + '000000') ADCD FROM BSN_STBPRP_V WHERE STCD = #{stcd})
                AND RV_CODE IN (SELECT A.OBJCD
                                FROM BSN_OBJONLY_B A
                                         LEFT JOIN BSN_OBJONLY_B B ON B.OBJID = A.OBJID
                                WHERE A.OBJTP = '4'
                                  AND B.OBJTP = '1'
                                  AND B.OBJCD = #{stcd})) T
                 LEFT JOIN ATT_RV_BASE R ON R.RV_CODE = T.RV_CODE
                 LEFT JOIN BNS_DIKEPERSON_B P1 ON P1.DIKE_CODE = T.DIKE_CODE AND P1.PERTP = '1'
                 LEFT JOIN BNS_DIKEPERSON_B P2 ON P2.DIKE_CODE = T.DIKE_CODE AND P2.PERTP = '2'
                 LEFT JOIN BNS_DIKEPERSON_B P3 ON P3.DIKE_CODE = T.DIKE_CODE AND P3.PERTP = '3'
        ORDER BY T.RV_CODE, T.DIKE_NAME ASC
    </select>
    <select id="getPageList" useCache="false" resultType="com.huitu.cloud.api.ewci.person.entity.DikePerson">
        SELECT ROW_NUMBER() OVER(ORDER BY T.ADCD, T.RV_CODE, T.DIKE_NAME ASC) SORTNO, A.ADNM, B.ADNM TOWN, D.RV_NAME,
        T.DIKE_CODE,T.ADCD,T.RV_CODE,T.DIKE_NAME, T.START_LGTD, T.START_LTTD, T.END_LGTD, T.END_LTTD, T.DIKE_TYPE,
        T.DIKE_LEN, T.DIKE_BANK, P1.REALNM
        XZ_REALNM, P1.POST XZ_POST, P1.MOBILE XZ_MOBILE, P2.REALNM JS_REALNM, P2.POST JS_POST, P2.MOBILE JS_MOBILE,
        P3.REALNM CJ_REALNM, P3.POST CJ_POST, P3.MOBILE CJ_MOBILE, T.TCOUNT,
        COALESCE(P1.TS,P2.TS,P3.TS) TS
        FROM (
        SELECT DISTINCT DIKE_CODE, ADCD, RV_CODE, DIKE_NAME, START_LGTD, START_LTTD, END_LGTD, END_LTTD, DIKE_TYPE,
        DIKE_LEN, DIKE_BANK, TCOUNT FROM BNS_DIKEPERSON_B
        WHERE LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.pernm != null and map.pernm != ''">
            AND CHARINDEX(#{map.pernm}, REALNM) > 0
        </if>
        <if test="map.udtm !=null and map.udtm =='1'.toString()">
            and YEAR(TS) != YEAR(GETDATE())
        </if>
        ) T
        LEFT JOIN MDT_ADCDINFO_B A ON A.ADCD = LEFT (T.ADCD, 6) + '*********'
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = LEFT (T.ADCD, 9) + '000000'
        LEFT JOIN ATT_RV_BASE D ON D.RV_CODE = T.RV_CODE
        LEFT JOIN BNS_DIKEPERSON_B P1 ON P1.DIKE_CODE = T.DIKE_CODE AND P1.PERTP = '1'
        LEFT JOIN BNS_DIKEPERSON_B P2 ON P2.DIKE_CODE = T.DIKE_CODE AND P2.PERTP = '2'
        LEFT JOIN BNS_DIKEPERSON_B P3 ON P3.DIKE_CODE = T.DIKE_CODE AND P3.PERTP = '3'
        <where>
            <if test="map.rvName != null and map.rvName != ''">
                AND CHARINDEX(#{map.rvName}, D.RV_NAME) > 0
            </if>
            <if test="map.dikeName != null and map.dikeName != ''">
                AND CHARINDEX(#{map.dikeName}, T.DIKE_NAME) > 0
            </if>
        </where>
        ORDER BY T.ADCD, T.RV_CODE, T.DIKE_NAME ASC
    </select>
    <select id="batchImport" statementType="CALLABLE" useCache="false"
            resultType="com.huitu.cloud.api.ewci.person.entity.BnsDikePersonTmp">
        {CALL PROC_IMPORT_DIKE_PERSON(#{batchNo, mode=IN})}
    </select>
    <select id="getSimpleList" resultType="com.huitu.cloud.api.ewci.person.entity.PersonInfo">
        SELECT REALNM, MOBILE FROM BNS_DIKEPERSON_B
        WHERE MOBILE IS NOT NULL AND LTRIM(RTRIM(MOBILE)) != ''
        AND LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.pertps != null and map.pertps.size() != 0">
            AND PERTP IN (<foreach collection="map.pertps" item="pertp" separator=",">#{pertp}</foreach>)
        </if>
        ORDER BY ADCD, RV_CODE, DIKE_NAME, PERTP ASC
    </select>
    <select id="getAllList" resultType="com.huitu.cloud.api.ewci.person.entity.DikePersonP">
        SELECT SZ.ADNM      CITY,
               XS.ADNM      COUNTY,
               XZ.ADNM      TOWN,
               RV.RV_NAME,
               T.DIKE_NAME,
               T.START_LGTD,
               T.START_LTTD,
               T.END_LGTD,
               T.END_LTTD,
               T.DIKE_TYPE,
               T.DIKE_LEN,
               T.DIKE_BANK,
               P1.REALNM    XZ_REALNM,
               P1.POST      XZ_POST,
               P1.MOBILE    XZ_MOBILE,
               P2.REALNM    JS_REALNM,
               P2.POST      JS_POST,
               P2.MOBILE    JS_MOBILE,
               P3.REALNM    CJ_REALNM,
               P3.POST      CJ_POST,
               P3.MOBILE    CJ_MOBILE,
               T.TCOUNT,
               ROW_NUMBER() OVER(ORDER BY SH.SORTNO, XZ.ADCD, T.RV_CODE, T.DIKE_NAME ASC) SORTNO
        FROM (SELECT DISTINCT DIKE_CODE,
                              ADCD,
                              RV_CODE,
                              DIKE_NAME,
                              START_LGTD,
                              START_LTTD,
                              END_LGTD,
                              END_LTTD,
                              DIKE_TYPE,
                              DIKE_LEN,
                              DIKE_BANK,
                              TCOUNT
              FROM BNS_DIKEPERSON_B) T
                 LEFT JOIN MDT_ADCDINFO_B XZ ON XZ.ADCD = T.ADCD
                 LEFT JOIN MDT_ADCDINFO_B XS ON XS.ADCD = XZ.PADCD
                 LEFT JOIN MDT_ADCDINFO_B SZ ON SZ.ADCD = (CASE XS.ADLVL WHEN 2 THEN XS.ADCD ELSE XS.PADCD END)
                 LEFT JOIN MDT_ADCDINFO_E SH ON SH.ADCD = SZ.ADCD
                 LEFT JOIN ATT_RV_BASE RV ON RV.RV_CODE = T.RV_CODE
                 LEFT JOIN BNS_DIKEPERSON_B P1 ON P1.DIKE_CODE = T.DIKE_CODE AND P1.PERTP = '1'
                 LEFT JOIN BNS_DIKEPERSON_B P2 ON P2.DIKE_CODE = T.DIKE_CODE AND P2.PERTP = '2'
                 LEFT JOIN BNS_DIKEPERSON_B P3 ON P3.DIKE_CODE = T.DIKE_CODE AND P3.PERTP = '3'
    </select>
    <select id="getDikePersonSummaryList"
            resultType="com.huitu.cloud.api.ewci.person.entity.DikeSummaryVo">
        SELECT ADCD, ADNM, PADCD, ALLNUM, XZCOUNT, JSCOUNT, CJCOUNT FROM (
        <if test="map.include == '1'.toString()">
            SELECT 0 SORTNO, A.ADCD ADCD, B.ADNM ADNM, B.PADCD PADCD,A.ALLNUM ,A.XZCOUNT, A.JSCOUNT, A.CJCOUNT FROM (
            SELECT #{map.adcd} ADCD,COUNT(DISTINCT A.REALNM )
            ALLNUM,
            COUNT(DISTINCT B.REALNM ) XZCOUNT,
            COUNT(DISTINCT C.REALNM ) JSCOUNT,
            COUNT(DISTINCT D.REALNM ) CJCOUNT
            FROM BNS_DIKEPERSON_B A
            LEFT JOIN BNS_DIKEPERSON_B B ON A.ADCD = B.ADCD AND A.DIKE_CODE = B.DIKE_CODE AND A.RV_CODE = B.RV_CODE AND
            B.PERTP = '1'
            LEFT JOIN BNS_DIKEPERSON_B C ON A.ADCD = C.ADCD AND A.DIKE_CODE = C.DIKE_CODE AND A.RV_CODE = C.RV_CODE AND
            C.PERTP = '2'
            LEFT JOIN BNS_DIKEPERSON_B D ON A.ADCD = D.ADCD AND A.DIKE_CODE = D.DIKE_CODE AND A.RV_CODE = D.RV_CODE AND
            D.PERTP = '3'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">
                AND LEFT(A.ADCD, 6) NOT IN ('220581')
            </if>
            ) A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
            WHERE EXISTS (SELECT 8 FROM MDT_ADCDINFO_E WHERE ADTP = '1' AND ADCD = A.ADCD)
        </if>
        <if test="map.include == '1'.toString() and map.level &lt;= 4">
            UNION ALL
        </if>
        <if test="map.level &lt;= 4">
            SELECT B.SORTNO, A.ADCD ADCD, A.ADNM ADNM, A.PADCD PADCD,C.ALLNUM ,C.XZCOUNT, C.JSCOUNT, C.CJCOUNT
            FROM MDT_ADCDINFO_B A
            LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
            LEFT JOIN (SELECT ADCD, COUNT(DISTINCT AREALNM ) ALLNUM,
            COUNT(DISTINCT BREALNM ) XZCOUNT,
            COUNT(DISTINCT CREALNM ) JSCOUNT,
            COUNT(DISTINCT DREALNM ) CJCOUNT
            FROM (SELECT LEFT(LEFT(A.ADCD, #{map.lowLevel}) + '*********000000', 15) ADCD, A.MOBILE AMOBILE, A.REALNM
            AREALNM,
            B.MOBILE BMOBILE, B.REALNM BREALNM, C.MOBILE CMOBILE, C.REALNM CREALNM, D.MOBILE DMOBILE, D.REALNM DREALNM
            FROM BNS_DIKEPERSON_B A
            LEFT JOIN BNS_DIKEPERSON_B B ON A.ADCD = B.ADCD AND A.DIKE_CODE = B.DIKE_CODE AND A.RV_CODE = B.RV_CODE AND
            B.PERTP = '1'
            LEFT JOIN BNS_DIKEPERSON_B C ON A.ADCD = C.ADCD AND A.DIKE_CODE = C.DIKE_CODE AND A.RV_CODE = C.RV_CODE AND
            C.PERTP = '2'
            LEFT JOIN BNS_DIKEPERSON_B D ON A.ADCD = D.ADCD AND A.DIKE_CODE = D.DIKE_CODE AND A.RV_CODE = D.RV_CODE AND
            D.PERTP = '3'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND LEFT(A.ADCD, 6) NOT IN ('220581')
            <if test="map.level == '2'.toString()">
                UNION ALL
                SELECT '220581*********' ADCD, A.MOBILE AMOBILE, A.REALNM AREALNM,
                B.MOBILE BMOBILE, B.REALNM BREALNM, C.MOBILE CMOBILE, C.REALNM CREALNM, D.MOBILE DMOBILE, D.REALNM
                DREALNM
                FROM BNS_DIKEPERSON_B A
                LEFT JOIN BNS_DIKEPERSON_B B ON A.ADCD = B.ADCD AND A.DIKE_CODE = B.DIKE_CODE AND A.RV_CODE = B.RV_CODE
                AND B.PERTP = '1'
                LEFT JOIN BNS_DIKEPERSON_B C ON A.ADCD = C.ADCD AND A.DIKE_CODE = C.DIKE_CODE AND A.RV_CODE = C.RV_CODE
                AND C.PERTP = '2'
                LEFT JOIN BNS_DIKEPERSON_B D ON A.ADCD = D.ADCD AND A.DIKE_CODE = D.DIKE_CODE AND A.RV_CODE = D.RV_CODE
                AND D.PERTP = '3'
                WHERE LEFT(A.ADCD, 6) = '220581'
            </if>
            ) A GROUP BY ADCD) C ON C.ADCD = A.ADCD
            WHERE A.PADCD = #{map.adcd}
        </if>
        ) A
        ORDER BY SORTNO ASC
    </select>

    <delete id="delByDikeCode">
        DELETE
        FROM BNS_DIKEPERSON_B
        WHERE DIKE_CODE = #{dikeCode}
    </delete>
    <insert id="insertList">
        INSERT INTO
        BNS_DIKEPERSON_B(DIKE_CODE,ADCD,RV_CODE,DIKE_NAME,START_LGTD,START_LTTD,END_LGTD,END_LTTD,DIKE_TYPE,DIKE_LEN,DIKE_BANK,PERTP,REALNM,POST,MOBILE,TCOUNT,TS)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.dikeCode},#{item.adcd},#{item.rvCode},#{item.dikeName},#{item.startLgtd},#{item.startLttd},#{item.endLgtd},#{item.endLttd},
            #{item.dikeType},#{item.dikeLen},#{item.dikeBank},#{item.pertp},#{item.realnm},#{item.post},#{item.mobile},#{item.tcount},#{item.ts})
        </foreach>
    </insert>

</mapper>
