package com.huitu.cloud.api.hsfxt.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * <p>
 * 风险分析成果基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-28
 */
@TableName("KB_RVSANALY_G")
@ApiModel(value="KbRvsanalyG对象", description="风险分析成果基本信息表")
public class KbRvsanalyG extends KbRscminB {

    @ApiModelProperty(value = "成果编号")
    @TableId(value = "CID", type = IdType.NONE)
    private String cid;

    @ApiModelProperty(value = "成果名称")
    @TableField("CNM")
    private String cnm;

    @ApiModelProperty(value = "溃口宽度")
    @TableField("BWwidth")
    private Double bwwidth;

    @ApiModelProperty(value = "溃口深度")
    @TableField("BWdepth")
    private Double bwdepth;

    @ApiModelProperty(value = "坝顶高程")
    @TableField("Dmtpel")
    private Double dmtpel;

    @ApiModelProperty(value = "坝顶高程对应库容")
    @TableField("Dmtpst")
    private Double dmtpst;

    @ApiModelProperty(value = "溃口底高程")
    @TableField("BWbotel")
    private Double bwbotel;

    @ApiModelProperty(value = "溃口底高程对应库容")
    @TableField("BWbotst")
    private Double bwbotst;

    @ApiModelProperty(value = "断面间隔")
    @TableField("RSint")
    private Double rsint;

    @ApiModelProperty(value = "断面（总）点数")
    @TableField("RSnum")
    private Double rsnum;

    @ApiModelProperty(value = "水库下游DEM")
    @TableField("Demfile")
    private String demfile;

    @ApiModelProperty(value = "河道中心线SHP")
    @TableField("RCshpfile")
    private String rcshpfile;

    @ApiModelProperty(value = "淹没范围SHP")
    @TableField("RSshpfile")
    private String rsshpfile;

    @ApiModelProperty(value = "淹没范围图片")
    @TableField("RSimgfile")
    private String rsimgfile;

    @ApiModelProperty(value = "淹没范围DEM")
    @TableField("RSdemfile")
    private String rsdemfile;

    @ApiModelProperty(value = "淹没范围最小X")
    @TableField("EMINX")
    private Double eminx;

    @ApiModelProperty(value = "淹没范围最小Y")
    @TableField("EMINY")
    private Double eminy;

    @ApiModelProperty(value = "淹没范围最大X")
    @TableField("EMAXX")
    private Double emaxx;

    @ApiModelProperty(value = "淹没范围最大Y")
    @TableField("EMAXY")
    private Double emaxy;

    @ApiModelProperty(value = "淹没面积")
    @TableField("RSarea")
    private Double rsarea;

    @ApiModelProperty(value = "溃口最大流量")
    @TableField("BWQ")
    private Double bwq;

    @ApiModelProperty(value = "备注")
    @TableField("RM")
    private String rm;

    @ApiModelProperty(value = "创建时间")
    @TableField("TM")
    private String tm;

    @ApiModelProperty(value = "政区名称")
    private String adnm;


    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getCnm() {
        return cnm;
    }

    public void setCnm(String cnm) {
        this.cnm = cnm;
    }

    public Double getBwwidth() {
        return bwwidth;
    }

    public void setBwwidth(Double bwwidth) {
        this.bwwidth = bwwidth;
    }

    public Double getBwdepth() {
        return bwdepth;
    }

    public void setBwdepth(Double bwdepth) {
        this.bwdepth = bwdepth;
    }

    public Double getDmtpel() {
        return dmtpel;
    }

    public void setDmtpel(Double dmtpel) {
        this.dmtpel = dmtpel;
    }

    public Double getDmtpst() {
        return dmtpst;
    }

    public void setDmtpst(Double dmtpst) {
        this.dmtpst = dmtpst;
    }

    public Double getBwbotel() {
        return bwbotel;
    }

    public void setBwbotel(Double bwbotel) {
        this.bwbotel = bwbotel;
    }

    public Double getBwbotst() {
        return bwbotst;
    }

    public void setBwbotst(Double bwbotst) {
        this.bwbotst = bwbotst;
    }

    public Double getRsint() {
        return rsint;
    }

    public void setRsint(Double rsint) {
        this.rsint = rsint;
    }

    public Double getRsnum() {
        return rsnum;
    }

    public void setRsnum(Double rsnum) {
        this.rsnum = rsnum;
    }

    public String getDemfile() {
        return demfile;
    }

    public void setDemfile(String demfile) {
        this.demfile = demfile;
    }

    public String getRcshpfile() {
        return rcshpfile;
    }

    public void setRcshpfile(String rcshpfile) {
        this.rcshpfile = rcshpfile;
    }

    public String getRsshpfile() {
        return rsshpfile;
    }

    public void setRsshpfile(String rsshpfile) {
        this.rsshpfile = rsshpfile;
    }

    public String getRsimgfile() {
        return rsimgfile;
    }

    public void setRsimgfile(String rsimgfile) {
        this.rsimgfile = rsimgfile;
    }

    public String getRsdemfile() {
        return rsdemfile;
    }

    public void setRsdemfile(String rsdemfile) {
        this.rsdemfile = rsdemfile;
    }

    public Double getEminx() {
        return eminx;
    }

    public void setEminx(Double eminx) {
        this.eminx = eminx;
    }

    public Double getEminy() {
        return eminy;
    }

    public void setEminy(Double eminy) {
        this.eminy = eminy;
    }

    public Double getEmaxx() {
        return emaxx;
    }

    public void setEmaxx(Double emaxx) {
        this.emaxx = emaxx;
    }

    public Double getEmaxy() {
        return emaxy;
    }

    public void setEmaxy(Double emaxy) {
        this.emaxy = emaxy;
    }

    public Double getRsarea() {
        return rsarea;
    }

    public void setRsarea(Double rsarea) {
        this.rsarea = rsarea;
    }

    public Double getBwq() {
        return bwq;
    }

    public void setBwq(Double bwq) {
        this.bwq = bwq;
    }

    public String getRm() {
        return rm;
    }

    public void setRm(String rm) {
        this.rm = rm;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
