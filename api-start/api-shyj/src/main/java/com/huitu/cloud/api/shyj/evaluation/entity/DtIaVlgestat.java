package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
/**
 * <p>
 * 社会经济情况统计
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
@ApiModel(value="DtIaVlgestat对象", description="社会经济情况统计")
public class DtIaVlgestat extends DtExtendInfo{

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "土地面积（km2）")
    private Double ldarea;

    @ApiModelProperty(value = "乡(镇)个数(个)")
    private Double xzcount;

    @ApiModelProperty(value = "村民委员会个数(个)")
    private Double cmwyh;

    @ApiModelProperty(value = "年末总户数（户）")
    private Double nmhs;

    @ApiModelProperty(value = "乡村户数（户）")
    private Double xchs;

    @ApiModelProperty(value = "年末总人口（人）")
    private Double nmzrk;

    @ApiModelProperty(value = "乡村人口（人）")
    private Double xcrk;

    @ApiModelProperty(value = "年末单位从业人员数（人）")
    private Double nmdwcy;

    @ApiModelProperty(value = "乡村从业人员数（人）")
    private Double xccy;

    @ApiModelProperty(value = "农林牧渔业从业人（人）")
    private Double llmy;

    @ApiModelProperty(value = "农业机械总动力（万千瓦特）")
    private Double lyjx;

    @ApiModelProperty(value = "固定电话用户（户）")
    private Double phonen;

    @ApiModelProperty(value = "第一产业增加值（万元）")
    private Double dycy;

    @ApiModelProperty(value = "第二产业增加值（万元）")
    private Double drcy;

    @ApiModelProperty(value = "地方财政一般预算收入（万元）")
    private Double czys;

    @ApiModelProperty(value = "地方财政一般预算支出（万元）")
    private Double czzc;

    @ApiModelProperty(value = "城乡居民储蓄存款余额（万元）")
    private Double cxck;

    @ApiModelProperty(value = "年末金融机构各项贷款余额（万元）")
    private Double jrdk;

    @ApiModelProperty(value = "粮食总产量（吨）")
    private Double nscl;

    @ApiModelProperty(value = "棉花产量（吨）")
    private Double mhcl;

    @ApiModelProperty(value = "油料产量（吨）")
    private Double ulcl;

    @ApiModelProperty(value = "肉类总产量（吨）")
    private Double rlcl;

    @ApiModelProperty(value = "规模以上工业企业个数（个）")
    private Double gyqyn;

    @ApiModelProperty(value = "规模以上工业总产值(现价)（万元）")
    private Double gyqycz;

    @ApiModelProperty(value = "固定资产投资(不含农户)（万元）")
    private Double gdzctz;

    @ApiModelProperty(value = "普通中学在校学生数（人）")
    private Double zxzxs;

    @ApiModelProperty(value = "小学在校学生数（人）")
    private Double xxzxs;

    @ApiModelProperty(value = "医院、卫生院床位数（床）")
    private Double yycws;

    @ApiModelProperty(value = "各种社会福利收养性单位数（个）")
    private Double fldws;

    @ApiModelProperty(value = "各种社会福利收养性单位床位数（床）")
    private Double flcws;

    @ApiModelProperty(value = "下级统计")
    private List<DtIaVlgestat> children;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getLdarea() {
        return ldarea;
    }

    public void setLdarea(Double ldarea) {
        this.ldarea = ldarea;
    }

    public Double getXzcount() {
        return xzcount;
    }

    public void setXzcount(Double xzcount) {
        this.xzcount = xzcount;
    }

    public Double getCmwyh() {
        return cmwyh;
    }

    public void setCmwyh(Double cmwyh) {
        this.cmwyh = cmwyh;
    }

    public Double getNmhs() {
        return nmhs;
    }

    public void setNmhs(Double nmhs) {
        this.nmhs = nmhs;
    }

    public Double getXchs() {
        return xchs;
    }

    public void setXchs(Double xchs) {
        this.xchs = xchs;
    }

    public Double getNmzrk() {
        return nmzrk;
    }

    public void setNmzrk(Double nmzrk) {
        this.nmzrk = nmzrk;
    }

    public Double getXcrk() {
        return xcrk;
    }

    public void setXcrk(Double xcrk) {
        this.xcrk = xcrk;
    }

    public Double getNmdwcy() {
        return nmdwcy;
    }

    public void setNmdwcy(Double nmdwcy) {
        this.nmdwcy = nmdwcy;
    }

    public Double getXccy() {
        return xccy;
    }

    public void setXccy(Double xccy) {
        this.xccy = xccy;
    }

    public Double getLlmy() {
        return llmy;
    }

    public void setLlmy(Double llmy) {
        this.llmy = llmy;
    }

    public Double getLyjx() {
        return lyjx;
    }

    public void setLyjx(Double lyjx) {
        this.lyjx = lyjx;
    }

    public Double getPhonen() {
        return phonen;
    }

    public void setPhonen(Double phonen) {
        this.phonen = phonen;
    }

    public Double getDycy() {
        return dycy;
    }

    public void setDycy(Double dycy) {
        this.dycy = dycy;
    }

    public Double getDrcy() {
        return drcy;
    }

    public void setDrcy(Double drcy) {
        this.drcy = drcy;
    }

    public Double getCzys() {
        return czys;
    }

    public void setCzys(Double czys) {
        this.czys = czys;
    }

    public Double getCzzc() {
        return czzc;
    }

    public void setCzzc(Double czzc) {
        this.czzc = czzc;
    }

    public Double getCxck() {
        return cxck;
    }

    public void setCxck(Double cxck) {
        this.cxck = cxck;
    }

    public Double getJrdk() {
        return jrdk;
    }

    public void setJrdk(Double jrdk) {
        this.jrdk = jrdk;
    }

    public Double getNscl() {
        return nscl;
    }

    public void setNscl(Double nscl) {
        this.nscl = nscl;
    }

    public Double getMhcl() {
        return mhcl;
    }

    public void setMhcl(Double mhcl) {
        this.mhcl = mhcl;
    }

    public Double getUlcl() {
        return ulcl;
    }

    public void setUlcl(Double ulcl) {
        this.ulcl = ulcl;
    }

    public Double getRlcl() {
        return rlcl;
    }

    public void setRlcl(Double rlcl) {
        this.rlcl = rlcl;
    }

    public Double getGyqyn() {
        return gyqyn;
    }

    public void setGyqyn(Double gyqyn) {
        this.gyqyn = gyqyn;
    }

    public Double getGyqycz() {
        return gyqycz;
    }

    public void setGyqycz(Double gyqycz) {
        this.gyqycz = gyqycz;
    }

    public Double getGdzctz() {
        return gdzctz;
    }

    public void setGdzctz(Double gdzctz) {
        this.gdzctz = gdzctz;
    }

    public Double getZxzxs() {
        return zxzxs;
    }

    public void setZxzxs(Double zxzxs) {
        this.zxzxs = zxzxs;
    }

    public Double getXxzxs() {
        return xxzxs;
    }

    public void setXxzxs(Double xxzxs) {
        this.xxzxs = xxzxs;
    }

    public Double getYycws() {
        return yycws;
    }

    public void setYycws(Double yycws) {
        this.yycws = yycws;
    }

    public Double getFldws() {
        return fldws;
    }

    public void setFldws(Double fldws) {
        this.fldws = fldws;
    }

    public Double getFlcws() {
        return flcws;
    }

    public void setFlcws(Double flcws) {
        this.flcws = flcws;
    }

    public List<DtIaVlgestat> getChildren() {
        return children;
    }

    public void setChildren(List<DtIaVlgestat> children) {
        this.children = children;
    }
}
