<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwWarningProcessDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getProcessList" resultType="com.huitu.cloud.api.ew.entity.response.EwWarningProcessResponse">
        SELECT A.WARN_ID,
               <PERSON><PERSON>RN_<PERSON>RADE_ID,
               C.GRADE_NAME   WARN_GRADE_NAME,
               C.ALIAS_NAME   WARN_GRADE_ALIAS_NAME,
               A.WARN_MODEL,
               A.WARN_TIME,
               A.WARN_DESC,
               B.WARN_STDT,
               B.ADCD,
               E.ADNM,
               D.WSCD,
               <PERSON>.ST<PERSON>,
               F.ST<PERSON>,
               D.STM,
               D.ETM,
               D.<PERSON>,
               <PERSON><PERSON>_<PERSON>,
               D.<PERSON>,
               D.SLEP,
               A.<PERSON>,
               A.<PERSON>_ID,
               G.ISSUING_UNIT PLATFORM_NAME
        FROM EW_WARNING_PROCESS A
                 LEFT JOIN EW_WARNING_RECORD B ON B.WARN_ID = A.WARN_ID
                 LEFT JOIN EW_WARNING_GRADE C ON C.GRADE_ID = A.WARN_GRADE_ID
                 LEFT JOIN EW_SNAPSHOT_MONITOR D ON D.WARN_ID = A.WARN_ID AND D.WARN_GRADE_ID = A.WARN_GRADE_ID
                 LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = B.ADCD
                 LEFT JOIN ST_STBPRP_B F ON F.STCD = D.STCD
                 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID
        WHERE A.WARN_ID = #{warnId}
        ORDER BY A.WARN_TIME ASC
    </select>
    <insert id="insert">
        INSERT INTO EW_WARNING_PROCESS(WARN_ID, WARN_GRADE_ID, WARN_MODEL, WARN_TIME, WARN_DESC, LATEST_TIME, PLATFORM_ID)
        SELECT WARN_ID, WARN_GRADE_ID, WARN_MODEL, WARN_TIME, WARN_DESC, GETDATE() LATEST_TIME, PLATFORM_ID
        FROM EW_WARNING_RECORD
        WHERE WARN_ID = #{warnId}
    </insert>
</mapper>