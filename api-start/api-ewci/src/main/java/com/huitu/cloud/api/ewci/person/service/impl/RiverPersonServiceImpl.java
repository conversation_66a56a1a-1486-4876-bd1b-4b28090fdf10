package com.huitu.cloud.api.ewci.person.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.exception.RiverPersonImportException;
import com.huitu.cloud.api.ewci.person.mapper.RiverPersonDao;
import com.huitu.cloud.api.ewci.person.mapper.RiverPersonTmpDao;
import com.huitu.cloud.api.ewci.person.service.RiverPersonService;
import com.huitu.cloud.util.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 江河责任人管理服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class RiverPersonServiceImpl implements RiverPersonService {

    private static final Logger logger = LoggerFactory.getLogger(RiverPersonServiceImpl.class);

    private RiverPersonDao baseDao;
    private RiverPersonTmpDao tmpDao;

    @Autowired
    public void setBaseDao(RiverPersonDao baseDao) {
        this.baseDao = baseDao;
    }

    @Autowired
    public void setTmpDao(RiverPersonTmpDao tmpDao) {
        this.tmpDao = tmpDao;
    }

    @Override
    public IPage<RiverPerson> getPageList(RiverPersonQuery query) {
        return baseDao.getPageList(query.toPageParam(), query.toQueryParam());
    }

    @Override
    public List<RiverSummaryVo> getRiverSummaryList(RiverSummaryQuery query) {
        if (query.getLevel() > 6) {
            throw new RuntimeException("行政区划代码无效，仅支持到县以上级别（含县）");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", query.getAdcd());
        params.put("level", query.getLevel());
        params.put("include", query.isInclude() ? 1 : 0);
        params.put("lowLevel", query.getLowLevel());
        List<RiverSummaryVo> source = baseDao.getRiverSummaryList(params);

        if (CollectionUtils.isEmpty(source)) {
            return new ArrayList<>();
        }
        if (!query.isInclude()) {
            // 不包含下级，无需创建树
            return source;
        }
        Map<String, RiverSummaryVo> center = new LinkedHashMap<>(source.size());
        source.forEach(ad -> center.put(ad.getAdcd(), ad));
        List<RiverSummaryVo> target = new ArrayList<>();
        for (RiverSummaryVo node : source) {
            if (center.containsKey(node.getPadcd())) {
                RiverSummaryVo parent = center.get(node.getPadcd());
                if (null == parent.getChildren()) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            } else {
                target.add(node);
            }
        }
        return target;
    }

    @Override
    public void dataExport(RiverPersonQuery query, OutputStream output) {
        IPage<RiverPerson> page = baseDao.getPageList(query.toPageParam(), query.toQueryParam());
        EasyExcel.write(output, RiverPerson.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("江河责任人信息")
                .doWrite(page.getRecords());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult<BnsRiverPersonTmp> dataImport(String xadcd, InputStream input) {
        List<BnsRiverPersonTmp> tmpData;
        try {
            tmpData = EasyExcel.read(input, BnsRiverPersonTmp.class, null)
                    .headRowNumber(2).sheet().doReadSync();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);

            throw new RiverPersonImportException("Excel文件格式不正确，解析失败", ex);
        }

        if (CollectionUtils.isEmpty(tmpData)) {
            throw new RiverPersonImportException("Excel文件中不包含任何数据，请检查");
        }
        // 过滤空数据，任何一项有数据即为有效
        tmpData = tmpData.stream().filter(this::nonNulProperty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tmpData)) {
            throw new RiverPersonImportException("Excel文件中数据无效，请检查");
        }

        // 去掉多余的空格
        tmpData.forEach(this::trimProperty);

        try {
            String batchNo = batchInsertTmpData(xadcd, tmpData);
            List<BnsRiverPersonTmp> errorData = baseDao.batchImport(batchNo);
            if (!CollectionUtils.isEmpty(errorData)) {
                ImportResult<BnsRiverPersonTmp> result = new ImportResult<>();
                result.setStatus(tmpData.size() == errorData.size() ? 0 : 2);
                result.setMessage(String.format("数据（%s）导入失败，请检查", result.getStatus() == 0 ? "全部" : "部分"));
                result.setData(errorData);
                return result;
            }
        } catch (Exception ex) {
            logger.error("江河责任人信息入库失败", ex);

            throw new RiverPersonImportException("数据未能成功入库，导入失败", tmpData, ex);
        }
        return new ImportResult<>();
    }

    @Override
    public List<PersonInfo> getSimplePersonList(PersonQuery query) {
        List<PersonInfo> persons = baseDao.getSimpleList(query.toQueryParam());
        if (!CollectionUtils.isEmpty(persons)) {
            persons = persons.stream().distinct().collect(Collectors.toList());
        }
        return persons;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int riverPersonEdit(RiverPerson entity) {
        List<RiverPersonB> list = new ArrayList<>();
        // 行政责任人
        if (StringUtils.isNotBlank(entity.getXzRealnm()) && StringUtils.isNotBlank(entity.getXzMobilePhone())) {
            RiverPersonB xzPerson = new RiverPersonB();
            xzPerson.setRvCode(entity.getRvCode());
            xzPerson.setBasCode(entity.getBasCode());
            xzPerson.setAdcd(entity.getAdcd());
            xzPerson.setPertp("1");
            xzPerson.setSno(entity.getSno());
            xzPerson.setRealnm(entity.getXzRealnm());
            xzPerson.setPost(entity.getXzPost());
            xzPerson.setOfficePhone(entity.getXzOfficePhone());
            xzPerson.setMobilePhone(entity.getXzMobilePhone());
            xzPerson.setTs(LocalDateTime.now());
            list.add(xzPerson);
        }

        // 技术责任人
        if (StringUtils.isNotBlank(entity.getJsRealnm()) && StringUtils.isNotBlank(entity.getJsMobilePhone())) {
            RiverPersonB jsPerson = new RiverPersonB();
            jsPerson.setRvCode(entity.getRvCode());
            jsPerson.setBasCode(entity.getBasCode());
            jsPerson.setAdcd(entity.getAdcd());
            jsPerson.setPertp("2");
            jsPerson.setSno(entity.getSno());
            jsPerson.setRealnm(entity.getJsRealnm());
            jsPerson.setPost(entity.getJsPost());
            jsPerson.setOfficePhone(entity.getJsOfficePhone());
            jsPerson.setMobilePhone(entity.getJsMobilePhone());
            jsPerson.setTs(LocalDateTime.now());
            list.add(jsPerson);
        }
        Map<String, Object> param = new HashMap<>();
        param.put("rvCode", entity.getRvCode());
        param.put("adcd", entity.getAdcd());
        param.put("sno", entity.getSno());
        int insertCode = 0;
        int delCode = baseDao.delByRvCodeAndAdcdAndSno(param);
        if (delCode > 0) {
            insertCode = baseDao.insertList(list);
        }
        return insertCode;
    }

    /**
     * 批量插入临时数据
     *
     * @param xadcd   县级行政区划代码
     * @param persons 责任人信息集合
     * @return 批号
     **/
    private String batchInsertTmpData(String xadcd, List<BnsRiverPersonTmp> persons) {
        String batchNo = UUID.randomUUID().toString();
        if (!CollectionUtils.isEmpty(persons)) {
            Map<String, Object> params = new HashMap<>();
            params.put("batchNo", batchNo);
            params.put("xadcd", xadcd);
            List<List<BnsRiverPersonTmp>> lists = ListUtils.splitList(persons, 150);
            for (List<BnsRiverPersonTmp> list : lists) {
                params.put("list", list);
                tmpDao.batchInsert(params);
            }
        }
        return batchNo;
    }

    /**
     * 属性不为空
     *
     * @param tmp 临时信息
     * @return true: 非空  false: 空
     **/
    private boolean nonNulProperty(BnsRiverPersonTmp tmp) {
        return StringUtils.isNotBlank(tmp.getBasName())
                || StringUtils.isNotBlank(tmp.getRvName())
                || StringUtils.isNotBlank(tmp.getAdnm())
                || StringUtils.isNotBlank(tmp.getXzRealnm())
                || StringUtils.isNotBlank(tmp.getXzPost())
                || StringUtils.isNotBlank(tmp.getXzOfficePhone())
                || StringUtils.isNotBlank(tmp.getXzMobilePhone())
                || StringUtils.isNotBlank(tmp.getJsRealnm())
                || StringUtils.isNotBlank(tmp.getJsPost())
                || StringUtils.isNotBlank(tmp.getJsOfficePhone())
                || StringUtils.isNotBlank(tmp.getJsMobilePhone());
    }

    /**
     * 去掉字符串属性的空格（两端）
     *
     * @param tmp 临时信息
     **/
    private void trimProperty(BnsRiverPersonTmp tmp) {
        tmp.setBasName(StringUtils.trim(tmp.getBasName()));
        tmp.setRvName(StringUtils.trim(tmp.getRvName()));
        tmp.setAdnm(StringUtils.trim(tmp.getAdnm()));
        tmp.setXzRealnm(StringUtils.trim(tmp.getXzRealnm()));
        tmp.setXzPost(StringUtils.trim(tmp.getXzPost()));
        tmp.setXzOfficePhone(StringUtils.trim(tmp.getXzOfficePhone()));
        tmp.setXzMobilePhone(StringUtils.trim(tmp.getXzMobilePhone()));
        tmp.setJsRealnm(StringUtils.trim(tmp.getJsRealnm()));
        tmp.setJsPost(StringUtils.trim(tmp.getJsPost()));
        tmp.setJsOfficePhone(StringUtils.trim(tmp.getJsOfficePhone()));
        tmp.setJsMobilePhone(StringUtils.trim(tmp.getJsMobilePhone()));
    }
}
