package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class RainfallStatisticsVo implements Serializable {
    @ApiModelProperty(value = "流域名称")
    @TableField("BSNM")
    private String bsnm;
    @ApiModelProperty(value = "平均降水量(mm)")
    private String avgDrp;
    @ApiModelProperty(value = "最大点雨量（mm）")
    private String maxDrp;
    @ApiModelProperty(value = "测站编码")
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "是否所有测站的最大降雨站")
    private boolean maxFlag;

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getAvgDrp() {
        return avgDrp;
    }

    public void setAvgDrp(String avgDrp) {
        this.avgDrp = avgDrp;
    }

    public String getMaxDrp() {
        return maxDrp;
    }

    public void setMaxDrp(String maxDrp) {
        this.maxDrp = maxDrp;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public boolean isMaxFlag() {
        return maxFlag;
    }

    public void setMaxFlag(boolean maxFlag) {
        this.maxFlag = maxFlag;
    }
}
