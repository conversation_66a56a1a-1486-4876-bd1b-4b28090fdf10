package com.huitu.cloud.api.ewci.hydrology.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.exception.HyWarnException;
import com.huitu.cloud.api.ewci.hydrology.entity.HyWarnAccept;
import com.huitu.cloud.api.ewci.hydrology.entity.query.HyWarnRecordQuery;
import com.huitu.cloud.api.ewci.hydrology.entity.request.HyWarnFileRequest;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnDocumentResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnFileResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnRecordResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnUserResponse;
import com.huitu.cloud.api.ewci.hydrology.mapper.HyWarnReleaseDao;
import com.huitu.cloud.api.ewci.hydrology.service.HyWarnReleaseService;
import com.huitu.cloud.api.ewci.remote.OssRemoteService;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import feign.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 洪水预警发布服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class HyWarnReleaseServiceImpl implements HyWarnReleaseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HyWarnReleaseServiceImpl.class);

    private static final String TEMPLATE_FILE_PATH = "template/hydrology_warn_{wgrd}.docx";
    private static final String[] WGRD_NAME_ARRAY = new String[]{"红色", "橙色", "黄色", "蓝色"};

    private HyWarnReleaseDao releaseDao;
    private OssRemoteService ossService;

    @Autowired
    public void setReleaseDao(HyWarnReleaseDao releaseDao) {
        this.releaseDao = releaseDao;
    }

    @Autowired
    public void setOssService(OssRemoteService ossService) {
        this.ossService = ossService;
    }

    @Override
    public IPage<HyWarnRecordResponse> getPageList(HyWarnRecordQuery query) {
        return releaseDao.getPageList(query.toPageParam(), query.toQueryParam());
    }

    @Override
    public HyWarnRecordResponse getRecordInfo(String warnId) {
        return releaseDao.getRecordInfo(warnId);
    }

    @Override
    public HyWarnFileResponse getFileInfo(String warnId) {
        return releaseDao.getFileInfo(warnId);
    }

    @Override
    public int getNewFileNo(String yr) {
        return releaseDao.getNewFileNo(yr);
    }

    @Override
    public List<HyWarnUserResponse> getUserList(String deptid) {
        return releaseDao.getUserList(deptid);
    }

    @Override
    public HyWarnDocumentResponse getDocument(String warnId) {
        return releaseDao.getDocument(warnId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveFile(HyWarnFileRequest request) {
        int result = releaseDao.edit(request.getWarnId());
        if (result > 0) {
            HyWarnFileResponse origFile = releaseDao.getFileInfo(request.getWarnId());
            if (null != origFile) {
                // 比较原始发送单位和当前发送单位是否一致
                if (!Objects.equals(origFile.getSendDept(), request.getSendDept())) {
                    throw new HyWarnException("抱歉，您没有权限执行此操作");
                }
                try {
                    ossService.deleteFile(origFile.getFilePath());
                } catch (Exception ignored) {
                }
            }
            HyWarnAccept accept = releaseDao.getAccept();
            if (null == accept) {
                throw new HyWarnException("尚未设置预警接收单位，请联系管理员");
            }
            request.setReceiveDept(accept.getDeptId());
            request.setReceiveAdcd(accept.getAdcd());
            request.setFileName(String.format("洪水预警%s年第%s期—%s", request.getYr(), request.getNo(), request.getFileTitle()));

            HyWarnRecordResponse record = releaseDao.getRecordInfo(request.getWarnId());

            ResponseEntity<SuccessResponse<Map<String, String>>> response = ossService.copyFile(
                    TEMPLATE_FILE_PATH.replace("{wgrd}", record.getWgrd().toString()), request.getFileName());
            Map<String, String> map = Objects.requireNonNull(response.getBody()).getData();
            request.setFileName(map.get("filename"));
            request.setFilePath(map.get("filepath"));

            result = releaseDao.saveFile(request);

            updateDocument(record, releaseDao.getFileInfo(request.getWarnId()));
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int ignore(String warnId) {
        int result = releaseDao.ignore(warnId);
        if (result > 0) {
            HyWarnFileResponse origFile = releaseDao.getFileInfo(warnId);
            if (null != origFile) {
                try {
                    ossService.deleteFile(origFile.getFilePath());
                } catch (Exception ignored) {
                }
                releaseDao.deleteFile(warnId);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HyWarnFileResponse release(String warnId, UserInfos user) {
        if (releaseDao.isRepeatFileNo(warnId)) {
            throw new HyWarnException("发文字号重复，请修改后重试");
        }
        HyWarnFileResponse origFile = releaseDao.getFileInfo(warnId);
        // 比较原始发送单位和当前发送单位是否一致
        if (null == origFile || !Objects.equals(origFile.getSendDept(), user.getDeptid())) {
            throw new HyWarnException("抱歉，您没有权限执行此操作");
        }
        int result = releaseDao.release(warnId);
        if (result > 0) {
            releaseDao.updateSender(warnId, user.getLoginnm());

            return releaseDao.getFileInfo(warnId);
        }
        throw new HyWarnException("签发失败，请刷新后重试");
    }

    private void updateDocument(HyWarnRecordResponse record, HyWarnFileResponse file) {
        try {
            Map<String, String> valueMap = new HashMap<>();
            valueMap.put("yr", file.getYr());
            valueMap.put("no", file.getNo().toString());
            valueMap.put("wtm_ymd", DateFormatUtils.format(record.getWtm(), "yyyy年M月d日"));
            valueMap.put("rvnm", record.getRvnm());
            valueMap.put("adnm", record.getAdnm());
            valueMap.put("stnm", record.getStnm());
            valueMap.put("wtm_mdh", DateFormatUtils.format(record.getWtm(), "M月d日 H时"));
            valueMap.put("wtype", "Z".equals(record.getWtype()) ? "水位" : "流量");
            valueMap.put("wval", record.getWval().toString());
            valueMap.put("unit", "Z".equals(record.getWtype()) ? "米" : "立方米/秒");
            if (record.getWval() > 0) {
                valueMap.put("wdesc", String.format("超过%s预警（%s%s）%s%s", WGRD_NAME_ARRAY[record.getWgrd() - 1],
                        record.getWidx(), valueMap.get("unit"), record.getEwidx(), valueMap.get("unit")));
            } else {
                valueMap.put("wdesc", String.format("达到%s预警（%s%s）", WGRD_NAME_ARRAY[record.getWgrd() - 1]
                        , record.getWidx(), valueMap.get("unit")));
            }
            valueMap.put("releaseUnit", record.getReleaseFullName());
            valueMap.put("wtm_ymdh", DateFormatUtils.format(record.getWtm(), "yyyy年M月d日 H时"));
            valueMap.put("signer", file.getSignerName());
            valueMap.put("auditor", file.getAuditorName());
            valueMap.put("editor", file.getEditorName());

            Response response = ossService.getFile(file.getFilePath(), file.getFileName());
            XWPFDocument document = new XWPFDocument(response.body().asInputStream());

            String patternString = "\\$\\{(" + StringUtils.join(valueMap.keySet(), "|") + ")\\}";
            Pattern pattern = Pattern.compile(patternString);

            List<XWPFParagraph> paragraphs = document.getParagraphs();
            for (XWPFParagraph paragraph : paragraphs) {
                String text = paragraph.getText();
                Matcher matcher = pattern.matcher(text);

                StringBuffer sb = new StringBuffer();
                while (matcher.find()) {
                    matcher.appendReplacement(sb, valueMap.get(matcher.group(1)));
                }
                matcher.appendTail(sb);

                String valueStr = sb.toString();
                List<XWPFRun> runs = paragraph.getRuns();
                for (int i = 0; i < runs.size(); i++) {
                    XWPFRun run = runs.get(i);
                    if (i == 0) {
                        run.setText(valueStr, 0);
                    } else {
                        run.setText("", 0);
                    }
                }
            }

            // 创建字节流
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            // 将文档输出至流
            document.write(output);
            // 更新原始文件
            ossService.putFile(file.getFilePath().replace("/", "HTJLUP"), output.toByteArray());
        } catch (Exception ex) {
            LOGGER.error("[洪水预警]文档更新失败", ex);
        }
    }
}
