package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 作战图-设计洪峰流量
 */
@ApiModel(value = "作战图-设计洪峰流量")
public class RiverDesingVo implements Serializable {

    @ApiModelProperty(value = "河流编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "序号")
    @TableField(value = "SNO")
    private Integer sno;

    @ApiModelProperty(value = "控制点")
    @TableField(value = "CL")
    private String cl;

    @ApiModelProperty(value = "面积")
    @TableField(value = "AREA")
    private Double area;

    @ApiModelProperty(value = "设计值P(1%)")
    @TableField(value = "DESING_ONE")
    private Double desingOne;

    @ApiModelProperty(value = "设计值P(2%)")
    @TableField(value = "DESING_TWO")
    private Double desingTwo;

    @ApiModelProperty(value = "设计值P(3.33%)")
    @TableField(value = "DESING_THREE")
    private Double desingThree;

    @ApiModelProperty(value = "设计值P(5%)")
    @TableField(value = "DESING_FIVE")
    private Double desingFive;

    @ApiModelProperty(value = "备注")
    @TableField(value = "REMARK")
    private String remark;

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public Integer getSno() {
        return sno;
    }

    public void setSno(Integer sno) {
        this.sno = sno;
    }

    public String getCl() {
        return cl;
    }

    public void setCl(String cl) {
        this.cl = cl;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public Double getDesingOne() {
        return desingOne;
    }

    public void setDesingOne(Double desingOne) {
        this.desingOne = desingOne;
    }

    public Double getDesingTwo() {
        return desingTwo;
    }

    public void setDesingTwo(Double desingTwo) {
        this.desingTwo = desingTwo;
    }

    public Double getDesingThree() {
        return desingThree;
    }

    public void setDesingThree(Double desingThree) {
        this.desingThree = desingThree;
    }

    public Double getDesingFive() {
        return desingFive;
    }

    public void setDesingFive(Double desingFive) {
        this.desingFive = desingFive;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
