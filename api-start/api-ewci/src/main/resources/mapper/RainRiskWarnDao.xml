<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.RainRiskWarnDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="listRiverRainRiskWarn" resultType="com.huitu.cloud.api.ewci.warn.entity.RiverRainRiskWarnVo">
        SELECT WARN_ID, BW.BAS_CODE, BS.BAS_NAME, BS.RIVER_AREA, RV.RV_CODE, RV.RV_NAME, RV.FLOW_AREA
            , WARN_TIME, WARN_DESC, ACCP, MAX_ACCP, BW.MAX_STCD, STB.STNM MAX_STNM, WARN_STDT
            , WARN_GRADE_ID, WARN_INDEX, EXCEED_WARN_INDEX, STATE, LATEST_TIME
        FROM EW_BS_RAIN_WARNING_RECORD BW
        LEFT JOIN BSN_BAS_B BS ON BW.BAS_CODE=BS.BAS_CODE
        LEFT JOIN REL_RV_BAS RB ON BS.BAS_CODE=RB.BAS_CODE
        LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE
        LEFT JOIN ST_STBPRP_B STB ON BW.MAX_STCD=STB.STCD
        <where>
            <if test="form.stm != null">
                AND WARN_TIME &gt;= #{form.stm}
            </if>
            <if test="form.etm != null">
                AND WARN_TIME &lt;= #{form.etm}
            </if>
        </where>
        ORDER BY WARN_TIME DESC
    </select>

    <select id="listRsvrRainRiskWarn" resultType="com.huitu.cloud.api.ewci.warn.entity.RsvrRainRiskWarnVo">
        SELECT WARN_ID, RW.RES_CODE, RS.RES_NAME, RS.ENG_SCAL
            , LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, WAT_SHED_AREA
            , AD.ADCD, AD.ADNM, WARN_TIME, WARN_DESC
            , ACCP, MAX_ACCP, RW.MAX_STCD, STB.STNM MAX_STNM, WARN_STDT
            , WARN_GRADE_ID, WARN_INDEX, EXCEED_WARN_INDEX, STATE, LATEST_TIME
        FROM EW_RS_RAIN_WARNING_RECORD RW
        LEFT JOIN ATT_RES_BASE RS ON RW.RES_CODE=RS.RES_CODE
        LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE
        LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD
        LEFT JOIN ST_STBPRP_B STB ON RW.MAX_STCD=STB.STCD
        <where>
            LEFT(AD.ADCD, #{form.adlvl}) = LEFT(#{form.adcd}, #{form.adlvl})
            <if test="form.adlvl == '4'.toString()">
                AND LEFT(AD.ADCD, 6) NOT IN ('220581')
            </if>
            <if test="form.engScals != null and form.engScals.size() > 0">
                <foreach collection="form.engScals" item="engScal" separator=", " open="AND RS.ENG_SCAL IN (" close=")">
                    #{engScal}
                </foreach>
            </if>
            <if test="form.stm != null">
                AND WARN_TIME &gt;= #{form.stm}
            </if>
            <if test="form.etm != null">
                AND WARN_TIME &lt;= #{form.etm}
            </if>
        </where>
        ORDER BY WARN_TIME DESC
    </select>

</mapper>