<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.river.mapper.RiverProcessLineDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getBasinList" resultType="com.huitu.cloud.api.ewci.river.entity.RiverBasin">
        SELECT A.BAS_CODE, BAS_NAME, BAS_LEVEL, PBAS_CODE
        FROM BSN_BAS_ST A
                 LEFT JOIN BSN_BAS_B B ON B.BAS_CODE = A.BAS_CODE
        WHERE STCD = #{stcd}
        ORDER BY A.BAS_CODE ASC
    </select>
    <select id="getStationList" resultType="com.huitu.cloud.api.ewci.river.entity.RiverStation">
        SELECT STCD, STNM, XADCD ADCD, XADNM ADNM, RVNM, STLC, ISNULL(PLGTD, LTTD) LGTD, ISNULL(PLTTD, LTTD) LTTD
        FROM BSN_STBPRP_V A
        WHERE USFL = '1' AND STTP IN ('ZZ', 'ZQ')
        <if test="code != null and code != ''">
            <if test='"ad".equalsIgnoreCase(type)'>
                AND LEFT(ADCD, #{level}) = LEFT(#{code}, #{level})
                <if test="level == '4'.toString()">
                    AND LEFT(ADCD, 6) NOT IN ('220581')
                </if>
            </if>
            <if test='"ws".equalsIgnoreCase(type)'>
                AND EXISTS(SELECT 8 FROM BSN_BAS_ST WHERE BAS_CODE = #{code} AND STCD = A.STCD)
            </if>
        </if>
        ORDER BY ADCD, STCD ASC
    </select>
    <select id="getNodeList" resultType="com.huitu.cloud.api.ewci.river.entity.RiverProcessLineNode">
        SELECT STCD, TM, Z, Q FROM ST_RIVER_R
        WHERE TM BETWEEN #{stm} AND #{etm}
        <foreach collection="stcds" item="stcd" open="AND STCD IN (" close=")" separator=",">
            #{stcd}
        </foreach>
        ORDER BY STCD ASC, TM ASC
    </select>
</mapper>
