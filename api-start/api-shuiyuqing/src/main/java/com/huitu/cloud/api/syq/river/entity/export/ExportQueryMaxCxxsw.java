package com.huitu.cloud.api.syq.river.entity.export;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * <p>
 * 最大超警水位统计导出
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-02
 */
@ApiModel(value="ExportQueryMaxCxxsw对象", description="最大超警水位统计导出")
public class ExportQueryMaxCxxsw {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd",required = true)
    private String etm;
    @ApiModelProperty(value = "政区编码",required = true)
    private String adcd;


    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }
}
