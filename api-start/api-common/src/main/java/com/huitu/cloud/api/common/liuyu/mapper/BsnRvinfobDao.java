package com.huitu.cloud.api.common.liuyu.mapper;

import com.huitu.cloud.api.common.liuyu.entity.BsnBasB;
import com.huitu.cloud.api.common.liuyu.entity.BsnRvinfoB;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.jmx.export.annotation.ManagedOperationParameter;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 河流流域常量表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
public interface BsnRvinfobDao extends BaseMapper<BsnBasB> {
    /**
     * 查询所有流域
     * @return
     */
    List<BsnBasB>  selectRiver();
    List<BsnBasB> selectSt(Map<String,Object> param);
    List<BsnBasB> getStbyBscd(Map<String,Object> param);
}
