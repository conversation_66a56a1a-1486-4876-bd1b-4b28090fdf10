package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 控制断面设计洪水成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@TableName("IA_A_SDTDTB")
@ApiModel(value="IaASdtdtb对象", description="控制断面设计洪水成果表")
public class IaSdtdtb extends Model<IaSdtdtb> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableId(value = "ADCD", type = IdType.NONE)
    private String adcd;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "断面代码")
    @TableField("HSCD")
    private String hscd;

    @ApiModelProperty(value = "重现期(年)")
    @TableField("CXQ")
    private String cxq;

    @ApiModelProperty(value = "洪峰(m³/s)")
    @TableField("SJHSHF")
    private Double sjhshf;

    @ApiModelProperty(value = "洪量(m³)")
    @TableField("Q")
    private Double q;

    @ApiModelProperty(value = "涨洪历时(h)")
    @TableField("ZHLS")
    private Double zhls;

    @ApiModelProperty(value = "洪水历时(h)")
    @TableField("HSLS")
    private Double hsls;

    @ApiModelProperty(value = "洪峰水位(m)")
    @TableField("HFSW")
    private Double hfsw;

    @ApiModelProperty(value = "导入人")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;
    @ApiModelProperty(value = "流域名称")
    private String wsnm;
    @ApiModelProperty(value = "政区名称")
    private String adnm;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getHscd() {
        return hscd;
    }

    public void setHscd(String hscd) {
        this.hscd = hscd;
    }

    public String getCxq() {
        return cxq;
    }

    public void setCxq(String cxq) {
        this.cxq = cxq;
    }

    public Double getSjhshf() {
        return sjhshf;
    }

    public void setSjhshf(Double sjhshf) {
        this.sjhshf = sjhshf;
    }

    public Double getQ() {
        return q;
    }

    public void setQ(Double q) {
        this.q = q;
    }

    public Double getZhls() {
        return zhls;
    }

    public void setZhls(Double zhls) {
        this.zhls = zhls;
    }

    public Double getHsls() {
        return hsls;
    }

    public void setHsls(Double hsls) {
        this.hsls = hsls;
    }

    public Double getHfsw() {
        return hfsw;
    }

    public void setHfsw(Double hfsw) {
        this.hfsw = hfsw;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.adcd;
    }

    @Override
    public String toString() {
        return "IaSdtdtb{" +
        "adcd=" + adcd +
        ", wscd=" + wscd +
        ", hscd=" + hscd +
        ", cxq=" + cxq +
        ", sjhshf=" + sjhshf +
        ", q=" + q +
        ", zhls=" + zhls +
        ", hsls=" + hsls +
        ", hfsw=" + hfsw +
        ", signer=" + signer +
        ", audid=" + audid +
        ", status=" + status +
        ", remark=" + remark +
        ", moditime=" + moditime +
        "}";
    }
}
