************* - - [13/Aug/2025:08:35:13 +0800] "GET /doc.html HTTP/1.1" 302 -
************* - - [13/Aug/2025:08:35:13 +0800] "GET /login HTTP/1.1" 302 -
************* - - [13/Aug/2025:08:35:50 +0800] "GET /doc.html HTTP/1.1" 302 -
************* - - [13/Aug/2025:08:35:50 +0800] "GET /login HTTP/1.1" 302 -
************* - - [13/Aug/2025:08:38:47 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/?redirect=%252FworkbenchMainFrame%252FWorkMain%253Ftmpparams%253D%257B%2522title%2522%253A%2522%25E5%25B7%25A5%25E4%25BD%259C%25E5%258F%25B0%2522,%2522url%2522%253A%2522%2522%257D HTTP/1.1" 302 -
************* - - [13/Aug/2025:08:38:47 +0800] "GET /login HTTP/1.1" 302 -
************* - - [13/Aug/2025:08:38:53 +0800] "GET /login?code=a0ikuP&state=pc04JS HTTP/1.1" 302 -
************* - - [13/Aug/2025:08:38:53 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/?redirect=%252FworkbenchMainFrame%252FWorkMain%253Ftmpparams%253D%257B%2522title%2522%253A%2522%25E5%25B7%25A5%25E4%25BD%259C%25E5%258F%25B0%2522,%2522url%2522%253A%2522%2522%257D HTTP/1.1" 302 -
************* - - [13/Aug/2025:08:38:54 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1755045539520 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:55 +0800] "GET /doc.html HTTP/1.1" 200 71645
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************* - - [13/Aug/2025:08:38:55 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
************* - - [13/Aug/2025:08:38:55 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************* - - [13/Aug/2025:08:38:55 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************* - - [13/Aug/2025:08:38:56 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723076
************* - - [13/Aug/2025:08:38:57 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1755045539520 HTTP/1.1" 200 552
************* - - [13/Aug/2025:08:38:57 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755045542518 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:57 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755045542518 HTTP/1.1" 200 61368
************* - - [13/Aug/2025:08:38:57 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755045542628 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:57 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755045542628 HTTP/1.1" 200 10388
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755045542678 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755045542678 HTTP/1.1" 200 2009
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755045543236 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:39:03&etm=&_timer304=1755045543237 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755045543237 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755045543237 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755045543237 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755045543237 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:38:58 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755045543236 HTTP/1.1" 200 1482
************* - - [13/Aug/2025:08:38:58 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755045543237 HTTP/1.1" 200 164
************* - - [13/Aug/2025:08:38:58 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755045543237 HTTP/1.1" 200 166
************* - - [13/Aug/2025:08:38:58 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [13/Aug/2025:08:38:58 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:39:03&etm=&_timer304=1755045543237 HTTP/1.1" 200 156
************* - - [13/Aug/2025:08:38:58 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:08:38:58 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:08:38:58 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [13/Aug/2025:08:38:58 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755045543237 HTTP/1.1" 200 169
************* - - [13/Aug/2025:08:38:58 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755045543237 HTTP/1.1" 200 13016
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/base/saas/token?_timer304=1755045544787 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755045544787 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755045544824 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:00 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
************* - - [13/Aug/2025:08:39:00 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
************* - - [13/Aug/2025:08:39:00 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
************* - - [13/Aug/2025:08:39:00 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************* - - [13/Aug/2025:08:39:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************* - - [13/Aug/2025:08:39:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:08:39:01 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [13/Aug/2025:08:39:01 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************* - - [13/Aug/2025:08:39:01 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [13/Aug/2025:08:39:02 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************* - - [13/Aug/2025:08:39:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [13/Aug/2025:08:39:02 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [13/Aug/2025:08:39:02 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [13/Aug/2025:08:39:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:08:39:02 +0800] "GET /api/base/saas/token?_timer304=1755045544787 HTTP/1.1" 200 411
************* - - [13/Aug/2025:08:39:02 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [13/Aug/2025:08:39:02 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755045544824 HTTP/1.1" 200 2009
************* - - [13/Aug/2025:08:39:02 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [13/Aug/2025:08:39:02 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************* - - [13/Aug/2025:08:39:04 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************* - - [13/Aug/2025:08:39:05 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755045544787 HTTP/1.1" 200 418
************* - - [13/Aug/2025:08:39:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755045550158 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:05 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755045550162 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755045550161 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:05 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755045550162 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:39:05 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755045550162 HTTP/1.1" 200 148
************* - - [13/Aug/2025:08:39:05 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755045550162 HTTP/1.1" 200 152
************* - - [13/Aug/2025:08:39:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755045550161 HTTP/1.1" 200 159
************* - - [13/Aug/2025:08:39:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755045550158 HTTP/1.1" 200 160
************* - - [13/Aug/2025:08:42:22 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755045747068 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755045747068 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755045747068 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:22 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:42:27&etm=&_timer304=1755045747068 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:22 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755045747068 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755045747068 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:22 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [13/Aug/2025:08:42:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:08:42:22 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [13/Aug/2025:08:42:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:08:42:22 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755045747068 HTTP/1.1" 200 1482
************* - - [13/Aug/2025:08:42:22 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755045747068 HTTP/1.1" 200 164
************* - - [13/Aug/2025:08:42:22 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:42:27&etm=&_timer304=1755045747068 HTTP/1.1" 200 156
************* - - [13/Aug/2025:08:42:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755045747068 HTTP/1.1" 200 166
************* - - [13/Aug/2025:08:42:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755045747068 HTTP/1.1" 200 169
************* - - [13/Aug/2025:08:42:22 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755045747068 HTTP/1.1" 200 13016
************* - - [13/Aug/2025:08:42:24 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755045748937 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:24 +0800] "OPTIONS /api/base/saas/token?_timer304=1755045748937 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:24 +0800] "GET /api/base/saas/token?_timer304=1755045748937 HTTP/1.1" 200 411
************* - - [13/Aug/2025:08:42:24 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755045748937 HTTP/1.1" 200 418
************* - - [13/Aug/2025:08:42:24 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755045748978 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [13/Aug/2025:08:42:24 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755045748978 HTTP/1.1" 200 2009
************* - - [13/Aug/2025:08:42:24 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************* - - [13/Aug/2025:08:42:25 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [13/Aug/2025:08:42:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [13/Aug/2025:08:42:26 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************* - - [13/Aug/2025:08:42:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755045757157 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755045757159 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:32 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755045757161 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:32 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755045757161 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755045757157 HTTP/1.1" 200 160
************* - - [13/Aug/2025:08:42:32 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755045757161 HTTP/1.1" 200 148
************* - - [13/Aug/2025:08:42:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755045757159 HTTP/1.1" 200 159
************* - - [13/Aug/2025:08:42:32 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755045757161 HTTP/1.1" 200 152
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1755045774375 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1755045774375 HTTP/1.1" 200 144
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:42:54&etm=&_timer304=1755045774518 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755045774518 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755045774518 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755045774518 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755045774518 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755045774519 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755045774524 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:08:42:49 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:08:42:49 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [13/Aug/2025:08:42:49 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [13/Aug/2025:08:42:49 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:42:54&etm=&_timer304=1755045774518 HTTP/1.1" 200 156
************* - - [13/Aug/2025:08:42:49 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755045774518 HTTP/1.1" 200 166
************* - - [13/Aug/2025:08:42:49 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755045774518 HTTP/1.1" 200 164
************* - - [13/Aug/2025:08:42:49 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755045774519 HTTP/1.1" 200 161
************* - - [13/Aug/2025:08:42:49 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755045774518 HTTP/1.1" 200 169
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755045774645 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:49 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755045774524 HTTP/1.1" 200 159616
************* - - [13/Aug/2025:08:42:50 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [13/Aug/2025:08:42:50 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755045774665 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:50 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755045774518 HTTP/1.1" 200 13016
************* - - [13/Aug/2025:08:42:50 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [13/Aug/2025:08:42:50 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755045774665 HTTP/1.1" 200 258
************* - - [13/Aug/2025:08:42:50 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [13/Aug/2025:08:42:50 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [13/Aug/2025:08:42:51 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755045774645 HTTP/1.1" 200 441332
************* - - [13/Aug/2025:08:42:51 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [13/Aug/2025:08:42:51 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755045776112 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:51 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [13/Aug/2025:08:42:51 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755045776112 HTTP/1.1" 200 258
************* - - [13/Aug/2025:08:42:52 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [13/Aug/2025:08:42:53 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3109
************* - - [13/Aug/2025:08:42:54 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755045778900 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:42:54 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755045778900 HTTP/1.1" 200 232
************* - - [13/Aug/2025:08:43:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755045784653 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755045784653 HTTP/1.1" 200 148
************* - - [13/Aug/2025:08:43:00 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:00 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [13/Aug/2025:08:43:07 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:07 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 329001
************* - - [13/Aug/2025:08:43:16 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212100&_timer304=1755045801090 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:16 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9A1F1307-4C9F-4B68-80DB-7FA5F42C123E&_timer304=1755045801090 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:16 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9A1F1307-4C9F-4B68-80DB-7FA5F42C123E&_timer304=1755045801090 HTTP/1.1" 200 357
************* - - [13/Aug/2025:08:43:16 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212100&_timer304=1755045801090 HTTP/1.1" 200 520
************* - - [13/Aug/2025:08:43:16 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9A1F1307-4C9F-4B68-80DB-7FA5F42C123E&_timer304=1755045801138 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:16 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9A1F1307-4C9F-4B68-80DB-7FA5F42C123E&_timer304=1755045801138 HTTP/1.1" 200 619
************* - - [13/Aug/2025:08:43:25 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=11605000&stm=2025-08-03+09:00&etm=2025-08-03+12:00&drps=&type=2&_timer304=1755045810228 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:25 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=11605000&stm=2025-08-03+09:00&etm=2025-08-03+12:00&drps=&type=2&_timer304=1755045810228 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:25 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=11605000&stm=2025-08-03+09:00&etm=2025-08-03+12:00&drps=&type=2&_timer304=1755045810228 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:43:25 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=11605000&stm=2025-08-03+09:00&etm=2025-08-03+12:00&drps=&type=2&_timer304=1755045810228 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:43:36 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220605105208103&_timer304=1755045821050 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:36 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&warnGradeId=5&_timer304=1755045821050 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:36 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:36 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220605105208103&_timer304=1755045821050 HTTP/1.1" 200 517
************* - - [13/Aug/2025:08:43:36 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&warnGradeId=5&_timer304=1755045821050 HTTP/1.1" 200 439
************* - - [13/Aug/2025:08:43:36 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 2572
************* - - [13/Aug/2025:08:43:36 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&_timer304=1755045821095 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:36 +0800] "OPTIONS /api/ew/warning/process-list?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&_timer304=1755045821095 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:36 +0800] "OPTIONS /api/ew/warning/message-list?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&_timer304=1755045821095 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:36 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&_timer304=1755045821096 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:36 +0800] "GET /api/ew/warning/process-list?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&_timer304=1755045821095 HTTP/1.1" 200 720
************* - - [13/Aug/2025:08:43:36 +0800] "GET /api/ew/warning/flow-list?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&_timer304=1755045821095 HTTP/1.1" 200 1382
************* - - [13/Aug/2025:08:43:36 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&_timer304=1755045821096 HTTP/1.1" 200 330
************* - - [13/Aug/2025:08:43:37 +0800] "GET /api/ew/warning/message-list?warnId=7C3B197D-2C08-44EB-ACE9-EFC84B0A4976&_timer304=1755045821095 HTTP/1.1" 200 7665
************* - - [13/Aug/2025:08:43:37 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=E9512QXZ&stm=2025-08-03+09:00&etm=2025-08-03+12:00&drps=&type=2&_timer304=1755045822575 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:37 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=E9512QXZ&stm=2025-08-03+09:00&etm=2025-08-03+12:00&drps=&type=2&_timer304=1755045822575 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:37 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=E9512QXZ&stm=2025-08-03+09:00&etm=2025-08-03+12:00&drps=&type=2&_timer304=1755045822575 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:43:37 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=E9512QXZ&stm=2025-08-03+09:00&etm=2025-08-03+12:00&drps=&type=2&_timer304=1755045822575 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:43:59 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220521102201100&_timer304=1755045843769 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:59 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&warnGradeId=5&_timer304=1755045843769 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:59 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:59 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&warnGradeId=5&_timer304=1755045843769 HTTP/1.1" 200 437
************* - - [13/Aug/2025:08:43:59 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220521102201100&_timer304=1755045843769 HTTP/1.1" 200 535
************* - - [13/Aug/2025:08:43:59 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1776
************* - - [13/Aug/2025:08:43:59 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&_timer304=1755045843820 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:59 +0800] "OPTIONS /api/ew/warning/process-list?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&_timer304=1755045843820 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:59 +0800] "OPTIONS /api/ew/warning/message-list?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&_timer304=1755045843820 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:59 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&_timer304=1755045843820 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:43:59 +0800] "GET /api/ew/warning/process-list?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&_timer304=1755045843820 HTTP/1.1" 200 720
************* - - [13/Aug/2025:08:43:59 +0800] "GET /api/ew/warning/flow-list?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&_timer304=1755045843820 HTTP/1.1" 200 1382
************* - - [13/Aug/2025:08:43:59 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&_timer304=1755045843820 HTTP/1.1" 200 326
************* - - [13/Aug/2025:08:43:59 +0800] "GET /api/ew/warning/message-list?warnId=F18EBDC5-69D8-4CCF-B966-00F1F0AB2253&_timer304=1755045843820 HTTP/1.1" 200 5570
************* - - [13/Aug/2025:08:44:05 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=11631770&stm=2025-08-03+10:00&etm=2025-08-03+13:00&drps=&type=2&_timer304=1755045849755 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:05 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=11631770&stm=2025-08-03+10:00&etm=2025-08-03+13:00&drps=&type=2&_timer304=1755045849755 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:05 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=11631770&stm=2025-08-03+10:00&etm=2025-08-03+13:00&drps=&type=2&_timer304=1755045849755 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:44:05 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=11631770&stm=2025-08-03+10:00&etm=2025-08-03+13:00&drps=&type=2&_timer304=1755045849755 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:44:13 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&warnGradeId=5&_timer304=1755045857843 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:13 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220523107203100&_timer304=1755045857843 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:13 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:13 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&warnGradeId=5&_timer304=1755045857843 HTTP/1.1" 200 434
************* - - [13/Aug/2025:08:44:13 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1002
************* - - [13/Aug/2025:08:44:13 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220523107203100&_timer304=1755045857843 HTTP/1.1" 200 516
************* - - [13/Aug/2025:08:44:13 +0800] "OPTIONS /api/ew/warning/message-list?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&_timer304=1755045857876 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:13 +0800] "OPTIONS /api/ew/warning/process-list?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&_timer304=1755045857876 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:13 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&_timer304=1755045857876 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:13 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&_timer304=1755045857876 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:13 +0800] "GET /api/ew/warning/flow-list?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&_timer304=1755045857876 HTTP/1.1" 200 1132
************* - - [13/Aug/2025:08:44:13 +0800] "GET /api/ew/warning/process-list?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&_timer304=1755045857876 HTTP/1.1" 200 714
************* - - [13/Aug/2025:08:44:13 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&_timer304=1755045857876 HTTP/1.1" 200 326
************* - - [13/Aug/2025:08:44:13 +0800] "GET /api/ew/warning/message-list?warnId=DAD88217-D9C0-43AC-B81E-0E0BCF134D7A&_timer304=1755045857876 HTTP/1.1" 200 9847
************* - - [13/Aug/2025:08:44:15 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=10812385&stm=2025-08-03+14:00&etm=2025-08-03+15:00&drps=&type=2&_timer304=1755045859743 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:15 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=10812385&stm=2025-08-03+14:00&etm=2025-08-03+15:00&drps=&type=2&_timer304=1755045859743 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:15 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=10812385&stm=2025-08-03+14:00&etm=2025-08-03+15:00&drps=&type=2&_timer304=1755045859743 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:44:15 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=10812385&stm=2025-08-03+14:00&etm=2025-08-03+15:00&drps=&type=2&_timer304=1755045859743 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:44:26 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=222402102200007&_timer304=1755045871267 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:26 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=85E1FD1E-FAAF-4BA5-B71D-D064EE9EE41F&_timer304=1755045871267 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:26 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=85E1FD1E-FAAF-4BA5-B71D-D064EE9EE41F&_timer304=1755045871267 HTTP/1.1" 200 365
************* - - [13/Aug/2025:08:44:26 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=222402102200007&_timer304=1755045871267 HTTP/1.1" 200 517
************* - - [13/Aug/2025:08:44:26 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=85E1FD1E-FAAF-4BA5-B71D-D064EE9EE41F&_timer304=1755045871307 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:26 +0800] "GET /api/fusion/warning/snapshot-index?warnId=85E1FD1E-FAAF-4BA5-B71D-D064EE9EE41F&_timer304=1755045871307 HTTP/1.1" 200 633
************* - - [13/Aug/2025:08:44:32 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=222424104207002&_timer304=1755045877316 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:32 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:32 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&warnGradeId=5&_timer304=1755045877316 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:32 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&warnGradeId=5&_timer304=1755045877316 HTTP/1.1" 200 428
************* - - [13/Aug/2025:08:44:32 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1762
************* - - [13/Aug/2025:08:44:32 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=222424104207002&_timer304=1755045877316 HTTP/1.1" 200 155
************* - - [13/Aug/2025:08:44:32 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&_timer304=1755045877349 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:32 +0800] "OPTIONS /api/ew/warning/message-list?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&_timer304=1755045877349 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:32 +0800] "OPTIONS /api/ew/warning/process-list?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&_timer304=1755045877349 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:32 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&_timer304=1755045877349 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:32 +0800] "GET /api/ew/warning/process-list?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&_timer304=1755045877349 HTTP/1.1" 200 705
************* - - [13/Aug/2025:08:44:32 +0800] "GET /api/ew/warning/flow-list?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&_timer304=1755045877349 HTTP/1.1" 200 1382
************* - - [13/Aug/2025:08:44:32 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&_timer304=1755045877349 HTTP/1.1" 200 323
************* - - [13/Aug/2025:08:44:33 +0800] "GET /api/ew/warning/message-list?warnId=91A26DDA-31EB-4CC3-92E8-774AE834E2D3&_timer304=1755045877349 HTTP/1.1" 200 5812
************* - - [13/Aug/2025:08:44:33 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=11535290&stm=2025-08-01+11:00&etm=2025-08-01+14:00&drps=&type=2&_timer304=1755045878489 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:33 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=11535290&stm=2025-08-01+11:00&etm=2025-08-01+14:00&drps=&type=2&_timer304=1755045878489 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:44:33 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=11535290&stm=2025-08-01+11:00&etm=2025-08-01+14:00&drps=&type=2&_timer304=1755045878489 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:44:33 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=11535290&stm=2025-08-01+11:00&etm=2025-08-01+14:00&drps=&type=2&_timer304=1755045878489 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:45:55 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=222405104204004&_timer304=1755045959966 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:45:55 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=64DE565C-7389-437C-8A84-F69469C9381F&warnGradeId=5&_timer304=1755045959966 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:45:55 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:45:55 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=64DE565C-7389-437C-8A84-F69469C9381F&warnGradeId=5&_timer304=1755045959966 HTTP/1.1" 200 442
************* - - [13/Aug/2025:08:45:55 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1009
************* - - [13/Aug/2025:08:45:55 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=222405104204004&_timer304=1755045959966 HTTP/1.1" 200 502
************* - - [13/Aug/2025:08:45:55 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=64DE565C-7389-437C-8A84-F69469C9381F&_timer304=1755045960003 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:45:55 +0800] "OPTIONS /api/ew/warning/message-list?warnId=64DE565C-7389-437C-8A84-F69469C9381F&_timer304=1755045960003 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:45:55 +0800] "OPTIONS /api/ew/warning/process-list?warnId=64DE565C-7389-437C-8A84-F69469C9381F&_timer304=1755045960003 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:45:55 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=64DE565C-7389-437C-8A84-F69469C9381F&_timer304=1755045960003 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:45:55 +0800] "GET /api/ew/warning/flow-list?warnId=64DE565C-7389-437C-8A84-F69469C9381F&_timer304=1755045960003 HTTP/1.1" 200 1382
************* - - [13/Aug/2025:08:45:55 +0800] "GET /api/ew/warning/process-list?warnId=64DE565C-7389-437C-8A84-F69469C9381F&_timer304=1755045960003 HTTP/1.1" 200 727
************* - - [13/Aug/2025:08:45:55 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=64DE565C-7389-437C-8A84-F69469C9381F&_timer304=1755045960003 HTTP/1.1" 200 327
************* - - [13/Aug/2025:08:45:56 +0800] "GET /api/ew/warning/message-list?warnId=64DE565C-7389-437C-8A84-F69469C9381F&_timer304=1755045960003 HTTP/1.1" 200 11264
************* - - [13/Aug/2025:08:45:57 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=115S4415&stm=2025-08-01+13:40&etm=2025-08-01+14:40&drps=&type=2&_timer304=1755045961716 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:45:57 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=115S4415&stm=2025-08-01+13:40&etm=2025-08-01+14:40&drps=&type=2&_timer304=1755045961716 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:45:57 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=115S4415&stm=2025-08-01+13:40&etm=2025-08-01+14:40&drps=&type=2&_timer304=1755045961716 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:45:57 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=115S4415&stm=2025-08-01+13:40&etm=2025-08-01+14:40&drps=&type=2&_timer304=1755045961716 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755045965748 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755045965741 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755045965741 HTTP/1.1" 200 161
************* - - [13/Aug/2025:08:46:01 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755045965748 HTTP/1.1" 200 159616
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:46:05&etm=&_timer304=1755045965808 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755045965808 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755045965808 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755045965808 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755045965808 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755045965881 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:46:05&etm=&_timer304=1755045965808 HTTP/1.1" 200 156
************* - - [13/Aug/2025:08:46:01 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755045965906 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:08:46:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755045965808 HTTP/1.1" 200 166
************* - - [13/Aug/2025:08:46:01 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755045965808 HTTP/1.1" 200 164
************* - - [13/Aug/2025:08:46:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:08:46:01 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [13/Aug/2025:08:46:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755045965808 HTTP/1.1" 200 169
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755045965808 HTTP/1.1" 200 13016
************* - - [13/Aug/2025:08:46:01 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [13/Aug/2025:08:46:01 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755045965906 HTTP/1.1" 200 258
************* - - [13/Aug/2025:08:46:01 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [13/Aug/2025:08:46:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [13/Aug/2025:08:46:02 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [13/Aug/2025:08:46:02 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755045967001 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:02 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [13/Aug/2025:08:46:02 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755045967001 HTTP/1.1" 200 258
************* - - [13/Aug/2025:08:46:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:02 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755045965881 HTTP/1.1" 200 441332
************* - - [13/Aug/2025:08:46:02 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755045967498 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:02 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:02 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755045967498 HTTP/1.1" 200 148
************* - - [13/Aug/2025:08:46:02 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [13/Aug/2025:08:46:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [13/Aug/2025:08:46:03 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3109
************* - - [13/Aug/2025:08:46:05 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:06 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************* - - [13/Aug/2025:08:46:07 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755045972447 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:07 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755045972447 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:07 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:07 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755045972447 HTTP/1.1" 200 443
************* - - [13/Aug/2025:08:46:07 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755045972447 HTTP/1.1" 200 510
************* - - [13/Aug/2025:08:46:07 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************* - - [13/Aug/2025:08:46:07 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755045972514 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:07 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755045972514 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:07 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755045972514 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:07 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755045972514 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:07 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755045972514 HTTP/1.1" 200 1382
************* - - [13/Aug/2025:08:46:07 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755045972514 HTTP/1.1" 200 727
************* - - [13/Aug/2025:08:46:07 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755045972514 HTTP/1.1" 200 329
************* - - [13/Aug/2025:08:46:08 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755045972514 HTTP/1.1" 200 12917
************* - - [13/Aug/2025:08:46:09 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=10814409&stm=2025-08-03+12:15&etm=2025-08-03+18:15&drps=&type=2&_timer304=1755045974013 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:09 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=10814409&stm=2025-08-03+12:15&etm=2025-08-03+18:15&drps=&type=2&_timer304=1755045974013 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:09 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=10814409&stm=2025-08-03+12:15&etm=2025-08-03+18:15&drps=&type=2&_timer304=1755045974013 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:46:09 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=10814409&stm=2025-08-03+12:15&etm=2025-08-03+18:15&drps=&type=2&_timer304=1755045974013 HTTP/1.1" 200 147
************* - - [13/Aug/2025:08:46:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755045975515 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755045975515 HTTP/1.1" 200 160
************* - - [13/Aug/2025:08:46:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755045975544 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:10 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755045975545 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:10 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755045975545 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:46:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755045975544 HTTP/1.1" 200 159
************* - - [13/Aug/2025:08:46:10 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755045975545 HTTP/1.1" 200 148
************* - - [13/Aug/2025:08:46:10 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755045975545 HTTP/1.1" 200 152
************ - - [13/Aug/2025:08:48:41 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [13/Aug/2025:08:48:41 +0800] "GET /login HTTP/1.1" 302 -
************ - - [13/Aug/2025:08:48:41 +0800] "GET /login?code=WazLmH&state=S5U08B HTTP/1.1" 302 -
************ - - [13/Aug/2025:08:48:41 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1755046122368 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1755046122368 HTTP/1.1" 200 552
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755046122571 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755046122571 HTTP/1.1" 200 61368
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755046122609 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755046122609 HTTP/1.1" 200 10388
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755046122660 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755046122660 HTTP/1.1" 200 2009
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755046122795 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:48:42&etm=&_timer304=1755046122795 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755046122795 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755046122795 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755046122795 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755046122795 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+08:48:42&etm=&_timer304=1755046122795 HTTP/1.1" 200 156
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755046122795 HTTP/1.1" 200 166
************ - - [13/Aug/2025:08:48:42 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+09:00&filterCnt=6&_timer304=1755046122795 HTTP/1.1" 200 164
************ - - [13/Aug/2025:08:48:42 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755046122795 HTTP/1.1" 200 1482
************ - - [13/Aug/2025:08:48:42 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:08:48:42 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755046122795 HTTP/1.1" 200 169
************ - - [13/Aug/2025:08:48:42 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755046122795 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755046123133 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/base/saas/token?_timer304=1755046123133 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "GET /api/base/saas/token?_timer304=1755046123133 HTTP/1.1" 200 411
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755046123143 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755046123133 HTTP/1.1" 200 418
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [13/Aug/2025:08:48:43 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755046123143 HTTP/1.1" 200 2009
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [13/Aug/2025:08:48:43 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [13/Aug/2025:08:48:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [13/Aug/2025:08:48:44 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [13/Aug/2025:08:48:44 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [13/Aug/2025:08:48:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755046132105 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755046132106 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:52 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755046132112 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:52 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755046132112 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:48:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755046132105 HTTP/1.1" 200 160
************ - - [13/Aug/2025:08:48:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755046132106 HTTP/1.1" 200 159
************ - - [13/Aug/2025:08:48:52 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755046132112 HTTP/1.1" 200 152
************ - - [13/Aug/2025:08:48:52 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755046132112 HTTP/1.1" 200 148
************ - - [13/Aug/2025:08:53:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755046422709 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:53:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755046422709 HTTP/1.1" 200 160
************ - - [13/Aug/2025:08:53:52 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755046432627 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:53:52 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755046432627 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:53:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755046432626 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:53:52 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755046432627 HTTP/1.1" 200 148
************ - - [13/Aug/2025:08:53:52 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755046432627 HTTP/1.1" 200 152
************ - - [13/Aug/2025:08:53:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755046432626 HTTP/1.1" 200 159
************* - - [13/Aug/2025:08:57:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755046652765 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:57:33 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755046652765 HTTP/1.1" 200 160
************* - - [13/Aug/2025:08:58:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755046698213 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:58:19 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755046698213 HTTP/1.1" 200 161
************* - - [13/Aug/2025:08:58:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755046702078 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:58:23 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:58:23 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:58:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755046702082 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:58:23 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:58:23 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755046702084 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:58:23 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755046702084 HTTP/1.1" 200 -
************* - - [13/Aug/2025:08:58:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:08:58:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755046702078 HTTP/1.1" 200 159
************* - - [13/Aug/2025:08:58:23 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755046702084 HTTP/1.1" 200 152
************* - - [13/Aug/2025:08:58:23 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755046702084 HTTP/1.1" 200 148
************* - - [13/Aug/2025:08:58:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:08:58:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755046702082 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:08:58:24 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:08:58:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755046722611 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:58:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755046722611 HTTP/1.1" 200 160
************ - - [13/Aug/2025:08:58:53 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755046733621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:58:53 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755046733621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:58:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755046733621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:08:58:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755046733621 HTTP/1.1" 200 159
************ - - [13/Aug/2025:08:58:53 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755046733621 HTTP/1.1" 200 148
************ - - [13/Aug/2025:08:58:53 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755046733621 HTTP/1.1" 200 152
************* - - [13/Aug/2025:09:02:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755046919496 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:02:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755046919496 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:02:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755046953502 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:02:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755046953502 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:02:49 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:02:49 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:02:49 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:02:49 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755046968013 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:02:49 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:02:49 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:02:50 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755046968013 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:02:50 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:03:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755047022618 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:03:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755047022618 HTTP/1.1" 200 160
************ - - [13/Aug/2025:09:03:54 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755047034626 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:03:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047034626 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:03:54 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755047034626 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:03:54 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755047034626 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:03:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047034626 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:03:54 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755047034626 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:04:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047059119 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:04:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047059119 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:04:53 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755047092642 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:04:53 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755047092642 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:04:53 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755047092642 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:04:53 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755047092642 HTTP/1.1" 200 152
************* - - [13/Aug/2025:09:07:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755047226997 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:07:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755047226997 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:07:50 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755047269277 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:07:50 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755047269277 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:08:25 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:08:25 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755047304651 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:08:25 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:08:25 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:08:25 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:08:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:08:26 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755047304651 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:08:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:08:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755047322615 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:08:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755047322615 HTTP/1.1" 200 160
************ - - [13/Aug/2025:09:08:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047335611 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:08:55 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755047335612 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:08:55 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755047335612 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:08:55 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755047335612 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:08:55 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755047335612 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:08:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047335611 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:11:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047484298 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:11:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047484298 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:12:05 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755047524337 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:12:05 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755047524337 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:12:05 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755047524339 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:12:05 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755047524339 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:12:05 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755047524337 HTTP/1.1" 200 152
************* - - [13/Aug/2025:09:12:05 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755047524337 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:12:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755047524458 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:12:05 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:12:05 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:12:05 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:12:05 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755047524459 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:12:05 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:12:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755047524458 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:12:06 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:12:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755047524459 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:12:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:13:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755047622624 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:13:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755047622624 HTTP/1.1" 200 160
************ - - [13/Aug/2025:09:13:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047636621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:13:56 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755047636621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:13:56 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755047636621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:13:56 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755047636621 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:13:56 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755047636621 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:13:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047636621 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:18:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047937621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:18:57 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755047937621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:18:57 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755047937621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:18:57 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755047937621 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:18:57 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755047937621 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:18:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755047937621 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:19:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755047958619 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:19:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755047958619 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:19:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755047970827 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:19:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755047970827 HTTP/1.1" 200 161
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1755048101137 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1755048101137 HTTP/1.1" 200 144
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+09:21:41&etm=&_timer304=1755048101317 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755048101317 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+10:00&filterCnt=6&_timer304=1755048101317 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755048101317 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755048101317 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048101317 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755048101322 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:09:21:41 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:09:21:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:09:21:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:09:21:41 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+09:21:41&etm=&_timer304=1755048101317 HTTP/1.1" 200 156
************ - - [13/Aug/2025:09:21:41 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+10:00&filterCnt=6&_timer304=1755048101317 HTTP/1.1" 200 164
************ - - [13/Aug/2025:09:21:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755048101317 HTTP/1.1" 200 166
************ - - [13/Aug/2025:09:21:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048101317 HTTP/1.1" 200 161
************ - - [13/Aug/2025:09:21:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755048101317 HTTP/1.1" 200 169
************ - - [13/Aug/2025:09:21:41 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755048101322 HTTP/1.1" 200 159616
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048101454 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [13/Aug/2025:09:21:41 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755048101317 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:09:21:41 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755048101481 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:41 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755048101481 HTTP/1.1" 200 258
************ - - [13/Aug/2025:09:21:41 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [13/Aug/2025:09:21:41 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [13/Aug/2025:09:21:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [13/Aug/2025:09:21:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [13/Aug/2025:09:21:42 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755048102503 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:42 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755048102503 HTTP/1.1" 200 258
************ - - [13/Aug/2025:09:21:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [13/Aug/2025:09:21:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048101454 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:09:21:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1386
************ - - [13/Aug/2025:09:21:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3111
************ - - [13/Aug/2025:09:21:44 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755048104331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:44 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755048104331 HTTP/1.1" 200 232
************ - - [13/Aug/2025:09:21:47 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755048107035 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:47 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:47 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755048107035 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:21:47 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:09:21:54 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [13/Aug/2025:09:21:55 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755048115812 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:55 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755048115812 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:55 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755048115812 HTTP/1.1" 200 371
************ - - [13/Aug/2025:09:21:55 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755048115812 HTTP/1.1" 200 520
************ - - [13/Aug/2025:09:21:55 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755048115843 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:21:55 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755048115843 HTTP/1.1" 200 646
************ - - [13/Aug/2025:09:23:03 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002102&_timer304=1755048183726 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:03 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755048183726 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:03 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002102&_timer304=1755048183726 HTTP/1.1" 200 155
************ - - [13/Aug/2025:09:23:03 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755048183726 HTTP/1.1" 200 371
************ - - [13/Aug/2025:09:23:03 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755048183760 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:03 +0800] "GET /api/fusion/warning/snapshot-index?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755048183760 HTTP/1.1" 200 646
************ - - [13/Aug/2025:09:23:07 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755048187421 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:07 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002104&_timer304=1755048187421 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:07 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755048187421 HTTP/1.1" 200 371
************ - - [13/Aug/2025:09:23:07 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002104&_timer304=1755048187421 HTTP/1.1" 200 155
************ - - [13/Aug/2025:09:23:07 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755048187458 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:07 +0800] "GET /api/fusion/warning/snapshot-index?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755048187458 HTTP/1.1" 200 646
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048187148 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048187148 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755048187623 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755048187625 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755048187625 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755048187623 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:23:08 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755048187625 HTTP/1.1" 200 152
************* - - [13/Aug/2025:09:23:08 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755048187625 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048187739 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048187741 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755048187749 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:23:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048187739 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:23:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:23:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755048187749 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:23:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:09:23:10 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=6D3B5620-3D7D-45DD-A7B8-10DDE638586F&_timer304=1755048190270 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:10 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205100&_timer304=1755048190270 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:10 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=6D3B5620-3D7D-45DD-A7B8-10DDE638586F&_timer304=1755048190270 HTTP/1.1" 200 373
************ - - [13/Aug/2025:09:23:10 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=6D3B5620-3D7D-45DD-A7B8-10DDE638586F&_timer304=1755048190314 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:10 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205100&_timer304=1755048190270 HTTP/1.1" 200 514
************ - - [13/Aug/2025:09:23:10 +0800] "GET /api/fusion/warning/snapshot-index?warnId=6D3B5620-3D7D-45DD-A7B8-10DDE638586F&_timer304=1755048190314 HTTP/1.1" 200 655
************* - - [13/Aug/2025:09:23:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048187741 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:23:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:23:19 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:19 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [13/Aug/2025:09:23:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755048222113 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755048222113 HTTP/1.1" 200 160
************ - - [13/Aug/2025:09:23:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048237653 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:57 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755048237653 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:57 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755048237653 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:23:57 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755048237653 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:23:57 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755048237653 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:23:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048237653 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:26:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048401710 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:26:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:26:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:26:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048401713 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:26:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:26:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048401710 HTTP/1.1" 200 161
************ - - [13/Aug/2025:09:26:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:09:26:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:09:26:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048401713 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:09:26:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************* - - [13/Aug/2025:09:26:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048403039 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:26:44 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048403039 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:27:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:27:36 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048454945 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:27:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:27:36 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755048454938 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:27:36 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:27:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:27:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755048454938 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:27:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:27:37 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048454945 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:27:37 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************* - - [13/Aug/2025:09:28:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048487599 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:28:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048487599 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:28:09 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755048488606 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:28:09 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755048488606 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:28:09 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755048488606 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:28:09 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755048488606 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:28:58 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755048538624 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:28:58 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755048538624 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:28:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048538625 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:28:58 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755048538624 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:28:58 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755048538624 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:28:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048538625 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:29:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755048593810 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:29:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755048593810 HTTP/1.1" 200 160
************ - - [13/Aug/2025:09:31:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048701621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:31:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048701622 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:31:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048701621 HTTP/1.1" 200 161
************ - - [13/Aug/2025:09:31:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:09:31:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:09:31:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048701622 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:09:31:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************* - - [13/Aug/2025:09:32:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048775333 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:32:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048775333 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:33:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755048816305 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:33:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755048816305 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:33:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048816477 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:33:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048816477 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:33:37 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755048816694 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:33:37 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755048816694 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:33:37 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755048816694 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:33:37 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755048816694 HTTP/1.1" 200 152
************* - - [13/Aug/2025:09:33:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:33:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:33:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048816882 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:33:38 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:33:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:33:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:33:39 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048816882 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:33:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:33:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755048839616 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:33:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755048839616 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:33:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048839617 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:33:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755048839616 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:33:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755048839616 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:33:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755048839617 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:35:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755048918620 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:35:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755048918620 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:36:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:36:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048967578 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:36:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:36:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048967585 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:36:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:36:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755048967583 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:36:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755048967578 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:36:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755048967583 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:36:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:36:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:36:09 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755048967585 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:36:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:36:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049001622 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:36:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049001623 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:36:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049001622 HTTP/1.1" 200 161
************ - - [13/Aug/2025:09:36:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:09:36:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:09:36:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049001623 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:09:36:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:38:01 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 32146
************ - - [13/Aug/2025:09:38:06 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78290
************ - - [13/Aug/2025:09:38:14 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 32146
************ - - [13/Aug/2025:09:38:16 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 324700
************ - - [13/Aug/2025:09:38:25 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [13/Aug/2025:09:38:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755049122108 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:38:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755049122108 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:38:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049137002 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:38:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049137002 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:38:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755049139648 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:38:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755049139648 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:38:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049139649 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:38:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755049139648 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:38:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755049139648 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:38:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049139649 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:39:30 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755049169600 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:39:30 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755049169600 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:39:30 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755049169600 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:39:30 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755049169600 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:39:41 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 324700
************ - - [13/Aug/2025:09:39:45 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [13/Aug/2025:09:39:56 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755049196257 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:39:56 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755049196257 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:39:56 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755049196257 HTTP/1.1" 200 12285
************ - - [13/Aug/2025:09:39:56 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755049196257 HTTP/1.1" 200 12285
************ - - [13/Aug/2025:09:39:56 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [13/Aug/2025:09:40:04 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [13/Aug/2025:09:40:10 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 32146
************ - - [13/Aug/2025:09:40:27 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [13/Aug/2025:09:40:35 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [13/Aug/2025:09:41:18 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:41:18 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [13/Aug/2025:09:41:34 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:41:34 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [13/Aug/2025:09:41:37 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:41:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [13/Aug/2025:09:41:40 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:41:40 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [13/Aug/2025:09:41:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049301171 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:41:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049301171 HTTP/1.1" 200 161
************ - - [13/Aug/2025:09:41:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:41:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049301396 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:41:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:41:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:41:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:09:41:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:09:41:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049301396 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:09:41:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************* - - [13/Aug/2025:09:42:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049360074 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049360091 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755049360075 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049360074 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:42:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:42:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755049360075 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:42:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:42:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049360091 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:42:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:42:43 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:42:43 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************* - - [13/Aug/2025:09:42:44 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:44 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************* - - [13/Aug/2025:09:42:45 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:45 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 32146
************* - - [13/Aug/2025:09:42:47 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755049366076 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:47 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755049366076 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:47 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:42:47 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755049366076 HTTP/1.1" 200 12285
************* - - [13/Aug/2025:09:42:47 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755049366076 HTTP/1.1" 200 12285
************* - - [13/Aug/2025:09:42:47 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [13/Aug/2025:09:43:14 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:43:14 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [13/Aug/2025:09:43:43 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755049423619 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:43:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755049423619 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:43:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049437607 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:43:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049437607 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:44:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755049440617 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:44:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755049440617 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:44:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049440618 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:44:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755049440617 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:44:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755049440617 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:44:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049440618 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:44:31 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755049470606 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:44:31 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755049470606 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:44:31 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755049470606 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:44:31 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755049470606 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:45:04 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:45:04 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************* - - [13/Aug/2025:09:46:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049560610 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:46:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049560610 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:46:02 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755049561608 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:46:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:46:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:46:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:46:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049561613 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:46:02 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755049561608 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:46:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:46:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:46:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049561613 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:46:04 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:46:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049601738 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:46:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:46:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:46:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049601740 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:46:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:46:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049601738 HTTP/1.1" 200 161
************ - - [13/Aug/2025:09:46:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:09:46:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:09:46:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049601740 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:09:46:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************* - - [13/Aug/2025:09:47:20 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************* - - [13/Aug/2025:09:47:20 +0800] "GET /login HTTP/1.1" 302 -
************* - - [13/Aug/2025:09:47:20 +0800] "GET /login?code=QtWPyG&state=pS0JDq HTTP/1.1" 302 -
************* - - [13/Aug/2025:09:47:20 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1755049640327 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1755049640327 HTTP/1.1" 200 552
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755049640423 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755049640423 HTTP/1.1" 200 61368
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755049640492 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755049640492 HTTP/1.1" 200 10388
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755049640564 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755049640564 HTTP/1.1" 200 2009
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755049640730 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+09:47:20&etm=&_timer304=1755049640730 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+10:00&filterCnt=6&_timer304=1755049640730 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755049640730 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755049640730 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755049640730 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:09:47:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:09:47:21 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [13/Aug/2025:09:47:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755049640730 HTTP/1.1" 200 166
************* - - [13/Aug/2025:09:47:21 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+09:47:20&etm=&_timer304=1755049640730 HTTP/1.1" 200 156
************* - - [13/Aug/2025:09:47:21 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+10:00&filterCnt=6&_timer304=1755049640730 HTTP/1.1" 200 164
************* - - [13/Aug/2025:09:47:21 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755049640730 HTTP/1.1" 200 1482
************* - - [13/Aug/2025:09:47:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755049640730 HTTP/1.1" 200 169
************* - - [13/Aug/2025:09:47:22 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755049640730 HTTP/1.1" 200 13016
************* - - [13/Aug/2025:09:47:22 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755049641154 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:22 +0800] "OPTIONS /api/base/saas/token?_timer304=1755049641154 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:22 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755049641163 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************* - - [13/Aug/2025:09:47:22 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************* - - [13/Aug/2025:09:47:22 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [13/Aug/2025:09:47:22 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************* - - [13/Aug/2025:09:47:23 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [13/Aug/2025:09:47:23 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************* - - [13/Aug/2025:09:47:23 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [13/Aug/2025:09:47:23 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [13/Aug/2025:09:47:23 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [13/Aug/2025:09:47:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:47:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:47:23 +0800] "GET /api/base/saas/token?_timer304=1755049641154 HTTP/1.1" 200 411
************* - - [13/Aug/2025:09:47:23 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755049641163 HTTP/1.1" 200 2009
************* - - [13/Aug/2025:09:47:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1382
************* - - [13/Aug/2025:09:47:23 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 1746
************* - - [13/Aug/2025:09:47:24 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************* - - [13/Aug/2025:09:47:24 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755049641154 HTTP/1.1" 200 418
************* - - [13/Aug/2025:09:47:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049650054 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755049650050 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:31 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755049650055 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:31 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755049650055 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:47:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755049650050 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:47:31 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755049650055 HTTP/1.1" 200 152
************* - - [13/Aug/2025:09:47:31 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755049650055 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:47:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049650054 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:48:49 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755049729585 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:48:49 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755049729585 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:48:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049738596 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:48:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049738596 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:49:01 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755049741616 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:49:01 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755049741616 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:49:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049741621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:49:01 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755049741616 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:49:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049741621 HTTP/1.1" 200 159
************ - - [13/Aug/2025:09:49:01 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755049741616 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:49:24 +0800] "OPTIONS /api/ewci/base/mal/write/379?_timer304=1755049763196 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:24 +0800] "GET /api/ewci/base/mal/write/379?_timer304=1755049763196 HTTP/1.1" 200 146
************* - - [13/Aug/2025:09:49:26 +0800] "OPTIONS /api/weather/get-by-tm?tm=2025-08-13&_timer304=1755049765269 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:26 +0800] "OPTIONS /api/screen/reservoir/list?resName=&engScal=1,3,4,5,2&cxxOrder=true&stm=2025-08-12+10:00&etm=2025-08-13+10:00&resCode=&_timer304=1755049765271 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:26 +0800] "OPTIONS /api/screen/river/queryProjectTotal?adcd=220000000000000&_timer304=1755049765269 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:26 +0800] "OPTIONS /api/screen/reservoir/sk-station-list?resName=&engScal=1,3,4,5,2&cxxOrder=true&stm=2025-08-12+10:00&etm=2025-08-13+10:00&resCode=&_timer304=1755049765271 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:26 +0800] "OPTIONS /api/screen/dike/dike-list?dikeGrad=1,2,3,4,5,9&dikeName=&_timer304=1755049765271 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:26 +0800] "OPTIONS /api/screen/rain/rain-value-statistics?stm=2025-08-12+09:00&etm=2025-08-13+09:00&type=1&_timer304=1755049765271 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:26 +0800] "OPTIONS /api/screen/river/river-list?rvName=&_timer304=1755049765271 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:26 +0800] "OPTIONS /api/screen/river/river-station-list?rvCode=&stm=2025-08-12+10:00&etm=2025-08-13+10:00&_timer304=1755049765271 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:26 +0800] "GET /api/screen/reservoir/sk-station-list?resName=&engScal=1,3,4,5,2&cxxOrder=true&stm=2025-08-12+10:00&etm=2025-08-13+10:00&resCode=&_timer304=1755049765271 HTTP/1.1" 404 168
************* - - [13/Aug/2025:09:49:26 +0800] "GET /api/screen/reservoir/list?resName=&engScal=1,3,4,5,2&cxxOrder=true&stm=2025-08-12+10:00&etm=2025-08-13+10:00&resCode=&_timer304=1755049765271 HTTP/1.1" 404 157
************* - - [13/Aug/2025:09:49:26 +0800] "GET /api/screen/rain/rain-value-statistics?stm=2025-08-12+09:00&etm=2025-08-13+09:00&type=1&_timer304=1755049765271 HTTP/1.1" 200 129
************* - - [13/Aug/2025:09:49:26 +0800] "GET /api/screen/river/queryProjectTotal?adcd=220000000000000&_timer304=1755049765269 HTTP/1.1" 200 142
************* - - [13/Aug/2025:09:49:27 +0800] "GET /api/screen/dike/dike-list?dikeGrad=1,2,3,4,5,9&dikeName=&_timer304=1755049765271 HTTP/1.1" 200 65606
************* - - [13/Aug/2025:09:49:29 +0800] "GET /api/weather/get-by-tm?tm=2025-08-13&_timer304=1755049765269 HTTP/1.1" 200 110
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755049769581 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049769576 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049769576 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+09:49:29&etm=&_timer304=1755049769640 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755049769640 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755049769640 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+10:00&filterCnt=6&_timer304=1755049769640 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755049769640 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049769704 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+09:49:29&etm=&_timer304=1755049769640 HTTP/1.1" 200 156
************* - - [13/Aug/2025:09:49:30 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [13/Aug/2025:09:49:30 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [13/Aug/2025:09:49:30 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755049769581 HTTP/1.1" 200 159616
************* - - [13/Aug/2025:09:49:30 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755049769730 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:30 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:09:49:30 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755049769640 HTTP/1.1" 200 166
************* - - [13/Aug/2025:09:49:30 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+10:00&filterCnt=6&_timer304=1755049769640 HTTP/1.1" 200 164
************* - - [13/Aug/2025:09:49:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [13/Aug/2025:09:49:30 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:09:49:30 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755049769640 HTTP/1.1" 200 169
************* - - [13/Aug/2025:09:49:31 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:31 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755049769640 HTTP/1.1" 200 13016
************* - - [13/Aug/2025:09:49:31 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755049769730 HTTP/1.1" 200 258
************* - - [13/Aug/2025:09:49:31 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:31 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [13/Aug/2025:09:49:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [13/Aug/2025:09:49:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [13/Aug/2025:09:49:31 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:09:49:32 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755049770848 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:32 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755049770848 HTTP/1.1" 200 258
************* - - [13/Aug/2025:09:49:32 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049769704 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:49:32 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:09:49:33 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1386
************* - - [13/Aug/2025:09:49:33 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:33 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3111
************* - - [13/Aug/2025:09:49:33 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755049772776 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:34 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755049772776 HTTP/1.1" 200 232
************* - - [13/Aug/2025:09:49:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049779391 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755049779371 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:40 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755049779401 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:40 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755049779401 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:49:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755049779371 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:49:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755049779391 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:49:40 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755049779401 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:49:40 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755049779401 HTTP/1.1" 200 152
************* - - [13/Aug/2025:09:50:43 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755049842415 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:43 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:43 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [13/Aug/2025:09:50:43 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755049842415 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:50:46 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:46 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************* - - [13/Aug/2025:09:50:58 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:58 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220521101002100&_timer304=1755049857510 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:58 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=703967E2-2B06-43A3-893F-147361EE982C&warnGradeId=5&_timer304=1755049857510 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:58 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=703967E2-2B06-43A3-893F-147361EE982C&warnGradeId=5&_timer304=1755049857510 HTTP/1.1" 200 426
************* - - [13/Aug/2025:09:50:58 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1758
************* - - [13/Aug/2025:09:50:58 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220521101002100&_timer304=1755049857510 HTTP/1.1" 200 513
************* - - [13/Aug/2025:09:50:58 +0800] "OPTIONS /api/ew/warning/process-list?warnId=703967E2-2B06-43A3-893F-147361EE982C&_timer304=1755049857609 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:58 +0800] "OPTIONS /api/ew/warning/message-list?warnId=703967E2-2B06-43A3-893F-147361EE982C&_timer304=1755049857609 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:58 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=703967E2-2B06-43A3-893F-147361EE982C&_timer304=1755049857609 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:58 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=703967E2-2B06-43A3-893F-147361EE982C&_timer304=1755049857609 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:50:58 +0800] "GET /api/ew/warning/flow-list?warnId=703967E2-2B06-43A3-893F-147361EE982C&_timer304=1755049857609 HTTP/1.1" 200 888
************* - - [13/Aug/2025:09:50:58 +0800] "GET /api/ew/warning/process-list?warnId=703967E2-2B06-43A3-893F-147361EE982C&_timer304=1755049857609 HTTP/1.1" 200 701
************* - - [13/Aug/2025:09:50:58 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=703967E2-2B06-43A3-893F-147361EE982C&_timer304=1755049857609 HTTP/1.1" 200 323
************* - - [13/Aug/2025:09:50:59 +0800] "GET /api/ew/warning/message-list?warnId=703967E2-2B06-43A3-893F-147361EE982C&_timer304=1755049857609 HTTP/1.1" 200 3015
************ - - [13/Aug/2025:09:51:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049901611 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:51:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755049901611 HTTP/1.1" 200 161
************ - - [13/Aug/2025:09:52:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:52:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:52:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049938746 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:52:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:52:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:09:52:19 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:09:52:19 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755049938746 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:09:52:20 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************* - - [13/Aug/2025:09:52:40 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212101&_timer304=1755049959754 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:40 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=DD86CBFD-9937-41E9-98AE-613BEA3404AF&_timer304=1755049959754 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:40 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=DD86CBFD-9937-41E9-98AE-613BEA3404AF&_timer304=1755049959754 HTTP/1.1" 200 352
************* - - [13/Aug/2025:09:52:41 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212101&_timer304=1755049959754 HTTP/1.1" 200 520
************* - - [13/Aug/2025:09:52:41 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=DD86CBFD-9937-41E9-98AE-613BEA3404AF&_timer304=1755049959847 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:41 +0800] "GET /api/fusion/warning/snapshot-index?warnId=DD86CBFD-9937-41E9-98AE-613BEA3404AF&_timer304=1755049959847 HTTP/1.1" 200 607
************* - - [13/Aug/2025:09:52:43 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220521105207102&_timer304=1755049962385 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:43 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:43 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&warnGradeId=5&_timer304=1755049962385 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:43 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&warnGradeId=5&_timer304=1755049962385 HTTP/1.1" 200 426
************* - - [13/Aug/2025:09:52:43 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220521105207102&_timer304=1755049962385 HTTP/1.1" 200 526
************* - - [13/Aug/2025:09:52:43 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1752
************* - - [13/Aug/2025:09:52:43 +0800] "OPTIONS /api/ew/warning/process-list?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&_timer304=1755049962509 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:43 +0800] "OPTIONS /api/ew/warning/message-list?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&_timer304=1755049962509 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:43 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&_timer304=1755049962509 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:43 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&_timer304=1755049962509 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:43 +0800] "GET /api/ew/warning/process-list?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&_timer304=1755049962509 HTTP/1.1" 200 704
************* - - [13/Aug/2025:09:52:43 +0800] "GET /api/ew/warning/flow-list?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&_timer304=1755049962509 HTTP/1.1" 200 888
************* - - [13/Aug/2025:09:52:43 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&_timer304=1755049962509 HTTP/1.1" 200 320
************* - - [13/Aug/2025:09:52:43 +0800] "GET /api/ew/warning/message-list?warnId=E7D53DD8-24A2-4C2A-BE36-123AF45CCBE7&_timer304=1755049962509 HTTP/1.1" 200 1470
************* - - [13/Aug/2025:09:52:59 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:52:59 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************* - - [13/Aug/2025:09:53:32 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:53:32 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************* - - [13/Aug/2025:09:53:38 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:53:38 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************* - - [13/Aug/2025:09:53:39 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:53:39 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29143
************* - - [13/Aug/2025:09:53:50 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:53:50 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29143
************ - - [13/Aug/2025:09:54:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050042717 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:54:02 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755050042718 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:54:02 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755050042718 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:54:02 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755050042718 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:54:02 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755050042718 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:54:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050042717 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:54:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050069839 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755050069842 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050069839 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:54:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755050069842 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:54:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050070612 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:09:54:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:09:54:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050070612 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:54:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************* - - [13/Aug/2025:09:54:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050079492 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:40 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755050079524 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:40 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755050079524 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050079492 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:54:40 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755050079524 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:54:40 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755050079524 HTTP/1.1" 200 152
************* - - [13/Aug/2025:09:54:44 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:54:44 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29143
************ - - [13/Aug/2025:09:55:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755050118615 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:55:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755050118615 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:56:47 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:56:47 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29143
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050221747 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755050221751 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+09:57:01&etm=&_timer304=1755050221809 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+10:00&filterCnt=6&_timer304=1755050221809 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755050221809 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755050221809 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050221747 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755050221809 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050221870 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+09:57:01&etm=&_timer304=1755050221809 HTTP/1.1" 200 156
************* - - [13/Aug/2025:09:57:03 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755050221894 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+10:00&filterCnt=6&_timer304=1755050221809 HTTP/1.1" 200 164
************* - - [13/Aug/2025:09:57:03 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755050221809 HTTP/1.1" 200 166
************* - - [13/Aug/2025:09:57:03 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:09:57:03 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:09:57:03 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [13/Aug/2025:09:57:03 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755050221751 HTTP/1.1" 200 159616
************* - - [13/Aug/2025:09:57:03 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755050221809 HTTP/1.1" 200 169
************* - - [13/Aug/2025:09:57:03 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755050221809 HTTP/1.1" 200 13016
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755050221894 HTTP/1.1" 200 258
************* - - [13/Aug/2025:09:57:03 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [13/Aug/2025:09:57:03 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [13/Aug/2025:09:57:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [13/Aug/2025:09:57:04 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755050222905 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:04 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:04 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050221870 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:09:57:04 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [13/Aug/2025:09:57:04 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:09:57:04 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:09:57:04 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755050223480 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:04 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755050223480 HTTP/1.1" 200 258
************* - - [13/Aug/2025:09:57:04 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:05 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:05 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [13/Aug/2025:09:57:05 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755050222905 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:57:05 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1386
************* - - [13/Aug/2025:09:57:05 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:06 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3111
************* - - [13/Aug/2025:09:57:08 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:08 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [13/Aug/2025:09:57:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755050231540 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:12 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755050231571 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050231570 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:12 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755050231571 HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755050231540 HTTP/1.1" 200 160
************* - - [13/Aug/2025:09:57:12 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755050231571 HTTP/1.1" 200 148
************* - - [13/Aug/2025:09:57:12 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755050231571 HTTP/1.1" 200 152
************* - - [13/Aug/2025:09:57:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050231570 HTTP/1.1" 200 159
************* - - [13/Aug/2025:09:57:12 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:13 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [13/Aug/2025:09:57:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050238618 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:57:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050238618 HTTP/1.1" 200 161
************* - - [13/Aug/2025:09:57:21 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:21 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29448
************* - - [13/Aug/2025:09:57:33 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:33 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29310
************* - - [13/Aug/2025:09:57:52 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:09:57:52 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29310
************ - - [13/Aug/2025:09:58:03 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:58:03 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:58:03 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050283363 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:58:03 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:58:03 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:09:58:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:09:58:04 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050283363 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:09:58:04 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53066
************ - - [13/Aug/2025:09:58:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755050322989 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:58:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755050322989 HTTP/1.1" 200 160
************ - - [13/Aug/2025:09:59:03 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755050343623 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:59:03 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755050343623 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:59:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050343624 HTTP/1.1" 200 -
************ - - [13/Aug/2025:09:59:03 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755050343623 HTTP/1.1" 200 148
************ - - [13/Aug/2025:09:59:03 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755050343623 HTTP/1.1" 200 152
************ - - [13/Aug/2025:09:59:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050343624 HTTP/1.1" 200 159
************ - - [13/Aug/2025:10:02:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050538614 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:02:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050538614 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:02:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050541361 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050541363 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:22 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755050541361 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:02:22 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755050541432 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:22 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755050541432 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755050541432 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050541434 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050541363 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:02:22 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755050541432 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:02:22 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755050541432 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:02:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:10:02:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755050541432 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:02:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:10:02:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050541434 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:10:02:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:10:02:35 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&warnGradeId=5&_timer304=1755050554225 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:35 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200013&_timer304=1755050554225 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:35 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:35 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&warnGradeId=5&_timer304=1755050554225 HTTP/1.1" 200 466
************* - - [13/Aug/2025:10:02:35 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200013&_timer304=1755050554225 HTTP/1.1" 200 520
************* - - [13/Aug/2025:10:02:35 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1014
************* - - [13/Aug/2025:10:02:35 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&_timer304=1755050554371 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:35 +0800] "OPTIONS /api/ew/warning/process-list?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&_timer304=1755050554371 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:35 +0800] "OPTIONS /api/ew/warning/message-list?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&_timer304=1755050554371 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:35 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&_timer304=1755050554371 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:35 +0800] "GET /api/ew/warning/flow-list?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&_timer304=1755050554371 HTTP/1.1" 200 1382
************* - - [13/Aug/2025:10:02:35 +0800] "GET /api/ew/warning/process-list?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&_timer304=1755050554371 HTTP/1.1" 200 754
************* - - [13/Aug/2025:10:02:35 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&_timer304=1755050554371 HTTP/1.1" 200 332
************* - - [13/Aug/2025:10:02:36 +0800] "GET /api/ew/warning/message-list?warnId=BBDA9A35-940E-4B98-B6A6-89935EF32DA1&_timer304=1755050554371 HTTP/1.1" 200 6077
************* - - [13/Aug/2025:10:02:45 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220605102203101&_timer304=1755050564525 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:02:45 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220605102203101&_timer304=1755050564525 HTTP/1.1" 200 513
************* - - [13/Aug/2025:10:03:13 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:13 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&warnGradeId=5&_timer304=1755050592120 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:13 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281106206001&_timer304=1755050592120 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:13 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&warnGradeId=5&_timer304=1755050592120 HTTP/1.1" 200 437
************* - - [13/Aug/2025:10:03:13 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281106206001&_timer304=1755050592120 HTTP/1.1" 200 551
************* - - [13/Aug/2025:10:03:13 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1004
************* - - [13/Aug/2025:10:03:13 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&_timer304=1755050592229 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:13 +0800] "OPTIONS /api/ew/warning/process-list?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&_timer304=1755050592229 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:13 +0800] "OPTIONS /api/ew/warning/message-list?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&_timer304=1755050592229 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:13 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&_timer304=1755050592229 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:13 +0800] "GET /api/ew/warning/process-list?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&_timer304=1755050592229 HTTP/1.1" 200 719
************* - - [13/Aug/2025:10:03:13 +0800] "GET /api/ew/warning/flow-list?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&_timer304=1755050592229 HTTP/1.1" 200 1382
************* - - [13/Aug/2025:10:03:13 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&_timer304=1755050592229 HTTP/1.1" 200 326
************* - - [13/Aug/2025:10:03:14 +0800] "GET /api/ew/warning/message-list?warnId=77BEF024-5964-4B9D-99A5-7911B935D6C2&_timer304=1755050592229 HTTP/1.1" 200 14209
************* - - [13/Aug/2025:10:03:17 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220523103208100&_timer304=1755050596379 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:17 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&warnGradeId=5&_timer304=1755050596379 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:17 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:17 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&warnGradeId=5&_timer304=1755050596379 HTTP/1.1" 200 443
************* - - [13/Aug/2025:10:03:17 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1014
************* - - [13/Aug/2025:10:03:17 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220523103208100&_timer304=1755050596379 HTTP/1.1" 200 517
************* - - [13/Aug/2025:10:03:17 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&_timer304=1755050596467 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:17 +0800] "OPTIONS /api/ew/warning/process-list?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&_timer304=1755050596467 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:17 +0800] "OPTIONS /api/ew/warning/message-list?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&_timer304=1755050596467 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:17 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&_timer304=1755050596467 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:17 +0800] "GET /api/ew/warning/flow-list?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&_timer304=1755050596467 HTTP/1.1" 200 1132
************* - - [13/Aug/2025:10:03:17 +0800] "GET /api/ew/warning/process-list?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&_timer304=1755050596467 HTTP/1.1" 200 732
************* - - [13/Aug/2025:10:03:17 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&_timer304=1755050596467 HTTP/1.1" 200 326
************* - - [13/Aug/2025:10:03:18 +0800] "GET /api/ew/warning/message-list?warnId=A5C84FD9-7A60-4D05-B767-7808F06C0C4E&_timer304=1755050596467 HTTP/1.1" 200 8062
************ - - [13/Aug/2025:10:03:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:03:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:03:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050598627 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:03:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:03:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:10:03:19 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:10:03:19 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755050598627 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:10:03:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:10:03:25 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&warnGradeId=5&_timer304=1755050603896 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:25 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220681103205102&_timer304=1755050603896 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:25 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:25 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&warnGradeId=5&_timer304=1755050603896 HTTP/1.1" 200 469
************* - - [13/Aug/2025:10:03:25 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1841
************* - - [13/Aug/2025:10:03:25 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220681103205102&_timer304=1755050603896 HTTP/1.1" 200 526
************* - - [13/Aug/2025:10:03:25 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&_timer304=1755050604007 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:25 +0800] "OPTIONS /api/ew/warning/process-list?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&_timer304=1755050604007 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:25 +0800] "OPTIONS /api/ew/warning/message-list?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&_timer304=1755050604007 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:25 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&_timer304=1755050604007 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:25 +0800] "GET /api/ew/warning/flow-list?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&_timer304=1755050604007 HTTP/1.1" 200 1382
************* - - [13/Aug/2025:10:03:25 +0800] "GET /api/ew/warning/process-list?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&_timer304=1755050604007 HTTP/1.1" 200 746
************* - - [13/Aug/2025:10:03:25 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&_timer304=1755050604007 HTTP/1.1" 200 361
************* - - [13/Aug/2025:10:03:25 +0800] "GET /api/ew/warning/message-list?warnId=690FDE72-74F0-4EC2-8289-6E110FC8AD2B&_timer304=1755050604007 HTTP/1.1" 200 16866
************* - - [13/Aug/2025:10:03:31 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9A56D6E2-EB96-47B4-89F4-E985E53287DB&_timer304=1755050609960 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:31 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103206002&_timer304=1755050609960 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:31 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9A56D6E2-EB96-47B4-89F4-E985E53287DB&_timer304=1755050609960 HTTP/1.1" 200 376
************* - - [13/Aug/2025:10:03:31 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103206002&_timer304=1755050609960 HTTP/1.1" 200 155
************* - - [13/Aug/2025:10:03:31 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9A56D6E2-EB96-47B4-89F4-E985E53287DB&_timer304=1755050610080 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:31 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9A56D6E2-EB96-47B4-89F4-E985E53287DB&_timer304=1755050610080 HTTP/1.1" 200 659
************* - - [13/Aug/2025:10:03:33 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205003&_timer304=1755050612546 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:33 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:33 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&warnGradeId=5&_timer304=1755050612546 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:33 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&warnGradeId=5&_timer304=1755050612546 HTTP/1.1" 200 443
************* - - [13/Aug/2025:10:03:33 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************* - - [13/Aug/2025:10:03:33 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205003&_timer304=1755050612546 HTTP/1.1" 200 510
************* - - [13/Aug/2025:10:03:33 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755050612643 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:33 +0800] "OPTIONS /api/ew/warning/process-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755050612643 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:33 +0800] "OPTIONS /api/ew/warning/message-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755050612643 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:33 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755050612643 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:03:33 +0800] "GET /api/ew/warning/flow-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755050612643 HTTP/1.1" 200 1382
************* - - [13/Aug/2025:10:03:33 +0800] "GET /api/ew/warning/process-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755050612643 HTTP/1.1" 200 727
************* - - [13/Aug/2025:10:03:33 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755050612643 HTTP/1.1" 200 329
************* - - [13/Aug/2025:10:03:34 +0800] "GET /api/ew/warning/message-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755050612643 HTTP/1.1" 200 12917
************ - - [13/Aug/2025:10:04:04 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755050644625 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:04:04 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755050644625 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:04:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050644626 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:04:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050644626 HTTP/1.1" 200 159
************ - - [13/Aug/2025:10:04:04 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755050644625 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:04:04 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755050644625 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:04:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755050672129 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:04:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755050672129 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:08:07 +0800] "GET /doc.html HTTP/1.1" 302 -
************ - - [13/Aug/2025:10:08:07 +0800] "GET /login HTTP/1.1" 302 -
************ - - [13/Aug/2025:10:08:08 +0800] "GET /login?code=nzH27X&state=LqNH5y HTTP/1.1" 302 -
************ - - [13/Aug/2025:10:08:08 +0800] "GET /doc.html HTTP/1.1" 200 71645
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************ - - [13/Aug/2025:10:08:08 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************ - - [13/Aug/2025:10:08:09 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
************ - - [13/Aug/2025:10:08:09 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************ - - [13/Aug/2025:10:08:09 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************ - - [13/Aug/2025:10:08:10 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723793
************ - - [13/Aug/2025:10:08:41 +0800] "GET /doc.html HTTP/1.1" 200 71645
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************ - - [13/Aug/2025:10:08:41 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************ - - [13/Aug/2025:10:08:41 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************ - - [13/Aug/2025:10:08:41 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
************ - - [13/Aug/2025:10:08:41 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723793
************ - - [13/Aug/2025:10:08:43 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755050923625 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:08:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755050923625 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:08:51 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
************ - - [13/Aug/2025:10:08:51 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
************ - - [13/Aug/2025:10:08:51 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
************ - - [13/Aug/2025:10:08:51 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:08:51 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:08:56 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
************ - - [13/Aug/2025:10:08:56 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 251
************ - - [13/Aug/2025:10:08:56 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:09:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050945624 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:09:05 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755050945625 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:09:05 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755050945625 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:09:06 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755050945625 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:09:06 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755050945625 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:09:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755050945624 HTTP/1.1" 200 159
************ - - [13/Aug/2025:10:10:23 +0800] "GET /doc.html HTTP/1.1" 302 -
************ - - [13/Aug/2025:10:10:23 +0800] "GET /login HTTP/1.1" 302 -
************ - - [13/Aug/2025:10:10:23 +0800] "GET /login?code=4BARA2&state=J79zKf HTTP/1.1" 302 -
************ - - [13/Aug/2025:10:10:24 +0800] "GET /doc.html HTTP/1.1" 200 71645
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************ - - [13/Aug/2025:10:10:24 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
************ - - [13/Aug/2025:10:10:24 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************ - - [13/Aug/2025:10:10:24 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************ - - [13/Aug/2025:10:10:25 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723793
************ - - [13/Aug/2025:10:10:33 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
************ - - [13/Aug/2025:10:10:33 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
************ - - [13/Aug/2025:10:10:33 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
************ - - [13/Aug/2025:10:10:33 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:10:33 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:10:36 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
************ - - [13/Aug/2025:10:10:38 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23603
************ - - [13/Aug/2025:10:10:38 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:11:25 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:11:25 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [13/Aug/2025:10:11:33 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:11:33 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [13/Aug/2025:10:11:41 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23603
************ - - [13/Aug/2025:10:11:41 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:11:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051101612 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:11:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051101612 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:11:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:11:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:11:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051102624 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:11:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:11:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:10:11:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:10:11:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:10:11:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051102624 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:10:11:53 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:11:53 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29143
************ - - [13/Aug/2025:10:12:00 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23514
************ - - [13/Aug/2025:10:12:00 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:12:14 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:12:14 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29297
************ - - [13/Aug/2025:10:12:26 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23603
************ - - [13/Aug/2025:10:12:26 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:12:36 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:12:36 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************* - - [13/Aug/2025:10:12:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755051163074 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:12:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755051163074 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:13:17 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:13:18 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************* - - [13/Aug/2025:10:13:26 +0800] "GET /doc.html HTTP/1.1" 302 -
************* - - [13/Aug/2025:10:13:26 +0800] "GET /login HTTP/1.1" 302 -
************* - - [13/Aug/2025:10:13:26 +0800] "GET /login?code=630CF5&state=An7LXv HTTP/1.1" 302 -
************* - - [13/Aug/2025:10:13:26 +0800] "GET /doc.html HTTP/1.1" 200 71645
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************* - - [13/Aug/2025:10:13:26 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************* - - [13/Aug/2025:10:13:27 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************* - - [13/Aug/2025:10:13:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************* - - [13/Aug/2025:10:13:27 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************* - - [13/Aug/2025:10:13:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************* - - [13/Aug/2025:10:13:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************* - - [13/Aug/2025:10:13:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************* - - [13/Aug/2025:10:13:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************* - - [13/Aug/2025:10:13:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************* - - [13/Aug/2025:10:13:27 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************* - - [13/Aug/2025:10:13:27 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************* - - [13/Aug/2025:10:13:27 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************* - - [13/Aug/2025:10:13:27 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723793
************* - - [13/Aug/2025:10:13:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051208601 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:13:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051208603 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:13:29 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:13:29 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:13:29 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755051208610 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:13:29 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:13:29 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051208605 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:13:29 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755051208610 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:13:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051208601 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:13:29 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:10:13:29 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755051208610 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:13:29 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755051208610 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:13:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051208603 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:13:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:10:13:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051208605 HTTP/1.1" 200 441927
************* - - [13/Aug/2025:10:13:31 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
************* - - [13/Aug/2025:10:13:31 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
************* - - [13/Aug/2025:10:13:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 52488
************* - - [13/Aug/2025:10:13:31 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
************* - - [13/Aug/2025:10:13:31 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************* - - [13/Aug/2025:10:13:31 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [13/Aug/2025:10:13:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755051222111 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:13:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755051222111 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:14:06 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755051246621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:14:06 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755051246621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:14:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051246622 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:14:06 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755051246621 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:14:06 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755051246621 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:14:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051246622 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755051297027 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051297002 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051297002 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:14:58 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755051297027 HTTP/1.1" 200 159616
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+10:14:57&etm=&_timer304=1755051297278 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755051297278 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755051297278 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+11:00&filterCnt=6&_timer304=1755051297278 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755051297278 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+10:14:57&etm=&_timer304=1755051297278 HTTP/1.1" 200 156
************* - - [13/Aug/2025:10:14:58 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051297530 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:58 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+11:00&filterCnt=6&_timer304=1755051297278 HTTP/1.1" 200 164
************* - - [13/Aug/2025:10:14:58 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755051297278 HTTP/1.1" 200 166
************* - - [13/Aug/2025:10:14:58 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:10:14:58 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755051297278 HTTP/1.1" 200 169
************* - - [13/Aug/2025:10:14:58 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:10:14:58 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [13/Aug/2025:10:14:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [13/Aug/2025:10:14:58 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051297664 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:59 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755051297278 HTTP/1.1" 200 13016
************* - - [13/Aug/2025:10:14:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [13/Aug/2025:10:14:59 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051297664 HTTP/1.1" 200 258
************* - - [13/Aug/2025:10:14:59 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:59 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:14:59 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [13/Aug/2025:10:14:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [13/Aug/2025:10:15:00 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:10:15:00 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051298811 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:15:00 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051297530 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:10:15:00 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051298811 HTTP/1.1" 200 258
************* - - [13/Aug/2025:10:15:00 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:10:15:00 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:15:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1980
************* - - [13/Aug/2025:10:15:01 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:15:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3113
************* - - [13/Aug/2025:10:15:02 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755051300916 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:15:02 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755051300916 HTTP/1.1" 200 232
************* - - [13/Aug/2025:10:15:27 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755051326615 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:15:28 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755051326615 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:15:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051327601 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:15:28 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755051327605 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:15:28 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755051327605 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:15:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051327601 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:15:28 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755051327605 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:15:29 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755051327605 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:18:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755051523614 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:18:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755051523614 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:18:54 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:18:56 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:10:18:56 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051535285 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:18:56 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051535285 HTTP/1.1" 200 258
************* - - [13/Aug/2025:10:18:56 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:18:56 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [13/Aug/2025:10:19:07 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755051547748 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:19:07 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755051547749 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:19:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051547750 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:19:07 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755051547748 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:19:07 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755051547749 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:19:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051547750 HTTP/1.1" 200 159
************ - - [13/Aug/2025:10:22:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:22:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051726940 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:22:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:22:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051726953 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:22:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:22:08 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:22:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051726940 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:22:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [13/Aug/2025:10:22:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:10:22:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:10:22:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:10:22:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051726953 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:10:22:23 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755051742652 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:22:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755051742652 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:23:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051781884 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:23:03 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051781884 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:23:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:23:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:23:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051820503 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:23:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:23:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [13/Aug/2025:10:23:41 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [13/Aug/2025:10:23:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [13/Aug/2025:10:23:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051820503 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:10:23:43 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755051823619 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:23:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755051823619 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051847310 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755051847314 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051847310 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+10:24:07&etm=&_timer304=1755051847379 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755051847379 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+11:00&filterCnt=6&_timer304=1755051847379 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755051847379 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755051847379 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051847464 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755051847314 HTTP/1.1" 200 159616
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051847513 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+10:24:07&etm=&_timer304=1755051847379 HTTP/1.1" 200 156
************ - - [13/Aug/2025:10:24:07 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:10:24:07 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+11:00&filterCnt=6&_timer304=1755051847379 HTTP/1.1" 200 164
************ - - [13/Aug/2025:10:24:07 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755051847379 HTTP/1.1" 200 166
************ - - [13/Aug/2025:10:24:07 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:10:24:07 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:10:24:07 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:10:24:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [13/Aug/2025:10:24:07 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755051847379 HTTP/1.1" 200 169
************ - - [13/Aug/2025:10:24:07 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [13/Aug/2025:10:24:07 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051847513 HTTP/1.1" 200 258
************ - - [13/Aug/2025:10:24:08 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755051847379 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:10:24:08 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [13/Aug/2025:10:24:08 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:08 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [13/Aug/2025:10:24:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051847464 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:10:24:09 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [13/Aug/2025:10:24:09 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [13/Aug/2025:10:24:09 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051849333 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:09 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051849333 HTTP/1.1" 200 258
************ - - [13/Aug/2025:10:24:09 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:10 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1980
************ - - [13/Aug/2025:10:24:10 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:11 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3113
************ - - [13/Aug/2025:10:24:11 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755051851389 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:11 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755051851389 HTTP/1.1" 200 232
************* - - [13/Aug/2025:10:24:15 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755051854552 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:15 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755051854551 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051854550 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:15 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:15 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755051854551 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:24:15 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755051854552 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:24:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051854550 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:24:16 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:10:24:16 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051855498 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:16 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051855498 HTTP/1.1" 200 258
************* - - [13/Aug/2025:10:24:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [13/Aug/2025:10:24:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755051857618 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051857620 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:17 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755051857621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:17 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755051857621 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:24:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755051857618 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:24:17 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755051857621 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:24:17 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755051857621 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:24:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051857620 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755051861708 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051861702 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+10:24:21&etm=&_timer304=1755051861767 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755051861767 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755051861767 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755051861767 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+11:00&filterCnt=6&_timer304=1755051861767 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755051861702 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051861828 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+10:24:21&etm=&_timer304=1755051861767 HTTP/1.1" 200 156
************* - - [13/Aug/2025:10:24:23 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [13/Aug/2025:10:24:23 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051861851 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:10:24:23 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755051861767 HTTP/1.1" 200 166
************* - - [13/Aug/2025:10:24:23 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755051861708 HTTP/1.1" 200 159616
************* - - [13/Aug/2025:10:24:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [13/Aug/2025:10:24:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [13/Aug/2025:10:24:23 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+11:00&filterCnt=6&_timer304=1755051861767 HTTP/1.1" 200 164
************* - - [13/Aug/2025:10:24:23 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755051861767 HTTP/1.1" 200 169
************* - - [13/Aug/2025:10:24:23 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755051861767 HTTP/1.1" 200 13016
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051861851 HTTP/1.1" 200 258
************* - - [13/Aug/2025:10:24:23 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [13/Aug/2025:10:24:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [13/Aug/2025:10:24:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [13/Aug/2025:10:24:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:10:24:24 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051863050 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************* - - [13/Aug/2025:10:24:24 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755051863050 HTTP/1.1" 200 258
************* - - [13/Aug/2025:10:24:24 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755051861828 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:10:24:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1980
************* - - [13/Aug/2025:10:24:25 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:26 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3113
************* - - [13/Aug/2025:10:24:26 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755051865116 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:26 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755051865116 HTTP/1.1" 200 232
************* - - [13/Aug/2025:10:24:27 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755051866488 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:27 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:27 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755051866488 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:24:27 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [13/Aug/2025:10:24:31 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:31 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************* - - [13/Aug/2025:10:24:32 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:32 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23603
************* - - [13/Aug/2025:10:24:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755051871502 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051871530 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755051871502 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:24:32 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755051871532 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:32 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755051871532 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:24:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755051871530 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:24:32 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755051871532 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:24:32 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755051871532 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:25:07 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755051907283 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:25:07 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755051907283 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:25:07 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:25:07 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:10:25:07 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:25:07 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:10:25:17 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:25:17 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:10:25:25 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:25:25 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [13/Aug/2025:10:25:45 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:25:45 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:10:26:18 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:26:18 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************* - - [13/Aug/2025:10:26:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755052007848 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:26:49 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:26:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755052007848 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:26:49 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************* - - [13/Aug/2025:10:26:53 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:26:53 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [13/Aug/2025:10:27:41 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:27:41 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [13/Aug/2025:10:29:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052147723 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:29:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755052147724 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:29:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:29:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:29:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:29:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052147726 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:29:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052147723 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:29:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:10:29:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755052147724 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:29:08 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:10:29:09 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052147726 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:10:29:09 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:10:29:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755052158614 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:29:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755052158614 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:29:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052158615 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:29:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755052158614 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:29:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755052158614 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:29:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052158615 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:29:58 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755052197504 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:29:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052197551 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:29:58 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755052197504 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:29:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052197551 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:29:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052198603 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:29:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755052198606 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:29:59 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:29:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755052198606 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:29:59 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:29:59 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:29:59 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052198618 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:29:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052198603 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:29:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755052198606 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:30:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:10:30:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755052198606 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:30:00 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:10:30:01 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:10:30:01 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052198618 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:10:30:45 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:30:45 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755052243863 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:30:45 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755052243863 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:30:45 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************* - - [13/Aug/2025:10:32:07 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:32:07 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23603
************* - - [13/Aug/2025:10:32:16 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:32:16 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************* - - [13/Aug/2025:10:32:17 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:32:17 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23514
************* - - [13/Aug/2025:10:32:23 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:32:23 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23387
************ - - [13/Aug/2025:10:34:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755052447626 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:34:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052447625 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:34:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:34:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:34:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052447629 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:34:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:34:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052447625 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:34:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755052447626 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:34:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:10:34:08 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:10:34:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052447629 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:10:34:08 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:10:34:19 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755052459615 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:34:19 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755052459615 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:34:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052459616 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:34:19 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755052459615 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:34:19 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755052459615 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:34:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052459616 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:34:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052463703 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:34:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755052463707 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:34:25 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:34:25 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:34:25 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:34:25 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052463708 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:34:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052463703 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:34:25 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:10:34:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755052463707 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:34:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:10:34:26 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052463708 HTTP/1.1" 200 441941
************* - - [13/Aug/2025:10:34:26 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 52488
************* - - [13/Aug/2025:10:35:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052558501 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:35:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052558501 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:36:37 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755052595922 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:36:37 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755052595921 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:36:37 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755052595922 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:36:37 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755052595921 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:39:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052746348 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:39:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755052746349 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:39:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:39:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:39:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052746350 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:39:06 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:39:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052746348 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:39:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:10:39:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755052746349 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:39:06 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:10:39:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052746350 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:10:39:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:10:39:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052759349 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:39:19 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755052759348 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:39:19 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755052759348 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:39:19 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755052759348 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:39:19 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755052759348 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:39:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052759349 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:39:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755052785259 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:39:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755052785259 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:40:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052811634 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:40:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755052811634 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:40:44 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:40:44 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:40:44 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:40:44 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052844488 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:40:44 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:10:40:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:10:40:45 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755052844488 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:10:40:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:10:40:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052858932 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:40:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755052858932 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:41:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755052909349 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:41:49 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755052909349 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:41:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755052909349 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:41:49 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755052909349 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:44:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755053046469 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:44:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053046469 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:44:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:44:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:44:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053046471 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:44:06 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:44:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053046469 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:44:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:10:44:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755053046469 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:44:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:10:44:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053046471 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:10:44:08 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:10:44:20 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755053060349 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:44:20 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755053060349 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:44:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053060350 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:44:20 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755053060349 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:44:20 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755053060349 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:44:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053060350 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:44:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053095422 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:44:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755053095424 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:44:55 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053095422 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:44:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755053095424 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:44:55 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:44:55 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:44:55 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:44:55 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053095597 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:44:55 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:10:44:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:10:44:57 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:10:44:57 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053095597 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:10:45:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053159067 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:45:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053159067 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:46:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755053209480 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:46:49 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755053209480 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:46:49 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755053209480 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:46:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755053209480 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:49:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755053346358 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:49:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053346357 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:49:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:49:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:49:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053346359 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:49:06 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:49:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053346357 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:49:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:10:49:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755053346358 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:49:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:10:49:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053346359 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:10:49:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:10:49:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053361346 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:49:21 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755053361345 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:49:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755053361345 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:49:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053361346 HTTP/1.1" 200 159
************ - - [13/Aug/2025:10:49:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755053361345 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:49:21 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755053361345 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:50:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053440133 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:50:40 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053440133 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:52:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053539950 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:52:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053539950 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:52:58 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755053578172 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:52:58 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755053578172 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:53:28 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755053608392 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:53:28 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755053608392 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:53:28 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755053608392 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:53:28 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755053608392 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:54:03 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:54:03 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:54:03 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053643789 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:54:03 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:54:04 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:10:54:04 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:10:54:05 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053643789 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:10:54:05 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:10:54:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053646353 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:54:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053646353 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:54:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755053657355 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:54:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:54:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:54:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053657357 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:54:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:54:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:10:54:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755053657355 HTTP/1.1" 200 160
************ - - [13/Aug/2025:10:54:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:10:54:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053657357 HTTP/1.1" 200 442541
************ - - [13/Aug/2025:10:54:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:10:54:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053662350 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:54:22 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755053662351 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:54:22 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755053662351 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:54:22 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755053662351 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:54:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053662350 HTTP/1.1" 200 159
************ - - [13/Aug/2025:10:54:22 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755053662351 HTTP/1.1" 200 148
************* - - [13/Aug/2025:10:56:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053775789 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:56:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755053775790 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:56:16 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053775789 HTTP/1.1" 200 161
************* - - [13/Aug/2025:10:56:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755053775790 HTTP/1.1" 200 160
************* - - [13/Aug/2025:10:56:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:56:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:56:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053775911 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:56:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:56:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:10:56:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:10:56:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755053775911 HTTP/1.1" 200 442541
************* - - [13/Aug/2025:10:56:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:10:57:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053840602 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:57:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053840602 HTTP/1.1" 200 159
************ - - [13/Aug/2025:10:59:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053957356 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:59:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755053957356 HTTP/1.1" 200 161
************ - - [13/Aug/2025:10:59:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053963348 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:59:23 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755053963349 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:59:23 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755053963349 HTTP/1.1" 200 -
************ - - [13/Aug/2025:10:59:23 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755053963349 HTTP/1.1" 200 148
************ - - [13/Aug/2025:10:59:23 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755053963349 HTTP/1.1" 200 152
************ - - [13/Aug/2025:10:59:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755053963348 HTTP/1.1" 200 159
************* - - [13/Aug/2025:10:59:39 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755053979059 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:59:39 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755053979059 HTTP/1.1" 200 -
************* - - [13/Aug/2025:10:59:39 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755053979059 HTTP/1.1" 200 152
************* - - [13/Aug/2025:10:59:39 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755053979059 HTTP/1.1" 200 148
************ - - [13/Aug/2025:11:00:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755054017355 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:00:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:00:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:00:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:00:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054017357 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:00:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:11:00:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755054017355 HTTP/1.1" 200 160
************ - - [13/Aug/2025:11:00:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:11:00:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054017357 HTTP/1.1" 200 442537
************ - - [13/Aug/2025:11:00:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:11:01:50 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054110767 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:01:51 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054110767 HTTP/1.1" 200 161
************* - - [13/Aug/2025:11:03:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054233709 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:03:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054233709 HTTP/1.1" 200 159
************ - - [13/Aug/2025:11:04:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054246344 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:04:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054246344 HTTP/1.1" 200 161
************ - - [13/Aug/2025:11:04:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755054247350 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:04:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:04:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:04:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054247352 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:04:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:04:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:11:04:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755054247350 HTTP/1.1" 200 160
************ - - [13/Aug/2025:11:04:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:11:04:08 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:11:04:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054247352 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:11:04:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755054251916 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:04:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755054251916 HTTP/1.1" 200 160
************ - - [13/Aug/2025:11:04:24 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755054264356 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:04:24 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755054264356 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:04:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054264357 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:04:24 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755054264356 HTTP/1.1" 200 152
************ - - [13/Aug/2025:11:04:24 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755054264356 HTTP/1.1" 200 148
************ - - [13/Aug/2025:11:04:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054264357 HTTP/1.1" 200 159
************* - - [13/Aug/2025:11:04:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:04:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:04:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:04:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054274647 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:04:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054274614 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:04:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:11:04:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054274647 HTTP/1.1" 200 161
************* - - [13/Aug/2025:11:04:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:11:04:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054274614 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:11:04:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:11:04:39 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755054279610 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:04:39 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755054279610 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:04:39 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755054279610 HTTP/1.1" 200 148
************* - - [13/Aug/2025:11:04:39 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755054279610 HTTP/1.1" 200 152
************* - - [13/Aug/2025:11:05:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755054342702 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:05:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755054342702 HTTP/1.1" 200 160
************* - - [13/Aug/2025:11:07:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:07:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:07:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054435898 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:07:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:07:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:11:07:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:11:07:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054435898 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:11:07:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:11:09:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054546356 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:09:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054546356 HTTP/1.1" 200 161
************ - - [13/Aug/2025:11:09:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755054547343 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:09:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054547345 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:09:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:11:09:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755054547343 HTTP/1.1" 200 160
************ - - [13/Aug/2025:11:09:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:11:09:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054547345 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:11:09:08 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************ - - [13/Aug/2025:11:09:25 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755054565348 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:09:25 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755054565348 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:09:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054565349 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:09:25 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755054565348 HTTP/1.1" 200 152
************ - - [13/Aug/2025:11:09:25 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755054565348 HTTP/1.1" 200 148
************ - - [13/Aug/2025:11:09:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054565349 HTTP/1.1" 200 159
************* - - [13/Aug/2025:11:09:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054585084 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:09:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054585084 HTTP/1.1" 200 159
************* - - [13/Aug/2025:11:11:32 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755054691795 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:11:32 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755054691795 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:11:32 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755054691795 HTTP/1.1" 200 152
************* - - [13/Aug/2025:11:11:32 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755054691795 HTTP/1.1" 200 148
************* - - [13/Aug/2025:11:12:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054731685 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:12:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054731685 HTTP/1.1" 200 161
************ - - [13/Aug/2025:11:14:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054857357 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:14:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054857357 HTTP/1.1" 200 161
************ - - [13/Aug/2025:11:14:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755054866352 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:14:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755054866352 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:14:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054866353 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:14:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755054866352 HTTP/1.1" 200 148
************ - - [13/Aug/2025:11:14:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755054866352 HTTP/1.1" 200 152
************ - - [13/Aug/2025:11:14:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054866353 HTTP/1.1" 200 159
************* - - [13/Aug/2025:11:14:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755054874603 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:14:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:14:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:14:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:14:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054874608 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:14:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054874654 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:14:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755054874603 HTTP/1.1" 200 160
************* - - [13/Aug/2025:11:14:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:11:14:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755054874654 HTTP/1.1" 200 161
************* - - [13/Aug/2025:11:14:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:11:14:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:11:14:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054874608 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:11:14:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054885610 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:14:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755054885610 HTTP/1.1" 200 159
************ - - [13/Aug/2025:11:15:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755054917343 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:15:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054917344 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:15:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:11:15:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755054917343 HTTP/1.1" 200 160
************ - - [13/Aug/2025:11:15:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:11:15:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054917344 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:11:15:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:11:15:36 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755054936597 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:15:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755054936597 HTTP/1.1" 200 160
************* - - [13/Aug/2025:11:15:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:15:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054936739 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:15:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:15:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:15:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [13/Aug/2025:11:15:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [13/Aug/2025:11:15:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:11:15:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755054936739 HTTP/1.1" 200 442527
************* - - [13/Aug/2025:11:16:33 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755054992910 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:16:33 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755054992910 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:16:33 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755054992910 HTTP/1.1" 200 152
************* - - [13/Aug/2025:11:16:33 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755054992910 HTTP/1.1" 200 148
************ - - [13/Aug/2025:11:19:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755055157349 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:19:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755055157349 HTTP/1.1" 200 161
************ - - [13/Aug/2025:11:19:27 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755055167355 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:19:27 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755055167355 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:19:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755055167355 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:19:27 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755055167355 HTTP/1.1" 200 152
************ - - [13/Aug/2025:11:19:27 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755055167355 HTTP/1.1" 200 148
************ - - [13/Aug/2025:11:19:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755055167355 HTTP/1.1" 200 159
************ - - [13/Aug/2025:11:20:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755055217351 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:20:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755055217352 HTTP/1.1" 200 -
************ - - [13/Aug/2025:11:20:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:11:20:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755055217351 HTTP/1.1" 200 160
************ - - [13/Aug/2025:11:20:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:11:20:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755055217352 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:11:20:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53675
************* - - [13/Aug/2025:11:20:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755055223741 HTTP/1.1" 200 -
************* - - [13/Aug/2025:11:20:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755055223741 HTTP/1.1" 200 159
************ - - [13/Aug/2025:13:38:36 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [13/Aug/2025:13:38:36 +0800] "GET /login HTTP/1.1" 302 -
************ - - [13/Aug/2025:13:38:37 +0800] "GET /login?code=N2Ep9L&state=hIcNZT HTTP/1.1" 302 -
************ - - [13/Aug/2025:13:38:37 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [13/Aug/2025:13:38:45 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [13/Aug/2025:13:38:46 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1755063526315 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:48 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1755063526315 HTTP/1.1" 200 552
************ - - [13/Aug/2025:13:38:48 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755063528965 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755063528965 HTTP/1.1" 200 61784
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755063529042 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755063529042 HTTP/1.1" 200 10388
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755063529059 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755063529059 HTTP/1.1" 200 2009
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755063529247 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+13:38:49&etm=&_timer304=1755063529247 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755063529247 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+14:00&filterCnt=6&_timer304=1755063529247 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755063529247 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755063529247 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755063529247 HTTP/1.1" 200 1482
************ - - [13/Aug/2025:13:38:49 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+13:38:49&etm=&_timer304=1755063529247 HTTP/1.1" 200 156
************ - - [13/Aug/2025:13:38:49 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755063529247 HTTP/1.1" 200 166
************ - - [13/Aug/2025:13:38:49 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+14:00&filterCnt=6&_timer304=1755063529247 HTTP/1.1" 200 164
************ - - [13/Aug/2025:13:38:49 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:13:38:49 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:13:38:49 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:13:38:49 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:13:38:49 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755063529247 HTTP/1.1" 200 169
************ - - [13/Aug/2025:13:38:49 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755063529247 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755063529852 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/base/saas/token?_timer304=1755063529852 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755063529861 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:49 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:50 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************ - - [13/Aug/2025:13:38:51 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:13:38:51 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:13:38:51 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1974
************ - - [13/Aug/2025:13:38:51 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 2338
************ - - [13/Aug/2025:13:38:52 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [13/Aug/2025:13:38:52 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [13/Aug/2025:13:38:52 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [13/Aug/2025:13:38:52 +0800] "GET /api/base/saas/token?_timer304=1755063529852 HTTP/1.1" 200 411
************ - - [13/Aug/2025:13:38:52 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755063529861 HTTP/1.1" 200 2009
************ - - [13/Aug/2025:13:38:52 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [13/Aug/2025:13:38:52 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [13/Aug/2025:13:38:52 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [13/Aug/2025:13:38:52 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [13/Aug/2025:13:38:52 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [13/Aug/2025:13:38:53 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [13/Aug/2025:13:38:55 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-12&_timer304=1755063529852 HTTP/1.1" 200 418
************ - - [13/Aug/2025:13:38:56 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755063536067 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755063536067 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:56 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755063536074 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:56 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755063536074 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:56 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755063536074 HTTP/1.1" 200 152
************ - - [13/Aug/2025:13:38:56 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755063536074 HTTP/1.1" 200 148
************ - - [13/Aug/2025:13:38:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755063536067 HTTP/1.1" 200 159
************ - - [13/Aug/2025:13:38:56 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755063536067 HTTP/1.1" 200 160
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/ewci/base/mal/write/875?_timer304=1755063538140 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+13:38:58&etm=&_timer304=1755063538248 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+14:00&filterCnt=6&_timer304=1755063538248 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755063538248 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755063538248 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755063538248 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755063538249 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755063538249 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:13:38:58 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:13:38:58 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:13:38:58 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:13:38:58 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755063538248 HTTP/1.1" 200 166
************ - - [13/Aug/2025:13:38:58 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+14:00&filterCnt=6&_timer304=1755063538248 HTTP/1.1" 200 164
************ - - [13/Aug/2025:13:38:58 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+13:38:58&etm=&_timer304=1755063538248 HTTP/1.1" 200 156
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/xxjh/plan/select-by-tree HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755063538249 HTTP/1.1" 200 161
************ - - [13/Aug/2025:13:38:58 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755063538248 HTTP/1.1" 200 169
************ - - [13/Aug/2025:13:38:58 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755063538248 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:13:38:58 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 148
************ - - [13/Aug/2025:13:38:58 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755063538249 HTTP/1.1" 200 159616
************ - - [13/Aug/2025:13:38:58 +0800] "OPTIONS /api/xxjh/plan/select-by-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:58 +0800] "GET /api/ewci/base/mal/write/875?_timer304=1755063538140 HTTP/1.1" 200 146
************ - - [13/Aug/2025:13:38:59 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 386795
************ - - [13/Aug/2025:13:38:59 +0800] "OPTIONS /api/usif/ad/get-ad-list?adcd=220000000000000&adLvl=3&_timer304=1755063539040 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:38:59 +0800] "GET /api/usif/ad/get-ad-list?adcd=220000000000000&adLvl=3&_timer304=1755063539040 HTTP/1.1" 200 12483
************ - - [13/Aug/2025:13:38:59 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 5971
************ - - [13/Aug/2025:13:39:00 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 386795
************ - - [13/Aug/2025:13:39:06 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 148
************ - - [13/Aug/2025:13:39:06 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 148
************ - - [13/Aug/2025:13:43:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755063826439 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:43:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755063826439 HTTP/1.1" 200 160
************ - - [13/Aug/2025:13:43:56 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755063836320 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:43:56 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755063836320 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:43:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755063836321 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:43:56 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755063836320 HTTP/1.1" 200 152
************ - - [13/Aug/2025:13:43:56 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755063836320 HTTP/1.1" 200 148
************ - - [13/Aug/2025:13:43:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755063836321 HTTP/1.1" 200 159
************ - - [13/Aug/2025:13:43:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755063838319 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:43:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755063838319 HTTP/1.1" 200 161
************ - - [13/Aug/2025:13:48:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755064126329 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:48:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755064126329 HTTP/1.1" 200 160
************ - - [13/Aug/2025:13:48:57 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755064137324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:48:57 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755064137324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:48:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755064137324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:48:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755064137324 HTTP/1.1" 200 159
************ - - [13/Aug/2025:13:48:57 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755064137324 HTTP/1.1" 200 152
************ - - [13/Aug/2025:13:48:57 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755064137324 HTTP/1.1" 200 148
************ - - [13/Aug/2025:13:48:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755064138332 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:48:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755064138332 HTTP/1.1" 200 161
************ - - [13/Aug/2025:13:53:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755064426322 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:53:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755064426322 HTTP/1.1" 200 160
************ - - [13/Aug/2025:13:53:58 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755064438319 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:53:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755064438318 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:53:58 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755064438319 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:53:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755064438319 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:53:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755064438319 HTTP/1.1" 200 161
************ - - [13/Aug/2025:13:53:58 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755064438319 HTTP/1.1" 200 148
************ - - [13/Aug/2025:13:53:58 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755064438319 HTTP/1.1" 200 152
************ - - [13/Aug/2025:13:53:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755064438318 HTTP/1.1" 200 159
************ - - [13/Aug/2025:13:58:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755064726332 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:58:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755064726332 HTTP/1.1" 200 160
************ - - [13/Aug/2025:13:58:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755064738330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:58:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755064738330 HTTP/1.1" 200 161
************ - - [13/Aug/2025:13:58:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755064739330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:58:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755064739330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:58:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755064739330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:13:58:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755064739330 HTTP/1.1" 200 152
************ - - [13/Aug/2025:13:58:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755064739330 HTTP/1.1" 200 148
************ - - [13/Aug/2025:13:58:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755064739330 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:03:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755065026326 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:03:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755065026326 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:03:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755065038320 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:03:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755065038320 HTTP/1.1" 200 161
************ - - [13/Aug/2025:14:04:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755065040327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:04:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065040328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:04:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755065040327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:04:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755065040327 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:04:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755065040327 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:04:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065040328 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:08:41 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755065321105 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755065321104 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755065321104 HTTP/1.1" 200 161
************ - - [13/Aug/2025:14:08:41 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:08:41&etm=&_timer304=1755065321115 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065321115 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065321115 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065321115 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:41 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065321115 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:41 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:08:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:08:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:08:41 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755065321105 HTTP/1.1" 200 159616
************ - - [13/Aug/2025:14:08:41 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:08:41&etm=&_timer304=1755065321115 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:08:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:08:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065321115 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:08:41 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065321115 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:08:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065321115 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:08:41 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:08:41 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065321115 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:08:41 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 386795
************ - - [13/Aug/2025:14:08:42 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 5971
************ - - [13/Aug/2025:14:08:43 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 386795
************ - - [13/Aug/2025:14:08:51 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755065331322 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065331323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:51 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755065331323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:51 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755065331323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:08:51 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755065331322 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:08:51 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755065331323 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:08:51 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755065331323 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:08:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065331323 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:09:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755065382791 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:42 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755065382792 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755065382791 HTTP/1.1" 200 161
************ - - [13/Aug/2025:14:09:42 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:09:42&etm=&_timer304=1755065382805 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065382805 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065382805 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:42 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065382805 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065382805 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:42 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755065382792 HTTP/1.1" 200 159616
************ - - [13/Aug/2025:14:09:42 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:09:42 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:09:42 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:09:42 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:09:42&etm=&_timer304=1755065382805 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:09:42 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:09:42 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065382805 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:09:42 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065382805 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:09:42 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065382805 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:09:42 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:09:42 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065382805 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:09:44 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 386795
************ - - [13/Aug/2025:14:09:44 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 5971
************ - - [13/Aug/2025:14:09:45 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 386795
************ - - [13/Aug/2025:14:09:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755065392534 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755065392534 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:09:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065392564 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:52 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755065392565 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:52 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755065392565 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065392564 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:09:52 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755065392565 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:09:52 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755065392565 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:09:56 +0800] "OPTIONS /api/ewci/base/mal/write/59?_timer304=1755065396437 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:56 +0800] "GET /api/ewci/base/mal/write/59?_timer304=1755065396437 HTTP/1.1" 200 145
************ - - [13/Aug/2025:14:09:56 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:09:56&etm=&_timer304=1755065396515 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065396515 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065396515 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:56 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065396515 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065396515 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:56 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755065396519 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:56 +0800] "OPTIONS /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:56 +0800] "OPTIONS /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:09:56 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:09:56 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:09:56 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:09:56 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:09:56&etm=&_timer304=1755065396515 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:09:56 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:09:56 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065396515 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:09:56 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065396515 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:09:56 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755065396519 HTTP/1.1" 200 12285
************ - - [13/Aug/2025:14:09:56 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065396515 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:09:56 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065396515 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:09:57 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 713
************ - - [13/Aug/2025:14:09:57 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2978
************ - - [13/Aug/2025:14:10:01 +0800] "OPTIONS /api/ewci/base/mal/write/394?_timer304=1755065401651 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:01 +0800] "GET /api/ewci/base/mal/write/394?_timer304=1755065401651 HTTP/1.1" 200 146
************ - - [13/Aug/2025:14:10:01 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:10:01&etm=&_timer304=1755065401742 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065401742 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065401742 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065401742 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:01 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065401742 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:01 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:10:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:10:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:10:01 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:10:01&etm=&_timer304=1755065401742 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:10:01 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:10:01 +0800] "OPTIONS /api/ewci/warn/type/select-page?pageNum=1&pageSize=-1&_timer304=1755065401751 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:01 +0800] "OPTIONS /api/ewci/warn/info/select-info-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065401742 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:10:01 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065401742 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:10:01 +0800] "GET /api/ewci/warn/type/select-page?pageNum=1&pageSize=-1&_timer304=1755065401751 HTTP/1.1" 200 4572
************ - - [13/Aug/2025:14:10:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065401742 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:10:01 +0800] "POST /api/ewci/warn/info/select-info-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:14:10:01 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065401742 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:10:03 +0800] "OPTIONS /api/ewci/base/mal/write/59?_timer304=1755065403227 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:03 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:10:03&etm=&_timer304=1755065403227 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:03 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065403227 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065403227 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065403227 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065403227 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:03 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:10:03 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:10:03 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:10:03 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065403227 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:10:03 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065403227 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:10:03 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:10:03 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:10:03&etm=&_timer304=1755065403227 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:10:03 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065403227 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:10:03 +0800] "GET /api/ewci/base/mal/write/59?_timer304=1755065403227 HTTP/1.1" 200 145
************ - - [13/Aug/2025:14:10:03 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065403227 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:10:04 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 713
************ - - [13/Aug/2025:14:10:04 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2978
************ - - [13/Aug/2025:14:10:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065434289 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065434289 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:34 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:10:34&etm=&_timer304=1755065434289 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:34 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065434289 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065434289 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:10:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:10:34 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:10:34 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065434289 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:10:34 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:10:34&etm=&_timer304=1755065434289 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:10:34 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:10:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065434289 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:10:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065434289 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:10:34 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065434289 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:10:35 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 713
************ - - [13/Aug/2025:14:10:35 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2978
************ - - [13/Aug/2025:14:10:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755065444192 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065444193 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755065444192 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:10:44 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755065444208 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:44 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755065444208 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:10:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065444193 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:10:44 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755065444208 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:10:44 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755065444208 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:12:27 +0800] "OPTIONS /api/ew/statistics/export-ad-warn-grade-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:12:32 +0800] "POST /api/ew/statistics/export-ad-warn-grade-list HTTP/1.1" 200 4605
************ - - [13/Aug/2025:14:14:54 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 713
************ - - [13/Aug/2025:14:14:54 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2978
************ - - [13/Aug/2025:14:14:54 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 713
************ - - [13/Aug/2025:14:14:54 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2978
************ - - [13/Aug/2025:14:14:56 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 1287
************ - - [13/Aug/2025:14:14:56 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 701
************ - - [13/Aug/2025:14:15:00 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 713
************ - - [13/Aug/2025:14:15:00 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2978
************ - - [13/Aug/2025:14:15:04 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 1287
************ - - [13/Aug/2025:14:15:04 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 701
************ - - [13/Aug/2025:14:15:06 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 1845
************ - - [13/Aug/2025:14:15:07 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 704
************ - - [13/Aug/2025:14:15:08 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 163
************ - - [13/Aug/2025:14:15:09 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 699
************ - - [13/Aug/2025:14:15:10 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 163
************ - - [13/Aug/2025:14:15:10 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 697
************ - - [13/Aug/2025:14:15:13 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 163
************ - - [13/Aug/2025:14:15:13 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 699
************ - - [13/Aug/2025:14:15:14 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 1287
************ - - [13/Aug/2025:14:15:14 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 701
************ - - [13/Aug/2025:14:15:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755065734333 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:15:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755065734333 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:15:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065744333 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:15:44 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755065744334 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:15:44 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755065744334 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:15:44 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755065744334 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:15:44 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755065744334 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:15:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755065744333 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:16:50 +0800] "OPTIONS /api/ewci/base/mal/write/59?_timer304=1755065810511 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:16:50 +0800] "GET /api/ewci/base/mal/write/59?_timer304=1755065810511 HTTP/1.1" 200 145
************ - - [13/Aug/2025:14:16:51 +0800] "OPTIONS /api/ewci/base/mal/write/394?_timer304=1755065811666 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:16:51 +0800] "GET /api/ewci/base/mal/write/394?_timer304=1755065811666 HTTP/1.1" 200 146
************ - - [13/Aug/2025:14:16:51 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:16:51&etm=&_timer304=1755065811780 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:16:51 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065811780 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:16:51 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065811780 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:16:51 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065811780 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:16:51 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065811780 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:16:51 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:16:51 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:16:51 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:16:51 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:16:51&etm=&_timer304=1755065811780 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:16:51 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:16:51 +0800] "OPTIONS /api/ewci/warn/type/select-page?pageNum=1&pageSize=-1&_timer304=1755065811789 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:16:51 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065811780 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:16:51 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065811780 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:16:51 +0800] "GET /api/ewci/warn/type/select-page?pageNum=1&pageSize=-1&_timer304=1755065811789 HTTP/1.1" 200 4572
************ - - [13/Aug/2025:14:16:51 +0800] "POST /api/ewci/warn/info/select-info-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:14:16:51 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065811780 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:16:51 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065811780 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:16:58 +0800] "POST /api/ewci/warn/info/select-info-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:14:17:00 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:16:59&etm=&_timer304=1755065820053 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:17:00 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065820053 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:17:00 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065820053 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:17:00 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065820053 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:17:00 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065820053 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:17:00 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:17:00 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:17:00 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:16:59&etm=&_timer304=1755065820053 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:17:00 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:17:00 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755065820053 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:17:00 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:17:00 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755065820053 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:17:00 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755065820053 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:17:00 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755065820053 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:17:01 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 713
************ - - [13/Aug/2025:14:17:01 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2978
************ - - [13/Aug/2025:14:17:09 +0800] "OPTIONS /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:17:09 +0800] "OPTIONS /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:17:10 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 713
************ - - [13/Aug/2025:14:17:10 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2978
************ - - [13/Aug/2025:14:20:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755066034430 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:20:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755066034430 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:20:45 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755066045333 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:20:45 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755066045333 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:20:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755066045334 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:20:45 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755066045333 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:20:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755066045334 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:20:45 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755066045333 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:25:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755066334203 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:25:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755066334203 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:25:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755066346330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:25:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755066346331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:25:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755066346331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:25:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755066346331 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:25:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755066346330 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:25:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755066346331 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1755066601988 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1755066601988 HTTP/1.1" 200 144
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:30:02&etm=&_timer304=1755066602239 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755066602239 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755066602239 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755066602239 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755066602239 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755066602240 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755066602247 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:30:02&etm=&_timer304=1755066602239 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:30:02 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:30:02 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755066602239 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:30:02 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:30:02 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:30:02 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755066602239 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:30:02 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:30:02 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755066602240 HTTP/1.1" 200 161
************ - - [13/Aug/2025:14:30:02 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755066602239 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:30:02 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755066602247 HTTP/1.1" 200 159616
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755066602421 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755066602239 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755066602465 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [13/Aug/2025:14:30:02 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755066602465 HTTP/1.1" 200 258
************ - - [13/Aug/2025:14:30:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [13/Aug/2025:14:30:02 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [13/Aug/2025:14:30:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [13/Aug/2025:14:30:03 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [13/Aug/2025:14:30:03 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755066603596 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:03 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755066603674 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:03 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 740
************ - - [13/Aug/2025:14:30:03 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755066603674 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:30:03 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755066603596 HTTP/1.1" 200 258
************ - - [13/Aug/2025:14:30:03 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:03 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755066602421 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:14:30:03 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3140
************ - - [13/Aug/2025:14:30:04 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:05 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:05 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 240
************ - - [13/Aug/2025:14:30:05 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3140
************ - - [13/Aug/2025:14:30:19 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:19 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:20 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:20 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:21 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:21 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:21 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:21 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:22 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:22 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:23 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:23 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:24 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:24 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:24 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:24 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:25 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:25 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:25 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:25 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:26 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:26 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:26 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:26 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:14:30:32 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:32 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [13/Aug/2025:14:30:33 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:33 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 326225
************ - - [13/Aug/2025:14:30:33 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:33 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [13/Aug/2025:14:30:33 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:33 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 324700
************ - - [13/Aug/2025:14:30:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755066634195 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755066634195 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:30:35 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:35 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 32146
************ - - [13/Aug/2025:14:30:35 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:35 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [13/Aug/2025:14:30:36 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:36 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [13/Aug/2025:14:30:37 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 279896
************ - - [13/Aug/2025:14:30:37 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [13/Aug/2025:14:30:39 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:39 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 326225
************ - - [13/Aug/2025:14:30:39 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:39 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [13/Aug/2025:14:30:40 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:40 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 326225
************ - - [13/Aug/2025:14:30:40 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:40 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [13/Aug/2025:14:30:40 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:41 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 324700
************ - - [13/Aug/2025:14:30:41 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:41 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 324700
************ - - [13/Aug/2025:14:30:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755066646380 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755066646396 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755066646396 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755066646380 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:30:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755066646396 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:30:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755066646396 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:30:50 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:30:50 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23603
************ - - [13/Aug/2025:14:31:00 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:00 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [13/Aug/2025:14:31:00 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:00 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 326225
************ - - [13/Aug/2025:14:31:01 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:01 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23603
************ - - [13/Aug/2025:14:31:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [13/Aug/2025:14:31:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 326225
************ - - [13/Aug/2025:14:31:12 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:12 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78290
************ - - [13/Aug/2025:14:31:12 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:12 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 324700
************ - - [13/Aug/2025:14:31:13 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:13 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 324700
************ - - [13/Aug/2025:14:31:26 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:26 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23603
************ - - [13/Aug/2025:14:31:28 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:31:28 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23315
************ - - [13/Aug/2025:14:35:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755066902018 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:35:02 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755066902018 HTTP/1.1" 200 161
************ - - [13/Aug/2025:14:35:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:35:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755066902346 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:35:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:35:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:35:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:14:35:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:14:35:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755066902346 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:14:35:03 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:14:35:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755066934189 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:35:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755066934189 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:35:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755066946510 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:35:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755066946511 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:35:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755066946511 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:35:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755066946511 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:35:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755066946511 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:35:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755066946510 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:40:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755067202023 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:40:02 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755067202023 HTTP/1.1" 200 161
************ - - [13/Aug/2025:14:40:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:40:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:40:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755067202350 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:40:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:40:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:14:40:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:14:40:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755067202350 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:14:40:03 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:14:40:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755067234196 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:40:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755067234196 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:40:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755067246554 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:40:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755067246554 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:40:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755067246561 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:40:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755067246554 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:40:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755067246554 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:40:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755067246561 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:45:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755067502019 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:45:02 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755067502019 HTTP/1.1" 200 161
************ - - [13/Aug/2025:14:45:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:45:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:45:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755067502342 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:45:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:45:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:14:45:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:14:45:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755067502342 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:14:45:03 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:14:45:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755067534196 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:45:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755067534196 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:45:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755067546604 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:45:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755067546604 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:45:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755067546611 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:45:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755067546604 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:45:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755067546604 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:45:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755067546611 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:50:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755067802018 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:50:02 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755067802018 HTTP/1.1" 200 161
************ - - [13/Aug/2025:14:50:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:50:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:50:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755067802360 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:50:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:50:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:14:50:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:14:50:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755067802360 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:14:50:04 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:14:50:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755067834203 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:50:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755067834203 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:50:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755067846683 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:50:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755067846683 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:50:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755067846691 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:50:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755067846683 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:50:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755067846683 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:50:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755067846691 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:55:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755068102014 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:55:02 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755068102014 HTTP/1.1" 200 161
************ - - [13/Aug/2025:14:55:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:55:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:55:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755068102351 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:55:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:55:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:14:55:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:14:55:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755068102351 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:14:55:03 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:14:55:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755068134211 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:55:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755068134211 HTTP/1.1" 200 160
************ - - [13/Aug/2025:14:55:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755068146738 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:55:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755068146738 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:55:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755068146746 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:55:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755068146738 HTTP/1.1" 200 148
************ - - [13/Aug/2025:14:55:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755068146738 HTTP/1.1" 200 152
************ - - [13/Aug/2025:14:55:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755068146746 HTTP/1.1" 200 159
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/ewci/base/mal/write/59?_timer304=1755068354656 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:59:14&etm=&_timer304=1755068354656 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755068354656 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755068354656 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755068354656 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755068354656 HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "OPTIONS /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 -
************ - - [13/Aug/2025:14:59:14 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+14:59:14&etm=&_timer304=1755068354656 HTTP/1.1" 200 156
************ - - [13/Aug/2025:14:59:14 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:14:59:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755068354656 HTTP/1.1" 200 166
************ - - [13/Aug/2025:14:59:14 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+15:00&filterCnt=6&_timer304=1755068354656 HTTP/1.1" 200 164
************ - - [13/Aug/2025:14:59:14 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:14:59:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:59:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:14:59:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755068354656 HTTP/1.1" 200 169
************ - - [13/Aug/2025:14:59:14 +0800] "GET /api/ewci/base/mal/write/59?_timer304=1755068354656 HTTP/1.1" 200 145
************ - - [13/Aug/2025:14:59:14 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755068354656 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:14:59:15 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 713
************ - - [13/Aug/2025:14:59:15 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2978
************ - - [13/Aug/2025:15:00:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755068434193 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:00:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755068434193 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:00:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755068446778 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:00:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755068446779 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:00:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755068446778 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:00:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755068446778 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:00:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755068446779 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:00:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755068446778 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:05:47 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755068747329 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:05:47 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755068747329 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:05:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755068747328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:05:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755068747328 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:05:47 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755068747329 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:05:47 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755068747329 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:06:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755068805325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:06:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755068805325 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:10:48 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755069048323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:10:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755069048322 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:10:48 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755069048323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:10:48 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755069048323 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:10:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755069048322 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:10:48 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755069048323 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:11:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755069073879 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755069073879 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/ewci/base/mal/write/875?_timer304=1755069078222 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "GET /api/ewci/base/mal/write/875?_timer304=1755069078222 HTTP/1.1" 200 146
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+15:11:18&etm=&_timer304=1755069078472 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755069078472 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755069078472 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+16:00&filterCnt=6&_timer304=1755069078472 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755069078472 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755069078472 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755069078474 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:15:11:18 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+15:11:18&etm=&_timer304=1755069078472 HTTP/1.1" 200 156
************ - - [13/Aug/2025:15:11:18 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:15:11:18 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755069078472 HTTP/1.1" 200 166
************ - - [13/Aug/2025:15:11:18 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+16:00&filterCnt=6&_timer304=1755069078472 HTTP/1.1" 200 164
************ - - [13/Aug/2025:15:11:18 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:15:11:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755069078472 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:11:18 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:15:11:18 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755069078472 HTTP/1.1" 200 169
************ - - [13/Aug/2025:15:11:18 +0800] "OPTIONS /api/xxjh/plan/select-by-tree HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:18 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755069078474 HTTP/1.1" 200 159616
************ - - [13/Aug/2025:15:11:18 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755069078472 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:15:11:19 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 5971
************ - - [13/Aug/2025:15:11:19 +0800] "OPTIONS /api/xxjh/plan/select-by-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:20 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 386795
************ - - [13/Aug/2025:15:11:32 +0800] "OPTIONS /api/xxjh/plan/select-by-tree HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:32 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 270
************ - - [13/Aug/2025:15:11:32 +0800] "OPTIONS /api/xxjh/plan/select-by-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:33 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 35320
************ - - [13/Aug/2025:15:11:34 +0800] "OPTIONS /api/xxjh/plan/select-by-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:11:35 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 35320
************ - - [13/Aug/2025:15:12:42 +0800] "OPTIONS /api/xxjh/plan/select-title-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:12:43 +0800] "POST /api/xxjh/plan/select-title-list HTTP/1.1" 200 514
************ - - [13/Aug/2025:15:12:49 +0800] "OPTIONS /api/xxjh/plan/select-by-tree HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:12:49 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 270
************ - - [13/Aug/2025:15:12:49 +0800] "OPTIONS /api/xxjh/plan/select-by-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:12:50 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:12:50 +0800] "OPTIONS /api/xxjh/plan/select-by-tree HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:12:50 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 270
************ - - [13/Aug/2025:15:12:51 +0800] "OPTIONS /api/xxjh/plan/select-by-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:12:51 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 511
************ - - [13/Aug/2025:15:12:53 +0800] "OPTIONS /api/xxjh/plan/select-by-tree HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:12:53 +0800] "POST /api/xxjh/plan/select-by-tree HTTP/1.1" 200 270
************ - - [13/Aug/2025:15:12:53 +0800] "OPTIONS /api/xxjh/plan/select-by-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:12:53 +0800] "POST /api/xxjh/plan/select-by-list HTTP/1.1" 200 511
************ - - [13/Aug/2025:15:13:16 +0800] "OPTIONS /api/xxjh/plan/select-plan-sum-list-new HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:13:18 +0800] "POST /api/xxjh/plan/select-plan-sum-list-new HTTP/1.1" 200 6243
************ - - [13/Aug/2025:15:13:19 +0800] "OPTIONS /api/xxjh/plan/select-plan-sum-list-new HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:13:20 +0800] "POST /api/xxjh/plan/select-plan-sum-list-new HTTP/1.1" 200 6243
************ - - [13/Aug/2025:15:15:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755069334195 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:15:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755069334195 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:15:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755069349327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:15:49 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755069349328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:15:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755069349328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:15:49 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755069349328 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:15:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755069349328 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:15:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755069349327 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:16:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755069378330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:16:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755069378330 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:20:50 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755069650320 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:20:50 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755069650321 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:20:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755069650322 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:20:50 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755069650320 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:20:50 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755069650321 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:20:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755069650322 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:21:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755069678442 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:21:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755069678442 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:21:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755069705333 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:21:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755069705333 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:25:51 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755069951326 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:25:51 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755069951326 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:25:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755069951327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:25:51 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755069951326 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:25:51 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755069951326 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:25:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755069951327 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:26:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755069978330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:26:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755069978330 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:26:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755070005325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:26:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755070005325 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:30:52 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755070252328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:30:52 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755070252327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:30:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755070252329 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:30:52 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755070252327 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:30:52 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755070252328 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:30:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755070252329 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:31:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755070278321 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:31:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755070278321 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:31:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755070305436 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:31:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755070305436 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:35:53 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755070553324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:35:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755070553325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:35:53 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755070553324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:35:53 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755070553324 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:35:53 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755070553324 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:35:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755070553325 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:36:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755070578320 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:36:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755070578320 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:36:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755070605319 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:36:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755070605319 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1755070717060 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+15:38:36&etm=&_timer304=1755070717060 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755070717060 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755070717060 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+16:00&filterCnt=6&_timer304=1755070717060 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755070717060 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755070717067 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755070717068 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-10+15:38:36&etm=&_timer304=1755070717060 HTTP/1.1" 200 156
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220282000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755070717090 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755070717060 HTTP/1.1" 200 166
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755070717090 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-13+08:00&etm=2025-08-13+16:00&filterCnt=6&_timer304=1755070717060 HTTP/1.1" 200 164
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755070717067 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755070717060 HTTP/1.1" 200 169
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1755070717060 HTTP/1.1" 200 144
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755070717068 HTTP/1.1" 200 159616
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755070717060 HTTP/1.1" 200 13016
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755070717090 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755070717479 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 324700
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 324700
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [13/Aug/2025:15:38:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:15:38:37 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755070717479 HTTP/1.1" 200 258
************ - - [13/Aug/2025:15:38:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3140
************ - - [13/Aug/2025:15:38:38 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:38:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220282000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755070717090 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:15:38:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3140
************ - - [13/Aug/2025:15:40:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755070835330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:40:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755070835330 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:40:54 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755070854322 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:40:54 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755070854322 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:40:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755070854323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:40:54 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755070854322 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:40:54 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755070854322 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:40:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755070854323 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:43:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755071017454 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:43:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:43:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:43:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:43:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755071017457 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:43:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755071017454 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:43:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:15:43:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:15:43:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755071017457 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:15:43:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:15:45:55 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755071155332 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:45:55 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755071155332 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:45:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755071155333 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:45:55 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755071155332 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:45:55 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755071155332 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:45:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755071155333 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:46:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755071205329 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:46:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755071205329 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:48:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755071317324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:48:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:48:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:48:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755071317326 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:48:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:48:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755071317324 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:48:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:15:48:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:15:48:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755071317326 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:15:48:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:15:50:56 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755071456334 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:50:56 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755071456334 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:50:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755071456335 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:50:56 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755071456334 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:50:56 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755071456334 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:50:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755071456335 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:51:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755071505331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:51:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755071505331 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:53:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755071617323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:53:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:53:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:53:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:53:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755071617324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:53:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755071617323 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:53:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:15:53:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:15:53:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755071617324 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:15:53:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:15:55:57 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755071757327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:55:57 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755071757327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:55:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755071757329 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:55:57 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755071757327 HTTP/1.1" 200 148
************ - - [13/Aug/2025:15:55:57 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755071757327 HTTP/1.1" 200 152
************ - - [13/Aug/2025:15:55:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755071757329 HTTP/1.1" 200 159
************ - - [13/Aug/2025:15:56:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755071805325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:56:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755071805325 HTTP/1.1" 200 160
************ - - [13/Aug/2025:15:58:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755071917429 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:58:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:58:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755071917431 HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:58:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:58:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:15:58:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755071917429 HTTP/1.1" 200 161
************ - - [13/Aug/2025:15:58:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:15:58:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:15:58:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755071917431 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:15:58:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:00:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755072037123 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:00:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755072037123 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:00:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755072058329 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:00:58 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755072058328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:00:58 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755072058328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:00:58 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755072058328 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:00:58 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755072058328 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:00:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755072058329 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:03:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755072217321 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:03:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:03:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:03:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:03:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755072217323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:03:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755072217321 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:03:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:03:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:03:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755072217323 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:03:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:05:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755072342329 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:05:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755072342329 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:05:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755072359428 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:05:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755072359428 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:05:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755072359429 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:05:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755072359428 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:05:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755072359428 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:05:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755072359429 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:08:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755072516730 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:08:36 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755072516730 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:08:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:08:36 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755072516929 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:08:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:08:36 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:08:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:08:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:08:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755072516929 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:08:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:10:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755072634298 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:10:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755072634298 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:10:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755072659549 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:10:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755072659549 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:10:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755072659550 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:10:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755072659549 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:10:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755072659550 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:10:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755072659549 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:13:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755072816737 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:13:36 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755072816737 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:13:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:13:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:13:36 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755072816936 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:13:36 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:13:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:13:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:13:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755072816936 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:13:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:15:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755072934196 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:15:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755072934196 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:15:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755072959589 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:15:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755072959604 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:15:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755072959604 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:15:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755072959589 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:15:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755072959604 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:15:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755072959604 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:18:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755073125446 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:18:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755073125446 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:19:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:19:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:19:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:19:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755073174666 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:19:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:19:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:19:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755073174666 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:19:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:20:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755073235331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:20:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755073235331 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:21:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755073260325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:21:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755073260324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:21:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755073260325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:21:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755073260325 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:21:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755073260324 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:21:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755073260325 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:23:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755073425325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:23:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755073425325 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:24:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:24:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:24:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:24:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755073485450 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:24:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:24:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:24:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755073485450 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:24:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:26:01 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755073561325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:26:01 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755073561325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:26:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755073561324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:26:01 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755073561325 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:26:01 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755073561325 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:26:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755073561324 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:26:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755073605445 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:26:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755073605445 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:28:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755073725322 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:28:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755073725322 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:29:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:29:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:29:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755073785331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:29:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:29:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:29:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:29:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755073785331 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:29:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:31:02 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755073862434 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:31:02 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755073862434 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:31:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755073862435 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:31:02 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755073862434 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:31:02 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755073862434 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:31:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755073862435 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:31:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755073905441 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:31:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755073905441 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:33:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755074016730 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:33:36 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755074016730 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:33:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:33:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:33:36 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755074016931 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:33:36 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:33:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:33:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:33:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755074016931 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:33:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:35:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755074135321 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:35:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755074135321 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:36:03 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755074163323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:36:03 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755074163323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:36:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755074163324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:36:03 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755074163323 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:36:03 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755074163323 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:36:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755074163324 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:38:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755074325325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:38:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755074325325 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:39:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:39:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:39:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:39:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755074385333 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:39:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:39:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:39:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755074385333 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:39:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:41:04 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755074464432 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:41:04 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755074464432 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:41:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755074464433 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:41:04 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755074464432 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:41:04 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755074464432 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:41:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755074464433 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:41:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755074505331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:41:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755074505331 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:43:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755074625334 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:43:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755074625334 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:44:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:44:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755074685323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:44:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:44:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:44:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:44:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:44:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755074685323 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:44:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:46:05 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755074765325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:46:05 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755074765325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:46:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755074765326 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:46:05 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755074765325 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:46:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755074765326 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:46:05 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755074765325 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:46:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755074805329 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:46:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755074805329 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:48:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755074925331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:48:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755074925331 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:49:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:49:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:49:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755074985325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:49:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:49:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:49:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:49:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755074985325 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:49:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:51:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755075066330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:51:06 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755075066331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:51:06 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755075066331 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:51:06 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755075066331 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:51:06 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755075066331 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:51:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755075066330 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:51:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755075105323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:51:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755075105323 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:53:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755075225325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:53:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755075225325 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:54:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:54:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:54:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:54:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755075285422 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:54:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:54:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:54:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755075285422 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:54:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:16:56:07 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755075367327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:56:07 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755075367327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:56:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755075367328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:56:07 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755075367327 HTTP/1.1" 200 152
************ - - [13/Aug/2025:16:56:07 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755075367327 HTTP/1.1" 200 148
************ - - [13/Aug/2025:16:56:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755075367328 HTTP/1.1" 200 159
************ - - [13/Aug/2025:16:56:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755075405328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:56:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755075405328 HTTP/1.1" 200 160
************ - - [13/Aug/2025:16:58:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755075525334 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:58:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755075525334 HTTP/1.1" 200 161
************ - - [13/Aug/2025:16:59:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755075585326 HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:59:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:59:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:59:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:16:59:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:16:59:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:16:59:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755075585326 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:16:59:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:17:01:08 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755075668327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:01:08 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755075668327 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:01:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755075668328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:01:08 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755075668327 HTTP/1.1" 200 148
************ - - [13/Aug/2025:17:01:08 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755075668327 HTTP/1.1" 200 152
************ - - [13/Aug/2025:17:01:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755075668328 HTTP/1.1" 200 159
************ - - [13/Aug/2025:17:01:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755075705427 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:01:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755075705427 HTTP/1.1" 200 160
************ - - [13/Aug/2025:17:03:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755075825335 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:03:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755075825335 HTTP/1.1" 200 161
************ - - [13/Aug/2025:17:04:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:04:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:04:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755075885324 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:04:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:04:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:17:04:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:17:04:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755075885324 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:17:04:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:17:06:09 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755075969320 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:06:09 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755075969320 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:06:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755075969322 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:06:09 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755075969320 HTTP/1.1" 200 152
************ - - [13/Aug/2025:17:06:09 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755075969320 HTTP/1.1" 200 148
************ - - [13/Aug/2025:17:06:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755075969322 HTTP/1.1" 200 159
************ - - [13/Aug/2025:17:06:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755076005320 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:06:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755076005320 HTTP/1.1" 200 160
************ - - [13/Aug/2025:17:08:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755076125437 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:08:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755076125437 HTTP/1.1" 200 161
************ - - [13/Aug/2025:17:09:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:09:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755076185451 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:09:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:09:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:09:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:17:09:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:17:09:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755076185451 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:17:09:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:17:11:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755076270326 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:11:10 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755076270325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:11:10 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755076270325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:11:10 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755076270325 HTTP/1.1" 200 148
************ - - [13/Aug/2025:17:11:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755076270326 HTTP/1.1" 200 159
************ - - [13/Aug/2025:17:11:10 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755076270325 HTTP/1.1" 200 152
************ - - [13/Aug/2025:17:11:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755076305330 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:11:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755076305330 HTTP/1.1" 200 160
************ - - [13/Aug/2025:17:13:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755076425323 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:13:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755076425323 HTTP/1.1" 200 161
************ - - [13/Aug/2025:17:13:51 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:13:51 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755076431492 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:13:51 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:13:51 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:13:51 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:17:13:52 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:17:13:52 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755076431492 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:17:13:52 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:17:15:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755076544727 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:15:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755076544727 HTTP/1.1" 200 160
************ - - [13/Aug/2025:17:16:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755076570367 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:16:10 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755076570380 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:16:10 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755076570380 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:16:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755076570367 HTTP/1.1" 200 159
************ - - [13/Aug/2025:17:16:10 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755076570380 HTTP/1.1" 200 148
************ - - [13/Aug/2025:17:16:10 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755076570380 HTTP/1.1" 200 152
************ - - [13/Aug/2025:17:18:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755076717328 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:18:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755076717328 HTTP/1.1" 200 161
************ - - [13/Aug/2025:17:18:38 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:18:38 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:18:38 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:18:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755076718325 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:18:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:17:18:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:17:18:39 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755076718325 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:17:18:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
************ - - [13/Aug/2025:17:20:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755076837884 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:20:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755076837884 HTTP/1.1" 200 160
************ - - [13/Aug/2025:17:21:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755076870410 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:21:10 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755076870422 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:21:10 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755076870422 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:21:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755076870410 HTTP/1.1" 200 159
************ - - [13/Aug/2025:17:21:10 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755076870422 HTTP/1.1" 200 148
************ - - [13/Aug/2025:17:21:10 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755076870422 HTTP/1.1" 200 152
************ - - [13/Aug/2025:17:23:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755077016740 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:23:36 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755077016740 HTTP/1.1" 200 161
************ - - [13/Aug/2025:17:23:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:23:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:23:36 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755077016960 HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:23:36 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [13/Aug/2025:17:23:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [13/Aug/2025:17:23:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [13/Aug/2025:17:23:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-13+08:00&etm=2025-08-13+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755077016960 HTTP/1.1" 200 442527
************ - - [13/Aug/2025:17:23:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54860
