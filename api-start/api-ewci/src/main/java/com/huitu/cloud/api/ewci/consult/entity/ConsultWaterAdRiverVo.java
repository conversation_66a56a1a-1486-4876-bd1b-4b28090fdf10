package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @description: 实时水情-水情统计-市县-河道实体
 * @author: jiangjy
 * @create: 2024-4-17
 **/
@ApiModel(value = "实时水情-水情统计-市县-河道实体")
public class ConsultWaterAdRiverVo {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "正常水位站数量")
    private Integer z;

    @ApiModelProperty(value = "超警戒水位站数量")
    private Integer zwrz;

    @ApiModelProperty(value = "超保证水位站数量")
    private Integer zgrz;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getZ() {
        return z;
    }

    public void setZ(Integer z) {
        this.z = z;
    }

    public Integer getZwrz() {
        return zwrz;
    }

    public void setZwrz(Integer zwrz) {
        this.zwrz = zwrz;
    }

    public Integer getZgrz() {
        return zgrz;
    }

    public void setZgrz(Integer zgrz) {
        this.zgrz = zgrz;
    }
}
