package com.huitu.cloud.api.hsfxt.xzhq.service.impl;

import com.huitu.cloud.api.hsfxt.city.entity.DicHongshuiliangji;
import com.huitu.cloud.api.hsfxt.river.entity.JiangHeXiangQing;
import com.huitu.cloud.api.hsfxt.river.entity.TunXiangQing;
import com.huitu.cloud.api.hsfxt.river.entity.ZhuanYiFangAn;
import com.huitu.cloud.api.hsfxt.xzhq.entity.*;
import com.huitu.cloud.api.hsfxt.xzhq.mapper.XzhqDao;
import com.huitu.cloud.api.hsfxt.xzhq.service.XzhqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.apache.commons.lang.StringUtils.lastIndexOf;

/**
 * <p>
 * 风险图-蓄滞洪区 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
@Service
public class XzhqServiceImpl implements XzhqService {
    @Autowired
    XzhqDao xzhqDao;

    @Override
    public List<XzhqTree> getXzhqTreeInfo(String key, String name, String hl, String bzqid) {
        List<XzhqTree> result=new ArrayList<>();
        Map<String,Object> param=new HashMap<>();
        param.put("level",6);
        //查询县集合
        List<XzhqTree> xianList=xzhqDao.getXzhqTreeInfo(param);
        param.put("level",9);
        //查询乡镇集合
        List<XzhqTree> zhenList=xzhqDao.getXzhqTreeInfo(param);
        //查询屯集合
        Map<String,Object> newParam=new HashMap<>();
        newParam.put("hl",hl);
        newParam.put("bzqid",bzqid);
        DicHongshuiliangji hongshuiliangji=xzhqDao.getXzhqParam(newParam);
        if(hongshuiliangji != null){
            Map<String,Object> newParam2=new HashMap<>();
            newParam2.put("param1","DDSJ_"+hongshuiliangji.getBeizhu());
            newParam2.put("param2","HSLS_"+hongshuiliangji.getBeizhu());
            newParam2.put("param3","YMSS_"+hongshuiliangji.getBeizhu());
            //修改in需要的参数
            String key2=getParam(key);
            newParam2.put("key",key2);
            newParam2.put("name",name);
            List<XzhqTree> tunList=xzhqDao.getTunXzhqTreeInfo(newParam2);
            //转移路线
            List<XzhqZhuanYiFangAn> list=xzhqDao.getXzhqZhuanYiFangAn(null,bzqid);
            Map<String,List<XzhqZhuanYiFangAn>> map=new HashMap<>();
            list.forEach(item->{
                if(map.containsKey(item.getZhuanyidanyuanbianma())){
                    List<XzhqZhuanYiFangAn> list2=map.get(item.getZhuanyidanyuanbianma());
                    list2.add(item);
                }else{
                    List<XzhqZhuanYiFangAn> list2=new ArrayList<>();
                    list2.add(item);
                    map.put(item.getZhuanyidanyuanbianma(),list2);
                }
            });
            tunList.forEach(item->{
                if (map.containsKey(item.getAdcd())){
                    item.setZyfa(map.get(item.getAdcd()));
                }
            });
            //三级集合合并
            List<XzhqTree> AllList=new ArrayList<>();
            //根据屯取乡镇和区县
            Map<String,XzhqTree> xianMap=new HashMap<>();
            Map<String,XzhqTree> zhenMap=new HashMap<>();
            xianList.forEach(item->{
                xianMap.put(item.getAdcd(),item);
            });
            zhenList.forEach(item->{
                zhenMap.put(item.getAdcd(),item);
            });
            List<XzhqTree> xianList2=new ArrayList<>();
            List<XzhqTree> zhenList2=new ArrayList<>();
            tunList.forEach(item->{
                if (zhenMap.containsKey(item.getPadcd())){
                    XzhqTree zhen=zhenMap.get(item.getPadcd());
                    //业务逻辑需要
                    String keyIndex=item.getKeyindex().substring(0,lastIndexOf(item.getKeyindex(), "_"));
                    zhen.setKeyindex(keyIndex+"_"+hl);
                    zhenList2.add(zhen);
                }
            });
            List<XzhqTree> zhenList3 =zhenList2.stream().distinct().collect(Collectors.toList());
            zhenList3.forEach(item->{
                if(xianMap.containsKey(item.getPadcd())){
                    XzhqTree xian=xianMap.get(item.getPadcd());
                    xian.setKeyindex(item.getKeyindex());
                    xianList2.add(xian);
                }
            });
            List<XzhqTree> xianList3=xianList2.stream().distinct().collect(Collectors.toList());
            AllList.addAll(zhenList3);
            AllList.addAll(xianList3);
            AllList.addAll(tunList);
            //查询子节点集合并赋值
            AllList.forEach(item->{
                List<XzhqTree> childList=getXzhqChildren(AllList,item.getAdcd());
                item.setChildren(childList);
                //乡镇加入影响屯总数
                if (!"000".equals(item.getAdcd().substring(7,9))){
                    item.setChildrenCount(childList.size());
                }
            });
            //获取根节点
            result=AllList.stream().filter(item->"0".equals(item.getPadcd())).collect(Collectors.toList());
            //加入根节点影响屯总数
            result.forEach(item->{
                int count=0;
                for(XzhqTree item2:item.getChildren()){
                    count=count+item2.getChildren().size();
                }
                item.setChildrenCount(count);
            });
        }
        return result;
    }

    @Override
    public List<XzhShehuijingji> getXzhqShjjTreeInfo(String key) {
        List<XzhShehuijingji> result=new ArrayList<>();
        //修改in需要的参数
        String key2=getParam(key);
        List<XzhShehuijingji> list= xzhqDao.getXzhqShjjTreeInfo(key2);
        //组织树形结构
        list.forEach(item->{
            List<XzhShehuijingji> childList=getXzhqShjjChildren(list,item.getSortid().toString());
            item.setChildren(childList);
        });
        result =list.stream().filter(item->"0".equals(item.getPid())).collect(Collectors.toList());
        return result;
    }

    @Override
    public XzhqXiangQing getXzhqShjjDetailInfo(String key, String adcd, String bzqid) {
        XzhqXiangQing result=new XzhqXiangQing();
        XzhqXiangQing xzhqXiangQing=xzhqDao.getXianQuXiangZhen(adcd,bzqid);
        if(xzhqXiangQing!=null){
            result.setAdcd(xzhqXiangQing.getAdcd());
            result.setAdnm(xzhqXiangQing.getAdnm());
            result.setMianji(xzhqXiangQing.getMianji());
            result.setFushu(xzhqXiangQing.getFushu());
            result.setRenkou(xzhqXiangQing.getRenkou());
            result.setFangwujianshu(xzhqXiangQing.getFangwujianshu());
            result.setGendimianji(xzhqXiangQing.getGendimianji());
            result.setGdp(xzhqXiangQing.getGdp());
        }
        XzhqXiangQing xzhqXiangQing2=xzhqDao.getZaiHaiSunShi(adcd,key);
        if(xzhqXiangQing2!=null){
            result.setYanmomianji(xzhqXiangQing2.getYanmomianji());
            result.setYanmogendimianji(xzhqXiangQing2.getYanmogendimianji());
            result.setYinxiangrenkou(xzhqXiangQing2.getYinxiangrenkou());
            result.setYinxianggdp(xzhqXiangQing2.getYinxianggdp());
            result.setYinxiangfanwushu(xzhqXiangQing2.getYinxiangfanwushu());
            result.setHongzaisunshi(xzhqXiangQing2.getHongzaisunshi());
        }
        return result;
    }

    @Override
    public XzhqTunXiangqing getXzhqTunShjjDetailInfo(String key, String hl, String adcd, String bzqid) {
        XzhqTunXiangqing result=null;
        Map<String,Object> newParam=new HashMap<>();
        newParam.put("hl",hl);
        newParam.put("bzqid",bzqid);
        DicHongshuiliangji hongshuiliangji=xzhqDao.getXzhqParam(newParam);
        if(hongshuiliangji != null){
            Map<String,Object> newParam2=new HashMap<>();
            newParam2.put("param1","DDSJ_"+hongshuiliangji.getBeizhu());
            newParam2.put("param2","HSLS_"+hongshuiliangji.getBeizhu());
            newParam2.put("param3","YMSS_"+hongshuiliangji.getBeizhu());
            newParam2.put("key",key);
            newParam2.put("adcd",adcd);
            result=xzhqDao.getXzhqTunShjjDetailInfo(newParam2);
            List<XzhqZhuanYiFangAn> list=xzhqDao.getXzhqZhuanYiFangAn(adcd,bzqid);
            if (result!=null){
                result.setZyfa(list);
            }
        }
        return result;
    }


    private List<XzhqTree> getXzhqChildren(List<XzhqTree> list, String pid) {
        // 通过父级编码子类
        List<XzhqTree> childList = list.stream().filter(x -> x.getPadcd().equals(pid)).collect(Collectors.toList());
        return childList;
    }

    private List<XzhShehuijingji> getXzhqShjjChildren(List<XzhShehuijingji> list, String pid) {
        // 通过父级编码子类
        List<XzhShehuijingji> childList = list.stream().filter(x -> x.getPid().equals(pid)).collect(Collectors.toList());
        return childList;
    }

    /**
     * 批量查询时传入字符串转换为sql底层in所需的条件转换
     *
     * @param str 传入字符串 例（1,2）
     * @return
     */
    private String getParam(String str){
        String[] s=str.split(",");
        String newStr="";
        for (int i=0;i<s.length;i++){
            newStr=newStr+"'"+s[i]+"',";
        }
        return newStr.substring(0,newStr.length()-1);
    }
}
