package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModelProperty;
/**
 * <AUTHOR>
 */
public class QueryWarnStatistics {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "政区编码",required = true)
    private String adcd;
    @ApiModelProperty(value = "是否只查询未结束预警('1':查询未关闭的)")
    private String queryType;

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }
}
