package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 重点城(集)镇调查评价涉水工程-桥梁详情列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@ApiModel(value="DcjzQlBridgeSs对象", description="重点城(集)镇调查评价涉水工程-桥梁详情列表")
public class DcjzQlBridgeSs extends Model<DcjzQlBridgeSs> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "桥梁名称")
    @TableId(value = "BRNAME", type = IdType.NONE)
    private String brname;

    @ApiModelProperty(value = "类型")
    @TableId(value = "TYPE", type = IdType.NONE)
    private String type;

    @ApiModelProperty(value = "桥高")
    @TableId(value = "HEIGHT", type = IdType.NONE)
    private String height;

    @ApiModelProperty(value = "涵桥长")
    @TableId(value = "LENGTH", type = IdType.NONE)
    private String length;

    @ApiModelProperty(value = "桥宽")
    @TableId(value = "WIDTH", type = IdType.NONE)
        private String width;

    @ApiModelProperty(value = "描述")
    @TableId(value = "COMMENTS", type = IdType.NONE)
    private String comments;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getBrname() {
        return brname;
    }

    public void setBrname(String brname) {
        this.brname = brname;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public String toString() {
        return "DcjzQlBridgeSs{" +
                "adnm='" + adnm + '\'' +
                ", brname='" + brname + '\'' +
                ", type='" + type + '\'' +
                ", height='" + height + '\'' +
                ", length='" + length + '\'' +
                ", width='" + width + '\'' +
                ", comments='" + comments + '\'' +
                '}';
    }
}
