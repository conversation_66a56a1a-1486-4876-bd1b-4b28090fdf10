package com.huitu.cloud.api.fusion.entity.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value = "山洪雨量升级预警底部统计响应对象")
@Data
public class AscriptionTypeCountResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "村屯个数")
    private Integer villageCount;

    @ApiModelProperty(value = "预警次数")
    private Integer totalWarningCount;

    @ApiModelProperty(value = "一级预警次数")
    private Integer level1Count;

    @ApiModelProperty(value = "二级预警次数")
    private Integer level2Count;
}