package com.huitu.cloud.api.common.word.service.impl;

import com.huitu.cloud.api.common.word.entity.ExportWordQo;
import com.huitu.cloud.api.common.word.service.WordService;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;

@Service
public class WordServiceImpl implements WordService {
    @Override
    public void exportNoTemplate(ExportWordQo exportWordQo, HttpServletResponse response) throws Exception {
        String[] split = exportWordQo.getText().split("\n");

        XWPFDocument doc = new XWPFDocument();
        //创建一个段落
        XWPFParagraph para = doc.createParagraph();
        //一个XWPFRun代表具有相同属性的一个区域：一段文本
        XWPFRun run = para.createRun();
        // 标题内容
        run.setText(exportWordQo.getTitle());
        run.setColor("FF0000");
        run.setFontSize(20);
        run.setBold(true);
        para.setAlignment(ParagraphAlignment.CENTER);

        for (String s : split) {
            XWPFParagraph para1 = doc.createParagraph();
            XWPFRun run1 = para1.createRun();
            run1.setText(s);
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        //把doc输出到输出流
        doc.write(baos);
        byte[] body_data = baos.toByteArray();
        baos.close();
        if (response==null) {
            response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        }
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        String encodedFileName = URLEncoder.encode("测试导出(无模板)", "utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName + ".docx");

        FileCopyUtils.copy(body_data, response.getOutputStream());
    }
}
