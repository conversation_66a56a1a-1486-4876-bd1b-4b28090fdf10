package com.huitu.cloud.api.syq.rsvr.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value="水库最大连续超汛限天数统计", description="水库最大连续超汛限天数统计")
public class RsvrContinueAlermVo implements Serializable {

    @ApiModelProperty(value = "排序")
    @ExcelProperty("序号")
    private Integer sortNo;

    @ApiModelProperty(value = "测站编码")
    @ExcelIgnore
    private String stcd;

    @ApiModelProperty(value = "政区名称")
    @ExcelIgnore
    private String adnm;

    @ApiModelProperty(value = "测站政区编码")
    @ExcelIgnore
    private String adcd;

    @ApiModelProperty(value = "区县政区编码")
    @ExcelIgnore
    private String xadcd;

    @ApiModelProperty(value = "区县政区名称")
    @ExcelProperty("政区名称")
    private String xadnm;

    @ApiModelProperty(value = "测站名称")
    @ExcelProperty("测站名称")
    private String stnm;

    @ApiModelProperty(value = "规模 1：小(二)型、2：小(一)型、3：中型、4：大(二)型、 5：大(一)型、 9：其它。")
    @ExcelIgnore
    private String rsvrtp;

    @ApiModelProperty(value = "规模 1：小(二)型、2：小(一)型、3：中型、4：大(二)型、 5：大(一)型、 9：其它。")
    @ExcelProperty("规模")
    private String rsvrtps;

    @ApiModelProperty(value = "是否是开敞式溢洪道")
    @ExcelIgnore
    private String osflg;

    @ApiModelProperty(value = "开敞式溢洪道")
    @ExcelProperty("开敞式溢洪道")
    private String osflgs;

    @ApiModelProperty(value = "测站归属类型")
    @ExcelIgnore
    private String stadtp;

    @ApiModelProperty(value = "超汛限天数")
    @ExcelProperty("超汛限天数")
    private Integer fzdays;

    @ApiModelProperty(value = "最大连续超汛限天数")
    @ExcelProperty("连续最大超汛限天数")
    private Integer contfzdays;

    @ApiModelProperty(value = "当前最大连续超汛限天数")
    @ExcelProperty("当前连续最大超汛限天数")
    private Integer currentContfzdays;

    @ApiModelProperty(value = "该站全天超汛限日期")
    @ExcelIgnore
    private String tm;

    @ApiModelProperty(value = "该站全天超汛限数据数量")
    @ExcelIgnore
    private Integer daytotal;

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getOsflg() {
        return osflg;
    }

    public void setOsflg(String osflg) {
        this.osflg = osflg;
    }

    public String getRsvrtp() {
        return rsvrtp;
    }

    public void setRsvrtp(String rsvrtp) {
        this.rsvrtp = rsvrtp;
    }

    public String getStadtp() {
        return stadtp;
    }

    public void setStadtp(String stadtp) {
        this.stadtp = stadtp;
    }

    public Integer getFzdays() {
        return fzdays;
    }

    public void setFzdays(Integer fzdays) {
        this.fzdays = fzdays;
    }

    public Integer getContfzdays() {
        return contfzdays;
    }

    public void setContfzdays(Integer contfzdays) {
        this.contfzdays = contfzdays;
    }

    public Integer getCurrentContfzdays() {
        return currentContfzdays;
    }

    public void setCurrentContfzdays(Integer currentContfzdays) {
        this.currentContfzdays = currentContfzdays;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public Integer getDaytotal() {
        return daytotal;
    }

    public void setDaytotal(Integer daytotal) {
        this.daytotal = daytotal;
    }

    public String getRsvrtps() {
        return rsvrtps;
    }

    public void setRsvrtps(String rsvrtps) {
        this.rsvrtps = rsvrtps;
    }

    public String getOsflgs() {
        return osflgs;
    }

    public void setOsflgs(String osflgs) {
        this.osflgs = osflgs;
    }
}
