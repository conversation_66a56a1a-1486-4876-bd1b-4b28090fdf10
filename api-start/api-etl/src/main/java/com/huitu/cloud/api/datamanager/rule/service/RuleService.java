package com.huitu.cloud.api.datamanager.rule.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.datamanager.rule.entity.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 雨量规则配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-06
 */
public interface RuleService {
    /**
     * 修改雨量规则
     * @param entity 雨量规则类
     * @return
     */
   boolean updateById(BsnRainruleb entity);
    /**
     * 新增雨量规则
     * @param entity 雨量规则类
     * @return
     */
   boolean save(BsnRainruleb entity);
    /**
     * 一键设置所有测站的雨量规则
     * @param entity
     * @return
     */
    boolean saveAll(RainruleEx entity);
    /**
     * 删除雨量规则
     * @param stcd 测站编码
     * @return
     */
   boolean del(String stcd);
    /**
     * 查询雨量规则
     * @param stnm 测站名称
     * @param adcd 政区编码
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return
     */
   IPage<RainRuleVo> getRainRule(String stnm, String adcd, int pageNum, int pageSize);

    /**
     * 修改水位规则
     * @param entity 雨量规则类
     * @return
     */
    boolean updateZrById(BsnZrruleb entity);
    /**
     * 新增水位规则
     * @param entity 雨量规则类
     * @return
     */
    boolean saveZr(BsnZrruleb entity);
    /**
     * 删除水位规则
     * @param stcd 测站编码
     * @return
     */
    boolean delZr(String stcd);

    /**
     * 查询水位规则
     * @param stnm 测站名称
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return
     */
    IPage<ZrruleVo> getZrRule(String sttp, String stnm, String adcd, int pageNum, int pageSize);

    /**
     * 导出雨量规则数据集
     * @param stnm 测站名称
     * @param response
     */
    void exportRainRule(String stnm, String adcd, HttpServletResponse response);

    /**
     * 导出水位规则
     * @param sttp 测站类型 RR 水库，ZZ河道
     * @param stnm
     * @param response
     */
    void exportZrRule(String sttp, String stnm, String adcd, HttpServletResponse response);

    /**
     * 校验测站是否已经添加了雨量规则
     * @param stcd 测站编码
     * @return
     */
    String checkRainRuleIsExist(String stcd);
    /**
     * 校验测站是否已经添加了雨量规则
     * @param stcd 测站编码
     * @return
     */
    String checkZrRuleIsExist(String stcd);
}
