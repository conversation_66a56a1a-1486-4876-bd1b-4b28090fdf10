package com.huitu.cloud.api.fusion.mapper;

import com.huitu.cloud.api.fusion.entity.ext.FusionWarningSummary;
import com.huitu.cloud.api.fusion.entity.response.FusionWarnMessageStatisticsResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface FusionStatisticsDao {

    /**
     * 获取山洪监测预警汇总统计信息
     *
     * @param params 查询参数
     * @return 汇总统计信息
     */
    FusionWarningSummary getShWarningSummary(@Param("map") Map<String, Object> params);

    /**
     * 获取水库监测预警汇总统计信息
     *
     * @param params 查询参数
     * @return 汇总统计信息
     */
    FusionWarningSummary getRsvrWarningSummary(@Param("map") Map<String, Object> params);

    /**
     * 获取河道监测预警汇总统计信息
     *
     * @param params 查询参数
     * @return 汇总统计信息
     */
    FusionWarningSummary getRiverWarningSummary(@Param("map") Map<String, Object> params);

    /**
     * 统计政区预警立即转移，准备转移的数量
     *
     * @param param
     * @return
     */
    List<FusionWarnMessageStatisticsResp> getAdWarnMessageStatistics(@Param("map") Map<String, Object> param);
}
