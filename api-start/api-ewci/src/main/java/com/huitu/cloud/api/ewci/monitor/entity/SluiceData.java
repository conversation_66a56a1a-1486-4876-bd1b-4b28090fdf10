package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 水闸
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@ApiModel(value = "水闸列表")
public class SluiceData implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "水闸索引")
    @TableField(value = "SPCD")
    private String spcd;

    @ApiModelProperty(value = "水闸编码")
    @TableField(value = "GATE_CODE")
    private String gateCode;

    @ApiModelProperty(value = "水闸名称")
    @TableField(value = "GATE_NAME")
    private String gateName;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "小流域代码")
    @TableField(value = "wscd")
    private String wscd;

    @ApiModelProperty(value = "小流域名称")
    @TableField(value = "WSNM")
    private String wsnm;

    @ApiModelProperty(value = "河流（湖泊）编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "水闸类型")
    @TableField(value = "GATE_TYPE")
    private String gateType;

    @ApiModelProperty(value = "闸孔数量(孔)")
    @TableField(value = "HOLE_NUM")
    private Integer holeNum;

    @ApiModelProperty(value = "闸孔总净宽(m)")
    @TableField(value = "HOLE_WID")
    private BigDecimal holeWid;

    @ApiModelProperty(value = "过闸流量(m3/s)")
    @TableField(value = "FL_GATE_FLOW")
    private BigDecimal flGateFlow;

    @ApiModelProperty(value = "橡胶坝坝高(m)")
    @TableField(value = "RUB_DAM_HIG")
    private BigDecimal rubDamHig;

    @ApiModelProperty(value = "橡胶坝坝长(m)")
    @TableField(value = "RUB_DAM_LEN")
    private BigDecimal rubDamLen;

    @ApiModelProperty(value = "经度（°）")
    @TableField(value = "LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度（°）")
    @TableField(value = "LTTD")
    private BigDecimal lttd;

    public String getSpcd() {
        return spcd;
    }

    public void setSpcd(String spcd) {
        this.spcd = spcd;
    }

    public String getGateCode() {
        return gateCode;
    }

    public void setGateCode(String gateCode) {
        this.gateCode = gateCode;
    }

    public String getGateName() {
        return gateName;
    }

    public void setGateName(String gateName) {
        this.gateName = gateName;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getGateType() {
        return gateType;
    }

    public void setGateType(String gateType) {
        this.gateType = gateType;
    }

    public Integer getHoleNum() {
        return holeNum;
    }

    public void setHoleNum(Integer holeNum) {
        this.holeNum = holeNum;
    }

    public BigDecimal getHoleWid() {
        return holeWid;
    }

    public void setHoleWid(BigDecimal holeWid) {
        this.holeWid = holeWid;
    }

    public BigDecimal getFlGateFlow() {
        return flGateFlow;
    }

    public void setFlGateFlow(BigDecimal flGateFlow) {
        this.flGateFlow = flGateFlow;
    }

    public BigDecimal getRubDamHig() {
        return rubDamHig;
    }

    public void setRubDamHig(BigDecimal rubDamHig) {
        this.rubDamHig = rubDamHig;
    }

    public BigDecimal getRubDamLen() {
        return rubDamLen;
    }

    public void setRubDamLen(BigDecimal rubDamLen) {
        this.rubDamLen = rubDamLen;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }
}
