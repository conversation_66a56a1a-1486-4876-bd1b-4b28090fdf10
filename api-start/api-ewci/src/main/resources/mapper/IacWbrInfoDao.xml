<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.device.mapper.IacWbrInfoDao">


    <select id="getIacWbrInfoList" resultType="com.huitu.cloud.api.ewci.device.entity.IacWbrInfo">
        select
            a.WBRCD,
            a.ADDRESS,
            a.WSCD,
            a.ADCD,
            a.TYPE,
            a.BDATE,
            a.LGTD,
            a.LTTD,
            a.SIGNER,
            a.AUDID,
            a.STATUS,
            a.COMMENTS,
            a.MODITIME,
            a.WBRNM,
            a.SIMID,
            a.ADMIN,
            a.ADMINSIM,
            a.DEVICEID,
            a.USFL,
            a.ZDBD,
            a.ZDAR,
            a.DCAR,
            a.SPDAR,
            a.AMPAR,
            a.LGTDAR,
            a.DEVICEMODITIME,
            a.POLEAR,
            a.SOURCEID,
            a.CREATOR,
            a.CREATED,
            a.AUDITED,
            a.AUDITOR,
            h.DEVICESTATUS,
            ad.adnm AS adnm1,
            ISNULL(ad2.adnm, '') AS adnm2,
            dbo.fnGetFullAdnm(A.ADCD, 5) AS adnm3
        FROM BNS_IA_C_WBRINFO a
        LEFT JOIN BSN_ADCD_B ad ON CONCAT (LEFT(A.ADCD, 6), '000000000') = ad.adcd
        LEFT JOIN BSN_ADCD_B ad2 ON SUBSTRING(A.ADCD, 7, 3) != '000' and CONCAT (LEFT(A.ADCD, 9), '000000') = ad2.adcd
        LEFT JOIN BNS_IA_C_WBRINFO_HISTORY h ON h.WBRCD = a.WBRCD and (h.devicestatus = '1' or h.devicestatus = '2')
        WHERE
            1=1
        <if test="map.adcd != null and map.adcd !=''">
            and left(a.adcd,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wbrnm !=null and map.wbrnm !=''">
            and A.wbrnm like '%${map.wbrnm}%'
        </if>
        <if test="map.adnm3 !=null and map.adnm3 !=''">
            and dbo.fnGetFullAdnm(A.ADCD, 5) like '%${map.adnm3}%'
        </if>
        <if test="map.usfl!=null and map.usfl!= ''">
            and a.usfl=#{map.usfl}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '1'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.ZDAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '1'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.ZDAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '2'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.DCAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '2'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.DCAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '3'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.SPDAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '3'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.SPDAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.videoType!= '' and map.wbrType == '4'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.AMPAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.videoType!= '' and map.wbrType == '4'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.AMPAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '5'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.LGTDAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '5'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.LGTDAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '6'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.POLEAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '6'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.POLEAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        ORDER BY
        a.DEVICEMODITIME DESC, a.USFL DESC
    </select>

    <select id="getIacWbrPendingReviewList" resultType="com.huitu.cloud.api.ewci.device.entity.IacWbrPPHistoryInfo">
        select
        a.ID,
        a.WBRCD,
        a.ADDRESS,
        a.WSCD,
        a.ADCD,
        a.TYPE,
        a.BDATE,
        a.LGTD,
        a.LTTD,
        a.SIGNER,
        a.AUDID,
        a.STATUS,
        a.COMMENTS,
        a.MODITIME,
        a.WBRNM,
        a.SIMID,
        a.ADMIN,
        a.ADMINSIM,
        a.DEVICEID,
        a.USFL,
        a.ZDBD,
        a.ZDAR,
        a.DCAR,
        a.SPDAR,
        a.AMPAR,
        a.LGTDAR,
        a.DEVICEMODITIME,
        a.POLEAR,
        a.SOURCEID,
        a.CREATOR,
        a.CREATED,
        a.AUDITED,
        a.AUDITOR,
        a.devicestatus,
        a.audreason,
        ad.adnm AS adnm1,
        ISNULL(ad2.adnm, '') AS adnm2,
        dbo.fnGetFullAdnm(A.ADCD, 5) AS adnm3
        from BNS_IA_C_WBRINFO_HISTORY a
        LEFT JOIN BSN_ADCD_B ad ON CONCAT (LEFT(A.ADCD, 6), '000000000') = ad.adcd
        LEFT JOIN BSN_ADCD_B ad2 ON SUBSTRING(A.ADCD, 7, 3) != '000' and CONCAT (LEFT(A.ADCD, 9), '000000') = ad2.adcd
        WHERE
        1=1
        <if test="map.devicestatus !=null and map.devicestatus !=''">
            and CHARINDEX(a.devicestatus,#{map.devicestatus}) > 0
        </if>
        <if test="map.adcd != null and map.adcd !=''">
            and left(a.adcd,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wbrnm !=null and map.wbrnm !=''">
            and A.wbrnm like '%${map.wbrnm}%'
        </if>
        <if test="map.adnm3 !=null and map.adnm3 !=''">
            and dbo.fnGetFullAdnm(A.ADCD, 5) like '%${map.adnm3}%'
        </if>
        <if test="map.usfl!=null and map.usfl!= ''">
            and a.usfl=#{map.usfl}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '1'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.ZDAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '1'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.ZDAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '2'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.DCAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '2'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.DCAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '3'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.SPDAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '3'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.SPDAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.videoType!= '' and map.wbrType == '4'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.AMPAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.videoType!= '' and map.wbrType == '4'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.AMPAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '5'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.LGTDAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '5'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.LGTDAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '6'.toString() and map.startYear!=null and map.startYear!= ''">
            AND A.POLEAR &gt;= #{map.startYear}
        </if>
        <if test="map.wbrType!=null and map.wbrType!= '' and map.wbrType == '6'.toString() and map.endYear != null and map.endYear!= '' ">
            and A.POLEAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        ORDER BY
        a.DEVICEMODITIME DESC
    </select>
    <select id="getIacWbrAuditList" resultType="com.huitu.cloud.api.ewci.device.entity.IacWbrInfoHistory">
        select
            '0' as id,
            ISNULL(ad.adnm, '') + ISNULL(ad2.adnm, '') + ISNULL(dbo.fnGetFullAdnm (b.ADCD, 5), '') as adnm,
            b.WBRCD,
            b.ADDRESS,
            b.WSCD,
            b.ADCD,
            b.TYPE,
            b.BDATE,
            b.LGTD,
            b.LTTD,
            b.SIGNER,
            b.AUDID,
            b.STATUS,
            b.COMMENTS,
            b.MODITIME,
            b.WBRNM,
            b.SIMID,
            b.ADMIN,
            b.ADMINSIM,
            b.DEVICEID,
            b.USFL,
            b.ZDBD,
            b.ZDAR,
            b.DCAR,
            b.SPDAR,
            b.AMPAR,
            b.LGTDAR,
            b.DEVICEMODITIME,
            b.POLEAR,
            b.SOURCEID,
            b.CREATOR,
            b.CREATED,
            b.AUDITED,
            b.AUDITOR,
            '' as AUDREASON,
            '' as DEVICESTATUS
        from BNS_IA_C_WBRINFO b
                 LEFT JOIN BSN_ADCD_B ad ON CONCAT (LEFT(b.ADCD, 6), '000000000') = ad.adcd
                 LEFT JOIN BSN_ADCD_B ad2 ON SUBSTRING(b.ADCD, 7, 3) != '000' AND CONCAT (LEFT(b.ADCD, 9), '000000') = ad2.adcd
        where b.WBRCD = #{wbrcd}  UNION all select
                                                            a.ID,
                                                            ISNULL(ad.adnm, '') + ISNULL(ad2.adnm, '') + ISNULL(dbo.fnGetFullAdnm (a.ADCD, 5), '') as adnm,
                                                            a.WBRCD,
                                                            a.ADDRESS,
                                                            a.WSCD,
                                                            a.ADCD,
                                                            a.TYPE,
                                                            a.BDATE,
                                                            a.LGTD,
                                                            a.LTTD,
                                                            a.SIGNER,
                                                            a.AUDID,
                                                            a.STATUS,
                                                            a.COMMENTS,
                                                            a.MODITIME,
                                                            a.WBRNM,
                                                            a.SIMID,
                                                            a.ADMIN,
                                                            a.ADMINSIM,
                                                            a.DEVICEID,
                                                            a.USFL,
                                                            a.ZDBD,
                                                            a.ZDAR,
                                                            a.DCAR,
                                                            a.SPDAR,
                                                            a.AMPAR,
                                                            a.LGTDAR,
                                                            a.DEVICEMODITIME,
                                                            a.POLEAR,
                                                            a.SOURCEID,
                                                            a.CREATOR,
                                                            a.CREATED,
                                                            a.AUDITED,
                                                            a.AUDITOR,
                                                            a.AUDREASON,
                                                            a.DEVICESTATUS
        from BNS_IA_C_WBRINFO_HISTORY a
                 LEFT JOIN BSN_ADCD_B ad ON CONCAT (LEFT(a.ADCD, 6), '000000000') = ad.adcd
                 LEFT JOIN BSN_ADCD_B ad2 ON SUBSTRING(a.ADCD, 7, 3) != '000' AND CONCAT (LEFT(a.ADCD, 9), '000000') = ad2.adcd

                                            where a.WBRCD = #{wbrcd}  and a.devicestatus = '1'
    </select>
    <select id="getIacWbrHistoryList" resultType="com.huitu.cloud.api.ewci.device.entity.IacWbrInfoHistory">
        select res.* from
            (
                select
                    '0' as id,
                    b.WBRCD,
                    b.ADDRESS,
                    b.WSCD,
                    b.ADCD,
                    b.TYPE,
                    b.BDATE,
                    b.LGTD,
                    b.LTTD,
                    b.SIGNER,
                    b.AUDID,
                    b.STATUS,
                    b.COMMENTS,
                    b.MODITIME,
                    b.WBRNM,
                    b.SIMID,
                    b.ADMIN,
                    b.ADMINSIM,
                    b.DEVICEID,
                    b.USFL,
                    b.ZDBD,
                    b.ZDAR,
                    b.DCAR,
                    b.SPDAR,
                    b.AMPAR,
                    b.LGTDAR,
                    b.DEVICEMODITIME,
                    b.POLEAR,
                    b.SOURCEID,
                    b.CREATOR,
                    b.CREATED,
                    b.AUDITED,
                    b.AUDITOR,
                    '' as AUDREASON,
                    '' as DEVICESTATUS,
                    ad.adnm AS adnm1,
                    ISNULL(ad2.adnm, '') AS adnm2,
                    dbo.fnGetFullAdnm(B.ADCD, 5) AS adnm3
                from BNS_IA_C_WBRINFO b
                         LEFT JOIN BSN_ADCD_B ad ON CONCAT (LEFT(b.ADCD, 6), '000000000') = ad.adcd
                         LEFT JOIN BSN_ADCD_B ad2 ON SUBSTRING(b.ADCD, 7, 3) != '000' and CONCAT (LEFT(b.ADCD, 9), '000000') = ad2.adcd
                where b.WBRCD = #{wbrcd}
                UNION all
                select
                    a.ID,
                    a.WBRCD,
                    a.ADDRESS,
                    a.WSCD,
                    a.ADCD,
                    a.TYPE,
                    a.BDATE,
                    a.LGTD,
                    a.LTTD,
                    a.SIGNER,
                    a.AUDID,
                    a.STATUS,
                    a.COMMENTS,
                    a.MODITIME,
                    a.WBRNM,
                    a.SIMID,
                    a.ADMIN,
                    a.ADMINSIM,
                    a.DEVICEID,
                    a.USFL,
                    a.ZDBD,
                    a.ZDAR,
                    a.DCAR,
                    a.SPDAR,
                    a.AMPAR,
                    a.LGTDAR,
                    a.DEVICEMODITIME,
                    a.POLEAR,
                    a.SOURCEID,
                    a.CREATOR,
                    a.CREATED,
                    a.AUDITED,
                    a.AUDITOR,
                    a.AUDREASON,
                    a.DEVICESTATUS,
                    ad.adnm AS adnm1,
                    ISNULL(ad2.adnm, '') AS adnm2,
                    dbo.fnGetFullAdnm(A.ADCD, 5) AS adnm3
                from BNS_IA_C_WBRINFO_HISTORY a
                    LEFT JOIN BSN_ADCD_B ad ON CONCAT (LEFT(a.ADCD, 6), '000000000') = ad.adcd
                    LEFT JOIN BSN_ADCD_B ad2 ON SUBSTRING(a.ADCD, 7, 3) != '000' and CONCAT (LEFT(a.ADCD, 9), '000000') = ad2.adcd
                where a.WBRCD = #{wbrcd}  and a.devicestatus = '0'
            ) res order by res.DEVICEMODITIME desc
    </select>

    <!--    无线预警广播站信息统计-->
    <select id="getWbrCountList" resultType="com.huitu.cloud.api.ewci.device.entity.DeviceStatistics">
        SELECT
        AA.ADCD,
        AA.ADNM,
        COUNT ( BB.ADCD ) AS COUNT
        FROM
        ( SELECT A.ADCD, A.ADNM, A.padcd, A.adlvl FROM BSN_ADCD_B A WHERE
        LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        AND CHARINDEX(CAST(A.adlvl AS VARCHAR), #{map.adlvl}) > 0
        <if test="map.level !=null and map.level =='4'.toString()">
            and left(A.ADCD,6) != '220581'
        </if>
        ) AA
        LEFT JOIN (
        SELECT
        <if test="map.level !=null and map.level =='2'.toString()">
            CONCAT ( LEFT ( A.ADCD, 4 ), '00000000000' ) AS adcd
        </if>
        <if test="map.level !=null and map.level !='2'.toString()">
            CONCAT ( LEFT ( A.ADCD, 6 ), '000000000' ) AS adcd
        </if>
        from BNS_IA_C_WBRINFO a
        LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = a.adcd
        LEFT JOIN BNS_IA_C_WBRINFO_HISTORY h ON h.WBRCD = a.WBRCD and (h.devicestatus = '1' or h.devicestatus = '2')
        WHERE
        A.ADCD NOT LIKE '220581%'
        UNION ALL
        SELECT
        CONCAT ( LEFT ( A.ADCD, 6 ), '000000000' ) AS adcd
        from BNS_IA_C_WBRINFO a
        LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = a.adcd
        LEFT JOIN BNS_IA_C_WBRINFO_HISTORY h ON h.WBRCD = a.WBRCD and (h.devicestatus = '1' or h.devicestatus = '2')
        WHERE
        A.ADCD LIKE '220581%'
        ) BB ON
        LEFT( AA.ADCD, 6) = LEFT ( BB.ADCD, 6 )
        GROUP BY
        AA.ADCD,
        AA.ADNM
        ORDER BY
        AA.ADCD
    </select>

    <delete id="deleteId">
        delete
        from BNS_IA_C_WBRINFO_HISTORY
        where ID = #{id}
    </delete>

</mapper>
