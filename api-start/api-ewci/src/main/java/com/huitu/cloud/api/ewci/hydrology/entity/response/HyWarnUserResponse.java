package com.huitu.cloud.api.ewci.hydrology.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 洪水预警人员响应对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警人员响应对象")
public class HyWarnUserResponse implements Serializable {
    private static final long serialVersionUID = 3683377315008227531L;

    @ApiModelProperty(value = "用户ID")
    @TableField(value = "USERID")
    private String userid;

    @ApiModelProperty(value = "真实姓名")
    @TableField(value = "REALNM")
    private String realnm;

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getRealnm() {
        return realnm;
    }

    public void setRealnm(String realnm) {
        this.realnm = realnm;
    }
}
