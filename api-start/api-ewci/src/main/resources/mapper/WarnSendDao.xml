<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.WarnSendDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="selectMaxMsgId" resultType="java.lang.Integer">
        select isnull(MAX(MSGID), 0) + 1
        from MESSAGEINFO_R
        where adcd = #{adcd}
    </select>

    <select id="selectMaxSmsId" resultType="java.lang.Integer">
        select isnull(MAX(SMSID), 0) + 1
        from MESSAGESEND_R
        where code = #{year}
          and adcd = #{adcd}
    </select>

    <select id="getReleaserById" resultType="com.huitu.cloud.api.ewci.warn.entity.ReleaserVo">
        SELECT DISTINCT A.<PERSON>ERID, A.<PERSON>, C.adcd
        FROM BNS_USERLOGUSER_B A
                 LEFT JOIN BSN_UGRELATION B ON B.UserID = A.userid
                 LEFT JOIN BNS_DEPTINFO_B C ON C.deptid = B.deptid
        WHERE A.USERID = #{releaser}
    </select>

    <select id="getPersonLiableList" resultType="com.huitu.cloud.api.ewci.warn.entity.PersonLiableVo">
        SELECT DISTINCT PHONE, NAME
        FROM (select DISTINCT A.MOBILE PHONE, A.REALNM NAME
              FROM BSN_SH_Person_B A
              WHERE A.adcd IN
                    (SELECT DISTINCT
                  LEFT (LEFT (OBJECT_ID
                  , 12)+'000000000000000'
                  , 15)
              from BNS_WARN_OBJECT
              where OBJECT_TYPE = '0' and WARN_ID = #{warnId})
        UNION ALL
        select DISTINCT A.MOBILE PHONE, A.REALNM NAME
        FROM BNS_RSVRPERSON_B A
        WHERE A.RES_CODE IN
              (SELECT DISTINCT OBJECT_ID from BNS_WARN_OBJECT where OBJECT_TYPE = '1' and WARN_ID = #{warnId}) )A
        WHERE A.PHONE IS NOT NULL
    </select>


    <insert id="insertMessageInfoR">
        INSERT INTO MESSAGEINFO_R (MSGID, WARNID, MSGTYPEID, MSGCONTENT, SENDER, MEDIAID, SENDTM, REMARK, SENDTYPE,
                                   ADCD)
        VALUES (#{msgId}, #{warnId}, #{msgTypeId}, #{msgContent}, #{sender}, #{mediaId}, #{sendTm}, #{remark},
                #{sendType}, #{adcd})
    </insert>

    <insert id="insertMessageSendRList">
        INSERT INTO MESSAGESEND_R (MSGID, CODE, SMSID, SID,
        UNAME,OBJECTCD,OBJECTTYPE,SENDRESULT,RECTM,REMARK,ADCD,ISSEND)
        SELECT T.MSGID, T.CODE, T.SMSID, T.SID,
        T.UNAME,T.OBJECTCD,T.OBJECTTYPE,T.SENDRESULT,T.RECTM,T.REMARK,T.ADCD,T.ISSEND
        FROM (VALUES
        <foreach collection="map.list" item="item" separator=",">
            (#{item.msgId},
            #{item.code},#{item.smsId},#{item.sid},#{item.uname},#{item.objectcd},#{item.objectType},#{item.sendResult},
            #{item.rectm},#{item.remark},#{item.adcd},#{item.isSend})
        </foreach>
        ) AS T(MSGID, CODE, SMSID, SID, UNAME,OBJECTCD,OBJECTTYPE,SENDRESULT,RECTM,REMARK,ADCD,ISSEND)
    </insert>

    <select id="getUserIdBywarnId" resultType="java.lang.String">
        WITH DEPT AS (SELECT DEPTID, DEPTNM, PDEPTID
                      FROM BNS_DEPTINFO_B
                      WHERE DEPTID = '332d8816-11f4-49c0-8cda-1e9ed9c5abe5' -- 地方水旱组织ID
                      UNION ALL
                      SELECT T.DEPTID, T.DEPTNM, T.PDEPTID
                      FROM BNS_DEPTINFO_B T
                               INNER JOIN DEPT D ON T.PDEPTID = D.DEPTID)
        SELECT DISTINCT A.USERID
        FROM BSN_UGRELATION A
                 LEFT JOIN BNS_USERLOGUSER_B B ON A.USERID = B.USERID AND B.USEFLG = '1'
        WHERE A.DEPTID IN
              (SELECT DEPTID
               FROM BNS_DEPTINFO_B A
               WHERE EXISTS(SELECT 8 FROM BNS_WARN_OBJECT WHERE LEFT (OBJECT_ID, 6) = LEFT (A.ADCD, 6)
                   AND OBJECT_TYPE = '0'
                   AND WARN_ID = #{warnId})
                 AND EXISTS(SELECT 8 FROM DEPT WHERE DEPTID = A.DEPTID)
               UNION ALL
               SELECT DEPTID
               FROM BNS_DEPTINFO_B A
               WHERE EXISTS(SELECT 8
                            FROM REL_RES_AD R
                                     LEFT JOIN BNS_WARN_OBJECT B ON R.RES_CODE = B.OBJECT_ID
                                AND B.OBJECT_TYPE = '1'
                            WHERE B.WARN_ID = #{warnId} AND LEFT (R.AD_CODE, 6) = LEFT (A.ADCD, 6))
                 AND EXISTS(SELECT 8 FROM DEPT WHERE DEPTID = A.DEPTID))
    </select>

    <insert id="insertBnsWarnMessageList">
        INSERT INTO BNS_WARN_MESSAGE (WARN_ID, RECEIVER, STATUS, SEND_TIME)
        SELECT T.WARN_ID, T.RECEIVER, T.STATUS, T.SEND_TIME
        FROM (VALUES
        <foreach collection="map.list" item="item" separator=",">
            (#{item.warnId}, #{item.receiver},#{item.status},#{item.sendTime})
        </foreach>
        ) AS T(WARN_ID, RECEIVER, STATUS, SEND_TIME)
    </insert>

    <select id="getWarnMessageAppList" resultType="com.huitu.cloud.api.ewci.warn.entity.WarnMessageAppVo">
        SELECT B.WARN_ID, B.WARN_NAME, C.LOGINNM AUDITOR, B.AUDIT_TIME
        FROM BNS_WARN_MESSAGE A
        LEFT JOIN BNS_WARN_INFO B ON A.WARN_ID = B.WARN_ID
        LEFT JOIN BNS_USERLOGUSER_B C ON B.AUDITOR = C.userid
        WHERE A.RECEIVER = #{map.receiver}
        <if test="map.status == '0'.toString()">
            AND A.STATUS = '0'
        </if>
        <if test="map.status == '1'.toString()">
            AND A.STATUS = '1'
            <if test="map.stm != null and map.stm !=''">
                and SEND_TIME>CONVERT(datetime,#{map.stm})
            </if>
            <if test="map.etm != null and map.etm !=''">
                and SEND_TIME &lt;= CONVERT(datetime,#{map.etm})
            </if>
        </if>
        <if test="map.status == '2'.toString()">
            AND A.STATUS = '0'
            UNION ALL
            SELECT B.WARN_ID,B.WARN_NAME,C.LOGINNM,B.AUDIT_TIME FROM BNS_WARN_MESSAGE A LEFT JOIN BNS_WARN_INFO B ON
            A.WARN_ID = B.WARN_ID LEFT JOIN BNS_USERLOGUSER_B C ON B.AUDITOR = C.userid WHERE A.RECEIVER =
            #{map.receiver} AND
            A.STATUS='1'
            <if test="map.stm != null and map.stm !=''">
                and A.SEND_TIME > #{map.stm}
            </if>
            <if test="map.etm != null and map.etm !=''">
                and A.SEND_TIME &lt;= #{map.etm}
            </if>
        </if>
    </select>
    <select id="getAppWarnInfoByWarnId" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnInfo">
        SELECT C.ADNM,
               B.TYPE_NAME,
               A.WARN_GRADE,
               A.WARN_NAME,
               A.WARN_CONTENT,
               A.RELEASE_TIME,
               A.AUDIT_TIME,
               D.LOGINNM RELEASER_NAME,
               E.LOGINNM AUDITOR_NAME
        FROM BNS_WARN_INFO A
                 LEFT JOIN BNS_WARN_TYPE_SETTINGS B ON A.WARN_TYPE = B.ID
                 LEFT JOIN MDT_ADCDINFO_B C ON A.ADCD = C.ADCD
                 LEFT JOIN BNS_USERLOGUSER_B D ON A.RELEASER = D.USERID
                 LEFT JOIN BNS_USERLOGUSER_B E ON A.AUDITOR = E.USERID
        WHERE A.WARN_ID = #{warnId}
    </select>

    <update id="updateWarnMessageStatus">
        UPDATE BNS_WARN_MESSAGE
        SET STATUS    = '1',
            READ_TIME = GETDATE()
        WHERE WARN_ID = #{map.warnId}
          AND RECEIVER = #{map.receiver}
          AND STATUS = '0'
    </update>
</mapper>