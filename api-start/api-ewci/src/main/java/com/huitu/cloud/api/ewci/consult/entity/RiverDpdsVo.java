package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;

import java.io.Serializable;

/**
 * 提防统计 险工险段
 */
@ApiModel(value = "提防统计险工险段")
public class RiverDpdsVo implements Serializable {


    @ApiModelProperty(value = "险工险段编码")
    @TableField(value = "DPDS_CODE")
    private String dpdsCode;

    @ApiModelProperty(value = "政区编码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "位置")
    @TableField(value = "DPDS_SHORT_NAME")
    private String dpdsShortName;

    @ApiModelProperty(value = "长度")
    @TableField(value = "DPDS_LEN")
    private Double dpdsLen;

    @ApiModelProperty(value = "河流编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "河流名称")
    @TableField(value = "RV_NAME")
    private String rvName;

    @ApiModelProperty(value = "数量")
    @TableField(value = "TOTAL")
    private Integer total;

    public String getDpdsCode() {
        return dpdsCode;
    }

    public void setDpdsCode(String dpdsCode) {
        this.dpdsCode = dpdsCode;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getDpdsShortName() {
        return dpdsShortName;
    }

    public void setDpdsShortName(String dpdsShortName) {
        this.dpdsShortName = dpdsShortName;
    }

    public Double getDpdsLen() {
        return dpdsLen;
    }

    public void setDpdsLen(Double dpdsLen) {
        this.dpdsLen = dpdsLen;
    }

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
