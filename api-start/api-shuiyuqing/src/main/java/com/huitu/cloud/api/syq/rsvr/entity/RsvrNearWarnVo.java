package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2019-09-11
 */
public class RsvrNearWarnVo extends Model<RsvrNearWarnVo> {

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;

    @ApiModelProperty(value = "行政区划码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区名称")
    @TableField("ADNM")
    private String adnm;

    @ApiModelProperty(value = "县级政区编码")
    @TableField("XADCD")
    private String xadcd;

    @ApiModelProperty(value = "县级政区名称")
    @TableField("XADNM")
    private String xadnm;

    @ApiModelProperty(value = "站类")
    @TableField("RSVRTP")
    private String rsvrtp;

    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "水位")
    @TableField("RZ")
    private BigDecimal rz;

    @ApiModelProperty(value = "水势")
    @TableField("RWPTN")
    private String rwptn;

    @ApiModelProperty(value = "超临近设计水位")
    @TableField("ENDSFLZ")
    private BigDecimal endsflz;

    @ApiModelProperty(value = "距设计水位")
    @TableField("DDSFLZ")
    private BigDecimal ddsflz;

    @ApiModelProperty(value = "超临近正常高水位")
    @TableField("ennormz")
    private BigDecimal ennormz;

    @ApiModelProperty(value = "距正常高水位")
    @TableField("DNORMZ")
    private BigDecimal dnormz;

    @ApiModelProperty(value = "水位变幅")
    @TableField("CHV")
    private BigDecimal chv;

    @ApiModelProperty(value = "超水位变幅")
    @TableField("ECHV")
    private BigDecimal echv;

    @ApiModelProperty(value = "汛限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @ApiModelProperty(value = "设计水位")
    @TableField("DSFLZ")
    private BigDecimal dsflz;

    @ApiModelProperty(value = "正常高水位")
    @TableField("NORMZ")
    private BigDecimal normz;

    @ApiModelProperty(value = "超汛限水位")
    @TableField("RZFSLTDZ")
    private BigDecimal rzfsltdz;

    @ApiModelProperty(value = "超设计水位")
    @TableField("RZDSLFZ")
    private BigDecimal rzdsflz;

    @ApiModelProperty(value = "超正常高水位")
    @TableField("RZNORMZ")
    private BigDecimal rznormz;

    @ApiModelProperty(value = "偏移经度")
    @TableField("PLGTD")
    private BigDecimal plgtd;

    @ApiModelProperty(value = "偏移纬度")
    @TableField("PLTTD")
    private BigDecimal plttd;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getRsvrtp() {
        return rsvrtp;
    }

    public void setRsvrtp(String rsvrtp) {
        this.rsvrtp = rsvrtp;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public BigDecimal getRz() {
        return rz;
    }

    public void setRz(BigDecimal rz) {
        this.rz = rz;
    }

    public String getRwptn() {
        return rwptn;
    }

    public void setRwptn(String rwptn) {
        this.rwptn = rwptn;
    }

    public BigDecimal getEndsflz() {
        return endsflz;
    }

    public void setEndsflz(BigDecimal endsflz) {
        this.endsflz = endsflz;
    }

    public BigDecimal getDdsflz() {
        return ddsflz;
    }

    public void setDdsflz(BigDecimal ddsflz) {
        this.ddsflz = ddsflz;
    }

    public BigDecimal getEnnormz() {
        return ennormz;
    }

    public void setEnnormz(BigDecimal ennormz) {
        this.ennormz = ennormz;
    }

    public BigDecimal getDnormz() {
        return dnormz;
    }

    public void setDnormz(BigDecimal dnormz) {
        this.dnormz = dnormz;
    }

    public BigDecimal getChv() {
        return chv;
    }

    public void setChv(BigDecimal chv) {
        this.chv = chv;
    }

    public BigDecimal getEchv() {
        return echv;
    }

    public void setEchv(BigDecimal echv) {
        this.echv = echv;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public BigDecimal getDsflz() {
        return dsflz;
    }

    public void setDsflz(BigDecimal dsflz) {
        this.dsflz = dsflz;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public BigDecimal getRzdsflz() {
        return rzdsflz;
    }

    public void setRzdsflz(BigDecimal rzdsflz) {
        this.rzdsflz = rzdsflz;
    }

    public BigDecimal getRznormz() {
        return rznormz;
    }

    public void setRznormz(BigDecimal rznormz) {
        this.rznormz = rznormz;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

}
