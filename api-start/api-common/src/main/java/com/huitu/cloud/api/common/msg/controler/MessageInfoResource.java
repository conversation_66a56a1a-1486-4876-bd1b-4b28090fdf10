package com.huitu.cloud.api.common.msg.controler;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.common.msg.entity.BnsMessageR;
import com.huitu.cloud.api.common.msg.entity.FailMessage;
import com.huitu.cloud.api.common.msg.entity.MessageCommonQo;
import com.huitu.cloud.api.common.msg.entity.MessageList;
import com.huitu.cloud.api.common.msg.entity.MessageStatistics;
import com.huitu.cloud.api.common.msg.entity.MessageVo;
import com.huitu.cloud.api.common.msg.entity.MsgBean;
import com.huitu.cloud.api.common.msg.entity.MsgSucess;
import com.huitu.cloud.api.common.msg.entity.QueryMessageInfoList;
import com.huitu.cloud.api.common.msg.entity.QueryMessageStatistics;
import com.huitu.cloud.api.common.msg.entity.QueryUpdateFailMessage;
import com.huitu.cloud.api.common.msg.entity.QueryWarnMessage;
import com.huitu.cloud.api.common.msg.entity.WarnMessage;
import com.huitu.cloud.api.common.msg.service.MessageInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <p>
 * 预警消息记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */

@RestController
@Api(tags = "短息发送查询接口")
@RequestMapping("/api/common/msg/")
public class MessageInfoResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "b3bedd12-cfb4-4656-92c2-a6e353ca3d1e";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private MessageInfoService baseService;

    @ApiOperation(value = "短息发送接口", notes = "用于预警以及提示信息的短信发送")
    @PostMapping(value = "send")
    public ResponseEntity<SuccessResponse<MsgSucess>> sendMsg(@RequestBody MsgBean entity) throws Exception {
        MsgSucess msgSucess = baseService.sendMsg(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", msgSucess));
    }

    @ApiOperation(value = "通用信息发送接口", notes = "通用信息发送")
    @PostMapping(value = "send-common-message", produces = "application/json;charset=UTF-8")
    public ResponseEntity<SuccessResponse<Integer>> sendCommonMsg(@RequestBody List<BnsMessageR> entities) throws Exception {
        AtomicReference<Integer> result = new AtomicReference<>(0);
        entities.forEach((entity) -> {
            try {
                result.updateAndGet(v -> v + baseService.sendCommonMsg(entity));
            } catch (Exception ex) {
            }
        });
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result.get()));
    }

    @ApiOperation(value = "查询通用消息接口", notes = "用于查询通用消息记录")
    @PostMapping(value = "get-common-message")
    public ResponseEntity<SuccessResponse<MessageVo>> getCommonMsg(@RequestBody MessageCommonQo messageCommonQo) throws Exception {
        List<BnsMessageR> list = baseService.getCommonMsg(messageCommonQo.getId(), messageCommonQo.getType(), messageCommonQo.getAdcd(), messageCommonQo.getDeptid(), messageCommonQo.getStm(), messageCommonQo.getEtm(), messageCommonQo.getFlag());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "批量修改消息处理状态", notes = "批量修改消息处理状态")
    @PostMapping(value = "update-common-message-batches")
    public ResponseEntity<SuccessResponse<String>> updateCommonMsgBatches(@RequestBody List<String> ids) throws Exception {
        boolean flag = baseService.updateCommonMsgBatches(ids);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }

    @ApiOperation(value = "预警发送记录查询接口", notes = "用于查询预警的发送记录")
    @GetMapping(value = "get-by-warnId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "warnId", value = "预警编码id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "发送状态", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<MessageVo>> getMsg(@RequestParam String warnId, @RequestParam String status) throws Exception {
        List<MessageVo> list = baseService.getMsgByWarnId(warnId, status);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "预警短信政区统计", notes = "预警短信政区统计")
    @PostMapping(value = "get-warn-message-statistics")
    public ResponseEntity<SuccessResponse<List<MessageStatistics>>> getMsgStatistics(@RequestBody QueryMessageStatistics messageStatistics) throws Exception {
        List<MessageStatistics> list = baseService.getMsgStatistics(messageStatistics.getAdcd(), messageStatistics.getEtm(), messageStatistics.getStm());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "预警消息详情列表", notes = "分页查询预警消息详情列表")
    @PostMapping(value = "get-warn-message-by-query")
    public ResponseEntity<SuccessResponse<Page<MessageVo>>> getWarnMsgInfoByPage(@Validated @RequestBody QueryMessageInfoList queryMessage) throws Exception {
        IPage<MessageVo> list = baseService.getWarnMsgInfoByPage(queryMessage.getAdcd(), queryMessage.getEtm(), queryMessage.getMediaId(), queryMessage.getMsgTypeId(), queryMessage.getWarnId(), queryMessage.getStatus(), queryMessage.getStm(), queryMessage.getMsgId(), queryMessage.getPageNum(), queryMessage.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "预警短信发送记录列表", notes = "分页获取预警短信发送记录列表")
    @PostMapping(value = "select-warn-message-page")
    public ResponseEntity<SuccessResponse<Page<WarnMessage>>> getWarnMessagePage(@Validated @RequestBody QueryWarnMessage query) throws Exception {
        IPage<WarnMessage> list = baseService.getWarnMessagePage(query);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询短信信息列表", notes = "分页查询短信信息列表")
    @PostMapping(value = "get-warn-message-list")
    public ResponseEntity<SuccessResponse<Page<MessageList>>> getWarnMsgInfoList(@Validated @RequestBody QueryMessageInfoList queryMessage) throws Exception {
        IPage<MessageList> list = baseService.getWarnMsgInfoList(queryMessage.getAdcd(), queryMessage.getStm(), queryMessage.getEtm(), queryMessage.getMediaId(), queryMessage.getMsgTypeId(), queryMessage.getPageNum(), queryMessage.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "获取短信信息列表中的失败短信记录", notes = "获取短信信息列表中的失败短信记录")
    @GetMapping(value = "get-warn-fail-message")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "msgid", value = "短信编号", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<FailMessage>>> getFailMessage(@RequestParam String adcd, @RequestParam String msgid) throws Exception {
        List<FailMessage> list = baseService.getFailMessage(adcd, msgid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "重新发送失败短信", notes = "重新发送失败短信")
    @PostMapping(value = "update-send-result")
    public ResponseEntity<SuccessResponse<String>> updateSendResult(@RequestBody QueryUpdateFailMessage query) throws Exception {
        String result = baseService.updateSendResult(query.getMessages());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

}







