package com.huitu.cloud.api.ewci.weChat.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ApiModel(value="雨情列表信息")
public class WeChatRain implements Serializable {
    /**
     * TODO: 暂时设置为-3495965216042804492L，原因：缓存ID对应不上
     * 序列化id
     */
    private static final long serialVersionUID = -3495965216042804492L;

    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "河流")
    private String rvnm;

    @ApiModelProperty(value = "水系")
    private String hnnm;

    @ApiModelProperty(value = "流域")
    private String bsnm;

    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    @ApiModelProperty(value = "测站地址")
    private String stlc;

    @ApiModelProperty(value = "测站政区编码")
    private String adcd;

    @ApiModelProperty(value = "测站类型")
    private String sttp;

    @ApiModelProperty(value = "测站归属类型")
    private String frgrd;

    @ApiModelProperty(value = "累计雨量")
    private String drps;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "区县名称")
    private String xadnm;

    @ApiModelProperty(value = "乡镇级行政区划码")
    private String xzadcd;

    @ApiModelProperty(value = "乡镇级行政区划名称")
    private String xzadnm;

    @ApiModelProperty(value = "市名称")
    private String cadnm;

    @ApiModelProperty(value = "测站归属类别名称")
    private String stadtpnm;

    @ApiModelProperty(value = "测站归属类别")
    private String stadtp;

    @ApiModelProperty(value = "未来一小时降雨 仅预报降雨查询显示")
    private String oneHourRain;

    @ApiModelProperty(value = "预报降雨时显示，值为累计降雨和预报降雨的和")
    private String drpsrain;

    @ApiModelProperty(value = "偏移经度")
    private BigDecimal plgtd;

    @ApiModelProperty(value = "偏移纬度")
    private BigDecimal plttd;
    @ApiModelProperty(value = "预计3小时降雨量")
    private double rain3;
    @ApiModelProperty(value = "预计6小时降雨量")
    private double rain6;
    @ApiModelProperty(value = "预计12小时降雨量")
    private double rain12;
    @ApiModelProperty(value = "预计24小时降雨量")
    private double rain24;
    public String getDrpsrain() {
        return drpsrain;
    }

    public void setDrpsrain(String drpsrain) {
        this.drpsrain = drpsrain;
    }

    public String getOneHourRain() {
        return oneHourRain;
    }

    public void setOneHourRain(String oneHourRain) {
        this.oneHourRain = oneHourRain;
    }

    public String getCadnm() {
        return cadnm;
    }

    public void setCadnm(String cadnm) {
        this.cadnm = cadnm;
    }

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getXzadcd() {
        return xzadcd;
    }

    public void setXzadcd(String xzadcd) {
        this.xzadcd = xzadcd;
    }

    public String getXzadnm() {
        return xzadnm;
    }

    public void setXzadnm(String xzadnm) {
        this.xzadnm = xzadnm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getHnnm() {
        return hnnm;
    }

    public void setHnnm(String hnnm) {
        this.hnnm = hnnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getFrgrd() {
        return frgrd;
    }

    public void setFrgrd(String frgrd) {
        this.frgrd = frgrd;
    }

    public String getDrps() {
        return drps;
    }

    public void setDrps(String drps) {
        this.drps = drps;
    }

    public String getStadtp() {
        return stadtp;
    }

    public void setStadtp(String stadtp) {
        this.stadtp = stadtp;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    public double getRain3() {
        return rain3;
    }

    public void setRain3(double rain3) {
        this.rain3 = rain3;
    }

    public double getRain6() {
        return rain6;
    }

    public void setRain6(double rain6) {
        this.rain6 = rain6;
    }

    public double getRain12() {
        return rain12;
    }

    public void setRain12(double rain12) {
        this.rain12 = rain12;
    }

    public double getRain24() {
        return rain24;
    }

    public void setRain24(double rain24) {
        this.rain24 = rain24;
    }
}
