package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 防治区企事业单位汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-18
 */
@TableName("IA_C_BSNSSINFO")
@ApiModel(value="IaCBsnssinfo对象", description="防治区企事业单位汇总表")
public class IaBsnssinfo extends  RelevantInfo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "单位编码")
    @TableId(value = "EICD", type = IdType.NONE)
    private String eicd;

    @ApiModelProperty(value = "单位名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "危险区代码")
    @TableField("DAND")
    private String dand;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private Double lttd;

    @ApiModelProperty(value = "单位类别")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = "组织机构代码")
    @TableField("OCODE")
    private String ocode;

    @ApiModelProperty(value = "地址")
    @TableField("ADDRESS")
    private String address;

    @ApiModelProperty(value = "占地面积（亩）")
    @TableField("AREA")
    private Double area;

    @ApiModelProperty(value = "在岗人数（人）")
    @TableField("PCOUNT")
    private Double pcount;

    @ApiModelProperty(value = "房屋数量（座）")
    @TableField("HCOUNT")
    private Double hcount;

    @ApiModelProperty(value = "固定资产（万元）")
    @TableField("AVALUE")
    private Double avalue;

    @ApiModelProperty(value = "年产值（万元）")
    @TableField("OVALUE")
    private Double ovalue;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getEicd() {
        return eicd;
    }

    public void setEicd(String eicd) {
        this.eicd = eicd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getDand() {
        return dand;
    }

    public void setDand(String dand) {
        this.dand = dand;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOcode() {
        return ocode;
    }

    public void setOcode(String ocode) {
        this.ocode = ocode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public Double getPcount() {
        return pcount;
    }

    public void setPcount(Double pcount) {
        this.pcount = pcount;
    }

    public Double getHcount() {
        return hcount;
    }

    public void setHcount(Double hcount) {
        this.hcount = hcount;
    }

    public Double getAvalue() {
        return avalue;
    }

    public void setAvalue(Double avalue) {
        this.avalue = avalue;
    }

    public Double getOvalue() {
        return ovalue;
    }

    public void setOvalue(Double ovalue) {
        this.ovalue = ovalue;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }


}
