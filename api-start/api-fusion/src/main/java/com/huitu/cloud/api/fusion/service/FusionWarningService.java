package com.huitu.cloud.api.fusion.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.fusion.entity.request.AscriptionTypeCountReq;
import com.huitu.cloud.api.fusion.entity.request.FusionFollowupRequest;
import com.huitu.cloud.api.fusion.entity.request.FusionWarningRecordReq;
import com.huitu.cloud.api.fusion.entity.request.FusionWarningSendingReq;
import com.huitu.cloud.api.fusion.entity.response.*;

import java.io.OutputStream;
import java.util.List;

/**
 * 山洪雨量升级预警服务
 */
public interface FusionWarningService {

    /**
     * 获取升级预警分页列表
     *
     * @param request 请求参数
     * @return 升级预警分页列表
     **/
    IPage<FusionWarningRecordResp> getPageList(FusionWarningRecordReq request);


    /**
     * 获取山洪雨量升级预警底部统计
     *
     * @param request 请求参数
     * @return 升级预警底部统计数据
     **/
    AscriptionTypeCountResp getAscriptionTypeCount(AscriptionTypeCountReq request);

    /**
     * 根据预警ID和预警等级，获取对应的预警监测数据快照
     *
     * @param warnId 预警ID
     * @return 监测数据快照
     */
    FusionSnapshotMonitorResp getSnapshotMonitor(String warnId);

    /**
     * 获取山洪雨量升级预警基本信息
     *
     * @param warnId 预警ID
     * @return 山洪雨量升级预警基本信息
     */
    FusionSnapshotIndexResp getSnapshotIndex(String warnId);

    /**
     * 获取山洪雨量升级预警发送情况分页列表
     *
     * @param request 请求参数
     * @return 预警发送情况分页列表
     */
    IPage<FusionWarningSendingResp> getSendingPageList(FusionWarningSendingReq request);

    /**
     * 获取预警分页列表
     *
     * @param request 请求参数
     * @return 预警分页列表
     **/
    IPage<FusionWarningRecordResp> getDetailPageList(FusionWarningRecordReq request);

    /**
     * 获取最新的山洪预警分页列表(1、2、3级)
     *
     * @param request 请求参数
     * @return 预警分页列表
     **/
    IPage<FusionWarningRecordResp> getWarnLatestPageList(FusionWarningRecordReq request);

    /**
     * 判断平台是否启用了预警
     *
     * @param platformId 平台ID
     * @return true：启用，false：禁用
     */
    boolean isEnabled(String platformId);


    /**
     * 根据预警ID集合，获取预警摘要列表
     *
     * @param warnIds 预警ID集合
     * @return 摘要列表
     */
    List<FusionWarningDigestResponse> getDigestList(List<String> warnIds);

    /**
     * 根据预警ID，获取预警记录
     *
     * @param warnId 预警ID
     * @return 预警记录
     */
    FusionWarningRecordResp getRecord(String warnId);

    /**
     * 根据预警ID，获取山洪预警详情
     *
     * @param warnId 预警ID
     * @return 预警详情
     */
    List<FusionWarningDetailResp> getWarnDetail(String warnId);

    /**
     * 根据预警ID，获取预警流程列表
     *
     * @param warnId 预警ID
     * @return 流程列表
     */
    List<FusionWarningFlowResp> getFlowList(String warnId);

    /**
     * 根据预警ID，获取预警消息列表
     *
     * @param warnId 预警ID
     * @return 消息列表
     */
    List<FusionWarningMessageResp<?>> getMessageList(String warnId);

    /**
     * 获取预警防治区后续降雨分页列表
     *
     * @param request 请求参数
     * @return 后续降雨分页列表
     */
    IPage<FusionFollowupResponse> getFollowupPageList(FusionFollowupRequest request);

    /**
     * 预警防治区后续降雨导出
     *
     * @param query  查询条件
     * @param output 输出流
     **/
    void followupExport(FusionFollowupRequest query, OutputStream output);

}
