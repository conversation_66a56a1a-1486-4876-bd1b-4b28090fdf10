<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.common.liuyu.mapper.BsnRvinfobDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.common.liuyu.entity.BsnRvinfoB">
        <id column="RVCD" property="rvcd" />
        <result column="RVNM" property="rvnm" />
        <result column="PRVCD" property="prvcd" />
        <result column="ORDNUM" property="ordnum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RVCD, RVNM, PRVCD, ORDNUM
    </sql>
<!--    SELECT RVNM RVCD, RVNM, PRVCD, ORDNUM FROM BSN_RVINFO_B order by ORDNUM-->
    <select id="selectRiver" resultType="com.huitu.cloud.api.common.liuyu.entity.BsnBasB">
        SELECT BAS_CODE, BAS_NAME, BAS_LEVEL, PBAS_CODE, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B ORDER BY BAS_NO, BAS_CODE
    </select>
    <select id="selectSt" resultType="com.huitu.cloud.api.common.liuyu.entity.BsnBasB">
        <!--select STCD RVCD, RTRIM(STNM) RVNM, RTRIM(BSNM) PRVCD, 99 ORDNUM  from ST_STBPRP_B
        WHERE  substring(ADDVCD,1,#{level})=#{ad}
        <if test="sttp!=null and sttp=='ZZ'">
            and (STTP='ZZ' or STTP='ZQ')
        </if>
        <if test="sttp!=null and sttp=='RR'">
            and (STTP='RR' or STTP='RQ')
        </if>
        <if test="sttp!=null and sttp!=''  and sttp!='PP' and sttp!='ZZ' and sttp!='RR'" >
            and STTP=#{sttp}
        </if>
        order by BSNM,stcd-->

        select a.STCD BAS_CODE, RTRIM(b.stnm) BAS_NAME, RTRIM(a.PBAS_CODE) PBAS_CODE, a.*, b.STNM, b.ADDVCD from (
        select a.BAS_CODE, a.BAS_LEVEL, a.BAS_NAME, a.PBAS_CODE, b.STCD from BSN_BAS_B a
        inner join BSN_BAS_ST b on a.BAS_CODE = b.BAS_CODE
        inner join (select stcd, MAX(BAS_LEVEL) level1 from BSN_BAS_B a inner join BSN_BAS_ST b on a.BAS_CODE = b.BAS_CODE group by STCD) c on b.STCD = c.STCD and c.level1 = a.BAS_LEVEL
        ) a inner join ST_STBPRP_B b on a.STCD = b.STCD
        WHERE  substring(ADDVCD,1,#{level})=#{ad}
        <if test="sttp!=null and sttp=='ZZ'">
            and (STTP='ZZ' or STTP='ZQ')
        </if>
        <if test="sttp!=null and sttp=='RR'">
            and (STTP='RR' or STTP='RQ')
        </if>
        <if test="sttp!=null and sttp!=''  and sttp!='PP' and sttp!='ZZ' and sttp!='RR'" >
            and STTP=#{sttp}
        </if>
        order by a.BAS_LEVEL, a.BAS_CODE
    </select>
    <select id="getStbyBscd" resultType="com.huitu.cloud.api.common.liuyu.entity.BsnBasB">
        with subqry(BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE) as (
        select BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE from BSN_BAS_B where BAS_CODE = #{basCode} --指定id
        union all
        select BSN_BAS_B.BAS_CODE,BSN_BAS_B.BAS_NAME,BSN_BAS_B.BAS_LEVEL,BSN_BAS_B.PBAS_CODE from BSN_BAS_B,subqry where BSN_BAS_B.PBAS_CODE = subqry.BAS_CODE
        )
        SELECT A.STCD BAS_CODE, B.STNM BAS_NAME, RTRIM(A.BAS_CODE) PBAS_CODE  FROM (
        SELECT DISTINCT B.STCD AS STCD, #{basCode} as BAS_CODE FROM SUBQRY A
        INNER JOIN BSN_BAS_ST B ON A.BAS_CODE = B.BAS_CODE
        ) A
        inner join ST_STBPRP_B B on a.STCD = B.STCD
        WHERE  substring(ADDVCD,1,#{level})=#{ad}
        <if test="sttp!=null and sttp=='ZZ'">
            and (STTP='ZZ' or STTP='ZQ')
        </if>
        <if test="sttp!=null and sttp=='RR'">
            and (STTP='RR' or STTP='RQ')
        </if>
        <if test="sttp!=null and sttp!=''  and sttp!='PP' and sttp!='ZZ' and sttp!='RR'" >
            and STTP=#{sttp}
        </if>
    </select>

</mapper>
