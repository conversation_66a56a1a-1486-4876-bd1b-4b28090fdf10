package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 场次降雨表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-02
 */
@TableName("BSN_TIMES_R")
@ApiModel(value="BsnTimesR对象", description="场次降雨表")
public class BsnTimesR extends Model<BsnTimesR> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "场次降雨编码")
    @TableId(value = "TMSID", type = IdType.NONE)
    private String tmsid;

    @ApiModelProperty(value = "政区编码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "降雨开始时间")
    @TableField("BGTM")
    private LocalDateTime bgtm;

    @ApiModelProperty(value = "降雨结束时间")
    @TableField("ENDTM")
    private LocalDateTime endtm;

    @ApiModelProperty(value = "场次累计降雨")
    @TableField("ACCP")
    private Double accp;

    @ApiModelProperty(value = "降雨历时")
    @TableField("INTV")
    private Integer intv;

    public String getTmsid() {
        return tmsid;
    }

    public void setTmsid(String tmsid) {
        this.tmsid = tmsid;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public LocalDateTime getBgtm() {
        return bgtm;
    }

    public void setBgtm(LocalDateTime bgtm) {
        this.bgtm = bgtm;
    }

    public LocalDateTime getEndtm() {
        return endtm;
    }

    public void setEndtm(LocalDateTime endtm) {
        this.endtm = endtm;
    }

    public Double getAccp() {
        return accp;
    }

    public void setAccp(Double accp) {
        this.accp = accp;
    }

    public Integer getIntv() {
        return intv;
    }

    public void setIntv(Integer intv) {
        this.intv = intv;
    }

    @Override
    protected Serializable pkVal() {
        return this.tmsid;
    }

    @Override
    public String toString() {
        return "BsnTimesR{" +
        "tmsid=" + tmsid +
        ", adcd=" + adcd +
        ", bgtm=" + bgtm +
        ", endtm=" + endtm +
        ", accp=" + accp +
        ", intv=" + intv +
        "}";
    }
}
