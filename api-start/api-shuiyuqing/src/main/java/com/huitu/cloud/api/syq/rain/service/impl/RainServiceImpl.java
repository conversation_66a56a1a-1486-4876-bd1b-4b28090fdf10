package com.huitu.cloud.api.syq.rain.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.syq.ad.entity.AdcdB;
import com.huitu.cloud.api.syq.ad.service.AdService;
import com.huitu.cloud.api.syq.constants.SyqConstants;
import com.huitu.cloud.api.syq.rain.entity.*;
import com.huitu.cloud.api.syq.rain.mapper.RainDao;
import com.huitu.cloud.api.syq.rain.remoting.api.IsoService;
import com.huitu.cloud.api.syq.rain.remoting.model.IsoRequest;
import com.huitu.cloud.api.syq.rain.service.RainService;
import com.huitu.cloud.api.syq.rain.utils.RainHelper;
import com.huitu.cloud.api.syq.rsvr.entity.RsvrRainList;
import com.huitu.cloud.api.syq.warn.entity.HeavyRainfallAlarm;
import com.huitu.cloud.config.RedisCache;
import com.huitu.cloud.constants.CommConstants;
import com.huitu.cloud.entity.LoginUserInfo;
import com.huitu.cloud.util.*;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("ALL")
@Service
public class RainServiceImpl implements RainService {
    @Autowired
    private RainDao rainDao;
    @Autowired
    private AdService adService;
    @Autowired
    private IsoService isoService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${shpFile.city:}")
    private String cntyShpFile;

    @Value("${shpFile.bas:}")
    private String basShpFile;

    private static final Logger logger = LoggerFactory.getLogger(RainServiceImpl.class);

    @Override
    public IPage<Rain> getRainByCondition(String stm, String etm, List<String> stType, String adcd, String bscd, String stnm, String threshold, String rainShowType,
                                          int pageNum, int pageSize, List<String> isOut, List<String> isFollow, List<Integer> forecastHour, String isVideo) {

        Page page = new Page<>(pageNum, pageSize);
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals(""))) || (isOut != null && (isOut.size() == 0 || isOut.get(0).equals("")))) {
            page.setRecords(new ArrayList<Rain>());
            return page;
        }
        if (pageSize < 0) {//查询全部
            List list = getRainByConditionAll(stm, etm, stType, adcd, bscd, stnm, threshold, rainShowType, isOut, isFollow, forecastHour, isVideo);
            page.setRecords(list);
            page.setTotal(list.size());
            return page;
        }
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        if (StringUtils.isNoneBlank(threshold)) {
            String[] items = threshold.split("-");
            Map<String, Object> map1 = new HashMap<>();
            param.put("min", items[0]);
            if (items.length > 1) {
                param.put("max", items[1]);
            }
            //雨量阀值存在 雨量必须有数据
            rainShowType = SyqConstants.RainConstants.QUERYRAINTYPEONE;

        }
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            param.put("stTypes", stTypes);
        }

        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        param.put("stnm", stnm);
        param.put("isVideo", isVideo);
        //辖区内外条件拼接
//       getOutParam(param,isOut,adcd.substring(0, level));

        //获取登录用户信息 查询域外
        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
        if (org.apache.commons.lang.StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
            String userAd = loginUserInfo.getUserAd();
            int level2 = AdcdUtil.getAdLevel(userAd);
            param.put("userLevel", level2);
            param.put("userAd", userAd);
            param.put("userId", loginUserInfo.getUserId());
        }
//        param.put("userAd","220000000000000");
//        param.put("userLevel",2);
//        param.put("userId",1);
        //添加是否查询域外（省级）
        if (isOut != null && isOut.size() == 1) {
            if (isOut.get(0).equals("1")) {
                //查询域内
                param.put("isOut", "1");
            } else {
                //查询域外
                param.put("isOut", "2");
                param.put("ad", "");
            }
        } else if (isOut != null && isOut.size() == 2) {
            //查询域内域外都查
            param.put("isOut", "3");
            param.put("ad", "");
            param.put("ad2", adcd.substring(0, level));
        }
        // 添加是否关注站点参数
        if (isFollow != null && isFollow.size() == 1) {
            if (isFollow.get(0).equals("1")) {
                //查询关注站点
                param.put("isFollow", "1");
            } else {
                //查询未关注站点
                param.put("isFollow", "2");
            }
        }
        //查询结果对象返回map 可以根据service接口需要，转化成自己需要实体返回类型
        IPage<Rain> resultMap = null;
        List<Rain> allList = null;
        List<Rain> stList;
        List<Rain> stList1;
        if (SyqConstants.RainConstants.QUERYRAINTYPEONE.equals(rainShowType)) {
            //查询一定时间内所有上报雨量信息的站
            resultMap = rainDao.getRainByCondition(page, param);
            stList = resultMap.getRecords();
            stList1 = filterRainListByBscd(stList, bscd);
            resultMap.setRecords(stList1);
        } else if (SyqConstants.RainConstants.QUERYRAINTYPEZERO.equals(rainShowType)) {
            //查询所有雨量站信息
            resultMap = rainDao.getRainByConditionAllSt(page, param);
            stList = resultMap.getRecords();
            stList1 = filterRainListByBscd(stList, bscd);
            resultMap.setRecords(stList1);
        }
        if (level == 2) {//增加市级名称
            Map<String, AdcdB> adMap = adService.getAdInfoList(null, "2");
            List<Rain> list = resultMap.getRecords();
            list.forEach(x -> {
                if (x.getAdcd() != null) {
                    String adCity = x.getAdcd().substring(0, 4) + adcd.substring(4);
                    if (adMap.containsKey(adCity)) {
                        AdcdB adcdb = adMap.get(adCity);
                        x.setCadnm(adcdb.getAdnm());
                    }
                }
            });
        }

        return page;
    }

    @Override
    public List<Rain> listRainDiff(String stm, String etm, List<String> stType, String adcd, String bscd) {
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<Rain> rainList = rainDao.getAccpByTmAll(param);
        List<StPptnF> forecast3HourList = getForecast3Hours(null, stm, etm);
        List<StPptnF> forecast24HourList = getForecast24Hours(null, stm, etm);

        Map<String, Rain> rainMap = rainList.stream().collect(Collectors.toMap(Rain::getStcd, Function.identity()));
        Map<String, StPptnF> rainF3Map = forecast3HourList.stream().collect(Collectors.toMap(StPptnF::getStcd, Function.identity()));
        Map<String, StPptnF> rainF24Map = forecast24HourList.stream().collect(Collectors.toMap(StPptnF::getStcd, Function.identity()));

        Map<String, Object> stParam = new HashMap<>();
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            stParam.put("stTypes", stTypes);
        }
        stParam.put("stType", stType);
        stParam.put("ad", adcd.substring(0, level));
        stParam.put("level", level);
        stParam.put("adcd", adcd);
        List<Rain> stList = rainDao.getRainStInfo(stParam);
        stList = filterRainListByBscd(stList, bscd);

        //有雨量数据集合
        List<Rain> hbList = new ArrayList<>();
        //无雨量数据集合
        List<Rain> noRainList = new ArrayList<>();
        Map<String, AdcdB> adMap = null;
        if (level == 2) {//增加市级名称
            adMap = adService.getAdInfoList(adcd, "2");
        }
        for (Rain x : stList) {
            String stcd = x.getStcd();
            if (level == 2) {//增加市级名称
                if (x.getAdcd() != null && x.getAdcd().length() == 15) {
                    String adCity = x.getAdcd().substring(0, 4) + adcd.substring(4);
                    if (adMap.containsKey(adCity)) {
                        AdcdB adcdb = adMap.get(adCity);
                        x.setCadnm(adcdb.getAdnm());
                    }
                }
            }

            //查询一定时间内所有上报雨量信息的站
            if (rainMap.containsKey(stcd)) {
                x.setDrps(rainMap.get(stcd).getDrps());
                if (rainF3Map.containsKey(stcd)) {
                    x.setRain3(rainF3Map.get(stcd).getDrp());
                }
                // 未来24小时
                if (rainF24Map.containsKey(stcd)) {
                    x.setRain24(rainF24Map.get(stcd).getDrp());
                }
                hbList.add(x);
            }
        }
        //排序字段不能为空
        //Collections.sort(hbList, Comparator.comparingDouble(Rain::getDrps).reversed().thenComparing(Rain::getStcd));
        hbList.sort(new Comparator<Rain>() {
            @Override
            public int compare(Rain m1, Rain m2) {
                double drp1 = new BigDecimal(m1.getDrps()).doubleValue();
                double drp2 = new BigDecimal(m2.getDrps()).doubleValue();
                if (drp1 - drp2 > 0) {
                    return -1;
                } else if (drp1 - drp2 < 0) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });
        return hbList;
    }

    @Override
    public List<AdAvgRain> listAdRainDiff(String stm, String etm, List<String> stType, String adcd) {
        // 初始化
        if (CollectionUtils.isEmpty(stType)) {
            stType = new LinkedList<>();
            stType.add("1");
            stType.add("2");
            stType.add("3");
            stType.add("6");
        }

        List<AdAvgRain> avgRainList = this.getAvgRainTreeByTmNew(stm, etm, stType, adcd, "0");
        if (CollectionUtils.isEmpty(avgRainList)) {
            return Collections.emptyList();
        }

        List<Rain> rainList = this.listRainDiff(stm, etm, stType, adcd, null);
        if (CollectionUtils.isEmpty(rainList)) {
            return avgRainList;
        }

        DecimalFormat df = new DecimalFormat("#0.0");
        avgRainList.forEach(item -> {
            if (StringUtils.isEmpty(item.getAdcd())) {
                return;
            }

            int cnt = 0;
            double fore3AvgRain = 0, fore24AvgRain = 0;

            String adcdValid = adcd.substring(0, AdcdUtil.getAdLevel(item.getAdcd()));
            for (int i = 0; i < rainList.size(); i++) {
                Rain rain = rainList.get(i);
                if (StringUtils.isEmpty(rain.getAdcd()) || rain.getAdcd().indexOf(adcdValid) != 0) {
                    continue;
                }

                cnt = cnt + 1;
                fore3AvgRain = fore3AvgRain + rain.getRain3();
                fore24AvgRain = fore24AvgRain + rain.getRain24();
            }

            if (cnt > 0) {
                item.setFore3AvgRain(df.format(fore3AvgRain / cnt));
                item.setFore24AvgRain(df.format(fore24AvgRain / cnt));
            }
        });

        return avgRainList;
    }

    @Override
    public RainSummary getRainSummary(String tm, String adcd) {
        Date date = DateUtil.parseDate(tm);
        String redisKey = String.format("%s_%s_%s_%s"
                , Thread.currentThread().getStackTrace()[1].getClassName()
                , Thread.currentThread().getStackTrace()[1].getMethodName()
                , DateUtil.format(date, "yyyyMMdd"), adcd);

        RainSummary rainSummary = (RainSummary) redisTemplate.opsForValue().get(redisKey);
        if (rainSummary == null) {
            int mth = Integer.parseInt(DateUtil.format(date, "MM"));
            int day = Integer.parseInt(DateUtil.format(date, "dd"));

            String curYear = DateUtil.format(date, "yyyy");
            String curYearMth = DateUtil.format(date, "yyyyMM");
            String lastYear = DateUtil.format(DateUtil.offsetMonth(date, -12), "yyyy");
            String lastYearMth = DateUtil.format(DateUtil.offsetMonth(date, -12), "yyyyMM");

            List<Map<String, String>> mthRainList = this.getAdcdContrastBytm(adcd, "1", curYear + "," + lastYear);
            List<Map<String, String>> dayRainList = this.getAdcdContrastBytm(adcd, "2", curYearMth + "," + lastYearMth);

            double accpSum = 0, lyAccpSum = 0, myAccpSum = 0;
            double fldAccpSum = 0, lyFldAccpSum = 0, myFldAccpSum = 0;
            // month
            String accpKey = curYear;
            String lyAccpKey = lastYear;
            String myAccpKey = RainHelper.MYAV_KEY;
            for (int i = 1; i < mth; i++) {
                Map<String, String> mthRain = mthRainList.get(i - 1);

                double accp = 0, lyAccp = 0, myAccp = 0;
                try {
                    if (mthRain.containsKey(accpKey) && mthRain.get(accpKey) != null) {
                        accp = Double.parseDouble(mthRain.get(accpKey));
                    }
                    if (mthRain.containsKey(lyAccpKey) && mthRain.get(lyAccpKey) != null) {
                        lyAccp = Double.parseDouble(mthRain.get(lyAccpKey));
                    }
                    if (mthRain.containsKey(myAccpKey) && mthRain.get(myAccpKey) != null) {
                        myAccp = Double.parseDouble(mthRain.get(myAccpKey));
                    }
                } catch (Exception ex) {
                    logger.info(ex.toString());
                }

                accpSum = accpSum + accp;
                lyAccpSum = lyAccpSum + lyAccp;
                myAccpSum = myAccpSum + myAccp;
                if (i >= 6 && i <= 9) {
                    fldAccpSum = fldAccpSum + accp;
                    lyFldAccpSum = lyFldAccpSum + lyAccp;
                    myFldAccpSum = myFldAccpSum + myAccp;
                }
            }
            // day
            String mthStr = (mth < 10 ? "0" + mth : "" + mth);
            accpKey = curYear + mthStr;
            lyAccpKey = lastYear + mthStr;
            myAccpKey = RainHelper.MYAV_KEY + mthStr;
            for (int i = 1; i <= day; i++) {
                Map<String, String> dayRain = dayRainList.get(i - 1);

                double accp = 0, lyAccp = 0, myAccp = 0;
                try {
                    if (dayRain.containsKey(accpKey) && dayRain.get(accpKey) != null) {
                        accp = Double.parseDouble(dayRain.get(accpKey));
                    }
                    if (dayRain.containsKey(lyAccpKey) && dayRain.get(lyAccpKey) != null) {
                        lyAccp = Double.parseDouble(dayRain.get(lyAccpKey));
                    }
                    if (dayRain.containsKey(myAccpKey) && dayRain.get(myAccpKey) != null) {
                        myAccp = Double.parseDouble(dayRain.get(myAccpKey));
                    }
                } catch (Exception ex) {
                    logger.info(ex.toString());
                }

                accpSum = accpSum + accp;
                lyAccpSum = lyAccpSum + lyAccp;
                myAccpSum = myAccpSum + myAccp;
                if (mth >= 6 && mth <= 9) {
                    fldAccpSum = fldAccpSum + accp;
                    lyFldAccpSum = lyFldAccpSum + lyAccp;
                    myFldAccpSum = myFldAccpSum + myAccp;
                }
            }

            // 最终数据
            rainSummary = new RainSummary();
            rainSummary.setAdcd(adcd);
            rainSummary.setAccp(accpSum);
            rainSummary.setLyAccp(lyAccpSum);
            rainSummary.setMyAccp(myAccpSum);
            rainSummary.setFldAccp(fldAccpSum);
            rainSummary.setLyFldAccp(lyFldAccpSum);
            rainSummary.setMyFldAccp(myFldAccpSum);

            if (lyAccpSum > 0) {
                rainSummary.setLyRate(100 * (accpSum - lyAccpSum) / lyAccpSum);
            }
            if (myAccpSum > 0) {
                rainSummary.setMyRate(100 * (accpSum - myAccpSum) / myAccpSum);
            }
            if (lyFldAccpSum > 0) {
                rainSummary.setLyFldRate(100 * (fldAccpSum - lyFldAccpSum) / lyFldAccpSum);
            }
            if (myFldAccpSum > 0) {
                rainSummary.setMyFldRate(100 * (fldAccpSum - myFldAccpSum) / myFldAccpSum);
            }

            // 缓存数据
            if (accpSum > 0 || myFldAccpSum > 0) {
                long minutes = DateUtil.between(date, DateUtil.date(), DateUnit.MINUTE);
                redisTemplate.opsForValue().set(redisKey, rainSummary
                        , minutes > RedisCache.CACHE_LONGTIME_IN_MINUTES ? RedisCache.CACHE_LONGTIME_IN_MINUTES : RedisCache.CACHE_TIME_IN_MINUTES
                        , TimeUnit.MINUTES);
            }
        }
        return rainSummary;
    }

    @Override
    public List<RainByBas> getRainByBasCondition(QueryRain baseDao) {
        List<RainByBas> list = rainDao.getBasList(baseDao.getBscd());
        // TRK@20240705: 全部测站计算面雨量
        baseDao.setThreshold("0-99999");
        List<Rain> rainList = getRainByConditionAllByBas(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBscd(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getRainShowType(), baseDao.getIsOut(), baseDao.getIsFollow(), baseDao.getForecastHour());
        DecimalFormat df = new DecimalFormat("#.0");
        list.forEach(item -> {
            List<Rain> children = rainList.stream().filter(x -> item.getBasCode().equals(x.getBasCode())).collect(Collectors.toList());
            item.setChildren(children);
            if (!CollectionUtils.isEmpty(item.getChildren())) {
                // 雨量值>=0
                List<Rain> drpsList = item.getChildren().stream().filter(d -> Double.valueOf(d.getDrps()) >= 0).collect(Collectors.toList());
                // 平均降雨
                double drpsSum = drpsList.stream().map(d -> Double.valueOf(d.getDrps())).reduce(Double::sum).get();
                item.setAvgRain(Double.parseDouble(df.format(drpsSum / drpsList.size())));

                // 最大降雨
                double drpsMax = drpsList.stream().map(d -> Double.valueOf(d.getDrps())).reduce(Double::max).get();
                item.setMaxRain(drpsMax);
                // 前期24小时
                double rainb24Sum = drpsList.stream().map(d -> d.getRainb24()).reduce(Double::sum).get();
                item.setAvgLdrps(Double.parseDouble(df.format(rainb24Sum / drpsList.size())));
                // 未来3h
                double rain3Sum = drpsList.stream().map(d -> d.getRain3()).reduce(Double::sum).get();
                item.setAvgF3drps(Double.parseDouble(df.format(rain3Sum / drpsList.size())));
                // 未来24h
                double rain24Sum = drpsList.stream().map(d -> d.getRain24()).reduce(Double::sum).get();
                item.setAvgF24drps(Double.parseDouble(df.format(rain24Sum / drpsList.size())));
            }
        });
        // 排序
        Collections.sort(list, (bas1, bas2) -> {
            if (bas1.getAvgRain() < bas2.getAvgRain()) {
                return 1;
            } else if (bas1.getAvgRain() > bas2.getAvgRain()) {
                return -1;
            }
            if (bas1.getAvgF3drps() < bas2.getAvgF3drps()) {
                return 1;
            } else if (bas1.getAvgF3drps() > bas2.getAvgF3drps()) {
                return -1;
            }
            if (bas1.getBasNo() != null && bas2.getBasNo() != null) {
                if (bas1.getBasNo() < bas2.getBasNo()) {
                    return -1;
                } else if (bas1.getBasNo() > bas2.getBasNo()) {
                    return 1;
                }
            } else if (bas1.getBasNo() != null) {
                return -1;
            } else if (bas2.getBasNo() != null) {
                return 1;
            }
            return bas1.getBasCode().compareTo(bas2.getBasCode());
        });
        return list;
    }

    @Override
    public List<RainByBas> getRainByBasMainCondition(QueryRain baseDao) {
        List<RainByBas> list = rainDao.getBasMainList(baseDao.getBscd(),baseDao.getStnm());
        // TRK@20240705: 全部测站计算面雨量
        baseDao.setThreshold("0-99999");
        List<Rain> rainList = getRainByConditionAllByBas(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBscd(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getRainShowType(), baseDao.getIsOut(), baseDao.getIsFollow(), baseDao.getForecastHour());
        // 关联关系
        List<BsnBasStTo> basStToList = rainDao.getBasStList();
        Map<String, Map<String, List<BsnBasStTo>>> stMap = basStToList.stream().collect(Collectors.groupingBy(BsnBasStTo::getStcd, Collectors.groupingBy(BsnBasStTo::getBasCode)));

        DecimalFormat df = new DecimalFormat("#.0");
        list.forEach(item -> {
            Map<String, Object> tempMap = new HashMap<>();
            List<Rain> children = rainList.stream().filter(x -> (stMap.get(x.getStcd()) != null && stMap.get(x.getStcd()).get(item.getBasCode()) != null))
                    .filter(x -> tempMap.putIfAbsent(x.getStcd(), x) == null).collect(Collectors.toList());
            item.setChildren(children);
            if (!CollectionUtils.isEmpty(item.getChildren())) {
                // 雨量值>=0
                List<Rain> drpsList = item.getChildren().stream().filter(d -> Double.valueOf(d.getDrps()) >= 0).collect(Collectors.toList());
                // 平均降雨
                double drpsSum = drpsList.stream().map(d -> Double.valueOf(d.getDrps())).reduce(Double::sum).get();
                item.setAvgRain(Double.parseDouble(df.format(drpsSum / drpsList.size())));

                // 最大降雨
                double drpsMax = drpsList.stream().map(d -> Double.valueOf(d.getDrps())).reduce(Double::max).get();
                item.setMaxRain(drpsMax);
                // 前期24小时
                double rainb24Sum = drpsList.stream().map(d -> d.getRainb24()).reduce(Double::sum).get();
                item.setAvgLdrps(Double.parseDouble(df.format(rainb24Sum / drpsList.size())));
                // 未来3h
                double rain3Sum = drpsList.stream().map(d -> d.getRain3()).reduce(Double::sum).get();
                item.setAvgF3drps(Double.parseDouble(df.format(rain3Sum / drpsList.size())));
                // 未来24h
                double rain24Sum = drpsList.stream().map(d -> d.getRain24()).reduce(Double::sum).get();
                item.setAvgF24drps(Double.parseDouble(df.format(rain24Sum / drpsList.size())));
            }
        });
        List<RainByBas> list1 = list.stream().filter(i -> i.getIsZyjh().equals("1")).collect(Collectors.toList());

        List<RainByBas> list2 = list.stream().filter(i -> i.getIsZyjh().equals("2")).collect(Collectors.toList());
        Map<String, List<RainByBas>> rbMap = list2.stream().collect(Collectors.groupingBy(RainByBas::getPbasCode));
        // 排序
        Collections.sort(list1, (bas1, bas2) -> {
            if (bas1.getAvgRain() < bas2.getAvgRain()) {
                return 1;
            } else if (bas1.getAvgRain() > bas2.getAvgRain()) {
                return -1;
            }
            if (bas1.getAvgF3drps() < bas2.getAvgF3drps()) {
                return 1;
            } else if (bas1.getAvgF3drps() > bas2.getAvgF3drps()) {
                return -1;
            }
            if (bas1.getBasNo() != null && bas2.getBasNo() != null) {
                if (bas1.getBasNo() < bas2.getBasNo()) {
                    return -1;
                } else if (bas1.getBasNo() > bas2.getBasNo()) {
                    return 1;
                }
            } else if (bas1.getBasNo() != null) {
                return -1;
            } else if (bas2.getBasNo() != null) {
                return 1;
            }
            return bas1.getBasCode().compareTo(bas2.getBasCode());
        });

        list1.forEach(i -> {
            if (rbMap.containsKey(i.getBasCode())) {
                List<RainByBas> rainByBas = rbMap.get(i.getBasCode());
                i.setBasChildren(rainByBas);
            }
        });
        return list1;
    }

    @Override
    public List<RainByBas> getRainByBasMainConditionMobile(QueryRain baseDao) {
        List<RainByBas> list = rainDao.getBasMainMobileList(baseDao.getBscd());
        // TRK@20240705: 全部测站计算面雨量
        baseDao.setThreshold("0-99999");
        List<Rain> rainList = getRainByConditionAllByBas(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBscd(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getRainShowType(), baseDao.getIsOut(), baseDao.getIsFollow(), baseDao.getForecastHour());
        // 关联关系
        List<BsnBasStTo> basStToList = rainDao.getBasStList();
        Map<String, Map<String, List<BsnBasStTo>>> stMap = basStToList.stream().collect(Collectors.groupingBy(BsnBasStTo::getStcd, Collectors.groupingBy(BsnBasStTo::getBasCode)));

        DecimalFormat df = new DecimalFormat("#.0");
        list.forEach(item -> {
            Map<String, Object> tempMap = new HashMap<>();
            List<Rain> children = rainList.stream().filter(x -> (stMap.get(x.getStcd()) != null && stMap.get(x.getStcd()).get(item.getBasCode()) != null))
                    .filter(x -> tempMap.putIfAbsent(x.getStcd(), x) == null).collect(Collectors.toList());
            item.setChildren(children);
            if (!CollectionUtils.isEmpty(item.getChildren())) {
                // 雨量值>=0
                List<Rain> drpsList = item.getChildren().stream().filter(d -> Double.valueOf(d.getDrps()) >= 0).collect(Collectors.toList());
                // 平均降雨
                double drpsSum = drpsList.stream().map(d -> Double.valueOf(d.getDrps())).reduce(Double::sum).get();
                item.setAvgRain(Double.parseDouble(df.format(drpsSum / drpsList.size())));

                // 最大降雨
                double drpsMax = drpsList.stream().map(d -> Double.valueOf(d.getDrps())).reduce(Double::max).get();
                item.setMaxRain(drpsMax);
                // 前期24小时
                double rainb24Sum = drpsList.stream().map(d -> d.getRainb24()).reduce(Double::sum).get();
                item.setAvgLdrps(Double.parseDouble(df.format(rainb24Sum / drpsList.size())));
                // 未来3h
                double rain3Sum = drpsList.stream().map(d -> d.getRain3()).reduce(Double::sum).get();
                item.setAvgF3drps(Double.parseDouble(df.format(rain3Sum / drpsList.size())));
                // 未来24h
                double rain24Sum = drpsList.stream().map(d -> d.getRain24()).reduce(Double::sum).get();
                item.setAvgF24drps(Double.parseDouble(df.format(rain24Sum / drpsList.size())));
            }
            item.setChildren(null);
        });
        List<RainByBas> list1 = list.stream().filter(i -> i.getIsZyjh().equals("1")).collect(Collectors.toList());
        List<RainByBas> list2 = list.stream().filter(i -> i.getIsZyjh().equals("2")).collect(Collectors.toList());
        Map<String, List<RainByBas>> rbMap = list2.stream().collect(Collectors.groupingBy(RainByBas::getPbasCode));
        // 排序
        Collections.sort(list1, (bas1, bas2) -> {
            if (bas1.getAvgRain() < bas2.getAvgRain()) {
                return 1;
            } else if (bas1.getAvgRain() > bas2.getAvgRain()) {
                return -1;
            }
            if (bas1.getAvgF3drps() < bas2.getAvgF3drps()) {
                return 1;
            } else if (bas1.getAvgF3drps() > bas2.getAvgF3drps()) {
                return -1;
            }
            if (bas1.getBasNo() != null && bas2.getBasNo() != null) {
                if (bas1.getBasNo() < bas2.getBasNo()) {
                    return -1;
                } else if (bas1.getBasNo() > bas2.getBasNo()) {
                    return 1;
                }
            } else if (bas1.getBasNo() != null) {
                return -1;
            } else if (bas2.getBasNo() != null) {
                return 1;
            }
            return bas1.getBasCode().compareTo(bas2.getBasCode());
        });
        rbMap.forEach((key, value) -> {
            Optional<Integer> indexOpt = list1.stream()
                    .filter(i -> key.equals(i.getBasCode()))
                    .findFirst()
                    .map(i -> list1.indexOf(i));
            if (indexOpt.isPresent()) {
                List<RainByBas> collect = value.stream().sorted(Comparator.comparing(RainByBas::getBasCode).reversed()).collect(Collectors.toList());
                for (RainByBas rainByBas : collect) {
                    String basName = rainByBas.getBasName();
                    rainByBas.setFlowArea(basName);
                    list1.add(indexOpt.get() + 1, rainByBas);
                }
            }
        });

        return list1;
    }

    @Override
    public IPage<Rain> getRainByConditionEntity(String stm, String etm, List<String> stType, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stType", stType);

        QueryRain queryRain = new QueryRain();
        queryRain.setStm(stm);
        queryRain.setEtm(etm);
        queryRain.setStType(stType);
        //查询结果对象直接返回实体对象 实体传参
        IPage<Rain> list = rainDao.getRainByConditionParams(page, stm, etm);
        return list;
    }

    @Override
    public List<Rain> getRainByConditionAll(String stm, String etm, List<String> stType, String adcd, String bscd, String stnm, String threshold, String rainShowType, List<String> isOut, List<String> isFollow, List<Integer> forecastHour, String isVideo) {

        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<Rain> rainList = null;
        rainList = rainDao.getAccpByTmAll(param);
        List<StPptnF> forecast3HourList = new ArrayList<>();
        List<StPptnF> forecast24HourList = new ArrayList<>();
        if (DateUtil.parse(etm).between(new Date(), DateUnit.MINUTE) <= 60L) {
            // 未来2小时测站的预报降雨
            forecast3HourList = getForecast3Hours(null, null, null);
            // 未来24小时测站的预报降雨
            forecast24HourList = getForecast24Hours(null, null, null);
        }
        param.put("stm", DateUtil.parse(stm).offset(DateField.HOUR, -24).toString("yyyy-MM-dd HH:00:00"));
        param.put("etm", stm);
        // 过去24小时
        List<Rain> rainBefore24List = rainDao.getAccpByTmAll(param);

        Map<String, Rain> rainMap = rainList.stream().collect(Collectors.toMap(Rain::getStcd, Function.identity()));
        Map<String, Rain> rainB24Map = rainBefore24List.stream().collect(Collectors.toMap(Rain::getStcd, Function.identity()));
        Map<String, StPptnF> rainF3Map = forecast3HourList.stream().collect(Collectors.toMap(StPptnF::getStcd, Function.identity()));
        // 未来24小时
        Map<String, StPptnF> rainF24Map = forecast24HourList.stream().collect(Collectors.toMap(StPptnF::getStcd, Function.identity()));
        Map<String, Object> stParam = new HashMap<>();
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            stParam.put("stTypes", stTypes);
        }
        stParam.put("stType", stType);
        stParam.put("ad", adcd.substring(0, level));
        stParam.put("level", level);
        stParam.put("adcd", adcd);
        stParam.put("stnm", stnm);
        stParam.put("isVideo", isVideo);
        //辖区内外条件拼接
        //getOutParam(stParam,isOut,adcd.substring(0, level));
        //获取登录用户信息 查询域外
        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
        if (org.apache.commons.lang.StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
            String userAd = loginUserInfo.getUserAd();
            int level2 = AdcdUtil.getAdLevel(userAd);
            stParam.put("userLevel", level2);
            stParam.put("userAd", userAd);
            stParam.put("userId", loginUserInfo.getUserId());
        }
        //添加是否查询域外（省级）
        if (isOut != null && isOut.size() == 1) {
            if (isOut.get(0).equals("1")) {
                //查询域内
                stParam.put("isOut", "1");
            } else {
                //查询域外
                stParam.put("isOut", "2");
            }
        } else if (isOut != null && isOut.size() == 2) {
            //查询域内域外都查
            stParam.put("isOut", "3");
            stParam.put("ad2", adcd.substring(0, level));
        }
        // 添加是否关注站点参数
        if (isFollow != null && isFollow.size() == 1) {
            if (isFollow.get(0).equals("1")) {
                //查询关注站点
                stParam.put("isFollow", "1");
            } else {
                //查询未关注站点
                stParam.put("isFollow", "2");
            }
        }
        if (forecastHour != null) {
            stParam.put("forecastHour", forecastHour);
            forecastHour.sort(new Comparator<Integer>() {
                public int compare(Integer o1, Integer o2) {
                    if (o1 > o2)
                        return 1;//第二个元素（o1）比第一个元素（o2）大，返回1
                    if (o1 == o2)
                        return 0;
                    return -1;
                }//1,0,-1三者同时出现时，1表示不交换位置，0表示相等时不交换，-1表示交换
            });
            Date d = new Date();
            GregorianCalendar gc = new GregorianCalendar();
            gc.setTime(d);
            SimpleDateFormat sDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar cal = Calendar.getInstance();
            cal.setTime(d);
            cal.set(Calendar.HOUR_OF_DAY, d.getHours());
            if (gc.get(gc.MINUTE) == 0 && gc.get(gc.SECOND) == 0) {
                stParam.put("start_time", sDateFormat.format(d));
            } else {
                cal.add(Calendar.HOUR_OF_DAY, 1);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                // 24小时制
                stParam.put("start_time", sDateFormat.format(cal.getTime()));
            }
            List<String> endTimeList = new ArrayList<>();
            for (int i = 0; i < forecastHour.size(); i++) {
                cal.add(Calendar.HOUR_OF_DAY, i == 0 ? forecastHour.get(i) : forecastHour.get(i) - forecastHour.get(i - 1));
                stParam.put("end_time" + forecastHour.get(i), sDateFormat.format(cal.getTime()));
                endTimeList.add(sDateFormat.format(cal.getTime()));
            }
            stParam.put("endTimeList", endTimeList);
        }
//        System.out.println("==============="+ JSONObject.toJSONString(stParam));
        List<Rain> stList = rainDao.getRainStInfo(stParam);
        stList = filterRainListByBscd(stList, bscd);
        //有雨量数据集合
        List<Rain> hbList = new ArrayList<>();
        //无雨量数据集合
        List<Rain> noRainList = new ArrayList<>();
        Map<String, AdcdB> adMap = null;
        if (level == 2) {//增加市级名称
            adMap = adService.getAdInfoList(adcd, "2");
        }
        for (Rain x : stList) {
            String stcd = x.getStcd();
            if (level == 2) {//增加市级名称
                if (x.getAdcd() != null && x.getAdcd().length() == 15) {
                    String adCity = x.getAdcd().substring(0, 4) + adcd.substring(4);
                    if (adMap.containsKey(adCity)) {
                        AdcdB adcdb = adMap.get(adCity);
                        x.setCadnm(adcdb.getAdnm());
                    }
                }
            }
            if (SyqConstants.RainConstants.QUERYRAINTYPEONE.equals(rainShowType)) {
                //查询一定时间内所有上报雨量信息的站
                if (rainMap.containsKey(stcd)) {
                    x.setDrps(rainMap.get(stcd).getDrps());
                    if (rainB24Map.containsKey(stcd)) {
                        x.setRainb24(Double.parseDouble(rainB24Map.get(stcd).getDrps()));
                    }
                    if (rainF3Map.containsKey(stcd)) {
                        x.setRain3(rainF3Map.get(stcd).getDrp());
                    }
                    // 未来24小时
                    if (rainF24Map.containsKey(stcd)) {
                        x.setRain24(rainF24Map.get(stcd).getDrp());
                    }
                    hbList.add(x);
                }


            } else if (SyqConstants.RainConstants.QUERYRAINTYPEZERO.equals(rainShowType)) {
                //查询所有雨量站信息
                if (rainMap.containsKey(stcd)) {
                    x.setDrps(rainMap.get(stcd).getDrps());
                    if (rainB24Map.containsKey(stcd)) {
                        x.setRainb24(Double.parseDouble(rainB24Map.get(stcd).getDrps()));
                    }
                    if (rainF3Map.containsKey(stcd)) {
                        x.setRain3(rainF3Map.get(stcd).getDrp());
                    }
                    // 未来24小时
                    if (rainF24Map.containsKey(stcd)) {
                        x.setRain24(rainF24Map.get(stcd).getDrp());
                    }
                    hbList.add(x);
                } else {
                    if (rainB24Map.containsKey(stcd)) {
                        x.setRainb24(Double.parseDouble(rainB24Map.get(stcd).getDrps()));
                    }
                    if (rainF3Map.containsKey(stcd)) {
                        x.setRain3(rainF3Map.get(stcd).getDrp());
                    }
                    // 未来24小时
                    if (rainF24Map.containsKey(stcd)) {
                        x.setRain24(rainF24Map.get(stcd).getDrp());
                    }
                    noRainList.add(x);
                }
            }
        }
        if (StringUtils.isNoneBlank(threshold)) {
            String[] items = threshold.split("-");
            final double min = Double.parseDouble(items[0]);
            final double max = Double.parseDouble(items[1]);
            //根据阀值过滤
            hbList = hbList.stream().filter(x -> new BigDecimal(x.getDrps()).doubleValue() >= min && new BigDecimal(x.getDrps()).doubleValue() <= max).collect(Collectors.toList());
        }
        //排序字段不能为空
        //Collections.sort(hbList, Comparator.comparingDouble(Rain::getDrps).reversed().thenComparing(Rain::getStcd));
        hbList.sort(new Comparator<Rain>() {
            @Override
            public int compare(Rain m1, Rain m2) {
                double drp1 = new BigDecimal(m1.getDrps()).doubleValue();
                double drp2 = new BigDecimal(m2.getDrps()).doubleValue();
                if (drp1 - drp2 > 0) {
                    return -1;
                } else if (drp1 - drp2 < 0) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });
        //无数据暂时去掉
        if (SyqConstants.RainConstants.QUERYRAINTYPEZERO.equals(rainShowType) && !StringUtils.isNoneBlank(threshold)) {
            hbList.addAll(noRainList);
        }
        //判断需不需要取未来一小时降雨信息
        try {
            String date = DateFormatUtil.beforeOrAfterHourToString(1, "yyyy-MM-dd HH:00", new Date());
            if (etm.equals(date)) {
                //查询未来一小时降雨信息
                List<Map<String, Object>> OneHourRainList = rainDao.getOneHourRain();
                if (OneHourRainList != null && OneHourRainList.size() > 0) {
                    Map<String, Object> OneHourRain = new HashMap<>();
                    OneHourRainList.forEach(item -> {
                        OneHourRain.put(item.get("STCD").toString(), item);
                    });
                    //将未来一小时降雨信息放入累计雨量集合中
                    hbList.forEach(item -> {
                        if (OneHourRain.containsKey(item.getStcd().trim())) {
                            Map<String, Object> rainValue = (Map<String, Object>) OneHourRain.get(item.getStcd().trim());
                            item.setOneHourRain(rainValue.get("RAIN").toString());
                        }
                    });
                    //添加累计雨量和未来一小时降雨之和的累计降雨
                    hbList.forEach(item -> {
                        if (item.getOneHourRain() != null && !"".equals(item.getOneHourRain())) {
                            if (item.getDrps() != null && !"".equals(item.getDrps())) {
                                BigDecimal a = new BigDecimal(item.getDrps());
                                BigDecimal b = new BigDecimal(item.getOneHourRain());
                                item.setDrpsrain(a.add(b).toString());
                            } else {
                                item.setDrpsrain(item.getOneHourRain());
                            }
                        } else {
                            item.setDrpsrain(item.getDrps());
                        }
                    });
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return hbList;
    }


    @Override
    public List<Rain> getRainByConditionAllByBas(String stm, String etm, List<String> stType, String adcd, String bscd, String stnm, String threshold, String rainShowType, List<String> isOut, List<String> isFollow, List<Integer> forecastHour) {
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<Rain> rainList = null;
        rainList = rainDao.getAccpByTmAll(param);
        // 判断时间字符串格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");
        DateTimeFormatter formatterWithoutSeconds = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00");
        LocalDateTime stmB24 = null;
        LocalDateTime etmB24 = null;
        List<StPptnF> forecast3HourList = new ArrayList<>();
        List<StPptnF> forecast24HourList = new ArrayList<>();
        if (etm != null) {
            try {
                stmB24 = LocalDateTime.parse(stm, formatter);
                etmB24 = LocalDateTime.parse(etm, formatter);
            } catch (Exception e) {
                // 如果解析失败，尝试使用不包含秒的格式解析
                stmB24 = LocalDateTime.parse(stm, formatterWithoutSeconds);
                etmB24 = LocalDateTime.parse(etm, formatterWithoutSeconds);
            }
            long hoursDiff = Duration.between(etmB24, LocalDateTime.now()).toHours();
            if (Math.abs(hoursDiff) <= 1) {
                // 未来3小时测站的预报降雨
                forecast3HourList = getForecast3Hours(null, null, null);
                // 未来24小时测站的预报降雨
                forecast24HourList = getForecast24Hours(null, null, null);
            }
        }
        param.put("stm", stmB24.minusHours(24).format(formatter));
        param.put("etm", stm);
        // 过去24小时
        List<Rain> rainBefore24List = rainDao.getAccpByTmAll(param);

        Map<String, Rain> rainMap = rainList.stream().collect(Collectors.toMap(Rain::getStcd, Function.identity()));
        Map<String, Rain> rainB24Map = rainBefore24List.stream().collect(Collectors.toMap(Rain::getStcd, Function.identity()));
        Map<String, StPptnF> rainF3Map = forecast3HourList.stream().collect(Collectors.toMap(StPptnF::getStcd, Function.identity()));
        Map<String, StPptnF> rainF24Map = forecast24HourList.stream().collect(Collectors.toMap(StPptnF::getStcd, Function.identity()));
        Map<String, Object> stParam = new HashMap<>();
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            stParam.put("stTypes", stTypes);
        }
        stParam.put("stType", stType);
        stParam.put("ad", adcd.substring(0, level));
        stParam.put("level", level);
        stParam.put("adcd", adcd);
        stParam.put("stnm", stnm);
        //辖区内外条件拼接
//        getOutParam(stParam,isOut,adcd.substring(0, level));
        //获取登录用户信息 查询域外
        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
        if (org.apache.commons.lang.StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
            String userAd = loginUserInfo.getUserAd();
            int level2 = AdcdUtil.getAdLevel(userAd);
            stParam.put("userLevel", level2);
            stParam.put("userAd", userAd);
            stParam.put("userId", loginUserInfo.getUserId());
        }
        //添加是否查询域外（省级）
        if (isOut != null && isOut.size() == 1) {
            if (isOut.get(0).equals("1")) {
                //查询域内
                stParam.put("isOut", "1");
            } else {
                //查询域外
                stParam.put("isOut", "2");
            }
        } else if (isOut != null && isOut.size() == 2) {
            //查询域内域外都查
            stParam.put("isOut", "3");
            stParam.put("ad2", adcd.substring(0, level));
        }
        // 添加是否关注站点参数
        if (isFollow != null && isFollow.size() == 1) {
            if (isFollow.get(0).equals("1")) {
                //查询关注站点
                stParam.put("isFollow", "1");
            } else {
                //查询未关注站点
                stParam.put("isFollow", "2");
            }
        }
        if (forecastHour != null) {
            stParam.put("forecastHour", forecastHour);
            forecastHour.sort(new Comparator<Integer>() {
                public int compare(Integer o1, Integer o2) {
                    if (o1 > o2)
                        return 1;//第二个元素（o1）比第一个元素（o2）大，返回1
                    if (o1 == o2)
                        return 0;
                    return -1;
                }//1,0,-1三者同时出现时，1表示不交换位置，0表示相等时不交换，-1表示交换
            });
            Date d = new Date();
            GregorianCalendar gc = new GregorianCalendar();
            gc.setTime(d);
            SimpleDateFormat sDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar cal = Calendar.getInstance();
            cal.setTime(d);
            cal.set(Calendar.HOUR_OF_DAY, d.getHours());
            if (gc.get(gc.MINUTE) == 0 && gc.get(gc.SECOND) == 0) {
                stParam.put("start_time", sDateFormat.format(d));
            } else {
                cal.add(Calendar.HOUR_OF_DAY, 1);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                // 24小时制
                stParam.put("start_time", sDateFormat.format(cal.getTime()));
            }
            List<String> endTimeList = new ArrayList<>();
            for (int i = 0; i < forecastHour.size(); i++) {
                cal.add(Calendar.HOUR_OF_DAY, i == 0 ? forecastHour.get(i) : forecastHour.get(i) - forecastHour.get(i - 1));
                stParam.put("end_time" + forecastHour.get(i), sDateFormat.format(cal.getTime()));
                endTimeList.add(sDateFormat.format(cal.getTime()));
            }
            stParam.put("endTimeList", endTimeList);
        }
//        System.out.println("==============="+ JSONObject.toJSONString(stParam));
        List<Rain> stList = rainDao.getRainStInfoByBas(stParam);
        stList = filterRainListByBscd(stList, bscd);
        //有雨量数据集合
        List<Rain> hbList = new ArrayList<>();
        //无雨量数据集合
        List<Rain> noRainList = new ArrayList<>();
        Map<String, AdcdB> adMap = null;
        if (level == 2) {//增加市级名称
            adMap = adService.getAdInfoList(adcd, "2");
        }
        for (Rain x : stList) {
            String stcd = x.getStcd();
            if (level == 2) {//增加市级名称
                if (x.getAdcd() != null && x.getAdcd().length() == 15) {
                    String adCity = x.getAdcd().substring(0, 4) + adcd.substring(4);
                    if (adMap.containsKey(adCity)) {
                        AdcdB adcdb = adMap.get(adCity);
                        x.setCadnm(adcdb.getAdnm());
                    }
                }
            }
            if (SyqConstants.RainConstants.QUERYRAINTYPEONE.equals(rainShowType)) {
                //查询一定时间内所有上报雨量信息的站
                if (rainMap.containsKey(stcd)) {
                    x.setDrps(rainMap.get(stcd).getDrps());
                    if (rainB24Map.containsKey(stcd)) {
                        x.setRainb24(Double.parseDouble(rainB24Map.get(stcd).getDrps()));
                    }
                    if (rainF3Map.containsKey(stcd)) {
                        x.setRain3(rainF3Map.get(stcd).getDrp());
                    }
                    // 未来24小时
                    if (rainF24Map.containsKey(stcd)) {
                        x.setRain24(rainF24Map.get(stcd).getDrp());
                    }
                    hbList.add(x);
                }


            } else if (SyqConstants.RainConstants.QUERYRAINTYPEZERO.equals(rainShowType)) {
                //查询所有雨量站信息
                if (rainMap.containsKey(stcd)) {
                    x.setDrps(rainMap.get(stcd).getDrps());
                    if (rainB24Map.containsKey(stcd)) {
                        x.setRainb24(Double.parseDouble(rainB24Map.get(stcd).getDrps()));
                    }
                    if (rainF3Map.containsKey(stcd)) {
                        x.setRain3(rainF3Map.get(stcd).getDrp());
                    }
                    // 未来24小时
                    if (rainF24Map.containsKey(stcd)) {
                        x.setRain24(rainF24Map.get(stcd).getDrp());
                    }
                    hbList.add(x);
                } else {
                    if (rainB24Map.containsKey(stcd)) {
                        x.setRainb24(Double.parseDouble(rainB24Map.get(stcd).getDrps()));
                    }
                    if (rainF3Map.containsKey(stcd)) {
                        x.setRain3(rainF3Map.get(stcd).getDrp());
                    }
                    // 未来24小时
                    if (rainF24Map.containsKey(stcd)) {
                        x.setRain24(rainF24Map.get(stcd).getDrp());
                    }
                    noRainList.add(x);
                }
            }
        }
        if (StringUtils.isNoneBlank(threshold)) {
            String[] items = threshold.split("-");
            final double min = Double.parseDouble(items[0]);
            final double max = Double.parseDouble(items[1]);
            //根据阀值过滤
            hbList = hbList.stream().filter(x -> new BigDecimal(x.getDrps()).doubleValue() >= min && new BigDecimal(x.getDrps()).doubleValue() <= max).collect(Collectors.toList());
        }
        //排序字段不能为空
        //Collections.sort(hbList, Comparator.comparingDouble(Rain::getDrps).reversed().thenComparing(Rain::getStcd));
        hbList.sort(new Comparator<Rain>() {
            @Override
            public int compare(Rain m1, Rain m2) {
                double drp1 = new BigDecimal(m1.getDrps()).doubleValue();
                double drp2 = new BigDecimal(m2.getDrps()).doubleValue();
                if (drp1 - drp2 > 0) {
                    return -1;
                } else if (drp1 - drp2 < 0) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });
        //无数据暂时去掉
        if (SyqConstants.RainConstants.QUERYRAINTYPEZERO.equals(rainShowType) && !StringUtils.isNoneBlank(threshold)) {
            hbList.addAll(noRainList);
        }
//        //判断需不需要取未来一小时降雨信息
//        try {
//            String date = DateFormatUtil.beforeOrAfterHourToString(1, "yyyy-MM-dd HH:00", new Date());
//            if (etm.equals(date)) {
//                //查询未来一小时降雨信息
//                List<Map<String, Object>> OneHourRainList = rainDao.getOneHourRain();
//                if (OneHourRainList != null && OneHourRainList.size() > 0) {
//                    Map<String, Object> OneHourRain = new HashMap<>();
//                    OneHourRainList.forEach(item -> {
//                        OneHourRain.put(item.get("STCD").toString(), item);
//                    });
//                    //将未来一小时降雨信息放入累计雨量集合中
//                    hbList.forEach(item -> {
//                        if (OneHourRain.containsKey(item.getStcd().trim())) {
//                            Map<String, Object> rainValue = (Map<String, Object>) OneHourRain.get(item.getStcd().trim());
//                            item.setOneHourRain(rainValue.get("RAIN").toString());
//                        }
//                    });
//                    //添加累计雨量和未来一小时降雨之和的累计降雨
//                    hbList.forEach(item -> {
//                        if (item.getOneHourRain() != null && !"".equals(item.getOneHourRain())) {
//                            if (item.getDrps() != null && !"".equals(item.getDrps())) {
//                                BigDecimal a = new BigDecimal(item.getDrps());
//                                BigDecimal b = new BigDecimal(item.getOneHourRain());
//                                item.setDrpsrain(a.add(b).toString());
//                            } else {
//                                item.setDrpsrain(item.getOneHourRain());
//                            }
//                        } else {
//                            item.setDrpsrain(item.getDrps());
//                        }
//                    });
//                }
//            }
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
        return hbList;
    }


    @Override
    public List<StPptnR> getRainListByTm(String stm, String etm, String stcd, String type) {
        List<StPptnR> list = rainDao.getRainListByTm(stm, etm, stcd, type);
        return list;
    }

    private List<Rain> filterRainListByBscd(List<Rain> stList, String bscd) {
        if (bscd != null && !bscd.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<BsnBasStBTo> bsList = rainDao.getRiverTree(bscd);
            List<String> stcdList = bsList.stream().map(BsnBasStBTo::getStcd).collect(Collectors.toList());
            List<Rain> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            stList = intersection;
            return intersection;
        } else {
            return stList;
        }
    }

    private List<RainStrength> filterRainStrengthListByBscd(List<RainStrength> stList, String bscd) {
        if (bscd != null && !bscd.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<BsnBasStBTo> bsList = rainDao.getRiverTree(bscd);
            List<String> stcdList = bsList.stream().map(BsnBasStBTo::getStcd).collect(Collectors.toList());
            List<RainStrength> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            stList = intersection;
            return intersection;
        } else {
            return stList;
        }
    }

    private List<BsnRainAlarm> filterRainWarnListByBscd(List<BsnRainAlarm> stList, String bscd) {
        if (bscd != null && !bscd.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<BsnBasStBTo> bsList = rainDao.getRiverTree(bscd);
            List<String> stcdList = bsList.stream().map(BsnBasStBTo::getStcd).collect(Collectors.toList());
            List<BsnRainAlarm> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            stList = intersection;
            return intersection;
        } else {
            return stList;
        }
    }

    @Override
    public AdAvgRain getAvgRainTreeByTm(String stm, String etm, String adcd, List<String> stType, String showLevel) {
        AdAvgRain adAvgRain = null;
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stType", stType);
        param.put("ad", adcd.substring(0, level));
        List<AdAvgRain> allList = new ArrayList<>();
        if (level == 2) {
            param.put("level", level);
            param.put("zero", adcd.substring(level));
            param.put("level2", level);
            //省级雨量平均值
            List<AdAvgRain> listProvince = rainDao.getRainAdAvg(param);
            allList.addAll(listProvince);

            if (showLevel != null && Integer.parseInt(showLevel) > 1) {
                param.put("level2", level + 2);
                param.put("zero", adcd.substring(level + 2));
                //梅河口市作为地级市来处理 220581 市平均值去掉
                param.put("exceptFlag", true);
                //市级雨量平均值
                List<AdAvgRain> listCity = rainDao.getRainAdAvg(param);
                allList.addAll(listCity);

                if (Integer.parseInt(showLevel) > 2) {
                    param.remove("exceptFlag");
                    //县平均雨量值 begin
                    param.put("level2", level + 4);
                    param.put("zero", adcd.substring(level + 4));
                    List<AdAvgRain> listCounty = rainDao.getRainAdAvg(param);
                    //end
                    List<AdAvgRain> listCounty2 = new ArrayList<>();
                    for (AdAvgRain item : listCounty) {
                        if (!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd()) && !CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())) {
                            //市本级不展示在政区树上
                            if (SyqConstants.RainConstants.PROVINCEADCD.equals(item.getPadcd())) {
                            } else {
                                listCounty2.add(item);
                            }
                        } else {
                            listCounty2.add(item);
                        }
                    }
                    allList.addAll(listCounty2);
                } else {
                    // 展示到市级时，在此单查询梅河口的平均降雨 加在市平均列表中
                    param.remove("exceptFlag");
                    param.put("ad", CommConstants.Public.MEIHEKOU_ADCD.substring(0, 6));
                    param.put("level", 6);
                    param.put("level2", 6);
                    param.put("zero", adcd.substring(6));
                    List<AdAvgRain> listCounty = rainDao.getRainAdAvg(param);
                    allList.addAll(listCounty);

                    // 还原值
                    param.put("ad", adcd.substring(0, level));
                    param.put("level", level);
                }
            }
        } else if (level == 4) {
            param.put("level", level);
            param.put("zero", adcd.substring(level));
            param.put("level2", level);
            //梅河口市作为地级市来处理 220581 市平均值去掉
            param.put("exceptFlag", true);
            //市平均雨量值
            List<AdAvgRain> listCity = rainDao.getRainAdAvg(param);
            allList.addAll(listCity);

            if (Integer.parseInt(showLevel) > 2) {
                param.remove("exceptFlag");
                param.put("level2", level + 2);
                param.put("zero", adcd.substring(level + 2));
                // 县级雨量平均
                List<AdAvgRain> listCounty = rainDao.getRainAdAvg(param);
                List<AdAvgRain> listCounty2 = new ArrayList<>();
                for (AdAvgRain item : listCounty) {
                    if (!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd()) && !CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())) {
                        //市本级不展示在政区树上
                        if (SyqConstants.RainConstants.PROVINCEADCD.equals(item.getPadcd())) {
                        } else {
                            listCounty2.add(item);
                        }
                    } else {
                        listCounty2.add(item);
                    }
                }
                allList.addAll(listCounty2);
            }
        } else if (level == 6) {
            param.put("level", level);
            param.put("zero", adcd.substring(level));
            param.put("level2", level);
            //县平均
            List<AdAvgRain> listCounty = rainDao.getRainAdAvg(param);
            allList.addAll(listCounty);

            if (Integer.parseInt(showLevel) > 3) {
                param.put("level2", level + 3);
                param.put("zero", adcd.substring(level + 3));
                //乡镇平均
                List<AdAvgRain> listTown = rainDao.getRainAdAvg(param);
                allList.addAll(listTown);
            }
        }

        //查询最大雨量测站
        List<AdAvgRain> listSt = rainDao.getMaxRainStAd(param);
        //把最小级的政区最大雨量测站统计放入map并推算出高级政区的测站统计并存入map
        Map<String, Object> maxRainStAd = getMaxRainSt(listSt, level);
        //将最大雨量测站信息放入政区雨量集合中
        List<AdAvgRain> listTree = getTreeList(allList, maxRainStAd);
        if (listTree != null && listTree.size() > 0) {
            //获取等于当前政区编码节点
            adAvgRain = listTree.stream().filter(item -> item.getAdcd().equals(adcd)).collect(Collectors.toList()).get(0);
        }
        return adAvgRain;
    }

    @Override
    public List<AdAvgRain> getAvgRainTreeByTmNew(String stm, String etm, List<String> stType, String adcd, String isself) {
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if (stType != null && (stType.size() == 0 || stType.get(0).equals(""))) {
            return new ArrayList<AdAvgRain>();
        }
        if (isself != null && !"".equals(isself)) {

        } else {
            isself = "0";
        }
        List<AdAvgRain> adAvgRainList = null;
        String stadtp = "";
        if (stType != null && stType.size() > 0) {
            for (String type : stType) {
                stadtp = stadtp + type + ",";
            }
            stadtp = stadtp.substring(0, stadtp.length() - 1);
        }
        adAvgRainList = rainDao.getRainAdAvgNew(stm, etm, stadtp, adcd, isself);
        return adAvgRainList;
    }

    @Override
    public List<Rain> getMaxRainByTm(String stm, String etm, String adcd, List<String> stType, List<String> isOut) {
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals(""))) || (isOut != null && (isOut.size() == 0 || isOut.get(0).equals("")))) {
            return new ArrayList<Rain>();
        }
        List<Rain> list = getRainByConditionAll(stm, etm, stType, adcd, null, null, "0.1-9999", "1", isOut, null, (List<Integer>) null, null);
        List<Rain> result = new ArrayList<>();
        if (list != null && list.size() > 0) {
            result.add(list.get(0));
        }
        return result;
    }

    @Override
    public List<RainLevelStNum> getRainLevelStNumByTm(List<String> stType, String adcd, String etm, String stm, int rainRule0, int rainRule1, int rainRule2, int rainRule3, int rainRule4, int rainRule5) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        if (!CollectionUtils.isEmpty(stType)) {
            param.put("stType", String.join(",", stType));
        }
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("zero", adcd.substring(level));
        param.put("level", level);
        param.put("level2", level);
        param.put("rainRule0", rainRule0);
        param.put("rainRule1", rainRule1);
        param.put("rainRule2", rainRule2);
        param.put("rainRule3", rainRule3);
        param.put("rainRule4", rainRule4);
        param.put("rainRule5", rainRule5);
        List<RainLevelStNum> list = rainDao.getRainLevelStNumByTm(param);
        return list;
    }

    @Override
    public IPage<RainStrength> getRainStrength(String stm, String etm, List<String> stType, int intv, String adcd, String bscd, String stnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals("")))) {
            return page;
        }
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stType", stType);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("stnm", stnm);
        //查询结果对象返回map 可以根据service接口需要，转化成自己需要实体返回类型
        IPage<Map<String, Object>> resultMap = null;
        if (intv == 1) {
            resultMap = rainDao.getRainStrengthOneHour(page, param);
        } else {
            param.put("intv", intv);
            resultMap = rainDao.getRainStrength(page, param);
        }
        //转化实体 实体中添加字段注释
        List<RainStrength> result = MapBeanConvertUtil.mapsToBeans((List<Map<String, Object>>) resultMap.getRecords(), RainStrength.class);
        result = filterRainStrengthListByBscd(result, bscd);
        page.setRecords(result);
        return page;
    }

    @Override
    public AscriptionTypeCount getAscriptionTypeCount(String adcd, String etm, String stm, List<String> stType, List<String> isOut) {
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals(""))) || (isOut != null && (isOut.size() == 0 || isOut.get(0).equals("")))) {
            return new AscriptionTypeCount();
        }
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("adcd", adcd);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        String stTypes = "";
        if (stType != null && stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            param.put("stTypes", stTypes);
        }
//        getOutParam(param,isOut,adcd.substring(0, level));
        //获取登录用户信息 查询域外
        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
        if (org.apache.commons.lang.StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
            String userAd = loginUserInfo.getUserAd();
            int level2 = AdcdUtil.getAdLevel(userAd);
            param.put("userLevel", level2);
            param.put("userAd", userAd);
            param.put("userId", loginUserInfo.getUserId());
        }
        //添加是否查询域外（省级）
        if (isOut != null && isOut.size() == 1) {
            if (isOut.get(0).equals("1")) {
                //查询域内
                param.put("isOut", "1");
            } else {
                //查询域外
                param.put("isOut", "2");
            }
        } else if (isOut != null && isOut.size() == 2) {
            //查询域内域外都查
            param.put("isOut", "3");
            param.put("ad2", adcd.substring(0, level));
        }
        AscriptionTypeCount ascriptionTypeCount = rainDao.getAscriptionTypeCount(param);
        return ascriptionTypeCount;
    }

    @Override
    public AscriptionTypeCount getAscriptionTypeCountNew(String adcd, String bscd, String stType) {
        Map<String, Object> param = new HashMap<>();
        param.put("stType", stType);
        if (StringUtils.isNoneBlank(adcd)) {
            int level = AdcdUtil.getAdLevel(adcd);
            param.put("ad", adcd.substring(0, level));
            param.put("level", level);
            AscriptionTypeCount ascriptionTypeCount = rainDao.getAscriptionTypeCountNewByAdcd(param);
            return ascriptionTypeCount;
        } else {
            param.put("bscd", bscd);
            AscriptionTypeCount ascriptionTypeCount = rainDao.getAscriptionTypeCountNewByBscd(param);
            return ascriptionTypeCount;
        }

    }

    /**
     * 获取树的子节点
     */
    private List<AdAvgRain> getChildren(List<AdAvgRain> list, String pcode) {
        // 通过父级编码子类
        List<AdAvgRain> childList = list.stream().filter(item -> item.getPadcd().equals(pcode)).collect(Collectors.toList());
        return childList;
    }

    /**
     * 把最小级的政区最大雨量测站统计放入map并推算出高级政区的测站统计并存入map
     */
    private Map<String, Object> getMaxRainSt(List<AdAvgRain> listSt, int adLevel) {
        // 通过父级编码子类
        Map<String, Object> maxRainStAd = new HashMap<>();
        //把最小级的政区最大雨量测站统计放入map并推算出高级政区的测站统计并存入map
        listSt.forEach(item -> {
            String adcd = item.getAdcd();
            maxRainStAd.put(adcd, item);
            String cityAdcd = adcd.substring(0, 4) + "00000000000";
            String proAdcd = adcd.substring(0, 2) + "0000000000000";
            if (adLevel == 2 || adLevel == 4) {
                //梅河口市和公主岭市作为地级市来处理 220581，220381 所以这两个县不参与市的最大值判断
                if (adcd.equals(CommConstants.Public.MEIHEKOU_ADCD) || adcd.equals(CommConstants.Public.GONGZHULING_ADCD)) {

                } else {
                    //市级统计最大测站
                    if (maxRainStAd.containsKey(cityAdcd)) {
                        AdAvgRain rain = (AdAvgRain) maxRainStAd.get(cityAdcd);
                        BigDecimal maxRain = new BigDecimal(rain.getMaxRain());
                        BigDecimal maxRain2 = new BigDecimal(item.getMaxRain());
                        //如果后者大于前者，map中置换
                        if (maxRain2.compareTo(maxRain) == 1) {
                            maxRainStAd.put(cityAdcd, item);
                        }
                    } else {
                        maxRainStAd.put(cityAdcd, item);
                    }
                }

            }
            if (adLevel == 2) {
                //省级统计最大测站
                if (maxRainStAd.containsKey(proAdcd)) {
                    AdAvgRain rain = (AdAvgRain) maxRainStAd.get(proAdcd);
                    BigDecimal maxRain = new BigDecimal(rain.getMaxRain());
                    BigDecimal maxRain2 = new BigDecimal(item.getMaxRain());
                    //如果后者大于前者，map中置换
                    if (maxRain2.compareTo(maxRain) == 1) {
                        maxRainStAd.put(proAdcd, item);
                    }
                } else {
                    maxRainStAd.put(proAdcd, item);
                }
            }

        });
        return maxRainStAd;
    }

    /**
     * 将最大雨量测站信息放入政区雨量集合中
     */
    private List<AdAvgRain> getTreeList(List<AdAvgRain> list, Map<String, Object> maxRainStAd) {
        list.forEach(item -> {
            if (maxRainStAd.containsKey(item.getAdcd())) {
                AdAvgRain adAvgRain2 = (AdAvgRain) maxRainStAd.get(item.getAdcd());
                item.setStcd(adAvgRain2.getStcd());
                item.setStnm(adAvgRain2.getStnm());
                //梅河口市和公主岭市作为地级市来处理 220581，220381 改变父级节为省级
                if (item.getAdcd().equals(CommConstants.Public.MEIHEKOU_ADCD) || item.getAdcd().equals(CommConstants.Public.GONGZHULING_ADCD)) {
                    item.setPadcd(SyqConstants.RainConstants.PROVINCEADCD);
                }
            }
        });
        //组成树
        list.forEach(item -> {
            List<AdAvgRain> childList = getChildren(list, item.getAdcd());
            item.setChildren(childList);
        });
        return list;
    }

    @Override
    public IPage<BsnRainAlarm> getRainWarn(String adcd, String bscd, String stnm, String warnType, List<String> stType, String stdt, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals(""))) || (warnType != null && warnType.equals(""))) {
            page.setRecords(new ArrayList<BsnRainAlarm>());
            return page;
        }
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        param.put("adcd", level);
        param.put("stnm", stnm);
        param.put("stType", stType);
        param.put("ad", adcd.substring(0, level));
        param.put("warnType", warnType);
        param.put("stdt", stdt);
        IPage<BsnRainAlarm> list = rainDao.getRainWarn(page, param);

        List<BsnRainAlarm> stList = list.getRecords();
        List<BsnRainAlarm> stList1 = filterRainWarnListByBscd(stList, bscd);
        list.setRecords(stList1);
        return list;
    }

    @Override
    public BsnRainalarmB getBsnRainalarmb() {
        BsnRainalarmB param = rainDao.getBsnRainalarmb();
        return param;
    }

    @Override
    public Boolean updateBsnRainalarmb(String id, Double movedrp1h, Double movedrp3h, Double movedrp6h, Double movedrp12h, Double movedrp24h,
                                       Double warndrp1h, Double warndrp3h, Double warndrp6h, Double warndrp12h, Double warndrp24h) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("movedrp1h", movedrp1h);
        param.put("movedrp3h", movedrp3h);
        param.put("movedrp6h", movedrp6h);
        param.put("movedrp12h", movedrp12h);
        param.put("movedrp24h", movedrp24h);
        param.put("warndrp1h", warndrp1h);
        param.put("warndrp3h", warndrp3h);
        param.put("warndrp6h", warndrp6h);
        param.put("warndrp12h", warndrp12h);
        param.put("warndrp24h", warndrp24h);
        Boolean boo = rainDao.updateBsnRainalarmb(param);
        return boo;
    }


    @Override
    public IPage<HisRain> getRainHisListByPage(String adcd, String stm, String etm, List<String> stType, String tmType, String stnm, String threshold, int pageNum, int pageSize, List<String> isOut) {
        Page page = new Page<>(pageNum, pageSize);
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals(""))) || (isOut != null && (isOut.size() == 0 || isOut.get(0).equals("")))) {
            page.setRecords(new ArrayList<HisRain>());
            return page;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stType", stType);
        String ad = "";

        if (StringUtils.isNoneBlank(adcd)) {
            int level = AdcdUtil.getAdLevel(adcd);
            ad = adcd.substring(0, level);
            param.put("ad", ad);
            param.put("level", level);
//            getOutParam(param,isOut,adcd.substring(0, level));
        }
        param.put("tmType", tmType);
        param.put("stnm", stnm);
        param.put("threshold", threshold);
//        getOutParam(param,isOut,ad);
        //获取登录用户信息 查询域外
        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
        if (org.apache.commons.lang.StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
            String userAd = loginUserInfo.getUserAd();
            int level2 = AdcdUtil.getAdLevel(userAd);
            param.put("userLevel", level2);
            param.put("userAd", userAd);
            param.put("userId", loginUserInfo.getUserId());
        }
//        param.put("userAd","220000000000000");
//        param.put("userLevel",2);
//        param.put("userId",1);
        //添加是否查询域外（省级）
        if (isOut != null && isOut.size() == 1) {
            if (isOut.get(0).equals("1")) {
                //查询域内
                param.put("isOut", "1");
            } else {
                //查询域外
                param.put("isOut", "2");
                param.put("ad", "");
            }
        } else if (isOut != null && isOut.size() == 2) {
            //查询域内域外都查
            param.put("isOut", "3");
            param.put("ad", "");
            param.put("ad2", ad);
        }
        IPage<HisRain> result = null;
        if ("0".equals(tmType)) {
            result = rainDao.getRainHisHourListByPage(page, param);
        } else {
            result = rainDao.getRainHisOtherListByPage(page, param);
        }
        return result;
    }

    @Override
    public BsnBasStBTo getRainfallStatistics(String stTypes, String stm, String etm) {
        // 查询所有流域
        List<BsnBasStBTo> bsnBasStBTos = rainDao.selectRiver();
        // 过滤出一二级流域
        bsnBasStBTos = bsnBasStBTos.stream().filter(item -> item.getBasLevel() < 3).collect(Collectors.toList());
        // 参数赋值
        Map<String, Object> param = new HashMap<>();
        param.put("stTypes", stTypes);
        param.put("stm", stm);
        param.put("etm", etm);
        // 查询一二级流域对应的平均雨量
        List<RainfallStatisticsVo> result = new ArrayList<>();
        RainfallStatisticsVo rainfallStatisticsVo;
        List<RainfallStatisticsVo> avgList;
        List<RainfallStatisticsVo> maxList;
        for (BsnBasStBTo item : bsnBasStBTos) {
            param.put("code", item.getBasCode());
            avgList = rainDao.selectAvgMaxDrp(param);
            if (avgList != null && avgList.size() > 0 && avgList.get(0) != null) {
                item.setAvgDrp(avgList.get(0).getAvgDrp() == null ? "" : avgList.get(0).getAvgDrp());
            }
            maxList = rainDao.selectMaxDrpStnm(param);
            if (maxList.size() > 0) {
                RainfallStatisticsVo rainfallStatisticsVo1 = maxList.get(0);
                item.setStcd(rainfallStatisticsVo1.getStcd() == null ? "" : rainfallStatisticsVo1.getStcd());
                item.setStnm(rainfallStatisticsVo1.getStnm() == null ? "" : rainfallStatisticsVo1.getStnm());
                item.setMaxDrp(rainfallStatisticsVo1.getMaxDrp() == null ? "" : rainfallStatisticsVo1.getMaxDrp());
            }
        }
        BsnBasStBTo rainfallStatisticsVo1 = bsnBasStBTos.stream().max(Comparator.comparingDouble(item -> {
            if (item.getMaxDrp() == null || item.getMaxDrp().equals("")) {
                return 0.0;
            } else {
                return Double.parseDouble(item.getMaxDrp());
            }
        })).get();
//        bsnBasStBTos.forEach(item -> {
//            if (item.getBasName().equals(rainfallStatisticsVo1.getBasName())){
//                item.setMaxFlag(true);
//            }
//        });
        List<BsnBasStBTo> finalBsnBasStBTos = bsnBasStBTos;
        bsnBasStBTos.forEach(item -> {
            if (item.getBasName().equals(rainfallStatisticsVo1.getBasName())) {
                item.setMaxFlag(true);
            }
            if (item.getBasLevel() == 1) {
                List<BsnBasStBTo> childList = getChildrenBas(finalBsnBasStBTos, item.getBasCode());
                if (childList.size() != 0) {
                    item.setChildren(childList);
                }
            }
        });
        List<BsnBasStBTo> finalList = bsnBasStBTos.stream().filter(item -> "".equals(item.getPbasCode())).collect(Collectors.toList());
        BsnBasStBTo bsnBasB = new BsnBasStBTo();
        bsnBasB.setBasName("全部");
        bsnBasB.setBasCode("0");
        bsnBasB.setChildren(finalList);
        // 查询出全部对应的数据
        param.put("code", "");
        List<RainfallStatisticsVo> avgAllList = rainDao.selectAvgMaxDrp(param);
        List<RainfallStatisticsVo> maxAllList = rainDao.selectMaxDrpStnm(param);
        if (avgAllList != null && avgAllList.size() > 0 && avgAllList.get(0) != null) {
            bsnBasB.setAvgDrp(avgAllList.get(0).getAvgDrp() == null ? "" : avgAllList.get(0).getAvgDrp());
        }
        if (maxAllList != null && maxAllList.size() > 0 && maxAllList.get(0) != null) {
            bsnBasB.setMaxDrp(maxAllList.get(0).getMaxDrp() == null ? "" : maxAllList.get(0).getMaxDrp());
            bsnBasB.setStcd(maxAllList.get(0).getStcd() == null ? "" : maxAllList.get(0).getStcd());
            bsnBasB.setStnm(maxAllList.get(0).getStnm() == null ? "" : maxAllList.get(0).getStnm());
        }
        return bsnBasB;
    }

    private List<BsnBasStBTo> getChildrenBas(List<BsnBasStBTo> list, String pcode) {
        // 通过父级编码子类
        List<BsnBasStBTo> childList = list.stream().filter(x -> x.getPbasCode().equals(pcode)).collect(Collectors.toList());
        return childList;
    }

    @Override
    public RainByDutyRecort getRainInfoByDuty(String adcd, String stm, String etm) {
        Map<String, Object> param = new HashMap<>();
        RainByDutyRecort rainByDutyRecort = new RainByDutyRecort();
        int level = 2;
        List<String> stType = new ArrayList<>();
        // 2021年6月11日 姜金阳 指定数据来源 2、山洪 1、水文 3、气象 过滤掉已屏蔽站点
        stType.add("1");
        stType.add("2");
        stType.add("3");
        List<Rain> rainList = getRainByConditionAll(stm, etm, stType, adcd, null, null, "0.1-99999", "1", null, null, null, null);
        rainByDutyRecort.setRainCount(rainList.size());
        if (rainList.size() == 0) {
            rainByDutyRecort.setRainList(rainList);
            return rainByDutyRecort;
        } else {
            Map<String, List<Rain>> rainMap = rainList.stream().collect(Collectors.groupingBy(Rain::getStadtp));
            //水文最大值降雨站信息对象
            Rain swRain = null;
            //气象最大值降雨站信息对象
            Rain qxRain = null;
            //山洪最大值降雨站信息对象
            Rain shRain = null;
            if (rainMap.containsKey("1")) {
                swRain = rainMap.get("1").get(0);
            }
            if (rainMap.containsKey("2")) {
                shRain = rainMap.get("2").get(0);
            }
            if (rainMap.containsKey("3")) {
                qxRain = rainMap.get("3").get(0);
            }
            rainByDutyRecort.setSwMaxRain(swRain);
            rainByDutyRecort.setShMaxRain(shRain);
            rainByDutyRecort.setQxMaxRain(qxRain);
            if (rainList.size() > 10) {
                rainList = rainList.subList(0, 10);
            }

        }

        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("zero", adcd.substring(level));
        param.put("level2", level);
        //省级雨量平均值
        List<AdAvgRain> listProvince = rainDao.getRainAdAvg(param);
        param.put("level2", level + 2);
        param.put("zero", adcd.substring(level + 2));
        //梅河口市和公主岭市作为地级市来处理 220581，220381 市平均值去掉
        param.put("exceptFlag", true);
        //市级雨量平均值
        List<AdAvgRain> listCity = rainDao.getRainAdAvg(param);
        param.remove("exceptFlag");
        //县平均雨量值 begin
        param.put("level2", level + 4);
        param.put("zero", adcd.substring(level + 4));
        List<AdAvgRain> listCounty = rainDao.getRainAdAvg(param);
        Map<String, List<AdAvgRain>> map = listCounty.stream().collect(Collectors.groupingBy(AdAvgRain::getAdcd));
        List<AdAvgRain> mhkList = map.get(CommConstants.Public.MEIHEKOU_ADCD);
        List<AdAvgRain> gzlList = map.get(CommConstants.Public.GONGZHULING_ADCD);
        if (gzlList != null) {
            listCity.addAll(gzlList);
        }
        if (mhkList != null) {
            listCity.addAll(mhkList);
        }
        listCounty = listCounty.stream().sorted((u1, u2) -> Double.valueOf(u2.getAvgRain()).compareTo(Double.valueOf(u1.getAvgRain()))).collect(Collectors.toList());
        String provinceAvgRain = "0";
        if (listProvince != null && listProvince.get(0) != null && listProvince.get(0).getAvgRain() != null) {
            provinceAvgRain = listProvince.get(0).getAvgRain();
        }

        List<AdAvgRain> listCurrentAvg = getAvgRainTreeByTmNew(stm, etm, stType, adcd, "1");
        String avgRain = "0";
        if (listCurrentAvg != null && listCurrentAvg.get(0) != null && listCurrentAvg.get(0).getAvgRain() != null) {
            avgRain = listCurrentAvg.get(0).getAvgRain();
        }

        rainByDutyRecort.setProvinceAvgRain(provinceAvgRain);
        rainByDutyRecort.setCityAvgRain(listCity);
        rainByDutyRecort.setAvgRain(avgRain);
        rainByDutyRecort.setRainList(rainList);
        return rainByDutyRecort;
    }

    @Override
    public RainByYwmh getRainInfoByYwmh(String adcd, String stm, String etm) {
        //查询所有雨量超警
        RainByDutyRecort rainByDutyRecort = getRainInfoByDuty(adcd, stm, etm);
        RainByYwmh rainByYwmh = new RainByYwmh();
        rainByYwmh.setRainList(rainByDutyRecort.getRainList());
        rainByYwmh.setCityAvgRain(rainByDutyRecort.getCityAvgRain());
        rainByYwmh.setProvinceAvgRain(rainByDutyRecort.getProvinceAvgRain());
        rainByYwmh.setAvgRain(rainByDutyRecort.getAvgRain());
        rainByYwmh.setRainCount(rainByDutyRecort.getRainCount());
        rainByYwmh.setSwMaxRain(rainByDutyRecort.getSwMaxRain());
        rainByYwmh.setShMaxRain(rainByDutyRecort.getShMaxRain());
        rainByYwmh.setQxMaxRain(rainByDutyRecort.getQxMaxRain());
        IPage<BsnRainAlarm> iPage = getRainWarn(adcd, "", "", "", null, null, 1, -1);
        List<BsnRainAlarm> rainAlarmList = iPage.getRecords();
        Map<String, Long> rainMap = rainAlarmList.stream().collect(Collectors.groupingBy(BsnRainAlarm::getAlarmgradeid, Collectors.counting()));
        //雨量超警赋值
        rainByYwmh.setYlTotal(rainAlarmList.size());
        //超警
        rainByYwmh.setYlCjTotal(rainMap.get("1") != null ? rainMap.get("1").intValue() : 0);
        //超危险
        rainByYwmh.setYlCwTotal(rainMap.get("2") != null ? rainMap.get("2").intValue() : 0);
        return rainByYwmh;
    }

    @Override
    public void exportRainByCondition(int type, String stm, String etm, List<String> stType, String adcd, String bscd, String stnm, String threshold, String rainShowType, List<String> isOut) {
        IPage<Rain> rainIPage = getRainByCondition(stm, etm, stType, adcd, bscd, stnm, threshold, rainShowType, 0, -1, isOut, null, null, null);
        List<Rain> list = rainIPage.getRecords();
        //导出
        if (type == 1) {
            list.stream().forEach(item -> {
                String adnm = "";
                if (org.apache.commons.lang.StringUtils.isBlank(item.getXzadnm())) {
                    adnm = item.getXadnm();
                } else {
                    if (item.getXzadnm().equals(item.getXadnm())) {
                        adnm = item.getXadnm();
                    } else {
                        adnm = item.getXadnm() + " - " + item.getXzadnm();
                    }
                }
                item.setAdnm(adnm);
            });
            LocalDateTime etmB24 = null;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");
            DateTimeFormatter formatterWithoutSeconds = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00");
            List<StPptnF> forecast2HourList = new ArrayList<>();
            HashMap<String, String> hiddenCollumMap = new HashMap();
            if (etm != null) {
                try {
                    etmB24 = LocalDateTime.parse(etm, formatter);
                } catch (Exception e) {
                    // 如果解析失败，尝试使用不包含秒的格式解析
                    etmB24 = LocalDateTime.parse(etm, formatterWithoutSeconds);
                }
                long hoursDiff = Duration.between(etmB24, LocalDateTime.now()).toHours();
                if (!(Math.abs(hoursDiff) < 1.1)) {
                    hiddenCollumMap.put("未来3h（mm）", "rain3");
                }
            }
            ExcelExportUtil.executeWithHiddenColumn(list, "综合监视测站降雨", hiddenCollumMap);
        } else if (type == 2) {
            ExcelExportUtil.execute(list, "雨情查询累计雨量");
        } else if (type == 3) {
            ExcelExportUtil.execute(list, "综合监视预报降雨");
        } else if (type == 4) {
            ExcelExportUtil.execute(list, "气象信息预报降水");
        }
    }

    @Override
    public void exportRainTmList(String stm, String etm, String stcd, String type) {
        List<StPptnR> list = getRainListByTm(stm, etm, stcd, type);
        ExcelExportUtil.execute(list, "单站时段雨量列表");
    }

    @Override
    public void exportRainStrength(String stm, String etm, List<String> stType, int intv, String adcd, String bscd, String stnm) {
        IPage<RainStrength> strengthIPage = getRainStrength(stm, etm, stType, intv, adcd, bscd, stnm, 0, -1);
        List<RainStrength> list = strengthIPage.getRecords();
        ExcelExportUtil.execute(list, "降雨强度统计");
    }

    @Override
    public void exportRainHisList(String adcd, String stm, String etm, List<String> stType, String tmType, String stnm, String threshold, List<String> isOut) {
        int level = AdcdUtil.getAdLevel(adcd);
        IPage<HisRain> rainIPage = getRainHisListByPage(adcd, stm, etm, stType, tmType, stnm, threshold, 0, -1, isOut);
        List<HisRain> list = rainIPage.getRecords();
        if (level == 2) {
            ExcelExportUtil.execute(list, "历史雨量信息(省)");
        } else if (level == 4) {
            ExcelExportUtil.execute(list, "历史雨量信息(市)");
        } else if (level == 6) {
            ExcelExportUtil.execute(list, "历史雨量信息(县)");
        }
    }

    @Override
    public void exportRainfallStatistics(String stTypes, String stm, String etm) {
        BsnBasStBTo list = getRainfallStatistics(stTypes, stm, etm);
        ExcelExportUtil.execute(list.getChildren(), "流域降雨统计");
    }

    @Override
    public List<RsvrRainStatisticsVo> getRsvrRainStatistics(String stTypes, String stm, String etm) {
        Map<String, Object> param = new HashMap<>();
        param.put("stTypes", stTypes);
        param.put("stm", stm);
        param.put("etm", etm);

        //根据大型水库分组获取平均水量
        List<RsvrRainStatisticsVo> avgList = rainDao.selectResAvgDrp(param);
        //根据大型水库最大降雨的测站
        List<RsvrRainStatisticsVo> maxList = rainDao.selectResMaxDrp(param);
        Map<String, RsvrRainStatisticsVo> maxMap = maxList.stream().collect(Collectors.toMap(RsvrRainStatisticsVo::getResCode, Function.identity()));
        List<RsvrRainStatisticsVo> result = new ArrayList<>();
        avgList.forEach(x -> {
            RsvrRainStatisticsVo resRainStatisticsVo = maxMap.get(x.getResCode());

            if (resRainStatisticsVo != null) {
                if (maxList.get(0).getResCode().equals(resRainStatisticsVo.getResCode())) {
                    resRainStatisticsVo.setMaxFlag(true);
                } else {
                    resRainStatisticsVo.setMaxFlag(false);
                }
                resRainStatisticsVo.setAvgDrp(x.getAvgDrp());
                resRainStatisticsVo.setResName(x.getResName());
                resRainStatisticsVo.setLowLeftLat(x.getLowLeftLat());
                resRainStatisticsVo.setLowLeftLong(x.getLowLeftLong());
                resRainStatisticsVo.setUpRightLat(x.getUpRightLat());
                resRainStatisticsVo.setUpRightLong(x.getUpRightLong());
                result.add(resRainStatisticsVo);
            }
        });
        return result;
    }

    @Override
    public List<BsnTimesR> getRainTimes(String stm, String etm, String adcd) {
        List<BsnTimesR> list = rainDao.selectRainTimes(stm, etm, adcd);
        return list;
    }

    @Override
    public IPage<TmsRainVo> getTmsRainByCondition(String tmsid, String tmsad, List<String> stType, String adcd, String bsnm, String stnm, String threshold, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        if (StringUtils.isNoneBlank(threshold)) {
            String[] items = threshold.split("-");
            Map<String, Object> map1 = new HashMap<>();
            param.put("min", items[0]);
            if (items.length > 1) {
                param.put("max", items[1]);
            }
        }
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            param.put("stTypes", stTypes);
        }

        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        param.put("bsnm", bsnm);
        param.put("stnm", stnm);
        param.put("tmsad", tmsad);
        param.put("tmsid", tmsid);
        IPage<TmsRainVo> ipage = rainDao.selectTmsRainByCondition(page, param);
        return ipage;
    }

    @Override
    public void exportRainCountByRainTms(String tmsid, String tmsad, List<String> stType, String adcd, String bsnm, String stnm, String threshold, int pageNum, int pageSize) {
        IPage<TmsRainVo> result = getTmsRainByCondition(tmsid, tmsad, stType, adcd, bsnm, stnm, threshold, 0, -1);
        List<TmsRainVo> list = result.getRecords();
        ExcelExportUtil.execute(list, "场次降雨测站降雨");
    }

    @Override
    public void exportRsvrStatistics(String stTypes, String stm, String etm) {
        List<RsvrRainStatisticsVo> list = getRsvrRainStatistics(stTypes, stm, etm);
        ExcelExportUtil.execute(list, "大型水库降雨");
    }

    @Override
    public SmsDesInfo getSmsInfo(String stm, String etm, String adcd) {
        SmsDesInfo result = new SmsDesInfo();
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        //查询时间段内累计降雨测站统计
        List<String> stType = new ArrayList<>();
        stType.add("1");
        stType.add("2");
        stType.add("3");
        stType.add("4");
        stType.add("5");
        List<Rain> rainList = getRainByConditionAll(stm, etm, stType, adcd, null, null, null, "1", null, null, null, null);
        if (rainList.size() == 0) {
            return result;
        }
        if (rainList.size() > 5) {
            rainList = rainList.subList(0, 10);
        }
        result.setStRain(rainList);
        if (level == 2) {
            param.put("zero", adcd.substring(level));
            param.put("level2", level);
            //省级雨量平均值
            List<AdAvgRain> listProvince = rainDao.getRainAdAvg(param);
            param.put("level2", level + 2);
            param.put("zero", adcd.substring(level + 2));
            //梅河口市和公主岭市作为地级市来处理 220581，220381 市平均值去掉
            param.put("exceptFlag", true);
            //市级雨量平均值
            List<AdAvgRain> listCity = rainDao.getRainAdAvg(param);
            param.remove("exceptFlag");
            //县平均雨量值 begin
            param.put("level2", level + 4);
            param.put("zero", adcd.substring(level + 4));
            List<AdAvgRain> listCounty = rainDao.getRainAdAvg(param);
            List<AdAvgRain> listCounty2 = new ArrayList<>();
            for (AdAvgRain item : listCounty) {
                if (!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd()) && !CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())) {
                    //市本级不展示在政区树上
                    if (SyqConstants.RainConstants.PROVINCEADCD.equals(item.getPadcd())) {
                    } else {
                        listCounty2.add(item);
                    }
                } else {
                    listCity.add(item);
                }
            }
            listCounty2 = listCounty2.stream().sorted((u1, u2) -> Double.valueOf(u2.getAvgRain()).compareTo(Double.valueOf(u1.getAvgRain()))).collect(Collectors.toList());
            listCity = listCity.stream().sorted((u1, u2) -> Double.valueOf(u2.getAvgRain()).compareTo(Double.valueOf(u1.getAvgRain()))).collect(Collectors.toList());
            String provinceAvgRain = "0";
            if (listProvince != null && listProvince.get(0) != null && listProvince.get(0).getAvgRain() != null) {
                provinceAvgRain = listProvince.get(0).getAvgRain();
            }
            if (listCounty2.size() > 20) {
                listCounty2 = listCounty2.subList(0, 20);
            }
            result.setAdavg(provinceAvgRain);
            result.setLowAvg(listCity);
            result.setXianAvg(listCounty2);
        } else if (level == 4) {
            param.put("zero", adcd.substring(level));
            param.put("level2", level);
            //市级雨量平均值
            List<AdAvgRain> listProvince = rainDao.getRainAdAvg(param);
            //县平均雨量值 begin
            param.put("level2", level + 4);
            param.put("zero", adcd.substring(level + 4));
            List<AdAvgRain> listCounty = rainDao.getRainAdAvg(param);
            List<AdAvgRain> listCounty2 = new ArrayList<>();
            for (AdAvgRain item : listCounty) {
                if (!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd()) && !CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())) {
                    //市本级不展示在政区树上
                    if (SyqConstants.RainConstants.PROVINCEADCD.equals(item.getPadcd())) {
                    } else {
                        listCounty2.add(item);
                    }
                }
            }
            listCounty2 = listCounty2.stream().sorted((u1, u2) -> Double.valueOf(u2.getAvgRain()).compareTo(Double.valueOf(u1.getAvgRain()))).collect(Collectors.toList());
            String provinceAvgRain = "0";
            if (listProvince != null && listProvince.get(0) != null && listProvince.get(0).getAvgRain() != null) {
                provinceAvgRain = listProvince.get(0).getAvgRain();
            }
            if (listCounty.size() > 20) {
                listCounty = listCounty.subList(0, 20);
            }
            result.setAdavg(provinceAvgRain);
            result.setLowAvg(listCounty2);
            result.setXianAvg(listCounty2);
        } else if (level == 6) {
            param.put("zero", adcd.substring(level));
            param.put("level2", level);
            //县级雨量平均值
            List<AdAvgRain> listProvince = rainDao.getRainAdAvg(param);
            String provinceAvgRain = "0";
            if (listProvince != null && listProvince.get(0) != null && listProvince.get(0).getAvgRain() != null) {
                provinceAvgRain = listProvince.get(0).getAvgRain();
            }
            result.setAdavg(provinceAvgRain);
        }
        return result;
    }

    @Override
    public IPage<Rain> getRainByConditionForSqfx(String stm, String etm, String adcd, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        IPage<Rain> resultPage = rainDao.getRainStInfoSq(page, param);
        return resultPage;
    }

    @Override
    public void exportRainByConditionForSqfx(String stm, String etm, String adcd) {
        IPage<Rain> rainIPage = getRainByConditionForSqfx(stm, etm, adcd, 0, -1);
        List<Rain> list = rainIPage.getRecords();
        ExcelExportUtil.execute(list, "雨量统计");
    }

    public void setCellValue(XSSFSheet sheet, XSSFCellStyle style, int rownum, int columnIndex, String value) {
        XSSFRow row1 = sheet.getRow(rownum);
        if (row1 == null) {
            row1 = sheet.createRow(rownum);
        }
        XSSFCell cell = row1.createCell(columnIndex);
        cell.setCellStyle(style);
        cell.setCellValue(value);
    }

    @Override
    public IPage<OneDayRain> getRainListTmMerge(String date, String adcd, String stType, String stnm, String admauth, int pageNum, int pageSize) throws Exception {
        Page page = new Page<>(pageNum, pageSize);
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if (stType != null && (stType.equals(""))) {
            page.setRecords(new ArrayList<OneDayRain>());
            return page;
        }
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        String stm = date + " 08:00:00";
        String etm = DateFormatUtil.beforeOrAfterSpecialDateToString(1, "yyyy-MM-dd HH:mm:ss", stm);
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("stTypes", stType);
        param.put("stnm", stnm);
        param.put("admauth", admauth);
        IPage<OneDayRain> iPage = rainDao.getRainListTmMerge(page, param);
        return iPage;
    }

    @Override
    public IPage<OneDayRain> getRainListTmMergeForSqfx(String date, String adcd, int pageNum, int pageSize) throws Exception {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        String nowymd = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDateTime.now());
        // 当前小时
        int nhour = LocalDateTime.now().getHour();
        String stm = date + " 08:00:00";
        String etm = DateFormatUtil.beforeOrAfterSpecialDateToString(1, "yyyy-MM-dd HH:mm:ss", stm);
        if (nowymd.equals(date) && nhour < 9) {
            etm = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00").format(LocalDateTime.now());
            stm = DateFormatUtil.beforeOrAfterSpecialDateToString(-1, "yyyy-MM-dd HH:mm:ss", etm);
        }
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        IPage<OneDayRain> iPage = rainDao.getRainListTmMergeForSqfx(page, param);
        if (nowymd.equals(date)) {
            List<OneDayRain> list = iPage.getRecords();
            List<String> excelProperties = Arrays.asList(new String[]{"drp9", "drp10", "drp11", "drp12", "drp13", "drp14", "drp15", "drp16", "drp17", "drp18", "drp19", "drp20", "drp21", "drp22", "drp23", "drp0", "drp1", "drp2", "drp3", "drp4", "drp5", "drp6", "drp7", "drp8"});
            for (OneDayRain item : list) {
                for (String property : excelProperties) {
                    int phour = Integer.parseInt(property.replace("drp", ""));
                    if (nhour >= 9) {
                        if (phour > nhour || phour < 9) {
                            PropertyUtils.setProperty(item, property, "-");
                        }
                    } else {
                        if (phour > nhour && phour < 9) {
                            PropertyUtils.setProperty(item, property, "-");
                        }
                    }
                    Object property1 = PropertyUtils.getProperty(item, property);
                }
            }
        }

        return iPage;
    }

    @Override
    public void exportRainListTmMerge(String date, String adcd, String stType, String stnm, String admauth) {
        try {
            IPage<OneDayRain> iPage = this.getRainListTmMerge(date, adcd, stType, stnm, admauth, 1, -1);
            List<OneDayRain> list = iPage.getRecords();
            ExcelExportUtil.execute(list, "逐站24小时降雨信息");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void exportRainListTmMergeForSqfx(String date, String adcd) {
        try {
            IPage<OneDayRain> iPage = this.getRainListTmMergeForSqfx(date, adcd, 1, -1);
            List<OneDayRain> list = iPage.getRecords();
//            ExcelExportUtil.execute(list,"逐站24小时降雨信息");
            //创建XSSF工作薄
            XSSFWorkbook writeWorkbook = new XSSFWorkbook();
            //创建一个Sheet页
            XSSFSheet sheet = writeWorkbook.createSheet();
            // 设置数据字体样式
            XSSFFont font = writeWorkbook.createFont();
            font.setFontHeightInPoints((short) 10);
//            font.setFontName("黑体");

            // 设置绿圆样式
            XSSFFont font1 = writeWorkbook.createFont();
            font1.setFontHeight(14);
            font1.setColor(new XSSFColor(Color.decode("#7CFC00")));
            // 设置红圆样式
            XSSFFont font2 = writeWorkbook.createFont();
            font2.setFontHeight(14);
            font2.setColor(XSSFFont.COLOR_RED); //字体颜色
            // 设置数据单元格样式
            XSSFCellStyle style = writeWorkbook.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
//            style.setWrapText(true);
            style.setFont(font);
            // 设置绿圆单元格样式
            XSSFCellStyle style2 = writeWorkbook.createCellStyle();
            style2.setAlignment(HorizontalAlignment.CENTER);
            style2.setVerticalAlignment(VerticalAlignment.CENTER);
//            style.setWrapText(true);
            style2.setFont(font1);
            // 设置红圆单元格样式
            XSSFCellStyle style3 = writeWorkbook.createCellStyle();
            style3.setAlignment(HorizontalAlignment.CENTER);
            style3.setVerticalAlignment(VerticalAlignment.CENTER);
//            style.setWrapText(true);
            style3.setFont(font2);
            // 设置表头单元格样式
            XSSFCellStyle style1 = writeWorkbook.createCellStyle();
            style1.setAlignment(HorizontalAlignment.CENTER);
            style1.setVerticalAlignment(VerticalAlignment.CENTER);
//            style1.setWrapText(true);
//            style1.setFont(font);
            style1.setBorderBottom(BorderStyle.MEDIUM);
            style1.setBorderLeft(BorderStyle.MEDIUM);
            style1.setBorderRight(BorderStyle.MEDIUM);
            style1.setBorderTop(BorderStyle.MEDIUM);
            Object[][] headerArr = {
                    {0, 0, "序号"}, {0, 1, "政区"}, {0, 2, "测站名称"}, {0, 3, "测站编码"},
                    {0, 4, "9"}, {0, 5, "10"}, {0, 6, "11"}, {0, 7, "12"},
                    {0, 8, "13"}, {0, 9, "14"}, {0, 10, "15"}, {0, 11, "16"}, {0, 12, "17"},
                    {0, 13, "18"}, {0, 14, "19"}, {0, 15, "20"}, {0, 16, "21"}, {0, 17, "22"},
                    {0, 18, "23"}, {0, 19, "0"}, {0, 20, "1"}, {0, 21, "2"}, {0, 22, "3"},
                    {0, 23, "4"}, {0, 24, "5"}, {0, 25, "6"}, {0, 26, "7"}, {0, 27, "8"},
            };
            for (int i = 0; i < headerArr.length; i++) {
                Object[] a1 = headerArr[i];
                setCellValue(sheet, style1, Integer.parseInt(a1[0].toString()), Integer.parseInt(a1[1].toString()), a1[2].toString());
            }
            List<String> excelProperties = Arrays.asList(new String[]{"xadnm", "stnm", "comments", "drp9", "drp10", "drp11", "drp12", "drp13", "drp14", "drp15", "drp16", "drp17", "drp18", "drp19", "drp20", "drp21", "drp22", "drp23", "drp0", "drp1", "drp2", "drp3", "drp4", "drp5", "drp6", "drp7", "drp8"});
            for (int i = 0; i < list.size(); i++) {
                OneDayRain bnsRsvrpersonBVo = list.get(i);
                for (int j = 0; j < excelProperties.size() + 1; j++) {
                    if (j == 0) {
                        setCellValue(sheet, style, i + 1, 0, (i + 1) + "");
                        continue;
                    }
                    String property = excelProperties.get(j - 1);
                    Object property1 = PropertyUtils.getProperty(bnsRsvrpersonBVo, property);

                    if (property.indexOf("drp") != -1) {
                        if (property1 == null || property1.toString().equals("")) {
                            setCellValue(sheet, style3, i + 1, j, "●");
                            continue;
                        } else if (property1.toString().equals("-")) {
                            setCellValue(sheet, style, i + 1, j, "-");
                        } else {
                            setCellValue(sheet, style2, i + 1, j, "●");
                            continue;
                        }
                    } else {
                        if (property1 == null) {
                            setCellValue(sheet, style, i + 1, j, "");
                            continue;
                        } else {
                            setCellValue(sheet, style, i + 1, j, property1.toString());
                        }
                    }
                }
            }
            File file0 = new File("ee.xlsx");
            FileOutputStream outputStream = new FileOutputStream(file0);
            //将Excel写入输出流中
            writeWorkbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
            ExcelExportUtil.excleDownload("ee.xlsx", "测站预警", null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public IPage<HeavyRainfallAlarm> getHeavyRainfallAlarm(String adcd, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        IPage<HeavyRainfallAlarm> result = rainDao.getHeavyRainfallAlarm(page, param);

        /**
         * 2021-09-01 用户要求：使用模板一和模板二动态显示
         * 1. 是否存在[2]（已使用模板二）如果存在则不显示该市县
         * 2. 总数<5 （发送时拼接[1]）
         *    1. 存在[1]（已发第一次，需要在拼接站点字符串时排查存在[1]的站点）
         *    2. 不存在[1]（一次都没发，全部展示）
         * 3. 总数>5（发送时拼接[2]）
         *    1. 存在[1]（已发第一次，需要全部展示）
         *    2. 不存在[1]（在总数<5时没发，再次发送时直接拼[2]）
         */
        List<HeavyRainfallAlarm> alarms = result.getRecords();
        List<String> collect = alarms.stream().filter(item -> item.getReamrk().contains("[2]")).map(item -> item.getAdcd()).distinct().collect(Collectors.toList());
        List<HeavyRainfallAlarm> collect1 = alarms.stream().filter(item -> !collect.contains(item.getAdcd())).collect(Collectors.toList());
        Map<String, List<HeavyRainfallAlarm>> collect2 = collect1.stream().collect(Collectors.groupingBy(HeavyRainfallAlarm::getAdcd));
        List<HeavyRainfallAlarm> collect3 = new ArrayList<>();
        collect2.entrySet().iterator().forEachRemaining(item -> {
            if (item.getValue().size() < 5) {
                collect3.addAll(item.getValue().stream().filter(item1 -> !item1.getReamrk().contains("[1]")).collect(Collectors.toList()));
            } else {
                collect3.addAll(item.getValue());
            }
        });
        Date tm = new Date();
        String tm2 = DateFormatUtil.formatDateToString(tm, "MM月dd日HH时mm分");
        List<HeavyRainfallAlarm> list = new ArrayList<>();
        Map<String, HeavyRainfallAlarm> map = new HashMap<>();
        collect3.forEach(item -> {
            if (map.containsKey(item.getAdcd())) {
                HeavyRainfallAlarm alarm = map.get(item.getAdcd());
                alarm.setAlarmdesc(alarm.getAlarmdesc() + "、" + item.getAlarmdesc());
                alarm.getStcds().add(item.getStcd());
                map.put(item.getAdcd(), alarm);
            } else {
                HeavyRainfallAlarm alarm = new HeavyRainfallAlarm();
                alarm.setStadtp(item.getStadtp());
                alarm.setAdcd(item.getAdcd());
                alarm.setAdnm(item.getAdnm());
                alarm.setSadnm(item.getSadnm());
//                alarm.setAlarmdesc("截止" + tm2 + "，" + item.getAdnm() + item.getAlarmdesc());
                alarm.setAlarmdesc("截止" + tm2 + "，" + item.getAlarmdesc());
                alarm.getStcds().add(item.getStcd());
                map.put(item.getAdcd(), alarm);
            }
        });
        for (Map.Entry<String, HeavyRainfallAlarm> entry : map.entrySet()) {
            HeavyRainfallAlarm alarm = entry.getValue();
            if (StringUtils.isNotEmpty(alarm.getStadtp())) {
                String stadtp = alarm.getStadtp().trim();
                if ("1".equals(stadtp)) {
                    alarm.setAlarmdesc(alarm.getAlarmdesc() + "，同时还有多站点降雨量接近50毫米，预计降雨还将持续，省水利厅提醒你局加强山洪灾害防御，及时预警并提请政府及有关部门，适时转移受洪水威胁群众，确保人民生命安全。");
                } else {
                    alarm.setAlarmdesc(alarm.getAlarmdesc() + "，同时还有多站点降雨量接近50毫米，预计降雨还将持续，省水利厅提醒你局持续关注降雨实况，做好防汛工作。");
                }
            }
            list.add(alarm);
        }
        // 按照政区排序
        List<HeavyRainfallAlarm> collect4 = list.stream().sorted(Comparator.comparing(HeavyRainfallAlarm::getAdcd)).collect(Collectors.toList());
        result.setRecords(collect4);
        return result;
    }

    @Override
    public IPage<RainTimeIntervalInfo> getTimeInterval(String adcd, List<String> stType, String tm, String stnm, String sort, int pageNum, int pageSize) {
        Page page = new Page(pageNum, pageSize);
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if (stType != null && (stType.size() == 0 || stType.get(0).equals(""))) {
            page.setRecords(new ArrayList<RainTimeIntervalInfo>());
            return page;
        }
        // sort防注入
        if (StringUtils.isNotEmpty(sort)
                && sort.toLowerCase().replace("desc", "").replace("asc", "").trim().length() > 8) {
            page.setRecords(new ArrayList<RainTimeIntervalInfo>());
            return page;
        }
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        param.put("stnm", stnm);
        param.put("tm", tm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            param.put("stTypes", stTypes);
        }
        param.put("sort", sort);
        long startTime = System.currentTimeMillis();
        IPage<RainTimeIntervalInfo> iPage = rainDao.getTimeIntervalNew(page, param);
        long endTime = System.currentTimeMillis();
        logger.info("最近24小时累计降雨统计执行时间：" + (endTime - startTime) + "毫秒");
        return iPage;
    }

    @Override
    public void exportTimeInterval(QueryRainTimeInterval query, OutputStream output) {
        IPage<RainTimeIntervalInfo> page = getTimeInterval(query.getAdcd(), query.getStType(), query.getTm(), query.getStnm(), query.getSort(), 1, -1);
        for (int i = 0; i < page.getRecords().size(); i++) {
            // 序号
            page.getRecords().get(i).setSortno(i + 1);
        }
        EasyExcel.write(output, RainTimeIntervalInfo.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("时间维度累计降雨信息")
                .doWrite(page.getRecords());
    }

    @Override
    public List<RainTimeIntervalInfo> getTimeIntervalList(String adcd, List<String> stType, String tm, String stnm, String sort) {
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        param.put("stnm", stnm);
        param.put("tm", tm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            param.put("stTypes", stTypes);
        }
        param.put("sort", sort);
        long startTime = System.currentTimeMillis();
        List<RainTimeIntervalInfo> iPage = rainDao.getTimeIntervalList(param);
        long endTime = System.currentTimeMillis();
        logger.info("最近24小时累计降雨统计执行时间：" + (endTime - startTime) + "毫秒");
        return iPage;
    }

    @Override
    public void exportAvgRainTmTreeNew(String stm, String etm, List<String> stType, String adcd) {
        List<AdAvgRain> list = getAvgRainTreeByTmNew(stm, etm, stType, adcd, "0");
        list.forEach(item -> {
            item.setDrpstnm(item.getMaxRain() + "(" + item.getStnm() + ")");
        });
        ExcelExportUtil.execute(list, "政区平均降雨");
    }

    @Override
    public IPage<MoistureMeteorologyVo> getMoistureForSqfx(String stm, String etm, String query, int pageNum, int pageSize) {
        Page page = new Page(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("query", query);
        param.put("stm", stm);
        param.put("etm", etm);
        IPage<MoistureMeteorologyVo> iPage = rainDao.getMoistureForSqfx(page, param);
        iPage.getRecords().forEach(item -> {
            BigDecimal windSpeed = item.getWindSpeed().setScale(1, BigDecimal.ROUND_HALF_UP);
            item.setWindSpeed(windSpeed);
        });
        return iPage;
    }

    @Override
    public void exportMoistureForSqfx(String stm, String etm, String query, int pageNum, int pageSize) {
        IPage<MoistureMeteorologyVo> page = getMoistureForSqfx(stm, etm, query, pageNum, pageSize);
        List<MoistureMeteorologyVo> list = page.getRecords();
        ExcelExportUtil.execute(list, "墒情气象");
    }

    @Override
    public List<BasRainSummary> getBasRainSummaryList(String basCode, String stTypes, String stm, String etm, String isOut) {
        if (StringUtils.isBlank(isOut)) {
            return new ArrayList<>();
        }
        Map<String, Object> params = new HashMap<>();
        if (null != basCode && !"0".equals(basCode)) {
            params.put("basCode", basCode);
        } else {
            params.put("basCode", "");
        }
        if (null != stTypes) {
            params.put("stTypes", stTypes);
        } else {
            params.put("stTypes", "1,2,3");
        }
        params.put("stm", stm);
        params.put("etm", etm);
        params.put("isOut", isOut);
        List<BasRainSummary> list = rainDao.getBasRainSummaryList(params);
        if (!list.isEmpty()) {
            Map<String, BasRainSummary> map = new HashMap<>();
            list.forEach(item -> map.put(item.getBasCode(), item));

            List<BasRainSummary> result = new ArrayList<>();
            list.forEach(item -> {
                // 流域汇总仅保留主要江河
                if (!(StringUtils.isEmpty(item.getPbasCode()) || "0".equals(item.getPbasCode()) || "1".equals(item.getIsZyjh()))) {
                    return;
                }

                if (StringUtils.isBlank(item.getPbasCode()) || !map.containsKey(item.getPbasCode())) {
                    result.add(item);
                } else {
                    BasRainSummary parent = map.get(item.getPbasCode());
                    if (null == parent.getChildren()) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(item);
                }
            });
            return result;
        }
        return new ArrayList<>();
    }


    private Map<String, Object> getOutParam(Map<String, Object> param, List<String> isOut, String ad) {
        //辖区内与辖区外
        if (isOut != null && isOut.size() > 0) {
            //登录用户信息
            LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
            if (StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
                String userAd = loginUserInfo.getUserAd();
                int userAdLevl = AdcdUtil.getAdLevel(loginUserInfo.getUserAd());
                String userAdSub = userAd.substring(0, userAdLevl);
                if (isOut.size() == 1) {
                    String isOutValue = isOut.get(0);
                    //辖区外
                    if (isOutValue.equals("2")) {
                        //将政区设为空
                        param.put("ad", "");
                        param.put("userAdSub", userAdSub);
                        param.put("userAdLevl", userAdLevl);
                        //仅查询数据辖区外
                        param.put("dataQrType", "1");
                    }
                } else {
                    //辖区内与辖区外都查询
                    //将政区设为空

                    param.put("ad", "");
                    param.put("qrAd", ad);
                    param.put("userAdSub", userAdSub);
                    param.put("userAdLevl", userAdLevl);
                    //辖区内与辖区外都查询
                    param.put("dataQrType", "2");
                }
                param.put("userId", loginUserInfo.getUserId());
            }

        }
        return param;
    }

    @Override
    public List<Map<String, String>> getStcdContrastBytm(String stcd, String tp, String tms) {
        List<Map<String, String>> listMap = new ArrayList<>();

        List<String> tmList = Arrays.asList(tms.split(","));
        List<StPstatR> list = rainDao.getStcdContrastBytm(stcd, tp, tmList);

        // 年
        if (tp.equals("1")) {
            Map<String, String> tmValueMap = list.stream().collect(Collectors.toMap(item -> item.getIdtm().getYear() + "" + item.getIdtm().getMonthValue(), item -> item.getAccp().toString()));

            for (int i = 1; i <= 12; i++) {
                HashMap<String, String> map = new HashMap<>();
                map.put("tm", i + "月");
                for (int j = 0; j < tmList.size(); j++) {
                    map.put(tmList.get(j), tmValueMap.get(tmList.get(j) + "" + i));
                }
                listMap.add(map);
            }
        } else if (tp.equals("2")) {
            // 月
            Map<String, String> tmValueMap = list.stream().collect(Collectors.toMap(item -> item.getIdtm().format(DateTimeFormatter.ofPattern("yyyyMM")) + item.getIdtm().getDayOfMonth(), item -> item.getAccp().toString()));

            for (int i = 1; i <= 31; i++) {
                HashMap<String, String> map = new HashMap<>();
                map.put("tm", i + "日");
                for (int j = 0; j < tmList.size(); j++) {
                    map.put(tmList.get(j), tmValueMap.get(tmList.get(j) + "" + i));
                }
                listMap.add(map);
            }
        } else {
            return null;
        }
        return listMap;
    }

    @Override
    public List<Map<String, String>> getAdcdContrastBytm(String adcd, String tp, String tms) {

        List<Map<String, String>> listMap = new ArrayList<>();

        List<String> tmList = Arrays.asList(tms.split(","));

        int adLevl = AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0, adLevl);

        // 年
        if (tp.equals("1")) {
            List<StPstatR> list = rainDao.getAdcdContrastBytmYear(ad, tmList);
            Map<String, String> tmValueMap = list.stream().collect(Collectors.toMap(item -> item.getIdtm().getYear() + "" + item.getIdtm().getMonthValue(), item -> item.getAccp().toString()));

            for (int i = 1; i <= 12; i++) {
                HashMap<String, String> map = new HashMap<>();
                map.put("tm", i + "月");
                for (int j = 0; j < tmList.size(); j++) {
                    map.put(tmList.get(j), tmValueMap.get(tmList.get(j) + "" + i));
                }
                // 常年
                map.put(RainHelper.MYAV_KEY, tmValueMap.get(RainHelper.MYAV_YEAR + i));
                listMap.add(map);
            }
        } else if (tp.equals("2")) {
            List<StPstatR> list = rainDao.getAdcdContrastBytmMonth(ad, tmList);
            // 月
            Map<String, String> tmValueMap = list.stream().collect(Collectors.toMap(item -> item.getIdtm().format(DateTimeFormatter.ofPattern("yyyyMM")) + item.getIdtm().getDayOfMonth(), item -> item.getAccp().toString()));

            String mth = tmList.get(0);
            mth = mth.substring(mth.length() - 2);
            for (int i = 1; i <= 31; i++) {
                HashMap<String, String> map = new HashMap<>();
                map.put("tm", i + "日");
                for (int j = 0; j < tmList.size(); j++) {
                    map.put(tmList.get(j), tmValueMap.get(tmList.get(j) + "" + i));
                }
                // 常年
                map.put(RainHelper.MYAV_KEY + mth, tmValueMap.get(RainHelper.MYAV_YEAR + mth + i));
                listMap.add(map);
            }
        } else {
            return null;
        }
        return listMap;
    }

    @Override
    public List<Map<String, String>> getBscdContrastBytm(String bscd, String tp, String tms) {

        List<Map<String, String>> listMap = new ArrayList<>();

        List<String> tmList = Arrays.asList(tms.split(","));

        // 年
        if (tp.equals("1")) {
            List<StPstatR> list = rainDao.getBscdContrastBytmYear(bscd, tmList);
            Map<String, String> tmValueMap = list.stream().collect(Collectors.toMap(item -> item.getIdtm().getYear() + "" + item.getIdtm().getMonthValue(), item -> item.getAccp().toString()));

            for (int i = 1; i <= 12; i++) {
                HashMap<String, String> map = new HashMap<>();
                map.put("tm", i + "月");
                for (int j = 0; j < tmList.size(); j++) {
                    map.put(tmList.get(j), tmValueMap.get(tmList.get(j) + "" + i));
                }
                // 常年
                map.put(RainHelper.MYAV_KEY, tmValueMap.get(RainHelper.MYAV_YEAR + i));
                listMap.add(map);
            }
        } else if (tp.equals("2")) {
            List<StPstatR> list = rainDao.getBscdContrastBytmMonth(bscd, tmList);
            // 月
            Map<String, String> tmValueMap = list.stream().collect(Collectors.toMap(item -> item.getIdtm().format(DateTimeFormatter.ofPattern("yyyyMM")) + item.getIdtm().getDayOfMonth(), item -> item.getAccp().toString()));

            String mth = tmList.get(0);
            mth = mth.substring(mth.length() - 2);
            for (int i = 1; i <= 31; i++) {
                HashMap<String, String> map = new HashMap<>();
                map.put("tm", i + "日");
                for (int j = 0; j < tmList.size(); j++) {
                    map.put(tmList.get(j), tmValueMap.get(tmList.get(j) + "" + i));
                }
                // 常年
                map.put(RainHelper.MYAV_KEY + mth, tmValueMap.get(RainHelper.MYAV_YEAR + mth + i));
                listMap.add(map);
            }
        } else {
            return null;
        }
        return listMap;
    }

    @Override
    public List<RainForecastHour> getForecastHourByStcd(String stcd, String stm) {
        if (stcd == null || stcd.equals("")) return new ArrayList<>();
        return rainDao.getForecastHourByStcd(stcd, stm);
    }

    @Override
    public List<Rain> getForecastByStcd(String adcd, String stm, String etm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<Rain> frainList = rainDao.getFAccpByTmAll(param);
        return frainList;
    }

    @Override
    public List<Rain> getForecastList(String stm, String etm, List<String> stType, String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            param.put("stTypes", stTypes);
        }
        List<Rain> list = rainDao.getForecastList(param);

        return list;
    }

    @Override
    public List<AdAvgRainForecast> getAvgRainForecastListByAdcdTm(String adcd, String stm, String etm) {
        List<AdAvgRainForecast> retList = new ArrayList<>();
        Map<String, AdcdB> adMap = adService.getAdSonList(adcd);
        Collection<AdcdB> values = adMap.values();
        adMap.values().forEach(item -> {
            AdAvgRainForecast aarf = new AdAvgRainForecast();
            aarf.setAdcd(item.getAdcd());
            aarf.setAdnm(item.getAdnm());
            aarf.setAdlvl(item.getAdlvl());
            aarf.setLgtd(item.getLgtd());
            aarf.setLttd(item.getLttd());
            retList.add(aarf);
        });
        retList.sort(Comparator.comparing(AdAvgRainForecast::getAdcd));
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<AdAvgRainForecast> avgRainList = rainDao.getAvgRainList(param);
        List<AdAvgRainForecast> avgForecastList = rainDao.getAvgForecastList(param);
        retList.forEach(item -> {
            List<AdAvgRainForecast> avgRainCollect = avgRainList.stream().filter(avg -> item.getAdcd().equals(avg.getAdcd())).collect(Collectors.toList());
            if (avgRainCollect != null && avgRainCollect.size() > 0) {
                item.setAvgRain(avgRainCollect.get(0).getAvgRain());
            } else {
                item.setAvgRain(BigDecimal.valueOf(0));
            }
            List<AdAvgRainForecast> avgForecasCollect = avgForecastList.stream().filter(avg -> item.getAdcd().equals(avg.getAdcd())).collect(Collectors.toList());
            if (avgForecasCollect != null && avgForecasCollect.size() > 0) {
                item.setAvgForecast(avgForecasCollect.get(0).getAvgForecast());
            } else {
                item.setAvgForecast(BigDecimal.valueOf(0));
            }
        });
        return retList;
    }

    @Override
    public List<StPptnF> getForecast3Hours(String stcd, String stm, String etm) {
        return rainDao.getForecast3Hours(stcd, stm, etm);
    }

    @Override
    public List<StPptnF> getForecast24Hours(String stcd, String stm, String etm) {
        return rainDao.getForecast24Hours(stcd, stm, etm);
    }

    @Override
    public List<StPptnFVo> getForecast3Or72List(String stcd, String stm, String etm, String tp) {
        return rainDao.getForecast3Or72List(stcd, stm, etm, tp);
    }

    @Override
    public List<RainPointerAD> getRainPointerADList(String stcd) {
        return rainDao.getRainPointerADList(stcd);
    }

    @Override
    public List<RainPointerST> getRainPointerSTList(String adcd) {
        return rainDao.getRainPointerSTList(adcd);
    }

    @Override
    public List<RainArea> getRainAreaAd(QueryRain baseDao) {

        IPage<Rain> rainList = getRainByCondition(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBscd(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getRainShowType(), 1, -1, baseDao.getIsOut(), baseDao.getIsFollow(), baseDao.getForecastHour(), null);

        IsoRequest query = new IsoRequest();
        query.setBoundFile("220000000000000.shp");
        query.getParam().setLevels(Arrays.asList(new Double[]{(double) 0, 0.001, 10.0, 25.0, 50.0, 100.0, 250.0}));
        query.getResult().setType(2);
        query.getResult().setFormat("geojson");
        List<Double> pointCoords = new ArrayList<>();
        List<Double> pointValues = new ArrayList<>();

        int level = AdcdUtil.getAdLevel(baseDao.getAdcd());
        Map<String, Object> param = new HashMap<>();
        param.put("ad", baseDao.getAdcd().substring(0, level));
        param.put("level", level);
        List<RainArea> retList = rainDao.getAdcdArea(param);

        List<Rain> list = rainList.getRecords();
        list.forEach(i -> {
            if (i.getPlgtd() != null && i.getPlgtd().compareTo(BigDecimal.ZERO) > 0 && i.getPlttd() != null && i.getPlttd().compareTo(BigDecimal.ZERO) > 0) {
                pointCoords.add(i.getPlgtd().doubleValue());
                pointCoords.add(i.getPlttd().doubleValue());
                pointValues.add((i.getDrps() == null || i.getDrps().equals("")) ? 0 : Double.valueOf(i.getDrps()));
            }
        });
        query.getInput().setPointCoords(pointCoords);
        query.getInput().setPointValues(pointValues);
        String alyResult = null;
        try {
            alyResult = isoService.analyse(query);
        } catch (Exception e) {
            logger.error(e.toString());
            return retList;
        }
        String shpFile = cntyShpFile;
        if (StringUtils.isBlank(shpFile)) {
            shpFile = new ApplicationHome(getClass()).getSource().getParentFile().getAbsolutePath();
            shpFile = formatDirectory(shpFile).concat("jilinProvince.shp");
        }
        RainHelper rainHelper = new RainHelper(shpFile, RainHelper.KIND_CNTY);
        Map<String, RainHelper.RainArea> rainAreaMapOrgi = rainHelper.parseRainJsonArea(alyResult);
        Map<String, RainHelper.RainArea> rainAreaMap = new HashMap<>();
        for (Map.Entry<String, RainHelper.RainArea> entry : rainAreaMapOrgi.entrySet()) {
            if (entry.getKey().length() == 2) {
                rainAreaMap.put(entry.getKey() + "0000", entry.getValue());
            } else if (entry.getKey().length() == 4) {
                rainAreaMap.put(entry.getKey() + "00", entry.getValue());
            } else {
                rainAreaMap.put(entry.getKey(), entry.getValue());
            }
        }

        Map<String, List<Rain>> listMap = new HashMap<>();
        Map<String, List<Rain>> listMap6 = list.stream().collect(Collectors.groupingBy(i -> i.getAdcd().substring(0, 6)));
        Map<String, List<Rain>> listMap4 = list.stream().filter(i -> !i.getAdcd().startsWith("220581")).collect(Collectors.groupingBy(i -> i.getAdcd().substring(0, 4)));
        Map<String, List<Rain>> listMap2 = list.stream().collect(Collectors.groupingBy(i -> i.getAdcd().substring(0, 2)));
        listMap6.forEach((k, v) -> listMap.put(k, v));
        listMap4.forEach((k, v) -> listMap.put(k + "00", v));
        listMap2.forEach((k, v) -> listMap.put(k + "0000", v));

        retList.forEach(i -> {
            i.setArea25(BigDecimal.ZERO);
            i.setArea50(BigDecimal.ZERO);
            i.setArea100(BigDecimal.ZERO);
            if (i.getLdarea() != null) {
                i.setLdarea(i.getLdarea().setScale(0, RoundingMode.HALF_UP));
            }
            i.setAvgDrps(BigDecimal.ZERO);
            i.setTotalDrps(BigDecimal.ZERO);
            if (listMap.containsKey(i.getAdcd().substring(0, 6))) {
                List<Rain> adcdRainList = listMap.get(i.getAdcd().substring(0, 6));
                List<Rain> rains = adcdRainList.stream().filter(rain -> rain.getDrps() != null).collect(Collectors.toList());
                BigDecimal drps = rains.stream().map(rain -> new BigDecimal(rain.getDrps())).reduce(BigDecimal.ZERO, BigDecimal::add);
                long count = rains.size();
                if (count != 0) {
                    BigDecimal avgDrps = drps.divide(new BigDecimal(count), 1, RoundingMode.HALF_UP);
                    i.setAvgDrps(avgDrps);
                    if (i.getLdarea() != null) {
                        BigDecimal totalDrps = avgDrps.multiply(i.getLdarea()).divide(new BigDecimal(10)).setScale(0, RoundingMode.HALF_UP);
                        i.setTotalDrps(totalDrps);
                    }
                }
            }
            if (rainAreaMap.containsKey(i.getAdcd().substring(0, 6))) {
                RainHelper.RainArea rainArea = rainAreaMap.get(i.getAdcd().substring(0, 6));
                if (rainArea != null) {
                    BigDecimal area25 = BigDecimal.valueOf(rainArea.getArea25()).setScale(0, RoundingMode.HALF_UP);
                    BigDecimal area50 = BigDecimal.valueOf(rainArea.getArea50()).setScale(0, RoundingMode.HALF_UP);
                    BigDecimal area100 = BigDecimal.valueOf(rainArea.getArea100()).setScale(0, RoundingMode.HALF_UP);
                    if (i.getLdarea() != null && i.getLdarea().compareTo(BigDecimal.ZERO) != 0) {
                        i.setArea25(area25.compareTo(i.getLdarea()) > 0 ? i.getLdarea() : area25);
                        i.setArea50(area50.compareTo(i.getLdarea()) > 0 ? i.getLdarea() : area50);
                        i.setArea100(area100.compareTo(i.getLdarea()) > 0 ? i.getLdarea() : area100);
                    } else {
                        i.setArea25(area25);
                        i.setArea50(area50);
                        i.setArea100(area100);
                    }
                }
            }
        });
        return retList;
    }

    @Override
    public List<RainArea> getRainAreaBas(QueryRain baseDao) {
        List<RainByBas> list = rainDao.getBasMainList(baseDao.getBscd(),baseDao.getStnm());
        // TRK@20240705: 全部测站计算面雨量
        baseDao.setThreshold("0-99999");
        List<Rain> rainList = getRainByConditionAllByBas(baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(), baseDao.getAdcd(), baseDao.getBscd(),
                baseDao.getStnm(), baseDao.getThreshold(), baseDao.getRainShowType(), baseDao.getIsOut(), baseDao.getIsFollow(), baseDao.getForecastHour());
        // 关联关系
        List<BsnBasStTo> basStToList = rainDao.getBasStList();
        Map<String, Map<String, List<BsnBasStTo>>> stMap = basStToList.stream().collect(Collectors.groupingBy(BsnBasStTo::getStcd, Collectors.groupingBy(BsnBasStTo::getBasCode)));

        DecimalFormat df = new DecimalFormat("#.0");
        list.forEach(item -> {
            Map<String, Object> tempMap = new HashMap<>();
            List<Rain> children = rainList.stream().filter(x -> (stMap.get(x.getStcd()) != null && stMap.get(x.getStcd()).get(item.getBasCode()) != null))
                    .filter(x -> tempMap.putIfAbsent(x.getStcd(), x) == null).collect(Collectors.toList());
            item.setChildren(children);
            if (!CollectionUtils.isEmpty(item.getChildren())) {
                // 雨量值>=0
                List<Rain> drpsList = item.getChildren().stream().filter(d -> Double.valueOf(d.getDrps()) >= 0).collect(Collectors.toList());
                // 平均降雨
                double drpsSum = drpsList.stream().map(d -> Double.valueOf(d.getDrps())).reduce(Double::sum).get();
                item.setAvgRain(Double.parseDouble(df.format(drpsSum / drpsList.size())));

                // 最大降雨
                double drpsMax = drpsList.stream().map(d -> Double.valueOf(d.getDrps())).reduce(Double::max).get();
                item.setMaxRain(drpsMax);
                // 前期24小时
                double rainb24Sum = drpsList.stream().map(d -> d.getRainb24()).reduce(Double::sum).get();
                item.setAvgLdrps(Double.parseDouble(df.format(rainb24Sum / drpsList.size())));
                // 未来3h
                double rain3Sum = drpsList.stream().map(d -> d.getRain3()).reduce(Double::sum).get();
                item.setAvgF3drps(Double.parseDouble(df.format(rain3Sum / drpsList.size())));
                // 未来24h
                double rain24Sum = drpsList.stream().map(d -> d.getRain24()).reduce(Double::sum).get();
                item.setAvgF24drps(Double.parseDouble(df.format(rain24Sum / drpsList.size())));
            }
        });
        List<RainByBas> list1 = list.stream().filter(i -> i.getIsZyjh().equals("1")).collect(Collectors.toList());
        List<RainByBas> list2 = list.stream().filter(i -> i.getIsZyjh().equals("2")).collect(Collectors.toList());
        Map<String, List<RainByBas>> rbMap = list2.stream().collect(Collectors.groupingBy(RainByBas::getPbasCode));
        // 排序
        Collections.sort(list1, (bas1, bas2) -> {
            if (bas1.getAvgRain() < bas2.getAvgRain()) {
                return 1;
            } else if (bas1.getAvgRain() > bas2.getAvgRain()) {
                return -1;
            }
            if (bas1.getAvgF3drps() < bas2.getAvgF3drps()) {
                return 1;
            } else if (bas1.getAvgF3drps() > bas2.getAvgF3drps()) {
                return -1;
            }
            if (bas1.getBasNo() != null && bas2.getBasNo() != null) {
                if (bas1.getBasNo() < bas2.getBasNo()) {
                    return -1;
                } else if (bas1.getBasNo() > bas2.getBasNo()) {
                    return 1;
                }
            } else if (bas1.getBasNo() != null) {
                return -1;
            } else if (bas2.getBasNo() != null) {
                return 1;
            }
            return bas1.getBasCode().compareTo(bas2.getBasCode());
        });
        list1.forEach(i -> {
            if (rbMap.containsKey(i.getBasCode())) {
                List<RainByBas> rainByBas = rbMap.get(i.getBasCode());
                i.setBasChildren(rainByBas);
            }
        });

        // 面积计算
        IsoRequest query = new IsoRequest();
        query.setBoundFile("220000000000000.shp");
        query.getParam().setLevels(Arrays.asList(new Double[]{(double) 0, 0.001, 10.0, 25.0, 50.0, 100.0, 250.0}));
        query.getResult().setType(2);
        query.getResult().setFormat("geojson");
        List<Double> pointCoords = new ArrayList<>();
        List<Double> pointValues = new ArrayList<>();

        int level = AdcdUtil.getAdLevel(baseDao.getAdcd());
        Map<String, Object> param = new HashMap<>();
        param.put("ad", baseDao.getAdcd().substring(0, level));
        param.put("level", level);
        List<RainArea> retList = new ArrayList<>();

        rainList.forEach(i -> {
            if (i.getPlgtd() != null && i.getPlgtd().compareTo(BigDecimal.ZERO) > 0 && i.getPlttd() != null && i.getPlttd().compareTo(BigDecimal.ZERO) > 0) {
                pointCoords.add(i.getPlgtd().doubleValue());
                pointCoords.add(i.getPlttd().doubleValue());
                pointValues.add((i.getDrps() == null || i.getDrps().equals("")) ? 0 : Double.valueOf(i.getDrps()));
            }
        });
        query.getInput().setPointCoords(pointCoords);
        query.getInput().setPointValues(pointValues);
        String alyResult = null;
        try {
            alyResult = isoService.analyse(query);
        } catch (Exception e) {
            logger.error(e.toString());
            return retList;
        }
        String shpFile = basShpFile;
        if (StringUtils.isBlank(shpFile)) {
            shpFile = new ApplicationHome(getClass()).getSource().getParentFile().getAbsolutePath();
            shpFile = formatDirectory(shpFile).concat("BAS.shp");
        }
        RainHelper rainHelper = new RainHelper(shpFile, RainHelper.KIND_BAS);
        Map<String, RainHelper.RainArea> rainAreaMapOrgi = rainHelper.parseRainJsonArea(alyResult);

        list1.forEach(item -> {
            RainArea rainArea = new RainArea();
            rainArea.setBasCode(item.getBasCode());
            rainArea.setBasName(item.getBasName());
            rainArea.setPbasCode(item.getPbasCode());
            rainArea.setLdarea(BigDecimal.valueOf(item.getRiverArea()).setScale(0, RoundingMode.HALF_UP));
            rainArea.setAvgDrps(BigDecimal.valueOf(item.getAvgRain()).setScale(1, RoundingMode.HALF_UP));
            rainArea.setTotalDrps(BigDecimal.valueOf(item.getAvgRain() * item.getRiverArea()).divide(new BigDecimal(10)).setScale(0, RoundingMode.HALF_UP));
            rainArea.setArea25(BigDecimal.ZERO);
            rainArea.setArea50(BigDecimal.ZERO);
            rainArea.setArea100(BigDecimal.ZERO);
            if (rainAreaMapOrgi.containsKey(item.getBasCode())) {
                RainHelper.RainArea rainArea1 = rainAreaMapOrgi.get(item.getBasCode());
                BigDecimal area25 = BigDecimal.valueOf(rainArea1.getArea25()).setScale(0, RoundingMode.HALF_UP);
                BigDecimal area50 = BigDecimal.valueOf(rainArea1.getArea50()).setScale(0, RoundingMode.HALF_UP);
                BigDecimal area100 = BigDecimal.valueOf(rainArea1.getArea100()).setScale(0, RoundingMode.HALF_UP);
                if (rainArea.getLdarea() != null && rainArea.getLdarea().compareTo(BigDecimal.ZERO) != 0) {
                    rainArea.setArea25(area25.compareTo(rainArea.getLdarea()) > 0 ? rainArea.getLdarea() : area25);
                    rainArea.setArea50(area50.compareTo(rainArea.getLdarea()) > 0 ? rainArea.getLdarea() : area50);
                    rainArea.setArea100(area100.compareTo(rainArea.getLdarea()) > 0 ? rainArea.getLdarea() : area100);
                } else {
                    rainArea.setArea25(area25);
                    rainArea.setArea50(area50);
                    rainArea.setArea100(area100);
                }
            }
            if (CollectionUtils.isNotEmpty(item.getBasChildren())) {
                List<RainArea> basChildren = new LinkedList<>();
                item.getBasChildren().forEach(itemChild -> {
                    RainArea rainAreaChild = new RainArea();
                    rainAreaChild.setBasCode(itemChild.getBasCode());
                    rainAreaChild.setBasName(itemChild.getBasName());
                    rainAreaChild.setPbasCode(itemChild.getPbasCode());
                    rainAreaChild.setLdarea(BigDecimal.valueOf(itemChild.getRiverArea()).setScale(0, RoundingMode.HALF_UP));
                    rainAreaChild.setAvgDrps(BigDecimal.valueOf(itemChild.getAvgRain()).setScale(1, RoundingMode.HALF_UP));
                    rainAreaChild.setTotalDrps(BigDecimal.valueOf(itemChild.getAvgRain() * itemChild.getRiverArea()).divide(new BigDecimal(10)).setScale(0, RoundingMode.HALF_UP));
                    rainAreaChild.setArea25(BigDecimal.ZERO);
                    rainAreaChild.setArea50(BigDecimal.ZERO);
                    rainAreaChild.setArea100(BigDecimal.ZERO);
                    if (rainAreaMapOrgi.containsKey(itemChild.getBasCode())) {
                        RainHelper.RainArea rainArea1 = rainAreaMapOrgi.get(itemChild.getBasCode());
                        BigDecimal area25 = BigDecimal.valueOf(rainArea1.getArea25()).setScale(0, RoundingMode.HALF_UP);
                        BigDecimal area50 = BigDecimal.valueOf(rainArea1.getArea50()).setScale(0, RoundingMode.HALF_UP);
                        BigDecimal area100 = BigDecimal.valueOf(rainArea1.getArea100()).setScale(0, RoundingMode.HALF_UP);
                        if (rainAreaChild.getLdarea() != null && rainAreaChild.getLdarea().compareTo(BigDecimal.ZERO) != 0) {
                            rainAreaChild.setArea25(area25.compareTo(rainAreaChild.getLdarea()) > 0 ? rainAreaChild.getLdarea() : area25);
                            rainAreaChild.setArea50(area50.compareTo(rainAreaChild.getLdarea()) > 0 ? rainAreaChild.getLdarea() : area50);
                            rainAreaChild.setArea100(area100.compareTo(rainAreaChild.getLdarea()) > 0 ? rainAreaChild.getLdarea() : area100);
                        } else {
                            rainAreaChild.setArea25(area25);
                            rainAreaChild.setArea50(area50);
                            rainAreaChild.setArea100(area100);
                        }
                    }
                    basChildren.add(rainAreaChild);
                });
                rainArea.setBasChildren(basChildren);
            }
            retList.add(rainArea);
        });
        return retList;
    }


    /**
     * 获取树的子节点
     */
    private List<RainArea> getRainAreaChildren(List<RainArea> list, String pcode) {
        // 通过父级编码子类
        List<RainArea> childList = list.stream().filter(item -> item.getPbasCode().equals(pcode)).collect(Collectors.toList());
        return childList;
    }


    @Override
    public void exportRainArea(QueryRain baseDao) {
        List<RainArea> list = getRainAreaAd(baseDao);
        ExcelExportUtil.execute(list, "综合监视雨情笼罩面降雨");
    }

    @Override
    public void exportRainByBasCondition(QueryRain baseDao) {
        List<RainByBas> list = getRainByBasCondition(baseDao);
        ExcelExportUtil.execute(list, "综合监视雨情中小河流");
    }

    @Override
    public void exportRainByBasMainCondition(QueryRain baseDao) {
        List<RainByBas> list = getRainByBasMainCondition(baseDao);
        ExcelExportUtil.execute(list, "综合监视雨情中小河流");
    }

    private String formatDirectory(String dir) {
        if (dir.endsWith("\\") || dir.endsWith("/")) {
            dir = dir.substring(0, dir.length() - 1);
        }
        return dir.concat("\\");
    }

    @Override
    public List<RsvrRainList> getRainReservoirList(String basCode,String adcd,String resName , String engScal, String stm, String etm ) {
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("resName", resName);
        param.put("basCode", basCode);
        param.put("tp", "1");
        param.put("engScal", engScal);
        param.put("stTypes", "1,2,3,6");

        // TRK@20240710: 水库降雨查询优化
        List<RsvrRainList> list = new LinkedList<>();

        // 测站权重
        List<RsvrRainList> rsvrList = rainDao.getStRsvrRainBaseList(param);
        Map<String, List<RsvrRainList>> rsvrMap = new HashMap<>();
        Map<String, String> tempMap = new HashMap<>();
        rsvrList.forEach(item -> {
            if (org.apache.commons.lang.StringUtils.isNotEmpty(item.getTempCode())) {
                if (tempMap.containsKey(item.getTempCode()) && !tempMap.get(item.getTempCode()).equals(item.getResCode())) {
                    return;
                }
                tempMap.put(item.getTempCode(), item.getResCode());
            }

            List<RsvrRainList> stList = rsvrMap.get(item.getResCode());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(stList)) {
                RsvrRainList rsvrRain = new RsvrRainList();
                BeanUtils.copyProperties(item, rsvrRain);
                // 清空来水量&权重系数
                rsvrRain.setW(null);
                list.add(rsvrRain);

                stList = new LinkedList<>();
                rsvrMap.put(item.getResCode(), stList);
            }
            stList.add(item);
        });

        List<Rain> rainList = this.getRainByConditionAll(stm, etm, Collections.emptyList(), "220000000000000", "", null, "0-99999", "0", null, null, null, null);
        Map<String, Rain> rainMap = rainList.stream().collect(Collectors.toMap(Rain::getStcd, Function.identity(), (o, n) -> n));

        // 雨量数据
        list.forEach(item -> {
            List<RsvrRainList> stList = rsvrMap.get(item.getResCode());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(stList)) {
                item.setAvgDrps(BigDecimal.valueOf(0));
                item.setAvgLdrps(BigDecimal.valueOf(0));
                item.setAvgF3drps(BigDecimal.valueOf(0));
                item.setAvgF24drps(BigDecimal.valueOf(0));
                item.setChildren(Collections.emptyList());
                return;
            }

            double w = 0;
            double maxDrps = 0;
            double avgDrps = 0;
            double avgLdrps = 0;
            double avgF3drps = 0;
            double avgF24drps = 0;
            List<Rain> children = new LinkedList<>();
            for (int i = 0; i < stList.size(); i++) {
                RsvrRainList rsvrRain = stList.get(i);
                if (rsvrRain.getW() == null || rsvrRain.getW() <= 0) {
                    continue;
                }
                w = w + rsvrRain.getW();

                Rain rain = rainMap.get(rsvrRain.getStcd());
                if (rain == null) {
                    continue;
                }

                // drps
                try {
                    double drps = Double.parseDouble(rain.getDrps());
                    if (maxDrps < drps) {
                        maxDrps = drps;
                    }
                    avgDrps = avgDrps + drps * rsvrRain.getW();
                } catch (Exception ex) {
//                    log.trace(ex.getMessage());
                }
                // ldrps
                avgLdrps = avgLdrps + rain.getRainb24() * rsvrRain.getW();
                // avgF3drps
                avgF3drps = avgF3drps + rain.getRain3() * rsvrRain.getW();
                // avgF24drps
                avgF24drps = avgF24drps + rain.getRain24() * rsvrRain.getW();
                // 测站降雨
                children.add(rain);
            }

            item.setMaxDrps(BigDecimal.valueOf(maxDrps));
            if (w > 0) {
                item.setAvgDrps(BigDecimal.valueOf(avgDrps / w));
                item.setAvgLdrps(BigDecimal.valueOf(avgLdrps / w));
                item.setAvgF3drps(BigDecimal.valueOf(avgF3drps / w));
                item.setAvgF24drps(BigDecimal.valueOf(avgF24drps / w));
            } else {
                item.setAvgDrps(BigDecimal.valueOf(0));
                item.setAvgLdrps(BigDecimal.valueOf(0));
                item.setAvgF3drps(BigDecimal.valueOf(0));
                item.setAvgF24drps(BigDecimal.valueOf(0));
            }

            // 测站降雨
            try {
                Collections.sort(children, (o1, o2) -> {
                    int sort = 0;
                    if (o1.getDrps() != null && o2.getDrps() != null) {
                        try {
                            Double drp1 = Double.parseDouble(o1.getDrps());
                            Double drp2 = Double.parseDouble(o2.getDrps());
                            sort = drp2.compareTo(drp1);
                        } catch (Exception ex) {

                        }
                    } else if (o1.getDrps() != null) {
                        return -1;
                    } else if (o2.getDrps() != null) {
                        return 1;
                    }

                    if (sort == 0) {
                        if (o1.getAdcd() != null && o2.getAdcd() != null) {
                            sort = o2.getAdcd().compareTo(o1.getAdcd());
                        } else if (o1.getAdcd() != null) {
                            return -1;
                        } else if (o2.getAdcd() != null) {
                            return 1;
                        }
                    }
                    if (sort == 0) {
                        return o1.getStcd().compareTo(o2.getStcd());
                    }
                    return sort;
                });
                item.setChildren(children);
            } catch (Exception ex) {
                item.setChildren(Collections.emptyList());
            }
        });

        // 排序
        Collections.sort(list, (o1, o2) -> {
            int sort = 0;
            if (o1.getAvgDrps() != null && o2.getAvgDrps() != null) {
                sort = o2.getAvgDrps().compareTo(o1.getAvgDrps());
            } else if (o1.getAvgDrps() != null) {
                return -1;
            } else if (o2.getAvgDrps() != null) {
                return 1;
            }

            if (sort == 0) {
                sort = o1.getEngScal().compareTo(o2.getEngScal());
            }
            if (sort == 0) {
                sort = o1.getResCode().compareTo(o2.getResCode());
            }
            return sort;
        });

        // 径流系数
        List<RsvrRainList> rcList = rainDao.getStRsvrRcList(param);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rcList)) {
            Map<String, RsvrRainList> rcMap = rcList.stream().collect(Collectors.toMap(RsvrRainList::getResCode, Function.identity()));
            list.forEach(item -> {
                RsvrRainList itemRc = rcMap.get(item.getResCode());
                if (itemRc == null || itemRc.getRc() <= 0) {
                    return;
                }
                item.setRc(itemRc.getRc());

                if (item.getAvgDrps() == null || item.getWatShedArea() == null) {
                    return;
                }
                Double avgDrps = Double.parseDouble(item.getAvgDrps().toString());
                // 百万方
                item.setW(avgDrps * item.getWatShedArea() * itemRc.getRc() / 1000);
            });
        }
        return list;
    }

}
