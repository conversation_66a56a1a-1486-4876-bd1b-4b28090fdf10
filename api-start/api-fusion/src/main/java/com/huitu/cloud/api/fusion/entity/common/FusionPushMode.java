package com.huitu.cloud.api.fusion.entity.common;

/**
 * 消息推送方式
 */
public enum FusionPushMode {
    SMS("1", "手机短信"),
    XCC("2", "语音呼叫");

    /**
     * 代码
     */
    private String code;
    /**
     * 别名
     */
    private String alias;

    FusionPushMode(String code, String alias) {
        this.code = code;
        this.alias = alias;
    }

    public String getCode() {
        return code;
    }

    public String getAlias() {
        return alias;
    }

    /**
     * 判断代码是否相等
     *
     * @param code 代码
     * @return true: 相等, false: 不相等
     */
    public boolean equalsCode(String code) {
        return this.code.equalsIgnoreCase(code);
    }
}
