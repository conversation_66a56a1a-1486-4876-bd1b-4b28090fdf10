package com.huitu.cloud.api.ewci.person.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.exception.DanPersonImportException;
import com.huitu.cloud.api.ewci.person.mapper.DanPersonDao;
import com.huitu.cloud.api.ewci.person.mapper.DanPersonTmpDao;
import com.huitu.cloud.api.ewci.person.service.DanPersonService;
import com.huitu.cloud.util.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 危险区居民管理服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class DanPersonServiceImpl implements DanPersonService {

    private static final Logger logger = LoggerFactory.getLogger(DanPersonServiceImpl.class);

    private DanPersonDao baseDao;
    private DanPersonTmpDao tmpDao;

    @Autowired
    public void setBaseDao(DanPersonDao baseDao) {
        this.baseDao = baseDao;
    }

    @Autowired
    public void setTmpDao(DanPersonTmpDao tmpDao) {
        this.tmpDao = tmpDao;
    }

    @Override
    public IPage<DanPerson> getPageList(DanPersonQuery query) {
        return baseDao.getPageList(query.toPageParam(), query.toQueryParam());
    }

    @Override
    public List<DanSummaryVo> getDanSummaryList(DanSummaryQuery query) {
        if (query.getLevel() > 6) {
            throw new RuntimeException("行政区划代码无效，仅支持到县以上级别（含县）");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", query.getAdcd());
        params.put("level", query.getLevel());
        params.put("include", query.isInclude() ? 1 : 0);
        params.put("lowLevel", query.getLowLevel());
        List<DanSummaryVo> source = baseDao.getDanSummaryList(params);

        if (CollectionUtils.isEmpty(source)) {
            return new ArrayList<>();
        }
        if (!query.isInclude()) {
            // 不包含下级，无需创建树
            return source;
        }
        Map<String, DanSummaryVo> center = new LinkedHashMap<>(source.size());
        source.forEach(ad -> center.put(ad.getAdcd(), ad));
        List<DanSummaryVo> target = new ArrayList<>();
        for (DanSummaryVo node : source) {
            if (center.containsKey(node.getPadcd())) {
                DanSummaryVo parent = center.get(node.getPadcd());
                if (null == parent.getChildren()) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            } else {
                target.add(node);
            }
        }
        return target;
    }

    @Override
    public void dataExport(DanPersonQuery query, OutputStream output) {
        IPage<DanPerson> page = baseDao.getPageList(query.toPageParam(), query.toQueryParam());
        EasyExcel.write(output, DanPerson.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("危险区居民信息")
                .doWrite(page.getRecords());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult<BnsDanpersonTmp> dataImport(String xadcd, InputStream input) {
        List<BnsDanpersonTmp> tmpData;
        try {
            tmpData = EasyExcel.read(input, BnsDanpersonTmp.class, null)
                    .headRowNumber(1).sheet().doReadSync();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);

            throw new DanPersonImportException("Excel文件格式不正确，解析失败", ex);
        }

        if (CollectionUtils.isEmpty(tmpData)) {
            throw new DanPersonImportException("Excel文件中不包含任何数据，请检查");
        }
        // 过滤空数据，任何一项有数据即为有效
        tmpData = tmpData.stream().filter(this::nonNulProperty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tmpData)) {
            throw new DanPersonImportException("Excel文件中数据无效，请检查");
        }

        // 去掉多余的空格
        tmpData.forEach(this::trimProperty);

        try {
            String batchNo = batchInsertTmpData(xadcd, tmpData);
            List<BnsDanpersonTmp> errorData = baseDao.batchImport(batchNo);
            if (!CollectionUtils.isEmpty(errorData)) {
                ImportResult<BnsDanpersonTmp> result = new ImportResult<>();
                result.setStatus(tmpData.size() == errorData.size() ? 0 : 2);
                result.setMessage(String.format("数据（%s）导入失败，请检查", result.getStatus() == 0 ? "全部" : "部分"));
                result.setData(errorData);
                return result;
            }
        } catch (Exception ex) {
            logger.error("危险区居民信息入库失败", ex);

            throw new DanPersonImportException("数据未能成功入库，导入失败", tmpData, ex);
        }
        return new ImportResult<>();
    }

    @Override
    public int danPersonEdit(DanPerson entity) {
        return baseDao.danPersonEdit(entity);
    }

    /**
     * 批量插入临时数据
     *
     * @param xadcd   县级行政区划代码
     * @param persons 居民信息集合
     * @return 批号
     **/
    private String batchInsertTmpData(String xadcd, List<BnsDanpersonTmp> persons) {
        String batchNo = UUID.randomUUID().toString();
        if (!CollectionUtils.isEmpty(persons)) {
            Map<String, Object> params = new HashMap<>();
            params.put("batchNo", batchNo);
            params.put("xadcd", xadcd);
            List<List<BnsDanpersonTmp>> lists = ListUtils.splitList(persons, 250);
            for (List<BnsDanpersonTmp> list : lists) {
                params.put("list", list);
                tmpDao.batchInsert(params);
            }
        }
        return batchNo;
    }

    /**
     * 属性不为空
     *
     * @param tmp 临时信息
     * @return true: 非空  false: 空
     **/
    private boolean nonNulProperty(BnsDanpersonTmp tmp) {
        return StringUtils.isNotBlank(tmp.getTown())
                || StringUtils.isNotBlank(tmp.getXzc())
                || StringUtils.isNotBlank(tmp.getZrc())
                || StringUtils.isNotBlank(tmp.getDaname())
                || StringUtils.isNotBlank(tmp.getRealnm())
                || StringUtils.isNotBlank(tmp.getMobile());
    }

    /**
     * 去掉字符串属性的空格（两端）
     *
     * @param tmp 临时信息
     **/
    private void trimProperty(BnsDanpersonTmp tmp) {
        tmp.setTown(StringUtils.trim(tmp.getTown()));
        tmp.setXzc(StringUtils.trim(tmp.getXzc()));
        tmp.setZrc(StringUtils.trim(tmp.getZrc()));
        tmp.setDaname(StringUtils.trim(tmp.getDaname()));
        tmp.setRealnm(StringUtils.trim(tmp.getRealnm()));
        tmp.setMobile(StringUtils.trim(tmp.getMobile()));
    }
}
