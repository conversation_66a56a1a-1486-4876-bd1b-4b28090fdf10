package com.huitu.cloud.api.hsfxt.rsvr.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021-06-29
 */
@ApiModel(value="水库测站对应小水库风险图和调查评价", description="小水库风险图和调查评价表")
public class RsvrObjinfo implements Serializable {

    @ApiModelProperty(value = "防汛检查对象")
    private String objid;

    @ApiModelProperty(value = "防治区名称")
    private String objnms;

    @ApiModelProperty(value = "总人口数")
    private BigDecimal ptcount;

    @ApiModelProperty(value = "总户数")
    private BigDecimal etcount;

    @ApiModelProperty(value = "总房屋数")
    private BigDecimal htcount;

    @ApiModelProperty(value = "土地面积")
    private BigDecimal ldarea;

    @ApiModelProperty(value = "耕地面积")
    private BigDecimal plarea;

    @ApiModelProperty(value = "防治区类型")
    private String prevtp;

    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    @ApiModelProperty(value = "乡镇防汛责任人姓名")
    private String xzRealnm;

    @ApiModelProperty(value = "乡镇防汛责任人手机号")
    private String xzMobile;

    @ApiModelProperty(value = "村屯防汛责任人姓名")
    private String ctRealnm;

    @ApiModelProperty(value = "村屯防汛责任人手机号")
    private String ctMobile;

    public String getPrevtp() {
        return prevtp;
    }

    public void setPrevtp(String prevtp) {
        this.prevtp = prevtp;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getObjid() {
        return objid;
    }

    public void setObjid(String objid) {
        this.objid = objid;
    }

    public String getObjnms() {
        return objnms;
    }

    public void setObjnms(String objnms) {
        this.objnms = objnms;
    }

    public BigDecimal getPtcount() {
        return ptcount;
    }

    public void setPtcount(BigDecimal ptcount) {
        this.ptcount = ptcount;
    }

    public BigDecimal getEtcount() {
        return etcount;
    }

    public void setEtcount(BigDecimal etcount) {
        this.etcount = etcount;
    }

    public BigDecimal getHtcount() {
        return htcount;
    }

    public void setHtcount(BigDecimal htcount) {
        this.htcount = htcount;
    }

    public BigDecimal getLdarea() {
        return ldarea;
    }

    public void setLdarea(BigDecimal ldarea) {
        this.ldarea = ldarea;
    }

    public BigDecimal getPlarea() {
        return plarea;
    }

    public void setPlarea(BigDecimal plarea) {
        this.plarea = plarea;
    }

    public String getXzRealnm() {
        return xzRealnm;
    }

    public void setXzRealnm(String xzRealnm) {
        this.xzRealnm = xzRealnm;
    }

    public String getXzMobile() {
        return xzMobile;
    }

    public void setXzMobile(String xzMobile) {
        this.xzMobile = xzMobile;
    }

    public String getCtRealnm() {
        return ctRealnm;
    }

    public void setCtRealnm(String ctRealnm) {
        this.ctRealnm = ctRealnm;
    }

    public String getCtMobile() {
        return ctMobile;
    }

    public void setCtMobile(String ctMobile) {
        this.ctMobile = ctMobile;
    }
}
