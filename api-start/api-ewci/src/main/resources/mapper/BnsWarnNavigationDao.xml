<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.BnsWarnNavigationDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <insert id="insertAllNavigation">
        INSERT INTO BNS_WARN_NAVIGATION (WARN_ID,START_LGTD,
        START_LTTD,END_LGTD,END_LTTD,WARN_CONTENT,STATUS,SEND_TIME,FAILURE_REASON)
        SELECT T.WARN_ID,T.START_LGTD,
        T.START_LTTD,T.END_LGTD,T.END_LTTD,T.WARN_CONTENT,T.STATUS,T.SEND_TIME,T.FAILURE_REASON FROM
        (VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.warnId},#{item.startLgtd},
            #{item.startLttd},#{item.endLgtd},#{item.endLttd},#{item.warnContent},#{item.status},#{item.sendTime},#{item.failureReason})
        </foreach>
        ) AS T(WARN_ID,START_LGTD, START_LTTD,END_LGTD,END_LTTD,WARN_CONTENT,STATUS,SEND_TIME,FAILURE_REASON)
    </insert>


</mapper>