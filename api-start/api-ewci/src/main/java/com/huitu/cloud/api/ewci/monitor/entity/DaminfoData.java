package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 塘堰
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@ApiModel(value = "塘堰列表")
public class DaminfoData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "塘堰编码")
    @TableField(value = "DAMCD")
    private String damcd;

    @ApiModelProperty(value = "塘堰名称")
    @TableField(value = "DAMNAME")
    private String damname;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "小流域代码")
    @TableField(value = "wscd")
    private String wscd;

    @ApiModelProperty(value = "小流域名称")
    @TableField(value = "WSNM")
    private String wsnm;

    @ApiModelProperty(value = "照片编号")
    @TableField(value = "PICID")
    private String picid;

    @ApiModelProperty(value = "经度（°）")
    @TableField(value = "LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度（°）")
    @TableField(value = "LTTD")
    private BigDecimal lttd;

    @ApiModelProperty(value = "容积（m3）")
    @TableField(value = "XHST")
    private BigDecimal xhst;

    @ApiModelProperty(value = "坝高（m）")
    @TableField(value = "HEIGHT")
    private BigDecimal height;

    @ApiModelProperty(value = "坝长（m）")
    @TableField(value = "WIDTH")
    private BigDecimal width;

    @ApiModelProperty(value = "挡水主坝类型")
    @TableField(value = "MT")
    private String mt;

    @ApiModelProperty(value = "描述")
    @TableField(value = "COMMENTS")
    private String comments;

    public String getDamcd() {
        return damcd;
    }

    public void setDamcd(String damcd) {
        this.damcd = damcd;
    }

    public String getDamname() {
        return damname;
    }

    public void setDamname(String damname) {
        this.damname = damname;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public String getPicid() {
        return picid;
    }

    public void setPicid(String picid) {
        this.picid = picid;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public BigDecimal getXhst() {
        return xhst;
    }

    public void setXhst(BigDecimal xhst) {
        this.xhst = xhst;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public String getMt() {
        return mt;
    }

    public void setMt(String mt) {
        this.mt = mt;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }
}
