package com.huitu.cloud.config;

import com.huitu.security.TokenHelper;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Configuration
public class FeignConfig implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        if (template.url().startsWith("/oauth/")) {
            // todo oauth不需要使用附加token的请求,否则会出现StackOverflowError
            return;
        }

        try {
            RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = ((ServletRequestAttributes) attributes).getRequest();
                String token = TokenHelper.getAccessToken(request);
                template.header("Authorization", "Bearer" + token);
            }
        } catch (Exception ignored) {
        }
    }
}
