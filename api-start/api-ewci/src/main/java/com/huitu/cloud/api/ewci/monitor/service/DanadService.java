package com.huitu.cloud.api.ewci.monitor.service;

import com.huitu.cloud.api.ewci.monitor.entity.IaCDanad;

import java.util.List;

/**
 * 危险区基本情况调查成果汇总服务
 *
 * <AUTHOR>
 */
public interface DanadService {

    /**
     * 获取危险区基本情况调查成果汇总列表
     *
     * @param type 类型 ad=按行政村，ws=按小流域，空=按当前政区代码
     * @param adcd 行政区划代码
     * @return 汇总列表
     **/
    List<IaCDanad> getDanadList(String type, String adcd);
}
