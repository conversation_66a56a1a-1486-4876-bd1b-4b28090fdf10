package com.huitu.cloud.api.hsfxt.zxhl.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 转移方案(屯详情)
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-25
 */
@ApiModel(value = "ZxhlZhuanYiFangAn对象", description = "转移方案")
public class ZxhlZhuanYiFangAn implements Serializable {
    @ApiModelProperty(value = "转移方案编码")
    private String zhuanyifanganbianma;

    @ApiModelProperty(value = "转移方案名称")
    private String zhuanyifanganmingcheng;

    @ApiModelProperty(value = "转移路线编码")
    private String zhuanyiluxianbianma;

    @ApiModelProperty(value = "转移单元编码")
    private String zhuanyidanyuanbianma;

    @ApiModelProperty(value = "转移路线名称")
    private String zhuanyiluxianmingcheng;

    @ApiModelProperty(value = "安置区编码")
    private String anzhiqubianma;

    @ApiModelProperty(value = "安置区名称")
    private String anzhiqumingcheng;

    @ApiModelProperty(value = "转移路线长度")
    private String zhuanyiluxianchangdu;

    @ApiModelProperty(value = "转移时间")
    private String zhuanyishijian;

    public String getZhuanyidanyuanbianma() {
        return zhuanyidanyuanbianma;
    }

    public void setZhuanyidanyuanbianma(String zhuanyidanyuanbianma) {
        this.zhuanyidanyuanbianma = zhuanyidanyuanbianma;
    }

    public String getZhuanyifanganbianma() {
        return zhuanyifanganbianma;
    }

    public void setZhuanyifanganbianma(String zhuanyifanganbianma) {
        this.zhuanyifanganbianma = zhuanyifanganbianma;
    }

    public String getZhuanyifanganmingcheng() {
        return zhuanyifanganmingcheng;
    }

    public void setZhuanyifanganmingcheng(String zhuanyifanganmingcheng) {
        this.zhuanyifanganmingcheng = zhuanyifanganmingcheng;
    }

    public String getZhuanyiluxianbianma() {
        return zhuanyiluxianbianma;
    }

    public void setZhuanyiluxianbianma(String zhuanyiluxianbianma) {
        this.zhuanyiluxianbianma = zhuanyiluxianbianma;
    }

    public String getZhuanyiluxianmingcheng() {
        return zhuanyiluxianmingcheng;
    }

    public void setZhuanyiluxianmingcheng(String zhuanyiluxianmingcheng) {
        this.zhuanyiluxianmingcheng = zhuanyiluxianmingcheng;
    }

    public String getAnzhiqubianma() {
        return anzhiqubianma;
    }

    public void setAnzhiqubianma(String anzhiqubianma) {
        this.anzhiqubianma = anzhiqubianma;
    }

    public String getAnzhiqumingcheng() {
        return anzhiqumingcheng;
    }

    public void setAnzhiqumingcheng(String anzhiqumingcheng) {
        this.anzhiqumingcheng = anzhiqumingcheng;
    }

    public String getZhuanyiluxianchangdu() {
        return zhuanyiluxianchangdu;
    }

    public void setZhuanyiluxianchangdu(String zhuanyiluxianchangdu) {
        this.zhuanyiluxianchangdu = zhuanyiluxianchangdu;
    }

    public String getZhuanyishijian() {
        return zhuanyishijian;
    }

    public void setZhuanyishijian(String zhuanyishijian) {
        this.zhuanyishijian = zhuanyishijian;
    }
}
