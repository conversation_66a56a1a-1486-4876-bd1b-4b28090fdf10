package com.huitu.cloud.api.common.msg.entity;

import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public class MessageCommonQo {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd")
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd")
    private String etm;
    @ApiModelProperty(value = "类型（告警信息提醒：1，山洪预警信息提醒：2，异常数据提醒：3，强降雨提醒：4，定时必做提醒：5，收文：6，督查计划下发：7， 问题填报：8，问题确认：9，警示通报：10，风险预警：11）")
    private String type;
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "主键编码")
    private String id;
    @ApiModelProperty(value = "组织机构编码")
    private String deptid;
    @ApiModelProperty(value = "是否已处理标识（1：已处理，0：未处理）")
    private String flag;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
}
