package com.huitu.cloud.api.ewci.tencent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.ewci.tencent.entity.VideoPersonRecord;

import java.util.List;

/**
 * <p>
 * 会议人员记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface VideoPersonRecordService extends IService<VideoPersonRecord> {

    /**
     *
     * 查询并添加参会人员记录
     * @param videoPersonRecord 参会人员实体
     */
    void searchAndInsertRecord(VideoPersonRecord videoPersonRecord);

    /**
     * 退出房间更新参会人员记录
     *
     * @param videoPersonRecord 参会人员实体
     */
    void updateRecordForMemberLeave(VideoPersonRecord videoPersonRecord);

    /**
     * 解散房间更新参会人员记录
     *
     * @param videoPersonRecord 参会人员实体
     */
    void updateRecordForDestroyRoom(VideoPersonRecord videoPersonRecord);

    /**
     * 查询用户 部门 部门ID 政区 政区ID
     * @param userId 用户ID
     * @return VideoPersonRecord
     */
    VideoPersonRecord selectExtInfo(String userId);

    /**
     * 根据 roomId 查询参会人员
     * @param roomId 房间号
     * @return List<VideoPersonRecord>
     */
    List<VideoPersonRecord> selectByRoomId(String roomId);
}
