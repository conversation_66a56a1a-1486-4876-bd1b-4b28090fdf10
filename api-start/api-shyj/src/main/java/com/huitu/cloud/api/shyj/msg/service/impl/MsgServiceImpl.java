package com.huitu.cloud.api.shyj.msg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.shyj.msg.entity.MsgVo;
import com.huitu.cloud.api.shyj.msg.mapper.MsgDao;
import com.huitu.cloud.api.shyj.msg.service.MsgService;
import com.huitu.cloud.api.shyj.warn.entity.WarnrecordR;
import com.huitu.cloud.api.shyj.warn.mapper.WarnrecordDao;
import com.huitu.cloud.util.AdcdUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 消息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Service
public class MsgServiceImpl extends ServiceImpl<WarnrecordDao, WarnrecordR> implements MsgService {

    @Autowired
    private MsgDao msgDao;

    private static final String[] JLSBJ_LIST = {"","220201","220202","220203","220204","220211","220271","220272","220273"};
    private static final String[] THSBJ_LIST = {"","220501","220502","220503","220504","220505"};
    private static final String[] GLCS_LIST = {"","220101","220102","220103","220104","220105","220106","220171","220172","220173","220174","220301"
            ,"220302","220303","220401","220402","220403","220601","220701","220771","220801","220871","222296","222297","222298","222299"};

    @Override
    public List<MsgVo> getMsgSendReport(String adcd, String stm, String etm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("stm", stm);
        param.put("etm", etm);
        List<MsgVo> list1 = msgDao.getMsgSendReport1(param);
        List<MsgVo> list2 = msgDao.getMsgSendReport2(param);
        List<MsgVo> sjlist = new ArrayList<>();
        for(MsgVo add : list1){
            if(!"220000000000000".equals(add.getAdcd())){
                sjlist.add(add);
            }
        }
        for (MsgVo msgVo : sjlist) {
            String list1_ad = msgVo.getAdcd();
            if (StringUtils.isBlank(list1_ad)) {
                continue;
            }
            if (list1_ad.startsWith("220581000000000")) {
                continue;
            }
            int failed = 0;
            int success = 0;
            int failedBj = 0;
            int successBj = 0;
            List<MsgVo> listBj = new ArrayList<>();
            List<MsgVo> list = new ArrayList<>();
            for (MsgVo vo : list2) {
                if (StringUtils.isBlank(vo.getAdcd())) {
                    continue;
                }
                String list2_ad = vo.getAdcd().substring(0, 4);
                if (list1_ad.startsWith(list2_ad)) {
                    //判断 吉林市本级  else if 通化市本级 // 其他
                    String list3_ad = vo.getAdcd().substring(0, 6);
                    // 判断过滤不展示城市
                    if (Arrays.binarySearch(GLCS_LIST, list3_ad) < 0) {
                        if (Arrays.binarySearch(JLSBJ_LIST, list3_ad) > 0) {
                            failedBj += vo.getFailed();
                            successBj += vo.getSuccess();
                        } else if (Arrays.binarySearch(THSBJ_LIST, list3_ad) > 0) {
                            failedBj += vo.getFailed();
                            successBj += vo.getSuccess();
                        } else {
                            failed += vo.getFailed();
                            success += vo.getSuccess();
                            list.add(vo);
                        }
                    }
                }
            }
            msgVo.setSuccess(success + successBj);
            msgVo.setFailed(failed + failedBj);
            MsgVo benji = new MsgVo();
            benji.setList(listBj);
            benji.setFailed(failedBj);
            benji.setSuccess(successBj);
            benji.setAdlvl("3");
            if (list1_ad.equals("220200000000000")) {
                benji.setAdnm("吉林市本级");
                list.add(0, benji);
            }
            if (list1_ad.equals("220500000000000")) {
                benji.setAdnm("通化市本级");
                list.add(0, benji);
            }
            msgVo.setList(list);
        }
        return sjlist;
    }
}
