package com.huitu.cloud.api.datamanager.ststop.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.datamanager.ststop.entity.BsnStstopr;
import com.huitu.cloud.api.datamanager.ststop.entity.StStopQo;
import com.huitu.cloud.api.datamanager.ststop.entity.StstopVo;

import java.util.List;

/**
 * <p>
 * 站点屏蔽记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
public interface StstopService extends IService<BsnStstopr> {
    /**
     * 添加屏蔽记录
     * @param stStopQo
     * @return
     */
    String saveStStop(StStopQo stStopQo);

    /**
     * 查询当前未停用的屏蔽记录
     * @param adcd 政区编码
     * @param isAll 是否查询所有（包含未来的计划性屏蔽）， 1：是 0:否 默认否
     * @return
     */
    List<StstopVo> getStStopInfos(String adcd, String isAll,String isShow);

    /**
     * 关闭屏蔽记录
     * @param stcd 测站编码
     * @return
     */
    String closeStStop(String stcd);

    /**
     * 关闭屏蔽记录
     * @param stcd 测站编码
     * @param tbnm 屏蔽要素
     * @return
     */
    String closeStStopByTbnm(String stcd, String tbnm);
    /**
     * 根据测站编码与要素校验是否已存在屏蔽记录
     * @param stcd 测站编码
     * @param tbnm 屏蔽要素
     * @return 1 存在 ，0 不存在
     */
    String checkStStopByTbnmAndStcd(String stcd, String tbnm);

}
