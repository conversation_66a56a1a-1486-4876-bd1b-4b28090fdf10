package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@TableName("ST_RVYEVSQ_S")
@ApiModel(value="StRvyevsqS对象", description="")
public class StRvyevsqS extends Model<StRvyevsqS> {

    private static final long serialVersionUID=1L;


    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "年份")
    @TableField("YR")
    private BigDecimal yr;

    @ApiModelProperty(value = "最高水位")
    @TableField("HTZ")
    private BigDecimal htz;

    @ApiModelProperty(value = "最高水位出现时间")
    @TableField("HTZTM")
    private LocalDateTime htztm;

    @ApiModelProperty(value = "最大流量")
    @TableField("MXQ")
    private BigDecimal mxq;

    @ApiModelProperty(value = "最大流量出现时间")
    @TableField("MXQTM")
    private LocalDateTime mxqtm;

    @ApiModelProperty(value = "最低水位")
    @TableField("LTZ")
    private BigDecimal ltz;

    @ApiModelProperty(value = "最低水位出现时间")
    @TableField("LTZTM")
    private LocalDateTime ltztm;

    @ApiModelProperty(value = "最小流量")
    @TableField("MNQ")
    private BigDecimal mnq;

    @ApiModelProperty(value = "最小流量出现时间")
    @TableField("MNQTM")
    private LocalDateTime mnqtm;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public BigDecimal getYr() {
        return yr;
    }

    public void setYr(BigDecimal yr) {
        this.yr = yr;
    }

    public LocalDateTime getLtztm() {
        return ltztm;
    }

    public void setLtztm(LocalDateTime ltztm) {
        this.ltztm = ltztm;
    }

    public BigDecimal getMxq() {
        return mxq;
    }

    public void setMxq(BigDecimal mxq) {
        this.mxq = mxq;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    public BigDecimal getHtz() {
        return htz;
    }

    public void setHtz(BigDecimal htz) {
        this.htz = htz;
    }

    public LocalDateTime getMxqtm() {
        return mxqtm;
    }

    public void setMxqtm(LocalDateTime mxqtm) {
        this.mxqtm = mxqtm;
    }

    public BigDecimal getMnq() {
        return mnq;
    }

    public void setMnq(BigDecimal mnq) {
        this.mnq = mnq;
    }

    public LocalDateTime getHtztm() {
        return htztm;
    }

    public void setHtztm(LocalDateTime htztm) {
        this.htztm = htztm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public BigDecimal getLtz() {
        return ltz;
    }

    public void setLtz(BigDecimal ltz) {
        this.ltz = ltz;
    }

    public LocalDateTime getMnqtm() {
        return mnqtm;
    }

    public void setMnqtm(LocalDateTime mnqtm) {
        this.mnqtm = mnqtm;
    }

    @Override
    protected Serializable pkVal() {
        return null;
    }

    @Override
    public String toString() {
        return "StRvyevsqS{" +
        "yr=" + yr +
        ", ltztm=" + ltztm +
        ", mxq=" + mxq +
        ", moditime=" + moditime +
        ", htz=" + htz +
        ", mxqtm=" + mxqtm +
        ", mnq=" + mnq +
        ", htztm=" + htztm +
        ", stcd=" + stcd +
        ", ltz=" + ltz +
        ", mnqtm=" + mnqtm +
        "}";
    }
}
