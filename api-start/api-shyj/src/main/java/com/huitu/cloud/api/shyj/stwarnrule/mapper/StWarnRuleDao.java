package com.huitu.cloud.api.shyj.stwarnrule.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.stwarnrule.entity.RainWarnRule;
import com.huitu.cloud.api.shyj.stwarnrule.entity.SwWarnRule;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 测站预警规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-27
 */
public interface StWarnRuleDao {
    /**
     * 雨量预警指标列表查询
     * @param page 分页参数
     * @param param 查询参数
     * @return
     */
    IPage<RainWarnRule> getRainWarnrule(Page page, @Param("map") Map<String, Object> param);
    /**
     * 水位预警指标列表查询
     * @param page 分页参数
     * @param param 查询参数
     * @return
     */
    IPage<SwWarnRule> getSwWarnrule(Page page, @Param("map") Map<String, Object> param);

}
