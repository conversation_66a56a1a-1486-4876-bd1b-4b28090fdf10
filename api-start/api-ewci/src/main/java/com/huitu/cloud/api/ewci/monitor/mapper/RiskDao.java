package com.huitu.cloud.api.ewci.monitor.mapper;

import com.huitu.cloud.api.ewci.monitor.entity.RainData;
import com.huitu.cloud.api.ewci.monitor.entity.RiskLocation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 风险 周边降雨Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface RiskDao {

    /**
     * 根据测站编码查询政区、经度、纬度
     *
     * @param stcd 测站编码
     * @return
     */
    RiskLocation getLocation(String stcd);

    /**
     * 风险 周边降雨 行政村查询
     *
     * @param params 查询参数
     * @return 信息列表
     **/
    List<RainData> getRainfallByAd(@Param("map") Map<String, Object> params);

    /**
     * 风险 周边降雨 小流域查询
     *
     * @param params
     * @return
     */
    List<RainData> getRainfallByWs(@Param("map") Map<String, Object> params);

    /**
     * 风险 周边降雨 范围查询
     *
     * @param params
     * @return
     */
    List<RainData> getRainfallByRange(@Param("map") Map<String, Object> params);
}
