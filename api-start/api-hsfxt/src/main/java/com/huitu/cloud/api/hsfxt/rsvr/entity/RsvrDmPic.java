package com.huitu.cloud.api.hsfxt.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 河道断面图
 * </p>
 *
 * <AUTHOR>
 * @since 2020-1-6
 */
@ApiModel(value="RsvrDmPic对象", description="河道断面图")
public class RsvrDmPic implements Serializable {

    @ApiModelProperty(value = "距大坝距离")
    @TableField("CDIST")
    private Double cdist;

    @ApiModelProperty(value = "高程")
    @TableField("AMEL")
    private Double amel;

    @ApiModelProperty(value = "最高水位")
    @TableField("Z")
    private Double z;

    public Double getCdist() {
        return cdist;
    }

    public void setCdist(Double cdist) {
        this.cdist = cdist;
    }

    public Double getAmel() {
        return amel;
    }

    public void setAmel(Double amel) {
        this.amel = amel;
    }

    public Double getZ() {
        return z;
    }

    public void setZ(Double z) {
        this.z = z;
    }
}
