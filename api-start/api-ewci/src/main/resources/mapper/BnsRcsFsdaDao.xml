<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rcs.mapper.BnsRcsFsdaDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.rcs.entity.BnsRcsFsda" useCache="false">
        SELECT F.FSDA_CODE, F.FSDA_NAME, F.ADCD, A.ADNM, F.FSDA_TYPE, F.LGTD, F.LTTD, F.COMPLIANCED, F.COMPLETED, F.TS
        FROM BNS_RCS_FSDA F LEFT JOIN BSN_ADCD_B A ON A.ADCD = F.ADCD
        WHERE LEFT(F.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(F.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.fsdaName!= null and map.fsdaName !=''">
            AND CHARINDEX(#{map.fsdaName}, F.FSDA_NAME) > 0
        </if>
        <if test="map.fsdaType !=null and map.fsdaType !=''">
            AND F.FSDA_TYPE = #{map.fsdaType}
        </if>
        <if test="map.complianced !=null and map.complianced !=''">
            AND F.COMPLIANCED = #{map.complianced}
        </if>
        <if test="map.completed !=null and map.completed !=''">
            AND F.COMPLETED = #{map.completed}
        </if>
        ORDER BY F.ADCD, F.FSDA_CODE ASC
    </select>
    <select id="getStatListByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT F.ADCD, A.ADNM, '08' ENTYPE, COUNT(F.FSDA_CODE) TOTAL_COUNT
        FROM (
        SELECT FSDA_CODE, LEFT(LEFT(ADCD, #{map.lowerLevel}) + '000000000000000', 15) ADCD FROM BNS_RCS_FSDA
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT FSDA_CODE, '220581000000000' ADCD FROM BNS_RCS_FSDA WHERE LEFT (ADCD, 6) = '220581'
        </if>
        ) F
        LEFT JOIN BSN_ADCD_B A ON A.ADCD = F.ADCD
        GROUP BY F.ADCD, A.ADNM
        ORDER BY F.ADCD ASC
    </select>
    <select id="getStatByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT A.ADCD, A.ADNM, '08' ENTYPE, F.TOTAL_COUNT
        FROM BSN_ADCD_B A,
        (SELECT COUNT(FSDA_CODE) TOTAL_COUNT FROM BNS_RCS_FSDA
        WHERE LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) F
        WHERE A.ADCD = #{map.adcd}
    </select>
    <select id="getCountListWithType" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(FSDA_TYPE, '0') PROP_CODE, COUNT(FSDA_CODE) PROP_COUNT
        FROM BNS_RCS_FSDA
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY FSDA_TYPE
        ORDER BY FSDA_TYPE ASC
    </select>
    <select id="getCountListWithComplianced" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(COMPLIANCED, '0') PROP_CODE, COUNT(FSDA_CODE) PROP_COUNT
        FROM BNS_RCS_FSDA
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY COMPLIANCED
        ORDER BY COMPLIANCED ASC
    </select>
    <select id="getCountListWithCompleted" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(COMPLETED, '0') PROP_CODE, COUNT(FSDA_CODE) PROP_COUNT
        FROM BNS_RCS_FSDA
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY COMPLETED
        ORDER BY COMPLETED ASC
    </select>
</mapper>
