package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value="逐站24小时雨情信息")
public class OneDayRain implements Serializable {
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "备注")
    private String comments;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "区县名称")
    private String xadnm;

    @ApiModelProperty(value = "测站归属类别名称")
    private String stadtpnm;

    @ApiModelProperty(value = "信息管理单位")
    private String admauth;

    @ApiModelProperty(value = "累计雨量")
    private String drps;
    @ApiModelProperty(value = "0点时段雨量")
    private String drp0;
    @ApiModelProperty(value = "1点时段雨量")
    private String drp1;
    @ApiModelProperty(value = "2点时段雨量")
    private String drp2;
    @ApiModelProperty(value = "3点时段雨量")
    private String drp3;
    @ApiModelProperty(value = "4点时段雨量")
    private String drp4;
    @ApiModelProperty(value = "5点时段雨量")
    private String drp5;
    @ApiModelProperty(value = "6点时段雨量")
    private String drp6;

    @ApiModelProperty(value = "7点时段雨量")
    private String drp7;
    @ApiModelProperty(value = "8点时段雨量")
    private String drp8;
    @ApiModelProperty(value = "9点时段雨量")
    private String drp9;
    @ApiModelProperty(value = "10点时段雨量")
    private String drp10;
    @ApiModelProperty(value = "11点时段雨量")
    private String drp11;
    @ApiModelProperty(value = "12点时段雨量")
    private String drp12;

    @ApiModelProperty(value = "13点时段雨量")
    private String drp13;
    @ApiModelProperty(value = "14点时段雨量")
    private String drp14;
    @ApiModelProperty(value = "15点时段雨量")
    private String drp15;
    @ApiModelProperty(value = "16点时段雨量")
    private String drp16;
    @ApiModelProperty(value = "17点时段雨量")
    private String drp17;
    @ApiModelProperty(value = "18点时段雨量")
    private String drp18;
    @ApiModelProperty(value = "19点时段雨量")
    private String drp19;
    @ApiModelProperty(value = "20点时段雨量")
    private String drp20;
    @ApiModelProperty(value = "21点时段雨量")
    private String drp21;
    @ApiModelProperty(value = "22点时段雨量")
    private String drp22;
    @ApiModelProperty(value = "23点时段雨量")
    private String drp23;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public String getAdmauth() {
        return admauth;
    }

    public void setAdmauth(String admauth) {
        this.admauth = admauth;
    }

    public String getDrps() {
        return drps;
    }

    public void setDrps(String drps) {
        this.drps = drps;
    }

    public String getDrp0() {
        return drp0;
    }

    public void setDrp0(String drp0) {
        this.drp0 = drp0;
    }

    public String getDrp1() {
        return drp1;
    }

    public void setDrp1(String drp1) {
        this.drp1 = drp1;
    }

    public String getDrp2() {
        return drp2;
    }

    public void setDrp2(String drp2) {
        this.drp2 = drp2;
    }

    public String getDrp3() {
        return drp3;
    }

    public void setDrp3(String drp3) {
        this.drp3 = drp3;
    }

    public String getDrp4() {
        return drp4;
    }

    public void setDrp4(String drp4) {
        this.drp4 = drp4;
    }

    public String getDrp5() {
        return drp5;
    }

    public void setDrp5(String drp5) {
        this.drp5 = drp5;
    }

    public String getDrp6() {
        return drp6;
    }

    public void setDrp6(String drp6) {
        this.drp6 = drp6;
    }

    public String getDrp7() {
        return drp7;
    }

    public void setDrp7(String drp7) {
        this.drp7 = drp7;
    }

    public String getDrp8() {
        return drp8;
    }

    public void setDrp8(String drp8) {
        this.drp8 = drp8;
    }

    public String getDrp9() {
        return drp9;
    }

    public void setDrp9(String drp9) {
        this.drp9 = drp9;
    }

    public String getDrp10() {
        return drp10;
    }

    public void setDrp10(String drp10) {
        this.drp10 = drp10;
    }

    public String getDrp11() {
        return drp11;
    }

    public void setDrp11(String drp11) {
        this.drp11 = drp11;
    }

    public String getDrp12() {
        return drp12;
    }

    public void setDrp12(String drp12) {
        this.drp12 = drp12;
    }

    public String getDrp13() {
        return drp13;
    }

    public void setDrp13(String drp13) {
        this.drp13 = drp13;
    }

    public String getDrp14() {
        return drp14;
    }

    public void setDrp14(String drp14) {
        this.drp14 = drp14;
    }

    public String getDrp15() {
        return drp15;
    }

    public void setDrp15(String drp15) {
        this.drp15 = drp15;
    }

    public String getDrp16() {
        return drp16;
    }

    public void setDrp16(String drp16) {
        this.drp16 = drp16;
    }

    public String getDrp17() {
        return drp17;
    }

    public void setDrp17(String drp17) {
        this.drp17 = drp17;
    }

    public String getDrp18() {
        return drp18;
    }

    public void setDrp18(String drp18) {
        this.drp18 = drp18;
    }

    public String getDrp19() {
        return drp19;
    }

    public void setDrp19(String drp19) {
        this.drp19 = drp19;
    }

    public String getDrp20() {
        return drp20;
    }

    public void setDrp20(String drp20) {
        this.drp20 = drp20;
    }

    public String getDrp21() {
        return drp21;
    }

    public void setDrp21(String drp21) {
        this.drp21 = drp21;
    }

    public String getDrp22() {
        return drp22;
    }

    public void setDrp22(String drp22) {
        this.drp22 = drp22;
    }

    public String getDrp23() {
        return drp23;
    }

    public void setDrp23(String drp23) {
        this.drp23 = drp23;
    }
}
