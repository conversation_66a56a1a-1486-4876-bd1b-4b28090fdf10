package com.huitu.cloud.api.shyj.evaluation.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;
/**
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public class QueryIaGully extends PageBean {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "对象名称")
    private String name;
    @ApiModelProperty(value = "对象id(查单个对象时，其他参数置空)")
    private String key;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
