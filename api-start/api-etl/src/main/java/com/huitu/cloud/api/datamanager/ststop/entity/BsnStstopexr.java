package com.huitu.cloud.api.datamanager.ststop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 站点屏蔽原因表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-8-26
 */
@TableName("BSN_STSTOPEX_R")
@ApiModel(value="BsnStstopexR对象", description="站点屏蔽原因表")
public class BsnStstopexr extends Model<BsnStstopexr> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "站码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "是否展示0-显示 1-不显示")
    @TableField("IS_SHOW")
    private String isShow;

    @ApiModelProperty(value = "屏蔽原因")
    @TableField("REASON")
    private String reason;

    @ApiModelProperty(value = "屏蔽要素 雨量：st_pptn_r , 河道水位：st_river_r, 水库水位：st_rsvr_r ")
    @TableField("FACTOR")
    private String factor;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getIsshow() {
        return isShow;
    }

    public void setIsshow(String isshow) {
        this.isShow = isshow;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getIsShow() {
        return isShow;
    }

    public void setIsShow(String isShow) {
        this.isShow = isShow;
    }

    public String getFactor() {
        return factor;
    }

    public void setFactor(String factor) {
        this.factor = factor;
    }

    @Override
    public String toString() {
        return "BsnStstopexr{" +
                "stcd='" + stcd + '\'' +
                ", isShow='" + isShow + '\'' +
                ", reason='" + reason + '\'' +
                ", factor='" + factor + '\'' +
                '}';
    }
}
