<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.base.mapper.OnlineMonitorDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getOnlineWorkAdcdList" resultType="com.huitu.cloud.api.ewci.base.entity.OnlineWorkAdcdVo">
        SELECT O.ADCD                                                                          ADCD,
               M.ADNM                                                                          ADNM,
               COUNT(DISTINCT O.STCD)                                                          TOTAL,
               SUM(O.ZCNUM)                                                                    ZCNUM,
               SUM(O.YCNUM)                                                                    YCNUM,
               SUM(O.LBNUM)                                                                    LBNUM,
               CAST((COUNT(DISTINCT O.STCD) - SUM(O.YCNUM) - SUM(O.LBNUM)) / COUNT(DISTINCT O.STCD) AS NUMERIC(4,3)) NORRATE
        FROM (SELECT A.STCD, LEFT (A.ADCD, 6)+'000000000' ADCD, B.V_CTRL, C.VOLTAGE, CASE
                  WHEN C.VOLTAGE > B.V_CTRL THEN 1
                  ELSE 0
                  END ZCNUM, CASE
                  WHEN C.VOLTAGE &lt;= B.V_CTRL THEN 1
                  ELSE 0
                  END YCNUM, CASE
                  WHEN C.VOLTAGE IS NULL THEN 1
                  ELSE 0
                  END LBNUM
              FROM ST_STBPRP_COMPARE A
                  LEFT JOIN ST_RUNCTRL_B B
              ON A.STCD = B.STCD
                  LEFT JOIN (SELECT V.STCD,V.VOLTAGE FROM (SELECT ROW_NUMBER() over(partition BY STCD order by TM DESC) NO,VOLTAGE VOLTAGE,STCD STCD FROM ST_RUNINFO_R WHERE
                  TM >= #{map.stm}
                  AND TM &lt;= #{map.etm}) V WHERE V.NO = 1 ) C ON A.STCD = C.STCD
              WHERE LEFT (A.ADCD
                  , #{map.level}) = LEFT (#{map.adcd}
                  , #{map.level})) O
                 LEFT JOIN MDT_ADCDINFO_B M ON O.ADCD = M.ADCD
        GROUP BY O.ADCD, M.ADNM
        ORDER BY NORRATE DESC, O.ADCD ASC
    </select>
    <select id="getOnlineWorkCompList" resultType="com.huitu.cloud.api.ewci.base.entity.OnlineWorkCompVo">
        SELECT O.COMPCD                                                                        COMPCD,
               M.COMPNM                                                                        COMPNM,
               COUNT(DISTINCT O.STCD)                                                          TOTAL,
               SUM(O.ZCNUM)                                                                    ZCNUM,
               SUM(O.YCNUM)                                                                    YCNUM,
               SUM(O.LBNUM)                                                                    LBNUM,
               CAST((COUNT(DISTINCT O.STCD) - SUM(O.YCNUM) - SUM(O.LBNUM)) / COUNT(DISTINCT O.STCD) AS NUMERIC(4,3)) NORRATE
        FROM (SELECT A.STCD,
                     A.COMPCD COMPCD,
                     B.V_CTRL,
                     C.VOLTAGE,
                     CASE
                         WHEN C.VOLTAGE > B.V_CTRL THEN 1
                         ELSE 0
                         END  ZCNUM,
                     CASE
                         WHEN C.VOLTAGE &lt;= B.V_CTRL THEN 1
                         ELSE 0
                         END  YCNUM,
                     CASE
                         WHEN C.VOLTAGE IS NULL THEN 1
                         ELSE 0
                         END  LBNUM
              FROM ST_STBPRP_COMPARE A
                       LEFT JOIN ST_RUNCTRL_B B ON A.STCD = B.STCD
                       left join (SELECT V.STCD, V.VOLTAGE
                                  from (SELECT ROW_NUMBER() over(partition BY STCD order by TM DESC) NO,VOLTAGE VOLTAGE,STCD STCD
                                        FROM ST_RUNINFO_R
                                        WHERE TM >= #{map.stm}
                                          AND TM &lt;= #{map.etm}) V
                                  WHERE V.NO = 1) C ON A.STCD = C.STCD
                       LEFT JOIN ST_COMPDUTY_B D ON A.ADCD = D.ADCD AND A.COMPCD = D.COMPCD
              WHERE LEFT (A.ADCD
                  , #{map.level}) = LEFT (#{map.adcd}
                  , #{map.level})) O
                 LEFT JOIN ST_COMP_B M ON O.COMPCD = M.COMPCD
        GROUP BY O.COMPCD, M.COMPNM
        ORDER BY NORRATE DESC
    </select>
    <select id="getOnlineWorkDetailList"
            resultType="com.huitu.cloud.api.ewci.base.entity.OnlineWorkDetailVo">
        SELECT A.STCD
        , STB.STNM
        , STB.STTP
        , A.ADCD
        , B.V_CTRL
        , C.VOLTAGE
        , TMP
        , CHARGE_STATE
        , BATTERY_OVER_TIMES
        , BATTERY_FULL_TIMES
        , BATTERY_SOC
        , BATTERY_VOLTAGE
        , BATTERY_CURRENT
        , SOLAR_VOLTAGE
        , SOLAR_CURRENT
        , DAY_POWER_CHARGE
        , DAY_POWER_CONSUME
        , SIGNAL
        , ISP
        , ETH_INT_STATE
        , POWER_UP_TIME
        ,CASE
        WHEN C.VOLTAGE > B.V_CTRL AND C.VOLTAGE IS NOT NULL THEN '1'
        WHEN C.VOLTAGE &lt;= B.V_CTRL AND C.VOLTAGE IS NOT NULL THEN '2'
        ELSE '3'
        END STATUS
        FROM ST_STBPRP_COMPARE A
        LEFT JOIN ST_STBPRP_B STB ON A.STCD = STB.STCD
        LEFT JOIN ST_RUNCTRL_B B
        ON A.STCD = B.STCD
        LEFT JOIN (SELECT V.STCD
        , V.VOLTAGE
        , TMP
        , CHARGE_STATE
        , BATTERY_OVER_TIMES
        , BATTERY_FULL_TIMES
        , BATTERY_SOC
        , BATTERY_VOLTAGE
        , BATTERY_CURRENT
        , SOLAR_VOLTAGE
        , SOLAR_CURRENT
        , DAY_POWER_CHARGE
        , DAY_POWER_CONSUME
        , SIGNAL
        , ISP
        , ETH_INT_STATE
        , POWER_UP_TIME
        FROM (SELECT ROW_NUMBER() over(partition BY STCD order by TM DESC) NO,STCD,VOLTAGE ,TMP ,CHARGE_STATE
        ,BATTERY_OVER_TIMES,BATTERY_FULL_TIMES,BATTERY_SOC,BATTERY_VOLTAGE,BATTERY_CURRENT,SOLAR_VOLTAGE,SOLAR_CURRENT,DAY_POWER_CHARGE,DAY_POWER_CONSUME,SIGNAL,ISP,ETH_INT_STATE,POWER_UP_TIME
        FROM ST_RUNINFO_R
        WHERE TM >= #{map.stm}
        AND TM &lt;= #{map.etm}) V
        WHERE V.NO = 1) C ON A.STCD = C.STCD
        WHERE
        1=1
        <if test="map.adcd != null and map.adcd != ''">
            and left(A.ADCD,#{map.level})=left(#{map.adcd},#{map.level})
        </if>
        <if test="map.compcd != null and map.compcd != ''">
            AND A.ADCD IN (SELECT ADCD FROM ST_STBPRP_COMPARE WHERE COMPCD = #{map.compcd})
        </if>
        <if test="map.list !=null and  map.list.size() >0 ">
            AND(
            <foreach item="item" index="index" collection="map.list" separator="or">
                <if test="item=='1'.toString()">
                    (C.VOLTAGE > B.V_CTRL AND C.VOLTAGE IS NOT NULL)
                </if>
                <if test="item=='2'.toString()">
                    (C.VOLTAGE &lt;= B.V_CTRL AND C.VOLTAGE IS NOT NULL)
                </if>
                <if test="item=='3'.toString()">
                    (C.VOLTAGE IS NULL)
                </if>
            </foreach>
            )
        </if>
        ORDER BY STATUS DESC,A.ADCD ASC,STB.STCD ASC
    </select>
    <select id="getOnlineWorkWarnList" resultType="com.huitu.cloud.api.ewci.base.entity.OnlineWorkWarnVo">
        SELECT A.WARNID,A.ADCD,A.COMPCD,A.WARNTM,A.WARNNM,A.WARNDESC,A.REMARK,C.COMPCD,C.COMPNM,D.CONTACT,D.PHONE,M.ADNM
        FROM ST_RUNWARN_R A
        LEFT JOIN ST_COMP_B C ON A.COMPCD = C.COMPCD
        LEFT JOIN ST_COMPDUTY_B D ON A.COMPCD = D.COMPCD AND A.ADCD = D.ADCD
        LEFT JOIN MDT_ADCDINFO_B M ON A.ADCD = M.ADCD
        WHERE 1=1
        <if test="map.include == '1'.toString()">
            AND A.ADCD = #{map.adcd}
        </if>
        <if test="map.include == '0'.toString()">
            AND LEFT(A.ADCD,#{map.level})=LEFT(#{map.adcd},#{map.level})
        </if>
        <if test="map.stm != null and map.stm != ''">
            AND A.WARNTM >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm != ''">
            AND A.WARNTM &lt;= #{map.etm}
        </if>
        ORDER BY A.WARNTM DESC, A.ADCD ASC
    </select>
    <select id="getOnlineWorkWarnBaseList"
            resultType="com.huitu.cloud.api.ewci.base.entity.OnlineWorkWarnBaseVo">
        SELECT A.STCD, A.WARNID, A.TM, A.STATE, B.STNM, B.RVNM, B.STLC
        FROM ST_RUNWARN_STCD_R A
                 LEFT JOIN ST_STBPRP_B B ON A.STCD = B.STCD
        WHERE A.WARNID = #{warnId}
        ORDER BY A.TM DESC
    </select>

</mapper>