package com.huitu.cloud.api.ew.entity.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huitu.cloud.api.ew.entity.base.EwDisasterRecord;
import com.huitu.cloud.api.ew.entity.common.FileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * 灾情记录响应对象
 */
@ApiModel(value = "灾情记录响应对象")
public class EwDisasterRecordResponse extends EwDisasterRecord {
    private static final long serialVersionUID = 1L;

    @ColumnWidth(value = 10)
    @ExcelProperty(value = "序号", index = 0)
    @JsonIgnore
    private Integer sortno;
    @ColumnWidth(value = 18)
    @ExcelProperty(value = "县（市、区）", index = 1)
    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;
    @ExcelIgnore
    @ApiModelProperty(value = "创建人别名")
    @TableField(value = "CREATE_BY_ALIAS")
    private String createByAlias;
    @ExcelIgnore
    @ApiModelProperty(value = "最后更新人别名")
    @TableField(value = "LATEST_BY_ALIAS")
    private String latestByAlias;

    @ExcelIgnore
    @ApiModelProperty(value = "现场照片")
    private List<FileInfo> pictureList;
    @ExcelIgnore
    @ApiModelProperty(value = "现场视频")
    private List<FileInfo> videoList;

    public Integer getSortno() {
        return sortno;
    }

    public void setSortno(Integer sortno) {
        this.sortno = sortno;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getCreateByAlias() {
        return createByAlias;
    }

    public void setCreateByAlias(String createByAlias) {
        this.createByAlias = createByAlias;
    }

    public String getLatestByAlias() {
        return latestByAlias;
    }

    public void setLatestByAlias(String latestByAlias) {
        this.latestByAlias = latestByAlias;
    }

    public List<FileInfo> getPictureList() {
        if (null == pictureList) {
            pictureList = new ArrayList<>();
        }
        return pictureList;
    }

    public void setPictureList(List<FileInfo> pictureList) {
        this.pictureList = pictureList;
    }

    public List<FileInfo> getVideoList() {
        if (null == videoList) {
            videoList = new ArrayList<>();
        }
        return videoList;
    }

    public void setVideoList(List<FileInfo> videoList) {
        this.videoList = videoList;
    }
}
