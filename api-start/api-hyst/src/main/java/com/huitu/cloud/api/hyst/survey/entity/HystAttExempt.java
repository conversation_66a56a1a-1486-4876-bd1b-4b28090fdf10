package com.huitu.cloud.api.hyst.survey.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 小水电免考信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-30
 */
@TableName("HYST_ATT_EXEMPT")
@ApiModel(value = "HystAttExempt对象", description = "小水电免考信息表")
public class HystAttExempt extends Model<HystAttExempt> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水电站统计代码")
    @TableId(value = "HYST_CODE")
    private String hystCode;

    @ApiModelProperty(value = "免考开始日期")
    @TableField("EXEMPT_START")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exemptStart;

    @ApiModelProperty(value = "免考结束日期")
    @TableField("EXEMPT_END")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exemptEnd;

    @ApiModelProperty(value = "")
    @TableField("EXEMPT_REASON")
    private String exemptReason;

    public String getHystCode() {
        return hystCode;
    }

    public void setHystCode(String hystCode) {
        this.hystCode = hystCode;
    }

    public Date getExemptStart() {
        return exemptStart;
    }

    public void setExemptStart(Date exemptStart) {
        this.exemptStart = exemptStart;
    }

    public Date getExemptEnd() {
        return exemptEnd;
    }

    public void setExemptEnd(Date exemptEnd) {
        this.exemptEnd = exemptEnd;
    }

    public String getExemptReason() {
        return exemptReason;
    }

    public void setExemptReason(String exemptReason) {
        this.exemptReason = exemptReason;
    }
}