<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>api-start</artifactId>
        <groupId>com.huitu.cloud</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>api-all</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-core</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
        <groupId>com.huitu.cloud</groupId>
        <artifactId>api-shyj</artifactId>
        <version>1.0-SNAPSHOT</version>
        <scope>compile</scope>
    </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-shuiyuqing</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-usif</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-duty</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-xxjh</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-qx</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-dzsj</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-base</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.huitu.cloud</groupId>-->
<!--            <artifactId>api-datamanager</artifactId>-->
<!--            <version>1.0-SNAPSHOT</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-hsfxt</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-skdt</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-etl</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-cameras</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-om</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-ewci</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-screen</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-call</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-hyst</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-risk</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-cros</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!-- 即将作废的预警模块 BEGIN -->
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-ew</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-fusion</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!-- 即将作废的预警模块 END -->
        <!--<dependency>
            <groupId>com.huitu.cloud</groupId>
            <artifactId>api-warn</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>-->

        <!-- test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.9.RELEASE</version>
                <configuration>
                    <!-- 指定该Main Class为全局的唯一入口 -->
                    <mainClass>com.huitu.cloud.ApiApplication</mainClass>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

            <resources>

                <resource>
                    <directory>src/main/java</directory>
                    <includes>
                        <include>**/*.properties</include>
                        <include>**/*.xml</include>
                        <include>**/*.tld</include>
                        <include>**/*.*</include>
                    </includes>
                    <filtering>false</filtering>
                </resource>
            </resources>

    </build>




</project>