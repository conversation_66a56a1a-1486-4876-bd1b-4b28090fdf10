package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@ApiModel(value="EarlyWarningVo对象", description="")
public class RiskWarningsVo {

    private static final long serialVersionUID=1L;

    /**
     * 村庄预警集合
     */
    private List<EwVillageVo> villageVoList;

    /**
     * 河道预警集合
     */
    private List<EwRivelVo> ewRivelVoList;

    /**
     * 水库预警集合
     */
    private List<EwReserVo> ewReserVoList;

    /**
     * 村庄一小时预警集合
     */
    private List<EarlyWarningVo> earlyWarningVillageVo1List;

    /**
     * 河道一小时预警集合
     */
    private List<EarlyWarningVo> earlyWarningEwRivelVo1List;

    /**
     * 水库一小时预警集合
     */
    private List<EarlyWarningVo> earlyWarningEwReserVo1List;

    /**
     * 村庄三小时预警集合
     */
    private List<EarlyWarningVo> earlyWarningVillageVo3List;

    /**
     * 河道三小时预警集合
     */
    private List<EarlyWarningVo> earlyWarningEwRivelVo3List;

    /**
     * 水库三小时预警集合
     */
    private List<EarlyWarningVo> earlyWarningEwReserVo3List;

    /**
     * 村庄六小时预警集合
     */
    private List<EarlyWarningVo> earlyWarningVillageVo6List;

    /**
     * 河道六小时预警集合
     */
    private List<EarlyWarningVo> earlyWarningEwRivelVo6List;

    /**
     * 水库六小时预警集合
     */
    private List<EarlyWarningVo> earlyWarningEwReserVo6List;

    public List<EwVillageVo> getVillageVoList() {
        return villageVoList;
    }

    public void setVillageVoList(List<EwVillageVo> villageVoList) {
        this.villageVoList = villageVoList;
    }

    public List<EwRivelVo> getEwRivelVoList() {
        return ewRivelVoList;
    }

    public void setEwRivelVoList(List<EwRivelVo> ewRivelVoList) {
        this.ewRivelVoList = ewRivelVoList;
    }

    public List<EwReserVo> getEwReserVoList() {
        return ewReserVoList;
    }

    public void setEwReserVoList(List<EwReserVo> ewReserVoList) {
        this.ewReserVoList = ewReserVoList;
    }

    public List<EarlyWarningVo> getEarlyWarningVillageVo1List() {
        return earlyWarningVillageVo1List;
    }

    public void setEarlyWarningVillageVo1List(List<EarlyWarningVo> earlyWarningVillageVo1List) {
        this.earlyWarningVillageVo1List = earlyWarningVillageVo1List;
    }

    public List<EarlyWarningVo> getEarlyWarningEwRivelVo1List() {
        return earlyWarningEwRivelVo1List;
    }

    public void setEarlyWarningEwRivelVo1List(List<EarlyWarningVo> earlyWarningEwRivelVo1List) {
        this.earlyWarningEwRivelVo1List = earlyWarningEwRivelVo1List;
    }

    public List<EarlyWarningVo> getEarlyWarningEwReserVo1List() {
        return earlyWarningEwReserVo1List;
    }

    public void setEarlyWarningEwReserVo1List(List<EarlyWarningVo> earlyWarningEwReserVo1List) {
        this.earlyWarningEwReserVo1List = earlyWarningEwReserVo1List;
    }

    public List<EarlyWarningVo> getEarlyWarningVillageVo3List() {
        return earlyWarningVillageVo3List;
    }

    public void setEarlyWarningVillageVo3List(List<EarlyWarningVo> earlyWarningVillageVo3List) {
        this.earlyWarningVillageVo3List = earlyWarningVillageVo3List;
    }

    public List<EarlyWarningVo> getEarlyWarningEwRivelVo3List() {
        return earlyWarningEwRivelVo3List;
    }

    public void setEarlyWarningEwRivelVo3List(List<EarlyWarningVo> earlyWarningEwRivelVo3List) {
        this.earlyWarningEwRivelVo3List = earlyWarningEwRivelVo3List;
    }

    public List<EarlyWarningVo> getEarlyWarningEwReserVo3List() {
        return earlyWarningEwReserVo3List;
    }

    public void setEarlyWarningEwReserVo3List(List<EarlyWarningVo> earlyWarningEwReserVo3List) {
        this.earlyWarningEwReserVo3List = earlyWarningEwReserVo3List;
    }

    public List<EarlyWarningVo> getEarlyWarningVillageVo6List() {
        return earlyWarningVillageVo6List;
    }

    public void setEarlyWarningVillageVo6List(List<EarlyWarningVo> earlyWarningVillageVo6List) {
        this.earlyWarningVillageVo6List = earlyWarningVillageVo6List;
    }

    public List<EarlyWarningVo> getEarlyWarningEwRivelVo6List() {
        return earlyWarningEwRivelVo6List;
    }

    public void setEarlyWarningEwRivelVo6List(List<EarlyWarningVo> earlyWarningEwRivelVo6List) {
        this.earlyWarningEwRivelVo6List = earlyWarningEwRivelVo6List;
    }

    public List<EarlyWarningVo> getEarlyWarningEwReserVo6List() {
        return earlyWarningEwReserVo6List;
    }

    public void setEarlyWarningEwReserVo6List(List<EarlyWarningVo> earlyWarningEwReserVo6List) {
        this.earlyWarningEwReserVo6List = earlyWarningEwReserVo6List;
    }
}
