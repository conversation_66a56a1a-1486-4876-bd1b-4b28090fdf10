package com.huitu.cloud.api.ew.service;

import com.huitu.cloud.api.ew.entity.base.EwCallFeedback;
import com.huitu.cloud.api.ew.entity.request.EwCallFeedbackRequest;
import com.huitu.cloud.api.ew.entity.request.EwCallStatisticsRequest;
import com.huitu.cloud.api.ew.entity.request.EwManualConfirmRequest;
import com.huitu.cloud.api.ew.entity.request.EwNoticeReceiverRequest;
import com.huitu.cloud.api.ew.entity.response.EwCallStatisticsResponse;
import com.huitu.cloud.api.ew.entity.response.EwNoticeReceiverResponse;

import java.util.List;

/**
 * 叫应反馈服务
 */
public interface EwCallFeedbackService {

    /**
     * 根据叫应ID，获取叫应反馈记录
     *
     * @param callId 叫应ID
     * @return 叫应反馈记录
     */
    EwCallFeedback getFeedback(String callId);

    /**
     * 预警叫应反馈
     *
     * @param request 请求参数
     * @return 受影响的行数
     */
    int confirm(EwCallFeedbackRequest request);

    /**
     * 预警人工确认
     *
     * @param request 请求参数
     * @return 受影响的行数
     */
    int manualConfirm(EwManualConfirmRequest request);

    /**
     * 获取等待人工通知的预警接收人列表
     *
     * @param platformId 平台标识
     * @return 接收人列表
     */
    List<EwNoticeReceiverResponse> getNotNoticeReceiverList(String platformId);

    /**
     * 获取已人工通知的预警接收人列表
     *
     * @param request 请求参数
     * @return 接收人列表
     */
    List<EwNoticeReceiverResponse> getNotifiedReceiverList(EwNoticeReceiverRequest request);

    /**
     * 山洪监测预警叫应失败统计列表
     *
     * @param request 请求参数
     * @return 统计列表
     */
    List<EwCallStatisticsResponse> getCallFailStatsList(EwCallStatisticsRequest request);
}
