package com.huitu.cloud.api.fusion.entity.common;

/**
 * 反馈结果
 */
public enum FusionConfirmResult {
    SMS("1", "短信反馈"),
    SMS_TIMEOUT("2", "短信超时"),
    XCC("3", "语音反馈"),
    MANUAL("4", "人工通知");

    /**
     * 代码
     */
    private String code;
    /**
     * 别名
     */
    private String alias;

    FusionConfirmResult(String code, String alias) {
        this.code = code;
        this.alias = alias;
    }

    public String getCode() {
        return code;
    }

    public String getAlias() {
        return alias;
    }

    /**
     * 判断代码是否相等
     *
     * @param code 代码
     * @return true: 相等, false: 不相等
     */
    public boolean equalsCode(String code) {
        return this.code.equalsIgnoreCase(code);
    }
}
