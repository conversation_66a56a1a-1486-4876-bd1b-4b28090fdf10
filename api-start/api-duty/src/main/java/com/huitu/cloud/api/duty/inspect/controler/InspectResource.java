package com.huitu.cloud.api.duty.inspect.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.duty.floodinspect.entity.BnsFloodinspectobjB;
import com.huitu.cloud.api.duty.inspect.entity.*;
import com.huitu.cloud.api.duty.inspect.service.InspectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 运维监控 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-8-31
 */

@RestController
@Api(tags = "运维监控管理")
@RequestMapping("/api/duty/inspect")
public class InspectResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "29254d80-2e49-40b4-8315-5e8109b1070d";
    }
    @Override
    public String getVersion() {
        return "1.0";
    }
    @Autowired
    private InspectService inspectService;

//    @ApiOperation(value = "添加",notes="新增一条巡检记录信息")
//    @PostMapping(value = "add")
//    public ResponseEntity<SuccessResponse<String>> addBnsDutyuserb(@RequestBody BnsInspectR entity) throws Exception {
//        String flag = inspectService.saveInspectInfo(entity);
//        return ResponseEntity.ok(
//                new SuccessResponse(this, "OK", flag));
//    }


    @ApiOperation(value = "查询测站设备信息", notes = "按测站编码查询设备信息")
    @GetMapping(value = "select-st-hw-info")
    @ApiImplicitParam(name = "stcd", value = "测站编码", dataType = "String", required = true)
    public ResponseEntity<SuccessResponse<BnsHwinfo>> getStHwInfo(@RequestParam String stcd) throws Exception {
        BnsHwinfo info = inspectService.getStHwInfo(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", info));
    }

//    @ApiOperation(value = "查询单站巡检历史记录", notes = "按测站编码查询单站巡检的历史记录")
//    @GetMapping(value = "select-inspect-by-stcd")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "stcd", value = "测站编码",  dataType = "String"),
//            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
//            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
//    })
//    public ResponseEntity<SuccessResponse<Page<BnsInspectR>>> getInspectListByStcd(@RequestParam String stcd, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
//        IPage<BnsInspectR> list = inspectService.getInspectListByStcd(stcd,pageNum,pageSize);
//        return ResponseEntity.ok(
//                new SuccessResponse(this, "OK", list));
//    }

//    @ApiOperation(value = "查询单次巡检详情", notes = "按巡检记录编码查询巡检详情")
//    @GetMapping(value = "select-inspect-by-id")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "巡检记录编码",  dataType = "String"),
//    })
//    public ResponseEntity<SuccessResponse<BnsInspectR>> getInspectListById(@RequestParam String id) throws Exception {
//        BnsInspectR result = inspectService.getInspectListById(id);
//        return ResponseEntity.ok(
//                new SuccessResponse(this, "OK", result));
//    }

//    @ApiOperation(value = "查询巡检历史记录", notes = "查询巡检历史记录")
//    @PostMapping(value = "select-inspect-his-info")
//    public ResponseEntity<SuccessResponse<IPage<InspectVo>>> getInspectHisInfo(@RequestBody QueryInspect query) throws Exception {
//        IPage<InspectVo> result = inspectService.getInspectHisInfo(query.getAdcd(),query.getStnm(),query.getPageNum(),query.getPageSize());
//        return ResponseEntity.ok(
//                new SuccessResponse(this, "OK", result));
//    }

    @ApiOperation(value = "添加（新）",notes="新增一条巡检记录信息")
    @PostMapping(value = "add-new")
    public ResponseEntity<SuccessResponse<String>> addNewBnsDutyuserb(@RequestBody BnsInspectNewR entity) throws Exception {
        String flag = inspectService.saveNewInspectInfo(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "检查是否存在运行状况异常的上报数据", notes = "检查是否存在运行状况异常的上报数据")
    @GetMapping(value = "check-inspect-error-record")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", dataType = "String", required = true),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true)
    })
    public ResponseEntity<SuccessResponse<Boolean>> checkInspectErrorRecord(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        boolean flag = inspectService.checkInspectErrorRecord(stcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "维修上报",notes="维修上报")
    @GetMapping(value = "add-repair")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", dataType = "String", required = true),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true)
    })
    public ResponseEntity<SuccessResponse<String>> addRepairBnsDutyuserb(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        String flag = inspectService.addRepairBnsDutyuserb(stcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }
//    @ApiOperation(value = "查询存在运行状况异常的上报数据", notes = "查询存在运行状况异常的上报数据")
//    @GetMapping(value = "check-inspect-error-record")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "stcd", value = "测站编码", dataType = "String", required = true),
//            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true),
//            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true)
//    })
//    public ResponseEntity<SuccessResponse<List<BnsInspectNewR>>> checkInspectErrorRecord(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
//        List<BnsInspectNewR> list = inspectService.checkInspectErrorRecord(stcd, stm, etm);
//        return ResponseEntity.ok(
//                new SuccessResponse(this, "OK", list));
//    }

    @ApiOperation(value = "查询巡检记录（服务端）", notes = "查询巡检记录（服务端）")
    @PostMapping(value = "select-inspect-record-server")
    public ResponseEntity<SuccessResponse<IPage<InspectNewVo>>> getInspectRecordServer(@RequestBody QueryInspect query) throws Exception {
        IPage<InspectNewVo> result = inspectService.getInspectRecordForServer(query.getAdcd(), query.getType(), query.getStnm(), query.getStm(),query.getEtm(),query.getPageNum(),query.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "根据巡检记录ID下载各类报告记录表",notes="根据巡检记录ID下载各类报告记录表")
    @GetMapping(value = "create-inspect-report")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "inspectid", value = "巡检ID", dataType = "String", required = true)
    })
    public void createReport(@RequestParam String inspectid, HttpServletResponse response) throws Exception {
        inspectService.createReport(response,inspectid);
    }

    @ApiOperation(value = "查询巡检记录", notes = "查询巡检记录")
    @PostMapping(value = "select-inspect-record")
    public ResponseEntity<SuccessResponse<IPage<InspectVo>>> getInspectRecord(@RequestBody QueryInspect query) throws Exception {
        IPage<InspectNewVo> result = inspectService.getInspectRecord(query.getAdcd(),query.getInspect(),query.getType(),query.getStm(),query.getEtm(),query.getPageNum(),query.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }
    @ApiOperation(value = "查询巡检记录", notes = "查询巡检记录")
    @PostMapping(value = "select-inspect-record1")
    public ResponseEntity<SuccessResponse<IPage<InspectVo>>> getInspectRecord1(@RequestBody QueryInspect query) throws Exception {
        IPage<InspectNewVo> result = inspectService.getInspectRecord1(query.getAdcd(),query.getInspect(),query.getType(),query.getStm(),query.getEtm(),query.getPageNum(),query.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }
    @ApiOperation(value = "查询单站巡检历史记录", notes = "按测站编码查询单站巡检的历史记录")
    @GetMapping(value = "select-inspect-record-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码",  dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true)
    })
    public ResponseEntity<SuccessResponse<List<InspectRecordInfo>>> getInspectRecordByStcd(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<InspectRecordInfo> list = inspectService.getInspectRecordByStcd(stcd,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "查询巡检汇总", notes = "查询巡检汇总")
    @PostMapping(value = "select-inspect-summary")
    public ResponseEntity<SuccessResponse<IPage<InspectSummary>>> getInspectSummary(@RequestBody QueryInspect query) throws Exception {
        IPage<InspectSummary> result = inspectService.getInspectSummary(query.getAdcd(),query.getAdtp(), query.getStm(),query.getEtm(),query.getPageNum(),query.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "查询巡检统计", notes = "按照年份政区查询巡检接口")
    @GetMapping(value = "select-inspect-record-by-adcdAndYear")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区",  dataType = "String"),
            @ApiImplicitParam(name = "year", value = "年份", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String", required = true)
    })
    public ResponseEntity<SuccessResponse<InspectRecordTotal>> getInspectRecordByAdcdAndYear(@RequestParam String adcd, @RequestParam String year,@RequestParam String stm, @RequestParam String etm) throws Exception {
        InspectRecordTotal list = inspectService.getInspectRecordByAdcdAndYear(adcd,year,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

}
