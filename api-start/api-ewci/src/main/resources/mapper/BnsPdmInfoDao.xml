<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.pdm.mapper.BnsPdmInfoDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getPageList" useCache="false" flushCache="true"
            resultType="com.huitu.cloud.api.ewci.pdm.entity.BnsPdmInfo">
        SELECT MID, OBJTP, SEND_TIME, SENDER, (select count(1) from BNS_PDM_DETAIL where a.MID = MID) SENDCOUNT FROM
        BNS_PDM_INFO a
        <where>
            <if test="map.stm != null">
                and SEND_TIME >= #{map.stm}
            </if>
            <if test="map.etm != null">
                and SEND_TIME &lt;= #{map.etm}
            </if>
            <if test="map.objtp !=null and map.objtp !=''">
                AND CHARINDEX(#{map.objtp},OBJTP) >0
            </if>
        </where>
        ORDER BY SEND_TIME DESC
    </select>

    <!--获取山洪履职消息接收人列表-->
    <select id="getShReceiverList" resultType="com.huitu.cloud.api.ewci.pdm.entity.PdmReceiver">
        SELECT OBJNM, PERTP, REALNM, MOBILE
        FROM (SELECT (ROW_NUMBER() OVER (PARTITION BY OBJNM, PERTP, MOBILE ORDER BY REALNM DESC)) SNO,
                     OBJNM,
                     PERTP,
                     REALNM,
                     MOBILE
              FROM (
                       SELECT DISTINCT (CASE
                                            WHEN CHARINDEX(RYTP, '123') > 0 THEN RTRIM(B.ADNM)
                                            WHEN CHARINDEX(RTRIM(B.ADNM), A.ZRC) > 0 THEN RTRIM(A.ZRC)
                                            ELSE RTRIM(B.ADNM) + RTRIM(A.ZRC) END) OBJNM,
                                       RYTP                                        PERTP,
                                       REALNM,
                                       MOBILE
                       FROM BSN_SH_PERSON_B A
                                LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                       WHERE A.MOBILE IS NOT NULL
                         AND A.MOBILE != ''
                         AND B.ADCD IS NOT NULL) T) S
        WHERE S.SNO = 1
        ORDER BY PERTP, MOBILE, OBJNM ASC
    </select>
    <!--获取水库履职消息接收人列表-->
    <select id="getRsvrReceiverList" resultType="com.huitu.cloud.api.ewci.pdm.entity.PdmReceiver">
        SELECT RTRIM(OBJNM) OBJNM, PERTP, REALNM, MOBILE
        FROM (
                 SELECT (ROW_NUMBER() OVER (PARTITION BY OBJNM, PERTP, MOBILE ORDER BY REALNM DESC)) SNO,
                        OBJNM,
                        PERTP,
                        REALNM,
                        MOBILE
                 FROM (SELECT DISTINCT B.RES_NAME OBJNM, RESPERTP PERTP, REALNM, MOBILE
                       FROM BNS_RSVRPERSON_B A
                                LEFT JOIN ATT_RES_BASE B ON B.RES_CODE = A.RES_CODE
                       WHERE A.MOBILE != ''
                         AND B.RES_CODE IS NOT NULL) T) S
        WHERE S.SNO = 1
    </select>
    <!--获取江河履职消息接收人列表-->
    <select id="getRiverReceiverList" resultType="com.huitu.cloud.api.ewci.pdm.entity.PdmReceiver">
        SELECT RTRIM(OBJNM) OBJNM, PERTP, REALNM, MOBILE
        FROM (
                 SELECT (ROW_NUMBER() OVER (PARTITION BY OBJNM, PERTP, MOBILE ORDER BY REALNM DESC)) SNO,
                        OBJNM,
                        PERTP,
                        REALNM,
                        MOBILE
                 FROM (SELECT DISTINCT B.RV_NAME OBJNM, PERTP, REALNM, MOBILE_PHONE MOBILE
                       FROM BNS_RIVERPERSON_B A
                                LEFT JOIN ATT_RV_BASE B ON B.RV_CODE = A.RV_CODE
                       WHERE A.MOBILE_PHONE IS NOT NULL
                         AND A.MOBILE_PHONE != ''
                         AND B.RV_CODE IS NOT NULL) T) S
        WHERE S.SNO = 1
    </select>
    <!--获取堤防履职消息接收人列表-->
    <select id="getDikeReceiverList" resultType="com.huitu.cloud.api.ewci.pdm.entity.PdmReceiver">
        SELECT RTRIM(OBJNM) OBJNM, PERTP, REALNM, MOBILE
        FROM (
                 SELECT (ROW_NUMBER() OVER (PARTITION BY OBJNM, PERTP, MOBILE ORDER BY REALNM DESC)) SNO,
                        OBJNM,
                        PERTP,
                        REALNM,
                        MOBILE
                 FROM (SELECT DISTINCT DIKE_NAME OBJNM, PERTP, REALNM, MOBILE
                       FROM BNS_DIKEPERSON_B
                       WHERE MOBILE IS NOT NULL
                         AND MOBILE != '') T) S
        WHERE S.SNO = 1
    </select>
    <!--获取险工险段履职消息接收人列表-->
    <select id="getDpdsReceiverList" resultType="com.huitu.cloud.api.ewci.pdm.entity.PdmReceiver">
        SELECT RTRIM(OBJNM) OBJNM, PERTP, REALNM, MOBILE
        FROM (
                 SELECT (ROW_NUMBER() OVER (PARTITION BY OBJNM, PERTP, MOBILE ORDER BY REALNM DESC)) SNO,
                        OBJNM,
                        PERTP,
                        REALNM,
                        MOBILE
                 FROM (SELECT DISTINCT DPDS_NAME OBJNM, PERTP, REALNM, MOBILE
                       FROM BNS_DPDSPERSON_B
                       WHERE MOBILE IS NOT NULL
                         AND MOBILE != '') T) S
        WHERE S.SNO = 1
    </select>
    <!--获取蓄滞洪区履职消息接收人列表-->
    <select id="getFsdaReceiverList" resultType="com.huitu.cloud.api.ewci.pdm.entity.PdmReceiver">
        SELECT RTRIM(OBJNM) OBJNM, PERTP, REALNM, MOBILE
        FROM (
                 SELECT (ROW_NUMBER() OVER (PARTITION BY OBJNM, PERTP, MOBILE ORDER BY REALNM DESC)) SNO,
                        OBJNM,
                        PERTP,
                        REALNM,
                        MOBILE
                 FROM (SELECT DISTINCT B.FSDA_NAME OBJNM, PERTP, REALNM, MOBILE
                       FROM BNS_FSDAPERSON_B A
                                LEFT JOIN ATT_FSDA_BASE B ON B.FSDA_CODE = A.FSDA_CODE
                       WHERE A.MOBILE != ''
                         AND B.FSDA_CODE IS NOT NULL) T) S
        WHERE S.SNO = 1
    </select>
    <!--获取水库淹没范围履职消息接收人列表-->
    <select id="getVillageReceiverList" resultType="com.huitu.cloud.api.ewci.pdm.entity.PdmReceiver">
        SELECT RTRIM(OBJNM) OBJNM, PERTP, REALNM, MOBILE
        FROM (
                 SELECT (ROW_NUMBER() OVER (PARTITION BY OBJNM, PERTP, MOBILE ORDER BY REALNM DESC)) SNO,
                        OBJNM,
                        PERTP,
                        REALNM,
                        MOBILE
                 FROM (SELECT DISTINCT B.ADNM OBJNM, PERTP, REALNM, MOBILE
                       FROM BNS_VILLAGEPERSON_B A
                                LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                       WHERE A.MOBILE != ''
                         AND B.ADCD IS NOT NULL) T) S
        WHERE S.SNO = 1
    </select>

    <insert id="insert">
        INSERT INTO BNS_PDM_INFO(MID, OBJTP, SEND_TIME, SENDER)
        VALUES (#{mid}, #{objtp}, #{sendTime}, #{sender})
    </insert>
</mapper>