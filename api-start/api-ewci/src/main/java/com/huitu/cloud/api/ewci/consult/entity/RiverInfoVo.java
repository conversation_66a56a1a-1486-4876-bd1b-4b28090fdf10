package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 水库图层
 */
@ApiModel(value = "水库图层")
public class RiverInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "测站方位")
    @TableField(value = "STAZT")
    private String stazt;

    @ApiModelProperty(value = "测站类型")
    @TableField(value = "STTP")
    private String sttp;

    @ApiModelProperty(value = "纬度")
    @TableField(value = "LTTD")
    private Double lttd;

    @ApiModelProperty(value = "经度")
    @TableField(value = "LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "警戒流量")
    @TableField(value = "WRQ")
    private Double wrq;

    @ApiModelProperty(value = "警戒水位")
    @TableField(value = "WRZ")
    private Double wrz;

    @ApiModelProperty(value = "警戒水位")
    @TableField(value = "ZWRZ")
    private Double zwrz;

    @ApiModelProperty(value = "保证流量")
    @TableField(value = "GRQ")
    private Double grq;

    @ApiModelProperty(value = "保证水位")
    @TableField(value = "GRZ")
    private Double grz;

    @ApiModelProperty(value = "保证水位")
    @TableField(value = "ZGRZ")
    private Double zgrz;

    @ApiModelProperty(value = "堤顶高程(左)")
    @TableField(value = "LDKEL")
    private Double ldkel;

    @ApiModelProperty(value = "堤顶高程(右)")
    @TableField(value = "RDKEL")
    private Double rdkel;

    @ApiModelProperty(value = "漫堤流量")
    @TableField(value = "FLOW")
    private Double flow;

    @ApiModelProperty(value = "历史第一流量")
    @TableField(value = "ONE_FLOW")
    private Integer oneFlow;

    @ApiModelProperty(value = "历史第一时间")
    @TableField(value = "ONE_TM")
    private String oneTm;

    @ApiModelProperty(value = "历史第二流量")
    @TableField(value = "TWO_FLOW")
    private Integer twoFlow;

    @ApiModelProperty(value = "历史第二时间")
    @TableField(value = "TWO_TM")
    private String twoTm;

    @ApiModelProperty(value = "历史第三流量")
    @TableField(value = "THREE_FLOW")
    private Integer threeFlow;

    @ApiModelProperty(value = "历史第三时间")
    @TableField(value = "THREE_TM")
    private String threeTm;

    @ApiModelProperty(value = "水位")
    @TableField(value = "Z")
    private Double z;

    @ApiModelProperty(value = "水势")
    @TableField(value = "WPTN")
    private String wptn;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getStazt() {
        return stazt;
    }

    public void setStazt(String stazt) {
        this.stazt = stazt;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getWrq() {
        return wrq;
    }

    public void setWrq(Double wrq) {
        this.wrq = wrq;
    }

    public Double getWrz() {
        return wrz;
    }

    public void setWrz(Double wrz) {
        this.wrz = wrz;
    }

    public Double getGrq() {
        return grq;
    }

    public void setGrq(Double grq) {
        this.grq = grq;
    }

    public Double getGrz() {
        return grz;
    }

    public void setGrz(Double grz) {
        this.grz = grz;
    }

    public Double getLdkel() {
        return ldkel;
    }

    public void setLdkel(Double ldkel) {
        this.ldkel = ldkel;
    }

    public Double getRdkel() {
        return rdkel;
    }

    public void setRdkel(Double rdkel) {
        this.rdkel = rdkel;
    }

    public Double getFlow() {
        return flow;
    }

    public void setFlow(Double flow) {
        this.flow = flow;
    }

    public Integer getOneFlow() {
        return oneFlow;
    }

    public void setOneFlow(Integer oneFlow) {
        this.oneFlow = oneFlow;
    }

    public String getOneTm() {
        return oneTm;
    }

    public void setOneTm(String oneTm) {
        this.oneTm = oneTm;
    }

    public Integer getTwoFlow() {
        return twoFlow;
    }

    public void setTwoFlow(Integer twoFlow) {
        this.twoFlow = twoFlow;
    }

    public String getTwoTm() {
        return twoTm;
    }

    public void setTwoTm(String twoTm) {
        this.twoTm = twoTm;
    }

    public Integer getThreeFlow() {
        return threeFlow;
    }

    public void setThreeFlow(Integer threeFlow) {
        this.threeFlow = threeFlow;
    }

    public String getThreeTm() {
        return threeTm;
    }

    public void setThreeTm(String threeTm) {
        this.threeTm = threeTm;
    }

    public Double getZ() {
        return z;
    }

    public void setZ(Double z) {
        this.z = z;
    }

    public String getWptn() {
        return wptn;
    }

    public void setWptn(String wptn) {
        this.wptn = wptn;
    }

    public Double getZwrz() {
        return zwrz;
    }

    public void setZwrz(Double zwrz) {
        this.zwrz = zwrz;
    }

    public Double getZgrz() {
        return zgrz;
    }

    public void setZgrz(Double zgrz) {
        this.zgrz = zgrz;
    }
}
