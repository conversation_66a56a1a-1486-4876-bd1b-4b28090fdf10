package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 危险区基本情况调查成果汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
@TableName("IA_C_DANAD")
@ApiModel(value="IaCDanad对象", description="危险区基本情况调查成果汇总表")
public class IaDanad extends  RelevantInfo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "危险区代码")
    @TableId(value = "DAND", type = IdType.NONE)
    private String dand;

    @ApiModelProperty(value = "危险区名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "危险区内人口（人）")
    @TableField("PTCOUNT")
    private Double ptcount;

    @ApiModelProperty(value = "危险区内总户数（户）")
    @TableField("ETCOUNT")
    private Double etcount;

    @ApiModelProperty(value = "危险区内Ⅰ类经济户数（户）")
    @TableField("ECOUNT1")
    private Double ecount1;

    @ApiModelProperty(value = "危险区内Ⅱ类经济户数（户）")
    @TableField("ECOUNT2")
    private Double ecount2;

    @ApiModelProperty(value = "危险区内Ⅲ类经济户数（户）")
    @TableField("ECOUNT3")
    private Double ecount3;

    @ApiModelProperty(value = "危险区内Ⅳ类经济户数（户）")
    @TableField("ECOUNT4")
    private Double ecount4;

    @ApiModelProperty(value = "危险区内总房屋数（座）")
    @TableField("HTCOUNT")
    private Double htcount;

    @ApiModelProperty(value = "危险区内Ⅰ类房屋数（座）")
    @TableField("HCOUNT1")
    private Double hcount1;

    @ApiModelProperty(value = "危险区内Ⅱ类房屋数（座）")
    @TableField("HCOUNT2")
    private Double hcount2;

    @ApiModelProperty(value = "危险区内Ⅲ类房屋数（座）")
    @TableField("HCOUNT3")
    private Double hcount3;

    @ApiModelProperty(value = "危险区内Ⅳ类房屋数（座）")
    @TableField("HCOUNT4")
    private Double hcount4;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getDand() {
        return dand;
    }

    public void setDand(String dand) {
        this.dand = dand;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getPtcount() {
        return ptcount;
    }

    public void setPtcount(Double ptcount) {
        this.ptcount = ptcount;
    }

    public Double getEtcount() {
        return etcount;
    }

    public void setEtcount(Double etcount) {
        this.etcount = etcount;
    }

    public Double getEcount1() {
        return ecount1;
    }

    public void setEcount1(Double ecount1) {
        this.ecount1 = ecount1;
    }

    public Double getEcount2() {
        return ecount2;
    }

    public void setEcount2(Double ecount2) {
        this.ecount2 = ecount2;
    }

    public Double getEcount3() {
        return ecount3;
    }

    public void setEcount3(Double ecount3) {
        this.ecount3 = ecount3;
    }

    public Double getEcount4() {
        return ecount4;
    }

    public void setEcount4(Double ecount4) {
        this.ecount4 = ecount4;
    }

    public Double getHtcount() {
        return htcount;
    }

    public void setHtcount(Double htcount) {
        this.htcount = htcount;
    }

    public Double getHcount1() {
        return hcount1;
    }

    public void setHcount1(Double hcount1) {
        this.hcount1 = hcount1;
    }

    public Double getHcount2() {
        return hcount2;
    }

    public void setHcount2(Double hcount2) {
        this.hcount2 = hcount2;
    }

    public Double getHcount3() {
        return hcount3;
    }

    public void setHcount3(Double hcount3) {
        this.hcount3 = hcount3;
    }

    public Double getHcount4() {
        return hcount4;
    }

    public void setHcount4(Double hcount4) {
        this.hcount4 = hcount4;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }


}
