package com.huitu.cloud.api.syq.rain.entity;

import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;


/**
 * <AUTHOR>
 */
public class QueryAscriptionType {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    @SqlInjection
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    @SqlInjection
    private String etm;
    @ApiModelProperty(value = "流域编码")
    @SqlInjection
    private String bscd;

    @ApiModelProperty(value = "政区编码")
    @SqlInjection
    private String adcd;

    @ApiModelProperty(value = "测站归属类型 1、水文，2、山洪  3、气象")
    @SqlInjection
    private List<String> stType;

    @ApiModelProperty(value = "是否是辖区外(用户登录以后有用) 1：辖区内，2：辖区外")
    @SqlInjection
    private List<String> isOut;


    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public List<String> getIsOut() {
        return isOut;
    }

    public void setIsOut(List<String> isOut) {
        this.isOut = isOut;
    }
}
