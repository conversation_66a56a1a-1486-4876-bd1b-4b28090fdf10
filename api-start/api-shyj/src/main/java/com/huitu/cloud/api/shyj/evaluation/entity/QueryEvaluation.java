package com.huitu.cloud.api.shyj.evaluation.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2019-09-18
 */
public class QueryEvaluation extends PageBean {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "流域编码")
    private String wscd;
    @ApiModelProperty(value = "对象id(查单个对象时，其他参数置空)")
    private String key;
    @ApiModelProperty(value = "流域名称")
    private String wsnm;

    @ApiModelProperty(value = "防治区类型")
    private String prevtp;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public String getPrevtp() {
        return prevtp;
    }

    public void setPrevtp(String prevtp) {
        this.prevtp = prevtp;
    }
}
