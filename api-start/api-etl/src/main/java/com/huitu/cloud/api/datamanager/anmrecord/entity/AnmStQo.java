package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel
public class AnmStQo extends PageBean  {

    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "开始时间", example = "2021-10-25 08:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private String stm;

    @ApiModelProperty(value = "结束时间", example = "2021-10-26 08:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private String etm;

    @ApiModelProperty(value = "异常数据状态 多个值以逗号隔开" ,example = "1,2,3,4,5,6")
    private String staflgs;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getStaflgs() {
        return staflgs;
    }

    public void setStaflgs(String staflgs) {
        this.staflgs = staflgs;
    }
}
