package com.huitu.cloud.api.ewci.soilnews.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 墒情专报
 *
 * <AUTHOR>
 */
@ApiModel(value="BsnSoilNewsB对象", description="墒情专报信息")
public class BsnSoilNewsQo extends PageBean {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "期数")
    private String periods;


    public String getPeriods() {
        return periods;
    }

    public void setPeriods(String periods) {
        this.periods = periods;
    }
}
