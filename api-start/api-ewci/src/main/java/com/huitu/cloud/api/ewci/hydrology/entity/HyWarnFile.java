package com.huitu.cloud.api.ewci.hydrology.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.huitu.cloud.validation.constraints.Option;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 洪水预警文件
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警文件")
public class HyWarnFile implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @NotBlank(message = "参数[预警ID]不能为空")
    @Size(max = 36, message = "参数[预警ID]的长度不能超过36个字符")
    @TableField(value = "WARNID")
    private String warnId;

    @ApiModelProperty(value = "年份")
    @NotBlank(message = "参数[年份]不能为空")
    @Size(min = 4, max = 4, message = "参数[年份]的长度应为4个字符")
    @TableField(value = "YR")
    private String yr;

    @ApiModelProperty(value = "期数")
    @NotNull(message = "参数[期数]不能为空")
    @Min(value = 1, message = "参数[期数]不能小于1")
    @TableField(value = "NO")
    private Integer no;

    @ApiModelProperty(value = "文件类型（1=简报、2=吉水防御中心、3=吉水防办明电、4=吉水防御中心函、5=气象信息、6=其他、7=吉水防办、8=吉水防办函、9=吉水防、10=吉水防明电、11=预警信息）")
    @NotBlank(message = "参数[文件类型]不能为空")
    @Option(value = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11"}, message = "参数[文件类型]的值无效")
    @TableField(value = "FILE_TYPE")
    private String fileType;

    @ApiModelProperty(value = "文件标题")
    @NotBlank(message = "参数[文件标题]不能为空")
    @Size(max = 200, message = "参数[文件标题]的长度不能超过200个字符")
    @TableField(value = "FILE_TITLE")
    private String fileTitle;

    @ApiModelProperty(value = "文件名称")
    @TableField(value = "FILE_NAME")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    @TableField(value = "FILE_PATH")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String filePath;

    @ApiModelProperty(value = "文件标志（0=常规、1=紧急）")
    @NotBlank(message = "参数[文件标志]不能为空")
    @Option(value = {"0", "1"}, message = "参数[文件标志]的值无效")
    @TableField(value = "FILE_FLAG")
    private String fileFlag;

    @ApiModelProperty(value = "签发人")
    @NotBlank(message = "参数[签发人]不能为空")
    @Size(max = 36, message = "参数[签发人]的长度不能超过36个字符")
    @TableField(value = "SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核人")
    @NotBlank(message = "参数[审核人]不能为空")
    @Size(max = 36, message = "参数[审核人]的长度不能超过36个字符")
    @TableField(value = "AUDITOR")
    private String auditor;

    @ApiModelProperty(value = "编辑人")
    @NotBlank(message = "参数[编辑人]不能为空")
    @Size(max = 36, message = "参数[编辑人]的长度不能超过36个字符")
    @TableField(value = "EDITOR")
    private String editor;

    @ApiModelProperty(value = "接收单位")
    @TableField(value = "RECEIVE_DEPT")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String receiveDept;

    @ApiModelProperty(value = "接收政区")
    @TableField(value = "RECEIVE_ADCD")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String receiveAdcd;

    @ApiModelProperty(value = "发送单位")
    @TableField(value = "SEND_DEPT")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String sendDept;

    @ApiModelProperty(value = "发送人")
    @TableField(value = "SENDER")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String sender;

    @ApiModelProperty(value = "发送时间")
    @TableField(value = "SEND_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Date sendTime;

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getYr() {
        return yr;
    }

    public void setYr(String yr) {
        this.yr = yr;
    }

    public Integer getNo() {
        return no;
    }

    public void setNo(Integer no) {
        this.no = no;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileTitle() {
        return fileTitle;
    }

    public void setFileTitle(String fileTitle) {
        this.fileTitle = fileTitle;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileFlag() {
        return fileFlag;
    }

    public void setFileFlag(String fileFlag) {
        this.fileFlag = fileFlag;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public String getEditor() {
        return editor;
    }

    public void setEditor(String editor) {
        this.editor = editor;
    }

    public String getReceiveDept() {
        return receiveDept;
    }

    public void setReceiveDept(String receiveDept) {
        this.receiveDept = receiveDept;
    }

    public String getReceiveAdcd() {
        return receiveAdcd;
    }

    public void setReceiveAdcd(String receiveAdcd) {
        this.receiveAdcd = receiveAdcd;
    }

    public String getSendDept() {
        return sendDept;
    }

    public void setSendDept(String sendDept) {
        this.sendDept = sendDept;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }
}
