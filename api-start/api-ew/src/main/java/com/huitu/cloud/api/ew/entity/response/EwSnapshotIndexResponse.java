package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.api.ew.entity.base.EwSnapshotIndex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 山洪预警指标快照响应对象
 */
@ApiModel(value = "山洪预警指标快照响应对象")
public class EwSnapshotIndexResponse extends EwSnapshotIndex {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
