package com.huitu.cloud.api.ewci.consult.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.consult.entity.*;
import com.huitu.cloud.api.ewci.consult.service.ConsultService;
import com.huitu.cloud.api.ewci.exception.RsvrWaterReportException;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 防汛会商
 * </p>
 *
 * <AUTHOR>
 * @since 2024-4-17
 */
@RestController
@Api(tags = "防汛会商")
@RequestMapping("/api/ewci/consult")
public class ConsultResource  extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "e8d71fe3-ba73-6402-ebf5-4827faa50875";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final ConsultService baseService;

    public ConsultResource(ConsultService baseService) {
        this.baseService = baseService;
    }

    @ApiOperation(value = "实时水情-水情统计-市县-河道 统计", notes = "实时水情-水情统计-市县-河道 统计")
    @GetMapping(value = "select-water-ad-river-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultWaterAdRiverVo>>> getWaterAdRiverStatList(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<ConsultWaterAdRiverVo> list = baseService.getWaterAdRiverStatList(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "实时水情-水情统计-市县-水库 统计", notes = "实时水情-水情统计-市县-水库 统计")
    @GetMapping(value = "select-water-ad-rsvr-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "rvtp", value = "水库类型  大型：4,5 中型 :3, 1： 小(二)型  2 小（一）型 多个值用,隔开 例：1,2,3 ", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stType", value = "报汛类型 全部：1,4,5,2,6 自动: 1,2,5 人工: 1,4,5", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultWaterAdRsvrVo>>> getWaterAdRsvrStatList(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String rvtp, @RequestParam String stType) throws Exception {
        List<ConsultWaterAdRsvrVo> list = baseService.getWaterAdRsvrStatList(adcd, stm, etm, rvtp, stType);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "实时水情-水情统计-流域-河道 统计", notes = "实时水情-水情统计-流域-河道 统计")
    @GetMapping(value = "select-water-bas-river-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "basCode", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultWaterBasRiverVo>>> getWaterBasRiverStatList(@RequestParam String basCode, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<ConsultWaterBasRiverVo> list = baseService.getWaterBasRiverStatList(basCode, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "实时水情-水情统计-流域（19条主要江河）-河道 统计", notes = "实时水情-水情统计-流域-河道 统计")
    @GetMapping(value = "select-water-bas-zyjh-river-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultWaterBasRiverVo>>> getWaterBasZyjhRiverStatList(@RequestParam String stm, @RequestParam String etm) throws Exception {
        List<ConsultWaterBasRiverVo> list = baseService.getWaterBasZyjhRiverStatList(stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "实时水情-水情统计-流域-水库 统计", notes = "实时水情-水情统计-流域-水库 统计")
    @GetMapping(value = "select-water-bas-rsvr-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "basCode", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "rvtp", value = "水库类型  大型：4,5 中型 :3, 1： 小(二)型  2 小（一）型 多个值用,隔开 例：1,2,3 ", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stType", value = "报汛类型 全部：1,4,5,2,6 自动: 1,2,5 人工: 1,4,5", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultWaterBasRsvrVo>>> getWaterBasRsvrStatList(@RequestParam String basCode, @RequestParam String stm, @RequestParam String etm, @RequestParam String rvtp, @RequestParam String stType) throws Exception {
        List<ConsultWaterBasRsvrVo> list = baseService.getWaterBasRsvrStatList(basCode, stm, etm, rvtp, stType);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "实时水情-水情统计-流域（19条主要江河）-水库 统计", notes = "实时水情-水情统计-流域-水库 统计")
    @GetMapping(value = "select-water-bas-zyjh-rsvr-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "rvtp", value = "水库类型  大型：4,5 中型 :3, 1： 小(二)型  2 小（一）型 多个值用,隔开 例：1,2,3 ", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stType", value = "报汛类型 全部：1,4,5,2,6 自动: 1,2,5 人工: 1,4,5", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultWaterBasRsvrVo>>> getWaterBasZyjhRsvrStatList(@RequestParam String stm, @RequestParam String etm, @RequestParam String rvtp, @RequestParam String stType) throws Exception {
        List<ConsultWaterBasRsvrVo> list = baseService.getWaterBasZyjhRsvrStatList(stm, etm, rvtp, stType);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "预警查看-24h预警-山洪预警 统计", notes = "预警查看-24h预警-山洪预警 统计")
    @GetMapping(value = "select-warn-ad-sh-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultWarnAdVo>>> getWarnAdShStatList(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<ConsultWarnAdVo> list = baseService.getWarnAdShStatList(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "预警查看-24h预警-河道预警 统计", notes = "预警查看-24h预警-河道预警 统计")
    @GetMapping(value = "select-warn-ad-river-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultWarnAdVo>>> getWarnAdRiverStatList(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<ConsultWarnAdVo> list = baseService.getWarnAdRiverStatList(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "预警查看-24h预警-水库预警 统计", notes = "预警查看-24h预警-水库预警 统计")
    @GetMapping(value = "select-warn-ad-rsvr-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultWarnAdVo>>> getWarnAdRsvrStatList(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<ConsultWarnAdVo> list = baseService.getWarnAdRsvrStatList(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "风险研判-超汛超警-市县-河道 统计", notes = "风险研判-超汛超警-市县-河道 统计")
    @GetMapping(value = "select-risk-ad-river-forecast-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<ConsultRiskAdRiverVo>>> getRiskAdRiverStatList(@RequestParam String adcd) throws Exception {
        List<ConsultRiskAdRiverVo> list = baseService.getRiskAdRiverStatList(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "风险研判-超汛超警-市县-水库 统计", notes = "风险研判-超汛超警-市县-水库 统计")
    @GetMapping(value = "select-risk-ad-rsvr-forecast-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "rvtp", value = "水库类型  大型：4,5 中型 :3, 1： 小(二)型  2 小（一）型 多个值用,隔开 例：1,2,3 ", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stType", value = "报汛类型 全部：1,4,5,2,6 自动: 1,2,5 人工: 1,4,5", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultRiskAdRsvrVo>>> getRiskAdRsvrStatList(@RequestParam String adcd, @RequestParam String rvtp, @RequestParam String stType) throws Exception {
        List<ConsultRiskAdRsvrVo> list = baseService.getRiskAdRsvrStatList(adcd, rvtp, stType);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "风险研判-超汛超警-流域-河道 统计", notes = "风险研判-超汛超警-流域-河道 统计")
    @GetMapping(value = "select-risk-bas-river-forecast-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "basCode", value = "流域编码", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultRiskBasRiverVo>>> getRiskBasRiverStatList(@RequestParam String basCode) throws Exception {
        List<ConsultRiskBasRiverVo> list = baseService.getRiskBasRiverStatList(basCode);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "风险研判-超汛超警-流域-水库 统计", notes = "风险研判-超汛超警-流域-水库 统计")
    @GetMapping(value = "select-risk-bas-rsvr-forecast-stat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "basCode", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "rvtp", value = "水库类型  大型：4,5 中型 :3, 1： 小(二)型  2 小（一）型 多个值用,隔开 例：1,2,3 ", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stType", value = "报汛类型 全部：1,4,5,2,6 自动: 1,2,5 人工: 1,4,5", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ConsultRiskBasRsvrVo>>> getRiskBasRsvrStatList(@RequestParam String basCode, @RequestParam String rvtp, @RequestParam String stType) throws Exception {
        List<ConsultRiskBasRsvrVo> list = baseService.getRiskBasRsvrStatList(basCode, rvtp, stType);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "防御工作方案列表分页查询")
    @PostMapping("select-defense-work-plan-page")
    public ResponseEntity<SuccessResponse<IPage<BsnDefenseWorkPlanB>>> getPage(@Validated @RequestBody DefenseWorkPlanQo<BsnDefenseWorkPlanB> query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPage(query)));
    }

    @ApiOperation(value = "获取最大期数")
    @GetMapping("select-max-plan-no")
    public ResponseEntity<SuccessResponse<BsnDefenseWorkPlanB>> getMaxPlanNo() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getMaxPlanNo()));
    }

    @ApiOperation(value = "添加或修改防御工作方案")
    @PostMapping(value = "addOrUpdate")
    public ResponseEntity<SuccessResponse<BsnDefenseWorkPlanB>> addOrUpdate(@RequestBody BsnDefenseWorkPlanB entity) throws Exception {
        if (StringUtils.isNotBlank(entity.getDwpId())) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.update(entity)));
        } else {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insert(entity)));
        }
    }

    @ApiOperation(value = "保存防御工作方案")
    @PostMapping("save-file")
    public ResponseEntity<SuccessResponse<Integer>> saveFile(@Validated @RequestBody DefenseWorkPlanSo request) {
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.saveFile(request)));
        } catch (RsvrWaterReportException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new RsvrWaterReportException("保存失败", ex);
        }
    }

    @ApiOperation(value = "获取防御工作方案")
    @GetMapping("document")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dwpId", value = "防御工作方案编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<BsnDefenseWorkPlanB>> getDocument(String dwpId) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getDocument(dwpId)));
    }

    @ApiOperation(value = "发送防御工作方案")
    @GetMapping("send")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dwpId", value = "防御工作方案编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<BsnDefenseWorkPlanB>> send(String dwpId) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.send(dwpId)));
    }


}
