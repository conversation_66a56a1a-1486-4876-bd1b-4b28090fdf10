<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.usif.user.mapper.UserDao">
    <!--    <cache type="com.huitu.cloud.config.RedisCache"/>-->
    <insert id="batchSaveDeptAndRole">
        insert into BSN_UGRELATION(userId,deptId,groupId) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userid},#{item.deptId},#{item.groupId})
        </foreach>
    </insert>
    <insert id="saveUserExtend" parameterType="com.huitu.cloud.api.usif.user.entity.UserAdd">
        insert into BNS_USERLOGUSER_B(userId, loginNm, user_unit, user_post, useflg, ismobile, inspectgroupid)
        values (#{userId}, #{loginNm}, #{userUnit}, #{userPost}, '1', #{ismobile}, #{inspectgroupid})
    </insert>
    <insert id="saveDeptAndRole" parameterType="com.huitu.cloud.api.usif.user.entity.Userlation">
        insert into BSN_UGRELATION(userId, deptId, groupId)
        values (#{item.userid}, #{item.deptId}, #{item.groupId})
    </insert>
    <insert id="batchInsertUserSt">
        insert into BSN_USERAD_ST(USERAD,STCD) values
        <foreach collection="stcds" index="index" item="item" separator=",">
            (#{adcd},#{item})
        </foreach>
    </insert>
    <insert id="setUserFollowStByUserId">
        insert into BSN_USER_FOLLOWST(userId, STCD)
        values (#{userId}, #{stcd})
    </insert>
    <select id="resetOneUserSt" statementType="CALLABLE" useCache="false" parameterType="java.lang.String">
        {call PR_UPDATE_ONLY_USER__ST(
                #{adcd, mode=IN}
            )}
    </select>
    <select id="resetAllUserStPower" statementType="CALLABLE" useCache="false" parameterType="java.lang.Integer">
        {call PR_UPDATE_ALL_USER_ST()}
    </select>
    <delete id="deleteDeptAndRole">
        delete
        from BSN_UGRELATION
        where userId = #{userId}
    </delete>
    <delete id="deleteUserExtend">
        delete
        from BNS_USERLOGUSER_B
        where userid = #{userid}
    </delete>
    <delete id="delUserSt">
        delete
        from BSN_USERAD_ST
        where USERAD = #{adcd}
    </delete>
    <delete id="delUserFollowStByUserId">
        delete
        from BSN_USER_FOLLOWST
        where USERID = #{userId}
          and stcd = #{stcd}
    </delete>
    <delete id="batchDeletetUserSt">
        delete from BSN_USERAD_ST
        where USERAD = #{adcd} and STCD IN
        <foreach collection="stcds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="getUserDeptsAndRolesInfo" resultType="com.huitu.cloud.api.usif.user.entity.DeptRoleInfo">
        select userId,a.deptId,a.groupId,b.GroupNM,c.deptnm ,c.adcd,d.adnm from
        BSN_UGRELATION a inner join BSN_USERGROUP b on a.GroupID=b.GroupID
        left join BNS_DEPTINFO_B c on a.deptid=c.deptid
        inner join BSN_ADCD_B D on c.ADCD=D.ADCD
        where 1=1
        <if test="deptId !=null and deptId !=''">
            and a.deptid=#{deptId}
        </if>
        order by userId
    </select>
    <select id="getUserExtend" resultType="java.util.Map">
        SELECT USERID userId, LOGINNM loginNm, USER_UNIT userUnit, USER_POST userPost, ISMOBILE ismobile, T.GROUPID inspectgroupid,
        COALESCE(((CASE WHEN ADLVL = 1 THEN '省' ELSE ADNM END) + '-' + CAST(GROUPNUM AS VARCHAR(4))), '') inspectgroupname
        FROM (SELECT A.USERID, LOGINNM, USER_UNIT, USER_POST, ISMOBILE, COALESCE(B.GROUPID, C.GROUPID, '') GROUPID FROM BNS_USERLOGUSER_B A
        LEFT JOIN (SELECT GROUPID, LEADER USERID FROM BNS_FLOODINSPECTGROUP_B WHERE [YEAR] = YEAR(GETDATE())) B ON B.USERID = A.USERID
        LEFT JOIN (SELECT A.GROUPID, USERID FROM BNS_FLOODINSPECTGROUP_USER A, BNS_FLOODINSPECTGROUP_B B WHERE B.GROUPID = A.GROUPID AND
        [YEAR] = YEAR(GETDATE())) C ON C.USERID = A.USERID
        <where>
            <if test="userId !=null and userId !=''">
                a.userId=#{userId}
            </if>
        </where>
        ) T
        LEFT JOIN BNS_FLOODINSPECTGROUP_B B ON B.GROUPID = T.GROUPID
        LEFT JOIN BSN_ADCD_B C ON C.ADCD = B.ADCD
    </select>
    <select id="getUserDeptAndRole" resultType="com.huitu.cloud.api.usif.user.entity.UserInfos">
        select userId, a.deptId, a.groupId, b.GroupNM, c.deptnm, c.adcd, d.adnm
        from BSN_UGRELATION a
                 left join BSN_USERGROUP b on a.GroupID = b.GroupID
                 left join BNS_DEPTINFO_B c on a.deptid = c.deptid
                 left join BSN_ADCD_B D on c.ADCD = D.ADCD
        where userid = #{userId}
    </select>
    <select id="selectUserSt" resultType="com.huitu.cloud.api.usif.user.entity.UserSt">
        select USERAD, A.STCD
        from BSN_USERAD_ST A,
             ST_STBPRP_B B
        where USERAD = #{userAd}
          AND A.STCD = B.STCD
          AND LEFT(B.ADDVCD, #{adLvl}) != #{ad}
        order by stcd
    </select>
    <select id="selectFollowSt" resultType="com.huitu.cloud.api.usif.user.entity.QueryUserFollowSt">
        select USERID, STCD
        FROM BSN_USER_FOLLOWST
        WHERE USERID = #{userId}
          and stcd = #{stcd}
    </select>

    <select id="checkUserName" resultType="string">
        select username
        FROM BSN_MOBILE_REGISTER_USERS
        WHERE username = #{userName}
    </select>
    <insert id="saveRegisterUser" parameterType="com.huitu.cloud.api.usif.user.entity.MobileRegisterUser">
        insert into BSN_MOBILE_REGISTER_USERS(username, loginname, password, adcd, phone, dept, status)
        values (#{userName}, #{loginName}, #{password}, #{adcd}, #{phone}, #{dept}, 0)
    </insert>
    <insert id="saveUserPushMsg">
        <selectKey keyProperty="count" resultType="int" order="BEFORE">
            select count(*) count from BSN_USERPUSHMSG_B where USERID = #{userid} and TP = #{tp}
        </selectKey>
        <if test="count > 0">
            update BSN_USERPUSHMSG_B
            set DISFLG = #{disflg}
            where USERID = #{userid} and TP = #{tp}
        </if>
        <if test="count==0">
            insert into BSN_USERPUSHMSG_B values(#{userid},#{tp},#{disflg})
        </if>
    </insert>

    <select id="getRegisterUsers" resultType="com.huitu.cloud.api.usif.user.entity.MobileRegisterUserInfo">
        select userName,loginName,password, a.adcd, b.adnm,phone,dept,status,registerTm FROM BSN_MOBILE_REGISTER_USERS a
        inner join BSN_ADCD_B b on a.ADCD=b.ADCD
        WHERE 1=1
        <if test="map.ad!=null and map.ad!=''">
            and left(b.adcd, #{map.adlevel}) = #{map.ad}
        </if>
        <if test="map.stm!=null and map.stm!=''">
            and a.registerTm >= #{map.stm}
        </if>
        <if test="map.etm!=null and map.etm!=''">
            and a.registerTm &lt;= #{map.etm}
        </if>
        <if test="map.loginname!=null and map.loginname!=''">
            and CHARINDEX(#{map.loginname}, a.loginName)>0
        </if>
        <if test="map.username!=null and map.username!=''">
            and a.username = #{map.username}
        </if>
        <if test="map.status!=null and map.status!=''">
            and status = #{map.status}
        </if>
        order by a.registerTm desc
    </select>

    <update id="updateRegisterStatus">
        update BSN_MOBILE_REGISTER_USERS
        set status=#{map.status}
        where username = #{map.userName}
    </update>

    <select id="getMobileUser" resultType="com.huitu.cloud.api.usif.user.entity.UserInfoEx">
        select userid id
        from BNS_USERLOGUSER_B
        where ismobile = #{isMobile}
    </select>
    <select id="getInsideOrOutUserSt" resultType="com.huitu.cloud.api.usif.user.entity.UserAdStVo">
        select A.USERAD adcd,A.STCD stcd, B.STNM stnm from BSN_USERAD_ST A,ST_STBPRP_B B
        where USERAD=#{userAd} AND A.STCD=B.STCD
        <if test='inOrOut == "1"'>
            AND LEFT(B.ADDVCD,#{adLvl}) = #{ad}
        </if>
        <if test='inOrOut == "2"'>
            AND LEFT(B.ADDVCD,#{adLvl}) != #{ad}
        </if>
        order by stcd
    </select>
    <select id="getUserPushMsgByUserid" useCache="false" resultType="com.huitu.cloud.api.usif.user.entity.BsnUserpushmsgB">
        select USERID, TP, DISFLG
        from BSN_USERPUSHMSG_B
        where USERID = #{userid}
    </select>
    <insert id="saveMobileLoginInfo">
        MERGE INTO BSN_MOBILE_LOGIN_LOG AS T
        USING (SELECT #{map.userId}      USERID,
                      GETDATE()          LOGINTM,
                      #{map.ipAddress}   IP_ADDRESS,
                      #{map.brand}       DEVICE_BRAND,
                      #{map.model}       DEVICE_MODEL,
                      #{map.version}     DEVICE_VERSION,
                      #{map.imei}        DEVICE_IMEI,
                      #{map.simName}     SIM_NAME,
                      #{map.simNumber}   SIM_NUMBER,
                      #{map.versionCode} VERSION_CODE,
                      #{map.versionName} VERSION_NAME) AS S
        ON T.USERID = S.USERID AND T.LOGINTM = S.LOGINTM
        WHEN NOT MATCHED THEN
            INSERT (USERID, LOGINTM, IP_ADDRESS, DEVICE_BRAND, DEVICE_MODEL, DEVICE_VERSION, DEVICE_IMEI, SIM_NAME, SIM_NUMBER,
                    VERSION_CODE, VERSION_NAME)
            VALUES (S.USERID, S.LOGINTM, S.IP_ADDRESS, S.DEVICE_BRAND, S.DEVICE_MODEL, S.DEVICE_VERSION, S.DEVICE_IMEI, S.SIM_NAME,
                    S.SIM_NUMBER, S.VERSION_CODE, S.VERSION_NAME);
    </insert>
    <update id="updateMobileLastlyOnline">
        MERGE INTO BSN_MOBILE_ONLINE_USERS AS T
        USING (SELECT #{userId}    USERID,
                      #{ipAddress} IP_ADDRESS,
                      GETDATE()    LASTONLINETM) AS S
        ON T.USERID = S.USERID
        WHEN MATCHED THEN
            UPDATE
            SET T.IP_ADDRESS   = S.IP_ADDRESS,
                T.LASTONLINETM = S.LASTONLINETM
        WHEN NOT MATCHED THEN
            INSERT (USERID, IP_ADDRESS, LASTONLINETM)
            VALUES (S.USERID, S.IP_ADDRESS, S.LASTONLINETM);
    </update>
    <update id="updateWebLastlyOnline">
        MERGE INTO BSN_WEB_ONLINE_USERS AS T
        USING (SELECT #{userId}    USERID,
                      #{ipAddress} IP_ADDRESS,
                      GETDATE()    LASTONLINETM) AS S
        ON T.USERID = S.USERID
        WHEN MATCHED THEN
            UPDATE
            SET T.IP_ADDRESS   = S.IP_ADDRESS,
                T.LASTONLINETM = S.LASTONLINETM
        WHEN NOT MATCHED THEN
            INSERT (USERID, IP_ADDRESS, LASTONLINETM)
            VALUES (S.USERID, S.IP_ADDRESS, S.LASTONLINETM);
    </update>
    <select id="getAllAndOnlineUserCount" resultType="com.huitu.cloud.api.usif.user.entity.UserStaInfo">
        select (select count(1) from BNS_USERLOGUSER_B where ismobile = 1)                                         as allUserCount,
               (select COUNT(1) from BSN_MOBILE_ONLINE_USERS where LASTONLINETM >= dateadd(minute, -5, GETDATE())) as onlineUserCount
    </select>
    <select id="getOnlineUsersInfo" resultType="com.huitu.cloud.api.usif.user.entity.OnlineUserInfo">
        select A.userId, B.loginTM, C.loginNM, F.adnm
        from [dbo].[BSN_MOBILE_ONLINE_USERS] A
                 inner join (select userId, max(loginTm) loginTm from [dbo].[BSN_MOBILE_LOGIN_LOG] group by userid) B
                            on A.USERID = B.USERID and LASTONLINETM >= dateadd(minute, -5, GETDATE())
                 left join BNS_USERLOGUSER_B C ON A.userId = C.userid
                 left join BSN_UGRELATION D on A.userId = D.UserID
                 left join BNS_DEPTINFO_B E on D.deptid = E.deptid
                 left join BSN_ADCD_B F on E.adcd = F.adcd
    </select>
    <select id="getUserLoginSta" resultType="com.huitu.cloud.api.usif.user.entity.UserLoginSta">
        SELECT month(loginTM) AS mm , COUNT(*) AS num FROM BSN_MOBILE_LOGIN_LOG
        WHERE 1=1
        <if test="year !=null and year !=''">
            and DATEPART(year, loginTM) = #{year}
        </if>
        GROUP BY month(loginTM) order by MM
    </select>
    <select id="selectWebOnlineUserList" resultType="com.huitu.cloud.api.usif.user.entity.UserOnlineStatisticsVo">
        SELECT a.USERID, b.loginnm,datediff(MI,dateadd(minute,-30,GETDATE()), LASTONLINETM) onlineflag, LASTONLINETM FROM
        BSN_WEB_ONLINE_USERS a
        LEFT JOIN BNS_USERLOGUSER_B b on a.USERID = b.userid
        <where>
            <if test="map.username !=null and map.username !=''">
                and CHARINDEX(#{map.username},b.loginnm) >0
            </if>
            <if test="map.stm != null and map.stm !=''">
                and CONVERT(DATE,LASTONLINETM) >=CONVERT(DATE,#{map.stm})
            </if>
            <if test="map.etm != null and map.etm !=''">
                and CONVERT(DATE,LASTONLINETM) &lt;= CONVERT(DATE,#{map.etm})
            </if>
            <if test="map.onlineFlag !=null and map.onlineFlag !=''">
                and datediff(MI,dateadd(minute,-30,GETDATE()), LASTONLINETM)
                <choose>
                    <when test="map.onlineFlag == '1'.toString()">>=</when>
                    <otherwise>&lt;</otherwise>
                </choose>
                0
            </if>
        </where>
        ORDER BY LASTONLINETM DESC
    </select>
    <select id="selectMobileOnlineUserList" resultType="com.huitu.cloud.api.usif.user.entity.UserOnlineStatisticsVo">
        SELECT a.USERID, b.loginnm,datediff(MI,dateadd(minute,-30,GETDATE()), LASTONLINETM) onlineflag, LASTONLINETM FROM
        BSN_MOBILE_ONLINE_USERS a
        LEFT JOIN BNS_USERLOGUSER_B b on a.USERID = b.userid
        <where>
            <if test="map.username !=null and map.username !=''">
                and b.loginnm like '${map.username}%'
            </if>
            <if test="map.onlineFlag !=null and map.onlineFlag !=''">
                and datediff(MI,dateadd(minute,-30,GETDATE()), LASTONLINETM)
                <choose>
                    <when test="map.onlineFlag == '1'.toString()">>=</when>
                    <otherwise>&lt;</otherwise>
                </choose>
                0
            </if>
        </where>
        ORDER BY LASTONLINETM DESC
    </select>
</mapper>
