package com.huitu.cloud.api.syq.rain.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.syq.rain.entity.*;
import com.huitu.cloud.api.syq.rsvr.entity.RsvrRainList;
import com.huitu.cloud.api.syq.skdt.entity.RsvrAvgRain;
import com.huitu.cloud.api.syq.skdt.entity.RsvrRain;
import com.huitu.cloud.api.syq.skdt.entity.RsvrTmAvgRain;
import com.huitu.cloud.api.syq.warn.entity.DeptVo;
import com.huitu.cloud.api.syq.warn.entity.HeavyRainfallAlarm;
import com.huitu.cloud.api.syq.warn.entity.QueryNotice;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RainDao extends BaseMapper {
    /**
     * 分页查询累计雨量
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<Rain> getRainByCondition(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询累计雨量
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<Rain> getRainByConditionAllSt(Page page, @Param("map") Map<String, Object> param);

    List<RainByBas> getBasList(@Param("bscd") String bscd);
    List<RainByBas> getBasMainList(@Param("bscd") String bscd,@Param("keyWord") String keyWord);
    List<RainByBas> getBasMainMobileList(@Param("bscd") String bscd);

    List<RainByBas> getAllBasList(@Param("bscd") String bscd);

    List<BsnBasStTo> getBasStList();

    /**
     * 分页查询累计雨量
     *
     * @param page      分页page必须放在第一位
     * @param queryRain 实体传参 必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：em.stm
     * @return
     */
    IPage<Rain> getRainByConditionEntity(Page<Rain> page, @Param("em") QueryRain queryRain);

    /**
     * 多个参数分页查询 参数必须注解
     *
     * @param page 分页page必须放在第一位
     * @param stm
     * @param etm
     * @return
     */
    IPage<Rain> getRainByConditionParams(Page page, @Param("stm") String stm, @Param("etm") String etm);

    /**
     * 查询所有测站累计雨量
     *
     * @param param
     * @return
     */
    List<Rain> getAccpByTmAll(Map<String, Object> param);

    /**
     * 查询所有雨量测站
     *
     * @param param
     * @return
     */
    List<Rain> getRainStInfo(@Param("map") Map<String, Object> param);

    List<Rain> getRainStInfoByBas(@Param("map") Map<String, Object> param);

    /**
     * 单站雨量时段列表
     *
     * @param stm
     * @param etm
     * @param stcd
     * @return
     */
    List<StPptnR> getRainListByTm(@Param("stm") String stm, @Param("etm") String etm, @Param("stcd") String stcd, @Param("type") String type);

    /**
     * 查询雨量最大值
     *
     * @param param map对象必须注解
     * @return
     */
    List<Rain> getRainByConditionForMax(Map<String, Object> param);

    /**
     * 查询时间段内各个雨量级别的测站数量
     *
     * @param param map对象必须注解
     * @return
     */
    List<RainLevelStNum> getRainLevelStNumByTm(@Param("map") Map<String, Object> param);

    /**
     * 查询下级政区的面平均雨量和最大雨量
     *
     * @param param map对象必须注解
     * @return
     */
    List<AdAvgRain> getRainAdAvg(@Param("map") Map<String, Object> param);

    /**
     * 查询下级政区的面平均雨量和最大雨量
     *
     * @return
     */
    List<AdAvgRain> getRainAdAvgNew(@Param("stm") String stm, @Param("etm") String etm, @Param("stadtp") String stadtp, @Param("adcd") String adcd, @Param("isself") String isself);

    /**
     * 查询下级政区的最大雨量测站和上级政区的对应列表
     *
     * @param param map对象必须注解
     * @return
     */
    List<AdAvgRain> getMaxRainStAd(@Param("map") Map<String, Object> param);

    /**
     * 查询一段时间内1小时雨量的降雨强度
     *
     * @param param map对象必须注解
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<Map<String, Object>> getRainStrengthOneHour(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询一段时间内3,6小时雨量的降雨强度
     *
     * @param param map对象必须注解
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<Map<String, Object>> getRainStrength(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询一段时间山洪气象水文的降雨测站数统计
     *
     * @param param map对象必须注解
     * @return
     */
    AscriptionTypeCount getAscriptionTypeCount(@Param("map") Map<String, Object> param);

    /**
     * 降雨告警列表
     *
     * @param page  分页
     * @param param 参数集合
     * @param page
     * @return
     */
    IPage<BsnRainAlarm> getRainWarn(Page page, @Param("map") Map<String, Object> param);

    /**
     * 降雨告警指标设置
     *
     * @return
     */

    BsnRainalarmB getBsnRainalarmb();

    /**
     * 降雨告警指标设置
     *
     * @param param 参数集合
     * @return
     */
    Boolean updateBsnRainalarmb(@Param("map") Map<String, Object> param);

    /**
     * 分页查询历史雨量信息（小时）
     *
     * @param param
     * @param page
     * @return
     */
    IPage<HisRain> getRainHisHourListByPage(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询历史雨量信息（日，旬，月）
     *
     * @param param
     * @param page
     * @return
     */
    IPage<HisRain> getRainHisOtherListByPage(Page page, @Param("map") Map<String, Object> param);

    /**
     * 根据流域查询平均雨量和最大雨量
     *
     * @param param 参数集合
     * @return
     */
    List<RainfallStatisticsVo> selectAvgMaxDrp(@Param("map") Map<String, Object> param);

    /**
     * 根据流域最大雨量和该测站名称
     *
     * @param param 参数集合
     * @return
     */
    List<RainfallStatisticsVo> selectMaxDrpStnm(@Param("map") Map<String, Object> param);

    /**
     * 获取未来一小时降雨信息
     *
     * @return
     */
    List<Map<String, Object>> getOneHourRain();

    /**
     * 查询大型水库的平均雨量
     *
     * @param param 参数集合
     * @return
     */
    List<RsvrRainStatisticsVo> selectResAvgDrp(Map<String, Object> param);

    /**
     * 查询大型水库的最大雨量站
     *
     * @param param 参数集合
     * @return
     */
    List<RsvrRainStatisticsVo> selectResMaxDrp(Map<String, Object> param);

    /**
     * 按照政区年份查询降雨场次
     *
     * @param stm  开始时间 yyyy-MM-dd
     * @param etm  结束时间 yyyy-MM-dd
     * @param adcd 政区
     * @return
     */
    List<BsnTimesR> selectRainTimes(@Param("stm") String stm, @Param("etm") String etm, @Param("adcd") String adcd);

    /**
     * 根据场次查询测站的累计降雨
     *
     * @param page
     * @param param 条件集合
     * @return
     */
    IPage<TmsRainVo> selectTmsRainByCondition(Page page, @Param("map") Map<String, Object> param);

    /**
     * 按照时间查询水库调度关联水库的库面雨量
     *
     * @param stm 开始时间
     * @param etm 结束时间
     * @return
     */
    List<RsvrAvgRain> getSkdtRsvrRainInfo(@Param("stm") String stm, @Param("etm") String etm);

    /**
     * 按照水库工程编码查询库面雨量
     *
     * @param stm 开始时间
     * @param etm 结束时间
     * @return
     */
    RsvrAvgRain getSkdtRsvrRainInfoByResCode(@Param("stm") String stm, @Param("etm") String etm, @Param("resCode") String resCode);

    /**
     * 按照水库工程编码库区时段面雨量
     *
     * @param stm 开始时间
     * @param etm 结束时间
     * @return
     */
    List<RsvrTmAvgRain> getSkdtRsvrRainTmInfo(@Param("resCode") String resCode, @Param("stm") String stm, @Param("etm") String etm);

    /**
     * 根据测站编码集合查询累计雨量
     *
     * @param stcds
     * @param stm
     * @param etm
     * @return
     */
    List<Map<String, Object>> getRainByStcds(@Param("stcds") String stcds, @Param("stm") String stm, @Param("etm") String etm);

    /**
     * 根据测站编码集合查询实时雨量信息
     *
     * @param stcds 测站集合
     * @param stm   开始时间
     * @param etm
     * @return
     */
    List<StPptnR> getRainListByTmAndStcds(@Param("stcds") List<String> stcds, @Param("stm") String stm, @Param("etm") String etm);

    /**
     * 根据水库编码查询水库关联雨量站信息
     *
     * @param resCode
     * @param stm
     * @param etm
     * @return
     */
    List<RsvrRain> getSkdtRsvrRainByResCode(@Param("resCode") String resCode, @Param("stm") String stm, @Param("etm") String etm);

    IPage<Rain> getRainStInfoSq(Page page, @Param("map") Map<String, Object> param);

    List<Rain> getRainStInfoSqList(@Param("map") Map<String, Object> param);

    /**
     * 一天24小时时段降雨合并
     *
     * @param param
     * @return
     */
    IPage<OneDayRain> getRainListTmMerge(Page page, @Param("map") Map<String, Object> param);


    /**
     * 一天24小时时段降雨合并
     *
     * @param param
     * @return
     */
    IPage<OneDayRain> getRainListTmMergeForSqfx(Page page, @Param("map") Map<String, Object> param);

    /**
     * 强降雨告警列表（省级）
     *
     * @param param
     * @param page
     * @return
     */
    IPage<HeavyRainfallAlarm> getHeavyRainfallAlarm(Page page, @Param("map") Map<String, Object> param);

    /**
     * 强降雨告警通告期数查询
     *
     * @return
     */
    int getHeavyRainfallNoticeNo();

    /**
     * 强降雨告警通告新增
     *
     * @return
     */
    void insertHeavyRainfallNotice(QueryNotice query);

    /**
     * 查询发送单位
     *
     * @return
     */
    List<DeptVo> getReviceDept();

    /**
     * 最近24小时累计降雨统计
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return
     */
    IPage<RainTimeIntervalInfo> getTimeInterval(Page page, @Param("map") Map<String, Object> param);

    /**
     * 最近24小时累计降雨统计（新）
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return
     */
    IPage<RainTimeIntervalInfo> getTimeIntervalNew(Page page, @Param("map") Map<String, Object> param);

    /**
     * 最近24小时累计降雨统计（新）
     *
     * @param param 查询参数
     * @return
     */
    List<RainTimeIntervalInfo> getTimeIntervalList(@Param("map") Map<String, Object> param);


    /**
     * 根据流域编码查询所有的流域
     *
     * @param bscd 流域编码
     * @return
     */
    List<BsnBasStBTo> getRiverTree(@Param("bscd") String bscd);

    /**
     * 查询所有流域
     *
     * @return
     */
    List<BsnBasStBTo> selectRiver();

    /**
     * 墒情气象监测查询
     *
     * @param page
     * @param param
     * @return
     */
    IPage<MoistureMeteorologyVo> getMoistureForSqfx(Page page, @Param("map") Map<String, Object> param);

    void signHeavyRainfallNotice(@Param("stcd") String stcd, @Param("rightFillStr") String rightFillStr);

    /**
     * 获取流域降雨汇总列表
     *
     * @param params 查询条件
     * @return 汇总列表
     **/
    List<BasRainSummary> getBasRainSummaryList(@Param("map") Map<String, Object> params);

    List<StPstatR> getStcdContrastBytm(@Param("stcd") String stcd, @Param("tp") String tp, @Param("tms") List<String> tmList);

    List<StPstatR> getAdcdContrastBytmYear(@Param("ad") String ad, @Param("tms") List<String> tmList);

    List<StPstatR> getAdcdContrastBytmMonth(@Param("ad") String ad, @Param("tms") List<String> tmList);

    List<StPstatR> getBscdContrastBytmYear(@Param("bscd") String ad, @Param("tms") List<String> tmList);

    List<StPstatR> getBscdContrastBytmMonth(@Param("bscd") String ad, @Param("tms") List<String> tmList);

    List<RainForecastHour> getForecastHourByStcd(@Param("stcd") String stcd, @Param("stm") String stm);

    /**
     * 查询所有预报降雨测站累计雨量
     *
     * @param param
     * @return
     */
    List<Rain> getFAccpByTmAll(@Param("map") Map<String, Object> param);

    /**
     * 查询未来降雨量
     *
     * @param param
     * @return
     */
    List<Rain> getForecastList(@Param("map") Map<String, Object> param);

    List<AdAvgRainForecast> getAvgRainList(@Param("map") Map<String, Object> param);

    List<AdAvgRainForecast> getAvgForecastList(@Param("map") Map<String, Object> param);


    /**
     * 未来3小时预报降雨
     *
     * @param stcd
     * @param stm
     * @param etm
     * @return
     */
    List<StPptnF> getForecast3Hours(@Param("stcd") String stcd, @Param("stm") String stm, @Param("etm") String etm);


    /**
     * 未来3小时预报降雨
     *
     * @param stcd
     * @param stm
     * @param etm
     * @return
     */
    List<StPptnF> getForecast24Hours(@Param("stcd") String stcd, @Param("stm") String stm, @Param("etm") String etm);

    /**
     * 未来3小时或72小时逐小时预报降雨
     *
     * @param stcd
     * @param stm
     * @param etm
     * @return
     */
    List<StPptnFVo> getForecast3Or72List(@Param("stcd") String stcd, @Param("stm") String stm, @Param("etm") String etm, @Param("tb") String tb);

    List<RainPointerAD> getRainPointerADList(String stcd);

    List<RainPointerST> getRainPointerSTList(String adcd);

    /**
     * 雨情笼罩面降雨
     * @return
     */
    List<RainArea> getAdcdArea(@Param("map") Map<String, Object> param);

    /**
     * 根据政区编码查询一段时间内降雨测站归属类型统计
     * @param param
     * @return
     */
    AscriptionTypeCount getAscriptionTypeCountNewByAdcd(@Param("map") Map<String, Object> param);

    /**
     * 根据流域编码查询一段时间内降雨测站归属类型统计
     * @param param
     * @return
     */
    AscriptionTypeCount getAscriptionTypeCountNewByBscd(@Param("map") Map<String, Object> param);

    /**
     * 水库集水面积内测站降雨列表
     * @param param
     * @return
     */
    List<RsvrRainList> getStRsvrRainBaseList(@Param("map") Map<String, Object> param);

    /**
     * 水库集水面积内测站降雨列表
     * @param param
     * @return
     */
    List<RsvrRainList> getStRsvrRcList(@Param("map") Map<String, Object> param);
}
