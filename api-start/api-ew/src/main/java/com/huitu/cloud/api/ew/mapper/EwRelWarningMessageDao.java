package com.huitu.cloud.api.ew.mapper;

import com.huitu.cloud.api.ew.entity.base.EwRelWarningMessage;
import com.huitu.cloud.api.ew.entity.ext.EwWarningFlag;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwRelWarningMessageDao {

    /**
     * 获取预警标识
     *
     * @param msgId 消息ID
     * @return 预警标识
     */
    EwWarningFlag getWarningFlag(String msgId);
    /**
     * 新增预警和消息关联关系
     *
     * @param item 关联关系
     * @return 受影响的行数
     */
    int insert(EwRelWarningMessage item);
}
