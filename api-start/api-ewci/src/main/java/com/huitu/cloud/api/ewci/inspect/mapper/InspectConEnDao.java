package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectConEn;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 在建工程
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectConEnDao {

    /**
     * 判断是否添加过 在建工程
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectConEn> getInspectConEnList(@Param("adcd") String adcd, @Param("year") String year, @Param("enCode") String enCode);

    /**
     * 添加 在建工程
     *
     * @param entity
     * @return
     */
    int insertInspectConEn(BnsInspectConEn entity);

    /**
     * 修改 在建工程
     *
     * @param entity
     * @return
     */
    int updateInspectConEn(BnsInspectConEn entity);

    /**
     * 删除 在建工程
     *
     * @return
     */
    int deleteInspectConEn(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

