package com.huitu.cloud.api.hsfxt.city.mapper;

import com.huitu.cloud.api.hsfxt.city.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 风险图-城市 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-09
 */
public interface CityDao {

    /**
     * 编制区域信息查询
     *
     * @param fxtType 风险图类型
     * @return
     */
    List<DicBianzhiquyu> getBzqyInfo(@Param("fxtType") String fxtType);

    /**
     * 洪水来源信息查询
     *
     * @param sortIds 排序号
     * @return
     */
    List<DicHongShuiLaiYuan> getHslyInfo(@Param("sortIds") String sortIds);

    /**
     * 分区信息查询
     *
     * @param bianzhiquid 编制区编码
     * @return
     */
    List<DicJisuanfenqu> getFqInfo(@Param("bianzhiquid") String bianzhiquid);

    /**
     * 溃口信息查询
     *
     * @param jisuanfenquid 计算分区编码
     * @return
     */
    List<DicKuikou> getKkInfo(@Param("jisuanfenquid") String jisuanfenquid);

    /**
     * 洪水量级信息查询
     *
     * @param bianzhiquid 编制区编码
     * @return
     */
    List<DicHongshuiliangji> getHsljInfo(@Param("bianzhiquid") String bianzhiquid);

    /**
     * 降雨历时查询
     *
     * @param bianzhiquid 编制区编码
     * @return
     */
    List<DicJiangyulishi> getJylsInfo(@Param("bianzhiquid") String bianzhiquid);

    /**
     * 简介信息查询
     *
     * @param fengxiantuid 风险图类型编码
     * @param bianzhiquid  编制区编码
     * @return
     */
    List<DicJianjie> getJjInfo(@Param("fengxiantuid") String fengxiantuid, @Param("bianzhiquid") String bianzhiquid);

    /**
     * 溃口名称属性信息查询
     *
     * @param kuikousort 风险图类型编码
     * @return
     */
    List<DicShuxing> getKkmcInfo(@Param("kuikousort") String kuikousort);

    /**
     * 社会经济统计查询
     *
     * @param key 风险图类型编码
     * @return
     */
    List<CsShehuijingji> getShjjTreeInfo(@Param("key") String key);


    /**
     * 直接经济损失列表查询
     *
     * @param param  风险图类型编码
     * @return
     */
    List<CsZhijiejingjisunshi> getSjjjssTreeInfo(Map<String, Object> param);
}
