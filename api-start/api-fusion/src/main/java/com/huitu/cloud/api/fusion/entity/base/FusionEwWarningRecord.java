package com.huitu.cloud.api.fusion.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 山洪预警记录
 */
@ApiModel(value = "山洪预警记录")
public class FusionEwWarningRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARN_ID")
    private String warnId;
    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;
    @ApiModelProperty(value = "预警类型ID")
    @TableField(value = "WARN_TYPE_ID")
    private Integer warnTypeId;
    @ApiModelProperty(value = "预警等级ID")
    @TableField(value = "WARN_GRADE_ID")
    private Integer warnGradeId;
    @ApiModelProperty(value = "预警状态ID")
    @TableField(value = "WARN_STATUS_ID")
    private Integer warnStatusId;
    @ApiModelProperty(value = "预警模型，注：人工预警时，该字段为空")
    @TableField(value = "WARN_MODEL")
    private String warnModel;
    @ApiModelProperty(value = "预警时间")
    @TableField(value = "WARN_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warnTime;
    @ApiModelProperty(value = "预警方式，注：0=自动、1=人工")
    @TableField(value = "WARN_MODE")
    private String warnMode;
    @ApiModelProperty(value = "预警名称")
    @TableField(value = "WARN_NAME")
    private String warnName;
    @ApiModelProperty(value = "预警描述")
    @TableField(value = "WARN_DESC")
    private String warnDesc;
    @ApiModelProperty(value = "预警阈值历时")
    @TableField(value = "WARN_STDT")
    private Integer warnStdt;
    @ApiModelProperty(value = "经度")
    @TableField(value = "lgtd")
    private BigDecimal lgtd;
    @ApiModelProperty(value = "纬度")
    @TableField(value = "LTTD")
    private BigDecimal lttd;
    @ApiModelProperty(value = "备注")
    @TableField(value = "REMARK")
    private String remark;
    @ApiModelProperty(value = "平台ID")
    @TableField(value = "PLATFORM_ID")
    private String platformId;

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getWarnTypeId() {
        return warnTypeId;
    }

    public void setWarnTypeId(Integer warnTypeId) {
        this.warnTypeId = warnTypeId;
    }

    public Integer getWarnGradeId() {
        return warnGradeId;
    }

    public void setWarnGradeId(Integer warnGradeId) {
        this.warnGradeId = warnGradeId;
    }

    public Integer getWarnStatusId() {
        return warnStatusId;
    }

    public void setWarnStatusId(Integer warnStatusId) {
        this.warnStatusId = warnStatusId;
    }

    public String getWarnModel() {
        return warnModel;
    }

    public void setWarnModel(String warnModel) {
        this.warnModel = warnModel;
    }

    public Date getWarnTime() {
        return warnTime;
    }

    public void setWarnTime(Date warnTime) {
        this.warnTime = warnTime;
    }

    public String getWarnMode() {
        return warnMode;
    }

    public void setWarnMode(String warnMode) {
        this.warnMode = warnMode;
    }

    public String getWarnName() {
        return warnName;
    }

    public void setWarnName(String warnName) {
        this.warnName = warnName;
    }

    public String getWarnDesc() {
        return warnDesc;
    }

    public void setWarnDesc(String warnDesc) {
        this.warnDesc = warnDesc;
    }

    public Integer getWarnStdt() {
        return warnStdt;
    }

    public void setWarnStdt(Integer warnStdt) {
        this.warnStdt = warnStdt;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }
}
