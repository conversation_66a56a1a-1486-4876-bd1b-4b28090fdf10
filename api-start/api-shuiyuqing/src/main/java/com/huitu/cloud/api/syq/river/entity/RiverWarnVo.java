package com.huitu.cloud.api.syq.river.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value = "RiverWarnVo",description = "河道告警信息类")
public class RiverWarnVo {
    @ApiModelProperty(value = "超警戒集合")
    private List<StRiverVo> warnWrzList;
    @ApiModelProperty(value = "超保证集合")
    private List<StRiverVo> warnGrzList;

    public List<StRiverVo> getWarnWrzList() {
        return warnWrzList;
    }

    public void setWarnWrzList(List<StRiverVo> warnWrzList) {
        this.warnWrzList = warnWrzList;
    }

    public List<StRiverVo> getWarnGrzList() {
        return warnGrzList;
    }

    public void setWarnGrzList(List<StRiverVo> warnGrzList) {
        this.warnGrzList = warnGrzList;
    }
}
