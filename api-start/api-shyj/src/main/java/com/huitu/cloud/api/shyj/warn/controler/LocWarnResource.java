package com.huitu.cloud.api.shyj.warn.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.shyj.warn.entity.LocWarnQueryForm;
import com.huitu.cloud.api.shyj.warn.entity.LocWarnVo;
import com.huitu.cloud.api.shyj.warn.entity.StwarnrecordXdRVo;
import com.huitu.cloud.api.shyj.warn.service.LocWarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 现地预警记录表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RestController
@Api(tags = "现地预警记录接口")
@RequestMapping("/api/shyj/locwarn")
public class LocWarnResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "b8a50c0f-1f5f-4758-bc4c-ef6d02f53e1e";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private LocWarnService baseService;


    @ApiOperation(value = "分页查询现地预警信息", notes = "分页查询现地预警信息")
    @PostMapping(value = "pageLocWarn")
    public ResponseEntity<SuccessResponse<Page<LocWarnVo>>> pageLocWarn(@RequestBody LocWarnQueryForm queryForm) throws Exception {
        IPage<LocWarnVo> list = baseService.pageLocWarn(queryForm);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "列表查询现地预警信息", notes = "列表查询现地预警信息")
    @PostMapping(value = "listLocWarn")
    public ResponseEntity<SuccessResponse<List<StwarnrecordXdRVo>>> listLocWarn(@RequestBody LocWarnQueryForm queryForm) throws Exception {
        List<StwarnrecordXdRVo> list = baseService.listLocWarn(queryForm);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询测站在一段时间内现地预警信息", notes = "查询测站在一段时间内现地预警信息")
    @GetMapping(value = "listLocWarnDetailByStcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间",  dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间",  dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<StwarnrecordXdRVo>>> listLocWarnDetailByStcd(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        Assert.hasText(stcd, "参数[预警ID]不能为空");
        Assert.hasText(stm, "参数[预警ID]不能为空");
        Assert.hasText(etm, "参数[预警ID]不能为空");
        List<StwarnrecordXdRVo> list = baseService.listLocWarnDetailByStcd(stcd, stm, etm);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询现地预警详情", notes = "查询现地预警详情")
    @PostMapping(value = "getLocWarn")
    public ResponseEntity<SuccessResponse<LocWarnVo>> getLocWarn(@RequestBody LocWarnQueryForm queryForm) throws Exception {
        LocWarnVo locWarnVo = baseService.getLocWarnById(queryForm.getWarnid());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", locWarnVo));
    }

}
