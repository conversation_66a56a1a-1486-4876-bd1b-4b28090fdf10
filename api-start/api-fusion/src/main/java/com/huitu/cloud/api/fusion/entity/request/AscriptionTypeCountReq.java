package com.huitu.cloud.api.fusion.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.entity.EntityRequest;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.validation.constraints.StringLength;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiModel(value = "山洪雨量升级预警底部统计请求对象")
@Data
public class AscriptionTypeCountReq implements EntityRequest, Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码", example = "220000000000000", required = true)
    @NotBlank(message = "参数[行政区划代码]不能为空")
    @StringLength(min = 15, max = 15, message = "参数[行政区划代码]的长度应为15个字符")
    private String adcd;

    @ApiModelProperty(value = "开始时间，注：必填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-03 08:00:00", required = true)
    @NotNull(message = "参数[开始时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String stm;

    @ApiModelProperty(value = "结束时间，注：选填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-04 08:00:00")
    @NotNull(message = "参数[开始时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String etm;

    @ApiModelProperty(value = "预警名称，注：模糊匹配")
    private String warnName;

    @Override
    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("stm", getStm());
        params.put("etm", getEtm());
        params.put("warnName", getWarnName());
        return params;
    }
}
