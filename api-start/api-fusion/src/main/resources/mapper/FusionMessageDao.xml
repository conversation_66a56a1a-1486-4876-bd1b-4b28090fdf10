<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.fusion.mapper.FusionMessageDao">
    <select id="getMessageList" resultType="com.huitu.cloud.api.fusion.entity.ext.FusionWarningMessage">
        SELECT
            A.MSG_ID,
            A.WARN_ID,
            B.PUSH_MODE
        FROM
            EW_FUSION_REL_MESSAGE A
                LEFT JOIN EW_FUSION_MESSAGE B ON A.MSG_ID = B.MSG_ID
        WHERE
            A.WARN_ID = #{warnId}
        ORDER BY
            A.LATEST_TIME ASC
    </select>
</mapper>
