package com.huitu.cloud.api.fusion.entity.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value = "预警次数和短信条数统计", description = "预警次数和短信条数统计")
public class FusionWarnMessageStatisticsResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "上级政区编码")
    private String padcd;

    @ApiModelProperty(value = "预警总数")
    private int allTotal;

    @ApiModelProperty(value = "预警短信条数")
    private int totalMassage;
    @ApiModelProperty(value = "预警统计下级对象")
    private List<FusionWarnMessageStatisticsResp> children;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public int getAllTotal() {
        return allTotal;
    }

    public void setAllTotal(int allTotal) {
        this.allTotal = allTotal;
    }

    public int getTotalMassage() {
        return totalMassage;
    }

    public void setTotalMassage(int totalMassage) {
        this.totalMassage = totalMassage;
    }

    public List<FusionWarnMessageStatisticsResp> getChildren() {
        return children;
    }

    public void setChildren(List<FusionWarnMessageStatisticsResp> children) {
        this.children = children;
    }
}
