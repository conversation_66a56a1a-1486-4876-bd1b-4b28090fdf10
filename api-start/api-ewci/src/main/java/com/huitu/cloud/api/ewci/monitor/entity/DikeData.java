package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 堤防
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@ApiModel(value = "堤防列表")
public class DikeData implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "堤防索引")
    @TableField(value = "DIKECD")
    private String dikecd;

    @ApiModelProperty(value = "堤防编码")
    @TableField(value = "DIKE_CODE")
    private String dikeCode;

    @ApiModelProperty(value = "堤防名称")
    @TableField(value = "DIKE_NAME")
    private String dikeName;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "县级行政区划名称")
    @TableField(value = "XADNM")
    private String xadnm;

    @ApiModelProperty(value = "乡镇行政区划名称")
    @TableField(value = "XZADNM")
    private String xzadnm;

    @ApiModelProperty(value = "村行政区划名称")
    @TableField(value = "CADNM")
    private String cadnm;

    @ApiModelProperty(value = "小流域代码")
    @TableField(value = "wscd")
    private String wscd;

    @ApiModelProperty(value = "小流域名称")
    @TableField(value = "WSNM")
    private String wsnm;

    @ApiModelProperty(value = "河流（湖泊）编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "河流岸别")
    @TableField(value = "RV_BANK")
    private String rvBank;

    @ApiModelProperty(value = "堤防跨界情况")
    @TableField(value = "DIKE_COR_BOUN")
    private String dikeCorBoun;

    @ApiModelProperty(value = "堤防类型")
    @TableField(value = "DIKE_TYPE")
    private String dikeType;

    @ApiModelProperty(value = "堤防型式")
    @TableField(value = "DIKE_STYL")
    private String dikeStyl;

    @ApiModelProperty(value = "堤防级别")
    @TableField(value = "DIKE_GRAD")
    private String dikeGrad;

    @ApiModelProperty(value = "规划防洪(潮)标准［重现期］（年）")
    @TableField(value = "PLAN_FL_STA")
    private String planFlSta;

    @ApiModelProperty(value = "堤防长度(m)")
    @TableField(value = "DIKE_LEN")
    private BigDecimal dikeLen;

    @ApiModelProperty(value = "达到规划防洪（潮）标准的长度(m)")
    @TableField(value = "FL_STA_LEN")
    private BigDecimal flStaLen;


    @ApiModelProperty(value = "高程系统")
    @TableField(value = "ELE_SYS")
    private String eleSys;

    @ApiModelProperty(value = "设计水（高潮）位(m)")
    @TableField(value = "DES_STAG")
    private BigDecimal desStag;

    @ApiModelProperty(value = "堤防高度(m)：（最大值）")
    @TableField(value = "DIKE_HIG_MAX")
    private BigDecimal dikeHigMax;

    @ApiModelProperty(value = "堤顶宽度(m)：（最大值）")
    @TableField(value = "DIKE_WID_MAX")
    private BigDecimal dikeWidMax;

    @ApiModelProperty(value = "工程任务")
    @TableField(value = "ENG_TASK")
    private String engTask;

    @ApiModelProperty(value = "堤防高度(m)：（最小值）")
    @TableField(value = "DIKE_HIG_MIN")
    private BigDecimal dikeHigMin;

    @ApiModelProperty(value = "堤顶宽度(m)：（最小值）")
    @TableField(value = "DIKE_WID_MIN")
    private BigDecimal dikeWidMin;

    @ApiModelProperty(value = "堤顶高程起点高程(m)")
    @TableField(value = "DAM_CRE_BEG_ELE")
    private BigDecimal damCreBegEle;

    @ApiModelProperty(value = "堤顶高程终点高程(m)")
    @TableField(value = "DAM_CRE_EDN_ELE")
    private BigDecimal damCreEdnEle;

    public String getDikecd() {
        return dikecd;
    }

    public void setDikecd(String dikecd) {
        this.dikecd = dikecd;
    }

    public String getDikeCode() {
        return dikeCode;
    }

    public void setDikeCode(String dikeCode) {
        this.dikeCode = dikeCode;
    }

    public String getDikeName() {
        return dikeName;
    }

    public void setDikeName(String dikeName) {
        this.dikeName = dikeName;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getXzadnm() {
        return xzadnm;
    }

    public void setXzadnm(String xzadnm) {
        this.xzadnm = xzadnm;
    }

    public String getCadnm() {
        return cadnm;
    }

    public void setCadnm(String cadnm) {
        this.cadnm = cadnm;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRvBank() {
        return rvBank;
    }

    public void setRvBank(String rvBank) {
        this.rvBank = rvBank;
    }

    public String getDikeCorBoun() {
        return dikeCorBoun;
    }

    public void setDikeCorBoun(String dikeCorBoun) {
        this.dikeCorBoun = dikeCorBoun;
    }

    public String getDikeType() {
        return dikeType;
    }

    public void setDikeType(String dikeType) {
        this.dikeType = dikeType;
    }

    public String getDikeStyl() {
        return dikeStyl;
    }

    public void setDikeStyl(String dikeStyl) {
        this.dikeStyl = dikeStyl;
    }

    public String getDikeGrad() {
        return dikeGrad;
    }

    public void setDikeGrad(String dikeGrad) {
        this.dikeGrad = dikeGrad;
    }

    public String getPlanFlSta() {
        return planFlSta;
    }

    public void setPlanFlSta(String planFlSta) {
        this.planFlSta = planFlSta;
    }

    public BigDecimal getDikeLen() {
        return dikeLen;
    }

    public void setDikeLen(BigDecimal dikeLen) {
        this.dikeLen = dikeLen;
    }

    public BigDecimal getFlStaLen() {
        return flStaLen;
    }

    public void setFlStaLen(BigDecimal flStaLen) {
        this.flStaLen = flStaLen;
    }

    public String getEleSys() {
        return eleSys;
    }

    public void setEleSys(String eleSys) {
        this.eleSys = eleSys;
    }

    public BigDecimal getDesStag() {
        return desStag;
    }

    public void setDesStag(BigDecimal desStag) {
        this.desStag = desStag;
    }

    public BigDecimal getDikeHigMax() {
        return dikeHigMax;
    }

    public void setDikeHigMax(BigDecimal dikeHigMax) {
        this.dikeHigMax = dikeHigMax;
    }

    public BigDecimal getDikeWidMax() {
        return dikeWidMax;
    }

    public void setDikeWidMax(BigDecimal dikeWidMax) {
        this.dikeWidMax = dikeWidMax;
    }

    public String getEngTask() {
        return engTask;
    }

    public void setEngTask(String engTask) {
        this.engTask = engTask;
    }

    public BigDecimal getDikeHigMin() {
        return dikeHigMin;
    }

    public void setDikeHigMin(BigDecimal dikeHigMin) {
        this.dikeHigMin = dikeHigMin;
    }

    public BigDecimal getDikeWidMin() {
        return dikeWidMin;
    }

    public void setDikeWidMin(BigDecimal dikeWidMin) {
        this.dikeWidMin = dikeWidMin;
    }

    public BigDecimal getDamCreBegEle() {
        return damCreBegEle;
    }

    public void setDamCreBegEle(BigDecimal damCreBegEle) {
        this.damCreBegEle = damCreBegEle;
    }

    public BigDecimal getDamCreEdnEle() {
        return damCreEdnEle;
    }

    public void setDamCreEdnEle(BigDecimal damCreEdnEle) {
        this.damCreEdnEle = damCreEdnEle;
    }
}
