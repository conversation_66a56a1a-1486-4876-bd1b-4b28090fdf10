package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-29
 */
@ApiModel(value="StPptnF对象", description="")
public class StPptnF extends Model<StPptnF> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "发生时间")
    @TableField("TM")
    private LocalDateTime tm;

    @ApiModelProperty(value = "时段降水量")
    @TableField("DRP")
    private Double drp;

    @ApiModelProperty(value = "时间戳")
    @TableField("TS")
    private LocalDateTime ts;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public Double getDrp() {
        return drp;
    }

    public void setDrp(Double drp) {
        this.drp = drp;
    }

    public LocalDateTime getTs() {
        return ts;
    }

    public void setTs(LocalDateTime ts) {
        this.ts = ts;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "StPptnF{" +
        "stcd=" + stcd +
        ", tm=" + tm +
        ", drp=" + drp +
        ", ts=" + ts +
        "}";
    }
}
