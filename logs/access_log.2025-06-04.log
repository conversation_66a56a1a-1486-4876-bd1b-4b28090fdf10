12******* - - [04/Jun/2025:14:07:27 +0800] "GET /doc.html HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:07:27 +0800] "GET /login HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:07:28 +0800] "GET /doc.html HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:07:28 +0800] "GET /login HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /login?code=AD1O6x&state=Nhbjfn HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /doc.html HTTP/1.1" 200 71645
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
12******* - - [04/Jun/2025:14:07:35 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
12******* - - [04/Jun/2025:14:07:36 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
12******* - - [04/Jun/2025:14:07:36 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
12******* - - [04/Jun/2025:14:07:36 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
12******* - - [04/Jun/2025:14:07:36 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
12******* - - [04/Jun/2025:14:07:36 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
12******* - - [04/Jun/2025:14:07:36 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
12******* - - [04/Jun/2025:14:07:37 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
12******* - - [04/Jun/2025:14:07:37 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
12******* - - [04/Jun/2025:14:07:37 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
12******* - - [04/Jun/2025:14:07:37 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
12******* - - [04/Jun/2025:14:07:37 +0800] "GET /swagger-resources HTTP/1.1" 200 101
12******* - - [04/Jun/2025:14:07:37 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
12******* - - [04/Jun/2025:14:07:37 +0800] "GET /v2/api-docs HTTP/1.1" 200 2673949
12******* - - [04/Jun/2025:14:07:53 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
12******* - - [04/Jun/2025:14:07:53 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
12******* - - [04/Jun/2025:14:07:53 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
12******* - - [04/Jun/2025:14:07:54 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
12******* - - [04/Jun/2025:14:07:54 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
12******* - - [04/Jun/2025:14:07:57 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
12******* - - [04/Jun/2025:14:07:58 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3702
12******* - - [04/Jun/2025:14:07:58 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
12******* - - [04/Jun/2025:14:10:53 +0800] "GET /doc.html HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:10:53 +0800] "GET /login HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /login?code=8YuNr8&state=JlG7jc HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /doc.html HTTP/1.1" 200 71645
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
12******* - - [04/Jun/2025:14:10:54 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /swagger-resources HTTP/1.1" 200 101
12******* - - [04/Jun/2025:14:10:55 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
12******* - - [04/Jun/2025:14:10:56 +0800] "GET /v2/api-docs HTTP/1.1" 200 2673949
12******* - - [04/Jun/2025:14:10:59 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
12******* - - [04/Jun/2025:14:10:59 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
12******* - - [04/Jun/2025:14:10:59 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
12******* - - [04/Jun/2025:14:10:59 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
12******* - - [04/Jun/2025:14:10:59 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
12******* - - [04/Jun/2025:14:11:01 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
12******* - - [04/Jun/2025:14:11:02 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 8277
12******* - - [04/Jun/2025:14:11:02 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
12******* - - [04/Jun/2025:14:15:00 +0800] "GET /doc.html HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:15:00 +0800] "GET /login HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /login?code=iN1zpw&state=GKVpWy HTTP/1.1" 302 -
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /doc.html HTTP/1.1" 200 71645
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
12******* - - [04/Jun/2025:14:15:01 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
12******* - - [04/Jun/2025:14:15:02 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
12******* - - [04/Jun/2025:14:15:02 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
12******* - - [04/Jun/2025:14:15:02 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
12******* - - [04/Jun/2025:14:15:02 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
12******* - - [04/Jun/2025:14:15:02 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
12******* - - [04/Jun/2025:14:15:02 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
12******* - - [04/Jun/2025:14:15:03 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
12******* - - [04/Jun/2025:14:15:03 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
12******* - - [04/Jun/2025:14:15:03 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
12******* - - [04/Jun/2025:14:15:03 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
12******* - - [04/Jun/2025:14:15:03 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
12******* - - [04/Jun/2025:14:15:03 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
12******* - - [04/Jun/2025:14:15:03 +0800] "GET /swagger-resources HTTP/1.1" 200 101
12******* - - [04/Jun/2025:14:15:03 +0800] "GET /v2/api-docs HTTP/1.1" 200 2673949
12******* - - [04/Jun/2025:14:15:07 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
12******* - - [04/Jun/2025:14:15:07 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
12******* - - [04/Jun/2025:14:15:07 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
12******* - - [04/Jun/2025:14:15:07 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
12******* - - [04/Jun/2025:14:15:07 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
12******* - - [04/Jun/2025:14:15:13 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
12******* - - [04/Jun/2025:14:15:13 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 8277
12******* - - [04/Jun/2025:14:15:14 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [04/Jun/2025:17:26:34 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [04/Jun/2025:17:26:34 +0800] "GET /login HTTP/1.1" 302 -
************ - - [04/Jun/2025:17:26:35 +0800] "GET /login?code=n42mkQ&state=C0ZJ7A HTTP/1.1" 302 -
************ - - [04/Jun/2025:17:26:35 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [04/Jun/2025:17:26:36 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1749029196328 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1749029196328 HTTP/1.1" 200 508
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1749029199053 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1749029199053 HTTP/1.1" 200 61412
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1749029199118 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1749029199118 HTTP/1.1" 200 10388
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1749029199132 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1749029199132 HTTP/1.1" 200 2009
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1749029199363 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-01+17:26:39&etm=&_timer304=1749029199367 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1749029199367 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-04+08:00&etm=2025-06-04+18:00&filterCnt=6&_timer304=1749029199367 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1749029199367 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1749029199367 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:39 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [04/Jun/2025:17:26:41 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-01+17:26:39&etm=&_timer304=1749029199367 HTTP/1.1" 200 156
************ - - [04/Jun/2025:17:26:41 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-04+08:00&etm=2025-06-04+18:00&filterCnt=6&_timer304=1749029199367 HTTP/1.1" 200 164
************ - - [04/Jun/2025:17:26:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:26:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1749029199367 HTTP/1.1" 200 166
************ - - [04/Jun/2025:17:26:41 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [04/Jun/2025:17:26:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:26:41 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1749029199363 HTTP/1.1" 200 1482
************ - - [04/Jun/2025:17:26:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1749029199367 HTTP/1.1" 200 169
************ - - [04/Jun/2025:17:26:41 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1749029199367 HTTP/1.1" 200 13016
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-04&_timer304=1749029201743 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/base/saas/token?_timer304=1749029201743 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1749029201759 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:41 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [04/Jun/2025:17:26:43 +0800] "GET /api/base/saas/token?_timer304=1749029201743 HTTP/1.1" 200 411
************ - - [04/Jun/2025:17:26:43 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1749029203782 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:26:43 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-01+17:26:43&etm=&_timer304=1749029203936 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:43 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1749029203936 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:43 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1749029203936 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-04+08:00&etm=2025-06-04+18:00&filterCnt=6&_timer304=1749029203936 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1749029203936 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:44 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:44 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:44 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-04+08:00&etm=2025-06-04+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1749029204067 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:44 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1749029203941 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1749029203937 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 361
************ - - [04/Jun/2025:17:26:44 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1749029201759 HTTP/1.1" 200 2009
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:26:44 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-01+17:26:43&etm=&_timer304=1749029203936 HTTP/1.1" 200 156
************ - - [04/Jun/2025:17:26:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1749029203936 HTTP/1.1" 200 166
************ - - [04/Jun/2025:17:26:44 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-04+08:00&etm=2025-06-04+18:00&filterCnt=6&_timer304=1749029203936 HTTP/1.1" 200 164
************ - - [04/Jun/2025:17:26:44 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1749029203936 HTTP/1.1" 200 13016
************ - - [04/Jun/2025:17:26:44 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [04/Jun/2025:17:26:44 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1749029203782 HTTP/1.1" 200 144
************ - - [04/Jun/2025:17:26:45 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1749029203936 HTTP/1.1" 200 169
************ - - [04/Jun/2025:17:26:45 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-04&_timer304=1749029201743 HTTP/1.1" 200 356
************ - - [04/Jun/2025:17:26:45 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [04/Jun/2025:17:26:45 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1749029205429 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:45 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1749029203941 HTTP/1.1" 200 159491
************ - - [04/Jun/2025:17:26:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1749029203937 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:26:45 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1749029205429 HTTP/1.1" 200 155
************ - - [04/Jun/2025:17:26:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1749029206059 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:46 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:50 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [04/Jun/2025:17:26:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 8948
************ - - [04/Jun/2025:17:26:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 8948
************ - - [04/Jun/2025:17:26:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 8948
************ - - [04/Jun/2025:17:26:53 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [04/Jun/2025:17:26:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1749029206059 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:26:53 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [04/Jun/2025:17:26:53 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029213987 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:54 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:26:54 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029213987 HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:26:54 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:26:54 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029214368 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:26:54 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029214368 HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:26:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-04+08:00&etm=2025-06-04+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1749029204067 HTTP/1.1" 200 440836
************ - - [04/Jun/2025:17:26:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1377982
************ - - [04/Jun/2025:17:27:39 +0800] "OPTIONS /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029259776 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:39 +0800] "OPTIONS /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029259775 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:39 +0800] "GET /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029259776 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:27:39 +0800] "GET /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029259775 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:27:40 +0800] "OPTIONS /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029260540 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:40 +0800] "GET /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029260540 HTTP/1.1" 200 208
************ - - [04/Jun/2025:17:27:40 +0800] "OPTIONS /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:40 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:40 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029260624 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:40 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:40 +0800] "GET /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029260624 HTTP/1.1" 200 1264
************ - - [04/Jun/2025:17:27:40 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:27:40 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:27:40 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029260702 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:40 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029260702 HTTP/1.1" 200 791
************ - - [04/Jun/2025:17:27:40 +0800] "OPTIONS /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029260754 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:40 +0800] "GET /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029260754 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:27:40 +0800] "OPTIONS /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029260779 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:40 +0800] "GET /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029260779 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:27:40 +0800] "OPTIONS /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029260790 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:40 +0800] "GET /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029260790 HTTP/1.1" 200 1055
************ - - [04/Jun/2025:17:27:40 +0800] "POST /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 626476
************ - - [04/Jun/2025:17:27:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029261170 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:41 +0800] "OPTIONS /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:41 +0800] "OPTIONS /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029261170 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:41 +0800] "OPTIONS /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:30&_timer304=1749029261170 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:41 +0800] "POST /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 245
************ - - [04/Jun/2025:17:27:41 +0800] "GET /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:30&_timer304=1749029261170 HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:27:41 +0800] "GET /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029261170 HTTP/1.1" 200 158
************ - - [04/Jun/2025:17:27:41 +0800] "GET /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029261170 HTTP/1.1" 200 426
************ - - [04/Jun/2025:17:27:41 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029261410 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:41 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029261410 HTTP/1.1" 200 649
************ - - [04/Jun/2025:17:27:56 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:56 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:56 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:56 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:56 +0800] "OPTIONS /api/syq/rsvr/select-xxsw-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:56 +0800] "OPTIONS /api/syq/rsvr/select-xxsw-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:27:56 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 791
************ - - [04/Jun/2025:17:27:56 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 649
************ - - [04/Jun/2025:17:27:56 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 791
************ - - [04/Jun/2025:17:27:56 +0800] "GET /api/syq/rsvr/select-xxsw-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 792
************ - - [04/Jun/2025:17:27:56 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 649
************ - - [04/Jun/2025:17:27:56 +0800] "GET /api/syq/rsvr/select-xxsw-by-stcd?stcd=10812520&_timer304=1749029276142 HTTP/1.1" 200 792
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029297027 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029297027 HTTP/1.1" 200 208
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029297102 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "POST /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 626476
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029297102 HTTP/1.1" 200 1264
************ - - [04/Jun/2025:17:28:17 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:28:17 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029297337 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:30&_timer304=1749029297337 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029297337 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029297337 HTTP/1.1" 200 426
************ - - [04/Jun/2025:17:28:17 +0800] "POST /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 245
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029297349 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:30&_timer304=1749029297337 HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029297349 HTTP/1.1" 200 791
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029297429 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029297337 HTTP/1.1" 200 158
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029297429 HTTP/1.1" 200 649
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029297440 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029297440 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029297458 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029297458 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:28:17 +0800] "OPTIONS /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029297473 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:17 +0800] "GET /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029297473 HTTP/1.1" 200 1055
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029310185 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029310185 HTTP/1.1" 200 208
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029310245 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029310245 HTTP/1.1" 200 1264
************ - - [04/Jun/2025:17:28:30 +0800] "POST /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 626476
************ - - [04/Jun/2025:17:28:30 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:28:30 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029310481 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029310481 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:30&_timer304=1749029310481 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029310481 HTTP/1.1" 200 426
************ - - [04/Jun/2025:17:28:30 +0800] "POST /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 245
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:30&_timer304=1749029310481 HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029310494 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029310494 HTTP/1.1" 200 791
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029310481 HTTP/1.1" 200 158
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029310575 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029310575 HTTP/1.1" 200 649
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029310588 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029310588 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029310635 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029310635 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:28:30 +0800] "OPTIONS /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029310654 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:30 +0800] "GET /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029310654 HTTP/1.1" 200 1055
************ - - [04/Jun/2025:17:28:31 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-fhdd-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:31 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-gctx-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:31 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-swkrmjxl-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:31 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rsvr-design-flood-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:31 +0800] "OPTIONS /api/dzsj/rvrsvr/get-pic-info HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:31 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-hsdd-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:31 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-survey-info?resCode=220524024000019&_timer304=1749029311376 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:31 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-related-info?resCode=220524024000019&_timer304=1749029311376 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:31 +0800] "OPTIONS /api/syq/video/select-video-info-by-code?code=220524024000019&_timer304=1749029311376 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:31 +0800] "POST /api/dzsj/rvrsvr/get-pic-info HTTP/1.1" 200 148
************ - - [04/Jun/2025:17:28:31 +0800] "GET /api/dzsj/rvrsvr/get-rvrsvr-hsdd-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 156
************ - - [04/Jun/2025:17:28:31 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-design-flood-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 162
************ - - [04/Jun/2025:17:28:31 +0800] "GET /api/syq/video/select-video-info-by-code?code=220524024000019&_timer304=1749029311376 HTTP/1.1" 200 1055
************ - - [04/Jun/2025:17:28:31 +0800] "GET /api/dzsj/rvrsvr/get-rvrsvr-survey-info?resCode=220524024000019&_timer304=1749029311376 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:28:31 +0800] "GET /api/dzsj/rvrsvr/get-rvrsvr-swkrmjxl-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:28:31 +0800] "GET /api/dzsj/rvrsvr/get-rvrsvr-fhdd-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 510
************ - - [04/Jun/2025:17:28:31 +0800] "GET /api/dzsj/rvrsvr/get-rvrsvr-gctx-info?resCode=220524024000019&_timer304=1749029311375 HTTP/1.1" 200 1150
************ - - [04/Jun/2025:17:28:31 +0800] "GET /api/dzsj/rvrsvr/get-rvrsvr-related-info?resCode=220524024000019&_timer304=1749029311376 HTTP/1.1" 200 513
************ - - [04/Jun/2025:17:28:37 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029317762 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:37 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029317762 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:37 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029317762 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:37 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029317762 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:37 +0800] "OPTIONS /api/syq/rsvr/select-xxsw-by-stcd?stcd=10812520&_timer304=1749029317763 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:37 +0800] "OPTIONS /api/syq/rsvr/select-xxsw-by-stcd?stcd=10812520&_timer304=1749029317763 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:37 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029317762 HTTP/1.1" 200 791
************ - - [04/Jun/2025:17:28:37 +0800] "GET /api/syq/rsvr/select-xxsw-by-stcd?stcd=10812520&_timer304=1749029317763 HTTP/1.1" 200 792
************ - - [04/Jun/2025:17:28:37 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029317762 HTTP/1.1" 200 649
************ - - [04/Jun/2025:17:28:37 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029317762 HTTP/1.1" 200 791
************ - - [04/Jun/2025:17:28:37 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029317762 HTTP/1.1" 200 649
************ - - [04/Jun/2025:17:28:37 +0800] "GET /api/syq/rsvr/select-xxsw-by-stcd?stcd=10812520&_timer304=1749029317763 HTTP/1.1" 200 792
************ - - [04/Jun/2025:17:28:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1749029324232 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:44 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1749029324235 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:44 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1749029324232 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:28:44 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1749029324235 HTTP/1.1" 200 159491
************ - - [04/Jun/2025:17:28:44 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-01+17:28:44&etm=&_timer304=1749029324292 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1749029324292 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-04+08:00&etm=2025-06-04+18:00&filterCnt=6&_timer304=1749029324292 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:44 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1749029324292 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:44 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [04/Jun/2025:17:28:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1749029324292 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:44 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-04+08:00&etm=2025-06-04+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1749029324345 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:28:44 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [04/Jun/2025:17:28:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:28:44 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [04/Jun/2025:17:28:44 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-04+08:00&etm=2025-06-04+18:00&filterCnt=6&_timer304=1749029324292 HTTP/1.1" 200 164
************ - - [04/Jun/2025:17:28:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1749029324292 HTTP/1.1" 200 166
************ - - [04/Jun/2025:17:28:44 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-01+17:28:44&etm=&_timer304=1749029324292 HTTP/1.1" 200 156
************ - - [04/Jun/2025:17:28:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1749029324292 HTTP/1.1" 200 169
************ - - [04/Jun/2025:17:28:44 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1749029324292 HTTP/1.1" 200 13016
************ - - [04/Jun/2025:17:28:45 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [04/Jun/2025:17:28:45 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [04/Jun/2025:17:28:45 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1749029325364 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:45 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1749029325364 HTTP/1.1" 200 155
************ - - [04/Jun/2025:17:28:45 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [04/Jun/2025:17:28:45 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029325399 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:46 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [04/Jun/2025:17:28:47 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029325399 HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:28:47 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-04+08:00&etm=2025-06-04+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1749029324345 HTTP/1.1" 200 440836
************ - - [04/Jun/2025:17:28:50 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 8946
************ - - [04/Jun/2025:17:28:50 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:28:50 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:28:50 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029330899 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:50 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029330899 HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:28:50 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1377982
************ - - [04/Jun/2025:17:28:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1749029334069 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:28:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1749029334069 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:29:11 +0800] "OPTIONS /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029351519 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:11 +0800] "OPTIONS /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029351519 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:11 +0800] "GET /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029351519 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:29:11 +0800] "GET /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029351519 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:29:12 +0800] "OPTIONS /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029352551 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:12 +0800] "GET /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029352551 HTTP/1.1" 200 208
************ - - [04/Jun/2025:17:29:12 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029352610 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:12 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:29:12 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:29:12 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029352647 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:12 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029352647 HTTP/1.1" 200 791
************ - - [04/Jun/2025:17:29:12 +0800] "GET /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029352610 HTTP/1.1" 200 1264
************ - - [04/Jun/2025:17:29:12 +0800] "OPTIONS /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029352669 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:12 +0800] "GET /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029352669 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:29:12 +0800] "POST /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 626476
************ - - [04/Jun/2025:17:29:12 +0800] "OPTIONS /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029352704 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:12 +0800] "GET /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029352704 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:29:12 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029352937 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:12 +0800] "OPTIONS /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029352937 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:12 +0800] "OPTIONS /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:30&_timer304=1749029352937 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:12 +0800] "POST /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 245
************ - - [04/Jun/2025:17:29:12 +0800] "GET /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029352937 HTTP/1.1" 200 426
************ - - [04/Jun/2025:17:29:12 +0800] "OPTIONS /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029352943 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:12 +0800] "GET /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:30&_timer304=1749029352937 HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:29:12 +0800] "GET /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029352943 HTTP/1.1" 200 1055
************ - - [04/Jun/2025:17:29:13 +0800] "GET /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:30&etm=2025-06-04+17:30&_timer304=1749029352937 HTTP/1.1" 200 158
************ - - [04/Jun/2025:17:29:13 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029353028 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:29:13 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029353028 HTTP/1.1" 200 649
************ - - [04/Jun/2025:17:30:28 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-01+17:30:27&etm=&_timer304=1749029427735 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1749029427735 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1749029427681 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-04+08:00&etm=2025-06-04+18:00&filterCnt=6&_timer304=1749029427735 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:28 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1749029427735 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:28 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1749029427683 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-04+08:00&etm=2025-06-04+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1749029427790 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1749029427735 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [04/Jun/2025:17:30:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 8946
************ - - [04/Jun/2025:17:30:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [04/Jun/2025:17:30:33 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:30:33 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:30:33 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [04/Jun/2025:17:30:33 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-01+17:30:27&etm=&_timer304=1749029427735 HTTP/1.1" 200 156
************ - - [04/Jun/2025:17:30:33 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1749029427681 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:30:33 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1749029427683 HTTP/1.1" 200 159491
************ - - [04/Jun/2025:17:30:33 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1749029427735 HTTP/1.1" 200 166
************ - - [04/Jun/2025:17:30:33 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1749029427735 HTTP/1.1" 200 13016
************ - - [04/Jun/2025:17:30:33 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-04+08:00&etm=2025-06-04+18:00&filterCnt=6&_timer304=1749029427735 HTTP/1.1" 200 164
************ - - [04/Jun/2025:17:30:33 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1749029427735 HTTP/1.1" 200 169
************ - - [04/Jun/2025:17:30:34 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [04/Jun/2025:17:30:34 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [04/Jun/2025:17:30:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [04/Jun/2025:17:30:34 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1749029434863 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:34 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029434864 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:34 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1749029434863 HTTP/1.1" 200 155
************ - - [04/Jun/2025:17:30:34 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029434864 HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:30:34 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-04+08:00&etm=2025-06-04+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1749029427790 HTTP/1.1" 200 440836
************ - - [04/Jun/2025:17:30:35 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:30:35 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:30:35 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029435662 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:35 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [04/Jun/2025:17:30:35 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1749029435662 HTTP/1.1" 200 232
************ - - [04/Jun/2025:17:30:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1377980
************ - - [04/Jun/2025:17:30:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1749029437526 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1749029437526 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:30:40 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1377980
************ - - [04/Jun/2025:17:30:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1358500
************ - - [04/Jun/2025:17:30:44 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1358500
************ - - [04/Jun/2025:17:30:52 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1377980
************ - - [04/Jun/2025:17:30:59 +0800] "OPTIONS /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029459667 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:59 +0800] "OPTIONS /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029459667 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:30:59 +0800] "GET /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029459667 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:30:59 +0800] "GET /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029459667 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:31:02 +0800] "OPTIONS /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029462549 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:02 +0800] "OPTIONS /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029462549 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:02 +0800] "GET /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029462549 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:31:02 +0800] "GET /api/syq/rsvr/get-objOnlyIACAdinfo-by-stcd?stcd=10812520&_timer304=1749029462549 HTTP/1.1" 200 161
************ - - [04/Jun/2025:17:31:04 +0800] "OPTIONS /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029464832 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:04 +0800] "GET /api/dzsj/rvrsvr/get-info-by-cd-tp?objcd=10812520&objtp=6&_timer304=1749029464832 HTTP/1.1" 200 208
************ - - [04/Jun/2025:17:31:04 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029464902 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:05 +0800] "GET /api/xxjh/plan/get-plan_by_rescode?resCode=220524024000019&_timer304=1749029464902 HTTP/1.1" 200 1264
************ - - [04/Jun/2025:17:31:05 +0800] "POST /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 626476
************ - - [04/Jun/2025:17:31:05 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:31:05 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1176
************ - - [04/Jun/2025:17:31:05 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:35&etm=2025-06-04+17:35&_timer304=1749029465252 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:05 +0800] "OPTIONS /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:35&etm=2025-06-04+17:35&_timer304=1749029465252 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:05 +0800] "OPTIONS /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:35&_timer304=1749029465252 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:05 +0800] "POST /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 245
************ - - [04/Jun/2025:17:31:05 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029465264 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:05 +0800] "GET /api/syq/rsvr/select-streglat-stcd?stcd=10812520&ymdh=2025-06-04+17:35&_timer304=1749029465252 HTTP/1.1" 200 153
************ - - [04/Jun/2025:17:31:05 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10812520&_timer304=1749029465264 HTTP/1.1" 200 791
************ - - [04/Jun/2025:17:31:05 +0800] "OPTIONS /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029465321 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:05 +0800] "GET /api/hsfxt/rsvr/get-fxt-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029465321 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:31:05 +0800] "OPTIONS /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029465346 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:05 +0800] "GET /api/hsfxt/rsvr/get-fzq-rsvr-list-by-stcd?stcd=10812520&_timer304=1749029465346 HTTP/1.1" 200 160
************ - - [04/Jun/2025:17:31:05 +0800] "OPTIONS /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029465359 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:05 +0800] "GET /api/syq/video/select-video-info-by-code?code=10812520&_timer304=1749029465359 HTTP/1.1" 200 1055
************ - - [04/Jun/2025:17:31:05 +0800] "GET /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10812520&stm=2025-06-01+17:35&etm=2025-06-04+17:35&_timer304=1749029465252 HTTP/1.1" 200 158
************ - - [04/Jun/2025:17:31:05 +0800] "GET /api/syq/rsvr/select-tm-list?stcd=10812520&stm=2025-06-01+17:35&etm=2025-06-04+17:35&_timer304=1749029465252 HTTP/1.1" 200 425
************ - - [04/Jun/2025:17:31:05 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029465505 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:05 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10812520&_timer304=1749029465505 HTTP/1.1" 200 649
************ - - [04/Jun/2025:17:31:09 +0800] "OPTIONS /api/syq/rsvr/export-tm-list?stcd=10812520&stm=2025-06-01+17:35&etm=2025-06-04+17:35&_timer304=1749029469984 HTTP/1.1" 200 -
************ - - [04/Jun/2025:17:31:15 +0800] "GET /api/syq/rsvr/export-tm-list?stcd=10812520&stm=2025-06-01+17:35&etm=2025-06-04+17:35&_timer304=1749029469984 HTTP/1.1" 200 3659
