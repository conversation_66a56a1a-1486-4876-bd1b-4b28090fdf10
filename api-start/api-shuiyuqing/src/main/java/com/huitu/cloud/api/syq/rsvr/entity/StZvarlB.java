package com.huitu.cloud.api.syq.rsvr.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 库(湖)容曲线表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-10
 */
@TableName("ST_ZVARL_B")
@ApiModel(value="StZvarlB对象", description="库(湖)容曲线表")
public class StZvarlB extends Model<StZvarlB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "施测时间")
    @TableField("MSTM")
    private String mstm;

    @ApiModelProperty(value = "点序号")
    @TableField("PTNO")
    private Integer ptno;

    @ApiModelProperty(value = "库水位")
    @TableField("RZ")
    private BigDecimal rz;

    @ApiModelProperty(value = "蓄水量")
    @TableField("W")
    private BigDecimal w;

    @ApiModelProperty(value = "水面面积")
    @TableField("WSFA")
    private Integer wsfa;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getMstm() {
        return mstm;
    }

    public void setMstm(String mstm) {
        this.mstm = mstm;
    }

    public Integer getPtno() {
        return ptno;
    }

    public void setPtno(Integer ptno) {
        this.ptno = ptno;
    }

    public BigDecimal getRz() {
        return rz;
    }

    public void setRz(BigDecimal rz) {
        this.rz = rz;
    }

    public BigDecimal getW() {
        return w;
    }

    public void setW(BigDecimal w) {
        this.w = w;
    }

    public Integer getWsfa() {
        return wsfa;
    }

    public void setWsfa(Integer wsfa) {
        this.wsfa = wsfa;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "StZvarlB{" +
        "stcd=" + stcd +
        ", mstm=" + mstm +
        ", ptno=" + ptno +
        ", rz=" + rz +
        ", w=" + w +
        ", wsfa=" + wsfa +
        ", moditime=" + moditime +
        "}";
    }
}
