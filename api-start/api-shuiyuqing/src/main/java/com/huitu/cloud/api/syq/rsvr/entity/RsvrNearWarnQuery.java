package com.huitu.cloud.api.syq.rsvr.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@ApiModel(value = "临界告警查询条件")
public class RsvrNearWarnQuery {

    @ApiModelProperty(value = "行政区划代码（15位）", required = true)
    @NotBlank(message = "行政区划代码不能为空")
    @Size(min = 15, max = 15, message = "行政区划代码的长度应为15")
    private String adcd;

    @ApiModelProperty(value = "开始时间（必填，包含此时间）", required = true)
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间（选填，不包含此时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public Map<String, Object> toQueryParam() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("stm", getStm());
        if (null != getEtm()) {
            if (stm.getTime() >= etm.getTime()) {
                throw new IllegalArgumentException("结束时间应大于开始时间");
            }
            params.put("etm", getEtm());
        }
        return params;
    }

}
