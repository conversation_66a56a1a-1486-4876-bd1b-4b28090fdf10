package com.huitu.cloud.api.fusion.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 山洪雨量升级预警基本信息响应对象
 */
@Data
@ApiModel(value = "山洪雨量升级预警基本信息响应对象")
public class FusionSnapshotIndexResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARN_ID")
    private String warnId;

    @ApiModelProperty(value = "预警名称")
    @TableField(value = "WARN_NAME")
    private String warnName;

    @ApiModelProperty(value = "预警政区")
    @TableField(exist = false)
    private String warnAdnm;

    @ApiModelProperty(value = "预警模式  注：0=实时、1=实时+预报；人工预警时，该字段为空")
    @TableField(value = "WARN_MODEL")
    private String warnModel;

    @ApiModelProperty(value = "预警时间")
    @TableField(value = "WARN_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warnTime;

    @ApiModelProperty(value = "关闭时间")
    @TableField(exist = false)
    private String closeTime;

    @ApiModelProperty(value = "预警方式  注：0=自动、1=人工")
    @TableField(value = "WARN_MODE")
    private String warnMode;

    @ApiModelProperty(value = "预警描述")
    @TableField(value = "WARN_DESC")
    private String warnDesc;

    @ApiModelProperty(value = "政区名称")
    @TableField(exist = false)
    private String adnm;

    @ApiModelProperty(value = "重现期，单位：年")
    @TableField(exist = false)
    private BigDecimal reprd;

    @ApiModelProperty(value = "实时雨量阈值")
    @TableField(exist = false)
    private BigDecimal rdrpt;

    @ApiModelProperty(value = "实时阈值历时")
    @TableField(exist = false)
    private BigDecimal stdt;

    @ApiModelProperty(value = "预报雨量阈值")
    @TableField(exist = false)
    private BigDecimal fdrpt;

    @ApiModelProperty(value = "预报阈值历时")
    @TableField(exist = false)
    private BigDecimal fcdt;

    @ApiModelProperty(value = "测站名称")
    @TableField(exist = false)
    private String stnm;

    @ApiModelProperty(value = "测站编码")
    @TableField(exist = false)
    private String stcd;

    @ApiModelProperty(value = "实时累计降水量")
    @TableField(exist = false)
    private BigDecimal raccp;

    @ApiModelProperty(value = "预报累计降水量")
    @TableField(exist = false)
    private BigDecimal faccp;

}
