package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
@ApiModel(value ="TmsRainVo" ,description="场次测站累计雨量列表信息")
public class TmsRainVo implements Serializable {
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "流域")
    private String bsnm;
    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;
    @ApiModelProperty(value = "测站地址")
    private String stlc;

    @ApiModelProperty(value = "测站政区编码")
    private String adcd;

    @ApiModelProperty(value = "累计雨量")
    private String drps;

    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "区县名称")
    private String xadnm;
    @ApiModelProperty(value = "测站归属类别名称")
    private String stadtpnm;

    @ApiModelProperty(value = "降雨历时")
    private Integer intv;
    @ApiModelProperty(value = "偏移经度")
    private BigDecimal plgtd;

    @ApiModelProperty(value = "偏移纬度")
    private BigDecimal plttd;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getDrps() {
        return drps;
    }

    public void setDrps(String drps) {
        this.drps = drps;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public Integer getIntv() {
        return intv;
    }

    public void setIntv(Integer intv) {
        this.intv = intv;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }
}
