package com.huitu.cloud.api.syq.skdt.controler;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.syq.rsvr.entity.StRsvrTmVo;
import com.huitu.cloud.api.syq.skdt.entity.*;
import com.huitu.cloud.api.syq.skdt.service.SkdtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 水库调度 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-09
 */
@RestController
@Api(tags = "水库调度信息")
@RequestMapping("/api/syq/skdt")
public class SkdtResource  extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "AB0F5E3D-E4BA-474F-B4D4-910D0EE8ACD8";
    }
    @Override
    public String getVersion() {
        return "1.0";
    }
    @Autowired
    private SkdtService skdtService;
    @ApiOperation(value = "查询水库调度涉及水库最新水库水情",notes="查询水库调度涉及水库最新水库水情")
    @GetMapping(value = "select-latest")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<BsnSkdtRvEx>>> getSkdtRsvrLatestInfo(@RequestParam String  stm, @RequestParam String  etm) throws Exception {
        List<BsnSkdtRvEx> list=skdtService.getSkdtRsvrLatestInfo(stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "查询水库调度涉及水库的库区面平均降雨",notes="查询水库调度涉及水库的库区面平均降雨")
    @GetMapping(value = "select-rsvr-rain")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<BsnSkdtRvEx>>> getSkdtRsvraRainInfo(@RequestParam String  stm, @RequestParam String  etm) throws Exception {
        List<RsvrAvgRain> list=skdtService.getSkdtRsvrRainInfo(stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "查询水库工程编码的库区时段面平均降雨列表",notes="查询水库工程编码的库区时段面平均降雨列表")
    @GetMapping(value = "select-rsvr-rain-tm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resCode", value = "水库工程编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrTmAvgRain>>> getSkdtRsvraRainTmInfo(@RequestParam String  resCode, @RequestParam String  stm, @RequestParam String  etm) throws Exception {
        List<RsvrTmAvgRain> list=skdtService.getSkdtRsvrRainTmInfo(resCode,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "根据水库编码查询上下游河道水情信息",notes="根据水库编码查询上下游河道水情信息")
    @GetMapping(value = "select-river-by-sk")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "水库测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrRiverInfo>>> getRiverInfoBySkStcd(@RequestParam String  stcd,@RequestParam String  stm, @RequestParam String  etm) throws Exception {
        List<RsvrRiverInfo> list=skdtService.getSkdtRsvrRiverInfo(stcd,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "根据水库编码查询水库单站综合信息",notes="根据水库编码查询水库单站综合信息")
    @GetMapping(value = "select-skinfo-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "水库测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<SkInfo>> getInfoBySkStcd(@RequestParam String  stcd,@RequestParam String  stm, @RequestParam String  etm) throws Exception {
        SkInfo skInfo=skdtService.getInfoBySkStcd(stcd,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", skInfo));
    }

    @ApiOperation(value = "根据水库编码查询关联的雨量站信息",notes="根据水库编码查询关联的雨量站信息")
    @GetMapping(value = "select-rain-by-sk")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resCode", value = "水库工程编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrRain>>> getRainInfoBySkStcd(@RequestParam String  resCode,@RequestParam String  stm, @RequestParam String  etm) throws Exception {
        List<RsvrRain> list=skdtService.getSkdtRsvrRainInfo(resCode,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
}
