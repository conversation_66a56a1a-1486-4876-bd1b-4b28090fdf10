package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class RainByDutyRecort {
    @ApiModelProperty(value = "省平均降雨")
    private String provinceAvgRain;
    @ApiModelProperty(value = "市平均降雨")
    private List<AdAvgRain> cityAvgRain;
    @ApiModelProperty(value = "当前政区平均降雨")
    private String avgRain;
    @ApiModelProperty(value = "降雨量站")
    private List<Rain> rainList;
    @ApiModelProperty(value = "降雨站总数")
    private Integer  rainCount;
    @ApiModelProperty(value = "水文最大降雨站")
    private Rain swMaxRain;
    @ApiModelProperty(value = "山洪最大降雨站")
    private Rain shMaxRain;
    @ApiModelProperty(value = "气象最大降雨站")
    private Rain qxMaxRain;

    public String getAvgRain() {
        return avgRain;
    }

    public void setAvgRain(String avgRain) {
        this.avgRain = avgRain;
    }

    public Integer getRainCount() {
        return rainCount;
    }

    public void setRainCount(Integer rainCount) {
        this.rainCount = rainCount;
    }

    public List<Rain> getRainList() {
        return rainList;
    }

    public void setRainList(List<Rain> rainList) {
        this.rainList = rainList;
    }

    public String getProvinceAvgRain() {
        return provinceAvgRain;
    }

    public void setProvinceAvgRain(String provinceAvgRain) {
        this.provinceAvgRain = provinceAvgRain;
    }

    public List<AdAvgRain> getCityAvgRain() {
        return cityAvgRain;
    }

    public void setCityAvgRain(List<AdAvgRain> cityAvgRain) {
        this.cityAvgRain = cityAvgRain;
    }

    public Rain getSwMaxRain() {
        return swMaxRain;
    }

    public void setSwMaxRain(Rain swMaxRain) {
        this.swMaxRain = swMaxRain;
    }

    public Rain getShMaxRain() {
        return shMaxRain;
    }

    public void setShMaxRain(Rain shMaxRain) {
        this.shMaxRain = shMaxRain;
    }

    public Rain getQxMaxRain() {
        return qxMaxRain;
    }

    public void setQxMaxRain(Rain qxMaxRain) {
        this.qxMaxRain = qxMaxRain;
    }
}
