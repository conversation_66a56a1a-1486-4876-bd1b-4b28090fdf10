package com.huitu.cloud.api.screen.ly.service.impl;

import com.huitu.cloud.api.screen.fhjz.Constants;
import com.huitu.cloud.api.screen.fhjz.mapper.RainScreenDao;
import com.huitu.cloud.api.screen.fhjz.utils.DateTimeUtils;
import com.huitu.cloud.api.screen.ly.mapper.LyDao;
import com.huitu.cloud.api.screen.ly.service.LyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 流域信息获取 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Service
public class LyServiceImpl implements LyService {

    private static final Logger logger = LoggerFactory.getLogger(LyServiceImpl.class);
    @Autowired
    private LyDao lyDao;

    /**
     * 查询河流概况
     *
     * @param rvCode
     * @return
     */
    @Override
    public Map queryRvBaseFact(String rvCode) {
        Map result =lyDao.queryRvBaseFact(rvCode);
        return result;
    }

    /**
     * 查询河流分支
     *
     * @param rvCode
     * @param rvName
     * @return
     */
    @Override
    public List<Map<String, Object>> queryRvBranch(String rvCode, String rvName) {
        List<Map<String, Object>> result =lyDao.queryRvBranch(rvCode);
        return result;
    }

    /**
     * 根据河流编码查询水库的数量
     *
     * @param rvCode
     * @return
     */
    @Override
    public Map<String, Object> querySkCountByRv(String rvCode) {
        return lyDao.querySkCountByRv(rvCode);
    }

    @Override
    public Map<String, Object> querySkFhByRv(String rvCode,String engScal) {
        return lyDao.querySkFhByRv(rvCode,engScal);
    }

    @Override
    public List<Map<String, Object>> statisticSkFhByRv(String rvCode,String engScal) {
        return lyDao.statisticSkFhByRv(rvCode,engScal);
    }

    @Override
    public Map<String, Object> queryDikeLenByRv(String rvCode) {
        return lyDao.queryDikeLenByRv(rvCode);
    }

    @Override
    public List<Map<String, Object>> queryDikeStatisticByRv(String rvCode) {
        return lyDao.queryDikeStatisticByRv(rvCode);
    }

    @Override
    public List<Map<String, Object>> queryDikeListByRv(String rvCode) {
        return lyDao.queryDikeListByRv(rvCode);
    }

    @Override
    public Map<String, Object> queryHdCountByRv(String rvCode) {
        return lyDao.queryHdCountByRv(rvCode);
    }

    @Override
    public Map<String, Object> queryHdWarnCountByRv(String rvCode) {
        Map<String, Object> result = null;
        try{
            List<String> queryRealTimes = DateTimeUtils.queryRainTime(Constants.RAIN_REAL_TYPE,72);
            result = lyDao.queryHdWarnCountByRv(rvCode,queryRealTimes.get(0),queryRealTimes.get(1));
            return result;
        }catch (Exception ex){
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> queryHdStatisticByRv(String rvCode) {
        List<Map<String, Object>> result = null;
        try{
            List<String> queryRealTimes = DateTimeUtils.queryRainTime(Constants.RAIN_REAL_TYPE,72);
            result = lyDao.queryHdStatisticByRv(rvCode,queryRealTimes.get(0),queryRealTimes.get(1));
            return result;
        }catch (Exception ex){
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
        }
        return result;
    }

    @Override
    public Map<String, Object> queryRainCountByRv(String rvCode) {
        return lyDao.queryRainCountByRv(rvCode);
    }

    @Override
    public List<Map<String, Object>> queryRainStatisticByRv(String rvCode) {
        List<Map<String, Object>> result = null;
        try{
            List<String> queryRealTimes =  DateTimeUtils.queryRainTimeBy8(Constants.RAIN_REAL_TYPE,24);
            result = lyDao.queryRainStatisticByRv(rvCode,queryRealTimes.get(0),queryRealTimes.get(1));
            return result;
        }catch (Exception ex){
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
        }
        return result;
    }

    @Override
    public Map<String, Object> querySkStationCountByRv(String rvCode, String engScal) {
        return lyDao.querySkStationCountByRv(rvCode,engScal);
    }

    @Override
    public Map<String, Object> querySkStationCxxCountByRv(String rvCode, String engScal) {
        List<String> queryTimes = DateTimeUtils.queryReservoirTime();
        return lyDao.querySkStationCxxCountByRv(rvCode,engScal,queryTimes.get(0),queryTimes.get(1));
    }

    @Override
    public List<Map<String, Object>> querySkStationStatisticByRv(String rvCode, String engScal) {
        List<String> queryTimes = DateTimeUtils.queryReservoirTime();
        return lyDao.querySkStationStatisticByRv(rvCode,engScal,queryTimes.get(0),queryTimes.get(1));
    }

    @Override
    public List<Map<String, Object>> queryFhCityListByRv(String rvCode) {
        return lyDao.queryFhCityListByRv(rvCode);
    }

    @Override
    public Map<String, Object> queryRv(String rvCode) {
        return lyDao.queryRv(rvCode);
    }

    @Override
    public Map<String, Object> queryRvSourLoc(String rvCode) {
        return lyDao.queryRvSourLoc(rvCode);
    }

}
