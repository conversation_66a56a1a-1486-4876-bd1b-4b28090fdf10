package com.huitu.cloud.api.datamanager.manualreport.mapper;

import com.huitu.cloud.api.datamanager.manualreport.entity.BsnManualReport;
import com.huitu.cloud.api.datamanager.manualreport.entity.ManualReportVo;
import com.huitu.cloud.api.datamanager.manualreport.entity.QueryManualReport;
import com.huitu.cloud.api.datamanager.ststop.entity.BsnStStopTab;
import com.huitu.cloud.api.datamanager.ststop.entity.BsnStstopr;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人工报讯 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-23
 */
public interface ManualReportDao {
    /**
     * 人工报讯站点记录查询
     *
     * @param param 查询参数
     * @return
     */
    List<ManualReportVo> getManualReportStInfo(Map<String, Object> param);

    /**
     * 人工报讯配置表新增
     * @param bsnManualReport
     * @return
     */
    void insertBsnManualReport(BsnManualReport bsnManualReport);
    /**
     * 人工报讯配置关闭
     * @param stcd
     * @return
     */
    void close(@Param("stcd") String stcd,@Param("tbnm") String tbnm);

}
