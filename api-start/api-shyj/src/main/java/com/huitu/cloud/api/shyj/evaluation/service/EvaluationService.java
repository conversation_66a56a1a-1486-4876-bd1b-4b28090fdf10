package com.huitu.cloud.api.shyj.evaluation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.shyj.evaluation.entity.*;

import java.util.List;


/**
 * <p>
 * 调查成果  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public interface EvaluationService {
    /**
     * 分页查询无线广播站信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param wscd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<Wbrinfo> getWbrinfoList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize);

    /**
     * 查询无线广播站工况信息
     *
     * @param racd 广播站编码
     * @return
     */
    BsnWirelessinfo getWirelessInfo(String racd);

    /**
     * 查询无线广播站日志信息
     *
     * @param racd     广播站编码
     * @param stm      开始时间
     * @param etm      结束时间
     * @param type     类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<BsnWirelessinfoLog> getWirelessLogInfo(String racd, String stm, String etm, String type, int pageNum, int pageSize);

    /**
     * 分页查询危险区信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param wscd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaDanad> getDanList(String adcd, String key, String wscd, String wsnm, String name, String adnm, String prevtp, int pageNum, int pageSize);

    /**
     * 分页查询历史灾害信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param wscd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaHsfwater> getHsfwaterList(String address, String adcd, String key, String wscd, String wsnm, String prevtp, int pageNum, int pageSize);

    /**
     * 分页查询需防洪治理山洪沟信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param name     对象名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaGully> getGullyList(String adcd, String key, String name, String adnm, int pageNum, int pageSize);

    /**
     * 分页查询塘（堰）坝工程调查成果信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param wscd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaDaminfo> getDamList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize);

    /**
     * 分页查询重要沿河村落居民户列表信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param wscd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaFlrvvlg> getFlrvvlgList(String adcd, String key, String wscd, String name, String wsnm, String prevtp, int pageNum, int pageSize);

    /**
     * 分页查询简易雨量站列表信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param wscd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaSrstinfo> getSrstList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize);

    /**
     * 分页查询简易水位站列表信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param wscd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaSwstinfo> getSwstList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize);

    /**
     * 分页查询防治区企事业单位列表信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param wscd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaBsnssinfo> getBsnsstList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize);

    /**
     * 分页查询重要城（集）镇居民列表信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param wscd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaDtresident> getDtresidentList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize);

    /**
     * 分页查询历史洪痕列表信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaHsfm> getIaHsfmList(String adcd, String key, int pageNum, int pageSize);

    /**
     * 分页查询沟道横断面列表信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaHsurface> getHsurfaceList(String adcd, String key, int pageNum, int pageSize);

    /**
     * 分页查询沟道纵断面列表信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaVsurface> getVsurfaceList(String adcd, String key, int pageNum, int pageSize);

    /**
     * 分页查询防治区列表信息
     *
     * @param adcd     政区编码
     * @param key      对象id
     * @param adnm     政区名称
     * @param prevtp   防治区类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaAdinfo> getPrevadList(String adcd, String key, String adnm, String prevtp, int pageNum, int pageSize);

    /**
     * 查询政区概况统计信息
     *
     * @param adcd 政区编码
     * @return
     */
    List<DtAdInfo> getDtAdInfoList(String adcd);

    /**
     * 查询调查成果统计信息
     *
     * @param adcd 政区编码
     * @return
     */
    List<DtSurvey> getDtDccgInfoList(String adcd);

    /**
     * 查询涉水工程统计信息
     *
     * @param adcd 政区编码
     * @return
     */
    List<DtWading> getDtWadingInfoList(String adcd);

    /**
     * 查询分析评价对象统计信息
     *
     * @param adcd 政区编码
     * @return
     */
    List<DtAnalysis> getDtFxpjInfoList(String adcd);

    /**
     * 调查评价汇总统计
     *
     * @param adcd 政区编码
     * @return
     */
    DtAllEvaluationInfo getDtEvaluationInfo(String adcd);

    /**
     * 危险区本级信息统计
     *
     * @param adcd 政区编码
     * @return
     */
    DtIaDanad getOwnDtDanInfo(String adcd);

    /**
     * 危险区下级信息统计
     *
     * @param adcd 政区编码
     * @return
     */
    List<DtIaDanad> getLowerDtDanInfo(String adcd);

    /**
     * 危险区信息统计树
     *
     * @param adcd 政区编码
     * @return
     */
    DtIaDanad getDtDanInfoTree(String adcd);

    /**
     * 历史山洪灾害情况下级信息统计
     *
     * @param adcd 政区编码
     * @return
     */
    List<DtIaHsfwater> getLowerDtHsfwaterInfo(String adcd);

    /**
     * 历史山洪灾害情况信息统计树
     *
     * @param adcd 政区编码
     * @return
     */
    DtIaHsfwater getDtHsfwaterInfoTree(String adcd);

    /**
     * 需防洪治理山洪沟下级信息统计
     *
     * @param adcd 政区编码
     * @return
     */
    List<DtIaGully> getLowerDtIaGullyInfo(String adcd);

    /**
     * 需防洪治理山洪沟政区树
     *
     * @param adcd 政区编码
     * @return
     */
    DtIaGully getDtGullyInfoTree(String adcd);

    /**
     * 查询导出防洪治理山洪沟政区树信息
     *
     * @param adcd 政区编码
     * @return
     */
    DtIaGully getDtGullyInfoExport(String adcd);

    /**
     * 社会经济情况本级信息统计
     *
     * @param adcd 政区编码
     * @return
     */
    DtIaVlgestat getOwnDtVlgestatInfo(String adcd);

    /**
     * 社会经济情况下级信息统计
     *
     * @param adcd 政区编码
     * @return
     */
    List<DtIaVlgestat> getLowerDtVlgestatInfo(String adcd);

    /**
     * 社会经济情况统计树
     *
     * @param adcd 政区编码
     * @return
     */
    DtIaVlgestat getDtDanVlgestatTree(String adcd);

    /**
     * 重点区统计树
     *
     * @param query
     * @return
     */
    DtKeyAd getDtKeyAdTree(KeyAreasQuery query);

}
