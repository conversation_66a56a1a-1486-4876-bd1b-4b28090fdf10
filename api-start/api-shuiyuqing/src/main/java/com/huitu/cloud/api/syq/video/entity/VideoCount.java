package com.huitu.cloud.api.syq.video.entity;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2019-09-26
 */
public class VideoCount {
    @ApiModelProperty(value = "视频站总数")
    private int count;
    @ApiModelProperty(value = "视频头总数")
    private int countItem;
    @ApiModelProperty(value = "大型水库总数")
    private int countEleven;
    @ApiModelProperty(value = "中型水库总数")
    private int countTwelve;
    @ApiModelProperty(value = "小型水库总数")
    private int countThirteen;
    @ApiModelProperty(value = "主要江河总数")
    private int countTwo;
    @ApiModelProperty(value = "山洪沟总数")
    private int countThree;
    @ApiModelProperty(value = "防洪城市总数")
    private int countFour;
    @ApiModelProperty(value = "仓库总数")
    private int countSix;
    @ApiModelProperty(value = "中小河流总数")
    private int countFive;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getCountItem() {
        return countItem;
    }

    public void setCountItem(int countItem) {
        this.countItem = countItem;
    }

    public int getCountEleven() {
        return countEleven;
    }

    public void setCountEleven(int countEleven) {
        this.countEleven = countEleven;
    }

    public int getCountTwelve() {
        return countTwelve;
    }

    public void setCountTwelve(int countTwelve) {
        this.countTwelve = countTwelve;
    }

    public int getCountThirteen() {
        return countThirteen;
    }

    public void setCountThirteen(int countThirteen) {
        this.countThirteen = countThirteen;
    }

    public int getCountTwo() {
        return countTwo;
    }

    public void setCountTwo(int countTwo) {
        this.countTwo = countTwo;
    }

    public int getCountThree() {
        return countThree;
    }

    public void setCountThree(int countThree) {
        this.countThree = countThree;
    }

    public int getCountFour() {
        return countFour;
    }

    public void setCountFour(int countFour) {
        this.countFour = countFour;
    }

    public int getCountSix() {
        return countSix;
    }

    public void setCountSix(int countSix) {
        this.countSix = countSix;
    }

    public int getCountFive() {
        return countFive;
    }

    public void setCountFive(int countFive) {
        this.countFive = countFive;
    }
}
