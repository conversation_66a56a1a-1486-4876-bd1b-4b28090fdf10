package com.huitu.cloud.api.ewci.feedback.mapper;

import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackPic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 意见反馈信息 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface BnsFeedBackPicDao {

    /**
     * 新增图片
     *
     * @param info 参数
     * @return int 受影响的行数
     **/
    int addPicture(@Param("info") List<BnsFeedBackPic> info);

    /**+
     * 意见反馈详情查看图片
     * @param feedId
     * @return
     */
    List<BnsFeedBackPic> getFeedBackPicList(String feedId);
}
