package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 提防统计详细数据堤防
 */
@ApiModel(value = "提防统计详细数据堤防")
public class RiverDikeDetailsChildVo implements Serializable {

    @ApiModelProperty(value = "政区编码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "规划防洪标准")
    @TableField(value = "FSTAND")
    private String fstand;

    @ApiModelProperty(value = "堤防总长左岸")
    @TableField(value = "TOTAL_DIKE_LEN_L")
    private Double totalDikeLenL;

    @ApiModelProperty(value = "堤防总长右岸")
    @TableField(value = "TOTAL_DIKE_LEN_R")
    private Double totalDikeLenR;

    @ApiModelProperty(value = "堤防达标段长度左岸")
    @TableField(value = "UP_DIKE_LEN_L")
    private Double upDikeLenL;

    @ApiModelProperty(value = "堤防达标段长度右岸")
    @TableField(value = "UP_DIKE_LEN_R")
    private Double upDikeLenR;

    @ApiModelProperty(value = "堤防未达标段长度左岸")
    @TableField(value = "NOT_DIKE_LEN_L")
    private Double notDikeLenL;

    @ApiModelProperty(value = "堤防未达标段长度右岸")
    @TableField(value = "NOT_DIKE_LEN_R")
    private Double notDikeLenR;

    @ApiModelProperty(value = "长度")
    @TableField(value = "REACH_LEN")
    private Double reachLen;

    @ApiModelProperty(value = "河流编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "河流名称")
    @TableField(value = "RV_NAME")
    private String rvName;

    @ApiModelProperty(value = "流域面积")
    @TableField(value = "RV_BAS_AREA")
    private Double rvBasArea;


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getFstand() {
        return fstand;
    }

    public void setFstand(String fstand) {
        this.fstand = fstand;
    }

    public Double getTotalDikeLenL() {
        return totalDikeLenL;
    }

    public void setTotalDikeLenL(Double totalDikeLenL) {
        this.totalDikeLenL = totalDikeLenL;
    }

    public Double getTotalDikeLenR() {
        return totalDikeLenR;
    }

    public void setTotalDikeLenR(Double totalDikeLenR) {
        this.totalDikeLenR = totalDikeLenR;
    }

    public Double getUpDikeLenL() {
        return upDikeLenL;
    }

    public void setUpDikeLenL(Double upDikeLenL) {
        this.upDikeLenL = upDikeLenL;
    }

    public Double getUpDikeLenR() {
        return upDikeLenR;
    }

    public void setUpDikeLenR(Double upDikeLenR) {
        this.upDikeLenR = upDikeLenR;
    }

    public Double getNotDikeLenL() {
        return notDikeLenL;
    }

    public void setNotDikeLenL(Double notDikeLenL) {
        this.notDikeLenL = notDikeLenL;
    }

    public Double getNotDikeLenR() {
        return notDikeLenR;
    }

    public void setNotDikeLenR(Double notDikeLenR) {
        this.notDikeLenR = notDikeLenR;
    }

    public Double getReachLen() {
        return reachLen;
    }

    public void setReachLen(Double reachLen) {
        this.reachLen = reachLen;
    }

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public Double getRvBasArea() {
        return rvBasArea;
    }

    public void setRvBasArea(Double rvBasArea) {
        this.rvBasArea = rvBasArea;
    }
}
