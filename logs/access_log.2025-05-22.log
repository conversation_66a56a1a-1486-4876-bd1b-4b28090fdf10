************ - - [22/May/2025:09:56:24 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [22/May/2025:09:56:24 +0800] "GET /login HTTP/1.1" 302 -
************ - - [22/May/2025:09:56:25 +0800] "GET /login?code=iM8aaE&state=PjvNSB HTTP/1.1" 302 -
************ - - [22/May/2025:09:56:26 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [22/May/2025:09:56:27 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1747878986992 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1747878986992 HTTP/1.1" 200 508
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747878992311 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747878992311 HTTP/1.1" 200 59820
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747878992417 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747878992417 HTTP/1.1" 200 10388
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747878992436 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747878992436 HTTP/1.1" 200 2021
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+09:56:32&etm=&_timer304=1747878992661 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747878992661 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+10:00&filterCnt=6&_timer304=1747878992661 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747878992661 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747878992661 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:33 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:09:56:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:09:56:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:09:56:36 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+09:56:32&etm=&_timer304=1747878992661 HTTP/1.1" 200 156
************ - - [22/May/2025:09:56:36 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747878992661 HTTP/1.1" 200 166
************ - - [22/May/2025:09:56:36 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+10:00&filterCnt=6&_timer304=1747878992661 HTTP/1.1" 200 164
************ - - [22/May/2025:09:56:36 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747878992661 HTTP/1.1" 200 169
************ - - [22/May/2025:09:56:36 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:09:56:36 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:09:56:36 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747878992661 HTTP/1.1" 200 13016
************ - - [22/May/2025:09:56:36 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1747878996344 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:36 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1747878996344 HTTP/1.1" 200 1560
************ - - [22/May/2025:09:56:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:36 +0800] "OPTIONS /api/base/saas/token?_timer304=1747878996881 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:36 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:36 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747878996881 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747878996903 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/duty/event/listEventType HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/duty/event/select-list HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747878996978 HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286484
************ - - [22/May/2025:09:56:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:09:56:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:09:56:39 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:09:56:39 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [22/May/2025:09:56:39 +0800] "GET /api/base/saas/token?_timer304=1747878996881 HTTP/1.1" 200 411
************ - - [22/May/2025:09:56:39 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747878996881 HTTP/1.1" 200 359
************ - - [22/May/2025:09:56:40 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [22/May/2025:09:56:40 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [22/May/2025:09:56:40 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [22/May/2025:09:56:40 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747878996903 HTTP/1.1" 200 2021
************ - - [22/May/2025:09:56:40 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [22/May/2025:09:56:40 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 321
************ - - [22/May/2025:09:56:40 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [22/May/2025:09:56:40 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [22/May/2025:09:56:40 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [22/May/2025:09:56:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747878996978 HTTP/1.1" 200 160
************ - - [22/May/2025:09:56:40 +0800] "POST /api/duty/event/select-list HTTP/1.1" 200 111
************ - - [22/May/2025:09:56:40 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [22/May/2025:09:56:40 +0800] "POST /api/duty/event/listEventType HTTP/1.1" 200 231
************ - - [22/May/2025:09:56:40 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:09:56:40 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:09:57:37 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9529/ HTTP/1.1" 302 -
************ - - [22/May/2025:09:57:38 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1747879058817 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:38 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1747879058817 HTTP/1.1" 200 508
************ - - [22/May/2025:09:57:38 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747879058906 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:38 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747879058906 HTTP/1.1" 200 59820
************ - - [22/May/2025:09:57:39 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747879059115 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:39 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747879059115 HTTP/1.1" 200 10388
************ - - [22/May/2025:09:57:39 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879059812 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:39 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879059812 HTTP/1.1" 200 2021
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+09:57:40&etm=&_timer304=1747879060047 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879060047 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+10:00&filterCnt=6&_timer304=1747879060047 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879060047 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879060047 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:09:57:40 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:09:57:40 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+09:57:40&etm=&_timer304=1747879060047 HTTP/1.1" 200 156
************ - - [22/May/2025:09:57:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:09:57:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879060047 HTTP/1.1" 200 166
************ - - [22/May/2025:09:57:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:09:57:40 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:09:57:40 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+10:00&filterCnt=6&_timer304=1747879060047 HTTP/1.1" 200 164
************ - - [22/May/2025:09:57:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879060047 HTTP/1.1" 200 169
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1747879060234 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879060047 HTTP/1.1" 200 13016
************ - - [22/May/2025:09:57:40 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1747879060234 HTTP/1.1" 200 1560
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/base/saas/token?_timer304=1747879060659 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747879060659 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879060673 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/duty/event/listEventType HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "OPTIONS /api/duty/event/select-list HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:40 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [22/May/2025:09:57:40 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:09:57:40 +0800] "GET /api/base/saas/token?_timer304=1747879060659 HTTP/1.1" 200 411
************ - - [22/May/2025:09:57:40 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [22/May/2025:09:57:40 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286484
************ - - [22/May/2025:09:57:41 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:41 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 321
************ - - [22/May/2025:09:57:41 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747879060659 HTTP/1.1" 200 359
************ - - [22/May/2025:09:57:41 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [22/May/2025:09:57:41 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879060673 HTTP/1.1" 200 2021
************ - - [22/May/2025:09:57:41 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:09:57:41 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [22/May/2025:09:57:41 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [22/May/2025:09:57:41 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [22/May/2025:09:57:41 +0800] "POST /api/duty/event/listEventType HTTP/1.1" 200 231
************ - - [22/May/2025:09:57:41 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:09:57:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:09:57:41 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [22/May/2025:09:57:41 +0800] "POST /api/duty/event/select-list HTTP/1.1" 200 111
************ - - [22/May/2025:09:57:41 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [22/May/2025:09:57:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:09:57:48 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879068507 HTTP/1.1" 200 -
************ - - [22/May/2025:09:57:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879068507 HTTP/1.1" 200 160
************ - - [22/May/2025:09:58:43 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9529/ HTTP/1.1" 302 -
************ - - [22/May/2025:09:58:44 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1747879124957 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:44 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1747879124957 HTTP/1.1" 200 508
************ - - [22/May/2025:09:58:45 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747879125757 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:45 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747879125757 HTTP/1.1" 200 59820
************ - - [22/May/2025:09:58:45 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747879125911 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:45 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747879125911 HTTP/1.1" 200 10388
************ - - [22/May/2025:09:58:45 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879125985 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:45 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879125985 HTTP/1.1" 200 2021
************ - - [22/May/2025:09:58:46 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+09:58:46&etm=&_timer304=1747879126193 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:46 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879126193 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:46 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+10:00&filterCnt=6&_timer304=1747879126193 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:46 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879126193 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:46 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879126193 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:46 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:09:58:46 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:09:58:46 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+10:00&filterCnt=6&_timer304=1747879126193 HTTP/1.1" 200 164
************ - - [22/May/2025:09:58:46 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+09:58:46&etm=&_timer304=1747879126193 HTTP/1.1" 200 156
************ - - [22/May/2025:09:58:46 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:09:58:46 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:09:58:46 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:09:58:46 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879126193 HTTP/1.1" 200 166
************ - - [22/May/2025:09:58:46 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879126193 HTTP/1.1" 200 169
************ - - [22/May/2025:09:58:46 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1747879126382 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:46 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879126193 HTTP/1.1" 200 13016
************ - - [22/May/2025:09:58:46 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1747879126382 HTTP/1.1" 200 1560
************ - - [22/May/2025:09:58:47 +0800] "OPTIONS /api/base/saas/token?_timer304=1747879127041 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:47 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747879127041 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:47 +0800] "GET /api/base/saas/token?_timer304=1747879127041 HTTP/1.1" 200 411
************ - - [22/May/2025:09:58:47 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879127056 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:47 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747879127041 HTTP/1.1" 200 359
************ - - [22/May/2025:09:58:47 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286484
************ - - [22/May/2025:09:58:47 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 321
************ - - [22/May/2025:09:58:47 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:09:58:47 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:09:58:47 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [22/May/2025:09:58:47 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:09:58:47 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [22/May/2025:09:58:47 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [22/May/2025:09:58:47 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [22/May/2025:09:58:47 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [22/May/2025:09:58:47 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [22/May/2025:09:58:47 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:09:58:47 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [22/May/2025:09:58:47 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879127056 HTTP/1.1" 200 2021
************ - - [22/May/2025:09:58:47 +0800] "POST /api/duty/event/listEventType HTTP/1.1" 200 231
************ - - [22/May/2025:09:58:47 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:09:58:47 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [22/May/2025:09:58:47 +0800] "POST /api/duty/event/select-list HTTP/1.1" 200 111
************ - - [22/May/2025:09:58:53 +0800] "OPTIONS /api/ewci/base/mal/write/939?_timer304=1747879133444 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:53 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+09:58:53&etm=&_timer304=1747879133585 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:53 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:53 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879133585 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+10:00&filterCnt=6&_timer304=1747879133585 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:53 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879133585 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879133585 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:53 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:09:58:53 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:09:58:53 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:09:58:53 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:09:58:53 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+09:58:53&etm=&_timer304=1747879133585 HTTP/1.1" 200 156
************ - - [22/May/2025:09:58:53 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+10:00&filterCnt=6&_timer304=1747879133585 HTTP/1.1" 200 164
************ - - [22/May/2025:09:58:53 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879133585 HTTP/1.1" 200 166
************ - - [22/May/2025:09:58:53 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879133585 HTTP/1.1" 200 169
************ - - [22/May/2025:09:58:53 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879133585 HTTP/1.1" 200 13016
************ - - [22/May/2025:09:58:53 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:09:58:53 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 2313
************ - - [22/May/2025:09:58:54 +0800] "GET /api/ewci/base/mal/write/939?_timer304=1747879133444 HTTP/1.1" 200 146
************ - - [22/May/2025:09:58:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879134642 HTTP/1.1" 200 -
************ - - [22/May/2025:09:58:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879134642 HTTP/1.1" 200 160
************ - - [22/May/2025:10:00:02 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1747879202451 HTTP/1.1" 200 -
************ - - [22/May/2025:10:00:02 +0800] "OPTIONS /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:10:00:02 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1747879202451 HTTP/1.1" 200 12285
************ - - [22/May/2025:10:00:02 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 479620
************ - - [22/May/2025:10:00:06 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 186504
************ - - [22/May/2025:10:01:27 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879287319 HTTP/1.1" 200 -
************ - - [22/May/2025:10:01:27 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879287319 HTTP/1.1" 200 160
************ - - [22/May/2025:10:02:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879359315 HTTP/1.1" 200 -
************ - - [22/May/2025:10:02:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879359315 HTTP/1.1" 200 160
************ - - [22/May/2025:10:02:41 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 186504
************ - - [22/May/2025:10:02:42 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 8206
************ - - [22/May/2025:10:02:43 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:02:43&etm=&_timer304=1747879363711 HTTP/1.1" 200 -
************ - - [22/May/2025:10:02:43 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879363711 HTTP/1.1" 200 -
************ - - [22/May/2025:10:02:43 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879363711 HTTP/1.1" 200 -
************ - - [22/May/2025:10:02:43 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879363711 HTTP/1.1" 200 -
************ - - [22/May/2025:10:02:43 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879363711 HTTP/1.1" 200 -
************ - - [22/May/2025:10:02:43 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:10:02:43 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:02:43 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:10:02:43 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:02:43 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:02:43&etm=&_timer304=1747879363711 HTTP/1.1" 200 156
************ - - [22/May/2025:10:02:43 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879363711 HTTP/1.1" 200 166
************ - - [22/May/2025:10:02:43 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879363711 HTTP/1.1" 200 164
************ - - [22/May/2025:10:02:43 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879363711 HTTP/1.1" 200 169
************ - - [22/May/2025:10:02:43 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:10:02:43 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 2313
************ - - [22/May/2025:10:02:43 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879363711 HTTP/1.1" 200 13016
************ - - [22/May/2025:10:02:50 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 479622
************ - - [22/May/2025:10:02:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879373592 HTTP/1.1" 200 -
************ - - [22/May/2025:10:02:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879373592 HTTP/1.1" 200 160
************ - - [22/May/2025:10:03:53 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:03:53&etm=&_timer304=1747879433882 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879433882 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879433882 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:53 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879433882 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879433882 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:53 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:10:03:53 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:10:03:53 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:03:53 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:03:53&etm=&_timer304=1747879433882 HTTP/1.1" 200 156
************ - - [22/May/2025:10:03:53 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:03:53 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879433882 HTTP/1.1" 200 166
************ - - [22/May/2025:10:03:53 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879433882 HTTP/1.1" 200 164
************ - - [22/May/2025:10:03:53 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879433882 HTTP/1.1" 200 169
************ - - [22/May/2025:10:03:53 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:10:03:53 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879433882 HTTP/1.1" 200 13016
************ - - [22/May/2025:10:03:53 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 2313
************ - - [22/May/2025:10:03:54 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:03:54&etm=&_timer304=1747879434618 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879434618 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879434618 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:54 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879434618 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879434618 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:54 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:10:03:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:10:03:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:03:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:03:54 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:03:54&etm=&_timer304=1747879434618 HTTP/1.1" 200 156
************ - - [22/May/2025:10:03:54 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879434618 HTTP/1.1" 200 164
************ - - [22/May/2025:10:03:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879434618 HTTP/1.1" 200 166
************ - - [22/May/2025:10:03:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879434618 HTTP/1.1" 200 169
************ - - [22/May/2025:10:03:54 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:10:03:54 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879434618 HTTP/1.1" 200 13016
************ - - [22/May/2025:10:03:55 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1747879435134 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:55 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1747879435134 HTTP/1.1" 200 1560
************ - - [22/May/2025:10:03:55 +0800] "OPTIONS /api/base/saas/token?_timer304=1747879435704 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:55 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747879435704 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:55 +0800] "GET /api/base/saas/token?_timer304=1747879435704 HTTP/1.1" 200 411
************ - - [22/May/2025:10:03:55 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879435726 HTTP/1.1" 200 -
************ - - [22/May/2025:10:03:55 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286484
************ - - [22/May/2025:10:03:55 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 321
************ - - [22/May/2025:10:03:56 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:10:03:56 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:10:03:56 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:10:03:56 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [22/May/2025:10:03:56 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [22/May/2025:10:03:56 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [22/May/2025:10:03:56 +0800] "POST /api/duty/event/select-list HTTP/1.1" 200 111
************ - - [22/May/2025:10:03:56 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [22/May/2025:10:03:56 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10118
************ - - [22/May/2025:10:03:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:10:03:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:10:03:57 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [22/May/2025:10:03:57 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [22/May/2025:10:03:57 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747879435704 HTTP/1.1" 200 359
************ - - [22/May/2025:10:04:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879444319 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879444319 HTTP/1.1" 200 160
************ - - [22/May/2025:10:04:08 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:04:08&etm=&_timer304=1747879448187 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879448187 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879448187 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:08 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879448187 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879448187 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:08 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:10:04:08 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:04:08 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:04:08&etm=&_timer304=1747879448187 HTTP/1.1" 200 156
************ - - [22/May/2025:10:04:08 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879448187 HTTP/1.1" 200 166
************ - - [22/May/2025:10:04:08 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:04:08 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:10:04:08 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:10:04:08 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879448187 HTTP/1.1" 200 164
************ - - [22/May/2025:10:04:08 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879448187 HTTP/1.1" 200 169
************ - - [22/May/2025:10:04:08 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1747879448278 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:08 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1747879448278 HTTP/1.1" 200 1560
************ - - [22/May/2025:10:04:08 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879448187 HTTP/1.1" 200 13016
************ - - [22/May/2025:10:04:09 +0800] "OPTIONS /api/base/saas/token?_timer304=1747879449679 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:09 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747879449679 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:09 +0800] "GET /api/base/saas/token?_timer304=1747879449679 HTTP/1.1" 200 411
************ - - [22/May/2025:10:04:09 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879449696 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:09 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747879449679 HTTP/1.1" 200 359
************ - - [22/May/2025:10:04:09 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 321
************ - - [22/May/2025:10:04:09 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286484
************ - - [22/May/2025:10:04:09 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:10:04:10 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:10:04:10 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [22/May/2025:10:04:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [22/May/2025:10:04:10 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:10:04:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [22/May/2025:10:04:10 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [22/May/2025:10:04:10 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [22/May/2025:10:04:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:10:04:10 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [22/May/2025:10:04:10 +0800] "POST /api/duty/event/listEventType HTTP/1.1" 200 231
************ - - [22/May/2025:10:04:10 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [22/May/2025:10:04:10 +0800] "POST /api/duty/event/select-list HTTP/1.1" 200 111
************ - - [22/May/2025:10:04:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:10:04:10 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747879449696 HTTP/1.1" 200 2021
************ - - [22/May/2025:10:04:10 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [22/May/2025:10:04:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879458337 HTTP/1.1" 200 -
************ - - [22/May/2025:10:04:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879458337 HTTP/1.1" 200 160
************ - - [22/May/2025:10:05:22 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:05:22&etm=&_timer304=1747879522159 HTTP/1.1" 200 -
************ - - [22/May/2025:10:05:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879522159 HTTP/1.1" 200 -
************ - - [22/May/2025:10:05:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879522159 HTTP/1.1" 200 -
************ - - [22/May/2025:10:05:22 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879522159 HTTP/1.1" 200 -
************ - - [22/May/2025:10:05:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879522159 HTTP/1.1" 200 -
************ - - [22/May/2025:10:05:22 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:10:05:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:05:22 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:10:05:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:10:05:22 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+10:05:22&etm=&_timer304=1747879522159 HTTP/1.1" 200 156
************ - - [22/May/2025:10:05:22 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+11:00&filterCnt=6&_timer304=1747879522159 HTTP/1.1" 200 164
************ - - [22/May/2025:10:05:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747879522159 HTTP/1.1" 200 166
************ - - [22/May/2025:10:05:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747879522159 HTTP/1.1" 200 169
************ - - [22/May/2025:10:05:22 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:10:05:22 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 2313
************ - - [22/May/2025:10:05:22 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747879522159 HTTP/1.1" 200 13016
************ - - [22/May/2025:10:05:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879532059 HTTP/1.1" 200 -
************ - - [22/May/2025:10:05:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879532059 HTTP/1.1" 200 160
************ - - [22/May/2025:10:05:35 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:10:05:36 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 10716
************ - - [22/May/2025:10:05:43 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 479618
************ - - [22/May/2025:10:05:46 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 186504
************ - - [22/May/2025:10:06:08 +0800] "OPTIONS /api/call/inspect/add-inspect-task HTTP/1.1" 200 -
************ - - [22/May/2025:10:06:11 +0800] "POST /api/call/inspect/add-inspect-task HTTP/1.1" 200 152
************ - - [22/May/2025:10:06:11 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 2638
************ - - [22/May/2025:10:06:14 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 10568
************ - - [22/May/2025:10:06:22 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 10568
************ - - [22/May/2025:10:06:45 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 10568
************ - - [22/May/2025:10:06:47 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 10568
************ - - [22/May/2025:10:09:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879748326 HTTP/1.1" 200 -
************ - - [22/May/2025:10:09:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879748326 HTTP/1.1" 200 160
************ - - [22/May/2025:10:10:13 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 479622
************ - - [22/May/2025:10:10:17 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 186504
************ - - [22/May/2025:10:10:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747879822051 HTTP/1.1" 200 -
************ - - [22/May/2025:10:10:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747879822051 HTTP/1.1" 200 160
************ - - [22/May/2025:10:10:28 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 479620
************ - - [22/May/2025:10:12:25 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 10716
************ - - [22/May/2025:10:12:26 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=9b45902a-56bc-482f-952b-8f61d0921498&_timer304=1747879946893 HTTP/1.1" 200 -
************ - - [22/May/2025:10:12:26 +0800] "GET /api/call/common/message/call-result?extSendId=9b45902a-56bc-482f-952b-8f61d0921498&_timer304=1747879946893 HTTP/1.1" 200 3321
************ - - [22/May/2025:10:15:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747880122056 HTTP/1.1" 200 -
************ - - [22/May/2025:10:15:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747880122056 HTTP/1.1" 200 160
************ - - [22/May/2025:10:20:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747880422061 HTTP/1.1" 200 -
************ - - [22/May/2025:10:20:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747880422061 HTTP/1.1" 200 160
************ - - [22/May/2025:10:25:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747880722061 HTTP/1.1" 200 -
************ - - [22/May/2025:10:25:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747880722061 HTTP/1.1" 200 160
************ - - [22/May/2025:10:30:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747881022062 HTTP/1.1" 200 -
************ - - [22/May/2025:10:30:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747881022062 HTTP/1.1" 200 160
************ - - [22/May/2025:10:33:44 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 9692
************ - - [22/May/2025:10:35:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747881322060 HTTP/1.1" 200 -
************ - - [22/May/2025:10:35:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747881322060 HTTP/1.1" 200 160
************ - - [22/May/2025:10:40:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747881620916 HTTP/1.1" 200 -
************ - - [22/May/2025:10:40:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747881620916 HTTP/1.1" 200 160
************ - - [22/May/2025:10:43:58 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:10:43:58 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************ - - [22/May/2025:10:44:46 +0800] "POST /api/call/inspect/add-inspect-task HTTP/1.1" 200 158
************ - - [22/May/2025:10:44:48 +0800] "POST /api/call/inspect/add-inspect-task HTTP/1.1" 200 158
************ - - [22/May/2025:10:45:02 +0800] "POST /api/call/inspect/add-inspect-task HTTP/1.1" 200 158
************ - - [22/May/2025:10:45:06 +0800] "POST /api/call/inspect/add-inspect-task HTTP/1.1" 200 158
************ - - [22/May/2025:10:45:06 +0800] "POST /api/call/inspect/add-inspect-task HTTP/1.1" 200 158
************ - - [22/May/2025:10:45:14 +0800] "OPTIONS /api/call/inspect/add-inspect-task HTTP/1.1" 200 -
************ - - [22/May/2025:10:45:14 +0800] "POST /api/call/inspect/add-inspect-task HTTP/1.1" 200 158
************ - - [22/May/2025:10:45:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747881920917 HTTP/1.1" 200 -
************ - - [22/May/2025:10:45:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747881920917 HTTP/1.1" 200 160
************ - - [22/May/2025:10:50:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747882220913 HTTP/1.1" 200 -
************ - - [22/May/2025:10:50:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747882220913 HTTP/1.1" 200 160
************ - - [22/May/2025:10:55:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747882522166 HTTP/1.1" 200 -
************ - - [22/May/2025:10:55:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747882522166 HTTP/1.1" 200 160
************ - - [22/May/2025:11:01:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747882917172 HTTP/1.1" 200 -
************ - - [22/May/2025:11:01:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747882917172 HTTP/1.1" 200 160
************ - - [22/May/2025:11:06:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747883217288 HTTP/1.1" 200 -
************ - - [22/May/2025:11:06:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747883217288 HTTP/1.1" 200 160
************ - - [22/May/2025:11:11:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747883517269 HTTP/1.1" 200 -
************ - - [22/May/2025:11:11:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747883517269 HTTP/1.1" 200 160
************ - - [22/May/2025:11:16:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747883765270 HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747883765270 HTTP/1.1" 200 160
************ - - [22/May/2025:11:16:06 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:07 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 10568
************ - - [22/May/2025:11:16:11 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:11 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 236
************ - - [22/May/2025:11:16:17 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:17 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 236
************ - - [22/May/2025:11:16:18 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:19 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 10716
************ - - [22/May/2025:11:16:21 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:21 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 236
************ - - [22/May/2025:11:16:25 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:25 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 9692
************ - - [22/May/2025:11:16:27 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:27 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 236
************ - - [22/May/2025:11:16:28 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:28 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 236
************ - - [22/May/2025:11:16:38 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:38 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************ - - [22/May/2025:11:16:38 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3925
************ - - [22/May/2025:11:16:38 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:11:17:10 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:10 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 9535
************ - - [22/May/2025:11:17:14 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:15 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 9857
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/ewci/base/mal/write/205?_timer304=1747883875014 HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "GET /api/ewci/base/mal/write/205?_timer304=1747883875014 HTTP/1.1" 200 146
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+11:17:55&etm=&_timer304=1747883875314 HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747883875314 HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+12:00&filterCnt=6&_timer304=1747883875314 HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747883875314 HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747883875314 HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+11:17:55&etm=&_timer304=1747883875314 HTTP/1.1" 200 156
************ - - [22/May/2025:11:17:55 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:11:17:55 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:11:17:55 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747883875314 HTTP/1.1" 200 166
************ - - [22/May/2025:11:17:55 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+12:00&filterCnt=6&_timer304=1747883875314 HTTP/1.1" 200 164
************ - - [22/May/2025:11:17:55 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:11:17:55 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:11:17:55 +0800] "OPTIONS /api/base/shuser/select-by-conditon HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:55 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747883875314 HTTP/1.1" 200 169
************ - - [22/May/2025:11:17:55 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747883875314 HTTP/1.1" 200 13016
************ - - [22/May/2025:11:17:56 +0800] "POST /api/base/shuser/select-by-conditon HTTP/1.1" 200 20523
************ - - [22/May/2025:11:17:56 +0800] "OPTIONS /api/base/shuser/select-by-conditon HTTP/1.1" 200 -
************ - - [22/May/2025:11:17:57 +0800] "POST /api/base/shuser/select-by-conditon HTTP/1.1" 200 3454485
************ - - [22/May/2025:11:21:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747884117167 HTTP/1.1" 200 -
************ - - [22/May/2025:11:21:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747884117167 HTTP/1.1" 200 160
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+11:24:41&etm=&_timer304=1747884281228 HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747884281228 HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+12:00&filterCnt=6&_timer304=1747884281228 HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747884281228 HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747884281228 HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:41 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+11:24:41&etm=&_timer304=1747884281228 HTTP/1.1" 200 156
************ - - [22/May/2025:11:24:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:11:24:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:11:24:41 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:11:24:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:11:24:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747884281228 HTTP/1.1" 200 166
************ - - [22/May/2025:11:24:41 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+12:00&filterCnt=6&_timer304=1747884281228 HTTP/1.1" 200 164
************ - - [22/May/2025:11:24:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747884281228 HTTP/1.1" 200 169
************ - - [22/May/2025:11:24:41 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747884281228 HTTP/1.1" 200 13016
************ - - [22/May/2025:11:24:41 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:11:24:41 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3925
************ - - [22/May/2025:11:24:47 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:47 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************ - - [22/May/2025:11:24:48 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:11:24:48 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3925
************ - - [22/May/2025:11:25:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747884322176 HTTP/1.1" 200 -
************ - - [22/May/2025:11:25:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747884322176 HTTP/1.1" 200 160
************ - - [22/May/2025:11:31:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747884717174 HTTP/1.1" 200 -
************ - - [22/May/2025:11:31:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747884717174 HTTP/1.1" 200 160
************ - - [22/May/2025:11:36:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747885017175 HTTP/1.1" 200 -
************ - - [22/May/2025:11:36:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747885017175 HTTP/1.1" 200 160
************ - - [22/May/2025:11:41:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747885317177 HTTP/1.1" 200 -
************ - - [22/May/2025:11:41:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747885317177 HTTP/1.1" 200 160
************ - - [22/May/2025:11:46:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747885617168 HTTP/1.1" 200 -
************ - - [22/May/2025:11:46:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747885617168 HTTP/1.1" 200 160
************ - - [22/May/2025:11:51:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747885917164 HTTP/1.1" 200 -
************ - - [22/May/2025:11:51:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747885917164 HTTP/1.1" 200 160
************ - - [22/May/2025:11:56:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747886217286 HTTP/1.1" 200 -
************ - - [22/May/2025:11:56:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747886217286 HTTP/1.1" 200 160
************ - - [22/May/2025:12:01:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747886517173 HTTP/1.1" 200 -
************ - - [22/May/2025:12:01:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747886517173 HTTP/1.1" 200 160
************ - - [22/May/2025:12:06:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747886817171 HTTP/1.1" 200 -
************ - - [22/May/2025:12:06:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747886817171 HTTP/1.1" 200 160
************ - - [22/May/2025:12:11:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747887117288 HTTP/1.1" 200 -
************ - - [22/May/2025:12:11:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747887117288 HTTP/1.1" 200 160
************ - - [22/May/2025:12:16:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747887417163 HTTP/1.1" 200 -
************ - - [22/May/2025:12:16:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747887417163 HTTP/1.1" 200 160
************ - - [22/May/2025:12:21:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747887717165 HTTP/1.1" 200 -
************ - - [22/May/2025:12:21:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747887717165 HTTP/1.1" 200 160
************ - - [22/May/2025:12:26:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747888017177 HTTP/1.1" 200 -
************ - - [22/May/2025:12:26:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747888017177 HTTP/1.1" 200 160
************ - - [22/May/2025:12:31:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747888317293 HTTP/1.1" 200 -
************ - - [22/May/2025:12:31:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747888317293 HTTP/1.1" 200 160
************ - - [22/May/2025:12:36:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747888617169 HTTP/1.1" 200 -
************ - - [22/May/2025:12:36:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747888617169 HTTP/1.1" 200 160
************ - - [22/May/2025:12:41:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747888917175 HTTP/1.1" 200 -
************ - - [22/May/2025:12:41:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747888917175 HTTP/1.1" 200 160
************ - - [22/May/2025:12:46:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747889217289 HTTP/1.1" 200 -
************ - - [22/May/2025:12:46:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747889217289 HTTP/1.1" 200 160
************ - - [22/May/2025:12:51:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747889517175 HTTP/1.1" 200 -
************ - - [22/May/2025:12:51:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747889517175 HTTP/1.1" 200 160
************ - - [22/May/2025:12:56:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747889817165 HTTP/1.1" 200 -
************ - - [22/May/2025:12:56:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747889817165 HTTP/1.1" 200 160
************ - - [22/May/2025:13:01:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747890117285 HTTP/1.1" 200 -
************ - - [22/May/2025:13:01:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747890117285 HTTP/1.1" 200 160
************ - - [22/May/2025:13:06:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747890417299 HTTP/1.1" 200 -
************ - - [22/May/2025:13:06:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747890417299 HTTP/1.1" 200 160
************ - - [22/May/2025:13:11:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747890717168 HTTP/1.1" 200 -
************ - - [22/May/2025:13:11:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747890717168 HTTP/1.1" 200 160
************ - - [22/May/2025:13:16:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747891017167 HTTP/1.1" 200 -
************ - - [22/May/2025:13:16:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747891017167 HTTP/1.1" 200 160
************ - - [22/May/2025:13:21:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747891317287 HTTP/1.1" 200 -
************ - - [22/May/2025:13:21:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747891317287 HTTP/1.1" 200 160
************ - - [22/May/2025:13:26:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747891617176 HTTP/1.1" 200 -
************ - - [22/May/2025:13:26:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747891617176 HTTP/1.1" 200 160
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+13:30:02&etm=&_timer304=1747891802160 HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747891802160 HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747891802160 HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+14:00&filterCnt=6&_timer304=1747891802160 HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747891802160 HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:13:29:59 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [22/May/2025:13:29:59 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+13:30:02&etm=&_timer304=1747891802160 HTTP/1.1" 200 156
************* - - [22/May/2025:13:29:59 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [22/May/2025:13:29:59 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747891802160 HTTP/1.1" 200 166
************* - - [22/May/2025:13:29:59 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+14:00&filterCnt=6&_timer304=1747891802160 HTTP/1.1" 200 164
************* - - [22/May/2025:13:29:59 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:13:29:59 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:13:29:59 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747891802160 HTTP/1.1" 200 169
************* - - [22/May/2025:13:29:59 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747891802160 HTTP/1.1" 200 13016
************* - - [22/May/2025:13:29:59 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3925
************* - - [22/May/2025:13:29:59 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************* - - [22/May/2025:13:30:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747891812174 HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747891812174 HTTP/1.1" 200 160
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+13:30:54&etm=&_timer304=1747891854806 HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747891854806 HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+14:00&filterCnt=6&_timer304=1747891854806 HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747891854806 HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747891854806 HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:13:30:52 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [22/May/2025:13:30:52 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [22/May/2025:13:30:52 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747891854806 HTTP/1.1" 200 166
************* - - [22/May/2025:13:30:52 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:13:30:52 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+13:30:54&etm=&_timer304=1747891854806 HTTP/1.1" 200 156
************* - - [22/May/2025:13:30:52 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:13:30:52 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+14:00&filterCnt=6&_timer304=1747891854806 HTTP/1.1" 200 164
************* - - [22/May/2025:13:30:52 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747891854806 HTTP/1.1" 200 169
************* - - [22/May/2025:13:30:52 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747891854806 HTTP/1.1" 200 13016
************* - - [22/May/2025:13:30:52 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************* - - [22/May/2025:13:30:52 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3925
************* - - [22/May/2025:13:31:02 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747891864656 HTTP/1.1" 200 -
************* - - [22/May/2025:13:31:02 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747891864656 HTTP/1.1" 200 160
************ - - [22/May/2025:13:31:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747891917177 HTTP/1.1" 200 -
************ - - [22/May/2025:13:31:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747891917177 HTTP/1.1" 200 160
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/ewci/base/mal/write/937?_timer304=1747891939533 HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "GET /api/ewci/base/mal/write/937?_timer304=1747891939533 HTTP/1.1" 200 146
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+13:32:19&etm=&_timer304=1747891939782 HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747891939782 HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+14:00&filterCnt=6&_timer304=1747891939782 HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747891939782 HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747891939782 HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:13:32:19 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:13:32:19 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:13:32:19 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+13:32:19&etm=&_timer304=1747891939782 HTTP/1.1" 200 156
************ - - [22/May/2025:13:32:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747891939782 HTTP/1.1" 200 166
************ - - [22/May/2025:13:32:19 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+14:00&filterCnt=6&_timer304=1747891939782 HTTP/1.1" 200 164
************ - - [22/May/2025:13:32:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:13:32:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:13:32:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747891939782 HTTP/1.1" 200 169
************ - - [22/May/2025:13:32:19 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:13:32:19 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747891939782 HTTP/1.1" 200 13016
************ - - [22/May/2025:13:32:19 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:13:32:19 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 424
************ - - [22/May/2025:13:32:19 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:13:32:19 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 419
************* - - [22/May/2025:13:35:06 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:35:07 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [22/May/2025:13:35:07 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:35:07 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************* - - [22/May/2025:13:35:14 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:35:15 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************ - - [22/May/2025:13:35:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747892122163 HTTP/1.1" 200 -
************ - - [22/May/2025:13:35:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747892122163 HTTP/1.1" 200 160
************* - - [22/May/2025:13:35:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747892155159 HTTP/1.1" 200 -
************* - - [22/May/2025:13:35:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747892155159 HTTP/1.1" 200 160
************* - - [22/May/2025:13:40:34 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:40:34 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [22/May/2025:13:40:40 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:40:40 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [22/May/2025:13:40:41 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:40:41 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************* - - [22/May/2025:13:40:44 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:40:44 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************ - - [22/May/2025:13:40:48 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747892448641 HTTP/1.1" 200 -
************ - - [22/May/2025:13:40:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747892448641 HTTP/1.1" 200 160
************* - - [22/May/2025:13:40:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747892454652 HTTP/1.1" 200 -
************* - - [22/May/2025:13:40:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747892454652 HTTP/1.1" 200 160
************* - - [22/May/2025:13:42:30 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:42:31 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [22/May/2025:13:43:08 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:08 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************* - - [22/May/2025:13:43:11 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:11 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************* - - [22/May/2025:13:43:13 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:13 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************* - - [22/May/2025:13:43:14 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:14 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************* - - [22/May/2025:13:43:18 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:18 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************* - - [22/May/2025:13:43:19 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:19 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************* - - [22/May/2025:13:43:28 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:28 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [22/May/2025:13:43:29 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:29 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************* - - [22/May/2025:13:43:30 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:30 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************* - - [22/May/2025:13:43:32 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:32 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************* - - [22/May/2025:13:43:33 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:33 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 19737
************* - - [22/May/2025:13:43:46 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:46 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************* - - [22/May/2025:13:43:48 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:48 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 19737
************* - - [22/May/2025:13:43:49 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:49 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************* - - [22/May/2025:13:43:51 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:51 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************* - - [22/May/2025:13:43:53 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:53 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 19737
************* - - [22/May/2025:13:43:54 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:54 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************* - - [22/May/2025:13:43:55 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:55 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************* - - [22/May/2025:13:43:57 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:43:57 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 166516
************ - - [22/May/2025:13:44:00 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:00 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:00 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:00 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:00 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:00 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:13:44:00 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 2232
************ - - [22/May/2025:13:44:00 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 2401
************ - - [22/May/2025:13:44:00 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:13:44:00 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************* - - [22/May/2025:13:44:00 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:44:00 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 32257
************* - - [22/May/2025:13:44:01 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:13:44:01 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 166516
************ - - [22/May/2025:13:44:04 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:04 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:04 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:04 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:04 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:04 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 2232
************ - - [22/May/2025:13:44:04 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:13:44:04 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 2401
************ - - [22/May/2025:13:44:04 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:13:44:05 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:13:44:06 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:06 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:06 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:06 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:06 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:06 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 2401
************ - - [22/May/2025:13:44:06 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 2232
************ - - [22/May/2025:13:44:06 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:13:44:06 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:13:44:06 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:13:44:20 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:20 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:20 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:20 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:20 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:20 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:13:44:20 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:13:44:20 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 419
************ - - [22/May/2025:13:44:20 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 424
************ - - [22/May/2025:13:44:20 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:13:44:37 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:37 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:37 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:37 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:37 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:13:44:37 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:13:44:37 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:13:44:37 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:13:44:37 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1184
************ - - [22/May/2025:13:44:37 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1178
************ - - [22/May/2025:13:45:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747892722164 HTTP/1.1" 200 -
************ - - [22/May/2025:13:45:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747892722164 HTTP/1.1" 200 160
************* - - [22/May/2025:13:45:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747892755172 HTTP/1.1" 200 -
************* - - [22/May/2025:13:45:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747892755172 HTTP/1.1" 200 160
************ - - [22/May/2025:13:50:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747893024364 HTTP/1.1" 200 -
************ - - [22/May/2025:13:50:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747893024364 HTTP/1.1" 200 160
************* - - [22/May/2025:13:53:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747893206868 HTTP/1.1" 200 -
************* - - [22/May/2025:13:53:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747893206868 HTTP/1.1" 200 160
************ - - [22/May/2025:13:56:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747893417165 HTTP/1.1" 200 -
************ - - [22/May/2025:13:56:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747893417165 HTTP/1.1" 200 160
************* - - [22/May/2025:13:58:23 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747893505975 HTTP/1.1" 200 -
************* - - [22/May/2025:13:58:23 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747893505975 HTTP/1.1" 200 160
************* - - [22/May/2025:14:01:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747893709230 HTTP/1.1" 200 -
************* - - [22/May/2025:14:01:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747893709230 HTTP/1.1" 200 160
************ - - [22/May/2025:14:01:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747893717302 HTTP/1.1" 200 -
************ - - [22/May/2025:14:01:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747893717302 HTTP/1.1" 200 160
************ - - [22/May/2025:14:05:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747893922177 HTTP/1.1" 200 -
************ - - [22/May/2025:14:05:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747893922177 HTTP/1.1" 200 160
************* - - [22/May/2025:14:07:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747894079922 HTTP/1.1" 200 -
************* - - [22/May/2025:14:07:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747894079922 HTTP/1.1" 200 160
************ - - [22/May/2025:14:11:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747894317293 HTTP/1.1" 200 -
************ - - [22/May/2025:14:11:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747894317293 HTTP/1.1" 200 160
************* - - [22/May/2025:14:13:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747894393424 HTTP/1.1" 200 -
************* - - [22/May/2025:14:13:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747894393424 HTTP/1.1" 200 160
************* - - [22/May/2025:14:16:48 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747894611367 HTTP/1.1" 200 -
************* - - [22/May/2025:14:16:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747894611367 HTTP/1.1" 200 160
************ - - [22/May/2025:14:16:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747894617298 HTTP/1.1" 200 -
************ - - [22/May/2025:14:16:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747894617298 HTTP/1.1" 200 160
************ - - [22/May/2025:14:21:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747894917167 HTTP/1.1" 200 -
************ - - [22/May/2025:14:21:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747894917167 HTTP/1.1" 200 160
************* - - [22/May/2025:14:23:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747894994668 HTTP/1.1" 200 -
************* - - [22/May/2025:14:23:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747894994668 HTTP/1.1" 200 160
************ - - [22/May/2025:14:26:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747895217173 HTTP/1.1" 200 -
************ - - [22/May/2025:14:26:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747895217173 HTTP/1.1" 200 160
************* - - [22/May/2025:14:28:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747895282936 HTTP/1.1" 200 -
************* - - [22/May/2025:14:28:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747895282936 HTTP/1.1" 200 160
127.0.0.1 - - [22/May/2025:14:31:12 +0800] "GET /doc.html HTTP/1.1" 302 -
127.0.0.1 - - [22/May/2025:14:31:12 +0800] "GET /login HTTP/1.1" 302 -
************ - - [22/May/2025:14:31:15 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [22/May/2025:14:31:15 +0800] "GET /login HTTP/1.1" 302 -
************ - - [22/May/2025:14:31:16 +0800] "GET /login?code=EGDpqe&state=IPS5kt HTTP/1.1" 302 -
************ - - [22/May/2025:14:31:16 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [22/May/2025:14:31:17 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1747895477598 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1747895477598 HTTP/1.1" 200 508
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747895480306 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747895480306 HTTP/1.1" 200 59820
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747895480367 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747895480367 HTTP/1.1" 200 10388
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747895480382 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747895480382 HTTP/1.1" 200 2021
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+14:31:20&etm=&_timer304=1747895480549 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747895480549 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+15:00&filterCnt=6&_timer304=1747895480549 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747895480549 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747895480549 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:14:31:22 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+14:31:20&etm=&_timer304=1747895480549 HTTP/1.1" 200 156
************ - - [22/May/2025:14:31:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:14:31:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:14:31:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747895480549 HTTP/1.1" 200 166
************ - - [22/May/2025:14:31:22 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+15:00&filterCnt=6&_timer304=1747895480549 HTTP/1.1" 200 164
************ - - [22/May/2025:14:31:22 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:14:31:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747895480549 HTTP/1.1" 200 169
************ - - [22/May/2025:14:31:22 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:14:31:22 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747895480549 HTTP/1.1" 200 13016
************ - - [22/May/2025:14:31:22 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1747895482688 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:22 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1747895482688 HTTP/1.1" 200 1560
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747895483082 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/base/saas/token?_timer304=1747895483082 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747895483096 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/duty/event/select-list HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "OPTIONS /api/duty/event/listEventType HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286484
************ - - [22/May/2025:14:31:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:14:31:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [22/May/2025:14:31:24 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
************ - - [22/May/2025:14:31:24 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:25 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [22/May/2025:14:31:25 +0800] "GET /api/base/saas/token?_timer304=1747895483082 HTTP/1.1" 200 411
************ - - [22/May/2025:14:31:25 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [22/May/2025:14:31:25 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [22/May/2025:14:31:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [22/May/2025:14:31:25 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747895483096 HTTP/1.1" 200 2021
************ - - [22/May/2025:14:31:25 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [22/May/2025:14:31:25 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [22/May/2025:14:31:25 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [22/May/2025:14:31:25 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 321
************ - - [22/May/2025:14:31:25 +0800] "POST /api/duty/event/select-list HTTP/1.1" 200 111
************ - - [22/May/2025:14:31:25 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:14:31:25 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************ - - [22/May/2025:14:31:25 +0800] "POST /api/duty/event/listEventType HTTP/1.1" 200 231
************ - - [22/May/2025:14:31:25 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [22/May/2025:14:31:25 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-22&_timer304=1747895483082 HTTP/1.1" 200 359
************ - - [22/May/2025:14:31:27 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747895487334 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:27 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747895487334 HTTP/1.1" 200 160
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/ewci/base/mal/write/937?_timer304=1747895491985 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+14:31:31&etm=&_timer304=1747895492107 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+15:00&filterCnt=6&_timer304=1747895492107 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747895492107 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747895492107 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747895492107 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:32 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:14:31:32 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:14:31:32 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:14:31:32 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:14:31:32 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+14:31:31&etm=&_timer304=1747895492107 HTTP/1.1" 200 156
************ - - [22/May/2025:14:31:32 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+15:00&filterCnt=6&_timer304=1747895492107 HTTP/1.1" 200 164
************ - - [22/May/2025:14:31:32 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747895492107 HTTP/1.1" 200 166
************ - - [22/May/2025:14:31:32 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747895492107 HTTP/1.1" 200 169
************ - - [22/May/2025:14:31:32 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:14:31:32 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747895492107 HTTP/1.1" 200 13016
************ - - [22/May/2025:14:31:32 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:31:32 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:14:31:32 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:14:31:32 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:14:31:32 +0800] "GET /api/ewci/base/mal/write/937?_timer304=1747895491985 HTTP/1.1" 200 146
************ - - [22/May/2025:14:31:43 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:14:31:43 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:31:43 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:14:31:43 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:14:31:43 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:14:31:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747895517169 HTTP/1.1" 200 -
************ - - [22/May/2025:14:31:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747895517169 HTTP/1.1" 200 160
************ - - [22/May/2025:14:31:58 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:14:31:58 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:31:58 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:14:31:58 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:14:31:58 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:14:31:59 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:14:31:59 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:31:59 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:14:31:59 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:14:31:59 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:14:32:00 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:14:32:00 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:32:00 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:14:32:00 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:14:32:00 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:14:32:11 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:14:32:11 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:32:11 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:14:32:11 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:14:32:11 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+14:32:44&etm=&_timer304=1747895564236 HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747895564236 HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+15:00&filterCnt=6&_timer304=1747895564236 HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747895564236 HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747895564236 HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:44 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:14:32:44 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+14:32:44&etm=&_timer304=1747895564236 HTTP/1.1" 200 156
************ - - [22/May/2025:14:32:44 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:14:32:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747895564236 HTTP/1.1" 200 166
************ - - [22/May/2025:14:32:44 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:14:32:44 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+15:00&filterCnt=6&_timer304=1747895564236 HTTP/1.1" 200 164
************ - - [22/May/2025:14:32:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:14:32:44 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:32:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:14:32:44 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:14:32:44 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:14:32:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747895564236 HTTP/1.1" 200 169
************ - - [22/May/2025:14:32:44 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:14:32:44 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747895564236 HTTP/1.1" 200 13016
************* - - [22/May/2025:14:32:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747895568889 HTTP/1.1" 200 -
************* - - [22/May/2025:14:32:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747895568889 HTTP/1.1" 200 160
************ - - [22/May/2025:14:32:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747895574014 HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747895574014 HTTP/1.1" 200 160
************ - - [22/May/2025:14:32:54 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:54 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:54 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:54 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:54 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:14:32:54 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:14:32:54 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:32:54 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:14:32:54 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:14:32:54 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:14:36:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747895817173 HTTP/1.1" 200 -
************ - - [22/May/2025:14:36:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747895817173 HTTP/1.1" 200 160
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+15:00&filterCnt=6&_timer304=1747895932720 HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747895932720 HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+14:38:52&etm=&_timer304=1747895932720 HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747895932720 HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747895932720 HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:53 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:14:38:55 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:14:38:55 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:14:38:55 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:14:38:55 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:14:38:55 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:38:55 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:14:38:56 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747895932720 HTTP/1.1" 200 13016
************ - - [22/May/2025:14:38:57 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:14:38:57 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+14:38:52&etm=&_timer304=1747895932720 HTTP/1.1" 200 156
************ - - [22/May/2025:14:38:57 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+15:00&filterCnt=6&_timer304=1747895932720 HTTP/1.1" 200 164
************ - - [22/May/2025:14:38:57 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747895932720 HTTP/1.1" 200 166
************ - - [22/May/2025:14:38:57 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:14:38:57 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:14:38:57 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747895932720 HTTP/1.1" 200 169
************ - - [22/May/2025:14:39:02 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747895942496 HTTP/1.1" 200 -
************ - - [22/May/2025:14:39:02 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747895942496 HTTP/1.1" 200 160
************ - - [22/May/2025:14:39:08 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:14:39:08 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:14:39:08 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:14:39:08 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:14:39:08 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:14:39:08 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:14:39:08 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:14:39:08 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:14:39:08 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:14:39:08 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************* - - [22/May/2025:14:40:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896056159 HTTP/1.1" 200 -
************* - - [22/May/2025:14:40:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896056159 HTTP/1.1" 200 160
************ - - [22/May/2025:14:41:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896117175 HTTP/1.1" 200 -
************ - - [22/May/2025:14:41:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896117175 HTTP/1.1" 200 160
************ - - [22/May/2025:14:43:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896233168 HTTP/1.1" 200 -
************ - - [22/May/2025:14:43:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896233168 HTTP/1.1" 200 160
************* - - [22/May/2025:14:45:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896356170 HTTP/1.1" 200 -
************* - - [22/May/2025:14:45:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896356170 HTTP/1.1" 200 160
************ - - [22/May/2025:14:46:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896417169 HTTP/1.1" 200 -
************ - - [22/May/2025:14:46:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896417169 HTTP/1.1" 200 160
************* - - [22/May/2025:14:47:56 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [22/May/2025:14:47:57 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 9857
************* - - [22/May/2025:14:48:01 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [22/May/2025:14:48:01 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 236
************* - - [22/May/2025:14:48:02 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [22/May/2025:14:48:02 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 236
************* - - [22/May/2025:14:48:04 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [22/May/2025:14:48:04 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 9857
************* - - [22/May/2025:14:48:06 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [22/May/2025:14:48:06 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 236
************ - - [22/May/2025:14:48:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896533178 HTTP/1.1" 200 -
************ - - [22/May/2025:14:48:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896533178 HTTP/1.1" 200 160
************* - - [22/May/2025:14:49:58 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [22/May/2025:14:49:58 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [22/May/2025:14:49:58 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************* - - [22/May/2025:14:49:58 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 4232
************* - - [22/May/2025:14:50:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896654650 HTTP/1.1" 200 -
************* - - [22/May/2025:14:50:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896654650 HTTP/1.1" 200 160
************ - - [22/May/2025:14:51:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896717165 HTTP/1.1" 200 -
************ - - [22/May/2025:14:51:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896717165 HTTP/1.1" 200 160
************ - - [22/May/2025:14:53:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896833167 HTTP/1.1" 200 -
************ - - [22/May/2025:14:53:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896833167 HTTP/1.1" 200 160
************* - - [22/May/2025:14:55:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747896956180 HTTP/1.1" 200 -
************* - - [22/May/2025:14:55:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747896956180 HTTP/1.1" 200 160
************ - - [22/May/2025:14:56:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897017169 HTTP/1.1" 200 -
************ - - [22/May/2025:14:56:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897017169 HTTP/1.1" 200 160
************ - - [22/May/2025:14:58:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897132493 HTTP/1.1" 200 -
************ - - [22/May/2025:14:58:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897132493 HTTP/1.1" 200 160
************* - - [22/May/2025:15:00:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897254657 HTTP/1.1" 200 -
************* - - [22/May/2025:15:00:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897254657 HTTP/1.1" 200 160
************ - - [22/May/2025:15:01:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897317165 HTTP/1.1" 200 -
************ - - [22/May/2025:15:01:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897317165 HTTP/1.1" 200 160
************* - - [22/May/2025:15:03:23 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:23 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 9535
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747897414873 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+15:03:34&etm=&_timer304=1747897414873 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+16:00&filterCnt=6&_timer304=1747897414873 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747897414873 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747897414873 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:32 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [22/May/2025:15:03:32 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+15:03:34&etm=&_timer304=1747897414873 HTTP/1.1" 200 156
************* - - [22/May/2025:15:03:32 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+16:00&filterCnt=6&_timer304=1747897414873 HTTP/1.1" 200 164
************* - - [22/May/2025:15:03:32 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [22/May/2025:15:03:32 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747897414873 HTTP/1.1" 200 166
************* - - [22/May/2025:15:03:32 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:15:03:32 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:15:03:32 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747897414873 HTTP/1.1" 200 169
************* - - [22/May/2025:15:03:32 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747897414873 HTTP/1.1" 200 13016
************* - - [22/May/2025:15:03:32 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************* - - [22/May/2025:15:03:32 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 4232
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/ewci/base/mal/write/937?_timer304=1747897417959 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+15:03:37&etm=&_timer304=1747897418098 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747897418098 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747897418098 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+16:00&filterCnt=6&_timer304=1747897418098 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747897418098 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [22/May/2025:15:03:35 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:35 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+15:03:37&etm=&_timer304=1747897418098 HTTP/1.1" 200 156
************* - - [22/May/2025:15:03:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747897418098 HTTP/1.1" 200 166
************* - - [22/May/2025:15:03:35 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [22/May/2025:15:03:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:15:03:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747897418098 HTTP/1.1" 200 169
************* - - [22/May/2025:15:03:35 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+16:00&filterCnt=6&_timer304=1747897418098 HTTP/1.1" 200 164
************* - - [22/May/2025:15:03:35 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************* - - [22/May/2025:15:03:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:15:03:35 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747897418098 HTTP/1.1" 200 13016
************* - - [22/May/2025:15:03:35 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************* - - [22/May/2025:15:03:35 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************* - - [22/May/2025:15:03:35 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************* - - [22/May/2025:15:03:35 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************* - - [22/May/2025:15:03:37 +0800] "GET /api/ewci/base/mal/write/937?_timer304=1747897417959 HTTP/1.1" 200 146
************* - - [22/May/2025:15:03:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897424915 HTTP/1.1" 200 -
************* - - [22/May/2025:15:03:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897424915 HTTP/1.1" 200 160
************ - - [22/May/2025:15:03:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897433277 HTTP/1.1" 200 -
************ - - [22/May/2025:15:03:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897433277 HTTP/1.1" 200 160
************* - - [22/May/2025:15:04:29 +0800] "OPTIONS /api/usif/menu/move-user-menu?menuCodeFrom=939&menuCodeTo=939&userId=5440&_timer304=1747897472527 HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "GET /api/usif/menu/move-user-menu?menuCodeFrom=939&menuCodeTo=939&userId=5440&_timer304=1747897472527 HTTP/1.1" 200 150
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747897473497 HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+15:04:33&etm=&_timer304=1747897473497 HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+16:00&filterCnt=6&_timer304=1747897473497 HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747897473497 HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747897473497 HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:30 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [22/May/2025:15:04:31 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [22/May/2025:15:04:31 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747897473497 HTTP/1.1" 200 166
************* - - [22/May/2025:15:04:31 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+15:04:33&etm=&_timer304=1747897473497 HTTP/1.1" 200 156
************* - - [22/May/2025:15:04:31 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+16:00&filterCnt=6&_timer304=1747897473497 HTTP/1.1" 200 164
************* - - [22/May/2025:15:04:31 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:15:04:31 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [22/May/2025:15:04:31 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747897473497 HTTP/1.1" 200 169
************* - - [22/May/2025:15:04:31 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747897473497 HTTP/1.1" 200 13016
************* - - [22/May/2025:15:04:31 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************* - - [22/May/2025:15:04:31 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 4232
************* - - [22/May/2025:15:06:35 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [22/May/2025:15:06:35 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:06:35 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************* - - [22/May/2025:15:06:35 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3155
************ - - [22/May/2025:15:06:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897617171 HTTP/1.1" 200 -
************ - - [22/May/2025:15:06:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897617171 HTTP/1.1" 200 160
************* - - [22/May/2025:15:08:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897715159 HTTP/1.1" 200 -
************* - - [22/May/2025:15:08:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897715159 HTTP/1.1" 200 160
************ - - [22/May/2025:15:08:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897737169 HTTP/1.1" 200 -
************ - - [22/May/2025:15:08:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897737169 HTTP/1.1" 200 160
************ - - [22/May/2025:15:11:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747897917166 HTTP/1.1" 200 -
************ - - [22/May/2025:15:11:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747897917166 HTTP/1.1" 200 160
************* - - [22/May/2025:15:14:36 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898078624 HTTP/1.1" 200 -
************* - - [22/May/2025:15:14:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898078624 HTTP/1.1" 200 160
************ - - [22/May/2025:15:14:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898097291 HTTP/1.1" 200 -
************ - - [22/May/2025:15:14:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898097291 HTTP/1.1" 200 160
************ - - [22/May/2025:15:16:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898217172 HTTP/1.1" 200 -
************ - - [22/May/2025:15:16:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898217172 HTTP/1.1" 200 160
************* - - [22/May/2025:15:19:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898360570 HTTP/1.1" 200 -
************* - - [22/May/2025:15:19:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898360570 HTTP/1.1" 200 160
************ - - [22/May/2025:15:19:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898397297 HTTP/1.1" 200 -
************ - - [22/May/2025:15:19:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898397297 HTTP/1.1" 200 160
************ - - [22/May/2025:15:21:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898517174 HTTP/1.1" 200 -
************ - - [22/May/2025:15:21:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898517174 HTTP/1.1" 200 160
************ - - [22/May/2025:15:24:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898697165 HTTP/1.1" 200 -
************ - - [22/May/2025:15:24:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898697165 HTTP/1.1" 200 160
************* - - [22/May/2025:15:25:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898711976 HTTP/1.1" 200 -
************* - - [22/May/2025:15:25:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898711976 HTTP/1.1" 200 160
************ - - [22/May/2025:15:26:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898817166 HTTP/1.1" 200 -
************ - - [22/May/2025:15:26:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898817166 HTTP/1.1" 200 160
************ - - [22/May/2025:15:29:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747898997174 HTTP/1.1" 200 -
************ - - [22/May/2025:15:29:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747898997174 HTTP/1.1" 200 160
************* - - [22/May/2025:15:30:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899062468 HTTP/1.1" 200 -
************* - - [22/May/2025:15:31:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899062468 HTTP/1.1" 200 160
************ - - [22/May/2025:15:31:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899117174 HTTP/1.1" 200 -
************ - - [22/May/2025:15:31:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899117174 HTTP/1.1" 200 160
************ - - [22/May/2025:15:33:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899234171 HTTP/1.1" 200 -
************ - - [22/May/2025:15:33:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899234171 HTTP/1.1" 200 160
************* - - [22/May/2025:15:35:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899311316 HTTP/1.1" 200 -
************* - - [22/May/2025:15:35:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899311316 HTTP/1.1" 200 160
************ - - [22/May/2025:15:36:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899417176 HTTP/1.1" 200 -
************ - - [22/May/2025:15:36:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899417176 HTTP/1.1" 200 160
************ - - [22/May/2025:15:39:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899597297 HTTP/1.1" 200 -
************ - - [22/May/2025:15:39:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899597297 HTTP/1.1" 200 160
************* - - [22/May/2025:15:40:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899641400 HTTP/1.1" 200 -
************* - - [22/May/2025:15:40:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899641400 HTTP/1.1" 200 160
************ - - [22/May/2025:15:41:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899717177 HTTP/1.1" 200 -
************ - - [22/May/2025:15:41:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899717177 HTTP/1.1" 200 160
************ - - [22/May/2025:15:44:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899897166 HTTP/1.1" 200 -
************ - - [22/May/2025:15:44:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899897166 HTTP/1.1" 200 160
************* - - [22/May/2025:15:45:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747899957878 HTTP/1.1" 200 -
************* - - [22/May/2025:15:45:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747899957878 HTTP/1.1" 200 160
************ - - [22/May/2025:15:46:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900017172 HTTP/1.1" 200 -
************ - - [22/May/2025:15:46:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900017172 HTTP/1.1" 200 160
************ - - [22/May/2025:15:49:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900197170 HTTP/1.1" 200 -
************ - - [22/May/2025:15:49:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900197170 HTTP/1.1" 200 160
************* - - [22/May/2025:15:51:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900264518 HTTP/1.1" 200 -
************* - - [22/May/2025:15:51:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900264518 HTTP/1.1" 200 160
************ - - [22/May/2025:15:51:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900317171 HTTP/1.1" 200 -
************ - - [22/May/2025:15:51:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900317171 HTTP/1.1" 200 160
************ - - [22/May/2025:15:54:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900497297 HTTP/1.1" 200 -
************ - - [22/May/2025:15:54:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900497297 HTTP/1.1" 200 160
************* - - [22/May/2025:15:55:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900559627 HTTP/1.1" 200 -
************* - - [22/May/2025:15:55:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900559627 HTTP/1.1" 200 160
************ - - [22/May/2025:15:56:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900617178 HTTP/1.1" 200 -
************ - - [22/May/2025:15:56:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900617178 HTTP/1.1" 200 160
************* - - [22/May/2025:15:58:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900726592 HTTP/1.1" 200 -
************* - - [22/May/2025:15:58:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900726592 HTTP/1.1" 200 160
************* - - [22/May/2025:15:58:46 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:58:46 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [22/May/2025:15:58:47 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:58:47 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 433834
************* - - [22/May/2025:15:59:20 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:20 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 240
************* - - [22/May/2025:15:59:21 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:21 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 603
************* - - [22/May/2025:15:59:21 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:21 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 41592
************* - - [22/May/2025:15:59:22 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:22 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 36321
************* - - [22/May/2025:15:59:23 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:23 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 240
************* - - [22/May/2025:15:59:23 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:23 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 240
************* - - [22/May/2025:15:59:24 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:24 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 240
************* - - [22/May/2025:15:59:24 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:24 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 27338
************* - - [22/May/2025:15:59:25 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:25 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 36305
************* - - [22/May/2025:15:59:25 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:25 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 147316
************* - - [22/May/2025:15:59:36 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:36 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 36305
************* - - [22/May/2025:15:59:38 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:38 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 147316
************* - - [22/May/2025:15:59:41 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:42 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 433834
************* - - [22/May/2025:15:59:43 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [22/May/2025:15:59:43 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 166516
************ - - [22/May/2025:15:59:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900797171 HTTP/1.1" 200 -
************ - - [22/May/2025:15:59:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900797171 HTTP/1.1" 200 160
************ - - [22/May/2025:16:01:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747900917167 HTTP/1.1" 200 -
************ - - [22/May/2025:16:01:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747900917167 HTTP/1.1" 200 160
************ - - [22/May/2025:16:04:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901097170 HTTP/1.1" 200 -
************ - - [22/May/2025:16:04:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901097170 HTTP/1.1" 200 160
************* - - [22/May/2025:16:05:30 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901132753 HTTP/1.1" 200 -
************* - - [22/May/2025:16:05:30 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901132753 HTTP/1.1" 200 160
************ - - [22/May/2025:16:06:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901217176 HTTP/1.1" 200 -
************ - - [22/May/2025:16:06:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901217176 HTTP/1.1" 200 160
************ - - [22/May/2025:16:09:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901397163 HTTP/1.1" 200 -
************ - - [22/May/2025:16:09:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901397163 HTTP/1.1" 200 160
************* - - [22/May/2025:16:10:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901442448 HTTP/1.1" 200 -
************* - - [22/May/2025:16:10:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901442448 HTTP/1.1" 200 160
************ - - [22/May/2025:16:11:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901517174 HTTP/1.1" 200 -
************ - - [22/May/2025:16:11:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901517174 HTTP/1.1" 200 160
************ - - [22/May/2025:16:14:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901697284 HTTP/1.1" 200 -
************ - - [22/May/2025:16:14:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901697284 HTTP/1.1" 200 160
************* - - [22/May/2025:16:16:02 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901765079 HTTP/1.1" 200 -
************* - - [22/May/2025:16:16:02 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901765079 HTTP/1.1" 200 160
************ - - [22/May/2025:16:16:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901817166 HTTP/1.1" 200 -
************ - - [22/May/2025:16:16:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901817166 HTTP/1.1" 200 160
************* - - [22/May/2025:16:18:58 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901941092 HTTP/1.1" 200 -
************* - - [22/May/2025:16:18:58 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901941092 HTTP/1.1" 200 160
************ - - [22/May/2025:16:19:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747901997172 HTTP/1.1" 200 -
************ - - [22/May/2025:16:19:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747901997172 HTTP/1.1" 200 160
************ - - [22/May/2025:16:21:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747902117167 HTTP/1.1" 200 -
************ - - [22/May/2025:16:21:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747902117167 HTTP/1.1" 200 160
************ - - [22/May/2025:16:24:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747902297164 HTTP/1.1" 200 -
************ - - [22/May/2025:16:24:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747902297164 HTTP/1.1" 200 160
************* - - [22/May/2025:16:26:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747902371757 HTTP/1.1" 200 -
************* - - [22/May/2025:16:26:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747902371757 HTTP/1.1" 200 160
************ - - [22/May/2025:16:26:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747902417176 HTTP/1.1" 200 -
************ - - [22/May/2025:16:26:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747902417176 HTTP/1.1" 200 160
************ - - [22/May/2025:16:29:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747902597287 HTTP/1.1" 200 -
************ - - [22/May/2025:16:29:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747902597287 HTTP/1.1" 200 160
************* - - [22/May/2025:16:30:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747902603459 HTTP/1.1" 200 -
************* - - [22/May/2025:16:30:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747902603459 HTTP/1.1" 200 160
************ - - [22/May/2025:16:31:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747902717172 HTTP/1.1" 200 -
************ - - [22/May/2025:16:31:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747902717172 HTTP/1.1" 200 160
************ - - [22/May/2025:16:34:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747902897170 HTTP/1.1" 200 -
************ - - [22/May/2025:16:34:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747902897170 HTTP/1.1" 200 160
************* - - [22/May/2025:16:35:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747902944175 HTTP/1.1" 200 -
************* - - [22/May/2025:16:35:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747902944175 HTTP/1.1" 200 160
************ - - [22/May/2025:16:36:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747903017174 HTTP/1.1" 200 -
************ - - [22/May/2025:16:36:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747903017174 HTTP/1.1" 200 160
************ - - [22/May/2025:16:39:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747903197165 HTTP/1.1" 200 -
************ - - [22/May/2025:16:39:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747903197165 HTTP/1.1" 200 160
************* - - [22/May/2025:16:40:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747903209925 HTTP/1.1" 200 -
************* - - [22/May/2025:16:40:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747903209925 HTTP/1.1" 200 160
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:40:12&etm=&_timer304=1747903212552 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747903212552 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747903212552 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747903212552 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747903212552 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/base/shuser/select-by-conditon HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:16:40:12 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747903212552 HTTP/1.1" 200 166
************ - - [22/May/2025:16:40:12 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:40:12 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:16:40:12 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:40:12&etm=&_timer304=1747903212552 HTTP/1.1" 200 156
************ - - [22/May/2025:16:40:12 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:40:12 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747903212552 HTTP/1.1" 200 164
************ - - [22/May/2025:16:40:12 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747903212552 HTTP/1.1" 200 169
************ - - [22/May/2025:16:40:12 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747903212552 HTTP/1.1" 200 13016
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:40:12&etm=&_timer304=1747903212763 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747903212763 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747903212763 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747903212763 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747903212763 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:12 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:16:40:12 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:16:40:12 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:40:12 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:40:12 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747903212763 HTTP/1.1" 200 164
************ - - [22/May/2025:16:40:12 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:40:12&etm=&_timer304=1747903212763 HTTP/1.1" 200 156
************ - - [22/May/2025:16:40:12 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747903212763 HTTP/1.1" 200 166
************ - - [22/May/2025:16:40:12 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747903212763 HTTP/1.1" 200 169
************ - - [22/May/2025:16:40:12 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747903212763 HTTP/1.1" 200 13016
************ - - [22/May/2025:16:40:13 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 4232
************ - - [22/May/2025:16:40:13 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:16:40:13 +0800] "POST /api/base/shuser/select-by-conditon HTTP/1.1" 200 20523
************ - - [22/May/2025:16:40:15 +0800] "POST /api/base/shuser/select-by-conditon HTTP/1.1" 200 3454485
************ - - [22/May/2025:16:40:16 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:16 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:16:40:19 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 387
************ - - [22/May/2025:16:40:20 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 245
************ - - [22/May/2025:16:40:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747903220905 HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747903220905 HTTP/1.1" 200 160
************ - - [22/May/2025:16:40:21 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 245
************ - - [22/May/2025:16:40:22 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 245
************ - - [22/May/2025:16:40:22 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 245
************ - - [22/May/2025:16:40:23 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 245
************ - - [22/May/2025:16:40:24 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:16:40:33 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:33 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:16:40:34 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:34 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:16:40:34 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:34 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:16:40:35 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:35 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 388
************ - - [22/May/2025:16:40:36 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:16:40:36 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:42:01&etm=&_timer304=1747903321704 HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747903321704 HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747903321704 HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747903321704 HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747903321704 HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:01 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:42:01&etm=&_timer304=1747903321704 HTTP/1.1" 200 156
************ - - [22/May/2025:16:42:01 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:16:42:01 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:16:42:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747903321704 HTTP/1.1" 200 166
************ - - [22/May/2025:16:42:01 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747903321704 HTTP/1.1" 200 164
************ - - [22/May/2025:16:42:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:42:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:42:01 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:16:42:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747903321704 HTTP/1.1" 200 169
************ - - [22/May/2025:16:42:01 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:42:01 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747903321704 HTTP/1.1" 200 13016
************ - - [22/May/2025:16:42:01 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:16:42:01 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:16:42:01 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:42:05 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:05 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:05 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:05 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:05 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:05 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:16:42:05 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:42:05 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:16:42:05 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 722
************ - - [22/May/2025:16:42:05 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 716
************ - - [22/May/2025:16:42:08 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:08 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:08 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:08 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:08 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:08 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:16:42:08 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:42:08 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:16:42:09 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:16:42:09 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:16:42:11 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:11 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:11 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:11 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:11 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:11 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:16:42:11 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:42:11 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:16:42:11 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 716
************ - - [22/May/2025:16:42:11 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 722
************ - - [22/May/2025:16:42:12 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:12 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:12 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:12 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:12 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:12 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:16:42:12 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:42:12 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:16:42:12 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:16:42:12 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:42:14 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:14 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:14 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:14 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:14 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:42:15 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:16:42:15 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:42:15 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:16:42:15 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:16:42:15 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:16:43:20 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:20 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:20 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:20 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:20 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:20 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 250
************ - - [22/May/2025:16:43:20 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:43:20 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:43:20 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1216
************ - - [22/May/2025:16:43:20 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:16:43:21 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:21 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:21 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:21 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:21 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:21 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 253
************ - - [22/May/2025:16:43:21 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:43:21 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:43:22 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:16:43:22 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:16:43:23 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:23 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:23 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:23 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:23 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:23 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:16:43:23 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:43:23 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:43:23 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1216
************ - - [22/May/2025:16:43:23 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:16:43:25 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:25 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:25 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:25 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:25 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:25 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 250
************ - - [22/May/2025:16:43:25 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:43:25 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:43:25 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1216
************ - - [22/May/2025:16:43:25 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:16:43:29 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:29 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:29 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:29 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:29 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:43:29 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 253
************ - - [22/May/2025:16:43:30 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:43:30 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:43:30 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:16:43:30 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:16:44:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747903497291 HTTP/1.1" 200 -
************ - - [22/May/2025:16:44:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747903497291 HTTP/1.1" 200 160
************ - - [22/May/2025:16:45:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747903520907 HTTP/1.1" 200 -
************ - - [22/May/2025:16:45:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747903520907 HTTP/1.1" 200 160
************* - - [22/May/2025:16:46:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747903569503 HTTP/1.1" 200 -
************* - - [22/May/2025:16:46:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747903569503 HTTP/1.1" 200 160
************ - - [22/May/2025:16:49:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747903797296 HTTP/1.1" 200 -
************ - - [22/May/2025:16:49:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747903797296 HTTP/1.1" 200 160
************ - - [22/May/2025:16:51:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747903917173 HTTP/1.1" 200 -
************ - - [22/May/2025:16:51:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747903917173 HTTP/1.1" 200 160
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:54:42&etm=&_timer304=1747904082537 HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747904082537 HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747904082537 HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747904082537 HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747904082537 HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:42 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:43 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:16:54:43 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:16:54:43 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:16:54:43 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:16:54:43 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:54:43 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:54:44 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747904082537 HTTP/1.1" 200 13016
************ - - [22/May/2025:16:54:46 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:54:46 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747904082537 HTTP/1.1" 200 164
************ - - [22/May/2025:16:54:46 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:54:42&etm=&_timer304=1747904082537 HTTP/1.1" 200 156
************ - - [22/May/2025:16:54:46 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747904082537 HTTP/1.1" 200 166
************ - - [22/May/2025:16:54:46 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:54:46 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747904082537 HTTP/1.1" 200 169
************ - - [22/May/2025:16:54:46 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:16:54:47 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:47 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:47 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:47 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:47 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:47 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:16:54:47 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:54:47 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:16:54:47 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:16:54:47 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:16:54:49 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:49 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:49 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:49 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:49 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:49 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:16:54:49 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:54:49 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:16:54:49 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 722
************ - - [22/May/2025:16:54:49 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 716
************ - - [22/May/2025:16:54:51 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:51 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:51 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:51 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:51 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:51 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:16:54:51 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:54:51 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:16:54:51 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:16:54:51 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:54:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904093166 HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904093166 HTTP/1.1" 200 160
************ - - [22/May/2025:16:54:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904097162 HTTP/1.1" 200 -
************ - - [22/May/2025:16:54:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904097162 HTTP/1.1" 200 160
************* - - [22/May/2025:16:55:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904116638 HTTP/1.1" 200 -
************* - - [22/May/2025:16:55:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904116638 HTTP/1.1" 200 160
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:59:11&etm=&_timer304=1747904351357 HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747904351358 HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747904351358 HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747904351358 HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747904351358 HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:11 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:16:59:11 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:16:59:11 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+16:59:11&etm=&_timer304=1747904351357 HTTP/1.1" 200 156
************ - - [22/May/2025:16:59:11 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:16:59:11 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:59:11 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:16:59:11 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:59:11 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+17:00&filterCnt=6&_timer304=1747904351358 HTTP/1.1" 200 164
************ - - [22/May/2025:16:59:11 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747904351358 HTTP/1.1" 200 166
************ - - [22/May/2025:16:59:11 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:16:59:11 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:16:59:11 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747904351358 HTTP/1.1" 200 169
************ - - [22/May/2025:16:59:11 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:59:11 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747904351358 HTTP/1.1" 200 13016
************ - - [22/May/2025:16:59:19 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:19 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:19 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:19 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:19 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:19 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:16:59:19 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:16:59:19 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:16:59:19 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:16:59:19 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:16:59:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904361120 HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904361120 HTTP/1.1" 200 160
************ - - [22/May/2025:16:59:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904397168 HTTP/1.1" 200 -
************ - - [22/May/2025:16:59:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904397168 HTTP/1.1" 200 160
************* - - [22/May/2025:17:00:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904461705 HTTP/1.1" 200 -
************* - - [22/May/2025:17:00:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904461705 HTTP/1.1" 200 160
************ - - [22/May/2025:17:04:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904697164 HTTP/1.1" 200 -
************ - - [22/May/2025:17:04:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904697164 HTTP/1.1" 200 160
************* - - [22/May/2025:17:09:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904952687 HTTP/1.1" 200 -
************* - - [22/May/2025:17:09:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904952687 HTTP/1.1" 200 160
************ - - [22/May/2025:17:09:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904951124 HTTP/1.1" 200 -
************ - - [22/May/2025:17:09:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904951124 HTTP/1.1" 200 160
************ - - [22/May/2025:17:09:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747904997170 HTTP/1.1" 200 -
************ - - [22/May/2025:17:09:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747904997170 HTTP/1.1" 200 160
************ - - [22/May/2025:17:14:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747905251123 HTTP/1.1" 200 -
************ - - [22/May/2025:17:14:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747905251123 HTTP/1.1" 200 160
************ - - [22/May/2025:17:14:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747905297173 HTTP/1.1" 200 -
************ - - [22/May/2025:17:14:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747905297173 HTTP/1.1" 200 160
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+17:16:06&etm=&_timer304=1747905366594 HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747905366594 HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+18:00&filterCnt=6&_timer304=1747905366594 HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747905366594 HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:06 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747905366594 HTTP/1.1" 200 -
************* - - [22/May/2025:17:16:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747905369573 HTTP/1.1" 200 -
************* - - [22/May/2025:17:16:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747905369573 HTTP/1.1" 200 160
************ - - [22/May/2025:17:16:07 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:17:16:07 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:17:16:07 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:07 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:17:16:07 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:17:16:07 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:17:16:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:17:16:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:17:16:10 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+17:16:06&etm=&_timer304=1747905366594 HTTP/1.1" 200 156
************ - - [22/May/2025:17:16:10 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+18:00&filterCnt=6&_timer304=1747905366594 HTTP/1.1" 200 164
************ - - [22/May/2025:17:16:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747905366594 HTTP/1.1" 200 166
************ - - [22/May/2025:17:16:10 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:17:16:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747905366594 HTTP/1.1" 200 169
************ - - [22/May/2025:17:16:10 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747905366594 HTTP/1.1" 200 13016
************ - - [22/May/2025:17:16:10 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:10 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:10 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:10 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:10 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:10 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:17:16:10 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:10 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:17:16:10 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 716
************ - - [22/May/2025:17:16:10 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 722
************ - - [22/May/2025:17:16:13 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:13 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:13 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:13 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:13 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:13 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:17:16:13 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 225
************ - - [22/May/2025:17:16:13 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 220
************ - - [22/May/2025:17:16:13 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:13 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:17:16:15 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:15 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:15 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:15 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:15 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:15 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:17:16:15 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:15 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:17:16:15 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:17:16:15 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:17:16:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747905376359 HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747905376359 HTTP/1.1" 200 160
************ - - [22/May/2025:17:16:17 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:17 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:17 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:17 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:17 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:17 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 250
************ - - [22/May/2025:17:16:17 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:17 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:17:16:17 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1216
************ - - [22/May/2025:17:16:17 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:17:16:19 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:19 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:19 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:19 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:19 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:19 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 253
************ - - [22/May/2025:17:16:19 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:19 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:17:16:19 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:17:16:19 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:17:16:20 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:20 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:20 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:20 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:20 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:20 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:17:16:20 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:20 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:17:16:21 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1216
************ - - [22/May/2025:17:16:21 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:17:16:32 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:32 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:32 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:32 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:32 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:32 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:17:16:32 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:32 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:17:16:32 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 722
************ - - [22/May/2025:17:16:32 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 716
************ - - [22/May/2025:17:16:34 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:34 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:34 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:34 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:34 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:34 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 249
************ - - [22/May/2025:17:16:34 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:34 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 452
************ - - [22/May/2025:17:16:34 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 457
************ - - [22/May/2025:17:16:34 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 510
************ - - [22/May/2025:17:16:36 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:36 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:36 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:36 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:36 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
************ - - [22/May/2025:17:16:36 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 252
************ - - [22/May/2025:17:16:36 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
************ - - [22/May/2025:17:16:36 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
************ - - [22/May/2025:17:16:36 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 1217
************ - - [22/May/2025:17:16:36 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 1211
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/ewci/base/mal/write/939?_timer304=1747905505319 HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+17:18:25&etm=&_timer304=1747905505556 HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747905505556 HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+18:00&filterCnt=6&_timer304=1747905505556 HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747905505556 HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747905505556 HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************ - - [22/May/2025:17:18:25 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+17:18:25&etm=&_timer304=1747905505556 HTTP/1.1" 200 156
************ - - [22/May/2025:17:18:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:17:18:25 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:17:18:25 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:17:18:25 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+18:00&filterCnt=6&_timer304=1747905505556 HTTP/1.1" 200 164
************ - - [22/May/2025:17:18:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:17:18:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747905505556 HTTP/1.1" 200 166
************ - - [22/May/2025:17:18:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747905505556 HTTP/1.1" 200 169
************ - - [22/May/2025:17:18:25 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747905505556 HTTP/1.1" 200 13016
************ - - [22/May/2025:17:18:26 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:17:18:26 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 4232
************ - - [22/May/2025:17:18:28 +0800] "GET /api/ewci/base/mal/write/939?_timer304=1747905505319 HTTP/1.1" 200 146
************ - - [22/May/2025:17:19:04 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:19:04 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:17:19:05 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:19:05 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:17:19:05 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:19:05 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:17:19:07 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:19:07 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 388
************ - - [22/May/2025:17:19:09 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:19:09 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:17:19:10 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:19:10 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************* - - [22/May/2025:17:20:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747905656874 HTTP/1.1" 200 -
************* - - [22/May/2025:17:20:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747905656874 HTTP/1.1" 200 160
************ - - [22/May/2025:17:21:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747905667178 HTTP/1.1" 200 -
************ - - [22/May/2025:17:21:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747905667178 HTTP/1.1" 200 160
************* - - [22/May/2025:17:25:49 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747905951805 HTTP/1.1" 200 -
************* - - [22/May/2025:17:25:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747905951805 HTTP/1.1" 200 160
************ - - [22/May/2025:17:26:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747905967169 HTTP/1.1" 200 -
************ - - [22/May/2025:17:26:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747905967169 HTTP/1.1" 200 160
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+17:29:41&etm=&_timer304=1747906181042 HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747906181042 HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+18:00&filterCnt=6&_timer304=1747906181042 HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747906181042 HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747906181042 HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [22/May/2025:17:29:42 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 392
************ - - [22/May/2025:17:29:42 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 4232
************ - - [22/May/2025:17:29:42 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747906181042 HTTP/1.1" 200 13016
************ - - [22/May/2025:17:29:44 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-19+17:29:41&etm=&_timer304=1747906181042 HTTP/1.1" 200 156
************ - - [22/May/2025:17:29:44 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-22+08:00&etm=2025-05-22+18:00&filterCnt=6&_timer304=1747906181042 HTTP/1.1" 200 164
************ - - [22/May/2025:17:29:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747906181042 HTTP/1.1" 200 166
************ - - [22/May/2025:17:29:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:17:29:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [22/May/2025:17:29:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747906181042 HTTP/1.1" 200 169
************ - - [22/May/2025:17:29:44 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [22/May/2025:17:29:47 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:47 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:17:29:48 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:48 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:17:29:49 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:49 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:17:29:49 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:49 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 1400
************ - - [22/May/2025:17:29:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747906190804 HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747906190804 HTTP/1.1" 200 160
************ - - [22/May/2025:17:29:53 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:53 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 390
************ - - [22/May/2025:17:29:55 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:55 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 1400
************ - - [22/May/2025:17:29:55 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:55 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 1400
************ - - [22/May/2025:17:29:56 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:56 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:17:29:57 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:57 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 8084
************ - - [22/May/2025:17:29:58 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:58 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 245
************ - - [22/May/2025:17:29:58 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:58 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 245
************ - - [22/May/2025:17:29:59 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:29:59 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 1400
************ - - [22/May/2025:17:30:01 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:30:01 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 1523
************ - - [22/May/2025:17:30:02 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:30:02 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 1523
************ - - [22/May/2025:17:30:04 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:30:04 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 387
************ - - [22/May/2025:17:30:05 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:30:05 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 390
************ - - [22/May/2025:17:30:08 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:30:08 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 955
************ - - [22/May/2025:17:30:09 +0800] "OPTIONS /api/call/inspect/select-duty-person-list HTTP/1.1" 200 -
************ - - [22/May/2025:17:30:09 +0800] "POST /api/call/inspect/select-duty-person-list HTTP/1.1" 200 390
************* - - [22/May/2025:17:30:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747906242900 HTTP/1.1" 200 -
************* - - [22/May/2025:17:30:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747906242900 HTTP/1.1" 200 160
