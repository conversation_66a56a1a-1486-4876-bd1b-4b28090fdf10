package com.huitu.cloud.api.screen.fhjz.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value="堤防工程详情对象", description="堤防工程详情对象")
public class DikeDetailVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "堤防编号")
    private String dikeCode;

    @ApiModelProperty(value = "堤防名称")
    private String dikeName;

    @ApiModelProperty(value = "所属河流")
    private String rvName;

    @ApiModelProperty(value = "岸别 0：不分  1：左  2：右")
    private String bank;

    @ApiModelProperty(value = "起点所在位置")
    private String startLoc;

    @ApiModelProperty(value = "终点所在位置")
    private String endLoc;

    @ApiModelProperty(value = "堤防型式")
    private String dikePatt;

    @ApiModelProperty(value = "堤防级别 0：未知 1:1级 2:2级 3：3级 4:4级 5:5级 9:5级以下")
    private String dikeGrad;

    @ApiModelProperty(value = "达标长度")
    private String flctsdln;

    @ApiModelProperty(value = "堤防长度 单位：KM")
    private String dikeLen;

    @ApiModelProperty(value = "规划标准")
    private String planStandard;

    @ApiModelProperty(value = "堤防高度（最大值）")
    private String dikeHeigMax;

    @ApiModelProperty(value = "堤防高度（最小值）")
    private String dikeHeigMin;

    @ApiModelProperty(value = "堤防宽度（最大值）")
    private String dikeTopWidMax;

    @ApiModelProperty(value = "堤防宽度（最小值）")
    private String dikeTopWidMin;

    @ApiModelProperty(value = "涵闸个数")
    private String cnclgtnb;

    public String getDikeCode() {
        return dikeCode;
    }

    public void setDikeCode(String dikeCode) {
        this.dikeCode = dikeCode;
    }

    public String getDikeName() {
        return dikeName;
    }

    public void setDikeName(String dikeName) {
        this.dikeName = dikeName;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getStartLoc() {
        return startLoc;
    }

    public void setStartLoc(String startLoc) {
        this.startLoc = startLoc;
    }

    public String getEndLoc() {
        return endLoc;
    }

    public void setEndLoc(String endLoc) {
        this.endLoc = endLoc;
    }

    public String getDikePatt() {
        return dikePatt;
    }

    public void setDikePatt(String dikePatt) {
        this.dikePatt = dikePatt;
    }

    public String getDikeGrad() {
        return dikeGrad;
    }

    public void setDikeGrad(String dikeGrad) {
        this.dikeGrad = dikeGrad;
    }

    public String getFlctsdln() {
        return flctsdln;
    }

    public void setFlctsdln(String flctsdln) {
        this.flctsdln = flctsdln;
    }

    public String getDikeLen() {
        return dikeLen;
    }

    public void setDikeLen(String dikeLen) {
        this.dikeLen = dikeLen;
    }

    public String getPlanStandard() {
        return planStandard;
    }

    public void setPlanStandard(String planStandard) {
        this.planStandard = planStandard;
    }

    public String getDikeHeigMax() {
        return dikeHeigMax;
    }

    public void setDikeHeigMax(String dikeHeigMax) {
        this.dikeHeigMax = dikeHeigMax;
    }

    public String getDikeHeigMin() {
        return dikeHeigMin;
    }

    public void setDikeHeigMin(String dikeHeigMin) {
        this.dikeHeigMin = dikeHeigMin;
    }

    public String getDikeTopWidMax() {
        return dikeTopWidMax;
    }

    public void setDikeTopWidMax(String dikeTopWidMax) {
        this.dikeTopWidMax = dikeTopWidMax;
    }

    public String getDikeTopWidMin() {
        return dikeTopWidMin;
    }

    public void setDikeTopWidMin(String dikeTopWidMin) {
        this.dikeTopWidMin = dikeTopWidMin;
    }

    public String getCnclgtnb() {
        return cnclgtnb;
    }

    public void setCnclgtnb(String cnclgtnb) {
        this.cnclgtnb = cnclgtnb;
    }
}
