package com.huitu.cloud.api.syq.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 强降雨告警信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-7-29
 */
@ApiModel(value="HeavyRainfallAlarm对象", description="强降雨告警信息")
public class HeavyRainfallAlarm implements Serializable {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "预警描述")
    private String alarmdesc;
    @ApiModelProperty(value = "市级政区名称")
    private String sadnm;
    @ApiModelProperty(value = "测站编码")
    private String stcd;
    @ApiModelProperty(value = "测站编码列表")
    private List<String> stcds = new ArrayList();
    @ApiModelProperty(value = "备注")
    private String reamrk;
    @ApiModelProperty(value = "站类")
    private String stadtp;


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAlarmdesc() {
        return alarmdesc;
    }

    public void setAlarmdesc(String alarmdesc) {
        this.alarmdesc = alarmdesc;
    }

    public String getSadnm() {
        return sadnm;
    }

    public void setSadnm(String sadnm) {
        this.sadnm = sadnm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public List<String> getStcds() {
        return stcds;
    }

    public void setStcds(List<String> stcds) {
        this.stcds = stcds;
    }

    public String getReamrk() {
        return reamrk;
    }

    public void setReamrk(String reamrk) {
        this.reamrk = reamrk;
    }

    public String getStadtp() {
        return stadtp;
    }

    public void setStadtp(String stadtp) {
        this.stadtp = stadtp;
    }

}
