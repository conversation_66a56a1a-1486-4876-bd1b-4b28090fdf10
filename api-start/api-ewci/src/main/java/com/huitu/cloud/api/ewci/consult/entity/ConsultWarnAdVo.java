package com.huitu.cloud.api.ewci.consult.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description: 预警查看-24h预警-山洪预警实体
 * @author: jiangjy
 * @create: 2024-4-17
 **/
@ApiModel(value = "预警查看-24h预警-山洪预警实体")
public class ConsultWarnAdVo {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "山洪预警：准备转移，水库预警：1级，河道预警：1级")
    private Integer type1;

    @ApiModelProperty(value = "山洪预警：立即转移，水库预警：2级，河道预警：2级")
    private Integer type2;

    @ApiModelProperty(value = "水库预警：3级，河道预警：4级")
    private Integer type3;

    @ApiModelProperty(value = "水库预警：3级，河道预警：4级")
    private Integer type4;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getType1() {
        return type1;
    }

    public void setType1(Integer type1) {
        this.type1 = type1;
    }

    public Integer getType2() {
        return type2;
    }

    public void setType2(Integer type2) {
        this.type2 = type2;
    }

    public Integer getType3() {
        return type3;
    }

    public void setType3(Integer type3) {
        this.type3 = type3;
    }

    public Integer getType4() {
        return type4;
    }

    public void setType4(Integer type4) {
        this.type4 = type4;
    }
}
