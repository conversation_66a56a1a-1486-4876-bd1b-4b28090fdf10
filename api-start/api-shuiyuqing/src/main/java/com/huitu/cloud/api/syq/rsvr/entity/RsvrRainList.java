package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.huitu.cloud.api.syq.rain.entity.Rain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-3-6
 */
@TableName("RsvrRainList")
@ApiModel(value="RsvrRainList对象", description="")
public class RsvrRainList extends Model<RsvrRainList> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "水库编码")
    @TableField(value = "RES_CODE")
    private String resCode;

    @ApiModelProperty(value = "水库名称")
    @TableField("RES_NAME")
    private String resName;

    @ApiModelProperty(value = "水库规模(1:大一 2：大二  3：中 4：小一  5：小二)")
    @TableField("ENG_SCAL")
    private String engScal;

    @ApiModelProperty(value = "左下角经度")
    @TableField("LOW_LEFT_LONG")
    private Double lowLeftLong;

    @ApiModelProperty(value = "左下角纬度")
    @TableField("LOW_LEFT_LAT")
    private Double lowLeftLat;

    @ApiModelProperty(value = "右上角经度")
    @TableField("UP_RIGHT_LONG")
    private Double upRightLong;

    @ApiModelProperty(value = "右上角纬度")
    @TableField("UP_RIGHT_LAT")
    private Double upRightLat;

    @ApiModelProperty(value = "集雨面积")
    @TableField("WAT_SHED_AREA")
    private Double watShedArea;

    @ApiModelProperty(value = "政区编码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    @TableField("ADNM")
    private String adnm;

    @ApiModelProperty(value = "平均雨量")
    @TableField("AVG_DRPS")
    private BigDecimal avgDrps;

    @ApiModelProperty(value = "最大雨量")
    @TableField("MAX_DRPS")
    private BigDecimal maxDrps;

    @ApiModelProperty(value = "前期24小时")
    @TableField("AVG_LDRPS")
    private BigDecimal avgLdrps;

    @ApiModelProperty(value = "未来3小时")
    @TableField("AVG_F3DRPS")
    private BigDecimal avgF3drps;

    @ApiModelProperty(value = "未来2小时-彩云")
    @TableField("AVG_F24DRPS")
    private BigDecimal avgF24drps;

    @ApiModelProperty(value = "临时编码")
    @TableField("TEMP_CODE")
    private String tempCode;

    @ApiModelProperty(value = "径流系数")
    @TableField("RC")
    private Double rc;

    @ApiModelProperty(value = "预估来水量")
    @TableField("W")
    private Double w;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "测站降雨列表")
    private List<Rain> children;

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getEngScal() {
        return engScal;
    }

    public void setEngScal(String engScal) {
        this.engScal = engScal;
    }

    public BigDecimal getAvgDrps() {
        return avgDrps;
    }

    public void setAvgDrps(BigDecimal avgDrps) {
        this.avgDrps = avgDrps;
    }

    public BigDecimal getMaxDrps() {
        return maxDrps;
    }

    public void setMaxDrps(BigDecimal maxDrps) {
        this.maxDrps = maxDrps;
    }

    public BigDecimal getAvgF3drps() {
        return avgF3drps;
    }

    public void setAvgF3drps(BigDecimal avgF3drps) {
        this.avgF3drps = avgF3drps;
    }

    public BigDecimal getAvgF24drps() {
        return avgF24drps;
    }

    public void setAvgF24drps(BigDecimal avgF24drps) {
        this.avgF24drps = avgF24drps;
    }

    public BigDecimal getAvgLdrps() {
        return avgLdrps;
    }

    public void setAvgLdrps(BigDecimal avgLdrps) {
        this.avgLdrps = avgLdrps;
    }

    public Double getLowLeftLong() {
        return lowLeftLong;
    }

    public void setLowLeftLong(Double lowLeftLong) {
        this.lowLeftLong = lowLeftLong;
    }

    public Double getLowLeftLat() {
        return lowLeftLat;
    }

    public void setLowLeftLat(Double lowLeftLat) {
        this.lowLeftLat = lowLeftLat;
    }

    public Double getUpRightLong() {
        return upRightLong;
    }

    public void setUpRightLong(Double upRightLong) {
        this.upRightLong = upRightLong;
    }

    public Double getUpRightLat() {
        return upRightLat;
    }

    public void setUpRightLat(Double upRightLat) {
        this.upRightLat = upRightLat;
    }

    public Double getWatShedArea() {
        return watShedArea;
    }

    public void setWatShedArea(Double watShedArea) {
        this.watShedArea = watShedArea;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getTempCode() {
        return tempCode;
    }

    public void setTempCode(String tempCode) {
        this.tempCode = tempCode;
    }

    public Double getRc() {
        return rc;
    }

    public void setRc(Double rc) {
        this.rc = rc;
    }

    public Double getW() {
        return w;
    }

    public void setW(Double w) {
        this.w = w;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public List<Rain> getChildren() {
        return children;
    }

    public void setChildren(List<Rain> children) {
        this.children = children;
    }
}
