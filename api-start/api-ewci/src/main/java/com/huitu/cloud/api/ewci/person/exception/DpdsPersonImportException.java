package com.huitu.cloud.api.ewci.person.exception;

import com.huitu.cloud.api.ewci.person.entity.BnsDpdspersonTmp;

import java.util.List;

/**
 * 险工险段责任人信息导入异常
 *
 * <AUTHOR>
 */
public class DpdsPersonImportException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    List<BnsDpdspersonTmp> errorData;

    public List<BnsDpdspersonTmp> getErrorData() {
        return errorData;
    }

    public void setErrorData(List<BnsDpdspersonTmp> errorData) {
        this.errorData = errorData;
    }

    public DpdsPersonImportException(String message) {
        super(message);
    }

    public DpdsPersonImportException(String message, List<BnsDpdspersonTmp> errorData) {
        super(message);

        this.errorData = errorData;
    }

    public DpdsPersonImportException(String message, Throwable cause) {
        super(message, cause);
    }

    public DpdsPersonImportException(String message, List<BnsDpdspersonTmp> errorData, Throwable cause) {
        super(message, cause);

        this.errorData = errorData;
    }
}
