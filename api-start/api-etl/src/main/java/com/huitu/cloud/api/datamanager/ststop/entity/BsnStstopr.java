package com.huitu.cloud.api.datamanager.ststop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 站点屏蔽记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@TableName("BSN_STSTOP_R")
@ApiModel(value="BsnStstopR对象", description="站点屏蔽记录表")
public class BsnStstopr extends Model<BsnStstopr> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "站码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "时间")
    @TableField("TM")
    private Date tm;

    @ApiModelProperty(value = "状态：0停用，1启用")
    @TableField("STSTA")
    private String ststa;

    @ApiModelProperty(value = "用户")
    @TableField("USERNM")
    private String usernm;

    @ApiModelProperty(value = "站点屏蔽类型")
    @TableField("STSTTP")
    private String ststtp;

    @ApiModelProperty(value = "屏蔽要素 雨量：st_pptn_r , 河道水位：st_river_r, 水库水位：st_rsvr_r")
    private String tbnm;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }


    public Date getTm() {
        return tm;
    }

    public void setTm(Date tm) {
        this.tm = tm;
    }

    public String getStsta() {
        return ststa;
    }

    public void setStsta(String ststa) {
        this.ststa = ststa;
    }

    public String getUsernm() {
        return usernm;
    }

    public void setUsernm(String usernm) {
        this.usernm = usernm;
    }

    public String getStsttp() {
        return ststtp;
    }

    public void setStsttp(String ststtp) {
        this.ststtp = ststtp;
    }

    public String getTbnm() {
        return tbnm;
    }

    public void setTbnm(String tbnm) {
        this.tbnm = tbnm;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "BsnStstopR{" +
        "stcd=" + stcd +
        ", tm=" + tm +
        ", ststa=" + ststa +
        ", usernm=" + usernm +
        ", ststtp=" + ststtp +
        ", tbnm=" + tbnm +
        "}";
    }
}
