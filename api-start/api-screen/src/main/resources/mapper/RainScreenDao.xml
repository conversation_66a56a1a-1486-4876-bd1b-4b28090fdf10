<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.screen.fhjz.mapper.RainScreenDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>


    <select id="staticRainRealCount" resultType="com.huitu.cloud.api.screen.fhjz.entity.RainCountVo">
        select sum(t.leve1) leve1,
        sum(t.leve2) leve2,
        sum(t.leve3) leve3,
        sum(t.leve4) leve4,
        sum(t.leve5) leve5,
        sum(t.leve6) leve6
        from (
        select ba.stcd,
        case when  sum(pr.drp) &gt;= 0 and sum(pr.drp) &lt;= 10 then 1 else 0 end leve1,
        case when  sum(pr.drp) &gt; 10 and sum(pr.drp) &lt;= 25 then 1 else 0 end leve2,
        case when  sum(pr.drp) &gt; 25 and sum(pr.drp) &lt;= 50 then 1 else 0 end leve3,
        case when  sum(pr.drp) &gt; 50 and sum(pr.drp) &lt;= 100 then 1 else 0 end leve4,
        case when  sum(pr.drp) &gt; 100 and sum(pr.drp) &lt;= 250 then 1 else 0 end leve5,
        case when  sum(pr.drp) &gt; 250 then 1 else 0 end leve6
        from BSN_BAS_B bb
        left join BSN_BAS_ST ba on bb.bas_code = ba.bas_code
        inner join (select * from ST_PPTN_R where tm &gt; #{startTime} and tm &lt;= #{endTime} and drp &gt;= 0) pr on ba.stcd = pr.stcd
        where bb.bas_code=#{code}
        group by  ba.stcd
        ) t
    </select>

    <select id="staicRainForecastCount" resultType="com.huitu.cloud.api.screen.fhjz.entity.RainCountVo">
        select sum(t.leve1) leve1,
        sum(t.leve2) leve2,
        sum(t.leve3) leve3,
        sum(t.leve4) leve4,
        sum(t.leve5) leve5,
        sum(t.leve6) leve6
        from (
        select ba.stcd,
        case when  sum(pr.rain)&gt;= 0 and sum(pr.rain) &lt;= 10 then 1 else 0 end leve1,
        case when  sum(pr.rain)&gt; 10 and sum(pr.rain) &lt;= 25 then 1 else 0 end leve2,
        case when  sum(pr.rain)&gt; 25 and sum(pr.rain) &lt;= 50 then 1 else 0 end leve3,
        case when  sum(pr.rain)&gt; 50 and sum(pr.rain) &lt;= 100 then 1 else 0 end leve4,
        case when  sum(pr.rain)&gt; 100 and sum(pr.rain) &lt;= 250 then 1 else 0 end leve5,
        case when  sum(pr.rain)&gt; 250 then 1 else 0 end leve6
        from BSN_BAS_B bb
        left join BSN_BAS_ST ba on bb.bas_code = ba.bas_code
        inner join (select * from BSN_FORECASTRAIN_F
        where rain_time &gt; #{startTime}
        and rain_time &lt;= #{endTime}
        and rain is not null) pr on ba.stcd = pr.stcd
        where bb.bas_code=#{code}
        group by  ba.stcd
        ) t
    </select>

    <select id="staticProRainRealCount" resultType="com.huitu.cloud.api.screen.fhjz.entity.RainCountVo">
        select sum(t.leve1) leve1,
        sum(t.leve2) leve2,
        sum(t.leve3) leve3,
        sum(t.leve4) leve4,
        sum(t.leve5) leve5,
        sum(t.leve6) leve6
        from (
        SELECT t1.stcd,
        case when  sum(pr.drp)&gt;= 0 and sum(pr.drp)&lt;= 10 then 1 else 0 end leve1,
        case when  sum(pr.drp)&gt; 10 and sum(pr.drp) &lt;= 25 then 1 else 0 end leve2,
        case when  sum(pr.drp)&gt; 25 and sum(pr.drp) &lt;= 50 then 1 else 0 end leve3,
        case when  sum(pr.drp)&gt; 50 and sum(pr.drp) &lt;= 100 then 1 else 0 end leve4,
        case when  sum(pr.drp)&gt; 100 and sum(pr.drp) &lt;= 250 then 1 else 0 end leve5,
        case when  sum(pr.drp)&gt;250 then 1 else 0 end leve6
        FROM (select STCD from ST_STBPRP_B WHERE left(addvcd,2) ='22' and STTP IN ('PP','ZQ','RQ')) T1
        INNER JOIN  (select * from ST_PPTN_R where tm &gt; #{startTime} and tm &lt;= #{endTime} and drp &gt;= 0) pr
        ON pr.stcd=t1.stcd
        group by  t1.stcd
        ) t
    </select>

    <select id="staticProRainForecastCount" resultType="com.huitu.cloud.api.screen.fhjz.entity.RainCountVo">
        select sum(t.leve1) leve1,
        sum(t.leve2) leve2,
        sum(t.leve3) leve3,
        sum(t.leve4) leve4,
        sum(t.leve5) leve5,
        sum(t.leve6) leve6
        from (
        SELECT t1.stcd,
        case when  sum(pr.rain)&gt;= 0 and sum(pr.rain)&lt;= 10 then 1 else 0 end leve1,
        case when  sum(pr.rain)&gt; 10 and sum(pr.rain)&lt;= 25 then 1 else 0 end leve2,
        case when  sum(pr.rain)&gt; 25 and sum(pr.rain)&lt;= 50 then 1 else 0 end leve3,
        case when  sum(pr.rain)&gt; 50 and sum(pr.rain) &lt;= 100 then 1 else 0 end leve4,
        case when  sum(pr.rain)&gt; 100 and sum(pr.rain) &lt;= 250 then 1 else 0 end leve5,
        case when  sum(pr.rain)&gt; 250 then 1 else 0 end leve6
        FROM (select STCD from ST_STBPRP_B WHERE left(addvcd,2) ='22' and STTP IN ('PP','ZQ','RQ')) T1
        INNER JOIN  (select * from BSN_FORECASTRAIN_F where rain_time &gt;  #{startTime}  and rain_time &lt;= #{endTime} and rain &gt;= 0) pr
        ON pr.stcd=t1.stcd
        group by  t1.stcd
        ) t
    </select>

    <select id="statisticsRealRainValueByPro" resultType="map">
        select
        'pro' pro,
        CASE WHEN ( SUM ( t.drp ) / COUNT ( 1 ) ) IS NULL THEN 0 ELSE ( SUM ( t.drp ) / COUNT ( 1 ) )  END value
        from (
        SELECT t1.stcd,
        sum(pr.drp) drp
        FROM (select STCD from ST_STBPRP_B WHERE left(addvcd,2) ='22' and STTP IN ('PP','ZQ','RQ')) T1
        INNER JOIN  (select * from ST_PPTN_R where tm &gt; #{startTime} and tm &lt;= #{endTime} and drp &gt;= 0) pr
        ON pr.stcd=t1.stcd
        group by  t1.stcd
        ) t
    </select>

    <select id="statisticsRealRainValueByLy" resultType="map">
        select t1.bas_code,
				isnull(t2.value,0) value
        from (select t.bas_code from BSN_BAS_B t where t.bas_code in ('WAB0000','WBA0000','WAE0000','WBD0000','WAD0000')) t1
        left join (
            select t.bas_code,
            CASE WHEN ( SUM ( t.drp ) / COUNT ( 1 ) ) IS NULL THEN 0 ELSE ( SUM ( t.drp ) / COUNT ( 1 ) )  END value
            from (
            select bb.bas_code,
            ba.stcd,
            sum(pr.drp) drp
            from BSN_BAS_B bb
            left join BSN_BAS_ST ba on bb.bas_code = ba.bas_code
            inner join (select * from ST_PPTN_R where tm &gt; #{startTime} and tm &lt;= #{endTime} and drp &gt;= 0) pr on ba.stcd = pr.stcd
            where bb.bas_code in ('WAB0000','WBA0000','WAE0000','WBD0000','WAD0000')
            group by  bb.bas_code,ba.stcd
            ) t
            group by t.bas_code
        ) t2 on t1.bas_code = t2.bas_code
    </select>

    <select id="statisticsForecastRainValueByPro" resultType="map">
        select
        'pro' pro,
        CASE WHEN ( SUM ( t.rain ) / COUNT ( 1 ) ) IS NULL THEN 0 ELSE ( SUM ( t.rain ) / COUNT ( 1 ) )  END value
        from (
        SELECT t1.stcd,
        sum(pr.rain) rain
        FROM (select STCD from ST_STBPRP_B WHERE left(addvcd,2) ='22' and STTP IN ('PP','ZQ','RQ')) T1
        INNER JOIN  (select * from BSN_FORECASTRAIN_F where rain_time &gt;  #{startTime} and rain_time &lt;= #{endTime} and rain &gt;= 0) pr
        ON pr.stcd=t1.stcd
        group by  t1.stcd
        ) t
    </select>

    <select id="statisticsForecastRainValueByLy" resultType="map">
        select t1.bas_code,
               isnull(t2.value,0) value
        from (select t.bas_code from BSN_BAS_B t	where  t.bas_code in ('WAB0000','WBA0000','WAE0000','WBD0000','WAD0000')) t1
        left join (
            select t.bas_code,
            CASE WHEN ( SUM ( t.rain ) / COUNT ( 1 ) ) IS NULL THEN 0 ELSE ( SUM ( t.rain ) / COUNT ( 1 ) )  END value
            from (
            select bb.bas_code,
            ba.stcd,
            sum(pr.rain) rain
            from BSN_BAS_B bb
            left join BSN_BAS_ST ba on bb.bas_code = ba.bas_code
            inner join (select * from BSN_FORECASTRAIN_F where rain_time &gt;  #{startTime} and rain_time &lt;= #{endTime} and rain &gt;= 0) pr on ba.stcd = pr.stcd
            where bb.bas_code in ('WAB0000','WBA0000','WAE0000','WBD0000','WAD0000')
            group by  bb.bas_code,ba.stcd
            ) t
            group by t.bas_code
        ) t2 on t1.bas_code = t2.bas_code
    </select>

    <select id="queryProRainList" resultType="com.huitu.cloud.api.screen.fhjz.entity.RainVo">
        SELECT t1.stcd,
            t1.stnm,
            t2.PLGTD,
            t2.PLTTD,
            t4.STADTP,
            t2.adcd,
            (select adnm from MDT_ADCDINFO_B b where left(t2.adcd,4)+'00000000000' = b.adcd) sname,
            (select adnm from MDT_ADCDINFO_B b where left(t2.adcd,6)+'000000000' = b.adcd) xname,
            isnull(pr.drp,0) drp,
            isnull(ff.rain,0) rain
        FROM (select STCD,stnm,LGTD,LTTD from ST_STBPRP_B WHERE left(addvcd,2) ='22' and STTP IN ('PP','ZQ','RQ')) T1
        left join BSN_STBPRP_V t4 on T1.stcd = t4.stcd
        inner join BSN_STADTP_B t2 on t1.stcd=t2.stcd
        left join (select stcd,sum(drp) drp from ST_PPTN_R where tm &gt; #{rStartTime} and tm &lt;= #{rEndtTime} and drp > 0 group by stcd ) pr on pr.stcd = t1.stcd
        left JOIN  (select stcd,sum(rain) rain from BSN_FORECASTRAIN_F where rain_time &gt; #{fStartTime} and rain_time &lt;= #{fEndtTime} and rain > 0 group by stcd) ff
        ON ff.stcd=t1.stcd
        where CHARINDEX(t4.STADTP,#{stType})>0
        <if test="isAnalys">
        and (pr.drp &gt; 0 or ff.rain &gt; 0)
        </if>
        order by isnull(pr.drp,0) desc

    </select>

    <select id="querylyRainList" resultType="com.huitu.cloud.api.screen.fhjz.entity.RainVo">
        SELECT t1.stcd,
                t1.stnm,
                t2.PLGTD,
                t2.PLTTD,
                t4.STADTP,
                t2.adcd,
                (select adnm from MDT_ADCDINFO_B b where left(t2.adcd,4)+'00000000000' = b.adcd) sname,
                (select adnm from MDT_ADCDINFO_B b where left(t2.adcd,6)+'000000000' = b.adcd) xname,
                isnull(pr.drp,0) drp,
                isnull(ff.rain,0) rain
        FROM (
            select ba.stcd,sb.stnm,sb.LGTD,sb.LTTD
            from BSN_BAS_B bb
            left join BSN_BAS_ST ba on bb.bas_code = ba.bas_code
            inner join ST_STBPRP_B sb on sb.stcd=ba.stcd
            where bb.bas_code = #{code}
        ) T1
        left join BSN_STBPRP_V t4 on T1.stcd = t4.stcd
        inner join BSN_STADTP_B t2 on t1.stcd=t2.stcd
        left join (select stcd,sum(drp) drp from ST_PPTN_R where tm &gt; #{rStartTime} and tm &lt;= #{rEndtTime}  and drp >0 group by stcd) pr on pr.stcd = t1.stcd
        left JOIN  (select stcd,sum(rain) rain from BSN_FORECASTRAIN_F where rain_time &gt; #{fStartTime} and rain_time &lt;= #{fEndtTime}  and rain > 0 group by stcd) ff
        ON ff.stcd=t1.stcd
        where CHARINDEX(t4.STADTP,#{stType})>0
        <if test="isAnalys">
            and (pr.drp &gt; 0 or ff.rain &gt; 0)
        </if>
        ORDER BY isnull(pr.drp,0) DESC
    </select>
    <select id="statisticSoilValue" resultType="map">
        select CASE WHEN t1.CODE = '22000000000000000' THEN 'pro' else t1.CODE end pro ,
        t2.SLM10,t2.SLM20,t2.SLM40,sst.STNM
        from (
        select  t1.code,t1.STCD,max(tm) maxtm
        from BNS_SOIL_MOISTURE t1
        left join (select * from ST_SOIL_R where tm &gt;= #{startTime} and tm &lt;= #{endTime}) t2 on t1.stcd = t2.stcd
        group by t1.code,t1.STCD
        ) t1
        left join ST_SOIL_R t2 on t1.stcd = t2.stcd and t1.maxtm = t2.tm
        LEFT JOIN ST_STBPRP_B sst ON sst.stcd = t1.stcd
    </select>
</mapper>
