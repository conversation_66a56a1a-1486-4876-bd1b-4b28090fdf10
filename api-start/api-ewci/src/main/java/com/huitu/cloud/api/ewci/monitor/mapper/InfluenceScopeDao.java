package com.huitu.cloud.api.ewci.monitor.mapper;

import com.huitu.cloud.api.ewci.monitor.entity.FloodPerson;
import com.huitu.cloud.api.ewci.monitor.entity.InfluenceScope;
import com.huitu.cloud.api.ewci.monitor.entity.RsvrPerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 影响范围Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface InfluenceScopeDao {

    /**
     * 获取影响范围汇总统计信息
     *
     * @param adcd 政区编码
     * @return 汇总统计信息
     **/
    InfluenceScope getStatData(String adcd);

    /**
     * 获取完整的政区名称
     *
     * @param adcd  政区编码
     * @param adlvl 显示到第几级
     * @return 完整的政区名称
     **/
    String getFullAdnm(@Param("adcd") String adcd, @Param("adlvl") int adlvl);

    /**
     * 获取山洪责任人列表
     *
     * @param adcd 政区编码
     * @return 山洪责任人列表
     **/
    List<FloodPerson> getFloodPersonList(String adcd);

    /**
     * 获取水库责任人列表
     *
     * @param stcd 测站编码
     * @return 责任人列表
     **/
    List<RsvrPerson> getRsvrPersonList(String stcd);
}
