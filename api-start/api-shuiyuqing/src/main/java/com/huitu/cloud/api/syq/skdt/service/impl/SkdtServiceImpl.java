package com.huitu.cloud.api.syq.skdt.service.impl;

import com.huitu.cloud.api.syq.rain.entity.Rain;
import com.huitu.cloud.api.syq.rain.mapper.RainDao;
import com.huitu.cloud.api.syq.river.mapper.StRiverDao;
import com.huitu.cloud.api.syq.rsvr.entity.StRsvrR;
import com.huitu.cloud.api.syq.rsvr.mapper.RsvrDao;
import com.huitu.cloud.api.syq.skdt.entity.*;
import com.huitu.cloud.api.syq.skdt.service.SkdtService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 水库调度接口实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@Service
public class SkdtServiceImpl implements SkdtService {
    @Autowired
    private RainDao rainDao;
    @Autowired
    private RsvrDao rsvrDao;
    @Autowired
    private StRiverDao riverDao;

    @Override
    public List<BsnSkdtRvEx> getSkdtRsvrLatestInfo(String stm, String etm) {
        List<BsnSkdtRvEx> list=rsvrDao.getSkdtRsvrInfo(stm,etm,null);
        return list;
    }

    @Override
    public List<RsvrAvgRain> getSkdtRsvrRainInfo(String stm, String etm) {
        List<RsvrAvgRain> list=rainDao.getSkdtRsvrRainInfo(stm,etm);
        return list;
    }

    @Override
    public List<RsvrTmAvgRain> getSkdtRsvrRainTmInfo(String resCode, String stm, String etm) {
        List<RsvrTmAvgRain> list=rainDao.getSkdtRsvrRainTmInfo(resCode,stm,etm);
        return list;
    }

    @Override
    public List<RsvrRiverInfo> getSkdtRsvrRiverInfo(String stcd, String stm, String etm) {
        List<RsvrRiverInfo> list=riverDao.getSkdtRsvrRiverInfo(stcd,stm,etm);
        String rainSts= "";
        if(list.size()>0){
            for(RsvrRiverInfo x:list){
                if("1".equals(x.getPfl())){
                    rainSts="'"+x.getStcd()+"',";
                }
            }
            if(rainSts.length()>0){
                rainSts=rainSts.substring(rainSts.length()-1);
                Map<String,Object> param=new HashMap<>();
                Map<String,String> rainMap=new HashMap<>();
                List<Map<String,Object>> rainList=rainDao.getRainByStcds(rainSts,stm,etm);
                for(Map<String,Object> x:rainList){
                    rainMap.put(x.get("STCD").toString(),x.get("ACCP").toString());
                }
                list.forEach(x->{
                    if(rainMap.containsKey(x.getStcd())){
                        x.setDrps(rainMap.get(x.getStcd()));
                    }
                });
            }
        }
        return list;
    }

    @Override
    public SkInfo getInfoBySkStcd(String stcd, String stm, String etm) {
        Date date=new Date();
        List<BsnSkdtRvEx> list=rsvrDao.getSkdtRsvrInfo(stm,etm,stcd);
        Date date1=new Date();
        System.out.println(date1.getTime()-date.getTime());
        BsnSkdtRvEx bsnSkdtRvEx=list.get(0);

        //JSONObject json=JSONObject.fromObject(bsnSkdtRvEx);
        SkInfo skInfo= new SkInfo();
        skInfo.setTm(bsnSkdtRvEx.getTm());
        skInfo.setRz(bsnSkdtRvEx.getRz());
        skInfo.setResCode(bsnSkdtRvEx.getResCode());
        skInfo.setResName(bsnSkdtRvEx.getResName());
        skInfo.setW(bsnSkdtRvEx.getW());
        skInfo.setOtq(bsnSkdtRvEx.getOtq());
        skInfo.setInq(bsnSkdtRvEx.getInq());

        skInfo.setDrna(bsnSkdtRvEx.getDrna());
        skInfo.setFldcp(bsnSkdtRvEx.getFldcp());
        skInfo.setFsltdz(bsnSkdtRvEx.getFsltdz());
        skInfo.setNyDrp(bsnSkdtRvEx.getNyDrp());
        skInfo.setRzfsltdz(bsnSkdtRvEx.getRzfsltdz());
        skInfo.setUsablew(bsnSkdtRvEx.getUsablew());
        //水库最近一个月洪峰
        StRsvrR hf=rsvrDao.getHfRsvr(stcd);
        Date date2=new Date();
        System.out.println(date2.getTime()-date1.getTime());
        if(hf!=null){
            skInfo.setHftm(hf.getTm());
            skInfo.setHfinq(hf.getInq());
        }
        RsvrAvgRain rsvrAvgRain=rainDao.getSkdtRsvrRainInfoByResCode(stm,etm,bsnSkdtRvEx.getResCode());
        Date date3=new Date();
        System.out.println(date3.getTime()-date2.getTime());
        if(rsvrAvgRain!=null){
            skInfo.setAvgRain(rsvrAvgRain.getAvgRain());
            //计算产水量 产水量 ：(库区平均降雨量（mm）/1000)*集雨面积(km2)*1000000=库区平均降雨量**集雨面积*1000  单位m3 ；
            // 转化为百万m3公式 库区平均降雨量*集雨面积/1000
            if(skInfo.getDrna()!=null){
               BigDecimal rain= new BigDecimal(rsvrAvgRain.getAvgRain());
               BigDecimal dp=new BigDecimal(skInfo.getDrna());
               BigDecimal water=rain.multiply(dp).divide(new BigDecimal(1000));
               double result = water.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
               skInfo.setWaterYield(BigDecimal.valueOf(result));
            }
        }
        if(skInfo.getNyDrp()!=null){
            skInfo.setNyDrp(skInfo.getNyDrp().setScale(1,BigDecimal.ROUND_HALF_UP));
        }
        return skInfo;
    }

    @Override
    public List<RsvrRain> getSkdtRsvrRainInfo(String resCode, String stm, String etm) {
        List<RsvrRain> list=rainDao.getSkdtRsvrRainByResCode(resCode,stm,etm);
        return list;
    }
}
