package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * <p>
 * 重点城(集)镇调查评价汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@ApiModel(value="DcjzSummary对象", description="重点城(集)镇调查评价汇总")
public class DcjzSummary extends Model<DcjzSummary> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划码")
    @TableId(value = "adcd", type = IdType.NONE)
    private String adcd;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ZDADNM", type = IdType.NONE)
    private String zdadnm;

    @ApiModelProperty(value = "行政区名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "城镇居民户")
    @TableId(value = "HTCOUNT", type = IdType.NONE)
    private String htcount;

    @ApiModelProperty(value = "沿河居民户")
    @TableId(value = "YHCOUNT", type = IdType.NONE)
    private String yhcount;

    @ApiModelProperty(value = "历史山洪灾害个数")
    @TableId(value = "SHCOUNT", type = IdType.NONE)
    private String shcount;

    @ApiModelProperty(value = "企事业单位个数")
    @TableId(value = "QSYCOUNT", type = IdType.NONE)
    private String qsycount;

    @ApiModelProperty(value = "水库个数")
    @TableId(value = "SKCOUNT", type = IdType.NONE)
    private String skcount;

    @ApiModelProperty(value = "水闸个数")
    @TableId(value = "SZCOUNT", type = IdType.NONE)
    private String szcount;

    @ApiModelProperty(value = "堤防个数")
    @TableId(value = "DFCOUNT", type = IdType.NONE)
    private String dfcount;

    @ApiModelProperty(value = "路涵个数")
    @TableId(value = "LHCOUNT", type = IdType.NONE)
    private String lhcount;

    @ApiModelProperty(value = "桥梁个数")
    @TableId(value = "QLCOUNT", type = IdType.NONE)
    private String qlcount;

    @ApiModelProperty(value = "塘坝个数")
    @TableId(value = "TBCOUNT", type = IdType.NONE)
    private String tbcount;

    @ApiModelProperty(value = "自动监测站个数")
    @TableId(value = "ZDJCCOUNT", type = IdType.NONE)
    private String zdjccount;

    @ApiModelProperty(value = "无线预警广播站个数")
    @TableId(value = "WXGBCOUNT", type = IdType.NONE)
    private String wxgbcount;

    @ApiModelProperty(value = "简易雨量站个数")
    @TableId(value = "JYYLCOUNT", type = IdType.NONE)
    private String jyylcount;

    @ApiModelProperty(value = "简易水位站个数")
    @TableId(value = "JYSWCOUNT", type = IdType.NONE)
    private String jyswcount;

    @ApiModelProperty(value = "纵断面个数")
    @TableId(value = "ZDMCOUNT", type = IdType.NONE)
    private String zdmcount;

    @ApiModelProperty(value = "横断面个数")
    @TableId(value = "HDMCOUNT", type = IdType.NONE)
    private String hdmcount;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getZdadnm() {
        return zdadnm;
    }

    public void setZdadnm(String zdadnm) {
        this.zdadnm = zdadnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getHtcount() {
        return htcount;
    }

    public void setHtcount(String htcount) {
        this.htcount = htcount;
    }

    public String getYhcount() {
        return yhcount;
    }

    public void setYhcount(String yhcount) {
        this.yhcount = yhcount;
    }

    public String getShcount() {
        return shcount;
    }

    public void setShcount(String shcount) {
        this.shcount = shcount;
    }

    public String getQsycount() {
        return qsycount;
    }

    public void setQsycount(String qsycount) {
        this.qsycount = qsycount;
    }

    public String getSkcount() {
        return skcount;
    }

    public void setSkcount(String skcount) {
        this.skcount = skcount;
    }

    public String getSzcount() {
        return szcount;
    }

    public void setSzcount(String szcount) {
        this.szcount = szcount;
    }

    public String getDfcount() {
        return dfcount;
    }

    public void setDfcount(String dfcount) {
        this.dfcount = dfcount;
    }

    public String getLhcount() {
        return lhcount;
    }

    public void setLhcount(String lhcount) {
        this.lhcount = lhcount;
    }

    public String getQlcount() {
        return qlcount;
    }

    public void setQlcount(String qlcount) {
        this.qlcount = qlcount;
    }

    public String getTbcount() {
        return tbcount;
    }

    public void setTbcount(String tbcount) {
        this.tbcount = tbcount;
    }

    public String getZdjccount() {
        return zdjccount;
    }

    public void setZdjccount(String zdjccount) {
        this.zdjccount = zdjccount;
    }

    public String getWxgbcount() {
        return wxgbcount;
    }

    public void setWxgbcount(String wxgbcount) {
        this.wxgbcount = wxgbcount;
    }

    public String getJyylcount() {
        return jyylcount;
    }

    public void setJyylcount(String jyylcount) {
        this.jyylcount = jyylcount;
    }

    public String getJyswcount() {
        return jyswcount;
    }

    public void setJyswcount(String jyswcount) {
        this.jyswcount = jyswcount;
    }

    public String getZdmcount() {
        return zdmcount;
    }

    public void setZdmcount(String zdmcount) {
        this.zdmcount = zdmcount;
    }

    public String getHdmcount() {
        return hdmcount;
    }

    public void setHdmcount(String hdmcount) {
        this.hdmcount = hdmcount;
    }

    @Override
    public String toString() {
        return "DcjzSummary{" +
                "adcd='" + adcd + '\'' +
                ", zdadnm='" + zdadnm + '\'' +
                ", adnm='" + adnm + '\'' +
                ", htcount='" + htcount + '\'' +
                ", yhcount='" + yhcount + '\'' +
                ", shcount='" + shcount + '\'' +
                ", qsycount='" + qsycount + '\'' +
                ", skcount='" + skcount + '\'' +
                ", szcount='" + szcount + '\'' +
                ", dfcount='" + dfcount + '\'' +
                ", lhcount='" + lhcount + '\'' +
                ", qlcount='" + qlcount + '\'' +
                ", tbcount='" + tbcount + '\'' +
                ", zdjccount='" + zdjccount + '\'' +
                ", wxgbcount='" + wxgbcount + '\'' +
                ", jyylcount='" + jyylcount + '\'' +
                ", jyswcount='" + jyswcount + '\'' +
                ", zdmcount='" + zdmcount + '\'' +
                ", hdmcount='" + hdmcount + '\'' +
                '}';
    }
}
