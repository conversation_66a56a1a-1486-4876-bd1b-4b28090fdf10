package com.huitu.cloud.api.ewci.hydfcst.entity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 水库预报统计结果 Map 包装类
 *
 * <AUTHOR>
 */
public class HydFcstRsStat implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点编码")
    private String stcd;   // 站点编码

    @ApiModelProperty(value = "初始水位")
    private String initZ;   // 初始水位

    @ApiModelProperty(value = "峰值水位")
    private String maxZ;   // 峰值水位

    @ApiModelProperty(value = "峰值水位出现时间")
    private String ymdh;   // 峰值水位出现时间

    @ApiModelProperty(value = "汛限库容")
    private String fsltdw;   // 调度方式（来自ST_RSVRFSR_B）

    @ApiModelProperty(value = "正常高水位")
    private BigDecimal normz; // 正常蓄水位（来自ST_RSVRFCCH_B）

    @ApiModelProperty(value = "兴利水位")
    private BigDecimal actz;  // 实际水位（来自ST_RSVRFCCH_B）

    @ApiModelProperty(value = "设计洪水位")
    private BigDecimal dsflz; // 下游控制水位（来自ST_RSVRFCCH_B）

    @ApiModelProperty(value = "校核洪水位")
    private BigDecimal ckflz; // 出库水位（来自ST_RSVRFCCH_B）

    @ApiModelProperty(value = "坝顶高程")
    private BigDecimal damel; // 大坝高程（来自ST_RSVRFCCH_B）

    @ApiModelProperty(value = "总库容")
    private BigDecimal ttcp;  // 总调度能力（来自ST_RSVRFCCH_B）

    @ApiModelProperty(value = "开始月日")
    private String bgmd;      // 调度开始日期（月日）（来自ST_RSVRFSR_B）

    @ApiModelProperty(value = "结束月日")
    private String edmd;      // 调度结束日期（月日）（来自ST_RSVRFSR_B）

    @ApiModelProperty(value = "汛限水")
    private BigDecimal fsltdz; // 调度水位（来自ST_RSVRFSR_B）

    @ApiModelProperty(value = "站点名称")
    private String stnm;   // 站点名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "河流名称")
    private String rvnm;   // 河流名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "水文节点名称")
    private String hnnm;   // 水文节点名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "流域名称")
    private String bsnm;   // 水文节点名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "行政区划代码")
    private String adcd;   // 行政区划代码（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "行政区划名称")
    private String adnm;   // 行政区划名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;  // 另一流量或水尺读数（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;  // 另一流量或水尺读数（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "纠偏后经度")
    private BigDecimal plgtd;  // 另一流量或水尺读数（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "纠偏后纬度")
    private BigDecimal plttd;  // 另一流量或水尺读数（来自ST_RVFCCH_B）

    private String tmYmdh;
    private String rz;
    private String w;
    private String inq;
    private String otq;
    private String rzfsltdz;
    private String rznormz;
    private String wfsltdw;

    private String stlc;
    private String sttp;
    private String frgrd;
    private String stazt;
    private String xadcd;
    private String xadnm;

    private String rsvrtp;
    private BigDecimal fldcp;
    private BigDecimal actcp;

    private String resCode;
    private String resName;

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getRsvrtp() {
        return rsvrtp;
    }

    public void setRsvrtp(String rsvrtp) {
        this.rsvrtp = rsvrtp;
    }

    public BigDecimal getFldcp() {
        return fldcp;
    }

    public void setFldcp(BigDecimal fldcp) {
        this.fldcp = fldcp;
    }

    public BigDecimal getActcp() {
        return actcp;
    }

    public void setActcp(BigDecimal actcp) {
        this.actcp = actcp;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getFrgrd() {
        return frgrd;
    }

    public void setFrgrd(String frgrd) {
        this.frgrd = frgrd;
    }

    public String getStazt() {
        return stazt;
    }

    public void setStazt(String stazt) {
        this.stazt = stazt;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getTmYmdh() {
        return tmYmdh;
    }

    public void setTmYmdh(String tmYmdh) {
        this.tmYmdh = tmYmdh;
    }

    public String getRz() {
        return rz;
    }

    public void setRz(String rz) {
        this.rz = rz;
    }

    public String getW() {
        return w;
    }

    public void setW(String w) {
        this.w = w;
    }

    public String getInq() {
        return inq;
    }

    public void setInq(String inq) {
        this.inq = inq;
    }

    public String getOtq() {
        return otq;
    }

    public void setOtq(String otq) {
        this.otq = otq;
    }

    public String getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(String rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public String getRznormz() {
        return rznormz;
    }

    public void setRznormz(String rznormz) {
        this.rznormz = rznormz;
    }

    public String getWfsltdw() {
        return wfsltdw;
    }

    public void setWfsltdw(String wfsltdw) {
        this.wfsltdw = wfsltdw;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getInitZ() {
        return initZ;
    }

    public void setInitZ(String initZ) {
        this.initZ = initZ;
    }

    public String getMaxZ() {
        return maxZ;
    }

    public void setMaxZ(String maxZ) {
        this.maxZ = maxZ;
    }

    public String getYmdh() {
        return ymdh;
    }

    public void setYmdh(String ymdh) {
        this.ymdh = ymdh;
    }

    public BigDecimal getDamel() {
        return damel;
    }

    public void setDamel(BigDecimal damel) {
        this.damel = damel;
    }

    public String getFsltdw() {
        return fsltdw;
    }

    public void setFsltdw(String fsltdw) {
        this.fsltdw = fsltdw;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }

    public BigDecimal getActz() {
        return actz;
    }

    public void setActz(BigDecimal actz) {
        this.actz = actz;
    }

    public BigDecimal getDsflz() {
        return dsflz;
    }

    public void setDsflz(BigDecimal dsflz) {
        this.dsflz = dsflz;
    }

    public BigDecimal getCkflz() {
        return ckflz;
    }

    public void setCkflz(BigDecimal ckflz) {
        this.ckflz = ckflz;
    }

    public BigDecimal getTtcp() {
        return ttcp;
    }

    public void setTtcp(BigDecimal ttcp) {
        this.ttcp = ttcp;
    }

    public String getBgmd() {
        return bgmd;
    }

    public void setBgmd(String bgmd) {
        this.bgmd = bgmd;
    }

    public String getEdmd() {
        return edmd;
    }

    public void setEdmd(String edmd) {
        this.edmd = edmd;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getHnnm() {
        return hnnm;
    }

    public void setHnnm(String hnnm) {
        this.hnnm = hnnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

}
