package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel
public class RsvrPersonVo implements Serializable {

    @ApiModelProperty(value = "行号")
    private Integer sortno;

    @ApiModelProperty(value = "导入文件序号")
    private int importNo;

    @ApiModelProperty(value = "水库名称")
    private String resName;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "超讯限水位")
    private BigDecimal rzfsltdz;

    @ApiModelProperty(value = "市州政区名称")
    private String sadnm;

    @ApiModelProperty(value = "区县市政区名称")
    private String xadnm;

    @ApiModelProperty(value = "水库编码")
    @TableId(value = "RES_CODE", type = IdType.NONE)
    private String resCode;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "水库地址")
    @TableField("RES_LOC")
    private String resLoc;

    @ApiModelProperty(value = "水库工程规模")
    @TableField("ENG_SCAL")
    private String engScal;

    @ApiModelProperty(value = "安全度汛行政责任人-姓名")
    @TableField("SAVE_FLOOD_REALNM")
    private String saveFloodRealnm;

    @ApiModelProperty(value = "安全度汛行政责任人-单位")
    @TableField("SAVE_FLOOD_DEPTNM")
    private String saveFloodDeptnm;

    @ApiModelProperty(value = "安全度汛行政责任人-职务")
    @TableField("SAVE_FLOOD_POST")
    private String saveFloodPost;

    @ApiModelProperty(value = "安全度汛行政责任人-手机号")
    @TableField("SAVE_FLOOD_MOBILE")
    private String saveFloodMobile;

    @ApiModelProperty(value = "抢险技术责任人-姓名")
    @TableField("RESCUE_REALNM")
    private String rescueRealnm;

    @ApiModelProperty(value = "抢险技术责任人-单位")
    @TableField("RESCUE_DEPTNM")
    private String rescueDeptnm;

    @ApiModelProperty(value = "抢险技术责任人-职务")
    @TableField("RESCUE_POST")
    private String rescuePost;

    @ApiModelProperty(value = "抢险技术责任人-手机号")
    @TableField("RESCUE_MOBILE")
    private String rescueMobile;

    @ApiModelProperty(value = "主管部门责任人-姓名")
    @TableField("COMPT_DEPT_REALNM")
    private String comptDeptRealnm;

    @ApiModelProperty(value = "主管部门责任人-单位")
    @TableField("COMPT_DEPT_DEPTNM")
    private String comptDeptDeptnm;

    @ApiModelProperty(value = "主管部门责任人-职务")
    @TableField("COMPT_DEPT_POST")
    private String comptDeptPost;

    @ApiModelProperty(value = "主管部门责任人-手机号")
    @TableField("COMPT_DEPT_MOBILE")
    private String comptDeptMobile;

    @ApiModelProperty(value = "管理单位责任人-姓名")
    @TableField("MANG_UNIT_REALNM")
    private String mangUnitRealnm;

    @ApiModelProperty(value = "管理单位责任人-单位")
    @TableField("MANG_UNIT_DEPTNM")
    private String mangUnitDeptnm;

    @ApiModelProperty(value = "管理单位责任人-职务")
    @TableField("MANG_UNIT_POST")
    private String mangUnitPost;

    @ApiModelProperty(value = "管理单位责任人-手机号")
    @TableField("MANG_UNIT_MOBILE")
    private String mangUnitMobile;

    @ApiModelProperty(value = "巡查值守责任人-姓名")
    @TableField("PATROL_DUTY_REALNM")
    private String patrolDutyRealnm;

    @ApiModelProperty(value = "巡查值守责任人-单位")
    @TableField("PATROL_DUTY_DEPTNM")
    private String patrolDutyDeptnm;

    @ApiModelProperty(value = "巡查值守责任人-职务")
    @TableField("PATROL_DUTY_POST")
    private String patrolDutyPost;

    @ApiModelProperty(value = "巡查值守责任人-手机号")
    @TableField("PATROL_DUTY_MOBILE")
    private String patrolDutyMobile;

    @ApiModelProperty(value = "更新时间")
    @TableField("UDTM")
    private LocalDateTime udtm;


    public Integer getSortno() {
        return sortno;
    }

    public void setSortno(Integer sortno) {
        this.sortno = sortno;
    }

    public int getImportNo() {
        return importNo;
    }

    public void setImportNo(int importNo) {
        this.importNo = importNo;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public String getSadnm() {
        return sadnm;
    }

    public void setSadnm(String sadnm) {
        this.sadnm = sadnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getResLoc() {
        return resLoc;
    }

    public void setResLoc(String resLoc) {
        this.resLoc = resLoc;
    }

    public String getEngScal() {
        return engScal;
    }

    public void setEngScal(String engScal) {
        this.engScal = engScal;
    }

    public String getSaveFloodRealnm() {
        return saveFloodRealnm;
    }

    public void setSaveFloodRealnm(String saveFloodRealnm) {
        this.saveFloodRealnm = saveFloodRealnm;
    }

    public String getSaveFloodDeptnm() {
        return saveFloodDeptnm;
    }

    public void setSaveFloodDeptnm(String saveFloodDeptnm) {
        this.saveFloodDeptnm = saveFloodDeptnm;
    }

    public String getSaveFloodPost() {
        return saveFloodPost;
    }

    public void setSaveFloodPost(String saveFloodPost) {
        this.saveFloodPost = saveFloodPost;
    }

    public String getSaveFloodMobile() {
        return saveFloodMobile;
    }

    public void setSaveFloodMobile(String saveFloodMobile) {
        this.saveFloodMobile = saveFloodMobile;
    }

    public String getRescueRealnm() {
        return rescueRealnm;
    }

    public void setRescueRealnm(String rescueRealnm) {
        this.rescueRealnm = rescueRealnm;
    }

    public String getRescueDeptnm() {
        return rescueDeptnm;
    }

    public void setRescueDeptnm(String rescueDeptnm) {
        this.rescueDeptnm = rescueDeptnm;
    }

    public String getRescuePost() {
        return rescuePost;
    }

    public void setRescuePost(String rescuePost) {
        this.rescuePost = rescuePost;
    }

    public String getRescueMobile() {
        return rescueMobile;
    }

    public void setRescueMobile(String rescueMobile) {
        this.rescueMobile = rescueMobile;
    }

    public String getComptDeptRealnm() {
        return comptDeptRealnm;
    }

    public void setComptDeptRealnm(String comptDeptRealnm) {
        this.comptDeptRealnm = comptDeptRealnm;
    }

    public String getComptDeptDeptnm() {
        return comptDeptDeptnm;
    }

    public void setComptDeptDeptnm(String comptDeptDeptnm) {
        this.comptDeptDeptnm = comptDeptDeptnm;
    }

    public String getComptDeptPost() {
        return comptDeptPost;
    }

    public void setComptDeptPost(String comptDeptPost) {
        this.comptDeptPost = comptDeptPost;
    }

    public String getComptDeptMobile() {
        return comptDeptMobile;
    }

    public void setComptDeptMobile(String comptDeptMobile) {
        this.comptDeptMobile = comptDeptMobile;
    }

    public String getMangUnitRealnm() {
        return mangUnitRealnm;
    }

    public void setMangUnitRealnm(String mangUnitRealnm) {
        this.mangUnitRealnm = mangUnitRealnm;
    }

    public String getMangUnitDeptnm() {
        return mangUnitDeptnm;
    }

    public void setMangUnitDeptnm(String mangUnitDeptnm) {
        this.mangUnitDeptnm = mangUnitDeptnm;
    }

    public String getMangUnitPost() {
        return mangUnitPost;
    }

    public void setMangUnitPost(String mangUnitPost) {
        this.mangUnitPost = mangUnitPost;
    }

    public String getMangUnitMobile() {
        return mangUnitMobile;
    }

    public void setMangUnitMobile(String mangUnitMobile) {
        this.mangUnitMobile = mangUnitMobile;
    }

    public String getPatrolDutyRealnm() {
        return patrolDutyRealnm;
    }

    public void setPatrolDutyRealnm(String patrolDutyRealnm) {
        this.patrolDutyRealnm = patrolDutyRealnm;
    }

    public String getPatrolDutyDeptnm() {
        return patrolDutyDeptnm;
    }

    public void setPatrolDutyDeptnm(String patrolDutyDeptnm) {
        this.patrolDutyDeptnm = patrolDutyDeptnm;
    }

    public String getPatrolDutyPost() {
        return patrolDutyPost;
    }

    public void setPatrolDutyPost(String patrolDutyPost) {
        this.patrolDutyPost = patrolDutyPost;
    }

    public String getPatrolDutyMobile() {
        return patrolDutyMobile;
    }

    public void setPatrolDutyMobile(String patrolDutyMobile) {
        this.patrolDutyMobile = patrolDutyMobile;
    }

    public LocalDateTime getUdtm() {
        return udtm;
    }

    public void setUdtm(LocalDateTime udtm) {
        this.udtm = udtm;
    }
}
