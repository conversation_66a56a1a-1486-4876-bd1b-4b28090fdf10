package com.huitu.cloud.api.om.patrol.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.om.patrol.entity.*;
import com.huitu.cloud.api.om.patrol.entity.plan.OmResPatrolItemP;
import com.huitu.cloud.api.om.patrol.entity.plan.OmResPatrolP;
import com.huitu.cloud.api.om.patrol.mapper.OmResPatrolPlanDao;
import com.huitu.cloud.api.om.patrol.service.OmResPatrolPlanService;
import com.huitu.cloud.api.usif.util.LoginUtils;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 运管水库巡查计划服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class OmResPatrolPlanServiceImpl implements OmResPatrolPlanService {
    private static final Logger logger = LoggerFactory.getLogger(OmResPatrolPlanServiceImpl.class);

    private final OmResPatrolPlanDao baseDao;
    private final LoginUtils loginUtils;


    public OmResPatrolPlanServiceImpl(OmResPatrolPlanDao baseDao, LoginUtils loginUtils) {
        this.baseDao = baseDao;
        this.loginUtils = loginUtils;
    }

    @Override
    public IPage<OmResPatrolPVo> getPage(OmResPatrolPQuery query) {
        Page page = new Page<>(query.getPageNum(), query.getPageSize());
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", query.getAdcd());
        param.put("level", AdcdUtil.getAdLevel(query.getAdcd()));
        param.put("resName", query.getResName());
        param.put("engScal", query.getEngScal());
        param.put("periodType", query.getPeriodType());
        IPage<OmResPatrolPVo> result = baseDao.getPage(page, param);
        return result;
    }

    @Override
    public List<OmResPatrolPResInfoVo> getResInfoList(OmResPatrolPQuery query) {
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", query.getAdcd());
        param.put("level", AdcdUtil.getAdLevel(query.getAdcd()));
        param.put("engScal", query.getEngScal());
        List<OmResPatrolPResInfoVo> list = baseDao.getResInfoList(param);
        return list;
    }

    @Override
    public List<OmResPatrolItemBVo> getPatrolItemBList() {
        return baseDao.getPatrolItemBList();
    }

    @Override
    public List<OmResPatrolItemBVo> getPatrolItemPListByResCode(String resCode, String periodType) {
        Map<String, Object> param = new HashMap<>();
        param.put("resCode", resCode);
        param.put("periodType", periodType);
        return baseDao.getPatrolItemPListByResCode(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OmResPatrolPResInfoVo> insertPlanInfo(OmResPatrolPBo entity) {
        List<OmResPatrolPResInfoVo> list = entity.getResInfoList();
        // 存在巡查计划的水库
        List<String> resCodeList = list.stream().map(OmResPatrolPResInfoVo::getResCode).collect(Collectors.toList());
        Map<String, Object> param = new HashMap<>();
        param.put("list", resCodeList);
        param.put("periodType", entity.getPeriodType());
        List<OmResPatrolPResInfoVo> resList = baseDao.getResInfoListFromPlan(param);
        // 去掉已存在计划的水库
        if (!CollectionUtils.isEmpty(resList)) {
            list = list.stream().filter(item -> !resList.stream().map(OmResPatrolPResInfoVo::getResCode).collect(Collectors.toList()).contains(item.getResCode())).collect(Collectors.toList());
        }
        // 添加未存在计划的水库
        if (!CollectionUtils.isEmpty(list)) {

            List<OmResPatrolP> omResPatrolPs = new ArrayList<>();
            List<OmResPatrolItemP> omResPatrolItemPs = new ArrayList<>();
            list.forEach(i -> {
                // 巡查计划
                OmResPatrolP omResPatrolP = new OmResPatrolPBo();
                omResPatrolP.setResCode(i.getResCode());
                omResPatrolP.setPeriodType(entity.getPeriodType());
                omResPatrolP.setPeriodWeeks(entity.getPeriodWeeks());
                omResPatrolP.setPeriodTimes(entity.getPeriodTimes());
                omResPatrolP.setNotBeginRemindTime(entity.getNotBeginRemindTime());
                omResPatrolP.setNotCompletedRemindTime(entity.getNotCompletedRemindTime());
                omResPatrolP.setRemark(entity.getRemark());
                omResPatrolP.setCreateBy(loginUtils.getCurrentLoginUserID().toString());
                omResPatrolPs.add(omResPatrolP);

                // 巡查项目
                entity.getItemList().forEach(j -> {
                    OmResPatrolItemP omResPatrolItemP = new OmResPatrolItemP();
                    omResPatrolItemP.setResCode(i.getResCode());
                    omResPatrolItemP.setPeriodType(entity.getPeriodType());
                    omResPatrolItemP.setItemId(j.getItemId());
                    omResPatrolItemP.setImportant(j.getImportant());
                    omResPatrolItemPs.add(omResPatrolItemP);
                });
            });
            // 添加巡查计划
            Map<String, Object> planParams = new HashMap<>();
            List<List<OmResPatrolP>> planlists = ListUtils.splitList(omResPatrolPs, 100);
            for (List<OmResPatrolP> planlist : planlists) {
                planParams.put("list", planlist);
                int code = baseDao.insertAllOmResPatrolP(planParams);
                if (code < 1) {
                    throw new RuntimeException("水库巡查计划添加失败");
                }
            }
            // 添加巡查项目
            Map<String, Object> itemParams = new HashMap<>();
            List<List<OmResPatrolItemP>> itemlists = ListUtils.splitList(omResPatrolItemPs, 100);
            for (List<OmResPatrolItemP> itemlist : itemlists) {
                itemParams.put("list", itemlist);
                int code = baseDao.insertAllOmResPatrolItemP(itemParams);
                if (code < 1) {
                    throw new RuntimeException("水库巡查项目添加失败");
                }
            }
        }
        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int coverPlanInfo(OmResPatrolPBo entity) {
        List<String> list = new ArrayList<>();
        entity.getResInfoList().stream().forEach(i -> list.add(i.getResCode()));
        Map<String, Object> param = new HashMap<>();
        param.put("list", list);
        param.put("periodType", entity.getPeriodType());
        // 删除计划和模板
        int code = baseDao.deleteOmResPatrolP(param);
        if (code > 0) {
            baseDao.deleteOmResPatrolItemP(param);
            baseDao.deleteOmResPatrolRouteT(param);
            baseDao.deleteOmResPatrolItemT(param);

            List<OmResPatrolP> omResPatrolPs = new ArrayList<>();
            List<OmResPatrolItemP> omResPatrolItemPs = new ArrayList<>();
            entity.getResInfoList().forEach(i -> {
                // 巡查计划
                OmResPatrolP omResPatrolP = new OmResPatrolPBo();
                omResPatrolP.setResCode(i.getResCode());
                omResPatrolP.setPeriodType(entity.getPeriodType());
                omResPatrolP.setPeriodWeeks(entity.getPeriodWeeks());
                omResPatrolP.setPeriodTimes(entity.getPeriodTimes());
                omResPatrolP.setNotBeginRemindTime(entity.getNotBeginRemindTime());
                omResPatrolP.setNotCompletedRemindTime(entity.getNotCompletedRemindTime());
                omResPatrolP.setRemark(entity.getRemark());
                omResPatrolP.setCreateBy(loginUtils.getCurrentLoginUserID().toString());
                omResPatrolPs.add(omResPatrolP);

                // 巡查项目
                entity.getItemList().forEach(j -> {
                    OmResPatrolItemP omResPatrolItemP = new OmResPatrolItemP();
                    omResPatrolItemP.setResCode(i.getResCode());
                    omResPatrolItemP.setPeriodType(entity.getPeriodType());
                    omResPatrolItemP.setItemId(j.getItemId());
                    omResPatrolItemP.setImportant(j.getImportant());
                    omResPatrolItemPs.add(omResPatrolItemP);
                });
            });
            // 添加巡查计划
            Map<String, Object> planParams = new HashMap<>();
            List<List<OmResPatrolP>> planlists = ListUtils.splitList(omResPatrolPs, 100);
            for (List<OmResPatrolP> planlist : planlists) {
                planParams.put("list", planlist);
                int plancode = baseDao.insertAllOmResPatrolP(planParams);
                if (plancode < 1) {
                    throw new RuntimeException("水库巡查计划添加失败");
                }
            }
            // 添加巡查项目
            Map<String, Object> itemParams = new HashMap<>();
            List<List<OmResPatrolItemP>> itemlists = ListUtils.splitList(omResPatrolItemPs, 100);
            for (List<OmResPatrolItemP> itemlist : itemlists) {
                itemParams.put("list", itemlist);
                int itemcode = baseDao.insertAllOmResPatrolItemP(itemParams);
                if (itemcode < 1) {
                    throw new RuntimeException("水库巡查项目添加失败");
                }
            }
        } else {
            throw new RuntimeException("覆盖巡查计划失败");
        }
        return code;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePlanInfo(OmResPatrolPBo entity) {
        Map<String, Object> param = new HashMap<>();
        param.put("resCode", entity.getResCode());
        param.put("periodType", entity.getPeriodType());
        param.put("periodWeeks", entity.getPeriodWeeks());
        param.put("periodTimes", entity.getPeriodTimes());
        param.put("notBeginRemindTime", entity.getNotBeginRemindTime());
        param.put("notCompletedRemindTime", entity.getNotCompletedRemindTime());
        param.put("remark", entity.getRemark());
        int code = baseDao.updateOmResPatrolP(param);
        if (code > 0) {
            // 判断巡查项目是否修改
            if (!compareBySteam(entity.getItemList(), baseDao.getPatrolItemPListByResCode(param))) {
                // 先删除巡查项目
                Map<String, Object> delparam = new HashMap<>();
                delparam.put("list", Arrays.asList(entity.getResCode()));
                delparam.put("periodType", entity.getPeriodType());
                baseDao.deleteOmResPatrolItemP(delparam);
                baseDao.deleteOmResPatrolRouteT(delparam);
                baseDao.deleteOmResPatrolItemT(delparam);

                // 添加巡查项目
                List<OmResPatrolItemP> omResPatrolItemPs = new ArrayList<>();
                entity.getItemList().forEach(j -> {
                    OmResPatrolItemP omResPatrolItemP = new OmResPatrolItemP();
                    omResPatrolItemP.setResCode(entity.getResCode());
                    omResPatrolItemP.setPeriodType(entity.getPeriodType());
                    omResPatrolItemP.setItemId(j.getItemId());
                    omResPatrolItemP.setImportant(j.getImportant());
                    omResPatrolItemPs.add(omResPatrolItemP);
                });

                Map<String, Object> itemParams = new HashMap<>();
                List<List<OmResPatrolItemP>> itemlists = ListUtils.splitList(omResPatrolItemPs, 100);
                for (List<OmResPatrolItemP> itemlist : itemlists) {
                    itemParams.put("list", itemlist);
                    int itemcode = baseDao.insertAllOmResPatrolItemP(itemParams);
                    if (itemcode < 1) {
                        throw new RuntimeException("水库巡查项目添加失败");
                    }
                }
            }
        } else {
            throw new RuntimeException("编辑巡查计划失败");
        }
        return code;
    }

    @Override
    public int updatePlanState(String state, String resCode, String periodType) {
        return baseDao.updatePlanState(state, resCode, periodType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deletePlanInfo(String resCode, String periodType) {
        List<String> list = Arrays.asList(resCode);
        Map<String, Object> param = new HashMap<>();
        param.put("list", list);
        param.put("periodType", periodType);
        int code = baseDao.deleteOmResPatrolP(param);
        if (code > 0) {
            baseDao.deleteOmResPatrolItemP(param);
            baseDao.deleteOmResPatrolRouteT(param);
            baseDao.deleteOmResPatrolItemT(param);
        }
        return code;
    }

    /**
     * 判断巡查项目
     * list1和list2 是否相等
     *
     * @param list1 前端传的
     * @param list2 数据库查出来的
     * @return false不相等 true相等
     */
    private boolean compareBySteam(List<OmResPatrolItemBVo> list1, List<OmResPatrolItemBVo> list2) {
        boolean flag = false;
        if (list1.size() == list2.size()) {
            List<OmResPatrolItemBVo> item1 = list1.stream().filter(item -> !list2.stream().map(up -> up.getItemId() + up.getImportant()).collect(Collectors.toList()).contains(item.getItemId())).collect(Collectors.toList());
            List<OmResPatrolItemBVo> item2 = list2.stream().filter(item -> !list1.stream().map(up -> up.getItemId() + up.getImportant()).collect(Collectors.toList()).contains(item.getItemId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(item1) && CollectionUtils.isEmpty(item2)) {
                flag = true;
            }
        }
        return flag;
    }
}
