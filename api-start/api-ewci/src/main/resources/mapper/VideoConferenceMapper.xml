<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.tencent.mapper.VideoConferenceDao">

    <update id="updateVideoConference">
        update BSN_VIDEO_CONFERENCE set status = #{entity.status}
        <if test="entity.startTime != null">
            , start_time = #{entity.startTime}
        </if>
        <if test="entity.endTime != null">
            , end_time = #{entity.endTime}
        </if>
        <if test="entity.createId != null and  entity.createId != ''">
            , create_id = #{entity.createId}
        </if>
        where room_id= #{entity.roomId}
    </update>


    <!-- 查询会议中的列表   -->
    <select id="getMeetingList" resultType="com.huitu.cloud.api.ewci.tencent.entity.VideoConferenceVo">
        SELECT t.room_id,
        t.title,
        t.start_time,
        t.end_time,
        t.create_id,
        t.status,
        t.create_name,
        t.adnm,
        t.adcd,
        (select count(1)
        from (select distinct t1.member_id
        from BSN_VIDEO_PERSON_RECORD t1
        where t1.room_id = t.room_id) tt) roomPersonCount
        FROM BSN_VIDEO_CONFERENCE t
        WHERE
        1 = 1
        AND LEFT (t.adcd
        , #{params.level}) = LEFT (#{params.adcd}
        , #{params.level})
        AND EXISTS ( SELECT 1 FROM BSN_VIDEO_PERSON_RECORD tt WHERE tt.room_id= t.room_id
        AND tt.member_id = #{params.userId} )
        <if test="params.stm != null and params.stm != ''">
            AND t.start_time &gt;= #{params.stm}
        </if>
        <if test="params.etm != null and params.etm != ''">
            AND t.start_time &lt;= CONCAT(#{params.etm}, ' 23:59:59')
        </if>
        <if test="params.title != null and params.title != ''">
            AND t.title like '%${params.title}%'
        </if>
        ORDER BY
        t.start_time DESC
    </select>

</mapper>