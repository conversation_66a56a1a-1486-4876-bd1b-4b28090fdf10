package com.huitu.cloud.api.syq.rain.entity;

import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryRain  extends PageBean {

    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    @SqlInjection
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    @SqlInjection
    private String etm;
    @ApiModelProperty(value = "测站归属类型 1、水文，2、山洪  3、气象")
    private List<String> stType;
    @ApiModelProperty(value = "政区编码")
    @SqlInjection
    private String adcd;
    @ApiModelProperty(value = "流域编码")
    private String bscd;
    @ApiModelProperty(value = "流域名称")
    private String bsnm;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "降雨阈值范围,例：“1-10”")
    private String threshold;
    @ApiModelProperty(value = "显示类型（0：查询所有雨量站信息，1：查询一段时间内所有上报雨量的站）")
    private String rainShowType;

    @ApiModelProperty(value = "是否是辖区外(用户登录以后有用) 1：辖区内，2：辖区外")
    private List<String> isOut;

    @ApiModelProperty(value = "是否关注 1：关注，2：未关注")
    private List<String> isFollow;

    @ApiModelProperty(value = "是否仅视频 有值则显示匹配到视频站的测站")
    private String isVideo;
    @ApiModelProperty(value = "未来N小时预报降雨情况")
    @SqlInjection
    private List<Integer> forecastHour;

    public List<String> getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(List<String> isFollow) {
        this.isFollow = isFollow;
    }

    public String getRainShowType() {
        return rainShowType;
    }

    public void setRainShowType(String rainShowType) {
        this.rainShowType = rainShowType;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getThreshold() {
        return threshold;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public List<String> getIsOut() {
        return isOut;
    }

    public void setIsOut(List<String> isOut) {
        this.isOut = isOut;
    }

    public List<Integer> getForecastHour() {
        return forecastHour;
    }

    public void setForecastHour(List<Integer> forecastHour) {
        this.forecastHour = forecastHour;
    }

    public String getIsVideo() {
        return isVideo;
    }

    public void setIsVideo(String isVideo) {
        this.isVideo = isVideo;
    }
}
