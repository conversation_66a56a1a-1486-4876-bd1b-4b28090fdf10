package com.huitu.cloud.api.ewci.inspect.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 检查报告清单
 */
@Data
@ApiModel(value = "检查报告清单")
public class BnsInspectReportInfo implements Serializable {

    @ApiModelProperty(value = "检查报告ID")
    private String reportId;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "生成日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "是否确认(0否 1是)")
    private String confirmResult;

    @ApiModelProperty(value = "报告生成状态(1未生成2生成中3已生成)")
    private String status;

    @ApiModelProperty(value = "创建人")
    private Integer creator;
}
