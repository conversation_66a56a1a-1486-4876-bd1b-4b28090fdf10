package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 预警反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@TableName("WARN_FEEDBACK")
@ApiModel(value="WarnFeedback对象", description="预警反馈表")
public class WarnFeedback implements Serializable {
    @ApiModelProperty(value = "预警ID")
    @TableId(value = "WARNID", type = IdType.NONE)
    private String warnId;

    @ApiModelProperty(value = "是否发生山洪灾害(0:否,1:是)")
    @TableField("HAS_MOUTAIN_TORRENTS")
    private String hasMouTainTorrents;

    @ApiModelProperty(value = "情况描述")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "上传图片")
    @TableField("IMGS")
    private String imgs;

    @ApiModelProperty(value = "最后修改人ID")
    @TableField("OCCURUSER")
    private String occurUser;

    @ApiModelProperty(value = "最后修改时间")
    @TableField("OCCURTM")
    private Date occurTM;

    @ApiModelProperty(value = "UUID版本标识")
    @TableField("VCODE")
    private String vcode;

    public String getWarnId() {
        return warnId;
    }
    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getHasMouTainTorrents() {
        return hasMouTainTorrents;
    }
    public void setHasMouTainTorrents(String hasMouTainTorrents) {
        this.hasMouTainTorrents = hasMouTainTorrents;
    }

    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getImgs() {
        return imgs;
    }
    public void setImgs(String imgs) {
        this.imgs = imgs;
    }

    public String getOccurUser() {
        return occurUser;
    }
    public void setOccurUser(String occurUser) {
        this.occurUser = occurUser;
    }

    public Date getOccurTM() {
        return occurTM;
    }
    public void setOccurTM(Date occurTM) {
        this.occurTM = occurTM;
    }

    public String getVcode() {
        return vcode;
    }
    public void setVcode(String vcode) {
        this.vcode = vcode;
    }
}