package com.huitu.cloud.api.skdt.floodcontrol.forecast.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.skdt.floodcontrol.forecast.entity.FForecastInfo;
import com.huitu.cloud.api.skdt.floodcontrol.forecast.entity.FForecastResult;
import com.huitu.cloud.api.skdt.floodcontrol.forecast.entity.ForecastInfoVo;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-17
 */
public interface ForecastService extends IService<FForecastInfo> {
    /**
     *
     * @param stcd 水库测站编码
     * @param stm  开始时间
     * @param etm 结束时间
     * @param year 年份
     * @param pageNum 页码
     * @param pageSize 页数
     * @return
     */
    IPage<FForecastInfo> getForecastInfoList(String stcd ,String stm,String etm,String year,int pageNum ,int pageSize);

    /**
     * 根据预报编码查询预报方案结果
     * @param flid 预报编码
     * @return
     */
    List<FForecastResult> getForecastResult(String flid);

    /**
     * 根据预报编码查询预报综合信息
     * @param flid
     * @return
     * @throws Exception
     */
    ForecastInfoVo getForecastInfoByFlid(String flid) throws Exception;

    /**
     * 导入预报成果
     * @param stcd
     * @param flnm
     * @param reltm
     * @param is
     * @return
     */
    String importForcastResult(String stcd, String flnm, String reltm, InputStream is);
}
