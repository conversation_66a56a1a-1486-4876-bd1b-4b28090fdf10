package com.huitu.cloud.api.fusion.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 山洪预警详情响应对象
 */
@Data
@ApiModel(value = "山洪预警详情响应对象")
public class FusionWarningDetailResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARN_ID")
    private String warnId;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "重现期")
    @TableField(value = "REPRD")
    private Integer reprd;

    @ApiModelProperty(value = "阈值历时")
    @TableField(value = "STDT")
    private Integer stdt;

    @ApiModelProperty(value = "实时雨量阈值")
    @TableField(value = "RDRPT")
    private BigDecimal rdrpt;

    @ApiModelProperty(value = "实时累计降水量")
    @TableField(value = "RACCP")
    private BigDecimal raccp;

    @ApiModelProperty(value = "预报历时")
    @TableField(value = "FCDT")
    private Integer fcdt;

    @ApiModelProperty(value = "预报雨量阈值")
    @TableField(value = "FDRPT")
    private BigDecimal fdrpt;

    @ApiModelProperty(value = "预报累计降水量")
    @TableField(value = "FACCP")
    private BigDecimal faccp;

    @ApiModelProperty(value = "最后更新时间")
    @TableField(value = "LATEST_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date latestTime;

}
