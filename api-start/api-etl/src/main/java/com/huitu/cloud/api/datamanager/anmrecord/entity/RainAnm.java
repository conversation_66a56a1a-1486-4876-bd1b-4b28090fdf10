package com.huitu.cloud.api.datamanager.anmrecord.entity;

import io.swagger.annotations.ApiModelProperty;

public class RainAnm extends AnmVo {
    @ApiModelProperty(value = "瞬时阈值")
    private Double snv;

    @ApiModelProperty(value = "分钟数")
    private Integer inv;

    @ApiModelProperty(value = "5分钟数据量阈值")
    private Integer ctpm5;

    public Double getSnv() {
        return snv;
    }


    public void setSnv(Double snv) {
        this.snv = snv;
    }

    public Integer getInv() {
        return inv;
    }

    public void setInv(Integer inv) {
        this.inv = inv;
    }

    public Integer getCtpm5() {
        return ctpm5;
    }

    public void setCtpm5(Integer ctpm5) {
        this.ctpm5 = ctpm5;
    }
}
