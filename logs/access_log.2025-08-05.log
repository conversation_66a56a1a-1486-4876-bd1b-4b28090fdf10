************ - - [05/Aug/2025:08:34:43 +0800] "GET /doc.html HTTP/1.1" 302 -
************ - - [05/Aug/2025:08:34:43 +0800] "GET /login HTTP/1.1" 302 -
************ - - [05/Aug/2025:08:34:43 +0800] "GET /login?code=Yb1pkZ&state=dj6hZj HTTP/1.1" 302 -
************ - - [05/Aug/2025:08:34:44 +0800] "GET /doc.html HTTP/1.1" 200 71645
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************ - - [05/Aug/2025:08:34:44 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************ - - [05/Aug/2025:08:34:45 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
************ - - [05/Aug/2025:08:34:46 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************ - - [05/Aug/2025:08:34:46 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************ - - [05/Aug/2025:08:34:46 +0800] "GET /v2/api-docs HTTP/1.1" 200 2708120
************ - - [05/Aug/2025:08:34:53 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
************ - - [05/Aug/2025:08:34:53 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
************ - - [05/Aug/2025:08:34:53 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
************ - - [05/Aug/2025:08:34:53 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [05/Aug/2025:08:34:53 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [05/Aug/2025:08:34:56 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
************ - - [05/Aug/2025:08:34:59 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 9897
************ - - [05/Aug/2025:08:34:59 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [05/Aug/2025:08:36:05 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [05/Aug/2025:08:36:05 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [05/Aug/2025:08:38:20 +0800] "GET /doc.html HTTP/1.1" 302 -
************ - - [05/Aug/2025:08:38:20 +0800] "GET /login HTTP/1.1" 302 -
************ - - [05/Aug/2025:08:38:21 +0800] "GET /login?code=DA5YFT&state=yyomlj HTTP/1.1" 302 -
************ - - [05/Aug/2025:08:38:21 +0800] "GET /doc.html HTTP/1.1" 200 71645
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************ - - [05/Aug/2025:08:38:21 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************ - - [05/Aug/2025:08:38:22 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
************ - - [05/Aug/2025:08:38:22 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************ - - [05/Aug/2025:08:38:22 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************ - - [05/Aug/2025:08:38:23 +0800] "GET /v2/api-docs HTTP/1.1" 200 2708081
************ - - [05/Aug/2025:08:38:27 +0800] "GET /doc.html HTTP/1.1" 200 71645
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************ - - [05/Aug/2025:08:38:27 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************ - - [05/Aug/2025:08:38:28 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************ - - [05/Aug/2025:08:38:28 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************ - - [05/Aug/2025:08:38:28 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************ - - [05/Aug/2025:08:38:28 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************ - - [05/Aug/2025:08:38:28 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************ - - [05/Aug/2025:08:38:28 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************ - - [05/Aug/2025:08:38:28 +0800] "GET /v2/api-docs HTTP/1.1" 200 2708081
************ - - [05/Aug/2025:08:38:35 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
************ - - [05/Aug/2025:08:38:35 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
************ - - [05/Aug/2025:08:38:35 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
************ - - [05/Aug/2025:08:38:35 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [05/Aug/2025:08:38:35 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [05/Aug/2025:08:39:44 +0800] "GET /doc.html HTTP/1.1" 200 71645
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************ - - [05/Aug/2025:08:39:44 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************ - - [05/Aug/2025:08:39:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************ - - [05/Aug/2025:08:39:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************ - - [05/Aug/2025:08:39:46 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************ - - [05/Aug/2025:08:39:46 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************ - - [05/Aug/2025:08:39:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************ - - [05/Aug/2025:08:39:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************ - - [05/Aug/2025:08:39:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************ - - [05/Aug/2025:08:39:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************ - - [05/Aug/2025:08:39:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************ - - [05/Aug/2025:08:39:46 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************ - - [05/Aug/2025:08:39:46 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************ - - [05/Aug/2025:08:39:46 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************ - - [05/Aug/2025:08:39:47 +0800] "GET /v2/api-docs HTTP/1.1" 200 2708081
************ - - [05/Aug/2025:08:40:33 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [05/Aug/2025:08:40:33 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1754354973521 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754354973521 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+09:00&filterCnt=6&_timer304=1754354973521 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+08:49:33&etm=&_timer304=1754354973521 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754354973521 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754354973522 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:23 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754354973521 HTTP/1.1" 200 166
************* - - [05/Aug/2025:08:49:23 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+08:49:33&etm=&_timer304=1754354973521 HTTP/1.1" 200 156
************* - - [05/Aug/2025:08:49:23 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+09:00&filterCnt=6&_timer304=1754354973521 HTTP/1.1" 200 164
************* - - [05/Aug/2025:08:49:23 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [05/Aug/2025:08:49:23 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [05/Aug/2025:08:49:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:08:49:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:08:49:23 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754354973522 HTTP/1.1" 200 169
************* - - [05/Aug/2025:08:49:23 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754354973521 HTTP/1.1" 200 13016
************* - - [05/Aug/2025:08:49:24 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1754354973521 HTTP/1.1" 200 1482
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-04&_timer304=1754354978267 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/base/saas/token?_timer304=1754354978267 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754354978315 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:26 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:27 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************* - - [05/Aug/2025:08:49:28 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:08:49:28 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [05/Aug/2025:08:49:29 +0800] "GET /api/base/saas/token?_timer304=1754354978267 HTTP/1.1" 200 411
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [05/Aug/2025:08:49:29 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754354978315 HTTP/1.1" 200 2009
************* - - [05/Aug/2025:08:49:29 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************* - - [05/Aug/2025:08:49:30 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 332
************* - - [05/Aug/2025:08:49:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754354983167 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:31 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754354983173 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754354983171 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:49:31 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754354983173 HTTP/1.1" 200 148
************* - - [05/Aug/2025:08:49:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754354983171 HTTP/1.1" 200 159
************* - - [05/Aug/2025:08:49:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754354983167 HTTP/1.1" 200 160
************* - - [05/Aug/2025:08:49:32 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-04&_timer304=1754354978267 HTTP/1.1" 200 420
************* - - [05/Aug/2025:08:54:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754355273541 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:54:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754355273541 HTTP/1.1" 200 160
************* - - [05/Aug/2025:08:54:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754355284164 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:54:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754355284164 HTTP/1.1" 200 159
************* - - [05/Aug/2025:08:59:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754355573168 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:59:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754355573168 HTTP/1.1" 200 160
************* - - [05/Aug/2025:08:59:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754355585158 HTTP/1.1" 200 -
************* - - [05/Aug/2025:08:59:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754355585158 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:04:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754355873169 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:04:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754355873169 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:04:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754355886155 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:04:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754355886155 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:09:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754356187162 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:09:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754356187162 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:10:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754356222163 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:10:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754356222163 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:14:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754356488169 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:14:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754356488169 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:16:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754356582158 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:16:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754356582158 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:19:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754356789160 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:19:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754356789160 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:20:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754356827300 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:20:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754356827300 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:23:07 +0800] "GET /doc.html HTTP/1.1" 302 -
************* - - [05/Aug/2025:09:23:07 +0800] "GET /login HTTP/1.1" 302 -
************* - - [05/Aug/2025:09:23:07 +0800] "GET /login?code=f1izKV&state=MAGeBH HTTP/1.1" 302 -
************* - - [05/Aug/2025:09:23:07 +0800] "GET /doc.html HTTP/1.1" 200 71645
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************* - - [05/Aug/2025:09:23:07 +0800] "GET /doc.html HTTP/1.1" 200 71645
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************* - - [05/Aug/2025:09:23:07 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
************* - - [05/Aug/2025:09:23:08 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************* - - [05/Aug/2025:09:23:08 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************* - - [05/Aug/2025:09:23:08 +0800] "GET /v2/api-docs HTTP/1.1" 200 2708081
************* - - [05/Aug/2025:09:23:08 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************* - - [05/Aug/2025:09:23:09 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************* - - [05/Aug/2025:09:23:09 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************* - - [05/Aug/2025:09:23:09 +0800] "GET /v2/api-docs HTTP/1.1" 200 2708081
************* - - [05/Aug/2025:09:23:15 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
************* - - [05/Aug/2025:09:23:15 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
************* - - [05/Aug/2025:09:23:15 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
************* - - [05/Aug/2025:09:23:15 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************* - - [05/Aug/2025:09:23:15 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************* - - [05/Aug/2025:09:24:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754357092834 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:41 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754357092834 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:41 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+09:24:52&etm=&_timer304=1754357092834 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:41 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1754357092834 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+10:00&filterCnt=6&_timer304=1754357092834 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754357092835 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:09:24:41 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [05/Aug/2025:09:24:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:09:24:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [05/Aug/2025:09:24:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754357092834 HTTP/1.1" 200 166
************* - - [05/Aug/2025:09:24:41 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+09:24:52&etm=&_timer304=1754357092834 HTTP/1.1" 200 156
************* - - [05/Aug/2025:09:24:41 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+10:00&filterCnt=6&_timer304=1754357092834 HTTP/1.1" 200 164
************* - - [05/Aug/2025:09:24:41 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1754357092834 HTTP/1.1" 200 1482
************* - - [05/Aug/2025:09:24:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754357092835 HTTP/1.1" 200 169
************* - - [05/Aug/2025:09:24:41 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754357092834 HTTP/1.1" 200 13016
************* - - [05/Aug/2025:09:24:42 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-04&_timer304=1754357094396 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:42 +0800] "OPTIONS /api/base/saas/token?_timer304=1754357094396 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:42 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-04&_timer304=1754357094396 HTTP/1.1" 200 420
************* - - [05/Aug/2025:09:24:42 +0800] "GET /api/base/saas/token?_timer304=1754357094396 HTTP/1.1" 200 411
************* - - [05/Aug/2025:09:24:42 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754357094444 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [05/Aug/2025:09:24:43 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754357094444 HTTP/1.1" 200 2009
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************* - - [05/Aug/2025:09:24:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [05/Aug/2025:09:24:44 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [05/Aug/2025:09:24:44 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 332
************* - - [05/Aug/2025:09:24:50 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754357102160 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754357102159 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754357102158 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:24:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754357102158 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:24:50 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754357102160 HTTP/1.1" 200 148
************* - - [05/Aug/2025:09:24:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754357102159 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:25:01 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1754357113482 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1754357113482 HTTP/1.1" 200 144
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+09:25:13&etm=&_timer304=1754357113710 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754357113710 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+10:00&filterCnt=6&_timer304=1754357113710 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754357113710 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754357113710 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754357113711 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754357113719 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [05/Aug/2025:09:25:02 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754357113710 HTTP/1.1" 200 166
************* - - [05/Aug/2025:09:25:02 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+09:25:13&etm=&_timer304=1754357113710 HTTP/1.1" 200 156
************* - - [05/Aug/2025:09:25:02 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+10:00&filterCnt=6&_timer304=1754357113710 HTTP/1.1" 200 164
************* - - [05/Aug/2025:09:25:02 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:09:25:02 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754357113710 HTTP/1.1" 200 169
************* - - [05/Aug/2025:09:25:02 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754357113888 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754357113711 HTTP/1.1" 200 161
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754357113930 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754357113710 HTTP/1.1" 200 13016
************* - - [05/Aug/2025:09:25:02 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754357113719 HTTP/1.1" 200 159616
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [05/Aug/2025:09:25:02 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754357113930 HTTP/1.1" 200 258
************* - - [05/Aug/2025:09:25:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [05/Aug/2025:09:25:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [05/Aug/2025:09:25:03 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [05/Aug/2025:09:25:03 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754357115001 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:03 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754357115001 HTTP/1.1" 200 258
************* - - [05/Aug/2025:09:25:03 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:03 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [05/Aug/2025:09:25:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754357113888 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:09:25:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [05/Aug/2025:09:25:04 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:04 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754357116509 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:04 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:04 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754357116509 HTTP/1.1" 200 148
************* - - [05/Aug/2025:09:25:04 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:09:25:05 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [05/Aug/2025:09:25:12 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:25:12 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 1471362
************* - - [05/Aug/2025:09:29:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754357392161 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:29:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754357392161 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:29:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754357403167 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:29:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754357403167 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:30:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754357414164 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:30:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754357414177 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:30:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:30:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:30:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:30:02 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754357414164 HTTP/1.1" 200 161
************* - - [05/Aug/2025:09:30:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:09:30:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:09:30:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754357414177 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:09:30:04 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:09:34:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754357692164 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:34:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754357692164 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:34:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754357704167 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:34:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754357704167 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:35:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:35:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754357714163 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:35:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754357714170 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:35:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:35:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:35:02 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754357714163 HTTP/1.1" 200 161
************* - - [05/Aug/2025:09:35:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:09:35:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:09:35:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754357714170 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:09:35:03 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:09:39:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754357992172 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:39:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754357992172 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:39:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754358005163 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:39:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754358005163 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:40:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754358013518 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:40:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754358013518 HTTP/1.1" 200 161
************* - - [05/Aug/2025:09:40:02 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:40:02 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:40:02 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754358014170 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:40:02 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:40:02 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:09:40:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:09:40:03 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:09:40:03 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754358014170 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:09:45:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754358318977 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:45:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754358318978 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:45:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754358318979 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:45:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754358318983 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:45:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:45:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:45:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:45:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754358318978 HTTP/1.1" 200 161
************* - - [05/Aug/2025:09:45:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:09:45:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754358318979 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:45:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754358318977 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:45:08 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:09:45:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754358318983 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:09:45:08 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754358572249 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754358572244 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+09:49:32&etm=&_timer304=1754358572317 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754358572317 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754358572317 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754358572317 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+10:00&filterCnt=6&_timer304=1754358572317 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754358572244 HTTP/1.1" 200 161
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754358572382 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+09:49:32&etm=&_timer304=1754358572317 HTTP/1.1" 200 156
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754358572407 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:09:49:20 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [05/Aug/2025:09:49:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [05/Aug/2025:09:49:20 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754358572317 HTTP/1.1" 200 166
************* - - [05/Aug/2025:09:49:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [05/Aug/2025:09:49:20 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754358572317 HTTP/1.1" 200 169
************* - - [05/Aug/2025:09:49:20 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754358572249 HTTP/1.1" 200 159616
************* - - [05/Aug/2025:09:49:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:09:49:20 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+10:00&filterCnt=6&_timer304=1754358572317 HTTP/1.1" 200 164
************* - - [05/Aug/2025:09:49:20 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754358572317 HTTP/1.1" 200 13016
************* - - [05/Aug/2025:09:49:20 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:21 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754358572407 HTTP/1.1" 200 258
************* - - [05/Aug/2025:09:49:21 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:21 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [05/Aug/2025:09:49:21 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [05/Aug/2025:09:49:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [05/Aug/2025:09:49:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754358573586 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:21 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:22 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754358573586 HTTP/1.1" 200 148
************* - - [05/Aug/2025:09:49:22 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:09:49:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754358572382 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:09:49:22 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [05/Aug/2025:09:49:22 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [05/Aug/2025:09:49:22 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754358574007 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:22 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754358574007 HTTP/1.1" 200 258
************* - - [05/Aug/2025:09:49:22 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [05/Aug/2025:09:49:23 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [05/Aug/2025:09:49:25 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:26 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 1469440
************* - - [05/Aug/2025:09:49:30 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754358582038 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754358582068 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:30 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754358582077 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:49:30 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754358582038 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:49:30 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754358582077 HTTP/1.1" 200 148
************* - - [05/Aug/2025:09:49:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754358582068 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:51:32 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754358703640 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:51:32 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754358703640 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:51:32 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:51:32 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754358703640 HTTP/1.1" 200 12285
************* - - [05/Aug/2025:09:51:32 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754358703640 HTTP/1.1" 200 12285
************* - - [05/Aug/2025:09:51:32 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 23594
************* - - [05/Aug/2025:09:55:00 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754358911642 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:55:00 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754358911642 HTTP/1.1" 200 161
************* - - [05/Aug/2025:09:55:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754358911731 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:55:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754358911732 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:55:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:55:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:55:00 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754358911743 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:55:00 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:55:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754358911731 HTTP/1.1" 200 159
************* - - [05/Aug/2025:09:55:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:09:55:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754358911732 HTTP/1.1" 200 160
************* - - [05/Aug/2025:09:55:00 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:09:55:01 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754358911743 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:09:55:01 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:09:59:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754359205732 HTTP/1.1" 200 -
************* - - [05/Aug/2025:09:59:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754359205732 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:00:50 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754359262116 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:00:50 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754359262116 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:01:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:01:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:01:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754359297881 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:01:26 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:01:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:01:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:01:27 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754359297881 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:01:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:03:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754359410393 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:03:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754359410393 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:04:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754359509262 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:04:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754359509325 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:04:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754359509262 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:04:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754359509325 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:04:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:04:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:04:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754359510162 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:04:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:04:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:04:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:04:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754359510162 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:04:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:09:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754359765879 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:09:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754359765879 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:09:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754359772166 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:09:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754359772166 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:09:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754359773169 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:09:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754359773169 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:09:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:09:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:09:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:09:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754359774167 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:09:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:09:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:09:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754359774167 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:09:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:09:26 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:09:26 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 23594
************* - - [05/Aug/2025:10:14:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754360094951 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:14:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754360094951 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:16:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754360182165 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:16:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754360182168 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:16:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754360182165 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:16:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754360182168 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:17:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754360238283 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:06 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:17:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:17:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754360238283 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:17:08 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:17:13 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:13 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 1469440
************* - - [05/Aug/2025:10:17:19 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:19 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=13DED2B9-478D-4FC3-A546-B5D9AAFADC47&warnGradeId=2&_timer304=1754360251455 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:19 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103206002&_timer304=1754360251455 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:19 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1819
************* - - [05/Aug/2025:10:17:19 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=13DED2B9-478D-4FC3-A546-B5D9AAFADC47&warnGradeId=2&_timer304=1754360251455 HTTP/1.1" 200 153
************* - - [05/Aug/2025:10:17:19 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103206002&_timer304=1754360251455 HTTP/1.1" 200 155
************* - - [05/Aug/2025:10:17:19 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=13301C34-C85F-486A-9E7C-AD18ACC1CAF9&_timer304=1754360251550 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:19 +0800] "OPTIONS /api/ew/warning/process-list?warnId=13301C34-C85F-486A-9E7C-AD18ACC1CAF9&_timer304=1754360251550 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:19 +0800] "OPTIONS /api/ew/warning/message-list?warnId=13301C34-C85F-486A-9E7C-AD18ACC1CAF9&_timer304=1754360251550 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:19 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=13301C34-C85F-486A-9E7C-AD18ACC1CAF9&_timer304=1754360251550 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:19 +0800] "GET /api/ew/warning/process-list?warnId=13301C34-C85F-486A-9E7C-AD18ACC1CAF9&_timer304=1754360251550 HTTP/1.1" 200 745
************* - - [05/Aug/2025:10:17:19 +0800] "GET /api/ew/warning/flow-list?warnId=13301C34-C85F-486A-9E7C-AD18ACC1CAF9&_timer304=1754360251550 HTTP/1.1" 200 1382
************* - - [05/Aug/2025:10:17:20 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=13301C34-C85F-486A-9E7C-AD18ACC1CAF9&_timer304=1754360251550 HTTP/1.1" 200 333
************* - - [05/Aug/2025:10:17:20 +0800] "GET /api/ew/warning/message-list?warnId=13301C34-C85F-486A-9E7C-AD18ACC1CAF9&_timer304=1754360251550 HTTP/1.1" 200 8792
************* - - [05/Aug/2025:10:17:21 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=13DED2B9-478D-4FC3-A546-B5D9AAFADC47&warnGradeId=2&_timer304=1754360253404 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:21 +0800] "OPTIONS /api/ew/warning/snapshot-index?warnId=13DED2B9-478D-4FC3-A546-B5D9AAFADC47&warnGradeId=2&_timer304=1754360253404 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:21 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=13DED2B9-478D-4FC3-A546-B5D9AAFADC47&warnGradeId=2&_timer304=1754360253404 HTTP/1.1" 200 153
************* - - [05/Aug/2025:10:17:21 +0800] "GET /api/ew/warning/snapshot-index?warnId=13DED2B9-478D-4FC3-A546-B5D9AAFADC47&warnGradeId=2&_timer304=1754360253404 HTTP/1.1" 200 151
************* - - [05/Aug/2025:10:17:23 +0800] "OPTIONS /api/ew/warning/sending-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:17:23 +0800] "POST /api/ew/warning/sending-page-list HTTP/1.1" 200 237
************* - - [05/Aug/2025:10:19:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754360377163 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:19:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754360377163 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:19:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754360395168 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:19:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754360395168 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:20:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754360422529 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:20:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:20:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:20:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:20:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754360422538 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:20:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:20:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754360422529 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:20:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:20:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754360422538 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:20:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:24:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754360672553 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:24:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754360672553 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:24:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754360673160 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:24:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754360673160 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:24:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:24:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:24:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754360674166 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:24:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:24:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:24:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:24:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754360674166 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:24:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:24:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754360696165 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:24:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754360696165 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:30:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754361036391 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754361036391 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:30:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754361057576 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754361057591 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754361057594 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:46 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754361057576 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:30:46 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:30:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754361057591 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:30:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:30:47 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754361057594 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:30:47 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:30:47 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=CD16B94E-BCBD-46E1-945D-47854D8897CC&warnGradeId=2&_timer304=1754361059528 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:47 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103215003&_timer304=1754361059528 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:47 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:47 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=CD16B94E-BCBD-46E1-945D-47854D8897CC&warnGradeId=2&_timer304=1754361059528 HTTP/1.1" 200 153
************* - - [05/Aug/2025:10:30:47 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103215003&_timer304=1754361059528 HTTP/1.1" 200 155
************* - - [05/Aug/2025:10:30:47 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1787
************* - - [05/Aug/2025:10:30:47 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=10615116-74F9-44D6-9DD9-4CDFDF1B5E8F&_timer304=1754361059581 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:47 +0800] "OPTIONS /api/ew/warning/process-list?warnId=10615116-74F9-44D6-9DD9-4CDFDF1B5E8F&_timer304=1754361059581 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:47 +0800] "OPTIONS /api/ew/warning/message-list?warnId=10615116-74F9-44D6-9DD9-4CDFDF1B5E8F&_timer304=1754361059581 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:47 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=10615116-74F9-44D6-9DD9-4CDFDF1B5E8F&_timer304=1754361059581 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:47 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=10615116-74F9-44D6-9DD9-4CDFDF1B5E8F&_timer304=1754361059581 HTTP/1.1" 200 323
************* - - [05/Aug/2025:10:30:47 +0800] "GET /api/ew/warning/process-list?warnId=10615116-74F9-44D6-9DD9-4CDFDF1B5E8F&_timer304=1754361059581 HTTP/1.1" 200 735
************* - - [05/Aug/2025:10:30:47 +0800] "GET /api/ew/warning/flow-list?warnId=10615116-74F9-44D6-9DD9-4CDFDF1B5E8F&_timer304=1754361059581 HTTP/1.1" 200 1382
************* - - [05/Aug/2025:10:30:48 +0800] "GET /api/ew/warning/message-list?warnId=10615116-74F9-44D6-9DD9-4CDFDF1B5E8F&_timer304=1754361059581 HTTP/1.1" 200 15400
************* - - [05/Aug/2025:10:30:49 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=CD16B94E-BCBD-46E1-945D-47854D8897CC&_timer304=1754361060969 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:49 +0800] "GET /api/fusion/warning/snapshot-index?warnId=CD16B94E-BCBD-46E1-945D-47854D8897CC&_timer304=1754361060969 HTTP/1.1" 200 633
************* - - [05/Aug/2025:10:30:51 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:52 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4614
************* - - [05/Aug/2025:10:30:56 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&warnGradeId=5&_timer304=1754361068325 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:56 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:56 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220282104203002&_timer304=1754361068325 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:56 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&warnGradeId=5&_timer304=1754361068325 HTTP/1.1" 200 440
************* - - [05/Aug/2025:10:30:56 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1005
************* - - [05/Aug/2025:10:30:56 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220282104203002&_timer304=1754361068325 HTTP/1.1" 200 909
************* - - [05/Aug/2025:10:30:56 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&_timer304=1754361068414 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:56 +0800] "OPTIONS /api/ew/warning/process-list?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&_timer304=1754361068414 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:56 +0800] "OPTIONS /api/ew/warning/message-list?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&_timer304=1754361068414 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:56 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&_timer304=1754361068414 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:56 +0800] "GET /api/ew/warning/process-list?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&_timer304=1754361068414 HTTP/1.1" 200 726
************* - - [05/Aug/2025:10:30:56 +0800] "GET /api/ew/warning/flow-list?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&_timer304=1754361068414 HTTP/1.1" 200 1132
************* - - [05/Aug/2025:10:30:56 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&_timer304=1754361068414 HTTP/1.1" 200 326
************* - - [05/Aug/2025:10:30:57 +0800] "GET /api/ew/warning/message-list?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&_timer304=1754361068414 HTTP/1.1" 200 7327
************* - - [05/Aug/2025:10:30:57 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&warnGradeId=5&_timer304=1754361069572 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:57 +0800] "OPTIONS /api/ew/warning/snapshot-index?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&warnGradeId=5&_timer304=1754361069572 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:57 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&warnGradeId=5&_timer304=1754361069572 HTTP/1.1" 200 440
************* - - [05/Aug/2025:10:30:57 +0800] "GET /api/ew/warning/snapshot-index?warnId=EA5ABFA4-1FA7-48E5-B5D2-FF6F2C707D8A&warnGradeId=5&_timer304=1754361069572 HTTP/1.1" 200 319
************* - - [05/Aug/2025:10:30:59 +0800] "OPTIONS /api/ew/warning/sending-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:30:59 +0800] "POST /api/ew/warning/sending-page-list HTTP/1.1" 200 5783
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754361082008 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754361082013 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754361082008 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+10:31:22&etm=&_timer304=1754361082075 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754361082075 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+11:00&filterCnt=6&_timer304=1754361082075 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754361082075 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754361082075 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754361082135 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [05/Aug/2025:10:31:10 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-02+10:31:22&etm=&_timer304=1754361082075 HTTP/1.1" 200 156
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754361082158 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754361082013 HTTP/1.1" 200 159616
************* - - [05/Aug/2025:10:31:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:10:31:10 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-05+08:00&etm=2025-08-05+11:00&filterCnt=6&_timer304=1754361082075 HTTP/1.1" 200 164
************* - - [05/Aug/2025:10:31:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754361082075 HTTP/1.1" 200 166
************* - - [05/Aug/2025:10:31:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754361082075 HTTP/1.1" 200 169
************* - - [05/Aug/2025:10:31:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [05/Aug/2025:10:31:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [05/Aug/2025:10:31:10 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754361082075 HTTP/1.1" 200 13016
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754361082158 HTTP/1.1" 200 258
************* - - [05/Aug/2025:10:31:10 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:10 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [05/Aug/2025:10:31:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [05/Aug/2025:10:31:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [05/Aug/2025:10:31:11 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [05/Aug/2025:10:31:11 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754361083481 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:11 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754361083504 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:10:31:11 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754361083481 HTTP/1.1" 200 148
************* - - [05/Aug/2025:10:31:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [05/Aug/2025:10:31:12 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754361083504 HTTP/1.1" 200 258
************* - - [05/Aug/2025:10:31:12 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754361082135 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:31:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [05/Aug/2025:10:31:13 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [05/Aug/2025:10:31:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754361091810 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754361091840 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:20 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754361091841 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:31:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754361091810 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:31:20 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754361091841 HTTP/1.1" 200 148
************* - - [05/Aug/2025:10:31:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754361091840 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:36:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754361381809 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:36:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754361381871 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:36:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754361381809 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:36:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754361381871 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:36:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:36:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:36:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754361382088 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:36:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:36:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:36:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:36:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754361382088 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:36:11 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:36:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:36:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:10:36:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754361391981 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:36:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754361391981 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:41:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754361681812 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:41:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754361681875 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:41:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754361681812 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:41:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754361681875 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:41:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:41:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:41:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754361682093 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:41:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:41:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:41:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:41:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:41:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:10:41:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754361682093 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:41:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:41:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754361692105 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:41:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754361692105 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:46:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754362010023 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:46:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754362010023 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:47:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754362041546 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:47:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754362041546 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:47:46 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:47:46 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:47:46 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754362078594 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:47:46 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:47:47 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:47:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754362078656 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:47:47 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:47:47 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:10:47:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754362078656 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:47:47 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:47:48 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754362078594 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:47:48 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:51:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:51:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:51:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754362289988 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:51:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:51:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754362289989 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:51:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754362289981 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:51:18 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:51:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754362289981 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:51:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:51:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754362289988 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:51:18 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:10:51:19 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:51:19 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754362289989 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:51:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:53:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754362442032 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:53:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754362442032 HTTP/1.1" 200 159
************* - - [05/Aug/2025:10:56:46 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:56:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754362618040 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:56:46 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:56:46 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754362618038 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:56:46 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:56:46 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754362618048 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:56:46 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:56:46 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:10:56:46 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754362618038 HTTP/1.1" 200 161
************* - - [05/Aug/2025:10:56:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754362618040 HTTP/1.1" 200 160
************* - - [05/Aug/2025:10:56:46 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:10:56:47 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:10:56:48 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754362618048 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:10:56:48 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:10:58:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754362742169 HTTP/1.1" 200 -
************* - - [05/Aug/2025:10:58:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754362742169 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:01:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754362881815 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:01:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754362881872 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:01:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754362881815 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:01:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754362881872 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:01:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:01:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:01:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754362882095 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:01:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:01:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:11:01:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:11:01:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754362882095 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:11:01:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:01:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:11:01:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:11:04:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754363076252 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:04:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754363076252 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:07:05 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:07:05 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:07:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754363236779 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:07:05 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754363236778 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:07:05 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:07:05 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754363236788 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:07:05 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:07:05 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754363236778 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:07:05 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:11:07:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754363236779 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:07:05 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:11:07:05 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:11:07:06 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754363236788 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:11:07:06 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:11:09:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754363408137 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:09:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754363408137 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:13:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754363624066 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:13:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754363624066 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:16:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754363776643 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:16:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754363776643 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:17:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754363871780 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:17:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754363871780 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:18:13 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:18:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:18:13 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:18:13 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754363905359 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:18:13 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:11:18:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:11:18:14 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754363905359 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:11:18:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:11:20:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754364044037 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:20:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754364044037 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:21:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754364125142 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:21:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754364125142 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:22:32 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:22:32 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:11:24:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754364259853 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:24:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754364259853 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:26:46 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754364418174 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:26:46 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754364418174 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:27:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:27:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:27:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754364452691 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:27:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:27:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:11:27:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:11:27:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754364452691 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:11:27:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:11:28:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754364492494 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:28:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754364492494 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:29:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754364597985 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:29:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754364597985 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:33:18 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:33:18 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:11:34:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754364905551 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:34:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754364905551 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:35:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754364943521 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:35:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754364943521 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:37:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754365047225 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:37:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754365047225 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:39:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:39:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:39:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754365180119 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:39:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:39:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:11:39:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:11:39:29 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754365180119 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:11:39:29 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:11:40:51 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754365262967 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:40:51 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754365262967 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:42:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754365368607 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:42:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754365368607 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:43:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:43:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:11:45:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754365523318 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:45:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754365523318 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:47:29 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:47:29 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:47:29 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:47:29 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754365661512 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:47:29 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:11:47:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:11:47:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754365661512 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:11:47:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:11:48:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754365723411 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:48:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754365723411 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:49:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754365753120 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:49:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754365753120 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:50:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754365851425 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:50:39 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754365851425 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:52:23 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:52:23 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:11:53:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754366002419 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:53:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754366002419 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:54:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754366064159 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:54:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754366064159 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:54:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754366065158 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:54:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754366065158 HTTP/1.1" 200 159
************* - - [05/Aug/2025:11:55:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:55:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754366122165 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:55:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:55:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:55:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754366122172 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:55:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:11:55:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754366122172 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:55:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:11:55:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754366122165 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:11:55:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:11:56:10 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:56:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754366182204 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:56:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754366182204 HTTP/1.1" 200 161
************* - - [05/Aug/2025:11:56:10 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:11:57:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754366242600 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:57:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754366242600 HTTP/1.1" 200 160
************* - - [05/Aug/2025:11:58:25 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:58:25 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:58:25 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754366316657 HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:58:25 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:11:58:25 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:11:58:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:11:58:26 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754366316657 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:11:58:26 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:00:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754366421050 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:00:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754366421050 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:01:22 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:01:22 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:12:03:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754366602162 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:03:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754366602159 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:03:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754366602159 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:03:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754366602162 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:04:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754366668576 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:04:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:04:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:04:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:04:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:12:04:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:12:04:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754366668576 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:12:04:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:05:24 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:05:24 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:12:06:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754366774787 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:06:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754366774787 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:08:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754366903919 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:08:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754366903919 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:09:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754366962172 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:09:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754366962172 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:10:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:10:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:10:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754367029600 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:10:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:10:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:12:10:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:12:10:19 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754367029600 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:12:10:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:11:28 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:11:28 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:12:11:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754367129874 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:11:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754367129874 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:14:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754367294507 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:14:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754367294507 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:17:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754367466662 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:17:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754367466662 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:17:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754367490108 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:17:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754367490108 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:18:27 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:18:27 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:18:27 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754367519519 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:18:27 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:18:27 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:12:18:28 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:12:18:29 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754367519519 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:12:18:29 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:20:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754367646422 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:20:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754367646422 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:22:27 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:22:27 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:12:23:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754367807267 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:23:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754367807267 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:24:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754367861231 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:24:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754367861231 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:25:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754367961079 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:25:49 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754367961079 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:28:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:28:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:28:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:28:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754368109162 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:28:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:12:28:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:12:28:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754368109162 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:12:28:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:29:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754368177703 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:29:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754368177703 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:29:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754368200604 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:29:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754368200604 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:31:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754368317545 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:31:46 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754368317545 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:34:07 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:34:07 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:12:35:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754368526760 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:35:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754368526760 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:35:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754368549544 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:35:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754368549544 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:36:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:36:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:36:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754368582165 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:36:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:36:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754368582170 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:36:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:12:36:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754368582170 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:36:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:12:36:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754368582165 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:12:36:11 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:37:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754368642175 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:37:10 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:37:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:37:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:37:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754368642210 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:37:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:37:10 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:12:37:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:12:37:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754368642175 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:37:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:12:37:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754368642210 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:12:37:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:40:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754368862849 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:40:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754368862849 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:41:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754368882159 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:41:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754368882159 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:42:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754368942167 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:42:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:42:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:42:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:42:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754368942172 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:42:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:12:42:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754368942167 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:42:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:12:42:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754368942172 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:12:42:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:43:10 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:43:10 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:12:45:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754369167060 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:45:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754369167060 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:46:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754369182163 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:46:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754369182163 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:47:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754369244788 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:47:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754369244788 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:48:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:48:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:48:33 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:48:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754369325470 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:48:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:12:48:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:12:48:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754369325470 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:12:48:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:50:10 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:50:10 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:12:50:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754369467164 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:50:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754369467164 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:51:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754369482171 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:51:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754369482171 HTTP/1.1" 200 161
************* - - [05/Aug/2025:12:52:23 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754369555080 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:52:23 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754369555080 HTTP/1.1" 200 160
************* - - [05/Aug/2025:12:52:23 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:52:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754369555286 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:52:23 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:52:23 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:52:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:12:52:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:12:52:25 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754369555286 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:12:52:25 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:12:54:17 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:54:17 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:12:56:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754369782770 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:56:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754369782770 HTTP/1.1" 200 159
************* - - [05/Aug/2025:12:58:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754369936508 HTTP/1.1" 200 -
************* - - [05/Aug/2025:12:58:44 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754369936508 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:02:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754370136028 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:02:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754370136028 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:02:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754370167554 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:02:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754370167554 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:03:15 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:03:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754370207254 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:03:15 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:03:15 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:03:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:03:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:03:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:03:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754370207254 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:04:24 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:04:24 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:04:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754370307363 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:04:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754370307369 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:04:55 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:04:55 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:04:55 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754370307375 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:04:55 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:04:55 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754370307363 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:04:55 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:04:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754370307369 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:04:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:04:57 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754370307375 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:04:57 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:08:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754370508001 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:08:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754370508003 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:08:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:08:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754370508006 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:08:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:08:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:08:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754370508010 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:08:16 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:08:16 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754370508001 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:08:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:08:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754370508003 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:08:16 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:08:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754370508006 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:08:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:08:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754370508010 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:08:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:12:48 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:12:48 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754370779593 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:12:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754370779592 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:12:48 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754370779598 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:12:48 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:12:48 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:12:48 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:12:48 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:12:48 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754370779592 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:12:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754370779593 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:12:48 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:12:48 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:12:49 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754370779598 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:12:49 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:14:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754370876318 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:14:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754370876318 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:17:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754371053585 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:17:22 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754371053585 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:20:47 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754371259134 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:20:47 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754371259134 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:21:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:21:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:21:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:21:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754371292602 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:21:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:21:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:21:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754371292602 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:21:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:21:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754371328513 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:21:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754371328513 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:22:27 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:22:27 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:24:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754371486503 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:24:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754371486503 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:24:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754371486764 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:24:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754371486764 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:25:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:25:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754371522511 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:25:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:25:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:25:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:25:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:25:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754371522511 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:25:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:26:15 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:26:15 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:27:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754371660150 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:27:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754371660154 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:27:28 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754371660155 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:27:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:27:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:27:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754371660157 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:27:28 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:27:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:27:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754371660150 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:27:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:27:28 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:27:28 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754371660155 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:27:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754371660154 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:27:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:27:29 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754371660157 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:27:29 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:33:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754372007640 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:33:16 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754372007640 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:33:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754372040778 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:33:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754372040778 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:37:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754372242408 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:37:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754372242408 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:38:13 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:38:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:38:13 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754372304591 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:38:13 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:38:13 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:38:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:38:14 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754372304591 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:38:14 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:39:22 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:39:22 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:39:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754372406346 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:39:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754372406346 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:40:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754372437143 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:40:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754372437143 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:44:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754372658838 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:44:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754372658838 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:44:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754372707171 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:44:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754372707171 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:45:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754372719170 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:45:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:45:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:45:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754372719175 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:45:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:45:07 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:45:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:45:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754372719170 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:45:07 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:45:08 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:45:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754372719175 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:45:09 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:45:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754372722163 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:45:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754372722163 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:46:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754372782167 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:46:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754372782167 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:47:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754372842165 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:47:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:47:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:47:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:47:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754372842175 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:47:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754372842165 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:47:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:47:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:47:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754372842175 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:47:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:48:10 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:48:10 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:50:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754373019759 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:50:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754373019759 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:53:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754373197747 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:53:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754373197749 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:53:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:53:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:53:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754373197754 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:53:06 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:53:06 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:53:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754373197747 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:53:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:53:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754373197749 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:53:06 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:53:06 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:53:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754373197754 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:53:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:13:55:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754373334004 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:55:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754373334004 HTTP/1.1" 200 159
************* - - [05/Aug/2025:13:56:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754373413071 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:56:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754373413076 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:56:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:56:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:56:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754373413077 HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:56:41 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:56:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:13:56:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754373413071 HTTP/1.1" 200 161
************* - - [05/Aug/2025:13:56:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:13:56:41 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:13:56:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754373413076 HTTP/1.1" 200 160
************* - - [05/Aug/2025:13:56:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:13:56:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754373413077 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:13:56:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:01:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754373687211 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:01:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754373687211 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:02:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754373746497 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:02:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754373746509 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:02:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754373746507 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:02:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:02:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:02:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:02:14 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:02:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754373746497 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:02:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:02:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754373746507 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:02:14 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:02:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:02:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754373746509 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:02:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:06:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754374022843 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:06:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754374022843 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:08:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754374112690 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:08:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754374112690 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754374287894 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754374287899 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754374287921 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754374287926 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754374287928 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:11:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:11:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754374287894 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:11:16 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:11:16 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754374287921 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:11:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:11:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754374287926 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:11:16 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:11:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:11:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:11:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:11:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:11:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754374287928 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:11:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754374287899 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:12:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754374388816 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:12:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754374388816 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:18:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754374719310 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:18:27 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754374719310 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:19:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754374761860 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:19:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754374761860 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:22:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754374942170 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:22:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754374942177 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:22:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:22:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:22:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:22:10 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:22:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754374942194 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:22:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:22:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754374942170 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:22:10 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:22:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754374942194 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:22:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:22:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754374942177 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:22:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:23:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754375018533 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:23:27 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754375018533 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:24:46 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:24:46 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754375098461 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:24:46 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:24:46 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:24:46 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:24:47 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:24:48 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754375098461 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:24:48 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:25:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754375128463 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:25:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754375128463 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:28:47 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754375339453 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:28:48 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754375339453 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:32:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754375533228 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754375533228 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754375543231 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754375543234 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754375543241 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754375543265 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754375543262 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:32:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:32:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754375543231 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:32:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:32:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:32:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754375543241 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:32:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754375543262 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:32:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:32:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:32:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754375543234 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:32:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754375543265 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:32:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:32:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:38:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754375907799 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:38:16 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754375907799 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:38:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754375908168 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:38:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754375908168 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:39:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754375987040 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:39:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:39:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:39:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:39:35 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:39:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754375987043 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:39:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:39:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754375987040 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:39:35 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:39:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:39:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754375987043 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:39:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:41:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754376082166 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:41:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754376082166 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:41:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754376083165 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:41:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:41:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:41:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754376083168 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:41:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:41:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754376083165 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:41:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:41:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:41:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754376083168 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:41:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:41:13 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:41:13 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:43:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754376241662 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:43:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754376241662 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:46:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754376382165 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:46:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754376382165 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:46:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754376383157 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:46:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:46:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:46:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:46:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754376383166 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:46:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754376383157 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:46:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:46:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:46:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754376383166 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:46:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:46:13 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:46:13 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:49:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754376578013 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:49:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754376578013 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:53:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754376802481 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:53:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754376802481 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:55:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754376960491 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:55:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754376960491 HTTP/1.1" 200 159
************* - - [05/Aug/2025:14:56:23 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754376995472 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:56:23 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754376995472 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:57:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:57:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:57:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:57:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754377073321 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:57:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:57:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:57:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754377073321 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:57:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:14:58:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754377131636 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:58:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754377131643 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:58:40 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:58:40 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:58:40 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:58:40 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:58:40 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:58:40 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754377131644 HTTP/1.1" 200 -
************* - - [05/Aug/2025:14:58:40 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754377131636 HTTP/1.1" 200 161
************* - - [05/Aug/2025:14:58:40 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:14:58:40 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:58:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754377131643 HTTP/1.1" 200 160
************* - - [05/Aug/2025:14:58:40 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:14:58:40 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:14:58:41 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754377131644 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:14:58:41 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:01:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754377271949 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:01:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754377271949 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:01:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754377282165 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:01:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754377282165 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:01:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754377317194 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:01:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754377317194 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:02:20 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754377351848 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:02:20 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:02:20 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:02:20 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:02:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:15:02:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:15:02:21 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754377351848 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:15:02:21 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:02:22 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:02:22 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:15:06:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754377585657 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:06:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:06:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:06:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754377585659 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:06:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754377585665 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:06:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754377585663 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:06:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:06:14 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:06:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:15:06:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754377585659 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:06:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754377585663 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:06:14 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:15:06:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754377585657 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:06:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:15:06:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754377585665 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:15:06:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:12:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754377940595 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:12:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754377940595 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:12:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754377972761 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:12:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754377972761 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:16:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754378218425 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:16:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754378218425 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:17:23 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:17:23 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:17:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754378254910 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:17:23 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:17:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:15:17:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:15:17:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754378254910 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:15:17:24 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:18:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754378291914 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:18:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754378291914 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:18:35 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:18:35 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:15:20:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754378429132 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:20:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754378429132 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:23:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754378646682 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:23:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754378646682 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:24:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754378678520 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:24:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754378678520 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:25:36 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754378748186 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:25:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:25:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754378748184 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:25:36 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:25:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:25:36 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:25:36 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754378748203 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:25:36 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754378748184 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:25:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:15:25:36 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:15:25:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754378748203 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:25:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:15:25:37 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754378748186 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:15:25:37 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:26:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:26:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:26:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754378793716 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:26:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754378793710 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:26:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754378793715 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:26:22 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:26:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:26:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:15:26:22 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754378793710 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:26:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754378793715 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:26:22 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:15:26:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:15:26:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754378793716 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:15:26:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:30:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754379029143 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:30:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754379029143 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:32:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754379164542 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:32:33 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754379164542 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:36:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754379390803 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:36:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754379390803 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:36:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754379414712 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:36:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754379414712 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:37:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:37:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:37:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:37:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754379449893 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:37:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:15:37:19 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:15:37:19 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754379449893 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:15:37:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:38:33 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:38:34 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:15:40:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754379634448 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:40:22 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754379634448 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:42:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754379784487 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:42:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754379784487 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:43:30 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754379822133 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:43:30 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754379822133 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:45:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754379967332 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:45:55 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754379967332 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754379998310 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754379998301 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754379998320 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754379998322 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:46:26 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:15:46:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:15:46:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754379998310 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:46:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:15:46:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754379998320 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:46:27 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:15:46:27 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:15:46:27 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:15:46:28 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754379998301 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:15:46:28 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:46:28 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:46:28 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754379998322 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:15:48:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754380132311 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:48:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754380132311 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:52:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754380350781 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:52:19 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754380350781 HTTP/1.1" 200 161
************* - - [05/Aug/2025:15:55:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754380514857 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:55:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754380514857 HTTP/1.1" 200 159
************* - - [05/Aug/2025:15:55:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754380553590 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:55:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754380553590 HTTP/1.1" 200 160
************* - - [05/Aug/2025:15:56:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:56:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:56:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754380590052 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:56:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:56:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:15:56:19 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:15:56:19 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754380590052 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:15:56:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:15:57:22 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:57:22 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:15:59:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754380796600 HTTP/1.1" 200 -
************* - - [05/Aug/2025:15:59:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754380796600 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:01:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754380904283 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:01:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754380904283 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:03:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754381017919 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:03:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754381017919 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:04:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:04:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:04:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754381084567 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:04:33 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:04:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:16:04:33 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:16:04:34 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754381084567 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:16:04:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:16:05:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754381114805 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:05:03 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:05:03 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754381114819 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:05:03 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754381114805 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:05:03 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:16:05:03 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754381114819 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:07:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754381241968 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:07:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754381241968 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:07:47 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754381279304 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:07:47 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754381279304 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:10:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754381442717 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:10:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754381442717 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:11:47 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:11:47 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:11:47 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:11:47 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754381519501 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:11:47 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:16:11:48 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:16:11:49 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754381519501 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:16:11:49 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:16:12:25 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:12:25 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:16:12:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754381581479 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:12:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754381581479 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:15:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754381723966 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:15:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754381723966 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:18:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754381908749 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:18:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754381908749 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:18:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754381940748 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:18:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754381940748 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:19:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754381970137 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:19:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754381970137 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:20:18 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:20:18 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754382030432 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:20:18 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:20:18 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:20:18 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:16:20:19 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:16:20:20 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754382030432 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:16:20:20 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:16:21:12 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:21:12 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:16:22:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754382169403 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:22:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754382169403 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:23:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754382202168 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:23:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754382202168 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:24:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754382265559 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:24:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754382265559 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:25:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754382351954 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:25:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754382351954 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:26:47 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:26:47 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:26:47 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:26:47 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754382419468 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:26:48 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:16:26:48 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:16:26:49 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754382419468 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:16:26:49 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:16:27:27 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:27:27 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:16:29:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754382589767 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:29:38 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754382589767 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:32:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754382753956 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:32:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754382753956 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:32:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754382754159 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:32:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754382754159 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:33:15 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:33:15 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:33:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754382807029 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:33:15 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:33:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:16:33:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:16:33:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754382807029 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:16:33:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:16:34:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754382869744 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:34:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754382869744 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:36:42 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:36:43 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:16:37:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754383045972 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:37:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754383045972 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:37:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754383081159 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:37:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754383081159 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:38:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:38:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:38:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754383102487 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:38:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:38:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:16:38:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:16:38:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754383102487 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:16:38:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:16:40:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754383240218 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:40:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754383240218 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:43:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754383426055 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:43:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754383426055 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:44:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754383456652 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:44:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754383456652 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:44:30 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754383482537 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:44:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754383482537 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:45:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:45:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:45:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:45:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754383522469 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:45:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:16:45:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:16:45:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754383522469 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:16:45:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:16:46:40 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:46:40 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:16:47:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754383647831 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:47:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754383647831 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:49:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754383786102 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:49:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754383786102 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:50:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754383815978 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:50:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754383815978 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:53:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754384013990 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:53:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754384013990 HTTP/1.1" 200 160
************* - - [05/Aug/2025:16:54:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:54:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:54:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754384079600 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:54:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:54:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:16:54:28 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:16:54:29 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754384079600 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:16:54:29 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:16:56:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754384184491 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:56:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754384184491 HTTP/1.1" 200 159
************* - - [05/Aug/2025:16:56:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754384216362 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:56:44 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754384216362 HTTP/1.1" 200 161
************* - - [05/Aug/2025:16:57:57 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:57:57 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:16:59:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754384368272 HTTP/1.1" 200 -
************* - - [05/Aug/2025:16:59:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754384368272 HTTP/1.1" 200 160
************* - - [05/Aug/2025:17:02:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754384558122 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:02:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754384558122 HTTP/1.1" 200 159
************* - - [05/Aug/2025:17:04:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754384671343 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:04:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754384671343 HTTP/1.1" 200 160
************* - - [05/Aug/2025:17:06:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754384821368 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:06:49 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754384821368 HTTP/1.1" 200 161
************* - - [05/Aug/2025:17:08:05 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754384896851 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:08:05 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:08:05 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:08:05 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:08:05 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:17:08:05 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:17:08:06 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754384896851 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:17:08:06 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:17:08:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754384932221 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:08:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754384932221 HTTP/1.1" 200 159
************* - - [05/Aug/2025:17:09:20 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:09:20 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:17:10:29 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754385040885 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:10:29 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754385040885 HTTP/1.1" 200 160
************* - - [05/Aug/2025:17:11:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754385103646 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:11:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754385103646 HTTP/1.1" 200 161
************* - - [05/Aug/2025:17:12:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754385142165 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:12:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:12:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:12:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754385142173 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:12:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:12:10 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:12:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:17:12:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754385142165 HTTP/1.1" 200 160
************* - - [05/Aug/2025:17:12:10 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:17:12:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:17:12:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754385142173 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:17:12:11 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:17:14:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754385267528 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:14:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754385267528 HTTP/1.1" 200 159
************* - - [05/Aug/2025:17:17:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754385484388 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:17:52 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754385484388 HTTP/1.1" 200 161
************* - - [05/Aug/2025:17:18:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754385529189 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:18:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:18:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:18:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:18:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754385529194 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:18:37 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:18:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:17:18:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754385529189 HTTP/1.1" 200 160
************* - - [05/Aug/2025:17:18:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:17:18:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:17:18:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754385529194 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:17:18:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:17:19:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754385594541 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:19:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754385594541 HTTP/1.1" 200 159
************* - - [05/Aug/2025:17:23:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754385831401 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:23:39 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754385831401 HTTP/1.1" 200 161
************* - - [05/Aug/2025:17:24:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754385895166 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:24:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754385895166 HTTP/1.1" 200 159
************* - - [05/Aug/2025:17:26:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754385991575 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:26:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754385991575 HTTP/1.1" 200 160
************* - - [05/Aug/2025:17:27:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:27:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:27:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754386059798 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:27:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:27:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:17:27:28 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:17:27:29 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754386059798 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:17:27:29 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [05/Aug/2025:17:28:32 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:28:32 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************* - - [05/Aug/2025:17:30:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754386264986 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:30:53 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754386264986 HTTP/1.1" 200 161
************* - - [05/Aug/2025:17:31:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754386299290 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:31:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754386299290 HTTP/1.1" 200 159
************* - - [05/Aug/2025:17:31:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754386326204 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:31:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754386326204 HTTP/1.1" 200 160
************* - - [05/Aug/2025:17:32:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754386342413 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:32:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754386342413 HTTP/1.1" 200 161
************* - - [05/Aug/2025:17:33:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:33:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754386402432 HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:33:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:33:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [05/Aug/2025:17:33:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [05/Aug/2025:17:33:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [05/Aug/2025:17:33:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-05+08:00&etm=2025-08-05+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754386402432 HTTP/1.1" 200 441332
************* - - [05/Aug/2025:17:33:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
