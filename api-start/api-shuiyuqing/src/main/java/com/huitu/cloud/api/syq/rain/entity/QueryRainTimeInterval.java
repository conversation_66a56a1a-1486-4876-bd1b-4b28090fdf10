package com.huitu.cloud.api.syq.rain.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 最近时段雨量累计情况统计参数
 * </p>
 *
 * <AUTHOR>
 * @since 2020-8-7
 */
@ApiModel(value="QueryRainTimeInterval对象", description="最近时段雨量累计情况统计参数")
public class QueryRainTimeInterval extends PageBean {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "数据来源")
    private List<String> stType;
    @ApiModelProperty(value = "截止时间 格式 yyyy-MM-dd HH:mm")
    private String tm;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "排序规则")
    private String sort;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

}
