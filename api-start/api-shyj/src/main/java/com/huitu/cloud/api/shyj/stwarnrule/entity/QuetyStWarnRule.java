package com.huitu.cloud.api.shyj.stwarnrule.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 查询预警规则需要的参数
 * </p>
 *
 * <AUTHOR>
 * @since 2020-7-27
 */
@ApiModel(value="QuetyStWarnRule对象", description="查询预警规则需要的参数")
public class QuetyStWarnRule extends PageBean {
    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "预警等级 4:立即转移 5：准备转移 ")
    private String  warnGradeId;

    public String getWarnGradeId() {
        return warnGradeId;
    }

    public void setWarnGradeId(String warnGradeId) {
        this.warnGradeId = warnGradeId;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }
}
