<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.shyj.stwarnrule.mapper.StWarnRuleDao">

    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getRainWarnrule" resultType="com.huitu.cloud.api.shyj.stwarnrule.entity.RainWarnRule">
        SELECT A<PERSON>CD,<PERSON><PERSON>RNGRADEID,ADNM,STNM,WARNGRADENM,
               STTHRESHOLD10,STTHRESHOLD30,STTHRESHOLD60,STTHRESHOLD180,STTHRESHOLD360,STTHRESHOLD720,
               STTHRESHOLD1440
        FROM  (SELECT A.STCD,<PERSON><PERSON>RADEID,MAX(STTHRESHOLD10)STTHRESHOLD10,MAX(STTHRESHOLD30)STTHRESHOLD30,MAX(STTHRESHOLD60)STTHRESHOLD60,
                    MAX(STTHRESHOLD180)STTHRESHOLD180,MAX(STTHRESHOLD360)STTHRESHOLD360,MAX(STTHRESHOLD720)STTHRESHOLD720,MAX(STTHRESHOLD1440)STTHRESHOLD1440
              FROM  (SELECT STCD ,WARNGRADEID,
                    (CASE WHEN STDT='10' THEN STTHRESHOLD ELSE NULL END)STTHRESHOLD10,
                    (CASE WHEN STDT='30' THEN STTHRESHOLD ELSE NULL END)STTHRESHOLD30,
                    (CASE WHEN STDT='60' THEN STTHRESHOLD ELSE NULL END)STTHRESHOLD60,
                    (CASE WHEN STDT='180' THEN STTHRESHOLD ELSE NULL END)STTHRESHOLD180,
                    (CASE WHEN STDT='360' THEN STTHRESHOLD ELSE NULL END)STTHRESHOLD360,
                    (CASE WHEN STDT='720' THEN STTHRESHOLD ELSE NULL END)STTHRESHOLD720,
                    (CASE WHEN STDT='1440' THEN STTHRESHOLD ELSE NULL END)STTHRESHOLD1440
                    FROM STWARNRULE_B WHERE STINDEX='R')A
              GROUP BY A.STCD ,A.WARNGRADEID)A INNER JOIN ST_STBPRP_B ST ON A.STCD=ST.STCD
                                               LEFT JOIN WARNINGGRADE_B D ON A.WARNGRADEID=D.WARNGRADEID
                                               INNER JOIN ST_AD_B S ON A.STCD=S.STCD
                                               LEFT JOIN BSN_ADCD_B B ON S.ADCD=B.ADCD
        WHERE 1=1
        <if test="map.ad!= null and map.ad != ''">
            and left(S.ADCD,#{map.adLevel}) = #{map.ad}
        </if>
        <if test="map.warnGradeId!= null and map.warnGradeId != '' ">
            and  A.WARNGRADEID =#{map.warnGradeId}
        </if>
        order by B.ADCD
    </select>
    <select id="getSwWarnrule" resultType="com.huitu.cloud.api.shyj.stwarnrule.entity.SwWarnRule">
        SELECT a.STCD,ADNM,STNM,a.STTHRESHOLD,a.WARNGRADEID,d.WARNGRADENM
        FROM STWARNRULE_B A
                LEFT JOIN ST_STBPRP_B ST ON A.STCD=ST.STCD
                left join WARNINGGRADE_B d on a.WARNGRADEID=d.WARNGRADEID
                INNER JOIN ST_AD_B S ON A.STCD=S.STCD
                LEFT JOIN BSN_ADCD_B B ON S.ADCD=B.ADCD
        WHERE STINDEX='Z'
        <if test="map.ad!= null and map.ad != ''">
            and left(S.ADCD,#{map.adLevel}) = #{map.ad}
        </if>
        <if test="map.warnGradeId!= null and map.warnGradeId != '' ">
            and a.WARNGRADEID =#{map.warnGradeId}
        </if>
        order by B.ADCD
    </select>

</mapper>
