package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 调查成果统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-15
 */
@TableName("DT_SURVEY_S")
@ApiModel(value="DtSurveyS对象", description="调查成果统计表")
public class DtSurvey extends DtExtendInfo {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "重要沿河村落居民户总数")
    @TableField("FLRVVLGCT")
    private Integer flrvvlgct;

    @ApiModelProperty(value = "危险区总数")
    @TableField("DANADCT")
    private Integer danadct;

    @ApiModelProperty(value = "重要城（集）镇居民总数")
    @TableField("DTRESIDENTCT")
    private Integer dtresidentct;

    @ApiModelProperty(value = "防治区企事业单位总数")
    @TableField("BSNSSINFOCT")
    private Integer bsnssinfoct;

    @ApiModelProperty(value = "历史山洪总数")
    @TableField("HSFWATERCT")
    private Integer hsfwaterct;

    @ApiModelProperty(value = "沟道横断面总数")
    @TableField("HSURFACECT")
    private Integer hsurfacect;

    @ApiModelProperty(value = "沟道纵断面总数")
    @TableField("VSURFACECT")
    private Integer vsurfacect;

    @ApiModelProperty(value = "需防洪治理山洪沟总数")
    @TableField("GULLYCT")
    private Integer gullyct;


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getFlrvvlgct() {
        return flrvvlgct;
    }

    public void setFlrvvlgct(Integer flrvvlgct) {
        this.flrvvlgct = flrvvlgct;
    }

    public Integer getDanadct() {
        return danadct;
    }

    public void setDanadct(Integer danadct) {
        this.danadct = danadct;
    }

    public Integer getDtresidentct() {
        return dtresidentct;
    }

    public void setDtresidentct(Integer dtresidentct) {
        this.dtresidentct = dtresidentct;
    }

    public Integer getBsnssinfoct() {
        return bsnssinfoct;
    }

    public void setBsnssinfoct(Integer bsnssinfoct) {
        this.bsnssinfoct = bsnssinfoct;
    }

    public Integer getHsfwaterct() {
        return hsfwaterct;
    }

    public void setHsfwaterct(Integer hsfwaterct) {
        this.hsfwaterct = hsfwaterct;
    }

    public Integer getHsurfacect() {
        return hsurfacect;
    }

    public void setHsurfacect(Integer hsurfacect) {
        this.hsurfacect = hsurfacect;
    }

    public Integer getVsurfacect() {
        return vsurfacect;
    }

    public void setVsurfacect(Integer vsurfacect) {
        this.vsurfacect = vsurfacect;
    }

    public Integer getGullyct() {
        return gullyct;
    }

    public void setGullyct(Integer gullyct) {
        this.gullyct = gullyct;
    }

}
