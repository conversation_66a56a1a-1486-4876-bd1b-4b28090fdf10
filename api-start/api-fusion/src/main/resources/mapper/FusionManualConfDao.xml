<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.fusion.mapper.FusionManualConfDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getConf" resultType="com.huitu.cloud.api.fusion.entity.base.FusionManualConf">
        SELECT TOP (1) CALL_TYPE, MANUAL_FLAG, MANUAL_DELAY, MANUAL_EXPIRED
        FROM EW_MANUAL_CONF
        WHERE CALL_TYPE IN ('0', #{callType})
        ORDER BY CALL_TYPE DESC
    </select>
</mapper>