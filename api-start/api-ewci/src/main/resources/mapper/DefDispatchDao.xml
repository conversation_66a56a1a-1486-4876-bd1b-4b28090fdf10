<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.consult.mapper.DefDispatchDao">

    <select id="getRiverAffectadVo" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverAffectadVo">
        SELECT COUNT(DISTINCT XADCD) XADCD_NUM, COUNT(DISTINCT (CASE WHEN LTRIM(RTRIM(ADCD)) = '' THEN NULL ELSE ADCD END)) ADCD_NUM,
        SUM(XZC_NUM) XZC_NUM, SUM(ZRT_NUM) ZRT_NUM, SUM(HTCOUNT) HTCOUNT, SUM(PTCOUNT) PTCOUNT
        FROM (SELECT LEFT(PADCD, 6) + '*********' XADCD, ADCD, XZC_NUM, ZRT_NUM, HTCOUNT, PTCOUNT FROM BNS_RIVER_AFFECTAD
        <where>
            <if test="rvCode!= null and rvCode !=''">
                AND RV_CODE = #{rvCode}
            </if>
        </where>
        ) A
    </select>

    <select id="getRiverAffectadDetailsList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.RiverAffectadDetailsVo">
        SELECT DISTINCT LEFT(A.PADCD, 6) + '*********' XADCD, B.ADNM XADNM
        FROM BNS_RIVER_AFFECTAD A
                 LEFT JOIN MDT_ADCDINFO_B B
                           ON LEFT(A.PADCD, 6) + '*********' = B.ADCD
        WHERE A.RV_CODE = #{rvCode}
        ORDER BY XADCD ASC
    </select>

    <select id="getRiverAffectadDetailsChildList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.RiverAffectadDetailsChildVo">
        SELECT A.RV_CODE,
               A.BANK,
               LEFT(A.PADCD, 6) + '*********' XADCD,
               B.ADNM                         XADNM,
               A.ADCD                         ADCD,
               A.ADNM                         ADNM,
               ISNULL(A.XZC_NUM, 0)           XZC_NUM,
               ISNULL(A.ZRT_NUM, 0)           ZRT_NUM,
               ISNULL(A.HTCOUNT, 0)           HTCOUNT,
               ISNULL(A.PTCOUNT, 0)           PTCOUNT
        FROM BNS_RIVER_AFFECTAD A
                 LEFT JOIN MDT_ADCDINFO_B B
                           ON LEFT(A.PADCD, 6) + '*********' = B.ADCD
        WHERE A.RV_CODE = #{rvCode}
        ORDER BY XADCD ASC, A.BANK ASC
    </select>
    <select id="getRiverAffectadDetailsAllList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.RiverAffectadDetailsChildVo">
        SELECT O.RV_CODE,
               B.RV_NAME,
               COUNT(DISTINCT XADCD) XADCD_NUM,
               COUNT(DISTINCT ADCD)  ADCD_NUM,
               SUM(XZC_NUM)          XZC_NUM,
               SUM(ZRT_NUM)          ZRT_NUM,
               SUM(HTCOUNT)          HTCOUNT,
               SUM(PTCOUNT)          PTCOUNT
        FROM (SELECT A.RV_CODE,
                     LEFT(A.ADCD, 6) + '*********' XADCD,
                     A.ADCD                        ADCD,
                     ISNULL(A.XZC_NUM, 0)          XZC_NUM,
                     ISNULL(A.ZRT_NUM, 0)          ZRT_NUM,
                     ISNULL(A.HTCOUNT, 0)          HTCOUNT,
                     ISNULL(A.PTCOUNT, 0)          PTCOUNT
              FROM BNS_RIVER_AFFECTAD A) O
                 LEFT JOIN BNS_RIVER_BAS B ON O.RV_CODE = B.RV_CODE
        GROUP BY O.RV_CODE, B.SNO, B.RV_NAME
        ORDER BY B.SNO ASC
    </select>
    <select id="getRiverDikeDetailsList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDikeDetailsVo">
        SELECT DISTINCT A.ADCD, B.ADNM, A.REACH_LEN
        FROM BNS_RIVER_DIKE_S A
                 LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        WHERE A.RV_CODE = #{rvCode}
        ORDER BY A.ADCD ASC
    </select>
    <select id="getRiverDikeDetailsChildList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDikeDetailsChildVo">
        SELECT ADCD,
               FSTAND,
               TOTAL_DIKE_LEN_L,
               TOTAL_DIKE_LEN_R,
               UP_DIKE_LEN_L,
               UP_DIKE_LEN_R,
               NOT_DIKE_LEN_L,
               NOT_DIKE_LEN_R
        FROM BNS_RIVER_DIKE_S
        WHERE RV_CODE = #{rvCode}
        ORDER BY SNO
    </select>
    <select id="getRiverDikeDetailsAllList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDikeDetailsChildVo">
        SELECT A.RV_CODE,
               B.RV_NAME,
               C.RV_BAS_AREA,
               B.REACH_LEN,
               ISNULL(SUM(TOTAL_DIKE_LEN_L), 0) TOTAL_DIKE_LEN_L,
               ISNULL(SUM(TOTAL_DIKE_LEN_R), 0) TOTAL_DIKE_LEN_R,
               ISNULL(SUM(UP_DIKE_LEN_L), 0)    UP_DIKE_LEN_L,
               ISNULL(SUM(UP_DIKE_LEN_R), 0)    UP_DIKE_LEN_R,
               ISNULL(SUM(NOT_DIKE_LEN_L), 0)   NOT_DIKE_LEN_L,
               ISNULL(SUM(NOT_DIKE_LEN_R), 0)   NOT_DIKE_LEN_R
        FROM BNS_RIVER_DIKE_S A
                 LEFT JOIN BNS_RIVER_BAS B ON A.RV_CODE = B.RV_CODE
                 LEFT JOIN ATT_RV_BASE C ON A.RV_CODE = C.RV_CODE
        GROUP BY A.RV_CODE, B.RV_NAME, C.RV_BAS_AREA, B.REACH_LEN, B.SNO
        ORDER BY B.SNO ASC
    </select>
    <select id="getRiverDtgcVo" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDtgcVo">
        SELECT ISNULL(SUM(PIPECT), 0) PIPECT, ISNULL(SUM(LHCT), 0) LHCT, ISNULL(SUM(WAGACT), 0) WAGACT,
        ISNULL(SUM(PUSTCT), 0) PUSTCT
        FROM BNS_RIVER_DTGC
        WHERE 1=1
        <if test="rvCode!= null and rvCode !=''">
            AND RV_CODE = #{rvCode}
        </if>
    </select>
    <select id="getRiverDtgcList" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDtgcVo">
        SELECT A.RV_CODE,
               A.ADCD,
               B.ADNM,
               ISNULL(A.PIPECT, 0) + ISNULL(A.LHCT, 0) + ISNULL(A.WAGACT, 0) + ISNULL(A.PUSTCT, 0) TOTAL,
               ISNULL(A.PIPECT, 0)                                                                 PIPECT,
               ISNULL(A.LHCT, 0)                                                                   LHCT,
               ISNULL(A.WAGACT, 0)                                                                 WAGACT,
               ISNULL(A.PUSTCT, 0)                                                                 PUSTCT
        FROM BNS_RIVER_DTGC A
                 LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        WHERE A.RV_CODE = #{rvCode}
        ORDER BY A.ADCD ASC
    </select>
    <select id="getRiverDtgcAllList" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDtgcVo">
        SELECT O.RV_CODE,
               B.RV_NAME,
               SUM(TOTAL)  TOTAL,
               SUM(PIPECT) PIPECT,
               SUM(LHCT)   LHCT,
               SUM(WAGACT) WAGACT,
               SUM(PUSTCT) PUSTCT
        FROM (SELECT A.RV_CODE,
                     ISNULL(A.PIPECT, 0) + ISNULL(A.LHCT, 0) + ISNULL(A.WAGACT, 0) + ISNULL(A.PUSTCT, 0) TOTAL,
                     ISNULL(A.PIPECT, 0)                                                                 PIPECT,
                     ISNULL(A.LHCT, 0)                                                                   LHCT,
                     ISNULL(A.WAGACT, 0)                                                                 WAGACT,
                     ISNULL(A.PUSTCT, 0)                                                                 PUSTCT
              FROM BNS_RIVER_DTGC A) O
                 LEFT JOIN BNS_RIVER_BAS B ON O.RV_CODE = B.RV_CODE
        GROUP BY O.RV_CODE, B.RV_NAME, B.SNO
        ORDER BY B.SNO ASC
    </select>
    <select id="getRiverDesingVo" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDesingVo">
        SELECT RV_CODE,
               SNO,
               CL,
               AREA,
               DESING_ONE,
               DESING_TWO,
               DESING_THREE,
               DESING_FIVE,
               REMARK
        FROM BNS_RIVER_DESING
        WHERE RV_CODE = #{rvCode}
        ORDER BY SNO ASC
    </select>
    <select id="getRiverProList" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverProVo">
        SELECT A.RV_CODE,
        A.RV_NAME,
        A.SNO,
        A.STAR_NAME,
        A.END_NAME,
        A.LGTD,
        A.LTTD,
        A.PRO_DIST,
        A.PRO_HOUR
        FROM BNS_RIVER_PRO A
        WHERE 1=1
        <if test="rvCode!= null and rvCode !=''">
            AND A.RV_CODE = #{rvCode}
        </if>
        ORDER BY A.SNO ASC
    </select>

    <select id="getRiverDikeVo" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDikeVo">
        SELECT SUM(A.DIKE_LEN) DIKE_LEN,
        SUM(A.UP_DIKE_LEN) UP_DIKE_LEN,
        SUM(A.NOT_DIKE_LEN) NOT_DIKE_LEN,
        SUM(B.RV_BAS_AREA) RV_BAS_AREA,
        SUM(C.REACH_LEN) REACH_LEN FROM (
        SELECT RV_CODE ,
        ISNULL(SUM(TOTAL_DIKE_LEN_L), 0) + ISNULL(SUM(TOTAL_DIKE_LEN_R), 0) DIKE_LEN,
        ISNULL(SUM(UP_DIKE_LEN_L), 0) + ISNULL(SUM(UP_DIKE_LEN_R), 0) UP_DIKE_LEN,
        ISNULL(SUM(NOT_DIKE_LEN_L), 0) + ISNULL(SUM(NOT_DIKE_LEN_R), 0) NOT_DIKE_LEN
        FROM BNS_RIVER_DIKE_S
        WHERE 1=1
        <if test="rvCode!= null and rvCode !=''">
            AND RV_CODE = #{rvCode}
        </if>
        GROUP BY RV_CODE)A
        LEFT JOIN ATT_RV_BASE B ON A.RV_CODE = B.RV_CODE
        LEFT JOIN BNS_RIVER_BAS C ON A.RV_CODE = C.RV_CODE
    </select>
    <select id="getRiverBaseDikeVo" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDikeVo">
        SELECT ISNULL(SUM(B_LEN), 0) + ISNULL(SUM(D_LEN), 0) + ISNULL(SUM(BD_UPGOVERN_LEN), 0) +
        ISNULL(SUM(BD_NOTGOVERN_LEN), 0) B_LEN,
        ISNULL(SUM(B_UPGOVERN_LEN), 0) + ISNULL(SUM(D_UPGOVERN_LEN), 0) + ISNULL(SUM(BD_UPGOVERN_LEN), 0) B_UP_LEN,
        ISNULL(SUM(B_NOTGOVERN_LEN), 0) + ISNULL(SUM(D_NOTGOVERN_LEN), 0) + ISNULL(SUM(BD_NOTGOVERN_LEN), 0) B_NOT_LEN
        FROM BNS_RIVER_BASE_DIKE_S
        WHERE 1=1
        <if test="rvCode!= null and rvCode !=''">
            AND RV_CODE = #{rvCode}
        </if>
    </select>
    <select id="getRiverDpdsVo" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDikeVo">
        SELECT ISNULL(SUM(DPDS_LEN), 0) DPDS_LEN,
        ISNULL(COUNT(DISTINCT DPDS_CODE), 0) DPDS_NUM
        FROM BNS_RIVER_DPDS
        WHERE 1=1
        <if test="rvCode!= null and rvCode !=''">
            AND RV_CODE = #{rvCode}
        </if>
    </select>
    <select id="getRiverBaseDikeList" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverBaseDikeVo">
        SELECT A.ADCD,
        B.ADNM,
        A.B_UPGOVERN_LEN B_UP_LEN,
        A.B_NOTGOVERN_LEN B_NOT_LEN,
        A.BD_UPGOVERN_LEN BD_UP_LEN,
        A.BD_NOTGOVERN_LEN BD_NOT_LEN,
        ISNULL(A.B_UPGOVERN_LEN, 0) + ISNULL(A.B_NOTGOVERN_LEN, 0) + ISNULL(A.BD_UPGOVERN_LEN, 0) +
        ISNULL(A.BD_NOTGOVERN_LEN, 0) TOTAL_LEN
        FROM BNS_RIVER_BASE_DIKE_S A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        WHERE 1=1
        <if test="rvCode!= null and rvCode !=''">
            AND A.RV_CODE = #{rvCode}
        </if>
        ORDER BY TOTAL_LEN DESC
    </select>
    <select id="getRiverBaseDikeAllList" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverBaseDikeVo">
        SELECT A.RV_CODE,
               B.RV_NAME,
               ISNULL(SUM(A.B_UPGOVERN_LEN), 0)   B_UP_LEN,
               ISNULL(SUM(A.B_NOTGOVERN_LEN), 0)  B_NOT_LEN,
               ISNULL(SUM(A.BD_UPGOVERN_LEN), 0)  BD_UP_LEN,
               ISNULL(SUM(A.BD_NOTGOVERN_LEN), 0) BD_NOT_LEN,
               SUM(ISNULL(A.B_UPGOVERN_LEN, 0) + ISNULL(A.B_NOTGOVERN_LEN, 0) + ISNULL(A.BD_UPGOVERN_LEN, 0) +
                   ISNULL(A.BD_NOTGOVERN_LEN, 0)) TOTAL_LEN
        FROM BNS_RIVER_BASE_DIKE_S A
                 LEFT JOIN BNS_RIVER_BAS B ON A.RV_CODE = B.RV_CODE

        GROUP BY A.RV_CODE, B.RV_NAME, B.SNO
        ORDER BY B.SNO ASC
    </select>
    <select id="getRiverDpdsList" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDpdsVo">
        SELECT A.DPDS_CODE, LEFT(A.DPDS_CODE,6)+'*********' ADCD,B.ADNM,A.DPDS_SHORT_NAME, A.DPDS_LEN
        FROM BNS_RIVER_DPDS A
        LEFT JOIN MDT_ADCDINFO_B B ON LEFT(A.DPDS_CODE,6)+'*********' = B.ADCD
        WHERE 1=1
        <if test="rvCode!= null and rvCode !=''">
            AND A.RV_CODE = #{rvCode}
        </if>
        ORDER BY A.DPDS_LEN DESC
    </select>
    <select id="getRiverDpdsAllList" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverDpdsVo">
        SELECT A.RV_CODE, B.RV_NAME, COUNT(DISTINCT DPDS_CODE) TOTAL, SUM(DPDS_LEN) DPDS_LEN
        FROM BNS_RIVER_DPDS A
                 LEFT JOIN BNS_RIVER_BAS B ON A.RV_CODE = B.RV_CODE
        GROUP BY A.RV_CODE, B.RV_NAME, B.SNO
        ORDER BY B.SNO ASC
    </select>
    <select id="getRsvrInfoVoList" resultType="com.huitu.cloud.api.ewci.consult.entity.RsvrInfoVo">
        WITH RSVR_R AS (SELECT STCD, MAX(TM) TM FROM ST_RSVR_R
        <where>
            <if test="map.stm != null and map.stm !=''">
                and TM >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm !=''">
                and TM &lt; #{map.etm}
            </if>
        </where>
        GROUP BY STCD)
        SELECT A.STCD, B.STNM, E.TOT_CAP, COALESCE(E.LOW_LEFT_LAT, L.PLGTD, B.LTTD) LTTD, COALESCE(E.LOW_LEFT_LONG, L.PLTTD, B.LGTD) LGTD,
        E.WAT_SHED_AREA, E.ENG_SCAL, F.DSFLLV, F.CHFLLV, G.HHRZ, G.HHRZTM, G.HMXINQ, G.HMXINQTM, G.HMXOTQ, G.HMXOTQTM, H.FSLTDZ,
        J.RZ, J.RWPTN, K.UPH
        FROM BNS_RIVER_ST A
        LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
        LEFT JOIN BSN_OBJONLY_B C ON C.OBJCD = A.STCD AND C.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B D ON D.[OBJID] = C.[OBJID] AND D.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE E ON D.OBJCD = E.RES_CODE
        LEFT JOIN FHGC_RSPP F ON E.RES_CODE = F.RES_CODE
        LEFT JOIN ST_RSVRFCCH_B G ON G.STCD = A.STCD
        LEFT JOIN ST_RSVRFSR_B H ON H.STCD = A.STCD AND RIGHT(CONVERT(VARCHAR, GETDATE(), 112), 4) BETWEEN BGMD AND EDMD
        LEFT JOIN RSVR_R I ON I.STCD = A.STCD
        LEFT JOIN ST_RSVR_R J ON J.STCD = A.STCD AND I.TM = J.TM
        LEFT JOIN BSN_RSVRINFOEX_B K ON K.RES_CODE = E.RES_CODE
        LEFT JOIN BSN_STADTP_B L ON L.STCD = A.STCD
        WHERE B.STTP IN ('RR','RQ') AND A.RV_CODE IN
        <foreach collection="map.list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getRiverInfoVoList" resultType="com.huitu.cloud.api.ewci.consult.entity.RiverInfoVo">
        WITH RIVER_R AS (SELECT STCD, MAX(TM) TM FROM ST_RIVER_R
        <where>
            <if test="map.stm != null and map.stm !=''">
                and TM >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm !=''">
                and TM &lt; #{map.etm}
            </if>
        </where>
        GROUP BY STCD)
        SELECT A.STCD, B.STNM, B.STAZT, B.STTP, E.PLGTD LGTD, E.PLTTD LTTD, C.WRQ, C.WRZ, C.GRQ, C.GRZ, C.LDKEL,
        C.RDKEL, D.FLOW,
        ISNULL(D.ONE_FLOW, 0) ONE_FLOW, D.ONE_TM, ISNULL(D.TWO_FLOW, 0) TWO_FLOW, D.TWO_TM, ISNULL(D.THREE_FLOW,0)
        THREE_FLOW,
        D.THREE_TM, J.Z, J.WPTN, J.Z - C.WRZ ZWRZ,
        J.Z - C.GRZ ZGRZ
        FROM BNS_RIVER_ST A
        LEFT JOIN ST_STBPRP_B B ON A.STCD = B.STCD
        LEFT JOIN ST_RVFCCH_B C ON A.STCD = C.STCD
        LEFT JOIN BSN_STADTP_B E ON A.STCD = E.STCD
        LEFT JOIN BNS_ST_FLOW D ON A.STCD = D.STCD
        LEFT JOIN RIVER_R I ON A.STCD = I.STCD
        LEFT JOIN ST_RIVER_R J ON A.STCD = J.STCD AND I.TM = J.TM
        WHERE B.STTP IN ('ZZ','ZQ') AND A.RV_CODE IN
        <foreach collection="map.list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getBnsRiverBas" resultType="com.huitu.cloud.api.ewci.consult.entity.BnsRiverBas">
        SELECT RV_CODE,
               RV_NAME,
               LEFT_UP_LGTD,
               LEFT_UP_LTTD,
               LEFT_DOWN_LGTD,
               LEFT_DOWN_LTTD,
               RIGHT_UP_LGTD,
               RIGHT_UP_LTTD,
               RIGHT_DOWN_LGTD,
               RIGHT_DOWN_LTTD
        FROM BNS_RIVER_BAS
        WHERE RV_CODE = #{rvCode}
    </select>


</mapper>