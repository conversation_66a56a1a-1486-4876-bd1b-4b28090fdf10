package com.huitu.cloud.api.screen.fhjz.service.impl;

import com.huitu.cloud.api.screen.fhjz.Constants;
import com.huitu.cloud.api.screen.fhjz.entity.RainCountVo;
import com.huitu.cloud.api.screen.fhjz.entity.RainVo;
import com.huitu.cloud.api.screen.fhjz.mapper.RainScreenDao;
import com.huitu.cloud.api.screen.fhjz.service.RealRainService;
import com.huitu.cloud.api.screen.fhjz.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 雨量实时获取 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Service
public class RealRainServiceImpl implements RealRainService {

    private static final Logger logger = LoggerFactory.getLogger(RealRainServiceImpl.class);

    @Autowired
    private RainScreenDao rainScreenDao;

    @Override
    public RainCountVo statisticsRainCount(String code,String type,String stm,String etm) {
        try{
//           List<String> queryTimes = DateTimeUtils.queryRainTime(type,hour);
            RainCountVo result = null;
            if("pro".equals(code)){
                //统计全省雨量测站
                if(Constants.RAIN_REAL_TYPE.equals(type)){
                    //统计实时
                    result = rainScreenDao.staticProRainRealCount(stm,etm);
                }else if(Constants.RAIN_FORECAST_TYPE.equals(type)){
                    //统计预测
                    result = rainScreenDao.staticProRainForecastCount(stm,etm);
                }
            }else{
                //统计流域测站
                if(Constants.RAIN_REAL_TYPE.equals(type)){
                    //统计实时
                    result = rainScreenDao.staticRainRealCount(stm,etm,code);
                }else if(Constants.RAIN_FORECAST_TYPE.equals(type)){
                    //统计预测
                    result =  rainScreenDao.staicRainForecastCount(stm,etm,code);
                }
            }
            return result;
        }catch (Exception ex){
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
        }
        return null;
    }

    @Override
    public List<Map<String,Object>> statisticsRainValue(String type,String stm,String etm) {
        try{
//            List<String> queryTimes = DateTimeUtils.queryRainTime(type,hour);
            List<Map<String,Object>> proResultList = null;
            List<Map<String,Object>> lyResultList = null;
            if(Constants.RAIN_REAL_TYPE.equals(type)){
                proResultList = rainScreenDao.statisticsRealRainValueByPro(stm,etm);
                lyResultList = rainScreenDao.statisticsRealRainValueByLy(stm,etm);
            }else if(Constants.RAIN_FORECAST_TYPE.equals(type)){
                proResultList = rainScreenDao.statisticsForecastRainValueByPro(stm,etm);
                lyResultList = rainScreenDao.statisticsForecastRainValueByLy(stm,etm);
            }
            return Stream.concat(proResultList.stream(),lyResultList.stream()).collect(Collectors.toList());
        }catch (Exception ex){
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
        }
        return null;
    }

    @Override
    public List<RainVo> queryRainList(String code,String stType,String rstm,String retm,String fstm,String fetm,Boolean isAnalys) {
        List<RainVo> result = null;
        try{
            if("pro".equals(code)){
                result = rainScreenDao.queryProRainList(rstm,retm,fstm,fetm,stType,isAnalys);
            }else{
                result = rainScreenDao.querylyRainList(code,rstm,retm,fstm,fetm,stType,isAnalys);
            }
            return result;
        }catch (Exception ex){
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
        }
        return result;
    }

    @Override
    public List<Map<String,Object>> statisticsSoilVaule(String stm,String etm) {
        List<Map<String,Object>> result = null;
        try{
//            List<String> queryRealTimes = DateTimeUtils.queryRainTime(Constants.RAIN_REAL_TYPE,72);
            result = rainScreenDao.statisticSoilValue(stm, etm);
            return  result;
        }catch (Exception ex){
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
        }
        return result;
    }
}
