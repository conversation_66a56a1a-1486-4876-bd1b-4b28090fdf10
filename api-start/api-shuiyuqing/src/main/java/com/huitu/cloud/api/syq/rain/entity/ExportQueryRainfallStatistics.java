package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 流域降雨统计导出
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-29
 */
@ApiModel(value="ExportQueryRainfallStatistics对象", description="流域降雨统计导出")
public class ExportQueryRainfallStatistics {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "测站归属类型  1、水文，2、山洪  3、气象 多个值以逗号隔开")
    private String stTypes;


    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getStTypes() {
        return stTypes;
    }

    public void setStTypes(String stTypes) {
        this.stTypes = stTypes;
    }
}
