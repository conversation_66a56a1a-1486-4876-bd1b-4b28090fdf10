package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 在建工程
 */
@Data
@ApiModel(value = "在建工程")
public class BnsInspectConEn extends BnsInspectReportForm {

    @ApiModelProperty(value = "在建工程编码")
    private String enCode;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;

    @ApiModelProperty(value = "在建工程名称")
    private String enName;
}
