package com.huitu.cloud.api.fusion.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.fusion.entity.request.*;
import com.huitu.cloud.api.fusion.entity.response.*;
import com.huitu.cloud.api.fusion.exception.FusionException;
import com.huitu.cloud.api.fusion.service.FusionCallFeedbackService;
import com.huitu.cloud.api.fusion.service.FusionWarningService;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import com.huitu.cloud.api.usif.util.LoginUtils;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 山洪雨量升级预警
 *
 * <AUTHOR>
 */
@Api(tags = "000山洪雨量升级预警服务")
@RestController
@RequestMapping("/api/fusion/warning")
public class FusionWarningResource extends AbstractApiResource implements ApiResource {

    @Autowired
    private FusionWarningService baseService;

    @Autowired
    private FusionCallFeedbackService feedbackService;

    @Autowired
    private LoginUtils loginUtils;

    @Override
    public String getUuid() {
        return "cb2c20c3-eb3a-432a-b724-c1b0f942eff5";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @ApiOperation(value = "获取山洪雨量升级预警分页列表", notes = "作者：zhangbx")
    @PostMapping("page-list")
    public ResponseEntity<SuccessResponse<IPage<FusionWarningRecordResp>>> getPageList(@RequestBody @Validated FusionWarningRecordReq request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(request)));
    }


    @ApiOperation(value = "获取山洪雨量升级预警底部统计", notes = "作者：zhangbx")
    @GetMapping(value = "select-ascription-type-count")
    public ResponseEntity<SuccessResponse<AscriptionTypeCountResp>> getAscriptionTypeCountNew(@Validated AscriptionTypeCountReq request) throws Exception {
        AscriptionTypeCountResp result = baseService.getAscriptionTypeCount(request);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "获取山洪预警监测数据快照", notes = "作者：曹宝金")
    @GetMapping("snapshot-monitor")
    public ResponseEntity<SuccessResponse<FusionSnapshotMonitorResp>> getSnapshotMonitor(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSnapshotMonitor(warnId)));
    }

    @ApiOperation(value = "获取山洪雨量升级预警基本信息", notes = "作者：zhangbx")
    @GetMapping("snapshot-index")
    public ResponseEntity<SuccessResponse<FusionSnapshotIndexResp>> getSnapshotIndex(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSnapshotIndex(warnId)));
    }

    @ApiOperation(value = "获取山洪雨量升级预警发送情况", notes = "作者：zhangbx")
    @PostMapping("sending-page-list")
    public ResponseEntity<SuccessResponse<IPage<FusionWarningSendingResp>>> getSendingPageList(
            @RequestBody @Validated FusionWarningSendingReq request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSendingPageList(request)));
    }

    @ApiOperation(value = "获取山洪预警分页列表(1、2、3级)", notes = "作者：zhangbx")
    @PostMapping("detail-page-list")
    public ResponseEntity<SuccessResponse<IPage<FusionWarningRecordResp>>> getDetailPageList(@RequestBody @Validated FusionWarningRecordReq request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getDetailPageList(request)));
    }

    @ApiOperation(value = "获取最新的山洪预警分页列表(1、2、3级)", notes = "作者：zhangbx")
    @PostMapping("latest-warn-page-list")
    public ResponseEntity<SuccessResponse<IPage<FusionWarningRecordResp>>> getWarnLatestPageList(@RequestBody @Validated FusionWarningRecordReq request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getWarnLatestPageList(request)));
    }

    @ApiOperation(value = "判断平台是否启用了山洪监测预警", notes = "作者：zhangbx")
    @GetMapping("is-enabled")
    public ResponseEntity<SuccessResponse<Boolean>> isEnabled() {
        boolean enabled = false;
        try {
            String platformId = getPlatformId();
            if (StringUtils.isNotBlank(platformId)) {
                enabled = baseService.isEnabled(platformId);
            }
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", enabled));
    }

    @ApiOperation(value = "获取等待人工通知的预警接收人列表", notes = "作者：zhangbx")
    @GetMapping("not-notice-receiver-list")
    public ResponseEntity<SuccessResponse<List<FusionNoticeReceiverResponse>>> getNotNoticeReceiverList() {
        List<FusionNoticeReceiverResponse> result = new ArrayList<>();
        try {
            String platformId = getPlatformId();
            if (StringUtils.isNotBlank(platformId)) {
                result = feedbackService.getNotNoticeReceiverList(platformId);
            }
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", result));
    }

    @ApiOperation(value = "获取已人工通知的预警接收人列表", notes = "作者：zhangbx")
    @PostMapping("notified-receiver-list")
    public ResponseEntity<SuccessResponse<List<FusionNoticeReceiverResponse>>> getNotifiedReceiverList(@RequestBody @Validated FusionNoticeReceiverRequest request) {
        List<FusionNoticeReceiverResponse> result = new ArrayList<>();
        try {
            String platformId = getPlatformId();
            if (StringUtils.isNotBlank(platformId)) {
                request.setPlatformId(platformId);
                result = feedbackService.getNotifiedReceiverList(request);
            }
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", result));
    }

    @ApiOperation(value = "获取预警摘要列表", notes = "作者：zhangbx")
    @PostMapping("digest-list")
    public ResponseEntity<SuccessResponse<List<FusionWarningDigestResponse>>> getDigestList(@RequestBody List<String> warnIds) {
        List<FusionWarningDigestResponse> result = new ArrayList<>();
        try {
            if (!CollectionUtils.isEmpty(warnIds)) {
                result = baseService.getDigestList(warnIds);
            }
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", result));
    }

    @ApiOperation(value = "处理人工通知", notes = "作者：zhangbx")
    @PostMapping("manual-confirm")
    public ResponseEntity<SuccessResponse<Integer>> manualConfirm(@RequestBody @Validated FusionManualConfirmRequest request) {
        try {
            request.setConfirmBy(loginUtils.getCurrentLoginUser().getLoginnm());
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", feedbackService.manualConfirm(request)));
        } catch (Exception ex) {
            throw new FusionException("人工通知失败，请稍后重试", ex);
        }
    }

    @ApiOperation(value = "获取预警叫应失败统计列表", notes = "作者：曹宝金")
    @PostMapping("call-fail-stats-list")
    public ResponseEntity<SuccessResponse<List<FusionCallStatisticsResponse>>> getCallFailStatsList(@RequestBody @Validated FusionCallStatisticsRequest request) {
        List<FusionCallStatisticsResponse> result = new ArrayList<>();
        try {
            result = feedbackService.getCallFailStatsList(request);
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", result));
    }

    @ApiOperation(value = "获取山洪预警记录", notes = "作者：zhangbx")
    @GetMapping("record")
    public ResponseEntity<SuccessResponse<FusionWarningRecordResp>> getRecord(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRecord(warnId)));
    }

    @ApiOperation(value = "获取山洪预警详情", notes = "作者：zhangbx")
    @GetMapping("detail")
    public ResponseEntity<SuccessResponse<List<FusionWarningDetailResp>>> getWarnDetail(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getWarnDetail(warnId)));
    }

    @ApiOperation(value = "获取山洪预警流程列表", notes = "作者：zhangbx")
    @GetMapping("flow-list")
    public ResponseEntity<SuccessResponse<List<FusionWarningFlowResp>>> getFlowList(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getFlowList(warnId)));
    }

    @ApiOperation(value = "获取山洪预警消息列表", notes = "作者：zhangbx")
    @GetMapping("message-list")
    public ResponseEntity<SuccessResponse<List<FusionWarningMessageResp<?>>>> getMessageList(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getMessageList(warnId)));
    }

    @ApiOperation(value = "获取预警防治区后续降雨分页列表", notes = "作者：caobj")
    @PostMapping("followup-page-list")
    public ResponseEntity<SuccessResponse<IPage<FusionFollowupResponse>>> getFollowupPageList(@RequestBody @Validated FusionFollowupRequest request) {
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getFollowupPageList(request)));
        } catch (Exception e) {
            throw new FusionException("获取预警防治区后续降雨分页列表失败", e);
        }
    }

    @ApiOperation(value = "导出预警防治区后续降雨", notes = "作者：caobj")
    @PostMapping(value = "followup-export")
    public void dataExport(@Validated @RequestBody FusionFollowupRequest request, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("预警防治区后续降雨", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.followupExport(request, response.getOutputStream());
        } catch (Exception e) {
            throw new FusionException("预警防治区后续降雨导出失败", e);
        }
    }



    private String getPlatformId() {
        UserInfos info = loginUtils.getCurrentLoginUser();
        if (StringUtils.isBlank(info.getAdcd()) || AdcdUtil.getAdLevel(info.getAdcd()) <= 2) {
            // 未设置政区或省本级
            return null;
        }
        return info.getAdcd().substring(0, 6);
    }
}