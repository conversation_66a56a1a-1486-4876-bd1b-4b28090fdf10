package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 防治区基本情况调查成果汇总
 *
 * <AUTHOR>
 */
@ApiModel(value = "防治区基本情况调查成果汇总")
public class IaCPrevad implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "县级行政区划名称")
    @TableField(value = "XADNM")
    private String xadnm;

    @ApiModelProperty(value = "乡镇行政区划名称")
    @TableField(value = "XZADNM")
    private String xzadnm;

    @ApiModelProperty(value = "村行政区划名称")
    @TableField(value = "CADNM")
    private String cadnm;

    @ApiModelProperty(value = "防治区类型(1一般防治区2重点防治区)")
    @TableField(value = "PREVTP")
    private String prevtp;

    @ApiModelProperty(value = "总人口")
    @TableField(value = "PTCOUNT")
    private Integer ptcount;

    @ApiModelProperty(value = "土地面积")
    @TableField(value = "LDAREA")
    private Double ldarea;

    @ApiModelProperty(value = "耕地面积")
    @TableField(value = "plarea")
    private Double plarea;

    @ApiModelProperty(value = "总户数")
    @TableField(value = "ETCOUNT")
    private Integer etcount;

    @ApiModelProperty(value = "Ⅰ类经济户数")
    @TableField(value = "ECOUNT1")
    private Integer ecount1;

    @ApiModelProperty(value = "Ⅱ类经济户数")
    @TableField(value = "ECOUNT2")
    private Integer ecount2;

    @ApiModelProperty(value = "Ⅲ类经济户数")
    @TableField(value = "ECOUNT3")
    private Integer ecount3;

    @ApiModelProperty(value = "Ⅳ类经济户数")
    @TableField(value = "ECOUNT4")
    private Integer ecount4;

    @ApiModelProperty(value = "总房屋数")
    @TableField(value = "HTCOUNT")
    private Integer htcount;

    @ApiModelProperty(value = "Ⅰ类房屋数")
    @TableField(value = "HCOUNT1")
    private Integer hcount1;

    @ApiModelProperty(value = "Ⅱ类房屋数")
    @TableField(value = "HCOUNT2")
    private Integer hcount2;

    @ApiModelProperty(value = "Ⅲ类房屋数")
    @TableField(value = "HCOUNT3")
    private Integer hcount3;

    @ApiModelProperty(value = "Ⅳ类房屋数")
    @TableField(value = "HCOUNT4")
    private Integer hcount4;

    @ApiModelProperty(value = "经度")
    @TableField(value = "LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField(value = "LTTD")
    private BigDecimal lttd;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getXzadnm() {
        return xzadnm;
    }

    public void setXzadnm(String xzadnm) {
        this.xzadnm = xzadnm;
    }

    public String getCadnm() {
        return cadnm;
    }

    public void setCadnm(String cadnm) {
        this.cadnm = cadnm;
    }

    public String getPrevtp() {
        return prevtp;
    }

    public void setPrevtp(String prevtp) {
        this.prevtp = prevtp;
    }

    public Integer getPtcount() {
        return ptcount;
    }

    public void setPtcount(Integer ptcount) {
        this.ptcount = ptcount;
    }

    public Double getLdarea() {
        return ldarea;
    }

    public void setLdarea(Double ldarea) {
        this.ldarea = ldarea;
    }

    public Double getPlarea() {
        return plarea;
    }

    public void setPlarea(Double plarea) {
        this.plarea = plarea;
    }

    public Integer getEtcount() {
        return etcount;
    }

    public void setEtcount(Integer etcount) {
        this.etcount = etcount;
    }

    public Integer getEcount1() {
        return ecount1;
    }

    public void setEcount1(Integer ecount1) {
        this.ecount1 = ecount1;
    }

    public Integer getEcount2() {
        return ecount2;
    }

    public void setEcount2(Integer ecount2) {
        this.ecount2 = ecount2;
    }

    public Integer getEcount3() {
        return ecount3;
    }

    public void setEcount3(Integer ecount3) {
        this.ecount3 = ecount3;
    }

    public Integer getEcount4() {
        return ecount4;
    }

    public void setEcount4(Integer ecount4) {
        this.ecount4 = ecount4;
    }

    public Integer getHtcount() {
        return htcount;
    }

    public void setHtcount(Integer htcount) {
        this.htcount = htcount;
    }

    public Integer getHcount1() {
        return hcount1;
    }

    public void setHcount1(Integer hcount1) {
        this.hcount1 = hcount1;
    }

    public Integer getHcount2() {
        return hcount2;
    }

    public void setHcount2(Integer hcount2) {
        this.hcount2 = hcount2;
    }

    public Integer getHcount3() {
        return hcount3;
    }

    public void setHcount3(Integer hcount3) {
        this.hcount3 = hcount3;
    }

    public Integer getHcount4() {
        return hcount4;
    }

    public void setHcount4(Integer hcount4) {
        this.hcount4 = hcount4;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }
}
