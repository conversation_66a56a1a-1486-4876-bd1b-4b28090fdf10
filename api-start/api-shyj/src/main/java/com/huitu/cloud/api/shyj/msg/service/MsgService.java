package com.huitu.cloud.api.shyj.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.shyj.msg.entity.MsgVo;
import com.huitu.cloud.api.shyj.warn.entity.*;

import java.util.List;

/**
 * <p>
 * 消息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface MsgService extends IService<WarnrecordR> {
    /**
     * 查询短信预警统计
     * @return
     */
    List<MsgVo> getMsgSendReport(String adcd, String stm, String etm);
}