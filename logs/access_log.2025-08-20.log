************ - - [20/Aug/2025:10:30:47 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [20/Aug/2025:10:30:47 +0800] "GET /login HTTP/1.1" 302 -
************ - - [20/Aug/2025:10:30:48 +0800] "GET /login?code=EbawaW&state=oGaYZk HTTP/1.1" 302 -
************ - - [20/Aug/2025:10:30:49 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [20/Aug/2025:10:30:49 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [20/Aug/2025:10:30:50 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1755657050470 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:53 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1755657050470 HTTP/1.1" 200 552
************ - - [20/Aug/2025:10:30:53 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755657053737 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:53 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755657053737 HTTP/1.1" 200 61784
************ - - [20/Aug/2025:10:30:53 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755657053814 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:53 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755657053814 HTTP/1.1" 200 10388
************ - - [20/Aug/2025:10:30:53 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755657053846 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:53 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755657053846 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755657054000 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+10:30:53&etm=&_timer304=1755657054000 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755657054000 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+11:00&filterCnt=6&_timer304=1755657054000 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755657054000 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755657054000 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755657054000 HTTP/1.1" 200 1482
************ - - [20/Aug/2025:10:30:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755657054000 HTTP/1.1" 200 166
************ - - [20/Aug/2025:10:30:54 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+10:30:53&etm=&_timer304=1755657054000 HTTP/1.1" 200 156
************ - - [20/Aug/2025:10:30:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:10:30:54 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:10:30:54 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+11:00&filterCnt=6&_timer304=1755657054000 HTTP/1.1" 200 164
************ - - [20/Aug/2025:10:30:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:10:30:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:10:30:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755657054000 HTTP/1.1" 200 169
************ - - [20/Aug/2025:10:30:54 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755657054000 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755657054400 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/base/saas/token?_timer304=1755657054400 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755657054410 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:54 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:55 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287126
************ - - [20/Aug/2025:10:30:55 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:10:30:55 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1755657056619 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755657056777 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+10:30:56&etm=&_timer304=1755657056777 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755657056777 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755657056777 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755657056777 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+11:00&filterCnt=6&_timer304=1755657056777 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755657056781 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755657056948 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:56 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755657056973 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Aug/2025:10:30:57 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755657054410 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:10:30:57 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755657056777 HTTP/1.1" 200 166
************ - - [20/Aug/2025:10:30:57 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [20/Aug/2025:10:30:57 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+10:30:56&etm=&_timer304=1755657056777 HTTP/1.1" 200 156
************ - - [20/Aug/2025:10:30:57 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755657056777 HTTP/1.1" 200 169
************ - - [20/Aug/2025:10:30:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755657056777 HTTP/1.1" 200 161
************ - - [20/Aug/2025:10:30:57 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+11:00&filterCnt=6&_timer304=1755657056777 HTTP/1.1" 200 164
************ - - [20/Aug/2025:10:30:57 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755657056777 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:10:30:58 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755657056781 HTTP/1.1" 200 159616
************ - - [20/Aug/2025:10:30:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [20/Aug/2025:10:30:58 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1755657056619 HTTP/1.1" 200 144
************ - - [20/Aug/2025:10:30:58 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [20/Aug/2025:10:30:58 +0800] "GET /api/base/saas/token?_timer304=1755657054400 HTTP/1.1" 200 411
************ - - [20/Aug/2025:10:30:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [20/Aug/2025:10:30:58 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755657056973 HTTP/1.1" 200 258
************ - - [20/Aug/2025:10:30:58 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [20/Aug/2025:10:30:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755657059029 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:59 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:10:30:59 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755657059029 HTTP/1.1" 200 148
************ - - [20/Aug/2025:10:30:59 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:10:30:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755657056948 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:10:30:59 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Aug/2025:10:30:59 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755657059789 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:30:59 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Aug/2025:10:30:59 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755657059789 HTTP/1.1" 200 258
************ - - [20/Aug/2025:10:31:00 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:10:31:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755657060380 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755657060386 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755657060387 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755657060387 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755657060387 HTTP/1.1" 200 148
************ - - [20/Aug/2025:10:31:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755657060387 HTTP/1.1" 200 152
************ - - [20/Aug/2025:10:31:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755657060380 HTTP/1.1" 200 160
************ - - [20/Aug/2025:10:31:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755657060386 HTTP/1.1" 200 159
************ - - [20/Aug/2025:10:31:00 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Aug/2025:10:31:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Aug/2025:10:31:02 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755657054400 HTTP/1.1" 200 427
************ - - [20/Aug/2025:10:31:05 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [20/Aug/2025:10:31:06 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657066325 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:06 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281102203002&_timer304=1755657066325 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:06 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:06 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 1769
************ - - [20/Aug/2025:10:31:06 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657066362 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:06 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657066362 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:06 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657066362 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:31:06 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657066325 HTTP/1.1" 200 360
************ - - [20/Aug/2025:10:31:06 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281102203002&_timer304=1755657066325 HTTP/1.1" 200 520
************ - - [20/Aug/2025:10:31:06 +0800] "GET /api/fusion/warning/flow-list?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657066362 HTTP/1.1" 200 753
************ - - [20/Aug/2025:10:31:06 +0800] "GET /api/fusion/warning/snapshot-index?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657066362 HTTP/1.1" 200 624
************ - - [20/Aug/2025:10:31:07 +0800] "GET /api/fusion/warning/message-list?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657066362 HTTP/1.1" 200 10225
************ - - [20/Aug/2025:10:35:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755657350379 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:35:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755657350379 HTTP/1.1" 200 160
************ - - [20/Aug/2025:10:35:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755657356647 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:35:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755657356647 HTTP/1.1" 200 161
************ - - [20/Aug/2025:10:35:56 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755657356867 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:35:56 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:10:35:57 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:10:35:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755657356867 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:10:35:58 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:10:36:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755657360446 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:36:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755657360446 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:36:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755657360446 HTTP/1.1" 200 148
************ - - [20/Aug/2025:10:36:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755657360477 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:36:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755657360446 HTTP/1.1" 200 152
************ - - [20/Aug/2025:10:36:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755657360477 HTTP/1.1" 200 159
************ - - [20/Aug/2025:10:40:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755657650383 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:40:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755657650383 HTTP/1.1" 200 160
************ - - [20/Aug/2025:10:40:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755657656647 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:40:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755657656647 HTTP/1.1" 200 161
************ - - [20/Aug/2025:10:40:56 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755657656869 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:40:56 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:10:40:57 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:10:40:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755657656869 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:10:40:58 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:10:41:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755657660488 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:41:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755657660488 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:41:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755657660506 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:41:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755657660488 HTTP/1.1" 200 148
************ - - [20/Aug/2025:10:41:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755657660488 HTTP/1.1" 200 152
************ - - [20/Aug/2025:10:41:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755657660506 HTTP/1.1" 200 159
************ - - [20/Aug/2025:10:45:15 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=83F4A359-5F1E-4E54-B791-814DC46BE6B1&_timer304=1755657915629 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:15 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=83F4A359-5F1E-4E54-B791-814DC46BE6B1&_timer304=1755657915629 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:15 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=83F4A359-5F1E-4E54-B791-814DC46BE6B1&_timer304=1755657915629 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:15 +0800] "GET /api/fusion/warning/flow-list?warnId=83F4A359-5F1E-4E54-B791-814DC46BE6B1&_timer304=1755657915629 HTTP/1.1" 200 753
************ - - [20/Aug/2025:10:45:15 +0800] "GET /api/fusion/warning/snapshot-index?warnId=83F4A359-5F1E-4E54-B791-814DC46BE6B1&_timer304=1755657915629 HTTP/1.1" 200 628
************ - - [20/Aug/2025:10:45:16 +0800] "GET /api/fusion/warning/message-list?warnId=83F4A359-5F1E-4E54-B791-814DC46BE6B1&_timer304=1755657915629 HTTP/1.1" 200 10225
************ - - [20/Aug/2025:10:45:29 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:29 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6067
************ - - [20/Aug/2025:10:45:32 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657932063 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:32 +0800] "GET /api/fusion/warning/snapshot-index?warnId=B0BDA292-85FC-479A-A256-C8364B3B2E99&_timer304=1755657932063 HTTP/1.1" 200 624
************ - - [20/Aug/2025:10:45:32 +0800] "OPTIONS /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:32 +0800] "OPTIONS /api/ewci/bia/keyArea/select-basic-info/220281102203002?_timer304=1755657932794 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:32 +0800] "POST /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 239
************ - - [20/Aug/2025:10:45:32 +0800] "GET /api/ewci/bia/keyArea/select-basic-info/220281102203002?_timer304=1755657932794 HTTP/1.1" 200 401
************ - - [20/Aug/2025:10:45:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755657950911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755657950911 HTTP/1.1" 200 160
************ - - [20/Aug/2025:10:45:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755657956904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:56 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755657956906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:45:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755657956904 HTTP/1.1" 200 161
************ - - [20/Aug/2025:10:45:56 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:10:45:57 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:10:45:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755657956906 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:10:45:58 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:10:46:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755657960911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:46:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755657960910 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:46:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755657960910 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:46:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755657960910 HTTP/1.1" 200 148
************ - - [20/Aug/2025:10:46:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755657960910 HTTP/1.1" 200 152
************ - - [20/Aug/2025:10:46:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755657960911 HTTP/1.1" 200 159
************ - - [20/Aug/2025:10:50:27 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [20/Aug/2025:10:50:29 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&warnGradeId=5&_timer304=1755658229524 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:29 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281102203001&_timer304=1755658229524 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:29 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:29 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281102203001&_timer304=1755658229524 HTTP/1.1" 200 520
************ - - [20/Aug/2025:10:50:29 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 2601
************ - - [20/Aug/2025:10:50:29 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658229579 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:29 +0800] "OPTIONS /api/ew/warning/process-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658229579 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:29 +0800] "OPTIONS /api/ew/warning/message-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658229579 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:29 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658229579 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:29 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&warnGradeId=5&_timer304=1755658229524 HTTP/1.1" 200 450
************ - - [20/Aug/2025:10:50:29 +0800] "GET /api/ew/warning/flow-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658229579 HTTP/1.1" 200 1382
************ - - [20/Aug/2025:10:50:29 +0800] "GET /api/ew/warning/process-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658229579 HTTP/1.1" 200 725
************ - - [20/Aug/2025:10:50:29 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658229579 HTTP/1.1" 200 346
************ - - [20/Aug/2025:10:50:30 +0800] "GET /api/ew/warning/message-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658229579 HTTP/1.1" 200 7637
************ - - [20/Aug/2025:10:50:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755658250377 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755658250377 HTTP/1.1" 200 160
************ - - [20/Aug/2025:10:50:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755658256641 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755658256641 HTTP/1.1" 200 161
************ - - [20/Aug/2025:10:50:56 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755658256857 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:50:56 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:10:50:57 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:10:50:57 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755658256857 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:10:50:58 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:10:51:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755658260946 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755658260946 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755658260947 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755658260946 HTTP/1.1" 200 148
************ - - [20/Aug/2025:10:51:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755658260946 HTTP/1.1" 200 152
************ - - [20/Aug/2025:10:51:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755658260947 HTTP/1.1" 200 159
************ - - [20/Aug/2025:10:51:26 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281102203001&_timer304=1755658286505 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:26 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&warnGradeId=5&_timer304=1755658286505 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:26 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 2601
************ - - [20/Aug/2025:10:51:26 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&warnGradeId=5&_timer304=1755658286505 HTTP/1.1" 200 450
************ - - [20/Aug/2025:10:51:26 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658286532 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:26 +0800] "OPTIONS /api/ew/warning/process-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658286532 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:26 +0800] "OPTIONS /api/ew/warning/message-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658286532 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:26 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658286532 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:26 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281102203001&_timer304=1755658286505 HTTP/1.1" 200 520
************ - - [20/Aug/2025:10:51:26 +0800] "GET /api/ew/warning/process-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658286532 HTTP/1.1" 200 725
************ - - [20/Aug/2025:10:51:26 +0800] "GET /api/ew/warning/flow-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658286532 HTTP/1.1" 200 1382
************ - - [20/Aug/2025:10:51:26 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658286532 HTTP/1.1" 200 346
************ - - [20/Aug/2025:10:51:27 +0800] "GET /api/ew/warning/message-list?warnId=A6A2F82C-0983-47EC-8A96-ACAB8DE167AA&_timer304=1755658286532 HTTP/1.1" 200 7637
************ - - [20/Aug/2025:10:51:31 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755658291495 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755658291493 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755658291493 HTTP/1.1" 200 161
************ - - [20/Aug/2025:10:51:31 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755658291495 HTTP/1.1" 200 159616
************ - - [20/Aug/2025:10:51:31 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+10:51:31&etm=&_timer304=1755658291544 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755658291544 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+11:00&filterCnt=6&_timer304=1755658291544 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:31 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755658291544 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755658291544 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:31 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:10:51:31 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:10:51:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755658291601 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:31 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:10:51:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [20/Aug/2025:10:51:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:10:51:31 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755658291544 HTTP/1.1" 200 166
************ - - [20/Aug/2025:10:51:31 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+10:51:31&etm=&_timer304=1755658291544 HTTP/1.1" 200 156
************ - - [20/Aug/2025:10:51:31 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+11:00&filterCnt=6&_timer304=1755658291544 HTTP/1.1" 200 164
************ - - [20/Aug/2025:10:51:31 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755658291625 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:31 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755658291544 HTTP/1.1" 200 169
************ - - [20/Aug/2025:10:51:31 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755658291544 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:10:51:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [20/Aug/2025:10:51:31 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755658291625 HTTP/1.1" 200 258
************ - - [20/Aug/2025:10:51:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [20/Aug/2025:10:51:32 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [20/Aug/2025:10:51:32 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Aug/2025:10:51:32 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755658292905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755658291601 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:10:51:33 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755658292905 HTTP/1.1" 200 258
************ - - [20/Aug/2025:10:51:33 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Aug/2025:10:51:33 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Aug/2025:10:51:34 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Aug/2025:10:51:34 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755658294710 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:35 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1755658294710 HTTP/1.1" 200 232
************ - - [20/Aug/2025:10:51:35 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755658295819 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:35 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755658295819 HTTP/1.1" 200 148
************ - - [20/Aug/2025:10:51:35 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:10:51:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:10:51:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:10:51:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755658301362 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755658301371 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755658301362 HTTP/1.1" 200 160
************ - - [20/Aug/2025:10:51:41 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755658301380 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:41 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755658301380 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755658301371 HTTP/1.1" 200 159
************ - - [20/Aug/2025:10:51:41 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755658301380 HTTP/1.1" 200 148
************ - - [20/Aug/2025:10:51:41 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755658301380 HTTP/1.1" 200 152
************ - - [20/Aug/2025:10:51:44 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [20/Aug/2025:10:51:45 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755658305456 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:45 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755658305456 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:45 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755658305456 HTTP/1.1" 200 443
************ - - [20/Aug/2025:10:51:45 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [20/Aug/2025:10:51:45 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755658305456 HTTP/1.1" 200 510
************ - - [20/Aug/2025:10:51:45 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755658305488 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:45 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755658305488 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:45 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755658305488 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:45 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755658305488 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:45 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755658305488 HTTP/1.1" 200 1382
************ - - [20/Aug/2025:10:51:45 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755658305488 HTTP/1.1" 200 727
************ - - [20/Aug/2025:10:51:45 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755658305488 HTTP/1.1" 200 329
************ - - [20/Aug/2025:10:51:46 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755658305488 HTTP/1.1" 200 12917
************ - - [20/Aug/2025:10:51:52 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205002&_timer304=1755658312803 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:52 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=6EB55445-E4FD-432A-B429-A89453193090&warnGradeId=5&_timer304=1755658312803 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:52 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=6EB55445-E4FD-432A-B429-A89453193090&warnGradeId=5&_timer304=1755658312803 HTTP/1.1" 200 443
************ - - [20/Aug/2025:10:51:52 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [20/Aug/2025:10:51:52 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205002&_timer304=1755658312803 HTTP/1.1" 200 510
************ - - [20/Aug/2025:10:51:52 +0800] "OPTIONS /api/ew/warning/process-list?warnId=6EB55445-E4FD-432A-B429-A89453193090&_timer304=1755658312835 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:52 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=6EB55445-E4FD-432A-B429-A89453193090&_timer304=1755658312835 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:52 +0800] "OPTIONS /api/ew/warning/message-list?warnId=6EB55445-E4FD-432A-B429-A89453193090&_timer304=1755658312835 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:52 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=6EB55445-E4FD-432A-B429-A89453193090&_timer304=1755658312835 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:52 +0800] "GET /api/ew/warning/process-list?warnId=6EB55445-E4FD-432A-B429-A89453193090&_timer304=1755658312835 HTTP/1.1" 200 727
************ - - [20/Aug/2025:10:51:52 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=6EB55445-E4FD-432A-B429-A89453193090&_timer304=1755658312835 HTTP/1.1" 200 329
************ - - [20/Aug/2025:10:51:52 +0800] "GET /api/ew/warning/flow-list?warnId=6EB55445-E4FD-432A-B429-A89453193090&_timer304=1755658312835 HTTP/1.1" 200 1382
************ - - [20/Aug/2025:10:51:53 +0800] "GET /api/ew/warning/message-list?warnId=6EB55445-E4FD-432A-B429-A89453193090&_timer304=1755658312835 HTTP/1.1" 200 12917
************ - - [20/Aug/2025:10:51:58 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205003&_timer304=1755658318771 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:58 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&warnGradeId=5&_timer304=1755658318771 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:58 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&warnGradeId=5&_timer304=1755658318771 HTTP/1.1" 200 443
************ - - [20/Aug/2025:10:51:58 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [20/Aug/2025:10:51:58 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755658318795 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:58 +0800] "OPTIONS /api/ew/warning/process-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755658318795 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:58 +0800] "OPTIONS /api/ew/warning/message-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755658318795 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:58 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755658318795 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:51:58 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205003&_timer304=1755658318771 HTTP/1.1" 200 510
************ - - [20/Aug/2025:10:51:58 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755658318795 HTTP/1.1" 200 329
************ - - [20/Aug/2025:10:51:58 +0800] "GET /api/ew/warning/process-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755658318795 HTTP/1.1" 200 727
************ - - [20/Aug/2025:10:51:58 +0800] "GET /api/ew/warning/flow-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755658318795 HTTP/1.1" 200 1382
************ - - [20/Aug/2025:10:51:59 +0800] "GET /api/ew/warning/message-list?warnId=60E57E23-9730-482C-9F22-A0A34DB071E2&_timer304=1755658318795 HTTP/1.1" 200 12917
************ - - [20/Aug/2025:10:52:03 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 326225
************ - - [20/Aug/2025:10:52:03 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [20/Aug/2025:10:52:07 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220521102201101&_timer304=1755658327748 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:52:07 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=D6730691-2DD6-4A3F-9179-FC90EFD9BBD1&_timer304=1755658327748 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:52:07 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=D6730691-2DD6-4A3F-9179-FC90EFD9BBD1&_timer304=1755658327748 HTTP/1.1" 200 360
************ - - [20/Aug/2025:10:52:07 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220521102201101&_timer304=1755658327748 HTTP/1.1" 200 535
************ - - [20/Aug/2025:10:52:07 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 1795
************ - - [20/Aug/2025:10:52:07 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=D6730691-2DD6-4A3F-9179-FC90EFD9BBD1&_timer304=1755658327777 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:52:07 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=D6730691-2DD6-4A3F-9179-FC90EFD9BBD1&_timer304=1755658327777 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:52:07 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=D6730691-2DD6-4A3F-9179-FC90EFD9BBD1&_timer304=1755658327777 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:52:07 +0800] "GET /api/fusion/warning/flow-list?warnId=D6730691-2DD6-4A3F-9179-FC90EFD9BBD1&_timer304=1755658327777 HTTP/1.1" 200 550
************ - - [20/Aug/2025:10:52:07 +0800] "GET /api/fusion/warning/snapshot-index?warnId=D6730691-2DD6-4A3F-9179-FC90EFD9BBD1&_timer304=1755658327777 HTTP/1.1" 200 654
************ - - [20/Aug/2025:10:52:08 +0800] "GET /api/fusion/warning/message-list?warnId=D6730691-2DD6-4A3F-9179-FC90EFD9BBD1&_timer304=1755658327777 HTTP/1.1" 200 7717
************ - - [20/Aug/2025:10:52:24 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=9DE69A61-21B3-42B3-BDD6-60114110D73C&_timer304=1755658344358 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:52:24 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9DE69A61-21B3-42B3-BDD6-60114110D73C&_timer304=1755658344358 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:52:24 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=9DE69A61-21B3-42B3-BDD6-60114110D73C&_timer304=1755658344358 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:52:24 +0800] "GET /api/fusion/warning/flow-list?warnId=9DE69A61-21B3-42B3-BDD6-60114110D73C&_timer304=1755658344358 HTTP/1.1" 200 550
************ - - [20/Aug/2025:10:52:24 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9DE69A61-21B3-42B3-BDD6-60114110D73C&_timer304=1755658344358 HTTP/1.1" 200 623
************ - - [20/Aug/2025:10:52:25 +0800] "GET /api/fusion/warning/message-list?warnId=9DE69A61-21B3-42B3-BDD6-60114110D73C&_timer304=1755658344358 HTTP/1.1" 200 7717
************ - - [20/Aug/2025:10:54:00 +0800] "OPTIONS /api/syq/warn/rain HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:54:00 +0800] "POST /api/syq/warn/rain HTTP/1.1" 200 222
************ - - [20/Aug/2025:10:56:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755658591902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:56:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755658591902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:56:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755658591904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:56:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:10:56:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755658591902 HTTP/1.1" 200 161
************ - - [20/Aug/2025:10:56:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755658591902 HTTP/1.1" 200 160
************ - - [20/Aug/2025:10:56:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:10:56:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755658591904 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:10:56:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:10:56:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755658601906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:56:41 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755658601907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:56:41 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755658601907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:10:56:41 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755658601907 HTTP/1.1" 200 148
************ - - [20/Aug/2025:10:56:41 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755658601907 HTTP/1.1" 200 152
************ - - [20/Aug/2025:10:56:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755658601906 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:01:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755658891904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:01:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755658891904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:01:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755658891905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:01:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755658891904 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:01:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:01:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755658891904 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:01:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:01:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755658891905 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:01:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:01:42 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755658902903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:01:42 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755658902903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:01:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755658902904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:01:42 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755658902903 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:01:42 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755658902903 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:01:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755658902904 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:06:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755659191901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:06:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755659191901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:06:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755659191902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:06:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755659191901 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:06:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:06:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755659191901 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:06:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:06:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755659191902 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:06:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:06:43 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755659203909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:06:43 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755659203909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:06:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755659203909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:06:43 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755659203909 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:06:43 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755659203909 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:06:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755659203909 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:11:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755659491901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:11:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755659491902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:11:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755659491902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:11:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755659491901 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:11:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:11:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755659491902 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:11:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:11:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755659491902 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:11:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:11:44 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755659504901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:11:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755659504901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:11:44 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755659504901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:11:44 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755659504901 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:11:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755659504901 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:11:44 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755659504901 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:16:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755659791903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:16:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755659791903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:16:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755659791904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:16:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755659791903 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:16:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:16:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755659791903 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:16:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:16:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755659791904 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:16:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:16:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755659805913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:16:45 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755659805914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:16:45 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755659805914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:16:45 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755659805914 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:16:45 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755659805914 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:16:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755659805913 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:21:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755660091902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:21:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755660091902 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:21:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755660106899 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:21:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755660106899 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:21:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755660106900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:21:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755660106899 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:21:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755660106899 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:21:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755660106900 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:22:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755660128911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:22:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755660128912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:22:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:22:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755660128911 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:22:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:22:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755660128912 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:22:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:26:47 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755660407907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:26:47 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755660407907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:26:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755660407907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:26:47 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755660407907 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:26:47 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755660407907 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:26:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755660407907 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:27:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755660428909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:27:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755660428909 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:28:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755660488908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:28:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755660488909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:28:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:28:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755660488908 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:28:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:28:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755660488909 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:28:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:31:48 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755660708913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:31:48 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755660708913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:31:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755660708914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:31:48 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755660708913 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:31:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755660708914 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:31:48 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755660708913 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:32:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755660728901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:32:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755660728901 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:33:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755660788913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:33:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755660788914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:33:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:33:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755660788913 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:33:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:33:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755660788914 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:33:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:36:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755661009912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:36:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755661009913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:36:49 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755661009913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:36:49 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755661009913 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:36:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755661009913 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:36:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755661009912 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:37:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755661028909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:37:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755661028909 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:38:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755661088912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:38:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755661088913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:38:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:38:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755661088912 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:38:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:38:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755661088913 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:38:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:41:50 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755661310906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:41:50 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755661310906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:41:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755661310907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:41:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755661310907 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:41:50 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755661310906 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:41:50 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755661310906 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:42:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755661328902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:42:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755661328902 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:43:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755661388902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:43:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755661388903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:43:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:43:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755661388902 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:43:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:43:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755661388903 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:43:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:46:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755661611902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:46:51 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755661611902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:46:51 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755661611902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:46:51 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755661611902 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:46:51 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755661611902 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:46:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755661611902 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:47:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755661628913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:47:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755661628913 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:47:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755661636201 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:47:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755661636202 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:47:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:47:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755661636201 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:47:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:47:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755661636202 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:47:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:51:52 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755661912908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:51:52 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755661912908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:51:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755661912908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:51:52 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755661912908 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:51:52 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755661912908 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:51:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755661912908 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:52:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755661928911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:52:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755661928911 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:53:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755661988913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:53:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755661988914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:53:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:53:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755661988913 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:53:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:53:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755661988914 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:53:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:11:56:53 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755662213904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:56:53 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755662213904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:56:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755662213905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:56:53 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755662213904 HTTP/1.1" 200 148
************ - - [20/Aug/2025:11:56:53 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755662213904 HTTP/1.1" 200 152
************ - - [20/Aug/2025:11:56:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755662213905 HTTP/1.1" 200 159
************ - - [20/Aug/2025:11:57:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755662228902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:57:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755662228902 HTTP/1.1" 200 161
************ - - [20/Aug/2025:11:58:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755662288909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:58:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755662288909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:11:58:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:11:58:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755662288909 HTTP/1.1" 200 160
************ - - [20/Aug/2025:11:58:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:11:58:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755662288909 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:11:58:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:01:54 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755662514906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:01:54 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755662514906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:01:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755662514907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:01:54 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755662514906 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:01:54 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755662514906 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:01:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755662514907 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:02:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755662528913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:02:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755662528913 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:03:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755662588901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:03:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755662588902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:03:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:03:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755662588901 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:03:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:03:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755662588902 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:03:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:06:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755662815902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:06:55 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755662815901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:06:55 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755662815901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:06:55 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755662815901 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:06:55 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755662815901 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:06:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755662815902 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:07:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755662828904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:07:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755662828904 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:08:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755662888901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:08:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755662888902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:08:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:08:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755662888901 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:08:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:08:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755662888902 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:08:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:11:56 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755663116911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:11:56 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755663116911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:11:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755663116911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:11:56 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755663116911 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:11:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755663116911 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:11:56 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755663116911 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:12:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755663128903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:12:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755663128903 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:13:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755663188900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:13:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755663188900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:13:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:13:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755663188900 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:13:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:13:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755663188900 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:13:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:16:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755663417908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:16:57 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755663417908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:16:57 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755663417908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:16:57 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755663417908 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:16:57 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755663417908 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:16:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755663417908 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:17:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755663428915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:17:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755663428915 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:18:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755663488908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:18:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755663488909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:18:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:18:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755663488908 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:18:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:18:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755663488909 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:18:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:21:58 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755663718914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:21:58 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755663718914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:21:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755663718914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:21:58 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755663718914 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:21:58 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755663718914 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:21:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755663718914 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:22:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755663728903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:22:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755663728903 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:23:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755663788914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:23:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755663788915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:23:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:23:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755663788914 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:23:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:23:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755663788915 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:23:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:26:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755664019908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:26:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755664019908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:26:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755664019909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:26:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755664019908 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:26:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755664019908 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:26:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755664019909 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:27:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755664028899 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:27:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755664028899 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:28:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755664088907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:28:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755664088908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:28:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:28:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755664088907 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:28:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:28:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755664088908 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:28:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:32:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755664320911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:32:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755664320911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:32:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755664320912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:32:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755664320911 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:32:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755664320911 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:32:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755664320912 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:32:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755664328903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:32:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755664328903 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:33:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755664388914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:33:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:33:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:33:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755664388914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:33:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:33:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:33:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755664388914 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:33:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:33:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755664388914 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:33:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:37:01 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755664621910 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:37:01 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755664621910 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:37:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755664621911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:37:01 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755664621910 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:37:01 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755664621910 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:37:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755664621911 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:37:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755664628902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:37:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755664628902 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:38:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755664688907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:38:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755664688908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:38:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:38:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755664688907 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:38:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:38:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755664688908 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:38:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:42:02 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755664922905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:42:02 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755664922905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:42:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755664922906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:42:02 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755664922905 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:42:02 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755664922905 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:42:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755664922906 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:42:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755664929032 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:42:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755664929032 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:43:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755664988908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:43:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755664988908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:43:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:43:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755664988908 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:43:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:43:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755664988908 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:43:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:47:03 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755665223904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:47:03 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755665223904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:47:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755665223905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:47:03 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755665223904 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:47:03 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755665223904 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:47:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755665223905 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:47:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755665228902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:47:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755665228902 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:48:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755665288903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:48:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755665288903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:48:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:48:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755665288903 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:48:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:48:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755665288903 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:48:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:52:04 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755665524905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:52:04 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755665524905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:52:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755665524906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:52:04 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755665524905 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:52:04 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755665524905 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:52:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755665524906 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:52:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755665528901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:52:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755665528901 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:53:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755665588906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:53:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755665588907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:53:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:53:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755665588906 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:53:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:53:09 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755665588907 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:53:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:12:57:05 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755665825912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:57:05 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755665825912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:57:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755665825913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:57:05 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755665825912 HTTP/1.1" 200 152
************ - - [20/Aug/2025:12:57:05 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755665825912 HTTP/1.1" 200 148
************ - - [20/Aug/2025:12:57:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755665825913 HTTP/1.1" 200 159
************ - - [20/Aug/2025:12:57:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755665828913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:57:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755665828913 HTTP/1.1" 200 161
************ - - [20/Aug/2025:12:58:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755665888911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:58:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755665888911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:12:58:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:12:58:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755665888911 HTTP/1.1" 200 160
************ - - [20/Aug/2025:12:58:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:12:58:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755665888911 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:12:58:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:02:06 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755666126905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:02:06 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755666126905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:02:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755666126906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:02:06 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755666126905 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:02:06 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755666126905 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:02:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755666126906 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:02:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755666128912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:02:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755666128912 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:03:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755666188914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:03:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755666188914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:03:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:03:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755666188914 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:03:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:03:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755666188914 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:03:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:07:07 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755666427907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:07:07 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755666427907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:07:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755666427907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:07:07 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755666427907 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:07:07 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755666427907 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:07:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755666427907 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:07:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755666428913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:07:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755666428913 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:08:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755666488900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:08:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755666488901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:08:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:08:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755666488900 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:08:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:08:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755666488901 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:08:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:12:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755666728910 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:12:08 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755666728911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:12:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755666728911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:12:08 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755666728911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:12:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755666728910 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:12:08 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755666728911 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:12:08 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755666728911 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:12:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755666728911 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:13:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755666788904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:13:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755666788906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:13:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:13:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755666788904 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:13:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:13:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755666788906 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:13:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:17:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755667028900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:17:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755667028900 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:17:09 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755667029903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:17:09 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755667029903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:17:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755667029903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:17:09 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755667029903 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:17:09 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755667029903 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:17:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755667029903 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:18:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755667088909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:18:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755667088910 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:18:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:18:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755667088909 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:18:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:18:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755667088910 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:18:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:22:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755667328912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:22:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755667328912 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:22:10 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755667330899 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:22:10 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755667330899 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:22:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755667330900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:22:10 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755667330899 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:22:10 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755667330899 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:22:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755667330900 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:23:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755667388911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:23:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755667388912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:23:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:23:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755667388911 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:23:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:23:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755667388912 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:23:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:27:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755667628900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:27:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755667628900 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:27:11 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755667631913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:27:11 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755667631913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:27:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755667631914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:27:11 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755667631913 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:27:11 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755667631913 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:27:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755667631914 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:28:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755667688907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:28:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755667688907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:28:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:28:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755667688907 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:28:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:28:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755667688907 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:28:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:32:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755667928900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:32:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755667928900 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:32:12 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755667932907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:32:12 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755667932907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:32:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755667932907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:32:12 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755667932907 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:32:12 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755667932907 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:32:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755667932907 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:33:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755667988903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:33:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755667988903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:33:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:33:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755667988903 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:33:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:33:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755667988903 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:33:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:37:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755668228911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:37:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755668228911 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:37:13 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755668233911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:37:13 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755668233911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:37:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755668233912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:37:13 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755668233911 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:37:13 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755668233911 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:37:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755668233912 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:38:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755668288912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:38:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755668288913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:38:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:38:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755668288912 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:38:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:38:09 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755668288913 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:38:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:42:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755668528906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:42:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755668528906 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:42:14 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755668534907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:42:14 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755668534907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:42:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755668534907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:42:14 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755668534907 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:42:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755668534907 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:42:14 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755668534907 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:43:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755668588912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:43:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755668588912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:43:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:43:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755668588912 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:43:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:43:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755668588912 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:43:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:47:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755668828910 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:47:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755668828910 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:47:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755668835913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:47:15 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755668835914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:47:15 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755668835914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:47:15 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755668835914 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:47:15 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755668835914 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:47:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755668835913 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:47:51 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755668871968 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:47:51 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755668871969 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:47:51 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:47:51 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755668871968 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:47:52 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:47:53 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755668871969 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:47:53 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:52:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755669128913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:52:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755669128913 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:52:16 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755669136905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:52:16 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755669136905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:52:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755669136906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:52:16 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755669136905 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:52:16 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755669136905 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:52:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755669136906 HTTP/1.1" 200 159
************ - - [20/Aug/2025:13:53:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755669188904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:53:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755669188905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:53:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:53:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755669188904 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:53:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:53:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755669188905 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:53:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:56:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755669391358 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:56:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755669391358 HTTP/1.1" 200 160
************ - - [20/Aug/2025:13:56:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755669391403 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:56:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755669391403 HTTP/1.1" 200 161
************ - - [20/Aug/2025:13:56:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755669391560 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:56:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:13:56:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:13:56:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755669391560 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:13:56:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:13:57:16 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755669436952 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:57:16 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755669436952 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:57:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755669436952 HTTP/1.1" 200 -
************ - - [20/Aug/2025:13:57:16 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755669436952 HTTP/1.1" 200 148
************ - - [20/Aug/2025:13:57:16 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755669436952 HTTP/1.1" 200 152
************ - - [20/Aug/2025:13:57:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755669436952 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:02:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755669728903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:02:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755669728903 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:02:17 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755669737905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:02:17 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755669737905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:02:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755669737906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:02:17 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755669737905 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:02:17 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755669737905 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:02:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755669737906 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:03:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755669788907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:03:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755669788908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:03:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:03:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755669788907 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:03:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:03:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755669788908 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:14:03:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:14:04:01 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:02 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755669842464 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:02 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:02 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [20/Aug/2025:14:04:02 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755669842620 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:02 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755669842620 HTTP/1.1" 200 258
************ - - [20/Aug/2025:14:04:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:02 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755669842464 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:04:03 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [20/Aug/2025:14:04:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 796
************ - - [20/Aug/2025:14:04:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 796
************ - - [20/Aug/2025:14:04:04 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [20/Aug/2025:14:04:06 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755669846158 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:06 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669846158 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:06 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:06 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 1008
************ - - [20/Aug/2025:14:04:06 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669846158 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:04:06 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755669846158 HTTP/1.1" 200 520
************ - - [20/Aug/2025:14:04:06 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669846183 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:06 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669846183 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:06 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669846183 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:06 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669846183 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:04:06 +0800] "GET /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669846183 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:04:07 +0800] "GET /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669846183 HTTP/1.1" 200 6972
************ - - [20/Aug/2025:14:04:10 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002102&_timer304=1755669850084 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:10 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755669850084 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:10 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 1008
************ - - [20/Aug/2025:14:04:10 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002102&_timer304=1755669850084 HTTP/1.1" 200 155
************ - - [20/Aug/2025:14:04:10 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755669850084 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:04:10 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755669850112 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:10 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755669850112 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:10 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755669850112 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:10 +0800] "GET /api/fusion/warning/flow-list?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755669850112 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:04:10 +0800] "GET /api/fusion/warning/snapshot-index?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755669850112 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:04:11 +0800] "GET /api/fusion/warning/message-list?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1755669850112 HTTP/1.1" 200 8549
************ - - [20/Aug/2025:14:04:22 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669862015 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:22 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:22 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755669862015 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:22 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669862015 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:04:22 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 1008
************ - - [20/Aug/2025:14:04:22 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755669862015 HTTP/1.1" 200 520
************ - - [20/Aug/2025:14:04:22 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669862050 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:22 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669862050 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:22 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669862050 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:04:22 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669862050 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:04:22 +0800] "GET /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669862050 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:04:22 +0800] "GET /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669862050 HTTP/1.1" 200 6972
************ - - [20/Aug/2025:14:05:59 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755669959276 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:05:59 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755669959276 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:05:59 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:05:59 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755669959276 HTTP/1.1" 200 12285
************ - - [20/Aug/2025:14:05:59 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1755669959276 HTTP/1.1" 200 12285
************ - - [20/Aug/2025:14:05:59 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23514
************ - - [20/Aug/2025:14:06:01 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755669961483 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:01 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669961483 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:01 +0800] "OPTIONS /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755669961483 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:01 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669961483 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:06:01 +0800] "GET /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755669961483 HTTP/1.1" 200 912
************ - - [20/Aug/2025:14:06:01 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755669961483 HTTP/1.1" 200 520
************ - - [20/Aug/2025:14:06:01 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669961519 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:01 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669961519 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:01 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669961519 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:01 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669961519 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:06:01 +0800] "GET /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669961519 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:06:02 +0800] "GET /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669961519 HTTP/1.1" 200 6972
************ - - [20/Aug/2025:14:06:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755669991358 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755669991358 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:06:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755669991404 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755669991404 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:06:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755669991561 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:06:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:06:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755669991561 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:14:06:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:14:06:38 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755669998085 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:38 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669998085 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:38 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:38 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669998085 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:06:38 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 1008
************ - - [20/Aug/2025:14:06:38 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755669998085 HTTP/1.1" 200 520
************ - - [20/Aug/2025:14:06:38 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669998132 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:38 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669998132 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:38 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669998132 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:06:38 +0800] "GET /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669998132 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:06:38 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669998132 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:06:38 +0800] "GET /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755669998132 HTTP/1.1" 200 6972
************ - - [20/Aug/2025:14:07:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755670038907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755670038907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755670038908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755670038907 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:07:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755670038907 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:07:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755670038908 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:07:44 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:44 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [20/Aug/2025:14:07:51 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:51 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23514
************ - - [20/Aug/2025:14:07:52 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755670072787 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:52 +0800] "OPTIONS /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755670072787 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:52 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755670072787 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:52 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755670072787 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:07:52 +0800] "GET /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755670072787 HTTP/1.1" 200 912
************ - - [20/Aug/2025:14:07:52 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755670072787 HTTP/1.1" 200 520
************ - - [20/Aug/2025:14:07:52 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755670072827 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:52 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755670072827 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:52 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755670072827 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:07:52 +0800] "GET /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755670072827 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:07:52 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755670072827 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:07:53 +0800] "GET /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755670072827 HTTP/1.1" 200 6972
************ - - [20/Aug/2025:14:11:32 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:11:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755670291406 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:11:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755670291561 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:11:32 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:11:32 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:11:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755670291361 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:11:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755670291406 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:11:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:11:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:11:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [20/Aug/2025:14:11:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755670291561 HTTP/1.1" 200 441332
************ - - [20/Aug/2025:14:11:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755670291361 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:12:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755670338945 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:12:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755670338944 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:12:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755670338945 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:12:19 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755670338945 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:12:19 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755670338944 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:12:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755670338945 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:17:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755670628914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:17:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755670628914 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:17:19 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755670639915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:17:19 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755670639915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:17:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755670639916 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:17:19 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755670639915 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:17:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755670639916 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:17:19 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755670639915 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:18:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755670688906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:18:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:18:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:18:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755670688909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:18:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:18:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:18:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755670688906 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:18:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:18:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755670688909 HTTP/1.1" 200 441943
************ - - [20/Aug/2025:14:18:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 52489
************ - - [20/Aug/2025:14:22:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755670929027 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:22:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755670929027 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:22:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755670940902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:22:20 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755670940903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:22:20 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755670940903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:22:20 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755670940903 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:22:20 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755670940903 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:22:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755670940902 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:23:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755670988907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:23:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:23:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:23:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755670988909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:23:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:23:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:23:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755670988907 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:23:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:23:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755670988909 HTTP/1.1" 200 441943
************ - - [20/Aug/2025:14:23:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 54200
************ - - [20/Aug/2025:14:26:46 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755671206237 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755671206243 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:46 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:46 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:46 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:46 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755671206244 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:46 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755671206237 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:26:46 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:26:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755671206243 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:26:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:26:47 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755671206244 HTTP/1.1" 200 441943
************ - - [20/Aug/2025:14:26:47 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 55374
************ - - [20/Aug/2025:14:26:48 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755671208346 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:48 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671208346 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:48 +0800] "OPTIONS /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755671208347 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:48 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671208346 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:26:48 +0800] "GET /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755671208347 HTTP/1.1" 200 912
************ - - [20/Aug/2025:14:26:48 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671208418 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:48 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671208418 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:48 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671208418 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:48 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755671208346 HTTP/1.1" 200 520
************ - - [20/Aug/2025:14:26:48 +0800] "GET /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671208418 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:26:48 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671208418 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:26:49 +0800] "GET /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671208418 HTTP/1.1" 200 6972
************ - - [20/Aug/2025:14:26:54 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:55 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23514
************ - - [20/Aug/2025:14:26:57 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671217063 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:57 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755671217063 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:57 +0800] "OPTIONS /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755671217063 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:57 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671217063 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:26:57 +0800] "GET /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755671217063 HTTP/1.1" 200 912
************ - - [20/Aug/2025:14:26:57 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671217098 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:57 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671217098 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:57 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671217098 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:26:57 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755671217063 HTTP/1.1" 200 520
************ - - [20/Aug/2025:14:26:57 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671217098 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:26:57 +0800] "GET /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671217098 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:26:57 +0800] "GET /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755671217098 HTTP/1.1" 200 6972
************ - - [20/Aug/2025:14:27:02 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755671222487 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:02 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002104&_timer304=1755671222487 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:02 +0800] "OPTIONS /api/fusion/warning/record?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&warnGradeId=2&_timer304=1755671222487 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:02 +0800] "GET /api/fusion/warning/record?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&warnGradeId=2&_timer304=1755671222487 HTTP/1.1" 200 912
************ - - [20/Aug/2025:14:27:02 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755671222487 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:27:02 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002104&_timer304=1755671222487 HTTP/1.1" 200 155
************ - - [20/Aug/2025:14:27:02 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755671222531 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:02 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755671222531 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:02 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755671222531 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:02 +0800] "GET /api/fusion/warning/flow-list?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755671222531 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:27:02 +0800] "GET /api/fusion/warning/snapshot-index?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755671222531 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:27:03 +0800] "GET /api/fusion/warning/message-list?warnId=AACFB64E-3011-45AC-B953-3CE4EA5C1388&_timer304=1755671222531 HTTP/1.1" 200 8549
************ - - [20/Aug/2025:14:27:08 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:08 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23250
************ - - [20/Aug/2025:14:27:09 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205102&_timer304=1755671229286 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:09 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755671229286 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:09 +0800] "OPTIONS /api/fusion/warning/record?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&warnGradeId=1&_timer304=1755671229287 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:09 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755671229286 HTTP/1.1" 200 373
************ - - [20/Aug/2025:14:27:09 +0800] "GET /api/fusion/warning/record?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&warnGradeId=1&_timer304=1755671229287 HTTP/1.1" 200 919
************ - - [20/Aug/2025:14:27:09 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205102&_timer304=1755671229286 HTTP/1.1" 200 514
************ - - [20/Aug/2025:14:27:09 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755671229330 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:09 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755671229330 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:09 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755671229330 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:09 +0800] "GET /api/fusion/warning/snapshot-index?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755671229330 HTTP/1.1" 200 655
************ - - [20/Aug/2025:14:27:09 +0800] "GET /api/fusion/warning/flow-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755671229330 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:27:10 +0800] "GET /api/fusion/warning/message-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755671229330 HTTP/1.1" 200 8549
************ - - [20/Aug/2025:14:27:15 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:15 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23603
************ - - [20/Aug/2025:14:27:16 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205004&_timer304=1755671236503 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:16 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&warnGradeId=5&_timer304=1755671236503 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:16 +0800] "OPTIONS /api/ew/warning/record?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&warnGradeId=5&_timer304=1755671236503 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:16 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205004&_timer304=1755671236503 HTTP/1.1" 200 510
************ - - [20/Aug/2025:14:27:16 +0800] "GET /api/ew/warning/record?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&warnGradeId=5&_timer304=1755671236503 HTTP/1.1" 200 919
************ - - [20/Aug/2025:14:27:16 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&warnGradeId=5&_timer304=1755671236503 HTTP/1.1" 200 443
************ - - [20/Aug/2025:14:27:16 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755671236555 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:16 +0800] "OPTIONS /api/ew/warning/process-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755671236555 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:16 +0800] "OPTIONS /api/ew/warning/message-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755671236555 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:16 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755671236555 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:16 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755671236555 HTTP/1.1" 200 329
************ - - [20/Aug/2025:14:27:16 +0800] "GET /api/ew/warning/flow-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755671236555 HTTP/1.1" 200 1382
************ - - [20/Aug/2025:14:27:16 +0800] "GET /api/ew/warning/process-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755671236555 HTTP/1.1" 200 727
************ - - [20/Aug/2025:14:27:17 +0800] "GET /api/ew/warning/message-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755671236555 HTTP/1.1" 200 12917
************ - - [20/Aug/2025:14:27:20 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755671240948 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:20 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755671240948 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755671240949 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:27:20 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755671240948 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:27:20 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755671240948 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:27:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755671240949 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:31:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755671491358 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:31:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755671491358 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:31:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755671491400 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:31:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755671491400 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:31:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:31:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:31:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:31:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755671491558 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:31:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:31:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:31:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755671491558 HTTP/1.1" 200 441943
************ - - [20/Aug/2025:14:31:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 55374
************ - - [20/Aug/2025:14:32:20 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755671540990 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:32:20 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755671540990 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:32:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755671540996 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:32:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755671540990 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:32:21 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755671540990 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:32:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755671540996 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:36:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755671791357 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:36:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755671791357 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:36:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755671791400 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:36:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755671791400 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:36:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:36:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:36:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755671791558 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:36:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:36:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:36:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:36:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755671791558 HTTP/1.1" 200 441943
************ - - [20/Aug/2025:14:36:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 55374
************ - - [20/Aug/2025:14:37:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755671841026 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:37:21 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755671841026 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:37:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755671841033 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:37:21 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755671841026 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:37:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755671841026 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:37:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755671841033 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:42:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755672129032 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:42:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755672129032 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:42:21 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755672141915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:42:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755672141915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:42:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755672141916 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:42:21 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755672141915 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:42:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755672141915 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:42:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755672141916 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:43:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755672188908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:43:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:43:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:43:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755672188910 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:43:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:43:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:43:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755672188908 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:43:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:43:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755672188910 HTTP/1.1" 200 441943
************ - - [20/Aug/2025:14:43:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 55374
************ - - [20/Aug/2025:14:47:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755672429040 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:47:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755672429040 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:47:22 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755672442914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:47:22 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755672442914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:47:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755672442915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:47:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755672442915 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:47:22 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755672442914 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:47:22 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755672442914 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:48:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755672489035 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:48:09 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:48:09 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:48:09 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:48:09 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755672489037 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:48:09 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:48:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755672489035 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:48:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:48:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755672489037 HTTP/1.1" 200 441943
************ - - [20/Aug/2025:14:48:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 55374
************ - - [20/Aug/2025:14:49:46 +0800] "OPTIONS /api/ew/warning/record?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&warnGradeId=5&_timer304=1755672585778 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:46 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205004&_timer304=1755672585778 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:46 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&warnGradeId=5&_timer304=1755672585778 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:48 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205004&_timer304=1755672585778 HTTP/1.1" 200 510
************ - - [20/Aug/2025:14:49:48 +0800] "GET /api/ew/warning/record?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&warnGradeId=5&_timer304=1755672585778 HTTP/1.1" 200 919
************ - - [20/Aug/2025:14:49:48 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&warnGradeId=5&_timer304=1755672585778 HTTP/1.1" 200 443
************ - - [20/Aug/2025:14:49:48 +0800] "OPTIONS /api/ew/warning/process-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755672588926 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:48 +0800] "OPTIONS /api/ew/warning/message-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755672588926 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:48 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755672588926 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:48 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755672588926 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:49 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755672588926 HTTP/1.1" 200 329
************ - - [20/Aug/2025:14:49:49 +0800] "GET /api/ew/warning/flow-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755672588926 HTTP/1.1" 200 1382
************ - - [20/Aug/2025:14:49:49 +0800] "GET /api/ew/warning/process-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755672588926 HTTP/1.1" 200 727
************ - - [20/Aug/2025:14:49:49 +0800] "GET /api/ew/warning/message-list?warnId=0100A8B4-84D8-4363-BA5A-E05FF1023C2B&_timer304=1755672588926 HTTP/1.1" 200 12917
************ - - [20/Aug/2025:14:49:55 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:55 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23514
************ - - [20/Aug/2025:14:49:56 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755672596499 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:56 +0800] "OPTIONS /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755672596500 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:56 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755672596499 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:56 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755672596499 HTTP/1.1" 200 371
************ - - [20/Aug/2025:14:49:56 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755672596499 HTTP/1.1" 200 520
************ - - [20/Aug/2025:14:49:56 +0800] "GET /api/fusion/warning/record?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1755672596500 HTTP/1.1" 200 912
************ - - [20/Aug/2025:14:49:56 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755672596575 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:56 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755672596575 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:56 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755672596575 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:49:56 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755672596575 HTTP/1.1" 200 646
************ - - [20/Aug/2025:14:49:56 +0800] "GET /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755672596575 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:49:57 +0800] "GET /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755672596575 HTTP/1.1" 200 6972
************ - - [20/Aug/2025:14:50:01 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:50:01 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 23250
************ - - [20/Aug/2025:14:50:02 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205102&_timer304=1755672602058 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:50:02 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755672602058 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:50:02 +0800] "OPTIONS /api/fusion/warning/record?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&warnGradeId=1&_timer304=1755672602059 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:50:02 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755672602058 HTTP/1.1" 200 373
************ - - [20/Aug/2025:14:50:02 +0800] "GET /api/fusion/warning/record?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&warnGradeId=1&_timer304=1755672602059 HTTP/1.1" 200 919
************ - - [20/Aug/2025:14:50:02 +0800] "OPTIONS /api/fusion/warning/flow-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755672602106 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:50:02 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205102&_timer304=1755672602058 HTTP/1.1" 200 514
************ - - [20/Aug/2025:14:50:02 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755672602106 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:50:02 +0800] "OPTIONS /api/fusion/warning/message-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755672602106 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:50:02 +0800] "GET /api/fusion/warning/flow-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755672602106 HTTP/1.1" 200 550
************ - - [20/Aug/2025:14:50:02 +0800] "GET /api/fusion/warning/snapshot-index?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755672602106 HTTP/1.1" 200 655
************ - - [20/Aug/2025:14:50:03 +0800] "GET /api/fusion/warning/message-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755672602106 HTTP/1.1" 200 8549
************ - - [20/Aug/2025:14:52:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755672728905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:52:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755672728905 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:52:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755672743914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:52:23 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755672743915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:52:23 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755672743915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:52:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755672743915 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:52:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755672743915 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:52:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755672743914 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:53:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755672788904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:53:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:53:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:53:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:53:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755672788907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:53:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755672788904 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:53:09 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:53:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:53:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:14:53:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755672788907 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:14:57:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755673028908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:57:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755673028908 HTTP/1.1" 200 161
************ - - [20/Aug/2025:14:57:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755673046908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:57:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755673046908 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:57:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755673046909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:57:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755673046908 HTTP/1.1" 200 148
************ - - [20/Aug/2025:14:57:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755673046908 HTTP/1.1" 200 152
************ - - [20/Aug/2025:14:57:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755673046909 HTTP/1.1" 200 159
************ - - [20/Aug/2025:14:58:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755673088902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:58:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:58:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:58:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755673088904 HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:58:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:14:58:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:14:58:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755673088902 HTTP/1.1" 200 160
************ - - [20/Aug/2025:14:58:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:14:58:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755673088904 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:14:58:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:15:02:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755673329040 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:02:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755673329040 HTTP/1.1" 200 161
************ - - [20/Aug/2025:15:02:28 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755673348018 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:02:28 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755673348018 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:02:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755673348019 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:02:28 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755673348018 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:02:28 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755673348018 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:02:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755673348019 HTTP/1.1" 200 159
************ - - [20/Aug/2025:15:03:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755673388900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:03:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:03:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:03:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:03:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755673388902 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:03:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:03:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755673388900 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:03:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:03:09 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755673388902 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:15:03:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:15:07:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755673628906 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:07:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755673628906 HTTP/1.1" 200 161
************ - - [20/Aug/2025:15:07:28 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755673648900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:07:28 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755673648900 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:07:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755673648901 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:07:28 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755673648900 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:07:28 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755673648900 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:07:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755673648901 HTTP/1.1" 200 159
************ - - [20/Aug/2025:15:08:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755673688907 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:08:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:08:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:08:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755673688909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:08:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:08:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:08:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755673688907 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:08:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:08:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755673688909 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:15:08:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:15:12:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755673928909 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:12:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755673928909 HTTP/1.1" 200 161
************ - - [20/Aug/2025:15:12:29 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755673949903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:12:29 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755673949903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:12:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755673949905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:12:29 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755673949903 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:12:29 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755673949903 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:12:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755673949905 HTTP/1.1" 200 159
************ - - [20/Aug/2025:15:13:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755673988903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:13:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:13:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:13:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755673988905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:13:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:13:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:13:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755673988903 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:13:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:13:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755673988905 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:15:13:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:15:17:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755674229008 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:17:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755674229008 HTTP/1.1" 200 161
************ - - [20/Aug/2025:15:17:31 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755674251020 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:17:31 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755674251020 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:17:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755674251022 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:17:31 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755674251020 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:17:31 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755674251020 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:17:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755674251022 HTTP/1.1" 200 159
************ - - [20/Aug/2025:15:18:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755674288913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:18:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:18:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:18:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755674288915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:18:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:18:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:18:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755674288913 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:18:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:18:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755674288915 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:15:18:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:15:22:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755674528905 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:22:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755674528905 HTTP/1.1" 200 161
************ - - [20/Aug/2025:15:22:31 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755674551911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:22:31 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755674551911 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:22:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755674551912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:22:33 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755674551911 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:22:33 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755674551911 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:22:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755674551912 HTTP/1.1" 200 159
************ - - [20/Aug/2025:15:23:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755674589025 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:23:09 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:23:09 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:23:09 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755674589027 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:23:09 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:23:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755674589025 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:23:09 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:23:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:23:11 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:15:23:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755674589027 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:15:27:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755674828903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:27:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755674828903 HTTP/1.1" 200 161
************ - - [20/Aug/2025:15:27:33 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755674853912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:27:33 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755674853912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:27:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755674853913 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:27:33 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755674853912 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:27:33 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755674853912 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:27:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755674853913 HTTP/1.1" 200 159
************ - - [20/Aug/2025:15:28:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755674888903 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:28:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:28:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:28:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755674888915 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:28:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:28:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755674888903 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:28:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:28:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:28:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755674888915 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:15:28:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1755674975131 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755674975426 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755674975426 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:29:35&etm=&_timer304=1755674975426 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755674975426 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755674975426 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755674975479 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:35 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:29:35&etm=&_timer304=1755674975426 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:29:35 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755674975426 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:29:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755674975426 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:29:35 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:29:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:29:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:29:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:29:35 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755674975479 HTTP/1.1" 200 1482
************ - - [20/Aug/2025:15:29:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755674975426 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:29:36 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1755674975131 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:29:36 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755674975426 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755674976415 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/base/saas/token?_timer304=1755674976415 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755674976428 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:36 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:29:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:29:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287126
************ - - [20/Aug/2025:15:29:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9850
************ - - [20/Aug/2025:15:29:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:29:38 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Aug/2025:15:29:38 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 10216
************ - - [20/Aug/2025:15:29:38 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Aug/2025:15:29:38 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Aug/2025:15:29:38 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [20/Aug/2025:15:29:39 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755674976428 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:15:29:39 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Aug/2025:15:29:39 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Aug/2025:15:29:39 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:29:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Aug/2025:15:29:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [20/Aug/2025:15:29:46 +0800] "GET /api/base/saas/token?_timer304=1755674976415 HTTP/1.1" 200 411
************ - - [20/Aug/2025:15:29:54 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755674976415 HTTP/1.1" 200 427
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:30:34&etm=&_timer304=1755675035100 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675035100 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675035100 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675035100 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675035100 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755675035102 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755675035105 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755675035121 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755675035119 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:30:35 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:30:34&etm=&_timer304=1755675035100 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:30:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675035100 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:30:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:30:35 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675035100 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:30:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:30:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:30:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755675035102 HTTP/1.1" 200 161
************ - - [20/Aug/2025:15:30:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675035100 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:30:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:30:35 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755675035105 HTTP/1.1" 200 159616
************ - - [20/Aug/2025:15:30:35 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675035100 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:30:35 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755675035121 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:30:35 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [20/Aug/2025:15:30:35 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:35 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [20/Aug/2025:15:30:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:30:36 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 743
************ - - [20/Aug/2025:15:30:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755675035119 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:15:30:36 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755675036516 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:36 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755675036516 HTTP/1.1" 200 258
************ - - [20/Aug/2025:15:30:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:15:30:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9884
************ - - [20/Aug/2025:15:30:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9884
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:30:40&etm=&_timer304=1755675040249 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675040249 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675040249 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675040249 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675040249 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755675040257 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:30:40&etm=&_timer304=1755675040249 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:30:40 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:30:40 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:30:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675040249 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:30:40 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675040249 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:30:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:30:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:30:40 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755675040257 HTTP/1.1" 200 1482
************ - - [20/Aug/2025:15:30:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675040249 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:30:40 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675040249 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/base/saas/token?_timer304=1755675040745 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675040745 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675040745 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:30:40 +0800] "GET /api/base/saas/token?_timer304=1755675040745 HTTP/1.1" 200 411
************ - - [20/Aug/2025:15:30:40 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675040745 HTTP/1.1" 200 427
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287126
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Aug/2025:15:30:41 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675040745 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:15:30:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [20/Aug/2025:15:30:42 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9850
************ - - [20/Aug/2025:15:30:42 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 10216
************ - - [20/Aug/2025:15:30:42 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:31:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755675091378 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:31:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755675091378 HTTP/1.1" 200 160
127.0.0.1 - - [20/Aug/2025:15:31:54 +0800] "GET /doc.html HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:31:54 +0800] "GET /login HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:31:54 +0800] "GET /doc.html HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:31:54 +0800] "GET /login HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /login?code=cjzQzs&state=6YO2uL HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /doc.html HTTP/1.1" 200 71645
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
127.0.0.1 - - [20/Aug/2025:15:31:55 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /swagger-resources HTTP/1.1" 200 101
127.0.0.1 - - [20/Aug/2025:15:31:56 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
127.0.0.1 - - [20/Aug/2025:15:31:57 +0800] "GET /v2/api-docs HTTP/1.1" 200 2731874
127.0.0.1 - - [20/Aug/2025:15:32:22 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
127.0.0.1 - - [20/Aug/2025:15:32:22 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
127.0.0.1 - - [20/Aug/2025:15:32:22 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
127.0.0.1 - - [20/Aug/2025:15:32:22 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:32:22 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [20/Aug/2025:15:32:34 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755675154914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:32:34 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755675154914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:32:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675154916 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:32:34 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755675154914 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:32:34 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755675154914 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:32:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675154916 HTTP/1.1" 200 159
127.0.0.1 - - [20/Aug/2025:15:32:43 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
127.0.0.1 - - [20/Aug/2025:15:32:44 +0800] "POST /api/fusion/statistics/select-warn-summary HTTP/1.1" 200 394
127.0.0.1 - - [20/Aug/2025:15:32:44 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [20/Aug/2025:15:34:39 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1755675279488 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:34:39 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1755675279488 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:34:58 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:34:59 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:35:01 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:02 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:02 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:35:02 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:03 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:35:03 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755675311725 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:35:11&etm=&_timer304=1755675311725 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675311725 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675311725 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675311725 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675311725 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:11 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:35:11&etm=&_timer304=1755675311725 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:35:11 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:35:11 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675311725 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:35:11 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675311725 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:35:11 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:35:11 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:35:11 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755675311725 HTTP/1.1" 200 1482
************ - - [20/Aug/2025:15:35:11 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:35:11 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675311725 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:35:11 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675311725 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675312304 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/base/saas/token?_timer304=1755675312304 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675312315 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "GET /api/base/saas/token?_timer304=1755675312304 HTTP/1.1" 200 411
************ - - [20/Aug/2025:15:35:12 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:12 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675312304 HTTP/1.1" 200 427
************ - - [20/Aug/2025:15:35:12 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675312315 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:15:35:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287126
************ - - [20/Aug/2025:15:35:12 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Aug/2025:15:35:12 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Aug/2025:15:35:12 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [20/Aug/2025:15:35:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:35:13 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Aug/2025:15:35:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:35:13 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Aug/2025:15:35:13 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Aug/2025:15:35:13 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Aug/2025:15:35:13 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [20/Aug/2025:15:35:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9850
************ - - [20/Aug/2025:15:35:13 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 10216
************ - - [20/Aug/2025:15:35:14 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1755675319204 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1755675319204 HTTP/1.1" 200 144
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:35:19&etm=&_timer304=1755675319450 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675319450 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675319450 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675319450 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675319451 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755675319451 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755675319456 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:35:19&etm=&_timer304=1755675319450 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:35:19 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:35:19 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:35:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675319450 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:35:19 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675319450 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:35:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:35:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:35:19 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755675319451 HTTP/1.1" 200 161
************ - - [20/Aug/2025:15:35:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675319451 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:35:19 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755675319456 HTTP/1.1" 200 159616
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755675319608 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675319450 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755675319653 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [20/Aug/2025:15:35:19 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:19 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755675319653 HTTP/1.1" 200 258
************ - - [20/Aug/2025:15:35:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [20/Aug/2025:15:35:20 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:20 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [20/Aug/2025:15:35:20 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [20/Aug/2025:15:35:21 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755675319608 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:15:35:21 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 743
************ - - [20/Aug/2025:15:35:21 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755675321103 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:21 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 743
************ - - [20/Aug/2025:15:35:21 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755675321103 HTTP/1.1" 200 258
************ - - [20/Aug/2025:15:35:21 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755675321501 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675321511 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755675321501 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:35:21 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755675321521 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755675321521 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675321511 HTTP/1.1" 200 159
************ - - [20/Aug/2025:15:35:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755675321521 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:35:21 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755675321521 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:35:22 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9884
************ - - [20/Aug/2025:15:35:22 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9884
************ - - [20/Aug/2025:15:35:23 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755675323140 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:23 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:23 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:15:35:23 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755675323140 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/ewci/base/mal/write/902?_timer304=1755675338694 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "GET /api/ewci/base/mal/write/902?_timer304=1755675338694 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:35:38&etm=&_timer304=1755675338894 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675338894 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675338894 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675338894 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675338894 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:35:38 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:35:38&etm=&_timer304=1755675338894 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:35:38 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675338894 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:35:38 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:35:38 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675338894 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:35:38 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:35:38 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:35:38 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:35:38 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675338894 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:35:38 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:35:39 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675338894 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:36:46 +0800] "OPTIONS /api/ewci/base/mal/write/902?_timer304=1755675406662 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:36:46 +0800] "GET /api/ewci/base/mal/write/902?_timer304=1755675406662 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:36:55 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:36:55 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:38:11 +0800] "OPTIONS /api/ewci/base/mal/write/59?_timer304=1755675491853 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:11 +0800] "GET /api/ewci/base/mal/write/59?_timer304=1755675491853 HTTP/1.1" 200 145
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:38:11&etm=&_timer304=1755675492063 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675492063 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675492063 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675492063 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675492063 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "OPTIONS /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:12 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:38:11&etm=&_timer304=1755675492063 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:38:12 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:38:12 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675492063 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:38:12 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:38:12 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:38:12 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675492063 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:38:12 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:38:12 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675492063 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:38:12 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675492063 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:38:12 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 630
************ - - [20/Aug/2025:15:38:13 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2722
************ - - [20/Aug/2025:15:38:20 +0800] "OPTIONS /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:20 +0800] "OPTIONS /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:21 +0800] "POST /api/ew/statistics/select-warn-grade-summary HTTP/1.1" 200 630
************ - - [20/Aug/2025:15:38:21 +0800] "POST /api/ew/statistics/select-ad-warn-grade-list HTTP/1.1" 200 2722
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/ewci/base/mal/write/394?_timer304=1755675522531 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "GET /api/ewci/base/mal/write/394?_timer304=1755675522531 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:38:42&etm=&_timer304=1755675522735 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675522735 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675522735 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675522735 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675522735 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:38:42&etm=&_timer304=1755675522735 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/ewci/warn/type/select-page?pageNum=1&pageSize=-1&_timer304=1755675522745 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "OPTIONS /api/ewci/warn/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:42 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675522735 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:38:42 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:38:42 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675522735 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:38:42 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:38:42 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:38:42 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:38:42 +0800] "GET /api/ewci/warn/type/select-page?pageNum=1&pageSize=-1&_timer304=1755675522745 HTTP/1.1" 200 4572
************ - - [20/Aug/2025:15:38:42 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675522735 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:38:42 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675522735 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:38:42 +0800] "POST /api/ewci/warn/info/select-info-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/ewci/base/mal/write/902?_timer304=1755675530294 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:38:50&etm=&_timer304=1755675530294 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675530294 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675530294 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675530294 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675530294 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:50 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:38:50&etm=&_timer304=1755675530294 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:38:50 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:38:50 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:38:50 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675530294 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:38:50 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675530294 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:38:50 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:38:50 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:38:50 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:38:50 +0800] "GET /api/ewci/base/mal/write/902?_timer304=1755675530294 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:38:50 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675530294 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:38:50 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675530294 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1755675534431 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:38:54&etm=&_timer304=1755675534431 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675534431 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675534431 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675534431 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675534431 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755675534439 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:38:54&etm=&_timer304=1755675534431 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:38:54 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:38:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675534431 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:38:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:38:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:38:54 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675534431 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:38:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:38:54 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755675534439 HTTP/1.1" 200 1482
************ - - [20/Aug/2025:15:38:54 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1755675534431 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:38:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675534431 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:38:54 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675534431 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/base/saas/token?_timer304=1755675534895 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675534895 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "GET /api/base/saas/token?_timer304=1755675534895 HTTP/1.1" 200 411
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675534895 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:38:54 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675534895 HTTP/1.1" 200 427
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287126
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:38:55 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675534895 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [20/Aug/2025:15:38:55 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 10216
************ - - [20/Aug/2025:15:38:56 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9850
************ - - [20/Aug/2025:15:38:56 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:39:16 +0800] "OPTIONS /api/ewci/base/mal/write/57?_timer304=1755675556448 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:16 +0800] "GET /api/ewci/base/mal/write/57?_timer304=1755675556448 HTTP/1.1" 200 145
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:39:21&etm=&_timer304=1755675562053 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675562054 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675562054 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675562054 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675562054 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755675562061 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:39:21&etm=&_timer304=1755675562053 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:39:22 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:39:22 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:39:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:39:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675562054 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:39:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:39:22 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675562054 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:39:22 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755675562061 HTTP/1.1" 200 1482
************ - - [20/Aug/2025:15:39:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675562054 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:39:22 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675562054 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/base/saas/token?_timer304=1755675562592 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675562592 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675562592 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "GET /api/base/saas/token?_timer304=1755675562592 HTTP/1.1" 200 411
************ - - [20/Aug/2025:15:39:22 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:22 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675562592 HTTP/1.1" 200 427
************ - - [20/Aug/2025:15:39:22 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Aug/2025:15:39:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287126
************ - - [20/Aug/2025:15:39:22 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Aug/2025:15:39:23 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675562592 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 10216
************ - - [20/Aug/2025:15:39:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9850
************ - - [20/Aug/2025:15:39:24 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:39:33 +0800] "OPTIONS /api/ewci/base/mal/write/57?_timer304=1755675573681 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:33 +0800] "GET /api/ewci/base/mal/write/57?_timer304=1755675573681 HTTP/1.1" 200 145
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:39:35&etm=&_timer304=1755675575594 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675575594 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675575594 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675575594 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675575594 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755675575602 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:35 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:39:35&etm=&_timer304=1755675575594 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:39:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675575594 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:39:35 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:39:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:39:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:39:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:39:35 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675575594 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:39:35 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755675575602 HTTP/1.1" 200 1482
************ - - [20/Aug/2025:15:39:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675575594 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:39:35 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675575594 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/base/saas/token?_timer304=1755675576150 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675576150 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675576150 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:36 +0800] "GET /api/base/saas/token?_timer304=1755675576150 HTTP/1.1" 200 411
************ - - [20/Aug/2025:15:39:36 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675576150 HTTP/1.1" 200 427
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287126
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Aug/2025:15:39:36 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675576150 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [20/Aug/2025:15:39:36 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Aug/2025:15:39:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9850
************ - - [20/Aug/2025:15:39:37 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 10216
************ - - [20/Aug/2025:15:39:37 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/ewci/base/mal/write/902?_timer304=1755675588498 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:39:48&etm=&_timer304=1755675588498 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675588498 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675588498 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675588498 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675588498 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:48 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:39:48&etm=&_timer304=1755675588498 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:39:48 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:39:48 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675588498 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:39:48 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:39:48 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:39:48 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:39:48 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675588498 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:39:48 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:39:48 +0800] "GET /api/ewci/base/mal/write/902?_timer304=1755675588498 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:39:48 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675588498 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:39:48 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675588498 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:39:52 +0800] "OPTIONS /api/fusion/warning/detail-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:39:52 +0800] "POST /api/fusion/warning/detail-page-list HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1755675603622 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:40:03&etm=&_timer304=1755675603622 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675603622 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675603622 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675603622 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675603622 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755675603623 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755675603623 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755675603636 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755675603636 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:40:03&etm=&_timer304=1755675603622 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:40:03 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:40:03 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:40:03 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:40:03 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675603622 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:40:03 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:40:03 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675603622 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:40:03 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755675603623 HTTP/1.1" 200 161
************ - - [20/Aug/2025:15:40:03 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1755675603622 HTTP/1.1" 200 144
************ - - [20/Aug/2025:15:40:03 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675603622 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:40:03 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755675603623 HTTP/1.1" 200 159616
************ - - [20/Aug/2025:15:40:03 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:03 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755675603636 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:40:03 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:15:40:03 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675603622 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:40:03 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:15:40:04 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:40:04 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 743
************ - - [20/Aug/2025:15:40:04 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755675604710 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:04 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755675604710 HTTP/1.1" 200 258
************ - - [20/Aug/2025:15:40:04 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:05 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-20+08:00&etm=2025-08-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755675603636 HTTP/1.1" 200 444260
************ - - [20/Aug/2025:15:40:05 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 57155
************ - - [20/Aug/2025:15:40:05 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9884
************ - - [20/Aug/2025:15:40:05 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:06 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9884
************ - - [20/Aug/2025:15:40:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755675611497 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755675611497 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:40:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:11 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:15:40:16 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:16 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [20/Aug/2025:15:40:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675621553 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755675621558 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:21 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755675621558 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755675621558 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:40:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675621553 HTTP/1.1" 200 159
************ - - [20/Aug/2025:15:40:21 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755675621558 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1755675629807 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:40:29&etm=&_timer304=1755675629807 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675629807 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675629807 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675629807 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675629807 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755675629821 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:29 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:40:29&etm=&_timer304=1755675629807 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:40:29 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:40:29 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:40:29 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675629807 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:40:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:40:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:40:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675629807 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:40:29 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755675629821 HTTP/1.1" 200 1482
************ - - [20/Aug/2025:15:40:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675629807 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:40:29 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1755675629807 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:40:29 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675629807 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/base/saas/token?_timer304=1755675630381 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675630381 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675630381 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:30 +0800] "GET /api/base/saas/token?_timer304=1755675630381 HTTP/1.1" 200 411
************ - - [20/Aug/2025:15:40:30 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675630381 HTTP/1.1" 200 427
************ - - [20/Aug/2025:15:40:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287126
************ - - [20/Aug/2025:15:40:30 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Aug/2025:15:40:30 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Aug/2025:15:40:30 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [20/Aug/2025:15:40:31 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Aug/2025:15:40:31 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675630381 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:15:40:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:40:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:40:31 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Aug/2025:15:40:31 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Aug/2025:15:40:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Aug/2025:15:40:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [20/Aug/2025:15:40:31 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9850
************ - - [20/Aug/2025:15:40:31 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 10216
************ - - [20/Aug/2025:15:40:32 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755675634091 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:40:34&etm=&_timer304=1755675634091 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675634091 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675634091 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675634091 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675634091 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:40:34 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755675634091 HTTP/1.1" 200 1482
************ - - [20/Aug/2025:15:40:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Aug/2025:15:40:34 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Aug/2025:15:40:34 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-20+08:00&etm=2025-08-20+16:00&filterCnt=6&_timer304=1755675634091 HTTP/1.1" 200 164
************ - - [20/Aug/2025:15:40:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755675634091 HTTP/1.1" 200 166
************ - - [20/Aug/2025:15:40:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755675634091 HTTP/1.1" 200 169
************ - - [20/Aug/2025:15:40:34 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-17+15:40:34&etm=&_timer304=1755675634091 HTTP/1.1" 200 156
************ - - [20/Aug/2025:15:40:34 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Aug/2025:15:40:34 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755675634091 HTTP/1.1" 200 13016
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675634675 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/base/saas/token?_timer304=1755675634675 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675634686 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:34 +0800] "GET /api/base/saas/token?_timer304=1755675634675 HTTP/1.1" 200 411
************ - - [20/Aug/2025:15:40:34 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-19&_timer304=1755675634675 HTTP/1.1" 200 427
************ - - [20/Aug/2025:15:40:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287126
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Aug/2025:15:40:35 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755675634686 HTTP/1.1" 200 2009
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [20/Aug/2025:15:40:35 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 9850
************ - - [20/Aug/2025:15:40:36 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 10216
************ - - [20/Aug/2025:15:40:36 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [20/Aug/2025:15:40:43 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755675643853 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675643863 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755675643853 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:40:43 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755675643873 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:43 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755675643873 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:40:43 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755675643873 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:40:43 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755675643873 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:40:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675643863 HTTP/1.1" 200 159
************ - - [20/Aug/2025:15:41:44 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1755675704081 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:41:44 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1755675704081 HTTP/1.1" 200 146
************ - - [20/Aug/2025:15:41:45 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1755675705272 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:41:45 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1755675705272 HTTP/1.1" 200 146
127.0.0.1 - - [20/Aug/2025:15:42:12 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:42:12 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:42:17 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:42:17 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:42:23 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:42:23 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:42:25 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:42:25 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:43:07 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:43:07 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /doc.html HTTP/1.1" 200 71645
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
127.0.0.1 - - [20/Aug/2025:15:43:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
127.0.0.1 - - [20/Aug/2025:15:43:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
127.0.0.1 - - [20/Aug/2025:15:43:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
127.0.0.1 - - [20/Aug/2025:15:43:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
127.0.0.1 - - [20/Aug/2025:15:43:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
127.0.0.1 - - [20/Aug/2025:15:43:46 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
127.0.0.1 - - [20/Aug/2025:15:43:46 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
127.0.0.1 - - [20/Aug/2025:15:43:46 +0800] "GET /swagger-resources HTTP/1.1" 200 101
127.0.0.1 - - [20/Aug/2025:15:43:46 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
127.0.0.1 - - [20/Aug/2025:15:43:46 +0800] "GET /v2/api-docs HTTP/1.1" 200 2731874
127.0.0.1 - - [20/Aug/2025:15:43:50 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
127.0.0.1 - - [20/Aug/2025:15:43:50 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
127.0.0.1 - - [20/Aug/2025:15:43:51 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
127.0.0.1 - - [20/Aug/2025:15:43:51 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:43:51 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:44:08 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:44:08 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:44:16 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:44:16 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:44:30 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:44:30 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [20/Aug/2025:15:45:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755675933914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:45:33 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755675933914 HTTP/1.1" 200 160
************ - - [20/Aug/2025:15:45:44 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755675944912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:45:44 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755675944912 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:45:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675944914 HTTP/1.1" 200 -
************ - - [20/Aug/2025:15:45:44 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755675944912 HTTP/1.1" 200 148
************ - - [20/Aug/2025:15:45:44 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755675944912 HTTP/1.1" 200 152
************ - - [20/Aug/2025:15:45:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755675944914 HTTP/1.1" 200 159
127.0.0.1 - - [20/Aug/2025:15:47:44 +0800] "GET /doc.html HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:47:44 +0800] "GET /login HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /login?code=qngHm4&state=zuliuI HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /doc.html HTTP/1.1" 200 71645
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
127.0.0.1 - - [20/Aug/2025:15:47:45 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
127.0.0.1 - - [20/Aug/2025:15:47:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
127.0.0.1 - - [20/Aug/2025:15:47:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
127.0.0.1 - - [20/Aug/2025:15:47:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
127.0.0.1 - - [20/Aug/2025:15:47:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
127.0.0.1 - - [20/Aug/2025:15:47:46 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
127.0.0.1 - - [20/Aug/2025:15:47:46 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
127.0.0.1 - - [20/Aug/2025:15:47:46 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
127.0.0.1 - - [20/Aug/2025:15:47:46 +0800] "GET /swagger-resources HTTP/1.1" 200 101
127.0.0.1 - - [20/Aug/2025:15:47:46 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
127.0.0.1 - - [20/Aug/2025:15:47:47 +0800] "GET /v2/api-docs HTTP/1.1" 200 2731808
127.0.0.1 - - [20/Aug/2025:15:47:51 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
127.0.0.1 - - [20/Aug/2025:15:47:51 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
127.0.0.1 - - [20/Aug/2025:15:47:51 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
127.0.0.1 - - [20/Aug/2025:15:47:51 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:47:51 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:48:23 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
127.0.0.1 - - [20/Aug/2025:15:48:25 +0800] "POST /api/fusion/statistics/select-ad-warn-statistics HTTP/1.1" 200 271
127.0.0.1 - - [20/Aug/2025:15:48:25 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:48:35 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:48:35 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:48:39 +0800] "POST /api/fusion/statistics/select-ad-warn-statistics-tree-list HTTP/1.1" 200 1827
127.0.0.1 - - [20/Aug/2025:15:48:39 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:52:50 +0800] "GET /doc.html HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:52:50 +0800] "GET /login HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /login?code=InFF2J&state=ntAfof HTTP/1.1" 302 -
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /doc.html HTTP/1.1" 200 71645
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
127.0.0.1 - - [20/Aug/2025:15:52:51 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
127.0.0.1 - - [20/Aug/2025:15:52:52 +0800] "GET /swagger-resources HTTP/1.1" 200 101
127.0.0.1 - - [20/Aug/2025:15:52:53 +0800] "GET /v2/api-docs HTTP/1.1" 200 2731899
127.0.0.1 - - [20/Aug/2025:15:53:03 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
127.0.0.1 - - [20/Aug/2025:15:53:03 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
127.0.0.1 - - [20/Aug/2025:15:53:03 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
127.0.0.1 - - [20/Aug/2025:15:53:03 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
127.0.0.1 - - [20/Aug/2025:15:53:03 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
