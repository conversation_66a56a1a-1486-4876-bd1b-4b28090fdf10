<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.screen.ly.mapper.LyDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="queryRvBaseFact" resultType="map">
        select t1.RV_CODE,
                 t2.RV_LEN,
                 t2.RV_BAS_AREA,
                 t3.RV_LEN_PRO,
                 t3.RV_BAS_AREA_PRO
    from BNS_RIVER t1
    left join ATT_RV_BASE t2 on t1.RV_CODE=t2.RV_CODE
    left join BSN_RVINFOEX_B t3 on t1.RV_CODE=t3.RV_CODE
    where t1.RV_CODE = #{rvCode}
    </select>

    <select id="queryRvBranch" resultType="map">
        with river_base as (
        select t1.RV_CODE,
                     t1.RV_NAME,
                     t1.PRV_CODE,
                     t2.BANK
                from BNS_RIVER t1
                left join ATT_RV_BASE t2 on t1.rv_code=t2.rv_code
                where  t1.RV_CODE IN ('AAB00006','AAB10006','AAB21006','AAB13006','AAB11006','AAB10L06','AAB11B06','AAB11306','AAB11B56','AAB11B66','AAB13306','AAE00001','AAE13006','AAE11006','AAEl1506','AAE11526','ABA00000','ABA11006','ABA10006','ABD00000','ABD12006','AAD00001','AAD10006','AAD11006')
        )
        select t1.RV_CODE,
             (select count(1) from river_base t where t.PRV_CODE = t1.rv_code) LEAF_COUNT,
             t1.RV_NAME,
             t1.PRV_CODE,
             t1.BANK
       from river_base t1
       where t1.PRV_CODE = #{rvCode}
    </select>

    <select id="queryRv" resultType="map">
        select t1.RV_CODE,
               t1.RV_NAME,
               t1.PRV_CODE
        from BNS_RIVER t1
        where t1.RV_CODE =  #{rvCode}
    </select>

    <select id="queryRvSourLoc" resultType="map">
        select t.RV_SOUR_LOC from ATT_RV_BASE t where t.rv_code= #{rvCode}
    </select>

    <select id="querySkCountByRv" resultType="map">
        select t1.RV_CODE,count(1) VALUE
        from BNS_RIVER t1
        inner join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='1'
        inner join ATT_RES_BASE t3 on t3.res_code = t2.pro_id
        where t1.rv_code = #{rvCode}
        group by t1.rv_code
    </select>

    <select id="querySkFhByRv" resultType="map">
        select T1.RV_CODE,COUNT(1) VALUE, SUM(T3.TOT_CAP)  CAP
        from BNS_RIVER t1
        LEFT join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='1'
        LEFT join ATT_RES_BASE t3 on t3.res_code = t2.pro_id
        LEFT join FHGC_RPBIT t4 on t3.res_code = t4.res_code
        where t1.rv_code = #{rvCode}
        AND CHARINDEX(t3.eng_scal,#{engScal}) >0
        GROUP BY T1.RV_CODE
    </select>

    <select id="statisticSkFhByRv" resultType="map">
        select t1.RV_CODE,t3.RES_NAME,t3.RES_CODE,t3.TOT_CAP,
                     CASE WHEN CHARINDEX('1',t4.engts) >0 THEN '1' ELSE '0' END ISFH
        from BNS_RIVER t1
        LEFT join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='1'
        LEFT join ATT_RES_BASE t3 on t3.res_code = t2.pro_id
        LEFT join FHGC_RPBIT t4 on t3.res_code = t4.res_code
        where t1.rv_code = #{rvCode}
        AND CHARINDEX(t3.eng_scal,#{engScal}) >0
    </select>

    <select id="queryDikeLenByRv" resultType="map">
        select t1.RV_CODE,sum(t3.DIKE_LEN/1000) DIKE_LEN
        from BNS_RIVER t1
        LEFT join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='2'
        LEFT JOIN ATT_DIKE_BASE t3 on t3.dike_code = t2.pro_id
        where t1.RV_CODE = #{rvCode}
        group by t1.RV_CODE
    </select>

    <select id="queryDikeStatisticByRv" resultType="map">
        select CASE WHEN t3.DIKE_GRAD = 1 THEN '1级'
                    WHEN t3.DIKE_GRAD = 2 THEN '2级'
                    WHEN t3.DIKE_GRAD = 3 THEN '3级'
                    WHEN t3.DIKE_GRAD = 4 THEN '4级'
                    WHEN t3.DIKE_GRAD = 5 THEN '5级'
                    ELSE '其他' END name,
                    count(1) value
        from BNS_RIVER t1
        LEFT join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='2'
        LEFT JOIN ATT_DIKE_BASE t3 on t3.dike_code = t2.pro_id
        where t1.RV_CODE = #{rvCode}
        group by t3.DIKE_GRAD
    </select>

    <select id="queryDikeListByRv" resultType="map">
        select t1.RV_CODE,
                     t3.DIKE_CODE,
                     t3.DIKE_NAME,
                    CASE WHEN t3.DIKE_GRAD = 1 THEN '1级'
                    WHEN t3.DIKE_GRAD = 2 THEN '2级'
                                WHEN t3.DIKE_GRAD = 3 THEN '3级'
                                WHEN t3.DIKE_GRAD = 4 THEN '4级'
                                WHEN t3.DIKE_GRAD = 5 THEN '5级'
                              ELSE '其他' END DIKE_GRAD,
                    t3.DIKE_LEN,
                    t3.START_LOC,
                    t3.END_LOC,
                    t4.BANK
        from BNS_RIVER t1
        LEFT join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='2'
        LEFT JOIN ATT_DIKE_BASE t3 on t3.dike_code = t2.pro_id
        LEFT JOIN FHGC_DKCMIN t4 on t3.dike_code  = t4.dike_code
        left join REL_DIKE_AD t5  on t3.dike_code=t5.DIKE_CODE
        WHERE t1.RV_CODE = #{rvCode}
        order by t3.DIKE_GRAD asc,left(t5.ad_code,6) asc
    </select>

    <select id="queryHdCountByRv" resultType="map">
        SELECT t1.RV_CODE,COUNT(1) VALUE
        from BNS_RIVER t1
        inner join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='3'
        where t1.rv_code = #{rvCode}
        GROUP BY t1.RV_CODE
    </select>

    <select id="queryHdWarnCountByRv" resultType="map">
        with new_river as(
        select t2.* from (
        select t.stcd,max(tm) maxtm from ST_RIVER_R t where t.tm &gt; #{startTime} and t.tm &lt; #{endTime} group by t.stcd
        ) t1
        inner join ST_RIVER_R t2 on t1.stcd=t2.stcd and t1.maxtm = t2.tm
        )
        select t.rv_code,
        sum(t.WRZ) WRZCOUNT,
        sum(t.GRZ) GRZCOUNT
        from (
        select t1.RV_CODE,
        t3.stcd,
        case when t3.Z &gt; t4.WRZ and t3.Z &lt; t4.GRZ then 1 else 0 end WRZ,
        case when t3.Z &gt; t4.GRZ then 1 else 0 end GRZ
        from BNS_RIVER t1
        inner join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='3'
        inner join new_river t3 on t2.PRO_ID = t3.stcd
        inner join ST_RVFCCH_B t4 on t4.stcd = t3.stcd
        where t1.rv_code = #{rvCode}
        ) t
        group by t.rv_code
    </select>

    <select id="queryHdStatisticByRv" resultType="map">
        with new_river as(
            select t2.* from (
             select t.stcd,max(tm) maxtm from ST_RIVER_R t where t.tm &gt; #{startTime} and t.tm  &lt;= #{endTime} group by t.stcd) t1
             inner join ST_RIVER_R t2 on t1.stcd=t2.stcd and t1.maxtm = t2.tm
        )
        select
            t1.rv_code,
            t3.stcd,
            t3.tm,
            t3.z,
            t3.WPTN,
            t5.stnm,
            t6.PLTTD lttd,
            t6.PLGTD lgtd,
            t6.STADTP,
            case when t3.Z &gt; t4.WRZ and t3.Z &lt; t4.GRZ then 1 else 0 end wrz,
            case when t3.Z &gt; t4.GRZ then 1 else 0 end grz,
            t4.wrz wrzNum,
            t4.grz grzNum
        from BNS_RIVER t1
                 inner join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='3'
                 inner join new_river t3 on t2.PRO_ID = t3.stcd
                 left join ST_RVFCCH_B t4 on t4.stcd = t3.stcd
                 inner join ST_STBPRP_B t5 on t3.stcd = t5.stcd
                 left join BSN_STADTP_B t6 on t6.stcd = t3.stcd
        where
            t1.rv_code = #{rvCode}
        ORDER BY
            CASE WHEN SEQUENCE IS NULL THEN 1 ELSE 0 END ASC,SEQUENCE ASC
    </select>

    <select id="queryRainCountByRv" resultType="map">
        select t1.RV_CODE,count(1) VALUE
        from BNS_RIVER t1
        inner join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='4'
        where t1.RV_CODE = #{rvCode}
        GROUP BY t1.RV_CODE
    </select>

    <select id="queryRainStatisticByRv" resultType="map">
        select
            t3.stcd,
            t3.stnm,
            t5.PLGTD LGTD,
            t5.PLTTD LTTD,
            t5.ADCD,
            t4.drp
        from  bns_rv_pro t2
        inner join ST_STBPRP_B t3 on t2.PRO_ID=t3.stcd
        inner join (SELECT stcd,sum(drp) drp FROM ST_PPTN_R where TM &gt; #{startTime} AND TM &lt;= #{endTime} and drp >= 0 group by stcd)  t4 on t3.stcd=t4.stcd
        INNER JOIN BSN_STADTP_B t5 ON t3.stcd= t5.stcd
        where
            t2.PRO_TYPE='4'
        and
            t2.RV_CODE = #{rvCode}
        order by t4.drp desc
    </select>

    <select id="querySkStationCountByRv" resultType="map">
        select T1.RV_CODE,
			 COUNT(1) VALUE
        from BNS_RIVER t1
        inner join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='1'
        inner join ATT_RES_BASE t5 on t5.res_code = t2.pro_id
        inner join (select * from bsn_objonly_b where objtp = '6') t3 on t3.objcd = t2.pro_id
        inner join (select * from bsn_objonly_b where objtp = '1') t4 on t4.objid = t3.objid
        where t1.RV_CODE = #{rvCode} and CHARINDEX(t5.eng_scal,#{engScal}) > 0
        GROUP BY t1.RV_CODE
    </select>

    <select id="querySkStationCxxCountByRv" resultType="map">
        with stcdtm as (
        select stcd,max(tm) tm from ST_RSVR_R  WITH(NOLOCK) where tm &gt;= #{startTime} and tm&lt;= #{endTime}
        group by stcd
        ),
        rsvr as (
        select a.stcd,
        a.rz,
        a.rwptn
        from ST_RSVR_R a WITH(NOLOCK) inner join stcdtm b WITH(NOLOCK) on a.tm=b.tm and a.stcd=b.stcd and a.rz>0
        ),
        RSVRFSR1 as (
        select F.STCD, F.FSLTDZ, F.FSLTDW, F.MODITIME from stcdtm  a , ST_RSVRFSR_B  F WITH(NOLOCK)
        where  F.STCD = A.STCD AND CAST ( SUBSTRING(CONVERT(VARCHAR,A.TM,112),5,8) AS int ) between CAST (BGMD AS int)	AND CAST (EDMD AS int)
        ),
        RSVRFSR2 as (
        select stcd,FSLTDZ,FSLTDW,MODITIME from ( select stcd,FSLTDZ,FSLTDW,MODITIME ,ROW_NUMBER() over(partition by stcd order by MODITIME desc) rn from RSVRFSR1 )t where rn=1
        )
        select t1.RV_CODE,SUM(CASE WHEN t7.RZ > t8.FSLTDZ THEN 1 ELSE 0 END) VALUE
        from BNS_RIVER t1
        inner join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='1'
        inner join (select * from bsn_objonly_b where objtp = '6') t3 on t3.objcd = t2.pro_id
        inner join (select * from bsn_objonly_b where objtp = '1') t4 on t4.objid = t3.objid
        INNER JOIN ATT_RES_BASE t5 ON t5.RES_CODE = t3.objcd
        inner join rsvr t7 on t4.objcd  = t7.stcd
        inner join RSVRFSR2 t8 on t4.objcd = t8.stcd
        where t1.RV_CODE = #{rvCode}  and CHARINDEX(t5.eng_scal,#{engScal}) > 0
        GROUP BY t1.RV_CODE
    </select>

    <select id="querySkStationStatisticByRv" resultType="map">
        with stcdtm as (
        select stcd,max(tm) tm from ST_RSVR_R  WITH(NOLOCK) where tm &gt;= #{startTime} and tm&lt;= #{endTime}
        group by stcd
        ),
        rsvr as (
        select a.stcd,
        a.rz,
        a.rwptn
        from ST_RSVR_R a WITH(NOLOCK) inner join stcdtm b WITH(NOLOCK) on a.tm=b.tm and a.stcd=b.stcd and a.rz>0
        ),
        RSVRFSR1 as (
        select F.STCD, F.FSLTDZ, F.FSLTDW, F.MODITIME from stcdtm  a , ST_RSVRFSR_B  F WITH(NOLOCK)
        where  F.STCD = A.STCD AND CAST ( SUBSTRING(CONVERT(VARCHAR,A.TM,112),5,8) AS int ) between CAST (BGMD AS int)	AND CAST (EDMD AS int)
        ),
        RSVRFSR2 as (
        select stcd,FSLTDZ,FSLTDW,MODITIME from ( select stcd,FSLTDZ,FSLTDW,MODITIME ,ROW_NUMBER() over(partition by stcd order by MODITIME desc) rn from RSVRFSR1 )t where rn=1
        )
        select
               t6.STCD,t6.STNM,bsb.PLGTD LGTD,bsb.PLTTD LTTD,
               t7.RZ,t7.RWPTN,t8.FSLTDZ,t9.ADCD,
        CASE WHEN t7.RZ > t8.FSLTDZ THEN 1 ELSE 0 END ISCXX
        from BNS_RIVER t1
        inner join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='1'
        inner join (select * from bsn_objonly_b where objtp = '6') t3 on t3.objcd = t2.pro_id
        inner join (select * from bsn_objonly_b where objtp = '1') t4 on t4.objid = t3.objid
        INNER JOIN ATT_RES_BASE t5 ON t5.RES_CODE = t3.objcd
        inner join ST_STBPRP_B t6 on t6.stcd = t4.objcd
        left join  BSN_STADTP_B bsb on t6.stcd = bsb.stcd
        inner join rsvr t7 on t6.stcd  = t7.stcd
        inner join RSVRFSR2 t8 on t6.stcd = t8.stcd
        left join BSN_STBPRP_V t9 on t6.stcd=t9.stcd
        where t1.RV_CODE = #{rvCode} and CHARINDEX(t5.eng_scal,#{engScal}) > 0
    </select>

    <select id="queryFhCityListByRv" resultType="map">
        select
            t1.RV_CODE,
            t3.stcd,
            t3.adcd,
            t4.adnm + '|'+ t6.stnm adnm,
            t4.adfnm,
            t5.OBHTZ HTZ,
            t5.OBHTZTM HTZTM,
            t5.OBMXQ MXQ,
            t5.OBMXQTM MXQTM
        from BNS_RIVER t1
        inner join bns_rv_pro t2 on t1.RV_CODE = T2.RV_CODE and t2.PRO_TYPE='3'
        inner join BSN_STADTP_B t3 on t2.PRO_ID=t3.stcd
        INNER JOIN MDT_ADCDINFO_B t4 ON t4.adcd = LEFT ( t3.adcd, 6 ) + '000000000'
        left join ST_RVFCCH_B t5 on t5.stcd=t3.stcd
        left join ST_STBPRP_B t6 on t3.stcd=t6.stcd
        where t1.rv_code = #{rvCode}
        ORDER BY t2.SEQUENCE
    </select>

</mapper>
