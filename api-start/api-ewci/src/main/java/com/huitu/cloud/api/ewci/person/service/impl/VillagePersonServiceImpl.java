package com.huitu.cloud.api.ewci.person.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.exception.VillagePersonImportException;
import com.huitu.cloud.api.ewci.person.mapper.VillagePersonDao;
import com.huitu.cloud.api.ewci.person.mapper.VillagePersonTmpDao;
import com.huitu.cloud.api.ewci.person.service.VillagePersonService;
import com.huitu.cloud.util.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 水库淹没范围责任人 实现类
 *
 * <AUTHOR>
 */
@Service
public class VillagePersonServiceImpl implements VillagePersonService {

    private static final Logger logger = LoggerFactory.getLogger(VillagePersonServiceImpl.class);

    private VillagePersonDao baseDao;
    private VillagePersonTmpDao tmpDao;

    @Autowired
    public void setBaseDao(VillagePersonDao baseDao) {
        this.baseDao = baseDao;
    }

    @Autowired
    public void setTmpDao(VillagePersonTmpDao tmpDao) {
        this.tmpDao = tmpDao;
    }


    @Override
    public IPage<VillagePersonVo> getPageList(VillagePersonQuery query) {
        return baseDao.getPageList(query.toPageParam(), query.toQueryParam());
    }

    @Override
    public List<VillageSummaryVo> getVillageSummaryList(VillageSummaryQuery query) {
        if (query.getLevel() > 6) {
            throw new RuntimeException("行政区划代码无效，仅支持到县以上级别（含县）");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", query.getAdcd());
        params.put("level", query.getLevel());
        params.put("include", query.isInclude() ? 1 : 0);
        params.put("lowLevel", query.getLowLevel());
        List<VillageSummaryVo> source = baseDao.getVillageSummaryList(params);

        if (CollectionUtils.isEmpty(source)) {
            return new ArrayList<>();
        }
        if (!query.isInclude()) {
            // 不包含下级，无需创建树
            return source;
        }
        Map<String, VillageSummaryVo> center = new LinkedHashMap<>(source.size());
        source.forEach(ad -> center.put(ad.getAdcd(), ad));
        List<VillageSummaryVo> target = new ArrayList<>();
        for (VillageSummaryVo node : source) {
            if (center.containsKey(node.getPadcd())) {
                VillageSummaryVo parent = center.get(node.getPadcd());
                if (null == parent.getChildren()) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            } else {
                target.add(node);
            }
        }
        return target;
    }

    @Override
    public void dataExport(VillagePersonQuery query, OutputStream output) {
        IPage<VillagePersonVo> page = baseDao.getPageList(query.toPageParam(), query.toQueryParam());
        EasyExcel.write(output, VillagePersonVo.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("水库淹没范围责任人信息")
                .doWrite(page.getRecords());
    }

    @Override
    public ImportResult<BnsVillagePersonTmp> dataImport(String xadcd, InputStream input) {
        List<BnsVillagePersonTmp> tmpData;
        try {
            tmpData = EasyExcel.read(input, BnsVillagePersonTmp.class, null)
                    .headRowNumber(2).sheet().doReadSync();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);

            throw new VillagePersonImportException("Excel文件格式不正确，解析失败", ex);
        }

        if (CollectionUtils.isEmpty(tmpData)) {
            throw new VillagePersonImportException("Excel文件中不包含任何数据，请检查");
        }
        // 过滤空数据，任何一项有数据即为有效
        tmpData = tmpData.stream().filter(this::nonNulProperty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tmpData)) {
            throw new VillagePersonImportException("Excel文件中数据无效，请检查");
        }

        // 去掉多余的空格
        tmpData.forEach(this::trimProperty);

        try {
            String batchNo = batchInsertTmpData(xadcd, tmpData);
            List<BnsVillagePersonTmp> errorData = baseDao.batchImport(batchNo);
            if (!CollectionUtils.isEmpty(errorData)) {
                ImportResult<BnsVillagePersonTmp> result = new ImportResult<>();
                result.setStatus(tmpData.size() == errorData.size() ? 0 : 2);
                result.setMessage(String.format("数据（%s）导入失败，请检查", result.getStatus() == 0 ? "全部" : "部分"));
                result.setData(errorData);
                return result;
            }
        } catch (Exception ex) {
            logger.error("水库淹没范围责任人信息入库失败", ex);
            throw new VillagePersonImportException("数据未能成功入库，导入失败", tmpData, ex);
        }
        return new ImportResult<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int villagePersonEdit(VillagePersonVo entity) {
        List<VillagePersonB> list = new ArrayList<>();
        // 乡镇防汛责任人
        if (StringUtils.isNotBlank(entity.getXzRealnm()) && StringUtils.isNotBlank(entity.getXzMobile())) {
            VillagePersonB xzPerson = new VillagePersonB();
            xzPerson.setAdcd(entity.getAdcd());
            xzPerson.setPertp("1");
            xzPerson.setSno(entity.getSno());
            xzPerson.setRealnm(entity.getXzRealnm());
            xzPerson.setMobile(entity.getXzMobile());
            xzPerson.setTs(LocalDateTime.now());
            list.add(xzPerson);
        }

        // 村屯防汛责任人
        if (StringUtils.isNotBlank(entity.getCtRealnm()) && StringUtils.isNotBlank(entity.getCtMobile())) {
            VillagePersonB ctPerson = new VillagePersonB();
            ctPerson.setAdcd(entity.getAdcd());
            ctPerson.setPertp("2");
            ctPerson.setSno(entity.getSno());
            ctPerson.setRealnm(entity.getCtRealnm());
            ctPerson.setMobile(entity.getCtMobile());
            ctPerson.setTs(LocalDateTime.now());
            list.add(ctPerson);
        }
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", entity.getAdcd());
        param.put("sno", entity.getSno());
        int insertCode = 0;
        int delCode = baseDao.delAdcdAndSno(param);
        if (delCode > 0) {
            insertCode = baseDao.insertList(list);
        }
        return insertCode;
    }

    /**
     * 批量插入临时数据
     *
     * @param xadcd   县级行政区划代码
     * @param persons 责任人信息集合
     * @return 批号
     **/
    private String batchInsertTmpData(String xadcd, List<BnsVillagePersonTmp> persons) {
        String batchNo = UUID.randomUUID().toString();
        if (!CollectionUtils.isEmpty(persons)) {
            Map<String, Object> params = new HashMap<>();
            params.put("batchNo", batchNo);
            params.put("xadcd", xadcd);
            List<List<BnsVillagePersonTmp>> lists = ListUtils.splitList(persons, 150);
            for (List<BnsVillagePersonTmp> list : lists) {
                params.put("list", list);
                tmpDao.batchInsert(params);
            }
        }
        return batchNo;
    }

    /**
     * 属性不为空
     *
     * @param tmp 临时信息
     * @return true: 非空  false: 空
     **/
    private boolean nonNulProperty(BnsVillagePersonTmp tmp) {
        return StringUtils.isNotBlank(tmp.getResName())
                || StringUtils.isNotBlank(tmp.getXadnm())
                || StringUtils.isNotBlank(tmp.getZadnm())
                || StringUtils.isNotBlank(tmp.getXzadnm())
                || StringUtils.isNotBlank(tmp.getZradnm())
                || StringUtils.isNotBlank(tmp.getXzRealnm())
                || StringUtils.isNotBlank(tmp.getXzMobile())
                || StringUtils.isNotBlank(tmp.getCtRealnm())
                || StringUtils.isNotBlank(tmp.getCtMobile());
    }

    /**
     * 去掉字符串属性的空格（两端）
     *
     * @param tmp 临时信息
     **/
    /**
     * 去掉字符串属性的空格（两端）
     *
     * @param tmp 临时信息
     **/
    private void trimProperty(BnsVillagePersonTmp tmp) {
        tmp.setResName(StringUtils.trim(tmp.getResName()));
        tmp.setXadnm(StringUtils.trim(tmp.getXadnm()));
        tmp.setZadnm(StringUtils.trim(tmp.getZadnm()));
        tmp.setXzadnm(StringUtils.trim(tmp.getXzadnm()));
        tmp.setZradnm(StringUtils.trim(tmp.getZradnm()));
        tmp.setXzRealnm(StringUtils.trim(tmp.getXzRealnm()));
        tmp.setXzMobile(StringUtils.trim(tmp.getXzMobile()));
        tmp.setCtRealnm(StringUtils.trim(tmp.getCtRealnm()));
        tmp.setCtMobile(StringUtils.trim(tmp.getCtMobile()));
    }


}
