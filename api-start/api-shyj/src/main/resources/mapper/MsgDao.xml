<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.shyj.msg.mapper.MsgDao">

    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getMsgSendReport1" resultType="com.huitu.cloud.api.shyj.msg.entity.MsgVo">
        SELECT AD.ADCD,MAX(AD.ADNM) ADNM,MAX(AD.ADLVL) ADLVL,
            SUM(CASE WHEN A.SENDRESULT=0
            <if test="stm!=null and stm!=''">
                AND B.SENDTM &gt;= #{stm}
            </if>
            <if test="etm!=null and etm!=''">
                AND B.SENDTM &lt;= #{stm}
            </if>
            THEN 1 ELSE 0 END) FAILED,
            SUM(CASE WHEN A.SENDRESULT=1
            <if test="stm!=null and stm!=''">
                AND B.SENDTM &gt;= #{stm}
            </if>
            <if test="etm!=null and etm!=''">
                AND B.SENDTM &lt;= #{stm}
            </if>
            THEN 1 ELSE 0 END)	SUCCESS
        FROM
        (SELECT ADCD,ADNM,ADLVL FROM BSN_ADCD_B WHERE (SUBSTRING(ADCD,1,#{level})=#{ad} OR SUBSTRING(PADCD,1,#{level})=#{ad}) AND ADLVL&lt;=2) AD
        LEFT JOIN MESSAGESEND_R A ON AD.ADCD=A.ADCD
        LEFT JOIN MESSAGEINFO_R B ON A.ADCD=B.ADCD AND A.MSGID=B.MSGID
        WHERE 1=1
        <if test="ad!=null and ad!='' and level!=null and level!=''">
            AND substring(AD.ADCD,1,#{level})=#{ad}
        </if>
        GROUP BY AD.ADCD ORDER BY AD.ADCD,ADLVL
    </select>
    <select id="getMsgSendReport2" resultType="com.huitu.cloud.api.shyj.msg.entity.MsgVo">
        SELECT AD.ADCD,MAX(AD.ADNM) ADNM,MAX(AD.ADLVL) ADLVL,
        SUM(CASE WHEN A.SENDRESULT=0
        <if test="stm!=null and stm!=''">
            AND B.SENDTM &gt;= #{stm}
        </if>
        <if test="etm!=null and etm!=''">
            AND B.SENDTM &lt;= #{stm}
        </if>
        THEN 1 ELSE 0 END) FAILED,
        SUM(CASE WHEN A.SENDRESULT=1
        <if test="stm!=null and stm!=''">
            AND B.SENDTM &gt;= #{stm}
        </if>
        <if test="etm!=null and etm!=''">
            AND B.SENDTM &lt;= #{stm}
        </if>
        THEN 1 ELSE 0 END)	SUCCESS
        FROM
        (SELECT ADCD,ADNM,ADLVL FROM BSN_ADCD_B WHERE (SUBSTRING(ADCD,1,#{level})=#{ad} OR SUBSTRING(PADCD,1,#{level})=#{ad}) AND ADLVL=3) AD
        LEFT JOIN MESSAGESEND_R A ON AD.ADCD=A.ADCD
        LEFT JOIN MESSAGEINFO_R B ON A.ADCD=B.ADCD AND A.MSGID=B.MSGID
        WHERE 1=1
        <if test="ad!=null and ad!='' and level!=null and level!=''">
            AND substring(AD.ADCD,1,#{level})=#{ad}
        </if>
        GROUP BY AD.ADCD ORDER BY AD.ADCD,ADLVL
    </select>
</mapper>
