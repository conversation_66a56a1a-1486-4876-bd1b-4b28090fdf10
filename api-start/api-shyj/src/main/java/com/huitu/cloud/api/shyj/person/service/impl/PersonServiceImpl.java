package com.huitu.cloud.api.shyj.person.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.shyj.person.entity.Person;
import com.huitu.cloud.api.shyj.person.entity.RelevantPersionInfo;
import com.huitu.cloud.api.shyj.person.mapper.PersonDao;
import com.huitu.cloud.api.shyj.person.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人员信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
@Service
public class PersonServiceImpl extends ServiceImpl<PersonDao, Person> implements PersonService {
    @Autowired
    private PersonDao personDao;

    @Override
    public IPage<RelevantPersionInfo> getPersionList(String adcd, String name, List<String> depts, String mobile, String type, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        if(depts==null||depts.size()==0){
            depts.add("n");
        }
        param.put("adcd", adcd);
        param.put("name", name);
        param.put("depts", depts);
        param.put("mobile", mobile);
        param.put("type", type);
        IPage<RelevantPersionInfo> resultMap = personDao.getPersionListByPage(page, param);
        return resultMap;
    }

    @Override
    public String addPerson(Person entity) {
        Person person=personDao.selectMaxId();
        entity.setPersoncd(person.getPersoncd());
        personDao.insert(entity);
        return "1";
    }

    @Override
    public String updatePerson(Person entity) {
        personDao.updateById(entity);
        return "1";
    }

    @Override
    public String delPerson(String personcd) {
        personDao.delPerson(personcd);
        return "1";
    }
}
