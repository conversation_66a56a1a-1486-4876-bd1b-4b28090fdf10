<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwWarningRecordDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getFullAdnm" resultType="java.lang.String">
        <choose>
            <when test="map.level == '0'.toString()">
                SELECT ADNM FROM MDT_ADCDINFO_B WHERE ADCD = #{map.adcd}
            </when>
            <otherwise>
                SELECT dbo.fnGetFullAdnm(#{map.adcd}, #{map.level})
            </otherwise>
        </choose>
    </select>
    <select id="getPageList" resultType="com.huitu.cloud.api.ew.entity.response.EwWarningRecordResponse" useCache="false">
        SELECT A.WARN_ID, <PERSON><PERSON>ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME,
        D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME,
        E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC,
        WARN_STDT, A.LGTD, A.LTTD, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME
        FROM EW_WARNING_RECORD A
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
        LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID
        LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID
        LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID
        LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30
        LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID
        WHERE A.WARN_TYPE_ID = 10
        <if test="map.warnGradeList != null and map.warnGradeList.size() > 0">
            <foreach collection="map.warnGradeList" item="warnGrade" separator=", " open="AND A.WARN_GRADE_ID IN (" close=")">
                #{warnGrade}
            </foreach>
        </if>
        <if test="map.warnStatusList != null and map.warnStatusList.size() > 0">
            <foreach collection="map.warnStatusList" item="warnStatus" separator=", " open="AND A.WARN_STATUS_ID IN (" close=")">
                #{warnStatus}
            </foreach>
        </if>
        <if test="map.warnModeList != null and map.warnModeList.size() > 0">
            <foreach collection="map.warnModeList" item="warnMode" separator=", " open="AND A.WARN_MODE IN (" close=")">
                #{warnMode}
            </foreach>
        </if>
        AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">AND LEFT(A.ADCD, 6) NOT IN ('220581')</if>
        <if test="map.bscd != null and map.bscd != ''">
            AND EXISTS(SELECT 8 FROM BSN_BAS_AD WHERE ADCD = A.ADCD AND BAS_CODE = #{map.bscd})
        </if>
        AND WARN_TIME >= #{map.stm}
        <if test="map.etm != null">AND WARN_TIME &lt;= #{map.etm}</if>
        <if test="map.warnName != null and map.warnName.trim() != ''">AND CHARINDEX(#{map.warnName}, WARN_NAME) > 0</if>
        ORDER BY A.WARN_TIME DESC, LEFT(A.ADCD, 12) ASC, A.WARN_NAME DESC
    </select>
    <select id="getLatestPageList" resultType="com.huitu.cloud.api.ew.entity.response.EwWarningRecordResponse" useCache="false">
        SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID,
        WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME,
        WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM (
        SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM,
        WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME,
        A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME,
        A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK,
        COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME
        FROM EW_WARNING_RECORD A
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
        LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID
        LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID
        LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID
        LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30
        LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID
        WHERE A.WARN_TYPE_ID = 10
        <if test="map.warnGradeList != null and map.warnGradeList.size() > 0">
            <foreach collection="map.warnGradeList" item="warnGrade" separator=", " open="AND A.WARN_GRADE_ID IN (" close=")">
                #{warnGrade}
            </foreach>
        </if>
        <if test="map.warnStatusList != null and map.warnStatusList.size() > 0">
            <foreach collection="map.warnStatusList" item="warnStatus" separator=", " open="AND A.WARN_STATUS_ID IN (" close=")">
                #{warnStatus}
            </foreach>
        </if>
        <if test="map.warnModeList != null and map.warnModeList.size() > 0">
            <foreach collection="map.warnModeList" item="warnMode" separator=", " open="AND A.WARN_MODE IN (" close=")">
                #{warnMode}
            </foreach>
        </if>
        AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">AND LEFT(A.ADCD, 6) NOT IN ('220581')</if>
        <if test="map.bscd != null and map.bscd != ''">
            AND EXISTS(SELECT 8 FROM BSN_BAS_AD WHERE ADCD = A.ADCD AND BAS_CODE = #{map.bscd})
        </if>
        AND WARN_TIME >= #{map.stm}
        <if test="map.etm != null">AND WARN_TIME &lt;= #{map.etm}</if>
        <if test="map.warnName != null and map.warnName.trim() != ''">AND CHARINDEX(#{map.warnName}, WARN_NAME) > 0</if>
        ) T WHERE T.SORTNO = 1
        ORDER BY WARN_TIME DESC, ADCD ASC
    </select>
    <select id="getDigestList" resultType="com.huitu.cloud.api.ew.entity.response.EwWarningDigestResponse">
        SELECT WARN_ID, A.ADCD, ADNM, WARN_TIME, WARN_NAME, WARN_DESC, A.PLATFORM_ID, ISSUING_UNIT PLATFORM_NAME
        FROM EW_WARNING_RECORD A
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
        LEFT JOIN EW_WARNING_CONF C ON C.PLATFORM_ID = A.PLATFORM_ID
        WHERE WARN_ID IN (<foreach collection="warnIds" item="warnId" separator=",">#{warnId}</foreach>)
        ORDER BY A.ADCD ASC, WARN_TIME DESC
    </select>
    <select id="getRecord" resultType="com.huitu.cloud.api.ew.entity.response.EwWarningRecordResponse">
        SELECT A.WARN_ID, A.ADCD, C.ADNM, A.WARN_TYPE_ID, D.[TYPE_NAME] WARN_TYPE_NAME, B.WARN_GRADE_ID, E.GRADE_NAME WARN_GRADE_NAME,
        E.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, F.STATUS_NAME WARN_STATUS_NAME, F.ALIAS_NAME WARN_STATUS_ALIAS_NAME,
        F.SHORT_NAME WARN_STATUS_SHORT_NAME, B.WARN_MODEL, B.WARN_TIME, G.CREATE_TIME WARN_CLOSE_TIME, A.WARN_MODE, A.WARN_NAME,
        B.WARN_DESC, A.WARN_STDT, A.LGTD, A.LTTD, B.REMARK, B.PLATFORM_ID, H.ISSUING_UNIT PLATFORM_NAME
        FROM EW_WARNING_RECORD A
        <choose>
            <when test="warnGradeId != null">
                LEFT JOIN EW_WARNING_PROCESS B ON B.WARN_ID = A.WARN_ID AND B.WARN_GRADE_ID = #{warnGradeId}
            </when>
            <otherwise>
                LEFT JOIN EW_WARNING_PROCESS B ON B.WARN_ID = A.WARN_ID AND B.WARN_GRADE_ID = A.WARN_GRADE_ID
            </otherwise>
        </choose>
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
        LEFT JOIN EW_WARNING_TYPE D ON D.[TYPE_ID] = A.WARN_TYPE_ID
        LEFT JOIN EW_WARNING_GRADE E ON E.GRADE_ID = B.WARN_GRADE_ID
        LEFT JOIN EW_WARNING_STATUS F ON F.STATUS_ID = A.WARN_STATUS_ID
        LEFT JOIN EW_WARNING_FLOW G ON G.WARN_ID = A.WARN_ID AND G.WARN_GRADE_ID = A.WARN_GRADE_ID AND G.WARN_STATUS_ID = 30
        LEFT JOIN EW_WARNING_CONF H ON H.PLATFORM_ID = B.PLATFORM_ID
        WHERE B.WARN_ID = #{warnId}
    </select>
    <select id="getMfdPersonList" resultType="com.huitu.cloud.api.ew.entity.ext.EwMessageReceiver">
        SELECT '2' [TYPE], MOBILE PHONE_NO, REALNM RECEIVER, DUTY POSITION, ('11' + RIGHT('00' + RYTP, 2)) TAG, REMARK
        FROM (SELECT (ROW_NUMBER() OVER (PARTITION BY MOBILE ORDER BY RYTP, REALNM, ZRC ASC)) SNO,
                     MOBILE,
                     REALNM,
                     RYTP,
                     (CASE
                          WHEN DUTY IS NOT NULL AND LTRIM(RTRIM(DUTY)) != '' THEN LTRIM(RTRIM(DUTY))
                          WHEN RYTP = '1' THEN '县级责任人'
                          WHEN RYTP = '2' THEN '乡镇责任人'
                          WHEN RYTP = '3' THEN '行政村责任人'
                          WHEN RYTP = '4' THEN '自然村责任人'
                          WHEN RYTP = '5' THEN '监测责任人'
                          WHEN RYTP = '6' THEN '预警责任人'
                          WHEN RYTP = '7' THEN '转移责任人'
                          ELSE NULL END)                                                      DUTY,
                     (CASE
                          WHEN RYTP = '1' THEN XS.ADNM
                          WHEN RYTP = '2' THEN XZ.ADNM
                          WHEN RYTP = '3' THEN CT.ADNM
                          WHEN RYTP = '4' THEN ZRC
                          ELSE NULL END)                                                      REMARK
              FROM BSN_SH_PERSON_B A WITH (NOLOCK)
                       LEFT JOIN MDT_ADCDINFO_B XS ON XS.ADCD = LEFT(A.ADCD, 6) + '000000000'
                       LEFT JOIN MDT_ADCDINFO_B XZ ON XZ.ADCD = LEFT(A.ADCD, 9) + '000000'
                       LEFT JOIN MDT_ADCDINFO_B CT ON CT.ADCD = A.ADCD
              WHERE RYTP IS NOT NULL
                AND A.ADCD = LEFT(#{adcd}, 12) + '000') T
        WHERE T.SNO = 1
        ORDER BY RYTP ASC
    </select>
    <select id="getFddpPersonList" resultType="com.huitu.cloud.api.ew.entity.ext.EwMessageReceiver">
        WITH SH_DEPT AS (SELECT DEPTID, DEPTNM, PDEPTID, ADCD
                         FROM BNS_DEPTINFO_B A
                         WHERE EXISTS(SELECT 8
                                      FROM EW_WARNING_CONF
                                      WHERE PLATFORM_ID = LEFT(#{adcd}, 6)
                                        AND RECEIVING_UNIT = A.PDEPTID)
                         UNION ALL
                         SELECT T.DEPTID, T.DEPTNM, T.PDEPTID, T.ADCD
                         FROM BNS_DEPTINFO_B T
                                  INNER JOIN SH_DEPT D ON D.DEPTID = T.PDEPTID),
             XS_DEPT AS (SELECT DEPTID, DEPTNM, PDEPTID, ADCD FROM SH_DEPT WHERE LEFT(ADCD, #{level}) = LEFT(#{adcd}, #{level}))
        SELECT '1'    [TYPE],
               MOBILE PHONE_NO,
               REALNM RECEIVER,
               POST   POSITION,
               TAG,
               DEPTNM REMARK
        FROM (SELECT (ROW_NUMBER() OVER (PARTITION BY MOBILE ORDER BY REALNM ASC)) RNO,
                     C.ADCD,
                     A.MOBILE,
                     A.REALNM,
                     B.POST,
                     C.DEPTNM,
                     (CASE
                          WHEN PATINDEX('%[局][长]%', ISNULL(POST, '')) > 0 THEN '110A'
                          WHEN PATINDEX((CASE WHEN LEFT(ADCD, 4) != '2222' THEN '%[市州县区][长]%' ELSE '%长白山管委会副主任%' END),
                                        ISNULL(POST, '')) > 0 THEN NULL
                          ELSE '110B' END)                                         TAG
              FROM BNS_USERINFO_B A WITH (NOLOCK)
                       LEFT JOIN BNS_USERDEPT_B B ON B.USERID = A.USERID
                       LEFT JOIN XS_DEPT C ON C.DEPTID = B.DEPTID
              WHERE A.STAT = '1'
                AND A.DISFLG = '1'
                AND C.ADCD = LEFT(LEFT(#{adcd}, #{level}) + '000000000000000', 15)) T
        WHERE T.RNO = 1
          AND T.TAG IN ('110A', '110B')
        ORDER BY T.TAG ASC
    </select>
    <select id="getCorrectList" resultType="com.huitu.cloud.api.ew.entity.response.EwWarningCorrectResponse" useCache="false">
        SELECT A.WARN_ID,
               A.ADCD,
               C.ADNM,
               B.STCD,
               STNM,
               E.ADNM STADNM,
               WARN_TYPE_ID,
               A.WARN_GRADE_ID,
               WARN_STATUS_ID,
               WARN_MODEL,
               WARN_TIME,
               WARN_MODE,
               WARN_NAME,
               WARN_DESC,
               WARN_STDT,
               A.LGTD,
               A.LTTD,
               REMARK
        FROM EW_WARNING_RECORD A
                 LEFT JOIN EW_SNAPSHOT_MONITOR B ON B.WARN_ID = A.WARN_ID AND B.WARN_GRADE_ID = A.WARN_GRADE_ID
                 LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
                 LEFT JOIN ST_STBPRP_B D ON D.STCD = B.STCD
                 LEFT JOIN MDT_ADCDINFO_B E
                           ON EXISTS(SELECT 8 FROM BSN_STADTP_B WHERE STCD = B.STCD AND LEFT(ADCD, 6) + '000000000' = E.ADCD)
        WHERE WARN_STATUS_ID &lt; 30
          AND EXISTS(SELECT 8
                     FROM EW_WARNING_RECORD X
                              LEFT JOIN EW_SNAPSHOT_MONITOR Y ON Y.WARN_ID = X.WARN_ID AND Y.WARN_GRADE_ID = X.WARN_GRADE_ID
                     WHERE X.WARN_TYPE_ID = A.WARN_TYPE_ID
                       AND X.WARN_TIME = A.WARN_TIME
                       AND X.WARN_STDT = A.WARN_STDT
                       AND Y.STCD = B.STCD
                       AND X.WARN_ID = #{warnId})
        ORDER BY A.ADCD ASC
    </select>
    <select id="getCorrectReceivers" resultType="com.huitu.cloud.api.ew.entity.sms.SmsReceiver" useCache="false">
        SELECT PHONE_NO, RECEIVER
        FROM (SELECT (ROW_NUMBER() OVER (PARTITION BY PHONE_NO ORDER BY RECEIVER ASC)) RNO, PHONE_NO, RECEIVER
              FROM EW_RECEIVER A
                       LEFT JOIN EW_MESSAGE B ON B.MSG_ID = A.MSG_ID
              WHERE MSG_TYPE = '11'
                AND PUSH_MODE = '1'
                AND EXISTS(SELECT 8 FROM EW_REL_WARNING_MESSAGE WHERE CHARINDEX(WARN_ID, #{warnIds}) > 0 AND MSG_ID = A.MSG_ID)) T
        WHERE T.RNO = 1
    </select>
    <select id="getSuddenMessageParameter" resultType="java.lang.String">
        SELECT 'time=' + FORMAT(WARN_TIME, 'M月d日 H时m分') +
               '&amp;xznm=' + dbo.fnGetFullAdnm(LEFT(ADCD, 9) + '000000', 4) +
               '&amp;ctnm=' + dbo.fnGetFullAdnm(ADCD, 5)
        FROM EW_WARNING_RECORD A
                 LEFT JOIN EW_WARNING_CONF B ON B.PLATFORM_ID = LEFT(ADCD, 6)
        WHERE WARN_ID = #{warnId}
    </select>
    <insert id="create">
        INSERT INTO EW_WARNING_RECORD(WARN_ID, ADCD, WARN_TYPE_ID, WARN_GRADE_ID, WARN_STATUS_ID, WARN_MODEL, WARN_TIME, WARN_MODE,
                                      WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, LATEST_TIME, PLATFORM_ID)
        VALUES (#{record.warnId}, #{record.adcd}, #{record.warnTypeId}, #{record.warnGradeId}, #{record.warnStatusId}, #{record.warnModel},
                #{record.warnTime}, #{record.warnMode}, #{record.warnName}, #{record.warnDesc}, #{record.warnStdt}, #{record.lgtd},
                #{record.lttd}, #{record.remark}, GETDATE(), #{record.platformId})
    </insert>
    <update id="correct">
        UPDATE EW_WARNING_RECORD
        SET WARN_STATUS_ID = 31,
            LATEST_TIME    = GETDATE()
        WHERE WARN_STATUS_ID &lt; 30
          AND CHARINDEX(WARN_ID, #{warnIds}) > 0
    </update>
    <update id="close">
        UPDATE EW_WARNING_RECORD
        SET WARN_STATUS_ID = 30,
            LATEST_TIME    = GETDATE()
        WHERE WARN_STATUS_ID &lt; 30
          AND WARN_ID = #{warnId}
    </update>
    <update id="updateStatus">
        UPDATE EW_WARNING_RECORD
        SET WARN_STATUS_ID = #{warnStatusId},
            LATEST_TIME    = GETDATE()
        WHERE WARN_ID = #{warnId}
    </update>
    <update id="manualConfirm">
        UPDATE EW_WARNING_RECORD
        SET WARN_STATUS_ID = 21, LATEST_TIME = GETDATE()
        WHERE WARN_STATUS_ID &lt; 21
        AND WARN_ID IN (<foreach collection="warnIds" item="warnId" separator=",">#{warnId}</foreach>)
    </update>
</mapper>