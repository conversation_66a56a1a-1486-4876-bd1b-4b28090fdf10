package com.huitu.cloud.api.ewci.monitor.controller;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.monitor.entity.RainData;
import com.huitu.cloud.api.ewci.monitor.entity.RiskLocation;
import com.huitu.cloud.api.ewci.monitor.entity.RiskQuery;
import com.huitu.cloud.api.ewci.monitor.service.RiskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 风险 周边降雨
 *
 * <AUTHOR>
 */
@Api(tags = "周边信息")
@RestController
@RequestMapping("/api/ewci/monitor/risk")
public class RiskResource extends AbstractApiResource implements ApiResource {


    @Override
    public String getUuid() {
        return "a75e7942-d8b8-4c65-bc45-2e97f48f12f2";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final RiskService baseService;

    public RiskResource(RiskService baseService) {
        this.baseService = baseService;
    }

    @ApiOperation(value = "根据测站编码查询政区、经度、纬度", notes = "根据测站编码查询政区、经度、纬度")
    @GetMapping(value = "select-location")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<RiskLocation>> getLocation(@RequestParam String stcd) throws Exception {
        RiskLocation tree = baseService.getLocation(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", tree));
    }

    @ApiOperation(value = "风险-周边降雨列表查询", notes = "作者：赵英捷")
    @PostMapping("select-rainfall-list")
    public ResponseEntity<SuccessResponse<List<RainData>>> getRainfallList(@RequestBody RiskQuery qo) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRainfallList(qo)));
    }
}
