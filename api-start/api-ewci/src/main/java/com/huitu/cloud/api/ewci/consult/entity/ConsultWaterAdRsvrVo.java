package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @description: 实时水情-水情统计-地市-水库实体
 * @author: jiangjy
 * @create: 2024-4-17
 **/
@ApiModel(value = "实时水情-水情统计-市县-水库实体")
public class ConsultWaterAdRsvrVo {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "正常水位站数量")
    private Integer rz;

    @ApiModelProperty(value = "超讯限水位站数量")
    private Integer rzfsltdz;

    @ApiModelProperty(value = "超设计水位站数量")
    private Integer rzDsflz;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getRz() {
        return rz;
    }

    public void setRz(Integer rz) {
        this.rz = rz;
    }

    public Integer getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(Integer rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public Integer getRzDsflz() {
        return rzDsflz;
    }

    public void setRzDsflz(Integer rzDsflz) {
        this.rzDsflz = rzDsflz;
    }
}
