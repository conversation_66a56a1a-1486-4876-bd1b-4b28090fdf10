package com.huitu.cloud.api.fusion.entity.response;

import com.huitu.cloud.api.fusion.entity.base.FusionNoticeReceiver;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Set;

/**
 * 预警人工通知接收人响应对象
 */
@ApiModel(value = "预警人工通知接收人响应对象")
public class FusionNoticeReceiverResponse extends FusionNoticeReceiver {
    private static final long serialVersionUID = 1L;

    public FusionNoticeReceiverResponse(FusionNoticeReceiver receiver) {
        super(receiver);
    }

    @ApiModelProperty(value = "叫应ID集合")
    private Set<String> callIds;
    @ApiModelProperty(value = "预警ID集合")
    private Set<String> warnIds;

    public Set<String> getCallIds() {
        return callIds;
    }

    public void setCallIds(Set<String> callIds) {
        this.callIds = callIds;
    }

    public Set<String> getWarnIds() {
        return warnIds;
    }

    public void setWarnIds(Set<String> warnIds) {
        this.warnIds = warnIds;
    }
}
