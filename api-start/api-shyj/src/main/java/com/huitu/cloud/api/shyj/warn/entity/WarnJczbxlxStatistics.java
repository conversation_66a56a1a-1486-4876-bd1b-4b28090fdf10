package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2019-09-17
 */
@ApiModel(value="预警关联预警设施统计信息", description="预警关联预警设施统计信息")
public class WarnJczbxlxStatistics implements Serializable {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "雨量站总数")
    private String ppcount;
    @ApiModelProperty(value = "河道站总数")
    private String zzcount;
    @ApiModelProperty(value = "水库站总数")
    private String rrcount;
    @ApiModelProperty(value = "简易雨量站总数")
    private String srst;
    @ApiModelProperty(value = "简易水位站总数")
    private String swst;
    @ApiModelProperty(value = "图像站总数")
    private String pic;
    @ApiModelProperty(value = "视频站总数")
    private String video;
    @ApiModelProperty(value = "无线广播站总数")
    private String wbrst;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPpcount() {
        return ppcount;
    }

    public void setPpcount(String ppcount) {
        this.ppcount = ppcount;
    }

    public String getZzcount() {
        return zzcount;
    }

    public void setZzcount(String zzcount) {
        this.zzcount = zzcount;
    }

    public String getRrcount() {
        return rrcount;
    }

    public void setRrcount(String rrcount) {
        this.rrcount = rrcount;
    }

    public String getSrst() {
        return srst;
    }

    public void setSrst(String srst) {
        this.srst = srst;
    }

    public String getSwst() {
        return swst;
    }

    public void setSwst(String swst) {
        this.swst = swst;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getVideo() {
        return video;
    }

    public void setVideo(String video) {
        this.video = video;
    }

    public String getWbrst() {
        return wbrst;
    }

    public void setWbrst(String wbrst) {
        this.wbrst = wbrst;
    }
}
