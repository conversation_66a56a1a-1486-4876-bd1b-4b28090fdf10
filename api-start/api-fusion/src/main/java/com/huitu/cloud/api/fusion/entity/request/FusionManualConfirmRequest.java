package com.huitu.cloud.api.fusion.entity.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * 预警人工确认请求对象
 */
@ApiModel(value = "预警人工确认请求对象")
public class FusionManualConfirmRequest {

    @ApiModelProperty(value = "叫应ID集合")
    @NotEmpty(message = "参数[预警ID集合]不能为空")
    private Set<String> callIds;
    @ApiModelProperty(value = "预警ID集合")
    @NotEmpty(message = "参数[预警ID集合]不能为空")
    private Set<String> warnIds;
    /**
     * 确认人
     */
    @JsonIgnore
    private String confirmBy;

    public Set<String> getCallIds() {
        return callIds;
    }

    public void setCallIds(Set<String> callIds) {
        this.callIds = callIds;
    }

    public Set<String> getWarnIds() {
        return warnIds;
    }

    public void setWarnIds(Set<String> warnIds) {
        this.warnIds = warnIds;
    }

    public String getConfirmBy() {
        return confirmBy;
    }

    public void setConfirmBy(String confirmBy) {
        this.confirmBy = confirmBy;
    }
}
