package com.huitu.cloud.api.ewci.weChat.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.bia.entity.StRiverVos;
import com.huitu.cloud.api.ewci.weChat.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SituationRemarkDao extends BaseMapper {

    /**
     * 查询所有测站累计雨量
     *
     * @param param
     * @return
     */
    List<WeChatRain> getAccpByTmAll(Map<String, Object> param);

    /**
     * 查询所有雨量测站
     *
     * @param param
     * @return
     */
    List<WeChatRain> getRainStInfo(@Param("map") Map<String, Object> param);

    /**
     * 根据流域名称查询所有的流域
     *
     * @param bsnm 流域名称
     * @return
     */
    List<WeChatBsnBasStBTo> getRiverTree(@Param("bsnm") String bsnm);

    /**
     * 查询当前政区以及下级 列表信息  可以通过adLevl控制查询的级别
     *
     * @param map
     * @return
     */
    List<WeChatAdcdB> selectByAdLevel(Map<String, Object> map);

    /**
     * 获取未来一小时降雨信息
     *
     * @return
     */
    List<Map<String, Object>> getOneHourRain();

    /**
     * 查询下级政区的面平均雨量和最大雨量
     *
     * @param param map对象必须注解
     * @return
     */
    List<WeChatAdAvgRain> getRainAdAvg(@Param("map") Map<String, Object> param);

    /**
     * 降雨告警列表
     *
     * @param page  分页
     * @param param 参数集合
     * @param page
     * @return
     */
    IPage<WeChatBsnRainAlarm> getRainWarn(Page page, @Param("map") Map<String, Object> param);

    /**
     * 存储过程生成最新水库水情数据
     *
     * @param stm 开始时间
     * @param etm 结束时间
     */
    IPage<WeChatStRsvrVo> getRsvrLatestData(Page page, @Param("stm") String stm, @Param("etm") String etm, @Param("whereSql") String whereSql);

    /**
     * 最新河道水情信息列表
     *
     * @param page
     * @param param
     * @return
     */
    IPage<StRiverVos> getRiverByCondition(Page<StRiverVos> page, @Param("map") Map<String, Object> param);
    /**
     * 根据文件标题获取文件最新的一个文件信息
     *  @param filetitle
     * @return
     */
    List<WeChatBnsFilesAndUrls> getLastestBnsFileurlsr(@Param("filetitle") String  filetitle);
    /**
     * 查询最新的天气预报
     * @return
     */
    WeChatWeatherVo getWeatherForecast();

    List<WeChatQxpicgetRVo> getWxytList(@Param("tms") int tms, @Param("picType")String picType);

}
