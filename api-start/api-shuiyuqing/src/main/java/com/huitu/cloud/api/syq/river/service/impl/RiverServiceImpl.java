package com.huitu.cloud.api.syq.river.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.syq.rain.entity.BsnBasStBTo;
import com.huitu.cloud.api.syq.rain.mapper.RainDao;
import com.huitu.cloud.api.syq.river.entity.*;
import com.huitu.cloud.api.syq.river.mapper.StRiverDao;
import com.huitu.cloud.api.syq.river.service.RiverService;
import com.huitu.cloud.entity.LoginUserInfo;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import com.huitu.cloud.util.IPAndUserUtil;
import com.huitu.cloud.util.MapBeanConvertUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 河道水情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
@Service
public class RiverServiceImpl extends ServiceImpl<StRiverDao, StRiverR> implements RiverService {

    @Autowired
    private RainDao rainDao;

    @Override
    public IPage<StRiverVo> getRiverByConditon(String adcd, String stm, String etm, List<String> stType, String stnm,
                                               boolean dataFlag, boolean warnFlag, String bscd, int pageNum, int pageSize, List<String> isOut, List<String> isFollow, boolean videoFlag) {
        Page<StRiverVo> page = new Page<StRiverVo>(pageNum, pageSize);
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals(""))) || (isOut != null && (isOut.size() == 0 || isOut.get(0).equals("")))) {
            List<StRiverVo> stList = new ArrayList<>();
            page.setRecords(stList);
            return page;
        }
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stType", stType);
        param.put("adcd", adcd);
        param.put("ad", adcd.substring(0, adLevl));
        param.put("dataFlag", dataFlag);
        param.put("warnFlag", warnFlag);
        param.put("videoFlag", videoFlag);
        param.put("stnm", stnm != null ? stnm.trim() : "");
        param.put("adLevl", adLevl);
        //辖区内与辖区外
        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
        if (org.apache.commons.lang.StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
            String userAd = loginUserInfo.getUserAd();
            int level2 = AdcdUtil.getAdLevel(userAd);
            param.put("userLevel", level2);
            param.put("userAd", userAd);
            param.put("userId", loginUserInfo.getUserId());
        }
//        param.put("userAd","220000000000000");
//        param.put("userLevel",2);
//        param.put("userId",1);
        //添加是否查询域外（省级）
        if (isOut != null && isOut.size() == 1) {
            if (isOut.get(0).equals("1")) {
                //查询域内
                param.put("isOut", "1");
            } else {
                //查询域外
                param.put("isOut", "2");
            }
        } else if (isOut != null && isOut.size() == 2) {
            //查询域内域外都查
            param.put("isOut", "3");
            param.put("ad2", adcd.substring(0, adLevl));
        }
        // 添加是否关注站点参数
        if (isFollow != null && isFollow.size() == 1) {
            if (isFollow.get(0).equals("1")) {
                //查询关注站点
                param.put("isFollow", "1");
            } else {
                //查询未关注站点
                param.put("isFollow", "2");
            }
        }

        //查询结果对象返回map 可以根据service接口需要，转化成自己需要实体返回类型
        IPage<StRiverVo> result = baseMapper.getRiverByCondition(page, param);
        List<StRiverVo> stList = result.getRecords();
        List<StRiverVo> stList1 = filterRainListByBscd(stList, bscd);
        result.setRecords(stList1);
        return result;
    }

    private List<StRiverVo> filterRainListByBscd(List<StRiverVo> stList, String bscd) {
        if (bscd != null && !bscd.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<BsnBasStBTo> bsList = rainDao.getRiverTree(bscd);
            List<String> stcdList = bsList.stream().map(BsnBasStBTo::getStcd).collect(Collectors.toList());
            List<StRiverVo> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            stList = intersection;
            return intersection;
        } else {
            return stList;
        }
    }

    @Override
    public List<RiverTmVo> getRiverList(String stcd, String stm, String etm) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stcd", stcd);
        List<RiverTmVo> list = baseMapper.getRiverTmList(param);
        return list;
    }

    @Override
    public List<StRvsectB> getRiverDuanMianList(String stcd, String mstm) {
        List<StRvsectB> list = baseMapper.getRiverDuanMianList(stcd, mstm);
        return list;
    }

    @Override
    public List<StRvsectB> getNewRiverDuanMianList(String stcd) {
        List<StRvsectB> list = baseMapper.getNewRiverDuanMianList(stcd);
        return list;
    }

    @Override
    public List<RiverWarnByAdcdTmVo> selectWarnStatisticsByAdcdTm(String adcd, String stm, String etm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<RiverStWarnByAdcdTmVo> list = baseMapper.selectWarnStatisticsByAdcdTm(param);
        Map<String, List<RiverStWarnByAdcdTmVo>> listMap = list.stream().collect(Collectors.groupingBy(RiverStWarnByAdcdTmVo::getStcd));

        List<RiverWarnByAdcdTmVo> result = new ArrayList<>();
        listMap.forEach((k, stcdList) -> {
            // 警戒线流量
            BigDecimal wrq = stcdList.get(0).getWrq();
            // 最大动态流量
            double maxQ = stcdList.stream().mapToDouble(item -> item.getQ() == null ? 0 : item.getQ().doubleValue()).max().orElse(0);
            // 最大动态流量
            double minQ = stcdList.stream().mapToDouble(item -> item.getQ() == null ? 0 : item.getQ().doubleValue()).min().orElse(0);
            // x月x日超警戒水位取当天8点的值（倒序排列）
            List<RiverStWarnByAdcdTmVo> collect = stcdList.stream().filter(item -> item.getTm().getHour() == 8).sorted(Comparator.comparing(RiverStWarnByAdcdTmVo::getTm).reversed()).collect(Collectors.toList());

            RiverWarnByAdcdTmVo riverWarnByAdcdTmVo = new RiverWarnByAdcdTmVo();
            riverWarnByAdcdTmVo.setStcd(stcdList.get(0).getStcd());
            riverWarnByAdcdTmVo.setStnm(stcdList.get(0).getStnm());
            riverWarnByAdcdTmVo.setMaxQ(BigDecimal.valueOf(maxQ));
            riverWarnByAdcdTmVo.setMinQ(BigDecimal.valueOf(minQ));
            riverWarnByAdcdTmVo.setWrq(wrq);
            riverWarnByAdcdTmVo.setWarnWrzList(collect);
            result.add(riverWarnByAdcdTmVo);
        });
        return result;
    }


    @Override
    public List<String> getRiverDuanMianTmList(String stcd) {
        List<String> list = baseMapper.getRiverDuanMianTmList(stcd);
        return list;
    }

    @Override
    public IPage<RiverDayAvg> getDayAvgRiverByPage(String stm, String etm, List<String> stList, String tmType, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        String stcds = "";
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("tmType", tmType);
        if (stList.size() > 0) {
            for (String item : stList) {
                stcds += "'" + item + "'" + ",";
            }
            param.put("stcds", stcds.substring(0, stcds.length() - 1));
        } else {
            //登录用户信息
            LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
            if (StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
                int adLevl = AdcdUtil.getAdLevel(loginUserInfo.getUserAd());
                param.put("ad", loginUserInfo.getUserAd().substring(0, adLevl));
                param.put("level", adLevl);
            }
        }
        //查询结果对象返回map 可以根据service接口需要，转化成自己需要实体返回类型
        IPage<RiverDayAvg> resultMap = baseMapper.getDayAvgRiverByPage(page, param);
        return resultMap;
    }

    @Override
    public List<QueryStZqrlb> getZqNameBySt(String stcd) {
        List<QueryStZqrlb> zqrlList = baseMapper.getZqNameBySt(stcd);
        return zqrlList;
    }

    @Override
    public List<StZqrlb> getZq(String stcd, List<QueryStZqrlb> list) {
        Map<String, Object> param = new HashMap<>();
        param.put("stcd", stcd);
        param.put("list", list);
        List<StZqrlb> result = baseMapper.getZq(param);
        return result;
    }

    @Override
    public IPage<StForecastf> getZqForecastByPage(String adcd, String stnm, String wnstatus, Date stm, Date etm, List<String> stcds, int pageNum, int pageSize) {
        Page<StForecastf> page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("stnm", stnm);
        param.put("wnstatus", wnstatus);
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stcds", stcds);
        // 查询结果对象返回map 可以根据service接口需要，转化成自己需要实体返回类型
        IPage<Map<String, Object>> resultMap = baseMapper.getZqForecastByPage(page, param);
        // 转化实体 实体中添加字段注释
        List<StForecastf> result = MapBeanConvertUtil.mapsToBeans(resultMap.getRecords(), StForecastf.class);
        List<StRvyevsqS> htzTop10 = baseMapper.getStRvyevsqSTop10("HTZ");
        List<StRvyevsqS> mxqTop10 = baseMapper.getStRvyevsqSTop10("MXQ");
        Map<String, List<StRvyevsqS>> htzStcdTop10 = htzTop10.stream().collect(Collectors.groupingBy(StRvyevsqS::getStcd));
        Map<String, List<StRvyevsqS>> mxqStcdTop10 = mxqTop10.stream().collect(Collectors.groupingBy(StRvyevsqS::getStcd));
        result.forEach(item -> {
            if (htzStcdTop10.containsKey(item.getStcd())) {
                List<StRvyevsqS> stRvyevsqS = htzStcdTop10.get(item.getStcd());
                if (stRvyevsqS != null && !stRvyevsqS.isEmpty()) {
                    item.setHtzListTop10(stRvyevsqS);
                    BigDecimal htz = stRvyevsqS.get(0).getHtz();
                    if (null != htz) {
                        item.setObhtz(htz.setScale(2, RoundingMode.HALF_UP));
                    }
                }
            }
            if (mxqStcdTop10.containsKey(item.getStcd())) {
                List<StRvyevsqS> stRvyevsqS = mxqStcdTop10.get(item.getStcd());
                if (stRvyevsqS != null && !stRvyevsqS.isEmpty()) {
                    item.setMxqListTop10(stRvyevsqS);
                    BigDecimal mxq = stRvyevsqS.get(0).getMxq();
                    if (null != mxq) {
                        item.setObmxq(mxq.setScale(2, RoundingMode.HALF_UP));
                    }
                }
            }
        });
        page.setRecords(result);
        return page;
    }

    @Override
    public List<OneStForecast> getZqForecastByOneSt(String stcd) {
        List<OneStForecast> oneStForecasts = baseMapper.getZqForecastByOneSt(stcd);
        return oneStForecasts;
    }

    @Override
    public StDsnfld getStDsnfld(StDsnfldQuery param) {
        StDsnfld stDsnfld = baseMapper.getStDsnfld(param);
        if (stDsnfld == null) {
            stDsnfld = new StDsnfld();
        }

        try {
            List<StRvyevsqS> rvevsList = baseMapper.listStRvyevsq(param.getStcd());
            if (rvevsList != null && rvevsList.size() > 5) {
                rvevsList = rvevsList.subList(0, 5);
            }
            stDsnfld.setRvevsList(rvevsList);
        } catch (Exception ex) {
            log.warn(ex.toString());
        }
        return stDsnfld;
    }

    @Override
    public List<StRvevsR> listStRvevs(StRvevsQuery param) {
        return baseMapper.listStRvevs(param);
    }

    @Override
    public RiverWarnVo getRiverWarn(String adcd, String stm, String etm, List<String> stType, String stnm, String bscd) {
        //超保证集合
        List<StRiverVo> warnGrzList = new ArrayList<>();
        //超警戒集合
        List<StRiverVo> warnWrzList = new ArrayList<>();
        if (stType != null && (stType.size() == 0 || stType.get(0).equals(""))) {
            RiverWarnVo riverWarnVo = new RiverWarnVo();
            riverWarnVo.setWarnGrzList(warnGrzList);
            riverWarnVo.setWarnWrzList(warnWrzList);
            return riverWarnVo;
        }
        IPage<StRiverVo> pageRecord = getRiverByConditon(adcd, stm, etm, stType, stnm, true, true, bscd, 1, -1, null, null, false);
        List<StRiverVo> list = pageRecord.getRecords();
        list.forEach(x -> {
            //超警戒
            double zwrz = x.getZwrz().doubleValue();
            //超保证
            double zgrz = x.getZgrz() != null ? x.getZgrz().doubleValue() : 0;
            //水文上保证水位大于警戒水位 所以河道超警戒后 会有部分站数据超保证 测站只归属高级别的超警
            //测站在超保证 就不在超警戒中存储
            if (zgrz > 0) {
                warnGrzList.add(x);
            } else {
                warnWrzList.add(x);
            }
        });
        RiverWarnVo riverWarnVo = new RiverWarnVo();
        riverWarnVo.setWarnGrzList(warnGrzList);
        riverWarnVo.setWarnWrzList(warnWrzList);
        return riverWarnVo;
    }

    @Override
    public List<StRiverVo> getStRiverMaxWarnList(String adcd, String stm, String etm) {
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, adLevl));
        List<StRiverVo> result = baseMapper.getStRiverMaxWarnList(param);
        return result;
    }

    @Override
    public RiverInfoByDuty selectRiverInfoByDuty(String adcd, String stm, String etm) {
        List<String> stType = new ArrayList<>();
        stType.add("1");
        stType.add("2");
        stType.add("3");
        stType.add("4");
        stType.add("5");
        IPage<StRiverVo> page = getRiverByConditon(adcd, stm, etm, stType, "",
                true, true, "", 1, -1, null, null, false);
        List<StRiverVo> riverVoList = page.getRecords();
        RiverInfoByDuty riverInfoByDuty = new RiverInfoByDuty();
        riverInfoByDuty.setZwrzNum(riverVoList.size());
        riverInfoByDuty.setRiverVoList(riverVoList);
        return riverInfoByDuty;
    }

    @Override
    public void exportRiverByConditon(int type, String adcd, String stm, String etm, List<String> stType, String stnm, boolean dataFlag, boolean warnFlag, String bsnm, boolean videoFlag) {
        IPage<StRiverVo> riverVoIPage = getRiverByConditon(adcd, stm, etm, stType, stnm, dataFlag, warnFlag, bsnm, 0, -1, null, null, videoFlag);
        List<StRiverVo> list = riverVoIPage.getRecords();
        //导出
        if (type == 1) {
            ExcelExportUtil.execute(list, "综合监视最新河道水情");
        } else if (type == 2) {
            list.stream().forEach(item -> {
                String adnm = "";
                if (org.apache.commons.lang.StringUtils.isBlank(item.getXzadnm())) {
                    adnm = item.getXadnm();
                } else {
                    if (item.getXzadnm().equals(item.getXadnm())) {
                        adnm = item.getXadnm();
                    } else {
                        adnm = item.getXadnm() + " - " + item.getXzadnm();
                    }
                }
                item.setAdnm(adnm);
            });
            ExcelExportUtil.execute(list, "最新河道水情");
        }
    }

    @Override
    public void exportDayAvgRiver(String stm, String etm, List<String> stList, String tmType) {
        IPage<RiverDayAvg> dayAvgIPage = getDayAvgRiverByPage(stm, etm, stList, tmType, 0, -1);
        List<RiverDayAvg> list = dayAvgIPage.getRecords();
        ExcelExportUtil.execute(list, "河道日旬月均值信息");
    }

    @Override
    public void exportStRiverMaxWarn(String adcd, String stm, String etm) {
        List<StRiverVo> list = getStRiverMaxWarnList(adcd, stm, etm);
        ExcelExportUtil.execute(list, "最大超警水位统计");
    }

    @Override
    public void exportRiverTm(String stm, String etm, String stcd) {
        List<RiverTmVo> list = getRiverList(stcd, stm, etm);
        ExcelExportUtil.execute(list, "水位流量过程");
    }

    @Override
    public List<RiverNearWarnVo> selectNearWarnTemp(RiverNearWarnQuery query) {
        return baseMapper.selectNearWarnTemp(query.toQueryParam());
    }

    @Override
    public List<StForecastf> getForecastLatestTime(String adcd, String bscd, String stm, String etm) {
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, adLevl));
        param.put("level", adLevl);
        List<StForecastf> result = baseMapper.getForecastLatestTime(param);
        result = filterForecastLatestTimeByBscd(result, bscd);
        return result;
    }

    private List<StForecastf> filterForecastLatestTimeByBscd(List<StForecastf> stList, String bscd) {
        if (bscd != null && !bscd.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<BsnBasStBTo> bsList = rainDao.getRiverTree(bscd);
            List<String> stcdList = bsList.stream().map(BsnBasStBTo::getStcd).collect(Collectors.toList());
            List<StForecastf> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            return intersection;
        } else {
            return stList;
        }
    }

    @Override
    public List<BnsRiverBaseDikeSVo> getRiverBaseDikeList() {
        return baseMapper.getRiverBaseDikeList();
    }

    @Override
    public void exportRiverBaseDike(HttpServletResponse response) {
        List<BnsRiverBaseDikeSVo> list = getRiverBaseDikeList();
        ExcelExportUtil.excleDownloadModel(list, BnsRiverBaseDikeSVo.class, "沙基砂堤未治理段", response);
    }

    @Override
    public List<BnsRiverDikeSVo> getRiverDikeList() {
        return baseMapper.getRiverDikeList();
    }

    @Override
    public void exportRiverDike(HttpServletResponse response) {
        List<BnsRiverDikeSVo> list = getRiverDikeList();
        ExcelExportUtil.excleDownloadModel(list, BnsRiverDikeSVo.class, "主要江河未达标堤段", response);
    }

    @Override
    public RiverZuoZhanTuVo getRiverForZuoZhanTu(RiverZuoZhanTuQuery query) {
        return baseMapper.getRiverForZuoZhanTu(query.toQueryParam());
    }

}
