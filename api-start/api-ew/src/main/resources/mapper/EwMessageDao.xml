<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwMessageDao">
    <select id="getMessageList" resultType="com.huitu.cloud.api.ew.entity.ext.EwWarningMessage">
        SELECT A.MSG_ID,
               A.WARN_ID,
               A.GRADE_ID,
               A.STATUS_ID,
               B.PUSH_MODE
        FROM EW_REL_WARNING_MESSAGE A
                 LEFT JOIN EW_MESSAGE B ON A.MSG_ID = B.MSG_ID
        WHERE A.WARN_ID = #{warnId}
        ORDER BY A.LATEST_TIME ASC
    </select>
    <insert id="insert">
        INSERT INTO EW_MESSAGE(MSG_ID, MSG_TYPE, PUSH_MODE, TAG, SENDER, SEND_DEPT, LATEST_TIME)
        VALUES (#{msgId}, #{msgType}, #{pushMode}, #{tag}, #{sender}, #{sendDept}, GETDATE())
    </insert>
</mapper>
