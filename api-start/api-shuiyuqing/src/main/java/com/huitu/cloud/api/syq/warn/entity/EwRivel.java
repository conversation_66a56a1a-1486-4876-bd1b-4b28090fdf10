package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@TableName("EW_RIVEL")
@ApiModel(value="EwRivel对象", description="")
public class EwRivel extends Model<EwRivel> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "河道编码")
    @TableField("HSCD")
    private String hscd;

    @ApiModelProperty(value = "预报时间")
    @TableField("TM")
    private LocalDateTime tm;

    @ApiModelProperty(value = "预警流量")
    @TableField("Q")
    private Double q;

    @ApiModelProperty(value = "预警时间")
    @TableField("WARN_TM")
    private LocalDateTime warnTm;

    @ApiModelProperty(value = "LEVELH")
    @TableField("LEVELH")
    private Double levelh;

    @ApiModelProperty(value = "预警指标")
    @TableField("WARNQ")
    private Double warnq;

    @ApiModelProperty(value = "主键编码")
    @TableId(value = "ID", type = IdType.NONE)
    private String id;


    public String getHscd() {
        return hscd;
    }

    public void setHscd(String hscd) {
        this.hscd = hscd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public Double getQ() {
        return q;
    }

    public void setQ(Double q) {
        this.q = q;
    }

    public LocalDateTime getWarnTm() {
        return warnTm;
    }

    public void setWarnTm(LocalDateTime warnTm) {
        this.warnTm = warnTm;
    }

    public Double getLevelh() {
        return levelh;
    }

    public void setLevelh(Double levelh) {
        this.levelh = levelh;
    }

    public Double getWarnq() {
        return warnq;
    }

    public void setWarnq(Double warnq) {
        this.warnq = warnq;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "EwRivel{" +
        "hscd=" + hscd +
        ", tm=" + tm +
        ", q=" + q +
        ", warnTm=" + warnTm +
        ", levelh=" + levelh +
        ", warnq=" + warnq +
        ", id=" + id +
        "}";
    }
}
