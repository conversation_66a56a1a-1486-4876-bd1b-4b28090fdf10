package com.huitu.cloud.api.shyj.stwarnrule.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel
public class SwWarnRule extends Stwarnrule implements Serializable {
    @ApiModelProperty(value = " 测站名称 ")
    private String stnm;

    @ApiModelProperty(value = "预警等级名称 ")
    private String warngradenm;

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getWarngradenm() {
        return warngradenm;
    }

    public void setWarngradenm(String warngradenm) {
        this.warngradenm = warngradenm;
    }
}
