package com.huitu.cloud.api.ewci.consult.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.consult.entity.*;
import com.huitu.cloud.api.ewci.consult.mapper.ConsultDao;
import com.huitu.cloud.api.ewci.consult.service.ConsultService;
import com.huitu.cloud.api.ewci.exception.ConsultDefenseWorkPlanException;
import com.huitu.cloud.api.ewci.remote.OssRemoteService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ListUtils;
import feign.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 流域服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class ConsultServiceImpl  implements ConsultService {

    private static final String TEMPLATE_FILE_PATH = "template/consult_defense_work_plan.docx";

    private static final Logger LOGGER = LoggerFactory.getLogger(ConsultServiceImpl.class);

    private ConsultDao baseDao;

    private OssRemoteService ossService;

    @Autowired
    public void setBaseDao(ConsultDao baseDao, OssRemoteService ossService) {
        this.baseDao = baseDao;
        this.ossService = ossService;
    }

    @Override
    public List<ConsultWaterAdRiverVo> getWaterAdRiverStatList(String adcd, String stm, String etm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        List<ConsultWaterAdRiverVo> list = baseDao.getWaterAdRiverStatList(param);
        return list;
    }

    @Override
    public List<ConsultWaterAdRsvrVo> getWaterAdRsvrStatList(String adcd, String stm, String etm, String rvtp, String stType) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        param.put("rvtp", rvtp);
        param.put("stType", stType);
        List<ConsultWaterAdRsvrVo> list = baseDao.getWaterAdRsvrStatList(param);
        return list;
    }

    @Override
    public List<ConsultWaterBasRiverVo> getWaterBasRiverStatList(String basCode, String stm, String etm) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("basCode", basCode);
        List<ConsultWaterBasRiverVo> list = baseDao.getWaterBasRiverStatList(param);
        return list;
    }

    @Override
    public List<ConsultWaterBasRiverVo> getWaterBasZyjhRiverStatList(String stm, String etm) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        List<ConsultWaterBasRiverVo> list = baseDao.getWaterBasZyjhRiverStatList(param);
        return list;
    }

    @Override
    public List<ConsultWaterBasRsvrVo> getWaterBasRsvrStatList(String basCode, String stm, String etm, String rvtp, String stType) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("basCode", basCode);
        param.put("rvtp", rvtp);
        param.put("stType", stType);
        List<ConsultWaterBasRsvrVo> list = baseDao.getWaterBasRsvrStatList(param);
        return list;
    }

    @Override
    public List<ConsultWaterBasRsvrVo> getWaterBasZyjhRsvrStatList(String stm, String etm, String rvtp, String stType) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("rvtp", rvtp);
        param.put("stType", stType);
        List<ConsultWaterBasRsvrVo> list = baseDao.getWaterBasZyjhRsvrStatList(param);
        return list;
    }

    @Override
    public List<ConsultWarnAdVo> getWarnAdShStatList(String adcd, String stm, String etm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        List<ConsultWarnAdVo> list = baseDao.getWarnAdShStatList(param);
        return list;
    }

    @Override
    public List<ConsultWarnAdVo> getWarnAdRiverStatList(String adcd, String stm, String etm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        List<ConsultWarnAdVo> list = baseDao.getWarnAdRiverStatList(param);
        return list;
    }

    @Override
    public List<ConsultWarnAdVo> getWarnAdRsvrStatList(String adcd, String stm, String etm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        List<ConsultWarnAdVo> list = baseDao.getWarnAdRsvrStatList(param);
        return list;
    }

    @Override
    public List<ConsultRiskAdRiverVo> getRiskAdRiverStatList(String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        List<ConsultRiskAdRiverVo> list = baseDao.getRiskAdRiverStatList(param);
        return list;
    }

    @Override
    public List<ConsultRiskAdRsvrVo> getRiskAdRsvrStatList(String adcd, String rvtp, String stType) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adcd", adcd);
        param.put("rvtp", rvtp);
        param.put("stType", stType);
        List<ConsultRiskAdRsvrVo> list = baseDao.getRiskAdRsvrStatList(param);
        return list;
    }

    @Override
    public List<ConsultRiskBasRiverVo> getRiskBasRiverStatList(String basCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("basCode", basCode);
        List<ConsultRiskBasRiverVo> list = baseDao.getRiskBasRiverStatList(param);
        return list;
    }

    @Override
    public List<ConsultRiskBasRsvrVo> getRiskBasRsvrStatList(String basCode, String rvtp, String stType) {
        Map<String, Object> param = new HashMap<>();
        param.put("basCode", basCode);
        param.put("rvtp", rvtp);
        param.put("stType", stType);
        List<ConsultRiskBasRsvrVo> list = baseDao.getRiskBasRsvrStatList(param);
        return list;
    }

    @Override
    public IPage<BsnDefenseWorkPlanB> getPage(DefenseWorkPlanQo<BsnDefenseWorkPlanB> query) {
        IPage<BsnDefenseWorkPlanB> source = baseDao.getPage(query.toPageParam(), query.toQueryParam());
        return source;
    }

    @Override
    public BsnDefenseWorkPlanB getMaxPlanNo() {
        return baseDao.getMaxReportNo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BsnDefenseWorkPlanB insert(BsnDefenseWorkPlanB entity) {
        entity.setDwpId(getUUID());
        baseDao.insert(entity);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BsnDefenseWorkPlanB update(BsnDefenseWorkPlanB entity) {
        baseDao.update(entity);
        return entity;
    }

    @Override
    public Integer saveFile(DefenseWorkPlanSo request) {
        if (StringUtils.isNotBlank(request.getFilePath())) {
            try {
                ossService.deleteFile(request.getFilePath());
            } catch (Exception ignored) {
            }
        }
        request.setFileName(String.format("吉林省%s年第%s号防御工作指导意见", request.getReportYear(), request.getyReportNo()));
        ResponseEntity<SuccessResponse<Map<String, String>>> response = ossService.copyFile(
                TEMPLATE_FILE_PATH, request.getFileName());
        Map<String, String> map = Objects.requireNonNull(response.getBody()).getData();
        request.setFileName(map.get("filename"));
        request.setFilePath(map.get("filepath"));

        updateDocument(request);

        return baseDao.updateReportUrl(request.getDwpId(), request.getFileName(), request.getFilePath());
    }

    private void updateDocument(DefenseWorkPlanSo request) {
        try {

            Calendar cal = Calendar.getInstance();
            Map<String, String> valueMap = new HashMap<>();
            valueMap.put("year", String.valueOf(cal.get(Calendar.YEAR)));
            valueMap.put("month", String.valueOf(cal.get(Calendar.MONTH) + 1));
            valueMap.put("day", String.valueOf(cal.get(Calendar.DATE)));
            valueMap.put("rainInfo", request.getRainInfo());
            valueMap.put("riverInfo", request.getRiverInfo());
            valueMap.put("rsvrInfo", request.getRsvrInfo());
            String patternString = "\\$\\{(" + StringUtils.join(valueMap.keySet(), "|") + ")\\}";
            Pattern pattern = Pattern.compile(patternString);

            Response response = ossService.getFile(request.getFilePath(), request.getFileName());
            XWPFDocument document = new XWPFDocument(response.body().asInputStream());
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            for (XWPFParagraph paragraph : paragraphs) {
                String text = paragraph.getText();
                Matcher matcher = pattern.matcher(text);

                StringBuffer sb = new StringBuffer();
                while (matcher.find()) {
                    matcher.appendReplacement(sb, valueMap.get(matcher.group(1)));
                }
                matcher.appendTail(sb);

                String valueStr = sb.toString();
                List<XWPFRun> runs = paragraph.getRuns();
                for (int i = 0; i < runs.size(); i++) {
                    XWPFRun run = runs.get(i);
                    if (i == 0) {
                        run.setText(valueStr, 0);
                    } else {
                        run.setText("", 0);
                    }
                }
            }

            // 创建字节流
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            // 将文档输出至流
            document.write(output);
            // 更新原始文件
            ossService.putFile(request.getFilePath().replace("/", "HTJLUP"), output.toByteArray());
        } catch (Exception ex) {
            LOGGER.error("[防御工作方案]文档更新失败", ex);
        }
    }

    @Override
    public BsnDefenseWorkPlanB getDocument(String dwpId) {
        return baseDao.getDocument(dwpId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BsnDefenseWorkPlanB send(String dwpId) {
        if (baseDao.isRepeatFileNo(dwpId)) {
            throw new ConsultDefenseWorkPlanException("发文字号重复，请修改后重试");
        }
        int code = baseDao.updateStatus(dwpId);
        if (code > 0) {
            return baseDao.getFileById(dwpId);
        }
        throw new ConsultDefenseWorkPlanException("发送失败，请刷新后重试");
    }


    /**
     * 生成UUID
     *
     * @return
     */
    public String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
