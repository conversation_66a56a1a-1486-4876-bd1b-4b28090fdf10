package com.huitu.cloud.api.fusion.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.api.fusion.entity.response.FusionFollowupResponse;
import com.huitu.cloud.entity.EntityRequest;
import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.validation.constraints.SqlInjection;
import com.huitu.cloud.validation.constraints.StringLength;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 山洪预警后续降雨请求对象
 */
@Getter
@Setter
@ApiModel(value = "山洪预警后续降雨请求对象")
public class FusionFollowupRequest extends PageBean<FusionFollowupResponse> implements EntityRequest {

    @ApiModelProperty(value = "行政区划代码", example = "220000000000000", required = true)
    @SqlInjection(message = "参数[行政区划代码]包含非法字符")
    @NotBlank(message = "参数[行政区划代码]不能为空")
    @StringLength(min = 15, max = 15, message = "参数[行政区划代码]的长度应为15个字符")
    private String adcd;

    @ApiModelProperty(value = "开始时间，注：必填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-03 08:00:00", required = true)
    @NotNull(message = "参数[开始时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间，注：选填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-04 08:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;

    @ApiModelProperty(value = "预警名称，注：模糊匹配")
    @SqlInjection(message = "参数[预警名称]包含非法字符")
    private String warnName;

    @Override
    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("stm", getStm());
        if (null != getEtm()) {
            Date btm = getStm(), etm = getEtm();
            if (btm.getTime() >= etm.getTime()) {
                throw new IllegalArgumentException("参数[结束时间]应大于参数[开始时间]");
            }
            params.put("etm", etm);
        }
        params.put("warnName", getWarnName());
        return params;
    }
}
