package com.huitu.cloud.api.screen.fhjz.service;

import com.huitu.cloud.api.screen.fhjz.entity.DikeDetailVo;
import com.huitu.cloud.api.screen.fhjz.entity.DikeVo;
import com.huitu.cloud.api.screen.fhjz.entity.RainCountVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 堤防 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-15
 */
public interface DikeScreenService {

    /**
     * 查询堤防列表
     * @param dikeGrad 测站编码
     * @return
     */
    List<DikeVo> queryDikeList(String dikeGrad,String dikeName);

    /**
     * 查询堤防工程详情列表
     * @param dikeGrad 堤防等级
     * @param dikeName 堤防名称
     * @return
     */
    List<DikeDetailVo> dikeDetailList(String dikeGrad,String dikeName);
    /**
     * 查询堤防信息
     *
     * @param dikeCode
     * @return
     */
    Map<String, Object> queryDikeInfo(String dikeCode);

    /**
     * 查询堤防数量详情
     *
     * @param adcd
     * @return
     */
    String statisticDikeDetail(String adcd);
}
