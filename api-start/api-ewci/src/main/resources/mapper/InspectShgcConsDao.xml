<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.inspect.mapper.InspectShgcConsDao">

    <select id="getInspectShgcConsList"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectShgcCons">
        SELECT *
        FROM BNS_INSPECT_SHGC_CONS
        WHERE ADCD = #{adcd} AND [YEAR] = #{year}
          AND EN_CODE = #{enCode}
    </select>
    <select id="selectYearByInspectId" resultType="java.util.Date">
        select INSP_DATE FROM BNS_INSPECT_SHGC_CONS  WHERE INSPECT_ID = #{inspectId}
    </select>
    <insert id="insertInspectShgcCons">
        INSERT INTO BNS_INSPECT_SHGC_CONS(INSPECT_ID,
                                          ADCD,
                                          INSPECTOR,
                                          GROUPID,
                                          INSP_DATE,
                                          EN_CODE,
                                          EXIST_PROBLEMS,
                                          REC_ASK,
                                          REC_DATE,
                                          YEAR,
                                          LGTD,
                                          LTTD, CREATOR)
        VALUES ( #{inspectId}
               , #{adcd}
               , #{inspector}
               , #{groupid}
               , #{inspDate}
               , #{enCode}
               , #{existProblems}
               , #{recAsk}
               , #{recDate}
               , #{year}
               , #{lgtd}
               , #{lttd}, #{creator})
    </insert>
    <update id="updateInspectShgcCons">
        UPDATE BNS_INSPECT_SHGC_CONS
        <trim prefix="set" suffixOverrides=",">
            <if test="adcd != null">ADCD = #{adcd},</if>
            <if test="inspector != null">INSPECTOR = #{inspector},</if>
            <if test="groupid != null">GROUPID = #{groupid},</if>
            <if test="inspDate != null">INSP_DATE = #{inspDate},</if>
            <if test="enCode != null">EN_CODE = #{enCode},</if>
            <if test="existProblems != null">EXIST_PROBLEMS = #{existProblems},</if>
            <if test="recAsk != null">REC_ASK = #{recAsk},</if>
            <if test="recDate != null">REC_DATE = #{recDate},</if>
            <if test="year != null">[YEAR] = #{year},</if>
            <if test="lgtd != null">LGTD = #{lgtd},</if>
            <if test="lttd != null">LTTD = #{lttd},</if>
        </trim>
        WHERE INSPECT_ID=#{inspectId} AND CREATOR = #{creator}
    </update>

    <delete id="deleteInspectShgcCons">
        DELETE
        FROM BNS_INSPECT_SHGC_CONS
        WHERE INSPECT_ID = #{inspectId}
    </delete>

</mapper>