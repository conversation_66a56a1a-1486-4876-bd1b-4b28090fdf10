<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.shyj.warn.mapper.WarnrecordDao">

    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        WARNID, ADCD, WARNTYPEID, WARNGRADEID, WARNSTATUSID, WARNSTM, WARNETM, WARNNM, WARNDESC, INNERWARNTM, INNERWARNGRADEID, INNERWARNDESC, INNERWARNUSER, OUTWARNTM, OUTWARNGRADEID, OUTWARNDESC, OUTWARNUSER, TYPE, ISHANDMADE, ISFLASH, LGTD, LTTD, <PERSON>CC<PERSON><PERSON><PERSON>, OCCURTM, REMARK, MODEL
    </sql>
    <insert id="insertWarnStatusrecordR" parameterType="com.huitu.cloud.api.shyj.warn.entity.WarnrecordR">
       insert into SD_WARN_STATUSRECORD_R(WarnID,Time,WarnStatusID,WarnGradeID,WarnContent,OCCURUSER,OCCURTIME,remark,ADCD)
          values(#{warnId,jdbcType=VARCHAR},getdate(),#{warnStatusId,jdbcType=INTEGER},#{warnGradeId,jdbcType=INTEGER},#{warnDesc,jdbcType=VARCHAR},
                #{occurUser,jdbcType=VARCHAR},getdate(),#{remark,jdbcType=VARCHAR},#{adcd,jdbcType=VARCHAR})
    </insert>
    <insert id="insertWarnrecordT" parameterType="com.huitu.cloud.api.shyj.warn.entity.WarnrecordR">
      insert into SD_WARNRECORD_T(WarnID,ADCD,WarnTypeID,WarnGradeID,WarnStatusID,WarnSTM,WarnETM,WarnNM,WarnDesc,TYPE,SYSTM,Remark)
     values(#{warnId,jdbcType=VARCHAR},#{adcd,jdbcType=VARCHAR},#{warnTypeId,jdbcType=INTEGER},#{warnGradeId,jdbcType=INTEGER},#{warnStatusId,jdbcType=INTEGER},
          #{warnStm,jdbcType=TIMESTAMP}, #{warnEtm,jdbcType=TIMESTAMP}, #{warnNm,jdbcType=VARCHAR}, #{warnDesc,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},#{occurTm,jdbcType=TIMESTAMP},
            #{remark,jdbcType=VARCHAR})
    </insert>
    <insert id="insertWarn" parameterType="com.huitu.cloud.api.shyj.warn.entity.WarnrecordR">
    insert into SD_WARNRECORD_R (WarnID, ADCD, WarnTypeID,
          WarnGradeID, WarnStatusID, WarnSTM,
          WarnETM, WarnNM, WarnDesc,
          InnerWarnTM, InnerWarnGradeID, InnerWarnDesc,
          InnerWarnUser, OutWarnTM, OutWarnGradeID,
          OutWarnDesc, OutWarnUser, TYPE,
          IsHandMade, IsFlash, LGTD,
          LTTD, OccurUser, OccurTM,
          Remark)
        values (#{warnId,jdbcType=VARCHAR}, #{adcd,jdbcType=VARCHAR}, #{warnTypeId,jdbcType=INTEGER},
          #{warnGradeId,jdbcType=INTEGER}, #{warnStatusId,jdbcType=INTEGER},#{warnStm,jdbcType=TIMESTAMP},
          #{warnEtm,jdbcType=TIMESTAMP}, #{warnNm,jdbcType=VARCHAR}, #{warnDesc,jdbcType=VARCHAR},
          #{innerWarnTm,jdbcType=TIMESTAMP}, #{innerWarnGradeId,jdbcType=INTEGER}, #{innerWarnDesc,jdbcType=VARCHAR},
          #{innerWarnUser,jdbcType=VARCHAR}, #{outWarnTm,jdbcType=TIMESTAMP}, #{outWarnGradeId,jdbcType=INTEGER},
          #{outWarnDesc,jdbcType=VARCHAR}, #{outWarnUser,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
          #{isHandMade,jdbcType=VARCHAR}, #{isFlash,jdbcType=INTEGER}, #{lgtd,jdbcType=VARCHAR},
          #{lttd,jdbcType=VARCHAR}, #{occurUser,jdbcType=VARCHAR}, #{occurTm,jdbcType=TIMESTAMP},
          #{remark,jdbcType=VARCHAR})
    </insert>
    <insert id="insertWarnAlertConsultation" parameterType="com.huitu.cloud.api.shyj.warn.entity.WarnAlertConsultation">
           insert into WARN_ALERT_CONSULTATION  (ALERTID ,CONSULTATIONINFO,WRITER ,WRITETIME) values
           (#{alertId},#{consultationInfo}, #{writer},getdate())
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.huitu.cloud.api.shyj.warn.entity.WarnrecordR">
     update SD_WARNRECORD_R
        set ADCD = #{adcd,jdbcType=VARCHAR},
          WarnTypeID = #{warnTypeId,jdbcType=INTEGER},
          WarnGradeID = #{warnGradeId,jdbcType=INTEGER},
          WarnStatusID = #{warnStatusId,jdbcType=INTEGER},
          WarnSTM = #{warnStm,jdbcType=TIMESTAMP},
          WarnETM = #{warnEtm,jdbcType=TIMESTAMP},
          WarnNM = #{warnNm,jdbcType=VARCHAR},
          WarnDesc = #{warnDesc,jdbcType=VARCHAR},
          InnerWarnTM = #{innerWarnTm,jdbcType=TIMESTAMP},
          InnerWarnGradeID = #{innerWarnGradeId,jdbcType=INTEGER},
          InnerWarnDesc = #{innerWarnDesc,jdbcType=VARCHAR},
          InnerWarnUser = #{innerWarnUser,jdbcType=VARCHAR},
          OutWarnTM = #{outWarnTm,jdbcType=TIMESTAMP},
          OutWarnGradeID = #{outWarnGradeId,jdbcType=INTEGER},
          OutWarnDesc = #{outWarnDesc,jdbcType=VARCHAR},
          OutWarnUser = #{outWarnUser,jdbcType=VARCHAR},
          TYPE = #{type,jdbcType=VARCHAR},
          IsHandMade = #{isHandMade,jdbcType=VARCHAR},
          IsFlash = #{isFlash,jdbcType=INTEGER},
          LGTD = #{lgtd,jdbcType=VARCHAR},
          LTTD = #{lttd,jdbcType=VARCHAR},
          OccurUser = #{occurUser,jdbcType=VARCHAR},
          OccurTM = getdate(),
          Remark = #{remark,jdbcType=VARCHAR},
          Model =#{model,jdbcType=VARCHAR}
        where WarnID = #{warnId,jdbcType=VARCHAR}
    </update>
    <select id="getWarnByPage" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnRelatedInfo" useCache="false">
        select A.WARNID, A.ADCD,F.REMARK as REMARKS, A.WARNTYPEID, A.WARNGRADEID, A.WARNSTATUSID, A.WARNSTM, A.WARNETM, A.WARNNM,
        A.WARNDESC, A.INNERWARNTM,
        A.INNERWARNGRADEID, A.INNERWARNDESC, A.INNERWARNUSER, A.OUTWARNTM, A.OUTWARNGRADEID, A.OUTWARNDESC,
        A.OUTWARNUSER, A.TYPE, A.ISHANDMADE,
        A.ISFLASH, A.LGTD, A.LTTD, A.OCCURUSER, A.OCCURTM, A.REMARK, A.MODEL,
        (CASE WHEN F.HAS_MOUTAIN_TORRENTS IS NOT NULL THEN F.HAS_MOUTAIN_TORRENTS ELSE 0 END) HAS_MOUTAIN_TORRENTS,
        S.WARNSTATUSNM,WARNGRADENM,AD.ADNM,PD.ADNM PADNM,PD.ADCD PADCD,S.SHORTNM,
        (select COUNT(1) MSGCT from MESSAGESEND_R INFO,MESSAGEINFO_R B where INFO.MSGID=B.MSGID AND INFO.ADCD=B.ADCD and
        b.WarnID=A.WarnID and b.MEDIAID='10') MSGCT
        from WARNRECORD_R A
        LEFT JOIN BSN_ADCD_B AD ON AD.ADCD=A.ADCD
        LEFT JOIN BSN_ADCD_B PD ON substring(A.ADCD,1,6)+'000000000'=PD.ADCD
        LEFT JOIN WARNINGGRADE_B GB ON A.WARNGRADEID=GB.WARNGRADEID
        LEFT JOIN WARNINGSTATUS_B S ON S.WARNSTATUSID=A.WARNSTATUSID
        LEFT JOIN WARN_FEEDBACK F ON A.WARNID=F.WARNID
        where 1=1 and a.TYPE!='D'
        <if test="map.ad !=null and map.ad !=''">
            and substring(A.ADCD,1,#{map.level})=#{map.ad}
        </if>
        <if test="map.stm != null and map.stm !=''">
            and A.WARNSTM> CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and A.WARNSTM &lt;= CONVERT(datetime,#{map.etm})
        </if>
        <if test="map.warnStatusId != null and map.warnStatusId !=''">
            AND A.WARNSTATUSID=#{map.warnStatusId}
        </if>
        <if test="map.warnGradeId != null and map.warnGradeId !=''">
            AND A.WARNGRADEID=#{map.warnGradeId}
        </if>
        <if test="map.queryType != null and map.queryType=='1'.toString()">
            AND A.WARNSTATUSID !='30' and (A.WARNETM is null or A.WARNETM = '1900-01-01 00:00:00')
        </if>
        order by A.WARNSTM desc
    </select>
    <select id="getWrnGradeNmByCount" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnGradeStatistics">
        select
        S.WARNSTATUSNM as warnGradeName,
        COUNT
        ( S.WARNSTATUSNM ) as warnGradeCount
        from WARNRECORD_R A
        LEFT JOIN BSN_ADCD_B AD ON AD.ADCD=A.ADCD
        LEFT JOIN BSN_ADCD_B PD ON substring(A.ADCD,1,6)+'000000000'=PD.ADCD
        LEFT JOIN WARNINGGRADE_B GB ON A.WARNGRADEID=GB.WARNGRADEID
        LEFT JOIN WARNINGSTATUS_B S ON S.WARNSTATUSID=A.WARNSTATUSID
        LEFT JOIN WARN_FEEDBACK F ON A.WARNID=F.WARNID
        where 1=1 and a.TYPE!='D'
        <if test="map.ad !=null and map.ad !=''">
            and substring(A.ADCD,1,#{map.level})=#{map.ad}
        </if>
        <if test="map.stm != null and map.stm !=''">
            and A.WARNSTM> CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and A.WARNSTM &lt;= CONVERT(datetime,#{map.etm})
        </if>
        <if test="map.warnStatusId != null and map.warnStatusId !=''">
            AND A.WARNSTATUSID=#{map.warnStatusId}
        </if>
        <if test="map.warnGradeId != null and map.warnGradeId !=''">
            AND A.WARNGRADEID=#{map.warnGradeId}
        </if>
        <if test="map.queryType != null and map.queryType=='1'.toString()">
            AND A.WARNSTATUSID !='30' and (A.WARNETM is null or A.WARNETM = '1900-01-01 00:00:00')
        </if>
        GROUP BY
        S.WARNSTATUSNM
    </select>

    <select id="getWarnStatusyCount" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnStatus">
        select
        WARNGRADENM AS warnStatusName,
        COUNT ( WARNGRADENM ) AS warnStatusCount
        from WARNRECORD_R A
        LEFT JOIN BSN_ADCD_B AD ON AD.ADCD=A.ADCD
        LEFT JOIN BSN_ADCD_B PD ON substring(A.ADCD,1,6)+'000000000'=PD.ADCD
        LEFT JOIN WARNINGGRADE_B GB ON A.WARNGRADEID=GB.WARNGRADEID
        LEFT JOIN WARNINGSTATUS_B S ON S.WARNSTATUSID=A.WARNSTATUSID
        LEFT JOIN WARN_FEEDBACK F ON A.WARNID=F.WARNID
        where 1=1 and a.TYPE!='D'
        <if test="map.ad !=null and map.ad !=''">
            and substring(A.ADCD,1,#{map.level})=#{map.ad}
        </if>
        <if test="map.stm != null and map.stm !=''">
            and A.WARNSTM> CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and A.WARNSTM &lt;= CONVERT(datetime,#{map.etm})
        </if>
        <if test="map.warnStatusId != null and map.warnStatusId !=''">
            AND A.WARNSTATUSID=#{map.warnStatusId}
        </if>
        <if test="map.warnGradeId != null and map.warnGradeId !=''">
            AND A.WARNGRADEID=#{map.warnGradeId}
        </if>
        <if test="map.queryType != null and map.queryType=='1'.toString()">
            AND A.WARNSTATUSID !='30' and (A.WARNETM is null or A.WARNETM = '1900-01-01 00:00:00')
        </if>
        GROUP BY
        WARNGRADENM
    </select>

    <select id="getWarnById" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnRelatedInfo">
        select A.WARNID, A.ADCD, A.WARNTYPEID, A.WARNGRADEID, A.WARNSTATUSID, A.WARNSTM, A.WARNETM, A.WARNNM, A.WARNDESC, A.INNERWARNTM,
        A.INNERWARNGRADEID, A.INNERWARNDESC, A.INNERWARNUSER, A.OUTWARNTM, A.OUTWARNGRADEID, A.OUTWARNDESC, A.OUTWARNUSER, A.TYPE, A.ISHANDMADE,
        A.ISFLASH, A.LGTD, A.LTTD, A.OCCURUSER, A.OCCURTM, A.REMARK, A.MODEL,
        S.WARNSTATUSNM,WARNGRADENM,AD.ADNM,PD.ADNM PADNM,PD.ADCD PADCD,S.SHORTNM,
        (select COUNT(1) MSGCT  from MESSAGESEND_R INFO,MESSAGEINFO_R B where INFO.MSGID=B.MSGID AND  INFO.ADCD=B.ADCD and b.WarnID=A.WarnID) MSGCT
        from WARNRECORD_R A
        LEFT JOIN BSN_ADCD_B  AD ON AD.ADCD=A.ADCD
        LEFT JOIN BSN_ADCD_B  PD ON substring(A.ADCD,1,6)+'000000000'=PD.ADCD
        LEFT JOIN WARNINGGRADE_B GB ON A.WARNGRADEID=GB.WARNGRADEID
        LEFT JOIN WARNINGSTATUS_B S ON S.WARNSTATUSID=A.WARNSTATUSID
        where 1=1  AND A.WARNID=#{warnId}
    </select>
    <select id="getAdWarnGradeStatistics" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnStatistics">
        select ADCD,ADNM,LGTD,LTTD,PADCD,MAX(CASE WarnGradeID WHEN 5 THEN Total else 0 end ) TOTAL5,MAX(CASE WarnGradeID
        WHEN 4 THEN Total else 0 end ) TOTAL4,SUM(TOTAL) AllTOTAL FROM
        (select ADCD,ADNM,LGTD,LTTD,PADCD,WarnGradeID,count(*) Total from (select ad.ADCD ,ADNM
        ,WarnGradeID,AD.LGTD,AD.LTTD,ad.PADCD
        from WARNRECORD_R wr
        left join BSN_ADCD_B ad on substring(wr.ADCD,1,#{level2})+'${zero}'=ad.ADCD
        WHERE 1=1 and TYPE!='D'
        <if test="queryType != null and queryType=='1'.toString()">
            and wr.WARNSTATUSID !='30' and wr.WARNETM is null
        </if>
        <if test="ad != null and ad !=''">
            and substring(wr.ADCD,1,#{level})=#{ad}
        </if>
        <if test="level != null and level == '4'.toString()">
            and substring(wr.ADCD,1,6) not in ( '220581','220381' )
        </if>
        <if test="stm != null and stm !=''">
            and wr.WARNSTM> CONVERT(datetime,#{stm})
        </if>
        <if test="etm != null and etm !=''">
            and wr.WARNSTM &lt;= CONVERT(datetime,#{etm})
        </if>
        ) t GROUP BY ADCD,ADNM,WarnGradeID,LGTD,LTTD,PADCD )n where ADCD is not null group by ADCD,ADNM,LGTD,LTTD,PADCD
    </select>
    <select id="getAdWarnCloseStatistics" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnStatistics">
        select ADCD,ADNM ,count(*) CLOSETOTAL
        from (select ad.ADCD ,ADNM
        from WARNRECORD_R wr
        left join BSN_ADCD_B ad on substring(wr.ADCD,1,#{level2})+'${zero}'=ad.ADCD
        WHERE 1=1 and TYPE!='D'
        and (WarnStatusID='30' or WarnETM is not null)
        <if test="ad != null and ad !=''">
            and substring(wr.ADCD,1,#{level})=#{ad}
        </if>
        <if test="level != null and level == '4'.toString()">
            and substring(wr.ADCD,1,6) not in ( '220581','220381' )
        </if>
        <if test="stm != null and stm !=''">
            and wr.WARNSTM> CONVERT(datetime,#{stm})
        </if>
        <if test="etm != null and etm !=''">
            and wr.WARNSTM &lt;= CONVERT(datetime,#{etm})
        </if>
        ) t
        GROUP BY ADCD,ADNM
    </select>
    <select id="getAdWarnMessageStatistics" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnStatistics">
        select ADCD,ADNM ,count(*) TOTALMASSAGE
        from ( select b.ADCD, b.ADNM
        from WARNRECORD_R a left join BSN_ADCD_B b on substring(a.ADCD,1,#{level2})+'${zero}'=b.ADCD
        join MESSAGEINFO_R c on a.WarnID =c.WarnID join MESSAGESEND_R d on c.MSGID=d.MSGID and c.adcd=d.adcd
        where substring(a.ADCD,1,#{level})=#{ad}
        <if test="level != null and level == '4'.toString()">
            and substring(a.ADCD,1,6) not in ( '220581','220381' )
        </if>
        <if test="stm != null and stm !=''">
            and A.WARNSTM> CONVERT(datetime,#{stm})
        </if>
        <if test="etm != null and etm !=''">
            and A.WARNSTM &lt;= CONVERT(datetime,#{etm})
        </if>
        <if test="queryType != null and queryType=='1'.toString()">
            and a.WARNSTATUSID !='30' and a.WARNETM is null
        </if>
        ) T
        GROUP BY ADCD,ADNM
    </select>
    <select id="selectByPrimaryKey" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnrecordR">
        SELECT WARNID, ADCD, WARNTYPEID, WARNGRADEID, WARNSTATUSID, WARNSTM, WARNETM, WARNNM, WARNDESC, INNERWARNTM, INNERWARNGRADEID, INNERWARNDESC, INNERWARNUSER, OUTWARNTM, OUTWARNGRADEID, OUTWARNDESC, OUTWARNUSER, TYPE,
          ISHANDMADE, ISFLASH, LGTD, LTTD, OCCURUSER, OCCURTM, REMARK, MODEL
          from SD_WARNRECORD_R
          where WarnID = #{warnId,jdbcType=VARCHAR}
    </select>
    <select id="getWarnDanadStatistics" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnDanadStatistics">
        select ADCD,ADNM,SUM(PTCOUNT) PTCOUNT,SUM(ETCOUNT) ETCOUNT,SUM(HTCOUNT) HTCOUNT,COUNT(1) DANCOUNT FROM (
        select AD.ADCD,ADNM,PTCOUNT,ETCOUNT,HTCOUNT  from IA_C_DANAD A LEFT JOIN BSN_ADCD_B AD ON AD.ADCD=#{adcd}
        WHERE substring(a.ADCD,1,#{level})=#{ad}
        <if test="level != null and level == '4'.toString()">
            and substring(a.ADCD,1,6) not in ( '220581','220381' )
        </if>
        )M GROUP BY ADCD,ADNM
    </select>
    <select id="getWarnPrevcntStatistics"
            resultType="com.huitu.cloud.api.shyj.warn.entity.WarnPrevcntStatistics">
        select ADCD,ADNM,SUM(PTCOUNT) PTCOUNT,SUM(ETCOUNT) ETCOUNT,SUM(HTCOUNT) HTCOUNT,SUM(LDAREA) LDAREA,SUM(PLAREA) PLAREA,SUM(PREVCNT) PREVCNT,SUM(IMPPEVCNT) IMPPEVCNT,(SUM(PREVCNT)-SUM(IMPPEVCNT)) COMPEVCNT from(
        select  AD.ADCD,ADNM,PTCOUNT,ETCOUNT,HTCOUNT,LDAREA,PLAREA,PREVCNT,IMPPEVCNT,ISADCD from DT_PREVCNT_S A
        LEFT JOIN BSN_ADCD_B AD ON AD.ADCD=#{adcd}
        WHERE   substring(a.CODE,1,#{level})=#{ad} and ISADCD='1'
        <if test="level != null and level == '4'.toString()">
            and substring(a.CODE,1,6) not in ( '220581','220381' )
        </if>
        )m GROUP BY ADCD,ADNM
    </select>
    <select id="getWarnAdStatistics" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnAdStatistics">
        SELECT ADCD ,ADNM ,SUM(PTCOUNT) PTCOUNT,SUM(HTCOUNT) HTCOUNT,SUM(LDAREA) LDAREA,SUM(XZHCNT) XZHCNT,SUM(XZHCCNT) XZHCCNT,SUM(ZRCCNT) ZRCCNT,SUM(WSCNT) WSCNT,SUM(HSFWATERCNT) HSFWATERCNT FROM (
        select AD.ADCD,ADNM,PTCOUNT,HTCOUNT,LDAREA,ISADCD,XZHCNT,XZHCCNT,ZRCCNT,WSCNT,HSFWATERCNT from DT_ADSURVEYCNT_S A
        LEFT JOIN BSN_ADCD_B AD ON AD.ADCD=#{adcd}
        WHERE substring(a.CODE,1,#{level})=#{ad}
        <if test="level != null and level == '4'.toString()">
            and substring(a.CODE,1,6) not in ( '220581','220381' )
        </if>
        )M GROUP BY ADCD,ADNM
    </select>
    <select id="getWarnWadingStatistics"
            resultType="com.huitu.cloud.api.shyj.warn.entity.WarnWadingStatistics">
        SELECT ADCD,ADNM,SUM(RSST) RSST,SUM(SLUST) SLUST,SUM(DIKST) DIKST,SUM(DAMST) DAMST,SUM(CULST) CULST,SUM(BRIST) BRIST FROM
        ( SELECT  AD.ADCD,AD.ADNM,RSST,SLUST,DIKST,DAMST,CULST,BRIST,ISADCD
        FROM DT_WADING_S a LEFT JOIN BSN_ADCD_B AD ON AD.ADCD=#{adcd}
        WHERE  substring(a.ID,1,#{level})=#{ad} and ISADCD=1
        <if test="level != null and level == '4'.toString()">
            and substring(a.ID,1,6) not in ( '220581','220381' )
        </if>
        ) M group BY ADCD,ADNM
    </select>
    <select id="getWarnJczbxlxStatistics"
            resultType="com.huitu.cloud.api.shyj.warn.entity.WarnJczbxlxStatistics">
        select ADCD,ADNM,SUM(PPCOUNT) PPCOUNT ,SUM(ZZCOUNT) ZZCOUNT,SUM(RRCOUNT) RRCOUNT,SUM(SRST) SRST ,SUM(SWST) SWST ,SUM(PIC) PIC ,SUM(VIDEO) VIDEO ,SUM(WBRST) WBRST
        FROM (
        select AD.ADCD,AD.ADNM,PPCOUNT ,ZZCOUNT,RRCOUNT,SRST ,SWST ,PIC ,VIDEO ,WBRST
        from DT_JCZBXLX_S A   LEFT JOIN BSN_ADCD_B AD ON AD.ADCD=#{adcd}
        WHERE  substring(a.ADCD,1,#{level})=#{ad}
        <if test="level != null and level == '4'.toString()">
            and substring(a.ADCD,1,6) not in ( '220581','220381' )
        </if>
        )m GROUP BY ADCD,ADNM
    </select>
    <select id="getStAdWarnById" resultType="com.huitu.cloud.api.shyj.warn.entity.StwarnRelatedInfo">
        SELECT STWarnID,STAD.STCD,WarnTypeID,WarnGradeID,STWarnNM,STWarnDESC,STWarnSTM,STWarnETM, Remark,STB.STNM,AD.ADNM,STB.LGTD,STB.LTTD, STB.STTP, AD.LGTD AD_LGTD,AD.LTTD AD_LTTD FROM (
        select a.STWarnID,a.STCD ,a.WarnTypeID,a.WarnGradeID,a.STWarnNM,a.STWarnDESC ,a.STWarnSTM,a.STWarnETM ,a.Remark from (SELECT
         a.*,row_number() over (partition by a.STCD order by a.STWarnSTM desc) rn  FROM STWARNRECORD_R a,
         WARN_STCD_R b
         where a.STWarnID=b.STWarnID AND  b.WarnID = #{warnId}) a where a.rn=1)AA
         LEFT JOIN AD_ST_AD_B STAD ON STAD.ADCD = AA.STCD
         LEFT JOIN ST_STBPRP_B STB ON STB.STCD=STAD.STCD
         LEFT JOIN BSN_ADCD_B AD  ON AD.ADCD =AA.STCD
    </select>
    <select id="getWarnStatusList" resultType="com.huitu.cloud.api.shyj.warn.entity.WarningstatusB">
        select WARNSTATUSID,WARNSTATUSNM,BYNAME,SHORTNM
        from WARNINGSTATUS_B
        where 1=1
        <if test="isClose!=null and isClose==false">
            and WarnStatusID !='30'
        </if>
        order by WARNSTATUSID
    </select>
    <select id="getWarnGradeList" resultType="com.huitu.cloud.api.shyj.warn.entity.WarninggradeB">
        select WARNGRADEID,WARNGRADENM,WARNGRADEORDERID,WARNGRADETYPE,COUNTYNUM
        from WARNINGGRADE_B
        where 1=1
    </select>
    <select id="getWarnTypeList" resultType="com.huitu.cloud.api.shyj.warn.entity.WarningtypeB">
        select WARNTYPEID,WARNTYPENM
        from  WARNINGTYPE_B
        where 1=1
    </select>
    <select id="getWarnRecordById" resultType="com.huitu.cloud.api.shyj.warn.entity.RecordInfo">
         select  a.WarnID,a.WarnGradeID,c.WarnGradeNM,   a.WarnStatusID ,a.Warndesc,d.WarnStatusNM,  a.SYSTM ,a.ADCD,a.remark,MEDIAID,SENDTM,MSGCT0,MSGCT1,MSGCT2,MSGCT from SD_WARNRECORD_T a
                left join
                 (SELECT WarnID,MsgTypeID,MEDIAID,SENDTM,isnull(MSGCT0,0) MSGCT0,isnull(MSGCT1,0) MSGCT1,isnull(MSGCT2,0) MSGCT2, isnull(MSGCT0,0)+isnull(MSGCT1,0)+isnull(MSGCT2,0) MSGCT FROM
                       ( SELECT WarnID,MsgTypeID,MEDIAID,SENDTM,MAX(CASE WHEN SENDRESULT='0' THEN MSGCT END) MSGCT0 ,
                       MAX(CASE WHEN SENDRESULT='1' THEN MSGCT END) MSGCT1,MAX(CASE WHEN SENDRESULT='2' THEN MSGCT END) MSGCT2 FROM
                         (select WarnID,A.SENDRESULT,MsgTypeID,MEDIAID,SENDTM,COUNT(A.SID) MSGCT  from MESSAGESEND_R A
                           LEFT JOIN MESSAGEINFO_R B ON A.MSGID=B.MSGID AND  A.ADCD=B.ADCD
                           GROUP BY  SENDRESULT,WarnID,MsgTypeID,MEDIAID,SENDTM) T
                            GROUP BY WarnID,MsgTypeID,MEDIAID,SENDTM) M) msg on a.WarnID=msg.WarnID and a.SYSTM=msg.SENDTM
                  left join WARNINGGRADE_B  c on a.WarnGradeID=c.WarnGradeID
                  left join WARNINGSTATUS_B d on a.WarnStatusID=d.WarnStatusID
                    where  a.WarnID=#{warnId}
                    order by a.SYSTM
    </select>
    <select id="getWarnStatusRecordById" resultType="com.huitu.cloud.api.shyj.warn.entity.RecordInfo">
        select a.WarnID,a.WarnGradeID, a.WarnStatusID ,a.WarnContent Warndesc,d.WarnStatusNM, a.TIME,a.remark, NULL MEDIAID,NULL MSGCT0,NULL MSGCT1,NULL MSGCT2,NULL MSGCT from (
                  SELECT * FROM (select row_number() over (partition by a.WarnStatusID order by a.TIME desc) RN,a.* from  WARN_STATUSRECORD_R   a
               where warnid=#{warnId} )AA WHERE RN=1) a
                   left join WARNINGSTATUS_B d on d.WarnStatusID=a.WarnStatusID
                  inner join WARNRECORD_R B ON B.WarnID=a.WarnID
                  WHERE A.WarnStatusID IN('0','30')
		  union
         select msg.WarnID ,B.WarnGradeID,d.WarnStatusID,NULL Warndesc,d.WarnStatusNM, NULL TIME,null remark,MEDIAID,MSGCT0,MSGCT1,MSGCT2,MSGCT from
         (SELECT WarnID,MsgTypeID,MEDIAID,isnull(MSGCT0,0) MSGCT0,isnull(MSGCT1,0) MSGCT1,isnull(MSGCT2,0) MSGCT2, isnull(MSGCT0,0)+isnull(MSGCT1,0)+isnull(MSGCT2,0) MSGCT FROM
               ( SELECT WarnID,MsgTypeID,MEDIAID,MAX(CASE WHEN SENDRESULT='0' THEN MSGCT END) MSGCT0 ,
               MAX(CASE WHEN SENDRESULT='1' THEN MSGCT END) MSGCT1,MAX(CASE WHEN SENDRESULT='2' THEN MSGCT END) MSGCT2 FROM
                 (select WarnID,A.SENDRESULT,MsgTypeID,MEDIAID,COUNT(A.SID) MSGCT  from MESSAGESEND_R A
                   LEFT JOIN MESSAGEINFO_R B ON A.MSGID=B.MSGID
                   GROUP BY  SENDRESULT,WarnID,MsgTypeID,MEDIAID) T
                    GROUP BY WarnID,MsgTypeID,MEDIAID) M) msg
          inner join WARNRECORD_R B ON B.WarnID=msg.WarnID
          left join WARNINGSTATUS_B d on d.WarnStatusID=msg.MsgTypeID
           where  msg.WarnID=#{warnId}
    </select>
    <select id="getWarnFeedbackById" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnFeedback">
        select WARNID,HAS_MOUTAIN_TORRENTS,REMARK,IMGS,VCODE
        from WARN_FEEDBACK A
        where 1=1  AND A.WARNID=#{warnId}
    </select>
    <insert id="addWarnFeedback" parameterType="com.huitu.cloud.api.shyj.warn.entity.WarnFeedback">
        INSERT INTO WARN_FEEDBACK(WARNID,HAS_MOUTAIN_TORRENTS,REMARK,IMGS,OCCURUSER,OCCURTM,VCODE)
        values(#{warnId,jdbcType=VARCHAR},#{hasMouTainTorrents,jdbcType=CHAR},#{remark,jdbcType=VARCHAR}
              ,#{imgs,jdbcType=LONGVARCHAR},#{occurUser,jdbcType=VARCHAR},getdate(),#{vcode,jdbcType=VARCHAR})
    </insert>
    <update id="updateWarnFeedback" parameterType="com.huitu.cloud.api.shyj.warn.entity.WarnFeedback">
        UPDATE WARN_FEEDBACK SET
         HAS_MOUTAIN_TORRENTS = #{hasMouTainTorrents,jdbcType=CHAR},
         REMARK = #{remark,jdbcType=VARCHAR},
         IMGS = #{imgs,jdbcType=LONGVARCHAR},
         OCCURUSER = #{occurUser,jdbcType=VARCHAR},
         OCCURTM = getdate(),
         VCODE = #{vcode,jdbcType=VARCHAR}
        WHERE WARNID=#{warnId} AND VCODE=#{vcode}
    </update>
    <select id="getWarnDisAsterSituationById" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnDisasterSituationReport">
        select WARNID,PEOPLE_LOSS,PROPERTY_LOSS,FARMLAND_LOSS,IMGS,VCODE
        from WARN_DISASTERSITUATION_REPORT A
        where 1=1  AND A.WARNID=#{warnId}
    </select>
    <insert id="addWarnDisAsterSituation" parameterType="com.huitu.cloud.api.shyj.warn.entity.WarnDisasterSituationReport">
        INSERT INTO WARN_DISASTERSITUATION_REPORT(WARNID,PEOPLE_LOSS,PROPERTY_LOSS,FARMLAND_LOSS,IMGS,OCCURUSER,OCCURTM,VCODE)
        values(#{warnId,jdbcType=VARCHAR},#{peopleLoss,jdbcType=DECIMAL},#{ propertyLoss,jdbcType = DECIMAL },#{farmlandLoss,jdbcType=DECIMAL}
              ,#{imgs,jdbcType=LONGVARCHAR},#{occurUser,jdbcType=VARCHAR},getdate(),#{vcode,jdbcType=VARCHAR})
    </insert>
    <update id="updateWarnDisAsterSituation" parameterType="com.huitu.cloud.api.shyj.warn.entity.WarnDisasterSituationReport">
        UPDATE WARN_DISASTERSITUATION_REPORT SET
         PEOPLE_LOSS = #{peopleLoss,jdbcType=DECIMAL},
         PROPERTY_LOSS = #{propertyLoss,jdbcType=DECIMAL},
         FARMLAND_LOSS = #{farmlandLoss,jdbcType=DECIMAL},
         IMGS = #{imgs,jdbcType=LONGVARCHAR},
         OCCURUSER = #{occurUser,jdbcType=VARCHAR},
         OCCURTM = getdate(),
         VCODE = #{vcode,jdbcType=VARCHAR}
        WHERE WARNID=#{warnId} AND VCODE=#{vcode}
    </update>
    <select id="getWarnNm" resultType="java.lang.String">
        SELECT B.ADNM + CAST(YEAR (GETDATE()) AS VARCHAR (4)) + '年' + CAST(A.CNT + 1 AS VARCHAR(4)) + '号预警'
        from (SELECT LEFT (ADCD, 6) XADCD, COUNT (ADCD) CNT
              FROM SD_WARNRECORD_R
              WHERE YEAR (WARNSTM) = YEAR (GETDATE())
              GROUP BY LEFT (ADCD, 6)) A
                 LEFT JOIN MDT_ADCDINFO_B B ON A.XADCD + '000000000' = B.ADCD
    </select>
    <select id="getShPersonList" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnShPersonVo">
        WITH DEPT AS (SELECT DEPTID, DEPTNM, PDEPTID
                      FROM BNS_DEPTINFO_B
                      WHERE DEPTID = '332d8816-11f4-49c0-8cda-1e9ed9c5abe5' -- 地方水旱组织ID
                      UNION ALL
                      SELECT T.DEPTID, T.DEPTNM, T.PDEPTID
                      FROM BNS_DEPTINFO_B T
                               INNER JOIN DEPT D ON T.PDEPTID = D.DEPTID)
        SELECT UNAME + SID userid,
               SID         mobile,
               UNAME       realnm
        FROM (SELECT DISTINCT UI.REALNM UNAME, UI.MOBILE[SID]
              FROM BNS_USERINFO_B UI
                       LEFT JOIN BNS_USERDEPT_B UD ON UD.USERID = UI.USERID
                       LEFT JOIN BNS_DEPTINFO_B DI ON DI.DEPTID = UD.DEPTID
              WHERE UI.MOBILE IS NOT NULL
                AND UI.MOBILE != '' AND DI.DEPTID IN (SELECT DEPTID FROM DEPT) AND DI.ADCD = #{adcd}) T
    </select>

</mapper>
