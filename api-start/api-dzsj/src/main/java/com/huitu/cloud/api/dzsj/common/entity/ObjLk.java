package com.huitu.cloud.api.dzsj.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * <p>
 * 湖泊名录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-10
 */
@TableName("OBJ_LK")
@ApiModel(value="ObjLk对象", description="湖泊名录表")
public class ObjLk extends Model<ObjLk> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "湖泊代码")
    @TableId(value = "LK_CODE", type = IdType.NONE)
    private String lkCode;

    @ApiModelProperty(value = "湖泊名称")
    @TableField("LK_NAME")
    private String lkName;

    @ApiModelProperty(value = "对象建立时间")
    @TableField("FROM_DATE")
    private LocalDateTime fromDate;

    @ApiModelProperty(value = "对象终止时间")
    @TableField("TO_DATE")
    private LocalDateTime toDate;

    public String getLkCode() {
        return lkCode;
    }

    public void setLkCode(String lkCode) {
        this.lkCode = lkCode;
    }

    public String getLkName() {
        return lkName;
    }

    public void setLkName(String lkName) {
        this.lkName = lkName;
    }

    public LocalDateTime getFromDate() {
        return fromDate;
    }

    public void setFromDate(LocalDateTime fromDate) {
        this.fromDate = fromDate;
    }

    public LocalDateTime getToDate() {
        return toDate;
    }

    public void setToDate(LocalDateTime toDate) {
        this.toDate = toDate;
    }

    @Override
    public String toString() {
        return "ObjLk{" +
                "lkCode='" + lkCode + '\'' +
                ", lkName='" + lkName + '\'' +
                ", fromDate=" + fromDate +
                ", toDate=" + toDate +
                '}';
    }
}
