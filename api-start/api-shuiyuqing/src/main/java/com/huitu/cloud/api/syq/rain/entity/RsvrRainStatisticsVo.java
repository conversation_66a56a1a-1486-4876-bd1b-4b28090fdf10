package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class RsvrRainStatisticsVo implements Serializable {
    @ApiModelProperty(value = "水库编码")
    private String resCode;
    @ApiModelProperty(value = "水库名称")
    private String resName;
    @ApiModelProperty(value = "平均降水量(mm)")
    private String avgDrp;
    @ApiModelProperty(value = "最大点雨量（mm）")
    private String maxDrp;
    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;

    @ApiModelProperty(value = "左下角经度")
    @TableField("LOW_LEFT_LONG")
    private Double lowLeftLong;

    @ApiModelProperty(value = "左下角纬度")
    @TableField("LOW_LEFT_LAT")
    private Double lowLeftLat;

    @ApiModelProperty(value = "右上角经度")
    @TableField("UP_RIGHT_LONG")
    private Double upRightLong;

    @ApiModelProperty(value = "右上角纬度")
    @TableField("UP_RIGHT_LAT")
    private Double upRightLat;
    @ApiModelProperty(value = "是否所有测站的最大降雨站")
    private boolean maxFlag;

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getAvgDrp() {
        return avgDrp;
    }

    public void setAvgDrp(String avgDrp) {
        this.avgDrp = avgDrp;
    }

    public String getMaxDrp() {
        return maxDrp;
    }

    public void setMaxDrp(String maxDrp) {
        this.maxDrp = maxDrp;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public Double getLowLeftLong() {
        return lowLeftLong;
    }

    public void setLowLeftLong(Double lowLeftLong) {
        this.lowLeftLong = lowLeftLong;
    }

    public Double getLowLeftLat() {
        return lowLeftLat;
    }

    public void setLowLeftLat(Double lowLeftLat) {
        this.lowLeftLat = lowLeftLat;
    }

    public Double getUpRightLong() {
        return upRightLong;
    }

    public void setUpRightLong(Double upRightLong) {
        this.upRightLong = upRightLong;
    }

    public Double getUpRightLat() {
        return upRightLat;
    }

    public void setUpRightLat(Double upRightLat) {
        this.upRightLat = upRightLat;
    }

    public boolean isMaxFlag() {
        return maxFlag;
    }

    public void setMaxFlag(boolean maxFlag) {
        this.maxFlag = maxFlag;
    }
}
