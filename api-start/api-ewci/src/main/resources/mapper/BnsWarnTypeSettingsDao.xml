<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.BnsWarnTypeSettingsDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="insert" parameterType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnAccessSettings">
        INSERT INTO BNS_WARN_TYPE_SETTINGS (ID, TYPE_NAME, DESCRIPTION, SEND_MODE, FOLLOW_TEMPLATE, ALERT_TEMPLATE,
                                            DANGER_TEMPLATE, MOST_DANGER_TEMPLATE)
        VALUES (#{id}, #{typeName}, #{description}, #{sendMode}, #{followTemplate}, #{alertTemplate}, #{dangerTemplate},
                #{mostDangerTemplate})
    </insert>
    <update id="update">
        UPDATE BNS_WARN_TYPE_SETTINGS
        <set>
            <if test="typeName != null and typeName !=''">
                TYPE_NAME = #{typeName},
            </if>
            <if test="description != null and description !=''">
                DESCRIPTION = #{description},
            </if>
            <if test="sendMode != null and sendMode !=''">
                SEND_MODE = #{sendMode},
            </if>
            <if test="followTemplate != null and followTemplate !=''">
                FOLLOW_TEMPLATE = #{followTemplate},
            </if>
            <if test="alertTemplate != null and alertTemplate !=''">
                ALERT_TEMPLATE = #{alertTemplate},
            </if>
            <if test="dangerTemplate != null and dangerTemplate !=''">
                DANGER_TEMPLATE = #{dangerTemplate},
            </if>
            <if test="mostDangerTemplate != null and mostDangerTemplate !=''">
                MOST_DANGER_TEMPLATE = #{mostDangerTemplate},
            </if>
        </set>
        WHERE ID = #{id}
    </update>
    <delete id="delete">
        delete
        from BNS_WARN_TYPE_SETTINGS
        where ID = #{id}
    </delete>
    <select id="getList" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnTypeSettings">
        SELECT ID,
               TYPE_NAME,
               DESCRIPTION,
               SEND_MODE,
               FOLLOW_TEMPLATE,
               ALERT_TEMPLATE,
               DANGER_TEMPLATE,
               MOST_DANGER_TEMPLATE,
               TS
        FROM BNS_WARN_TYPE_SETTINGS
        ORDER BY ID ASC
    </select>
    <select id="getMaxId" resultType="java.lang.Long">
        SELECT (ISNULL(MAX(ID), 1000) + 1)
        FROM BNS_WARN_TYPE_SETTINGS
    </select>
    <select id="getSendModeById" resultType="java.lang.String">
        SELECT SEND_MODE
        FROM BNS_WARN_TYPE_SETTINGS
        WHERE ID = #{id}
    </select>
    <select id="getTemplateById" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnTypeSettings">
        SELECT FOLLOW_TEMPLATE,
               ALERT_TEMPLATE,
               DANGER_TEMPLATE,
               MOST_DANGER_TEMPLATE
        FROM BNS_WARN_TYPE_SETTINGS
        WHERE ID = #{id}
    </select>

</mapper>