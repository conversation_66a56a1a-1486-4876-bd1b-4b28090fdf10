package com.huitu.cloud.api.hsfxt.riverrisk.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmap;
import com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmapVo;
import com.huitu.cloud.api.hsfxt.riverrisk.mapper.FxtRiverRiskDao;
import com.huitu.cloud.api.hsfxt.riverrisk.service.FxtRiverRiskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Service
public class FxtRiverRiskServiceImpl extends ServiceImpl<FxtRiverRiskDao, RvRiskmap> implements FxtRiverRiskService {
    @Autowired
    FxtRiverRiskDao fxtRsvrDao;

    @Override
    public IPage<RvRiskmapVo> getRiverRiskList(String rvcode, String name, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("rvcode", rvcode);
        param.put("name", name);
        IPage<RvRiskmapVo>  result = fxtRsvrDao.getRiverRiskList(page, param);
        return result;
    }
}
