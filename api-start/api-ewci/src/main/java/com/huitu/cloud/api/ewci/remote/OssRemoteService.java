package com.huitu.cloud.api.ewci.remote;

import com.huitu.cloud.api.SuccessResponse;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@FeignClient(name = "dev-oss")
public interface OssRemoteService {

    @GetMapping(value = "/api/oss/minio/objects/file", consumes = MediaType.APPLICATION_PROBLEM_JSON_VALUE)
    Response getFile(@RequestParam("filepath") String filepath, @RequestParam("filename") String filename);

    @PostMapping("/api/oss/wopi/files/{id}/contents")
    void putFile(@PathVariable(name = "id") String id, @RequestBody byte[] content);

    @PostMapping("/api/oss/wopi/files/{id}/contents/convert")
    void putFileToConvert(@PathVariable(name = "id") String id, @RequestBody byte[] content);

    @GetMapping(value = "/api/oss/minio/objects/copyfile")
    ResponseEntity<SuccessResponse<Map<String, String>>> copyFile(@RequestParam("filepath") String filepath, @RequestParam("filename") String filename);

    @GetMapping(value = "/api/oss/minio/objects/deleteFile")
    ResponseEntity<SuccessResponse<String>> deleteFile(@RequestParam("filepath") String filepath);
}
