package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value="预警状态统计信息对象", description="预警状态统计信息对象")
public class WarnStatus implements Serializable {

    @ApiModelProperty(value = " 预警状态名称")
    private String warnStatusName;

    @ApiModelProperty(value = " 预警状态数量")
    private int warnStatusCount;

    public String getWarnStatusName() {
        return warnStatusName;
    }

    public void setWarnStatusName(String warnStatusName) {
        this.warnStatusName = warnStatusName;
    }

    public int getWarnStatusCount() {
        return warnStatusCount;
    }

    public void setWarnStatusCount(int warnStatusCount) {
        this.warnStatusCount = warnStatusCount;
    }
}
