package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

public class ZrStInfo extends StInfo {
    @ApiModelProperty(value = "水位上限")
    @TableField("UPV")
    private Double upv;

    @ApiModelProperty(value = "水位下限")
    @TableField("DWV")
    private Double dwv;

    @ApiModelProperty(value = "变幅")
    @TableField("CHV")
    private Double chv;

    public Double getUpv() {
        return upv;
    }

    public void setUpv(Double upv) {
        this.upv = upv;
    }

    public Double getDwv() {
        return dwv;
    }

    public void setDwv(Double dwv) {
        this.dwv = dwv;
    }

    public Double getChv() {
        return chv;
    }

    public void setChv(Double chv) {
        this.chv = chv;
    }
}
