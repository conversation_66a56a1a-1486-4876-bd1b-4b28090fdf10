package com.huitu.cloud.api.ewci.person.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 水库淹没范围责任人汇总统计查询对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "水库淹没范围责任人汇总统计查询对象")
public class VillageSummaryQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码", required = true)
    @NotBlank(message = "参数[行政区划代码]不能为空")
    @Size(min = 15, max = 15, message = "参数[行政区划代码]的长度应为15个字符")
    private String adcd;

    @ApiModelProperty(value = "是否包含本级")
    private boolean include;

    @ApiModelProperty(value = "政区级别", hidden = true)
    @JsonIgnore
    private int level;

    @ApiModelProperty(value = "下级政区级别", hidden = true)
    @JsonIgnore
    private int lowLevel;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;

        this.level = AdcdUtil.getAdLevel(adcd);
        if (this.level >= 6) {
            this.lowLevel = this.level + 3;
        } else {
            this.lowLevel = this.level + 2;
        }
    }

    public boolean isInclude() {
        return include;
    }

    public void setInclude(boolean include) {
        this.include = include;
    }

    public int getLevel() {
        return level;
    }

    public int getLowLevel() {
        return lowLevel;
    }
}
