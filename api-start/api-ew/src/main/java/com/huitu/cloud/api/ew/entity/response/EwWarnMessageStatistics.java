package com.huitu.cloud.api.ew.entity.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-22
 */
@ApiModel(value = "预警次数和短信条数统计", description = "预警次数和短信条数统计")
public class EwWarnMessageStatistics implements Serializable {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "上级政区编码")
    private String padcd;

    @ApiModelProperty(value = "预警总数")
    private int allTotal;

    @ApiModelProperty(value = "预警短信条数")
    private int totalMassage;
    @ApiModelProperty(value = "预警统计下级对象")
    private List<EwWarnMessageStatistics> children;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public int getAllTotal() {
        return allTotal;
    }

    public void setAllTotal(int allTotal) {
        this.allTotal = allTotal;
    }

    public int getTotalMassage() {
        return totalMassage;
    }

    public void setTotalMassage(int totalMassage) {
        this.totalMassage = totalMassage;
    }

    public List<EwWarnMessageStatistics> getChildren() {
        return children;
    }

    public void setChildren(List<EwWarnMessageStatistics> children) {
        this.children = children;
    }
}
