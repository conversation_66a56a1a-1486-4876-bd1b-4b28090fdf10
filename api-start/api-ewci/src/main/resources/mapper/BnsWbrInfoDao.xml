<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.sst.mapper.BnsWbrInfoDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.sst.entity.BnsWbrInfo" useCache="false">
        SELECT ROW_NUMBER() OVER(ORDER BY A.ADCD, A.WBRCD ASC) SORTNO,
        A.WBRCD, A.ADDRESS, A.WSCD, B.WSNM, A.ADCD, C.ADNM, A.TYPE, A.BDATE, A.LGTD, A.LTTD,COALESCE(D.PICTURE,0) AS
        PICTURES
        FROM BNS_IA_C_WBRINFO A
        LEFT JOIN IA_C_WATA B ON B.WSCD = A.WSCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
        LEFT JOIN (SELECT COUNT(A.WBRCD) PICTURE, A.WBRCD FROM BNS_IA_C_WBRINFO A LEFT JOIN BSN_STFILE_B B ON A.WBRCD =
        B.KEYID WHERE B.TYPE = '无线广播' GROUP BY WBRCD) D ON A.WBRCD = D.WBRCD
        WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.sdt != null">
            AND A.BDATE >= #{map.sdt}
        </if>
        <if test="map.edt != null">
            AND A.BDATE &lt;= #{map.edt}
        </if>
        <if test="map.address != null and map.address != ''">
            AND CHARINDEX(#{map.address}, A.ADDRESS) > 0
        </if>
        ORDER BY SORTNO ASC
    </select>

    <select id="exportPageList" resultType="com.huitu.cloud.api.ewci.sst.entity.BnsWbrInfo" useCache="false">
        SELECT ROW_NUMBER() OVER(ORDER BY A.ADCD, A.WBRCD ASC) SORTNO,
        A.WBRCD, A.ADDRESS, A.WSCD, B.WSNM, A.ADCD, C.ADNM, CASE
        A.TYPE
        WHEN '1' THEN
        'I型'
        WHEN '2' THEN
        'Ⅱ型'
        END AS TYPE, A.BDATE, A.LGTD, A.LTTD,COALESCE(D.PICTURE,0) AS PICTURES
        FROM BNS_IA_C_WBRINFO A
        LEFT JOIN IA_C_WATA B ON B.WSCD = A.WSCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
        LEFT JOIN (SELECT COUNT(A.WBRCD) PICTURE, A.WBRCD FROM BNS_IA_C_WBRINFO A LEFT JOIN BSN_STFILE_B B ON A.WBRCD =
        B.KEYID WHERE B.TYPE = '无线广播' GROUP BY WBRCD) D ON A.WBRCD = D.WBRCD
        WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.sdt != null">
            AND A.BDATE >= #{map.sdt}
        </if>
        <if test="map.edt != null">
            AND A.BDATE &lt;= #{map.edt}
        </if>
        <if test="map.address != null and map.address != ''">
            AND CHARINDEX(#{map.address}, A.ADDRESS) > 0
        </if>
        ORDER BY SORTNO ASC
    </select>

    <update id="update">
        UPDATE BNS_IA_C_WBRINFO
        SET ADDRESS = #{info.address},
            WSCD    = #{info.wscd},
            ADCD    = #{info.adcd},
            TYPE    = #{info.type},
            BDATE   = #{info.bdate},
            LGTD    = #{info.lgtd},
            LTTD    = #{info.lttd}
        WHERE WBRCD = #{info.wbrcd}
    </update>
    <delete id="delete">
        DELETE
        FROM BNS_IA_C_WBRINFO
        WHERE WBRCD = #{wbrcd}
    </delete>

    <select id="getNumber" resultType="java.lang.Integer">
        SELECT COUNT(*) as sum
        FROM
            BNS_IA_C_WBRINFO
        WHERE
            WBRCD = #{wbrcd}
    </select>

    <insert id="add">
        INSERT INTO BNS_IA_C_WBRINFO (WBRCD,
                                      ADDRESS,
                                      WSCD,
                                      ADCD,
                                      TYPE,
                                      BDATE,
                                      LGTD,
                                      LTTD,
                                      MODITIME)
        VALUES (#{info.wbrcd},
                #{info.address},
                #{info.wscd},
                #{info.adcd},
                #{info.type},
                #{info.bdate},
                #{info.lgtd},
                #{info.lttd},
                GETDATE())
    </insert>
</mapper>
