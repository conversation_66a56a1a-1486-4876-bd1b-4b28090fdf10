package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@ApiModel(value="EwVillage对象", description="村庄预警结果表")
public class EwVillageVo extends EwVillage {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "市级政区名称")
    private String adnm2;

    @ApiModelProperty(value = "县级政区名称")
    private String adnm3;

    @ApiModelProperty(value = "乡镇级政区名称")
    private String adnm4;

    @ApiModelProperty(value = "行政村政区名称")
    private String adnm5;

    @ApiModelProperty(value = "自然村政区名称")
    private String adnm6;

    public String getAdnm2() {
        return adnm2;
    }

    public void setAdnm2(String adnm2) {
        this.adnm2 = adnm2;
    }

    public String getAdnm3() {
        return adnm3;
    }

    public void setAdnm3(String adnm3) {
        this.adnm3 = adnm3;
    }

    public String getAdnm4() {
        return adnm4;
    }

    public void setAdnm4(String adnm4) {
        this.adnm4 = adnm4;
    }

    public String getAdnm5() {
        return adnm5;
    }

    public void setAdnm5(String adnm5) {
        this.adnm5 = adnm5;
    }

    public String getAdnm6() {
        return adnm6;
    }

    public void setAdnm6(String adnm6) {
        this.adnm6 = adnm6;
    }
}
