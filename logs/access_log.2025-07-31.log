0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:46:22 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:46:22 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:03 +0800] "GET /login?code=xhje4S&state=AtHB37 HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:04 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:05 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:06 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:06 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:07 +0800] "GET /v2/api-docs HTTP/1.1" 200 2707259
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:15 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:15 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:15 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:15 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:15 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:17 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:18 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 1594
0:0:0:0:0:0:0:1 - - [31/Jul/2025:08:47:19 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1753922984485 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+08:49:44&etm=&_timer304=1753922984486 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753922984486 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+09:00&filterCnt=6&_timer304=1753922984486 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753922984486 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753922984486 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:40 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+08:49:44&etm=&_timer304=1753922984486 HTTP/1.1" 200 156
************* - - [31/Jul/2025:08:49:40 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [31/Jul/2025:08:49:40 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+09:00&filterCnt=6&_timer304=1753922984486 HTTP/1.1" 200 164
************* - - [31/Jul/2025:08:49:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753922984486 HTTP/1.1" 200 166
************* - - [31/Jul/2025:08:49:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:08:49:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:08:49:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753922984486 HTTP/1.1" 200 169
************* - - [31/Jul/2025:08:49:40 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 562
************* - - [31/Jul/2025:08:49:40 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753922984486 HTTP/1.1" 200 13016
************* - - [31/Jul/2025:08:49:42 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1753922984485 HTTP/1.1" 200 1482
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-30&_timer304=1753922989061 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/base/saas/token?_timer304=1753922989061 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1753922989107 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:44 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************* - - [31/Jul/2025:08:49:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:08:49:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:08:49:47 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [31/Jul/2025:08:49:47 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [31/Jul/2025:08:49:47 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 611
************* - - [31/Jul/2025:08:49:47 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [31/Jul/2025:08:49:47 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [31/Jul/2025:08:49:48 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [31/Jul/2025:08:49:48 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1753922989107 HTTP/1.1" 200 2009
************* - - [31/Jul/2025:08:49:48 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************* - - [31/Jul/2025:08:49:48 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [31/Jul/2025:08:49:48 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [31/Jul/2025:08:49:48 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************* - - [31/Jul/2025:08:49:48 +0800] "GET /api/base/saas/token?_timer304=1753922989061 HTTP/1.1" 200 411
************* - - [31/Jul/2025:08:49:48 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************* - - [31/Jul/2025:08:49:49 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753922994159 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753922994160 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753922994165 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:49:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753922994165 HTTP/1.1" 200 148
************* - - [31/Jul/2025:08:49:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753922994160 HTTP/1.1" 200 159
************* - - [31/Jul/2025:08:49:49 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753922994159 HTTP/1.1" 200 160
************* - - [31/Jul/2025:08:49:52 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-30&_timer304=1753922989061 HTTP/1.1" 200 429
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1753923025690 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1753923025690 HTTP/1.1" 200 144
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+08:50:25&etm=&_timer304=1753923025900 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753923025900 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753923025900 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+09:00&filterCnt=6&_timer304=1753923025900 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753923025900 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923025901 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753923025910 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [31/Jul/2025:08:50:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 562
************* - - [31/Jul/2025:08:50:21 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+08:50:25&etm=&_timer304=1753923025900 HTTP/1.1" 200 156
************* - - [31/Jul/2025:08:50:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753923025900 HTTP/1.1" 200 166
************* - - [31/Jul/2025:08:50:21 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+09:00&filterCnt=6&_timer304=1753923025900 HTTP/1.1" 200 164
************* - - [31/Jul/2025:08:50:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:08:50:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:08:50:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923025901 HTTP/1.1" 200 161
************* - - [31/Jul/2025:08:50:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753923025900 HTTP/1.1" 200 169
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923026058 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923026083 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753923025900 HTTP/1.1" 200 13016
************* - - [31/Jul/2025:08:50:21 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753923025910 HTTP/1.1" 200 159616
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923026083 HTTP/1.1" 200 258
************* - - [31/Jul/2025:08:50:21 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [31/Jul/2025:08:50:21 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:21 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [31/Jul/2025:08:50:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [31/Jul/2025:08:50:22 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [31/Jul/2025:08:50:22 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [31/Jul/2025:08:50:22 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753923027266 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:22 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923027294 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:22 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753923027266 HTTP/1.1" 200 148
************* - - [31/Jul/2025:08:50:22 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:22 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:08:50:22 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923027294 HTTP/1.1" 200 258
************* - - [31/Jul/2025:08:50:22 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923026058 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:08:50:23 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923028297 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [31/Jul/2025:08:50:23 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:23 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:23 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 233
************* - - [31/Jul/2025:08:50:23 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923028297 HTTP/1.1" 200 243
************* - - [31/Jul/2025:08:50:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [31/Jul/2025:08:50:27 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923031857 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:27 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:50:27 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923031857 HTTP/1.1" 200 243
************* - - [31/Jul/2025:08:50:27 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753923207914 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923207914 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753923207914 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923207914 HTTP/1.1" 200 161
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923207957 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753923207914 HTTP/1.1" 200 148
************* - - [31/Jul/2025:08:53:23 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753923207914 HTTP/1.1" 200 159616
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:08:53:23 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:23 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:08:53:23 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:08:53:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:08:53:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [31/Jul/2025:08:53:25 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923207957 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:08:53:25 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:08:53:34 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923219129 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:34 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923219129 HTTP/1.1" 200 258
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753923221223 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923221219 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923221219 HTTP/1.1" 200 161
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+08:53:41&etm=&_timer304=1753923221279 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753923221223 HTTP/1.1" 200 159616
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753923221279 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+09:00&filterCnt=6&_timer304=1753923221279 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753923221279 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753923221279 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 562
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923221343 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923221385 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:36 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+08:53:41&etm=&_timer304=1753923221279 HTTP/1.1" 200 156
************* - - [31/Jul/2025:08:53:36 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [31/Jul/2025:08:53:36 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:08:53:36 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+09:00&filterCnt=6&_timer304=1753923221279 HTTP/1.1" 200 164
************* - - [31/Jul/2025:08:53:36 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753923221279 HTTP/1.1" 200 166
************* - - [31/Jul/2025:08:53:36 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:08:53:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [31/Jul/2025:08:53:36 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753923221279 HTTP/1.1" 200 169
************* - - [31/Jul/2025:08:53:37 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753923221279 HTTP/1.1" 200 13016
************* - - [31/Jul/2025:08:53:37 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:37 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923221385 HTTP/1.1" 200 258
************* - - [31/Jul/2025:08:53:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [31/Jul/2025:08:53:37 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [31/Jul/2025:08:53:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [31/Jul/2025:08:53:38 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [31/Jul/2025:08:53:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923221343 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:08:53:38 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923222840 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:38 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [31/Jul/2025:08:53:38 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923222840 HTTP/1.1" 200 258
************* - - [31/Jul/2025:08:53:38 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [31/Jul/2025:08:53:39 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:40 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [31/Jul/2025:08:53:40 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753923224652 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:40 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753923224908 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:40 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:40 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753923224652 HTTP/1.1" 200 232
************* - - [31/Jul/2025:08:53:40 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753923224908 HTTP/1.1" 200 148
************* - - [31/Jul/2025:08:53:40 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:08:53:44 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923229098 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:44 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:44 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923229098 HTTP/1.1" 200 243
************* - - [31/Jul/2025:08:53:44 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 233
************* - - [31/Jul/2025:08:53:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753923234820 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753923234820 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:50 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753923234821 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753923234820 HTTP/1.1" 200 160
************* - - [31/Jul/2025:08:53:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753923234820 HTTP/1.1" 200 159
************* - - [31/Jul/2025:08:53:50 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753923234821 HTTP/1.1" 200 148
************* - - [31/Jul/2025:08:53:54 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923239136 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:54 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:53:54 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923239136 HTTP/1.1" 200 243
************* - - [31/Jul/2025:08:53:54 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923434838 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753923434843 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923434838 HTTP/1.1" 200 161
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+08:57:14&etm=&_timer304=1753923434915 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753923434915 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753923434915 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+09:00&filterCnt=6&_timer304=1753923434915 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753923434915 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 562
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753923434843 HTTP/1.1" 200 159616
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923434976 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923435000 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+08:57:14&etm=&_timer304=1753923434915 HTTP/1.1" 200 156
************* - - [31/Jul/2025:08:57:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753923434915 HTTP/1.1" 200 166
************* - - [31/Jul/2025:08:57:10 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [31/Jul/2025:08:57:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:08:57:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:08:57:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [31/Jul/2025:08:57:10 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+09:00&filterCnt=6&_timer304=1753923434915 HTTP/1.1" 200 164
************* - - [31/Jul/2025:08:57:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753923434915 HTTP/1.1" 200 169
************* - - [31/Jul/2025:08:57:10 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753923434915 HTTP/1.1" 200 13016
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923435000 HTTP/1.1" 200 258
************* - - [31/Jul/2025:08:57:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [31/Jul/2025:08:57:10 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:10 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [31/Jul/2025:08:57:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [31/Jul/2025:08:57:11 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [31/Jul/2025:08:57:11 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923436194 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:11 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [31/Jul/2025:08:57:11 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753923436194 HTTP/1.1" 200 258
************* - - [31/Jul/2025:08:57:11 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923434976 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:08:57:12 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753923436793 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:12 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:12 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753923436793 HTTP/1.1" 200 148
************* - - [31/Jul/2025:08:57:12 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:08:57:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [31/Jul/2025:08:57:12 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:13 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923437974 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:13 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:13 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923437974 HTTP/1.1" 200 243
************* - - [31/Jul/2025:08:57:13 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 233
************* - - [31/Jul/2025:08:57:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [31/Jul/2025:08:57:14 +0800] "OPTIONS /api/ewci/warn/rain/risk/listRsvrRainRiskWarn HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:14 +0800] "POST /api/ewci/warn/rain/risk/listRsvrRainRiskWarn HTTP/1.1" 200 164
************* - - [31/Jul/2025:08:57:14 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923439313 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:14 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:14 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 233
************* - - [31/Jul/2025:08:57:14 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923439313 HTTP/1.1" 200 243
************* - - [31/Jul/2025:08:57:17 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923442109 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:17 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:17 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923442109 HTTP/1.1" 200 243
************* - - [31/Jul/2025:08:57:17 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:08:57:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753923444595 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753923444634 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:20 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753923444644 HTTP/1.1" 200 -
************* - - [31/Jul/2025:08:57:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753923444595 HTTP/1.1" 200 160
************* - - [31/Jul/2025:08:57:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753923444634 HTTP/1.1" 200 159
************* - - [31/Jul/2025:08:57:20 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753923444644 HTTP/1.1" 200 148
************* - - [31/Jul/2025:09:02:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923735166 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:02:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753923735170 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:02:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:02:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:02:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753923735166 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:02:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923735184 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:02:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:02:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:02:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753923735170 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:02:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:02:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753923735184 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:02:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:02:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753923745159 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:02:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753923745159 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:02:25 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923750220 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:02:25 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:02:25 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923750220 HTTP/1.1" 200 243
************* - - [31/Jul/2025:09:02:25 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:09:04:12 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923857101 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:04:12 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:04:12 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753923857101 HTTP/1.1" 200 243
************* - - [31/Jul/2025:09:04:12 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:09:04:17 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221102200104&_timer304=1753923861525 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:04:17 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221102200104&_timer304=1753923861525 HTTP/1.1" 200 484
************* - - [31/Jul/2025:09:07:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:07:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:07:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753924035496 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:07:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753924035493 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:07:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:07:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753924035502 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:07:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753924035493 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:07:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:07:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753924035496 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:07:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:07:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753924035502 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:07:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:07:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753924046167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:07:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753924046167 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:08:15 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221102200104&_timer304=1753924099829 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:08:15 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221102200104&_timer304=1753924099829 HTTP/1.1" 200 484
************* - - [31/Jul/2025:09:12:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753924335164 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:12:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753924335167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:12:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:12:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:12:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753924335176 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:12:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:12:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753924335164 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:12:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753924335167 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:12:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:12:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:12:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753924335176 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:12:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:12:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753924347163 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:12:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753924347163 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:17:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753924681926 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:17:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753924681926 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:19:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753924780502 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:19:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753924780502 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:20:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753924814972 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:20:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753924814972 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:21:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:21:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753924882441 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:21:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:21:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:21:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:21:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:21:19 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753924882441 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:21:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:23:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753925016035 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:23:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753925016036 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:23:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753925016035 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:23:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753925016036 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:23:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:23:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:23:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753925016134 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:23:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:23:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:23:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:23:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753925016134 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:23:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:24:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753925080583 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:24:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753925080583 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:27:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753925234602 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:27:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753925234602 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:27:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753925234680 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:27:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753925234680 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:27:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:27:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:27:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753925234931 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:27:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:27:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:27:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:27:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753925234931 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:27:11 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:29:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753925391637 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:29:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753925391637 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:33:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753925619860 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:33:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753925619860 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:35:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753925744527 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:35:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753925744527 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:35:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753925744714 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:35:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753925744714 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:36:55 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:36:55 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:36:55 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:36:55 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753925820143 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:36:55 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:36:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:36:56 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753925820143 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:36:56 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:39:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753926000772 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:39:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753926000772 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:42:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753926143328 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:42:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753926143328 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:42:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753926177107 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:42:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753926177107 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:44:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:44:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:44:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753926255647 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:44:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:44:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:44:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:44:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753926255647 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:44:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:46:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753926409595 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:46:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753926409595 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:48:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753926519205 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753926519205 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:48:47 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753926532028 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:47 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753926532028 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:48:47 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753926532215 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:47 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:47 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:47 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:47 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753926532216 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:47 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753926532215 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:48:47 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753926532220 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:47 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:48:47 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753926532220 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:48:48 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:48:48 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753926532216 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:48:49 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:48:52 +0800] "OPTIONS /api/ewci/bia/keyArea/select-basic-info/220221102200104?_timer304=1753926536598 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:52 +0800] "OPTIONS /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:48:52 +0800] "GET /api/ewci/bia/keyArea/select-basic-info/220221102200104?_timer304=1753926536598 HTTP/1.1" 200 393
************* - - [31/Jul/2025:09:48:52 +0800] "POST /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 637
************* - - [31/Jul/2025:09:53:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753926823326 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:53:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753926823327 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:53:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753926823355 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:53:38 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:53:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753926823356 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:53:38 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:53:38 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:53:38 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753926823326 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:53:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753926823327 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:53:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:53:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753926823355 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:53:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:53:40 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753926823356 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:09:53:40 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:55:34 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221102200104&_timer304=1753926938500 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:55:34 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221102200104&_timer304=1753926938500 HTTP/1.1" 200 484
************* - - [31/Jul/2025:09:57:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753927074367 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:57:49 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753927074367 HTTP/1.1" 200 161
************* - - [31/Jul/2025:09:59:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753927170434 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:59:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753927170435 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:59:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753927170471 HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:59:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:59:26 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:59:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:09:59:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:09:59:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753927170434 HTTP/1.1" 200 160
************* - - [31/Jul/2025:09:59:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753927170435 HTTP/1.1" 200 159
************* - - [31/Jul/2025:09:59:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:09:59:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:09:59:27 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753927170471 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:04:50 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753927495419 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:04:50 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753927495419 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:05:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753927531402 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:05:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753927531402 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:07:48 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753927672916 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:07:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753927672916 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:08:54 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753927739321 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:08:54 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:08:54 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:08:54 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:08:54 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:08:55 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:08:56 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753927739321 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:08:56 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:09:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753927779382 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:09:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753927779382 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:09:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753927779463 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:09:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:09:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753927779464 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:09:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:09:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:09:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753927779463 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:09:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:09:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:09:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753927779464 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:09:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:11:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753927891578 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:11:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753927891578 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:12:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753927977886 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:12:53 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:12:53 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:12:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753927977911 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:12:53 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:12:53 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753927977913 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:12:53 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753927977886 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:12:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:12:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753927977911 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:12:54 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:12:54 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753927977913 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:12:54 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:16:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753928202453 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:16:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753928202453 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:17:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753928259167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:17:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753928259167 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:17:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753928259351 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:17:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753928259351 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:18:43 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:18:43 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:18:43 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753928328430 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:18:43 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:18:44 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:18:44 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:18:45 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753928328430 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:18:45 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:22:07 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=361BEACF-B809-4239-97E5-1482EBF9DB30&_timer304=1753928531910 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:22:07 +0800] "GET /api/fusion/warning/snapshot-index?warnId=361BEACF-B809-4239-97E5-1482EBF9DB30&_timer304=1753928531910 HTTP/1.1" 200 664
************* - - [31/Jul/2025:10:22:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753928535664 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:22:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753928535664 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:22:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753928574701 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:22:50 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753928574700 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:22:50 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:22:50 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:22:50 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:22:50 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753928574731 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:22:50 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753928574700 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:22:50 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:22:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753928574701 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:22:50 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:22:51 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753928574731 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:22:51 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:28:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753928899325 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:28:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753928899325 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:28:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753928933132 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:28:48 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753928933132 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:30:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753929029154 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:30:24 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:30:24 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753929029156 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:30:24 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:30:24 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:30:24 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:30:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753929029154 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:30:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:30:25 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753929029156 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:30:25 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:34:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753929261642 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:34:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753929261642 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:34:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753929273258 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:34:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753929273258 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:34:28 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753929273313 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:34:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:34:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:34:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753929273315 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:34:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:34:28 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753929273313 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:34:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:34:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:34:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753929273315 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:34:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:39:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753929589113 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:39:44 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753929589113 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:40:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753929620024 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:40:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753929620024 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:40:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753929650381 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:40:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753929650381 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:41:46 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:41:46 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:41:46 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753929711174 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:41:46 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:41:46 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:41:47 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:41:47 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753929711174 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:41:48 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:43:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753929826439 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:43:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753929826439 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:45:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753929944115 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:45:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753929944115 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:47:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753930038161 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:47:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:47:13 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:47:13 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:47:13 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753930038167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:47:13 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:47:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753930038161 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:47:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:47:14 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:47:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753930038167 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:47:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753930059166 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:47:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753930059166 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:48:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753930119164 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:48:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:48:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:48:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753930119172 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:48:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:48:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:48:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753930119164 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:48:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:48:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753930119172 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:48:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:50:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753930261301 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:50:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753930261301 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:54:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753930493824 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:54:49 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753930493825 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:54:49 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753930493824 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:54:49 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753930493825 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:54:49 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:54:49 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:54:49 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753930494009 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:54:49 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:54:49 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:54:50 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:54:50 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753930494009 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:54:50 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:10:56:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753930613220 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:56:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753930613220 HTTP/1.1" 200 159
************* - - [31/Jul/2025:10:57:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753930659164 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:57:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753930659164 HTTP/1.1" 200 161
************* - - [31/Jul/2025:10:58:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753930719167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:58:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:58:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:58:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753930719176 HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:58:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:10:58:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:10:58:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753930719167 HTTP/1.1" 200 160
************* - - [31/Jul/2025:10:58:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:10:58:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753930719176 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:10:58:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:02:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753930939967 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:02:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753930939967 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:04:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753931096724 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:04:52 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753931096724 HTTP/1.1" 200 161
************* - - [31/Jul/2025:11:08:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753931302570 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:08:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753931302570 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:08:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753931342419 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:08:58 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753931342419 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:10:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:10:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:10:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753931421651 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:10:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:10:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:11:10:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:11:10:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753931421651 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:11:10:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:12:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753931546976 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:12:22 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753931546976 HTTP/1.1" 200 161
************* - - [31/Jul/2025:11:14:43 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753931688117 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:14:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753931688117 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:15:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753931722506 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:15:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753931722506 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:15:55 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:15:55 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753931759674 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:15:55 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:15:55 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:15:55 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:11:15:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753931759860 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:15:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753931759860 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:15:55 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:11:15:56 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753931759674 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:11:15:56 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:19:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753931967780 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:19:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753931967780 HTTP/1.1" 200 161
************* - - [31/Jul/2025:11:21:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753932087086 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:21:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753932087086 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:22:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753932184004 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:22:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753932184004 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:24:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:24:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:24:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753932251410 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:24:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:24:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:11:24:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:11:24:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753932251410 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:11:24:08 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:26:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753932388173 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:26:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753932388173 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:26:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753932394160 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:26:29 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753932394162 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:26:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753932394160 HTTP/1.1" 200 161
************* - - [31/Jul/2025:11:26:29 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753932394162 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:26:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:26:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:26:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753932399159 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:26:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:26:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:11:26:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:11:26:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753932399159 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:11:26:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:29:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753932553153 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:29:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753932553153 HTTP/1.1" 200 161
************* - - [31/Jul/2025:11:32:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753932761785 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:32:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753932761785 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:33:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753932795552 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:33:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753932795552 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:35:05 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:35:05 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:35:05 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753932910215 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:35:05 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:35:05 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:11:35:06 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:11:35:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:35:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753932910215 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:11:37:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753933056547 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:37:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753933056547 HTTP/1.1" 200 161
************* - - [31/Jul/2025:11:39:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753933170206 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:39:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753933170206 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:41:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753933276593 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:41:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753933276593 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:43:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:43:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:43:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753933415907 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:43:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:43:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:11:43:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:11:43:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753933415907 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:11:43:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:43:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753933419172 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:43:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753933419179 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:43:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753933419172 HTTP/1.1" 200 161
************* - - [31/Jul/2025:11:43:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753933419179 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:44:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:44:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:44:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753933459129 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:44:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:44:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:11:44:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:11:44:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753933459129 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:11:44:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:44:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753933485141 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:44:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753933485141 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:48:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753933737173 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:48:52 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753933737173 HTTP/1.1" 200 161
************* - - [31/Jul/2025:11:50:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753933835457 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:50:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753933835457 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:50:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753933864271 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:50:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753933864271 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:51:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:51:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:51:00 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753933864442 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:51:00 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:51:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:11:51:00 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:11:51:01 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753933864442 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:11:51:01 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:53:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753934023710 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:53:39 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753934023710 HTTP/1.1" 200 161
************* - - [31/Jul/2025:11:55:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753934136166 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:55:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753934136166 HTTP/1.1" 200 159
************* - - [31/Jul/2025:11:55:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753934139164 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:55:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:55:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:55:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753934139174 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:55:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:55:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:11:55:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753934139164 HTTP/1.1" 200 160
************* - - [31/Jul/2025:11:55:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:11:55:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753934139174 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:11:55:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:11:58:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753934341252 HTTP/1.1" 200 -
************* - - [31/Jul/2025:11:58:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753934341252 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:01:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753934485872 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:01:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753934485872 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:01:51 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753934516081 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:01:51 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753934516081 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:02:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:02:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753934569749 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:02:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:02:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:02:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:02:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:02:46 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753934569749 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:02:46 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:05:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753934717633 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:05:13 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753934717633 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:06:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753934793387 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:06:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753934793387 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:08:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753934891432 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:08:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753934891432 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:09:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:09:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:09:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:09:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753934966481 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:09:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:09:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:09:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753934966481 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:09:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:12:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753935126248 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:12:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753935126248 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:12:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753935152940 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:12:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753935152940 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:15:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753935324364 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:15:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753935324364 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:17:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:17:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:17:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:17:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753935455393 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:17:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:17:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:17:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753935455393 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:17:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:18:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753935518415 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:18:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753935518415 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:19:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753935564260 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:19:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753935564260 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:21:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753935672404 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:21:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753935672404 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:22:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753935732158 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:22:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753935732158 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:22:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:22:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:22:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753935759474 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:22:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:22:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753935759482 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:22:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753935759482 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:22:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:22:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:22:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753935759474 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:22:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:23:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753935822650 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:23:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753935822650 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:24:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753935865168 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:24:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753935865168 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:29:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753936152600 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:29:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753936152600 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:30:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753936211373 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:30:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753936211373 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:31:47 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753936312209 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:31:47 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753936312209 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:33:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:33:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:33:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753936401272 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:33:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:33:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:33:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:33:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:33:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753936401272 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:34:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753936479438 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:34:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753936479438 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:35:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753936519668 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:35:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753936519668 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:35:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753936539171 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:35:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753936539171 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:36:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:36:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753936599158 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:36:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:36:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:36:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:36:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:36:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:36:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753936599158 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:37:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753936659167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:37:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753936659167 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:38:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753936719448 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:38:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:38:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:38:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753936719452 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:38:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:38:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:38:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753936719448 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:38:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:38:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:38:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753936719452 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:40:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753936827269 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:40:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753936827269 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:43:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753937028686 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:43:44 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753937028686 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:45:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753937151202 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:45:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753937151202 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:47:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753937243459 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:47:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753937243459 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:47:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:47:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753937259165 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:47:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:47:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753937259175 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:47:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:47:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:47:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753937259175 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:47:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:47:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753937259165 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:47:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:48:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753937339176 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:48:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753937339176 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:50:03 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:50:03 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:50:03 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753937407789 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:50:03 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:50:03 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:50:04 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:50:04 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753937407789 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:50:04 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:51:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753937469850 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:51:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753937469850 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:53:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753937619161 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:53:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753937619165 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:53:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753937619161 HTTP/1.1" 200 161
************* - - [31/Jul/2025:12:53:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753937619165 HTTP/1.1" 200 160
************* - - [31/Jul/2025:12:54:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:54:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:54:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753937681486 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:54:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:54:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:12:54:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:12:54:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753937681486 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:12:54:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:12:57:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753937825252 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:57:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753937825252 HTTP/1.1" 200 159
************* - - [31/Jul/2025:12:58:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753937919510 HTTP/1.1" 200 -
************* - - [31/Jul/2025:12:58:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753937919510 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:02:29 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753938153702 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:02:29 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753938153702 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:03:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753938188748 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:03:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753938188748 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:03:38 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:03:38 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:03:38 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:03:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753938222518 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:03:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:03:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:03:39 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753938222518 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:03:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:06:04 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753938368864 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:06:04 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753938368864 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:07:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753938465290 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:40 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:40 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:40 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:40 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753938465293 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:40 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:07:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753938465290 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:07:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:07:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753938465293 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:07:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:07:43 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753938467569 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:43 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753938467569 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:07:43 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753938467613 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753938467613 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:07:43 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:43 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:43 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753938468181 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:43 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:07:43 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:07:44 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:07:45 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:07:45 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753938468181 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:08:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753938489162 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:08:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753938489162 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:12:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753938735180 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:12:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753938735180 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:12:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753938736166 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:12:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:12:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:12:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:12:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753938736173 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:12:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753938736166 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:12:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:12:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:12:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753938736173 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:12:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:13:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753938820883 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:13:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753938820883 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:18:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753939123023 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:18:38 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753939123023 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:20:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753939227270 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:20:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753939227270 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:22:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753939332913 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:22:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753939332913 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:23:24 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:23:24 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:23:24 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:23:24 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753939409129 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:23:24 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:23:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:23:26 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753939409129 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:23:26 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:24:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753939476532 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:24:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753939476530 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:24:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753939476530 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:24:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753939476532 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:24:32 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:24:32 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:24:32 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:24:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753939476629 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:24:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:24:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:24:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753939476629 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:24:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:25:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753939528167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:25:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753939528167 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:29:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753939796590 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:29:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753939796588 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:29:52 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:29:52 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:29:52 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753939796628 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:29:52 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:29:52 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753939796588 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:29:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753939796590 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:29:52 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:29:52 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:29:53 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753939796628 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:29:53 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:30:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753939850548 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:30:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753939850548 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:33:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753940000040 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:33:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753940000041 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:33:15 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:33:15 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:33:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753940000078 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:33:15 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:33:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753940000040 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:33:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753940000041 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:33:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:33:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:33:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753940000078 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:33:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:36:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753940167482 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:36:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753940167482 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:39:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753940400440 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:39:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753940400440 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:42:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753940552741 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:42:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753940552741 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:43:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753940594401 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:43:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753940594401 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:43:51 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:43:51 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:43:51 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753940636031 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:43:51 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:43:51 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:43:52 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:43:52 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753940636031 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:43:52 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:46:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753940765952 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:46:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753940765952 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:46:48 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753940813109 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:46:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753940813109 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:46:48 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:46:48 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:46:48 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:46:48 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753940813210 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:46:48 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:46:49 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:46:50 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753940813210 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:46:50 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:47:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753940870260 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:47:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753940870261 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:47:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753940870277 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:47:45 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:47:45 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:47:45 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:47:45 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753940870278 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:47:45 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753940870260 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:47:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:47:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753940870277 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:47:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753940870261 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:47:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:47:47 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753940870278 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:47:47 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:52:12 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:52:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753941136756 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:52:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753941136762 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:52:12 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:52:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:52:12 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753941136764 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:52:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753941136756 HTTP/1.1" 200 161
************* - - [31/Jul/2025:13:52:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:13:52:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753941136762 HTTP/1.1" 200 160
************* - - [31/Jul/2025:13:52:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:13:52:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753941136764 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:13:52:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:13:53:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753941186960 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:53:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753941186960 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:58:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753941539789 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:58:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753941539789 HTTP/1.1" 200 159
************* - - [31/Jul/2025:13:59:30 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753941575229 HTTP/1.1" 200 -
************* - - [31/Jul/2025:13:59:30 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753941575229 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:01:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:01:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753941699181 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:01:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:01:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753941699164 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:01:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:01:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:01:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753941699164 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:01:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:01:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753941699181 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:14:01:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [31/Jul/2025:14:04:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753941881544 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:04:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753941881544 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:05:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753941916919 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:05:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753941916918 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:05:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753941916919 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:05:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753941916918 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:05:12 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:05:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:05:12 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753941917033 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:05:12 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:05:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:05:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:05:14 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753941917033 HTTP/1.1" 200 441332
************* - - [31/Jul/2025:14:05:14 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 52489
************* - - [31/Jul/2025:14:08:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753942127377 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:08:43 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753942127377 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:10:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753942228982 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:10:24 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:10:24 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:10:24 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753942228983 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:10:24 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:10:24 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:10:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753942228982 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:10:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753942229037 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:10:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753942229037 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:10:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:10:25 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753942228983 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:10:25 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:12:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:12:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753942359535 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:12:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753942359530 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:12:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:12:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753942359536 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:12:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:12:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753942359530 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:12:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:12:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753942359535 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:12:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:12:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753942359536 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:12:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:16:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753942565838 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:16:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753942565838 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:17:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753942635193 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:17:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753942635193 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:17:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753942636163 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:17:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:17:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:17:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:17:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753942636167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:17:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753942636163 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:17:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:17:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:17:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753942636167 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:17:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:18:42 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221102200104&_timer304=1753942726619 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:18:42 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753942726627 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:18:42 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+09:00&warnName=&_timer304=1753942726627 HTTP/1.1" 200 243
************* - - [31/Jul/2025:14:18:42 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=361BEACF-B809-4239-97E5-1482EBF9DB30&_timer304=1753942726648 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:18:42 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221102200104&_timer304=1753942726619 HTTP/1.1" 200 484
************* - - [31/Jul/2025:14:18:42 +0800] "GET /api/fusion/warning/snapshot-index?warnId=361BEACF-B809-4239-97E5-1482EBF9DB30&_timer304=1753942726648 HTTP/1.1" 200 664
************* - - [31/Jul/2025:14:19:13 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:19:13 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 2437
************* - - [31/Jul/2025:14:20:25 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:20:25 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 2437
************* - - [31/Jul/2025:14:21:16 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:21:16 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 2437
************* - - [31/Jul/2025:14:21:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753942896762 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:21:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753942896762 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:22:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753942961903 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:22:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753942961897 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:22:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:22:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:22:37 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753942961904 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:22:37 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:22:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753942961897 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:22:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:22:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753942961903 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:22:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:22:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753942961904 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:22:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:27:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753943240281 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:27:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753943240281 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:28:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753943319161 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:28:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753943319161 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:29:09 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:29:09 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6852
************* - - [31/Jul/2025:14:29:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753943379161 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:29:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:29:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:29:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753943379166 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:29:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:29:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:29:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753943379161 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:29:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:29:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753943379166 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:29:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:32:17 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:32:17 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6852
************* - - [31/Jul/2025:14:32:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753943542170 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:32:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753943542170 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:32:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753943580517 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:32:56 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753943580518 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:32:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753943580517 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:32:56 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:32:56 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:32:56 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753943580518 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:32:56 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753943580550 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:32:56 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:32:56 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:32:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:32:57 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753943580550 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:32:57 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:38:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753943917674 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:38:33 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753943917674 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:39:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753943953747 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:39:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753943953747 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:40:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753944055136 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:40:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753944055136 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:41:53 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:41:53 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:41:53 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944118065 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:41:53 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:41:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:41:54 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:41:54 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944118065 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:41:54 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:43:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753944224583 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:43:40 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753944224583 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:45:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753944326082 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:45:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753944326082 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:45:56 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753944360753 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:45:56 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753944360753 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:46:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:46:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:46:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944399914 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:46:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:46:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:46:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:46:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944399914 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:46:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:47:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753944434601 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:47:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753944434601 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:47:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753944434679 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:47:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753944434679 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:47:10 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:47:10 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:47:10 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944434932 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:47:10 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:47:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:47:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:47:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:47:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944434932 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753944500183 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753944500188 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753944500183 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:48:15 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753944500188 HTTP/1.1" 200 159616
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+14:48:20&etm=&_timer304=1753944500249 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753944500249 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753944500249 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753944500249 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+15:00&filterCnt=6&_timer304=1753944500249 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+14:48:20&etm=&_timer304=1753944500249 HTTP/1.1" 200 156
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944500315 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [31/Jul/2025:14:48:15 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:14:48:15 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 562
************* - - [31/Jul/2025:14:48:15 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753944500249 HTTP/1.1" 200 166
************* - - [31/Jul/2025:14:48:15 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:14:48:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [31/Jul/2025:14:48:15 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+15:00&filterCnt=6&_timer304=1753944500249 HTTP/1.1" 200 164
************* - - [31/Jul/2025:14:48:15 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753944500339 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:15 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753944500249 HTTP/1.1" 200 169
************* - - [31/Jul/2025:14:48:16 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:16 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753944500249 HTTP/1.1" 200 13016
************* - - [31/Jul/2025:14:48:16 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753944500339 HTTP/1.1" 200 258
************* - - [31/Jul/2025:14:48:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:16 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [31/Jul/2025:14:48:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [31/Jul/2025:14:48:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [31/Jul/2025:14:48:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:14:48:17 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753944501509 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:14:48:17 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753944501509 HTTP/1.1" 200 258
************* - - [31/Jul/2025:14:48:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944500315 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:48:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:14:48:18 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:14:48:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753944503458 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:19 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753944503509 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:19 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:19 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753944503509 HTTP/1.1" 200 148
************* - - [31/Jul/2025:14:48:19 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:48:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753944503458 HTTP/1.1" 200 232
************* - - [31/Jul/2025:14:48:23 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753944507602 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:23 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:23 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753944507602 HTTP/1.1" 200 243
************* - - [31/Jul/2025:14:48:23 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 233
************* - - [31/Jul/2025:14:48:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753944509948 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753944509948 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:48:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753944509978 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:25 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753944509980 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753944509978 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:48:25 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753944509980 HTTP/1.1" 200 148
************* - - [31/Jul/2025:14:48:27 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753944511622 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:27 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:27 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753944511622 HTTP/1.1" 200 243
************* - - [31/Jul/2025:14:48:27 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:14:48:31 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100214100&_timer304=1753944515651 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:31 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=3B9E9EF6-D3C0-4FA8-9021-B9C6F3013BCD&_timer304=1753944515659 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:31 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100214100&_timer304=1753944515651 HTTP/1.1" 200 534
************* - - [31/Jul/2025:14:48:31 +0800] "GET /api/fusion/warning/snapshot-index?warnId=3B9E9EF6-D3C0-4FA8-9021-B9C6F3013BCD&_timer304=1753944515659 HTTP/1.1" 200 666
************* - - [31/Jul/2025:14:48:32 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:48:32 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6724
************* - - [31/Jul/2025:14:54:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753944869953 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:54:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753944869953 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:54:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753944895938 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:54:51 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753944895937 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:54:51 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753944895937 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:54:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753944895938 HTTP/1.1" 200 159
************* - - [31/Jul/2025:14:54:51 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:54:51 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:54:51 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:54:51 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944895990 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:54:51 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:54:52 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:54:52 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753944895990 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:54:52 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:14:58:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753945100172 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:58:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753945100170 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:58:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753945100170 HTTP/1.1" 200 161
************* - - [31/Jul/2025:14:58:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753945100172 HTTP/1.1" 200 160
************* - - [31/Jul/2025:14:58:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:58:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:58:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753945101174 HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:58:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:14:58:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:14:58:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:14:58:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753945101174 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:14:58:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:00:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753945240241 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:00:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753945240241 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:00:41 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753945246258 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:00:41 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:00:41 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:00:41 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753945246258 HTTP/1.1" 200 148
************* - - [31/Jul/2025:15:03:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753945400186 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:03:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753945400190 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:03:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753945400186 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:03:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753945400190 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:03:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:03:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:03:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753945401169 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:03:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:03:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:03:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:03:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753945401169 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:03:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:04:38 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753945482774 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:04:38 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753945482774 HTTP/1.1" 200 243
************* - - [31/Jul/2025:15:04:47 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753945491848 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:04:47 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:04:47 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753945491848 HTTP/1.1" 200 243
************* - - [31/Jul/2025:15:04:47 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:15:04:53 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=3B9E9EF6-D3C0-4FA8-9021-B9C6F3013BCD&_timer304=1753945497845 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:04:53 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100214100&_timer304=1753945497845 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:04:53 +0800] "GET /api/fusion/warning/snapshot-index?warnId=3B9E9EF6-D3C0-4FA8-9021-B9C6F3013BCD&_timer304=1753945497845 HTTP/1.1" 200 666
************* - - [31/Jul/2025:15:04:53 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100214100&_timer304=1753945497845 HTTP/1.1" 200 534
************* - - [31/Jul/2025:15:04:58 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:04:58 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6754
************* - - [31/Jul/2025:15:05:16 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:05:16 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6760
************* - - [31/Jul/2025:15:05:16 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:05:16 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6807
************* - - [31/Jul/2025:15:05:17 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:05:17 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6806
************* - - [31/Jul/2025:15:05:17 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:05:17 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6806
************* - - [31/Jul/2025:15:05:19 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:05:19 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6754
************* - - [31/Jul/2025:15:05:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753945541163 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:05:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753945541163 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:08:13 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:08:13 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6754
************* - - [31/Jul/2025:15:09:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753945766140 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:09:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753945766140 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:09:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753945766187 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:09:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753945766189 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:09:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:09:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:09:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:09:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753945766187 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:09:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:09:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:09:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:09:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753945766189 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:09:31 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:09:31 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6807
************* - - [31/Jul/2025:15:11:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753945869850 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:11:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753945869850 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:11:54 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753945918631 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:11:54 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753945918631 HTTP/1.1" 200 243
************* - - [31/Jul/2025:15:12:31 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753945956154 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:12:31 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:12:31 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:15:12:31 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+15:00&warnName=&_timer304=1753945956154 HTTP/1.1" 200 243
************* - - [31/Jul/2025:15:12:32 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100214100&_timer304=1753945957045 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:12:32 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=3B9E9EF6-D3C0-4FA8-9021-B9C6F3013BCD&_timer304=1753945957045 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:12:32 +0800] "GET /api/fusion/warning/snapshot-index?warnId=3B9E9EF6-D3C0-4FA8-9021-B9C6F3013BCD&_timer304=1753945957045 HTTP/1.1" 200 666
************* - - [31/Jul/2025:15:12:32 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100214100&_timer304=1753945957045 HTTP/1.1" 200 534
************* - - [31/Jul/2025:15:13:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946002786 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:13:18 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946002786 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:13:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753946003170 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:13:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753946003170 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:13:19 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:13:19 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:13:19 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946004167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:13:19 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:13:19 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:13:20 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:13:21 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946004167 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:13:21 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946126373 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753946126377 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946126373 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+15:15:26&etm=&_timer304=1753946126494 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753946126377 HTTP/1.1" 200 159616
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753946126494 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753946126494 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+16:00&filterCnt=6&_timer304=1753946126494 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753946126494 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946126568 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+15:15:26&etm=&_timer304=1753946126494 HTTP/1.1" 200 156
************* - - [31/Jul/2025:15:15:22 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 562
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [31/Jul/2025:15:15:22 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+16:00&filterCnt=6&_timer304=1753946126494 HTTP/1.1" 200 164
************* - - [31/Jul/2025:15:15:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753946126494 HTTP/1.1" 200 166
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946126594 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:15:15:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [31/Jul/2025:15:15:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:15:15:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753946126494 HTTP/1.1" 200 169
************* - - [31/Jul/2025:15:15:22 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753946126494 HTTP/1.1" 200 13016
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946126594 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:15:22 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:22 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [31/Jul/2025:15:15:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [31/Jul/2025:15:15:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [31/Jul/2025:15:15:23 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:15:23 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946127688 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:23 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:15:23 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946127688 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:15:23 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946126568 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:15:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:15:24 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:25 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753946129427 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:25 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:25 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753946129427 HTTP/1.1" 200 148
************* - - [31/Jul/2025:15:15:25 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:15:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:15:26 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946130535 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:26 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:26 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946130535 HTTP/1.1" 200 243
************* - - [31/Jul/2025:15:15:26 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 233
************* - - [31/Jul/2025:15:15:29 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946134043 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:29 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:29 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946134043 HTTP/1.1" 200 243
************* - - [31/Jul/2025:15:15:29 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:15:15:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753946135976 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753946135976 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:15:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753946136038 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:31 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753946136039 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753946136038 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:15:31 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753946136039 HTTP/1.1" 200 148
************* - - [31/Jul/2025:15:15:33 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100214100&_timer304=1753946137952 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:33 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=3B9E9EF6-D3C0-4FA8-9021-B9C6F3013BCD&_timer304=1753946137960 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:33 +0800] "GET /api/fusion/warning/snapshot-index?warnId=3B9E9EF6-D3C0-4FA8-9021-B9C6F3013BCD&_timer304=1753946137960 HTTP/1.1" 200 666
************* - - [31/Jul/2025:15:15:33 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100214100&_timer304=1753946137952 HTTP/1.1" 200 534
************* - - [31/Jul/2025:15:15:34 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:34 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 6754
************* - - [31/Jul/2025:15:15:36 +0800] "OPTIONS /api/ewci/bia/keyArea/select-basic-info/220221100214100?_timer304=1753946141119 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:36 +0800] "OPTIONS /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:36 +0800] "GET /api/ewci/bia/keyArea/select-basic-info/220221100214100?_timer304=1753946141119 HTTP/1.1" 200 394
************* - - [31/Jul/2025:15:15:36 +0800] "POST /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 639
************* - - [31/Jul/2025:15:15:37 +0800] "OPTIONS /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:37 +0800] "POST /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 639
************* - - [31/Jul/2025:15:15:38 +0800] "OPTIONS /api/ewci/bia/keyArea/select-shperson-list/220221100214100?_timer304=1753946142605 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:38 +0800] "GET /api/ewci/bia/keyArea/select-shperson-list/220221100214100?_timer304=1753946142605 HTTP/1.1" 200 823
************* - - [31/Jul/2025:15:15:52 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946157048 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:52 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:52 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:52 +0800] "OPTIONS /api/syq/rsvr/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1753946157079 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:52 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946157048 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:15:52 +0800] "GET /api/syq/rsvr/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1753946157079 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:15:52 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 200618
************* - - [31/Jul/2025:15:15:52 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:52 +0800] "OPTIONS /api/syq/river/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1753946157265 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:53 +0800] "GET /api/syq/river/select-forecast-Latest-time?adcd=220000000000000&bscd=&stm=&etm=&_timer304=1753946157265 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:15:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:15:53 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:15:53 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753946157774 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:53 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:53 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753946157774 HTTP/1.1" 200 148
************* - - [31/Jul/2025:15:15:53 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:15:53 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:15:53 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:54 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:15:55 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:56 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:15:56 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946160989 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:56 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946160989 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:15:56 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:57 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:15:57 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:58 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:15:58 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753946162841 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:15:58 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753946162841 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:17:55 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:17:56 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:17:56 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946280988 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:17:56 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946280988 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:17:56 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:17:57 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:17:57 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:17:58 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:17:58 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753946282856 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:17:58 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753946282856 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:19:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946370672 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:26 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753946370696 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:26 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946370672 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:19:26 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753946370696 HTTP/1.1" 200 159616
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+15:19:31&etm=&_timer304=1753946371067 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753946371068 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753946371068 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+16:00&filterCnt=6&_timer304=1753946371068 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753946371068 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+15:19:31&etm=&_timer304=1753946371067 HTTP/1.1" 200 156
************* - - [31/Jul/2025:15:19:27 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 562
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946371353 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [31/Jul/2025:15:19:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:15:19:27 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+16:00&filterCnt=6&_timer304=1753946371068 HTTP/1.1" 200 164
************* - - [31/Jul/2025:15:19:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753946371068 HTTP/1.1" 200 166
************* - - [31/Jul/2025:15:19:27 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [31/Jul/2025:15:19:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [31/Jul/2025:15:19:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753946371068 HTTP/1.1" 200 169
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946371473 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753946371068 HTTP/1.1" 200 13016
************* - - [31/Jul/2025:15:19:27 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946371473 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:19:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:27 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [31/Jul/2025:15:19:27 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************* - - [31/Jul/2025:15:19:28 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:19:28 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946372891 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:28 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946371353 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:19:28 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753946372891 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:19:28 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:19:28 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:19:29 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:30 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:19:30 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753946374763 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:30 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753946374763 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:19:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753946400164 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753946400164 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:19:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753946401178 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753946401178 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:19:57 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753946402161 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:19:57 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753946402161 HTTP/1.1" 200 148
************* - - [31/Jul/2025:15:20:47 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753946451584 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:20:47 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:20:47 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753946451584 HTTP/1.1" 200 148
************* - - [31/Jul/2025:15:20:47 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:20:48 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946453142 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:20:48 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:20:48 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946453142 HTTP/1.1" 200 243
************* - - [31/Jul/2025:15:20:48 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 233
************* - - [31/Jul/2025:15:23:28 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946612938 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:23:28 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-31+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946612938 HTTP/1.1" 200 243
************* - - [31/Jul/2025:15:23:28 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:23:28 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 233
************* - - [31/Jul/2025:15:23:32 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:23:32 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 233
************* - - [31/Jul/2025:15:23:34 +0800] "OPTIONS /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946619292 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:23:34 +0800] "OPTIONS /api/fusion/warning/page-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:23:34 +0800] "GET /api/fusion/warning/select-ascription-type-count?adcd=220000000000000&stm=2025-07-01+08:00&etm=2025-07-31+16:00&warnName=&_timer304=1753946619292 HTTP/1.1" 200 243
************* - - [31/Jul/2025:15:23:34 +0800] "POST /api/fusion/warning/page-list HTTP/1.1" 200 30908
************* - - [31/Jul/2025:15:24:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946670168 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:24:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753946670170 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:24:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946670168 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:24:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753946670170 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:24:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:24:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:24:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946671176 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:24:26 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:24:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:24:27 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:24:28 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:24:28 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946671176 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:24:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753946702174 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:24:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753946702174 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:29:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946970165 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:29:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753946970167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:29:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753946970165 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:29:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753946970167 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:29:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:29:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:29:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946971174 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:29:26 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:29:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:29:27 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:29:28 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753946971174 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:29:28 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:29:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753947003160 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:29:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753947003160 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:34:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753947270168 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:34:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753947270172 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:34:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753947270168 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:34:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753947270172 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:34:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:34:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:34:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753947271163 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:34:26 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:34:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:34:27 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:34:28 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:34:28 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753947271163 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:35:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753947304522 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:35:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753947304522 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:36:26 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:36:27 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:36:27 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753947391862 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:36:27 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753947391862 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:36:27 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:36:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:36:28 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:36:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:36:29 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753947393768 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:36:29 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753947393768 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:39:01 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:39:02 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:39:02 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753947547021 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:39:02 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753947547021 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:39:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:39:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:39:03 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:39:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:39:04 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753947548916 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:39:04 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753947548916 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:41:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753947697791 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:41:33 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753947697791 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:42:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753947767825 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:42:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753947767825 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:43:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753947843949 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:43:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753947843949 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:43:59 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:43:59 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:43:59 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753947844133 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:43:59 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:43:59 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:43:59 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:44:00 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:44:00 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:44:00 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753947845191 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:00 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753947845191 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:44:00 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:01 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753947844133 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:44:01 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:44:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:44:01 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:44:02 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753947847005 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:02 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753947847005 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:44:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753947880873 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:36 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753947880873 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:44:36 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:36 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753947880926 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:36 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753947880929 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:36 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:44:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753947880926 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:44:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:44:37 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:44:37 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753947881940 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:37 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753947881940 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:44:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:37 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753947880929 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:44:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:44:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:44:38 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:44:39 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753947883824 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:44:39 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753947883824 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:47:06 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:47:07 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:47:07 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948031441 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:47:07 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948031441 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:47:07 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:47:08 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:47:08 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:47:08 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:47:08 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948033312 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:47:09 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948033312 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:47:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753948081952 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:47:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753948081952 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:48:27 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:48:28 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:48:28 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948113115 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:48:28 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948113115 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:48:28 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:48:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:48:29 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:48:30 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:48:30 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948114990 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:48:30 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948114990 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:49:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753948201164 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:49:56 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753948201164 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:50:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:50:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753948239166 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:50:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:50:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753948239172 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:50:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:50:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:50:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753948239166 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:50:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:50:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753948239172 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:50:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:51:55 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:51:56 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:51:56 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948321145 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:51:56 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948321145 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:51:56 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:51:57 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:51:57 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:51:58 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:51:58 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948323028 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:51:58 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948323028 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:54:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753948450357 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:54:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753948450357 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:55:59 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:56:00 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:56:00 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948564381 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:56:00 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948564381 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:56:00 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:56:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:56:01 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:56:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:56:01 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948566280 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:56:02 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948566280 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:57:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753948661583 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:57:37 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753948661583 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:58:53 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753948737568 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:53 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753948737568 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:58:53 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:53 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:53 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:53 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:53 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753948737683 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:58:54 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:58:54 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:15:58:54 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948738668 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:54 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:58:54 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948738668 HTTP/1.1" 200 258
************* - - [31/Jul/2025:15:58:54 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:54 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753948737683 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:15:58:55 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:58:55 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:56 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:15:58:56 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948740891 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:58:56 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948740891 HTTP/1.1" 200 232
************* - - [31/Jul/2025:15:59:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753948787518 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:59:43 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753948787533 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:59:43 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753948787557 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:59:43 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:59:43 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:59:43 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753948787558 HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:59:43 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:15:59:43 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753948787533 HTTP/1.1" 200 161
************* - - [31/Jul/2025:15:59:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753948787518 HTTP/1.1" 200 159
************* - - [31/Jul/2025:15:59:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753948787557 HTTP/1.1" 200 160
************* - - [31/Jul/2025:15:59:43 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:15:59:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:15:59:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:15:59:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753948787558 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:16:00:26 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:00:27 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 744
************* - - [31/Jul/2025:16:00:27 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948831772 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:00:27 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948831772 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:00:27 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:00:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1416
************* - - [31/Jul/2025:16:00:28 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:00:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1416
************* - - [31/Jul/2025:16:00:29 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948833618 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:00:29 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948833618 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:02:35 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:02:36 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:02:36 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948960621 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:02:36 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753948960621 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:02:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:02:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:02:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:02:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:02:38 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948962451 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:02:38 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753948962451 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:05:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753949148715 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:05:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753949148715 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:06:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753949178585 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:06:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753949178585 HTTP/1.1" 200 161
************* - - [31/Jul/2025:16:07:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753949240742 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753949240742 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:07:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:34 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753949259209 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:16:07:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:16:07:35 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:07:35 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753949260309 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:36 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753949260309 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:07:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753949259209 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:16:07:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:16:07:36 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:07:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:07:37 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753949262116 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:07:38 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753949262116 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:09:35 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:09:36 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:09:36 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753949381126 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:09:36 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753949381126 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:09:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:09:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:09:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:09:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:09:38 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753949382990 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:09:38 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753949382990 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:11:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753949521815 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:11:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753949521815 HTTP/1.1" 200 161
************* - - [31/Jul/2025:16:12:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753949557691 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:12:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753949557691 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:14:56 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753949701073 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:14:56 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753949701073 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:16:40 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:16:40 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:16:40 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753949805160 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:16:40 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:16:40 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:16:16:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:16:16:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753949805160 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:16:16:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:16:17:55 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:17:56 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:17:56 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753949881216 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:17:57 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753949881216 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:17:57 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:17:58 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:17:58 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:17:59 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:17:59 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753949883602 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:17:59 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753949883602 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:18:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753949922589 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:18:38 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753949922589 HTTP/1.1" 200 161
************* - - [31/Jul/2025:16:18:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753949923162 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:18:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753949923162 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:20:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950039187 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:20:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753950039173 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:20:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950039187 HTTP/1.1" 200 161
************* - - [31/Jul/2025:16:20:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753950039173 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:20:34 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:20:35 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:20:35 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950040157 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:20:35 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950040157 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:20:35 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:20:36 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:20:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:20:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:20:37 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950042119 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:20:38 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950042119 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:21:49 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:21:49 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:21:49 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753950113339 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:21:49 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:21:49 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:16:21:49 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:16:21:50 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753950113339 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:16:21:50 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:16:22:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753950171238 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:22:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753950171238 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:22:47 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:22:47 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:22:48 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950172346 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:22:48 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950172346 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:22:48 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:22:49 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:22:49 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:22:49 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:22:49 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950174270 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:22:50 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950174270 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:23:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950224161 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:23:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950224161 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:27:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950425021 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:27:03 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950425021 HTTP/1.1" 200 161
************ - - [31/Jul/2025:16:28:31 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [31/Jul/2025:16:28:31 +0800] "GET /login HTTP/1.1" 302 -
************ - - [31/Jul/2025:16:28:31 +0800] "GET /login?code=lu0Lux&state=v0mF9H HTTP/1.1" 302 -
************ - - [31/Jul/2025:16:28:31 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [31/Jul/2025:16:28:32 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1753950512527 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1753950512527 HTTP/1.1" 200 552
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1753950515141 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1753950515141 HTTP/1.1" 200 61649
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1753950515223 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1753950515223 HTTP/1.1" 200 10388
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1753950515242 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1753950515242 HTTP/1.1" 200 2009
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1753950515394 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+16:28:35&etm=&_timer304=1753950515395 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753950515395 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+17:00&filterCnt=6&_timer304=1753950515395 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753950515395 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753950515395 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753950515395 HTTP/1.1" 200 166
************ - - [31/Jul/2025:16:28:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1753950515394 HTTP/1.1" 200 1482
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+17:00&filterCnt=6&_timer304=1753950515395 HTTP/1.1" 200 164
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+16:28:35&etm=&_timer304=1753950515395 HTTP/1.1" 200 156
************ - - [31/Jul/2025:16:28:35 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [31/Jul/2025:16:28:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [31/Jul/2025:16:28:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 562
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753950515395 HTTP/1.1" 200 169
************ - - [31/Jul/2025:16:28:35 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753950515395 HTTP/1.1" 200 13016
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-30&_timer304=1753950515835 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/base/saas/token?_timer304=1753950515835 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1753950515847 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:35 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [31/Jul/2025:16:28:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [31/Jul/2025:16:28:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1753950518307 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+16:28:38&etm=&_timer304=1753950518488 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753950518488 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753950518488 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+17:00&filterCnt=6&_timer304=1753950518488 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753950518488 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950518489 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753950518494 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753950518659 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950518685 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1416
************ - - [31/Jul/2025:16:28:38 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 1780
************ - - [31/Jul/2025:16:28:38 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 611
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1753950515847 HTTP/1.1" 200 2009
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 562
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-28+16:28:38&etm=&_timer304=1753950518488 HTTP/1.1" 200 156
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1753950518488 HTTP/1.1" 200 166
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-31+08:00&etm=2025-07-31+17:00&filterCnt=6&_timer304=1753950518488 HTTP/1.1" 200 164
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1753950518488 HTTP/1.1" 200 13016
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950518489 HTTP/1.1" 200 161
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/base/saas/token?_timer304=1753950515835 HTTP/1.1" 200 411
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1753950518488 HTTP/1.1" 200 169
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1753950518494 HTTP/1.1" 200 159616
************ - - [31/Jul/2025:16:28:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [31/Jul/2025:16:28:39 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950518685 HTTP/1.1" 200 258
************ - - [31/Jul/2025:16:28:40 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1753950518307 HTTP/1.1" 200 144
************ - - [31/Jul/2025:16:28:40 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [31/Jul/2025:16:28:40 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [31/Jul/2025:16:28:40 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************ - - [31/Jul/2025:16:28:40 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950520760 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:41 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753950520989 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:41 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:41 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753950520989 HTTP/1.1" 200 148
************ - - [31/Jul/2025:16:28:41 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950520760 HTTP/1.1" 200 258
************ - - [31/Jul/2025:16:28:41 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [31/Jul/2025:16:28:41 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************ - - [31/Jul/2025:16:28:41 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753950518659 HTTP/1.1" 200 442562
************ - - [31/Jul/2025:16:28:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753950522239 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950522244 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:42 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************ - - [31/Jul/2025:16:28:42 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1753950522248 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:42 +0800] "GET /api/ew/warning/is-enabled?_timer304=1753950522248 HTTP/1.1" 200 148
************ - - [31/Jul/2025:16:28:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753950522239 HTTP/1.1" 200 160
************ - - [31/Jul/2025:16:28:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950522244 HTTP/1.1" 200 159
************ - - [31/Jul/2025:16:28:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************ - - [31/Jul/2025:16:28:43 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-30&_timer304=1753950515835 HTTP/1.1" 200 429
************ - - [31/Jul/2025:16:28:51 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 78423
************ - - [31/Jul/2025:16:28:52 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&warnGradeId=5&_timer304=1753950532973 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:52 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220204007000000&_timer304=1753950532973 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:52 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:52 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 974
************ - - [31/Jul/2025:16:28:53 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&warnGradeId=5&_timer304=1753950532973 HTTP/1.1" 200 432
************ - - [31/Jul/2025:16:28:53 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&_timer304=1753950533019 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:53 +0800] "OPTIONS /api/ew/warning/process-list?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&_timer304=1753950533019 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:53 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&_timer304=1753950533019 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:53 +0800] "OPTIONS /api/ew/warning/message-list?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&_timer304=1753950533019 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:53 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220204007000000&_timer304=1753950532973 HTTP/1.1" 200 155
************ - - [31/Jul/2025:16:28:53 +0800] "GET /api/ew/warning/flow-list?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&_timer304=1753950533019 HTTP/1.1" 200 878
************ - - [31/Jul/2025:16:28:53 +0800] "GET /api/ew/warning/process-list?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&_timer304=1753950533019 HTTP/1.1" 200 707
************ - - [31/Jul/2025:16:28:53 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&_timer304=1753950533019 HTTP/1.1" 200 326
************ - - [31/Jul/2025:16:28:53 +0800] "GET /api/ew/warning/message-list?warnId=7D639EEB-B87D-4434-9D8E-8AE4F22602FD&_timer304=1753950533019 HTTP/1.1" 200 3923
************ - - [31/Jul/2025:16:28:56 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:28:56 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [31/Jul/2025:16:29:00 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1753950540514 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:29:00 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1753950540514 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:29:00 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1753950540514 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:29:00 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-07-31+08:00&etm=2025-07-31+16:30&_timer304=1753950540526 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:29:00 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1753950540514 HTTP/1.1" 200 519
************ - - [31/Jul/2025:16:29:00 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1753950540514 HTTP/1.1" 200 520
************ - - [31/Jul/2025:16:29:00 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1753950540514 HTTP/1.1" 200 831
************ - - [31/Jul/2025:16:29:00 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-07-31+08:00&etm=2025-07-31+16:30&_timer304=1753950540526 HTTP/1.1" 200 160
************ - - [31/Jul/2025:16:29:10 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 102389
************ - - [31/Jul/2025:16:29:13 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1753950553061 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:29:13 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1753950553061 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:29:13 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2024-06-01+08:00&etm=2025-07-31+16:30&_timer304=1753950553061 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:29:13 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1753950553061 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:29:13 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1753950553061 HTTP/1.1" 200 838
************ - - [31/Jul/2025:16:29:13 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1753950553061 HTTP/1.1" 200 519
************ - - [31/Jul/2025:16:29:13 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1753950553061 HTTP/1.1" 200 520
************ - - [31/Jul/2025:16:29:13 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2024-06-01+08:00&etm=2025-07-31+16:30&_timer304=1753950553061 HTTP/1.1" 200 20007
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950617819 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753950617818 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753950617818 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:30:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950617819 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950617954 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753950617955 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950617954 HTTP/1.1" 200 161
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753950617991 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:13 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:16:30:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753950617991 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:30:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:16:30:14 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:30:14 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950619195 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:14 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:30:14 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950619243 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:15 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950619195 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:30:15 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753950617955 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:16:30:15 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950619243 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:30:15 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:16:30:16 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:30:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:16 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:30:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:30:17 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950621760 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:30:17 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950621855 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:30:17 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950621760 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:30:17 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950621855 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:30:59 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:31:00 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:31:00 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950664427 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:31:00 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950664427 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:31:00 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:31:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:31:01 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:31:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:31:02 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950666404 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:31:02 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950666404 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:32:39 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:32:40 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:32:40 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950764600 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:32:40 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753950764600 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:32:40 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:32:41 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:32:41 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:32:42 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:32:42 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950766451 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:32:42 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753950766451 HTTP/1.1" 200 232
************ - - [31/Jul/2025:16:33:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753950812879 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:33:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753950812879 HTTP/1.1" 200 160
************ - - [31/Jul/2025:16:33:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950818766 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:33:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753950818767 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:33:38 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950818766 HTTP/1.1" 200 161
************ - - [31/Jul/2025:16:33:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [31/Jul/2025:16:33:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [31/Jul/2025:16:33:39 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753950818767 HTTP/1.1" 200 442562
************ - - [31/Jul/2025:16:33:40 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************ - - [31/Jul/2025:16:33:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950822769 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:33:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950822769 HTTP/1.1" 200 159
************ - - [31/Jul/2025:16:34:15 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1753950855265 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:34:15 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1753950855265 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:34:15 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1753950855265 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:34:15 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2024-06-01+08:00&etm=2025-07-31+16:30&_timer304=1753950855265 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:34:15 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1753950855265 HTTP/1.1" 200 838
************ - - [31/Jul/2025:16:34:15 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1753950855265 HTTP/1.1" 200 519
************ - - [31/Jul/2025:16:34:15 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1753950855265 HTTP/1.1" 200 520
************ - - [31/Jul/2025:16:34:15 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2024-06-01+08:00&etm=2025-07-31+16:30&_timer304=1753950855265 HTTP/1.1" 200 20007
************* - - [31/Jul/2025:16:35:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950964063 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:35:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753950964063 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:36:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950995703 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:36:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753950995703 HTTP/1.1" 200 161
************ - - [31/Jul/2025:16:38:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753951112767 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:38:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753951112767 HTTP/1.1" 200 160
************ - - [31/Jul/2025:16:38:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753951118767 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:38:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951118768 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:38:38 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753951118767 HTTP/1.1" 200 161
************ - - [31/Jul/2025:16:38:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [31/Jul/2025:16:38:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [31/Jul/2025:16:38:39 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951118768 HTTP/1.1" 200 442562
************ - - [31/Jul/2025:16:38:40 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************ - - [31/Jul/2025:16:38:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951123775 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:38:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951123775 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:40:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753951211785 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:40:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753951211785 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:41:21 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:41:22 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:41:22 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753951286509 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:41:22 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753951286509 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:41:22 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:41:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:41:23 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:41:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:41:24 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753951288407 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:41:24 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753951288407 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:42:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951325098 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:42:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951325098 HTTP/1.1" 200 159
************ - - [31/Jul/2025:16:43:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753951412777 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:43:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753951412777 HTTP/1.1" 200 160
************ - - [31/Jul/2025:16:43:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753951418764 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:43:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951418764 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:43:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [31/Jul/2025:16:43:38 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753951418764 HTTP/1.1" 200 161
************ - - [31/Jul/2025:16:43:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [31/Jul/2025:16:43:39 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951418764 HTTP/1.1" 200 442562
************ - - [31/Jul/2025:16:43:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************ - - [31/Jul/2025:16:43:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951424771 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:43:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951424771 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:43:51 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951436127 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:43:51 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:43:51 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:43:51 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:43:51 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:16:43:52 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:16:43:53 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951436127 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:16:43:53 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:16:46:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753951567428 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:46:03 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753951567428 HTTP/1.1" 200 161
************* - - [31/Jul/2025:16:47:44 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:47:44 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:47:44 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753951669239 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:47:45 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753951669239 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:47:45 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:47:45 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:47:45 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:47:46 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:47:46 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753951671193 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:47:47 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753951671193 HTTP/1.1" 200 232
************ - - [31/Jul/2025:16:48:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753951712770 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:48:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753951712770 HTTP/1.1" 200 160
************ - - [31/Jul/2025:16:48:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753951718768 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:48:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951718769 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:48:38 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753951718768 HTTP/1.1" 200 161
************ - - [31/Jul/2025:16:48:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [31/Jul/2025:16:48:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [31/Jul/2025:16:48:39 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951718769 HTTP/1.1" 200 442562
************ - - [31/Jul/2025:16:48:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************ - - [31/Jul/2025:16:48:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951725772 HTTP/1.1" 200 -
************ - - [31/Jul/2025:16:48:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951725772 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:49:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951775552 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:49:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753951775552 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:50:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753951814551 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:50:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753951814551 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:51:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:51:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:51:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951900040 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:51:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:51:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:16:51:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:16:51:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753951900040 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:16:51:37 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:16:52:51 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:52:52 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:52:52 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753951976419 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:52:52 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753951976419 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:52:52 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:52:53 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:52:53 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:52:53 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:52:54 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753951978354 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:52:54 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753951978354 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:53:50 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753952035165 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:53:50 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753952035165 HTTP/1.1" 200 161
************* - - [31/Jul/2025:16:54:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753952098870 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:54:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753952098870 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:54:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753952099212 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:54:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753952099212 HTTP/1.1" 200 159
************* - - [31/Jul/2025:16:55:34 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:34 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:34 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753952139174 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:16:55:34 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:16:55:36 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:55:36 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952140363 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:36 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952140363 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:55:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753952139174 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:16:55:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:16:55:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:55:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:55:38 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952142346 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:38 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952142346 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:55:45 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:46 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:55:46 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952150895 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:46 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952150895 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:55:46 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:47 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:55:47 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:48 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:55:48 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952152734 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:48 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952152734 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:55:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753952161525 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753952161525 HTTP/1.1" 200 161
************* - - [31/Jul/2025:16:55:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753952161674 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:57 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:57 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:57 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:57 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753952161684 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:55:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753952161674 HTTP/1.1" 200 160
************* - - [31/Jul/2025:16:55:57 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:16:55:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:16:55:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753952161684 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:16:55:58 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:16:56:04 +0800] "OPTIONS /api/ewci/soil/select-soil-disposi-by-etm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:56:04 +0800] "POST /api/ewci/soil/select-soil-disposi-by-etm HTTP/1.1" 200 203
************* - - [31/Jul/2025:16:56:04 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:56:04 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [31/Jul/2025:16:56:05 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:56:06 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:56:06 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952170754 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:56:06 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952170754 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:56:06 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:56:07 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:56:07 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:56:08 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:56:08 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952172607 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:56:08 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952172607 HTTP/1.1" 200 232
************* - - [31/Jul/2025:16:58:18 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:58:19 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:16:58:19 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952303613 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:58:19 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952303613 HTTP/1.1" 200 258
************* - - [31/Jul/2025:16:58:19 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:58:20 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:58:20 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:58:21 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:16:58:21 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952305542 HTTP/1.1" 200 -
************* - - [31/Jul/2025:16:58:21 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952305542 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:00:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753952462490 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:00:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753952462490 HTTP/1.1" 200 159
************* - - [31/Jul/2025:17:01:00 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753952464729 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753952464730 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:00 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753952464729 HTTP/1.1" 200 161
************* - - [31/Jul/2025:17:01:00 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:00 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753952464762 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:00 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753952464730 HTTP/1.1" 200 160
************* - - [31/Jul/2025:17:01:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:17:01:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:17:01:01 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:01:01 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952465868 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:01 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952465868 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:01:01 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:01 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753952464762 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:17:01:01 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:17:01:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:01:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:01:03 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952467822 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:01:03 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952467822 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:02:05 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:02:06 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:02:06 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952531106 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:02:06 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952531106 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:02:06 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:02:07 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:02:07 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:02:08 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:02:08 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952532989 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:02:08 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952532989 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:05:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753952757612 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:05:53 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753952757612 HTTP/1.1" 200 161
************* - - [31/Jul/2025:17:06:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753952790563 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:06:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753952790563 HTTP/1.1" 200 159
************* - - [31/Jul/2025:17:08:33 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:08:33 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:08:33 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952918262 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:08:34 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753952918262 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:08:34 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:08:34 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:08:34 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:08:35 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:08:35 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952920129 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:08:36 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753952920129 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:09:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753952986165 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:09:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753952986165 HTTP/1.1" 200 160
************* - - [31/Jul/2025:17:10:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:10:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:10:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953062865 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:10:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:10:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:17:10:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:17:10:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953062865 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:17:10:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:17:12:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753953159773 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:12:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753953159773 HTTP/1.1" 200 161
************* - - [31/Jul/2025:17:14:37 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:14:38 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:14:38 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953282679 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:14:38 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953282679 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:14:38 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:14:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:14:39 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:14:40 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:14:40 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953284591 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:14:40 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953284591 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:15:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753953319422 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:15:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753953319422 HTTP/1.1" 200 159
************* - - [31/Jul/2025:17:16:58 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753953422582 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:16:58 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753953422582 HTTP/1.1" 200 160
************* - - [31/Jul/2025:17:17:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:17:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:17:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:17:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953483161 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:17:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753953483167 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:17:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:17:17:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753953483167 HTTP/1.1" 200 161
************* - - [31/Jul/2025:17:17:58 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:17:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:17:17:59 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:17:59 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953484237 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:18:00 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953484237 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:18:00 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:18:00 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953483161 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:17:18:00 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:17:18:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:18:01 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:18:01 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:18:01 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953486221 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:18:02 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953486221 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:18:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753953519376 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:18:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:18:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:18:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953519379 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:18:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:18:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:17:18:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753953519376 HTTP/1.1" 200 160
************* - - [31/Jul/2025:17:18:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:17:18:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953519379 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:17:18:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:17:19:36 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:19:37 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:19:37 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953581692 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:19:37 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953581692 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:19:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:19:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:19:38 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:19:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:19:39 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953583557 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:19:39 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953583557 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:22:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753953725851 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:22:01 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753953725851 HTTP/1.1" 200 161
************* - - [31/Jul/2025:17:23:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753953825585 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:23:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753953825585 HTTP/1.1" 200 159
************* - - [31/Jul/2025:17:24:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753953888356 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:24:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753953888356 HTTP/1.1" 200 160
************* - - [31/Jul/2025:17:25:56 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:56 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:56 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:56 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953960441 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:56 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:17:25:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:17:25:57 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953960441 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:17:25:57 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:17:25:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753953963719 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:59 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:59 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753953963719 HTTP/1.1" 200 161
************* - - [31/Jul/2025:17:25:59 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753953963851 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:59 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:59 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:59 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953963856 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:59 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:25:59 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:17:25:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753953963851 HTTP/1.1" 200 160
************* - - [31/Jul/2025:17:26:00 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:17:26:01 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753953963856 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:17:26:01 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:17:26:01 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:26:01 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953965805 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:01 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:26:01 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953965822 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:01 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953965805 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:26:01 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953965822 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:26:01 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:01 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:26:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:26:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:26:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:26:03 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953968154 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:03 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953968186 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:04 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953968154 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:26:04 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953968186 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:26:06 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:07 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:26:07 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953972122 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:07 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753953972122 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:26:07 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:08 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:26:08 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:09 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:26:09 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953974053 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:26:10 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753953974053 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:28:42 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:28:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753954126637 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:28:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753954126637 HTTP/1.1" 200 159
************* - - [31/Jul/2025:17:28:43 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:28:43 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954127500 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:28:43 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954127500 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:28:43 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:28:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:28:44 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:28:45 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:28:45 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954129407 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:28:45 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954129407 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:29:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753954176409 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:29:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753954176424 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:29:32 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:29:32 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:29:32 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:29:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753954176448 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:29:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753954176409 HTTP/1.1" 200 161
************* - - [31/Jul/2025:17:29:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753954176424 HTTP/1.1" 200 160
************* - - [31/Jul/2025:17:29:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:17:29:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:17:29:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753954176448 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:17:29:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:17:32:03 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:32:05 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:32:05 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954329555 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:32:05 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954329555 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:32:05 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:32:06 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:32:06 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:32:07 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:32:07 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954331503 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:32:07 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954331503 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:34:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753954501439 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753954501440 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:57 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753954501481 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753954501440 HTTP/1.1" 200 161
************* - - [31/Jul/2025:17:34:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753954501439 HTTP/1.1" 200 159
************* - - [31/Jul/2025:17:34:57 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:57 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:57 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753954501519 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:57 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753954501481 HTTP/1.1" 200 160
************* - - [31/Jul/2025:17:34:57 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [31/Jul/2025:17:34:57 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [31/Jul/2025:17:34:58 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:34:58 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954502506 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:58 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954502506 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:34:58 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:34:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-07-31+08:00&etm=2025-07-31+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1753954501519 HTTP/1.1" 200 442562
************* - - [31/Jul/2025:17:34:58 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53097
************* - - [31/Jul/2025:17:34:59 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:34:59 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:35:00 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:35:00 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954504430 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:35:00 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954504430 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:38:23 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:38:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:38:24 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954709020 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:38:24 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954709020 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:38:24 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:38:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:38:25 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:38:26 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:38:26 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954711051 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:38:27 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954711051 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:40:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1753954833163 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:40:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1753954833163 HTTP/1.1" 200 159
************* - - [31/Jul/2025:17:40:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753954839166 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:40:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1753954839166 HTTP/1.1" 200 161
************* - - [31/Jul/2025:17:40:34 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:40:35 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 746
************* - - [31/Jul/2025:17:40:35 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954840104 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:40:35 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1753954840104 HTTP/1.1" 200 258
************* - - [31/Jul/2025:17:40:35 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:40:36 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:40:36 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:40:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1420
************* - - [31/Jul/2025:17:40:37 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954842073 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:40:38 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1753954842073 HTTP/1.1" 200 232
************* - - [31/Jul/2025:17:41:56 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1753954920720 HTTP/1.1" 200 -
************* - - [31/Jul/2025:17:41:56 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1753954920720 HTTP/1.1" 200 160
