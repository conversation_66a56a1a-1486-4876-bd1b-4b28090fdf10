<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rsvr.mapper.RsvrMaxInflowVolumeDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <!-- 获取基础表数据   -->
    <select id="getBaseList" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolumeBase">
        select stb.stcd, stb.stnm, stb.addvcd, ad.adnm, rfc.rsvrtp,
               stb.bsnm
        from st_stbprp_b stb
                 left join mdt_adcdinfo_b ad on stb.addvcd + '000000000' = ad.adcd
                 left join st_rsvrfcch_b rfc on stb.stcd=rfc.stcd
        <if test="bscd !=null and bscd != ''">
            LEFT JOIN bsn_bas_st bas ON bas.stcd = stb.stcd
        </if>
        <where>
            <if test="rsvrtps != null and rsvrtps.size() > 0">
                rfc.rsvrtp in
                <foreach collection="rsvrtps" item="rsvrtp" open="(" separator="," close=")">
                    #{rsvrtp}
                </foreach>
            </if>
            <if test="ad !=null and ad != ''">
                and left(stb.addvcd,#{level}) =#{ad}
            </if>
            <if test="bscd !=null and bscd != ''">
                and bas.bas_code = #{bscd}
            </if>
        </where>
    </select>

    <!-- 获取获取水库实时数据   -->
    <select id="selectRsvrByTimeAndStcds"
            resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolumeRealTime">
        SELECT rsvr.stcd, rsvr.tm, rsvr.inq
        FROM st_rsvr_r rsvr
        WHERE rsvr.inq IS NOT NULL
        <if test="stm != null and etm != null">
            AND rsvr.tm BETWEEN #{stm} AND #{etm}
        </if>
        <if test="stcds != null and stcds.length > 0">
            AND stcd IN
            <foreach item="item" index="index" collection="stcds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY rsvr.tm desc
    </select>

</mapper>
