<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.fusion.mapper.FusionReceiverDao">
    <select id="getSmsReceiverList" resultType="com.huitu.cloud.api.fusion.entity.ext.FusionSmsReceiver">
        SELECT A.RECEIVER_ID,
               A.MSG_ID,
               A.PHONE_NO,
               A.RECEIVER,
               A.POSITION,
               A.TAG,
               A.REMARK,
               B.SEND_ID,
               B.[STATUS] SEND_STATUS,
               B.SEND_TIME,
               B.ERROR_CODE,
               B.[ERROR_MESSAGE],
               C.[STATUS] READ_STATUS,
               C.CONFIRM_RESULT,
               C.CONFIRM_TIME
        FROM EW_FUSION_RECEIVER A
                 LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS (SELECT 8
                                                                                FROM SMS_MESSAGE
                                                                                WHERE MSG_ID = B.MSG_ID
                                                                                  AND BUSINESS_KEY = A.MSG_ID)
                 LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '9'
            AND C.SMS_WARN_MSG_ID = A.MSG_ID
            AND C.PHONE_NO = A.PHONE_NO
        WHERE EXISTS (SELECT 8
                      FROM EW_FUSION_MESSAGE B
                               INNER JOIN EW_FUSION_REL_MESSAGE C ON B.MSG_ID = C.MSG_ID
                      WHERE B.PUSH_MODE = '1'
                        AND C.WARN_ID = #{warnId}
                        AND B.MSG_ID = A.MSG_ID)
        ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC
    </select>
    <select id="getXccReceiverList" resultType="com.huitu.cloud.api.fusion.entity.ext.FusionXccReceiver">
        SELECT
            A.RECEIVER_ID,
            A.MSG_ID,
            A.PHONE_NO,
            A.RECEIVER,
            A.POSITION,
            A.TAG,
            A.REMARK,
            B.SEND_ID,
            B.EXT_SEND_ID,
            B.[STATUS] SEND_STATUS,
            B.SEND_TIME,
            B.RECEIPT_TIME,
            B.CALL_RESULT,
            B.CALL_RESULT_TEXT,
            B.FAIL_CAUSE
        FROM
            EW_FUSION_RECEIVER A
                LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID
        WHERE
            EXISTS (
                SELECT
                    8
                FROM
                    EW_FUSION_MESSAGE C
                        INNER JOIN EW_FUSION_REL_MESSAGE D ON C.MSG_ID = D.MSG_ID
                WHERE
                    C.PUSH_MODE = '2'
                  AND D.WARN_ID = #{warnId}
                  AND C.MSG_ID = A.MSG_ID)
        ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC
    </select>

</mapper>