package com.huitu.cloud.api.syq.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 强降雨告警通告
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-29
 */
@ApiModel(value="Notice对象", description="强降雨告警通告")
public class Notice extends BsnHeavyrainfallnoticeB{
    @ApiModelProperty(value = "发送单位")
    private String senddept;
    @ApiModelProperty(value = "接收单位List")
    private List<ReceiveDept> receiveDeptList;
    @ApiModelProperty(value = "文件访问路径")
    private String fileurl;
    @ApiModelProperty(value = "文件名称")
    private String filename;
    @ApiModelProperty(value = "文件真实路径")
    private String filerelurl;

    public String getFilerelurl() {
        return filerelurl;
    }

    public void setFilerelurl(String filerelurl) {
        this.filerelurl = filerelurl;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getFileurl() {
        return fileurl;
    }

    public void setFileurl(String fileurl) {
        this.fileurl = fileurl;
    }

    public String getSenddept() {
        return senddept;
    }

    public void setSenddept(String senddept) {
        this.senddept = senddept;
    }

    public List<ReceiveDept> getReceiveDeptList() {
        return receiveDeptList;
    }

    public void setReceiveDeptList(List<ReceiveDept> receiveDeptList) {
        this.receiveDeptList = receiveDeptList;
    }
}
