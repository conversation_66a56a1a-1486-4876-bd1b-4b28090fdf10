package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 安全度汛工程建设
 */
@Data
@ApiModel(value = "安全度汛工程建设")
public class BnsInspectAqdxCons extends BnsInspectReportForm {

    @ApiModelProperty(value = "安全度汛工程编码")
    private String enCode;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;

    @ApiModelProperty(value = "工程名称")
    private String enName;
}
