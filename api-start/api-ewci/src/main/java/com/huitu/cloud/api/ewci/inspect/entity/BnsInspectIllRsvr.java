package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病险水库
 */
@Data
@ApiModel(value = "病险水库")
public class BnsInspectIllRsvr extends BnsInspectReportForm {

    @ApiModelProperty(value = "水库编码")
    private String resCode;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;

    @ApiModelProperty(value = "病险水库名称")
    private String resName;

    @ApiModelProperty(value = "水库规模")
    private String engScal;
}
