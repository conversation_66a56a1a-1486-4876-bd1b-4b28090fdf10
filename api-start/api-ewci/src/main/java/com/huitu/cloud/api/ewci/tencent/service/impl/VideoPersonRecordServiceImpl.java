package com.huitu.cloud.api.ewci.tencent.service.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.ewci.tencent.entity.VideoConference;
import com.huitu.cloud.api.ewci.tencent.entity.VideoPersonRecord;
import com.huitu.cloud.api.ewci.tencent.mapper.VideoConferenceDao;
import com.huitu.cloud.api.ewci.tencent.mapper.VideoPersonRecordDao;
import com.huitu.cloud.api.ewci.tencent.service.VideoPersonRecordService;
import com.huitu.cloud.api.ewci.tencent.util.TLSSigAPIv2;
import com.huitu.cloud.util.UUIDFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * <p>
 * 会议人员记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
public class VideoPersonRecordServiceImpl extends ServiceImpl<VideoPersonRecordDao, VideoPersonRecord> implements VideoPersonRecordService {

    @Autowired
    private VideoConferenceDao videoConferenceDao;

    @Value("${tencentyun.trtc.sdkappid:1600057805}")
    private Long SDKAPPID;

    @Value("${tencentyun.trtc.sdkappsecretkey:24317168be546e92162618046424aa81800efe8373f9b76dadc05eaa589501eb}")
    private String SECRETKEY;

    /**
     * 查询并添加参会人员记录
     *
     * @param videoPersonRecord 参会人员实体
     */
    @Override
    public void searchAndInsertRecord(VideoPersonRecord videoPersonRecord) {

        VideoPersonRecord record = baseMapper.selectOne(
                new QueryWrapper<VideoPersonRecord>()
                        .eq("room_id", videoPersonRecord.getRoomId())
                        .eq("member_id", videoPersonRecord.getMemberId())
        );
        // 用户存在，更新 sign 状态
        if (Objects.nonNull(record)) {
            if (Objects.nonNull(record.getEnterTime())) {
                videoPersonRecord.setEnterTime(record.getEnterTime());
//                videoPersonRecord.setLeaveTime(null);
            }
            videoPersonRecord.setId(record.getId());
            videoPersonRecord.setSign(0);
            baseMapper.updateById(videoPersonRecord);
        } else {
            // 用户不存在，插入记录
            videoPersonRecord.setId(UUIDFactory.createUUID());
            String memberId = videoPersonRecord.getMemberId();
            VideoPersonRecord extUserInfo = baseMapper.selectExtInfo(memberId);
            videoPersonRecord.setDeptid(extUserInfo.getDeptid());
            videoPersonRecord.setDeptnm(extUserInfo.getDeptnm());
            videoPersonRecord.setAdcd(extUserInfo.getAdcd());
            videoPersonRecord.setAdnm(extUserInfo.getAdnm());
            baseMapper.insert(videoPersonRecord);
        }
    }

    /**
     * 退出房间更新参会人员记录
     *
     * @param videoPersonRecord 参会人员实体
     */
    @Override
    public void updateRecordForMemberLeave(VideoPersonRecord videoPersonRecord) {
        // 验证是否是主持人
        VideoConference videoConference = videoConferenceDao.selectOne(
                new QueryWrapper<VideoConference>()
                        .eq("room_id", videoPersonRecord.getRoomId())
                        .eq("create_id", videoPersonRecord.getMemberId())
        );

        VideoPersonRecord record = baseMapper.selectOne(
                new QueryWrapper<VideoPersonRecord>()
                        .eq("room_id", videoPersonRecord.getRoomId())
                        .eq("member_id", videoPersonRecord.getMemberId())
        );

        if (Objects.nonNull(record)) {
            videoPersonRecord.setId(record.getId());
            baseMapper.updateById(videoPersonRecord);
        }

        // 退出人是主持人，防止是直接关闭浏览器导致房间未解散，强制调用解散房间的接口
        if (Objects.nonNull(videoConference)) {

            TLSSigAPIv2 tLSSigAPIv2 = new TLSSigAPIv2(SDKAPPID, SECRETKEY);
            String userSig = tLSSigAPIv2.genUserSig("administrator", 30);

            String url = "https://console.tim.qq.com/v4/room_engine_http_srv/destroy_room?sdkappid=" + SDKAPPID + "&identifier=administrator&usersig=" + userSig + "&random=9999999&contenttype=json";
            JSONObject params = new JSONObject();
            params.put("RoomId", videoPersonRecord.getRoomId());
            String result2 = HttpRequest.post(url)
                    .body(params.toJSONString())
                    .timeout(20000)//超时，毫秒
                    .execute().body();
            System.out.println(result2);

            // 解散后更新房间状态
            videoConference.setEndTime(LocalDateTime.now());
            videoConference.setStatus(2);
            videoConferenceDao.updateById(videoConference);

            // 遍历房间内其他人 将 sign 更新为 1 离开房间，将 leaveTime 更新为当前

            Map<String, Object> queryRecordMapTemp = new HashMap<String, Object>() {{
                put("room_id", videoPersonRecord.getRoomId());
                put("sign", "0");
            }};
            List<VideoPersonRecord> videoPersonRecords1 = baseMapper.selectByMap(queryRecordMapTemp);
            videoPersonRecords1.forEach(r -> {
                r.setSign(1);
                r.setLeaveTime(LocalDateTime.now());
                baseMapper.updateById(r);
            });
        }
    }

    /**
     * 解散房间更新参会人员记录
     *
     * @param videoPersonRecord 参会人员实体
     */
    @Override
    public void updateRecordForDestroyRoom(VideoPersonRecord videoPersonRecord) {
        Map<String, Object> queryMap = new HashMap<String, Object>() {{
            put("room_id", videoPersonRecord.getRoomId());
            put("sign", "0");
        }};
        List<VideoPersonRecord> videoPersonRecords = baseMapper.selectByMap(queryMap);
        videoPersonRecords.forEach(record -> {
            record.setSign(1);
            record.setLeaveTime(LocalDateTime.now());
            baseMapper.updateById(record);
        });
    }

    /**
     * 查询用户 部门 部门ID 政区 政区ID
     *
     * @param userId 用户ID
     * @return VideoPersonRecord
     */
    @Override
    public VideoPersonRecord selectExtInfo(String userId) {
        return baseMapper.selectExtInfo(userId);
    }

    /**
     * 根据 roomId 查询参会人员
     *
     * @param roomId 房间号
     * @return List<VideoPersonRecord>
     */
    @Override
    public List<VideoPersonRecord> selectByRoomId(String roomId) {
        return baseMapper.selectList(
                new QueryWrapper<VideoPersonRecord>().eq("room_id", roomId)
        );

    }

    public void getRoomMemberList(String roomId) {
        TLSSigAPIv2 tLSSigAPIv2 = new TLSSigAPIv2(SDKAPPID, SECRETKEY);
        String userSig = tLSSigAPIv2.genUserSig("administrator", 30);

        String url = "https://console.tim.qq.com/v4/room_engine_http_srv/get_room_member_list?sdkappid="
                + SDKAPPID + "&identifier=administrator&usersig="
                + userSig + "&random=9999999&contenttype=json";
        JSONObject params = new JSONObject();
        params.put("RoomId", roomId);
//        params.put("Count", 50);
        String result2 = HttpRequest.post(url)
                .body(params.toJSONString())
                .timeout(20000)//超时，毫秒
                .execute().body();
        System.out.println(result2);
    }
}
