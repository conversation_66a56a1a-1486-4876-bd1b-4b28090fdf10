<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.BnsWarnInfoDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getPage" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnInfo">
        SELECT WARN_ID, WARN_TYPE, TYPE_NAME, WARN_GRADE, WARN_NAME, WARN_CONTENT, WARN_SCOPE, Q.ADCD,M.ADNM, Q.LGTD, Q.LTTD,
        SEND_MODE, STATUS, RELEASER, RELEASE_TIME, AUDITOR, LOGINNM AUDITOR_NAME, AUDIT_TIME, RETURN_REASON, TS,
        APPROVAL, IDENTITYS
        FROM (
        (SELECT A.WARN_ID, A.WARN_TYPE, B.TYPE_NAME, A.WARN_GRADE, A.WARN_NAME, A.WARN_CONTENT, A.WARN_SCOPE, A.ADCD,
        A.LGTD, A.LTTD, A.SEND_MODE, A.STATUS, A.RELEASER, A.RELEASE_TIME, A.AUDITOR, A.AUDIT_TIME, A.RETURN_REASON,
        A.TS,
        CASE WHEN A.RELEASER != A.AUDITOR THEN '1' ELSE '0' END APPROVAL,
        CASE WHEN A.RELEASER =#{map.userId} AND A.RELEASER != A.AUDITOR THEN '1'
        WHEN A.AUDITOR = #{map.userId} AND A.RELEASER != A.AUDITOR THEN '2' ELSE '3' END IDENTITYS
        FROM BNS_WARN_INFO A LEFT JOIN BNS_WARN_TYPE_SETTINGS B ON A.WARN_TYPE = B.ID
        WHERE (A.RELEASER =#{map.userId} AND A.RELEASER != A.AUDITOR )or (A.AUDITOR = #{map.userId} AND A.RELEASER !=
        A.AUDITOR
        AND STATUS != '0') or(A.RELEASER =#{map.userId} AND A.RELEASER = A.AUDITOR))
        UNION ALL
        (SELECT A.WARN_ID, A.WARN_TYPE, B.TYPE_NAME, A.WARN_GRADE, A.WARN_NAME, A.WARN_CONTENT, A.WARN_SCOPE, A.ADCD,
        A.LGTD, A.LTTD, A.SEND_MODE, A.STATUS, A.RELEASER, A.RELEASE_TIME, A.AUDITOR, A.AUDIT_TIME, A.RETURN_REASON,
        A.TS, '' APPROVAL, '' IDENTITYS
        FROM BNS_WARN_INFO A LEFT JOIN BNS_WARN_TYPE_SETTINGS B ON A.WARN_TYPE = B.ID
        WHERE A.RELEASER != #{map.userId} AND A.AUDITOR != #{map.userId} AND A.STATUS = '2')
        ) Q LEFT JOIN BNS_USERLOGUSER_B W on Q.auditor = w.userid
        LEFT JOIN MDT_ADCDINFO_B M ON Q.ADCD = M.ADCD
        WHERE LEFT(Q.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.stm != null and map.stm !=''">
            and TS>CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and TS &lt;= CONVERT(datetime,#{map.etm})
        </if>
        <if test="map.warnName !=null and map.warnName !=''">
            AND CHARINDEX(#{map.warnName},WARN_NAME) >0
        </if>
        <if test="map.warnType !=null and map.warnType !=''">
            AND WARN_TYPE = #{map.warnType}
        </if>
        <if test="map.warnGrade !=null and map.warnGrade !=''">
            AND WARN_GRADE = #{map.warnGrade}
        </if>
        <if test="map.status !=null and map.status !=''">
            AND Q.STATUS in (${map.status})
        </if>
        ORDER BY STATUS ASC,TS DESC
    </select>
    <insert id="insertInfo">
        INSERT INTO BNS_WARN_INFO (WARN_ID, WARN_TYPE, WARN_GRADE, WARN_NAME, WARN_CONTENT, WARN_SCOPE, ADCD, LGTD,
                                   LTTD, SEND_MODE, STATUS, RELEASER, RELEASE_TIME, AUDITOR, AUDIT_TIME, RETURN_REASON,
                                   TS)
        VALUES (#{warnId}, #{warnType}, #{warnGrade}, #{warnName}, #{warnContent}, #{warnScope}, #{adcd}, #{lgtd},
                #{lttd}, #{sendMode}, #{status}, #{releaser}, #{releaseTime}, #{auditor}, #{auditTime}, #{returnReason},
                #{ts})
    </insert>
    <insert id="insertObject">
        INSERT INTO BNS_WARN_OBJECT (WARN_ID,OBJECT_ID, OBJECT_TYPE)
        SELECT T.WARN_ID , T.OBJECT_ID, T.OBJECT_TYPE FROM (VALUES
        <foreach collection="map.list" item="item" separator=",">
            (#{item.warnId}, #{item.objectId}, #{item.objectType})
        </foreach>
        ) AS T(WARN_ID, OBJECT_ID, OBJECT_TYPE)
    </insert>
    <insert id="insertOperation">
        INSERT INTO BNS_WARN_OPERATION (WARN_ID, STATUS, OPERATOR, OPERATE_TIME)
        VALUES (#{warnId}, #{status}, #{operator}, #{operateTime})
    </insert>
    <update id="updateInfo">
        UPDATE BNS_WARN_INFO
        <set>
            <if test="warnType != null and warnType !=''">
                WARN_TYPE = #{warnType},
            </if>
            <if test="warnGrade != null and warnGrade !=''">
                WARN_GRADE = #{warnGrade},
            </if>
            <if test="warnName != null and warnName !=''">
                WARN_NAME = #{warnName},
            </if>
            <if test="warnContent != null and warnContent !=''">
                WARN_CONTENT = #{warnContent},
            </if>
            <if test="warnScope != null and warnScope !=''">
                WARN_SCOPE = #{warnScope},
            </if>
            <if test="sendMode != null and sendMode !=''">
                SEND_MODE = #{sendMode},
            </if>
        </set>
        WHERE WARN_ID = #{warnId} AND RELEASER = #{releaser} AND STATUS in ('0','3')
    </update>
    <update id="releaseInfo">
        UPDATE BNS_WARN_INFO
        set STATUS = '1'
        WHERE WARN_ID = #{map.warnId}
          AND RELEASER = #{map.userId}
          AND STATUS in ('0', '3')
    </update>
    <update id="auditorInfo">
        UPDATE BNS_WARN_INFO set
        <if test="map.returnReason != null and map.returnReason !=''">
            RETURN_REASON = #{map.returnReason},
        </if>
        <if test="map.releaseTime != null">
            RELEASE_TIME = #{map.releaseTime},
        </if>
        AUDIT_TIME = #{map.auditTime},
        STATUS = #{map.status}
        WHERE WARN_ID = #{map.warnId}
        AND AUDITOR = #{map.userId}
        AND STATUS in ('0','1')
    </update>
    <delete id="deleteInfo">
        delete
        from BNS_WARN_INFO
        where WARN_ID = #{map.warnId}
          AND RELEASER = #{map.userId}
    </delete>
    <delete id="deleteObject">
        delete
        from BNS_WARN_OBJECT
        where WARN_ID = #{warnId}
    </delete>
    <delete id="deleteOperation">
        delete
        from BNS_WARN_OPERATION
        where WARN_ID = #{warnId}
    </delete>
    <select id="getUserListByDept" resultType="com.huitu.cloud.api.ewci.warn.entity.WarnInfoUserInfos">
        SELECT Q.userid, Q.loginnm
        FROM (SELECT A.userid, A.loginnm, ROW_NUMBER() OVER(PARTITION BY A.userid ORDER BY loginnm DESC ) AS ITEM
              FROM BNS_USERLOGUSER_B A
                  LEFT JOIN BSN_UGRELATION B
              ON A.userid = CONVERT (varchar, B.UserID)
              WHERE B.deptid = #{deptId}
                and A.useflg = 1) Q WHERE Q.ITEM = 1
    </select>
    <select id="getAdcdInfoList" resultType="com.huitu.cloud.api.ewci.warn.entity.WarnInfoAdcdInfo">
        SELECT ADCD, ADNM, PADCD, LGTD, LTTD, ADLVL
        FROM MDT_ADCDINFO_B
        WHERE PADCD = #{padcd}
    </select>
    <select id="getAdnmList" resultType="java.lang.String">
        SELECT ADNM FROM MDT_ADCDINFO_B
        WHERE ADCD in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="getResNameList" resultType="java.lang.String">
        SELECT RES_NAME FROM ATT_RES_BASE
        WHERE RES_CODE in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="getObjectID" resultType="java.lang.String">
        SELECT OBJECT_ID
        FROM BNS_WARN_OBJECT
        WHERE WARN_ID = #{warnId}
          AND OBJECT_TYPE = #{type}
    </select>
    <select id="getByWarnId" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnInfo">
        SELECT *
        FROM BNS_WARN_INFO
        WHERE WARN_ID = #{warnId}
    </select>
    <select id="getLatelyADCD" resultType="java.lang.String">
        SELECT A.adcd
        from (SELECT *, dbo.fnGetDistance(#{lgtd}, #{lttd}, LGTD, LTTD) as NUM from MDT_ADCDINFO_B where ADLVL = 3) A
        WHERE A.NUM = (SELECT MIN(B.NUM)
                       FROM (SELECT *, dbo.fnGetDistance(#{lgtd}, #{lttd}, LGTD, LTTD) as NUM
                             from MDT_ADCDINFO_B
                             where ADLVL = 3) B)
    </select>
    <select id="getUnauditCount" resultType="java.lang.Integer">
        SELECT COUNT(WARN_ID) FROM BNS_WARN_INFO WHERE STATUS = '1' AND AUDITOR = #{userId}
    </select>


</mapper>