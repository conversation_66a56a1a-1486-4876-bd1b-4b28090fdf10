package com.huitu.cloud.api.shyj.warn.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.warn.entity.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 政区预警记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-16
 */
public interface WarnrecordDao extends BaseMapper<WarnrecordR> {
    /**
     * 分页查询预警信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<WarnRelatedInfo> getWarnByPage(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询预警等级统计
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @return
     */
    List<WarnGradeStatistics> getWrnGradeNmByCount(@Param("map") Map<String, Object> param);

    /**
     * 查询预警等级统计
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @return
     */
    List<WarnStatus> getWarnStatusyCount(@Param("map") Map<String, Object> param);

    /**
     * 查询单条预警信息
     *
     * @param warnId 预警id
     * @return
     */
    WarnRelatedInfo getWarnById(String warnId);

    /**
     * 统计政区预警立即转移，准备转移的数量
     *
     * @param param
     * @return
     */
    List<WarnStatistics> getAdWarnGradeStatistics(Map<String, Object> param);

    /**
     * 统计政区预警已关闭预警的数量
     *
     * @param param
     * @return
     */
    List<WarnStatistics> getAdWarnCloseStatistics(Map<String, Object> param);

    /**
     * 统计政区预警发送短信条数
     *
     * @param param
     * @return
     */
    List<WarnStatistics> getAdWarnMessageStatistics(Map<String, Object> param);

    /**
     * 统计预警关联危险区信息
     *
     * @param param
     * @return
     */
    WarnDanadStatistics getWarnDanadStatistics(Map<String, Object> param);

    /**
     * 统计预警关联防治区信息
     *
     * @param param
     * @return
     */
    WarnPrevcntStatistics getWarnPrevcntStatistics(Map<String, Object> param);

    /**
     * 统计预警关联政区信息
     *
     * @param param
     * @return
     */
    WarnAdStatistics getWarnAdStatistics(Map<String, Object> param);

    /**
     * 统计预警关联涉水工程信息
     *
     * @param param
     * @return
     */
    WarnWadingStatistics getWarnWadingStatistics(Map<String, Object> param);

    /**
     * 统计预警关联预警设施信息
     *
     * @param param
     * @return
     */
    WarnJczbxlxStatistics getWarnJczbxlxStatistics(Map<String, Object> param);

    /**
     * 插入预警状态变化表
     * @param record
     * @return
     */
    int insertWarnStatusrecordR(WarnrecordR record);

    /**
     * 更新县级预警记录表
     * @param record
     * @return
     */
    int updateByPrimaryKey(WarnrecordR record);

    /**
     * 查询县级预警记录表
     * @param warnid
     * @return
     */
    WarnrecordR selectByPrimaryKey(String warnid);

    /**
     * 插入预警操作记录表
     * @param record
     * @return
     */

    int insertWarnrecordT(WarnrecordR record);

    /**
     * 插入县级预警记录表
     * @param record
     * @return
     */

    int insertWarn(WarnrecordR record);

    /**
     * 插入会商记录表
     * @param record
     * @return
     */
    int insertWarnAlertConsultation(WarnAlertConsultation record);

    /**
     * 预警关联的测站或村预警列表
     *
     * @param warnId 预警id
     * @return
     */
    List<StwarnRelatedInfo> getStAdWarnById(String warnId);

    /**
     * 预警状态列表
     *
     * @param isClose 是否查询已关闭状态("0":查询非关闭的所有状态)
     * @return
     */
    List<WarningstatusB> getWarnStatusList(@Param("isClose") Boolean isClose);

    /**
     * 预警等级列表
     *
     * @return
     */
    List<WarninggradeB> getWarnGradeList();

    /**
     * 预警类型列表
     *
     * @return
     */
    List<WarningtypeB> getWarnTypeList();
    /**
     * 查询预警的操作记录信息
     *
     * @param warnId 预警id
     * @return
     */
    List<RecordInfo> getWarnRecordById(String warnId);
    /**
     * 查询预警的操作状态记录信息
     *
     * @param warnId 预警id
     * @return
     */
    List<RecordInfo> getWarnStatusRecordById(String warnId);
    /**
     * 根据预警ID查询预警反馈
     *
     * @param warnId 预警id
     * @return
     */
    WarnFeedback getWarnFeedbackById(String warnId);
    /**
     * 添加预警反馈
     *
     * @return
     */
    Integer addWarnFeedback(WarnFeedback feedback);
    /**
     * 修改预警反馈
     *
     * @return
     */
    Integer updateWarnFeedback(WarnFeedback feedback);
    /**
     * 根据预警ID查询预警反馈
     *
     * @param warnId 预警id
     * @return
     */
    WarnDisasterSituationReport getWarnDisAsterSituationById(String warnId);
    /**
     * 添加预警上报
     *
     * @return
     */
    Integer addWarnDisAsterSituation(WarnDisasterSituationReport report);
    /**
     * 添加或修改预警上报
     *
     * @return
     */
    Integer updateWarnDisAsterSituation(WarnDisasterSituationReport report);

    /**
     * 获取预警名称
     * @return
     */
    String getWarnNm();

    /**
     * 获取水旱责任人
     * @param page
     * @param adcd
     * @return
     */
    IPage<WarnShPersonVo> getShPersonList(Page page, @Param("adcd")String adcd);

}
