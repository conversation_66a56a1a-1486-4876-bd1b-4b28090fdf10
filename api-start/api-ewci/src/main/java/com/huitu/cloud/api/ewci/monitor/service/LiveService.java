package com.huitu.cloud.api.ewci.monitor.service;

import com.huitu.cloud.api.ewci.monitor.entity.*;

import java.util.List;

/**
 * 实况信息服务
 *
 * <AUTHOR>
 */
public interface LiveService {

    /**
     * 获取雨情信息
     *
     * @param query
     * @return
     **/
    List<LiveRain> getRainList(LiveQuery query);

    /**
     * 防治区信息列表
     *
     * @param list
     * @return
     */
    List<IaCPrevad> getPrevadList(List<String> list);

    /**
     * 危险区信息列表
     *
     * @param list
     * @return
     */
    List<IaCDanad> getDanadList(List<String> list);

    /**
     * 山洪责任人信息列表
     *
     * @param list
     * @return
     */
    List<FloodPerson> getFloodPersonList(List<String> list);

    /**
     * 防洪工程信息-水库列表
     *
     * @param list
     * @return
     */
    List<ReservoirData> getReservoirList(List<String> list);

    /**
     * 防洪工程信息-水闸列表
     *
     * @param list
     * @return
     */
    List<SluiceData> getSluiceList(List<String> list);

    /**
     * 防洪工程信息-堤防列表
     *
     * @param list
     * @return
     */
    List<DikeData> getDikeList(List<String> list);

    /**
     * 防洪工程信息-涵洞列表
     *
     * @param list
     * @return
     */
    List<CulvertData> getCulvertList(List<String> list);

    /**
     * 防洪工程信息-桥梁列表
     *
     * @param list
     * @return
     */
    List<BridgeData> getBridgeList(List<String> list);

    /**
     * 防洪工程信息-塘堰列表
     *
     * @param list
     * @return
     */
    List<DaminfoData> getDaminfoList(List<String> list);

    /**
     * 生成预警文件
     *
     * @param query
     * @return
     **/
    void generateWarnFile(LiveWarnFileQuery query) throws Exception;

    /**
     * 获取实况预警规则
     * @param level
     * @param adcd
     * @return
     */
    List<MessageWarnStatusB> getLiveWarnStatusList(String level, String adcd);

    /**
     * 水库责任人信息列表
     *
     * @param list
     * @return
     */
    List<RsvrPersonVo> getRsvrPersonList(List<String> list);
}
