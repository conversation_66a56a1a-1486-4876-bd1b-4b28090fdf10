package com.huitu.cloud.api.ewci.consult.controller;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.consult.entity.*;
import com.huitu.cloud.api.ewci.consult.service.DefDispatchService;
import com.huitu.cloud.api.ewci.soil.entity.SoilEchartsVo;
import com.huitu.cloud.api.ewci.soil.entity.SoilQuery;
import io.swagger.annotations.Api;

import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 防汛会商-防御调度
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "防汛会商-防御调度")
@RequestMapping("/api/ewci/consult/def")
public class DefDispatchResource extends AbstractApiResource implements ApiResource {

    public DefDispatchResource(DefDispatchService baseService) {
        this.baseService = baseService;
    }

    @Override
    public String getUuid() {
        return "fea15b6c-84f9-6945-5ddb-441a1f3a2fc1";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final DefDispatchService baseService;

    @ApiOperation(value = "作战图超标准洪水淹没范围信息", notes = "作者：赵英捷")
    @PostMapping("select-river-affectad")
    public ResponseEntity<SuccessResponse<RiverAffectadVo>> getRiverAffectadVo(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverAffectadVo(query)));
    }

    @ApiOperation(value = "作战图超标准洪水淹没范围信息详细数据", notes = "作者：赵英捷")
    @PostMapping("select-river-affectad-details")
    public ResponseEntity<SuccessResponse<List<RiverAffectadDetailsVo>>> getRiverAffectadDetailsList(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverAffectadDetailsList(query)));
    }

    @ApiOperation(value = "作战图全省超标准洪水淹没范围信息详细数据", notes = "作者：赵英捷")
    @PostMapping("select-river-affectad-details-all")
    public ResponseEntity<SuccessResponse<List<RiverAffectadDetailsChildVo>>> getRiverAffectadDetailsAllList() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverAffectadDetailsAllList()));
    }

    @ApiOperation(value = "作战图提防统计", notes = "作者：赵英捷")
    @PostMapping("select-river-dike")
    public ResponseEntity<SuccessResponse<RiverDikeVo>> getRiverDikeVo(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDikeVo(query)));
    }

    @ApiOperation(value = "作战图超标准洪水淹没范围信息详细数据", notes = "作者：赵英捷")
    @PostMapping("select-river-dike-details")
    public ResponseEntity<SuccessResponse<List<RiverDikeDetailsVo>>> getRiverDikeDetailsList(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDikeDetailsList(query)));
    }

    @ApiOperation(value = "作战图全省超标准洪水淹没范围信息详细数据", notes = "作者：赵英捷")
    @PostMapping("select-river-dike-details-all")
    public ResponseEntity<SuccessResponse<List<RiverDikeDetailsChildVo>>> getRiverDikeDetailsAllList() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDikeDetailsAllList()));
    }

    @ApiOperation(value = "作战图提防统计详细数据沙基沙堤", notes = "作者：赵英捷")
    @PostMapping("select-river-dike-details-base")
    public ResponseEntity<SuccessResponse<List<RiverBaseDikeVo>>> getRiverBaseDikeList(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverBaseDikeList(query)));
    }

    @ApiOperation(value = "作战图提防统计详细数据沙基沙堤", notes = "作者：赵英捷")
    @PostMapping("select-river-dike-details-base-all")
    public ResponseEntity<SuccessResponse<List<RiverBaseDikeVo>>> getRiverBaseDikeAllList() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverBaseDikeAllList()));
    }

    @ApiOperation(value = "作战图提防统计详细数据险工险段", notes = "作者：赵英捷")
    @PostMapping("select-river-dike-details-dpds")
    public ResponseEntity<SuccessResponse<List<RiverDpdsVo>>> getRiverDpdsList(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDpdsList(query)));
    }

    @ApiOperation(value = "作战图提防统计详细数据险工险段", notes = "作者：赵英捷")
    @PostMapping("select-river-dike-details-dpds-all")
    public ResponseEntity<SuccessResponse<List<RiverDpdsVo>>> getRiverDpdsAllList() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDpdsAllList()));
    }

    @ApiOperation(value = "作战图穿堤建筑统计", notes = "作者：赵英捷")
    @PostMapping("select-river-dtgc")
    public ResponseEntity<SuccessResponse<RiverDtgcVo>> getRiverDtgcVo(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDtgcVo(query)));
    }

    @ApiOperation(value = "作战图穿堤建筑统计详细数据", notes = "作者：赵英捷")
    @PostMapping("select-river-dtgc-details")
    public ResponseEntity<SuccessResponse<List<RiverDtgcVo>>> getRiverDtgcList(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDtgcList(query)));
    }

    @ApiOperation(value = "作战图穿堤建筑统计详细数据", notes = "作者：赵英捷")
    @PostMapping("select-river-dtgc-details-all")
    public ResponseEntity<SuccessResponse<List<RiverDtgcVo>>> getRiverDtgcAllList() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDtgcAllList()));
    }

    @ApiOperation(value = "作战图设计洪峰流量", notes = "作者：赵英捷")
    @PostMapping("select-river-desing")
    public ResponseEntity<SuccessResponse<List<RiverDesingVo>>> getRiverDesingVo(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDesingVo(query)));
    }

    @ApiOperation(value = "作战图洪水传播时间", notes = "作者：赵英捷")
    @PostMapping("select-river-pro")
    public ResponseEntity<SuccessResponse<List<RiverProVo>>> getRiverProList(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverProList(query)));
    }

    @ApiOperation(value = "水库图层", notes = "作者：赵英捷")
    @PostMapping("select-rsvr-info-list")
    public ResponseEntity<SuccessResponse<List<RsvrInfoVo>>> getRsvrInfoVoList(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRsvrInfoVoList(query)));
    }

    @ApiOperation(value = "河道图层", notes = "作者：赵英捷")
    @PostMapping("select-river-info-list")
    public ResponseEntity<SuccessResponse<List<RiverInfoVo>>> getRiverInfoVoList(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverInfoVoList(query)));
    }

    @ApiOperation(value = "获取河流四角坐标", notes = "作者：赵英捷")
    @PostMapping("select-river-bas-by-rvcode")
    public ResponseEntity<SuccessResponse<BnsRiverBas>> getBnsRiverBas(@Validated @RequestBody RiverZztQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBnsRiverBas(query)));
    }
}
