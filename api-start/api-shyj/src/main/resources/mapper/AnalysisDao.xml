<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.shyj.evaluation.mapper.AnalysisDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!--设计暴雨 -->
    <select id="getDesntbList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaDesntb" useCache="false">
        select a.WSCD, INTV, HVALUE, CV, CSVC, PMP, H1, H2, H5, H10, H20, SIGNER, AUDID, STATUS, REMARK, a.MODITIME,WSNM
        <if test="map.type == '1'.toString()">
            from IA_A_DESNTB A
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_DESNTB A
        </if>
        left join IA_C_WATA C ON A.WSCD=C.WSCD
        where
        A.WSCD NOT IN
        ( SELECT DISTINCT ( WSCD ) AS WSCD FROM
        <if test="map.type == '1'.toString()">
             IA_C_WSADCD D
        </if>
        <if test="map.type == '2'.toString()">
             BNS_IA_C_WSADCD D
        </if>
        WHERE LEFT (D.ADCD, #{map.level}) != LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        )
        <if test="map.wsnm !=null and map.wsnm !=''">
            AND CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        ORDER BY A.WSCD,A.INTV
    </select>

    <select id="getDesntbSum" resultType="int">
        SELECT COUNT
        ( AA.WSCD ) AS wscd
        FROM
        (
        SELECT
        a.WSCD
        <if test="map.type == '1'.toString()">
            from IA_A_DESNTB A
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_DESNTB A
        </if>
        left join IA_C_WATA C ON A.WSCD=C.WSCD
        where
        A.WSCD NOT IN
        ( SELECT DISTINCT ( WSCD ) AS WSCD FROM
        <if test="map.type == '1'.toString()">
            IA_C_WSADCD D
        </if>
        <if test="map.type == '2'.toString()">
            BNS_IA_C_WSADCD D
        </if>
        WHERE LEFT (D.ADCD, #{map.level}) != LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        )
        <if test="map.wsnm !=null and map.wsnm !=''">
            AND CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        GROUP BY
        a.WSCD
        ) AA
    </select>

    <!--小流域汇流时间设计暴雨时程分配表 -->
    <select id="getHlsjtb" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaHlsjtb" useCache="false">
        SELECT
        isnull(  A.WSNM, B.WSCD ) AS WSNM,
        B.WSCD,
        B.INTV,
        B.TMCD,
        B.PMP,
        B.H1,
        B.H2,
        B.H5,
        B.H10,
        B.H20,
        B.SIGNER,
        B.AUDID,
        B.STATUS,
        B.REMARK,
        B.MODITIME
        FROM
        <if test="map.radio == '1'.toString()">
            IA_A_HLSJTB b
        </if>
        <if test="map.radio == '2'.toString()">
            BNS_IA_A_HLSJTB b
        </if>
        LEFT JOIN
        (
        SELECT DISTINCT
        a.WSCD,
        isnull( b.WSNM, a.WSCD ) AS WSNM
        FROM
        <if test="map.radio == '1'.toString()">
            IA_C_WSADCD a
        </if>
        <if test="map.radio == '2'.toString()">
            BNS_IA_C_WSADCD a
        </if>
        LEFT JOIN IA_C_WATA b ON b.WSCD = a.WSCD
        WHERE 1=1
        <if test="map.wsnm !=null and map.wsnm !=''">
            AND CHARINDEX(#{map.wsnm},B.WSNM) >0
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        ) a
        ON b.WSCD = a.WSCD
        ORDER BY B.WSCD,B.INTV,B.TMCD
    </select>
    <!--查询小流域汇流时间设计暴雨时程信息流域统计 -->
    <select id="getHlsjtbSum" resultType="int">
        SELECT COUNT(AA.WSCD) AS SUM FROM (
        SELECT
        B.WSCD
        FROM
        <if test="map.radio == '1'.toString()">
            IA_A_HLSJTB B
        </if>
        <if test="map.radio == '2'.toString()">
            BNS_IA_A_HLSJTB B
        </if>
        LEFT JOIN
        (
        SELECT DISTINCT
        a.WSCD,
        isnull( b.WSNM, a.WSCD ) AS WSNM
        FROM
        <if test="map.radio == '1'.toString()">
            IA_C_WSADCD a
        </if>
        <if test="map.radio == '2'.toString()">
            BNS_IA_C_WSADCD a
        </if>
        LEFT JOIN IA_C_WATA b ON b.WSCD = a.WSCD
        WHERE 1=1
        <if test="map.wsnm !=null and map.wsnm !=''">
            AND CHARINDEX(#{map.wsnm},B.WSNM) >0
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        ) A
        ON A.WSCD = B.WSCD
        GROUP BY B.WSCD) AA
    </select>

    <!--控制断面设计洪水 -->
    <select id="getSdtdtb" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaSdtdtb" useCache="false">
        select A.ADCD, A.WSCD, A.HSCD, A.CXQ, A.SJHSHF, A.Q, A.ZHLS, A.HSLS, A.HFSW, A.SIGNER, A.AUDID, A.STATUS, A.REMARK, A.MODITIME,ADNM,WSNM
        from
        <if test="map.radio !=null and map.radio !='' and map.radio == 1">
            IA_A_SDTDTB A
        </if>
        <if test="map.radio !=null and map.radio !='' and map.radio == 2">
            BNS_IA_A_SDTDTB A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join IA_C_WATA C ON
        A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            and A.WSCD = #{map.wscd}
        </if>
        ORDER BY A.ADCD,A.WSCD,A.CXQ
    </select>
    <!--控制断面设计洪水防治区统计 -->
    <select id="getSdtdtbSum" resultType="int">
        SELECT COUNT(AA.ADCD) AS SUM FROM (SELECT
        A.ADCD
        from
        <if test="map.radio !=null and map.radio !='' and map.radio == 1">
            IA_A_SDTDTB A
        </if>
        <if test="map.radio !=null and map.radio !='' and map.radio == 2">
            BNS_IA_A_SDTDTB A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join IA_C_WATA C ON
        A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            and A.WSCD = #{map.wscd}
        </if>
        GROUP BY
        A.ADCD) AA
    </select>
    <!--控制断面设计洪水小流域统计 -->
    <select id="getSdtdtbbasinSum" resultType="int">
        SELECT COUNT(AA.WSCD) AS SUM FROM (SELECT
        A.WSCD
        from
        <if test="map.radio !=null and map.radio !='' and map.radio == 1">
            IA_A_SDTDTB A
        </if>
        <if test="map.radio !=null and map.radio !='' and map.radio == 2">
            BNS_IA_A_SDTDTB A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join IA_C_WATA C ON
        A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            and A.WSCD = #{map.wscd}
        </if>
        GROUP BY
        A.WSCD) AA
    </select>
    <!--控制断面水位-流量-人口关系表 -->
    <select id="getSwllrktb" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaSwllrktb" useCache="false">
        select A.ADCD, A.WSCD, A.HSCD, A.Z, A.SJHSHF, A.CXQ, A.PCOUNT, A.HCOUNT, A.HNCOUNT, A.SIGNER, A.AUDID, A.STATUS,
        A.REMARK, A.MODITIME,ADNM,WSNM
        <if test="map.type == '1'.toString()">
            from IA_A_SWLLRKTB A
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_SWLLRKTB A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        left join IA_C_WATA C ON A.WSCD=C.WSCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            AND CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            and A.WSCD = #{map.wscd}
        </if>
        ORDER BY A.ADCD,A.WSCD,A.HSCD,A.CXQ
    </select>
    <!--查询控制断面水位-流量-人口关系表统计政区 -->
    <select id="getDistrictSum" resultType="int">
        SELECT COUNT(AA.ADCD) AS SUM FROM (
        select A.ADCD
        <if test="map.type == '1'.toString()">
            from IA_A_SWLLRKTB A
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_SWLLRKTB A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        left join IA_C_WATA C ON A.WSCD=C.WSCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            AND CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            and A.WSCD = #{map.wscd}
        </if>
        GROUP BY A.ADCD) AA
    </select>
    <!--查询控制断面水位-流量-人口关系表统计小流域 -->
    <select id="getBasinSum" resultType="int">
        SELECT
        COUNT ( AA.WSCD ) AS SUM
        FROM
        (
        select A.WSCD
        <if test="map.type == '1'.toString()">
            from IA_A_SWLLRKTB A
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_SWLLRKTB A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        left join IA_C_WATA C ON A.WSCD=C.WSCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            AND CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            and A.WSCD = #{map.wscd}
        </if>
        GROUP BY
        A.WSCD
        ) AA
    </select>
    <!--防洪现状评价 -->
    <select id="getNowfhtb" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaNowfhtb" useCache="false">
        select A.ADCD, A.WSCD, A.HSCD, A.FCA, A.MXPN, A.MXHN, A.SENPN, A.SENHN, A.WXPN, A.WXHN, A.SIGNER, A.AUDID,
        A.STATUS, A.REMARK, A.MODITIME,ADNM,WSNM
        <if test="map.type == '1'.toString()">
            from IA_A_NOWFHTB A
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_NOWFHTB A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        left join IA_C_WATA C ON A.WSCD=C.WSCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            and A.WSCD = #{map.wscd}
        </if>
        ORDER BY A.ADCD,A.WSCD,A.HSCD
    </select>
    <!--统计防洪现状评价表-政区 -->
    <select id="getNowfhtbDistrictSum" resultType="int">
        SELECT
        COUNT ( AA.ADCD ) AS ADCDSUM
        FROM
        (
        select A.ADCD
        <if test="map.type == '1'.toString()">
            from IA_A_NOWFHTB A
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_NOWFHTB A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        left join IA_C_WATA C ON A.WSCD=C.WSCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            and A.WSCD = #{map.wscd}
        </if>
        GROUP BY
        A.ADCD) AA
    </select>
    <!--统计防洪现状评价表-小流域 -->
    <select id="getNowfhtbBasinSum" resultType="int">
        SELECT COUNT
        ( AA.WSCD ) AS WSCDSUM
        FROM
        (
        select A.WSCD
        <if test="map.type == '1'.toString()">
            from IA_A_NOWFHTB A
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_NOWFHTB A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        left join IA_C_WATA C ON A.WSCD=C.WSCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        <if test="map.wscd !=null and map.wscd !=''">
            and A.WSCD = #{map.wscd}
        </if>
        GROUP BY
        A.WSCD
        ) AA
    </select>
    <!--临界雨量经验估计法成果表 -->
    <select id="getJygstb" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaJygstb" useCache="false">
        select A.ADCD, A.MT, A.LWATER, A.CRP, A.CALMATH, A.SIGNER, A.AUDID, A.STATUS, A.REMARK, A.MODITIME,ADNM from
        IA_A_JYGSTB A LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        ORDER BY A.ADCD,A.MT
    </select>
    <!--临界雨量降雨分析法成果表-->
    <select id="getYjcjtb" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaYjcjtb" useCache="false">
        select A.ADCD, A.CRP, A.CALMATH, A.STGNER, A.AUDID, A.STATUS, A.REMARK, A.MODITIME,ADNM from IA_A_YJCJTB A LEFT
        JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        ORDER BY A.ADCD
    </select>
    <!--临界雨量模型分析法-->
    <select id="getFxcgtb" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaFxcgtb" useCache="false">
        select A.ADCD, A.LWATER, A.MT, A.ZBPV, A.CALMATH, A.SIGNER, A.AUDID, A.STATUS, A.REMARK, A.MODITIME,ADNM
        <if test="map.type == '1'.toString()">
            from IA_A_FXCGTB A
            left join IA_C_WSADCD W ON A.ADCD = W.ADCD
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_FXCGTB A
            left join BNS_IA_C_WSADCD W ON A.ADCD = W.ADCD
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        ORDER BY A.ADCD,A.LWATER,A.MT
    </select>
    <!--查询临界雨量模型分析法信息防治区统计-->
    <select id="getFxcgtbDistrictSum" resultType="int">
        SELECT COUNT(AA.ADCD) from (
        select A.ADCD
        <if test="map.type == '1'.toString()">
            from IA_A_FXCGTB A
            left join IA_C_WSADCD W ON A.ADCD = W.ADCD
        </if>
        <if test="map.type == '2'.toString()">
            from BNS_IA_A_FXCGTB A
            left join BNS_IA_C_WSADCD W ON A.ADCD = W.ADCD
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY A.ADCD ) AA
    </select>

    <!--预警指标时段雨量-->
    <select id="getDfwrule" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaDfwrule" useCache="false">
        select A.ADCD, A.WSCD, A.WARNGRADEID, A.LWATER, A.STDT, A.DRPT, A.CALMATH, A.SIGNER, A.AUDID, A.STATUS,
        A.REMARK, A.MODITIME,ADNM,WSNM from IA_A_DFWRULE A LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join
        IA_C_WATA C ON
        A.WSCD=C.WSCD
        where A.WARNGRADEID != '4'
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        order by A.ADCD,A.WSCD,A.STDT
    </select>
    <!--预警指标时段雨量统计防治区-->
    <select id="getDfwruleDistrictSum" resultType="int">
        SELECT COUNT(AA.ADCD) AS ADCD FROM (
        select A.ADCD from IA_A_DFWRULE A LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join
        IA_C_WATA C ON
        A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        GROUP BY A.ADCD) AA
    </select>
    <!--预警指标综合雨量-->
    <select id="getYjicr" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaYjicr" useCache="false">
        select A.ADCD, A.WARNGRADEID, A.CRP, A.CALMATH, A.SIGNER, A.AUDID, A.STATUS, A.REMARK, A.MODITIME,ADNM
        from
        <if test="map.radio == '1'.toString()">
            IA_A_YJICR A
        </if>
        <if test="map.radio == '2'.toString()">
            BNS_IA_A_YJICR A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        ORDER BY A.ADCD,A.WARNGRADEID
    </select>
    <!--查询预警指标综合雨量信息防治区统计-->
    <select id="getYjicrSum" resultType="int">
        SELECT COUNT(AA.ADCD) AS ADCD FROM (
        select A.ADCD
        from
        <if test="map.radio == '1'.toString()">
            IA_A_YJICR A
        </if>
        <if test="map.radio == '2'.toString()">
            BNS_IA_A_YJICR A
        </if>
        LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        GROUP BY A.ADCD) AA
    </select>

    <!--预警指标水位-->
    <select id="getWlwrule" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaWlwrule">
        select A.ADCD, A.WSCD, A.WARNGRADEID, A.STCD, A.ETIME, A.STLEN, A.ZT, A.CALMATH, A.SIGNER, A.AUDID, A.STATUS,
        A.REMARK, A.MODITIME,ADNM,WSNM from IA_A_WLWRULE A LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join
        IA_C_WATA C ON
        A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        ORDER BY A.ADCD,A.WSCD,A.WARNGRADEID,A.ETIME
    </select>
    <!--查询预警指标水位信息防治区统计-->
    <select id="getWlwruleSum" resultType="int">
        SELECT COUNT(AA.ADCD) AS ADCD FROM (
        select A.ADCD from IA_A_WLWRULE A LEFT JOIN IA_C_ADINFO ad on A.ADCD=AD.ADCD left join
        IA_C_WATA C ON
        A.WSCD=C.WSCD
        where 1=1
        <if test="map.ad !=null and map.ad !=''">
            AND A.ADCD LIKE '${map.ad}%'
        </if>
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},WSNM) >0
        </if>
        GROUP BY A.ADCD) AA
    </select>
    <!--    计算单元(防灾对象)信息查询-->
    <select id="getUnitdp" resultType="com.huitu.cloud.api.shyj.evaluation.entity.BnsIaUnitdp" useCache="false">
        select A.ADCD, A.WAT_SHED_AREA, A.HSCD, A.SLP, A.AVEROUC,A.AVEROUL,A.AVEROUR, A.CZZ, A.STDT, A.LWATER, A.REMARK, A.TS,ADNM
        from BNS_IA_UNITDP A
        LEFT JOIN IA_C_ADINFO B on A.ADCD=B.ADCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        ORDER BY A.ADCD
    </select>

    <!--重点城(集)镇计算单元（小流域）信息-->
    <select id="getUnitws" resultType="com.huitu.cloud.api.shyj.evaluation.entity.BnsIaUnitws" useCache="false">
        SELECT
        a.wscd,
        a.area,
        a.chlength,
        a.vegcoverage,
        a.sjy,
        a.desntb,
        a.sdtdtb,
        a.wruletb,
        a.flowtm,
        a.comments,
        isnull( b.WSNM, a.WSCD ) AS wsnm
        FROM
        BNS_IA_UNITWS a
        left join BNS_IA_C_WATA b
        on a.WSCD = b.WSCD
        WHERE
        1=1
        <if test="map.wsnm !=null and map.wsnm !=''">
            and CHARINDEX(#{map.wsnm},b.wsnm) >0
        </if>
        ORDER BY a.wscd
    </select>
    <!--查询重点城(集)镇调查评价汇总-->
    <select id="getCjzSummary" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzSummary" useCache="false">
        SELECT
        a.adcd,
        a.adnm as ZDADNM,
        b.adnm AS ADNM,
        c.HTCOUNT,
        d.YHCOUNT,
        e.SHCOUNT,
        f.QSYCOUNT,
        h.SKCOUNT,
        i.SZCOUNT,
        g.DFCOUNT,
        k.LHCOUNT,
        l.QLCOUNT,
        m.TBCOUNT,
        n.ZDJCCOUNT,
        o.WXGBCOUNT,
        p.JYYLCOUNT,
        q.JYSWCOUNT,
        r.ZDMCOUNT,
        s.HDMCOUNT
        FROM
        ( SELECT LEFT ( ADCD, 9 ) AS ADCD, adnm, padcd FROM MDT_ADCDINFO_B WHERE adlvl IN ( 4 ) AND adcd LIKE '22%' ) a
        LEFT JOIN ( SELECT adcd, adnm FROM MDT_ADCDINFO_B WHERE adcd LIKE '22%' ) b ON a.padcd = b.adcd
        left join (SELECT LEFT
        ( A.ADCD, 9 ) AS adcd,
        SUM ( A.htcount ) AS HTCOUNT
        FROM
        ( SELECT LEFT ( ADCD, 9 ) AS adcd, htcount FROM BNS_IA_C_DTRESIDENT WHERE adcd LIKE '22%' ) A
        WHERE
        A.adcd LIKE '22%'
        GROUP BY
        A.ADCD) c on a.adcd = c.adcd
        left join (select A.ADCD,count(1) AS YHCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_FLRVVLG) A group by A.ADCD) d
        on a.adcd  = d.adcd
        left join (select A.ADCD,count(1) AS SHCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_HSFWATER) A group by A.ADCD) e
        on a.adcd = e.adcd
        left join (select A.ADCD,count(1) AS QSYCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_BSNSSINFO) A group by A.ADCD) f
        on a.adcd = f.adcd
        left join (select A.ADCD,count(1) AS SKCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_RS) A group by A.ADCD) h
        on a.adcd = h.adcd
        left join (select A.ADCD,count(1) AS SZCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_SLUICE) A group by A.ADCD) i
        on a.adcd = i.adcd
        left join (select A.ADCD,count(1) AS DFCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_DIKE) A group by A.ADCD)g
        on a.adcd = g.adcd
        left join (select A.ADCD,count(1) AS LHCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_CULVERT) A group by A.ADCD)k
        on a.adcd = k.adcd
        left join (select A.ADCD,count(1) AS QLCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_BRIDGE) A group by A.ADCD) l
        on a.adcd = l.adcd
        left join (select A.ADCD,count(1) AS TBCOUNT   from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_DAMINFO) A group by A.ADCD) m
        on a.adcd = m.adcd
        left join (select A.ADCD,count(1) AS ZDJCCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_STINFO) A group by A.ADCD) n
        on a.adcd = n.adcd
        left join (select A.ADCD,count(1) AS WXGBCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_WBRINFO) A group by A.ADCD) o
        on a.adcd = o.adcd
        left join (select A.ADCD,count(1) AS JYYLCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_SRSTINFO) A group by A.ADCD) p
        on a.adcd = p.adcd
        left join (select A.ADCD,count(1) AS JYSWCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_C_SWSTINFO) A group by A.ADCD) q
        on a.adcd = q.adcd
        left join (select A.ADCD,count(1) AS ZDMCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_M_VSURFACE) A group by A.ADCD) r
        on a.adcd = r.adcd
        left join (select A.ADCD,count(1) AS HDMCOUNT  from (SELECT left(ADCD,9) as ADCD from BNS_IA_M_HSURFACE) A group by A.ADCD) s
        on a.adcd = s.adcd
        WHERE 1=1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        ORDER BY A.ADCD ASC
    </select>
    <!--查询重点城(集)镇调查评价涉水工程-水库详情列表-->
    <select id="getCjzReservoit" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzReservoitSs" useCache="false">
        SELECT
        a.adnm AS adnm,
        b.rs_name AS rsName,
        b.ENG_GRAD AS engGcgm,
        b.ENG_GRAD AS engGcdj,
        b.RS_TYPE AS rsType,
        b.MAIN_WR_TYPE AS mainWrType,
        b.DAM_TYPE AS damType,
        b.TOT_CAP AS totCap,
        b.MUL_AVER_RUN AS mulAverRun,
        b.DAM_SIZE_HIG AS damSizeHig,
        b.DAM_SIZE_LEN AS damSizeLen,
        b.MAX_DIS_FLOW AS maxDisFlow
        FROM
        MDT_ADCDINFO_B a
        JOIN BNS_IA_C_RS b ON a.adcd = b.adcd
        WHERE
        1=1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.rsName !=null and map.rsName !=''">
            and CHARINDEX(#{map.rsName},b.rs_name) >0
        </if>
        <if test="map.engGrad !=null and map.engGrad !=''">
            and b.ENG_GRAD = #{map.engGrad}
        </if>
        ORDER BY
        a.adcd,
        b.RS_CODE ASC
    </select>
    <!--查询重点城(集)镇调查评价涉水工程-水闸详情列表-->
    <select id="getCjzSluice" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzSluiceSs" useCache="false">
        SELECT
        a.adnm AS adnm,
        b.GATE_NAME  AS gateName,
        b.GATE_TYPE as gateType,
        b.HOLE_NUM as holeNum,
        b.HOLE_WID as holeWid,
        b.FL_GATE_FLOW as flGateFlow,
        b.RUB_DAM_HIG as rubDamHig,
        b.RUB_DAM_LEN as rubDamLen
        FROM
        MDT_ADCDINFO_B a
        JOIN BNS_IA_C_SLUICE b ON a.adcd = b.adcd
        WHERE
        1=1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.gateName !=null and map.gateName !=''">
            and CHARINDEX(#{map.gateName},b.GATE_NAME) >0
        </if>
        <if test="map.gateType !=null and map.gateType !='' and map.gateType != '1'.toString()">
            and b.GATE_TYPE = #{map.gateType}
        </if>
        <if test="map.gateType !=null and map.gateType !='' and map.gateType == '1'.toString()">
            and b.GATE_TYPE is null
        </if>
        ORDER BY
        a.adcd,
        b.GATE_CODE ASC
    </select>

    <!--查询重点城(集)镇调查评价涉水工程-堤防详情列表-->
    <select id="getDfDike" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzDfDikeSs" useCache="false">
        SELECT
        a.adnm AS adnm,
        b.DIKE_NAME AS dikeName,
        b.RV_BANK AS rvBank,
        b.DIKE_COR_BOUN AS dikeCorBoun,
        b.DIKE_TYPE AS dikeType,
        b.DIKE_STYL AS dikeStyl,
        b.DIKE_GRAD AS dikeGrad,
        b.PLAN_FL_STA AS planFlSta,
        b.DIKE_LEN AS dikeLen,
        b.FL_STA_LEN AS flStaLen,
        b.ELE_SYS AS eleSys,
        b.DES_STAG AS desStag,
        b.DAM_CRE_BEG_ELE AS damCreBegEle,
        b.DAM_CRE_EDN_ELE AS damCreEdnEle,
        b.DIKE_HIG_MAX AS dikeHigMax,
        b.DIKE_WID_MAX AS dikeWidMax,
        b.DIKE_HIG_MIN AS dikeHigMin,
        b.DIKE_WID_MIN AS dikeWidMin
        FROM
        MDT_ADCDINFO_B a
        JOIN BNS_IA_C_DIKE b ON a.adcd = b.adcd
        WHERE
        1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.dikeName !=null and map.dikeName !=''">
            and CHARINDEX(#{map.dikeName},b.DIKE_NAME) >0
        </if>
        <if test="map.dikeGrad !=null and map.dikeGrad !='' and map.dikeGrad != '6'.toString() and map.dikeGrad != '7'.toString()">
            and b.DIKE_GRAD = #{map.dikeGrad}
        </if>
        <if test="map.dikeGrad !=null and map.dikeGrad !='' and map.dikeGrad == '6'.toString()">
            and b.DIKE_GRAD in ('1级','2级','3级','4级''5级')
        </if>
        <if test="map.dikeGrad !=null and map.dikeGrad !='' and map.dikeGrad == '7'.toString()">
            and b.DIKE_GRAD is null
        </if>
        ORDER BY
        a.adcd,
        b.DIKE_CODE ASC
    </select>

    <!--查询重点城(集)镇调查评价涉水工程-路涵详情列表-->
    <select id="getLhZdjz" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzLhRCSs" useCache="false">
        SELECT
        a.adnm AS adnm,
        b.CULNAME AS culname,
        CASE
        WHEN b.TYPE = '1' THEN
        '圆管涵'
        WHEN b.TYPE = '2' THEN
        '拱涵'
        WHEN b.TYPE = '3' THEN
        '盖板涵'
        WHEN b.TYPE = '4' THEN
        '箱涵'
        END AS type,
        b.HEIGHT AS height,
        b.LENGHT AS length,
        b.WIDTH AS width,
        b.COMMENTS AS comments
        FROM
        MDT_ADCDINFO_B a
        JOIN BNS_IA_C_CULVERT b ON a.adcd = b.adcd
        WHERE
        1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.culname !=null and map.culname !=''">
            and CHARINDEX(#{map.culname},b.culname) >0
        </if>
        <if test="map.type !=null and map.type !=''">
        	and b.TYPE = #{map.type}
        </if>
        ORDER BY
        a.adcd,
        b.CULCD ASC
    </select>

    <!--查询重点城(集)镇调查评价涉水工程-桥梁详情列表-->
    <select id="getBridgeQl" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzQlBridgeSs" useCache="false">
        SELECT
        a.adnm AS adnm,
        b.BRNAME AS brname,
        CASE
        WHEN b.TYPE = 1  THEN '桥梁'
        WHEN b.TYPE = 2  THEN '浮桥'
        WHEN b.TYPE = 3  THEN '索桥'
        WHEN b.TYPE = 4  THEN '拱桥'
        END as type,
        b.HEIGHT AS height,
        b.LENGTH AS length,
        b.WIDTH AS width,
        b.COMMENTS AS comments
        FROM
        MDT_ADCDINFO_B a
        JOIN BNS_IA_C_BRIDGE b ON a.adcd = b.adcd
        WHERE
        1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.brname !=null and map.brname !=''">
            and CHARINDEX(#{map.brname},b.brname) >0
        </if>
        <if test="map.type !=null and map.type !=''">
            and b.TYPE = #{map.type}
        </if>
        ORDER BY
        a.adcd,B.BRCD  ASC
    </select>

    <!--查询重点城(集)镇调查评价涉水工程-塘坝详情列表-->
    <select id="getPondTb" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzTbPondSs" useCache="false">
        SELECT
        a.adnm AS adnm,
        b.DAMNAME AS damname,
        b.MT AS mt,
        b.HEIGHT AS height,
        b.WIDTH AS width,
        b.XHST AS xhst
        FROM
        MDT_ADCDINFO_B a
        JOIN BNS_IA_C_DAMINFO b ON a.adcd = b.adcd
        WHERE
        1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.damname !=null and map.damname !=''">
            and CHARINDEX(#{map.damname},b.damname) >0
        </if>
        <if test="map.mt !=null and map.mt !=''">
            and b.mt = #{map.mt}
        </if>
        ORDER BY
        a.adcd, b.DAMCD ASC
    </select>

    <!--查询重点城(集)镇调查评价设备信息-自动检测站-->
    <select id="getZdjczCz" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzStationSs" useCache="false">
    SELECT
    a.adnm AS adnm,
    b.STNM AS stnm,
    b.RVNM AS rvnm,
    b.HNNM AS hnnm,
    b.BSNM AS bsnm,
    b.STLC AS stlc,
    CASE
    WHEN b.STTP = 'MM' THEN
    '气象站'
    WHEN b.STTP = 'PP' THEN
    '雨量站'
    WHEN b.STTP = 'RR' THEN
    '水位站'
    WHEN b.STTP = 'ZZ' THEN
    '水文站'
    END AS sttp,
    b.LGTD AS lgtd,
    b.LTTD AS lttd,
    b.ESSTYM AS esstym
    FROM
    MDT_ADCDINFO_B a
    JOIN BNS_IA_C_STINFO b ON a.adcd = b.adcd
    WHERE
    1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.stnm !=null and map.stnm !=''">
            and CHARINDEX(#{map.stnm},b.STNM) >0
        </if>
    ORDER BY
    a.adcd,
    B.STCD ASC
    </select>

    <!--查询重点城(集)镇调查评价设备信息-无线预警广播-->
    <select id="getRadioGb" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzRadioGbSs" useCache="false">
    SELECT
        a.adnm AS adnm,
        b.ADDRESS AS address,
        b.LGTD	AS lgtd,
        b.LTTD AS lttd,
        CASE
            WHEN b.TYPE = '1' THEN
                'I型'
            WHEN b.TYPE = '2' THEN
                'Ⅱ型'
            END AS type,
        b.BDATE AS bdate,
        c.wsnm as wsnm,
        b.COMMENTS AS comments
        FROM
        MDT_ADCDINFO_B a
        JOIN BNS_IA_C_WBRINFO b ON a.adcd = b.adcd
        left join BNS_IA_C_WATA c ON b.wscd = c.wscd
        WHERE
        1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.address !=null and map.address !=''">
            and CHARINDEX(#{map.address},b.ADDRESS) >0
        </if>
        <if test="map.type !=null and map.type !=''">
            and b.TYPE = #{map.type}
        </if>
        ORDER BY
        a.adcd,
        B.WBRCD ASC
    </select>

    <!--查询重点城(集)镇调查评价设备信息-简易雨量站-->
    <select id="getSimpleJyyl" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzSimpleJyylSs" useCache="false">
        SELECT
            a.adnm AS adnm,
            b.ADDRESS AS address,
            b.LGTD AS lgtd,
            b.LTTD AS lttd,
            c.wsnm AS wsnm,
            b.BDATE AS bdate,
            b.MRAIN AS mrain,
            b.ALVOICE AS alvoice,
            b.LIGHT AS light,
            b.WVALUE AS wvalue,
            b.PRAIN AS prain,
            b.COMMENTS AS comments
        FROM
            MDT_ADCDINFO_B a
                JOIN BNS_IA_C_SRSTINFO b ON a.adcd = b.adcd
                LEFT JOIN BNS_IA_C_WATA c ON b.wscd = c.wscd
        WHERE
            1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.address !=null and map.address !=''">
            and CHARINDEX(#{map.address},b.ADDRESS) >0
        </if>
        ORDER BY
            a.adcd,
            B.SRSTCD ASC
    </select>

    <!--查询重点城(集)镇调查评价设备信息-简易水位站-->
    <select id="getWLJysw" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzWLJysw" useCache="false">
        SELECT
        a.adnm AS adnm,
        b.ADDRESS AS address,
        b.LGTD AS lgtd,
        b.LTTD AS lttd,
        c.wsnm AS wsnm,
        b.BDATE AS bdate,
        b.MWARTER AS mwarter,
        b.ALVOICE AS alvoice,
        b.ALIGHT AS alight
        FROM
        MDT_ADCDINFO_B a
        JOIN BNS_IA_C_SWSTINFO b ON a.adcd = b.adcd
        LEFT JOIN BNS_IA_C_WATA c ON b.wscd = c.wscd
        WHERE
        1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        <if test="map.address !=null and map.address !=''">
            and CHARINDEX(#{map.address},b.ADDRESS) >0
        </if>
        ORDER BY
        a.adcd,
        B.SWSTCD ASC
    </select>

    <!--查询重点城(集)镇调查评价-河道纵断面信息-->
    <select id="getHdzdm" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzHdzdm" useCache="false">
    SELECT
    a.adnm AS adnm,
    b.ADDRESS AS address,
    b.CHANNEL AS channel,
    CASE
        WHEN b.ISCTOWN = '1' THEN
        '跨县'
        WHEN b.ISCTOWN = '0' THEN
        '不跨县'
    END AS ISCTOWN,
    b.CLGTD AS clgtd,
    b.CLTTD AS clttd,
    b.CELE AS cele,
    CASE
        WHEN b.ELETYPE = '1' THEN
        '85 高程系'
        WHEN b.ELETYPE = '2' THEN
        '假定高程系'
    END AS eletype,
    CASE
        WHEN b.METHOD = '1' THEN
        '水准仪卷尺测量法'
        WHEN b.METHOD = '2' THEN
        'GNSS RTK 测量法'
        WHEN b.METHOD = '3' THEN
        '全站仪法'
        WHEN b.METHOD = '3' THEN
        '三维激光'
    END AS method,
    c.PNAME AS pname,
    c.ELE AS ele,
    c.LGTD AS lgtd,
    c.LTTD AS lttd
    FROM
    MDT_ADCDINFO_B a
    JOIN BNS_IA_M_VSURFACE b ON a.adcd = b.adcd
    JOIN BNS_IA_M_VSPOINT c ON b.VECD = c.VECD
    WHERE
    1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
    ORDER BY
    a.adcd ASC
    </select>

    <!--查询重点城(集)镇调查评价-河道横断面信息-->
    <select id="getCjzHdhdm" resultType="com.huitu.cloud.api.shyj.evaluation.entity.DcjzHdhdm" useCache="false">
        SELECT
        a.adnm AS adnm,
        b.ADDRESS AS address,
        b.CHANNEL AS channel,
        CASE
        WHEN b.DMIDENTIT = '0' THEN
        '上游'
        WHEN b.DMIDENTIT = '1' THEN
        '下游'
        WHEN b.DMIDENTIT = '2' THEN
        '控制断面'
        END AS dmidentit,
        CASE
        WHEN b.DMFORM = '0' THEN
        '矩形'
        WHEN b.DMFORM = '1' THEN
        '抛物线型'
        WHEN b.DMFORM = '2' THEN
        '三角形'
        WHEN b.DMFORM = '3' THEN
        '复合型'
        END AS dmform,
        CASE
        WHEN b.ISCTOWN = '1' THEN
        '跨县'
        WHEN b.ISCTOWN = '0' THEN
        '不跨县'
        END AS ISCTOWN,
        CASE
        WHEN b.TEXTURE = '0' THEN
        '岩石'
        WHEN b.TEXTURE = '1' THEN
        '砂砾石'
        WHEN b.TEXTURE = '2' THEN
        '砂土'
        WHEN b.TEXTURE = '3' THEN
        '壤土'
        WHEN b.TEXTURE = '4' THEN
        '粘土'
        END AS texture,
        b.BASELGTD AS baselgtd,
        b.BASELTTD AS baselttd,
        b.BASEELE AS baseele,
        b.AZIMUTH AS azimuth,
        b.HMZ AS hmz,
        b.CZZ AS czz,
        CASE
        WHEN b.METHOD = '1' THEN
        '水准仪卷尺测量法'
        WHEN b.METHOD = '2' THEN
        'GNSS RTK 测量法'
        WHEN b.METHOD = '3' THEN
        '全站仪法'
        WHEN b.METHOD = '4' THEN
        '三维激光扫描'
        END AS method,
        c.CDISTANCE AS cdistance,
        c.ELE AS ele,
        c.LGTD AS lgtd,
        c.LTTD AS lttd,
        c.COEFF AS coeff
        FROM
        MDT_ADCDINFO_B a
        JOIN BNS_IA_M_HSURFACE b ON a.adcd = b.adcd
        JOIN BNS_IA_M_HSPOINT c ON b.HECD = c.HECD
        WHERE
        1 = 1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(a.ADCD,#{map.adLevl})=#{map.adcd}
        </if>
        <if test="map.adLevl == '4'.toString()">
            AND LEFT(a.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.zdadnm !=null and map.zdadnm !=''">
            and CHARINDEX(#{map.zdadnm},A.adnm) >0
        </if>
        ORDER BY
        a.adcd ,b.HECD ASC
    </select>
    <select id="getStcdInfo" resultType="com.huitu.cloud.api.shyj.evaluation.entity.StbprpInfoVo">
        SELECT adcd, adnm, stcd, stnm FROM BSN_STBPRP_V where STCD = #{stcd}
    </select>
    <select id="getDfwruleByAdcd" resultType="com.huitu.cloud.api.shyj.evaluation.entity.IaDfwruleVo">
        SELECT A.ADCD, C.adnm, A.WSCD, A.WARNGRADEID, A.LWATER, A.STDT, A.DRPT, A.CALMATH, A.SIGNER, A.AUDID, A.STATUS, A.REMARK, A.MODITIME FROM IA_A_DFWRULE A
        left join BSN_ADCD_B C ON A.ADCD=C.ADCD
        <where>
            <if test="adcd !=null and adcd !=''">
                AND A.ADCD LIKE '${adcd}%'
            </if>
        </where>
    </select>


</mapper>