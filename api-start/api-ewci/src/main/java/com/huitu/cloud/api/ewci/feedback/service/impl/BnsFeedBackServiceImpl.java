package com.huitu.cloud.api.ewci.feedback.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBack;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackPic;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackQuery;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackVo;
import com.huitu.cloud.api.ewci.feedback.mapper.BnsFeedBackDao;
import com.huitu.cloud.api.ewci.feedback.mapper.BnsFeedBackPicDao;
import com.huitu.cloud.api.ewci.feedback.service.BnsFeedBackService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 意见反馈信息 实现类
 *
 * <AUTHOR>
 */
@Service
public class BnsFeedBackServiceImpl implements BnsFeedBackService {

    private BnsFeedBackDao baseDao;
    private BnsFeedBackPicDao feedBackPicDao;

    public BnsFeedBackServiceImpl(BnsFeedBackDao baseDao, BnsFeedBackPicDao feedBackPicDao) {
        this.baseDao = baseDao;
        this.feedBackPicDao = feedBackPicDao;
    }

    @Override
    public IPage<BnsFeedBackVo> getPage(BnsFeedBackQuery query) {
        Page page = new Page<>(query.getPageNum(), query.getPageSize());
        return baseDao.getPage(page, query.toQueryParam());
    }

    @Override
    public int insertFeedBack(BnsFeedBack entity) {
        int code = baseDao.insertFeedBack(entity);
        if (code > 0) {
            if (!CollectionUtils.isEmpty(entity.getPicList())) {
                entity.getPicList().forEach(i->{
                    i.setFeedId(entity.getId());
                });
                feedBackPicDao.addPicture(entity.getPicList());
            }
        }
        return code;
    }

    @Override
    public int handleFeedBack(BnsFeedBack entity) {
        return baseDao.handleFeedBack(entity);
    }

    @Override
    public List<BnsFeedBackPic> getFeedBackPicList(String id) {
        return feedBackPicDao.getFeedBackPicList(id);
    }
}
