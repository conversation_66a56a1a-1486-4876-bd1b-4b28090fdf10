<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.feedback.mapper.BnsFeedBackPicDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="addPicture">
        insert into BNS_FEEDBACK_PIC ( ID ,FEED_ID ,FILE_NAME ,FILEPATH ,UPTM ) values
        <foreach collection="info" item="item" index="index" separator=",">
            (
            NEWID(),
            #{item.feedId},
            #{item.fileName},
            #{item.filePath},
            GETDATE()
            )
        </foreach>
    </insert>
    <select id="getFeedBackPicList" resultType="com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackPic">
        SELECT * FROM BNS_FEEDBACK_PIC WHERE FEED_ID = #{feedId}
    </select>


</mapper>