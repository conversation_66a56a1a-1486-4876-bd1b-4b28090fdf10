package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 重点城(集)镇调查评价设备信息-自动检测站
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@ApiModel(value="DcjzStationSs对象", description="重点城(集)镇调查评价设备信息-自动检测站")
public class DcjzStationSs extends Model<DcjzStationSs> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "测站名称")
    @TableId(value = "STNM", type = IdType.NONE)
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    @TableId(value = "RVNM", type = IdType.NONE)
    private String rvnm;

    @ApiModelProperty(value = "水系名称")
    @TableId(value = "HNNM", type = IdType.NONE)
    private String hnnm;

    @ApiModelProperty(value = "流域名称")
    @TableId(value = "BSNM", type = IdType.NONE)
    private String bsnm;

    @ApiModelProperty(value = "站址")
    @TableId(value = "STLC", type = IdType.NONE)
    private String stlc;

    @ApiModelProperty(value = "站类")
    @TableId(value = "STTP", type = IdType.NONE)
    private String sttp;

    @ApiModelProperty(value = "经度")
    @TableId(value = "LGTD", type = IdType.NONE)
    private String lgtd;

    @ApiModelProperty(value = "纬度")
    @TableId(value = "LTTD", type = IdType.NONE)
    private String lttd;

    @ApiModelProperty(value = "建站年月")
    @TableId(value = "ESSTYM", type = IdType.NONE)
    private String esstym;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getHnnm() {
        return hnnm;
    }

    public void setHnnm(String hnnm) {
        this.hnnm = hnnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }

    public String getEsstym() {
        return esstym;
    }

    public void setEsstym(String esstym) {
        this.esstym = esstym;
    }

    @Override
    public String toString() {
        return "DcjzStationSs{" +
                "adnm='" + adnm + '\'' +
                ", stnm='" + stnm + '\'' +
                ", rvnm='" + rvnm + '\'' +
                ", hnnm='" + hnnm + '\'' +
                ", bsnm='" + bsnm + '\'' +
                ", stlc='" + stlc + '\'' +
                ", sttp='" + sttp + '\'' +
                ", lgtd='" + lgtd + '\'' +
                ", lttd='" + lttd + '\'' +
                ", esstym='" + esstym + '\'' +
                '}';
    }
}
