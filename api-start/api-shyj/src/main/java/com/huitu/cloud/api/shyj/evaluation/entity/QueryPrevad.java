package com.huitu.cloud.api.shyj.evaluation.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;
/**
 *
 * <AUTHOR>
 * @since 2019-09-24
 */
public class QueryPrevad extends PageBean {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "对象id(查单个对象时，其他参数置空)")
    private String key;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "防治区类型（1:防治区，2:重点防治区）")
    private String prevtp;

    public String getPrevtp() {
        return prevtp;
    }

    public void setPrevtp(String prevtp) {
        this.prevtp = prevtp;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
