<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.sst.mapper.BnsSwstInfoDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.sst.entity.BnsSwstInfo" useCache="false">
        SELECT ROW_NUMBER() OVER(ORDER BY A.ADCD, A.SWSTCD ASC) SORTNO,
        A.SWSTCD, A.ADDRESS, A.WSCD, B.WSNM, A.ADCD, C.ADNM, A.BDATE, A.LGTD, A.LTTD,COALESCE(D.PICTURE,0) AS PICTURES
        FROM BNS_IA_C_SWSTINFO A
        LEFT JOIN IA_C_WATA B ON B.WSCD = A.WSCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
        LEFT JOIN (SELECT COUNT(A.SWSTCD) PICTURE, A.SWSTCD FROM BNS_IA_C_SWSTINFO A LEFT JOIN BSN_STFILE_B B ON
        A.SWSTCD = B.KEYID WHERE B.TYPE = '简易水位' GROUP BY SWSTCD) D ON A.SWSTCD = D.SWSTCD
        WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.sdt != null">
            AND A.BDATE >= #{map.sdt}
        </if>
        <if test="map.edt != null">
            AND A.BDATE &lt;= #{map.edt}
        </if>
        <if test="map.address != null and map.address != ''">
            AND CHARINDEX(#{map.address}, A.ADDRESS) > 0
        </if>
        ORDER BY SORTNO ASC
    </select>
    <update id="update">
        UPDATE BNS_IA_C_SWSTINFO
        SET ADDRESS = #{info.address},
            WSCD    = #{info.wscd},
            ADCD    = #{info.adcd},
            BDATE   = #{info.bdate},
            LGTD    = #{info.lgtd},
            LTTD    = #{info.lttd}
        WHERE SWSTCD = #{info.swstcd}
    </update>
    <delete id="delete">
        DELETE
        FROM BNS_IA_C_SWSTINFO
        WHERE SWSTCD = #{swstcd}
    </delete>

    <select id="getNumber" resultType="java.lang.Integer">
        SELECT COUNT(*) as sum
        FROM
            BNS_IA_C_SWSTINFO
        WHERE
            SWSTCD = #{swstcd}
    </select>

    <insert id="add">
        INSERT INTO BNS_IA_C_SWSTINFO (SWSTCD,
                                       ADDRESS,
                                       WSCD,
                                       ADCD,
                                       BDATE,
                                       LGTD,
                                       LTTD,
                                       MODITIME)
        VALUES (#{info.swstcd},
                #{info.address},
                #{info.wscd},
                #{info.adcd},
                #{info.bdate},
                #{info.lgtd},
                #{info.lttd},
                GETDATE())
    </insert>
</mapper>
