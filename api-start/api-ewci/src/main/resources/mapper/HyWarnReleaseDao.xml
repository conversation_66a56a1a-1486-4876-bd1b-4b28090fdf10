<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.hydrology.mapper.HyWarnReleaseDao">
    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnRecordResponse">
        SELECT A.WARNID, D.ADCD, D.ADNM, A.STCD, STNM, RVNM, RELEASE_UNIT, F.[TYPE] RELEASE_UNIT_TYPE,
        SHORT_NAME RELEASE_UNIT_NAME, FULL_NAME RELEASE_FULL_NAME, WTM, WVAL, WTYPE, WGRD, WIDX, EWIDX, [STATE]
        FROM HY_WARN_RECORD A
        LEFT JOIN HY_WARN_FILE B ON B.WARNID = A.WARNID
        LEFT JOIN BSN_STBPRP_V C ON C.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = LEFT(C.ADCD, 6) + '000000000'
        LEFT JOIN HY_WARN_INDEX E ON E.STCD = A.STCD
        LEFT JOIN HY_WARN_UNIT F ON F.CODE = E.RELEASE_UNIT
        WHERE WTM BETWEEN #{map.stm} AND #{map.etm}
        <if test="map.wgrd != null">
            AND WGRD = #{map.wgrd}
        </if>
        AND (A.[STATE] IN ('0', '2', '3') OR (A.[STATE] = '1' AND B.SEND_DEPT = #{map.sendDept}))
        AND LEFT(C.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(C.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.stnm != null and map.stnm != ''">
            AND CHARINDEX(#{map.stnm}, C.STNM) > 0
        </if>
        <if test="map.rvnm != null and map.rvnm != ''">
            AND CHARINDEX(#{map.rvnm}, C.STNM) > 0
        </if>
        <if test="map.unitType != null and map.unitType != ''">
            AND F.[TYPE] = #{map.unitType}
        </if>
        ORDER BY WTM DESC, C.ADCD ASC, A.STCD ASC
    </select>
    <select id="getRecordInfo" resultType="com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnRecordResponse">
        SELECT WARNID,
               C.ADCD,
               C.ADNM,
               A.STCD,
               STNM,
               RVNM,
               RELEASE_UNIT,
               E.[TYPE]   RELEASE_UNIT_TYPE,
               SHORT_NAME RELEASE_UNIT_NAME,
               FULL_NAME  RELEASE_FULL_NAME,
               WTM,
               WVAL,
               WTYPE,
               WGRD,
               WIDX,
               EWIDX,
               [STATE]
        FROM HY_WARN_RECORD A
                 LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
                 LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = LEFT(B.ADCD, 6) + '000000000'
                 LEFT JOIN HY_WARN_INDEX D ON D.STCD = A.STCD
                 LEFT JOIN HY_WARN_UNIT E ON E.CODE = D.RELEASE_UNIT
        WHERE WARNID = #{warnId}
    </select>
    <select id="getFileInfo" resultType="com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnFileResponse">
        SELECT WARNID,
               YR,
               [NO],
               FILE_TYPE,
               FILE_TITLE,
               [FILE_NAME],
               FILE_PATH,
               FILE_FLAG,
               SIGNER,
               B.REALNM SIGNER_NAME,
               AUDITOR,
               C.REALNM AUDITOR_NAME,
               EDITOR,
               D.REALNM EDITOR_NAME,
               RECEIVE_DEPT,
               RECEIVE_ADCD,
               SEND_DEPT,
               SENDER,
               SEND_TIME,
               TS
        FROM HY_WARN_FILE A
                 LEFT JOIN BNS_USERINFO_B B ON B.USERID = SIGNER
                 LEFT JOIN BNS_USERINFO_B C ON C.USERID = AUDITOR
                 LEFT JOIN BNS_USERINFO_B D ON D.USERID = EDITOR
        WHERE WARNID = #{warnId}
    </select>
    <select id="getNewFileNo" resultType="java.lang.Integer">
        SELECT ISNULL(MAX([NO]), 0) + 1
        FROM HY_WARN_FILE
        WHERE YR = #{yr}
    </select>
    <select id="getUserList" resultType="com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnUserResponse">
        SELECT A.USERID, REALNM
        FROM BNS_USERINFO_B A
                 LEFT JOIN BNS_USERDEPT_B B ON B.USERID = A.USERID
        WHERE A.STAT = '1'
          AND B.DEPTID = #{deptid}
        ORDER BY REALNM ASC
    </select>
    <select id="getAccept" resultType="com.huitu.cloud.api.ewci.hydrology.entity.HyWarnAccept">
        SELECT TOP (1) DEPT_ID, ADCD
        FROM HY_WARN_ACCEPT
    </select>
    <select id="getDocument" resultType="com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnDocumentResponse">
        SELECT WARNID, [FILE_NAME], FILE_PATH
        FROM HY_WARN_FILE
        WHERE WARNID = #{warnId}
    </select>
    <select id="isRepeatFileNo" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(A.WARNID) > 0 THEN 1 ELSE 0 END
        FROM HY_WARN_FILE A
                 LEFT JOIN HY_WARN_RECORD B ON B.WARNID = A.WARNID
        WHERE B.[STATE] = '2' AND A.WARNID != #{warnId}
          AND EXISTS(SELECT 8 FROM HY_WARN_FILE WHERE WARNID = #{warnId} AND YR = A.YR AND [NO] = A.[NO])
    </select>
    <update id="edit">
        UPDATE HY_WARN_RECORD
        SET STATE = '1',
            TS    = GETDATE()
        WHERE STATE IN ('0', '1')
          AND WARNID = #{warnId}
    </update>
    <update id="ignore">
        UPDATE HY_WARN_RECORD
        SET STATE = '3',
            TS    = GETDATE()
        WHERE STATE IN ('0', '1')
          AND WARNID = #{warnId}
    </update>
    <update id="release">
        UPDATE HY_WARN_RECORD
        SET STATE = '2',
            TS    = GETDATE()
        WHERE STATE = '1'
          AND WARNID = #{warnId}
    </update>
    <update id="saveFile">
        MERGE INTO HY_WARN_FILE AS T
        USING (SELECT #{warnId}      WARNID,
                      #{yr}          YR,
                      #{no}          NO,
                      #{fileType}    FILE_TYPE,
                      #{fileTitle}   FILE_TITLE,
                      #{fileName}    FILE_NAME,
                      #{filePath}    FILE_PATH,
                      #{fileFlag}    FILE_FLAG,
                      #{signer}      SIGNER,
                      #{auditor}     AUDITOR,
                      #{editor}      EDITOR,
                      #{receiveDept} RECEIVE_DEPT,
                      #{receiveAdcd} RECEIVE_ADCD,
                      #{sendDept}    SEND_DEPT,
                      GETDATE()      TS) AS S
        ON T.WARNID = S.WARNID
        WHEN MATCHED THEN
            UPDATE
            SET T.YR           = S.YR,
                T.NO           = S.NO,
                T.FILE_TYPE    = S.FILE_TYPE,
                T.FILE_TITLE   = S.FILE_TITLE,
                T.FILE_NAME    = S.FILE_NAME,
                T.FILE_PATH    = S.FILE_PATH,
                T.FILE_FLAG    = S.FILE_FLAG,
                T.SIGNER       = S.SIGNER,
                T.AUDITOR      = S.AUDITOR,
                T.EDITOR       = S.EDITOR,
                T.RECEIVE_DEPT = S.RECEIVE_DEPT,
                T.RECEIVE_ADCD = S.RECEIVE_ADCD,
                T.SEND_DEPT    = S.SEND_DEPT,
                T.TS           = S.TS
        WHEN NOT MATCHED THEN
            INSERT (WARNID, YR, NO, FILE_TYPE, FILE_TITLE, FILE_NAME, FILE_PATH, FILE_FLAG, SIGNER, AUDITOR, EDITOR,
                    RECEIVE_DEPT, RECEIVE_ADCD, SEND_DEPT, TS)
            VALUES (S.WARNID, S.YR, S.NO, S.FILE_TYPE, S.FILE_TITLE, S.FILE_NAME, S.FILE_PATH, S.FILE_FLAG, S.SIGNER,
                    S.AUDITOR, S.EDITOR, S.RECEIVE_DEPT, S.RECEIVE_ADCD, S.SEND_DEPT, S.TS);
    </update>
    <update id="updateSender">
        UPDATE HY_WARN_FILE
        SET SENDER    = #{sender},
            SEND_TIME = GETDATE(),
            TS        = GETDATE()
        WHERE WARNID = #{warnId}
    </update>
    <delete id="deleteFile">
        DELETE
        FROM HY_WARN_FILE
        WHERE WARNID = #{warnId}
    </delete>
</mapper>
