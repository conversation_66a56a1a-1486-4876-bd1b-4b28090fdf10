package com.huitu.cloud.api.fusion.entity.ext;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 山洪预警短信接收人
 */
@ApiModel(value = "山洪预警短信接收人")
@Data
public class FusionSmsReceiver extends FusionReceiver {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "发送ID")
    @TableField(value = "SEND_ID")
    private String sendId;

    @ApiModelProperty(value = "发送状态，注：0=等待发送、1=发送成功、2=发送失败、9=正在发送")
    @TableField(value = "SEND_STATUS")
    private String sendStatus;

    @ApiModelProperty(value = "发送时间")
    @TableField(value = "SEND_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    @ApiModelProperty(value = "错误代码")
    @TableField(value = "ERROR_CODE")
    private Integer errorCode;

    @ApiModelProperty(value = "错误消息")
    @TableField(value = "ERROR_MESSAGE")
    private String errorMessage;

    @ApiModelProperty(value = "读取状态，注：0=未读、1=已读")
    @TableField(value = "READ_STATUS")
    private String readStatus;

    @ApiModelProperty(value = "确认结果，注：1=正常、2=超时、3=语音")
    @TableField(value = "CONFIRM_RESULT")
    private String confirmResult;

    @ApiModelProperty(value = "确认时间")
    @TableField(value = "CONFIRM_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;
}
