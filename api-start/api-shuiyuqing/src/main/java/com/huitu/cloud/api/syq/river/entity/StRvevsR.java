package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@TableName("ST_RVEVS_R")
@ApiModel(value="StRvevsR对象", description="")
public class StRvevsR extends Model<StRvevsR> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "时间")
    @TableField("IDTM")
    private Date idtm;

    @ApiModelProperty(value = "时段长")
    @TableField("STTDRCD")
    private Integer sttdrcd;

    @ApiModelProperty(value = "最高水位")
    @TableField("HTZ")
    private BigDecimal htz;

    @ApiModelProperty(value = "最高水位出现时间")
    @TableField("HTZTM")
    private LocalDateTime htztm;

    @ApiModelProperty(value = "最大流量")
    @TableField("MXQ")
    private BigDecimal mxq;

    @ApiModelProperty(value = "最大流量出现时间")
    @TableField("MXQTM")
    private LocalDateTime mxqtm;

    @ApiModelProperty(value = "最低水位")
    @TableField("LTZ")
    private BigDecimal ltz;

    @ApiModelProperty(value = "最低水位出现时间")
    @TableField("LTZTM")
    private LocalDateTime ltztm;

    @ApiModelProperty(value = "最小流量")
    @TableField("MNQ")
    private BigDecimal mnq;

    @ApiModelProperty(value = "最小流量出现时间")
    @TableField("MNQTM")
    private LocalDateTime mnqtm;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Date getIdtm() {
        return idtm;
    }

    public void setIdtm(Date idtm) {
        this.idtm = idtm;
    }

    public Integer getSttdrcd() {
        return sttdrcd;
    }

    public void setSttdrcd(Integer sttdrcd) {
        this.sttdrcd = sttdrcd;
    }

    public BigDecimal getHtz() {
        return htz;
    }

    public void setHtz(BigDecimal htz) {
        this.htz = htz;
    }

    public LocalDateTime getHtztm() {
        return htztm;
    }

    public void setHtztm(LocalDateTime htztm) {
        this.htztm = htztm;
    }

    public BigDecimal getMxq() {
        return mxq;
    }

    public void setMxq(BigDecimal mxq) {
        this.mxq = mxq;
    }

    public LocalDateTime getMxqtm() {
        return mxqtm;
    }

    public void setMxqtm(LocalDateTime mxqtm) {
        this.mxqtm = mxqtm;
    }

    public BigDecimal getLtz() {
        return ltz;
    }

    public void setLtz(BigDecimal ltz) {
        this.ltz = ltz;
    }

    public LocalDateTime getLtztm() {
        return ltztm;
    }

    public void setLtztm(LocalDateTime ltztm) {
        this.ltztm = ltztm;
    }

    public BigDecimal getMnq() {
        return mnq;
    }

    public void setMnq(BigDecimal mnq) {
        this.mnq = mnq;
    }

    public LocalDateTime getMnqtm() {
        return mnqtm;
    }

    public void setMnqtm(LocalDateTime mnqtm) {
        this.mnqtm = mnqtm;
    }

}
