package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectShgcCons;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 水毁工程修复建设
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectShgcConsDao {

    /**
     * 判断是否添加 水毁工程修复建设
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectShgcCons> getInspectShgcConsList(@Param("adcd") String adcd, @Param("year") String year, @Param("enCode") String enCode);

    /**
     * 添加 水毁工程修复建设
     *
     * @param entity
     * @return
     */
    int insertInspectShgcCons(BnsInspectShgcCons entity);

    /**
     * 修改 水毁工程修复建设
     *
     * @param entity
     * @return
     */
    int updateInspectShgcCons(BnsInspectShgcCons entity);

    /**
     * 删除 水毁工程修复建设
     *
     * @return
     */
    int deleteInspectShgcCons(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

