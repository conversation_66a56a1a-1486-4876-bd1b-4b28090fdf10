package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@TableName("BSN_ST_DSNFLD")
@ApiModel(value="StDsnfldQuery对象", description="")
public class StDsnfldQuery extends Model<StDsnfldQuery> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    private String stcd;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

}
