package com.huitu.cloud.api.ew.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.ext.EwRiverWarningSending;
import com.huitu.cloud.api.ew.entity.request.EwRiverWarningCorrectRequest;
import com.huitu.cloud.api.ew.entity.request.EwRiverWarningRecordRequest;
import com.huitu.cloud.api.ew.entity.request.EwRiverWarningSendingRequest;
import com.huitu.cloud.api.ew.entity.response.EwRiverWarningRecordResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarningGradeSummaryResponse;
import com.huitu.cloud.api.usif.user.entity.UserInfos;

import java.math.BigDecimal;
import java.util.List;

/**
 * 河道预警服务
 */
public interface EwRiverWarningService {

    /**
     * 获取预警分页列表
     *
     * @param request 请求参数
     * @return 预警分页列表
     **/
    IPage<EwRiverWarningRecordResponse> getPageList(EwRiverWarningRecordRequest request);

    /**
     * 根据预警ID，获取预警记录
     *
     * @param warnId 预警ID
     * @return 预警记录
     */
    EwRiverWarningRecordResponse getRecord(String warnId);

    /**
     * 获取预警发送记录分页列表
     *
     * @param request 请求参数
     * @return 发送记录分页列表
     */
    IPage<EwRiverWarningSending> getSendingPageList(EwRiverWarningSendingRequest<EwRiverWarningSending> request);

    /**
     * 获取预警标识
     *
     * @param msgId 消息ID
     * @return 预警标识
     */
    String getWarningFlag(String msgId);

    /**
     * 河道预警等级统计
     *
     * @param request
     * @return
     */
    List<EwWarningGradeSummaryResponse> getRiverWarningGradeSummary(EwRiverWarningRecordRequest request);

    /**
     * 获取测站最新水位
     *
     * @param stcd 测站编码
     * @return 最新水位
     */
    BigDecimal getLatestZ(String stcd);

    /**
     * 更正预警
     *
     * @param request 请求参数
     * @param user    用户信息
     * @return 受影响的行数
     */
    int correct(EwRiverWarningCorrectRequest request, UserInfos user);
}
