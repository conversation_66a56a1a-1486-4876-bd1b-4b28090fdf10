package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 设计洪水表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@TableName("BSN_ST_DSNFLD")
@ApiModel(value="BSN_ST_DSNFLD对象", description="设计洪水表")
public class StDsnfld extends Model<StDsnfld> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "P1")
    @TableField("P1")
    private Double p1;

    @ApiModelProperty(value = "P2")
    @TableField("P2")
    private Double p2;

    @ApiModelProperty(value = "P3")
    @TableField("P3")
    private Double p3;

    @ApiModelProperty(value = "P5")
    @TableField("P5")
    private Double p5;

    @ApiModelProperty(value = "P10")
    @TableField("P10")
    private Double p10;

    @ApiModelProperty(value = "P20")
    @TableField("P20")
    private Double p20;

    @ApiModelProperty(value = "rvevsList")
    private List<StRvyevsqS> rvevsList;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Double getP1() {
        return p1;
    }

    public void setP1(Double p1) {
        this.p1 = p1;
    }

    public Double getP2() {
        return p2;
    }

    public void setP2(Double p2) {
        this.p2 = p2;
    }

    public Double getP3() {
        return p3;
    }

    public void setP3(Double p3) {
        this.p3 = p3;
    }

    public Double getP5() {
        return p5;
    }

    public void setP5(Double p5) {
        this.p5 = p5;
    }

    public Double getP10() {
        return p10;
    }

    public void setP10(Double p10) {
        this.p10 = p10;
    }

    public Double getP20() {
        return p20;
    }

    public void setP20(Double p20) {
        this.p20 = p20;
    }

    public List<StRvyevsqS> getRvevsList() {
        return rvevsList;
    }

    public void setRvevsList(List<StRvyevsqS> rvevsList) {
        this.rvevsList = rvevsList;
    }

}
