package com.huitu.cloud.api.ew.mapper;

import com.huitu.cloud.api.ew.entity.response.EwShVillageDataResponse;
import com.huitu.cloud.api.ew.entity.response.EwSnapshotIndexResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwSnapshotIndexDao {

    /**
     * 根据预警ID，获取对应的预警指标快照列表
     *
     * @param warnId 预警ID
     * @return 指标快照
     */
    List<EwSnapshotIndexResponse> getSnapshotIndexList(String warnId);

    /**
     * 根据预警ID和预警等级，获取对应的预警指标快照
     *
     * @param warnId      预警ID
     * @param warnGradeId 预警等级ID
     * @return 指标快照
     */
    EwSnapshotIndexResponse getSnapshotIndex(@Param("warnId") String warnId, @Param("warnGradeId") Integer warnGradeId);

    /**
     * 根据消息ID，获取对应的预警影响村屯
     *
     * @param msgId 消息ID
     * @return 影响村屯
     */
    List<EwShVillageDataResponse> getVillageList(String msgId);
}
