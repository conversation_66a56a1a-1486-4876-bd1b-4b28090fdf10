package com.huitu.cloud.api.ew.mapper;

import com.huitu.cloud.api.ew.entity.base.EwCallFeedback;
import com.huitu.cloud.api.ew.entity.ext.EwNoticeReceiver;
import com.huitu.cloud.api.ew.entity.response.EwCallStatisticsResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwCallFeedbackDao {

    /**
     * 根据叫应ID，获取叫应反馈记录
     *
     * @param callId 叫应ID
     * @return 叫应反馈记录
     */
    EwCallFeedback getFeedback(String callId);

    /**
     * 批量插入叫应反馈记录
     *
     * @param feedbacks 叫应反馈记录集合
     * @return 受影响的行数
     */
    int batchInsert(@Param("list") List<EwCallFeedback> feedbacks);

    /**
     * 预警叫应反馈
     *
     * @param params 请求参数
     * @return 受影响的行数
     */
    int confirm(@Param("map") Map<String, Object> params);

    /**
     * 预警人工确认
     *
     * @param callIds 叫应ID集合
     * @return 受影响的行数
     */
    int manualConfirm(@Param("callIds") List<String> callIds);

    /**
     * 获取人工通知接收人列表
     *
     * @param params 查询参数
     * @return 接收人列表
     */
    List<EwNoticeReceiver> getNoticeReceiverList(@Param("map") Map<String, Object> params);

    /**
     * 山洪监测预警叫应失败统计列表
     *
     * @param params 查询参数
     * @return 统计列表
     */
    List<EwCallStatisticsResponse> getCallFailStatsList(@Param("map") Map<String, Object> params);
}
