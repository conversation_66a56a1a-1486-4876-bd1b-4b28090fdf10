package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value="雨量级别统计测站数")
public class RainLevelStNum implements Serializable {
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "0-10级别统计测站数量")
    private int rain1;
    @ApiModelProperty(value = "10-25级别统计测站数量")
    private int rain2;
    @ApiModelProperty(value = "25-50级别统计测站数量")
    private int rain3;
    @ApiModelProperty(value = "50-100级别统计测站数量")
    private int rain4;
    @ApiModelProperty(value = "100-250级别统计测站数量")
    private int rain5;
    @ApiModelProperty(value = "大于250级别统计测站数量")
    private int rain6;
    @ApiModelProperty(value = "所有级别统计测站总数")
    private int total;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public int getRain1() {
        return rain1;
    }

    public void setRain1(int rain1) {
        this.rain1 = rain1;
    }

    public int getRain2() {
        return rain2;
    }

    public void setRain2(int rain2) {
        this.rain2 = rain2;
    }

    public int getRain3() {
        return rain3;
    }

    public void setRain3(int rain3) {
        this.rain3 = rain3;
    }

    public int getRain4() {
        return rain4;
    }

    public void setRain4(int rain4) {
        this.rain4 = rain4;
    }

    public int getRain5() {
        return rain5;
    }

    public void setRain5(int rain5) {
        this.rain5 = rain5;
    }

    public int getRain6() {
        return rain6;
    }

    public void setRain6(int rain6) {
        this.rain6 = rain6;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}
