package com.huitu.cloud.api.shyj.dept.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;
/**
 * <AUTHOR>
 * @since 2019-09-18
 */
public class QueryDept extends PageBean {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "部门名称")
    private String deptNm;
    @ApiModelProperty(value = "上级部门编码")
    private String pid;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getDeptNm() {
        return deptNm;
    }

    public void setDeptNm(String deptNm) {
        this.deptNm = deptNm;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
}
