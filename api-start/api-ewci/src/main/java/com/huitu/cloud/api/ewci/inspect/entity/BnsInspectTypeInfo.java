package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;

import java.io.Serializable;

/**
 * 检查类型清单
 */
@ApiModel(value = "检查类型清单")
public class BnsInspectTypeInfo implements Serializable {

    @ApiModelProperty(value = "检查类型编码")
    @TableField(value = "INSPECT_CODE")
    private String inspectCode;

    @ApiModelProperty(value = "检查类型名称")
    @TableField(value = "INSPECT_NAME")
    private String inspectName;

    @ApiModelProperty(value = "序号")
    @TableField(value = "SNO")
    private Integer sno;

    @ApiModelProperty(value = "状态(0未启用1已启用)")
    @TableField(value = "STATE")
    private String state;

    public String getInspectCode() {
        return inspectCode;
    }

    public void setInspectCode(String inspectCode) {
        this.inspectCode = inspectCode;
    }

    public String getInspectName() {
        return inspectName;
    }

    public void setInspectName(String inspectName) {
        this.inspectName = inspectName;
    }

    public Integer getSno() {
        return sno;
    }

    public void setSno(Integer sno) {
        this.sno = sno;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
