package com.huitu.cloud.api.datamanager.ststop.entity;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;


public class BsnStStopTab implements Serializable {
    private String tbnm;
    private String tbenm;
    private String type;

    public String getTbnm() {
        return tbnm;
    }

    public void setTbnm(String tbnm) {
        this.tbnm = tbnm;
    }

    public String getTbenm() {
        return tbenm;
    }

    public void setTbenm(String tbenm) {
        this.tbenm = tbenm;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
