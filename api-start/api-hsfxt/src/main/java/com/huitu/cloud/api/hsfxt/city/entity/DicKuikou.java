package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 溃口信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@TableName("Dic_KuiKou")
@ApiModel(value = "DicKuikou对象", description = "溃口信息")
public class DicKuikou extends Model<DicKuikou> {

    @ApiModelProperty(value = "溃口编码")
    private String kuikouid;

    @ApiModelProperty(value = "溃口名称")
    private String kuikoumingcheng;

    @ApiModelProperty(value = "排序号")
    private Integer kuikousort;

    @ApiModelProperty(value = "分区编码")
    private String jisuanfenquid;

    public String getKuikouid() {
        return kuikouid;
    }

    public void setKuikouid(String kuikouid) {
        this.kuikouid = kuikouid;
    }

    public String getKuikoumingcheng() {
        return kuikoumingcheng;
    }

    public void setKuikoumingcheng(String kuikoumingcheng) {
        this.kuikoumingcheng = kuikoumingcheng;
    }

    public Integer getKuikousort() {
        return kuikousort;
    }

    public void setKuikousort(Integer kuikousort) {
        this.kuikousort = kuikousort;
    }

    public String getJisuanfenquid() {
        return jisuanfenquid;
    }

    public void setJisuanfenquid(String jisuanfenquid) {
        this.jisuanfenquid = jisuanfenquid;
    }
}
