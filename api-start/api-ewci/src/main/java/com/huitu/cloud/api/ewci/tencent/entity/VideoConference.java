package com.huitu.cloud.api.ewci.tencent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 音视频会议表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@TableName("BSN_VIDEO_CONFERENCE")
@ApiModel("音视频会议表")
public class VideoConference extends Model<VideoConference> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "会议房间号")
    @TableId(value = "room_id", type = IdType.NONE)
    private String roomId;

    @ApiModelProperty(value = "会议标题")
    private String title;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "创建人id")
    private String createId;

    @ApiModelProperty(value = "创建人名称/主持人名称")
    private String createName;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "会议状态：0: 新增   1：房间创建 2：房间解散  3：会议删除（逻辑删除）")
    private Integer status;

    @TableField(exist = false)
    @ApiModelProperty(value = "时长")
    private Long duration;

    @TableField(exist = false)
    @ApiModelProperty(value = "参会人员ID集合")
    private String members;

    @TableField(exist = false)
    @ApiModelProperty(value = "参会人员姓名集合")
    private String memberNames;

    @TableField(exist = false)
    @ApiModelProperty(value = "参会人员")
    private List<VideoPersonRecord> videoPersonRecord;

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMembers() {
        return members;
    }

    public void setMembers(String members) {
        this.members = members;
    }

    public String getMemberNames() {
        return memberNames;
    }

    public void setMemberNames(String memberNames) {
        this.memberNames = memberNames;
    }

    @Override
    public String toString() {
        return "VideoConference{" +
                "roomId='" + roomId + '\'' +
                ", title='" + title + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", createId='" + createId + '\'' +
                ", createName='" + createName + '\'' +
                ", adcd='" + adcd + '\'' +
                ", adnm='" + adnm + '\'' +
                ", status=" + status +
                ", duration=" + duration +
                ", members='" + members + '\'' +
                ", memberNames='" + memberNames + '\'' +
                ", videoPersonRecord=" + videoPersonRecord +
                '}';
    }

    public List<VideoPersonRecord> getVideoPersonRecord() {
        return videoPersonRecord;
    }

    public void setVideoPersonRecord(List<VideoPersonRecord> videoPersonRecord) {
        this.videoPersonRecord = videoPersonRecord;
    }
}
