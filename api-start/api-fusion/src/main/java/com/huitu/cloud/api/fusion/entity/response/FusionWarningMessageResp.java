package com.huitu.cloud.api.fusion.entity.response;

import com.huitu.cloud.api.fusion.entity.ext.FusionReceiver;
import com.huitu.cloud.api.fusion.entity.ext.FusionWarningMessage;

import java.util.List;

public class FusionWarningMessageResp <T extends FusionReceiver> extends FusionWarningMessage {

    private static final long serialVersionUID = 1L;

    public FusionWarningMessageResp() {
        super();
    }

    public FusionWarningMessageResp(FusionWarningMessage message) {
        super(message);
    }

    public FusionWarningMessageResp(FusionWarningMessage message, List<T> receivers) {
        super(message);

        this.receivers = receivers;
    }

    /**
     * 接收人集合
     */
    private List<T> receivers;

    public List<T> getReceivers() {
        return receivers;
    }

    public void setReceivers(List<T> receivers) {
        this.receivers = receivers;
    }
}
