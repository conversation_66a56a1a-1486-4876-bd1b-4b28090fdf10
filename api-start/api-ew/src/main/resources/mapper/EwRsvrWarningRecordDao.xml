<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwRsvrWarningRecordDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ew.entity.response.EwRsvrWarningRecordResponse" useCache="false">
        SELECT A.WARN_ID, E.ADCD XADCD, E.ADNM XADNM, C.ADCD, D.ADNM, A.STCD, B.STNM, A.RES_CODE, F.RES_NAME, F.RES_LOC, F.ENG_SCAL,
        A.WARN_TIME, A<PERSON>_DESC, A.RZ, A<PERSON>WARN_GRADE_ID, <PERSON><PERSON>R<PERSON>_INDEX, A<PERSON>EXCEED_WARN_INDEX, A<PERSON>[STATE], C.PLGTD LGTD, C.PLTTD LTTD,
        A.LATEST_TIME FROM EW_RSVR_WARNING_RECORD A
        LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
        LEFT JOIN ATT_RES_BASE F ON F.RES_CODE = A.RES_CODE
        WHERE LEFT(C.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">AND LEFT(C.ADCD, 6) NOT IN ('220581')</if>
        <if test="map.bscd != null and map.bscd != ''">
            AND EXISTS(SELECT 8 FROM BSN_BAS_ST WHERE STCD = A.STCD AND BAS_CODE = #{map.bscd})
        </if>
        AND A.WARN_TIME >= #{map.stm}
        <if test="map.etm != null">AND A.WARN_TIME &lt;= #{map.etm}</if>
        <if test="map.stadtps != null and map.stadtps != ''">AND CHARINDEX(C.STADTP, #{map.stadtps}) > 0</if>
        <if test="map.states != null and map.states != ''">AND CHARINDEX(A.[STATE], #{map.states}) > 0</if>
        AND EXISTS(SELECT 8 FROM ATT_RES_BASE WHERE RES_CODE = A.RES_CODE)
        <if test="map.scales != null and map.scales != ''">AND CHARINDEX(F.ENG_SCAL, #{map.scales}) > 0</if>
        ORDER BY A.WARN_TIME DESC, A.WARN_GRADE_ID ASC, F.ENG_SCAL ASC
    </select>
    <select id="getRecord" resultType="com.huitu.cloud.api.ew.entity.response.EwRsvrWarningRecordResponse">
        SELECT A.WARN_ID,
               E.ADCD  XADCD,
               E.ADNM  XADNM,
               C.ADCD,
               D.ADNM,
               A.STCD,
               B.STNM,
               A.RES_CODE,
               F.RES_NAME,
               F.RES_LOC,
               F.ENG_SCAL,
               A.WARN_TIME,
               A.WARN_DESC,
               A.RZ,
               A.WARN_GRADE_ID,
               A.WARN_INDEX,
               A.EXCEED_WARN_INDEX,
               A.[STATE],
               C.PLGTD LGTD,
               C.PLTTD LTTD,
               A.LATEST_TIME
        FROM EW_RSVR_WARNING_RECORD A
                 LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
                 LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
                 LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
                 LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
                 LEFT JOIN ATT_RES_BASE F ON F.RES_CODE = A.RES_CODE
        WHERE A.WARN_ID = #{warnId}
    </select>
    <select id="getRsvrWarningGradeSummary"
            resultType="com.huitu.cloud.api.ew.entity.response.EwWarningGradeSummaryResponse">
        SELECT A.ADCD, A.ADNM,ISNULL(C.ONE, 0) ONE, ISNULL(C.TWO, 0) TWO,ISNULL(C.THREE, 0) THREE,ISNULL(C.FOUR, 0) FOUR
        FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.ONE) ONE, SUM(T.TWO) TWO,SUM(T.THREE) THREE,SUM(T.FOUR) FOUR FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, A.WARN_ID, ONE, TWO, THREE, FOUR FROM (
        SELECT WARN_ID, B.ADCD, WARN_GRADE_ID,A.WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        LEFT JOIN ATT_RES_BASE D ON D.RES_CODE = A.RES_CODE
        <where>
            <if test="map.stadtps != null and map.stadtps != ''">AND CHARINDEX(C.STADTP, #{map.stadtps}) > 0</if>
            <if test="map.scales != null and map.scales != ''">AND CHARINDEX(D.ENG_SCAL, #{map.scales}) > 0</if>
            <if test="map.states != null and map.states != ''">AND CHARINDEX(A.[STATE], #{map.states}) > 0</if>
        </where>
        ) A
        LEFT JOIN (SELECT WARN_ID, 1 ONE FROM EW_RSVR_WARNING_RECORD WHERE WARN_GRADE_ID = 1) B ON B.WARN_ID = A.WARN_ID
        LEFT JOIN (SELECT WARN_ID, 1 TWO FROM EW_RSVR_WARNING_RECORD WHERE WARN_GRADE_ID = 2) C ON C.WARN_ID = A.WARN_ID
        LEFT JOIN (SELECT WARN_ID, 1 THREE FROM EW_RSVR_WARNING_RECORD WHERE WARN_GRADE_ID = 3) D ON D.WARN_ID = A.WARN_ID
        LEFT JOIN (SELECT WARN_ID, 1 FOUR FROM EW_RSVR_WARNING_RECORD WHERE WARN_GRADE_ID = 4) E ON E.WARN_ID = A.WARN_ID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, SUM(ONE) ONE, SUM(TWO) TWO, SUM(THREE) THREE, SUM(FOUR) FOUR FROM (
            SELECT WARN_ID, B.ADCD, WARN_GRADE_ID, A.WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
            LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
            LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
            LEFT JOIN ATT_RES_BASE D ON D.RES_CODE = A.RES_CODE
            WHERE LEFT(B.ADCD, 6) = '220581'
            <if test="map.stadtps != null and map.stadtps != ''">AND CHARINDEX(C.STADTP, #{map.stadtps}) > 0</if>
            <if test="map.scales != null and map.scales != ''">AND CHARINDEX(D.ENG_SCAL, #{map.scales}) > 0</if>
            <if test="map.states != null and map.states != ''">AND CHARINDEX(A.[STATE], #{map.states}) > 0</if>
            ) A
            LEFT JOIN (SELECT WARN_ID, 1 ONE FROM EW_RSVR_WARNING_RECORD WHERE WARN_GRADE_ID = 1) B ON B.WARN_ID = A.WARN_ID
            LEFT JOIN (SELECT WARN_ID, 1 TWO FROM EW_RSVR_WARNING_RECORD WHERE WARN_GRADE_ID = 2) C ON C.WARN_ID = A.WARN_ID
            LEFT JOIN (SELECT WARN_ID, 1 THREE FROM EW_RSVR_WARNING_RECORD WHERE WARN_GRADE_ID = 3) D ON D.WARN_ID = A.WARN_ID
            LEFT JOIN (SELECT WARN_ID, 1 FOUR FROM EW_RSVR_WARNING_RECORD WHERE WARN_GRADE_ID = 4) E ON E.WARN_ID = A.WARN_ID
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getLatestRz" resultType="java.math.BigDecimal" useCache="false">
        SELECT TOP (1) RZ
        FROM ST_RSVR_R
        WHERE TM >= DATEADD(HOUR, -24, GETDATE())
          AND STCD = #{stcd}
        ORDER BY TM DESC
    </select>
    <select id="getCorrectMessage" resultType="com.huitu.cloud.api.ew.entity.sms.SmsMessage" useCache="false">
        SELECT NEWID()                                     MSG_ID,
               A.WARN_ID                                   BUSINESS_KEY,
               '0'                                         SMS_TYPE,
               ('resName=' + LTRIM(RTRIM(B.RES_NAME)) +
                '&amp;adnm=' + LTRIM(RTRIM(C.ADNM)) +
                '&amp;rz=' + FORMAT(#{map.latestRz}, '###0.##') +
                '&amp;grade=' + (CASE
                                     WHEN WARN_GRADE_ID = 1 THEN '坝顶高程'
                                     WHEN WARN_GRADE_ID = 2 THEN '校核水位'
                                     WHEN WARN_GRADE_ID = 3 THEN '设计水位'
                                     ELSE '汛限水位' END)) SMS_CONTENT
        FROM EW_RSVR_WARNING_RECORD A
                 LEFT JOIN ATT_RES_BASE B ON B.RES_CODE = A.RES_CODE
                 LEFT JOIN MDT_ADCDINFO_B C ON EXISTS(SELECT 8 FROM REL_RES_AD WHERE RES_CODE = A.RES_CODE AND AD_CODE = C.ADCD)
        WHERE A.WARN_ID = #{map.warnId}
    </select>
    <select id="getCorrectReceivers" resultType="com.huitu.cloud.api.ew.entity.sms.SmsReceiver" useCache="false">
        SELECT PHONE_NO, RECEIVER
        FROM SMS_SENDING A
                 LEFT JOIN SMS_MESSAGE B ON B.MSG_ID = A.MSG_ID
        WHERE A.[STATUS] = '1'
          AND EXISTS(SELECT 8
                     FROM EW_RSVR_WARNING_MESSAGE
                     WHERE PUSH_MODE = '1'
                       AND WARN_ID = #{warnId}
                       AND MSG_ID = B.BUSINESS_KEY)
        ORDER BY PHONE_NO ASC
    </select>
    <update id="correct">
        UPDATE EW_RSVR_WARNING_RECORD
        SET [STATE]     = '3',
            LATEST_TIME = GETDATE()
        WHERE [STATE] IN ('0', '1')
          AND WARN_ID = #{map.warnId}
    </update>
</mapper>