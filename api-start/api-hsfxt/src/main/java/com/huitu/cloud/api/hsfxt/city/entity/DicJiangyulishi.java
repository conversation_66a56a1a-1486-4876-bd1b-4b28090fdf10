package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 降雨历时
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@TableName("Dic_JiangYuLiShi")
@ApiModel(value="DicJiangyulishi对象", description="降雨历时")
public class DicJiangyulishi extends Model<DicJiangyulishi> {

    @ApiModelProperty(value = "排序号")
    private Integer sortid;

    @ApiModelProperty(value = "降雨历时编码")
    private String jiangyulishiid;

    @ApiModelProperty(value = "降雨历时名称")
    private String jiangyulishimingcheng;

    @ApiModelProperty(value = "暴雨量级编码")
    private String baoyuliangjiid;

    @ApiModelProperty(value = "备注")
    private String beizhu;

    public Integer getSortid() {
        return sortid;
    }

    public void setSortid(Integer sortid) {
        this.sortid = sortid;
    }

    public String getJiangyulishiid() {
        return jiangyulishiid;
    }

    public void setJiangyulishiid(String jiangyulishiid) {
        this.jiangyulishiid = jiangyulishiid;
    }

    public String getJiangyulishimingcheng() {
        return jiangyulishimingcheng;
    }

    public void setJiangyulishimingcheng(String jiangyulishimingcheng) {
        this.jiangyulishimingcheng = jiangyulishimingcheng;
    }

    public String getBaoyuliangjiid() {
        return baoyuliangjiid;
    }

    public void setBaoyuliangjiid(String baoyuliangjiid) {
        this.baoyuliangjiid = baoyuliangjiid;
    }

    public String getBeizhu() {
        return beizhu;
    }

    public void setBeizhu(String beizhu) {
        this.beizhu = beizhu;
    }
}
