************ - - [19/Jun/2025:14:38:12 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [19/Jun/2025:14:38:12 +0800] "GET /login HTTP/1.1" 302 -
************ - - [19/Jun/2025:14:38:12 +0800] "GET /login?code=69xW2V&state=K6900m HTTP/1.1" 302 -
************ - - [19/Jun/2025:14:38:12 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [19/Jun/2025:14:38:14 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750315094433 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:15 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750315094433 HTTP/1.1" 200 552
************ - - [19/Jun/2025:14:38:15 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750315095572 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:15 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750315095572 HTTP/1.1" 200 61649
************ - - [19/Jun/2025:14:38:15 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750315095791 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:15 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750315095791 HTTP/1.1" 200 10388
************ - - [19/Jun/2025:14:38:15 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750315095956 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:15 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750315095956 HTTP/1.1" 200 2009
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750315096149 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-16+14:38:16&etm=&_timer304=1750315096149 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750315096149 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750315096149 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-19+08:00&etm=2025-06-19+15:00&filterCnt=6&_timer304=1750315096149 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750315096149 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750315096149 HTTP/1.1" 200 1482
************ - - [19/Jun/2025:14:38:16 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [19/Jun/2025:14:38:16 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [19/Jun/2025:14:38:16 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750315096149 HTTP/1.1" 200 166
************ - - [19/Jun/2025:14:38:16 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-16+14:38:16&etm=&_timer304=1750315096149 HTTP/1.1" 200 156
************ - - [19/Jun/2025:14:38:16 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-19+08:00&etm=2025-06-19+15:00&filterCnt=6&_timer304=1750315096149 HTTP/1.1" 200 164
************ - - [19/Jun/2025:14:38:16 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [19/Jun/2025:14:38:16 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [19/Jun/2025:14:38:16 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750315096149 HTTP/1.1" 200 169
************ - - [19/Jun/2025:14:38:16 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750315096149 HTTP/1.1" 200 13016
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-19&_timer304=1750315096629 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/base/saas/token?_timer304=1750315096630 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750315096642 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [19/Jun/2025:14:38:17 +0800] "GET /api/base/saas/token?_timer304=1750315096630 HTTP/1.1" 200 411
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1951
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 2315
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [19/Jun/2025:14:38:17 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750315096642 HTTP/1.1" 200 2009
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [19/Jun/2025:14:38:17 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [19/Jun/2025:14:38:18 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 302
************ - - [19/Jun/2025:14:38:19 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-19&_timer304=1750315096629 HTTP/1.1" 200 441
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/ewci/base/mal/write/371?_timer304=1750315100695 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:20 +0800] "GET /api/ewci/base/mal/write/371?_timer304=1750315100695 HTTP/1.1" 200 146
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-16+14:38:20&etm=&_timer304=1750315100943 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750315100943 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-19+08:00&etm=2025-06-19+15:00&filterCnt=6&_timer304=1750315100943 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750315100943 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-bnolist?adcd=220000000000000&_timer304=1750315100943 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750315100943 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315100943 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [19/Jun/2025:14:38:20 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [19/Jun/2025:14:38:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [19/Jun/2025:14:38:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [19/Jun/2025:14:38:20 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-16+14:38:20&etm=&_timer304=1750315100943 HTTP/1.1" 200 156
************ - - [19/Jun/2025:14:38:20 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750315100943 HTTP/1.1" 200 166
************ - - [19/Jun/2025:14:38:20 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-19+08:00&etm=2025-06-19+15:00&filterCnt=6&_timer304=1750315100943 HTTP/1.1" 200 164
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750315100965 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:20 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750315100965 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750315100943 HTTP/1.1" 200 169
************ - - [19/Jun/2025:14:38:21 +0800] "GET /api/ewci/project/yxjsindex-benefit-bnolist?adcd=220000000000000&_timer304=1750315100943 HTTP/1.1" 200 498
************ - - [19/Jun/2025:14:38:21 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750315100965 HTTP/1.1" 200 12285
************ - - [19/Jun/2025:14:38:21 +0800] "GET /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315100943 HTTP/1.1" 200 11871
************ - - [19/Jun/2025:14:38:21 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750315100965 HTTP/1.1" 200 12285
************ - - [19/Jun/2025:14:38:21 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750315100943 HTTP/1.1" 200 13016
************ - - [19/Jun/2025:14:38:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750315104280 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315104281 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750315104280 HTTP/1.1" 200 160
************ - - [19/Jun/2025:14:38:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315104281 HTTP/1.1" 200 159
************ - - [19/Jun/2025:14:38:58 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-addorupdate/ HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:58 +0800] "POST /api/ewci/project/yxjsindex-benefit-addorupdate/ HTTP/1.1" 200 169
************ - - [19/Jun/2025:14:38:58 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315138129 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:38:58 +0800] "GET /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315138129 HTTP/1.1" 200 12267
************ - - [19/Jun/2025:14:39:06 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315146791 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:06 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-bnolist?adcd=220000000000000&_timer304=1750315146791 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:06 +0800] "GET /api/ewci/project/yxjsindex-benefit-bnolist?adcd=220000000000000&_timer304=1750315146791 HTTP/1.1" 200 498
************ - - [19/Jun/2025:14:39:06 +0800] "GET /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315146791 HTTP/1.1" 200 12267
************ - - [19/Jun/2025:14:39:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750315146832 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:06 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-16+14:39:06&etm=&_timer304=1750315146832 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-19+08:00&etm=2025-06-19+15:00&filterCnt=6&_timer304=1750315146832 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:06 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750315146832 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750315146832 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:06 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [19/Jun/2025:14:39:06 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [19/Jun/2025:14:39:06 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [19/Jun/2025:14:39:06 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-16+14:39:06&etm=&_timer304=1750315146832 HTTP/1.1" 200 156
************ - - [19/Jun/2025:14:39:06 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750315146832 HTTP/1.1" 200 166
************ - - [19/Jun/2025:14:39:06 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [19/Jun/2025:14:39:06 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-19+08:00&etm=2025-06-19+15:00&filterCnt=6&_timer304=1750315146832 HTTP/1.1" 200 164
************ - - [19/Jun/2025:14:39:06 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750315146832 HTTP/1.1" 200 169
************ - - [19/Jun/2025:14:39:06 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750315146832 HTTP/1.1" 200 13016
************ - - [19/Jun/2025:14:39:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750315156669 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315156675 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750315156669 HTTP/1.1" 200 160
************ - - [19/Jun/2025:14:39:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315156675 HTTP/1.1" 200 159
************ - - [19/Jun/2025:14:39:24 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-bnolist?adcd=220000000000000&_timer304=1750315164956 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:24 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315164956 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:24 +0800] "GET /api/ewci/project/yxjsindex-benefit-bnolist?adcd=220000000000000&_timer304=1750315164956 HTTP/1.1" 200 498
************ - - [19/Jun/2025:14:39:24 +0800] "GET /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315164956 HTTP/1.1" 200 12267
************ - - [19/Jun/2025:14:39:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750315164992 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:24 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-16+14:39:24&etm=&_timer304=1750315164992 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-19+08:00&etm=2025-06-19+15:00&filterCnt=6&_timer304=1750315164992 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:24 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750315164992 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750315164992 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:25 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [19/Jun/2025:14:39:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [19/Jun/2025:14:39:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750315164992 HTTP/1.1" 200 166
************ - - [19/Jun/2025:14:39:25 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [19/Jun/2025:14:39:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [19/Jun/2025:14:39:25 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-16+14:39:24&etm=&_timer304=1750315164992 HTTP/1.1" 200 156
************ - - [19/Jun/2025:14:39:25 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-19+08:00&etm=2025-06-19+15:00&filterCnt=6&_timer304=1750315164992 HTTP/1.1" 200 164
************ - - [19/Jun/2025:14:39:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750315164992 HTTP/1.1" 200 169
************ - - [19/Jun/2025:14:39:25 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750315164992 HTTP/1.1" 200 13016
************ - - [19/Jun/2025:14:39:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750315174835 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315174841 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750315174835 HTTP/1.1" 200 160
************ - - [19/Jun/2025:14:39:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315174841 HTTP/1.1" 200 159
************ - - [19/Jun/2025:14:39:57 +0800] "POST /api/ewci/project/yxjsindex-benefit-addorupdate/ HTTP/1.1" 200 169
************ - - [19/Jun/2025:14:39:57 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315197511 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:39:57 +0800] "GET /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-19&etm=2025-06-20&type=1&year=2025&_timer304=1750315197511 HTTP/1.1" 200 12267
************ - - [19/Jun/2025:14:44:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750315464941 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:44:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750315464941 HTTP/1.1" 200 160
************ - - [19/Jun/2025:14:44:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315475848 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:44:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315475848 HTTP/1.1" 200 159
************ - - [19/Jun/2025:14:49:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750315764849 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:49:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750315764849 HTTP/1.1" 200 160
************ - - [19/Jun/2025:14:49:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315776844 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:49:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750315776844 HTTP/1.1" 200 159
************ - - [19/Jun/2025:14:54:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750316064845 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:54:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750316064845 HTTP/1.1" 200 160
************ - - [19/Jun/2025:14:54:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750316077709 HTTP/1.1" 200 -
************ - - [19/Jun/2025:14:54:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750316077709 HTTP/1.1" 200 159
