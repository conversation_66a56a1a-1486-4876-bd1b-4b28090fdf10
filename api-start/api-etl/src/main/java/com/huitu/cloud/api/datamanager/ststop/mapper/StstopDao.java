package com.huitu.cloud.api.datamanager.ststop.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huitu.cloud.api.datamanager.ststop.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 站点屏蔽记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
public interface StstopDao extends BaseMapper<BsnStstopr> {
   /**
    * 查询屏蔽站点记录
    * @param param
    * @return
    */
   List<StstopVo> getInfoByParam(Map<String, Object> param);

   /**
    * 根据测站编码与要素查询屏蔽记录信息
    * @param stcd
    * @param tbnm
    * @return
    */
   List<BsnStstoprEx> getOneStstop(@Param("stcd") String stcd, @Param("tbnm") String tbnm);

   /**
    * 根据测站编码与要素关闭站点的屏蔽
    * @param stcd
    * @param tbnm
    * @return
    */
   int colseStStop(@Param("stcd") String stcd, @Param("tbnm") String tbnm);
   /**
    * 根据测站关闭站点的所有要素屏蔽
    * @param stcd
    * @return
    */
   int colseALLStStop(@Param("stcd") String stcd);

   int updateEtm(@Param("stcd") String stcd, @Param("etm") Date etm);

   /**
    * 更新计划性任务的结束时间
    * @param stcd
    * @param etm
    * @param tbnm
    * @return
    */
   int updateEtmByTbnm(@Param("stcd") String stcd, @Param("etm") Date etm, @Param("tbnm") String tbnm);

   /**
    * 根据测站与要素查询 屏蔽的记录
    * @param stcd
    * @param tbnm
    * @return
    */
   int getCountByStcdAndTbnm(@Param("stcd") String stcd, @Param("tbnm") String tbnm);

   /**
    * 根据要素
    * @param type
    * @return
    */
   List<BsnStStopTab> getTabByType(@Param("type") String type);
   /**
    * 查询测站扩展信息表中的测站类型
    * @param stcd
    * @return
    */
   String getStType(@Param("stcd") String stcd);

   void updateStType(@Param("type") String type, @Param("stcd") String stcd);

   void deleteFactor(@Param("stcd") String stcd);

   void deleteStcdFactor(@Param("stcd") String stcd, @Param("tbnm") String tbnm);

   void insertBsnStstopexr(BsnStstopexr bsnStstopexr);
}
