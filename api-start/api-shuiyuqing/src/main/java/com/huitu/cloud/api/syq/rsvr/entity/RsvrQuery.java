package com.huitu.cloud.api.syq.rsvr.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class RsvrQuery  extends PageBean {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "测站归属类型 1、水文 2、山洪 4、小水库报讯 ")
    private List<String> stType;
    @ApiModelProperty(value = "政区编码",required = true)
    private String adcd;
    @ApiModelProperty(value = "流域编码")
    private String bscd;
    @ApiModelProperty(value = "流域名称")
    private String bsnm;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "超汛限标志  true： 超汛限 false 全部数据")
    private boolean warnFlag;
    @ApiModelProperty(value = "开敞式溢洪道超汛限标志  true： 开敞式溢洪道超汛限 false 全部数据")
    private boolean warnOpenFlag;
    @ApiModelProperty(value = "运管标志  true： 运管 false 全部数据")
    private boolean ygFlag;
    @ApiModelProperty(value = "显示数据控制 true：只显示有数据的 false 显示全部(暂不使用！！！)")
    private boolean dataFlag;

    @ApiModelProperty(value = "水库类型  大型：4,5 中型 :3, 1： 小(二)型  2 小（一）型 多个值用,隔开 例：1,2,3 ")
    private String rvType;

    @ApiModelProperty(value = "是否是辖区外(用户登录以后有用) 1：辖区内，2：辖区外")
    private List<String> isOut;

    @ApiModelProperty(value = "是否关注 1：关注，2：未关注")
    private List<String> isFollow;

    @ApiModelProperty(value = "水位差(人工&自动)")
    private Double drz;
    @ApiModelProperty(value = "仅有视频标志  true： 仅有视频")
    private boolean videoFlag;

    public boolean isYgFlag() {
        return ygFlag;
    }

    public void setYgFlag(boolean ygFlag) {
        this.ygFlag = ygFlag;
    }

    public List<String> getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(List<String> isFollow) {
        this.isFollow = isFollow;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public boolean isWarnFlag() {
        return warnFlag;
    }

    public void setWarnFlag(boolean warnFlag) {
        this.warnFlag = warnFlag;
    }

    public boolean isWarnOpenFlag() {
        return warnOpenFlag;
    }

    public void setWarnOpenFlag(boolean warnOpenFlag) {
        this.warnOpenFlag = warnOpenFlag;
    }

    public boolean isDataFlag() {
        return dataFlag;
    }

    public void setDataFlag(boolean dataFlag) {
        this.dataFlag = dataFlag;
    }

    public String getRvType() {
        return rvType;
    }

    public void setRvType(String rvType) {
        this.rvType = rvType;
    }

    public List<String> getIsOut() {
        return isOut;
    }

    public void setIsOut(List<String> isOut) {
        this.isOut = isOut;
    }

    public Double getDrz() {
        return drz;
    }

    public void setDrz(Double drz) {
        this.drz = drz;
    }

    public boolean isVideoFlag() {
        return videoFlag;
    }

    public void setVideoFlag(boolean videoFlag) {
        this.videoFlag = videoFlag;
    }
}
