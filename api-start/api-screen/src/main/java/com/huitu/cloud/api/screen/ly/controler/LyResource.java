package com.huitu.cloud.api.screen.ly.controler;


import com.huitu.api.SuccessResponse;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.screen.fhjz.entity.RainCountVo;
import com.huitu.cloud.api.screen.fhjz.entity.RainVo;
import com.huitu.cloud.api.screen.fhjz.service.RealRainService;
import com.huitu.cloud.api.screen.ly.service.LyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 一体机大屏流域 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-16
 */

@RestController
@Api(tags = "一体机大屏流域")
@RequestMapping("/api/screen/ly")
public class LyResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "B761C064-1554-4164-80CA-B52A17E42144";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private LyService baseService;

    @ApiOperation(value = "基本信息概况统计",notes="基本信息概况统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 【松花江：AAB00006  图们江：AAE00001  辽河：ABA00000  鸭绿江：ABD00000 绥芬河：AAD00001】", required = true, dataType = "String")
    })
    @GetMapping(value = "base-fact")
    public ResponseEntity<SuccessResponse<Map>> queryBaseByRv(@RequestParam String rvCode) throws Exception {
        Map result= baseService.queryRvBaseFact(rvCode);
            return ResponseEntity.ok(
                new SuccessResponse(result));
     }


     // 【松花江：AAB00006  图们江：AAE00001  辽河：ABA00000  鸭绿江：ABD00000 绥芬河：AAD00001】
    @ApiOperation(value = "河流分支查询",notes="河流分支查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String"),
            @ApiImplicitParam(name = "rvName", value = "河流名称 如：松花江", required = true, dataType = "String")
    })
    @GetMapping(value = "rv-branch")
    public ResponseEntity<SuccessResponse<List>> queryRvBranch(String rvCode,String rvName) throws Exception {
        List<Map<String,Object>> result= baseService.queryRvBranch(rvCode,rvName);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "河流河流信息",notes="河流河流信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "queryRv")
    public ResponseEntity<SuccessResponse<Map<String,Object>>> queryRv(String rvCode) throws Exception {
        Map<String,Object> result= baseService.queryRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "查询河流源头信息",notes="查询河流源头信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "queryRvSourLoc")
    public ResponseEntity<SuccessResponse<Map<String,Object>>> queryRvSourLoc(String rvCode) throws Exception {
        Map<String,Object> result= baseService.queryRvSourLoc(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码查询水库数量",notes="根据河流编码查询水库数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "sk-count")
    public ResponseEntity<SuccessResponse<Map>> querySkCountByRv(String rvCode) throws Exception {
        Map<String,Object> result= baseService.querySkCountByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码查询防洪水库数量和总容量",notes="根据河流编码查询防洪水库数量和总容量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String"),
            @ApiImplicitParam(name = "engScal", value = "水库规模 如：'4,5'", required = true, dataType = "String")
    })
    @GetMapping(value = "sk-fh-count-statistic")
    public ResponseEntity<SuccessResponse<Map>> querySkFhByRv(String rvCode,String engScal) throws Exception {
        Map<String,Object> result= baseService.querySkFhByRv(rvCode, engScal);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码水库统计图表",notes="根据河流编码水库统计图表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String"),
            @ApiImplicitParam(name = "engScal", value = "水库规模 如：'4,5'", required = true, dataType = "String")
    })
    @GetMapping(value = "sk-fh-statistic")
    public ResponseEntity<SuccessResponse<List>> statisticSkFhByRv(String rvCode,String engScal) throws Exception {
        List<Map<String,Object>> result= baseService.statisticSkFhByRv(rvCode,engScal);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }


    @ApiOperation(value = "根据河流编码查询堤防公里数",notes="根据河流编码查询堤防公里数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "dike-len")
    public ResponseEntity<SuccessResponse<Map>> queryDikeLenByRv(String rvCode) throws Exception {
        Map<String,Object> result= baseService.queryDikeLenByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码按堤防规模统计堤防数量",notes="根据河流编码按堤防规模统计堤防数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "dike-statistic")
    public ResponseEntity<SuccessResponse<List>> queryDikeStatisticByRv(String rvCode) throws Exception {
        List<Map<String,Object>> result= baseService.queryDikeStatisticByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码查询堤防详情",notes="根据河流编码查询堤防详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "dike-list")
    public ResponseEntity<SuccessResponse<List>> queryDikeListByRv(String rvCode) throws Exception {
        List<Map<String,Object>> result= baseService.queryDikeListByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }




    @ApiOperation(value = "根据河流编码查询河道站数量",notes="根据河流编码查询河道站数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "hd-count")
    public ResponseEntity<SuccessResponse<Map>> queryHdCountByRv(String rvCode) throws Exception {
        Map<String,Object> result= baseService.queryHdCountByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码查询预警河道站数量",notes="根据河流编码查询预警河道站数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "hd-warn-count")
    public ResponseEntity<SuccessResponse<Map>> queryHdWarnCountByRv(String rvCode) throws Exception {
        Map<String,Object> result= baseService.queryHdWarnCountByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码统计河道站水位",notes="根据河流编码统计河道站水位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "hd-statistic")
    public ResponseEntity<SuccessResponse<List>> queryHdStatisticByRv(String rvCode) throws Exception {
        List<Map<String,Object>> result= baseService.queryHdStatisticByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码查询雨量站数量",notes="根据河流编码查询雨量站数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "rain-count")
    public ResponseEntity<SuccessResponse<Map>> queryRainCountByRv(String rvCode) throws Exception {
        Map<String,Object> result= baseService.queryRainCountByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码统计雨量站降水量",notes="根据河流编码统计雨量站降水量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "rain-statistic")
    public ResponseEntity<SuccessResponse<List>> queryRainStatisticByRv(String rvCode) throws Exception {
        List<Map<String,Object>> result= baseService.queryRainStatisticByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }


    @ApiOperation(value = "根据河流编码查询水库站数量",notes="根据河流编码查询水库站数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String"),
            @ApiImplicitParam(name = "engScal", value = "水库规模，多个用逗号分隔 如：1,2,3,4,5", required = true, dataType = "String")
    })
    @GetMapping(value = "sk-station-count")
    public ResponseEntity<SuccessResponse<Map>> querySkStationCountByRv(String rvCode,String engScal) throws Exception {
        Map<String,Object> result= baseService.querySkStationCountByRv(rvCode,engScal);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码查询超汛限水库站数量",notes="根据河流编码查询超汛限水库站数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String"),
            @ApiImplicitParam(name = "engScal", value = "水库规模，多个用逗号分隔 如：1,2,3,4,5", required = true, dataType = "String")
    })
    @GetMapping(value = "sk-station-cxx-count")
    public ResponseEntity<SuccessResponse<Map>> querySkStationCxxCountByRv(String rvCode,String engScal) throws Exception {
        Map<String,Object> result= baseService.querySkStationCxxCountByRv(rvCode,engScal);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据河流编码统计水库站水位",notes="根据河流编码统计水库站水位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String"),
            @ApiImplicitParam(name = "engScal", value = "水库规模，多个用逗号分隔 如：1,2,3,4,5", required = true, dataType = "String")
    })
    @GetMapping(value = "sk-station-statistic")
    public ResponseEntity<SuccessResponse<List>> querySkStationStatisticByRv(String rvCode,String engScal) throws Exception {
        List<Map<String,Object>> result= baseService.querySkStationStatisticByRv(rvCode,engScal);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }


    @ApiOperation(value = "根据河流编码统计重点防洪城市",notes="根据河流编码统计重点防洪城市")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码 如：AAB00006", required = true, dataType = "String")
    })
    @GetMapping(value = "fh-city-list")
    public ResponseEntity<SuccessResponse<List>> queryFhCityListByRv(String rvCode) throws Exception {
        List<Map<String,Object>> result= baseService.queryFhCityListByRv(rvCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

}







