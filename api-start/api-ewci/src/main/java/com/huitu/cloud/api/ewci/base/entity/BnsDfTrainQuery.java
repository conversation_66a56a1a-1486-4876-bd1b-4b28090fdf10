package com.huitu.cloud.api.ewci.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 水旱灾害防御培训
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-31
 */
@TableName("BNS_DF_TRAIN")
@ApiModel(value = "BnsDfTrain对象", description = "水旱灾害防御培训")
public class BnsDfTrainQuery extends PageBean {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private String stm;
    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private String etm;


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }


}