package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 预警上报
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@TableName("WARN_DISASTERSITUATION_REPORT")
@ApiModel(value="WarnDisasterSituationReport对象", description="预警上报表")
public class WarnDisasterSituationReport implements Serializable {
    @ApiModelProperty(value = "预警ID")
    @TableId(value = "WARNID", type = IdType.NONE)
    private String warnId;

    @ApiModelProperty(value = "死亡人数(人)")
    @TableField("PEOPLE_LOSS")
    private int peopleLoss;

    @ApiModelProperty(value = "财产损失(万元)")
    @TableField("PROPERTY_LOSS")
    private double propertyLoss;

    @ApiModelProperty(value = "淹没农田(亩)")
    @TableField("FARMLAND_LOSS")
    private double farmlandLoss;

    @ApiModelProperty(value = "上传图片")
    @TableField("IMGS")
    private String imgs;

    @ApiModelProperty(value = "最后修改人ID")
    @TableField("OCCURUSER")
    private String occurUser;

    @ApiModelProperty(value = "最后修改时间")
    @TableField("OCCURTM")
    private Date occurTM;

    @ApiModelProperty(value = "UUID版本标识")
    @TableField("VCODE")
    private String vcode;

    public String getWarnId() {
        return warnId;
    }
    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public int getPeopleLoss() {
        return peopleLoss;
    }
    public void setPeopleLoss(int peopleLoss) {
        this.peopleLoss = peopleLoss;
    }

    public double getPropertyLoss() {
        return propertyLoss;
    }
    public void setPropertyLoss(double propertyLoss) {
        this.propertyLoss = propertyLoss;
    }

    public double getFarmlandLoss() {
        return farmlandLoss;
    }
    public void setFarmlandLoss(double farmlandLoss) {
        this.farmlandLoss = farmlandLoss;
    }

    public String getImgs() {
        return imgs;
    }
    public void setImgs(String imgs) {
        this.imgs = imgs;
    }

    public String getOccurUser() {
        return occurUser;
    }
    public void setOccurUser(String occurUser) {
        this.occurUser = occurUser;
    }

    public Date getOccurTM() {
        return occurTM;
    }
    public void setOccurTM(Date occurTM) {
        this.occurTM = occurTM;
    }

    public String getVcode() {
        return vcode;
    }
    public void setVcode(String vcode) {
        this.vcode = vcode;
    }
}