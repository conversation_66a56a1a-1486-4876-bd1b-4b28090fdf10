package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
@TableName("Reservoir_rain")
@ApiModel(value="ReservoirRain对象", description="")
public class ReservoirRain extends Model<ReservoirRain> {

    private static final long serialVersionUID=1L;

    @TableField("RAIN1")
    private Double rain1;

    @TableField("RAIN4")
    private Double rain4;

    @TableField("RAIN3")
    private Double rain3;

    @TableField("CHARH4")
    private Double charh4;

    @TableField("RSCD")
    private String rscd;

    @TableField("CHARH1")
    private Double charh1;

    @TableField("TM")
    private LocalDateTime tm;

    @TableField("REALH")
    private Double realh;

    @TableField("RAIN2")
    private Double rain2;

    @TableField("CHARH3")
    private Double charh3;

    @TableField("CHARH2")
    private Double charh2;

    @TableField("STDT")
    private Double stdt;


    public Double getRain1() {
        return rain1;
    }

    public void setRain1(Double rain1) {
        this.rain1 = rain1;
    }

    public Double getRain4() {
        return rain4;
    }

    public void setRain4(Double rain4) {
        this.rain4 = rain4;
    }

    public Double getRain3() {
        return rain3;
    }

    public void setRain3(Double rain3) {
        this.rain3 = rain3;
    }

    public Double getCharh4() {
        return charh4;
    }

    public void setCharh4(Double charh4) {
        this.charh4 = charh4;
    }

    public String getRscd() {
        return rscd;
    }

    public void setRscd(String rscd) {
        this.rscd = rscd;
    }

    public Double getCharh1() {
        return charh1;
    }

    public void setCharh1(Double charh1) {
        this.charh1 = charh1;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public Double getRealh() {
        return realh;
    }

    public void setRealh(Double realh) {
        this.realh = realh;
    }

    public Double getRain2() {
        return rain2;
    }

    public void setRain2(Double rain2) {
        this.rain2 = rain2;
    }

    public Double getCharh3() {
        return charh3;
    }

    public void setCharh3(Double charh3) {
        this.charh3 = charh3;
    }

    public Double getCharh2() {
        return charh2;
    }

    public void setCharh2(Double charh2) {
        this.charh2 = charh2;
    }

    public Double getStdt() {
        return stdt;
    }

    public void setStdt(Double stdt) {
        this.stdt = stdt;
    }

    @Override
    protected Serializable pkVal() {
        return null;
    }

    @Override
    public String toString() {
        return "ReservoirRain{" +
        "rain1=" + rain1 +
        ", rain4=" + rain4 +
        ", rain3=" + rain3 +
        ", charh4=" + charh4 +
        ", rscd=" + rscd +
        ", charh1=" + charh1 +
        ", tm=" + tm +
        ", realh=" + realh +
        ", rain2=" + rain2 +
        ", charh3=" + charh3 +
        ", charh2=" + charh2 +
        ", stdt=" + stdt +
        "}";
    }
}
