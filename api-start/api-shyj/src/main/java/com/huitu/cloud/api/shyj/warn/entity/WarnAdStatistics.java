package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2019-09-17
 */
@ApiModel(value="预警关联政区统计信息", description="预警关联政区统计信息")
public class WarnAdStatistics implements Serializable {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "人口总数")
    private String ptcount;
    @ApiModelProperty(value = "户数总数")
    private String htcount;
    @ApiModelProperty(value = "总土地面积")
    private String ldarea;
    @ApiModelProperty(value = "乡镇个数乡镇总数")
    private String xzhcnt;
    @ApiModelProperty(value = "行政村总数")
    private String xzhccnt;
    @ApiModelProperty(value = "自然村总数")
    private String zrccnt;
    @ApiModelProperty(value = "小流域总数")
    private String wscnt;
    @ApiModelProperty(value = "历史山洪总数")
    private String hsfwatercnt;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPtcount() {
        return ptcount;
    }

    public void setPtcount(String ptcount) {
        this.ptcount = ptcount;
    }

    public String getHtcount() {
        return htcount;
    }

    public void setHtcount(String htcount) {
        this.htcount = htcount;
    }

    public String getLdarea() {
        return ldarea;
    }

    public void setLdarea(String ldarea) {
        this.ldarea = ldarea;
    }

    public String getXzhcnt() {
        return xzhcnt;
    }

    public void setXzhcnt(String xzhcnt) {
        this.xzhcnt = xzhcnt;
    }

    public String getXzhccnt() {
        return xzhccnt;
    }

    public void setXzhccnt(String xzhccnt) {
        this.xzhccnt = xzhccnt;
    }

    public String getZrccnt() {
        return zrccnt;
    }

    public void setZrccnt(String zrccnt) {
        this.zrccnt = zrccnt;
    }

    public String getWscnt() {
        return wscnt;
    }

    public void setWscnt(String wscnt) {
        this.wscnt = wscnt;
    }

    public String getHsfwatercnt() {
        return hsfwatercnt;
    }

    public void setHsfwatercnt(String hsfwatercnt) {
        this.hsfwatercnt = hsfwatercnt;
    }
}
