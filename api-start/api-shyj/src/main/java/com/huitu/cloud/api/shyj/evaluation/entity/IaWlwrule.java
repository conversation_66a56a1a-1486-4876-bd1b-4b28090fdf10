package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警指标水位成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@TableName("IA_A_WLWRULE")
@ApiModel(value="IaAWlwrule对象", description="预警指标水位成果表")
public class IaWlwrule extends Model<IaWlwrule> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableId(value = "ADCD", type = IdType.NONE)
    private String adcd;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "预警等级")
    @TableField("WARNGRADEID")
    private Integer warngradeid;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "演进时间(Min)")
    @TableField("ETIME")
    private Integer etime;

    @ApiModelProperty(value = "距离(km)")
    @TableField("STLEN")
    private Double stlen;

    @ApiModelProperty(value = "水位阈值(mm)")
    @TableField("ZT")
    private Integer zt;

    @ApiModelProperty(value = "分析方法")
    @TableField("CALMATH")
    private String calmath;

    @ApiModelProperty(value = "导入人")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;
    @ApiModelProperty(value = "流域名称")
    private String wsnm;
    @ApiModelProperty(value = "政区名称")
    private String adnm;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public Integer getWarngradeid() {
        return warngradeid;
    }

    public void setWarngradeid(Integer warngradeid) {
        this.warngradeid = warngradeid;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Integer getEtime() {
        return etime;
    }

    public void setEtime(Integer etime) {
        this.etime = etime;
    }

    public Double getStlen() {
        return stlen;
    }

    public void setStlen(Double stlen) {
        this.stlen = stlen;
    }

    public Integer getZt() {
        return zt;
    }

    public void setZt(Integer zt) {
        this.zt = zt;
    }

    public String getCalmath() {
        return calmath;
    }

    public void setCalmath(String calmath) {
        this.calmath = calmath;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.adcd;
    }

    @Override
    public String toString() {
        return "IaWlwrule{" +
        "adcd=" + adcd +
        ", wscd=" + wscd +
        ", warngradeid=" + warngradeid +
        ", stcd=" + stcd +
        ", etime=" + etime +
        ", stlen=" + stlen +
        ", zt=" + zt +
        ", calmath=" + calmath +
        ", signer=" + signer +
        ", audid=" + audid +
        ", status=" + status +
        ", remark=" + remark +
        ", moditime=" + moditime +
        "}";
    }
}
