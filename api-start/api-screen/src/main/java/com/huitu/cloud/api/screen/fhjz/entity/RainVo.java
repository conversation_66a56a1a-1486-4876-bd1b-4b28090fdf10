package com.huitu.cloud.api.screen.fhjz.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value="降雨对象", description="降雨对象")
public class RainVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编号")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "市级名称")
    private String sname;

    @ApiModelProperty(value = "县级名称")
    private String xname;

    @ApiModelProperty(value = "实时累计降雨量")
    private String drp;

    @ApiModelProperty(value = "预测累计降雨量")
    private String rain;

    @ApiModelProperty(value = "东经")
    private Double plgtd;

    @ApiModelProperty(value = "北纬")
    private Double plttd;

    @ApiModelProperty(value = "数据来源")
    private String stadtp;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getSname() {
        return sname;
    }

    public void setSname(String sname) {
        this.sname = sname;
    }

    public String getXname() {
        return xname;
    }

    public void setXname(String xname) {
        this.xname = xname;
    }

    public String getDrp() {
        return drp;
    }

    public void setDrp(String drp) {
        this.drp = drp;
    }

    public String getRain() {
        return rain;
    }

    public void setRain(String rain) {
        this.rain = rain;
    }

    public String getStadtp() {
        return stadtp;
    }

    public void setStadtp(String stadtp) {
        this.stadtp = stadtp;
    }

    public Double getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(Double plgtd) {
        this.plgtd = plgtd;
    }

    public Double getPlttd() {
        return plttd;
    }

    public void setPlttd(Double plttd) {
        this.plttd = plttd;
    }
}
