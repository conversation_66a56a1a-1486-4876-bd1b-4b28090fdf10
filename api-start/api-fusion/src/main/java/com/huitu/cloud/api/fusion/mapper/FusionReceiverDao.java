package com.huitu.cloud.api.fusion.mapper;

import com.huitu.cloud.api.fusion.entity.ext.FusionSmsReceiver;
import com.huitu.cloud.api.fusion.entity.ext.FusionXccReceiver;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FusionReceiverDao {
    /**
     * 根据预警ID，获取预警短信接收人列表
     *
     * @param warnId 预警ID
     * @return 短信接收人列表
     */
    List<FusionSmsReceiver> getSmsReceiverList(String warnId);

    /**
     * 根据预警ID，获取预警语音接收人列表
     *
     * @param warnId 预警ID
     * @return 语音接收人列表
     */
    List<FusionXccReceiver> getXccReceiverList(String warnId);
}
