package com.huitu.cloud.api.screen.fhjz.service.impl;

import com.huitu.cloud.api.screen.fhjz.entity.ReservoirVo;
import com.huitu.cloud.api.screen.fhjz.entity.RsvrDetailVo;
import com.huitu.cloud.api.screen.fhjz.mapper.ReservoirDao;
import com.huitu.cloud.api.screen.fhjz.service.ReservoirService;
import com.huitu.cloud.api.screen.fhjz.utils.DateTimeUtils;
import com.huitu.cloud.util.AdcdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 雨量规则配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-06
 */
@Service
public class ReservoirServiceImpl implements ReservoirService {

    @Autowired
    private ReservoirDao reservoirDao;

    @Override
    public List<ReservoirVo> list(String engScal, boolean cxxOrder,String stm,String etm,String resName) {
//        List<String> queryTimes = DateTimeUtils.queryReservoirTime();
        List<ReservoirVo> list = reservoirDao.getRsvrLatestData(stm, etm, engScal,cxxOrder,resName);
        return list;
    }

    @Override
    public List<RsvrDetailVo> detailList(String engScal, String resName, String adcd,String stm,String etm) {
//        List<String> queryTimes = DateTimeUtils.queryReservoirTime();
        int adLevl = AdcdUtil.getAdLevel(adcd);
        List<RsvrDetailVo> list = reservoirDao.detailList(stm, etm, engScal,resName,adcd,adLevl);
        return list;
    }

    @Override
    public List<Map> querySkStationList(String engScal, String resCode,String stm,String etm) {
//        List<String> queryTimes = DateTimeUtils.queryReservoirTime();
        List<Map> list = reservoirDao.querySkStationList(stm, etm, engScal,resCode);
        return list;
    }

    @Override
    public Map<String, Object> querySkAdcd(String resCode) {
        return reservoirDao.querySkAdcd(resCode);
    }

    @Override
    public String statisticRsvrDetail(String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        int lowLevel = 0;
        String suffix = "";
        if (level >= 6) {
            lowLevel = level + 3;
        } else {
            lowLevel = level + 2;
        }
        if(lowLevel == 2){
            suffix = "0000000000000";
        }else if(lowLevel == 4){
            suffix = "00000000000";
        }else if(lowLevel == 6){
            suffix = "000000000";
        }else if(lowLevel == 9){
            suffix = "000000";
        }
        List<Map>  detailList = reservoirDao.statisticRsvrDetail(adcd,level,lowLevel,suffix);
        String title = "";
        if(level == 2){
            title = "全省";
        }else if(level == 4){
            title = "全市";
        }else if(level == 6){
            title = "全县";
        }
        String totalDetail = "";
        StringBuffer subDetail = new StringBuffer("");
        // 组装前台显示内容
        if(detailList!=null && detailList.size()>0){
            for(Map<String,Object> item:detailList){
                if("SUM".equals(item.get("adnm"))){
                    totalDetail = String.format("水库共%s座，其中大型%s座，中型%s座，小一型%s座，小二型%s座。", String.valueOf(item.get("TOTAL")),String.valueOf(item.get("LEVEL1")),String.valueOf(item.get("LEVEL2")),String.valueOf(item.get("LEVEL3")),String.valueOf(item.get("LEVEL4")));
                }else{
                    subDetail.append(String.format("【%s】水库共%s座，其中大型%s座，中型%s座，小一型%s座，小二型%s座。",String.valueOf(item.get("adnm")), String.valueOf(item.get("TOTAL")),String.valueOf(item.get("LEVEL1")),String.valueOf(item.get("LEVEL2")),String.valueOf(item.get("LEVEL3")),String.valueOf(item.get("LEVEL4"))));
                }
            }
        }
        return title + totalDetail + subDetail.toString();
    }
}
