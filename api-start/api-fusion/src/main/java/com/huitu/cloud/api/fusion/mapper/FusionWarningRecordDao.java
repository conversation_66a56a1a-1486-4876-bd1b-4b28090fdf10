package com.huitu.cloud.api.fusion.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.fusion.entity.response.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface FusionWarningRecordDao {

    /**
     * 获取预警升级分页列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 预警升级分页列表
     **/
    IPage<FusionWarningRecordResp> getPageList(IPage<FusionWarningRecordResp> page, @Param("map") Map<String, Object> params);

    /**
     * 获取山洪雨量升级预警底部统计
     *
     * @param query 查询参数
     * @return 升级预警底部统计数据
     **/
    AscriptionTypeCountResp getAscriptionTypeCount(@Param("map") Map<String, Object> query);

    /**
     * 根据预警ID和预警等级，获取对应的预警监测数据快照
     *
     * @param warnId      预警ID
     * @return 监测数据快照
     */
    FusionSnapshotMonitorResp getSnapshotMonitor(@Param("warnId") String warnId);

    /**
     * 获取山洪雨量升级预警基本信息
     * @param warnId 预警ID
     * @return 山洪雨量升级预警基本信息
     */
    FusionSnapshotIndexResp getSnapshotIndex(@Param("warnId") String warnId);

    /**
     * 获取山洪雨量升级预警发送情况分页列表
     * @param page 分页参数
     * @param params 查询参数
     * @return 山洪雨量升级预警发送情况分页列表
     */
    IPage<FusionWarningSendingResp> getSendingPageList(Page page, @Param("map") Map<String, Object> params);

    /**
     * 获取预警分页列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 预警分页列表
     **/
    IPage<FusionWarningRecordResp> getDetailPageList(Page<FusionWarningRecordResp> page, @Param("map") Map<String, Object> params);

    /**
     * 获取最新的山洪预警分页列表(1、2、3级)
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 预警分页列表
     **/
    IPage<FusionWarningRecordResp> getWarnLatestPageList(IPage<FusionWarningRecordResp> page, @Param("map") Map<String, Object> params);

    /**
     * 获取预警摘要列表
     *
     * @param warnIds 预警ID集合
     * @return 摘要列表
     */
    List<FusionWarningDigestResponse> getDigestList(@Param("warnIds") List<String> warnIds);

    /**
     * 人工确认
     *
     * @param warnIds 预警ID集合
     * @return 受影响的行数
     */
    int manualConfirm(@Param("warnIds") List<String> warnIds);

    /**
     * 根据预警ID，获取预警详情
     *
     * @param warnId 预警ID
     * @return 获取预警详情
     */
    List<FusionWarningDetailResp> getWarnDetail(String warnId);

    /**
     * 根据预警ID，获取预警记录
     *
     * @param warnId      预警ID
     * @return 预警记录
     */
    FusionWarningRecordResp getRecord(String warnId);
}
