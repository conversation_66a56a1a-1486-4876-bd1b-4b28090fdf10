package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value="RainForecastHour对象", description="测站降雨预报")
public class RainForecastHour implements Serializable {
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "政区代码")
    private String adcd;
    @ApiModelProperty(value = "测站编码")
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "未来小时数：1、3、6....")
    private String type;
    @ApiModelProperty(value = "未来小时降雨量")
    private BigDecimal rainnum;
    @ApiModelProperty(value = "实时降雨量")
    private BigDecimal rain0;
    @ApiModelProperty(value = "未来1小时降雨量")
    private BigDecimal rain1;

    @ApiModelProperty(value = "未来2小时降雨量")
    private BigDecimal rain2;
    @ApiModelProperty(value = "未来3小时降雨量")
    private BigDecimal rain3;
    @ApiModelProperty(value = "未来6小时降雨量")
    private BigDecimal rain6;
    @ApiModelProperty(value = "未来12小时降雨量")
    private BigDecimal rain12;
    @ApiModelProperty(value = "未来24小时降雨量")
    private BigDecimal rain24;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getRainnum() {
        return rainnum;
    }

    public void setRainnum(BigDecimal rainnum) {
        this.rainnum = rainnum;
    }

    public BigDecimal getRain0() {
        return rain0;
    }

    public void setRain0(BigDecimal rain0) {
        this.rain0 = rain0;
    }

    public BigDecimal getRain1() {
        return rain1;
    }

    public void setRain1(BigDecimal rain1) {
        this.rain1 = rain1;
    }

    public BigDecimal getRain2() {
        return rain2;
    }

    public void setRain2(BigDecimal rain2) {
        this.rain2 = rain2;
    }

    public BigDecimal getRain3() {
        return rain3;
    }

    public void setRain3(BigDecimal rain3) {
        this.rain3 = rain3;
    }

    public BigDecimal getRain6() {
        return rain6;
    }

    public void setRain6(BigDecimal rain6) {
        this.rain6 = rain6;
    }

    public BigDecimal getRain12() {
        return rain12;
    }

    public void setRain12(BigDecimal rain12) {
        this.rain12 = rain12;
    }

    public BigDecimal getRain24() {
        return rain24;
    }

    public void setRain24(BigDecimal rain24) {
        this.rain24 = rain24;
    }
}
