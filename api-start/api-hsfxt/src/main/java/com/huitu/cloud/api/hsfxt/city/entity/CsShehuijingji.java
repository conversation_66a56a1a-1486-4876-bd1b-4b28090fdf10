package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 社会经济
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@TableName("CS_SheHuiJingJi")
@ApiModel(value="CsShehuijingji对象", description="社会经济")
public class CsShehuijingji extends Model<CsShehuijingji> {

    @ApiModelProperty(value = "序号")
    private Integer sortid;

    @ApiModelProperty(value = "查询条件")
    private String keyindex;

    @ApiModelProperty(value = "类别")
    private String ymssindex;

    @ApiModelProperty(value = "政区编码")
    private String xzqhindex;

    @ApiModelProperty(value = "政区名称")
    private String mingcheng;

    @ApiModelProperty(value = "淹没面积")
    private Float ymmj;

    @ApiModelProperty(value = "淹没房屋面积")
    private Float ymfwmj;

    @ApiModelProperty(value = "影响公路")
    private Float yxgl;

    @ApiModelProperty(value = "影响铁路")
    private Float yxtl;

    @ApiModelProperty(value = "影响人口")
    private Float yxrk;

    @ApiModelProperty(value = "影响GDP")
    private Float yxgdp;

    @ApiModelProperty(value = "影响重点单位")
    private Integer yxzddw;

    @ApiModelProperty(value = "居民房屋损失")
    private Float jmfwss;

    @ApiModelProperty(value = "家庭财产损失")
    private Float jtccss;

    @ApiModelProperty(value = "xxx")
    private Float gyzcss;

    @ApiModelProperty(value = "xxx")
    private Float gyczss;

    @ApiModelProperty(value = "xxx")
    private Float smyzcss;

    @ApiModelProperty(value = "xxx")
    private Float smyzysrss;

    @ApiModelProperty(value = "道路损失")
    private Float dlss;

    @ApiModelProperty(value = "编制区编码")
    private String fenqu;

    @ApiModelProperty(value = "节点名称")
    private String name;

    @ApiModelProperty(value = "上级序号")
    private String pid;

    @ApiModelProperty(value = "下级节点")
    private List<CsShehuijingji> children;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public Integer getSortid() {
        return sortid;
    }

    public void setSortid(Integer sortid) {
        this.sortid = sortid;
    }

    public String getKeyindex() {
        return keyindex;
    }

    public void setKeyindex(String keyindex) {
        this.keyindex = keyindex;
    }

    public String getYmssindex() {
        return ymssindex;
    }

    public void setYmssindex(String ymssindex) {
        this.ymssindex = ymssindex;
    }

    public String getXzqhindex() {
        return xzqhindex;
    }

    public void setXzqhindex(String xzqhindex) {
        this.xzqhindex = xzqhindex;
    }

    public String getMingcheng() {
        return mingcheng;
    }

    public void setMingcheng(String mingcheng) {
        this.mingcheng = mingcheng;
    }

    public Float getYmmj() {
        return ymmj;
    }

    public void setYmmj(Float ymmj) {
        this.ymmj = ymmj;
    }

    public Float getYmfwmj() {
        return ymfwmj;
    }

    public void setYmfwmj(Float ymfwmj) {
        this.ymfwmj = ymfwmj;
    }

    public Float getYxgl() {
        return yxgl;
    }

    public void setYxgl(Float yxgl) {
        this.yxgl = yxgl;
    }

    public Float getYxtl() {
        return yxtl;
    }

    public void setYxtl(Float yxtl) {
        this.yxtl = yxtl;
    }

    public Float getYxrk() {
        return yxrk;
    }

    public void setYxrk(Float yxrk) {
        this.yxrk = yxrk;
    }

    public Float getYxgdp() {
        return yxgdp;
    }

    public void setYxgdp(Float yxgdp) {
        this.yxgdp = yxgdp;
    }

    public Integer getYxzddw() {
        return yxzddw;
    }

    public void setYxzddw(Integer yxzddw) {
        this.yxzddw = yxzddw;
    }

    public Float getJmfwss() {
        return jmfwss;
    }

    public void setJmfwss(Float jmfwss) {
        this.jmfwss = jmfwss;
    }

    public Float getJtccss() {
        return jtccss;
    }

    public void setJtccss(Float jtccss) {
        this.jtccss = jtccss;
    }

    public Float getGyzcss() {
        return gyzcss;
    }

    public void setGyzcss(Float gyzcss) {
        this.gyzcss = gyzcss;
    }

    public Float getGyczss() {
        return gyczss;
    }

    public void setGyczss(Float gyczss) {
        this.gyczss = gyczss;
    }

    public Float getSmyzcss() {
        return smyzcss;
    }

    public void setSmyzcss(Float smyzcss) {
        this.smyzcss = smyzcss;
    }

    public Float getSmyzysrss() {
        return smyzysrss;
    }

    public void setSmyzysrss(Float smyzysrss) {
        this.smyzysrss = smyzysrss;
    }

    public Float getDlss() {
        return dlss;
    }

    public void setDlss(Float dlss) {
        this.dlss = dlss;
    }

    public String getFenqu() {
        return fenqu;
    }

    public void setFenqu(String fenqu) {
        this.fenqu = fenqu;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<CsShehuijingji> getChildren() {
        return children;
    }

    public void setChildren(List<CsShehuijingji> children) {
        this.children = children;
    }
}
