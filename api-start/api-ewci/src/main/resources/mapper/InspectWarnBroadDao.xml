<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.inspect.mapper.InspectWarnBroadDao">

    <insert id="insertInspectWarnBroad">
        INSERT INTO BNS_INSPECT_WARN_BROAD(INSPECT_ID,
                                           ADCD,
                                           INSPECTOR,
                                           GROUPID,
                                           INSP_DATE,
                                           TADCD,
                                           WBRNM,
                                           EXIST_PROBLEMS,
                                           REC_ASK,
                                           REC_DATE,
                                           YEAR,
                                           LGTD,
                                           LTTD, CREATOR)
        VALUES ( #{inspectId}
               , #{adcd}
               , #{inspector}
               , #{groupid}
               , #{inspDate}
               , #{tadcd}
               , #{wbrnm}
               , #{existProblems}
               , #{recAsk}
               , #{recDate}
               , #{year}
               , #{lgtd}
               , #{lttd}, #{creator})
    </insert>
    <update id="updateInspectWarnBroad">
        UPDATE BNS_INSPECT_WARN_BROAD
        <trim prefix="set" suffixOverrides=",">
            <if test="adcd != null">ADCD = #{adcd},</if>
            <if test="inspector != null">INSPECTOR = #{inspector},</if>
            <if test="groupid != null">GROUPID = #{groupid},</if>
            <if test="inspDate != null">INSP_DATE = #{inspDate},</if>
            <if test="tadcd != null">TADCD = #{tadcd},</if>
            <if test="wbrnm != null">WBRNM = #{wbrnm},</if>
            <if test="existProblems != null">EXIST_PROBLEMS = #{existProblems},</if>
            <if test="recAsk != null">REC_ASK = #{recAsk},</if>
            <if test="recDate != null">REC_DATE = #{recDate},</if>
            <if test="year != null">[YEAR] = #{year},</if>
            <if test="lgtd != null">LGTD = #{lgtd},</if>
            <if test="lttd != null">LTTD = #{lttd},</if>
        </trim>
        WHERE INSPECT_ID=#{inspectId} AND CREATOR = #{creator}
    </update>
    <delete id="deleteInspectWarnBroad">
        DELETE
        FROM BNS_INSPECT_WARN_BROAD
        WHERE INSPECT_ID = #{inspectId}
    </delete>
    <select id="getInspectWarnBroadList"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectWarnBroad">
        SELECT * FROM BNS_INSPECT_WARN_BROAD WHERE TADCD = #{ tadcd }
                                                       AND YEAR = #{ year }
    </select>
    <select id="selectYearByInspectId" resultType="java.util.Date">
        select INSP_DATE FROM BNS_INSPECT_WARN_BROAD  WHERE INSPECT_ID = #{inspectId}
    </select>

</mapper>