package com.huitu.cloud.api.hyst.survey.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 小水电下泄流量信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-30
 */
@TableName("HYST_ATT_EBF")
@ApiModel(value = "HystAttEbf对象", description = "小水电下泄流量信息表")
public class HystAttEbf extends Model<HystAttEbf> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水电站统计代码")
    @TableId(value = "HYST_CODE")
    private String hystCode;

    @ApiModelProperty(value = "监测通道编号")
    @TableField("WAIN_NUM")
    private String wainNum;

    @ApiModelProperty(value = "下泄流量，单位：m³/s")
    @TableField("ECO_FLOW")
    private BigDecimal ecoFlow;

    @ApiModelProperty(value = "记录时间")
    @TableField("REC_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recTime;

    public String getHystCode() {
        return hystCode;
    }

    public void setHystCode(String hystCode) {
        this.hystCode = hystCode;
    }

    public String getWainNum() {
        return wainNum;
    }

    public void setWainNum(String wainNum) {
        this.wainNum = wainNum;
    }

    public BigDecimal getEcoFlow() {
        return ecoFlow;
    }

    public void setEcoFlow(BigDecimal ecoFlow) {
        this.ecoFlow = ecoFlow;
    }

    public LocalDateTime getRecTime() {
        return recTime;
    }

    public void setRecTime(LocalDateTime recTime) {
        this.recTime = recTime;
    }
}