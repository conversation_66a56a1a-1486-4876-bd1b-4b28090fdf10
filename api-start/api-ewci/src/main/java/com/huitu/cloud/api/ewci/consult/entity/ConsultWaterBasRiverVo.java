package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description: 实时水情-水情统计-市县-河道实体
 * @author: jiangjy
 * @create: 2024-4-17
 **/
@ApiModel(value = "实时水情-水情统计-流域-河道实体")
public class ConsultWaterBasRiverVo {

    @ApiModelProperty(value = "中小河流的流域名称")
    private String basName;

    @ApiModelProperty(value = "中小河流的流域编码")
    private String basCode;

    @ApiModelProperty(value = "正常水位站数量")
    private Integer z;

    @ApiModelProperty(value = "超警戒水位站数量")
    private Integer zwrz;

    @ApiModelProperty(value = "超保证水位站数量")
    private Integer zgrz;

    public String getBasName() {
        return basName;
    }

    public void setBasName(String basName) {
        this.basName = basName;
    }

    public String getBasCode() {
        return basCode;
    }

    public void setBasCode(String basCode) {
        this.basCode = basCode;
    }

    public Integer getZ() {
        return z;
    }

    public void setZ(Integer z) {
        this.z = z;
    }

    public Integer getZwrz() {
        return zwrz;
    }

    public void setZwrz(Integer zwrz) {
        this.zwrz = zwrz;
    }

    public Integer getZgrz() {
        return zgrz;
    }

    public void setZgrz(Integer zgrz) {
        this.zgrz = zgrz;
    }
}
