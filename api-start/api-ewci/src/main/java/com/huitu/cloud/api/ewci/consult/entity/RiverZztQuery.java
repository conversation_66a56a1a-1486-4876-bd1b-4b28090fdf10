package com.huitu.cloud.api.ewci.consult.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 作战图参数
 */
@ApiModel(value = "作战图参数")
public class RiverZztQuery implements Serializable {

    @ApiModelProperty(value = "河流编码")
    private String rvCode;

    @ApiModelProperty(value = "开始时间")
    private String stm;

    @ApiModelProperty(value = "结束时间")
    private String etm;

    @ApiModelProperty(value = "河流编码")
    private List<String> list;

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public List<String> getList() {
        return list;
    }

    public void setList(List<String> list) {
        this.list = list;
    }
}
