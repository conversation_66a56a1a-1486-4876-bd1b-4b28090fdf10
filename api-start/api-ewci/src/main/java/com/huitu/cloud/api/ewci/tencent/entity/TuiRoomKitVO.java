package com.huitu.cloud.api.ewci.tencent.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 会议信息实体
 */
@ApiModel("会议信息")
public class TuiRoomKitVO {

    @ApiModelProperty(value = "会议房间号")
    private String roomId;

    @ApiModelProperty(value = "会议标题")
    private String title;

    @ApiModelProperty(value = "创建人id")
    private String createId;

    @ApiModelProperty(value = "创建人名称/主持人名称")
    private String createName;

    @ApiModelProperty(value = "邀请链接")
    private String invitationLink;

    @ApiModelProperty(value = "参会人员列表")
    private List<TuiRoomKitUsersVO> usersVOList;

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getInvitationLink() {
        return invitationLink;
    }

    public void setInvitationLink(String invitationLink) {
        this.invitationLink = invitationLink;
    }

    public List<TuiRoomKitUsersVO> getUsersVOList() {
        return usersVOList;
    }

    public void setUsersVOList(List<TuiRoomKitUsersVO> usersVOList) {
        this.usersVOList = usersVOList;
    }
}
