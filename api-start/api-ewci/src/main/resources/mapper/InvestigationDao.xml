<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.bia.mapper.InvestigationDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!--    调查重点城（集）镇专题展示-->
    <select id="getDtAKeyTownADCDList" resultType="com.huitu.cloud.api.ewci.bia.entity.DtAKeyTown">
        SELECT a.ADCD, ADNM, LGTD, LTTD, PADCD
        FROM (SELECT e.ADCD, ADNM, LGTD, LTTD, e.SORTNO, PADCD
              FROM MDT_ADCDINFO_E e
                       LEFT JOIN MDT_ADCDINFO_B b ON e.adcd = b.adcd
              WHERE left (e.ADCD
                  , #{level}) = left (#{ad}
                  , #{level})
                AND adlvl =#{lv}) a
        <if test="level2 !=null and level2 =='6'.toString()">
            ORDER BY a.adcd
        </if>
        <if test="level2 !=null and level2 !='6'.toString()">
            ORDER BY a.SORTNO
        </if>
    </select>

    <select id="adcds" resultType="com.huitu.cloud.api.ewci.bia.entity.DtAKeyTown">
        SELECT
            ADCD,
            ADNM,
            LGTD,
            LTTD,
            PADCD
        FROM
            MDT_ADCDINFO_B
        WHERE
          adlvl = 4
          AND ADCD LIKE concat(#{ad},'%')
    </select>
    <select id="getUrbanResidents" resultType="com.huitu.cloud.api.ewci.bia.entity.DtAKeyTown">
        SELECT
            A.adcd AS adcd,
            SUM ( A.HTCOUNT ) AS czHtcount,
            SUM ( A.PTCOUNT ) AS czPtcount
        FROM
            (
                SELECT
                    ( LEFT ( ADCD, 9 ) + '000000' ) AS adcd,
                    HTCOUNT,
                    PTCOUNT
                FROM
                    BNS_IA_C_DTRESIDENT
                WHERE
                ADCD LIKE concat(#{ad},'%')
                  AND HTCOUNT IS NOT NULL
                   OR PTCOUNT IS NOT NULL
            ) A
        GROUP BY
            A.adcd
    </select>

    <select id="getRiversideResidents" resultType="com.huitu.cloud.api.ewci.bia.entity.DtAKeyTown">
        SELECT
            A.adcd AS adcd,
            COUNT ( 1 ) AS yhHtcount,
            SUM ( A.PTCOUNT ) AS yhPtcount
        FROM
            (
                SELECT
                    ( LEFT ( ADCD, 9 ) + '000000' ) AS adcd,
                    AVRCD,
                    PTCOUNT
                FROM
                    BNS_IA_C_FLRVVLG
                WHERE
                    ADCD LIKE concat(#{ad},'%')
                  AND PTCOUNT IS NOT NULL
            ) A
        GROUP BY
            A.adcd
    </select>

    <select id="getEnterprises" resultType="com.huitu.cloud.api.ewci.bia.entity.DtAKeyTown">
        SELECT
            A.adcd AS adcd,
            COUNT ( 1 ) AS eicount
        FROM
            (
                SELECT
                    ( LEFT ( ADCD, 9 ) + '000000' ) AS adcd,
                    EICD
                FROM
                    BNS_IA_C_BSNSSINFO
                WHERE
                    ADCD LIKE concat(#{ad},'%')
            ) A
        GROUP BY
            A.adcd
    </select>

    <select id="getMountainTorrent" resultType="com.huitu.cloud.api.ewci.bia.entity.DtAKeyTown">
        SELECT
            A.adcd AS adcd,
            COUNT ( 1 ) AS mtcount
        FROM
            (
                SELECT
                    ( LEFT ( ADCD, 9 ) + '000000' ) AS adcd,
                    MTCD
                FROM
                    BNS_IA_C_HSFWATER
                WHERE
                    ADCD LIKE concat(#{ad},'%')
            ) A
        GROUP BY
            A.adcd
    </select>

<!--    <select id="getDtAKeyTownList" resultType="com.huitu.cloud.api.ewci.bia.entity.DtAKeyTown">-->
<!--        select a.ADCD,-->
<!--               ADNM,-->
<!--               LGTD,-->
<!--               LTTD,-->
<!--               PADCD,-->
<!--               czHtcount,-->
<!--               czPtcount,-->
<!--               yhHtcount,-->
<!--               yhPtcount,-->
<!--               eicount,-->
<!--               mtcount-->
<!--        from (SELECT ADCD, ADNM, LGTD, LTTD, PADCD-->
<!--              FROM MDT_ADCDINFO_B-->
<!--              WHERE-->
<!--                  left (ADCD-->
<!--                  , #{level}) = left (#{ad}-->
<!--                  , #{level})-->
<!--                and adlvl =4) a-->
<!--                 left join (SELECT A.adcd AS adcd, SUM(A.HTCOUNT) AS czHtcount, SUM(A.PTCOUNT) AS czPtcount-->
<!--                            FROM (SELECT (left(ADCD,9)+'000000') AS adcd, HTCOUNT, PTCOUNT-->
<!--                                  FROM BNS_IA_C_DTRESIDENT-->
<!--                                  WHERE left (ADCD-->
<!--                                      , #{level}) = left (#{ad}-->
<!--                                      , #{level})) A-->
<!--                            GROUP BY A.adcd) c on a.adcd = c.adcd-->

<!--                 left join (SELECT A.adcd AS adcd, count(1) AS yhHtcount, SUM(A.PTCOUNT) AS yhPtcount-->
<!--                            FROM (SELECT (left(ADCD,9)+'000000') AS adcd, AVRCD, PTCOUNT-->
<!--                                  FROM BNS_IA_C_FLRVVLG-->
<!--                                  WHERE left (ADCD-->
<!--                                      , #{level}) = left (#{ad}-->
<!--                                      , #{level})) A-->
<!--                            GROUP BY A.adcd) d on a.adcd = d.adcd-->

<!--                 left join (SELECT A.adcd AS adcd, COUNT(1) AS eicount-->
<!--                            FROM (SELECT (left(ADCD,9)+'000000') AS adcd, EICD-->
<!--                                  FROM BNS_IA_C_BSNSSINFO-->
<!--                                  WHERE left (ADCD-->
<!--                                      , #{level}) = left (#{ad}-->
<!--                                      , #{level})) A-->
<!--                            GROUP BY A.adcd) e on a.adcd = e.adcd-->

<!--                 left join (SELECT A.adcd AS adcd, count(1) AS mtcount-->
<!--                            FROM (SELECT (left(ADCD,9)+'000000') AS adcd, MTCD-->
<!--                                  FROM BNS_IA_C_HSFWATER-->
<!--                                  WHERE left (ADCD-->
<!--                                      , #{level}) = left (#{ad}-->
<!--                                      , #{level})) A-->
<!--                            GROUP BY A.adcd) f on a.adcd = f.adcd-->
<!--        where czHtcount is not null-->
<!--           or czPtcount is not null-->
<!--           or yhHtcount is not null-->
<!--           or yhPtcount is not null-->
<!--           or eicount is not null-->
<!--           or mtcount is not null-->
<!--    </select>-->

    <!--    城镇居民详情列表-->
    <select id="getDtresidentList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaCDtresident">
        select
        A.ADCD,B.ADNM,A.IURCD,A.ADDRESS,A.HTCOUNT,A.PTCOUNT,A.LGTD,A.LTTD,A.HELE,A.AREA,A.BWATER,A.BHILL,A.BTYPE,A.STYPE,A.COMMENTS,A.MODITIME,A.ZADCD
        FROM BNS_IA_C_DTRESIDENT A
        LEFT JOIN MDT_ADCDINFO_B B on A.ADCD = B.ADCD
        WHERE 1=1
        <if test="param.adcd !=null and param.adcd !=''">
            AND left(A.ADCD,#{param.level})=left(#{param.adcd},#{param.level})
        </if>
        <if test="param.adnm !=null and param.adnm !=''">
            and CHARINDEX(#{param.adnm},B.ADNM) > 0
        </if>
        <if test="param.bwater !=null and param.bwater !=''">
            and A.BWATER = #{param.bwater}
        </if>
        <if test="param.bhill !=null and param.bhill !=''">
            and A.BHILL = #{param.bhill}
        </if>
        ORDER BY A.ADCD ASC
    </select>
    <!--    查询导出城镇居民详情列表-->
    <select id="exportDtresidentList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaCDtresident">
        select
        A.ADCD,B.ADNM,A.IURCD,A.ADDRESS,A.HTCOUNT,A.PTCOUNT,A.LGTD,A.LTTD,A.HELE,A.AREA,CASE
        A.BWATER
        WHEN '0' THEN
        '否'
        WHEN '1' THEN
        '是'
        END AS BWATER,
        CASE
        A.BHILL
        WHEN '0' THEN
        '否'
        WHEN '1' THEN
        '是'
        END AS BHILL,CASE
        A.BTYPE
        WHEN '1' THEN
        '1层住宅'
        WHEN '2' THEN
        '2层住宅'
        WHEN '3' THEN
        '3层住宅'
        WHEN '4' THEN
        '3层以上住宅'
        END AS BTYPE,
        CASE
        A.STYPE
        WHEN '1' THEN
        '钢筋混凝土结构'
        WHEN '2' THEN
        '混合结构'
        WHEN '3' THEN
        '砖木结构'
        WHEN '4' THEN
        '其他结构'
        END AS STYPE,A.COMMENTS,A.MODITIME,A.ZADCD
        FROM BNS_IA_C_DTRESIDENT A
        LEFT JOIN MDT_ADCDINFO_B B on A.ADCD = B.ADCD
        WHERE 1=1
        <if test="param.adcd !=null and param.adcd !=''">
            AND left(A.ADCD,#{param.level})=left(#{param.adcd},#{param.level})
        </if>
        <if test="param.adnm !=null and param.adnm !=''">
            and CHARINDEX(#{param.adnm},B.ADNM) > 0
        </if>
        <if test="param.bwater !=null and param.bwater !=''">
            and A.BWATER = #{param.bwater}
        </if>
        <if test="param.bhill !=null and param.bhill !=''">
            and A.BHILL = #{param.bhill}
        </if>
        ORDER BY A.ADCD ASC
    </select>
    <!--    沿河居民户详情列表-->
    <select id="getFlrvvlgList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaCFlrvvlg">
        select
        A.ADCD,B.ADNM,A.AVRCD,A.NAME,A.WSCD,A.BLGTD,A.BLTTD,A.BELE,A.PTCOUNT,A.AREA,A.BTYPE,A.STYPE,A.LGTD,A.LTTD,A.HELE,A.BWATER,A.BHILL,A.COMMENTS,A.MODITIME
        FROM BNS_IA_C_FLRVVLG A
        LEFT JOIN MDT_ADCDINFO_B B on A.ADCD = B.ADCD
        WHERE 1=1
        <if test="param.adcd !=null and param.adcd !=''">
            AND left(A.ADCD,#{param.level})=left(#{param.adcd},#{param.level})
        </if>
        <if test="param.adnm !=null and param.adnm !=''">
            and CHARINDEX(#{param.adnm},B.ADNM) > 0
        </if>
        <if test="param.bwater !=null and param.bwater !=''">
            and A.BWATER = #{param.bwater}
        </if>
        <if test="param.bhill !=null and param.bhill !=''">
            and A.BHILL = #{param.bhill}
        </if>
        ORDER BY A.ADCD ASC
    </select>

    <!--    查询导出沿河居民户详情列表-->
    <select id="exportFlrvvlgList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaCFlrvvlg">
        select
        A.ADCD,B.ADNM,A.AVRCD,A.NAME,A.WSCD,A.BLGTD,A.BLTTD,A.BELE,A.PTCOUNT,A.AREA,CASE
        A.BTYPE
        WHEN '1' THEN
        '1层住宅'
        WHEN '2' THEN
        '2层住宅'
        WHEN '3' THEN
        '3层住宅'
        WHEN '4' THEN
        '3层以上住宅'
        END AS BTYPE,
        CASE
        A.STYPE
        WHEN '1' THEN
        '钢筋混凝土结构'
        WHEN '2' THEN
        '混合结构'
        WHEN '3' THEN
        '砖木结构'
        WHEN '4' THEN
        '其他结构'
        END AS STYPE,A.LGTD,A.LTTD,CASE
        A.BWATER
        WHEN '0' THEN
        '否'
        WHEN '1' THEN
        '是'
        END AS BWATER,
        CASE
        A.BHILL
        WHEN '0' THEN
        '否'
        WHEN '1' THEN
        '是'
        END AS BHILL,A.BHILL,A.HELE,A.COMMENTS,A.MODITIME
        FROM BNS_IA_C_FLRVVLG A
        LEFT JOIN MDT_ADCDINFO_B B on A.ADCD = B.ADCD
        WHERE 1=1
        <if test="param.adcd !=null and param.adcd !=''">
            AND left(A.ADCD,#{param.level})=left(#{param.adcd},#{param.level})
        </if>
        <if test="param.adnm !=null and param.adnm !=''">
            and CHARINDEX(#{param.adnm},B.ADNM) > 0
        </if>
        <if test="param.bwater !=null and param.bwater !=''">
            and A.BWATER = #{param.bwater}
        </if>
        <if test="param.bhill !=null and param.bhill !=''">
            and A.BHILL = #{param.bhill}
        </if>
        ORDER BY A.ADCD ASC
    </select>
    <!--    历史山洪灾害详情列表-->
    <select id="getHsfwaterList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaCHsfwater">
        select
        A.MTCD,A.WSCD,A.ADCD,B.ADNM,A.OTIME,A.ADDRESS,A.LGTD,A.LTTD,A.PFRAIN,A.DPCOUNT,A.MPCOUNT,A.CHCOUNT,A.SPCOUNT,A.ELOSE,A.DDSCRIB,A.COMMENTS,A.MODITIME
        FROM BNS_IA_C_HSFWATER A
        LEFT JOIN MDT_ADCDINFO_B B on A.ADCD = B.ADCD
        WHERE 1=1
        <if test="param.adcd !=null and param.adcd !=''">
            AND left(A.ADCD,#{param.level})=left(#{param.adcd},#{param.level})
        </if>
        <if test="param.adnm !=null and param.adnm !=''">
            and CHARINDEX(#{param.adnm},B.ADNM) > 0
        </if>
        ORDER BY A.OTIME DESC,A.ADCD ASC
    </select>
    <!--    企事业单位详情列表-->
    <select id="getBsnssinfoList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaCBsnssinfo">
        select
        A.EICD,A.NAME,A.WSCD,A.ADCD,B.ADNM,A.DAND,A.LGTD,A.LTTD,A.TYPE,A.OCODE,A.ADDRESS,A.AREA,A.PCOUNT,A.HCOUNT,A.AVALUE,A.OVALUE,A.MODITIME,A.COMMENTS
        FROM BNS_IA_C_BSNSSINFO A
        LEFT JOIN MDT_ADCDINFO_B B on A.ADCD = B.ADCD
        WHERE 1=1
        <if test="param.adcd !=null and param.adcd !=''">
            AND left(A.ADCD,#{param.level})=left(#{param.adcd},#{param.level})
        </if>
        <if test="param.adnm !=null and param.adnm !=''">
            and CHARINDEX(#{param.adnm},B.ADNM) > 0
        </if>
        <if test="param.type !=null and param.type !=''">
            and A.TYPE = #{param.type}
        </if>
        ORDER BY A.ADCD ASC
    </select>


</mapper>
