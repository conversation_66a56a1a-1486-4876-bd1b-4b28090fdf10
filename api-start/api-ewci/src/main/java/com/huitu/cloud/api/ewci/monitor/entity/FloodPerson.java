package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 山洪责任人
 *
 * <AUTHOR>
 */
@ApiModel(value = "山洪责任人")
public class FloodPerson implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "县（市、区）")
    private String xadnm;

    @ApiModelProperty(value = "乡镇")
    private String xz;

    @ApiModelProperty(value = "行政村")
    private String xzc;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "自然村名称")
    @TableField(value = "ZRC")
    private String zrc;

    @ApiModelProperty(value = "县市责任人-姓名")
    @TableField(value = "XS_REALNM")
    private String xsRealnm;

    @ApiModelProperty(value = "县市责任人-职务")
    @TableField(value = "XS_DUTY")
    private String xsDuty;

    @ApiModelProperty(value = "县市责任人-电话号码")
    @TableField(value = "XS_MOBILE")
    private String xsMobile;

    @ApiModelProperty(value = "乡镇责任人-姓名")
    @TableField(value = "XZ_REALNM")
    private String xzRealnm;

    @ApiModelProperty(value = "乡镇责任人-职务")
    @TableField(value = "XZ_DUTY")
    private String xzDuty;

    @ApiModelProperty(value = "乡镇责任人-电话号码")
    @TableField(value = "XZ_MOBILE")
    private String xzMobile;

    @ApiModelProperty(value = "行政村责任人-姓名")
    @TableField(value = "XZC_REALNM")
    private String xzcRealnm;

    @ApiModelProperty(value = "行政村责任人-职务")
    @TableField(value = "XZC_DUTY")
    private String xzcDuty;

    @ApiModelProperty(value = "行政村责任人-电话号码")
    @TableField(value = "XZC_MOBILE")
    private String xzcMobile;

    @ApiModelProperty(value = "自然村责任人-姓名")
    @TableField(value = "ZRC_REALNM")
    private String zrcRealnm;

    @ApiModelProperty(value = "自然村责任人-职务")
    @TableField(value = "ZRC_DUTY")
    private String zrcDuty;

    @ApiModelProperty(value = "自然村责任人-电话号码")
    @TableField(value = "ZRC_MOBILE")
    private String zrcMobile;

    @ApiModelProperty(value = "监测责任人-姓名")
    @TableField(value = "JC_REALNM")
    private String jcRealnm;

    @ApiModelProperty(value = "监测责任人-职务")
    @TableField(value = "JC_DUTY")
    private String jcDuty;

    @ApiModelProperty(value = "监测责任人-电话号码")
    @TableField(value = "JC_MOBILE")
    private String jcMobile;

    @ApiModelProperty(value = "预警责任人-姓名")
    @TableField(value = "YJ_REALNM")
    private String yjRealnm;

    @ApiModelProperty(value = "预警责任人-职务")
    @TableField(value = "YJ_DUTY")
    private String yjDuty;

    @ApiModelProperty(value = "预警责任人-电话号码")
    @TableField(value = "YJ_MOBILE")
    private String yjMobile;

    @ApiModelProperty(value = "转移责任人-姓名")
    @TableField("zx_realnm")
    private String zxRealnm;

    @ApiModelProperty(value = "转移责任人-手机号码")
    @TableField("zx_mobile")
    private String zxMobile;

    @ApiModelProperty(value = "转移责任人-电话号码")
    @TableField(value = "ZY_MOBILE")
    private String zyMobile;

    @ApiModelProperty(value = "管理单位责任人-姓名")
    @TableField(value = "GLDW_REALNM")
    private String gldwRealnm;

    @ApiModelProperty(value = "管理单位责任人-职务")
    @TableField(value = "GLDW_DUTY")
    private String gldwDuty;

    @ApiModelProperty(value = "管理单位责任人-电话号码")
    @TableField(value = "GLDW_MOBILE")
    private String gldwMobile;

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getXz() {
        return xz;
    }

    public void setXz(String xz) {
        this.xz = xz;
    }

    public String getXzc() {
        return xzc;
    }

    public void setXzc(String xzc) {
        this.xzc = xzc;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getZrc() {
        return zrc;
    }

    public void setZrc(String zrc) {
        this.zrc = zrc;
    }

    public String getXsRealnm() {
        return xsRealnm;
    }

    public void setXsRealnm(String xsRealnm) {
        this.xsRealnm = xsRealnm;
    }

    public String getXsDuty() {
        return xsDuty;
    }

    public void setXsDuty(String xsDuty) {
        this.xsDuty = xsDuty;
    }

    public String getXsMobile() {
        return xsMobile;
    }

    public void setXsMobile(String xsMobile) {
        this.xsMobile = xsMobile;
    }

    public String getXzRealnm() {
        return xzRealnm;
    }

    public void setXzRealnm(String xzRealnm) {
        this.xzRealnm = xzRealnm;
    }

    public String getXzDuty() {
        return xzDuty;
    }

    public void setXzDuty(String xzDuty) {
        this.xzDuty = xzDuty;
    }

    public String getXzMobile() {
        return xzMobile;
    }

    public void setXzMobile(String xzMobile) {
        this.xzMobile = xzMobile;
    }

    public String getXzcRealnm() {
        return xzcRealnm;
    }

    public void setXzcRealnm(String xzcRealnm) {
        this.xzcRealnm = xzcRealnm;
    }

    public String getXzcDuty() {
        return xzcDuty;
    }

    public void setXzcDuty(String xzcDuty) {
        this.xzcDuty = xzcDuty;
    }

    public String getXzcMobile() {
        return xzcMobile;
    }

    public void setXzcMobile(String xzcMobile) {
        this.xzcMobile = xzcMobile;
    }

    public String getZrcRealnm() {
        return zrcRealnm;
    }

    public void setZrcRealnm(String zrcRealnm) {
        this.zrcRealnm = zrcRealnm;
    }

    public String getZrcDuty() {
        return zrcDuty;
    }

    public void setZrcDuty(String zrcDuty) {
        this.zrcDuty = zrcDuty;
    }

    public String getZrcMobile() {
        return zrcMobile;
    }

    public void setZrcMobile(String zrcMobile) {
        this.zrcMobile = zrcMobile;
    }

    public String getJcRealnm() {
        return jcRealnm;
    }

    public void setJcRealnm(String jcRealnm) {
        this.jcRealnm = jcRealnm;
    }

    public String getJcDuty() {
        return jcDuty;
    }

    public void setJcDuty(String jcDuty) {
        this.jcDuty = jcDuty;
    }

    public String getJcMobile() {
        return jcMobile;
    }

    public void setJcMobile(String jcMobile) {
        this.jcMobile = jcMobile;
    }

    public String getYjRealnm() {
        return yjRealnm;
    }

    public void setYjRealnm(String yjRealnm) {
        this.yjRealnm = yjRealnm;
    }

    public String getYjDuty() {
        return yjDuty;
    }

    public void setYjDuty(String yjDuty) {
        this.yjDuty = yjDuty;
    }

    public String getYjMobile() {
        return yjMobile;
    }

    public void setYjMobile(String yjMobile) {
        this.yjMobile = yjMobile;
    }

    public String getZxRealnm() {
        return zxRealnm;
    }

    public void setZxRealnm(String zxRealnm) {
        this.zxRealnm = zxRealnm;
    }

    public String getZxMobile() {
        return zxMobile;
    }

    public void setZxMobile(String zxMobile) {
        this.zxMobile = zxMobile;
    }

    public String getZyMobile() {
        return zyMobile;
    }

    public void setZyMobile(String zyMobile) {
        this.zyMobile = zyMobile;
    }

    public String getGldwRealnm() {
        return gldwRealnm;
    }

    public void setGldwRealnm(String gldwRealnm) {
        this.gldwRealnm = gldwRealnm;
    }

    public String getGldwDuty() {
        return gldwDuty;
    }

    public void setGldwDuty(String gldwDuty) {
        this.gldwDuty = gldwDuty;
    }

    public String getGldwMobile() {
        return gldwMobile;
    }

    public void setGldwMobile(String gldwMobile) {
        this.gldwMobile = gldwMobile;
    }
}
