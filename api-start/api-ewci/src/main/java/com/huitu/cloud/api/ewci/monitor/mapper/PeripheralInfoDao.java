package com.huitu.cloud.api.ewci.monitor.mapper;

import com.huitu.cloud.api.ewci.monitor.entity.RainData;
import com.huitu.cloud.api.ewci.monitor.entity.RiverData;
import com.huitu.cloud.api.ewci.monitor.entity.RsvrData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 周边信息Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface PeripheralInfoDao {

    /**
     * 获取降雨数据列表
     *
     * @param params 查询参数
     * @return 降雨数据列表
     **/
    List<RainData> getRainDataList(@Param("map") Map<String, Object> params);

    /**
     * 获取水库数据列表
     *
     * @param params 查询参数
     * @return 水库数据列表
     **/
    List<RsvrData> getRsvrDataList(@Param("map") Map<String, Object> params);

    /**
     * 获取河道数据列表
     *
     * @param params 查询参数
     * @return 河道数据列表
     **/
    List<RiverData> getRiverDataList(@Param("map") Map<String, Object> params);
}
