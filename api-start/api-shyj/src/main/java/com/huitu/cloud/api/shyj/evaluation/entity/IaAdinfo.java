package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 防治区基本情况调查成果汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@TableName("IA_C_ADINFO")
@ApiModel(value="IA_C_ADINFO对象", description="防治区基本情况调查成果汇总表")
public class IaAdinfo extends Model<IaAdinfo> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableId(value = "ADCD", type = IdType.NONE)
    private String adcd;

    @ApiModelProperty(value = "行政区名称")
    @TableField("ADNM")
    private String adnm;

    @ApiModelProperty(value = "总人口")
    @TableField("PCOUNT")
    private BigDecimal pcount;

    @ApiModelProperty(value = "总户数")
    @TableField("HTCOUNT")
    private BigDecimal htcount;

    @ApiModelProperty(value = "土地面积")
    @TableField("LDAREA")
    private BigDecimal ldarea;

    @ApiModelProperty(value = "耕地面积")
    @TableField("PLAREA")
    private BigDecimal plarea;

    @ApiModelProperty(value = "防治区类型(1：一般防治区  2：重点防治区)")
    @TableField("PREVTP")
    private String prevtp;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private BigDecimal lttd;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public BigDecimal getPcount() {
        return pcount;
    }

    public void setPcount(BigDecimal pcount) {
        this.pcount = pcount;
    }

    public BigDecimal getHtcount() {
        return htcount;
    }

    public void setHtcount(BigDecimal htcount) {
        this.htcount = htcount;
    }

    public BigDecimal getLdarea() {
        return ldarea;
    }

    public void setLdarea(BigDecimal ldarea) {
        this.ldarea = ldarea;
    }

    public BigDecimal getPlarea() {
        return plarea;
    }

    public void setPlarea(BigDecimal plarea) {
        this.plarea = plarea;
    }

    public String getPrevtp() {
        return prevtp;
    }

    public void setPrevtp(String prevtp) {
        this.prevtp = prevtp;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }
}
