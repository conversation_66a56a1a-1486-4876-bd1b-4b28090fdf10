<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ledger.mapper.LgConstructionManagementDao">
    <!--    <cache type="com.huitu.cloud.config.RedisCache"/>-->
    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.ledger.entity.LgConstruction">
        WITH Temp as (
        SELECT
        ROW_NUMBER () OVER (ORDER BY a.UPDATED DESC, a.CREATED DESC) SORTNO,
        a.PROJECT_ID AS projectId,
        a.ADCD AS adcd,
        a.FUNDING_BATCH AS fundingBatch,
        a.USAGE_DIRECTION AS usageDirection,
        a.IMPLEMENTATION_YEAR AS implementationYear,
        a.TENDER_TYPE AS tenderType,
        a.BID_NOTICE_DATE AS bidNoticeDate,
        a.BID_WINNER AS bidWinner,
        a.BID_AMOUNT AS bidAmount,
        a.CONTRACT_AMOUNT AS contractAmount,
        a.IS_STARTED AS isStarted,
        a.IS_COMPLETED AS isCompleted,
        a.PLANNED_START_DATE AS plannedStartDate,
        a.PLANNED_COMPLETION_DATE AS plannedCompletionDate,
        a.IS_CONTRACT_COMPLETED AS isContractCompleted,
        a.IS_FINAL_ACCEPTANCE AS isFinalAcceptance,
        a.TOTAL_FUNDS_ALLOCATED AS totalFundsAllocated,
        a.CENTRAL_SUBSIDY_FUNDS AS centralSubsidyFunds,
        a.LOCAL_MATCHING_FUNDS AS localMatchingFunds,
        a.TOTAL_INVESTMENT_COMPLETED AS totalInvestmentCompleted,
        a.CENTRAL_SUBSIDY_INVESTMENT AS centralSubsidyInvestment,
        a.LOCAL_MATCHING_INVESTMENT AS localMatchingInvestment,
        a.INVESTMENT_COMPLETION_RATE AS investmentCompletionRate,
        a.TOTAL_SETTLED_FUNDS AS totalSettledFunds,
        a.CENTRAL_SUBSIDY_SETTLED AS centralSubsidySettled,
        a.LOCAL_MATCHING_SETTLED AS localMatchingSettled,
        a.TOTAL_PAID_FUNDS AS totalPaidFunds,
        a.CENTRAL_SUBSIDY_PAID AS centralSubsidyPaid,
        a.LOCAL_MATCHING_PAID AS localMatchingPaid,
        a.FUNDS_PAYMENT_RATE AS fundsPaymentRate,
        a.PERFORMANCE_TARGET_RESERVOIR AS performanceTargetReservoir,
        a.PERFORMANCE_TARGET_DIKE AS performanceTargetDike,
        a.PERFORMANCE_TARGET_GATE AS performanceTargetGate,
        a.PERFORMANCE_TARGET_HYDROLOGICAL AS performanceTargetHydrological,
        a.PERFORMANCE_TARGET_OTHER AS performanceTargetOther,
        a.DEADLINE AS deadline,
        a.ADVANCE_DAY AS advanceDay,
        a.DEADLINE_NOTICE AS deadlineNotice,
        a.REMARKS AS remarks,
        ad.adnm AS adAdnm,
        adv.adnm AS advAdnm,
        b.PROJECT_NAME AS projectName,
        b.BASIN AS basin,
        b.RIVER AS river,
        b.TRIBUTARY_SECTION_NAME AS tributarySectionName,
        b.ORIGINAL_WATER_PROJECT AS originalWaterProject,
        b.ORIGINAL_WATER_PROJECT_NO AS originalWaterProjectNo,
        b.START_LOCATION AS startLocation,
        b.START_ORIGINAL_PILE AS startOriginalPile,
        b.START_CURRENT_PILE AS startCurrentPile,
        b.START_LONGITUDE AS startLongitude,
        b.START_LATITUDE AS startLatitude,
        b.END_LOCATION AS endLocation,
        b.END_ORIGINAL_PILE AS endOriginalPile,
        b.END_CURRENT_PILE AS endCurrentPile,
        b.END_LONGITUDE AS endLongitude,
        b.END_LATITUDE AS endLatitude,
        b.DAM_TOP AS damTop,
        b.DAM_FRONT AS damFront,
        b.DAM_BACK AS damBack,
        b.SPILLWAY_INLET AS spillwayInlet,
        b.SPILLWAY_CONTROL AS spillwayControl,
        b.SPILLWAY_CHANNEL AS spillwayChannel,
        b.SPILLWAY_ENERGY_DISSIPATION AS spillwayEnergyDissipation,
        b.SPILLWAY_DRAIN AS spillwayDrain,
        b.FLOOD_DIVERSION_INLET AS floodDiversionInlet,
        b.FLOOD_DIVERSION_GATE AS floodDiversionGate,
        b.FLOOD_DIVERSION_CONTROL AS floodDiversionControl,
        b.FLOOD_DIVERSION_TUNNEL AS floodDiversionTunnel,
        b.FLOOD_DIVERSION_ENERGY AS floodDiversionEnergy,
        b.FLOOD_DIVERSION_DRAIN AS floodDiversionDrain,
        b.OTHER_FACILITIES AS otherFacilities,
        b.EMBANKMENT AS embankment,
        b.BANK_PROTECTION AS bankProtection,
        b.CULVERT AS culvert,
        b.HYDROLOGICAL_FACILITIES AS hydrologicalFacilities,
        b.DISASTER_TIME AS disasterTime,
        b.REPORTED_TO_NATIONAL_SYSTEM AS reportedToNationalSystem,
        b.DISASTER_DAMAGE_DESCRIPTION AS disasterDamageDescription,
        b.PRELIMINARY_WORK AS preliminaryWork,
        b.PRELIMINARY_DESIGN_NO AS preliminaryDesignNo,
        b.PRELIMINARY_DESIGN_APPROVAL AS preliminaryDesignApproval,
        b.PROTECTION_TARGET AS protectionTarget,
        b.TOTAL_INVESTMENT AS totalInvestment,
        b.CONSTRUCTION_COST AS constructionCost,
        b.CENTRAL_SUBSIDY AS centralSubsidy,
        b.PERFORMANCE_TARGET_RESERVOIR_PLAN AS performanceTargetReservoirPlan,
        b.PERFORMANCE_TARGET_EMBANKMENT_PLAN AS performanceTargetEmbankmentPlan,
        b.PERFORMANCE_TARGET_CULVERT_PLAN AS performanceTargetCulvertPlan,
        b.PERFORMANCE_TARGET_HYDRO_PLAN AS performanceTargetHydroPlan,
        b.PERFORMANCE_TARGET_OTHER_PLAN AS performanceTargetOtherPlan,
        b.CONTACT_PERSON AS contactPerson,
        b.CONTACT_PHONE AS contactPhone,
        (
        SELECT
        COUNT(1)
        FROM
        LEDGER_REL_PROJECT_FUND c
        WHERE
        A.PROJECT_ID = C.PROJECT_ID
        AND a.FUNDING_BATCH = c.FUNDING_BATCH
        AND a.USAGE_DIRECTION = c.USAGE_DIRECTION
        AND a.IMPLEMENTATION_YEAR = c.IMPLEMENTATION_YEAR
        AND c.adcd = #{map.adcds}
        ) AS summaryStatus
        FROM
        LEDGER_CONSTRUCTION a
        LEFT JOIN LEDGER_PROJECT b ON a.PROJECT_ID = b.PROJECT_ID
        LEFT JOIN BSN_ADCD_B ad ON LEFT(b.ADCD, 4) + '***********' = ad.adcd
        LEFT JOIN BSN_ADCD_B adv ON b.ADCD = adv.adcd
        WHERE
        1 = 1
        <if test="map.adcd != null and map.adcd !=''">
            and left(a.adcd,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(a.adcd, 6) NOT IN ('220581')
        </if>
        <if test="map.projectName!= null and map.projectName != '' ">
            and CHARINDEX(#{map.projectName},b.PROJECT_NAME) >0
        </if>
        <if test="map.river!= null and map.river != '' ">
            and CHARINDEX(#{map.river},b.river) >0
        </if>
        <if test="map.implementationYear!=null and map.implementationYear!= ''">
            and a.IMPLEMENTATION_YEAR=#{map.implementationYear}
        </if>
        <if test="map.usageDirection!=null and map.usageDirection!= ''">
            and b.USAGE_DIRECTION=#{map.usageDirection}
        </if>
        <if test="map.fundingBatch!=null and map.fundingBatch!= ''">
            and a.FUNDING_BATCH=#{map.fundingBatch}
        </if>
        )
        SELECT * FROM Temp
        <if test="map.summaryStatus!=null and map.summaryStatus!= ''">
            WHERE summaryStatus=#{map.summaryStatus}
        </if>
        ORDER BY SORTNO ASC
    </select>


    <select id="selectProgectIds"  resultType="com.huitu.cloud.api.ewci.ledger.entity.LgConstruction">
        select * from LEDGER_CONSTRUCTION where PROJECT_ID = #{projectId}
    </select>

    <delete id="deleteProjectId">
        DELETE FROM LEDGER_CONSTRUCTION
        WHERE  PROJECT_ID = #{projectId}
    </delete>
</mapper>
