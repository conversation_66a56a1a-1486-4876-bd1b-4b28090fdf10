<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.monitor.mapper.PeripheralInfoDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getRainDataList" resultType="com.huitu.cloud.api.ewci.monitor.entity.RainData">
        SELECT T.STCD, B.STNM, B.STLC, T.ACCP, R1.RAINFALL RAINFALL1, R3.RAINFALL RAINFALL3, R6.RAINFALL RAINFALL6 FROM
        (SELECT A.STCD, SUM(A.DRP) ACCP FROM ST_PPTN_R A
        RIGHT JOIN (
        <choose>
            <when test='"ws".equalsIgnoreCase(map.type)'>
                SELECT DISTINCT ST.STCD FROM BSN_STBPRP_V ST
                LEFT JOIN IA_C_WSADCD AD ON AD.ADCD = ST.ADCD
                LEFT JOIN IA_C_WSADCD WS ON WS.WSCD = AD.WSCD
                WHERE ST.FRGRD != '8' AND ST.STCD != #{map.stcd} AND WS.ADCD = #{map.adcd}
            </when>
            <otherwise>
                SELECT DISTINCT STCD FROM BSN_STBPRP_V
                WHERE FRGRD != '8' AND STCD != #{map.stcd} AND LEFT(ADCD, 12) = LEFT(#{map.adcd}, 12)
            </otherwise>
        </choose>
        ) B ON B.STCD = A.STCD
        WHERE A.INTV = 1 AND A.TM >= #{map.stm} AND A.TM &lt;= #{map.etm}
        GROUP BY A.STCD) T
        LEFT JOIN BSN_STBPRP_V B ON T.STCD = B.STCD
        LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL FROM BSN_FORECASTRAIN_F
        WHERE RAIN_TIME >= #{map.fstm} AND RAIN_TIME &lt;= #{map.fetm1}
        GROUP BY STCD) R1 ON R1.STCD = T.STCD
        LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL FROM BSN_FORECASTRAIN_F
        WHERE RAIN_TIME >= #{map.fstm} AND RAIN_TIME &lt;= #{map.fetm3}
        GROUP BY STCD) R3 ON R3.STCD = T.STCD
        LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL FROM BSN_FORECASTRAIN_F
        WHERE RAIN_TIME >= #{map.fstm} AND RAIN_TIME &lt;= #{map.fetm6}
        GROUP BY STCD) R6 ON R6.STCD = T.STCD
        ORDER BY T.ACCP DESC, T.STCD ASC
    </select>
    <select id="getRsvrDataList" statementType="CALLABLE" resultType="com.huitu.cloud.api.ewci.monitor.entity.RsvrData">
        {CALL PROC_GET_RSVRDATA_ALL_NEW(#{map.stm, mode=IN}, #{map.etm, mode=IN}, #{map.whereSql, mode=IN})}
    </select>
    <select id="getRiverDataList" resultType="com.huitu.cloud.api.ewci.monitor.entity.RiverData">
        SELECT T.STCD, ST.STNM, ST.STLC, T.TM, R.TM, R.Z, (R.Z - F.WRZ) ZWRZ, F.WRZ
        FROM (SELECT A.STCD, MAX(A.TM) TM FROM ST_RIVER_R A
        RIGHT JOIN (
        <choose>
            <when test='"ws".equalsIgnoreCase(map.type)'>
                SELECT DISTINCT ST.STCD FROM BSN_STBPRP_V ST
                LEFT JOIN IA_C_WSADCD AD ON AD.ADCD = ST.ADCD
                LEFT JOIN IA_C_WSADCD WS ON WS.WSCD = AD.WSCD
                WHERE ST.STTP IN ('ZZ', 'ZQ') AND ST.FRGRD != '8' AND ST.STCD != #{map.stcd} AND WS.ADCD = #{map.adcd}
            </when>
            <otherwise>
                SELECT DISTINCT STCD FROM BSN_STBPRP_V
                WHERE STTP IN ('ZZ', 'ZQ') AND FRGRD != '8' AND STCD != #{map.stcd} AND LEFT(ADCD, 12) = LEFT(#{map.adcd}, 12)
            </otherwise>
        </choose>
        ) B ON B.STCD = A.STCD
        WHERE A.TM >= #{map.stm} AND A.TM &lt;= #{map.etm}
        GROUP BY A.STCD) T
        LEFT JOIN ST_RIVER_R R ON R.STCD = T.STCD AND R.TM = T.TM
        LEFT JOIN BSN_STBPRP_V ST ON ST.STCD = T.STCD
        LEFT JOIN ST_RVFCCH_B F ON F.STCD = T.STCD
        ORDER BY ZWRZ DESC, STCD ASC
    </select>
</mapper>
