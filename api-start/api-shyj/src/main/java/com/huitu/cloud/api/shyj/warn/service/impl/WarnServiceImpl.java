package com.huitu.cloud.api.shyj.warn.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.shyj.constants.ShyjConstants;
import com.huitu.cloud.api.shyj.warn.entity.*;
import com.huitu.cloud.api.shyj.warn.mapper.WarnrecordDao;
import com.huitu.cloud.api.shyj.warn.service.WarnService;
import com.huitu.cloud.constants.CommConstants;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import com.huitu.cloud.util.UUIDFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 政区预警记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-16
 */
@Service
public class WarnServiceImpl extends ServiceImpl<WarnrecordDao, WarnrecordR> implements WarnService {
    @Autowired
    private WarnrecordDao warnrecordDao;

    @Override
    public IPage<WarnRelatedInfo> getWarnByPage(String stm, String etm, String adcd, String queryType, String warnGradeId, String warnStatusId, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("queryType", queryType);
        param.put("warnGradeId", warnGradeId);
        param.put("warnStatusId", warnStatusId);
        IPage<WarnRelatedInfo> resultMap = warnrecordDao.getWarnByPage(page, param);
        return resultMap;
    }

    @Override
    public WarnStatisticsVo getWarnByCount(String stm, String etm, String adcd, String queryType, String warnGradeId, String warnStatusId) {
        WarnStatisticsVo warnSum = new WarnStatisticsVo();
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("queryType", queryType);
        param.put("warnGradeId", warnGradeId);
        param.put("warnStatusId", warnStatusId);
        warnSum.setWarnStatusLsit(warnrecordDao.getWarnStatusyCount(param));
        warnSum.setWarnGradeStatisticsList(warnrecordDao.getWrnGradeNmByCount(param));
        return warnSum;
    }

    @Override
    public WarnRelatedInfo getWarnById(String warnId) {
        WarnRelatedInfo warnRelatedInfo = warnrecordDao.getWarnById(warnId);
        return warnRelatedInfo;
    }

    @Override
    public WarnStatistics getAdWarnStatistics(String stm, String etm, String adcd, String queryType) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("zero", adcd.substring(level));
        param.put("level", level);
        param.put("level2", level);
        param.put("queryType", queryType);
        //统计本级政区预警信息
        List<WarnStatistics> list = getAdWarnList(param);
        WarnStatistics warnStatistics = null;
        if (list.size() > 0) {
            warnStatistics = list.get(0);
        }
        return warnStatistics;
    }

    @Override
    public List<WarnStatistics> getAdWarnStatisticsTreeList(String stm, String etm, String adcd, String queryType) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("zero", adcd.substring(level + (level < 6 ? 2 : 3)));
        param.put("level", level);
        param.put("level2", level + (level < 6 ? 2 : 3));
        param.put("queryType", queryType);
        //统计下级政区预警信息
        List<WarnStatistics> list = getAdWarnList(param);

        if (level == 2) {
            param.put("zero", adcd.substring(level + 4));
            param.put("level2", level + 4);
            List<WarnStatistics> list2 = getAdWarnList(param);
            List<WarnStatistics> list3 = new ArrayList<>();
            for (WarnStatistics x : list2) {
                if (!CommConstants.Public.MEIHEKOU_ADCD.equals(x.getAdcd()) && !CommConstants.Public.GONGZHULING_ADCD.equals(x.getAdcd())) {
                    list3.add(x);
                } else {
                    list.add(x);
                }
            }
            list.forEach(x -> {
                List<WarnStatistics> childList = getChildren(list3, x.getAdcd());
                x.setChildren(childList);
            });
        }
        return list;
    }

    @Override
    public WarnDanadStatistics getWarnDanadStatistics(String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        WarnDanadStatistics danadStatistics = warnrecordDao.getWarnDanadStatistics(param);
        return danadStatistics;
    }

    @Override
    public WarnPrevcntStatistics getWarnPrevcntStatistics(String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        WarnPrevcntStatistics prevcntStatistics = warnrecordDao.getWarnPrevcntStatistics(param);
        return prevcntStatistics;
    }

    @Override
    public WarnAdStatistics getWarnAdStatistics(String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        WarnAdStatistics adStatistics = warnrecordDao.getWarnAdStatistics(param);
        return adStatistics;
    }

    @Override
    public WarnWadingStatistics getWarnWadingStatistics(String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        WarnWadingStatistics wadingStatistics = warnrecordDao.getWarnWadingStatistics(param);
        return wadingStatistics;
    }

    @Override
    public WarnJczbxlxStatistics getWarnJczbxlxStatistics(String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        WarnJczbxlxStatistics jczbxlxStatistics = warnrecordDao.getWarnJczbxlxStatistics(param);
        return jczbxlxStatistics;
    }

    @Override
    public List<StwarnRelatedInfo> getStAdWarnById(String warnId) {
        List<StwarnRelatedInfo> result = warnrecordDao.getStAdWarnById(warnId);
        return result;
    }

    @Override
    public List<WarningstatusB> getWarnStatusList(Boolean isClose) {
        List<WarningstatusB> result = warnrecordDao.getWarnStatusList(isClose);
        return result;
    }

    @Override
    public List<WarninggradeB> getWarnGradeList() {
        List<WarninggradeB> result = warnrecordDao.getWarnGradeList();
        return result;
    }

    @Override
    public List<WarningtypeB> getWarnTypeList() {
        List<WarningtypeB> result = warnrecordDao.getWarnTypeList();
        return result;
    }

    @Override
    public List<RecordInfo> getWarnRecordById(String warnId) {
        List<RecordInfo> recordInfos = warnrecordDao.getWarnRecordById(warnId);
        if (recordInfos != null && recordInfos.size() > 0) {
        } else {
            recordInfos = warnrecordDao.getWarnStatusRecordById(warnId);
        }
        return recordInfos;
    }

    @Transactional
    @Override
    public String updateWarn(WarnrecordrEx warnrecordR) {
        String msg = "1";//预警处理成功 不改变状态
        WarnrecordR oldWarnRecordR = null;
        //查询县级预警表
        oldWarnRecordR = warnrecordDao.selectByPrimaryKey(warnrecordR.getWarnId());
        if (oldWarnRecordR.getWarnStatusId().equals(ShyjConstants.WarnConstants.STATUSTHIRTY.getValue())) {
            return ShyjConstants.WarnConstants.STATUSTHIRTY.getValue() + "";
        }
        // 使用@Transactional开启事务  多个sql同时提交事务 遇到异常同时回滚
        if (warnrecordR.getWarnStatusId().equals(ShyjConstants.WarnConstants.STATUSTEN.getValue())) {//内部预警
            oldWarnRecordR.setInnerWarnTm(warnrecordR.getInnerWarnTm());
            oldWarnRecordR.setInnerWarnUser(warnrecordR.getInnerWarnUser());
            oldWarnRecordR.setInnerWarnGradeId(warnrecordR.getInnerWarnGradeId());
            oldWarnRecordR.setInnerWarnDesc(warnrecordR.getInnerWarnDesc());
            oldWarnRecordR.setRemark(warnrecordR.getRemark());
            oldWarnRecordR.setIsFlash(0);
        }
        if (warnrecordR.getWarnStatusId().equals(ShyjConstants.WarnConstants.STATUSTWENTY.getValue())) {//外部预警
            oldWarnRecordR.setOutWarnTm(warnrecordR.getOutWarnTm());
            oldWarnRecordR.setOutWarnUser(warnrecordR.getOutWarnUser());
            oldWarnRecordR.setOutWarnGradeId(warnrecordR.getOutWarnGradeId());
            oldWarnRecordR.setOutWarnDesc(warnrecordR.getOutWarnDesc());
            oldWarnRecordR.setIsFlash(0);
            oldWarnRecordR.setRemark(warnrecordR.getRemark());
        }
        if (warnrecordR.getWarnStatusId().equals(ShyjConstants.WarnConstants.STATUSTHIRTY.getValue())) {//结束预警
            warnrecordR.setOccurTm(new Date());
            oldWarnRecordR.setRemark(warnrecordR.getRemark());
            oldWarnRecordR.setWarnEtm(new Date());
        }
        if (warnrecordR.getWarnStatusId().equals(ShyjConstants.WarnConstants.STATUSELEVEN.getValue())) {//会商研判
            warnrecordR.setOccurTm(new Date());
            WarnAlertConsultation warnAlertConsultation = new WarnAlertConsultation();
            warnAlertConsultation.setAlertId(warnrecordR.getWarnId());
            warnAlertConsultation.setConsultationInfo(warnrecordR.getConsultationInfo());
            warnAlertConsultation.setWriter(warnrecordR.getOccurUser());
            warnrecordDao.insertWarnAlertConsultation(warnAlertConsultation);
        }
        if (warnrecordR.getWarnStatusId().equals(ShyjConstants.WarnConstants.STATUSTWENTYONE.getValue())) {//跟踪督导
        }

        oldWarnRecordR.setOccurUser(warnrecordR.getOccurUser());
        if (warnrecordR.getWarnStatusId() > oldWarnRecordR.getWarnStatusId()) {
            oldWarnRecordR.setWarnStatusId(warnrecordR.getWarnStatusId());
            warnrecordDao.updateByPrimaryKey(oldWarnRecordR);
            warnrecordDao.insertWarnStatusrecordR(oldWarnRecordR);
            msg = "0";//预警处理成功 改变状态
        } else {
            warnrecordDao.updateByPrimaryKey(oldWarnRecordR);

        }
        oldWarnRecordR.setOccurTm(warnrecordR.getOccurTm());
        oldWarnRecordR.setWarnStatusId(warnrecordR.getWarnStatusId());
        warnrecordDao.insertWarnrecordT(oldWarnRecordR);
        return msg;
    }

    @Transactional
    @Override
    public String saveWarn(WarnrecordR warnrecordR) {
        String uuid = UUIDFactory.createUUID();
        warnrecordR.setWarnId(uuid);
        warnrecordR.setIsHandMade("1");
        warnrecordR.setWarnNm(warnrecordDao.getWarnNm());
        warnrecordDao.insertWarn(warnrecordR);
        warnrecordDao.insertWarnrecordT(warnrecordR);
        warnrecordDao.insertWarnStatusrecordR(warnrecordR);
        return "1";
    }

    /**
     * 获取树的子节点
     */
    private List<WarnStatistics> getChildren(List<WarnStatistics> list, String pcode) {
        // 通过父级编码子类
        List<WarnStatistics> childList = list.stream().filter(item -> item.getPadcd().equals(pcode)).collect(Collectors.toList());
        return childList;
    }

    /**
     * 统计政区预警信息
     */
    private List<WarnStatistics> getAdWarnList(Map<String, Object> param) {
        //统计政区预警立即转移，准备转移的数量
        List<WarnStatistics> list = warnrecordDao.getAdWarnGradeStatistics(param);
        //统计政区预警已经关闭的数量
        List<WarnStatistics> list2 = warnrecordDao.getAdWarnCloseStatistics(param);
        //统计政区预警发送短信条数
        List<WarnStatistics> list3 = warnrecordDao.getAdWarnMessageStatistics(param);
        Map<String, WarnStatistics> closeMap = list2.stream().collect(Collectors.toMap(WarnStatistics::getAdcd, Function.identity()));
        Map<String, WarnStatistics> messageMap = list3.stream().collect(Collectors.toMap(WarnStatistics::getAdcd, Function.identity()));
        list.forEach(item -> {
            String adcdKey = item.getAdcd();
            if (closeMap.containsKey(adcdKey)) {
                WarnStatistics close = closeMap.get(adcdKey);
                item.setCloseTotal(close.getCloseTotal());
                item.setOpenTotal(item.getAllTotal() - close.getCloseTotal());
            } else {
                item.setCloseTotal(0);
                item.setOpenTotal(item.getAllTotal());
            }
            if (messageMap.containsKey(adcdKey)) {
                WarnStatistics message = messageMap.get(adcdKey);
                item.setTotalMassage(message.getTotalMassage());
            } else {
                item.setTotalMassage(0);
            }
        });
        return list;
    }

    @Override
    public WarnInfoByYwmh getWarnInfoByYwmh(String adcd, String stm, String etm) {
        WarnInfoByYwmh warnInfoByYwmh = new WarnInfoByYwmh();
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("zero", adcd.substring(level + (level < 6 ? 2 : 3)));
        param.put("level", level);
        param.put("level2", level + (level < 6 ? 2 : 3));
        //统计下级政区预警信息
        List<WarnStatistics> list = getAdWarnList(param);
        //市数量
        Integer cityTotal = 0;
        //县数量
        Integer countyTotal = 0;
        //总数量
        Integer allTotal = 0;
        for (WarnStatistics x : list) {
            int adLevel = AdcdUtil.getAdLevel(x.getAdcd());
            if ("4".equals(adLevel + "")) {
                cityTotal++;
                allTotal += x.getAllTotal();
            } else if ("6".equals(adLevel + "")) {
                if (CommConstants.Public.MEIHEKOU_ADCD.equals(x.getAdcd()) || CommConstants.Public.GONGZHULING_ADCD.equals(x.getAdcd())) {
                    cityTotal++;
                    allTotal += x.getAllTotal();
                } else {
                    countyTotal++;
                }

            }
        }
        warnInfoByYwmh.setCityTotal(cityTotal);
        warnInfoByYwmh.setCountyTotal(countyTotal);
        warnInfoByYwmh.setAllTotal(allTotal);
        return warnInfoByYwmh;
    }

    @Override
    public void ExportWarnByPage(String stm, String etm, String adcd, String queryType, String warnGradeId, String warnStatusId, String type) {
        IPage<WarnRelatedInfo> result = getWarnByPage(stm, etm, adcd, queryType, warnGradeId, warnStatusId, 0, -1);
        List<WarnRelatedInfo> list = result.getRecords();
        if ("1".equals(type)) {
            ExcelExportUtil.execute(list, "综合监视山洪预警");
        } else if ("2".equals(type)) {
            ExcelExportUtil.execute(list, "预警信息");
        }

    }

    @Override
    public WarnFeedback getWarnFeedbackById(String warnId) {
        WarnFeedback feedback = warnrecordDao.getWarnFeedbackById(warnId);
        return feedback;
    }

    @Override
    public Integer addOrUpdateWarnFeedback(WarnFeedback feedback) {
        if (feedback.getVcode() == null || "".equals(feedback.getVcode())) {
            feedback.setVcode(UUID.randomUUID().toString().replaceAll("-", ""));
            return warnrecordDao.addWarnFeedback(feedback);
        } else {
            return warnrecordDao.updateWarnFeedback(feedback);
        }
    }

    @Override
    public WarnDisasterSituationReport getWarnDisAsterSituationById(String warnId) {
        WarnDisasterSituationReport report = warnrecordDao.getWarnDisAsterSituationById(warnId);
        return report;
    }

    @Override
    public Integer addOrUpdatearnDisAsterSituation(WarnDisasterSituationReport report) {
        if (report.getVcode() == null || "".equals(report.getVcode())) {
            report.setVcode(UUID.randomUUID().toString().replaceAll("-", ""));
            return warnrecordDao.addWarnDisAsterSituation(report);
        } else {
            return warnrecordDao.updateWarnDisAsterSituation(report);
        }
    }

    @Override
    public IPage<WarnShPersonVo> getShPersonList(WarnShPersonQuery query) {
        Page page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<WarnShPersonVo> iPage = warnrecordDao.getShPersonList(page, query.getAdcd());
        return iPage;
    }

}
