package com.huitu.cloud.api.hsfxt.rsvr.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.hsfxt.rsvr.entity.KbRvsanalyG;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmInfo;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmPic;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrObjinfo;
import com.huitu.cloud.api.hsfxt.rsvr.service.FxtRsvrService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 风险图-小水库 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */

@RestController
@Api(tags = "风险图-小水库接口")
@RequestMapping("/api/hsfxt/rsvr")
public class FxtRsvrResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "6ee40744-ef51-42c5-8c78-1da6ab51427c";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    FxtRsvrService fxtRsvrService;

    @ApiOperation(value = "查询小水库风险信息", notes = "查询小水库风险信息")
    @GetMapping(value = "select-fxt-rsvr-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "name", value = "水库名称", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<KbRvsanalyG>>> getFxtRsvrList(@RequestParam String adcd, @RequestParam String name, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<KbRvsanalyG> list = fxtRsvrService.getFxtRsvrList(adcd, name, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询小水库河道断面信息", notes = "查询小水库河道断面信息")
    @GetMapping(value = "select-fxt-rsvr-dm-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cid", value = "成果编码", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrDmInfo>>> getFxtRsvrDmList(@RequestParam String cid) throws Exception {
        List<RsvrDmInfo> list = fxtRsvrService.getFxtRsvrDmList(cid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "小水库河道断面图", notes = "小水库河道断面图")
    @GetMapping(value = "select-fxt-rsvr-dm-pic")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cid", value = "成果编码", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrDmPic>>> getFxtRsvrDmPic(@RequestParam String cid) throws Exception {
        List<RsvrDmPic> list = fxtRsvrService.getFxtRsvrDmPic(cid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询测站对应的风险信息", notes = "查询测站对应的风险信息")
    @GetMapping(value = "get-fxt-rsvr-list-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<KbRvsanalyG>>> getFxtRsvrListByStcd(@RequestParam String stcd) throws Exception {
        List<KbRvsanalyG> list = fxtRsvrService.getFxtRsvrListByStcd(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询测站防治区信息", notes = "查询测站防治区信息")
    @GetMapping(value = "get-fzq-rsvr-list-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrObjinfo>>> getFzqRsvrListByStcd(@RequestParam String stcd) throws Exception {
        List<RsvrObjinfo> list = fxtRsvrService.getFzqRsvrListByStcd(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
}
