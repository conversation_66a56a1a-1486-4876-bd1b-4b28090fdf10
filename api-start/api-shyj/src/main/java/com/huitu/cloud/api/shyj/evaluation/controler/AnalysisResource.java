package com.huitu.cloud.api.shyj.evaluation.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import com.huitu.cloud.api.shyj.evaluation.service.AnalysisService;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 分析评价
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@RestController
@Api(tags = "分析评价接口")
@RequestMapping("/api/shyj/eton/fxpj")
@Validated
public class AnalysisResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "d988a11a-c2ca-49be-8f2d-51f8dd90dd13";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private AnalysisService analysisService;

    @ApiOperation(value = "查询设计暴雨信息", notes = "分页查询设计暴雨列表信息")
    @GetMapping(value = "select-desntb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaDesntb>>> getDesntbList(@RequestParam String wsnm, @RequestParam String adcd, @RequestParam String wscd,@RequestParam String type, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaDesntb> list = analysisService.getDesntbList(wsnm, adcd,wscd, type, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询设计暴雨小流域统计", notes = "查询设计暴雨小流域统计")
    @GetMapping(value = "select-desntb-sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Map<String,Object>>> getDesntbSum(@RequestParam String wsnm, @RequestParam String adcd, @RequestParam String wscd, @RequestParam String type) throws Exception {
        Map<String,Object> sum = analysisService.getDesntbSum(wsnm, adcd,wscd, type);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", sum));
    }

    @ApiOperation(value = "导出设计暴雨信息", notes = "导出设计暴雨列表信息")
    @GetMapping(value = "export-desntb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wscd", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String")
    })
    public void exportDesntbList(@RequestParam String wsnm, @RequestParam String adcd,@RequestParam String wscd, @RequestParam String type) throws Exception {
        analysisService.exportDesntbList(wsnm, adcd,wscd, type);
    }

    @ApiOperation(value = "查询小流域汇流时间设计暴雨时程信息", notes = "分页查询小流域汇流时间设计暴雨时程分配表列表信息")
    @GetMapping(value = "select-hlsjtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wsnm", value = "流域名称",  dataType = "String"),
            @ApiImplicitParam(name = "radio", value = "查询条件",  dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaHlsjtb>>> getHlsjtb(@RequestParam String wsnm, @RequestParam String radio, @RequestParam String adcd,  @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaHlsjtb> list = analysisService.getHlsjtb(wsnm,radio,adcd,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询小流域汇流时间设计暴雨时程信息流域统计", notes = "查询小流域汇流时间设计暴雨时程信息流域统计")
    @GetMapping(value = "select-hlsjtb-sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wsnm", value = "流域名称",  dataType = "String"),
            @ApiImplicitParam(name = "radio", value = "查询条件",  dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Map<String,Object>>> getHlsjtbSum(@RequestParam String wsnm, @RequestParam String radio, @RequestParam String adcd) throws Exception {
        Map<String,Object> sum = analysisService.getHlsjtbSum(wsnm,radio,adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", sum));
    }

    @ApiOperation(value = "导出小流域汇流时间设计暴雨信息", notes = "导出小流域汇流时间设计暴雨信息")
    @GetMapping(value = "export-hlsjtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wsnm", value = "流域名称",  dataType = "String"),
            @ApiImplicitParam(name = "radio", value = "查询条件",  dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
    })
    public void exportHlsjtb(@RequestParam String wsnm, @RequestParam String radio, @RequestParam String adcd) throws Exception {
        analysisService.exportHlsjtb(wsnm,radio,adcd);
    }

    @ApiOperation(value = "查询控制断面设计洪水信息", notes = "分页查询控制断面设计洪水列表信息")
    @GetMapping(value = "select-sdtdtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称",  dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码",  dataType = "String"),
            @ApiImplicitParam(name = "radio", value = "查询条件",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaSdtdtb>>> getSdtdtb(@SqlInjection @RequestParam String adcd, @RequestParam String wsnm, @RequestParam String wscd, @RequestParam String radio, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaSdtdtb> list = analysisService.getSdtdtb(adcd,wsnm,wscd,radio,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "控制断面设计洪水防治区统计", notes = "控制断面设计洪水防治区统计")
    @GetMapping(value = "select-sdtdtb-sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称",  dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码",  dataType = "String"),
            @ApiImplicitParam(name = "radio", value = "查询条件",  dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Map<String,Object>>> getSdtdtbSum(@SqlInjection @RequestParam String adcd, @RequestParam String wsnm, @RequestParam String wscd, @RequestParam String radio) throws Exception {
        Map<String,Object> sum = analysisService.getSdtdtbSum(adcd,wsnm,wscd,radio);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", sum));
    }

    @ApiOperation(value = "导出控制断面设计洪水信息", notes = "导出控制断面设计洪水列表信息")
    @GetMapping(value = "export-sdtdtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称",  dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码",  dataType = "String"),
            @ApiImplicitParam(name = "radio", value = "查询条件",  dataType = "String")
    })
    public void exportSdtdtb(@RequestParam String adcd, @RequestParam String wsnm, @RequestParam String wscd,@RequestParam String radio) throws Exception {
        analysisService.exportSdtdtb(adcd,wsnm,wscd,radio);
    }

    @ApiOperation(value = "查询控制断面水位-流量-人口关系表信息", notes = "分页查询控制断面水位-流量-人口关系表列表信息")
    @GetMapping(value = "select-swllrktb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Page<IaSwllrktb>>> getSwllrktb(@RequestParam String adcd, @RequestParam String wsnm, @RequestParam String wscd,@RequestParam String type, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaSwllrktb> list = analysisService.getSwllrktb(adcd, wsnm,wscd, type, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询控制断面水位-流量-人口关系表统计政区、小流域", notes = "查询控制断面水位-流量-人口关系表统计政区、小流域")
    @GetMapping(value = "select-swllrktb-sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Map<String,Object>>> getSwllrktbSum(@RequestParam String adcd, @RequestParam String wsnm, @RequestParam String wscd,@RequestParam String type) throws Exception {
        Map<String,Object> list = analysisService.getSwllrktbSum(adcd, wsnm,wscd, type);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出控制断面水位-流量-人口关系信息", notes = "导出控制断面水位-流量-人口关系信息")
    @GetMapping(value = "export-swllrktb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String")
    })
    public void exportSwllrktb(@RequestParam String adcd, @RequestParam String wsnm, @RequestParam String wscd,@RequestParam String type) throws Exception {
        analysisService.exportSwllrktb(adcd, wsnm,wscd, type);
    }

    @ApiOperation(value = "查询防洪现状评价表信息", notes = "分页查询防洪现状评价表列表信息")
    @GetMapping(value = "select-nowfhtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaNowfhtb>>> getNowfhtb(@RequestParam String adcd, @RequestParam String wsnm, @RequestParam String wscd,@RequestParam String type, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaNowfhtb> list = analysisService.getNowfhtb(adcd, wsnm,wscd, type, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "统计防洪现状评价表政区、小流域", notes = "统计防洪现状评价表政区、小流域")
    @GetMapping(value = "select-nowfhtb-sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Map<String,Object>>> getNowfhtbSum(@RequestParam String adcd, @RequestParam String wsnm, @RequestParam String wscd,@RequestParam String type) throws Exception {
        Map<String,Object> list = analysisService.getNowfhtbSum(adcd, wsnm,wscd, type);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出防洪现状评价信息", notes = "导出防洪现状评价信息")
    @GetMapping(value = "export-nowfhtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "wscd", value = "流域编码",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String")
    })
    public void exportNowfhtb(@RequestParam String adcd, @RequestParam String wsnm,@RequestParam String wscd, @RequestParam String type) throws Exception {
        analysisService.exportNowfhtb(adcd, wsnm,wscd, type);
    }

    @ApiOperation(value = "查询临界雨量经验估值法成果表信息", notes = "分页查询临界雨量经验估值法成果表信息")
    @GetMapping(value = "select-jygstb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaJygstb>>> getJygstb(@SqlInjection @RequestParam String adcd, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaJygstb> list = analysisService.getJygstb(adcd, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出临界雨量经验估值法成果信息", notes = "导出临界雨量经验估值法成果信息")
    @GetMapping(value = "export-jygstb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String")
    })
    public void exportJygstb(@RequestParam String adcd) throws Exception {
        analysisService.exportJygstb(adcd);
    }

    @ApiOperation(value = "查询临界雨量降雨分析法成果表信息", notes = "分页查询临界雨量降雨分析法成果表信息")
    @GetMapping(value = "select-yjcjtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaYjcjtb>>> getYjcjtb(@SqlInjection @RequestParam String adcd, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaYjcjtb> list = analysisService.getYjcjtb(adcd, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出临界雨量降雨分析法成果信息", notes = "导出临界雨量降雨分析法成果信息")
    @GetMapping(value = "export-yjcjtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String")
    })
    public void exportYjcjtb(@RequestParam String adcd) throws Exception {
        analysisService.exportYjcjtb(adcd);
    }

    @ApiOperation(value = "查询临界雨量模型分析法信息", notes = "分页查询临界雨量模型分析法信息")
    @GetMapping(value = "select-fxcgtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaFxcgtb>>> getFxcgtb(@RequestParam String adcd, @RequestParam String type, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaFxcgtb> list = analysisService.getFxcgtb(adcd,type, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询临界雨量模型分析法信息防治区统计", notes = "查询临界雨量模型分析法信息防治区统计")
    @GetMapping(value = "select-fxcgtb-sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Map<String,Object>>> getFxcgtbSum(@RequestParam String adcd, @RequestParam String type) throws Exception {
        Map<String,Object> sum = analysisService.getFxcgtbSum(adcd,type);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", sum));
    }

    @ApiOperation(value = "导出临界雨量模型分析法成果信息", notes = "导出临界雨量模型分析法成果信息")
    @GetMapping(value = "export-fxcgtb-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "调查评价类型", dataType = "String")
    })
    public void exportFxcgtb(@RequestParam String adcd,@RequestParam String type) throws Exception {
        analysisService.exportFxcgtb(adcd, type);
    }

    @ApiOperation(value = "查询预警指标时段雨量表信息", notes = "分页查询预警指标时段雨量表列表信息")
    @GetMapping(value = "select-dfwrule-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaDfwrule>>> getDfwrule(@SqlInjection @RequestParam String adcd, @RequestParam String wsnm, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaDfwrule> list = analysisService.getDfwrule(adcd, wsnm, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "统计预警指标时段雨量表防治区", notes = "统计预警指标时段雨量表防治区")
    @GetMapping(value = "select-dfwrule-sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Map<String,Object>>> getDfwruleSum(@SqlInjection @RequestParam String adcd, @RequestParam String wsnm) throws Exception {
        Map<String,Object> list = analysisService.getDfwruleSum(adcd, wsnm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询测站对应预警指标时段雨量表信息", notes = "查询测站对应预警指标时段雨量表信息")
    @GetMapping(value = "select-dfwrule-list-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码",  dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<IaDfwruleVo>>> getDfwruleListByStcd(@RequestParam String stcd) throws Exception {
        List<IaDfwruleVo> list = analysisService.getDfwruleByStcd(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出预警指标时段雨量信息", notes = "导出预警指标时段雨量信息")
    @GetMapping(value = "export-dfwrule-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String")
    })
    public void exportDfwrule(@RequestParam String adcd, @RequestParam String wsnm) throws Exception {
        analysisService.exportDfwrule(adcd, wsnm);
    }

    @ApiOperation(value = "查询预警指标综合雨量信息", notes = "分页查询预警指标综合雨量信息")
    @GetMapping(value = "select-yjicr-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "radio", value = "查询条件",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaYjicr>>> getYjicr(@SqlInjection @RequestParam String adcd, @RequestParam String radio,@RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaYjicr> list = analysisService.getYjicr(adcd,radio,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询预警指标综合雨量信息防治区统计", notes = "查询预警指标综合雨量信息防治区统计")
    @GetMapping(value = "select-yjicr-sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "radio", value = "查询条件",  dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Map<String,Object>>> getYjicrSum(@SqlInjection @RequestParam String adcd, @RequestParam String radio) throws Exception {
        Map<String,Object> list = analysisService.getYjicrSum(adcd,radio);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出预警指标综合雨量信息", notes = "导出预警指标综合雨量信息")
    @GetMapping(value = "export-yjicr-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "radio", value = "查询条件",  dataType = "String")
    })
    public void exportYjicr(@RequestParam String adcd, @RequestParam String radio) throws Exception {
        analysisService.exportYjicr(adcd,radio);
    }

    @ApiOperation(value = "查询预警指标水位信息", notes = "分页查询预警指标水位信息")
    @GetMapping(value = "select-wlwrule-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<IaWlwrule>>> getWlwrule(@SqlInjection @RequestParam String adcd, @RequestParam String wsnm, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<IaWlwrule> list = analysisService.getWlwrule(adcd, wsnm, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询预警指标水位信息防治区统计", notes = "查询预警指标水位信息防治区统计")
    @GetMapping(value = "select-wlwrule-sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Map<String,Object>>> getWlwruleSum(@SqlInjection @RequestParam String adcd, @RequestParam String wsnm) throws Exception {
        Map<String,Object> sum = analysisService.getWlwruleSum(adcd, wsnm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", sum));
    }

    @ApiOperation(value = "导出预警指标水位信息", notes = "导出预警指标水位信息")
    @GetMapping(value = "export-wlwrule-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "wsnm", value = "流域名称", dataType = "String")
    })
    public void exportWlwrule(@RequestParam String adcd, @RequestParam String wsnm) throws Exception {
        analysisService.exportWlwrule(adcd, wsnm);
    }

    @ApiOperation(value = "查询计算单元(防灾对象)信息", notes = "分页查询计算单元(防灾对象)信息")
    @GetMapping(value = "select-unitdp-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<BnsIaUnitdp>>> getUnitdp(@RequestParam String adcd, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<BnsIaUnitdp> list = analysisService.getUnitdp(adcd, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出计算单元(防灾对象)信息", notes = "导出计算单元(防灾对象)信息")
    @GetMapping(value = "export-unitdp-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String")
    })
    public void exportUnitdp(@RequestParam String adcd) throws Exception {
        analysisService.exportUnitdp(adcd);
    }

    @ApiOperation(value = "查询重点城(集)镇计算单元（小流域）信息", notes = "分页查询重点城(集)镇计算单元（小流域）信息")
    @GetMapping(value = "select-wlwrule-getUnitws")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wsnm", value = "流域名称",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<BnsIaUnitws>>> getUnitws(@RequestParam String wsnm, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<BnsIaUnitws> list = analysisService.getUnitws(wsnm,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出重点城(集)镇计算单元（小流域）信息", notes = "导出重点城(集)镇计算单元（小流域）信息")
    @GetMapping(value = "export-wlwrule-unitwsList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wsnm", value = "流域名称",  dataType = "String")
    })
    public void exportUnitws(@RequestParam String wsnm) throws Exception {
        analysisService.exportUnitws(wsnm);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价汇总", notes = "分页查询重点城(集)镇调查评价汇总")
    @GetMapping(value = "select-wlwrule-getCjzSummary")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<DcjzSummary>>> getCjzSummary(@RequestParam String adcd, @RequestParam String zdadnm, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzSummary> list = analysisService.getCjzSummary(adcd,zdadnm,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出重点城(集)镇调查评价汇总", notes = "导出重点城(集)镇调查评价汇总")
    @GetMapping(value = "export-wlwrule-cjzSummaryList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadcd", value = "城集镇名称",  dataType = "String")
    })
    public void exportCjzSummary(@RequestParam String adcd, @RequestParam String zdadnm) throws Exception {
        analysisService.exportCjzSummary(adcd,zdadnm);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价涉水工程-水库信息列表", notes = "分页查询重点城(集)镇调查评价涉水工程-水库信息列表")
    @GetMapping(value = "select-wlwrule-cjzReservoit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "rsName", value = "水库名称",  dataType = "String"),
            @ApiImplicitParam(name = "engGrad", value = "工程规模",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<DcjzReservoitSs>>> getCjzReservoit(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String rsName,@RequestParam String engGrad, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzReservoitSs> list = analysisService.getCjzReservoit(adcd,zdadnm,rsName,engGrad,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出重点城(集)镇调查评价涉水工程-水库信息列表", notes = "导出重点城(集)镇调查评价涉水工程-水库信息列表")
    @GetMapping(value = "export-wlwrule-CjzReservoitList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "rsName", value = "水库名称",  dataType = "String"),
            @ApiImplicitParam(name = "engGrad", value = "工程规模",  dataType = "String")
    })
    public void exportCjzReservoit(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String rsName,@RequestParam String engGrad) throws Exception {
        analysisService.exportCjzReservoit(adcd,zdadnm,rsName,engGrad);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价涉水工程-水闸信息列表", notes = "分页查询重点城(集)镇调查评价涉水工程-水闸信息列表")
    @GetMapping(value = "select-wlwrule-cjzSluice")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "gateName", value = "水闸名称",  dataType = "String"),
            @ApiImplicitParam(name = "gateType", value = "水闸类型",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<DcjzSluiceSs>>> getCjzSluice(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String gateName,@RequestParam String gateType, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzSluiceSs> list = analysisService.getCjzSluice(adcd,zdadnm,gateName,gateType,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出重点城(集)镇调查评价涉水工程-水闸信息列表", notes = "导出重点城(集)镇调查评价涉水工程-水闸信息列表")
    @GetMapping(value = "export-wlwrule-CjzSluiceList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "gateName", value = "水闸名称",  dataType = "String"),
            @ApiImplicitParam(name = "gateType", value = "水闸类型",  dataType = "String")
    })
    public void exportCjzSluice(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String gateName,@RequestParam String gateType) throws Exception {
        analysisService.exportCjzSluice(adcd,zdadnm,gateName,gateType);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价涉水工程-堤防信息列表", notes = "分页查询重点城(集)镇调查评价涉水工程-堤防信息列表")
    @GetMapping(value = "select-wlwrule-cjzDfDike")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "dikeName", value = "堤防名称",  dataType = "String"),
            @ApiImplicitParam(name = "dikeGrad", value = "堤防类型",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<DcjzDfDikeSs>>> getDfDike(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String dikeName,@RequestParam String dikeGrad, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzDfDikeSs> list = analysisService.getDfDike(adcd,zdadnm,dikeName,dikeGrad,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价涉水工程-堤防信息列表", notes = "导出询重点城(集)镇调查评价涉水工程-堤防信息列表")
    @GetMapping(value = "export-wlwrule-CjzDfDikeList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "dikeName", value = "堤防名称",  dataType = "String"),
            @ApiImplicitParam(name = "dikeGrad", value = "堤防类型",  dataType = "String")
    })
    public void exportCjzDfDike(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String dikeName,@RequestParam String dikeGrad) throws Exception {
        analysisService.exportCjzDfDike(adcd,zdadnm,dikeName,dikeGrad);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价涉水工程-路涵信息列表", notes = "分页查询重点城(集)镇调查评价涉水工程-路涵信息列表")
    @GetMapping(value = "select-wlwrule-CjzLhZdjz")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "culname", value = "路涵名称",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "类型",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<DcjzLhRCSs>>> getLhZdjz(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String culname,@RequestParam String type, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzLhRCSs> list = analysisService.getLhZdjz(adcd,zdadnm,culname,type,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价涉水工程-路涵信息列表", notes = "导出询重点城(集)镇调查评价涉水工程-路涵信息列表")
    @GetMapping(value = "export-wlwrule-CjzLhZdjzist")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "culname", value = "路涵名称",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "类型",  dataType = "String")
    })
    public void exportCjzLhZdjzist(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String culname,@RequestParam String type) throws Exception {
        analysisService.exportCjzLhZdjzist(adcd,zdadnm,culname,type);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价涉水工程-桥梁信息列表", notes = "分页查询重点城(集)镇调查评价涉水工程-桥梁信息列表")
    @GetMapping(value = "select-wlwrule-CjzBridgeQl")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "brname", value = "桥梁名称",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "类型",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<DcjzQlBridgeSs>>> getBridgeQl(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String brname,@RequestParam String type, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzQlBridgeSs> list = analysisService.getBridgeQl(adcd,zdadnm,brname,type,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价涉水工程-桥梁信息列表", notes = "导出询重点城(集)镇调查评价涉水工程-桥梁信息列表")
    @GetMapping(value = "export-wlwrule-exportCjzBridgeQl")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "brname", value = "桥梁名称",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "类型",  dataType = "String"),
    })
    public void exportCjzBridgeQl(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String brname,@RequestParam String type) throws Exception {
        analysisService.exportCjzBridgeQl(adcd,zdadnm,brname,type);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价涉水工程-塘坝信息列表", notes = "分页查询重点城(集)镇调查评价涉水工程-塘坝信息列表")
    @GetMapping(value = "select-wlwrule-CjzPondTb")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "damname", value = "塘坝名称",  dataType = "String"),
            @ApiImplicitParam(name = "mt", value = "类型",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<DcjzTbPondSs>>> getPondTb(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String damname,@RequestParam String mt, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzTbPondSs> list = analysisService.getPondTb(adcd,zdadnm,damname,mt,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价涉水工程-塘坝信息列表", notes = "导出询重点城(集)镇调查评价涉水工程-塘坝信息列表")
    @GetMapping(value = "export-wlwrule-exportCjzPondTb")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "damname", value = "塘坝名称",  dataType = "String"),
            @ApiImplicitParam(name = "mt", value = "类型",  dataType = "String")
    })
    public void exportCjzPondTb(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String damname,@RequestParam String mt) throws Exception {
        analysisService.exportCjzPondTb(adcd,zdadnm,damname,mt);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价设备信息-自动检测站", notes = "分页查询重点城(集)镇调查评价设备信息-自动检测站")
    @GetMapping(value = "select-wlwrule-CjzZdjczCz")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "stnm", value = "测站名称",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<DcjzStationSs>>> getZdjczCz(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String stnm,@RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzStationSs> list = analysisService.getZdjczCz(adcd,zdadnm,stnm,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价设备信息-自动检测站", notes = "导出询重点城(集)镇调查评价设备信息-自动检测站")
    @GetMapping(value = "export-wlwrule-exportZdjczCz")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "stnm", value = "测站名称",  dataType = "String")
    })
    public void exportZdjczCz(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String stnm) throws Exception {
        analysisService.exportZdjczCz(adcd,zdadnm,stnm);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价设备信息-无线预警广播", notes = "分页查询重点城(集)镇调查评价设备信息-无线预警广播")
    @GetMapping(value = "select-wlwrule-CjzRadioGb")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "address", value = "地址",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "设备类型",  dataType = "Integer"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public  ResponseEntity<SuccessResponse<Page<DcjzRadioGbSs>>>  getRadioGb(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String address,@RequestParam Integer type,@RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzRadioGbSs> list = analysisService.getRadioGb(adcd,zdadnm,address,type,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价设备信息-无线预警广播", notes = "导出询重点城(集)镇调查评价设备信息-无线预警广播")
    @GetMapping(value = "export-wlwrule-exportRadioGb")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "address", value = "地址",  dataType = "String"),
            @ApiImplicitParam(name = "type", value = "设备类型",  dataType = "Integer")
    })
    public void exportRadioGb(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String address,@RequestParam Integer type ) throws Exception {
        analysisService.exportRadioGb(adcd,zdadnm,address,type);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价设备信息-简易雨量站", notes = "分页查询重点城(集)镇调查评价设备信息-简易雨量站")
    @GetMapping(value = "select-wlwrule-CjzSimpleJyyl")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "address", value = "地址",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public  ResponseEntity<SuccessResponse<Page<DcjzSimpleJyylSs>>> getSimpleJyyl(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String address,@RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzSimpleJyylSs> list = analysisService.getSimpleJyyl(adcd,zdadnm,address,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价设备信息-简易雨量站", notes = "导出询重点城(集)镇调查评价设备信息-简易雨量站")
    @GetMapping(value = "export-wlwrule-exportSimpleJyyl")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "address", value = "地址",  dataType = "String")
    })
    public void exportSimpleJyyl(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String address) throws Exception {
        analysisService.exportSimpleJyyl(adcd,zdadnm,address);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价设备信息-简易水位站", notes = "分页查询重点城(集)镇调查评价设备信息-简易水位站")
    @GetMapping(value = "select-wlwrule-CjzWLJysw")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "address", value = "地址",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public  ResponseEntity<SuccessResponse<Page<DcjzWLJysw>>> getWLJysw(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String address,@RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzWLJysw> list = analysisService.getWLJysw(adcd,zdadnm,address,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价设备信息-简易水位站", notes = "导出询重点城(集)镇调查评价设备信息-简易水位站")
    @GetMapping(value = "export-wlwrule-exportWLJysw")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "address", value = "地址",  dataType = "String")
    })
    public void exportWLJysw(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam String address) throws Exception {
        analysisService.exportWLJysw(adcd,zdadnm,address);
    }

    @ApiOperation(value = "查询重点城(集)镇调查评价-河道纵断面信息", notes = "分页查询重点城(集)镇调查评价-河道纵断面信息")
    @GetMapping(value = "select-wlwrule-CjzHdzdm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public  ResponseEntity<SuccessResponse<Page<DcjzHdzdm>>> getHdzdm(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzHdzdm> list = analysisService.getHdzdm(adcd,zdadnm,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价-河道纵断面信息", notes = "导出询重点城(集)镇调查评价-河道纵断面信息")
    @GetMapping(value = "export-wlwrule-exportHdzdm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String")
    })
    public void exportHdzdm(@RequestParam String adcd, @RequestParam String zdadnm) throws Exception {
        analysisService.exportHdzdm(adcd,zdadnm);
    }


    @ApiOperation(value = "查询重点城(集)镇调查评价-河道横断面信息", notes = "分页查询重点城(集)镇调查评价-河道横断面信息")
    @GetMapping(value = "select-wlwrule-CjzHdhdm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public  ResponseEntity<SuccessResponse<Page<DcjzHdhdm>>> getCjzHdhdm(@RequestParam String adcd, @RequestParam String zdadnm,@RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<DcjzHdhdm> list = analysisService.getCjzHdhdm(adcd,zdadnm,pageNum,pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出询重点城(集)镇调查评价-河道横断面信息", notes = "导出询重点城(集)镇调查评价-河道横断面信息")
    @GetMapping(value = "export-wlwrule-exportHdhdm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "zdadnm", value = "城集镇名称",  dataType = "String")
    })
    public void exportHdhdm(@RequestParam String adcd, @RequestParam String zdadnm) throws Exception {
        analysisService.exportHdhdm(adcd,zdadnm);
    }



}
