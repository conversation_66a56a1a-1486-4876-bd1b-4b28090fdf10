package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * <p>
 * 危险区基本情况统计
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-16
 */
@ApiModel(value="DtIaCDanad对象", description="危险区基本情况调查成果统计")
public class DtIaDanad extends DtExtendInfo{

    @ApiModelProperty(value = "行政区划代码")
    private String adcd;

    @ApiModelProperty(value = "危险区个数")
    private Double danct;

    @ApiModelProperty(value = "危险区内人口（人）")
    private Double ptcount;

    @ApiModelProperty(value = "危险区内总户数（户）")
    private Double etcount;

    @ApiModelProperty(value = "危险区内Ⅰ类经济户数（户）")
    private Double ecount1;

    @ApiModelProperty(value = "危险区内Ⅱ类经济户数（户）")
    private Double ecount2;

    @ApiModelProperty(value = "危险区内Ⅲ类经济户数（户）")
    private Double ecount3;

    @ApiModelProperty(value = "危险区内Ⅳ类经济户数（户）")
    private Double ecount4;

    @ApiModelProperty(value = "危险区内总房屋数（座）")
    private Double htcount;

    @ApiModelProperty(value = "危险区内Ⅰ类房屋数（座）")
    private Double hcount1;

    @ApiModelProperty(value = "危险区内Ⅱ类房屋数（座）")
    private Double hcount2;

    @ApiModelProperty(value = "危险区内Ⅲ类房屋数（座）")
    private Double hcount3;

    @ApiModelProperty(value = "危险区内Ⅳ类房屋数（座）")
    private Double hcount4;

    @ApiModelProperty(value = "企事业单位个数")
    private Double bsct;

    @ApiModelProperty(value = "下级统计")
    private List<DtIaDanad> children;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getDanct() {
        return danct;
    }

    public void setDanct(Double danct) {
        this.danct = danct;
    }

    public Double getPtcount() {
        return ptcount;
    }

    public void setPtcount(Double ptcount) {
        this.ptcount = ptcount;
    }

    public Double getEtcount() {
        return etcount;
    }

    public void setEtcount(Double etcount) {
        this.etcount = etcount;
    }

    public Double getEcount1() {
        return ecount1;
    }

    public void setEcount1(Double ecount1) {
        this.ecount1 = ecount1;
    }

    public Double getEcount2() {
        return ecount2;
    }

    public void setEcount2(Double ecount2) {
        this.ecount2 = ecount2;
    }

    public Double getEcount3() {
        return ecount3;
    }

    public void setEcount3(Double ecount3) {
        this.ecount3 = ecount3;
    }

    public Double getEcount4() {
        return ecount4;
    }

    public void setEcount4(Double ecount4) {
        this.ecount4 = ecount4;
    }

    public Double getHtcount() {
        return htcount;
    }

    public void setHtcount(Double htcount) {
        this.htcount = htcount;
    }

    public Double getHcount1() {
        return hcount1;
    }

    public void setHcount1(Double hcount1) {
        this.hcount1 = hcount1;
    }

    public Double getHcount2() {
        return hcount2;
    }

    public void setHcount2(Double hcount2) {
        this.hcount2 = hcount2;
    }

    public Double getHcount3() {
        return hcount3;
    }

    public void setHcount3(Double hcount3) {
        this.hcount3 = hcount3;
    }

    public Double getHcount4() {
        return hcount4;
    }

    public void setHcount4(Double hcount4) {
        this.hcount4 = hcount4;
    }

    public Double getBsct() {
        return bsct;
    }

    public void setBsct(Double bsct) {
        this.bsct = bsct;
    }

    public List<DtIaDanad> getChildren() {
        return children;
    }

    public void setChildren(List<DtIaDanad> children) {
        this.children = children;
    }
}
