package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.api.ew.entity.base.EwRiverWarningRecord;
import com.huitu.cloud.api.ew.entity.common.RiverWarnGrade;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 河道预警记录响应对象
 */
@ApiModel(value = "河道预警记录响应对象")
public class EwRiverWarningRecordResponse extends EwRiverWarningRecord {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "县级行政区划代码")
    @TableField(value = "XADCD")
    private String xadcd;

    @ApiModelProperty(value = "县级行政区划名称")
    @TableField(value = "XADNM")
    private String xadnm;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    @TableField(value = "RVNM")
    private String rvnm;

    @ApiModelProperty(value = "经度")
    @TableField(value = "LGTD")
    private String lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField(value = "LTTD")
    private String lttd;

    /**
     * 预警等级名称
     */
    public String getWarnGradeName() {
        return RiverWarnGrade.get(getWarnGradeId()).getName();
    }

    /**
     * 预警等级别名
     */
    public String getWarnGradeAliasName() {
        return RiverWarnGrade.get(getWarnGradeId()).getAlias();
    }

    /**
     * 预警等级简称
     */
    public String getWarnGradeShortName() {
        return RiverWarnGrade.get(getWarnGradeId()).getShortened();
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }
}
