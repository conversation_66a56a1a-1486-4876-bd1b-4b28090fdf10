<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.hsfxt.city.mapper.CityDao">

    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getBzqyInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicBianzhiquyu">
        SELECT SORTID, BIANZHIQUID, BIANZHIQUMINGCHENG, KUIBA, WANNIAN, DONGHUA, DONGHUASRC, XMIN, XMAX, YMIN, YMAX,
        FENGXIANTULEIXINGID, KUIJUELEIXING, TONGJIRIQI
        FROM DIC_BIANZHIQUYU
        WHERE 1=1
        <if test="fxtType != null and fxtType != ''">
            AND FENGXIANTULEIXINGID=#{fxtType}
        </if>
        order by SORTID
    </select>
    <select id="getHslyInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicHongShuiLaiYuan">
        SELECT SORTID, HONGSHUILAIYUANID, HONGSHUILAIYUANMINGCHENG
                FROM DICHONGSHUILAIYUAN
        WHERE 1=1
        <if test="sortIds !=null and sortIds != ''">
            AND SORTID in (${sortIds})
        </if>
        order by SORTID
    </select>
    <select id="getFqInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicJisuanfenqu">
        SELECT SORTID, JISUANFENQUID, JISUANFENGQUMINGCHENG, BIANZHIQUID, XMIN, XMAX, YMIN, YMAX, FLOODRISKFLASHID
        FROM DIC_JISUANFENQU
        WHERE 1=1
        <if test="bianzhiquid != null and bianzhiquid != ''">
            and BIANZHIQUID=#{bianzhiquid}
        </if>
        order by SORTID
    </select>
    <select id="getKkInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicKuikou">
        SELECT KUIKOUID, KUIKOUMINGCHENG, KUIKOUSORT, JISUANFENQUID
        FROM DIC_KUIKOU
        WHERE 1=1
        <if test="jisuanfenquid != null and jisuanfenquid != ''">
            AND JISUANFENQUID in (${jisuanfenquid})
        </if>
        order by KUIKOUID
    </select>
    <select id="getHsljInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicHongshuiliangji">
        SELECT HONGSHUILIANGJIID, HONGSHUILIANGJIMINGCHENG, BIANZHIQUID, BEIZHU, SORTID, BEIZHU3
        FROM DIC_HONGSHUILIANGJI
        WHERE 1=1
        <if test="bianzhiquid != null and bianzhiquid != ''">
            AND BIANZHIQUID=#{bianzhiquid}
        </if>
        order by HONGSHUILIANGJIID
    </select>
    <select id="getJylsInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicJiangyulishi">
        SELECT SORTID, JIANGYULISHIID, JIANGYULISHIMINGCHENG, BAOYULIANGJIID, BEIZHU
        FROM DIC_JIANGYULISHI
        WHERE 1=1
        <if test="bianzhiquid != null and bianzhiquid != ''">
            AND BAOYULIANGJIID=#{bianzhiquid}
        </if>
        order by SORTID
    </select>
    <select id="getJjInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicJianjie">
        SELECT JIANJIEID, FENGXIANTUID, BIANZHIQUID, PROJECTINFO, SOCIOECONOMIC, HISTORYFLOOD, FLOODSCHEME
        FROM DIC_JIANJIE
        WHERE 1=1
        <if test="fengxiantuid != null and fengxiantuid != ''">
            AND FENGXIANTUID=#{fengxiantuid}
        </if>
        <if test="bianzhiquid != null and bianzhiquid != ''">
            AND BIANZHIQUID=#{bianzhiquid}
        </if>
        order by JIANJIEID
    </select>
    <select id="getKkmcInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicShuxing">
        SELECT SHUXINGID, BREACHINFO
        FROM DIC_SHUXING
        WHERE 1=1
        <if test="kuikousort != null and kuikousort != ''">
            AND SHUXINGID=#{kuikousort}
        </if>
        order by SHUXINGID
    </select>
    <select id="getShjjTreeInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.CsShehuijingji">
        SELECT SORTID, KEYINDEX, YMSSINDEX, XZQHINDEX, MINGCHENG, YMMJ, YMFWMJ, YXGL, YXTL, YXRK, YXGDP, YXZDDW, JMFWSS,
                 JTCCSS, GYZCSS, GYCZSS, SMYZCSS, SMYZYSRSS, DLSS, FENQU, PID,
                 (CASE WHEN (ISNULL(XZQHINDEX,'') = '') AND ( ISNULL(YMSSINDEX,'') != '') THEN YMSSINDEX ELSE MINGCHENG END)NAME
        FROM CS_SHEHUIJINGJI
        WHERE 1=1
        <if test="key != null and key != ''">
            AND  KEYINDEX in (${key})
        </if>
    </select>
    <select id="getSjjjssTreeInfo" resultType="com.huitu.cloud.api.hsfxt.city.entity.CsZhijiejingjisunshi">
        SELECT SORTID, KEYINDEX, XZQHINDEX, MINGCHENG, YMSS, YMMJ, YMFWMJ, YXGL, YXTL, YXRK, YXGDP, JMFWSS, JTCCSS,
                GYSS, SYSS, DLSS, FENQU, PID
        FROM CS_ZHIJIEJINGJISUNSHI
        WHERE 1=1
        <if test="key != null and key != ''">
            AND KEYINDEX in (${key})
        </if>
        <if test="adcd != null and adcd != ''">
            AND XZQHINDEX=#{adcd}
        </if>
        <if test="name != null and name != ''">
            AND CHARINDEX(#{name}, MINGCHENG)>0 AND SUBSTRING(XZQHINDEX,7,3)&lt;>'000'
        </if>
    </select>
</mapper>