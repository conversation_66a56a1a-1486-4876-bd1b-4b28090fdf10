package com.huitu.cloud.api.screen.fhjz.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value="堤防对象", description="堤防对象")
public class DikeVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "堤防编号")
    private String dikeCode;

    @ApiModelProperty(value = "堤防名称")
    private String dikeName;

    @ApiModelProperty(value = "岸别 0：不分  1：左  2：右")
    private String bank;

    @ApiModelProperty(value = "起点所在位置")
    private String startLoc;

    @ApiModelProperty(value = "终点所在位置")
    private String endLoc;

    @ApiModelProperty(value = "堤防级别 0：未知 1:1级 2:2级 3：3级 4:4级 5:5级 9:5级以下")
    private String dikeGrad;

    @ApiModelProperty(value = "堤防长度 单位：KM")
    private String dikeLen;

    public String getDikeCode() {
        return dikeCode;
    }

    public void setDikeCode(String dikeCode) {
        this.dikeCode = dikeCode;
    }

    public String getDikeName() {
        return dikeName;
    }

    public void setDikeName(String dikeName) {
        this.dikeName = dikeName;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getStartLoc() {
        return startLoc;
    }

    public void setStartLoc(String startLoc) {
        this.startLoc = startLoc;
    }

    public String getEndLoc() {
        return endLoc;
    }

    public void setEndLoc(String endLoc) {
        this.endLoc = endLoc;
    }

    public String getDikeGrad() {
        return dikeGrad;
    }

    public void setDikeGrad(String dikeGrad) {
        this.dikeGrad = dikeGrad;
    }

    public String getDikeLen() {
        return dikeLen;
    }

    public void setDikeLen(String dikeLen) {
        this.dikeLen = dikeLen;
    }
}
