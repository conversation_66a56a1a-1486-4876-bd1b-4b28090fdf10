package com.huitu.cloud.api.ewci.hydropower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-08
 */
@TableName("BSN_SHPWR_R")
@ApiModel(value="BsnShpwrR对象", description="")
public class BsnShpwrR extends Model<BsnShpwrR> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "水位")
    @TableField("Z")
    private String z;

    @ApiModelProperty(value = "流量")
    @TableField("Q")
    private BigDecimal q;

    @ApiModelProperty(value = "断面过水面积")
    @TableField("XSA")
    private BigDecimal xsa;

    @ApiModelProperty(value = "断面平均流速")
    @TableField("XSAVV")
    private BigDecimal xsavv;

    @ApiModelProperty(value = "断面最大流速")
    @TableField("XSMXV")
    private BigDecimal xsmxv;

    @ApiModelProperty(value = "河水特征码")
    @TableField("FLWCHRCD")
    private String flwchrcd;

    @ApiModelProperty(value = "水势")
    @TableField("WPTN")
    private String wptn;

    @ApiModelProperty(value = "测流方法")
    @TableField("MSQMT")
    private String msqmt;

    @ApiModelProperty(value = "测积方法")
    @TableField("MSAMT")
    private String msamt;

    @ApiModelProperty(value = "测速方法")
    @TableField("MSVMT")
    private String msvmt;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getZ() {
        return z;
    }

    public void setZ(String z) {
        this.z = z;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public BigDecimal getXsa() {
        return xsa;
    }

    public void setXsa(BigDecimal xsa) {
        this.xsa = xsa;
    }

    public BigDecimal getXsavv() {
        return xsavv;
    }

    public void setXsavv(BigDecimal xsavv) {
        this.xsavv = xsavv;
    }

    public BigDecimal getXsmxv() {
        return xsmxv;
    }

    public void setXsmxv(BigDecimal xsmxv) {
        this.xsmxv = xsmxv;
    }

    public String getFlwchrcd() {
        return flwchrcd;
    }

    public void setFlwchrcd(String flwchrcd) {
        this.flwchrcd = flwchrcd;
    }

    public String getWptn() {
        return wptn;
    }

    public void setWptn(String wptn) {
        this.wptn = wptn;
    }

    public String getMsqmt() {
        return msqmt;
    }

    public void setMsqmt(String msqmt) {
        this.msqmt = msqmt;
    }

    public String getMsamt() {
        return msamt;
    }

    public void setMsamt(String msamt) {
        this.msamt = msamt;
    }

    public String getMsvmt() {
        return msvmt;
    }

    public void setMsvmt(String msvmt) {
        this.msvmt = msvmt;
    }

    @Override
    public String toString() {
        return "BsnShpwrR{" +
                "stcd='" + stcd + '\'' +
                ", tm=" + tm +
                ", z='" + z + '\'' +
                ", q=" + q +
                ", xsa=" + xsa +
                ", xsavv=" + xsavv +
                ", xsmxv=" + xsmxv +
                ", flwchrcd='" + flwchrcd + '\'' +
                ", wptn='" + wptn + '\'' +
                ", msqmt='" + msqmt + '\'' +
                ", msamt='" + msamt + '\'' +
                ", msvmt='" + msvmt + '\'' +
                '}';
    }
}
