package com.huitu.cloud.api.common.msg.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
public class QueryWarnMessage extends PageBean {

    @ApiModelProperty(value = "短信发送方式 10-短信； 20-传真； 30-广播")
    @NotBlank(message = "参数[短信发送方式]不能为空")
    private String mediaId;

    @ApiModelProperty(value = "预警标识")
    @NotBlank(message = "参数[预警标识]不能为空")
    private String warnId;

    @ApiModelProperty(value = "状态")
    private String status;

    public String getMediaId() {
        return mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
