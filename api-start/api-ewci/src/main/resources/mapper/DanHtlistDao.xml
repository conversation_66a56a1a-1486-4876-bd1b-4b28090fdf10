<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ia.mapper.DanHtlistDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getResList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanHtRes" useCache="false">
        <!--
            由于 dbo.fnGetAdLevel(ADCD) ADLVL 查询产生多级数据，例如：9、12、15，
            由此 LEFT(B.ADCD, A.ADLVL) = LEFT(A.ADCD, A.ADLVL) 关联查询时增加运行时间，现暂时修改 B.ADCD = A.ADCD 关联，待后续优化
        -->
<!--        SELECT ROW_NUMBER() OVER(ORDER BY A.DAN<PERSON>, B.ADCD, B.RES_CODE ASC) SORTNO, A.DAND, A.DANAME, B.RES_CODE,-->
<!--        B.RES_NAME, B.ENG_CODE, B.ADCD, C.ADNM, B.RES_LOC, B.LGTD, B.LTTD, B.MWRDAM_TYPE, B.COMPLETED, B.COMPLETED_DATE,-->
<!--        B.CONCLUSION, B.REINFORCED FROM-->
<!--        (SELECT DAND, NAME DANAME, ADCD, dbo.fnGetAdLevel(ADCD) ADLVL FROM IA_C_DANAD-->
<!--        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})-->
<!--        <if test="map.daname != null and map.daname != ''">-->
<!--            AND CHARINDEX(#{map.daname}, NAME) > 0-->
<!--        </if>) A-->
<!--        LEFT JOIN BNS_RCS_RES B ON LEFT(B.ADCD, A.ADLVL) = LEFT(A.ADCD, A.ADLVL)-->
<!--        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = B.ADCD-->
<!--        WHERE B.RES_CODE IS NOT NULL-->
<!--        <if test="map.resName != null and map.resName != ''">-->
<!--            AND CHARINDEX(#{map.resName}, B.RES_NAME) > 0-->
<!--        </if>-->
<!--        <if test="map.reinforced != null and map.reinforced != ''">-->
<!--            AND B.REINFORCED = #{map.reinforced}-->
<!--        </if>-->
<!--        ORDER BY SORTNO ASC-->
        SELECT ROW_NUMBER() OVER(ORDER BY A.DAND, B.ADCD, B.RES_CODE ASC) SORTNO, A.DAND, A.DANAME, B.RES_CODE,
        B.RES_NAME, B.ENG_CODE, B.ADCD, C.ADNM, B.RES_LOC, B.LGTD, B.LTTD, B.MWRDAM_TYPE, B.COMPLETED, B.COMPLETED_DATE,
        B.CONCLUSION, B.REINFORCED FROM
        (SELECT DAND, NAME DANAME, ADCD FROM IA_C_DANAD
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.daname != null and map.daname != ''">
            AND CHARINDEX(#{map.daname}, NAME) > 0
        </if>) A
        LEFT JOIN BNS_RCS_RES B ON B.ADCD = A.ADCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = B.ADCD
        WHERE B.RES_CODE IS NOT NULL
        <if test="map.resName != null and map.resName != ''">
            AND CHARINDEX(#{map.resName}, B.RES_NAME) > 0
        </if>
        <if test="map.reinforced != null and map.reinforced != ''">
            AND B.REINFORCED = #{map.reinforced}
        </if>
        ORDER BY SORTNO ASC
    </select>
    <select id="getIfmpList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanIfmp" >
        SELECT A.IFMPCD, A.TM, A.IFMPNM, A.IFMPTP, A.IFMPDESC, A.ADCD, B.ADNM, A.LGTD, A.LTTD, A.UPTM, A.EXIST_PROBLEMS,
        A.MEASURES, A.IMPACT, A.PROGRESS, A.DTM, A.PERSON, A.ENNMCD
        FROM BSN_IFMPRECORD_R A
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
        WHERE A.ENNMCD = #{map.code} AND A.UPTM >= #{map.stm} AND A.UPTM &lt; #{map.etm}
        AND A.IFMPTP IN (<foreach collection="map.types" item="type" separator=",">#{type}</foreach>)
        ORDER BY A.UPTM DESC, A.ADCD ASC
    </select>
    <select id="getDikeList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanHtDike" useCache="false">
        <!--
            由于 dbo.fnGetAdLevel(ADCD) ADLVL 查询产生多级数据，例如：9、12、15，
            由此 LEFT(B.ADCD, A.ADLVL) = LEFT(A.ADCD, A.ADLVL) 关联查询时增加运行时间，现暂时修改 B.ADCD = A.ADCD 关联，待后续优化
        -->
<!--        SELECT ROW_NUMBER() OVER(ORDER BY A.DAND, B.ADCD, B.DIKE_CODE ASC) SORTNO, A.DAND, A.DANAME, B.DIKE_CODE,-->
<!--        B.DIKE_NAME, B.ENG_CODE, B.ADCD, C.ADNM, B.RV_OR_LE_CODE, B.RV_OR_LE_NAME, B.BANK, B.START_LGTD, B.START_LTTD,-->
<!--        B.END_LGTD, B.END_LTTD, B.DIKE_LEN, B.DIKE_PATT, B.DIKE_GRAD, B.PLFLCTSD, B.ACFLCTSD, B.COMPLIANCED FROM-->
<!--        (SELECT DAND, NAME DANAME, ADCD, dbo.fnGetAdLevel(ADCD) ADLVL FROM IA_C_DANAD-->
<!--        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})-->
<!--        <if test="map.daname != null and map.daname != ''">-->
<!--            AND CHARINDEX(#{map.daname}, NAME) > 0-->
<!--        </if>) A-->
<!--        LEFT JOIN BNS_RCS_DIKE B ON LEFT(B.ADCD, A.ADLVL) = LEFT(A.ADCD, A.ADLVL)-->
<!--        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = B.ADCD-->
<!--        WHERE B.DIKE_CODE IS NOT NULL-->
<!--        <if test="map.dikeName != null and map.dikeName != ''">-->
<!--            AND CHARINDEX(#{map.dikeName}, B.DIKE_NAME) > 0-->
<!--        </if>-->
<!--        <if test="map.complianced != null and map.complianced != ''">-->
<!--            AND B.COMPLIANCED = #{map.complianced}-->
<!--        </if>-->
<!--        ORDER BY SORTNO ASC-->
        SELECT ROW_NUMBER() OVER(ORDER BY A.DAND, B.ADCD, B.DIKE_CODE ASC) SORTNO, A.DAND, A.DANAME, B.DIKE_CODE,
        B.DIKE_NAME, B.ENG_CODE, B.ADCD, C.ADNM, B.RV_OR_LE_CODE, B.RV_OR_LE_NAME, B.BANK, B.START_LGTD, B.START_LTTD,
        B.END_LGTD, B.END_LTTD, B.DIKE_LEN, B.DIKE_PATT, B.DIKE_GRAD, B.PLFLCTSD, B.ACFLCTSD, B.COMPLIANCED FROM
        (SELECT DAND, NAME DANAME, ADCD FROM IA_C_DANAD
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.daname != null and map.daname != ''">
            AND CHARINDEX(#{map.daname}, NAME) > 0
        </if>) A
        LEFT JOIN BNS_RCS_DIKE B ON B.ADCD = A.ADCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = B.ADCD
        WHERE B.DIKE_CODE IS NOT NULL
        <if test="map.dikeName != null and map.dikeName != ''">
            AND CHARINDEX(#{map.dikeName}, B.DIKE_NAME) > 0
        </if>
        <if test="map.complianced != null and map.complianced != ''">
            AND B.COMPLIANCED = #{map.complianced}
        </if>
        ORDER BY SORTNO ASC
    </select>
    <select id="getWagaList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanHtWaga" useCache="false">
        <!--
            由于 dbo.fnGetAdLevel(ADCD) ADLVL 查询产生多级数据，例如：9、12、15，
            由此 LEFT(B.ADCD, A.ADLVL) = LEFT(A.ADCD, A.ADLVL) 关联查询时增加运行时间，现暂时修改 B.ADCD = A.ADCD 关联，待后续优化
        -->
<!--        SELECT ROW_NUMBER() OVER(ORDER BY A.DAND, B.ADCD, B.WAGA_CODE ASC) SORTNO, A.DAND, A.DANAME, B.WAGA_CODE,-->
<!--        B.WAGA_NAME, B.ENG_CODE, B.ADCD, C.ADNM, B.WAGA_LOC, B.LGTD, B.LTTD, B.WAGA_TYPE, B.ENG_GRAD, B.COMPLETED,-->
<!--        B.COMPLETED_DATE, B.CONCLUSION, B.REINFORCED FROM-->
<!--        (SELECT DAND, NAME DANAME, ADCD, dbo.fnGetAdLevel(ADCD) ADLVL FROM IA_C_DANAD-->
<!--        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})-->
<!--        <if test="map.daname != null and map.daname != ''">-->
<!--            AND CHARINDEX(#{map.daname}, NAME) > 0-->
<!--        </if>) A-->
<!--        LEFT JOIN BNS_RCS_WAGA B ON LEFT(B.ADCD, A.ADLVL) = LEFT(A.ADCD, A.ADLVL)-->
<!--        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = B.ADCD-->
<!--        WHERE B.WAGA_CODE IS NOT NULL-->
<!--        <if test="map.wagaName != null and map.wagaName != ''">-->
<!--            AND CHARINDEX(#{map.wagaName}, B.WAGA_NAME) > 0-->
<!--        </if>-->
<!--        <if test="map.reinforced != null and map.reinforced != ''">-->
<!--            AND B.REINFORCED = #{map.reinforced}-->
<!--        </if>-->
<!--        ORDER BY SORTNO ASC-->
        SELECT ROW_NUMBER() OVER(ORDER BY A.DAND, B.ADCD, B.WAGA_CODE ASC) SORTNO, A.DAND, A.DANAME, B.WAGA_CODE,
        B.WAGA_NAME, B.ENG_CODE, B.ADCD, C.ADNM, B.WAGA_LOC, B.LGTD, B.LTTD, B.WAGA_TYPE, B.ENG_GRAD, B.COMPLETED,
        B.COMPLETED_DATE, B.CONCLUSION, B.REINFORCED FROM
        (SELECT DAND, NAME DANAME, ADCD FROM IA_C_DANAD
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.daname != null and map.daname != ''">
            AND CHARINDEX(#{map.daname}, NAME) > 0
        </if>) A
        LEFT JOIN BNS_RCS_WAGA B ON B.ADCD = A.ADCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = B.ADCD
        WHERE B.WAGA_CODE IS NOT NULL
        <if test="map.wagaName != null and map.wagaName != ''">
            AND CHARINDEX(#{map.wagaName}, B.WAGA_NAME) > 0
        </if>
        <if test="map.reinforced != null and map.reinforced != ''">
            AND B.REINFORCED = #{map.reinforced}
        </if>
        ORDER BY SORTNO ASC
    </select>
    <select id="getFsdaList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanHtFsda" useCache="false">
        <!--
            由于 dbo.fnGetAdLevel(ADCD) ADLVL 查询产生多级数据，例如：9、12、15，
            由此 LEFT(B.ADCD, A.ADLVL) = LEFT(A.ADCD, A.ADLVL) 关联查询时增加运行时间，现暂时修改 B.ADCD = A.ADCD 关联，待后续优化
        -->
<!--        SELECT ROW_NUMBER() OVER(ORDER BY A.DAND, B.ADCD, B.FSDA_CODE ASC) SORTNO, A.DAND, A.DANAME, B.FSDA_CODE,-->
<!--        B.FSDA_NAME, B.ADCD, C.ADNM, B.FSDA_TYPE, B.LGTD, B.LTTD, B.COMPLIANCED, B.COMPLETED FROM-->
<!--        (SELECT DAND, NAME DANAME, ADCD, dbo.fnGetAdLevel(ADCD) ADLVL FROM IA_C_DANAD-->
<!--        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})-->
<!--        <if test="map.daname != null and map.daname != ''">-->
<!--            AND CHARINDEX(#{map.daname}, NAME) > 0-->
<!--        </if>) A-->
<!--        LEFT JOIN BNS_RCS_FSDA B ON LEFT(B.ADCD, A.ADLVL) = LEFT(A.ADCD, A.ADLVL)-->
<!--        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = B.ADCD-->
<!--        WHERE B.FSDA_CODE IS NOT NULL-->
<!--        <if test="map.fsdaName != null and map.fsdaName != ''">-->
<!--            AND CHARINDEX(#{map.fsdaName}, B.FSDA_NAME) > 0-->
<!--        </if>-->
<!--        <if test="map.complianced != null and map.complianced != ''">-->
<!--            AND B.COMPLIANCED = #{map.complianced}-->
<!--        </if>-->
<!--        <if test="map.completed != null and map.completed != ''">-->
<!--            AND B.COMPLETED = #{map.completed}-->
<!--        </if>-->
<!--        ORDER BY SORTNO ASC-->
        SELECT ROW_NUMBER() OVER(ORDER BY A.DAND, B.ADCD, B.FSDA_CODE ASC) SORTNO, A.DAND, A.DANAME, B.FSDA_CODE,
        B.FSDA_NAME, B.ADCD, C.ADNM, B.FSDA_TYPE, B.LGTD, B.LTTD, B.COMPLIANCED, B.COMPLETED FROM
        (SELECT DAND, NAME DANAME, ADCD FROM IA_C_DANAD
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.daname != null and map.daname != ''">
            AND CHARINDEX(#{map.daname}, NAME) > 0
        </if>) A
        LEFT JOIN BNS_RCS_FSDA B ON B.ADCD = A.ADCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = B.ADCD
        WHERE B.FSDA_CODE IS NOT NULL
        <if test="map.fsdaName != null and map.fsdaName != ''">
            AND CHARINDEX(#{map.fsdaName}, B.FSDA_NAME) > 0
        </if>
        <if test="map.complianced != null and map.complianced != ''">
            AND B.COMPLIANCED = #{map.complianced}
        </if>
        <if test="map.completed != null and map.completed != ''">
            AND B.COMPLETED = #{map.completed}
        </if>
        ORDER BY SORTNO ASC
    </select>
</mapper>
