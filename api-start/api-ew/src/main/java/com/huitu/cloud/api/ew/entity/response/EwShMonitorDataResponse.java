package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 山洪预警监测数据响应对象
 */
@ApiModel(value = "山洪预警监测数据响应对象")
public class EwShMonitorDataResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;
    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STNM")
    private String stnm;
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "STM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;
    @ApiModelProperty(value = "结束时间")
    @TableField(value = "ETM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;
    @ApiModelProperty(value = "累计降水量")
    @TableField(value = "ACCP")
    private BigDecimal accp;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public BigDecimal getAccp() {
        return accp;
    }

    public void setAccp(BigDecimal accp) {
        this.accp = accp;
    }
}
