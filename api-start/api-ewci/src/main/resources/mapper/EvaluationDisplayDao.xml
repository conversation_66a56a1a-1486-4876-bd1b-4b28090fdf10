<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.bia.mapper.EvaluationDisplayDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!--    计算小流域划分图 防灾对象-->
    <select id="getUnitdpList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaUnitdps">
        SELECT
        B.adnm as ADNM,
        A.ADCD as ADCD,
        A.WAT_SHED_AREA as WAT_SHED_AREA,
        A.HSCD as HSCD,
        A.SLP as SLP,
        A.AVEROUC as AVEROUC,
        A.AVEROUL as AVEROUL,
        A.AVEROUR as AVEROUR,
        A.CZZ as CZZ,
        A.STDT as STDT,
        <PERSON><PERSON>,
        A.<PERSON>,
        A.T<PERSON>,
        B.LGTD,
        B.LTTD,
        <if test="map.type == '1'.toString()">
            '1' as type from IA_UNITDP A
        </if>
        <if test="map.type == '2'.toString()">
            '2' as type from BNS_IA_UNITDP A
        </if>
        LEFT JOIN MDT_ADCDINFO_B B on A.ADCD=B.ADCD
        where
        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        ORDER BY
        A.ADCD
    </select>
<!--    <select id="getUnitdpList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaUnitdps">-->
<!--            select A.ADCD, B.ADNM,A.WAT_SHED_AREA, A.HSCD, A.SLP, A.AVEROUC, A.AVEROUL,A.AVEROUR,A.CZZ, A.STDT, A.LWATER,-->
<!--        A.REMARK, A.TS , B.LGTD , B.LTTD,ISNULL(C.FLOWTM, 0) FLOWTM,-->
<!--        <if test="map.type == '1'.toString()">-->
<!--            '1' as type from IA_UNITDP A-->
<!--            LEFT JOIN IA_C_WSADCD D ON A.ADCD = D.ADCD-->
<!--            LEFT JOIN IA_UNITWS C ON D.WSCD=C.WSCD-->
<!--        </if>-->
<!--        <if test="map.type == '2'.toString()">-->
<!--            '2' as type from BNS_IA_UNITDP A-->
<!--            LEFT JOIN BNS_IA_C_WSADCD D ON A.ADCD = D.ADCD-->
<!--            LEFT JOIN BNS_IA_UNITWS C ON D.WSCD=C.WSCD-->
<!--        </if>-->
<!--        LEFT JOIN MDT_ADCDINFO_B B on A.ADCD=B.ADCD-->
<!--        where-->
<!--        LEFT (A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})-->
<!--        <if test="map.level == '4'.toString()">-->
<!--            AND LEFT(A.ADCD, 6) NOT IN ('220581')-->
<!--        </if>-->
<!--        ORDER BY A.ADCD-->
<!--    </select>-->
    <!--    计算小流域划分图 计算单元-->
    <select id="getUnitwsList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaUnitwss">
        SELECT a.WSCD,
        a.AREA,a.CHLENGTH,a.CHPERCENT,a.VEGCOVERAGE,a.SJY,a.DESNTB,a.SDTDTB,a.WRULETB,a.FLOWTM,a.COMMENTS,D.ADCD,
        b.WSNM , b.NLGTD as lgtd , b.NLTTD as lttd,
        <if test="map.type == '1'.toString()">
            '1' as type FROM IA_UNITWS a
            LEFT JOIN (SELECT
            BB.ADCD,
            BB.WSCD,
            AA.COMMENTS
            FROM
            IA_C_WSADCD AA
            JOIN ( SELECT LEFT ( ADCD, 10 ) AS ADCD, WSCD FROM IA_C_WSADCD WHERE left(ADCD,#{map.level})=LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">
                AND LEFT(D.ADCD, 6) NOT IN ('220581')
            </if>
            ) BB ON AA.WSCD = BB.WSCD
            GROUP BY
            BB.ADCD,
            BB.WSCD,
            AA.COMMENTS) D ON a.WSCD = D.WSCD
        </if>
        <if test="map.type == '2'.toString()">
            '2' as type FROM BNS_IA_UNITWS a
            LEFT JOIN BNS_IA_C_WSADCD D ON a.WSCD = D.WSCD
        </if>
        left join BNS_IA_C_WATA b on a.WSCD = b.WSCD
        WHERE
        1=1
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(D.ADCD,#{map.level})=LEFT(#{map.adcd}, #{map.level})
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        ORDER BY
        D.ADCD
    </select>
    <!--    现状防洪能力分布图-->
    <select id="getNowfhtbList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaANowfhtb">
        select U.ADCD,A.WSCD,U.HSCD,A.FCA,A.MXPN,A.MXHN,A.SENPN,A.SENHN,A.WXPN,A.WXHN,B.ADNM,B.LGTD,B.LTTD,
        <if test="map.type == '1'.toString()">
            '1' as type FROM IA_UNITDP U
            LEFT JOIN IA_A_NOWFHTB A ON U.ADCD = A.ADCD
        </if>
        <if test="map.type == '2'.toString()">
            '2' as type FROM BNS_IA_UNITDP U
            LEFT JOIN BNS_IA_A_NOWFHTB A ON U.ADCD = A.ADCD
        </if>
        LEFT JOIN MDT_ADCDINFO_B B on U.ADCD=B.ADCD
        where
        LEFT (U.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(U.ADCD, 6) NOT IN ('220581')
        </if>
        ORDER BY U.ADCD
    </select>
    <!--    预警指标分布图-->
    <select id="getDfwruleList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaADfwrule">
        select U.ADCD,U.HSCD,A.WSCD,A.WARNGRADEID,A.LWATER,A.STDT,A.DRPT,A.CALMATH,B.ADNM,B.LGTD,B.LTTD,
        <if test="map.type == '1'.toString()">
            '1' as type FROM IA_UNITDP U
            LEFT JOIN IA_A_DFWRULE A ON U.ADCD = A.ADCD
        </if>
        <if test="map.type == '2'.toString()">
            '2' as type FROM BNS_IA_UNITDP U
            LEFT JOIN BNS_IA_A_DFWRULE A ON U.ADCD = A.ADCD
        </if>
        LEFT JOIN MDT_ADCDINFO_B B on U.ADCD=B.ADCD
        where
        LEFT (U.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(U.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.warngradeid !=null and map.warngradeid !=''">
            AND A.WARNGRADEID = #{map.warngradeid} AND A.STDT = 1
        </if>
        ORDER BY U.ADCD
    </select>
    <!--    洪峰流量分布图-->
    <select id="getSdtdtbList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaASdtdtb">
        select U.ADCD,A.WSCD,U.HSCD,A.CXQ,A.SJHSHF,A.Q,A.ZHLS,A.HSLS,A.HFSW,B.ADNM,B.LGTD,B.LTTD,
        <if test="map.type == '1'.toString()">
            '1' as type FROM IA_UNITDP U
            LEFT JOIN IA_A_SDTDTB A ON U.ADCD = A.ADCD
        </if>
        <if test="map.type == '2'.toString()">
            '2' as type FROM BNS_IA_UNITDP U
            LEFT JOIN BNS_IA_A_SDTDTB A ON U.ADCD = A.ADCD
        </if>
        LEFT JOIN MDT_ADCDINFO_B B on U.ADCD=B.ADCD
        where
        LEFT (U.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(U.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.cxq !=null and map.cxq !=''">
            AND A.CXQ = #{map.cxq}
        </if>
        ORDER BY U.ADCD
    </select>
    <!--    计算小流域划分图 防灾对象 基本信息-->
    <select id="getBnsIaUnitdpsVo" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaUnitdps">
        select A.ADCD,A.WAT_SHED_AREA, A.HSCD, A.SLP, A.AVEROUC, A.AVEROUL,A.AVEROUR,A.CZZ, A.STDT, A.LWATER,
        A.REMARK, A.TS , B.PTCOUNT AS fzPtcount,B.ETCOUNT as fzEtcount,B.LDAREA,B.PLAREA,B.HTCOUNT as
        fzHtcount,C.PTCOUNT AS wxPtcount,C.ETCOUNT as wxEtcount,C.HTCOUNT AS wxHtcount,D.NAME as gullyName , E.ADNM
        ,F.WSNM
        <if test="map.type == '1'.toString()">
            FROM IA_UNITDP A
            LEFT JOIN IA_C_WSADCD J ON J.ADCD = A.ADCD
            LEFT JOIN IA_C_WATA F ON F.WSCD = J.WSCD
        </if>
        <if test="map.type == '2'.toString()">
            FROM BNS_IA_UNITDP A
            LEFT JOIN BNS_IA_C_WSADCD J ON J.ADCD = A.ADCD
            LEFT JOIN BNS_IA_C_WATA F ON F.WSCD = J.WSCD
        </if>
        LEFT JOIN IA_C_PREVAD B ON A.ADCD = B.ADCD
        LEFT JOIN IA_C_DANAD C ON A.ADCD = C.ADCD
        LEFT JOIN IA_C_GULLY D ON A.ADCD= D.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON A.ADCD = E.ADCD
        WHERE 1=1
        <if test="map.hscd !=null and map.hscd !=''">
            AND A.HSCD = #{map.hscd}
        </if>
        <if test="map.adcd !=null and map.adcd !=''">
            AND A.ADCD = #{map.adcd}
        </if>
    </select>

    <!--    计算小流域划分图 计算单元 基本信息-->
    <select id="getBnsIaUnitwssVo" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaUnitwss">
        SELECT a.WSCD,
        a.AREA,a.CHLENGTH,a.CHPERCENT,a.VEGCOVERAGE,a.SJY,a.DESNTB,a.SDTDTB,a.WRULETB,a.FLOWTM,a.COMMENTS,
        b.WSNM AS wsnm
        <if test="map.type == '1'.toString()">
            FROM IA_UNITWS a
            left join IA_C_WATA b on a.WSCD = b.WSCD
        </if>
        <if test="map.type == '2'.toString()">
            FROM BNS_IA_UNITWS a
            left join BNS_IA_C_WATA b on a.WSCD = b.WSCD
        </if>
        WHERE 1=1
        <if test="map.wscd !=null and map.wscd !=''">
            AND a.WSCD = #{map.wscd}
        </if>
    </select>
    <!--    防灾对象 分析评价 降雨信息列表-->
    <select id="getFzdxRainfallList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanRain">
        SELECT T.STCD, B.STNM, B.STTP, T.ACCP, R1.RAINFALL RAINFALL1, R3.RAINFALL RAINFALL3, R6.RAINFALL RAINFALL6
        FROM (SELECT A.STCD, SUM(A.DRP) ACCP
              FROM ST_PPTN_R A
                       RIGHT JOIN (SELECT DISTINCT STCD
                                   FROM BSN_STBPRP_V
                                   WHERE FRGRD != '8' AND LEFT (ADCD
                                       , #{map.level}) = LEFT (#{map.adcd}
                                       , #{map.level})) B ON B.STCD = A.STCD
              WHERE A.INTV = 1
                AND A.TM >= #{map.stm}
                AND A.TM &lt;= #{map.etm}
              GROUP BY A.STCD) T
                 LEFT JOIN BSN_STBPRP_V B ON T.STCD = B.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm1}
                            GROUP BY STCD) R1 ON R1.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm3}
                            GROUP BY STCD) R3 ON R3.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm6}
                            GROUP BY STCD) R6 ON R6.STCD = T.STCD
        ORDER BY T.ACCP DESC, T.STCD ASC
    </select>
    <!--    计算单元 分析评价 降雨信息列表-->
    <select id="getJsdyRainfallList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanRain">
        SELECT T.STCD, B.STNM, B.STTP, T.ACCP, R1.RAINFALL RAINFALL1, R3.RAINFALL RAINFALL3, R6.RAINFALL RAINFALL6
        FROM (SELECT A.STCD, SUM(A.DRP) ACCP
        FROM ST_PPTN_R A
        RIGHT JOIN (SELECT DISTINCT V.STCD
        FROM BSN_STBPRP_V V
        <if test="map.type == '1'.toString()">
            left join IA_C_WSADCD W on V.ADCD = W.ADCD
        </if>
        <if test="map.type == '2'.toString()">
            left join BNS_IA_C_WSADCD W on V.ADCD = W.ADCD
        </if>
        WHERE V.FRGRD != '8' AND LEFT (V.ADCD
        , 12) = LEFT (W.ADCD
        , 12) AND W.WSCD = #{map.WSCD}) B ON B.STCD = A.STCD
        WHERE A.INTV = 1
        AND A.TM >= #{map.stm}
        AND A.TM &lt;= #{map.etm}
        GROUP BY A.STCD) T
        LEFT JOIN BSN_STBPRP_V B ON T.STCD = B.STCD
        LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
        FROM BSN_FORECASTRAIN_F
        WHERE RAIN_TIME >= #{map.fstm}
        AND RAIN_TIME &lt;= #{map.fetm1}
        GROUP BY STCD) R1 ON R1.STCD = T.STCD
        LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
        FROM BSN_FORECASTRAIN_F
        WHERE RAIN_TIME >= #{map.fstm}
        AND RAIN_TIME &lt;= #{map.fetm3}
        GROUP BY STCD) R3 ON R3.STCD = T.STCD
        LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
        FROM BSN_FORECASTRAIN_F
        WHERE RAIN_TIME >= #{map.fstm}
        AND RAIN_TIME &lt;= #{map.fetm6}
        GROUP BY STCD) R6 ON R6.STCD = T.STCD
        ORDER BY T.ACCP DESC, T.STCD ASC
    </select>
    <!--    防灾对象 分析评价 水库水情信息列表-->
    <select id="getFzdxReservoirList" statementType="CALLABLE"
            resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanRsvr">
        {CALL PROC_GET_RSVRDATA_ALL_NEW(#{map.stm, mode=IN}, #{map.etm, mode=IN}, #{map.whereSql, mode=IN})}
    </select>
    <!--    防灾对象 分析评价 河道水情信息列表-->
    <select id="getFzdxRiverWayList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanRiver">
        SELECT T.STCD,
               ST.STNM,
               ST.STTP,
               T.TM,
               R.TM,
               R.Z,
               (R.Z - F.WRZ) ZWRZ,
               F.WRZ
        FROM (SELECT A.STCD, MAX(A.TM) TM
              FROM ST_RIVER_R A
                       RIGHT JOIN (SELECT DISTINCT STCD
                                   FROM BSN_STBPRP_V
                                   WHERE STTP IN ('ZZ', 'ZQ')
                                     AND FRGRD != '8' AND LEFT(ADCD, #{map.level}) = LEFT (#{map.adcd}
                                       , #{map.level})) B ON B.STCD = A.STCD
              WHERE A.TM >= #{map.stm}
                AND A.TM &lt;= #{map.etm}
              GROUP BY A.STCD) T
                 LEFT JOIN ST_RIVER_R R ON R.STCD = T.STCD AND R.TM = T.TM
                 LEFT JOIN BSN_STBPRP_V ST ON ST.STCD = T.STCD
                 LEFT JOIN ST_RVFCCH_B F ON F.STCD = T.STCD
        ORDER BY ZWRZ DESC, STCD ASC
    </select>
    <!--    计算单元 分析评价 河道水情信息列表-->
    <select id="getJsdyRiverWayList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanRiver">
        SELECT T.STCD,
        ST.STNM,
        ST.STTP,
        T.TM,
        R.TM,
        R.Z,
        (R.Z - F.WRZ) ZWRZ,
        F.WRZ
        FROM (SELECT A.STCD, MAX(A.TM) TM
        FROM ST_RIVER_R A
        RIGHT JOIN (SELECT DISTINCT V.STCD
        FROM BSN_STBPRP_V V
        <if test="map.type == '1'.toString()">
            left join IA_C_WSADCD W on V.ADCD = W.ADCD
        </if>
        <if test="map.type == '2'.toString()">
            left join BNS_IA_C_WSADCD W on V.ADCD = W.ADCD
        </if>
        WHERE V.STTP IN ('ZZ', 'ZQ')
        AND V.FRGRD != '8' AND LEFT(V.ADCD, 12) = LEFT (W.ADCD
        , 12) and W.WSCD = #{map.wscd}) B ON B.STCD = A.STCD
        WHERE A.TM >= #{map.stm}
        AND A.TM &lt;= #{map.etm}
        GROUP BY A.STCD) T
        LEFT JOIN ST_RIVER_R R ON R.STCD = T.STCD AND R.TM = T.TM
        LEFT JOIN BSN_STBPRP_V ST ON ST.STCD = T.STCD
        LEFT JOIN ST_RVFCCH_B F ON F.STCD = T.STCD
        ORDER BY ZWRZ DESC, STCD ASC
    </select>
    <!--    预警信息 山洪预警指标-->
    <select id="getShyjzbList" resultType="com.huitu.cloud.api.ewci.bia.entity.BnsIaADfwrule">
        select A.ADCD,A.WSCD,A.WARNGRADEID,A.LWATER,A.STDT,A.DRPT as zbDrpt,B.DRPT AS ljDrpt,A.CALMATH,C.ADNM as adnm from
        (select ADCD,WSCD,WARNGRADEID,LWATER,STDT,DRPT,CALMATH
        <if test="map.type == '1'.toString()">
            FROM IA_A_DFWRULE A
        </if>
        <if test="map.type == '2'.toString()">
            FROM BNS_IA_A_DFWRULE A
        </if>
        WHERE WARNGRADEID = '4') A
        INNER JOIN (select ADCD,WSCD,WARNGRADEID,LWATER,STDT,DRPT,CALMATH
        <if test="map.type == '1'.toString()">
            FROM IA_A_DFWRULE A
        </if>
        <if test="map.type == '2'.toString()">
            FROM BNS_IA_A_DFWRULE A
        </if>
        WHERE WARNGRADEID = '5') B on a.ADCD = b.ADCD and a.WSCD = b.WSCD and a.STDT = b.STDT
        left join MDT_ADCDINFO_B C ON A.ADCD = C.ADCD
        WHERE 1=1
        <if test="map.wscd !=null and map.wscd !=''">
            AND A.WSCD = #{map.wscd}
        </if>
        <if test="map.adcd !=null and map.adcd !=''">
            AND left(A.ADCD,12) = left(#{map.adcd},12)
        </if>
    </select>
    <select id="getRainWarn" resultType="com.huitu.cloud.api.ewci.bia.entity.BsnRainAlarms">
        SELECT
        a.STCD,
        a.STDT,
        a.ALARMGRADEID,
        a.ALARMSTM,
        a.ALARMETM,
        a.ALARMDESC,
        a.DRP,
        a.ALERMDRP,
        a.REAMRK,
        b.STNM,
        b.BSNM,
        b.STLC,
        b.ADDVCD,
        b.STTP,
        b.LGTD,
        b.LTTD,
        b.stadtp,
        b.ADNM,
        b.STADTPNM,
        b.XADNM,PLGTD,PLTTD
        FROM
        BSN_RAIN_ALARM a
        LEFT JOIN BSN_STBPRP_V b ON a.STCD = b.STCD
        <if test="map.type == '1'.toString()">
            left join IA_C_WSADCD W on b.ADCD = W.ADCD
        </if>
        <if test="map.type == '2'.toString()">
            left join BNS_IA_C_WSADCD W on b.ADCD = W.ADCD
        </if>
        WHERE 1=1
        <if test="map.wscd != null and map.wscd !=''">
            and LEFT(b.ADCD, 12) = LEFT (W.ADCD, 12) and W.WSCD = #{map.wscd}
        </if>
        <if test="map.ad != null and map.ad !=''">
            and left(b.ADDVCD,#{map.adcd})=#{map.ad}
        </if>
        <if test="map.stnm !=null and  map.stnm !=''">
            and CHARINDEX(#{map.stnm},b.stnm) >0
        </if>
        <if test="map.warnType !=null and  map.warnType !=''">
            and CHARINDEX(ALARMGRADEID,#{map.warnType}) >0
        </if>
        <if test="map.stType !=null and  map.stType.size() >0 ">
            and b.stadtp in
            <foreach collection="map.stType" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by a.STCD desc
    </select>
    <select id="getRiverTree" resultType="com.huitu.cloud.api.ewci.bia.entity.BsnBasStBTos">
        WITH SUBQRY(BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE) AS (
        　　SELECT BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE FROM BSN_BAS_B
        <if test="bsnm != null and bsnm !=''">
            WHERE BAS_NAME = #{bsnm}
        </if>
        <if test="bsnm == null or bsnm ==''">
            WHERE PBAS_CODE = ''
        </if>
        　　UNION ALL
        　　SELECT BSN_BAS_B.BAS_CODE,BSN_BAS_B.BAS_NAME,BSN_BAS_B.BAS_LEVEL,BSN_BAS_B.PBAS_CODE FROM BSN_BAS_B,SUBQRY
        WHERE BSN_BAS_B.PBAS_CODE = SUBQRY.BAS_CODE
        )
        SELECT A.BAS_CODE,A.BAS_NAME,A.BAS_LEVEL,A.PBAS_CODE, B.STCD FROM SUBQRY A INNER JOIN BSN_BAS_ST B
        ON A.BAS_CODE = B.BAS_CODE
    </select>
    <select id="getBsnObjOnly" resultType="com.huitu.cloud.api.ewci.bia.entity.BsnObjonlyBs">
        select objid, objcd, objtp
        from bsn_objonly_b
        where objtp = '1'
    </select>
    <select id="getRsvrLatestData" statementType="CALLABLE" parameterType="string"
            resultType="com.huitu.cloud.api.ewci.bia.entity.StRsvrVos">
        {call proc_get_rsvrdata_all_new(
                #{stm, mode=IN}, #{etm, mode=IN}, #{whereSql, mode=IN}
            )}
    </select>
    <select id="getRiverByCondition" resultType="com.huitu.cloud.api.ewci.bia.entity.StRiverVos">
        select STB.STCD,
        STB.STNM,
        A.TM,
        Convert(decimal(18,2),A.Z) Z,
        Q,
        XSA,
        XSAVV,
        XSMXV,
        FLWCHRCD,
        WPTN,
        MSQMT,
        MSAMT,
        MSVMT,
        STB.LTTD,
        STB.LGTD,
        STB.ADNM,
        STB.ADCD,
        STB.XADCD,
        STB.XADNM,
        STB.XZADCD,
        STB.XZADNM,
        STB.STLC,
        STB.FRGRD,
        STB.RVNM,
        STB.BSNM,
        STB.HNNM,
        STB.STTP,
        STB.STADTP,
        STB.STADTPNM,
        fh.LDKEL,
        fh.RDKEL,
        WRZ,
        WRQ,
        GRZ,
        GRQ,
        Z - WRZ ZWRZ,
        Z - GRZ ZGRZ,
        Q - WRQ QWRQ,
        Q - GRQ QGRQ
        ,PLGTD,PLTTD
        from (select stcd, max(tm) tm
        from ST_RIVER_R
        WHERE 1 = 1
        <if test="map.stm != null and map.stm !=''">
            and tm>=CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and TM &lt;= CONVERT(datetime,#{map.etm})
        </if>
        group by stcd) t left join BSN_STBPRP_V STB
        ON t.STCD = STB.STCD
        left join ST_RIVER_R A
        on A.STCD = t.STCD
        and A.TM = t.tm
        left join ST_RVFCCH_B fh
        on a.STCD = fh.STCD
        <if test="map.isFollow!= null and map.isFollow != ''">
            left join BSN_USER_FOLLOWST follow on follow.stcd=t.stcd
        </if>
        <if test="map.type == '1'.toString()">
            left join IA_C_WSADCD W on STB.ADCD = W.ADCD
        </if>
        <if test="map.type == '2'.toString()">
            left join BNS_IA_C_WSADCD W on STB.ADCD = W.ADCD
        </if>
        where 1=1
        and CHARINDEX(STTP,'ZZ,ZQ')>0
        <if test=" map.wscd != null and map.wscd !=''">
            and W.WSCD = #{map.wscd}
        </if>
        <if test="map.isOut == null and map.ad != null and map.ad !=''">
            AND STB.ADCD like '${map.ad}%'
            <if test="map.adLevl !=null and map.adLevl =='4'.toString()">
                and left(STB.ADCD,6) != '220581'
            </if>
        </if>
        <if test="map.stnm !=null and  map.stnm !=''">
            AND CHARINDEX(#{map.stnm},stnm) >0
        </if>
        <if test="map.stType !=null and  map.stType.size() >0">
            AND STB.STADTP IN
            <foreach collection="map.stType" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY ZWRZ desc ,stcd
    </select>
    <select id="getWarnByPage" resultType="com.huitu.cloud.api.ewci.bia.entity.WarnRelatedInfos">
        select A.WARNID, A.ADCD, A.WARNTYPEID, A.WARNGRADEID, A.WARNSTATUSID, A.WARNSTM, A.WARNETM, A.WARNNM,
        A.WARNDESC, A.INNERWARNTM,
        A.INNERWARNGRADEID, A.INNERWARNDESC, A.INNERWARNUSER, A.OUTWARNTM, A.OUTWARNGRADEID, A.OUTWARNDESC,
        A.OUTWARNUSER, A.TYPE, A.ISHANDMADE,
        A.ISFLASH, A.LGTD, A.LTTD, A.OCCURUSER, A.OCCURTM, A.REMARK, A.MODEL,
        (CASE WHEN F.HAS_MOUTAIN_TORRENTS IS NOT NULL THEN F.HAS_MOUTAIN_TORRENTS ELSE 0 END) HAS_MOUTAIN_TORRENTS,
        S.WARNSTATUSNM,WARNGRADENM,AD.ADNM,PD.ADNM PADNM,PD.ADCD PADCD,S.SHORTNM,
        (select COUNT(1) MSGCT from MESSAGESEND_R INFO,MESSAGEINFO_R B where INFO.MSGID=B.MSGID AND INFO.ADCD=B.ADCD and
        b.WarnID=A.WarnID and b.MEDIAID='10') MSGCT
        from WARNRECORD_R A
        LEFT JOIN BSN_ADCD_B AD ON AD.ADCD=A.ADCD
        LEFT JOIN BSN_ADCD_B PD ON substring(A.ADCD,1,6)+'000000000'=PD.ADCD
        LEFT JOIN WARNINGGRADE_B GB ON A.WARNGRADEID=GB.WARNGRADEID
        LEFT JOIN WARNINGSTATUS_B S ON S.WARNSTATUSID=A.WARNSTATUSID
        LEFT JOIN WARN_FEEDBACK F ON A.WARNID=F.WARNID
        <if test="map.type == '1'.toString()">
            LEFT JOIN IA_C_WSADCD W ON A.ADCD = LEFT(W.ADCD,9)
        </if>
        <if test="map.type == '2'.toString()">
            LEFT JOIN BNS_IA_C_WSADCD W ON A.ADCD = LEFT(W.ADCD,9)
        </if>
        where 1=1 and a.TYPE!='D'
        <if test="map.wscd !=null and map.wscd !=''">
            and W.WSCD = #{map.wscd}
        </if>
        <if test="map.ad !=null and map.ad !=''">
            and substring(A.ADCD,1,#{map.level})=#{map.ad}
        </if>
        <if test="map.stm != null and map.stm !=''">
            and A.WARNSTM> CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and A.WARNSTM &lt;= CONVERT(datetime,#{map.etm})
        </if>
        <if test="map.warnStatusId != null and map.warnStatusId !=''">
            AND A.WARNSTATUSID=#{map.warnStatusId}
        </if>
        <if test="map.warnGradeId != null and map.warnGradeId !=''">
            AND A.WARNGRADEID=#{map.warnGradeId}
        </if>
        <if test="map.queryType != null and map.queryType=='1'.toString()">
            AND A.WARNSTATUSID !='30' and (A.WARNETM is null or A.WARNETM = '1900-01-01 00:00:00')
        </if>
        order by A.WARNSTM desc
    </select>
</mapper>
