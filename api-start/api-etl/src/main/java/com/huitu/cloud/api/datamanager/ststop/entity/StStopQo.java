package com.huitu.cloud.api.datamanager.ststop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value="StStopQo", description="站点屏蔽记录-添加参数类")
public class StStopQo {
    @ApiModelProperty(value = "站码")
    private String stcd;
    @ApiModelProperty(value = "站点屏蔽类型  1：计划型 0：非计划型， 仅当屏蔽类型为（1：计划型）时， 开始时间结束时间必须传值")
    private String ststtp;
    @ApiModelProperty(value = "用户")
    private String usernm;
    @ApiModelProperty(value = "开始时间 yyyy-MM-dd HH:mm:ss")
    private String stm;
    @ApiModelProperty(value = "结束时间 yyyy-MM-dd HH:mm:ss")
    private String etm;

    @ApiModelProperty(value = "屏蔽要素 雨量：st_pptn_r , 河道水位：st_river_r, 水库水位：st_rsvr_r")
    private String tbnm;

    @ApiModelProperty(value = "是否显示 1：是 0:否")
    private String isAll;

    @ApiModelProperty(value = "屏蔽原因")
    private String reason;

    @ApiModelProperty(value = "水位屏蔽原因")
    private String waterReason;

    @ApiModelProperty(value = "河道屏蔽原因")
    private String rainfallReason;

//    @ApiModelProperty(value = "屏蔽要素 0-雨量  1-水位 ")
//    private List<String> factor;

    @ApiModelProperty(value = "是否显示 1：是 0:否 （BSN_STSTOPEX_R表）")
    private String isShow;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStsttp() {
        return ststtp;
    }

    public void setStsttp(String ststtp) {
        this.ststtp = ststtp;
    }

    public String getUsernm() {
        return usernm;
    }

    public void setUsernm(String usernm) {
        this.usernm = usernm;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getTbnm() {
        return tbnm;
    }

    public void setTbnm(String tbnm) {
        this.tbnm = tbnm;
    }

    public String getIsAll() {
        return isAll;
    }

    public void setIsAll(String isAll) {
        this.isAll = isAll;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getWaterReason() {
        return waterReason;
    }

    public void setWaterReason(String waterReason) {
        this.waterReason = waterReason;
    }

    public String getRainfallReason() {
        return rainfallReason;
    }

    public void setRainfallReason(String rainfallReason) {
        this.rainfallReason = rainfallReason;
    }

    public String getIsShow() {
        return isShow;
    }

    public void setIsShow(String isShow) {
        this.isShow = isShow;
    }
}
