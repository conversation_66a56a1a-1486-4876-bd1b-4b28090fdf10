<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.hsfxt.riverrisk.mapper.FxtRiverRiskDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmap">
        <result column="RV_CODE" property="rvCode" />
        <result column="DEPTH" property="depth" />
        <result column="RV_NAME" property="rvName" />
        <result column="AREA" property="area" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RV_CODE, DEPTH, RV_NAME, AREA
    </sql>
    <select id="getRiverRiskList" resultType="com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmapVo">
        WITH BASLIST AS (
            SELECT RV_CODE, RV_NAME, PRV_CODE, RV_LEVEL, BAS_CODE FROM BNS_RIVER WHERE
            <if test="map.rvcode !=null and map.rvcode !=''">
                BAS_CODE = #{map.rvcode}
            </if>
            <if test="map.rvcode ==null or map.rvcode ==''">
                RV_LEVEL = '1'
            </if>

            UNION ALL
            SELECT B.RV_CODE, B.RV_NAME, B.PRV_CODE, B.RV_LEVEL, B.BAS_CODE FROM BASLIST A INNER JOIN BNS_RIVER B ON A.RV_CODE = B.PRV_CODE
        )

        select ltrim(rtrim(a.rv_code)) rv_code, d.rv_name, d.prv_code, e.rv_name prv_name,d.RV_LEVEL, a.area, a.depth, e.rv_code riverCode, d.BAS_CODE basCode from RV_RISKMAP a
            inner join bsn_objonly_b b on a.RV_CODE = b.objcd and b.objtp = '32'
            inner join bsn_objonly_b c on c.objid = b.objid and c.objtp = '4'
            inner join (select distinct RV_CODE, RV_NAME, PRV_CODE, RV_LEVEL, BAS_CODE from BASLIST) d on c.objcd = d.RV_CODE
            left join BASLIST e on d.PRV_CODE = e.RV_CODE
        <where>
            <if test="map.name != null and map.name != ''">
                AND CHARINDEX(#{map.name}, d.rv_name)>0
            </if>
        </where>
        order by a.rv_code
    </select>

</mapper>
