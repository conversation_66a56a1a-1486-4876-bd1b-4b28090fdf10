package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@TableName("WARN_RWM_STATUS_R")
@ApiModel(value="WarnRwmStatusR对象", description="")
public class WarnRwmStatusR extends Model<WarnRwmStatusR> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "预警ID")
    @TableId(value = "STWARNID", type = IdType.NONE)
    private String stwarnid;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "入户报警器编号")
    @TableField("RWID")
    private Integer rwid;

    @ApiModelProperty(value = "入户报警器类型")
    @TableField("RWTP")
    private Integer rwtp;

    @ApiModelProperty(value = "操作时间")
    @TableField("TM")
    private LocalDateTime tm;

    @ApiModelProperty(value = "入户报警器状态（0-关闭、1-开启、9-失联）")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "指令类型（10-自动触发报警、20-人工触发报警）")
    @TableField("CMD_TYPE")
    private Integer cmdType;


    public String getStwarnid() {
        return stwarnid;
    }

    public void setStwarnid(String stwarnid) {
        this.stwarnid = stwarnid;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Integer getRwid() {
        return rwid;
    }

    public void setRwid(Integer rwid) {
        this.rwid = rwid;
    }

    public Integer getRwtp() {
        return rwtp;
    }

    public void setRwtp(Integer rwtp) {
        this.rwtp = rwtp;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCmdType() {
        return cmdType;
    }

    public void setCmdType(Integer cmdType) {
        this.cmdType = cmdType;
    }

    @Override
    protected Serializable pkVal() {
        return this.stwarnid;
    }

    @Override
    public String toString() {
        return "WarnRwmStatusR{" +
        "stwarnid=" + stwarnid +
        ", stcd=" + stcd +
        ", rwid=" + rwid +
        ", rwtp=" + rwtp +
        ", tm=" + tm +
        ", status=" + status +
        ", cmdType=" + cmdType +
        "}";
    }
}
