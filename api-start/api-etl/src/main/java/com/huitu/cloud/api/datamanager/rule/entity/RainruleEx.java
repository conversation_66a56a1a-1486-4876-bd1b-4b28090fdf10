package com.huitu.cloud.api.datamanager.rule.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="RainruleEx对象", description="雨量规则一键配置表")
public class RainruleEx {
    @ApiModelProperty(value = "分钟数")
    @TableField("MNCNT")
    private Integer inv;

    @ApiModelProperty(value = "瞬时阈值")
    @TableField("SNV")
    private Double snv;

    @ApiModelProperty(value = "5分钟数据量阈值")
    @TableField("CTPM")
    private Integer ctpm5;

    @ApiModelProperty(value = "备注")
    @TableField("NT")
    private String nt;
    @ApiModelProperty(value = "政区")
    private String adcd;

    public Integer getInv() {
        return inv;
    }

    public void setInv(Integer inv) {
        this.inv = inv;
    }

    public Integer getCtpm5() {
        return ctpm5;
    }

    public void setCtpm5(Integer ctpm5) {
        this.ctpm5 = ctpm5;
    }

    public Double getSnv() {
        return snv;
    }

    public void setSnv(Double snv) {
        this.snv = snv;
    }


    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }
}
