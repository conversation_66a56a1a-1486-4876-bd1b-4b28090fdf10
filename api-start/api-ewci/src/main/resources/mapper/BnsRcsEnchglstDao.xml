<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rcs.mapper.BnsRcsEnchglstDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.rcs.entity.BnsRcsEnchglstVo" useCache="false">
        SELECT D.ID, D.ENTYPE, D.ENNMCD, D.ENNM, D.ADCD, D.NEW_ENNMCD, D.NEW_ENNM, D.TYPE, D.REASON, D.CREATOR,
        D.CREATED_DATE, D.CREATOR_UNIT, D.CREATOR_PHONE, D.AUDITOR, D<PERSON>AUDITED_DATE, D.AUDITOR_UNIT, D.AUDITOR_PHONE,
        D.TS, A.ADNM
        FROM BNS_RCS_ENCHGLST D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE LEFT(D.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.ennm!= null and map.ennm !=''">
            AND CHARINDEX(#{map.ennm}, D.ENNM) > 0
        </if>
        <if test="map.entype !=null and map.entype !=''">
            AND D.ENTYPE = #{map.entype}
        </if>
        <if test="map.type!= null and map.type !=''">
            AND D.TYPE = #{map.type}
        </if>
        ORDER BY D.ADCD, D.TYPE, D.ID ASC
    </select>

    <select id="exportPageList" resultType="com.huitu.cloud.api.ewci.rcs.entity.BnsRcsEnchglstVo" useCache="false">
        SELECT D.ID, CASE
        D.ENTYPE
        WHEN '03' THEN
        '水库'
        WHEN '06' THEN
        '堤防'
        WHEN '08' THEN
        '蓄滞洪区'
        WHEN '11' THEN
        '水闸'
        END AS ENTYPE, D.ENNMCD, D.ENNM, D.ADCD, D.NEW_ENNMCD, D.NEW_ENNM, D.TYPE, D.REASON, D.CREATOR,
        D.CREATED_DATE, D.CREATOR_UNIT, D.CREATOR_PHONE, D.AUDITOR, D.AUDITED_DATE, D.AUDITOR_UNIT, D.AUDITOR_PHONE,
        D.TS, A.ADNM
        FROM BNS_RCS_ENCHGLST D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE LEFT(D.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.ennm!= null and map.ennm !=''">
            AND CHARINDEX(#{map.ennm}, D.ENNM) > 0
        </if>
        <if test="map.entype !=null and map.entype !=''">
            AND D.ENTYPE = #{map.entype}
        </if>
        <if test="map.type!= null and map.type !=''">
            AND D.TYPE = #{map.type}
        </if>
        ORDER BY D.ADCD, D.TYPE, D.ID ASC
    </select>

    <select id="getStatListByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT D.ADCD, A.ADNM, COUNT(D.ID) TOTAL_COUNT
        FROM (
        SELECT ID, LEFT(LEFT(ADCD, #{map.lowerLevel}) + '000000000000000', 15) ADCD FROM BNS_RCS_ENCHGLST
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT ID, '220581000000000' ADCD FROM BNS_RCS_ENCHGLST WHERE LEFT (ADCD, 6) = '220581'
        </if>
        ) D
        LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        GROUP BY D.ADCD, A.ADNM
        ORDER BY D.ADCD ASC
    </select>
    <select id="getStatByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT A.ADCD, A.ADNM, D.TOTAL_COUNT
        FROM BSN_ADCD_B A,
        (SELECT COUNT(ID) TOTAL_COUNT FROM BNS_RCS_ENCHGLST
        WHERE LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) D
        WHERE A.ADCD = #{map.adcd}
    </select>
    <select id="getCountListWithEnchglstField" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsPropCount">
        SELECT ISNULL(${field}, '1') PROP_CODE, COUNT(ID) PROP_COUNT
        FROM BNS_RCS_ENCHGLST
        WHERE LEFT (ADCD , #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY ${field}
        ORDER BY ${field} ASC
    </select>
    <select id="getStatListWithProp" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsChgStat">
        SELECT E.ADCD, A.ADNM, E.TYPE, COUNT(E.ID) TOTAL_COUNT
        FROM (
        SELECT LEFT(LEFT(ADCD, #{map.lowerLevel}) + '000000000000000', 15) ADCD, ${propName} TYPE, ID FROM BNS_RCS_ENCHGLST
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000', ${propName} TYPE, ID FROM BNS_RCS_ENCHGLST WHERE LEFT(ADCD, 6) = '220581'
        </if>
        ) E
        LEFT JOIN BSN_ADCD_B A ON A.ADCD = E.ADCD
        GROUP BY E.ADCD, A.ADNM, E.TYPE
        ORDER BY E.ADCD ASC
    </select>
</mapper>
