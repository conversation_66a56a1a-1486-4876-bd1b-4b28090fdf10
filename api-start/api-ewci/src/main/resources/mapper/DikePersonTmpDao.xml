<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.DikePersonTmpDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="batchInsert">
        INSERT INTO BNS_DIKEPERSON_TMP (ID, NO, XADCD, ADNM, TOWN, RV_NAME, DIKE_NAME, START_LGTD, START_LTTD, END_LGTD,
        END_LTTD, DIKE_TYPE, DIKE_LEN, DIKE_BANK, XZ_REALNM, XZ_POST, XZ_MOBILE, JS_REALNM, JS_POST, J<PERSON>_<PERSON><PERSON><PERSON><PERSON>,
        CJ_REALNM, CJ_POST, CJ_MOBILE, TCOUNT)
        SELECT T.ID, T.NO, T.XADCD, T.ADNM, T.TOWN, T.RV_NAME, T.DIKE_NAME, T.START_LGTD, T.START_LTTD, T.END_LGTD,
        T.END_LTTD, T.DIKE_TYPE, T.DIKE_LEN, T.DIKE_BANK, T.XZ_REALNM, T.XZ_POST, T.XZ_MOBILE, T.JS_REALNM, T.JS_POST,
        T.JS_MOBILE, T.CJ_REALNM, T.CJ_POST, T.CJ_MOBILE, T.TCOUNT FROM (VALUES
        <foreach collection="map.list" item="item" separator=",">
            (NEWID(), #{map.batchNo}, #{map.xadcd}, #{item.adnm}, #{item.town}, #{item.rvName}, #{item.dikeName},
            #{item.startLgtd}, #{item.startLttd}, #{item.endLgtd}, #{item.endLttd}, #{item.dikeType}, #{item.dikeLen},
            #{item.dikeBank}, #{item.xzRealnm}, #{item.xzPost}, #{item.xzMobile}, #{item.jsRealnm}, #{item.jsPost},
            #{item.jsMobile}, #{item.cjRealnm}, #{item.cjPost}, #{item.cjMobile}, #{item.tcount})
        </foreach>) AS T(ID, NO, XADCD, ADNM, TOWN, RV_NAME, DIKE_NAME, START_LGTD, START_LTTD, END_LGTD,
        END_LTTD, DIKE_TYPE, DIKE_LEN, DIKE_BANK, XZ_REALNM, XZ_POST, XZ_MOBILE, JS_REALNM, JS_POST, JS_MOBILE,
        CJ_REALNM, CJ_POST, CJ_MOBILE, TCOUNT)
    </insert>
</mapper>
