package com.huitu.cloud.api.ewci.consult.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.consult.entity.*;

import java.util.List;

/**
 * 防汛会商服务
 *
 * <AUTHOR>
 */
public interface ConsultService {

    /**
     * 实时水情-水情统计-市县-河道 统计
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<ConsultWaterAdRiverVo> getWaterAdRiverStatList(String adcd, String stm, String etm);

    /**
     * 实时水情-水情统计-市县-水库 统计
     * @param adcd
     * @param stm
     * @param etm
     * @param rvtp
     * @param stType
     * @return
     */
    List<ConsultWaterAdRsvrVo> getWaterAdRsvrStatList(String adcd, String stm, String etm, String rvtp, String stType);

    /**
     * 实时水情-水情统计-流域-河道 统计
     * @param basCode
     * @param stm
     * @param etm
     * @return
     */
    List<ConsultWaterBasRiverVo> getWaterBasRiverStatList(String basCode, String stm, String etm);

    /**
     * 实时水情-水情统计-流域（19条主要江河）-河道 统计
     * @param stm
     * @param etm
     * @return
     */
    List<ConsultWaterBasRiverVo> getWaterBasZyjhRiverStatList(String stm, String etm);

    /**
     * 实时水情-水情统计-流域-水库 统计
     * @param basCode
     * @param stm
     * @param etm
     * @param rvtp
     * @param stType
     * @return
     */
    List<ConsultWaterBasRsvrVo> getWaterBasRsvrStatList(String basCode, String stm, String etm, String rvtp, String stType);

    /**
     * 实时水情-水情统计-流域-水库 统计
     * @param stm
     * @param etm
     * @param rvtp
     * @param stType
     * @return
     */
    List<ConsultWaterBasRsvrVo> getWaterBasZyjhRsvrStatList(String stm, String etm, String rvtp, String stType);

    /**
     * 预警查看-24h预警-山洪预警 统计
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<ConsultWarnAdVo> getWarnAdShStatList(String adcd, String stm, String etm);

    /**
     * 预警查看-24h预警-河道预警 统计
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<ConsultWarnAdVo> getWarnAdRiverStatList(String adcd, String stm, String etm);

    /**
     * 预警查看-24h预警-水库预警 统计
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<ConsultWarnAdVo> getWarnAdRsvrStatList(String adcd, String stm, String etm);

    /**
     * 风险研判-超汛超警-市县-河道 统计
     * @param adcd
     * @return
     */
    List<ConsultRiskAdRiverVo> getRiskAdRiverStatList(String adcd);

    /**
     * 风险研判-超汛超警-市县-水库 统计
     * @param adcd
     * @param rvtp
     * @param stType
     * @return
     */
    List<ConsultRiskAdRsvrVo> getRiskAdRsvrStatList(String adcd, String rvtp, String stType);

    /**
     * 风险研判-超汛超警-流域-河道 统计
     * @param basCode
     * @return
     */
    List<ConsultRiskBasRiverVo> getRiskBasRiverStatList(String basCode);

    /**
     * 风险研判-超汛超警-流域-水库 统计
     * @param basCode
     * @param rvtp
     * @param stType
     * @return
     */
    List<ConsultRiskBasRsvrVo> getRiskBasRsvrStatList(String basCode, String rvtp, String stType);

    /**
     * 防御工作方案列表分页查询
     *
     * @param query
     * @return
     */
    IPage<BsnDefenseWorkPlanB> getPage(DefenseWorkPlanQo<BsnDefenseWorkPlanB> query);

    /**
     * 获取最大期数
     *
     * @return
     */
    BsnDefenseWorkPlanB getMaxPlanNo();

    /**
     * 新增防御工作方案
     *
     * @param entity
     * @return
     */
    BsnDefenseWorkPlanB insert(BsnDefenseWorkPlanB entity);

    /**
     * 编辑防御工作方案
     *
     * @param entity
     * @return
     */
    BsnDefenseWorkPlanB update(BsnDefenseWorkPlanB entity);


    /**
     * 生成防御工作方案文件
     *
     * @param request
     * @return
     */
    Integer saveFile(DefenseWorkPlanSo request);

    /**
     * 获取专报文件文档
     *
     * @param dwpId
     * @return
     */
    BsnDefenseWorkPlanB getDocument(String dwpId);

    /**
     * 发送
     *
     * @param dwpId
     * @return
     */
    BsnDefenseWorkPlanB send(String dwpId);
}
