package com.huitu.cloud.api.syq.rsvr.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 水库水情表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-09
 */
@TableName("ST_RSVR_R")
@ApiModel(value="StRsvrR对象", description="水库水情表")
public class StRsvrR extends Model<StRsvrR> {

    private static final long serialVersionUID=2L;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "库上水位")
    @TableField("RZ")
    private String rz;

    @ApiModelProperty(value = "入库流量")
    @TableField("INQ")
    private BigDecimal inq;

    @ApiModelProperty(value = "蓄水量")
    @TableField("W")
    private BigDecimal w;

    @ApiModelProperty(value = "库下水位")
    @TableField("BLRZ")
    private BigDecimal blrz;

    @ApiModelProperty(value = "出库流量")
    @TableField("OTQ")
    private BigDecimal otq;

    @ApiModelProperty(value = "库水特征码")
    @TableField("RWCHRCD")
    private String rwchrcd;

    @ApiModelProperty(value = "库水水势")
    @TableField("RWPTN")
    private String rwptn;

    @ApiModelProperty(value = "入流时段长")
    @TableField("INQDR")
    private BigDecimal inqdr;

    @ApiModelProperty(value = "测流方法")
    @TableField("MSQMT")
    private String msqmt;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getRz() {
        return rz;
    }

    public void setRz(String rz) {
        this.rz = rz;
    }

    public BigDecimal getInq() {
        return inq;
    }

    public void setInq(BigDecimal inq) {
        this.inq = inq;
    }

    public BigDecimal getW() {
        return w;
    }

    public void setW(BigDecimal w) {
        this.w = w;
    }

    public BigDecimal getBlrz() {
        return blrz;
    }

    public void setBlrz(BigDecimal blrz) {
        this.blrz = blrz;
    }

    public BigDecimal getOtq() {
        return otq;
    }

    public void setOtq(BigDecimal otq) {
        this.otq = otq;
    }

    public String getRwchrcd() {
        return rwchrcd;
    }

    public void setRwchrcd(String rwchrcd) {
        this.rwchrcd = rwchrcd;
    }

    public String getRwptn() {
        return rwptn;
    }

    public void setRwptn(String rwptn) {
        this.rwptn = rwptn;
    }

    public BigDecimal getInqdr() {
        return inqdr;
    }

    public void setInqdr(BigDecimal inqdr) {
        this.inqdr = inqdr;
    }

    public String getMsqmt() {
        return msqmt;
    }

    public void setMsqmt(String msqmt) {
        this.msqmt = msqmt;
    }

    @Override
    protected Serializable pkVal() {
        return this.tm;
    }

    @Override
    public String toString() {
        return "StRsvrR{" +
        "stcd=" + stcd +
        ", tm=" + tm +
        ", rz=" + rz +
        ", inq=" + inq +
        ", w=" + w +
        ", blrz=" + blrz +
        ", otq=" + otq +
        ", rwchrcd=" + rwchrcd +
        ", rwptn=" + rwptn +
        ", inqdr=" + inqdr +
        ", msqmt=" + msqmt +
        "}";
    }
}
