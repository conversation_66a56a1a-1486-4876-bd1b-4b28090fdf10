package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2019-09-17
 */
@ApiModel(value="预警关联危险区统计信息", description="预警关联危险区统计信息")
public class WarnDanadStatistics implements Serializable {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "危险区内人口总数")
    private String ptcount;
    @ApiModelProperty(value = "危险区内总户数")
    private String etcount;
    @ApiModelProperty(value = "危险区内总房屋数")
    private String htcount;
    @ApiModelProperty(value = "危险区总数")
    private String dancount;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPtcount() {
        return ptcount;
    }

    public void setPtcount(String ptcount) {
        this.ptcount = ptcount;
    }

    public String getEtcount() {
        return etcount;
    }

    public void setEtcount(String etcount) {
        this.etcount = etcount;
    }

    public String getHtcount() {
        return htcount;
    }

    public void setHtcount(String htcount) {
        this.htcount = htcount;
    }

    public String getDancount() {
        return dancount;
    }

    public void setDancount(String dancount) {
        this.dancount = dancount;
    }
}
