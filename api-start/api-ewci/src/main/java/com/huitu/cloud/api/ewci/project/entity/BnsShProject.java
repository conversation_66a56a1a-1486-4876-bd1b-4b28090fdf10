package com.huitu.cloud.api.ewci.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <p>
 * 建设（运行维护）进度情况统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@TableName("BNS_SH_PROJECT")
@ApiModel(value="BnsShProject对象", description="建设（运行维护）进度情况统计表")
public class BnsShProject extends Model<BnsShProject> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划名称")
    @TableField("ADCD")
    private String adnm;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "代码")
    @TableField("ADCDS")
    private String adcds;

    @ApiModelProperty(value = "年度")
    @TableField("YEAR")
    private String year;

    @ApiModelProperty(value = "批次编码，生成规则：yyyyMMdd，默认：00000000")
    @TableField("BNO")
    private String bno;

    @ApiModelProperty(value = "建设-中央下达资金")
    @TableField("CS_FUNDS1")
    private Double csFunds1;

    @ApiModelProperty(value = "建设-省级下达资金")
    @TableField("CS_FUNDS2")
    private Double csFunds2;

    @ApiModelProperty(value = "建设-实施方案是否批复")
    @TableField("CS_APPROVED")
    private String csApproved;

    @ApiModelProperty(value = "建设-招标情况")
    @TableField("CS_BIDTP")
    private String csBidtp;

    @ApiModelProperty(value = "建设-中标单位（实施单位）")
    @TableField("CS_IMPL_UNIT")
    private String csImplUnit;

    @ApiModelProperty(value = "建设-完成投资")
    @TableField("CS_INVESTED")
    private Double csInvested;

    @ApiModelProperty(value = "建设-形象进度")
    @TableField("CS_PROGRESS")
    private String csProgress;

    @ApiModelProperty(value = "建设-完成支付")
    @TableField("CS_PAID")
    private Double csPaid;

    @ApiModelProperty(value = "建设-初步验收")
    @TableField("CS_INITIALIZED")
    private String csInitialized;

    @ApiModelProperty(value = "建设-竣工验收")
    @TableField("CS_COMPLETED")
    private String csCompleted;

    @ApiModelProperty(value = "建设-预计完成时间")
    @TableField("CS_PREDICT_TIME")
    private String csPredictTime;

    @ApiModelProperty(value = "运维-下达资金")
    @TableField("OP_FUNDS1")
    private Double opFunds1;

    @ApiModelProperty(value = "运维-本级配套资金")
    @TableField("OP_FUNDS2")
    private Double opFunds2;

    @ApiModelProperty(value = "运维-实施方案是否批复")
    @TableField("OP_APPROVED")
    private String opApproved;

    @ApiModelProperty(value = "运维-招标情况")
    @TableField("OP_BIDTP")
    private String opBidtp;

    @ApiModelProperty(value = "运维-中标单位（实施单位）")
    @TableField("OP_IMPL_UNIT")
    private String opImplUnit;

    @ApiModelProperty(value = "运维-完成投资")
    @TableField("OP_INVESTED")
    private Double opInvested;

    @ApiModelProperty(value = "运维-形象进度")
    @TableField("OP_PROGRESS")
    private String opProgress;

    @ApiModelProperty(value = "运维-完成支付")
    @TableField("OP_PAID")
    private Double opPaid;

    @ApiModelProperty(value = "运维-初步验收")
    @TableField("OP_INITIALIZED")
    private String opInitialized;

    @ApiModelProperty(value = "运维-竣工验收")
    @TableField("OP_COMPLETED")
    private String opCompleted;

    @ApiModelProperty(value = "运维-预计完成时间")
    @TableField("OP_PREDICT_TIME")
    private String opPredictTime;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED")
    private Date created;

    @ApiModelProperty(value = "统计时间")
    @TableField("COMPLETED")
    private Date completed;

    @ApiModelProperty(value = "时间戳")
    @TableField("TS")
    private Date ts;

    public String getAdcds() {
        return adcds;
    }

    public void setAdcds(String adcds) {
        this.adcds = adcds;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBno() {
        return bno;
    }

    public void setBno(String bno) {
        this.bno = bno;
    }

    public Double getCsFunds1() {
        return csFunds1;
    }

    public void setCsFunds1(Double csFunds1) {
        this.csFunds1 = csFunds1;
    }

    public Double getCsFunds2() {
        return csFunds2;
    }

    public void setCsFunds2(Double csFunds2) {
        this.csFunds2 = csFunds2;
    }

    public String getCsApproved() {
        return csApproved;
    }

    public void setCsApproved(String csApproved) {
        this.csApproved = csApproved;
    }


    public String getCsImplUnit() {
        return csImplUnit;
    }

    public void setCsImplUnit(String csImplUnit) {
        this.csImplUnit = csImplUnit;
    }

    public Double getCsInvested() {
        return csInvested;
    }

    public void setCsInvested(Double csInvested) {
        this.csInvested = csInvested;
    }

    public String getCsProgress() {
        return csProgress;
    }

    public void setCsProgress(String csProgress) {
        this.csProgress = csProgress;
    }

    public Double getCsPaid() {
        return csPaid;
    }

    public void setCsPaid(Double csPaid) {
        this.csPaid = csPaid;
    }

    public String getCsInitialized() {
        return csInitialized;
    }

    public void setCsInitialized(String csInitialized) {
        this.csInitialized = csInitialized;
    }

    public String getCsCompleted() {
        return csCompleted;
    }

    public void setCsCompleted(String csCompleted) {
        this.csCompleted = csCompleted;
    }

    public String getCsPredictTime() {
        return csPredictTime;
    }

    public void setCsPredictTime(String csPredictTime) {
        this.csPredictTime = csPredictTime;
    }

    public Double getOpFunds1() {
        return opFunds1;
    }

    public void setOpFunds1(Double opFunds1) {
        this.opFunds1 = opFunds1;
    }

    public Double getOpFunds2() {
        return opFunds2;
    }

    public void setOpFunds2(Double opFunds2) {
        this.opFunds2 = opFunds2;
    }

    public String getOpApproved() {
        return opApproved;
    }

    public void setOpApproved(String opApproved) {
        this.opApproved = opApproved;
    }

    public String getOpImplUnit() {
        return opImplUnit;
    }

    public void setOpImplUnit(String opImplUnit) {
        this.opImplUnit = opImplUnit;
    }

    public Double getOpInvested() {
        return opInvested;
    }

    public void setOpInvested(Double opInvested) {
        this.opInvested = opInvested;
    }

    public String getOpProgress() {
        return opProgress;
    }

    public void setOpProgress(String opProgress) {
        this.opProgress = opProgress;
    }

    public Double getOpPaid() {
        return opPaid;
    }

    public void setOpPaid(Double opPaid) {
        this.opPaid = opPaid;
    }

    public String getOpInitialized() {
        return opInitialized;
    }

    public void setOpInitialized(String opInitialized) {
        this.opInitialized = opInitialized;
    }

    public String getOpCompleted() {
        return opCompleted;
    }

    public void setOpCompleted(String opCompleted) {
        this.opCompleted = opCompleted;
    }

    public String getOpPredictTime() {
        return opPredictTime;
    }

    public void setOpPredictTime(String opPredictTime) {
        this.opPredictTime = opPredictTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getCompleted() {
        return completed;
    }

    public void setCompleted(Date completed) {
        this.completed = completed;
    }

    public Date getTs() {
        return ts;
    }

    public String getCsBidtp() {
        return csBidtp;
    }

    public void setCsBidtp(String csBidtp) {
        this.csBidtp = csBidtp;
    }

    public String getOpBidtp() {
        return opBidtp;
    }

    public void setOpBidtp(String opBidtp) {
        this.opBidtp = opBidtp;
    }

    public void setTs(Date ts) {
        this.ts = ts;
    }

}
