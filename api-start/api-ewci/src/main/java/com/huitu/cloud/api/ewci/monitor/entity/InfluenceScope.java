package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 影响范围统计
 *
 * <AUTHOR>
 */
@ApiModel(value = "影响范围统计")
public class InfluenceScope implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "行政区划完整名称")
    @TableField(value = "FULL_ADNM")
    private String fullAdnm;

    @ApiModelProperty(value = "防治区类型 1=防治区，2=重点防治区，其他为非防治区")
    @TableField(value = "PREVTP")
    private String prevtp;

    @ApiModelProperty(value = "山洪沟数量")
    @TableField(value = "GULLY_COUNT")
    private Integer gullyCount;

    @ApiModelProperty(value = "堤防数量")
    @TableField(value = "DIKE_COUNT")
    private Integer dikeCount;

    @ApiModelProperty(value = "水库数量")
    @TableField(value = "RES_COUNT")
    private Integer resCount;

    @ApiModelProperty(value = "塘（堰）坝数量")
    @TableField(value = "DAM_COUNT")
    private Integer damCount;

    @ApiModelProperty(value = "桥梁数量")
    @TableField(value = "BRID_COUNT")
    private Integer bridCount;

    @ApiModelProperty(value = "水闸数量")
    @TableField(value = "WAGA_COUNT")
    private Integer wagaCount;

    @ApiModelProperty(value = "路涵数量")
    @TableField(value = "CUL_COUNT")
    private Integer culCount;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getFullAdnm() {
        return fullAdnm;
    }

    public void setFullAdnm(String fullAdnm) {
        this.fullAdnm = fullAdnm;
    }

    public String getPrevtp() {
        return prevtp;
    }

    public void setPrevtp(String prevtp) {
        this.prevtp = prevtp;
    }

    public Integer getGullyCount() {
        return gullyCount;
    }

    public void setGullyCount(Integer gullyCount) {
        this.gullyCount = gullyCount;
    }

    public Integer getDikeCount() {
        return dikeCount;
    }

    public void setDikeCount(Integer dikeCount) {
        this.dikeCount = dikeCount;
    }

    public Integer getResCount() {
        return resCount;
    }

    public void setResCount(Integer resCount) {
        this.resCount = resCount;
    }

    public Integer getDamCount() {
        return damCount;
    }

    public void setDamCount(Integer damCount) {
        this.damCount = damCount;
    }

    public Integer getBridCount() {
        return bridCount;
    }

    public void setBridCount(Integer bridCount) {
        this.bridCount = bridCount;
    }

    public Integer getWagaCount() {
        return wagaCount;
    }

    public void setWagaCount(Integer wagaCount) {
        this.wagaCount = wagaCount;
    }

    public Integer getCulCount() {
        return culCount;
    }

    public void setCulCount(Integer culCount) {
        this.culCount = culCount;
    }
}
