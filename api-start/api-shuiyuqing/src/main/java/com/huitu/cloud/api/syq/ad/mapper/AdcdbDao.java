package com.huitu.cloud.api.syq.ad.mapper;

import com.huitu.cloud.api.syq.ad.entity.AdcdB;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 政区基本信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
public interface AdcdbDao {
    /**
     * 查询当前政区以及下级 列表信息  可以通过adLevl控制查询的级别
     * @param map
     * @return
     */
    List<AdcdB> selectByAdLevel(Map<String, Object> map);

    /**
     * 根据政区编码查询下一级政区
     * @param adcd
     * @return
     */
    List<AdcdB> getAdSonList(@Param("adcd") String adcd);
}
