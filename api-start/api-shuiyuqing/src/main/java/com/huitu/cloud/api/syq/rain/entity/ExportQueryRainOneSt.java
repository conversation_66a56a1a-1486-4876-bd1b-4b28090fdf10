package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * <p>
 * 单站时段雨量累计雨量统计导出接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-29
 */
@ApiModel(value = "ExportQueryRainOneSt对象", description = "单站时段雨量累计雨量统计导出接口")
public class ExportQueryRainOneSt {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true)
    private String etm;
    @ApiModelProperty(value = "测站名称")
    private String stcd;

    @ApiModelProperty(value = "时分")
    private String type;

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
