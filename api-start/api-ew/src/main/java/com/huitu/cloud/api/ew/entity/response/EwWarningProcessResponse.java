package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.api.ew.entity.base.EwWarningProcess;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 山洪预警过程响应对象
 */
@ApiModel(value = "山洪预警过程响应对象")
public class EwWarningProcessResponse extends EwWarningProcess {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警等级名称")
    @TableField(value = "WARN_GRADE_NAME")
    private String warnGradeName;
    @ApiModelProperty(value = "预警等级别名")
    @TableField(value = "WARN_GRADE_ALIAS_NAME")
    private String warnGradeAliasName;
    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;
    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;
    @ApiModelProperty(value = "平台名称")
    @TableField(value = "PLATFORM_NAME")
    private String platformName;
    @ApiModelProperty(value = "小流域代码")
    @TableField(value = "WSCD")
    private String wscd;
    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;
    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STNM")
    private String stnm;
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "STM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;
    @ApiModelProperty(value = "结束时间")
    @TableField(value = "ETM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;
    @ApiModelProperty(value = "累计降水量")
    @TableField(value = "ACCP")
    private BigDecimal accp;
    @ApiModelProperty(value = "土壤含水率时间")
    @TableField(value = "SLM_TM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date slmTm;
    @ApiModelProperty(value = "土壤含水率")
    @TableField(value = "SLM")
    private BigDecimal slm;
    @ApiModelProperty(value = "超渗降水，注：1=是、0=否")
    @TableField(value = "SLEP")
    private String slep;

    public String getWarnGradeName() {
        return warnGradeName;
    }

    public void setWarnGradeName(String warnGradeName) {
        this.warnGradeName = warnGradeName;
    }

    public String getWarnGradeAliasName() {
        return warnGradeAliasName;
    }

    public void setWarnGradeAliasName(String warnGradeAliasName) {
        this.warnGradeAliasName = warnGradeAliasName;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public BigDecimal getAccp() {
        return accp;
    }

    public void setAccp(BigDecimal accp) {
        this.accp = accp;
    }

    public Date getSlmTm() {
        return slmTm;
    }

    public void setSlmTm(Date slmTm) {
        this.slmTm = slmTm;
    }

    public BigDecimal getSlm() {
        return slm;
    }

    public void setSlm(BigDecimal slm) {
        this.slm = slm;
    }

    public String getSlep() {
        return slep;
    }

    public void setSlep(String slep) {
        this.slep = slep;
    }
}
