package com.huitu.cloud.api.ewci.inspect.entity;

import com.huitu.cloud.validation.constraints.StringLength;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 检查上报参数
 */
@ApiModel(value = "检查上报参数")
public class BnsInspectReportQuery implements Serializable {


    @ApiModelProperty(value = "检查类型编码")
    @NotBlank(message = "参数[检查类型编码]不能为空")
    @StringLength(max = 50, message = "参数[检查类型编码]的长度不能超过50个字符")
    private String inspectCode;

    public String getInspectCode() {
        return inspectCode;
    }

    public void setInspectCode(String inspectCode) {
        this.inspectCode = inspectCode;
    }
}
