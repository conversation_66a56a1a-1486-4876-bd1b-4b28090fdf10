package com.huitu.cloud.api.common.msg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.common.msg.entity.BnsMessageR;
import com.huitu.cloud.api.common.msg.entity.FailMessage;
import com.huitu.cloud.api.common.msg.entity.MessageList;
import com.huitu.cloud.api.common.msg.entity.MessageStatistics;
import com.huitu.cloud.api.common.msg.entity.MessageVo;
import com.huitu.cloud.api.common.msg.entity.MsgBean;
import com.huitu.cloud.api.common.msg.entity.MsgSucess;
import com.huitu.cloud.api.common.msg.entity.QueryWarnMessage;
import com.huitu.cloud.api.common.msg.entity.WarnMessage;

import java.util.List;

/**
 * <p>
 * 预警消息记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public interface MessageInfoService {
    /**
     * 短信发送接口
     *
     * @param msgBean
     * @return
     */
    MsgSucess sendMsg(MsgBean msgBean);

    /**
     * 按照预警id查询短信发送记录
     *
     * @param warnId 预警编号ID
     * @param status 发送状态
     * @return
     */
    List<MessageVo> getMsgByWarnId(String warnId, String status);

    /**
     * 预警短信政区统计
     *
     * @param adcd 政区编码
     * @param etm  结束时间
     * @param stm  开始时间
     * @return
     */
    List<MessageStatistics> getMsgStatistics(String adcd, String etm, String stm);

    /**
     * 按照条件查询短信发送记录列表
     *
     * @param adcd      政区编码
     * @param stm       政区编码
     * @param etm       政区编码
     * @param mediaId   短信发送方式 10-短信； 20-传真； 30-广播
     * @param msgTypeId 短信类型编号 10-内部预警消息； 20-外部预警消息
     * @param warnId    预警编号ID
     * @param status    发送状态
     * @param msgid     短信编号
     * @param pageNum   页码
     * @param pageSize  每页个数
     * @return
     */
    IPage<MessageVo> getWarnMsgInfoByPage(String adcd, String etm, String mediaId, Integer msgTypeId, String warnId, String status, String stm, String msgid, int pageNum, int pageSize);

    /**
     * 分页获取预警短信发送记录列表
     *
     * @param query
     * @return
     **/
    IPage<WarnMessage> getWarnMessagePage(QueryWarnMessage query);

    /**
     * 按照条件查询短信信息列表
     *
     * @param adcd      政区编码
     * @param stm       开始时间
     * @param etm       结束时间
     * @param mediaId   短信发送方式 10-短信； 20-传真； 30-广播
     * @param msgTypeId 短信类型编号 10-内部预警消息； 20-外部预警消息
     * @param pageNum   页码
     * @param pageSize  每页个数
     * @return
     */
    IPage<MessageList> getWarnMsgInfoList(String adcd, String stm, String etm, String mediaId, Integer msgTypeId, int pageNum, int pageSize);

    /**
     * 获取短信信息列表中的失败短信记录
     *
     * @param adcd  政区编码
     * @param msgid 短信编号
     * @return
     */
    List<FailMessage> getFailMessage(String adcd, String msgid);

    /**
     * 按照条件查询短信发送记录列表
     *
     * @param messages 发送失败短信集合
     * @return
     */
    String updateSendResult(List<FailMessage> messages);

    int sendCommonMsg(BnsMessageR entity);

    List<BnsMessageR> getCommonMsg(String id, String type, String adcd, String deptid, String stm, String etm, String flag);

    boolean updateCommonMsgBatches(List<String> ids);
}
