package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 检查记录参数
 */
@ApiModel(value = "检查记录参数")
public class BnsInspectRecordsQuery extends PageBean implements Serializable {

    @ApiModelProperty(value = "组别id")
    private String groupid;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "年份")
    private String year;

    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("year", getYear());
        params.put("groupid", getGroupid());
        return params;
    }

    public IPage<BnsInspectRecordsChild> toPageParam() {
        return new Page<>(Math.max(getPageNum(), 1), getPageSize() > 0 ? getPageSize() : -1);
    }
}
