package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 水闸工程运行
 */
@Data
@ApiModel(value = "水闸工程运行")
public class BnsInspectWagaRun extends BnsInspectReportForm {

    @ApiModelProperty(value = "村屯政区编码")
    private String tadcd;

    @ApiModelProperty(value = "水闸名称")
    private String wagaName;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;

    @ApiModelProperty(value = "村屯政区名称")
    private String tadnm;

}
