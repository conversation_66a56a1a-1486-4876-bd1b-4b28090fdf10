<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.hydrology.mapper.HyWarnIndexDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getStationList" resultType="com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnStationResponse">
        SELECT STCD, STNM, RVNM FROM BSN_STBPRP_V A
        WHERE USFL = '1' AND STADTP = '1' AND STTP IN ('ZZ', 'ZQ')
        AND LEFT(ADCD, #{level}) = LEFT(#{adcd}, #{level})
        <if test="level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="!include">
            AND NOT EXISTS(SELECT 8 FROM HY_WARN_INDEX WHERE STCD = A.STCD)
        </if>
        ORDER BY ADCD, STCD ASC
    </select>

    <select id="getPageList" useCache="false"
            resultType="com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnIndexResponse">
        SELECT C.ADCD, C.ADNM, A.STCD, STNM, RVNM, WIDX_TYPE, WIDX_BLUE, WIDX_YELLOW, WIDX_ORANGE, WIDX_RED,
        RELEASE_UNIT, D.[TYPE] RELEASE_UNIT_TYPE, D.SHORT_NAME RELEASE_UNIT_NAME, A.USFL FROM HY_WARN_INDEX A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = LEFT(B.ADCD, 6) + '000000000'
        LEFT JOIN HY_WARN_UNIT D ON D.CODE = A.RELEASE_UNIT
        WHERE LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.unitType != null and map.unitType != ''">
            AND D.[TYPE] = #{map.unitType}
        </if>
        <if test="map.stnm != null and map.stnm != ''">
            AND CHARINDEX(#{map.stnm}, B.STNM) > 0
        </if>
        ORDER BY D.[TYPE], B.ADCD, A.STCD ASC
    </select>

    <update id="save">
        MERGE INTO HY_WARN_INDEX AS T
        USING (SELECT #{stcd}        STCD,
                      #{widxType}    WIDX_TYPE,
                      #{widxBlue}    WIDX_BLUE,
                      #{widxYellow}  WIDX_YELLOW,
                      #{widxOrange}  WIDX_ORANGE,
                      #{widxRed}     WIDX_RED,
                      #{releaseUnit} RELEASE_UNIT,
                      #{usfl}        USFL,
                      GETDATE()      TS) AS S
        ON T.STCD = S.STCD
        WHEN MATCHED THEN
            UPDATE
            SET T.WIDX_TYPE    = S.WIDX_TYPE,
                T.WIDX_BLUE    = S.WIDX_BLUE,
                T.WIDX_YELLOW  = S.WIDX_YELLOW,
                T.WIDX_ORANGE  = S.WIDX_ORANGE,
                T.WIDX_RED     = S.WIDX_RED,
                T.RELEASE_UNIT = S.RELEASE_UNIT,
                T.USFL         = S.USFL,
                T.TS           = S.TS
        WHEN NOT MATCHED THEN
            INSERT (STCD, WIDX_TYPE, WIDX_BLUE, WIDX_YELLOW, WIDX_ORANGE, WIDX_RED, RELEASE_UNIT, USFL, TS)
            VALUES (S.STCD, S.WIDX_TYPE, S.WIDX_BLUE, S.WIDX_YELLOW, S.WIDX_ORANGE, S.WIDX_RED, S.RELEASE_UNIT, S.USFL,
                    S.TS);
    </update>

    <delete id="delete">
        DELETE
        FROM HY_WARN_INDEX
        WHERE STCD = #{stcd}
    </delete>

    <update id="updateUseFlag">
        UPDATE HY_WARN_INDEX
        SET USFL = #{usfl},
            TS   = GETDATE()
        WHERE STCD = #{stcd}
    </update>
</mapper>
