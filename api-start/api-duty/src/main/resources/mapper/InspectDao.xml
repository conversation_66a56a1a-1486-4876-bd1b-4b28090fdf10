<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.duty.inspect.mapper.InspectDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <insert id="batchInsertDevice">
        INSERT INTO BNS_INSPECTDEVICE_B(DEVICEID,DEVICENM,DEVICETYPE,DEVICESTATE,INSPECTID)VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deviceid},#{item.devicenm},#{item.devicetype},#{item.devicestate},#{item.inspectid})
        </foreach>
    </insert>
    <insert id="batchInsertDeviceNew">
        INSERT INTO BNS_INSPECTDEVICE_NEW_B(deviceid, devicenm, devicestate, inspectid, devicetype, devicetm)VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deviceid},#{item.devicenm},#{item.devicestate},#{item.inspectid},#{item.devicetype},#{item.devicetm})
        </foreach>
    </insert>
    <insert id="batchInsertFile">
        INSERT INTO BNS_INSPECTFILE_B(IFID,IFNAME,IFURL,INSPECTID)VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ifid},#{item.ifname},#{item.ifurl},#{item.inspectid})
        </foreach>
    </insert>
    <insert id="insertInspect">
        INSERT INTO BNS_INSPECT_R(ID,STCD,INSPECTPERSON,INSPECTDETP,INSPECTMOBILE,INSPECTTYPE,INSPECTRESULT,INSPECTTM,RECORD)
        VALUES(#{id},#{stcd},#{inspectperson},#{inspectdetp},#{inspectmobile},#{inspecttype},#{inspectresult},#{inspecttm},#{record})
    </insert>
    <insert id="insertInspectNew">
        INSERT INTO BNS_INSPECT_NEW_R
              (ID, STCD, TM, TYPE, SAVEORREPT, INSPECTSTATUS, INSPECTPERSON, INSPECTDETP, OWNER, OWNERMAN, RTUBRAND, DTUBRAND, SIM, TERMINALBRAND, SURONDSTATUS, SURONDRELOCATE, FENCESTATUS, FENCERELOCATE, DATATEST, DATARETEST, ENDPOINTPRO, HANDLESUGGESTIONS, HANDLERESULT, ROOMENVIRONMENT, PROBLEMRECORD)
        VALUES(#{id},#{stcd},#{tm},#{type},#{saveorrept},#{inspectstatus},#{inspectperson},#{inspectdetp},#{owner},#{ownerman},#{rtubrand},#{dtubrand},#{sim},#{terminalbrand},#{surondstatus},#{surondrelocate},#{fencestatus},#{fencerelocate},#{datatest},#{dataretest},#{endpointpro},#{handlesuggestions},#{handleresult},#{roomenvironment},#{problemrecord})
    </insert>
    <update id="updateRepairInspectNew">
        update BNS_INSPECT_NEW_R set DATARETEST = '1', INSPECTSTATUS = '1'
        where id = #{inspectid}
    </update>
    <update id="updateRepairInspectdeviceNew">
        update BNS_INSPECTDEVICE_NEW_B set devicetm = GETDATE() where inspectid = #{inspectid} and devicestate = '0'
    </update>
    <select id="getStHwInfo" resultType="com.huitu.cloud.api.duty.inspect.entity.BnsHwinfo">
        SELECT STCD, RTUBD, RTUNUM, RTUCT, RAINBD, RAINNUM, RAINCT, BTTBD, BTTNUM, BTTTP, BTTCT, RVBD, RVNUM, RVCT, NT, MODITIME, CHARGECONTROLBD, CHARGECONTROLNUM, SOLARENERGYBD, SOLARENERGYNUM, RCSTBD, RCSTNUM
        FROM   BNS_HWINFO_B
        WHERE STCD=#{stcd}
    </select>
    <select id="getInspectListByStcd" resultType="com.huitu.cloud.api.duty.inspect.entity.BnsInspectR">
        SELECT ID,STCD,INSPECTPERSON,INSPECTDETP,INSPECTMOBILE,INSPECTTYPE,INSPECTRESULT,INSPECTTM,RECORD
        FROM BNS_INSPECT_R
        WHERE STCD=#{stcd}
        order by ID
    </select>
    <select id="getdeviceInfo" resultType="com.huitu.cloud.api.duty.inspect.entity.BnsInspectdeviceB">
        select DEVICEID,DEVICENM,DEVICETYPE,DEVICESTATE,INSPECTID
        from BNS_INSPECTDEVICE_B
        where INSPECTID=#{id}
    </select>
    <select id="getfileInfo" resultType="com.huitu.cloud.api.duty.inspect.entity.BnsInspectfileB">
        select IFID,IFNAME,IFURL,INSPECTID
        from BNS_INSPECTFILE_B
        where INSPECTID=#{id}
    </select>
    <select id="getInspectListById" resultType="com.huitu.cloud.api.duty.inspect.entity.BnsInspectR">
        select ID,STCD,INSPECTPERSON,INSPECTDETP,INSPECTMOBILE,INSPECTTYPE,INSPECTRESULT,INSPECTTM,RECORD
        from BNS_INSPECT_R
        where ID=#{id}
    </select>
    <select id="getInspectHisInfo" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectVo">
        select ID,STCD,INSPECTPERSON,INSPECTDETP,INSPECTMOBILE,INSPECTTYPE,INSPECTRESULT,INSPECTTM,RECORD,STNM,ADNM
        from   (select a.ID,a.STCD,a.INSPECTPERSON,a.INSPECTDETP,a.INSPECTMOBILE,a.INSPECTTYPE,a.INSPECTRESULT,a.INSPECTTM,a.RECORD,
                       B.STNM,B.ADNM,ROW_NUMBER() over(partition BY a.STCD order by INSPECTTM DESC) rn
                from BNS_INSPECT_R a LEFT JOIN BSN_STBPRP_V b on a.stcd=b.stcd
                WHERE 1=1
                <if test="map.ad != null and map.ad !=''">
                    and left(b.adcd,#{map.level})=#{map.ad}
                </if>
                <if test="map.stnm != null and map.stnm !=''">
                    AND CHARINDEX(#{map.stnm}, b.STNM)>0
                </if>
                ) c
        where c.rn=1
        order by INSPECTTM desc,ID
    </select>
    <select id="getInspectCount" resultType="java.util.Map">
        select STCD,count(0) CT
        from BNS_INSPECT_R
        group by stcd
    </select>
    <select id="getInspectRecord" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectNewVo">
        select L.stcd,L.stnm,L.adnm,L.state,S.sttp,L.lgtd,L.lttd FROM (
        SELECT
        W.stcd,
        V.stnm,
        Y.adnm,
        Y.adcd,
        MAX(V.LGTD) lgtd,
        MAX(V.LTTD) lttd,
        CASE
        WHEN SUM (
        CONVERT ( INT, W.INSPECTSTATUS )) = COUNT ( 1 ) THEN
        '1' ELSE '0'
        END AS state
        FROM
        BNS_INSPECT_NEW_R W
        LEFT JOIN BSN_STBPRP_V V ON W.STCD = V.STCD
        LEFT JOIN bsn_adcd_b Y ON V.ADCD = Y.adcd
        WHERE 1=1
        <if test="map.stm != null and map.stm != ''">
            AND W.tm >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm != ''">
            AND W.tm &lt;= #{map.etm}
        </if>
        <if test="map.ad != null and map.ad !=''">
            and left(V.adcd,#{map.level})=#{map.ad}
        </if>
        <if test='map.type != null and map.type !="" and map.type =="1" '>
            and (V.STTP='PP')
        </if>
        <if test='map.type != null and map.type !="" and map.type =="2" '>
            and (V.STTP='RR' or V.STTP='ZZ')
        </if>
        <if test='map.type != null and map.type !="" and map.type =="3" '>
            and (V.STTP='RQ' or V.STTP='ZQ')
        </if>
        GROUP BY
        W.stcd,
        V.stnm,
        Y.adnm,
        Y.adcd
        ) L left join BSN_STBPRP_V S on S.stcd = L.stcd  WHERE L.stnm IS NOT NULL
        <if test='map.state != null and map.state !=""'>
            and L.state = #{map.state}
        </if>
         order by L.adcd,L.stcd
    </select>
    <select id="getInspectNoRecord" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectNewVo">
       SELECT P.stcd,P.stnm,P.adnm,P.state,S.sttp,P.lgtd,P.lttd FROM
        (SELECT
        A.stcd,
        A.stnm,
        A.sttp,
        A.ADCD,
        B.adnm,
        A.LGTD lgtd,
        A.LTTD lttd,
        '-' AS state
        FROM
        BSN_STBPRP_V A
        LEFT JOIN bsn_adcd_b B ON A.ADCD = b.adcd
        WHERE
        A.sttp IN ( 'PP', 'ZZ', 'RR', 'RQ', 'ZQ' )
        AND a.stcd NOT IN ( SELECT stcd FROM BNS_INSPECT_NEW_R x
        WHERE 1=1
        <if test="map.stm != null and map.stm != ''">
            AND x.tm >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm != ''">
            AND x.tm &lt;= #{map.etm}
        </if>
        )
        <if test="map.ad != null and map.ad !=''">
            and left(A.adcd,#{map.level})=#{map.ad}
        </if>
        ) P left join BSN_STBPRP_V S on s.stcd = P.stcd  WHERE 1=1
        <if test='map.type != null and map.type !="" and map.type =="1" '>
            and (P.STTP='PP')
        </if>
        <if test='map.type != null and map.type !="" and map.type =="2" '>
            and (P.STTP='RR' or P.STTP='ZZ')
        </if>
        <if test='map.type != null and map.type !="" and map.type =="3" '>
            and (P.STTP='RQ' or P.STTP='ZQ')
        </if>
          order by P.ADCD,P.stcd
    </select>
    <select id="getInspectRecord1" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectNewVo">
        select a.* from (
        <if test='map.inspect != null and (map.inspect.contains("2") or map.inspect.contains("3"))'>
            select L.adcd,L.stcd,L.stnm,L.adnm,L.state,S.sttp,L.lgtd,L.lttd FROM (
            SELECT
            W.stcd,
            V.stnm,
            Y.adnm,
            Y.adcd,
            MAX(T.PLGTD) lgtd,
            MAX(T.PLTTD) lttd,
            CASE
            WHEN SUM (
            CONVERT ( INT, W.INSPECTSTATUS )) = COUNT ( 1 ) THEN
            '1' ELSE '0'
            END AS state
            FROM
            BSN_STBPRP_V V
            LEFT JOIN BNS_INSPECT_NEW_R W ON W.STCD = V.STCD
            LEFT JOIN bsn_adcd_b Y ON V.ADCD = Y.adcd
            LEFT JOIN BSN_STADTP_B T ON V.STCD = T.STCD
            WHERE V.FRGRD=5
            <if test="map.stm != null and map.stm != ''">
                AND W.tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND W.tm &lt;= #{map.etm}
            </if>
            <if test="map.ad != null and map.ad !=''">
                and left(V.adcd,#{map.level})=#{map.ad}
            </if>
            <!--<if test='map.type != null and map.type !="" and map.type =="1" '>
                and (V.STTP='PP')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="2" '>
                and (V.STTP='RR' or V.STTP='ZZ')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="3" '>
                and (V.STTP='RQ' or V.STTP='ZQ')
            </if>-->
            <if test='map.type != null and map.type !="" and map.type =="2" '>
                and (V.STTP='PP')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="3" '>
                and (V.STTP='ZZ')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="4" '>
                and (V.STTP='RR')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="5" '>
                and (V.STTP='ZQ')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="6" '>
                and (V.STTP='RQ')
            </if>
            GROUP BY
            W.stcd,
            V.stnm,
            Y.adnm,
            Y.adcd
            ) L left join BSN_STBPRP_V S on S.stcd = L.stcd  WHERE L.stnm IS NOT NULL
            <if test='map.state != null and map.state !=""'>
                and L.state = #{map.state}
            </if>
            </if>
        <if test='map.inspect != null and map.inspect.contains("0") and (map.inspect.contains("2") or map.inspect.contains("3"))'>
            union
        </if>
        <if test='map.inspect != null and map.inspect.contains("0")'>
        SELECT P.ADCD adcd,P.stcd,P.stnm,P.adnm,P.state,S.sttp,S.PLGTD lgtd,S.PLTTD lttd FROM
            (SELECT
            A.stcd,
            A.stnm,
            A.sttp,
            A.ADCD,
            B.adnm,
            A.LGTD lgtd,
            A.LTTD lttd,
            '-' AS state
            FROM
            BSN_STBPRP_V A
            LEFT JOIN bsn_adcd_b B ON A.ADCD = b.adcd
            WHERE A.FRGRD=5 AND
            A.sttp IN ( 'PP', 'ZZ', 'RR', 'RQ', 'ZQ' )
            AND a.stcd NOT IN ( SELECT stcd FROM BNS_INSPECT_NEW_R x
            WHERE 1=1
            <if test="map.stm != null and map.stm != ''">
                AND x.tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND x.tm &lt;= #{map.etm}
            </if>
            )
            <if test="map.ad != null and map.ad !=''">
                and left(A.adcd,#{map.level})=#{map.ad}
            </if>
            ) P left join BSN_STBPRP_V S on s.stcd = P.stcd  WHERE 1=1
            <if test='map.type != null and map.type !="" and map.type =="2" '>
                and (P.STTP='PP')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="3" '>
                and (P.STTP='ZZ')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="4" '>
                and (P.STTP='RR')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="5" '>
                and (P.STTP='ZQ')
            </if>
            <if test='map.type != null and map.type !="" and map.type =="6" '>
                and (P.STTP='RQ')
            </if>
<!--            <if test='map.type != null and map.type !="" and map.type =="1" '>-->
<!--                and (P.STTP='PP')-->
<!--            </if>-->
<!--            <if test='map.type != null and map.type !="" and map.type =="2" '>-->
<!--                and (P.STTP='RR' or P.STTP='ZZ')-->
<!--            </if>-->
<!--            <if test='map.type != null and map.type !="" and map.type =="3" '>-->
<!--                and (P.STTP='RQ' or P.STTP='ZQ')-->
<!--            </if>-->
        </if>
        ) a
        order by state desc,adcd,stcd
    </select>
    <select id="getInspectNewCount" resultType="java.util.Map">
        select STCD,count(0) CT
        from BNS_INSPECT_R
        group by stcd
    </select>
    <select id="getInspectSummary1" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectSummary">
        select adcd, (select adnm from BSN_ADCD_B where adcd = tt.adcd) adnm,
        (
        select COUNT(1) from BSN_STBPRP_V a where left(a.adcd,2) = left(tt.adcd,2)
        ) as count,
        (
        select count(*) from BSN_STBPRP_V a  left join ( select * from BNS_INSPECT_NEW_R
        <where>
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) b on a.STCD = b.STCD
        where left(a.adcd,2)=left(tt.adcd,2) and b.stcd is NULL
        )as inspect0,
        (
        select count(1) from (
        select stcd, stnm, adnm from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,2) = left(tt.adcd,2)
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d
        ) as inspect1,
        (
        select count(state) from (
        select stcd, stnm, adnm,
        case when SUM(convert(int,INSPECTSTATUS)) = COUNT(INSPECTSTATUS) then 1
        else 0 end state
        from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm, INSPECTSTATUS from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,2) = left(tt.adcd,2)
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d where state > 0
        ) as normal,
        (
        select count(state) from (
        select stcd, stnm, adnm,
        case when SUM(convert(int,INSPECTSTATUS)) = COUNT(INSPECTSTATUS) then 1
        else 0 end state
        from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm, INSPECTSTATUS from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,2) = left(tt.adcd,2)
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d where state = 0
        ) as abnormal
        from
        (
        select ad + LEFT('000000000000000', LEN('000000000000000') - LEN(ad) ) adcd, count(ad) cc from
        (
        select adcd, adnm, left(a.ADCD, 2) ad from BSN_STBPRP_V a
        where left(a.ADCD, 2) = '22'
        ) a group by ad
        ) tt order by adcd
    </select>
    <select id="getInspectSummary2" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectSummary">
        select adcd, (select adnm from BSN_ADCD_B where adcd = tt.adcd) adnm,
        (
        select COUNT(1) from BSN_STBPRP_V a where left(a.adcd,4) = left(tt.adcd,4)
        ) as count,
        (
        select count(*) from BSN_STBPRP_V a  left join ( select * from BNS_INSPECT_NEW_R
        <where>
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) b on a.STCD = b.STCD
        where left(a.adcd,4)=left(tt.adcd,4) and b.stcd is NULL
        )as inspect0,
        (
        select count(1) from (
        select stcd, stnm, adnm from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,4) = left(tt.adcd,4)
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d
        ) as inspect1,
        (
        select count(state) from (
        select stcd, stnm, adnm,
        case when SUM(convert(int,INSPECTSTATUS)) = COUNT(INSPECTSTATUS) then 1
        else 0 end state
        from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm, INSPECTSTATUS from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,4) = left(tt.adcd,4)
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d where state > 0
        ) as normal,
        (
        select count(state) from (
        select stcd, stnm, adnm,
        case when SUM(convert(int,INSPECTSTATUS)) = COUNT(INSPECTSTATUS) then 1
        else 0 end state
        from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm, INSPECTSTATUS from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,4) = left(tt.adcd,4)
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d where state = 0
        ) as abnormal
        from
        (
        select ad + LEFT('000000000000000', LEN('000000000000000') - LEN(ad) ) adcd, count(ad) cc from
        (
        select adcd, adnm, left(a.ADCD, 4) ad from BSN_STBPRP_V a
        where left(a.ADCD, 2) = '22'
        ) a group by ad
        ) tt order by adcd
    </select>
    <select id="getInspectSummary3" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectSummary">
        select adcd, (select adnm from BSN_ADCD_B where adcd = tt.adcd) adnm,
        (
        select COUNT(1) from BSN_STBPRP_V a where left(a.adcd,6) = left(tt.adcd,6)
        ) as count,
        (
        select count(*) from BSN_STBPRP_V a  left join ( select * from BNS_INSPECT_NEW_R
        <where>
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) b on a.STCD = b.STCD
        where left(a.adcd,6)=left(tt.adcd,6) and b.stcd is NULL
        )as inspect0,
        (
        select count(1) from (
        select stcd, stnm, adnm from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,6) = left(tt.adcd,6)
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d
        ) as inspect1,
        (
        select count(state) from (
        select stcd, stnm, adnm,
        case when SUM(convert(int,INSPECTSTATUS)) = COUNT(INSPECTSTATUS) then 1
        else 0 end state
        from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm, INSPECTSTATUS from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,6) = left(tt.adcd,6)
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d where state > 0
        ) as normal,
        (
        select count(state) from (
        select stcd, stnm, adnm,
        case when SUM(convert(int,INSPECTSTATUS)) = COUNT(INSPECTSTATUS) then 1
        else 0 end state
        from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm, INSPECTSTATUS from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,6) = left(tt.adcd,6)
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d where state = 0
        ) as abnormal
        from
        (
        select ad + LEFT('000000000000000', LEN('000000000000000') - LEN(ad) ) adcd, count(ad) cc from
        (
        select adcd, adnm, left(a.ADCD, 6) ad from BSN_STBPRP_V a
        where left(a.adcd,#{map.level})=#{map.ad}
        ) a group by ad
        ) tt order by adcd
    </select>

    <select id="getInspectSummary" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectSummary">
        select adcd, (select adnm from BSN_ADCD_B where adcd = tt.adcd) adnm,
        (
        select COUNT(1) from BSN_STBPRP_V a where left(a.adcd,#{map.level2}) = left(tt.adcd,#{map.level2})
        ) as count,
        (
        select count(*) from BSN_STBPRP_V a  left join ( select * from BNS_INSPECT_NEW_R
        <where>
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) b on a.STCD = b.STCD
        where left(a.adcd,#{map.level2})=left(tt.adcd,#{map.level2}) and b.stcd is NULL
        )as inspect0,
        (
        select count(1) from (
        select stcd, stnm, adnm from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,#{map.level2}) = left(tt.adcd,#{map.level2})
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d
        ) as inspect1,
        (
        select count(state) from (
        select stcd, stnm, adnm,
        case when SUM(convert(int,INSPECTSTATUS)) = COUNT(INSPECTSTATUS) then 1
        else 0 end state
        from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm, INSPECTSTATUS from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,#{map.level2}) = left(tt.adcd,#{map.level2})
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d where state > 0
        ) as normal,
        (
        select count(state) from (
        select stcd, stnm, adnm,
        case when SUM(convert(int,INSPECTSTATUS)) = COUNT(INSPECTSTATUS) then 1
        else 0 end state
        from (
        select a.STCD stcd, a.stnm stnm,a.adnm adnm, INSPECTSTATUS from BSN_STBPRP_V a
        left join BNS_INSPECT_NEW_R b on a.STCD = b.STCD
        <where>
            left(a.adcd,#{map.level2}) = left(tt.adcd,#{map.level2})
            <if test="map.stm != null and map.stm != ''">
                AND tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND tm &lt;= #{map.etm}
            </if>
        </where>
        ) a
        group by stcd, stnm, adnm
        ) d where state = 0
        ) as abnormal
        from
        (
        select ad + LEFT('000000000000000', LEN('000000000000000') - LEN(ad) ) adcd, count(ad) cc from
        (
        select adcd, adnm, left(a.ADCD, #{map.level2}) ad from BSN_STBPRP_V a
        where left(a.adcd,#{map.level})=#{map.ad}
        ) a group by ad
        ) tt order by adcd
    </select>
    <select id="checkInspectErrorRecord" resultType="com.huitu.cloud.api.duty.inspect.entity.BnsInspectNewR">
        select * from BNS_INSPECT_NEW_R
        <where>
            INSPECTSTATUS = '0'
            <if test="stm != null and stm != ''">
                AND tm >= #{stm}
            </if>
            <if test="etm != null and etm != ''">
                AND tm &lt;= #{etm}
            </if>
            <if test="stcd != null and stcd != ''">
                AND stcd = #{stcd}
            </if>
        </where>
        order by tm desc
    </select>
    <select id="getInspectByStcdTm" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectRecordInfo">
        select b.ADCD,b.ADNM,b.STNM,b.STLC,b.LGTD,b.LTTD, a.ID, a.STCD, a.TM, a.TYPE, a.SAVEORREPT, a.INSPECTSTATUS, a.INSPECTPERSON, a.INSPECTDETP, a.OWNER, a.OWNERMAN, a.RTUBRAND, a.DTUBRAND, a.SIM, a.TERMINALBRAND, a.SURONDSTATUS, a.SURONDRELOCATE, a.FENCESTATUS, a.FENCERELOCATE, a.DATATEST, a.DATARETEST, a.ENDPOINTPRO, a.HANDLESUGGESTIONS, a.HANDLERESULT, a.ROOMENVIRONMENT, a.PROBLEMRECORD
        from BNS_INSPECT_NEW_R a
        left join BSN_STBPRP_V b on a.STCD = b.STCD
        <where>
            <if test="inspectid !=null and inspectid !=''">
                and id = #{inspectid}
            </if>
        </where>
    </select>
    <!-- and CONVERT (smalldatetime,tm)&lt;= CONVERT(smalldatetime,#{tm}) -->
    <select id="getInspectRecordForServer" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectNewVo">
        select a.id, b.STNM, a.type, b.ADNM + c.adnm adnm, a.owner, a.tm from BNS_INSPECT_NEW_R a
        left join BSN_STBPRP_V b on a.STCD = b.STCD
        left join BSN_ADCD_B c on c.adcd = b.XPADCD
        <where>
            <if test="map.ad != null and map.ad !=''">
                and left(b.adcd,#{map.level})=#{map.ad}
            </if>
            <if test="map.stnm != null and map.stnm !=''">
                AND CHARINDEX(#{map.stnm}, b.STNM)>0
            </if>
            <if test="map.type != null and map.type !=''">
                AND a.type = #{map.type}
            </if>
            <if test="map.stm != null and map.stm != ''">
                AND a.tm >= #{map.stm}
            </if>
            <if test="map.etm != null and map.etm != ''">
                AND a.tm &lt;= #{map.etm}
            </if>
        </where>
        order by b.stcd
    </select>
    <select id="getInspectdeviceNewByInspID" resultType="com.huitu.cloud.api.duty.inspect.entity.BnsInspectdeviceNewB">
        select deviceid, devicenm, devicestate, inspectid, devicetype, devicetm from BNS_INSPECTDEVICE_NEW_B
        where inspectid = #{inspectid}
    </select>
    <select id="getInspectRecordByStcd" resultType="com.huitu.cloud.api.duty.inspect.entity.InspectRecordInfo">
        select b.ADCD,b.ADNM,b.STNM,b.STLC,f.PLGTD as LGTD,f.PLTTD as LTTD,
        a.* from BNS_INSPECT_NEW_R a
        left join BSN_STBPRP_V b on a.STCD = b.STCD
        left join BSN_STADTP_B F on a.STCD = F.STCD
        <where>
            <if test="stm != null and stm != ''">
                AND a.tm >= #{stm}
            </if>
            <if test="etm != null and etm != ''">
                AND a.tm &lt;= #{etm}
            </if>
            <if test="stcd != null and stcd != ''">
                AND a.stcd = #{stcd}
            </if>
        </where>
        order by a.tm desc
    </select>

    <select id="getStNumByAdcd" resultType="java.lang.String">
        select count(1) as num from BSN_STBPRP_V where 1=1 AND FRGRD=5 and sttp IN ( 'PP', 'ZZ', 'RR', 'RQ', 'ZQ' )
        <if test="ad != null and ad !=''">
            and left(adcd,#{level})=#{ad}
        </if>
    </select>

    <select id="getAlreadyStnum" resultType="java.lang.String">
        SELECT COUNT
        ( DISTINCT a.STCD )
        FROM
        BNS_INSPECT_NEW_R a
        LEFT JOIN BSN_STBPRP_V b ON a.stcd = b.stcd
        WHERE
        b.FRGRD=5
        <if test="year != null and year !=''">
            and  datepart(yy,a.TM)=#{year}
        </if>
        <if test="ad != null and ad !=''">
            and left(b.adcd,#{level})=#{ad}
        </if>
        <if test="stm != null and stm != ''">
            AND a.tm >= #{stm}
        </if>
        <if test="etm != null and etm != ''">
            AND a.tm &lt;= #{etm}
        </if>
    </select>

    <select id="getAbnormalStnum" resultType="java.lang.String">
        select count(1) FROM (
        SELECT
        W.stcd,
        V.stnm,
        Y.adnm,
        CASE
        WHEN SUM (
        CONVERT ( INT, W.INSPECTSTATUS )) = COUNT ( 1 ) THEN
        '1' ELSE '0'
        END AS state
        FROM
        BNS_INSPECT_NEW_R W
        LEFT JOIN BSN_STBPRP_V V ON W.STCD = V.STCD
        LEFT JOIN bsn_adcd_b Y ON V.ADCD = Y.adcd
        WHERE 1=1 and V.STTP in ( 'PP', 'ZZ', 'RR', 'RQ', 'ZQ' )
        <if test="stm != null and stm != ''">
            AND W.tm >= #{stm}
        </if>
        <if test="etm != null and etm != ''">
            AND W.tm &lt;= #{etm}
        </if>
        <if test="year != null and year !=''">
            and  datepart(yy,W.TM)=#{year}
        </if>
        <if test="ad != null and ad !=''">
            and left(V.adcd,#{level})=#{ad}
        </if>
        GROUP BY
        W.stcd,
        V.stnm,
        Y.adnm
        ) L left join BSN_STBPRP_V S on S.stcd = L.stcd  WHERE 1=1
            and L.state = '0' AND S.FRGRD=5
    </select>
</mapper>
