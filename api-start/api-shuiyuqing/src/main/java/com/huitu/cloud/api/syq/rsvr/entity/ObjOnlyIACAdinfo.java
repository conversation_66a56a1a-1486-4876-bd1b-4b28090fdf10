package com.huitu.cloud.api.syq.rsvr.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021-06-29
 */
@ApiModel(value="水库测站对应小水库风险图和调查评价", description="小水库风险图和调查评价表")
public class ObjOnlyIACAdinfo implements Serializable {

    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "防汛检查对象")
    private String objid;

    @ApiModelProperty(value = "防治区名称")
    private String objnms;

    @ApiModelProperty(value = "总人口数")
    private BigDecimal ptcounts;

    @ApiModelProperty(value = "总户数")
    private BigDecimal etcounts;

    @ApiModelProperty(value = "总房屋数")
    private BigDecimal htcounts;

    @ApiModelProperty(value = "土地面积")
    private BigDecimal ldareas;

    @ApiModelProperty(value = "耕地面积")
    private BigDecimal plareas;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getObjid() {
        return objid;
    }

    public void setObjid(String objid) {
        this.objid = objid;
    }

    public String getObjnms() {
        return objnms;
    }

    public void setObjnms(String objnms) {
        this.objnms = objnms;
    }

    public BigDecimal getPtcounts() {
        return ptcounts;
    }

    public void setPtcounts(BigDecimal ptcounts) {
        this.ptcounts = ptcounts;
    }

    public BigDecimal getEtcounts() {
        return etcounts;
    }

    public void setEtcounts(BigDecimal etcounts) {
        this.etcounts = etcounts;
    }

    public BigDecimal getHtcounts() {
        return htcounts;
    }

    public void setHtcounts(BigDecimal htcounts) {
        this.htcounts = htcounts;
    }

    public BigDecimal getLdareas() {
        return ldareas;
    }

    public void setLdareas(BigDecimal ldareas) {
        this.ldareas = ldareas;
    }

    public BigDecimal getPlareas() {
        return plareas;
    }

    public void setPlareas(BigDecimal plareas) {
        this.plareas = plareas;
    }
}
