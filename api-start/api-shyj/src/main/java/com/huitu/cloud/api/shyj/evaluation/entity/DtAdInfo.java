package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 政区概况统计
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-15
 */
@ApiModel(value="DtAdInfo对象", description="政区概况统计")
public class DtAdInfo extends DtExtendInfo{
    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = " 人口 ")
    private Double ptcount;

    @ApiModelProperty(value = "户数 ")
    private Double htcount;

    @ApiModelProperty(value = "土地面积")
    private Double ldarea;

    @ApiModelProperty(value = "耕地面积")
    private Double plarea;

    @ApiModelProperty(value = "防治区个数")
    private Double prevcnt;

    @ApiModelProperty(value = " 重点防治区个数")
    private Double imppevcnt;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getPtcount() {
        return ptcount;
    }

    public void setPtcount(Double ptcount) {
        this.ptcount = ptcount;
    }

    public Double getHtcount() {
        return htcount;
    }

    public void setHtcount(Double htcount) {
        this.htcount = htcount;
    }

    public Double getLdarea() {
        return ldarea;
    }

    public void setLdarea(Double ldarea) {
        this.ldarea = ldarea;
    }

    public Double getPlarea() {
        return plarea;
    }

    public void setPlarea(Double plarea) {
        this.plarea = plarea;
    }

    public Double getPrevcnt() {
        return prevcnt;
    }

    public void setPrevcnt(Double prevcnt) {
        this.prevcnt = prevcnt;
    }

    public Double getImppevcnt() {
        return imppevcnt;
    }

    public void setImppevcnt(Double imppevcnt) {
        this.imppevcnt = imppevcnt;
    }
}
