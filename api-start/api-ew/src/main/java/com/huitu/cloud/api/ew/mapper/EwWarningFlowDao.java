package com.huitu.cloud.api.ew.mapper;

import com.huitu.cloud.api.ew.entity.response.EwWarningFlowResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwWarningFlowDao {

    /**
     * 根据预警ID，获取预警流程列表
     *
     * @param warnId 预警ID
     * @return 流程列表
     */
    List<EwWarningFlowResponse> getFlowList(String warnId);

    /**
     * 插入新流程节点
     *
     * @param params 参数
     * @return 受影响的行数
     */
    int insert(@Param("map") Map<String, Object> params);

    /**
     * 插批量入新流程节点
     *
     * @param params 参数
     * @return 受影响的行数
     */
    int batchInsert(@Param("map") Map<String, Object> params);

    /**
     * 人工确认
     *
     * @param confirmBy 创建人
     * @param warnIds   预警ID集合
     * @return 受影响的行数
     */
    int manualConfirm(@Param("confirmBy") String confirmBy, @Param("warnIds") List<String> warnIds);
}
