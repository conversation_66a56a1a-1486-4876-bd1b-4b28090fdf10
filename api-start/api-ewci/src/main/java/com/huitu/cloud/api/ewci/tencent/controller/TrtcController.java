package com.huitu.cloud.api.ewci.tencent.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.ewci.tencent.entity.VideoConference;
import com.huitu.cloud.api.ewci.tencent.entity.VideoPersonRecord;
import com.huitu.cloud.api.ewci.tencent.service.VideoConferenceService;
import com.huitu.cloud.api.ewci.tencent.service.VideoPersonRecordService;
import com.huitu.cloud.api.ewci.tencent.util.TLSSigAPIv2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "腾讯云服务")
@RequestMapping("/api/saas/unsafe/v1")
public class TrtcController extends AbstractApiResource implements ApiResource {

    @Autowired
    private VideoConferenceService videoConferenceService;

    @Autowired
    private VideoPersonRecordService videoPersonRecordService;

    @Value("${tencentyun.trtc.sdkappid:1600057805}")
    private Long SDKAPPID;

    @Value("${tencentyun.trtc.sdkappsecretkey:24317168be546e92162618046424aa81800efe8373f9b76dadc05eaa589501eb}")
    private String SECRETKEY;

    @Value("${tencentyun.trtc.expiretime:604800}")
    private long EXPIRETIME;

    @Value("${tencentyun.trtc.callbacksecret:htkf123456}")
    private String secret;

    @ApiOperation(value = "获取腾讯音视频userSig")
    @GetMapping("/getUserSig")
    public String getUserSig(String userId) {
        TLSSigAPIv2 tLSSigAPIv2 = new TLSSigAPIv2(SDKAPPID, SECRETKEY);
        return tLSSigAPIv2.genUserSig(userId, EXPIRETIME);
    }

    @ApiOperation(value = "音视频视频房间回调")
    @PostMapping("/callback")
    public Map<String, Object> callback(HttpServletRequest request, @RequestHeader Map<String, String> headers, @RequestBody String body) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        String sign = request.getParameter("Sign");
        String requestTime = request.getParameter("RequestTime");
        String originalString = secret + requestTime;
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] encodedhash = digest.digest(originalString.getBytes(StandardCharsets.UTF_8));

        if (sign.equals(bytesToHex(encodedhash))) {
            JSONObject jsonObject = JSON.parseObject(body);
            String callbackCommand = jsonObject.getString("CallbackCommand");
            long eventMsTs = jsonObject.getLong("EventTime");
            System.out.println(jsonObject.toJSONString());
            VideoConference videoConference = new VideoConference();
            VideoPersonRecord videoPersonRecord = new VideoPersonRecord();
            if ("Room.CallbackAfterCreateRoom".equals(callbackCommand)) {
                String roomInfo = jsonObject.getString("RoomInfo"); // 事件信息
                JSONObject jsonObject1 = JSON.parseObject(roomInfo);
                String roomId = jsonObject1.getString("RoomId"); // 房间号
                String userId = jsonObject1.getString("Owner_Account"); // 用户ID
                VideoConference conference = videoConferenceService.getById(roomId);
                if (conference == null) {
                    videoPersonRecord.setRoomId(roomId);
                    videoConference.setRoomId(roomId);
                    videoConference.setStartTime(new Date(eventMsTs).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                    videoConference.setCreateId(userId);
                    videoConference.setStatus(1);
                    videoConferenceService.updateVideoConference(videoConference);
                    System.out.println("创建房间===>" + videoConference.toString());
                } else {
                    conference.setStatus(1);
                    videoConferenceService.updateVideoConference(videoConference);

                }
            } else if ("Room.CallbackAfterDestroyRoom".equals(callbackCommand)) {
                String roomId = jsonObject.getString("RoomId");
                videoConference.setRoomId(roomId);
                videoConference.setEndTime(new Date(eventMsTs).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                videoConference.setStatus(2);
                videoConferenceService.updateVideoConference(videoConference);
                videoPersonRecord.setRoomId(roomId);
                videoPersonRecordService.updateRecordForDestroyRoom(videoPersonRecord);
                System.out.println("解散房间===>" + videoConference.toString());
            } else if ("Room.CallbackAfterMemberEnter".equals(callbackCommand)) {
                String roomId = jsonObject.getString("RoomId");
                String userId = jsonObject.getString("Operator_Account");
                videoPersonRecord.setRoomId(roomId);
                videoPersonRecord.setSign(0);
                videoPersonRecord.setMemberId(userId);
                videoPersonRecord.setEnterTime(new Date(eventMsTs).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                videoPersonRecordService.searchAndInsertRecord(videoPersonRecord);
                System.out.println("进入房间===>" + videoPersonRecord.toString());
            } else if ("Room.CallbackAfterMemberLeave".equals(callbackCommand)) {
                String roomId = jsonObject.getString("RoomId");
                String userId = jsonObject.getString("Operator_Account");
                videoPersonRecord.setRoomId(roomId);
                videoPersonRecord.setSign(1);
                videoPersonRecord.setMemberId(userId);
                videoPersonRecord.setLeaveTime(new Date(eventMsTs).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                videoPersonRecordService.updateRecordForMemberLeave(videoPersonRecord);
                System.out.println("退出房间===>" + videoPersonRecord.toString());
            }
        }
        return null;
    }

    private String bytesToHex(byte[] hash) {
        StringBuffer hexString = new StringBuffer();
        for (int i = 0; i < hash.length; i++) {
            String hex = Integer.toHexString(0xff & hash[i]);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

    @Override
    public String getUuid() {
        return "********-0000-0000-0000-********0000";
    }


    @Override
    public String getVersion() {
        return "1.0";
    }

}
