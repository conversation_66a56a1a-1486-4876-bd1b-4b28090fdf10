package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.api.ew.entity.base.EwWarningRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 山洪预警更正响应对象
 */
@ApiModel(value = "山洪预警更正响应对象")
public class EwWarningCorrectResponse extends EwWarningRecord {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;
    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;
    @ApiModelProperty(value = "测站所属政区")
    @TableField(value = "STADNM")
    private String stadnm;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getStadnm() {
        return stadnm;
    }

    public void setStadnm(String stadnm) {
        this.stadnm = stadnm;
    }
}
