package com.huitu.cloud.api.datamanager.rule.entity;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;


public class RainruleModel {
    @ColumnWidth(15)
    @ExcelProperty("测站编码")
    private String stcd;
    @ColumnWidth(20)
    @ExcelProperty("测站名称")
    private String stnm;
    @ExcelProperty(value = "分钟数")
    private Integer mncnt;

    @ColumnWidth(30)
    @ExcelProperty(value = "分钟数据量阈值")
    private Integer ctpm;

    @ColumnWidth(15)
    @ExcelProperty(value = "雨量阀值")
    private Double snv;
    @ExcelProperty(value = "备注")
    private String nt;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public Integer getMncnt() {
        return mncnt;
    }

    public void setMncnt(Integer mncnt) {
        this.mncnt = mncnt;
    }

    public Integer getCtpm() {
        return ctpm;
    }

    public void setCtpm(Integer ctpm) {
        this.ctpm = ctpm;
    }

    public Double getSnv() {
        return snv;
    }

    public void setSnv(Double snv) {
        this.snv = snv;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }
}
