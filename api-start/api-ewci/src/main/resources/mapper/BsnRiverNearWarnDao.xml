<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.near.mapper.BsnRiverNearWarnDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getUnreadMessageList" resultType="com.huitu.cloud.api.ewci.near.entity.response.BsnRiverNearWarnMResponse">
        SELECT STCD, STNM, RVNM, TM, Z, DWRZ, DGRZ, CHV, GRADE, ECHV FROM (
        SELECT STCD, STNM, RVNM, ADCD, TM, Z, DWRZ, DGRZ, CHV, GRADE, ECHV,
        (CASE WHEN TTM >= LTTM THEN LFTM ELSE DATEADD(DD, -1, LFTM) END) LBTM,
        (CASE WHEN TTM &lt; LTTM THEN LFTM ELSE DATEADD(DD, 1, LFTM) END) LETM
        FROM (
        SELECT (ROW_NUMBER() OVER(PARTITION BY A.STCD ORDER BY TM DESC)) RNO, A.STCD, STNM, RVNM, ADCD, TM, Z, DWRZ, DGRZ, CHV,
        (CASE WHEN ENGRZ >= 0 THEN 1 WHEN ENWRZ >=0 THEN 2 ELSE 9 END) GRADE, (CASE WHEN ECHV >= 0 THEN 1 ELSE 0 END) ECHV,
        CAST(TM AS TIME) TTM, CAST('08:00:00' AS TIME) LTTM, CAST(FORMAT(TM, 'yyyy-MM-dd 08:00:00') AS DATETIME) LFTM
        FROM BSN_RIVERNEARWARN_R A
        LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        WHERE (TM BETWEEN DATEADD(HH, -#{map.hours}, GETDATE()) AND GETDATE())
        AND ( (LEFT(C.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">AND LEFT(C.ADCD, 6) NOT IN ('220581')</if>
        ) OR EXISTS(SELECT 8 FROM BSN_RIVER_SCOPE WHERE ADCD = #{map.adcd} AND STCD = A.STCD))
        ) X WHERE RNO = 1) Y
        WHERE NOT EXISTS(SELECT 8 FROM BSN_RIVERNEARWARN_M WHERE USERID = #{map.userId} AND STCD = Y.STCD
        AND (GRADE &lt;= Y.GRADE OR ECHV = Y.ECHV) AND TM >= Y.LBTM AND TM &lt; Y.LETM)
        ORDER BY TM DESC, ADCD, STCD ASC
    </select>
    <insert id="setMessageReaded">
        MERGE INTO BSN_RIVERNEARWARN_M AS T
        USING (SELECT USERID, STCD, TM, GRADE, ECHV FROM (VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.stcd}, #{item.tm}, #{item.grade}, #{item.echv})
        </foreach>) AS A(USERID, STCD, TM, GRADE, ECHV)) S
        ON T.USERID = S.USERID AND T.STCD = S.STCD AND T.TM = S.TM
        WHEN NOT MATCHED THEN
        INSERT (USERID, STCD, TM, GRADE, ECHV, LATEST_TIME) VALUES (S.USERID, S.STCD, S.TM, S.GRADE, S.ECHV, GETDATE());
    </insert>
</mapper>
