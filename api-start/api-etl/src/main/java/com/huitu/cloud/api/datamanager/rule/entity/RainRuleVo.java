package com.huitu.cloud.api.datamanager.rule.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "RainRuleVo",description = "雨量规则查询结果类")
public class RainRuleVo extends BsnRainruleb {
    @ApiModelProperty(value = "测站名称")
    private String stnm;

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }
}
