<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ia.mapper.BnsIaDandfobjDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DandfobjInfo">
        SELECT B.ID, A.<PERSON>, A.[NAME], A.ADCD, dbo.fnGetFullAdnm(A.ADCD, #{map.adnmlvl}) ADNM, A.WSCD,
        STUFF((SELECT '、' + WSNM FROM IA_C_WATA WHERE WSCD IN (SELECT FIELD FROM dbo.fnSplitString(A.WSCD, ',')) FOR XML
        PATH('')), 1, 1, '') WSNM, B.RISKTP, B.RISKLVL, B.FCA, B.ETCOUNT, B.PTCOUNT, B.SPCOUNT, B.REALNM, B.MOBILE,
        B.ESNM, B.ESPCOUNT, B.[STATE], B.TS
        FROM IA_C_DANAD A
        LEFT JOIN BNS_IA_DANDFOBJ B ON B.DAND = A.DAND AND B.[STATE] = '1'
        WHERE LEFT(A.ADCD, #{map.adcdlvl}) = LEFT(#{map.adcd}, #{map.adcdlvl})
        <if test="map.adcdlvl == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.name != null and map.name != ''">
            AND CHARINDEX(#{map.name}, A.[NAME]) > 0
        </if>
        <if test="map.realnm != null and map.realnm != ''">
            AND CHARINDEX(#{map.realnm}, B.REALNM) > 0
        </if>
        <if test="map.risktp != null and map.risktp != ''">
            AND CHARINDEX(#{map.risktp}, B.RISKTP) > 0
        </if>
        <if test="map.risklvl != null and map.risklvl != ''">
            AND CHARINDEX(#{map.risklvl}, B.RISKLVL) > 0
        </if>
        ORDER BY A.ADCD ASC, B.RISKLVL DESC
    </select>
    <insert id="insert">
        INSERT INTO BNS_IA_DANDFOBJ (DAND, RISKTP, RISKLVL, FCA, ETCOUNT, PTCOUNT, SPCOUNT, REALNM, MOBILE, ESNM,
                                     ESPCOUNT, STATE, TS)
        VALUES (#{data.dand}, #{data.risktp}, #{data.risklvl}, #{data.fca}, #{data.etcount}, #{data.ptcount},
                #{data.spcount}, #{data.realnm}, #{data.mobile}, #{data.esnm}, #{data.espcount}, '1', GETDATE())
    </insert>
    <update id="logicDelete">
        UPDATE BNS_IA_DANDFOBJ
        SET [STATE] = '0'
        WHERE [STATE] = '1' AND DAND = #{dand}
    </update>
    <select id="getCount" resultType="java.lang.Integer">
        SELECT COUNT(DAND)
        FROM IA_C_DANAD
        WHERE DAND = #{dand}
    </select>
    <select id="getPersonPageList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DandfobjPerson">
        SELECT REALNM, MOBILE FROM (
        SELECT (ROW_NUMBER() OVER(PARTITION BY A.MOBILE ORDER BY B.ADCD, A.REALNM ASC)) SNO, B.ADCD, A.REALNM, A.MOBILE
        FROM BNS_IA_DANDFOBJ A LEFT JOIN IA_C_DANAD B ON B.DAND = A.DAND
        WHERE A.MOBILE IS NOT NULL AND LTRIM(RTRIM(A.MOBILE)) != '' AND A.STATE = '1'
        ) T
        WHERE SNO = 1 AND LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        ORDER BY ADCD, REALNM ASC
    </select>
    <select id="getHistoryListByDand" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DandfobjInfo">
        SELECT A.ID,
               A.DAND,
               B.[NAME],
               B.ADCD,
               dbo.fnGetFullAdnm(B.ADCD, 1)                                                                          ADNM,
               B.WSCD,
               STUFF((SELECT '、' + WSNM
                      FROM IA_C_WATA
                      WHERE WSCD IN (SELECT FIELD FROM dbo.fnSplitString(B.WSCD, ',')) FOR XML PATH ('')), 1, 1, '') WSNM,
               A.RISKTP,
               A.RISKLVL,
               A.FCA,
               A.ETCOUNT,
               A.PTCOUNT,
               A.SPCOUNT,
               A.REALNM,
               A.MOBILE,
               A.ESNM,
               A.ESPCOUNT,
               A.[STATE],
               A.TS
        FROM BNS_IA_DANDFOBJ A
                 LEFT JOIN IA_C_DANAD B ON B.DAND = A.DAND
        WHERE A.DAND = #{dand}
        ORDER BY TS DESC
    </select>
    <select id="getExportList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DandfobjExport">
        SELECT ROW_NUMBER() OVER(ORDER BY A.ADCD ASC, B.RISKLVL DESC) SORTNO, A.[NAME], dbo.fnGetFullAdnm(A.ADCD,
        #{map.adnmlvl}) ADNM, STUFF((SELECT '、' + WSNM FROM IA_C_WATA WHERE WSCD IN (SELECT FIELD FROM
        dbo.fnSplitString(A.WSCD, ',')) FOR XML PATH('')), 1, 1, '') WSNM, (CASE B.RISKTP WHEN '1' THEN '临河隐患' WHEN '2'
        THEN '历史山洪' END) RISKTP, (CASE B.RISKLVL WHEN '1' THEN '危险' WHEN '2' THEN '高危' WHEN '3' THEN '极高危' END) RISKLVL,
        B.FCA, B.ETCOUNT, B.PTCOUNT, B.SPCOUNT, B.REALNM, B.MOBILE, B.ESNM, B.ESPCOUNT
        FROM IA_C_DANAD A
        LEFT JOIN BNS_IA_DANDFOBJ B ON B.DAND = A.DAND AND B.[STATE] = '1'
        WHERE LEFT(A.ADCD, #{map.adcdlvl}) = LEFT(#{map.adcd}, #{map.adcdlvl})
        <if test="map.adcdlvl == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.name != null and map.name != ''">
            AND CHARINDEX(#{map.name}, A.[NAME]) > 0
        </if>
        <if test="map.risktp != null and map.risktp != ''">
            AND CHARINDEX(#{map.risktp}, B.RISKTP) > 0
        </if>
        <if test="map.risklvl != null and map.risklvl != ''">
            AND CHARINDEX(#{map.risklvl}, B.RISKLVL) > 0
        </if>
        ORDER BY SORTNO ASC
    </select>
    <select id="getDanadNodeList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DandfobjNode">
        SELECT CODE, NAME, PCODE, DCOUNT, ETCOUNT, PTCOUNT, 1 FLAG FROM (
        <if test="map.include == '1'.toString()">
            SELECT 0 SORTNO, A.ADCD CODE, B.ADNM NAME, B.PADCD PCODE, A.DCOUNT, A.ETCOUNT, A.PTCOUNT FROM (
            SELECT #{map.adcd} ADCD, COUNT(A.DAND) DCOUNT, SUM(B.ETCOUNT) ETCOUNT, SUM(B.PTCOUNT) PTCOUNT
            FROM IA_C_DANAD A LEFT JOIN BNS_IA_DANDFOBJ B ON B.DAND = A.DAND AND B.[STATE] = '1'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">
                AND LEFT(A.ADCD, 6) NOT IN ('220581')
            </if>
            ) A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
            WHERE EXISTS (SELECT 8 FROM MDT_ADCDINFO_E WHERE ADTP = '1' AND ADCD = A.ADCD)
            UNION ALL
        </if>
        <if test="map.level &lt;= 4">
            SELECT A.SORTNO, A.ADCD CODE, B.ADNM NAME, B.PADCD PCODE, C.DCOUNT, C.ETCOUNT, C.PTCOUNT
            FROM MDT_ADCDINFO_E A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
            LEFT JOIN (SELECT ADCD, COUNT(DAND) DCOUNT, SUM(ETCOUNT) ETCOUNT, SUM(PTCOUNT) PTCOUNT FROM (
            SELECT LEFT(LEFT(A.ADCD, #{map.lowLevel}) + '000000000000000', 15) ADCD, A.DAND, B.ETCOUNT, B.PTCOUNT
            FROM IA_C_DANAD A LEFT JOIN BNS_IA_DANDFOBJ B ON B.DAND = A.DAND AND B.[STATE] = '1'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND LEFT(A.ADCD, 6) NOT IN ('220581')
            <if test="map.level == '2'.toString()">
                UNION ALL
                SELECT '220581000000000' ADCD, A.DAND, B.ETCOUNT, B.PTCOUNT
                FROM IA_C_DANAD A LEFT JOIN BNS_IA_DANDFOBJ B ON B.DAND = A.DAND AND B.[STATE] = '1'
                WHERE LEFT(A.ADCD, 6) = '220581'
            </if>
            ) A GROUP BY ADCD) C ON C.ADCD = A.ADCD
            WHERE A.ADTP = '1' AND B.PADCD = #{map.adcd}
        </if>
        <if test="map.level > 4">
            SELECT ROW_NUMBER() OVER(ORDER BY A.ADCD ASC) SORTNO, A.ADCD CODE, B.ADNM NAME, B.PADCD PCODE, A.DCOUNT,
            A.ETCOUNT, A.PTCOUNT FROM (
            SELECT ADCD, COUNT(DAND) DCOUNT, SUM(ETCOUNT) ETCOUNT, SUM(PTCOUNT) PTCOUNT FROM (
            SELECT LEFT(LEFT(A.ADCD, #{map.lowLevel}) + '000000000000000', 15) ADCD, A.DAND, B.ETCOUNT, B.PTCOUNT
            FROM IA_C_DANAD A LEFT JOIN BNS_IA_DANDFOBJ B ON B.DAND = A.DAND AND B.[STATE] = '1'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})) A GROUP BY ADCD) A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
            WHERE B.ADCD IS NOT NULL
        </if>
        ) A
        ORDER BY SORTNO ASC
    </select>
    <select id="getDandfobjNodeList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DandfobjNode">
        SELECT CODE, NAME, PCODE, DCOUNT, RISKTP, RISKLVL, FCA, ETCOUNT, PTCOUNT, FLAG FROM (
        <if test="map.include == '1'.toString()">
            SELECT A.ADCD CODE, B.ADNM NAME, B.PADCD PCODE, A.DCOUNT, NULL RISKTP, NULL RISKLVL, NULL FCA, A.ETCOUNT,
            A.PTCOUNT, 1 FLAG FROM (
            SELECT #{map.adcd} ADCD, COUNT(A.DAND) DCOUNT, SUM(B.ETCOUNT) ETCOUNT, SUM(B.PTCOUNT) PTCOUNT
            FROM IA_C_DANAD A LEFT JOIN BNS_IA_DANDFOBJ B ON B.DAND = A.DAND AND B.[STATE] = '1'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})) A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
            UNION ALL
        </if>
        SELECT A.DAND CODE, A.NAME, LEFT(LEFT(A.ADCD, #{map.level}) + '000000000000000', 15) PCODE, NULL DCOUNT,
        B.RISKTP, B.RISKLVL, B.FCA, B.ETCOUNT, B.PTCOUNT, 2 FLAG
        FROM IA_C_DANAD A LEFT JOIN BNS_IA_DANDFOBJ B ON B.DAND = A.DAND AND B.[STATE] = '1'
        WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})) A
        ORDER BY A.CODE ASC, A.RISKLVL DESC
    </select>
    <select id="getDanBasicInfo" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanBasicInfo">
        SELECT TOP 1 A.DAND, A.NAME,
               C.ADNM,
               STUFF((SELECT '、' + WSNM
                      FROM IA_C_WATA
                      WHERE WSCD IN (SELECT FIELD FROM dbo.fnSplitString(A.WSCD, ',')) FOR XML PATH ('')), 1, 1, '') WSNM,
               A.WSCD,
               B.RISKTP,
               B.RISKLVL,
               B.FCA,
               B.PTCOUNT,
               B.ETCOUNT,
               A.ECOUNT1,
               A.ECOUNT2,
               A.ECOUNT3,
               A.ECOUNT4,
               A.HTCOUNT,
               A.HCOUNT1,
               A.HCOUNT2,
               A.HCOUNT3,
               A.HCOUNT4,
               B.SPCOUNT,
               B.REALNM,
               B.MOBILE,
               B.ESNM,
               B.ESPCOUNT
        FROM IA_C_DANAD A
                 LEFT JOIN BNS_IA_DANDFOBJ B ON B.DAND = A.DAND AND B.[STATE] = '1'
                 LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
        WHERE A.DAND = #{dand}
    </select>
    <select id="getRefInfo" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaRefInfo">
        SELECT TOP 1 ADCD, WSCD
        FROM IA_C_DANAD
        WHERE DAND = #{dand}
    </select>
    <select id="getDanHsfwaterList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaDanHsfwater">
        SELECT A.MTCD,
               B.ADNM,
               C.WSNM,
               A.OTIME,
               A.ADDRESS,
               A.LGTD,
               A.LTTD,
               A.PFRAIN,
               A.DPCOUNT,
               A.MPCOUNT,
               A.CHCOUNT,
               A.SPCOUNT,
               A.ELOSE,
               A.DDSCRIB
        FROM IA_C_HSFWATER A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
        WHERE LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY A.MTCD ASC
    </select>
    <select id="getDanFlrvvlgList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaDanFlrvvlg">
        SELECT A.AVRCD,
               A.NAME,
               B.ADNM,
               C.WSNM,
               A.BLGTD,
               A.BLTTD,
               A.BELE,
               A.PTCOUNT,
               A.AREA,
               A.BTYPE,
               A.STYPE,
               A.LGTD,
               A.LTTD,
               A.HELE,
               A.BWATER,
               A.BHILL
        FROM IA_C_FLRVVLG A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
        WHERE LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY A.AVRCD ASC
    </select>
    <select id="getDanRsList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaDanRs">
        SELECT A.RS_CODE,
               A.RS_NAME,
               B.ADNM,
               C.WSNM,
               A.RS_TYPE,
               A.MAIN_WR_TYPE,
               A.DAM_TYPE,
               A.MAIN_FL_TYPE,
               A.MUL_AVER_RUN,
               A.ENG_GRAD,
               A.DAM_SIZE_HIG,
               A.DAM_SIZE_LEN,
               A.MAX_DIS_FLOW,
               A.DES_FL_STAG,
               A.TOT_CAP,
               A.COR_SUR_AREA
        FROM IA_C_RS A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
        WHERE LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY A.RS_CODE ASC
    </select>
    <select id="getDanSluiceList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaDanSluice">
        SELECT A.GATE_CODE,
               A.GATE_NAME,
               B.ADNM,
               C.WSNM,
               A.GATE_TYPE,
               A.HOLE_NUM,
               A.HOLE_WID,
               A.FL_GATE_FLOW,
               A.RUB_DAM_HIG,
               A.RUB_DAM_LEN
        FROM IA_C_SLUICE A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
        WHERE LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY A.GATE_CODE ASC
    </select>
    <select id="getDanDikeList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaDanDike">
        SELECT A.DIKE_CODE,
               A.DIKE_NAME,
               B.ADNM,
               C.WSNM,
               A.RV_BANK,
               A.DIKE_COR_BOUN,
               A.DIKE_TYPE,
               A.DIKE_STYL,
               A.DIKE_GRAD,
               A.PLAN_FL_STA,
               A.DIKE_LEN,
               A.FL_STA_LEN,
               A.ELE_SYS,
               A.DES_STAG,
               A.DIKE_HIG_MAX,
               A.DIKE_WID_MAX,
               A.ENG_TASK,
               A.DIKE_HIG_MIN,
               A.DIKE_WID_MIN,
               A.DAM_CRE_BEG_ELE,
               A.DAM_CRE_EDN_ELE
        FROM IA_C_DIKE A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
        WHERE LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY A.DIKE_CODE ASC
    </select>
    <select id="getDanDamList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaDanDam">
        SELECT A.DAMCD,
               A.DAMNAME,
               B.ADNM,
               C.WSNM,
               A.XHST,
               A.HEIGHT,
               A.WIDTH,
               A.MT
        FROM IA_C_DAMINFO A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
        WHERE LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY A.DAMCD ASC
    </select>
    <select id="getDanCulvertList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaDanCulvert">
        SELECT A.CULCD,
               A.CULNAME,
               B.ADNM,
               C.WSNM,
               A.HEIGHT,
               A.LENGHT,
               A.WIDTH,
               A.TYPE
        FROM IA_C_CULVERT A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
        WHERE LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY A.CULCD ASC
    </select>
    <select id="getDanBridgeList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaDanBridge">
        SELECT A.BRCD,
               A.BRNAME,
               B.ADNM,
               C.WSNM,
               A.LENGTH,
               A.WIDTH,
               A.HEIGHT,
               A.TYPE
        FROM IA_C_BRIDGE A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
        WHERE LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY A.BRCD ASC
    </select>
    <select id="getDanRainList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanRain">
        SELECT T.STCD, B.STNM, B.STTP, T.ACCP, R1.RAINFALL RAINFALL1, R3.RAINFALL RAINFALL3, R6.RAINFALL RAINFALL6
        FROM (SELECT A.STCD, SUM(A.DRP) ACCP
              FROM ST_PPTN_R A
                       RIGHT JOIN (SELECT DISTINCT A.STCD
                                   FROM EW_REL_ST_AD A
                                            LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
                                   WHERE B.FRGRD != '8' AND LEFT(A.ADCD, #{map.level}) = LEFT (#{map.adcd}
                                       , #{map.level})) B ON B.STCD = A.STCD
              WHERE A.INTV = 1
                AND A.TM >= #{map.stm}
                AND A.TM &lt;= #{map.etm}
              GROUP BY A.STCD) T
                 LEFT JOIN BSN_STBPRP_V B ON T.STCD = B.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm1}
                            GROUP BY STCD) R1 ON R1.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm3}
                            GROUP BY STCD) R3 ON R3.STCD = T.STCD
                 LEFT JOIN (SELECT STCD, SUM(RAIN) RAINFALL
                            FROM BSN_FORECASTRAIN_F
                            WHERE RAIN_TIME >= #{map.fstm}
                              AND RAIN_TIME &lt;= #{map.fetm6}
                            GROUP BY STCD) R6 ON R6.STCD = T.STCD
        ORDER BY T.ACCP DESC, T.STCD ASC
    </select>
    <select id="getDanRsvrList" statementType="CALLABLE" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanRsvr">
        {CALL PROC_GET_RSVRDATA_ALL_NEW(#{map.stm, mode=IN}, #{map.etm, mode=IN}, #{map.whereSql, mode=IN})}
    </select>
    <select id="getDanRiverList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanRiver">
        SELECT T.STCD,
               ST.STNM,
               ST.STTP,
               T.TM,
               R.TM,
               R.Z,
               (R.Z - F.WRZ) ZWRZ,
               F.WRZ
        FROM (SELECT A.STCD, MAX(A.TM) TM
              FROM ST_RIVER_R A
                       RIGHT JOIN (SELECT DISTINCT A.STCD
                                   FROM AD_ST_AD_B A
                                            LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
                                   WHERE B.STTP IN ('ZZ', 'ZQ')
                                     AND B.FRGRD != '8' AND LEFT(A.ADCD, #{map.level}) = LEFT (#{map.adcd}
                                       , #{map.level})) B ON B.STCD = A.STCD
              WHERE A.TM >= #{map.stm}
                AND A.TM &lt;= #{map.etm}
              GROUP BY A.STCD) T
                 LEFT JOIN ST_RIVER_R R ON R.STCD = T.STCD AND R.TM = T.TM
                 LEFT JOIN BSN_STBPRP_V ST ON ST.STCD = T.STCD
                 LEFT JOIN ST_RVFCCH_B F ON F.STCD = T.STCD
        ORDER BY ZWRZ DESC, STCD ASC
    </select>
    <select id="getDanDfwruleList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanDfwrule">
        SELECT B.ADNM,
               C.WSNM,
               D.WARNGRADENM,
               A.LWATER,
               (CASE WHEN A.STDT &lt; 60 THEN A.STDT ELSE A.STDT / 60 END) STDT,
               A.DRPT,
               A.CALMATH,
               A.REMARK
        FROM IA_A_DFWRULE A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
                 LEFT JOIN WARNINGGRADE_B D ON D.WARNGRADEID = A.WARNGRADEID AND D.WARNGRADETYPE = 'E'
        WHERE LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        AND  A.WARNGRADEID != '4'
        ORDER BY A.ADCD, A.WSCD, A.WARNGRADEID, A.STDT ASC
    </select>
    <select id="getDanShPersonList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanShPerson">
        SELECT ZRC, REALNM, RYTP, DUTY, MOBILE
        FROM BSN_SH_PERSON_B
        WHERE LEFT (ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY ZRC, RYTP ASC
    </select>
    <select id="getDanSwllrktbList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanSwllrktb">
        SELECT ADCD, WSCD, HSCD, Z, SJHSHF, CXQ, PCOUNT, HCOUNT, HNCOUNT FROM (
        SELECT ADCD, WSCD, HSCD, Z, SJHSHF, CXQ, PCOUNT, HCOUNT, HNCOUNT FROM IA_A_SWLLRKTB A
        WHERE EXISTS(SELECT 8 FROM IA_C_DANAD WHERE DAND = #{dand} AND ADCD = A.ADCD)
        <if test="include == '1'.toString()">
            UNION ALL
            SELECT NULL ADCD, NULL WSCD, NULL HSCD, NULL Z, NULL SJHSHF, NULL CXQ, PTCOUNT PCOUNT, ETCOUNT HCOUNT,
            HTCOUNT HNCOUNT FROM IA_C_DANAD WHERE DAND = #{dand}
        </if>) A ORDER BY Z ASC
    </select>
    <select id="getDanStList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanStInfo">
        SELECT DISTINCT A.STCD, B.STNM, B.STTP
        FROM AD_ST_AD_B A
                 LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
        WHERE B.FRGRD != '8' AND B.STTP IN ('PP', 'RQ', 'ZQ')
        AND LEFT (A.ADCD , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY A.STCD ASC
    </select>
    <select id="getDanList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.IaDanInfo">
        SELECT DAND, NAME, ADCD, WSCD
        FROM IA_C_DANAD
        WHERE LEFT (ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        ORDER BY ADCD, DAND ASC
    </select>
    <select id="getDanDrptList" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanDrpt">
        SELECT A.ADCD, A.WSCD, A.STDT, B.DRPT DRPT4, C.DRPT DRPT5
        FROM (SELECT DISTINCT ADCD, WSCD, STDT FROM IA_A_DFWRULE WHERE ADCD = #{adcd}) A
                 LEFT JOIN IA_A_DFWRULE B ON B.ADCD = A.ADCD AND B.STDT = A.STDT AND B.WARNGRADEID = 4
                 LEFT JOIN IA_A_DFWRULE C ON C.ADCD = A.ADCD AND C.STDT = A.STDT AND C.WARNGRADEID = 5
        ORDER BY A.ADCD, A.WSCD, A.STDT ASC
    </select>
    <select id="getDanFcaAndCzz" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.DanFcaAndCzz">
        SELECT A.DAND, A.NAME, A.ADCD, B.FCA, C.CZZ
        FROM IA_C_DANAD A
                 LEFT JOIN IA_A_NOWFHTB B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_UNITDP C ON C.ADCD = A.ADCD
        WHERE A.DAND = #{dand}
    </select>
    <select id="getKeyAreaBasicInfo" resultType="com.huitu.cloud.api.ewci.bia.entity.KeyAreaBasicInfo">
        SELECT TOP 1 A.ADCD, B.ADNM,
               A.PTCOUNT,
               A.LDAREA,
               A.PLAREA,
               A.ETCOUNT,
               A.ECOUNT1,
               A.ECOUNT2,
               A.ECOUNT3,
               A.ECOUNT4,
               A.HTCOUNT,
               A.HCOUNT1,
               A.HCOUNT2,
               A.HCOUNT3,
               A.HCOUNT4
        FROM IA_C_PREVAD A
                 LEFT JOIN IA_C_ADINFO B ON A.ADCD = B.ADCD
        WHERE A.ADCD = #{adcd}
    </select>
    <select id="getWatas" resultType="com.huitu.cloud.api.ewci.ia.entity.ext.BnsWata">
        select a.WSCD, AVEROU, ONDCD, MAXLSLP, OUTLETY, PWSCD, CENTERY, WSSHPC, CENTERELV, (SELECT top 1 b.WSNM FROM IA_C_WATA b WHERE b.WSCD = a.wscd ) WSNM, WSAREA, MAXLS1085, OUTLETELV, WSPERI, IWSCD, CENTERX, AVEINF, WSCS, MAXLEN, GB, WSTYPE, MAXELV, OWSCD, WSSLP, OUTLETX
        from BNS_WATA a where  CHARINDEX(CONVERT(varchar(16), a.wscd),  #{wscd}) > 0
    </select>
</mapper>
