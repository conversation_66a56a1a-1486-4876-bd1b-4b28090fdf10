package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 计算分区
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@TableName("Dic_JiSuanFenQu")
@ApiModel(value = "DicJisuanfenqu对象", description = "计算分区")
public class DicJisuanfenqu extends Model<DicJisuanfenqu> {

    @ApiModelProperty(value = "排序号")
    private Integer sortid;

    @ApiModelProperty(value = "分区编码")
    private String jisuanfenquid;

    @ApiModelProperty(value = "分区名称")
    private String jisuanfengqumingcheng;

    @ApiModelProperty(value = "编制区编码")
    private String bianzhiquid;

    @ApiModelProperty(value = "最小经度")
    private Float xmin;

    @ApiModelProperty(value = "最大经度")
    private Float xmax;

    @ApiModelProperty(value = "最小纬度")
    private Float ymin;

    @ApiModelProperty(value = "最大纬度")
    private Float ymax;

    @ApiModelProperty(value = "xxx")
    private String floodriskflashid;

    public Integer getSortid() {
        return sortid;
    }

    public void setSortid(Integer sortid) {
        this.sortid = sortid;
    }

    public String getJisuanfenquid() {
        return jisuanfenquid;
    }

    public void setJisuanfenquid(String jisuanfenquid) {
        this.jisuanfenquid = jisuanfenquid;
    }

    public String getJisuanfengqumingcheng() {
        return jisuanfengqumingcheng;
    }

    public void setJisuanfengqumingcheng(String jisuanfengqumingcheng) {
        this.jisuanfengqumingcheng = jisuanfengqumingcheng;
    }

    public String getBianzhiquid() {
        return bianzhiquid;
    }

    public void setBianzhiquid(String bianzhiquid) {
        this.bianzhiquid = bianzhiquid;
    }

    public Float getXmin() {
        return xmin;
    }

    public void setXmin(Float xmin) {
        this.xmin = xmin;
    }

    public Float getXmax() {
        return xmax;
    }

    public void setXmax(Float xmax) {
        this.xmax = xmax;
    }

    public Float getYmin() {
        return ymin;
    }

    public void setYmin(Float ymin) {
        this.ymin = ymin;
    }

    public Float getYmax() {
        return ymax;
    }

    public void setYmax(Float ymax) {
        this.ymax = ymax;
    }

    public String getFloodriskflashid() {
        return floodriskflashid;
    }

    public void setFloodriskflashid(String floodriskflashid) {
        this.floodriskflashid = floodriskflashid;
    }
}
