package com.huitu.cloud.api.ewci.person.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

/**
 * 水库淹没范围责任人
 *
 * <AUTHOR>
 */
public interface VillagePersonService {

    /**
     * 分页获取水库淹没范围责任人列表
     *
     * @param query
     * @return
     */
    IPage<VillagePersonVo> getPageList(VillagePersonQuery query);

    /**
     * 水库淹没范围责任人统计
     *
     * @param query
     * @return
     */
    List<VillageSummaryVo> getVillageSummaryList(VillageSummaryQuery query);

    /**
     * 数据导出
     *
     * @param query  查询条件
     * @param output 输出流
     **/
    void dataExport(VillagePersonQuery query, OutputStream output);

    /**
     * 数据导入
     *
     * @param xadcd 县级行政区划代码
     * @param input 输入流
     * @return 导入结果
     **/
    ImportResult<BnsVillagePersonTmp> dataImport(String xadcd, InputStream input);

    /**
     * 编辑水库淹没范围责任人
     *
     * @param entity
     * @return
     */
    int villagePersonEdit(VillagePersonVo entity);
}
