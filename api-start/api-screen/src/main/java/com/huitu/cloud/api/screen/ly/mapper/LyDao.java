package com.huitu.cloud.api.screen.ly.mapper;


import com.huitu.cloud.api.screen.fhjz.entity.RainCountVo;
import com.huitu.cloud.api.screen.fhjz.entity.RainVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
public interface LyDao {


    Map queryRvBaseFact(@Param("rvCode") String rvCode);

    List<Map<String, Object>> queryRvBranch(@Param("rvCode") String rvCode);

    Map<String, Object> querySkCountByRv(@Param("rvCode") String rvCode);

    Map<String, Object> querySkFhByRv(@Param("rvCode") String rvCode,@Param("engScal") String engScal);

    List<Map<String, Object>> statisticSkFhByRv(@Param("rvCode") String rvCode,@Param("engScal") String engScal);

    Map<String, Object> queryDikeLenByRv(@Param("rvCode") String rvCode);

    List<Map<String, Object>> queryDikeStatisticByRv(@Param("rvCode") String rvCode);

    List<Map<String, Object>> queryDikeListByRv(@Param("rvCode") String rvCode);

    Map<String, Object> queryHdCountByRv(@Param("rvCode") String rvCode);

    Map<String, Object> queryHdWarnCountByRv(@Param("rvCode") String rvCode, @Param("startTime")String startTime, @Param("endTime")String endTime);

    List<Map<String, Object>> queryHdStatisticByRv(@Param("rvCode") String rvCode, @Param("startTime")String startTime, @Param("endTime")String endTime);

    Map<String, Object> queryRainCountByRv(@Param("rvCode") String rvCode);

    List<Map<String, Object>> queryRainStatisticByRv(@Param("rvCode") String rvCode, @Param("startTime")String startTime, @Param("endTime")String endTime);

    Map<String, Object> querySkStationCountByRv(@Param("rvCode") String rvCode, @Param("engScal") String engScal);

    Map<String, Object> querySkStationCxxCountByRv(@Param("rvCode") String rvCode, @Param("engScal") String engScal, @Param("startTime")String startTime, @Param("endTime")String endTime);

    List<Map<String, Object>> querySkStationStatisticByRv(@Param("rvCode") String rvCode, @Param("engScal") String engScal, @Param("startTime")String startTime, @Param("endTime")String endTime);

    List<Map<String, Object>> queryFhCityListByRv(@Param("rvCode") String rvCode);

    Map<String, Object> queryRv(@Param("rvCode") String rvCode);

    Map<String, Object> queryRvSourLoc(@Param("rvCode") String rvCode);
}
