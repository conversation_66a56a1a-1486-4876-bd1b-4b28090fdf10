package com.huitu.cloud.api.ewci.person.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.exception.DanPersonImportException;
import com.huitu.cloud.api.ewci.person.service.DanPersonService;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * 危险区居民管理服务
 *
 * <AUTHOR>
 */
@Api(tags = "危险区居民管理")
@RestController
@RequestMapping("/api/ewci/person/dan")
public class DanPersonResource extends AbstractApiResource implements ApiResource {

    private static final Logger logger = LoggerFactory.getLogger(DanPersonResource.class);

    @Override
    public String getUuid() {
        return "3354e3bb-0fa4-a45d-fb20-4ac35356952e";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final DanPersonService baseService;

    public DanPersonResource(DanPersonService baseService) {
        this.baseService = baseService;
    }

    @ApiOperation(value = "分页获取危险区居民信息列表", notes = "作者：曹宝金")
    @PostMapping("select-page-list")
    public ResponseEntity<SuccessResponse<IPage<DanPerson>>> getPageList(@Validated @RequestBody DanPersonQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(query)));
    }

    @ApiOperation(value = "危险区居民汇总统计", notes = "作者：赵英捷")
    @PostMapping("select-dan-per-sum-list")
    public ResponseEntity<SuccessResponse<List<DanSummaryVo>>> getDanSummaryList(@RequestBody DanSummaryQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getDanSummaryList(query)));
    }

    @ApiOperation(value = "导入危险区居民信息", notes = "作者：曹宝金")
    @ApiImplicitParams({@ApiImplicitParam(name = "xadcd", value = "县级行政区划代码", required = true, dataType = "String"),})
    @PostMapping(value = "import", headers = "content-type=multipart/form-data")
    public ResponseEntity<SuccessResponse<ImportResult<BnsDanpersonTmp>>> dataImport(@RequestParam String xadcd, @ApiParam(value = "file", required = true) MultipartFile data) throws Exception {
        if (AdcdUtil.getXzAdLevel(xadcd) != 3) {
            throw new RuntimeException("参数[县级行政区划代码]无效");
        }
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.dataImport(xadcd, data.getInputStream())));
        } catch (DanPersonImportException ex) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "Error", new ImportResult<>(0, ex.getMessage(), ex.getErrorData())));
        }
    }

    @ApiOperation(value = "导出危险区居民信息", notes = "作者：曹宝金")
    @PostMapping(value = "export")
    public void dataExport(@Validated @RequestBody DanPersonQuery query, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("危险区居民信息", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.dataExport(query, response.getOutputStream());
        } catch (Exception ex) {
            logger.error("危险区居民信息导出失败，参数：{}", JSON.toJSONString(query));
            logger.error(ex.getMessage(), ex);
        }
    }

    @ApiOperation(value = "编辑危险区居民", notes = "zyj")
    @PostMapping(value = "dan-person-edit")
    public ResponseEntity<SuccessResponse<Integer>> danPersonEdit(@RequestBody DanPerson entity) throws Exception {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.danPersonEdit(entity)));
    }
}
