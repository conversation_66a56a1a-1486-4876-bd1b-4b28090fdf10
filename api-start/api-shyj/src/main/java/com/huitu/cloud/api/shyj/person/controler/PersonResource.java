package com.huitu.cloud.api.shyj.person.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.shyj.person.entity.Person;
import com.huitu.cloud.api.shyj.person.entity.QueryPerson;
import com.huitu.cloud.api.shyj.person.entity.RelevantPersionInfo;
import com.huitu.cloud.api.shyj.person.service.PersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 人员信息  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
@RestController
@Api(tags = "防汛人员信息接口")
@RequestMapping("/api/shyj/person")
public class PersonResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "1c9188f9-ae76-44ea-adbd-c0e3f09c1b2c";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    PersonService baseService;


    @ApiOperation(value = "查询人员信息", notes = "查询人员信息列表")
    @PostMapping(value = "select-person-list")
    public ResponseEntity<SuccessResponse<Page<RelevantPersionInfo>>> getPersonList(@RequestBody QueryPerson queryPerson) throws Exception {
        IPage<RelevantPersionInfo> list = baseService.getPersionList(queryPerson.getAdcd(), queryPerson.getName(), queryPerson.getDepts(), queryPerson.getMobile(), queryPerson.getType()
                , queryPerson.getPageNum(), queryPerson.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "添加人员信息", notes = "添加人员信息")
    @PostMapping(value = "add-person")
    public ResponseEntity<SuccessResponse<String>> addPerson(@RequestBody Person entity) throws Exception {
        String flag = baseService.addPerson(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "更新人员信息", notes = "更新人员信息")
    @PostMapping(value = "update-person")
    public ResponseEntity<SuccessResponse<String>> updatePerson(@RequestBody Person entity) throws Exception {
        String flag = baseService.updatePerson(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "删除人员信息", notes = "删除人员信息")
    @GetMapping(value = "del-person-by-id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "personcd", value = "人员编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<String>> delPerson(@RequestParam String personcd) throws Exception {
        String flag = baseService.delPerson(personcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

}
