package com.huitu.cloud.api.screen.ly.service.impl;

import com.huitu.cloud.api.screen.ly.mapper.LyDao;
import com.huitu.cloud.api.screen.ly.mapper.SvgDao;
import com.huitu.cloud.api.screen.ly.service.SvgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 流域信息获取 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Service
public class SvgServiceImpl implements SvgService {

    private static final Logger logger = LoggerFactory.getLogger(SvgServiceImpl.class);

    @Autowired
    private SvgDao svgDao;

    @Override
    public List<Map<String, Object>> queryRsvrInfo(List<String> resCodeList) {
        return svgDao.queryRsvrInfo(resCodeList);
    }

    @Override
    public List<Map<String, Object>> queryRiverInfo(List<String> resCodeList) {
        return svgDao.queryRiverInfo(resCodeList);
    }

    @Override
    public List<Map<String, Object>> queryFloodControlList(String rvCode,String type) {
        return svgDao.queryFloodControlList(rvCode,type);
    }

    @Override
    public List<Map<String, Object>> queryDikeInfo(List<String> resCodeList) {
        return svgDao.queryDikeInfo(resCodeList);
    }
}
