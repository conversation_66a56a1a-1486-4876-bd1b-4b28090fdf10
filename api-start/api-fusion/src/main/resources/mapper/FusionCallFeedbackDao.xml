<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.fusion.mapper.FusionCallFeedbackDao">

    <select id="getNoticeReceiverList" resultType="com.huitu.cloud.api.fusion.entity.base.FusionNoticeReceiver">
        SELECT
        A.PHONE_NO,
        RECEIVER,
        POSITION,
        CASE
        WHEN TAG IS NOT NULL THEN '110' + TAG
        ELSE NULL
        END AS TAG,
        CALL_ID,
        C.WARN_ID,
        CREATE_TIME,
        CONFIRM_TIME
        FROM
        EW_CALL_FEEDBACK A
        LEFT JOIN EW_FUSION_RECEIVER B ON B.MSG_ID = A.XCC_WARN_MSG_ID
        AND B.PHONE_NO = A.PHONE_NO
        LEFT JOIN EW_FUSION_REL_MESSAGE C ON C.MSG_ID = B.MSG_ID
        WHERE
        CALL_TYPE = '9'
        AND [STATUS] = #{ map.status}
        <choose>
            <when test="map.status == '1'.toString()">
                AND CONFIRM_RESULT = '4' AND (CONFIRM_TIME BETWEEN #{map.stm} AND #{map.etm})
            </when>
            <otherwise>
                AND (CREATE_TIME BETWEEN #{map.stm} AND #{map.etm})
            </otherwise>
        </choose>
        AND EXISTS(SELECT 8 FROM EW_FUSION_WARNING_RECORD WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd},
        #{map.level})
        <if test="map.exclusions != null and map.exclusions != ''">
            AND CHARINDEX(LEFT(ADCD, 6), #{map.exclusions}) &lt;= 0
        </if>AND WARN_ID = C.WARN_ID)
    </select>
    <update id="manualConfirm">
        UPDATE EW_CALL_FEEDBACK
        SET [STATUS] = '1', CONFIRM_RESULT = '4', CONFIRM_TIME = GETDATE(), LATEST_TIME = GETDATE()
        WHERE [STATUS] = '0' AND CALL_ID IN
        <foreach collection="callIds" item="callId" separator="," open="(" close=")">#{callId}</foreach>
    </update>
    <select id="getCallFailStatsList" resultType="com.huitu.cloud.api.fusion.entity.response.FusionCallStatisticsResponse">
        SELECT T.ADCD,
               AD.ADNM,
               FAIL_COUNT,
               STUFF((SELECT '、' + PHONE_NO +
                             (CASE WHEN LTRIM(RTRIM(ISNULL(REMARK, ''))) != '' THEN '(' + REMARK + ')' ELSE '' END)
                      FROM BSN_DUTY_PHONE
                      WHERE ADCD = T.ADCD
                      FOR XML PATH ('')), 1, 1, '') DUTY_PHONE
        FROM (SELECT M.ADCD, COUNT(DISTINCT S.PHONE_NO) FAIL_COUNT
              FROM (SELECT (PLATFORM_ID + '000000000')                 ADCD,
                           dbo.fnGetAdLevel(PLATFORM_ID + '000000000') ADLVL,
                           ISNULL(EXCLUSIONS, '')                      EXCLUSIONS
                    FROM EW_FUSION_WARNING_CONF
                    WHERE PLATFORM_ID != '220000') M
                       INNER JOIN (SELECT (LEFT(B.ADCD, 6) + '000000000') ADCD, A.PHONE_NO
                                   FROM EW_CALL_FEEDBACK A
                                            LEFT JOIN EW_FUSION_WARNING_RECORD B
                                                      ON EXISTS(SELECT 8
                                                                FROM EW_FUSION_REL_MESSAGE
                                                                WHERE MSG_ID = A.XCC_WARN_MSG_ID
                                                                  AND WARN_ID =
                                                                      B.WARN_ID)
                                   WHERE A.CALL_TYPE = '9'
                                     AND A.[STATUS] = '0'
                                     AND (A.CREATE_TIME BETWEEN #{map.stm} AND #{map.etm})) S ON
                          LEFT(S.ADCD, M.ADLVL) = LEFT(M.ADCD, M.ADLVL) AND CHARINDEX(LEFT(S.ADCD, 6), M.EXCLUSIONS) &lt;= 0
              GROUP BY M.ADCD) T
                 LEFT JOIN MDT_ADCDINFO_B AD ON AD.ADCD = T.ADCD
        ORDER BY FAIL_COUNT DESC, T.ADCD ASC
    </select>
</mapper>