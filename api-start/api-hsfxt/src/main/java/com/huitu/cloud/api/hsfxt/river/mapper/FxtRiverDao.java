package com.huitu.cloud.api.hsfxt.river.mapper;

import com.huitu.cloud.api.hsfxt.city.entity.DicHongshuiliangji;
import com.huitu.cloud.api.hsfxt.river.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 风险图-河流 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
public interface FxtRiverDao {
    /**
     * 查询屯信息
     *
     * @param param 查询参数
     * @return
     */
    List<RiverTree> getTunRiverTreeInfo(Map<String, Object> param);

    /**
     * 查询县和乡镇信息
     *
     * @param param 查询参数
     * @return
     */
    List<RiverTree> getRiverTreeInfo(Map<String, Object> param);

    /**
     * 河流社会经济统计查询
     *
     * @param key 查询条件组合
     * @return
     */
    List<SheHuiJingJi> getRiverShjjTreeInfo(@Param("key") String key);

    /**
     * 查询屯信息需要的洪水量级参数
     *
     * @param newParam 查询参数
     * @return
     */
    DicHongshuiliangji getRiverParam(Map<String, Object> newParam);

    /**
     * 查询县区乡镇信息
     *
     * @param adcd  政区编码
     * @param bzqid 编制区编码
     * @return
     */
    JiangHeXiangQing getXianQuXiangZhen(@Param("adcd") String adcd, @Param("bzqid") String bzqid);

    /**
     * 查询灾害损失信息
     *
     * @param adcd 政区编码
     * @param key  查询条件组合
     * @return
     */
    JiangHeXiangQing getZaiHaiSunShi(@Param("adcd") String adcd, @Param("key") String key);

    /**
     * 查询屯详细信息
     *
     * @param newParam2 查询参数
     * @return
     */
    TunXiangQing getTunShjjDetailInfo(Map<String, Object> newParam2);

    /**
     * 查询转移方案信息
     *
     * @param adcd  政区编码
     * @param bzqid 编制区编码
     * @return
     */
    List<ZhuanYiFangAn> getZhuanYiFangAn(@Param("adcd")String adcd, @Param("bzqid")String bzqid);
}
