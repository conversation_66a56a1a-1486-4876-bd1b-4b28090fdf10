<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.inspect.mapper.InspectReportDao">

    <select id="getBnsInspectTypeInfoList"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectTypeInfo">
        SELECT INSPECT_CODE, INSPECT_NAME, SNO, [STATE]
        FROM BNS_INSPECT_TYPE_INFO
        <where>
            <if test="state != null and state !=''">
                AND [STATE] = #{state}
            </if>
        </where>
        ORDER BY SNO ASC
    </select>
    <select id="getBnsInspectPointInfoList"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectPointInfo">
        SELECT INSPECT_POINT, SNO, INSPECT_CODE
        FROM BNS_INSPECT_POINT_INFO
        WHERE INSPECT_CODE = #{inspectCode}
        ORDER BY SNO ASC
    </select>
    <select id="getBnsInspectReportCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM BNS_INSPECT_REPORT_INFO
        WHERE ADCD = #{adcd} AND YEAR = #{year}
    </select>
    <select id="getBnsInspectRecordsList"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectRecords">
        SELECT O.GROUPID,
        O.ADCD,
        O.YEAR,
        O.NUM,
        C.ADNM,
        (CASE WHEN D.adlvl = 1 THEN '省' ELSE D.adnm END) + '-' + CONVERT(VARCHAR (2), B.GROUPNUM) GROUPNM
        FROM (SELECT A.GROUPID, A.ADCD, [YEAR], COUNT (INSPECT_ID) NUM
        FROM BNS_INSPECT_RECORDS A
        GROUP BY A.GROUPID, A.ADCD, A.YEAR) O
        LEFT JOIN BNS_FLOODINSPECTGROUP_B B ON O.GROUPID = B.groupid
        LEFT JOIN bsn_adcd_b C ON O.adcd = C.adcd
        LEFT JOIN bsn_adcd_b D ON B.adcd = D.adcd
        WHERE O.YEAR = #{map.year}
        <if test="map.adcd != null and map.adcd !=''">
            AND LEFT(O.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(O.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.groupid != null and map.groupid !=''">
            AND O.GROUPID = #{map.groupid}
        </if>
        ORDER BY B.groupnum ASC
    </select>
    <select id="getBnsInspectRecordsChildList"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectRecordsChild">
        SELECT INSPECT_ID, [YEAR], GROUPID, ADCD, TYPE_CODE, TYPE_NAME, CODE, [NAME], INSP_DATE, EXIST_PROBLEMS, REC_ASK, REC_DATE
        FROM BNS_INSPECT_RECORDS
        WHERE [YEAR] = #{map.year} AND ADCD = #{map.adcd}
        <if test="map.groupid != null and map.groupid !=''">
          AND GROUPID = #{map.groupid}
        </if>
        ORDER BY INSP_DATE DESC
    </select>
    <select id="getRsvrRunList" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectRsvr">
        SELECT A.RES_CODE, A.RES_NAME,B.AD_CODE ADCD
        FROM ATT_RES_BASE A
                 LEFT JOIN REL_RES_AD B ON A.RES_CODE = B.RES_CODE
        WHERE CHARINDEX(A.ENG_SCAL, #{map.engScal}) > 0
        AND LEFT(B.AD_CODE, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        ORDER BY A.RES_CODE
    </select>
    <select id="getIllRsvrList" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectRsvr">
        SELECT RES_CODE, RES_NAME, ADCD
        FROM BNS_ILL_RSVR_B
        WHERE 1=1
        <if test="map.engScal != null and map.engScal !=''">
            AND CHARINDEX(ENG_SCAL, #{map.engScal}) > 0
        </if>
          AND LEFT (ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
          AND [YEAR] = #{map.year}
        ORDER BY RES_CODE
    </select>
    <select id="getConstructEngList" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsEngB">
        SELECT EN_CODE,EN_NAME FROM BNS_CONSTRUCT_ENG_B
        WHERE LEFT (ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
          AND [YEAR] = #{map.year}
        ORDER BY EN_CODE
    </select>
    <select id="getShgcConstructList" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsEngB">
        SELECT EN_CODE,EN_NAME FROM BNS_SHGC_CONSTRUCT
        WHERE LEFT (ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
          AND [YEAR] = #{map.year}
        ORDER BY EN_CODE
    </select>
    <select id="getAqdxConstructList" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsEngB">
        SELECT EN_CODE,EN_NAME FROM BNS_AQDX_CONSTRUCT
        WHERE LEFT (ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
          AND [YEAR] = #{map.year}
        ORDER BY EN_CODE
    </select>
    <select id="getAutoMonitorList" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsAutoMonitorB">
        SELECT STCD, STNM
        FROM ST_STBPRP_B
        WHERE FRGRD = 5
        AND LEFT (ADDVCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        AND STTP = #{map.sttp}
    </select>
    <insert id="addFile">
        INSERT INTO BNS_INSPECT_FILE_B(
        FILE_ID,
        BUSINESS_KEY,
        FILE_NAME,
        FILE_PATH,
        FILE_TYPE)
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (NEWID()
            , #{item.businessKey}
            , #{item.fileName}
            , #{item.filePath}
            , #{item.fileType})
        </foreach>
    </insert>
    <delete id="deleteFile">
        DELETE
        FROM BNS_INSPECT_FILE_B
        WHERE BUSINESS_KEY = #{businessKey}
    </delete>
    <delete id="deleteFileByType">
        DELETE
        FROM BNS_INSPECT_FILE_B
        WHERE BUSINESS_KEY = #{businessKey}
          AND FILE_TYPE = #{fileType}
    </delete>
    <select id="selectByAdLevel" resultType="com.huitu.cloud.api.usif.ad.entity.BsnAdcdB">
        select ADCD, ADNM, PADCD, LGTD, LTTD, ADLVL FROM BSN_ADCD_B WHERE 1=1
        <if test="level !=null and level !='' and ad !=null and ad !=''">
            and left(ADCD,#{level})=#{ad}
        </if>
        <if test="adLvl !=null and adLvl !=''">
            and ADLVL &lt; #{adLvl}
        </if>
        order by adcd
    </select>
    <select id="getAdnmByAdcd" resultType="java.lang.String">
        WITH adnm_path AS (
            SELECT
                adcd,
                CAST(adnm AS VARCHAR(MAX)) AS adnm,
                padcd
            FROM
                BSN_ADCD_B
            WHERE
                adcd = #{adcd}
            UNION ALL
            SELECT
                a.adcd,
                CAST(a.adnm AS VARCHAR(MAX)) + ' > ' + b.adnm,
                a.padcd
            FROM
                BSN_ADCD_B a
                    JOIN adnm_path b ON a.adcd = b.padcd
        )
        SELECT
            REPLACE(LTRIM(SUBSTRING(adnm, CHARINDEX('>', adnm) + 2, LEN(adnm))), ' > ', '') AS adnm
        FROM
            adnm_path
        WHERE
            padcd = '0';
    </select>
    <select id="getTadnmByAdcd" resultType="java.lang.String">
        WITH adnm_path AS (
            SELECT
                adcd,
                CAST(adnm AS VARCHAR(MAX)) AS adnm,
                padcd
            FROM
                BSN_ADCD_B
            WHERE
                adcd = #{adcd}

            UNION ALL

            SELECT
                a.adcd,
                CAST(a.adnm AS VARCHAR(MAX)) + '>' + b.adnm,
                a.padcd
            FROM
                BSN_ADCD_B a
                    JOIN adnm_path b ON a.adcd = b.padcd
        )
        SELECT
            SUBSTRING(adnm, CHARINDEX('>', adnm) + 1, LEN(adnm)) AS tadnm
        FROM
            adnm_path
        WHERE
            padcd = '0';

    </select>
    <select id="getPersonImpInfo" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectPersonImp">
        SELECT
            A.*,
            B.INSPECT_NAME
        FROM
            BNS_INSPECT_PERSON_IMP A
                LEFT JOIN BNS_INSPECT_TYPE_INFO B ON #{ inspectCode } = B.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="getFile" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectFile">
        SELECT * FROM BNS_INSPECT_FILE_B
        WHERE BUSINESS_KEY = #{businessKey}
    </select>
    <select id="getPlanReviseInfo" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectPlanRevise">
        SELECT
            A.*,
            B.INSPECT_NAME
        FROM
            BNS_INSPECT_PLAN_REVISE A
                LEFT JOIN BNS_INSPECT_TYPE_INFO B ON #{ inspectCode } = B.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId}
    </select>
    <select id="getEmgCommDevInfo" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectEmgCommDev">
        SELECT
            A.*,
            B.INSPECT_NAME
        FROM
            BNS_INSPECT_EMG_COMMDEV A
                LEFT JOIN BNS_INSPECT_TYPE_INFO B ON #{ inspectCode } = B.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId}
    </select>
    <select id="getRsvrRunInfo" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectRsvrRun">
        SELECT
            A.*,
            B.RES_NAME,
            B.ENG_SCAL,
            C.INSPECT_NAME
        FROM
            BNS_INSPECT_RSVR_RUN A
                LEFT JOIN ATT_RES_BASE B ON B.RES_CODE = A.RES_CODE
                LEFT JOIN BNS_INSPECT_TYPE_INFO C ON #{ inspectCode } = C.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId}
    </select>
    <select id="getInspectIllRsvrInfo" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectIllRsvr">
        SELECT
            A.*,
            B.RES_NAME,
            B.ENG_SCAL,
            C.INSPECT_NAME
        FROM
            BNS_INSPECT_ILL_RSVR A
                LEFT JOIN BNS_ILL_RSVR_B B ON B.RES_CODE = A.RES_CODE
                LEFT JOIN BNS_INSPECT_TYPE_INFO C ON #{ inspectCode } = C.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId}
    </select>
    <select id="getInspectRiverDikeInfo"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectRiverDike">
        SELECT
            A.*,
            B.INSPECT_NAME
        FROM
            BNS_INSPECT_RIVER_DIKE A
                LEFT JOIN BNS_INSPECT_TYPE_INFO B ON #{ inspectCode } = B.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="getInspectConEnInfo" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectConEn">
        SELECT
            A.*,
            B.EN_NAME,
            C.INSPECT_NAME
        FROM
            BNS_INSPECT_CON_EN A
                LEFT JOIN BNS_CONSTRUCT_ENG_B B ON A.EN_CODE = B.EN_CODE
                LEFT JOIN BNS_INSPECT_TYPE_INFO C ON #{ inspectCode } = C.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="getInspectWagaRunInfo" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectWagaRun">
        SELECT
            A.*,
            B.INSPECT_NAME
        FROM
            BNS_INSPECT_WAGA_RUN A
                LEFT JOIN BNS_INSPECT_TYPE_INFO B ON #{ inspectCode } = B.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="getInspectAutoMonitorInfo"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectAutoMonitor">
        SELECT
            A.*,
            B.STTP,
            B.STNM,
            C.INSPECT_NAME
        FROM
            BNS_INSPECT_AUTO_MONITOR A
                LEFT JOIN ST_STBPRP_B B ON A.STCD = b.STCD
                LEFT JOIN BNS_INSPECT_TYPE_INFO C ON #{ inspectCode } = C.INSPECT_CODE
        WHERE
            INSPECT_ID = #{ inspectId }
    </select>
    <select id="getInspectSimpleRainInfo"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectSimpleRain">
        SELECT
            A.*,
            B.INSPECT_NAME
        FROM
            BNS_INSPECT_SIMPLE_RAIN A
                LEFT JOIN BNS_INSPECT_TYPE_INFO B ON #{ inspectCode } = B.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="getInspectWarnBroadInfo"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectWarnBroad">
        SELECT
            A.*,
            B.INSPECT_NAME
        FROM
            BNS_INSPECT_WARN_BROAD A
                LEFT JOIN BNS_INSPECT_TYPE_INFO B ON #{ inspectCode } = B.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="getInspectCountyPlatformInfo"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectCountyPlatform">
        SELECT
            A.*,
            B.INSPECT_NAME
        FROM
            BNS_INSPECT_COUNTY_PLATFORM A
                LEFT JOIN BNS_INSPECT_TYPE_INFO B ON #{ inspectCode } = B.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="getInspectMainExpInfo" resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectMainExp">
        SELECT
            A.*,
            B.INSPECT_NAME
        FROM
            BNS_INSPECT_MAIN_EXP A
                LEFT JOIN BNS_INSPECT_TYPE_INFO B ON #{ inspectCode } = B.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="getInspectShgcConsInfo"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectShgcCons">
        SELECT
            A.*,
            B.EN_NAME,
            C.INSPECT_NAME
        FROM
            BNS_INSPECT_SHGC_CONS A
                LEFT JOIN BNS_SHGC_CONSTRUCT B ON A.EN_CODE = B.EN_CODE
                LEFT JOIN BNS_INSPECT_TYPE_INFO C ON #{ inspectCode } = C.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="getInspectAqdxConsInfo"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectAqdxCons">
        SELECT
            A.*,
            B.EN_NAME,
            C.INSPECT_NAME
        FROM
            BNS_INSPECT_AQDX_CONS A
                LEFT JOIN BNS_AQDX_CONSTRUCT B ON A.EN_CODE = B.EN_CODE
                LEFT JOIN BNS_INSPECT_TYPE_INFO C ON #{ inspectCode } = C.INSPECT_CODE
        WHERE
            A.INSPECT_ID = #{ inspectId }
    </select>
    <select id="selectReportCount" resultType="java.lang.Integer">
        select count(1) from BNS_INSPECT_REPORT_INFO WHERE ADCD = #{ adcd } and YEAR = #{year}
    </select>
    <insert id="insertReport">
        INSERT INTO BNS_INSPECT_REPORT_INFO (REPORT_ID, ADCD, YEAR, CREATE_DATE, CONFIRM_RESULT, STATUS, LATEST_TIME, CREATOR)
        VALUES
            (#{ reportId }
            , #{ adcd }, #{year},#{ createDate },#{ confirmResult },#{ status },#{ latestTime },#{ creator })
    </insert>
    <select id="getBnsInspectReportsChildList"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectReportsChild">
        SELECT
            A.*,
            B.ADNM,
            C.loginnm
        FROM
            BNS_INSPECT_REPORT_INFO A
                LEFT JOIN bsn_adcd_b B ON B.adcd = A.adcd
                LEFT JOIN BNS_USERLOGUSER_B C ON C.userid = A.CREATOR
        WHERE
            A.[YEAR] = #{ map.year }
        AND LEFT (A.ADCD
            , #{map.level}) = LEFT (#{map.adcd}
            , #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        AND CHARINDEX(A.CONFIRM_RESULT, #{ map.confirmResult}) > 0
        ORDER BY A.CREATE_DATE DESC,A.ADCD ASC
    </select>
    <select id="getReportConfirmResult" resultType="java.lang.Integer">
        SELECT count(1)
        FROM BNS_INSPECT_REPORT_INFO
        WHERE REPORT_ID = #{ reportId } and CONFIRM_RESULT = '1'
    </select>
    <select id="getCurrentAdnmByAdcd" resultType="java.lang.String">
        SELECT ADNM FROM BSN_ADCD_B WHERE ADCD = #{adcd}
    </select>
    <update id="updateReportConfirm">
        UPDATE BNS_INSPECT_REPORT_INFO
        SET CONFIRM_RESULT = '1', LATEST_TIME = GETDATE()
        WHERE REPORT_ID = #{ reportId }
          AND CONFIRM_RESULT = '0'
    </update>
    <delete id="deleteInspectReport">
        DELETE
        FROM
            BNS_INSPECT_REPORT_INFO
        WHERE REPORT_ID = #{ reportId }
        AND CONFIRM_RESULT = '0'
    </delete>
    <update id="updateReportStatus">
        UPDATE BNS_INSPECT_REPORT_INFO
        SET STATUS = #{status}, LATEST_TIME = GETDATE()
        WHERE REPORT_ID = #{reportId} AND CONFIRM_RESULT = '0'
    </update>
    <select id="getInspectReportById"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectReportsChild">
        SELECT A.*,
               B.loginnm,
               C.FILE_PATH
        FROM
         BNS_INSPECT_REPORT_INFO A
         LEFT JOIN BNS_USERLOGUSER_B B ON B.userid = A.CREATOR
         LEFT JOIN BNS_INSPECT_FILE_B C ON A.REPORT_ID = C.BUSINESS_KEY AND C.FILE_TYPE = '4'
        WHERE A.REPORT_ID = #{reportId}
    </select>
</mapper>