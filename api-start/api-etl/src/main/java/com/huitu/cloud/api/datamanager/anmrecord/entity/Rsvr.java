package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 水库水情表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-09
 */
@TableName("ST_RSVR_R")
@ApiModel(value="StRsvrR对象", description="水库水情表")
public class Rsvr extends Model<Rsvr> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "时间")
    private String tm;

    @ApiModelProperty(value = "库上水位")
    @TableField("RZ")
    private String rz;

    @ApiModelProperty(value = "入库流量")
    @TableField("INQ")
    private BigDecimal inq;

    @ApiModelProperty(value = "蓄水量")
    @TableField("W")
    private BigDecimal w;

    @ApiModelProperty(value = "库下水位")
    @TableField("BLRZ")
    private BigDecimal blrz;

    @ApiModelProperty(value = "出库流量")
    @TableField("OTQ")
    private BigDecimal otq;

    @ApiModelProperty(value = "库水特征码")
    @TableField("RWCHRCD")
    private String rwchrcd;

    @ApiModelProperty(value = "库水水势")
    @TableField("RWPTN")
    private String rwptn;

    @ApiModelProperty(value = "入流时段长")
    @TableField("INQDR")
    private BigDecimal inqdr;

    @ApiModelProperty(value = "测流方法")
    @TableField("MSQMT")
    private String msqmt;
    @ApiModelProperty(value = "修改值")
    private String editvalue;
    private Date  tmTwo;
    public String getEditvalue() {
        return editvalue;
    }

    public void setEditvalue(String editvalue) {
        this.editvalue = editvalue;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getRz() {
        return rz;
    }

    public void setRz(String rz) {
        this.rz = rz;
    }

    public BigDecimal getInq() {
        return inq;
    }

    public void setInq(BigDecimal inq) {
        this.inq = inq;
    }

    public BigDecimal getW() {
        return w;
    }

    public void setW(BigDecimal w) {
        this.w = w;
    }

    public BigDecimal getBlrz() {
        return blrz;
    }

    public void setBlrz(BigDecimal blrz) {
        this.blrz = blrz;
    }

    public BigDecimal getOtq() {
        return otq;
    }

    public void setOtq(BigDecimal otq) {
        this.otq = otq;
    }

    public String getRwchrcd() {
        return rwchrcd;
    }

    public void setRwchrcd(String rwchrcd) {
        this.rwchrcd = rwchrcd;
    }

    public String getRwptn() {
        return rwptn;
    }

    public void setRwptn(String rwptn) {
        this.rwptn = rwptn;
    }

    public BigDecimal getInqdr() {
        return inqdr;
    }

    public void setInqdr(BigDecimal inqdr) {
        this.inqdr = inqdr;
    }

    public String getMsqmt() {
        return msqmt;
    }

    public void setMsqmt(String msqmt) {
        this.msqmt = msqmt;
    }

    public Date getTmTwo() {
        return tmTwo;
    }

    public void setTmTwo(Date tmTwo) {
        this.tmTwo = tmTwo;
    }

    @Override
    public String toString() {
        return "StRsvrR{" +
        "stcd=" + stcd +
        ", tm=" + tm +
        ", rz=" + rz +
        ", inq=" + inq +
        ", w=" + w +
        ", blrz=" + blrz +
        ", otq=" + otq +
        ", rwchrcd=" + rwchrcd +
        ", rwptn=" + rwptn +
        ", inqdr=" + inqdr +
        ", msqmt=" + msqmt +
        "}";
    }
}
