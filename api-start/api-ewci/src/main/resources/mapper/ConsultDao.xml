<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.consult.mapper.ConsultDao">

    <select id="getWaterAdRiverStatList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultWaterAdRiverVo">
        SELECT ADCD, ADNM, isnull(Z, 0) Z, isnull(ZWRZ, 0) ZWRZ, isnull(ZGRZ, 0) ZGRZ FROM (
        SELECT ADCD, ADNM, ADLVL, PADCD,
        CASE WHEN ADLVL = 2 AND ADCD = '220581000000000' THEN LEFT(ADCD, 6) ELSE LEFT(ADCD, 6) END SIMADCD
        FROM MDT_ADCDINFO_B
        ) A LEFT JOIN (
        SELECT SADCD,
        SUM(CASE WHEN ZWRZ &lt; 0 OR ZGRZ &lt; 0 OR ZWRZ IS NULL OR ZWRZ IS NULL THEN 1 ELSE 0 END) Z,
        SUM(CASE WHEN ZWRZ > 0 THEN 1 ELSE 0 END) ZWRZ,
        SUM(CASE WHEN ZGRZ > 0 THEN 1 ELSE 0 END) ZGRZ
        FROM (
        SELECT A.STCD, A.TM, A.Z, FH.WRZ, A.Z - FH.WRZ AS ZWRZ, FH.GRZ, A.Z - FH.GRZ ZGRZ, STADTP.ADCD,C.ADLVL,
        CASE WHEN LEFT(STADTP.ADCD, 6) = '220581' THEN '220581' ELSE
        <choose>
            <when test="map.level !=null and map.level =='4'.toString()">
                LEFT(STADTP.ADCD, 6)
            </when>
            <otherwise>
                LEFT(LEFT(STADTP.ADCD, 4) + '0000', 6)
            </otherwise>
        </choose>
        END SADCD
        from (
        select stcd, max(tm) tm from ST_RIVER_R
        where tm>=CONVERT(datetime,#{map.stm}) and TM &lt;= CONVERT(datetime,#{map.etm})
        group by stcd
        ) T INNER JOIN ST_RIVER_R A ON A.STCD = T.STCD and A.TM = T.tm
        LEFT JOIN ST_STBPRP_B ST ON A.STCD = ST.STCD
        INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
        LEFT JOIN ST_RVFCCH_B FH on STADTP.STCD = FH.STCD
        LEFT JOIN bsn_adcd_b C ON STADTP.ADCD=C.ADCD
        WHERE left(STADTP.ADCD, #{map.level})=#{map.ad} and CHARINDEX(ST.STTP,'ZZ,ZQ')>0
        ) A GROUP BY SADCD
        ) B ON A.SIMADCD = B.SADCD
        WHERE A.PADCD = #{map.adcd}
    </select>

    <select id="getWaterAdRsvrStatList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultWaterAdRsvrVo">
        SELECT ADCD, ADNM, isnull(RZ,0) RZ, isnull(RZFSLTDZ,0) RZFSLTDZ, isnull(RZDSFLZ,0) RZDSFLZ FROM (
        SELECT ADCD, ADNM, ADLVL, PADCD,
        CASE WHEN ADLVL = 2 AND ADCD = '220581000000000' THEN LEFT(ADCD, 6) ELSE LEFT(ADCD, 6) END SIMADCD
        FROM MDT_ADCDINFO_B
        ) A LEFT JOIN (
        SELECT SADCD,
        COUNT(DISTINCT (CASE WHEN RZFSLTDZ > 0 AND RZDSFLZ > 0 AND RZNORMZ > 0 AND OSFLG &lt;&gt; 1 THEN OBJID ELSE NULL END)) RZDSFLZ,
        COUNT(DISTINCT (CASE WHEN RZFSLTDZ > 0 AND (RZDSFLZ &lt;= 0 OR RZNORMZ &lt;= 0) AND OSFLG &lt;&gt; 1 THEN OBJID ELSE NULL END)) RZFSLTDZ,
        COUNT(DISTINCT (CASE WHEN RZFSLTDZ &lt;= 0 OR RZFSLTDZ IS NULL OR OSFLG = 1 THEN OBJID ELSE NULL END)) RZ
        FROM (
        SELECT A.STCD, A.TM, A.RZ, F.FSLTDZ, A.RZ - F.FSLTDZ RZFSLTDZ, A.RZ - FH.DSFLZ RZDSFLZ, A.RZ - FH.NORMZ RZNORMZ, OO.OBJID, STADTP.ADCD, STADTP.OSFLG, C.ADLVL,
        CASE WHEN LEFT(STADTP.ADCD, 6) = '220581' THEN '220581' ELSE
        <choose>
            <when test="map.level !=null and map.level =='4'.toString()">
                LEFT(STADTP.ADCD, 6)
            </when>
            <otherwise>
                LEFT(LEFT(STADTP.ADCD, 4) + '0000', 6)
            </otherwise>
        </choose>
        END SADCD
        from (
        SELECT STCD, MAX(TM) TM FROM ST_RSVR_R
        where tm>=CONVERT(datetime,#{map.stm}) and TM &lt;= CONVERT(datetime,#{map.etm})
        group by stcd
        ) T INNER JOIN ST_RSVR_R A ON A.STCD = T.STCD AND T.TM = A.TM
        LEFT JOIN ST_STBPRP_B ST ON A.STCD = ST.STCD
        LEFT JOIN BSN_OBJONLY_B OO ON A.STCD=OO.OBJCD AND OO.OBJTP=1
        INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
        LEFT JOIN ST_RSVRFSR_B F ON F.STCD = A.STCD AND right(CONVERT(VARCHAR,A.TM,112),4) BETWEEN BGMD AND EDMD
        LEFT JOIN ST_RSVRFCCH_B FH ON A.STCD = FH.STCD AND FH.RSVRTP IN ('1','2','3','4','5')
        LEFT JOIN bsn_adcd_b C ON STADTP.ADCD=C.ADCD
        WHERE left(STADTP.ADCD, #{map.level})=#{map.ad} and ST.STTP IN ('RR','RQ')
        <if test="map.stType !=null and  map.stType !=''">
            and CHARINDEX(STADTP,#{map.stType}) >0
        </if>
        <if test="map.rvtp!= null and map.rvtp != '' ">
            AND CHARINDEX(RSVRTP,#{map.rvtp}) >0
        </if>
        ) A GROUP BY SADCD
        ) B ON A.SIMADCD = B.SADCD
        WHERE A.PADCD = #{map.adcd}
    </select>
    <select id="getWaterBasRiverStatList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultWaterBasRiverVo">
        SELECT A.BAS_CODE, A.BAS_NAME, isnull(Z, 0) Z, isnull(ZWRZ, 0) ZWRZ, isnull(ZGRZ, 0) ZGRZ FROM (
           SELECT BAS_CODE,BAS_NAME FROM BSN_BAS_B A WHERE PBAS_CODE = #{map.basCode}
       ) A LEFT JOIN (
            SELECT BAS_CODE,BAS_NAME,
                   SUM(CASE WHEN ZWRZ &lt; 0 OR ZGRZ &lt; 0 OR ZWRZ IS NULL OR ZWRZ IS NULL THEN 1 ELSE 0 END) Z,
                   SUM(CASE WHEN ZWRZ > 0 THEN 1 ELSE 0 END) ZWRZ,
                   SUM(CASE WHEN ZGRZ > 0 THEN 1 ELSE 0 END) ZGRZ
            FROM (
                     SELECT A.STCD, A.TM, A.Z, FH.WRZ, A.Z - FH.WRZ AS ZWRZ, FH.GRZ, A.Z - FH.GRZ ZGRZ, BS.BAS_CODE,BS.BAS_NAME
                     from (
                              select stcd, max(tm) tm from ST_RIVER_R
                              where tm>=CONVERT(datetime,#{map.stm}) and TM &lt;= CONVERT(datetime,#{map.etm})
                              group by stcd
                          ) T INNER JOIN ST_RIVER_R A ON A.STCD = T.STCD and A.TM = T.tm
                              LEFT JOIN ST_STBPRP_B ST ON A.STCD = ST.STCD
                              INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
                              LEFT JOIN ST_RVFCCH_B FH on STADTP.STCD = FH.STCD
                              LEFT JOIN BSN_BAS_ST BST ON BST.STCD = STADTP.STCD
                              INNER JOIN BSN_BAS_B BS ON BS.BAS_CODE = BST.BAS_CODE
                     WHERE BS.PBAS_CODE = #{map.basCode} and CHARINDEX(ST.STTP,'ZZ,ZQ')>0
                 ) A GROUP BY BAS_CODE,BAS_NAME
        ) B ON A.BAS_CODE = B.BAS_CODE
    </select>
    <select id="getWaterBasZyjhRiverStatList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultWaterBasRiverVo">
        SELECT A.BAS_CODE, A.BAS_NAME, isnull(Z, 0) Z, isnull(ZWRZ, 0) ZWRZ, isnull(ZGRZ, 0) ZGRZ FROM (
           SELECT BAS_CODE,BAS_NAME,BAS_LEVEL FROM BSN_BAS_B A WHERE IS_ZYJH = '1'
       ) A LEFT JOIN (
            SELECT BAS_CODE,BAS_NAME,
                   SUM(CASE WHEN ZWRZ &lt; 0 OR ZGRZ &lt; 0 OR ZWRZ IS NULL OR ZWRZ IS NULL THEN 1 ELSE 0 END) Z,
                   SUM(CASE WHEN ZWRZ > 0 THEN 1 ELSE 0 END) ZWRZ,
                   SUM(CASE WHEN ZGRZ > 0 THEN 1 ELSE 0 END) ZGRZ
            FROM (
                 SELECT A.STCD, A.TM, A.Z, FH.WRZ, A.Z - FH.WRZ AS ZWRZ, FH.GRZ, A.Z - FH.GRZ ZGRZ, BS.BAS_CODE,BS.BAS_NAME
                 from (
                          select stcd, max(tm) tm from ST_RIVER_R
                          where tm>=CONVERT(datetime,#{map.stm}) and TM &lt;= CONVERT(datetime,#{map.etm})
                          group by stcd
                      ) T INNER JOIN ST_RIVER_R A ON A.STCD = T.STCD and A.TM = T.tm
                          LEFT JOIN ST_STBPRP_B ST ON A.STCD = ST.STCD
                          INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
                          LEFT JOIN ST_RVFCCH_B FH on STADTP.STCD = FH.STCD
                          LEFT JOIN BSN_BAS_ST BST ON BST.STCD = STADTP.STCD
                          INNER JOIN BSN_BAS_B BS ON BS.BAS_CODE = BST.BAS_CODE
                 WHERE BS.IS_ZYJH = '1' and CHARINDEX(ST.STTP,'ZZ,ZQ')>0
             ) A GROUP BY BAS_CODE,BAS_NAME
        ) B ON A.BAS_CODE = B.BAS_CODE
        ORDER BY A.BAS_LEVEL
    </select>
    <select id="getWaterBasRsvrStatList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultWaterBasRsvrVo">
        SELECT A.BAS_CODE, A.BAS_NAME, isnull(RZ,0) RZ, isnull(RZFSLTDZ,0) RZFSLTDZ, isnull(RZDSFLZ,0) RZDSFLZ FROM (
            SELECT BAS_CODE, BAS_NAME FROM BSN_BAS_B A WHERE PBAS_CODE = #{map.basCode}
        ) A LEFT JOIN (
            SELECT BAS_CODE,BAS_NAME,
            COUNT(DISTINCT (CASE WHEN RZFSLTDZ > 0 AND RZDSFLZ > 0 AND RZNORMZ > 0 AND OSFLG &lt;&gt; 1 THEN OBJID ELSE NULL END)) RZDSFLZ,
            COUNT(DISTINCT (CASE WHEN RZFSLTDZ > 0 AND (RZDSFLZ &lt;= 0 OR RZNORMZ &lt;= 0) AND OSFLG &lt;&gt; 1 THEN OBJID ELSE NULL END)) RZFSLTDZ,
            COUNT(DISTINCT (CASE WHEN RZFSLTDZ &lt;= 0 OR RZFSLTDZ IS NULL OR OSFLG = 1 THEN OBJID ELSE NULL END)) RZ
            FROM (
                SELECT A.STCD, T.TM, ST.STNM, RSVRTP, STADTP.STADTP, STADTP.OSFLG, A.RZ, F.FSLTDZ, A.RZ - F.FSLTDZ RZFSLTDZ, A.RZ - FH.DSFLZ RZDSFLZ, A.RZ - FH.NORMZ RZNORMZ, OO.OBJID, BS.BAS_CODE, BS.BAS_NAME
                from (
                    SELECT STCD, MAX(TM) TM FROM ST_RSVR_R
                    where tm>=CONVERT(datetime,#{map.stm}) and TM &lt;= CONVERT(datetime,#{map.etm})
                    group by stcd
                ) T INNER JOIN ST_RSVR_R A ON A.STCD = T.STCD AND T.TM = A.TM
                LEFT JOIN ST_STBPRP_B ST ON A.STCD = ST.STCD
                LEFT JOIN BSN_OBJONLY_B OO ON A.STCD=OO.OBJCD AND OO.OBJTP=1
                INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
                LEFT JOIN ST_RSVRFSR_B F ON F.STCD = A.STCD AND right(CONVERT(VARCHAR,A.TM,112),4) BETWEEN BGMD AND EDMD
                LEFT JOIN ST_RSVRFCCH_B FH ON A.STCD = FH.STCD AND FH.RSVRTP IN ('1','2','3','4','5')
                LEFT JOIN BSN_BAS_ST BST ON BST.STCD = STADTP.STCD
                INNER JOIN BSN_BAS_B BS ON BS.BAS_CODE = BST.BAS_CODE
                WHERE BS.PBAS_CODE = #{map.basCode} and ST.STTP IN ('RR','RQ')
                <if test="map.stType !=null and  map.stType !=''">
                    AND CHARINDEX(STADTP,#{map.stType}) > 0
                </if>
                <if test="map.rvtp!= null and map.rvtp != '' ">
                    AND CHARINDEX(RSVRTP,#{map.rvtp}) > 0
                </if>
            ) A GROUP BY BAS_CODE, BAS_NAME
        ) B ON A.BAS_CODE = B.BAS_CODE
    </select>
    <select id="getWaterBasZyjhRsvrStatList"
            resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultWaterBasRsvrVo">
        SELECT A.BAS_CODE, A.BAS_NAME, isnull(RZ,0) RZ, isnull(RZFSLTDZ,0) RZFSLTDZ, isnull(RZDSFLZ,0) RZDSFLZ FROM (
            SELECT BAS_CODE, BAS_NAME, BAS_LEVEL FROM BSN_BAS_B A WHERE IS_ZYJH = '1'
        ) A LEFT JOIN (
            SELECT BAS_CODE,BAS_NAME,
            COUNT(DISTINCT (CASE WHEN RZFSLTDZ > 0 AND RZDSFLZ > 0 AND RZNORMZ > 0 AND OSFLG &lt;&gt; 1 THEN OBJID ELSE NULL END)) RZDSFLZ,
            COUNT(DISTINCT (CASE WHEN RZFSLTDZ > 0 AND (RZDSFLZ &lt;= 0 OR RZNORMZ &lt;= 0) AND OSFLG &lt;&gt; 1 THEN OBJID ELSE NULL END)) RZFSLTDZ,
            COUNT(DISTINCT (CASE WHEN RZFSLTDZ &lt;= 0 OR RZFSLTDZ IS NULL OR OSFLG = 1 THEN OBJID ELSE NULL END)) RZ
            FROM (
                SELECT A.STCD, T.TM, ST.STNM, RSVRTP, STADTP.STADTP, STADTP.OSFLG, A.RZ, F.FSLTDZ, A.RZ - F.FSLTDZ RZFSLTDZ, A.RZ - FH.DSFLZ RZDSFLZ, A.RZ - FH.NORMZ RZNORMZ, OO.OBJID, BS.BAS_CODE, BS.BAS_NAME
                from (
                    SELECT STCD, MAX(TM) TM FROM ST_RSVR_R
                    where tm>=CONVERT(datetime,#{map.stm}) and TM &lt;= CONVERT(datetime,#{map.etm})
                    group by stcd
                ) T INNER JOIN ST_RSVR_R A ON A.STCD = T.STCD AND T.TM = A.TM
                LEFT JOIN ST_STBPRP_B ST ON A.STCD = ST.STCD
                LEFT JOIN BSN_OBJONLY_B OO ON A.STCD=OO.OBJCD AND OO.OBJTP=1
                INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
                LEFT JOIN ST_RSVRFSR_B F ON F.STCD = A.STCD AND right(CONVERT(VARCHAR,A.TM,112),4) BETWEEN BGMD AND EDMD
                LEFT JOIN ST_RSVRFCCH_B FH ON A.STCD = FH.STCD AND FH.RSVRTP IN ('1','2','3','4','5')
                LEFT JOIN BSN_BAS_ST BST ON BST.STCD = STADTP.STCD
                INNER JOIN BSN_BAS_B BS ON BS.BAS_CODE = BST.BAS_CODE
                WHERE BS.IS_ZYJH = '1' and ST.STTP IN ('RR','RQ')
                <if test="map.stType !=null and  map.stType !=''">
                    and CHARINDEX(STADTP,#{map.stType}) >0
                </if>
                <if test="map.rvtp!= null and map.rvtp != '' ">
                    AND CHARINDEX(RSVRTP,#{map.rvtp}) >0
                </if>
            ) A GROUP BY BAS_CODE, BAS_NAME
        ) B ON A.BAS_CODE = B.BAS_CODE
        ORDER BY A.BAS_LEVEL
    </select>
    <select id="getWarnAdShStatList" resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultWarnAdVo">
        SELECT A.ADCD, A.ADNM, isnull(B.TYPE1, 0) TYPE1, isnull(B.TYPE2, 0) TYPE2 FROM MDT_ADCDINFO_B A LEFT JOIN (
        SELECT SADCD, SUM(CASE WHEN WARNGRADEID = '4' THEN 1 ELSE 0 END) TYPE1, SUM(CASE WHEN WARNGRADEID = '5' THEN 1 ELSE 0 END) TYPE2 FROM (
        SELECT A.WARNID, A.WARNGRADEID,CASE WHEN LEFT(A.ADCD, 6) = '220581' THEN '220581' ELSE
        <choose>
            <when test="map.level !=null and map.level =='4'.toString()">
                LEFT(LEFT(A.ADCD, 6) + '0000', 6)
            </when>
            <otherwise>
                LEFT(LEFT(A.ADCD, 4) + '0000', 6)
            </otherwise>
        </choose>
        END SADCD
        FROM WARNRECORD_R A
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
        WHERE A.WARNTYPEID = 10 AND left(A.ADCD, #{map.level})=#{map.ad}
        AND A.WARNSTM >= CONVERT(datetime,#{map.stm}) AND A.WARNSTM &lt;= CONVERT(datetime,#{map.etm})
        ) A GROUP BY SADCD
        ) B ON LEFT(A.ADCD, 6) = B.SADCD
        WHERE A.PADCD = #{map.adcd}
    </select>
    <select id="getWarnAdRiverStatList" resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultWarnAdVo">
        SELECT A.ADCD, A.ADNM, isnull(B.TYPE1, 0) TYPE1, isnull(B.TYPE2, 0) TYPE2 FROM MDT_ADCDINFO_B A LEFT JOIN (
        SELECT SADCD, SUM(CASE WHEN WGRD = '1' THEN 1 ELSE 0 END) TYPE1, SUM(CASE WHEN WGRD = '2' THEN 1 ELSE 0 END) TYPE2 FROM (
        SELECT A.WARNID, A.WGRD,CASE WHEN LEFT(B.ADCD, 6) = '220581' THEN '220581' ELSE
        <choose>
            <when test="map.level !=null and map.level =='4'.toString()">
                LEFT(LEFT(B.ADCD, 6) + '0000', 6)
            </when>
            <otherwise>
                LEFT(LEFT(B.ADCD, 4) + '0000', 6)
            </otherwise>
        </choose>
        END SADCD
        FROM BSN_RIVER_RISKWARN_R A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        WHERE left(B.ADCD, #{map.level})=#{map.ad}
        AND A.WTM >= CONVERT(datetime,#{map.stm}) AND A.WTM &lt;= CONVERT(datetime,#{map.etm})
        ) A GROUP BY SADCD
        ) B ON LEFT(A.ADCD, 6) = B.SADCD
        WHERE A.PADCD = #{map.adcd}
    </select>
    <select id="getWarnAdRsvrStatList" resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultWarnAdVo">
        SELECT A.ADCD, A.ADNM, isnull(B.TYPE1, 0) TYPE1, isnull(B.TYPE2, 0) TYPE2, isnull(B.TYPE3, 0) TYPE3, isnull(B.TYPE4, 0) TYPE4 FROM MDT_ADCDINFO_B A LEFT JOIN (
        SELECT SADCD, SUM(CASE WHEN WGRD = '1' THEN 1 ELSE 0 END) TYPE1, SUM(CASE WHEN WGRD = '2' THEN 1 ELSE 0 END) TYPE2
        , SUM(CASE WHEN WGRD = '3' THEN 1 ELSE 0 END) TYPE3, SUM(CASE WHEN WGRD = '4' THEN 1 ELSE 0 END) TYPE4 FROM (
        SELECT A.WARNID, A.WGRD, CASE WHEN LEFT(B.ADCD, 6) = '220581' THEN '220581' ELSE
        <choose>
            <when test="map.level !=null and map.level =='4'.toString()">
                LEFT(LEFT(B.ADCD, 6) + '0000', 6)
            </when>
            <otherwise>
                LEFT(LEFT(B.ADCD, 4) + '0000', 6)
            </otherwise>
        </choose>
        END SADCD
        FROM BSN_RSVR_RISKWARN_R A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
        WHERE left(B.ADCD, #{map.level})=#{map.ad}
        AND A.WTM >= CONVERT(datetime,#{map.stm}) AND A.WTM &lt;= CONVERT(datetime,#{map.etm})
        AND H.RES_CODE IS NOT NULL
        ) A GROUP BY SADCD
        ) B ON LEFT(A.ADCD, 6) = B.SADCD
        WHERE A.PADCD = #{map.adcd}
    </select>

    <select id="getRiskAdRiverStatList" resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultRiskAdRiverVo">
        SELECT ADCD, ADNM, isnull(Z, 0) Z, isnull(ZWRZ, 0) ZWRZ, isnull(ZGRZ, 0) ZGRZ FROM (
            SELECT ADCD, ADNM, ADLVL, PADCD,
            CASE WHEN ADLVL = 2 AND ADCD = '220581000000000' THEN LEFT(ADCD, 6) ELSE LEFT(ADCD, 6) END SIMADCD
            FROM MDT_ADCDINFO_B
        ) A LEFT JOIN (
            SELECT SADCD,
            SUM(CASE WHEN ZWRZ &lt; 0 OR ZGRZ &lt; 0 OR ZWRZ IS NULL OR ZWRZ IS NULL THEN 1 ELSE 0 END) Z,
            SUM(CASE WHEN ZWRZ > 0 THEN 1 ELSE 0 END) ZWRZ,
            SUM(CASE WHEN ZGRZ > 0 THEN 1 ELSE 0 END) ZGRZ
            FROM (
                SELECT CASE WHEN LEFT(STADTP.ADCD, 6) = '220581' THEN '220581' ELSE
                <choose>
                    <when test="map.level !=null and map.level =='4'.toString()">
                        LEFT(STADTP.ADCD, 6)
                    </when>
                    <otherwise>
                        LEFT(LEFT(STADTP.ADCD, 4) + '0000', 6)
                    </otherwise>
                </choose>
                END SADCD, a.STCD, a.Z - fh.WRZ ZWRZ, A.Z - FH.GRZ ZGRZ
                FROM (
                    select STCD ,UNITNAME,PLCD,FYMDH,IYMDH,YMDH,Z,Q,DENSE_RANK() over(partition by STCD order by IYMDH desc,Z desc ,YMDH) rank
                    from ST_FORECAST_F where 1=1 and YMDH > getdate()
                ) a
                LEFT JOIN ST_STBPRP_B ST ON A.STCD = ST.STCD
                INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
                LEFT JOIN ST_RVFCCH_B FH on STADTP.STCD = FH.STCD
                LEFT JOIN bsn_adcd_b C ON STADTP.ADCD=C.ADCD
                WHERE a.rank=1 AND CHARINDEX(ST.STTP,'ZZ,ZQ')>0 and left(STADTP.ADCD, #{map.level})=#{map.ad}
            ) A GROUP BY SADCD
        ) B ON A.SIMADCD = B.SADCD
        WHERE A.PADCD = #{map.adcd}
    </select>

    <select id="getRiskAdRsvrStatList" resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultRiskAdRsvrVo">
        SELECT ADCD, ADNM, isnull(RZ, 0) RZ, isnull(RZFSLTDZ, 0) RZFSLTDZ, isnull(DSFLZ, 0) DSFLZ FROM (
            SELECT ADCD, ADNM, ADLVL, PADCD,
            CASE WHEN ADLVL = 2 AND ADCD = '220581000000000' THEN LEFT(ADCD, 6) ELSE LEFT(ADCD, 6) END SIMADCD
            FROM MDT_ADCDINFO_B
        ) A LEFT JOIN (
            SELECT SADCD,
            SUM(CASE WHEN RZFSLTDZ &lt; 0 OR RZFSLTDZ IS NULL THEN 1 ELSE 0 END) RZ,
            SUM(CASE WHEN RZFSLTDZ > 0 THEN 1 ELSE 0 END) RZFSLTDZ,
            SUM(CASE WHEN RZFSLTDZ > 0 AND Z > DSFLZ THEN 1 ELSE 0 END) DSFLZ
            FROM (
                SELECT CASE WHEN LEFT(STADTP.ADCD, 6) = '220581' THEN '220581' ELSE
                <choose>
                    <when test="map.level !=null and map.level =='4'.toString()">
                        LEFT(STADTP.ADCD, 6)
                    </when>
                    <otherwise>
                        LEFT(LEFT(STADTP.ADCD, 4) + '0000', 6)
                    </otherwise>
                </choose>
                END SADCD, c.STCD, c.Z, F.FSLTDZ, C.Z - F.FSLTDZ RZFSLTDZ ,FH.DSFLZ, CASE WHEN FH.RSVRTP = '4' or FH.RSVRTP = '5' THEN '5' ELSE FH.RSVRTP END RSVRTP
                FROM (
                    select * from (
                        SELECT E.*, ROW_NUMBER () OVER (PARTITION BY E .STCD ORDER BY E .IYMDH DESC,E .Z DESC,E .YMDH) rn,
                        CAST ( SUBSTRING(CONVERT(VARCHAR,YMDH,112),5,8) AS int ) NTM FROM ST_REGLAT_F E where 1=1 and YMDH > getdate()
                    ) aa WHERE aa.rn = 1
                ) c left JOIN ST_FORECASTC_F D ON D.stcd = c.stcd AND D.UNITNAME = c.UNITNAME AND D.FYMDH = c.FYMDH AND D.IYMDH = c.IYMDH AND D.PLCD = c.PLCD
                LEFT JOIN ST_STBPRP_B ST ON c.STCD = ST.STCD
                INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
                LEFT JOIN ST_RSVRFSR_B F ON F.STCD = c.STCD AND NTM >= CAST (BGMD AS int) AND NTM &lt;= CAST (EDMD AS int)
                LEFT JOIN ST_RSVRFCCH_B FH ON c.STCD = FH.STCD and CHARINDEX(fh.RSVRTP,'4,5,3,2,1')>0
                LEFT JOIN bsn_adcd_b e ON STADTP.ADCD=e.ADCD
                WHERE left(STADTP.ADCD, #{map.level})=#{map.ad} and CHARINDEX(ST.STTP,'RR,RQ')>0
                <if test="map.stType !=null and  map.stType !=''">
                    and CHARINDEX(STADTP,#{map.stType}) >0
                </if>
                <if test="map.rvtp!= null and map.rvtp != '' ">
                    AND CHARINDEX(RSVRTP,#{map.rvtp}) >0
                </if>
            ) A GROUP BY SADCD
        ) B ON A.SIMADCD = B.SADCD
        WHERE A.PADCD = #{map.adcd}
    </select>

    <select id="getRiskBasRiverStatList" resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultRiskBasRiverVo">
        SELECT A.BAS_CODE, A.BAS_NAME, isnull(Z, 0) Z, isnull(ZWRZ, 0) ZWRZ, isnull(ZGRZ, 0) ZGRZ FROM (
            SELECT BAS_CODE,BAS_NAME FROM BSN_BAS_B A WHERE PBAS_CODE = #{map.basCode}
        ) A LEFT JOIN (
            SELECT BAS_CODE,BAS_NAME,
            SUM(CASE WHEN ZWRZ &lt; 0 OR ZGRZ &lt; 0 OR ZWRZ IS NULL OR ZWRZ IS NULL THEN 1 ELSE 0 END) Z,
            SUM(CASE WHEN ZWRZ > 0 THEN 1 ELSE 0 END) ZWRZ,
            SUM(CASE WHEN ZGRZ > 0 THEN 1 ELSE 0 END) ZGRZ
            FROM (
                SELECT BS.BAS_CODE,BS.BAS_NAME, a.STCD, a.Z - fh.WRZ ZWRZ, A.Z - FH.GRZ ZGRZ
                FROM (
                    select STCD ,UNITNAME,PLCD,FYMDH,IYMDH,YMDH,Z,Q,DENSE_RANK() over(partition by STCD order by IYMDH desc,Z desc ,YMDH) rank
                    from ST_FORECAST_F where 1=1 and YMDH > getdate()
                ) a
                LEFT JOIN ST_STBPRP_B ST ON A.STCD = ST.STCD
                INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
                LEFT JOIN ST_RVFCCH_B FH on STADTP.STCD = FH.STCD
                LEFT JOIN BSN_BAS_ST BST ON BST.STCD = STADTP.STCD
                INNER JOIN BSN_BAS_B BS ON BS.BAS_CODE = BST.BAS_CODE
                WHERE a.rank=1 AND BS.PBAS_CODE = #{map.basCode} and CHARINDEX(ST.STTP,'ZZ,ZQ')>0
            ) A GROUP BY BAS_CODE,BAS_NAME
        ) B ON A.BAS_CODE = B.BAS_CODE
    </select>

    <select id="getRiskBasRsvrStatList" resultType="com.huitu.cloud.api.ewci.consult.entity.ConsultRiskBasRsvrVo">
        SELECT A.BAS_CODE, A.BAS_NAME, isnull(RZ, 0) RZ, isnull(RZFSLTDZ, 0) RZFSLTDZ,isnull(DSFLZ, 0) DSFLZ FROM (
            SELECT BAS_CODE,BAS_NAME FROM BSN_BAS_B A WHERE PBAS_CODE = #{map.basCode}
        ) A LEFT JOIN (
            SELECT BAS_CODE,BAS_NAME,
            SUM(CASE WHEN RZFSLTDZ &lt; 0 OR RZFSLTDZ IS NULL THEN 1 ELSE 0 END) RZ,
            SUM(CASE WHEN RZFSLTDZ > 0 THEN 1 ELSE 0 END) RZFSLTDZ,
            SUM(CASE WHEN RZFSLTDZ > 0 AND Z > DSFLZ THEN 1 ELSE 0 END) DSFLZ
            FROM (
                SELECT BS.BAS_CODE,BS.BAS_NAME, c.STCD, c.Z, F.FSLTDZ, C.Z - F.FSLTDZ RZFSLTDZ,FH.DSFLZ , CASE WHEN FH.RSVRTP = '4' or FH.RSVRTP = '5' THEN '5' ELSE FH.RSVRTP END RSVRTP
                FROM (
                    select * from (
                        SELECT E.*, ROW_NUMBER () OVER (PARTITION BY E .STCD ORDER BY E .IYMDH DESC,E .Z DESC,E .YMDH) rn,
                        CAST ( SUBSTRING(CONVERT(VARCHAR,YMDH,112),5,8) AS int ) NTM FROM ST_REGLAT_F E where 1=1 and YMDH > getdate()
                    ) aa WHERE aa.rn = 1
                ) c left JOIN ST_FORECASTC_F D ON D.stcd = c.stcd AND D.UNITNAME = c.UNITNAME AND D.FYMDH = c.FYMDH AND D.IYMDH = c.IYMDH AND D.PLCD = c.PLCD
                LEFT JOIN ST_STBPRP_B ST ON c.STCD = ST.STCD
                INNER JOIN bsn_stadtp_b STADTP on ST.stcd = STADTP.stcd
                LEFT JOIN ST_RSVRFSR_B F ON F.STCD = c.STCD AND NTM >= CAST (BGMD AS int) AND NTM &lt;= CAST (EDMD AS int)
                LEFT JOIN ST_RSVRFCCH_B FH ON c.STCD = FH.STCD and CHARINDEX(fh.RSVRTP,'4,5,3,2,1')>0
                LEFT JOIN BSN_BAS_ST BST ON BST.STCD = STADTP.STCD
                INNER JOIN BSN_BAS_B BS ON BS.BAS_CODE = BST.BAS_CODE
                WHERE BS.PBAS_CODE = #{map.basCode} and CHARINDEX(ST.STTP,'RR,RQ')>0
                <if test="map.stType !=null and  map.stType !=''">
                    and CHARINDEX(STADTP,#{map.stType}) >0
                </if>
                <if test="map.rvtp!= null and map.rvtp != '' ">
                    AND CHARINDEX(RSVRTP,#{map.rvtp}) >0
                </if>
            ) A GROUP BY BAS_CODE,BAS_NAME
        ) B ON A.BAS_CODE = B.BAS_CODE
    </select>

    <select id="getMaxReportNo" resultType="com.huitu.cloud.api.ewci.consult.entity.BsnDefenseWorkPlanB">
        SELECT ISNULL(MAX(Y_REPORT_NO), 0) Y_REPORT_NO, ISNULL(MAX(TOTAL_NO), 0) TOTAL_NO
        FROM BSN_DEFENSE_WORK_PLAN_B
        WHERE REPORT_YEAR = YEAR (GETDATE())
    </select>

    <insert id="insert">
        INSERT INTO BSN_DEFENSE_WORK_PLAN_B (DWP_ID, DWP_NAME, DWP_TM, Y_REPORT_NO, TOTAL_NO, REPORT_YEAR, STATUS, SEND_TIME)
        VALUES (#{dwpId}, #{dwpName}, #{dwpTm}, #{yReportNo}, #{totalNo}, #{reportYear}, #{status}, #{sendTime})
    </insert>

    <update id="update">
        UPDATE BSN_DEFENSE_WORK_PLAN_B
        <set>
            <if test="yReportNo != null and yReportNo !=''">
                Y_REPORT_NO = #{yReportNo},
            </if>
            <if test="totalNo != null and totalNo !=''">
                TOTAL_NO = #{totalNo},
            </if>
        </set>
        WHERE DWP_ID = #{dwpId} AND STATUS = '0'
    </update>
    <update id="updateReportUrl">
        UPDATE BSN_DEFENSE_WORK_PLAN_B
        SET FILE_PATH = #{filePath},
            FILE_NAME = #{fileName}
        WHERE DWP_ID = #{dwpId}
          AND STATUS = '0'
    </update>

    <select id="getFileById" resultType="com.huitu.cloud.api.ewci.consult.entity.BsnDefenseWorkPlanB">
        SELECT DWP_ID, DWP_NAME, FILE_NAME, FILE_PATH, DWP_TM, Y_REPORT_NO, TOTAL_NO, REPORT_YEAR, STATUS, SEND_TIME, TS
        FROM BSN_DEFENSE_WORK_PLAN_B A
        WHERE DWP_ID = #{dwpId}
    </select>

    <select id="getDocument" resultType="com.huitu.cloud.api.ewci.consult.entity.BsnDefenseWorkPlanB">
        SELECT DWP_ID, FILE_NAME, FILE_PATH
        FROM BSN_DEFENSE_WORK_PLAN_B
        WHERE DWP_ID = #{dwpId}
    </select>

    <select id="isRepeatFileNo" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(A.DWP_ID) > 0 THEN 1 ELSE 0 END
        FROM BSN_DEFENSE_WORK_PLAN_B A
        WHERE A.[STATUS] = '1' AND A.DWP_ID != #{dwpId}
          AND EXISTS(SELECT 8 FROM BSN_DEFENSE_WORK_PLAN_B WHERE DWP_ID = #{dwpId} AND Y_REPORT_NO = A.Y_REPORT_NO AND TOTAL_NO = A.TOTAL_NO)
    </select>
    <select id="getPage" resultType="com.huitu.cloud.api.ewci.consult.entity.BsnDefenseWorkPlanB">
        SELECT DWP_ID, DWP_NAME, FILE_NAME, FILE_PATH, DWP_TM, Y_REPORT_NO, TOTAL_NO, REPORT_YEAR, STATUS, SEND_TIME, TS
        FROM BSN_DEFENSE_WORK_PLAN_B
        <where>
            <if test="map.etm !=null and map.etm !=''">
                and CONVERT(DATE,TS) &lt;= CONVERT(DATE,#{map.etm})
            </if>
            <if test="map.stm !=null and map.stm !=''">
                and CONVERT(DATE,TS) >= CONVERT(DATE,#{map.stm})
            </if>
            <if test="map.name !=null and  map.name !=''">
                AND CHARINDEX(#{dwpName},DWP_NAME) >0
            </if>
        </where>
        ORDER BY TS DESC
    </select>

    <update id="updateStatus">
        UPDATE BSN_DEFENSE_WORK_PLAN_B
        SET STATUS = '1',SEND_TIME = getdate()
        WHERE DWP_ID = #{dwpId}
          AND STATUS = '0'
    </update>
</mapper>