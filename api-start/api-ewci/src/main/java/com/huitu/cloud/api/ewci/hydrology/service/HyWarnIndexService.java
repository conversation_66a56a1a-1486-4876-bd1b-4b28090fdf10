package com.huitu.cloud.api.ewci.hydrology.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnStationResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.query.HyWarnIndexQuery;
import com.huitu.cloud.api.ewci.hydrology.entity.request.HyWarnIndexRequest;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnIndexResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnUnitResponse;

import java.util.List;

/**
 * 洪水预警指标服务
 *
 * <AUTHOR>
 */
public interface HyWarnIndexService {

    /**
     * 获取预警站列表
     *
     * @param adcd    政区代码
     * @param include 是否包含已设置预警指标的预警站
     * @return 预警站列表
     */
    List<HyWarnStationResponse> getStationList(String adcd, boolean include);

    /**
     * 获取预警单位列表
     *
     * @return 单位列表
     */
    List<HyWarnUnitResponse> getUnitList();

    /**
     * 分页获取预警指标列表
     *
     * @param query 查询参数
     * @return 指标列表
     */
    IPage<HyWarnIndexResponse> getPageList(HyWarnIndexQuery query);

    /**
     * 保存洪水预警指标
     *
     * @param request 请求对象
     * @return 受影响的行数
     */
    int save(HyWarnIndexRequest request);

    /**
     * 删除洪水预警指标
     *
     * @param stcd 测站编码
     * @return 受影响的行数
     */
    int delete(String stcd);

    /**
     * 更新启用标识
     *
     * @param stcd 测站编码
     * @param usfl 启用标识
     * @return 受影响的行数
     */
    int updateUseFlag(String stcd, String usfl);
}
