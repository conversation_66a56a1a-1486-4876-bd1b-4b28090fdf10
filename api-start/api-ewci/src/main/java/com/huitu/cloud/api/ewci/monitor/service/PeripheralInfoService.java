package com.huitu.cloud.api.ewci.monitor.service;

import com.huitu.cloud.api.ewci.monitor.entity.RainData;
import com.huitu.cloud.api.ewci.monitor.entity.RealtimeQuery;
import com.huitu.cloud.api.ewci.monitor.entity.RiverData;
import com.huitu.cloud.api.ewci.monitor.entity.RsvrData;

import java.util.List;

/**
 * 周边信息服务
 *
 * <AUTHOR>
 */
public interface PeripheralInfoService {

    /**
     * 获取降雨数据列表
     *
     * @param query 实时数据查询条件
     * @return 降雨数据列表
     **/
    List<RainData> getRainDataList(RealtimeQuery query);

    /**
     * 获取水库数据列表
     *
     * @param query 实时数据查询条件
     * @return 水库数据列表
     **/
    List<RsvrData> getRsvrDataList(RealtimeQuery query);

    /**
     * 获取河道数据列表
     *
     * @param query 实时数据查询条件
     * @return 河道数据列表
     **/
    List<RiverData> getRiverDataList(RealtimeQuery query);
}
