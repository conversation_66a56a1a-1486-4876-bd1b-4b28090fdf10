package com.huitu.cloud.api.ewci.feedback.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBack;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackPic;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackQuery;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackVo;

import java.util.List;

/**
 * 意见反馈信息
 *
 * <AUTHOR>
 */
public interface BnsFeedBackService {

    /**
     * 意见反馈信息分页查询
     *
     * @param query
     * @return
     */
    IPage<BnsFeedBackVo> getPage(BnsFeedBackQuery query);

    /**
     * 添加意见反馈
     *
     * @param entity
     * @return
     */
    int insertFeedBack(BnsFeedBack entity);

    /**
     * 处理意见反馈
     * @param entity
     * @return
     */
    int handleFeedBack(BnsFeedBack entity);

    /**
     * 意见反馈详情查看图片
     * @param id
     * @return
     */
    List<BnsFeedBackPic> getFeedBackPicList(String id);
}
