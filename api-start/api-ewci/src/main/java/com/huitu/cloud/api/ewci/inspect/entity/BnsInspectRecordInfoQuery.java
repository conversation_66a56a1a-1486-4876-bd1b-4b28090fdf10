package com.huitu.cloud.api.ewci.inspect.entity;

import com.huitu.cloud.validation.constraints.StringLength;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 检查记录详情参数
 */
@ApiModel(value = "检查记录详情参数")
public class BnsInspectRecordInfoQuery implements Serializable {

    @ApiModelProperty(value = "检查类型编码")
    @NotBlank(message = "参数[检查类型编码]不能为空")
    @StringLength(max = 50, message = "参数[检查类型编码]的长度不能超过50个字符")
    private String inspectCode;

    @ApiModelProperty(value = "检查ID")
    @NotBlank(message = "参数[检查ID]不能为空")
    @StringLength(min = 36, max = 36, message = "参数[检查ID]的长度应为36个字符")
    private String inspectId;

    public String getInspectCode() {
        return inspectCode;
    }

    public void setInspectCode(String inspectCode) {
        this.inspectCode = inspectCode;
    }
    public String getInspectId() {
        return inspectId;
    }

    public void setInspectId(String inspectId) {
        this.inspectId = inspectId;
    }


}
