package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 属性
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@TableName("Dic_ShuXing")
@ApiModel(value="DicShuxing对象", description="属性")
public class DicShuxing extends Model<DicShuxing> {

    @ApiModelProperty(value = "属性编码（溃口序号）")
    private Integer shuxingid;

    @ApiModelProperty(value = "属性值")
    private String breachinfo;

    public Integer getShuxingid() {
        return shuxingid;
    }

    public void setShuxingid(Integer shuxingid) {
        this.shuxingid = shuxingid;
    }

    public String getBreachinfo() {
        return breachinfo;
    }

    public void setBreachinfo(String breachinfo) {
        this.breachinfo = breachinfo;
    }
}
