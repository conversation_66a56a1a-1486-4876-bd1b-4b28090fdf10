package com.huitu.cloud.api.ewci.monitor.service.impl;

import com.huitu.cloud.api.ewci.monitor.entity.RainData;
import com.huitu.cloud.api.ewci.monitor.entity.RealtimeQuery;
import com.huitu.cloud.api.ewci.monitor.entity.RiverData;
import com.huitu.cloud.api.ewci.monitor.entity.RsvrData;
import com.huitu.cloud.api.ewci.monitor.mapper.PeripheralInfoDao;
import com.huitu.cloud.api.ewci.monitor.service.PeripheralInfoService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 周边信息服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class PeripheralInfoServiceImpl implements PeripheralInfoService {

    private PeripheralInfoDao baseDao;

    @Autowired
    public void setBaseDao(PeripheralInfoDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public List<RainData> getRainDataList(RealtimeQuery query) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", query.getType());
        params.put("stcd", query.getStcd());
        params.put("adcd", query.getAdcd());
        params.put("stm", query.getStm());
        params.put("etm", query.getEtm());
        // 预报时间，开始时间默认为实时数据筛选条件中的结束时间
        params.put("fstm", query.getEtm());
        params.put("fetm1", DateUtils.addHours(query.getEtm(), 1));
        params.put("fetm3", DateUtils.addHours(query.getEtm(), 3));
        params.put("fetm6", DateUtils.addHours(query.getEtm(), 6));
        return baseDao.getRainDataList(params);
    }

    @Override
    public List<RsvrData> getRsvrDataList(RealtimeQuery query) {
        Map<String, Object> params = new HashMap<>();
        params.put("stm", DateFormatUtils.format(query.getStm(), "yyyy-MM-dd HH:mm"));
        params.put("etm", DateFormatUtils.format(query.getEtm(), "yyyy-MM-dd HH:mm"));
        String stcdSql;
        if ("ws".equalsIgnoreCase(query.getType())) {
            stcdSql = "SELECT DISTINCT ST.STCD FROM BSN_STBPRP_V ST " +
                    "LEFT JOIN IA_C_WSADCD AD ON AD.ADCD = ST.ADCD " +
                    "LEFT JOIN IA_C_WSADCD WS ON WS.WSCD = AD.WSCD " +
                    "WHERE ST.STTP IN ('RR', 'RQ') AND ST.FRGRD != '8' AND ST.STCD != '" + query.getStcd() + "' AND WS.ADCD = '" + query.getAdcd() + "'";
        } else {
            stcdSql = "SELECT DISTINCT STCD FROM BSN_STBPRP_V " +
                    "WHERE STTP IN ('RR', 'RQ') AND FRGRD != '8' AND STCD != '" + query.getStcd() + "' AND LEFT(ADCD, 12) = LEFT('" + query.getAdcd() + "', 12)";
        }
        params.put("whereSql", "AND AA.STCD IN (" + stcdSql + ")");
        return baseDao.getRsvrDataList(params);
    }

    @Override
    public List<RiverData> getRiverDataList(RealtimeQuery query) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", query.getType());
        params.put("stcd", query.getStcd());
        params.put("adcd", query.getAdcd());
        params.put("stm", query.getStm());
        params.put("etm", query.getEtm());
        return baseDao.getRiverDataList(params);
    }
}
