package com.huitu.cloud.api.ewci.monitor.mapper;

import com.huitu.cloud.api.ewci.monitor.entity.IaCDanad;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 危险区基本情况调查成果汇总Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface IaCDanadDao {

    /**
     * 获取危险区基本情况调查成果汇总列表
     *
     * @param params 查询参数
     * @return 汇总列表
     **/
    List<IaCDanad> getDanadList(@Param("map") Map<String, Object> params);
}
