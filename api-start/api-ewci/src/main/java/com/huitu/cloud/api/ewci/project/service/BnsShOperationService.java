package com.huitu.cloud.api.ewci.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.ewci.project.entity.BnsShOperation;
import com.huitu.cloud.api.ewci.project.entity.BnsShProject;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 山洪灾害项目运行效益情况和建设进度情况统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
public interface BnsShOperationService extends IService<BnsShOperation> {

    /**
     * 添加或修改
     * @param bnsShOperation
     * @return
     */
    Boolean addorupdate(BnsShOperation bnsShOperation);

    /**
     * "运行效益情况汇总
     * @return
     */
    boolean summary();

    /**
     * 查询山洪灾害项目运行效益情况列表
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<BnsShOperation> getBenefitData(String adcd,String stm,String etm,String bno, Integer type,String year);

    /**
     * 查询山洪灾害项目运行效益情况当前时间最近的一条记录
     * @param adcd
     * @return
     */
    List<BnsShOperation> getCurrentTimeData(String adcd);

    /**
     * 查询批次列表
     * @param adcd
     * @return
     */
    List<BnsShOperation> getBnoList(String adcd);

    /**
     * 查询建设（运行维护）进度情况列表
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<BnsShProject> getrojectData(String adcd,String stm,String etm,String bno, String year,Integer type);

    /**
     * 查询批次列表
     * @param adcd
     * @return
     */
    List<BnsShProject> getrojectList(String adcd);

    /**
     * 添加或修改
     * @param bnsShProject
     * @return
     */
    Boolean updateRojectBRoject(BnsShProject bnsShProject);

    /**
     * "运行建设（运行维护）进度情况汇总
     * @return
     */
    Boolean summaryRojectB();

    /**
     * 查询行建设（运行维护）进度情况当前时间最近的一条记录
     * @param adcd
     * @return
     */
    List<BnsShProject> getCurrentOneData(String adcd);

}
