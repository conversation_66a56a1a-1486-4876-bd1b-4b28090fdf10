package com.huitu.cloud.api.ewci.person.exception;

import com.huitu.cloud.api.ewci.person.entity.BnsVillagePersonTmp;

import java.util.List;

/**
 * 水库淹没范围责任人信息导入异常
 *
 * <AUTHOR>
 */
public class VillagePersonImportException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    List<BnsVillagePersonTmp> errorData;

    public List<BnsVillagePersonTmp> getErrorData() {
        return errorData;
    }

    public void setErrorData(List<BnsVillagePersonTmp> errorData) {
        this.errorData = errorData;
    }

    public VillagePersonImportException(String message) {
        super(message);
    }

    public VillagePersonImportException(String message, List<BnsVillagePersonTmp> errorData) {
        super(message);

        this.errorData = errorData;
    }

    public VillagePersonImportException(String message, Throwable cause) {
        super(message, cause);
    }

    public VillagePersonImportException(String message, List<BnsVillagePersonTmp> errorData, Throwable cause) {
        super(message, cause);

        this.errorData = errorData;
    }
}
