package com.huitu.cloud.api.ewci.hydfcst.controller;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.hydfcst.entity.*;
import com.huitu.cloud.api.ewci.hydfcst.service.HydfcstService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 水文预报服务
 *
 * <AUTHOR>
 */
@Api(tags = "水文预报服务")
@RestController
@RequestMapping("/api/ewci/hydfcst")
public class HydfcstResource extends AbstractApiResource implements ApiResource {

    private HydfcstService hydfcstService;

    @Override
    public String getUuid() {
        return "800d73a4-e92f-45d1-afa0-d9de6560b8b6";
    }

    @Override
    public String getVersion() {
        return "";
    }

    public HydfcstResource(HydfcstService hydfcstService) {
        this.hydfcstService = hydfcstService;
    }

    @ApiOperation(value = "获取水文预报时间过程数据", notes = "作者：张宝兴")
    @PostMapping("list-tm-process-data")
    public ResponseEntity<SuccessResponse<List<StResultMap>>> listHydfcstTmProcessData(@Validated @RequestBody HydfcstQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", hydfcstService.listHydfcstTmProcessData(query)));
    }


    @ApiOperation(value = "获取水文预报时间批次", notes = "作者：张宝兴")
    @PostMapping("list-tm-batch")
    public ResponseEntity<SuccessResponse<List<String>>> listHydfcstBatch(@Validated @RequestBody HydfcstTmBatchQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", hydfcstService.listHydfcstBatch(query)));
    }


    @ApiOperation(value = "获取预报统计结果", notes = "作者：张宝兴")
    @PostMapping("list-forecast-statistics")
    public ResponseEntity<SuccessResponse<HydFcstStat>> listForecastStatistics(@Validated @RequestBody HydfcstStatisticsQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", hydfcstService.listForecastStatistics(query)));
    }

}
