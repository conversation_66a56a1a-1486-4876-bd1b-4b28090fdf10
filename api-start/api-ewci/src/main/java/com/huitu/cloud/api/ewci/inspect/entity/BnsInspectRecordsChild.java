package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 检查记录
 */
@ApiModel(value = "检查记录")
public class BnsInspectRecordsChild implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组别id")
    @TableField(value = "GROUPID")
    private String groupid;

    @ApiModelProperty(value = "检查ID")
    @TableField(value = "INSPECT_ID")
    private String inspectId;

    @ApiModelProperty(value = "政区编码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "年份")
    @TableField(value = "YEAR")
    private String year;

    @ApiModelProperty(value = "检查类型编码")
    @TableField(value = "TYPE_CODE")
    private String typeCode;

    @ApiModelProperty(value = "检查类型名称")
    @TableField(value = "TYPE_NAME")
    private String typeName;

    @ApiModelProperty(value = "检查对象编码")
    @TableField(value = "CODE")
    private String code;

    @ApiModelProperty(value = "检查对象名称")
    @TableField(value = "NAME")
    private String name;

    @ApiModelProperty(value = "检查时间")
    @TableField(value = "INSP_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inspDate;

    @ApiModelProperty(value = "存在问题")
    @TableField(value = "EXIST_PROBLEMS")
    private String existProblems;

    @ApiModelProperty(value = "整改要求")
    @TableField(value = "REC_ASK")
    private String recAsk;

    @ApiModelProperty(value = "整改时限")
    @TableField(value = "REC_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recDate;

    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    public String getInspectId() {
        return inspectId;
    }

    public void setInspectId(String inspectId) {
        this.inspectId = inspectId;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getInspDate() {
        return inspDate;
    }

    public void setInspDate(Date inspDate) {
        this.inspDate = inspDate;
    }

    public String getExistProblems() {
        return existProblems;
    }

    public void setExistProblems(String existProblems) {
        this.existProblems = existProblems;
    }

    public String getRecAsk() {
        return recAsk;
    }

    public void setRecAsk(String recAsk) {
        this.recAsk = recAsk;
    }

    public Date getRecDate() {
        return recDate;
    }

    public void setRecDate(Date recDate) {
        this.recDate = recDate;
    }
}
