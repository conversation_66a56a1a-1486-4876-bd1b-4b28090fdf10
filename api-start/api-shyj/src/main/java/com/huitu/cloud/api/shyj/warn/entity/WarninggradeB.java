package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 预警等级表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("WARNINGGRADE_B")
@ApiModel(value="WarninggradeB对象", description="预警等级表")
public class WarninggradeB extends Model<WarninggradeB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "预警等级ID ")
    @TableId(value = "WARNGRADEID", type = IdType.NONE)
    private Integer warngradeid;

    @ApiModelProperty(value = "预警等级名称 ")
    @TableField("WARNGRADENM")
    private String warngradenm;

    @ApiModelProperty(value = "预警级别 ")
    @TableField("WARNGRADEORDERID")
    private BigDecimal warngradeorderid;

    @ApiModelProperty(value = " 县和乡镇预警标识符 ")
    @TableField("WARNGRADETYPE")
    private String warngradetype;

    @ApiModelProperty(value = "县预警等级")
    @TableField("COUNTYNUM")
    private BigDecimal countynum;


    public Integer getWarngradeid() {
        return warngradeid;
    }

    public void setWarngradeid(Integer warngradeid) {
        this.warngradeid = warngradeid;
    }

    public String getWarngradenm() {
        return warngradenm;
    }

    public void setWarngradenm(String warngradenm) {
        this.warngradenm = warngradenm;
    }

    public BigDecimal getWarngradeorderid() {
        return warngradeorderid;
    }

    public void setWarngradeorderid(BigDecimal warngradeorderid) {
        this.warngradeorderid = warngradeorderid;
    }

    public String getWarngradetype() {
        return warngradetype;
    }

    public void setWarngradetype(String warngradetype) {
        this.warngradetype = warngradetype;
    }

    public BigDecimal getCountynum() {
        return countynum;
    }

    public void setCountynum(BigDecimal countynum) {
        this.countynum = countynum;
    }

    @Override
    protected Serializable pkVal() {
        return this.warngradeid;
    }

    @Override
    public String toString() {
        return "WarninggradeB{" +
        "warngradeid=" + warngradeid +
        ", warngradenm=" + warngradenm +
        ", warngradeorderid=" + warngradeorderid +
        ", warngradetype=" + warngradetype +
        ", countynum=" + countynum +
        "}";
    }
}
