package com.huitu.cloud.api.ewci.weChat.entity;


import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

public class WeChatStTypeQo {
    @ApiModelProperty(value = "查询条件 1 水文 2 山洪",required = true)
    @Valid
    @Size(max = 2, message = "集合不能大于2")
    public List<String> stType;

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

}
