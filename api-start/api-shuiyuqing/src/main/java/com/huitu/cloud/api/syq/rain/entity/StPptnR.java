package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;


import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */

public class StPptnR  extends Model<StPptnR> {

    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "时段")
    private LocalDateTime tm;

    @ApiModelProperty(value = "时段降雨量")
    private String drp;

    @ApiModelProperty(value = "统计间隔")
    private BigDecimal intv;
    @ApiModelProperty(value = "日雨量")
    private BigDecimal dyp;

    @ApiModelProperty(value = "天气情况")
    private String wth;

    @ApiModelProperty(value = "时段累计雨量")
    private String sumdp;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getDrp() {
        return drp;
    }

    public void setDrp(String drp) {
        this.drp = drp;
    }

    public String getSumdp() {
        return sumdp;
    }

    public void setSumdp(String sumdp) {
        this.sumdp = sumdp;
    }

    public BigDecimal getIntv() {
        return intv;
    }

    public void setIntv(BigDecimal intv) {
        this.intv = intv;
    }

    public BigDecimal getDyp() {
        return dyp;
    }

    public void setDyp(BigDecimal dyp) {
        this.dyp = dyp;
    }

    public String getWth() {
        return wth;
    }

    public void setWth(String wth) {
        this.wth = wth;
    }

}
