package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 编制区域
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@TableName("Dic_BianZhiQuYu")
@ApiModel(value = "DicBianzhiquyu对象", description = "编制区域")
public class DicBianzhiquyu extends Model<DicBianzhiquyu> {

    @ApiModelProperty(value = "排序号")
    private Integer sortid;

    @ApiModelProperty(value = "编制区域编码")
    private String bianzhiquid;

    @ApiModelProperty(value = "编制区域名称")
    private String bianzhiqumingcheng;

    @ApiModelProperty(value = "xxx")
    private Integer kuiba;

    @ApiModelProperty(value = "xxx")
    private Integer wannian;

    @ApiModelProperty(value = "动画")
    private String donghua;

    @ApiModelProperty(value = "xxx")
    private String donghuasrc;

    @ApiModelProperty(value = "最小经度")
    private Float xmin;

    @ApiModelProperty(value = "最大经度")
    private Float xmax;

    @ApiModelProperty(value = "最小纬度")
    private Float ymin;

    @ApiModelProperty(value = "最大纬度")
    private Float ymax;

    @ApiModelProperty(value = "风险图类型")
    private String fengxiantuleixingid;

    @ApiModelProperty(value = "溃决类型")
    private String kuijueleixing;

    @ApiModelProperty(value = "统计日期")
    private String tongjiriqi;

    public Integer getSortid() {
        return sortid;
    }

    public void setSortid(Integer sortid) {
        this.sortid = sortid;
    }

    public String getBianzhiquid() {
        return bianzhiquid;
    }

    public void setBianzhiquid(String bianzhiquid) {
        this.bianzhiquid = bianzhiquid;
    }

    public String getBianzhiqumingcheng() {
        return bianzhiqumingcheng;
    }

    public void setBianzhiqumingcheng(String bianzhiqumingcheng) {
        this.bianzhiqumingcheng = bianzhiqumingcheng;
    }

    public Integer getKuiba() {
        return kuiba;
    }

    public void setKuiba(Integer kuiba) {
        this.kuiba = kuiba;
    }

    public Integer getWannian() {
        return wannian;
    }

    public void setWannian(Integer wannian) {
        this.wannian = wannian;
    }

    public String getDonghua() {
        return donghua;
    }

    public void setDonghua(String donghua) {
        this.donghua = donghua;
    }

    public String getDonghuasrc() {
        return donghuasrc;
    }

    public void setDonghuasrc(String donghuasrc) {
        this.donghuasrc = donghuasrc;
    }

    public Float getXmin() {
        return xmin;
    }

    public void setXmin(Float xmin) {
        this.xmin = xmin;
    }

    public Float getXmax() {
        return xmax;
    }

    public void setXmax(Float xmax) {
        this.xmax = xmax;
    }

    public Float getYmin() {
        return ymin;
    }

    public void setYmin(Float ymin) {
        this.ymin = ymin;
    }

    public Float getYmax() {
        return ymax;
    }

    public void setYmax(Float ymax) {
        this.ymax = ymax;
    }

    public String getFengxiantuleixingid() {
        return fengxiantuleixingid;
    }

    public void setFengxiantuleixingid(String fengxiantuleixingid) {
        this.fengxiantuleixingid = fengxiantuleixingid;
    }

    public String getKuijueleixing() {
        return kuijueleixing;
    }

    public void setKuijueleixing(String kuijueleixing) {
        this.kuijueleixing = kuijueleixing;
    }

    public String getTongjiriqi() {
        return tongjiriqi;
    }

    public void setTongjiriqi(String tongjiriqi) {
        this.tongjiriqi = tongjiriqi;
    }
}
