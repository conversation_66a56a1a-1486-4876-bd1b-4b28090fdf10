package com.huitu.cloud.api.ewci.soilnews.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 墒情专报
 *
 * <AUTHOR>
 */
@TableName("BSN_SOILNEWS_B")
@ApiModel(value="BsnSoilNewsB对象", description="墒情专报信息")
public class BsnSoilNewsB extends Model<BsnSoilNewsB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "期数")
    @TableId(value = "PERIODS", type = IdType.NONE)
    private Integer periods;

    @ApiModelProperty(value = "文件名称")
    @TableField("FILENM")
    private String filenm;

    @ApiModelProperty(value = "文件路径")
    @TableField("FILEURL")
    private String fileurl;

    @ApiModelProperty(value = "时间戳")
    @TableField("TS")
    private LocalDateTime ts;

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public String getFilenm() {
        return filenm;
    }

    public void setFilenm(String filenm) {
        this.filenm = filenm;
    }

    public String getFileurl() {
        return fileurl;
    }

    public void setFileurl(String fileurl) {
        this.fileurl = fileurl;
    }

    public LocalDateTime getTs() {
        return ts;
    }

    public void setTs(LocalDateTime ts) {
        this.ts = ts;
    }
}
