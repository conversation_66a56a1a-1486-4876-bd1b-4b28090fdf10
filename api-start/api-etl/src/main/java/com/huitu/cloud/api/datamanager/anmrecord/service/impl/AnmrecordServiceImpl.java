package com.huitu.cloud.api.datamanager.anmrecord.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmByStcdEdit;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmEdit;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmStatistics;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmVo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.BsnAnmrecordlogr;
import com.huitu.cloud.api.datamanager.anmrecord.entity.BsnAnmrecordr;
import com.huitu.cloud.api.datamanager.anmrecord.entity.RainStInfo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.River;
import com.huitu.cloud.api.datamanager.anmrecord.entity.Rsvr;
import com.huitu.cloud.api.datamanager.anmrecord.entity.RzStInfo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.StSwVo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.ZrStInfo;
import com.huitu.cloud.api.datamanager.anmrecord.mapper.AnmrecordDao;
import com.huitu.cloud.api.datamanager.anmrecord.service.AnmrecordService;
import com.huitu.cloud.api.datamanager.rule.entity.BsnRainruleb;
import com.huitu.cloud.api.datamanager.rule.mapper.BsnZrrulebDao;
import com.huitu.cloud.api.datamanager.rule.mapper.RainRuleDao;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.DateFormatUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 异常数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-07
 */
@Service
public class AnmrecordServiceImpl extends ServiceImpl<AnmrecordDao, BsnAnmrecordr> implements AnmrecordService {
    @Autowired
    private RainRuleDao rainRuleDao;
    @Autowired
    private BsnZrrulebDao zrrulebDao;

    @Override
    public List<AnmVo> getAnmRainRecord(String adcd, String staflgs, String stm, String etm, Integer filterCnt) {

        List<AnmVo> anmList = baseMapper.getBsnAnmrecordr("0", staflgs, stm, etm, filterCnt);
//        List<AnmVo> anmList = baseMapper.getBsnAnmrecordr("0", staflgs, stm, etm, null);
        Map<String, String> stMap = new HashMap<>();
        String inSql = "";
        List<AnmVo> resList = new ArrayList<>();
        for (AnmVo anmVo : anmList) {
            //超时段数量阀值的数据不展示
            if (anmVo.getAnmtp().equals("1")) {
                continue;
            }
            //转化异常数据json
            JSONObject jsonObj = JSONObject.fromObject(anmVo.getDatajs());
            //主键key全部转化为小写
            JSONObject jsonObject = transferJsonKey(jsonObj, true);
            String stcd = anmVo.getStcd();
            anmVo.setStcd(stcd);
            anmVo.setDrp(jsonObject.getString("drp"));
            anmVo.setSttp("雨量站");
            if (jsonObject.get("editvalue") != null) {
                anmVo.setEditValue(jsonObject.getString("editvalue"));
            }
            anmVo.setDataTm(jsonObject.getString("tm"));
            if (stMap.containsKey(stcd)) {
            } else {
                stMap.put(stcd, stcd);
                inSql = inSql + "'" + stcd + "',";
            }
            resList.add(anmVo);
        }
        List<AnmVo> result = new ArrayList<>();
        if (resList.size() > 0) {
            Map<String, Object> param = new HashMap<>();
            int level = AdcdUtil.getAdLevel(adcd);
            if (level > 0) {
                param.put("ad", adcd.substring(0, level));
                param.put("level", level);
            }
            //测站去掉最后一个逗号
            String stcds = inSql.substring(0, inSql.length() - 1);
            param.put("stcds", stcds);
            List<RainStInfo> stInfos = baseMapper.getRainStInfo(param);
            Map<String, RainStInfo> stInfoMap = stInfos.stream().collect(Collectors.toMap(RainStInfo::getStcd, Function.identity()));
            resList.forEach(x -> {
                if (stInfoMap.containsKey(x.getStcd())) {
                    RainStInfo stInfo = stInfoMap.get(x.getStcd());
                    x.setStnm(stInfo.getStnm());
                    x.setAdnm(stInfo.getAdnm());
                    x.setStadtpnm(stInfo.getStadtpnm());
                    x.setXadnm(stInfo.getXadnm());
                    x.setThreshold(stInfo.getSnv().toString());
                    x.setMphone(stInfo.getMphone());
                    x.setOfficer(stInfo.getOfficer());
                    x.setAdcd(stInfo.getAdcd());
//                    Double drp = Double.parseDouble(x.getDrp());
//                    //超指标的值
//                    BigDecimal drpBg = new BigDecimal(drp.toString());
//                    BigDecimal snvBg = new BigDecimal(stInfo.getSnv().toString());
//                    Double value = drpBg.subtract(snvBg).doubleValue();
//                    String desc = "超指标" + value + "mm";
                    x.setDesc(x.getRedesc());
                    result.add(x);
                }
            });
        }
        return result;
    }

    @Override
    public List<AnmVo> getZrAnmRecord(String adcd, String staflgs, String stm, String etm, int filterCnt) {
        List<AnmVo> anmList = baseMapper.getBsnAnmrecordr("3,4,5,6", staflgs, stm, etm, filterCnt);

        Map<String, String> stMap = new HashMap<>();
        String inSql = "";
        List<AnmVo> resList = new ArrayList<>();
        for (AnmVo anmVo : anmList) {
            //转化异常数据json
            JSONObject jsonObj = JSONObject.fromObject(anmVo.getDatajs());
            //主键key全部转化为小写
            JSONObject jsonObject = transferJsonKey(jsonObj, true);
            String stcd = anmVo.getStcd();
            if (jsonObject.containsKey("z")) {
                //河道站
                anmVo.setZ(jsonObject.getString("z"));
                anmVo.setSttp("河道站");
            } else {
                //水库站
                anmVo.setZ(jsonObject.getString("rz"));
                anmVo.setSttp("水库站");
            }
            if (jsonObject.get("editvalue") != null) {
                anmVo.setEditValue(jsonObject.getString("editvalue"));
            }
            anmVo.setDataTm(jsonObject.getString("tm"));
            if (stMap.containsKey(stcd)) {
            } else {
                stMap.put(stcd, stcd);
                inSql = inSql + "'" + stcd + "',";
            }
            resList.add(anmVo);
        }
        List<AnmVo> result = new ArrayList<>();
        if (resList.size() > 0) {
            Map<String, Object> param = new HashMap<>();
            int level = AdcdUtil.getAdLevel(adcd);
            if (level > 0) {
                param.put("ad", adcd.substring(0, level));
                param.put("level", level);
            }
            //测站去掉最后一个逗号
            String stcds = inSql.substring(0, inSql.length() - 1);
            param.put("stcds", stcds);
            List<ZrStInfo> stInfos = baseMapper.getZrStInfo(param);
            Map<String, ZrStInfo> stInfoMap = stInfos.stream().collect(Collectors.toMap(ZrStInfo::getStcd, Function.identity()));
            //超指标的值
            //异常类型： 0，雨量超瞬时阈值；1，雨量超分钟数量阈值；2，水位超上限；3，水位超下限；4，水位超变幅。
            resList.stream().filter(x -> stInfoMap.containsKey(x.getStcd())).forEach(x -> {
                ZrStInfo stInfo = stInfoMap.get(x.getStcd());
                x.setStnm(stInfo.getStnm());
                x.setAdnm(stInfo.getAdnm());
                x.setStadtpnm(stInfo.getStadtpnm());
                x.setXadnm(stInfo.getXadnm());
                x.setMphone(stInfo.getMphone());
                x.setOfficer(stInfo.getOfficer());
                x.setAdcd(stInfo.getAdcd());
//                Double z = Double.parseDouble(x.getZ());
//                Double tvalue = 0.0;
//                String desc = "";
//                BigDecimal dBg = new BigDecimal(x.getZ());
//                if (x.getAnmtp().equals("3")) {
//                    if(stInfo.getUpv()!=null){
//                        tvalue = stInfo.getUpv();
//                    }
//                    BigDecimal tBg = new BigDecimal(tvalue.toString());
//                    //水位值减去指标值
//                    Double value = dBg.subtract(tBg).doubleValue();
//                    desc = "高于上限" + value.toString() + "m";
//                } else if (x.getAnmtp().equals("4")) {
//                    if(stInfo.getDwv()!=null){
//                        tvalue = stInfo.getDwv();
//                    }
//                    BigDecimal tBg = new BigDecimal(tvalue.toString());
//                    //指标值减去水位值
//                    Double value = tBg.subtract(dBg).doubleValue();
//                    desc = "低于下限" + value.toString() + "m";
//                } else if (x.getAnmtp().equals("5")) {
//                    Date date=DateFormatUtil.fomatUtcStringToDate(x.getDataTm());
//                    StSwVo stSwVo = null;
//                    if (x.getSttp().equals("河道站")) {
//                        stSwVo = baseMapper.getRiverSwByTm(x.getStcd(), date);
//                    } else {
//                        stSwVo = baseMapper.getRsvrSwByTm(x.getStcd(), date);
//                    }
//                    Double value = 0.0;
//                    Double ctChv = 0.0;
//                    if (stSwVo != null) {
//                        BigDecimal hz = new BigDecimal(stSwVo.getZ());
//                        Double upperSw = Double.parseDouble(stSwVo.getZ());
//                        BigDecimal chvBg = new BigDecimal(stInfo.getChv().toString());
//                        //上次水位值 大于本次水位 变幅=上次减去本次
//                        if (upperSw > z) {
//                            ctChv = hz.subtract(dBg).doubleValue();
//                            value = hz.subtract(dBg).subtract(chvBg).doubleValue();
//                        } else {
//                            //上次水位值 小于本次水位 变幅=本次水位减上次水位
//                            ctChv = dBg.subtract(hz).doubleValue();
//                            value = dBg.subtract(hz).subtract(chvBg).doubleValue();
//                        }
//                    }
//                    desc = "变幅" + ctChv + "超过变幅指标" + value + "m";
//                }
                x.setDesc(x.getRedesc());
                result.add(x);
            });
        }
        return result;
    }

    @Override
    @Transactional
    public boolean editAnmRecord(AnmEdit anmEdit) {
        String staflg = anmEdit.getStaflg();

        BsnAnmrecordr bsnAnmrecordr = baseMapper.selectById(anmEdit.getReid());
        //原纪录json
        String oldJson = bsnAnmrecordr.getDatajs();
        //新纪录json
        String newJson = "";
        //转化异常数据json
        JSONObject jsonObject = JSONObject.fromObject(oldJson);
        if (StringUtils.isNotBlank(anmEdit.getEditValue())) {
            //将修改值放入
            jsonObject.put("editvalue", anmEdit.getEditValue());
        }
        Date date = new Date();
        //0:终审审批通过且入库；2终审审批未通过； 3,初审通过；4，初审未通过； 5，已修改
        if (staflg.equals("5")) {
            //将修改值放入
            //jsonObject.put("editvalue",anmEdit.getEditValue());
            //新json数据
            bsnAnmrecordr.setDatajs(jsonObject.toString());
            //修改人
            bsnAnmrecordr.setUpuser(anmEdit.getDoer());
            //修改时间
            bsnAnmrecordr.setUptm(date);

        } else if (staflg.equals("4") || staflg.equals("3")) {
            //初审审批人
            bsnAnmrecordr.setFaduser(anmEdit.getDoer());
            //初审审批时间
            bsnAnmrecordr.setFadtm(date);
        } else if (staflg.equals("2") || staflg.equals("0")) {
            //终审审批人
            bsnAnmrecordr.setRaduser(anmEdit.getDoer());
            //终审审批时间
            bsnAnmrecordr.setRadtm(date);
        }
        //新json赋值
        newJson = jsonObject.toString();
        //操作时间
        bsnAnmrecordr.setModitime(date);
        bsnAnmrecordr.setNt(anmEdit.getNt());
        //状态
        bsnAnmrecordr.setStaflg(anmEdit.getStaflg());
        baseMapper.updateById(bsnAnmrecordr);
        //终审通过插入水雨情表
        if (staflg.equals("0")) {

            //主键key全部转化为小写
            JSONObject json = transferJsonKey(jsonObject, true);
            String tbnm = bsnAnmrecordr.getTbnm();
            if (json.containsKey("drp")) {

                Map<String, Object> data = jsonObjectToHashMap(json);
                Date tmTwo = DateFormatUtil.fomatUtcStringToDate(data.get("tm").toString());
                data.put("tm", tmTwo);
                //雨量数据插入
                baseMapper.insertRain(data);
            } else if (json.containsKey("z")) {
                River river = (River) JSONObject.toBean(json, River.class);
                //将utc时间格式转换为时间
                Date tmTwo = DateFormatUtil.fomatUtcStringToDate(river.getTm());
                river.setTmTwo(tmTwo);
                //河道数据插入
                baseMapper.insertRiver(river);
            } else if (json.containsKey("rz")) {
                Rsvr rsvrR = (Rsvr) JSONObject.toBean(json, Rsvr.class);
                //将utc时间格式转换为时间
                Date tmTwo = DateFormatUtil.fomatUtcStringToDate(rsvrR.getTm());
                rsvrR.setTmTwo(tmTwo);
                //水库数据插入
                baseMapper.insertRsvr(rsvrR);
            }
        }
        //日志
        BsnAnmrecordlogr bsnAnmrecordlogr = new BsnAnmrecordlogr();
        bsnAnmrecordlogr.setReid(anmEdit.getReid());
        bsnAnmrecordlogr.setTm(date);
        bsnAnmrecordlogr.setOdatajs(oldJson);
        bsnAnmrecordlogr.setNdatajs(newJson);
        bsnAnmrecordlogr.setNt(anmEdit.getNt());
        bsnAnmrecordlogr.setModitime(date);
        bsnAnmrecordlogr.setDoer(anmEdit.getDoer());
        baseMapper.insertAnmLog(bsnAnmrecordlogr);
        return true;
    }

    @Override
    public List<StSwVo> getLatelySw(String stcd, String tbnm) {
        if (tbnm.equals("st_river_r")) {
            //河道
            return baseMapper.getRiverSw(stcd);
        } else {
            //水库
            return baseMapper.getRsvrSw(stcd);
        }
    }

    @Override
    public String getAdAvg(String adcd, String tm) {
        String avgDrp = baseMapper.getAdAvg(adcd, tm);
        return avgDrp;
    }

    @Override
    public String updateStaflg(String stcd, String tbnm) {
        //将雨量屏蔽要素转化为异常数据类型  0，雨量超瞬时阈值；1，雨量超时段阈值；
        if (tbnm.indexOf("st_pptn_r") > -1) {
            tbnm = tbnm.replace("st_pptn_r", "0,1");
        }
        //将河道水位屏蔽要素转化为异常数据类型  3，水位超上限；4，水位超下限；5，水位超变幅
        if (tbnm.indexOf("st_river_r") > -1) {
            tbnm = tbnm.replace("st_river_r", "3,4,5");
        }
        //将河道水位屏蔽要素转化为异常数据类型  3，水位超上限；4，水位超下限；5，水位超变幅
        if (tbnm.indexOf("st_rsvr_r") > -1) {
            tbnm = tbnm.replace("st_rsvr_r", "3,4,5");
        }

        baseMapper.updateAnmrecordrStaflg(stcd, tbnm);
        return "OK";
    }

    @Override
    public List<AnmVo> getAnmRainStcd(String adcd) {
        //雨量异常数据标志
        //String tbnm="bsn_ycrain_sx_r,bsn_ycrain_yzsf_r";
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        if (level > 0) {
            param.put("ad", adcd.substring(0, level));
            param.put("level", level);
        }
        List<AnmVo> list = baseMapper.getAnmrecordrByExpt(param);
        if (list.size() > 0) {
            List<String> stList = list.stream().map(AnmVo::getStcd).collect(Collectors.toList());
            List<BsnRainruleb> ruleList = rainRuleDao.selectBatchIds(stList);
            Map<String, BsnRainruleb> stInfoMap = ruleList.stream().collect(Collectors.toMap(BsnRainruleb::getStcd, Function.identity()));
            list.forEach(anmVo -> {
                BsnRainruleb stInfo = stInfoMap.get(anmVo.getStcd());
                //超指标的值
//                String desc = stInfo.getInv() + "分钟上报数据超过了" + stInfo.getCtpm5() + "条";
                anmVo.setDesc(anmVo.getRedesc());
            });
        }
        return list;
    }

    @Override
    public IPage<AnmVo> getAnmRainRecordByPage(String adcd, String staflgs, int pageNum, int pageSize) {
        //雨量异常数据标志
        // String tbnm="bsn_ycrain_sx_r,bsn_ycrain_yzsf_r";
        //雨量超瞬时
        String anmtp = "0,1";
        IPage<AnmVo> ipage = this.getAnmRecordByPage(adcd, staflgs, anmtp, "", pageNum, pageSize);
        List<AnmVo> list = ipage.getRecords();
        if (list.size() > 0) {
//            List<String> stList = list.stream().map(AnmVo::getStcd).collect(Collectors.toList());
//            List<BsnRainruleb> ruleList = rainRuleDao.selectBatchIds(stList);
//            Map<String, BsnRainruleb> stInfoMap = ruleList.stream().collect(Collectors.toMap(BsnRainruleb::getStcd, Function.identity()));
            list.forEach(anmVo -> {
                //转化异常数据json
                JSONObject jsonObj = JSONObject.fromObject(anmVo.getDatajs());
                //主键key全部转化为小写
                JSONObject jsonObject = transferJsonKey(jsonObj, true);
//                String stcd = anmVo.getStcd();
                anmVo.setDrp(jsonObject.getString("drp"));
                anmVo.setSttp("雨量站");
                if (jsonObject.get("editvalue") != null) {
                    anmVo.setEditValue(jsonObject.getString("editvalue"));
                }
                anmVo.setDataTm(jsonObject.getString("tm"));
//                Double drp = Double.parseDouble(anmVo.getDrp());
//                BsnRainruleb stInfo = stInfoMap.get(anmVo.getStcd());
//                //超指标的值
//                BigDecimal drpBg = new BigDecimal(drp.toString());
//                BigDecimal snvBg = new BigDecimal(stInfo.getSnv().toString());
//                Double value = drpBg.subtract(snvBg).doubleValue();
//                String desc = "超指标" + value + "mm";
                anmVo.setDesc(anmVo.getRedesc());
            });
            ipage.setRecords(list);
        }

        return ipage;
    }

    @Override
    public IPage<AnmVo> getAnmSwRecordByPage(String adcd, String staflgs, int pageNum, int pageSize) {
        //水位异常数据标志
        //String tbnm="st_river_sx_r,st_river_yzsf_r,st_rsvr_sx_r,st_rsvr_yzsf_r";
        //3，水位超上限；4，水位超下限；5，水位超变幅
        String anmtp = "3,4,5";
        IPage<AnmVo> ipage = this.getAnmRecordByPage(adcd, staflgs, anmtp, "", pageNum, pageSize);
        List<AnmVo> list = ipage.getRecords();
        if (list.size() > 0) {
//            List<String> stList = list.stream().map(AnmVo::getStcd).collect(Collectors.toList());
//            List<BsnZrruleb> ruleList = zrrulebDao.selectBatchIds(stList);
//            Map<String, BsnZrruleb> stInfoMap = ruleList.stream().collect(Collectors.toMap(BsnZrruleb::getStcd, Function.identity()));
            list.forEach(anmVo -> {
                //转化异常数据json
                JSONObject jsonObj = JSONObject.fromObject(anmVo.getDatajs());
                //主键key全部转化为小写
                JSONObject jsonObject = transferJsonKey(jsonObj, true);
//                String stcd = anmVo.getStcd();
                if (jsonObject.containsKey("z")) {
                    //河道站
                    anmVo.setZ(jsonObject.getString("z"));
                    anmVo.setSttp("河道站");
                } else {
                    //水库站
                    anmVo.setZ(jsonObject.getString("rz"));
                    anmVo.setSttp("水库站");
                }
                if (jsonObject.get("editvalue") != null) {
                    anmVo.setEditValue(jsonObject.getString("editvalue"));
                }
                anmVo.setDataTm(jsonObject.getString("tm"));
//                BsnZrruleb stInfo = stInfoMap.get(anmVo.getStcd());
//                Double z = Double.parseDouble(anmVo.getZ());
//                Double tvalue = 0.0;
//                String desc = "";
//                BigDecimal dBg = new BigDecimal(anmVo.getZ());
//                if (anmVo.getAnmtp().equals("3")) {
//                    tvalue = stInfo.getUpv();
//                    BigDecimal tBg = new BigDecimal(tvalue.toString());
//                    //水位值减去指标值
//                    Double value = dBg.subtract(tBg).doubleValue();
//                    desc = "高于上限" + value.toString() + "m";
//                } else if (anmVo.getAnmtp().equals("4")) {
//                    tvalue = stInfo.getDwv();
//                    BigDecimal tBg = new BigDecimal(tvalue.toString());
//                    //指标值减去水位值
//                    Double value = tBg.subtract(dBg).doubleValue();
//                    desc = "低于下限" + value.toString() + "m";
//                } else if (anmVo.getAnmtp().equals("5")) {
//                    StSwVo stSwVo = null;
//                    Date date=DateFormatUtil.fomatUtcStringToDate(anmVo.getDataTm());
//                    if (anmVo.getSttp().equals("河道站")) {
//                        stSwVo = baseMapper.getRiverSwByTm(anmVo.getStcd(), date);
//                    } else {
//                        stSwVo = baseMapper.getRsvrSwByTm(anmVo.getStcd(), date);
//                    }
//                    Double value = 0.0;
//                    Double ctChv = 0.0;
//                    if (stSwVo != null) {
//                        BigDecimal hz = new BigDecimal(stSwVo.getZ());
//                        Double upperSw = Double.parseDouble(stSwVo.getZ());
//                        BigDecimal chvBg = new BigDecimal(stInfo.getChv().toString());
//                        //上次水位值 大于本次水位 变幅=上次减去本次
//                        if (upperSw > z) {
//                            ctChv = hz.subtract(dBg).doubleValue();
//                            value = hz.subtract(dBg).subtract(chvBg).doubleValue();
//                        } else {
//                            //上次水位值 小于本次水位 变幅=本次水位减上次水位
//                            ctChv = dBg.subtract(hz).doubleValue();
//                            value = dBg.subtract(hz).subtract(chvBg).doubleValue();
//                        }
//                    }
//                    desc = "变幅" + ctChv + "超过变幅指标" + value + "m";
//                }
                anmVo.setDesc(anmVo.getRedesc());
            });
            ipage.setRecords(list);
        }
        return ipage;
    }

    @Override
    public IPage<AnmVo> getAnmSwRecordStcdByPage(String stcd, String stm, String etm, String staflgs, int pageNum, int pageSize) {
        //水位异常数据标志
        //String tbnm="st_river_sx_r,st_river_yzsf_r,st_rsvr_sx_r,st_rsvr_yzsf_r";
        //3，水位超上限；4，水位超下限；5，水位超变幅
        String anmtp = "3,4,5";
        IPage<AnmVo> ipage = this.getAnmRecordStcdByPage(stcd,stm,etm, staflgs, anmtp, "", pageNum, pageSize);
        List<AnmVo> list = ipage.getRecords();
        if (list.size() > 0) {
            list.forEach(anmVo -> {
                //转化异常数据json
                JSONObject jsonObj = JSONObject.fromObject(anmVo.getDatajs());
                //主键key全部转化为小写
                JSONObject jsonObject = transferJsonKey(jsonObj, true);
                if (jsonObject.containsKey("z")) {
                    //河道站
                    anmVo.setZ(jsonObject.getString("z"));
                    anmVo.setSttp("河道站");
                } else {
                    //水库站
                    anmVo.setZ(jsonObject.getString("rz"));
                    anmVo.setSttp("水库站");
                }
                if (jsonObject.get("editvalue") != null) {
                    anmVo.setEditValue(jsonObject.getString("editvalue"));
                }
                anmVo.setDataTm(jsonObject.getString("tm"));
                anmVo.setDesc(anmVo.getRedesc());
            });
            ipage.setRecords(list);
        }
        return ipage;
    }

    @Override
    public int getRecordCount(String adcd, String staflgs) {
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        if (level > 0) {
            param.put("ad", adcd.substring(0, level));
            param.put("level", level);
        }
        param.put("staflgs", staflgs);
        int ct = baseMapper.getCountByAd(param);
        return ct;
    }

    @Override
    public IPage<RzStInfo> getAnmRzSt(String adcd, String anmtp, int pageNum, int pageSize) {
        Page page = new Page(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        if (level > 0) {
            param.put("ad", adcd.substring(0, level));
            param.put("level", level);
        }
        String staflgs = "6";
        param.put("staflgs", staflgs);
        param.put("anmtp", anmtp);
        IPage<RzStInfo> ipage = baseMapper.getAnmRzSt(page, param);
        return ipage;
    }

    @Transactional
    @Override
    public Boolean updateBsnAnmrecordr(AnmByStcdEdit anmEdit) {
        // 添加过滤条件：stcd和tbnm 必须存在，否则mapper.xml查询的范围会变大，从而出现修改其他测站或其他类型的数据
        if (anmEdit.getStcd().equals("") || anmEdit.getTbnm().equals("")) {
            return false;
        }
        String staflg = anmEdit.getStaflg();
        List<Map<String, Object>> rainList = new ArrayList<>();
        List<River> riverList = new ArrayList<>();
        List<Rsvr> rsvrList = new ArrayList<>();

        // 不同数据来源对同一条拦截的数据取同一个站同一时间
        List<Map<String, Object>> confltRainPKList = new ArrayList<>();
        List<River> confltRiverPKList = new ArrayList<>();
        List<Rsvr> confltRsvrPKList = new ArrayList<>();
        List<String> pkList = new ArrayList<>();

        List<BsnAnmrecordlogr> logList = new ArrayList<>();
        //查询异常数据
        List<BsnAnmrecordr> bsnAnmrecordrs = baseMapper.getAnmrecordrs(anmEdit.getStcd(), anmEdit.getTbnm());
        bsnAnmrecordrs.forEach(bsnAnmrecordr -> {
            //原纪录j son
            String oldJson = bsnAnmrecordr.getDatajs();
            //新纪录json
            String newJson = "";
            //转化异常数据json
            JSONObject jsonObject = JSONObject.fromObject(oldJson);
            if (StringUtils.isNotBlank(anmEdit.getEditValue())) {
                //将修改值放入
                jsonObject.put("editvalue", anmEdit.getEditValue());
            }
            Date date = new Date();
            //0:终审审批通过且入库；2终审审批未通过； 3,初审通过；4，初审未通过； 5，已修改
            if (staflg.equals("5")) {
                //将修改值放入
                //jsonObject.put("editvalue",anmEdit.getEditValue());
                //新json数据
                bsnAnmrecordr.setDatajs(jsonObject.toString());
                //修改人
                bsnAnmrecordr.setUpuser(anmEdit.getDoer());
                //修改时间
                bsnAnmrecordr.setUptm(date);

            } else if (staflg.equals("4") || staflg.equals("3")) {
                //初审审批人
                bsnAnmrecordr.setFaduser(anmEdit.getDoer());
                //初审审批时间
                bsnAnmrecordr.setFadtm(date);
            } else if (staflg.equals("2") || staflg.equals("0")) {
                //终审审批人
                bsnAnmrecordr.setRaduser(anmEdit.getDoer());
                //终审审批时间
                bsnAnmrecordr.setRadtm(date);
            }
            //新json赋值
            newJson = jsonObject.toString();
            //操作时间
            bsnAnmrecordr.setModitime(date);
            bsnAnmrecordr.setNt(anmEdit.getNt());
            //状态
            bsnAnmrecordr.setStaflg(anmEdit.getStaflg());
            //终审通过组合数据集
            if (staflg.equals("0")) {
                //主键key全部转化为小写
                JSONObject json = transferJsonKey(jsonObject, true);
                if (json.containsKey("drp")) {
                    Map<String, Object> data = jsonObjectToHashMap(json);
                    Date tmTwo = DateFormatUtil.fomatUtcStringToDate(data.get("tm").toString());
                    data.put("tm", tmTwo);
                    if (pkList.contains(data.get("tm").toString() + data.get("stcd").toString())) {
                        confltRainPKList.add(data);
                    } else {
                        pkList.add(data.get("tm").toString() + data.get("stcd").toString());
                        //雨量数据集合
                        rainList.add(data);
                    }
                } else if (json.containsKey("z")) {
                    River river = (River) JSONObject.toBean(json, River.class);
                    //将utc时间格式转换为时间
                    Date tmTwo = DateFormatUtil.fomatUtcStringToDate(river.getTm());
                    river.setTmTwo(tmTwo);
                    if (pkList.contains(river.getTm() + river.getStcd())) {
                        confltRiverPKList.add(river);
                    } else {
                        pkList.add(river.getTm() + river.getStcd());
                        //河道数据集合
                        riverList.add(river);
                    }
                } else if (json.containsKey("rz")) {
                    Rsvr rsvr = (Rsvr) JSONObject.toBean(json, Rsvr.class);
                    //将utc时间格式转换为时间
                    Date tmTwo = DateFormatUtil.fomatUtcStringToDate(rsvr.getTm());
                    rsvr.setTmTwo(tmTwo);
                    if (pkList.contains(rsvr.getTm() + rsvr.getStcd())) {
                        confltRsvrPKList.add(rsvr);
                    } else {
                        pkList.add(rsvr.getTm() + rsvr.getStcd());
                        //水库数据集合
                        rsvrList.add(rsvr);
                    }
                }
            }
            //日志数据放入聚合
            BsnAnmrecordlogr bsnAnmrecordlogr = new BsnAnmrecordlogr();
            bsnAnmrecordlogr.setReid(bsnAnmrecordr.getReid());
            bsnAnmrecordlogr.setTm(date);
            bsnAnmrecordlogr.setOdatajs(oldJson);
            bsnAnmrecordlogr.setNdatajs(newJson);
            bsnAnmrecordlogr.setNt(anmEdit.getNt());
            bsnAnmrecordlogr.setModitime(date);
            bsnAnmrecordlogr.setDoer(anmEdit.getDoer());
            logList.add(bsnAnmrecordlogr);
        });
        //批量修改异常数据表
        //创建临时表
        String tableName = "##tmp";
//        baseMapper.dropTemoraryTable(tableName);
//        baseMapper.createTemoraryTable(tableName);
        //数据插入临时表
        int count = 0;
        if (bsnAnmrecordrs.size() % 130 == 0) {
            count = bsnAnmrecordrs.size() / 130;
        } else {
            count = bsnAnmrecordrs.size() / 130 + 1;
        }
        for (int i = 0; i < count; i++) {
            int min = i * 130;
            int max = (i + 1) * 130;
            if (max >= bsnAnmrecordrs.size()) {
                max = bsnAnmrecordrs.size();
            }
            List<BsnAnmrecordr> list3 = bsnAnmrecordrs.subList(min, max);
            baseMapper.batchUpdateBsnAnmrecordr(list3);
        }
        //临时表的数据更新至原表
//        baseMapper.updateBsnAnmrecordr(tableName);
        //终审通过插入水雨情表
        if (staflg.equals("0")) {
            //主键key全部转化为小写
            JSONObject jsonObject2 = JSONObject.fromObject(bsnAnmrecordrs.get(0).getDatajs());
            JSONObject json = transferJsonKey(jsonObject2, true);
            if (json.containsKey("drp")) {
                //雨量数据批量插入
                int count2 = 0;
                if (rainList.size() % 500 == 0) {
                    count2 = rainList.size() / 500;
                } else {
                    count2 = rainList.size() / 500 + 1;
                }
                for (int i = 0; i < count2; i++) {
                    int min = i * 500;
                    int max = (i + 1) * 500;
                    if (max >= rainList.size()) {
                        max = rainList.size();
                    }
                    List<Map<String, Object>> list3 = rainList.subList(min, max);
                    baseMapper.batchInsertRain(list3);
                }
            } else if (json.containsKey("z")) {
                //河道数据批量插入
                int count2 = 0;
                if (riverList.size() % 170 == 0) {
                    count2 = riverList.size() / 170;
                } else {
                    count2 = riverList.size() / 170 + 1;
                }
                for (int i = 0; i < count2; i++) {
                    int min = i * 170;
                    int max = (i + 1) * 170;
                    if (max >= riverList.size()) {
                        max = riverList.size();
                    }
                    List<River> list3 = riverList.subList(min, max);
                    baseMapper.batchInsertRiver(list3);
                }
            } else if (json.containsKey("rz")) {
                //水库数据批量插入
                int count2 = 0;
                if (rsvrList.size() % 170 == 0) {
                    count2 = rsvrList.size() / 170;
                } else {
                    count2 = rsvrList.size() / 170 + 1;
                }
                for (int i = 0; i < count2; i++) {
                    int min = i * 170;
                    int max = (i + 1) * 170;
                    if (max >= rsvrList.size()) {
                        max = rsvrList.size();
                    }
                    List<Rsvr> list3 = rsvrList.subList(min, max);
                    baseMapper.batchInsertRsvr(list3);
                }
            }
        }
        //批量插入日志
        int count3 = 0;
        if (logList.size() % 280 == 0) {
            count3 = logList.size() / 280;
        } else {
            count3 = logList.size() / 280 + 1;
        }
        for (int i = 0; i < count3; i++) {
            int min = i * 280;
            int max = (i + 1) * 280;
            if (max >= logList.size()) {
                max = logList.size();
            }
            List<BsnAnmrecordlogr> list3 = logList.subList(min, max);
            baseMapper.batchInsertAnmLog(list3);
        }
        return true;
    }

    @Override
    public List<AnmStatistics> getAnmStatisticsList(String adcd, Date bgtm, Date endtm) {
        return baseMapper.getAnmStatisticsList(adcd, bgtm, endtm);
    }

    @Override
    public void exportZrAnmRecord(String adcd, String staflgs, String stm, String etm, int filterCnt, HttpServletResponse response) {
        List<AnmVo> data = getZrAnmRecord(adcd, staflgs, stm, etm, filterCnt);
        if (AdcdUtil.getAdLevel(adcd) <= 4 || adcd.equals("220581000000000")) {
            data.forEach(item -> item.setAdnm(item.getXadnm()));
        }

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        ZoneId shanghaiZone = ZoneId.of("Asia/Shanghai");
        data.forEach(i -> {
            // 解析为Instant
            Instant instant = Instant.parse(i.getDataTm());

            // 转换为特定时区的ZonedDateTime（例如，转换到亚洲/上海时区）
            ZonedDateTime zonedDateTime = instant.atZone(shanghaiZone);

            // 从ZonedDateTime中提取LocalDateTime，此时时间已经转换到了上海时区
            LocalDateTime localDateTime = zonedDateTime.toLocalDateTime();

            // 格式化为字符串
            String formattedTime = localDateTime.format(formatter);
            i.setDataTm(formattedTime);
        });

        ExcelExportUtil.execute(data, "水情异常数据");
    }

    @Override
    public void exportAnmRainRecord(String adcd, String staflgs, String stm, String etm, HttpServletResponse response) {
        List<AnmVo> data = getAnmRainRecord(adcd, staflgs, stm, etm,null);
        if (AdcdUtil.getAdLevel(adcd) <= 4 || adcd.equals("220581000000000")) {
            data.forEach(item -> item.setAdnm(item.getXadnm()));
        }
        ExcelExportUtil.execute(data, "雨情异常数据");
    }

    private IPage<AnmVo> getAnmRecordByPage(String adcd, String staflgs, String anmtp, String tbnm, int pageNum, int pageSize) {
        Page page = new Page(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        if (level > 0) {
            param.put("ad", adcd.substring(0, level));
            param.put("level", level);
        }
        param.put("staflgs", staflgs);
        param.put("tbnm", tbnm);
        param.put("anmtp", anmtp);
        IPage<AnmVo> ipage = baseMapper.getAnmrecordrByPage(page, param);
        return ipage;
    }

    private IPage<AnmVo> getAnmRecordStcdByPage(String stcd, String stm, String etm, String staflgs, String anmtp, String tbnm, int pageNum, int pageSize) {
        Page page = new Page(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("stcd", stcd);
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("staflgs", staflgs);
        param.put("tbnm", tbnm);
        param.put("anmtp", anmtp);
        IPage<AnmVo> ipage = baseMapper.getAnmrecordrStcdByPage(page, param);
        return ipage;
    }

    private HashMap<String, Object> jsonObjectToHashMap(JSONObject jsonObj) {
        HashMap<String, Object> data = new HashMap<String, Object>();
        Iterator it = jsonObj.keys();
        while (it.hasNext()) {
            String key = String.valueOf(it.next().toString());
            if (jsonObj.get(key) == null || jsonObj.get(key).toString().equals("null")) {
                data.put(key, null);
            } else {
                data.put(key, jsonObj.get(key));
            }

        }
        return data;
    }

    /**
     * 修改json的key为大写或者小写
     *
     * @param jsonObject
     * @param transferMode 当值为true时，说明是转小写，false为转大写
     * @return
     * @throws
     * @Description
     */
    public static JSONObject transferJsonKey(JSONObject jsonObject, boolean transferMode) {
        JSONObject object = new JSONObject();
        Iterator iterator = jsonObject.keys();
        while (iterator.hasNext()) {
            String jsonKey = (String) iterator.next();
            Object valueObject = jsonObject.get(jsonKey);
            if (transferMode) {
                jsonKey = jsonKey.toLowerCase();
            } else {
                jsonKey = jsonKey.toUpperCase();
            }
            object.put(jsonKey, valueObject);
        }
        return object;
    }


}
