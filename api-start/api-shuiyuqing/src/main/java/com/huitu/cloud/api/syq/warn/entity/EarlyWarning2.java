package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@TableName("EARLY_WARNING_2")
@ApiModel(value="EarlyWarning2对象", description="")
public class EarlyWarning2 extends Model<EarlyWarning2> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "预警ID")
    @TableId(value = "ID", type = IdType.NONE)
    private String id;

    @ApiModelProperty(value = "预警对象编码")
    @TableField("EWCODE")
    private String ewcode;

    @ApiModelProperty(value = "预警对象类型:1.村庄  2.水库  3.河道")
    @TableField("EWTYPE")
    private String ewtype;

    @ApiModelProperty(value = "预报时间")
    @TableField("FTM")
    private LocalDateTime ftm;

    @ApiModelProperty(value = "预警时间")
    @TableField("WTM")
    private LocalDateTime wtm;

    @ApiModelProperty(value = "预警开始时间")
    @TableField("NTM")
    private LocalDateTime ntm;

    @ApiModelProperty(value = "预警等级：1.一级预警  2.二级预警  3.三级预警  4.无预警")
    @TableField("WLEVEL")
    private String wlevel;

    @ApiModelProperty(value = "预警来源：1.雨情  2.流量  3.水位")
    @TableField("STYPE")
    private String stype;

    @ApiModelProperty(value = "预警来源对象编码")
    @TableField("SCODE")
    private String scode;

    @ApiModelProperty(value = "预报累计降雨")
    @TableField("DRP")
    private Double drp;

    @ApiModelProperty(value = "预报降雨开始时间")
    @TableField("STARTDATE")
    private LocalDateTime startdate;

    @ApiModelProperty(value = "预报降雨结束时间")
    @TableField("ENDDATE")
    private LocalDateTime enddate;

    @ApiModelProperty(value = "预警数据")
    @TableField("WDATA")
    private Double wdata;

    @ApiModelProperty(value = "雨量降雨步长")
    @TableField("STEP")
    private Double step;

    @ApiModelProperty(value = "预警数据单位")
    @TableField("UNIT")
    private String unit;

    @ApiModelProperty(value = "纳雨能力")
    @TableField("CR")
    private Double cr;

    @ApiModelProperty(value = "一级预警指标")
    @TableField("LEVEL1")
    private Double level1;

    @ApiModelProperty(value = "二级预警指标")
    @TableField("LEVEL2")
    private Double level2;

    @ApiModelProperty(value = "三级预警指标")
    @TableField("LEVEL3")
    private Double level3;

    @ApiModelProperty(value = "预警状态：1.新产生  2.升级   3 持续  4 降级  5 结束")
    @TableField("WARNSTATE")
    private String warnstate;

    @ApiModelProperty(value = "处理状态：00.未处理  10.内部预警  01.外部预警  11.内外部预警")
    @TableField("DISPOSESTATE")
    private String disposestate;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "场次标识")
    @TableField("SSPCD")
    private String sspcd;

    @ApiModelProperty(value = "预警时间：0.过去产生 1.未来产生")
    @TableField("SIG")
    private String sig;

    @TableField("ADDTIME")
    private LocalDateTime addtime;

    @TableField("STATE")
    private String state;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEwcode() {
        return ewcode;
    }

    public void setEwcode(String ewcode) {
        this.ewcode = ewcode;
    }

    public String getEwtype() {
        return ewtype;
    }

    public void setEwtype(String ewtype) {
        this.ewtype = ewtype;
    }

    public LocalDateTime getFtm() {
        return ftm;
    }

    public void setFtm(LocalDateTime ftm) {
        this.ftm = ftm;
    }

    public LocalDateTime getWtm() {
        return wtm;
    }

    public void setWtm(LocalDateTime wtm) {
        this.wtm = wtm;
    }

    public LocalDateTime getNtm() {
        return ntm;
    }

    public void setNtm(LocalDateTime ntm) {
        this.ntm = ntm;
    }

    public String getWlevel() {
        return wlevel;
    }

    public void setWlevel(String wlevel) {
        this.wlevel = wlevel;
    }

    public String getStype() {
        return stype;
    }

    public void setStype(String stype) {
        this.stype = stype;
    }

    public String getScode() {
        return scode;
    }

    public void setScode(String scode) {
        this.scode = scode;
    }

    public Double getDrp() {
        return drp;
    }

    public void setDrp(Double drp) {
        this.drp = drp;
    }

    public LocalDateTime getStartdate() {
        return startdate;
    }

    public void setStartdate(LocalDateTime startdate) {
        this.startdate = startdate;
    }

    public LocalDateTime getEnddate() {
        return enddate;
    }

    public void setEnddate(LocalDateTime enddate) {
        this.enddate = enddate;
    }

    public Double getWdata() {
        return wdata;
    }

    public void setWdata(Double wdata) {
        this.wdata = wdata;
    }

    public Double getStep() {
        return step;
    }

    public void setStep(Double step) {
        this.step = step;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getCr() {
        return cr;
    }

    public void setCr(Double cr) {
        this.cr = cr;
    }

    public Double getLevel1() {
        return level1;
    }

    public void setLevel1(Double level1) {
        this.level1 = level1;
    }

    public Double getLevel2() {
        return level2;
    }

    public void setLevel2(Double level2) {
        this.level2 = level2;
    }

    public Double getLevel3() {
        return level3;
    }

    public void setLevel3(Double level3) {
        this.level3 = level3;
    }

    public String getWarnstate() {
        return warnstate;
    }

    public void setWarnstate(String warnstate) {
        this.warnstate = warnstate;
    }

    public String getDisposestate() {
        return disposestate;
    }

    public void setDisposestate(String disposestate) {
        this.disposestate = disposestate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSspcd() {
        return sspcd;
    }

    public void setSspcd(String sspcd) {
        this.sspcd = sspcd;
    }

    public String getSig() {
        return sig;
    }

    public void setSig(String sig) {
        this.sig = sig;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "EarlyWarning2{" +
        "id=" + id +
        ", ewcode=" + ewcode +
        ", ewtype=" + ewtype +
        ", ftm=" + ftm +
        ", wtm=" + wtm +
        ", ntm=" + ntm +
        ", wlevel=" + wlevel +
        ", stype=" + stype +
        ", scode=" + scode +
        ", drp=" + drp +
        ", startdate=" + startdate +
        ", enddate=" + enddate +
        ", wdata=" + wdata +
        ", step=" + step +
        ", unit=" + unit +
        ", cr=" + cr +
        ", level1=" + level1 +
        ", level2=" + level2 +
        ", level3=" + level3 +
        ", warnstate=" + warnstate +
        ", disposestate=" + disposestate +
        ", remark=" + remark +
        ", sspcd=" + sspcd +
        ", sig=" + sig +
        ", addtime=" + addtime +
        ", state=" + state +
        "}";
    }
}
