package com.huitu.cloud.api.ewci.hydrology.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 洪水预警单位
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警单位")
public class HyWarnUnit implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单位编码")
    @TableField(value = "CODE")
    private String code;

    @ApiModelProperty(value = "单位类型（0=省级、1=市州）")
    @TableField(value = "TYPE")
    private String type;

    @ApiModelProperty(value = "单位简称")
    @TableField(value = "SHORT_NAME")
    private String shortName;

    @ApiModelProperty(value = "单位全称")
    @TableField(value = "FULL_NAME")
    private String fullName;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
}
