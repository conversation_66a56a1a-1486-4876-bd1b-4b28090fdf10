package com.huitu.cloud.api.screen.fhjz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.screen.fhjz.entity.RainCountVo;
import com.huitu.cloud.api.screen.fhjz.entity.RainVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 实时降雨 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-15
 */
public interface RealRainService {

    /**
     * 降雨段 测站数量
     * @param code 流域编码,查全省传空字符串
     * @param type 查询类型
     * @param stm 查询开始时间
     * @param etm 查询结束时间
     *
     * @return
     */
    RainCountVo statisticsRainCount(String code,String type,String stm,String etm);



    /**
     * 降雨值统计
     * @param type 查询类型
     * @param stm 查询开始时间
     * @param etm 查询结束时间
     * @return
     */
    List<Map<String,Object>> statisticsRainValue(String type,String stm,String etm);

    /**
     * 查询降雨量详情
     * @param code
     * @param stType
     * @param rstm
     * @param retm
     * @param fstm
     * @param fetm
     * @param isAnalys
     * @return
     */
    List<RainVo> queryRainList(String code,String stType,String rstm,String retm,String fstm,String fetm,Boolean isAnalys);

    /**
     * 土壤含水率
     *
     * @return
     */
    List<Map<String,Object>> statisticsSoilVaule(String stm,String etm);
}
