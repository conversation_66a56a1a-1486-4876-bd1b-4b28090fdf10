package com.huitu.cloud.api.syq.skdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 水库调度信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@TableName("BSN_SKDT_RV")
@ApiModel(value="BsnSkdtRv对象", description="水库调度信息表")
public class BsnSkdtRv extends Model<BsnSkdtRv> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;
    @ApiModelProperty(value = "水库工程编码")
    private String resCode;
    @ApiModelProperty(value = "水库名称")
    private String resName;

    @ApiModelProperty(value = "排序字段")
    @TableField("SORTNO")
    private Integer sortno;

    @ApiModelProperty(value = "是否展示调度方案  1：显示 0：不显示")
    @TableField("DTFLAG")
    private String dtflag;

    @ApiModelProperty(value = "是否展示洪水预报成果")
    @TableField("FTFLAG")
    private String ftflag;

    @ApiModelProperty(value = "是否展示水库调度演算 1：显示  0：不显示")
    @TableField("YSFLAG")
    private String ysflag;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Integer getSortno() {
        return sortno;
    }

    public void setSortno(Integer sortno) {
        this.sortno = sortno;
    }

    public String getDtflag() {
        return dtflag;
    }

    public void setDtflag(String dtflag) {
        this.dtflag = dtflag;
    }

    public String getFtflag() {
        return ftflag;
    }

    public void setFtflag(String ftflag) {
        this.ftflag = ftflag;
    }

    public String getYsflag() {
        return ysflag;
    }

    public void setYsflag(String ysflag) {
        this.ysflag = ysflag;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "BsnSkdtRv{" +
        "stcd=" + stcd +
        ", sortno=" + sortno +
        ", dtflag=" + dtflag +
        ", ftflag=" + ftflag +
        ", ysflag=" + ysflag +
        "}";
    }
}
