package com.huitu.cloud.api.shyj.stwarnrule.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel
public class RainWarnruleSave implements Serializable {
    @ApiModelProperty(value = " 测站代码 ")
    private String stcd;

    @ApiModelProperty(value = " 预警等级ID 4:立即转移  5：准备转移 ")
    private Double warngradeid;
    @ApiModelProperty(value = " 10分钟指标阈值 ")
    private Double stthreshold10;
    @ApiModelProperty(value = " 30分钟指标阈值 ")
    private Double stthreshold30;
    @ApiModelProperty(value = " 1小时指标阈值 ")
    private Double stthreshold60;
    @ApiModelProperty(value = " 3小时指标阈值 ")
    private Double stthreshold180;

    @ApiModelProperty(value = " 6小时指标阈值 ")
    private Double stthreshold360;

    @ApiModelProperty(value = " 12小时指标阈值 ")
    private Double stthreshold720;

    @ApiModelProperty(value = " 24小时指标阈值 ")
    private Double stthreshold1440;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Double getWarngradeid() {
        return warngradeid;
    }

    public void setWarngradeid(Double warngradeid) {
        this.warngradeid = warngradeid;
    }

    public Double getStthreshold10() {
        return stthreshold10;
    }

    public void setStthreshold10(Double stthreshold10) {
        this.stthreshold10 = stthreshold10;
    }

    public Double getStthreshold30() {
        return stthreshold30;
    }

    public void setStthreshold30(Double stthreshold30) {
        this.stthreshold30 = stthreshold30;
    }

    public Double getStthreshold60() {
        return stthreshold60;
    }

    public void setStthreshold60(Double stthreshold60) {
        this.stthreshold60 = stthreshold60;
    }

    public Double getStthreshold180() {
        return stthreshold180;
    }

    public void setStthreshold180(Double stthreshold180) {
        this.stthreshold180 = stthreshold180;
    }

    public Double getStthreshold360() {
        return stthreshold360;
    }

    public void setStthreshold360(Double stthreshold360) {
        this.stthreshold360 = stthreshold360;
    }

    public Double getStthreshold720() {
        return stthreshold720;
    }

    public void setStthreshold720(Double stthreshold720) {
        this.stthreshold720 = stthreshold720;
    }

    public Double getStthreshold1440() {
        return stthreshold1440;
    }

    public void setStthreshold1440(Double stthreshold1440) {
        this.stthreshold1440 = stthreshold1440;
    }
}
