package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 调查评价汇总统计
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-15
 */
@ApiModel(value="DtAllEvaluationInfo对象", description="调查评价汇总统计")
public class DtAllEvaluationInfo extends DtExtendInfo{
    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区级别")
    private String adlvl;

    @ApiModelProperty(value = "返回标识")
    private String flag;

    @ApiModelProperty(value = "下级统计")
    private List<DtAllEvaluationInfo> children;

    @ApiModelProperty(value = " 人口 ")
    private Double ptcount;

    @ApiModelProperty(value = "户数 ")
    private Double htcount;

    @ApiModelProperty(value = "土地面积")
    private Double ldarea;

    @ApiModelProperty(value = "耕地面积")
    private Double plarea;

    @ApiModelProperty(value = "防治区个数")
    private Double prevcnt;

    @ApiModelProperty(value = " 重点防治区个数")
    private Double imppevcnt;

    @ApiModelProperty(value = "时段雨量预警指标对象总数")
    private Integer dfwrulect;

    @ApiModelProperty(value = "水位预警指标对象总数")
    private Integer wlwrulect;

    @ApiModelProperty(value = "综合雨量预警指标对象总数")
    private Integer yjicrct;

    @ApiModelProperty(value = "防洪现状对象总数")
    private Integer nowfhtbct;

    @ApiModelProperty(value = "临界雨量模型分析法对象总数")
    private Integer fxcgtbct;

    @ApiModelProperty(value = "临界雨量经验估计法对象总数")
    private Integer jygstbct;

    @ApiModelProperty(value = "临界雨量降雨分析法对象总数")
    private Integer yjcjtbct;

    @ApiModelProperty(value = "控制断面设计洪水对象总数")
    private Integer sdtdtbct;

    @ApiModelProperty(value = "控制断面水位-流量-人口关系对象总数")
    private Integer swllrktbct;

    @ApiModelProperty(value = "重要沿河村落居民户总数")
    private Integer flrvvlgct;

    @ApiModelProperty(value = "危险区总数")
    private Integer danadct;

    @ApiModelProperty(value = "重要城（集）镇居民总数")
    private Integer dtresidentct;

    @ApiModelProperty(value = "防治区企事业单位总数")
    private Integer bsnssinfoct;

    @ApiModelProperty(value = "历史山洪总数")
    private Integer hsfwaterct;

    @ApiModelProperty(value = "沟道横断面总数")
    private Integer hsurfacect;

    @ApiModelProperty(value = "沟道纵断面总数")
    private Integer vsurfacect;

    @ApiModelProperty(value = "需防洪治理山洪沟总数")
    private Integer gullyct;

    @ApiModelProperty(value = "水库个数 ")
    private Double rsst;

    @ApiModelProperty(value = "水闸个数 ")
    private Double slust;

    @ApiModelProperty(value = "堤防个数 ")
    private Double dikst;

    @ApiModelProperty(value = "塘坝个数")
    private Double damst;

    @ApiModelProperty(value = "路涵个数")
    private Double culst;

    @ApiModelProperty(value = "桥梁个数 ")
    private Double brist;

    public List<DtAllEvaluationInfo> getChildren() {
        return children;
    }

    public void setChildren(List<DtAllEvaluationInfo> children) {
        this.children = children;
    }

    public String getAdlvl() {
        return adlvl;
    }

    public void setAdlvl(String adlvl) {
        this.adlvl = adlvl;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getPtcount() {
        return ptcount;
    }

    public void setPtcount(Double ptcount) {
        this.ptcount = ptcount;
    }

    public Double getHtcount() {
        return htcount;
    }

    public void setHtcount(Double htcount) {
        this.htcount = htcount;
    }

    public Double getLdarea() {
        return ldarea;
    }

    public void setLdarea(Double ldarea) {
        this.ldarea = ldarea;
    }

    public Double getPlarea() {
        return plarea;
    }

    public void setPlarea(Double plarea) {
        this.plarea = plarea;
    }

    public Double getPrevcnt() {
        return prevcnt;
    }

    public void setPrevcnt(Double prevcnt) {
        this.prevcnt = prevcnt;
    }

    public Double getImppevcnt() {
        return imppevcnt;
    }

    public void setImppevcnt(Double imppevcnt) {
        this.imppevcnt = imppevcnt;
    }

    public Integer getDfwrulect() {
        return dfwrulect;
    }

    public void setDfwrulect(Integer dfwrulect) {
        this.dfwrulect = dfwrulect;
    }

    public Integer getWlwrulect() {
        return wlwrulect;
    }

    public void setWlwrulect(Integer wlwrulect) {
        this.wlwrulect = wlwrulect;
    }

    public Integer getYjicrct() {
        return yjicrct;
    }

    public void setYjicrct(Integer yjicrct) {
        this.yjicrct = yjicrct;
    }

    public Integer getNowfhtbct() {
        return nowfhtbct;
    }

    public void setNowfhtbct(Integer nowfhtbct) {
        this.nowfhtbct = nowfhtbct;
    }

    public Integer getFxcgtbct() {
        return fxcgtbct;
    }

    public void setFxcgtbct(Integer fxcgtbct) {
        this.fxcgtbct = fxcgtbct;
    }

    public Integer getJygstbct() {
        return jygstbct;
    }

    public void setJygstbct(Integer jygstbct) {
        this.jygstbct = jygstbct;
    }

    public Integer getYjcjtbct() {
        return yjcjtbct;
    }

    public void setYjcjtbct(Integer yjcjtbct) {
        this.yjcjtbct = yjcjtbct;
    }

    public Integer getSdtdtbct() {
        return sdtdtbct;
    }

    public void setSdtdtbct(Integer sdtdtbct) {
        this.sdtdtbct = sdtdtbct;
    }

    public Integer getSwllrktbct() {
        return swllrktbct;
    }

    public void setSwllrktbct(Integer swllrktbct) {
        this.swllrktbct = swllrktbct;
    }

    public Integer getFlrvvlgct() {
        return flrvvlgct;
    }

    public void setFlrvvlgct(Integer flrvvlgct) {
        this.flrvvlgct = flrvvlgct;
    }

    public Integer getDanadct() {
        return danadct;
    }

    public void setDanadct(Integer danadct) {
        this.danadct = danadct;
    }

    public Integer getDtresidentct() {
        return dtresidentct;
    }

    public void setDtresidentct(Integer dtresidentct) {
        this.dtresidentct = dtresidentct;
    }

    public Integer getBsnssinfoct() {
        return bsnssinfoct;
    }

    public void setBsnssinfoct(Integer bsnssinfoct) {
        this.bsnssinfoct = bsnssinfoct;
    }

    public Integer getHsfwaterct() {
        return hsfwaterct;
    }

    public void setHsfwaterct(Integer hsfwaterct) {
        this.hsfwaterct = hsfwaterct;
    }

    public Integer getHsurfacect() {
        return hsurfacect;
    }

    public void setHsurfacect(Integer hsurfacect) {
        this.hsurfacect = hsurfacect;
    }

    public Integer getVsurfacect() {
        return vsurfacect;
    }

    public void setVsurfacect(Integer vsurfacect) {
        this.vsurfacect = vsurfacect;
    }

    public Integer getGullyct() {
        return gullyct;
    }

    public void setGullyct(Integer gullyct) {
        this.gullyct = gullyct;
    }

    public Double getRsst() {
        return rsst;
    }

    public void setRsst(Double rsst) {
        this.rsst = rsst;
    }

    public Double getSlust() {
        return slust;
    }

    public void setSlust(Double slust) {
        this.slust = slust;
    }

    public Double getDikst() {
        return dikst;
    }

    public void setDikst(Double dikst) {
        this.dikst = dikst;
    }

    public Double getDamst() {
        return damst;
    }

    public void setDamst(Double damst) {
        this.damst = damst;
    }

    public Double getCulst() {
        return culst;
    }

    public void setCulst(Double culst) {
        this.culst = culst;
    }

    public Double getBrist() {
        return brist;
    }

    public void setBrist(Double brist) {
        this.brist = brist;
    }
}
