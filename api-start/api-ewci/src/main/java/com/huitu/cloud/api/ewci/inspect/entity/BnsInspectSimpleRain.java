package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 简易雨量站
 */
@Data
@ApiModel(value = "简易雨量站")
public class BnsInspectSimpleRain extends BnsInspectReportForm {

    @ApiModelProperty(value = "村屯政区编码")
    private String tadcd;

    @ApiModelProperty(value = "简易雨量站名称")
    private String srstnm;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;

    @ApiModelProperty(value = "村屯政区名称")
    private String tadnm;

}
