package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 强降雨告警信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-01
 */
@TableName("BSN_Heavyrainfallalarm_B")
@ApiModel(value="BSN_Heavyrainfallalarm_B", description="强降雨告警信息表")
public class BSN_Heavyrainfallalarm_B extends Model<BSN_Heavyrainfallalarm_B> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "告警开始时间")
    @TableField("ALARMSTM")
    private LocalDateTime alarmstm;

    @ApiModelProperty(value = "告警信息")
    @TableField("ALARMDESC")
    private String alarmdesc;

    @ApiModelProperty(value = "雨量")
    @TableField("DRP")
    private BigDecimal drp;

    @ApiModelProperty(value = "备注")
    @TableField("REAMRK")
    private String reamrk;

    @ApiModelProperty(value = "入库时间")
    @TableField("INPUTTM")
    private LocalDateTime inputtm;

    @ApiModelProperty(value = "市级告警时间")
    @TableField("CITYTM")
    private LocalDateTime citytm;

    @ApiModelProperty(value = "县级告警时间")
    @TableField("COUNTYTM")
    private LocalDateTime countytm;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getAlarmstm() {
        return alarmstm;
    }

    public void setAlarmstm(LocalDateTime alarmstm) {
        this.alarmstm = alarmstm;
    }

    public String getAlarmdesc() {
        return alarmdesc;
    }

    public void setAlarmdesc(String alarmdesc) {
        this.alarmdesc = alarmdesc;
    }

    public BigDecimal getDrp() {
        return drp;
    }

    public void setDrp(BigDecimal drp) {
        this.drp = drp;
    }

    public String getReamrk() {
        return reamrk;
    }

    public void setReamrk(String reamrk) {
        this.reamrk = reamrk;
    }

    public LocalDateTime getInputtm() {
        return inputtm;
    }

    public void setInputtm(LocalDateTime inputtm) {
        this.inputtm = inputtm;
    }

    public LocalDateTime getCitytm() {
        return citytm;
    }

    public void setCitytm(LocalDateTime citytm) {
        this.citytm = citytm;
    }

    public LocalDateTime getCountytm() {
        return countytm;
    }

    public void setCountytm(LocalDateTime countytm) {
        this.countytm = countytm;
    }
}
