package com.huitu.cloud.api.ew.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.response.EwRiverWarningRecordResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarningGradeSummaryResponse;
import com.huitu.cloud.api.ew.entity.sms.SmsMessage;
import com.huitu.cloud.api.ew.entity.sms.SmsReceiver;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwRiverWarningRecordDao {

    /**
     * 获取预警分页列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 预警分页列表
     **/
    IPage<EwRiverWarningRecordResponse> getPageList(IPage<EwRiverWarningRecordResponse> page, @Param("map") Map<String, Object> params);

    /**
     * 根据预警ID，获取预警记录
     *
     * @param warnId 预警ID
     * @return 预警记录
     */
    EwRiverWarningRecordResponse getRecord(String warnId);

    /**
     * 河道预警等级统计
     * @param params
     * @return
     */
    List<EwWarningGradeSummaryResponse> getRiverWarningGradeSummary(@Param("map") Map<String, Object> params);

    /**
     * 获取测站最新水位
     *
     * @param stcd 测站编码
     * @return 最新水位
     */
    BigDecimal getLatestZ(String stcd);

    /**
     * 获取预警更正消息
     *
     * @param params 查询参数
     * @return 消息
     */
    SmsMessage getCorrectMessage(@Param("map") Map<String, Object> params);

    /**
     * 获取预警更正消息接收人
     *
     * @param warnId 预警ID
     * @return 接收人
     */
    List<SmsReceiver> getCorrectReceivers(String warnId);

    /**
     * 更正预警
     *
     * @param warnId 预警ID
     * @return 受影响的行数
     */
    int correct(String warnId);
}
