package com.huitu.cloud.api.ewci.tencent.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("推送消息的扩展数据")
public class MobPushExtraVO {

    @ApiModelProperty(value = "消息发送人ID")
    private String msgFromUid;

    @ApiModelProperty(value = "参会人ID")
    private List<TuiRoomKitUsersVO> userList;

    @ApiModelProperty(value = "消息创建人ID")
    private String createId;

    @ApiModelProperty(value = "消息创建人姓名")
    private String createName;

    @ApiModelProperty(value = "会议链接")
    private String invitationLink;

    @ApiModelProperty(value = "房间号")
    private String roomId;

    @ApiModelProperty(value = "会议主题")
    private String title;

    public String getMsgFromUid() {
        return msgFromUid;
    }

    public void setMsgFromUid(String msgFromUid) {
        this.msgFromUid = msgFromUid;
    }

    public List<TuiRoomKitUsersVO> getUserList() {
        return userList;
    }

    public void setUserList(List<TuiRoomKitUsersVO> userList) {
        this.userList = userList;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getInvitationLink() {
        return invitationLink;
    }

    public void setInvitationLink(String invitationLink) {
        this.invitationLink = invitationLink;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
