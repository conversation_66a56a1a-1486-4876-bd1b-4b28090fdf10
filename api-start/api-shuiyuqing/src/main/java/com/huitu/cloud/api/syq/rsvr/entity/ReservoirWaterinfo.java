package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 水库水情信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-18
 */
@ApiModel(value="ReservoirWaterRegime对象", description="水库水情信息")
public class ReservoirWaterinfo implements Serializable {
    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "水库名称")
    @TableId(value = "STNM", type = IdType.NONE)
    private String stnm;

    @ApiModelProperty(value = "水位")
    @TableId(value = "RZ", type = IdType.NONE)
    private String rz;

    @ApiModelProperty(value = "蓄水量")
    @TableId(value = "W", type = IdType.NONE)
    private String w;

    @ApiModelProperty(value = "出库流量")
    @TableId(value = "OTQ", type = IdType.NONE)
    private String otq;

    @ApiModelProperty(value = "入库流量")
    @TableId(value = "INQ", type = IdType.NONE)
    private String inq;

    @ApiModelProperty(value = "库水水势")
    @TableId(value = "RWPTN", type = IdType.NONE)
    private String rwptn;

    @ApiModelProperty(value = "观测时间")
    @TableId(value = "TM", type = IdType.NONE)
    private Date tm;

    @ApiModelProperty(value = "填报人")
    @TableId(value = "WRTER", type = IdType.NONE)
    private String wrter;

    @ApiModelProperty(value = "照片文件路径")
    @TableId(value = "FILEURL", type = IdType.NONE)
    private String fileurl;

    @ApiModelProperty(value = "照片文件名")
    @TableId(value = "FILENM", type = IdType.NONE)
    private String filenm;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRz() {
        return rz;
    }

    public void setRz(String rz) {
        this.rz = rz;
    }

    public String getW() {
        return w;
    }

    public void setW(String w) {
        this.w = w;
    }

    public String getOtq() {
        return otq;
    }

    public void setOtq(String otq) {
        this.otq = otq;
    }

    public String getInq() {
        return inq;
    }

    public void setInq(String inq) {
        this.inq = inq;
    }

    public String getRwptn() {
        return rwptn;
    }

    public void setRwptn(String rwptn) {
        this.rwptn = rwptn;
    }

    public Date getTm() {
        return tm;
    }

    public void setTm(Date tm) {
        this.tm = tm;
    }

    public String getWrter() {
        return wrter;
    }

    public void setWrter(String wrter) {
        this.wrter = wrter;
    }

    public String getFileurl() {
        return fileurl;
    }

    public void setFileurl(String fileurl) {
        this.fileurl = fileurl;
    }

    public String getFilenm() {
        return filenm;
    }

    public void setFilenm(String filenm) {
        this.filenm = filenm;
    }
}