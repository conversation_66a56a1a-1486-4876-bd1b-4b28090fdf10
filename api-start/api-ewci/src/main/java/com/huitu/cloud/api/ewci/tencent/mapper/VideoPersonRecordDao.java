package com.huitu.cloud.api.ewci.tencent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huitu.cloud.api.ewci.tencent.entity.VideoPersonRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会议人员记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface VideoPersonRecordDao extends BaseMapper<VideoPersonRecord> {

    /**
     * 根据房间号、成员id和状态获取房间记录信息
     *
     * @param roomId 房间号
     * @param memberId 成员id
     * @param sign 成员在房间中的状态
     * @return List<VideoPersonRecord>
     */
    List<VideoPersonRecord> queryVideoRecord(@Param("roomId") String roomId, @Param("memberId") String memberId, @Param("sign") int sign);

    /**
     * 查询用户 部门 部门ID 政区 政区ID
     * @param memberId 用户ID
     * @return VideoPersonRecord
     */
    VideoPersonRecord selectExtInfo(@Param("memberId") String memberId);
}
