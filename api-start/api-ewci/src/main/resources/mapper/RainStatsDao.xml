<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rain.mapper.RainStatsDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getRainRainHourlyList" resultType="com.huitu.cloud.api.ewci.rain.entity.RainHourly">
        SELECT A.STCD, STNM, XADCD ADCD, XADNM ADNM, STADTP, TM, DRP FROM ST_PPTN_R A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        WHERE TM > #{stm} AND TM &lt;= #{etm} AND INTV = 1 AND DRP > 0 AND CHARINDEX(STADTP, #{stadtps}) > 0
        <if test="code != null and code != ''">
            <if test='"ad".equalsIgnoreCase(type)'>
                <choose>
                    <when test="scopes == '1'.toString()">
                        AND LEFT(ADCD, #{level}) = LEFT(#{code}, #{level})
                        <if test="level == '4'.toString()">
                            AND LEFT(ADCD, 6) NOT IN ('220581')
                        </if>
                    </when>
                    <when test="scopes == '2'.toString()">
                        AND LEFT(ADCD, 2) != '22'
                    </when>
                    <otherwise>
                        AND ((LEFT(ADCD, #{level}) = LEFT(#{code}, #{level})
                        <if test="level == '4'.toString()">
                            AND LEFT(ADCD, 6) NOT IN ('220581')
                        </if>)
                        OR LEFT(ADCD, 2) != '22')
                    </otherwise>
                </choose>
            </if>
            <if test='"ws".equalsIgnoreCase(type)'>
                AND EXISTS(SELECT 8 FROM BSN_BAS_ST WHERE BAS_CODE = #{code} AND STCD = A.STCD)
            </if>
        </if>
    </select>
</mapper>
