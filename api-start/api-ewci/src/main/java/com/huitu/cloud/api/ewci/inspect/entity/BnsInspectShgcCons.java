package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 水毁工程修复建设
 */
@Data
@ApiModel(value = "水毁工程修复建设")
public class BnsInspectShgcCons extends BnsInspectReportForm {

    @ApiModelProperty(value = "水毁修复工程编码")
    private String enCode;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;

    @ApiModelProperty(value = "工程名称")
    private String enName;
}
