package com.huitu.cloud.api.shyj.evaluation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import com.huitu.cloud.api.shyj.warn.entity.WarnFeedback;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 调查成果  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public interface ExamService {
    /**
     * 根据政区编码、年月获取与省级部门配合得分
     * @return
     */
    List<ExamProvinceScore> getExamProvinceScoreListByAdcd(String adcd, String month);
    /**
     * 添加修改年月省级部门得分
     *
     * @return
     */
    Integer addOrUpdateExamProvinceScoreByAdcdAndMonth(ExamProvinceScore examProvinceScore);
    /**
     * 获取所有考核指标
     * @return
     */
    List<ExamIndex> getExamIndexList();
    /**
     * 根据政区编码、年月获取市县得分
     * @return
     */
    List<ExamCityScore> getExamCityScoreListByYm(String month);
    /**
     * 添加修改年月市县得分
     *
     * @return
     */
    Integer addOrUpdateExamCityScoreByAdcdAndMonth(ExamCityScore examCityScore);

    boolean saveExamIndex(ExamIndex entity);

    List<ExamIndex> getExamIndexById(ExamIndex entity);

    boolean updateExamIndex(ExamIndex entity);

    boolean deleteExamIndex(String examType, String examOption);

    IPage<ExamIndex> getExamIndexListPage(String type, int pageNum, int pageSize);

}
