package com.huitu.cloud.api.ew.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.base.*;
import com.huitu.cloud.api.ew.entity.common.PushMode;
import com.huitu.cloud.api.ew.entity.ext.*;
import com.huitu.cloud.api.ew.entity.request.*;
import com.huitu.cloud.api.ew.entity.response.*;
import com.huitu.cloud.api.ew.entity.sms.SmsMessage;
import com.huitu.cloud.api.ew.entity.sms.SmsReceiver;
import com.huitu.cloud.api.ew.exception.WarningException;
import com.huitu.cloud.api.ew.mapper.*;
import com.huitu.cloud.api.ew.service.EwWarningService;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import com.huitu.cloud.config.properties.ApplicationProperties;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 山洪预警服务 实现类
 */
@Service
@EnableConfigurationProperties(EwWarningServiceImpl.Properties.class)
public class EwWarningServiceImpl implements EwWarningService {

    private ApplicationProperties applicationProperties;
    private Properties warningProperties;
    private EwWarningConfDao confDao;
    private EwWarningGradeDao gradeDao;
    private EwWarningStatusDao statusDao;
    private EwWarningRecordDao recordDao;
    private EwWarningProcessDao processDao;
    private EwWarningFlowDao flowDao;
    private EwRelWarningMessageDao relWarningDao;
    private EwMessageDao messageDao;
    private EwReceiverDao receiverDao;
    private EwSnapshotIndexDao indexDao;
    private EwSnapshotMonitorDao monitorDao;
    private EwSnapshotPptnDao pptnDao;
    private EwWarningSendingDao sendingDao;
    private EwCallFeedbackDao callDao;
    private SmsSendDao sendDao;
    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;

    @Autowired
    public void setApplicationProperties(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @Autowired
    public void setWarningProperties(Properties warningProperties) {
        this.warningProperties = warningProperties;
    }

    @Autowired
    public void setConfDao(EwWarningConfDao confDao) {
        this.confDao = confDao;
    }

    @Autowired
    public void setGradeDao(EwWarningGradeDao gradeDao) {
        this.gradeDao = gradeDao;
    }

    @Autowired
    public void setStatusDao(EwWarningStatusDao statusDao) {
        this.statusDao = statusDao;
    }

    @Autowired
    public void setRecordDao(EwWarningRecordDao recordDao) {
        this.recordDao = recordDao;
    }

    @Autowired
    public void setProcessDao(EwWarningProcessDao processDao) {
        this.processDao = processDao;
    }

    @Autowired
    public void setFlowDao(EwWarningFlowDao flowDao) {
        this.flowDao = flowDao;
    }

    @Autowired
    public void setRelWarningDao(EwRelWarningMessageDao relWarningDao) {
        this.relWarningDao = relWarningDao;
    }

    @Autowired
    public void setMessageDao(EwMessageDao messageDao) {
        this.messageDao = messageDao;
    }

    @Autowired
    public void setReceiverDao(EwReceiverDao receiverDao) {
        this.receiverDao = receiverDao;
    }

    @Autowired
    public void setIndexDao(EwSnapshotIndexDao indexDao) {
        this.indexDao = indexDao;
    }

    @Autowired
    public void setMonitorDao(EwSnapshotMonitorDao monitorDao) {
        this.monitorDao = monitorDao;
    }

    @Autowired
    public void setPptnDao(EwSnapshotPptnDao pptnDao) {
        this.pptnDao = pptnDao;
    }

    @Autowired
    public void setSendingDao(EwWarningSendingDao sendingDao) {
        this.sendingDao = sendingDao;
    }

    @Autowired
    public void setCallDao(EwCallFeedbackDao callDao) {
        this.callDao = callDao;
    }

    @Autowired
    public void setSendDao(SmsSendDao sendDao) {
        this.sendDao = sendDao;
    }

    @Override
    public List<EwWarningGrade> getGradeList() {
        return gradeDao.getAllList();
    }

    @Override
    public List<EwWarningStatus> getStatusList() {
        return statusDao.getAllList();
    }

    @Override
    public String getFullAdnm(EwFullAdnmRequest request) {
        return recordDao.getFullAdnm(request.toQuery());
    }

    @Override
    public IPage<EwWarningRecordResponse> getPageList(EwWarningRecordRequest request) {
        return recordDao.getPageList(request.toPage(), request.toQuery());
    }

    @Override
    public IPage<EwWarningRecordResponse> getLatestPageList(EwWarningRecordRequest request) {
        IPage<EwWarningRecordResponse> page = recordDao.getLatestPageList(request.toPage(), request.toQuery());
        if (null != page) {
            List<EwWarningRecordResponse> records = page.getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                records.forEach(itm -> {
                    itm.setLatestWarn(true);
                    itm.setRequest(request);
                });
            }
        }
        return page;
    }

    @Override
    public List<EwWarningDigestResponse> getDigestList(List<String> warnIds) {
        warnIds = warnIds.stream().distinct().collect(Collectors.toList());
        if (warnIds.size() <= 500) {
            return recordDao.getDigestList(warnIds);
        }
        List<List<String>> lists = ListUtils.splitList(warnIds, 500);
        List<CompletableFuture<List<EwWarningDigestResponse>>> futures = new ArrayList<>();
        for (List<String> list : lists) {
            futures.add(CompletableFuture.supplyAsync(() -> recordDao.getDigestList(list), asyncExecutor));
        }
        List<EwWarningDigestResponse> digests = Collections.synchronizedList(new ArrayList<>());
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .whenComplete((v, th) -> futures.forEach(cf -> {
                    try {
                        digests.addAll(cf.get());
                    } catch (Exception ignored) {
                    }
                })).join();
        return digests;
    }

    @Override
    public EwWarningRecordResponse getRecord(String warnId, Integer warnGradeId) {
        return recordDao.getRecord(warnId, warnGradeId);
    }

    @Override
    public List<EwMessageReceiver> getMessageReceiverList(String villageAdcd, String userAdcd) {
        CompletableFuture<List<EwMessageReceiver>> fddpPersonFuture =
                CompletableFuture.supplyAsync(() -> recordDao.getFddpPersonList(userAdcd, AdcdUtil.getAdLevel(userAdcd)), asyncExecutor);
        CompletableFuture<List<EwMessageReceiver>> mfdPersonFuture =
                CompletableFuture.supplyAsync(() -> recordDao.getMfdPersonList(villageAdcd), asyncExecutor);

        List<EwMessageReceiver> receiverList = Collections.synchronizedList(new ArrayList<>());
        CompletableFuture.allOf(fddpPersonFuture, mfdPersonFuture).whenComplete((v, th) -> {
            try {
                receiverList.addAll(fddpPersonFuture.get());
                receiverList.addAll(mfdPersonFuture.get());
            } catch (Exception ignored) {
            }
        }).join();
        return receiverList;
    }

    @Override
    public boolean isEnabled(String platformId) {
        return confDao.isEnabled(platformId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int create(EwWarningCreateRequest request, UserInfos user) {
        EwWarningRecord record = request.toRecord(IdUtil.randomUUID().toUpperCase());
        record.setPlatformId(applicationProperties.getPlatformId());
        int result = recordDao.create(record);
        if (result > 0) {
            processDao.insert(record.getWarnId());

            if (StringUtils.isBlank(user.getLoginnm())) {
                user.setLoginnm(user.getUsername());
            }

            // 流程参数
            Map<String, Object> params = new HashMap<>();
            params.put("warnId", record.getWarnId());
            params.put("createBy", user.getLoginnm());

            // 新产生
            params.put("warnStatusId", record.getWarnStatusId());
            flowDao.insert(params);

            // 处理预警接收人
            List<EwMessageReceiver> receivers = request.getReceivers().stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EwMessageReceiver::getPhoneNo))),
                            ArrayList<EwMessageReceiver>::new
                    )
            );

            // 添加内部预警消息
            List<EwMessageReceiver> insideReceivers = receivers.stream()
                    .filter(itm -> "1".equals(itm.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(insideReceivers)) {
                record.setWarnStatusId(10);
                params.put("warnStatusId", record.getWarnStatusId());
                flowDao.insert(params);
                // 发送预警消息
                sendWarningMessage(record, insideReceivers, request.isXccEnabled(), user);
            }

            // 添加外部预警消息
            List<EwMessageReceiver> outsideReceivers = receivers.stream()
                    .filter(itm -> !"1".equals(itm.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(outsideReceivers)) {
                record.setWarnStatusId(20);
                params.put("warnStatusId", record.getWarnStatusId());
                flowDao.insert(params);
                // 发送预警消息
                sendWarningMessage(record, outsideReceivers, request.isXccEnabled(), user);
            }

            // 更新预警状态
            recordDao.updateStatus(record.getWarnId(), record.getWarnStatusId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int close(EwWarningCloseRequest request, UserInfos user) {
        if (recordDao.close(request.getWarnId()) <= 0) {
            throw new WarningException("当前预警不存在或已关闭，请稍后重试");
        }
        request.setCloseBy(StringUtils.isNotBlank(user.getLoginnm()) ? user.getLoginnm() : user.getUsername());
        return flowDao.insert(request.toInsert());
    }

    @Override
    public List<EwWarningProcessResponse> getProcessList(String warnId) {
        return processDao.getProcessList(warnId);
    }

    @Override
    public List<EwWarningFlowResponse> getFlowList(String warnId) {
        return flowDao.getFlowList(warnId);
    }

    @Override
    public List<EwWarningMessageResponse<?>> getMessageList(String warnId) {
        CompletableFuture<List<EwWarningMessage>> sourceListFuture =
                CompletableFuture.supplyAsync(() -> messageDao.getMessageList(warnId), asyncExecutor);
        CompletableFuture<List<EwSmsReceiver>> smsReceiverListFuture =
                CompletableFuture.supplyAsync(() -> receiverDao.getSmsReceiverList(warnId), asyncExecutor);
        CompletableFuture<List<EwXccReceiver>> xccReceiverListFuture =
                CompletableFuture.supplyAsync(() -> receiverDao.getXccReceiverList(warnId), asyncExecutor);

        AtomicReference<List<EwWarningMessageResponse<?>>> reference = new AtomicReference<>();
        CompletableFuture.allOf(sourceListFuture, smsReceiverListFuture, xccReceiverListFuture).whenComplete((v, th) -> {
            List<EwWarningMessageResponse<?>> messageList = new ArrayList<>();
            try {
                List<EwWarningMessage> sourceList = sourceListFuture.get();
                if (!sourceList.isEmpty()) {
                    // 预警短信接收人
                    Map<String, List<EwWarningMessage>> smsMessageGroup = sourceList.stream()
                            .filter(msg -> PushMode.SMS.equalsCode(msg.getPushMode()))
                            .collect(Collectors.groupingBy(EwWarningMessage::groupVal));
                    if (!smsMessageGroup.isEmpty()) {
                        List<EwSmsReceiver> smsReceivers = smsReceiverListFuture.get();
                        for (Map.Entry<String, List<EwWarningMessage>> group : smsMessageGroup.entrySet()) {
                            List<EwWarningMessage> messagesList = group.getValue();
                            List<String> messageIdList = group.getValue().stream()
                                    .map(EwWarningMessage::getMsgId).collect(Collectors.toList());
                            List<EwSmsReceiver> receivers = smsReceivers.stream()
                                    .filter(msg -> messageIdList.contains(msg.getMsgId())).collect(Collectors.toList());
                            messageList.add(new EwWarningMessageResponse<>(messagesList.get(0), receivers));
                        }
                    }
                    // 预警语音接收人
                    Map<String, List<EwWarningMessage>> xccMessageGroup = sourceList.stream()
                            .filter(msg -> PushMode.XCC.equalsCode(msg.getPushMode()))
                            .collect(Collectors.groupingBy(EwWarningMessage::groupVal));
                    if (!xccMessageGroup.isEmpty()) {
                        List<EwXccReceiver> xccReceivers = xccReceiverListFuture.get();
                        for (Map.Entry<String, List<EwWarningMessage>> group : xccMessageGroup.entrySet()) {
                            List<EwWarningMessage> messagesList = group.getValue();
                            List<String> messageIdList = group.getValue().stream()
                                    .map(EwWarningMessage::getMsgId).collect(Collectors.toList());
                            List<EwXccReceiver> receivers = xccReceivers.stream()
                                    .filter(msg -> messageIdList.contains(msg.getMsgId())).collect(Collectors.toList());
                            messageList.add(new EwWarningMessageResponse<>(messagesList.get(0), receivers));
                        }
                    }
                }
            } catch (Exception ignored) {
            }
            reference.set(messageList);
        }).join();
        return reference.get();
    }

    @Override
    public List<EwSnapshotIndexResponse> getSnapshotIndexList(String warnId) {
        return indexDao.getSnapshotIndexList(warnId);
    }

    @Override
    public EwSnapshotIndexResponse getSnapshotIndex(String warnId, Integer warnGradeId) {
        return indexDao.getSnapshotIndex(warnId, warnGradeId);
    }

    @Override
    public EwSnapshotMonitorResponse getSnapshotMonitor(String warnId, Integer warnGradeId) {
        return monitorDao.getSnapshotMonitor(warnId, warnGradeId);
    }

    @Override
    public IPage<EwWarningSending> getSendingPageList(EwWarningSendingRequest<EwWarningSending> request) {
        IPage<EwWarningSending> page = sendingDao.getSendingPageList(request.toPage(), request.toQuery());
        if (null != page) {
            EwSendingBase.handleXccData(page.getRecords());
        }
        return page;
    }

    @Override
    public EwWarningFlag getWarningFlag(String msgId) {
        return relWarningDao.getWarningFlag(msgId);
    }

    @Override
    public List<EwWarningCorrectResponse> getCorrectList(String warnId) {
        return recordDao.getCorrectList(warnId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int correct(EwWarningCorrectRequest request, UserInfos user) {
        request.setCorrectBy(StringUtils.isNotBlank(user.getLoginnm()) ? user.getLoginnm() : user.getUsername());
        int result = recordDao.correct(request.getWarnIdString());
        if (result != request.getWarnIdCount() || result != flowDao.batchInsert(request.toInsert())) {
            throw new WarningException("预警状态已发生改变，请稍后重试");
        }

        List<SmsReceiver> receivers = recordDao.getCorrectReceivers(request.getWarnIdString());
        if (!CollectionUtils.isEmpty(receivers)) {
            SmsMessage message = new SmsMessage();
            message.setMsgId(IdUtil.fastUUID().toUpperCase());
            message.setBusinessKey(request.getBusinessKey());
            message.setSmsType("0");
            message.setSignName(sendDao.getSignName(applicationProperties.getPlatformId(), applicationProperties.getSmsSignName()));
            message.setTemplateCode(warningProperties.getCorrectSmsTemplate());
            message.setSmsContent(request.getSmsContent());
            message.setSender(request.getCorrectBy());
            message.setUnitCode(user.getAdcd());
            message.setUnitName(user.getDeptnm());
            message.setAccessKeyId(user.getUsername());
            message.setPlatformId(applicationProperties.getPlatformId());
            if (sendDao.saveMessage(message) > 0) {
                List<List<SmsReceiver>> lists = ListUtils.splitList(receivers, 200);
                for (List<SmsReceiver> list : lists) {
                    sendDao.saveReceivers(message.getMsgId(), list, message.getPlatformId());
                }
            }
        }
        return result;
    }

    @Override
    public EwShMonitorDataResponse getMonitorData(String msgId) {
        return monitorDao.getMonitorData(msgId);
    }

    @Override
    public List<EwShVillageDataResponse> getVillageList(String msgId) {
        return indexDao.getVillageList(msgId);
    }

    @Override
    public List<EwShPptnDataResponse> getPptnList(String msgId) {
        List<EwShPptnDataResponse> pptnList = pptnDao.getPptnList(msgId);
        if (!CollectionUtils.isEmpty(pptnList)) {
            Double currentAccp = null;
            for (EwShPptnDataResponse pptn : pptnList) {
                if (null == currentAccp) {
                    currentAccp = pptn.getDrp();
                } else {
                    currentAccp += pptn.getDrp();
                }
                pptn.setAccp(currentAccp);
            }
        }
        return pptnList;
    }

    /**
     * 发送预警消息
     *
     * @param record     预警记录
     * @param receivers  预警接收人
     * @param xccEnabled 是否语音呼叫
     * @param user       当前登录用户
     */
    private void sendWarningMessage(EwWarningRecord record, List<EwMessageReceiver> receivers, boolean xccEnabled, UserInfos user) {
        // 预警消息
        EwMessage warnMessage = new EwMessage();
        warnMessage.setMsgId(IdUtil.randomUUID().toUpperCase());
        warnMessage.setMsgType("11");
        warnMessage.setPushMode("1");
        warnMessage.setSender(user.getLoginnm());
        warnMessage.setSendDept(user.getDeptnm());
        messageDao.insert(warnMessage);

        // 预警与消息关联
        EwRelWarningMessage relWarning = new EwRelWarningMessage();
        relWarning.setWarnId(record.getWarnId());
        relWarning.setMsgId(warnMessage.getMsgId());
        relWarning.setGradeId(record.getWarnGradeId());
        relWarning.setStatusId(record.getWarnStatusId());
        relWarningDao.insert(relWarning);

        // 预警接收人
        List<EwReceiver> warnReceivers = new ArrayList<>();
        for (EwMessageReceiver receiver : receivers) {
            EwReceiver warnReceiver = new EwReceiver();
            warnReceiver.setMsgId(warnMessage.getMsgId());
            warnReceiver.setPhoneNo(receiver.getPhoneNo());
            warnReceiver.setReceiver(receiver.getReceiver());
            warnReceiver.setPosition(receiver.getPosition());
            warnReceiver.setTag(receiver.getTag());
            warnReceivers.add(warnReceiver);
        }
        receiverDao.batchInsert(warnReceivers);

        // 叫应反馈
        List<EwCallFeedback> feedbacks = new ArrayList<>();
        for (EwMessageReceiver receiver : receivers) {
            EwCallFeedback feedback = new EwCallFeedback();
            feedback.setCallId(IdUtil.simpleUUID().toLowerCase());
            feedback.setCallType("1");
            feedback.setSmsWarnMsgId(warnMessage.getMsgId());
            feedback.setPhoneNo(receiver.getPhoneNo());
            feedback.setXccTimes((short) (xccEnabled ? 0 : -1));
            feedback.setRemark(receiver.getReceiver());
            feedbacks.add(feedback);
        }
        callDao.batchInsert(feedbacks);

        // 短信消息
        SmsMessage smsMessage = new SmsMessage();
        smsMessage.setMsgId(IdUtil.randomUUID().toUpperCase());
        smsMessage.setBusinessKey(warnMessage.getMsgId());
        smsMessage.setSmsType("0");
        smsMessage.setSignName(sendDao.getSignName(applicationProperties.getPlatformId(), applicationProperties.getSmsSignName()));
        smsMessage.setTemplateCode(warningProperties.getSuddenSmsTemplate());
        smsMessage.setSmsContent(recordDao.getSuddenMessageParameter(record.getWarnId()));
        smsMessage.setSender(user.getLoginnm());
        smsMessage.setUnitCode(user.getAdcd());
        smsMessage.setUnitName(user.getDeptnm());
        smsMessage.setAccessKeyId(user.getUsername());
        smsMessage.setPlatformId(applicationProperties.getPlatformId());
        sendDao.saveMessage(smsMessage);

        // 短信接收人
        List<SmsReceiver> smsReceivers = new ArrayList<>();
        for (EwCallFeedback feedback : feedbacks) {
            SmsReceiver smsReceiver = new SmsReceiver();
            smsReceiver.setPhoneNo(feedback.getPhoneNo());
            smsReceiver.setReceiver(feedback.getRemark());
            smsReceiver.setMsgExtend(feedback.getCallId());
            smsReceivers.add(smsReceiver);
        }
        sendDao.saveReceivers(smsMessage.getMsgId(), smsReceivers, smsMessage.getPlatformId());
    }

    @ConfigurationProperties(prefix = Properties.PREFIX, ignoreInvalidFields = true)
    public static class Properties {
        public static final String PREFIX = "huitu.warning";

        /**
         * 突发预警短信模版CODE
         */
        private String suddenSmsTemplate;
        /**
         * 预警更正短信模版CODE
         */
        private String correctSmsTemplate;

        public String getSuddenSmsTemplate() {
            return suddenSmsTemplate;
        }

        public void setSuddenSmsTemplate(String suddenSmsTemplate) {
            this.suddenSmsTemplate = suddenSmsTemplate;
        }

        public String getCorrectSmsTemplate() {
            return correctSmsTemplate;
        }

        public void setCorrectSmsTemplate(String correctSmsTemplate) {
            this.correctSmsTemplate = correctSmsTemplate;
        }
    }
}
