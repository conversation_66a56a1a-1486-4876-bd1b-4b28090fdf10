package com.huitu.cloud.api.datamanager.manualreport.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 人工报讯对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-24
 */
@ApiModel(value="QueryManualReport对象", description="人工报讯参数")
public class QueryManualReport {
    @ApiModelProperty(value = "站码")
    private String stcd;

    @ApiModelProperty(value = "表名")
    private String tbnm;

    @ApiModelProperty(value = "时间")
    private String tm;

    @ApiModelProperty(value = "水位")
    private String z;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getTbnm() {
        return tbnm;
    }

    public void setTbnm(String tbnm) {
        this.tbnm = tbnm;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getZ() {
        return z;
    }

    public void setZ(String z) {
        this.z = z;
    }
}
