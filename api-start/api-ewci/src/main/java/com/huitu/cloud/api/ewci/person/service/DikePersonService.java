package com.huitu.cloud.api.ewci.person.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

/**
 * 堤防责任人管理服务
 *
 * <AUTHOR>
 */
public interface DikePersonService {

    /**
     * 分页获取堤防责任人信息列表
     *
     * @param query 查询条件
     * @return 分页后的信息列表
     **/
    IPage<DikePerson> getPageList(DikePersonQuery query);

    /**
     * 堤防责任人汇总统计
     *
     * @param query
     * @return
     */
    List<DikeSummaryVo> getDikePersonSummaryList(DikeSummaryQuery query);

    /**
     * 数据导出
     *
     * @param query  查询条件
     * @param output 输出流
     **/
    void dataExport(DikePersonQuery query, OutputStream output);

    /**
     * 导出全部
     *
     * @param output 输出流
     **/
    void dataExport(OutputStream output);

    /**
     * 数据导入
     *
     * @param xadcd 县级行政区划代码
     * @param input 输入流
     * @return 导入结果
     **/
    ImportResult<BnsDikePersonTmp> dataImport(String xadcd, InputStream input);

    /**
     * 获取堤防责任人信息列表（仅包含姓名和手机号码）
     *
     * @param query 查询对象
     * @return 信息列表
     **/
    List<PersonInfo> getSimplePersonList(PersonQuery query);

    /**
     * 编辑堤防责任人
     *
     * @param entity
     * @return
     */
    int dikePersonEdit(DikePerson entity);
}
