package com.huitu.cloud.api.ewci.monitor.controller;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.monitor.entity.IaCDanad;
import com.huitu.cloud.api.ewci.monitor.entity.IaCPrevad;
import com.huitu.cloud.api.ewci.monitor.entity.RainData;
import com.huitu.cloud.api.ewci.monitor.entity.RealtimeQuery;
import com.huitu.cloud.api.ewci.monitor.entity.RiverData;
import com.huitu.cloud.api.ewci.monitor.entity.RsvrData;
import com.huitu.cloud.api.ewci.monitor.service.DanadService;
import com.huitu.cloud.api.ewci.monitor.service.PeripheralInfoService;
import com.huitu.cloud.api.ewci.monitor.service.PrevadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 周边信息
 *
 * <AUTHOR>
 */
@Api(tags = "周边信息")
@RestController
@RequestMapping("/api/ewci/monitor/peripheral")
public class PeripheralInfoResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "7964469b-59c4-1ad7-0516-c74ba5985109";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final PeripheralInfoService baseService;
    private final PrevadService prevadService;
    private final DanadService danadService;

    public PeripheralInfoResource(PeripheralInfoService baseService, PrevadService prevadService, DanadService danadService) {
        this.baseService = baseService;
        this.prevadService = prevadService;
        this.danadService = danadService;
    }

    @ApiOperation(value = "获取防治区基本情况调查成果汇总列表", notes = "作者：曹宝金")
    @GetMapping("select-prevad-list/{type}/{adcd}")
    public ResponseEntity<SuccessResponse<List<IaCPrevad>>> getPrevadList(@PathVariable(value = "type") String type, @PathVariable(value = "adcd") String adcd) {
        if (StringUtils.isBlank(adcd)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", prevadService.getPrevadList(type, adcd)));
    }

    @ApiOperation(value = "获取危险区基本情况调查成果汇总列表", notes = "作者：曹宝金")
    @GetMapping("select-danad-list/{type}/{adcd}")
    public ResponseEntity<SuccessResponse<List<IaCDanad>>> getDanadList(@PathVariable(value = "type") String type, @PathVariable(value = "adcd") String adcd) {
        if (StringUtils.isBlank(adcd)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", danadService.getDanadList(type, adcd)));
    }

    @ApiOperation(value = "获取降雨数据列表", notes = "作者：曹宝金")
    @PostMapping("select-rain-data-list")
    public ResponseEntity<SuccessResponse<List<RainData>>> getRainDataList(@RequestBody @Validated RealtimeQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRainDataList(query)));
    }

    @ApiOperation(value = "获取水库数据列表", notes = "作者：曹宝金")
    @PostMapping("select-rsvr-data-list")
    public ResponseEntity<SuccessResponse<List<RsvrData>>> getRsvrDataList(@RequestBody @Validated RealtimeQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRsvrDataList(query)));
    }

    @ApiOperation(value = "获取河道数据列表", notes = "作者：曹宝金")
    @PostMapping("select-river-data-list")
    public ResponseEntity<SuccessResponse<List<RiverData>>> getRiverDataList(@RequestBody @Validated RealtimeQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverDataList(query)));
    }
}
