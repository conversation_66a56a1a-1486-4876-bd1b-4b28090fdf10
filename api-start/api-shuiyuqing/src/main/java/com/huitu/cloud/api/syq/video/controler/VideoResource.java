package com.huitu.cloud.api.syq.video.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.syq.video.entity.*;
import com.huitu.cloud.api.syq.video.service.VideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-26
 */

@RestController
@Api(tags = "视频监控信息查询接口")
@RequestMapping("/api/syq/video")
public class VideoResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "c0ab104c-ef3a-473e-baca-a0357173552c";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private VideoService videoService;

    @ApiOperation(value = "查询视频站列表信息", notes = "分页查询视频站列表信息")
    @PostMapping(value = "select-video-info-by-page")
    public ResponseEntity<SuccessResponse<List<BsnVdstinfo>>> getVideoInfoByPage(@RequestBody QueryVideoInfo videoInfo) throws Exception {
        List<BsnVdstinfo> list = videoService.getVideoInfoByPage(videoInfo.getAdcd(), videoInfo.getBscd(), videoInfo.getName(), videoInfo.getTypes());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "视频站列表信息导出",notes="视频站列表信息导出")
    @PostMapping(value = "export-video-info")
    public void exportVideoInfo(@RequestBody QueryVideoInfo videoInfo) throws Exception {
        videoService.exportVideoInfo(videoInfo.getAdcd(), videoInfo.getBscd(), videoInfo.getName(), videoInfo.getTypes());
    }

    @ApiOperation(value = "查询视频站单站信息", notes = "查询视频站单站信息")
    @GetMapping(value = "select-video-info-by-id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "视频站编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<BsnVdstinfo>> getVideoInfoById(@RequestParam String id) throws Exception {
        BsnVdstinfo vdstinfo = videoService.getVideoInfoById(id);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", vdstinfo));
    }

    @ApiOperation(value = "根据测站编码查询视频站单站整体信息", notes = "根据测站编码查询视频站单站整体信息")
    @GetMapping(value = "select-video-info-by-code")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "测站编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<BsnVdstinfo>>> getVideoInfoByCode(@RequestParam String code) throws Exception {
        List<BsnVdstinfo> vdstinfo = videoService.getVideoInfoByCode(code);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", vdstinfo));
    }

    @ApiOperation(value = "查询防洪工程对象关联视频站信息", notes = "查询防洪工程对象关联视频站信息")
    @GetMapping(value = "select-video-info-by-ennmcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ennmcd", value = "工程编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<BsnVdstinfo>> getVideoInfoByEnnmcd(@RequestParam String ennmcd) throws Exception {
        BsnVdstinfo vdstinfo = videoService.getVideoInfoByEnnmcd(ennmcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", vdstinfo));
    }

    @ApiOperation(value = "查询测站关联单个视频站多个视频头信息", notes = "查询测站关联单个视频站多个视频头信息")
    @GetMapping(value = "select-video-detail-info-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<BsnCcdinfo>>> getVideoDetailInfoByStcd(@RequestParam String stcd) throws Exception {
        List<BsnCcdinfo> list = videoService.getVideoDetailInfoByStcd(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询视频站统计信息", notes = "查询视频站统计信息")
    @PostMapping(value = "select-video-count-info")
    public ResponseEntity<SuccessResponse<VideoCount>> getVideoCountInfo(@RequestBody QueryVideoCount videoInfo) throws Exception {
        VideoCount count = videoService.getVideoCountInfo(videoInfo.getAdcd(), videoInfo.getBscd(), videoInfo.getName(), videoInfo.getTypes());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", count));
    }



}
