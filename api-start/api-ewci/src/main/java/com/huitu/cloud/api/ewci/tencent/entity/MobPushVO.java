package com.huitu.cloud.api.ewci.tencent.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("推送消息")
public class MobPushVO {

    @ApiModelProperty(value = "应用ID")
    private String appId;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "扩展数据临时属性")
    private MobPushExtraVO extraTemp;

    @ApiModelProperty(value = "扩展数据")
    private String extra;

    @ApiModelProperty(value = "标签")
    private String tag;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public MobPushExtraVO getExtraTemp() {
        return extraTemp;
    }

    public void setExtraTemp(MobPushExtraVO extraTemp) {
        this.extraTemp = extraTemp;
    }
}
