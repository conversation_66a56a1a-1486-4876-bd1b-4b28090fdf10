package com.huitu.cloud.api.ewci.feedback.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * <p>
 * 意见反馈信息图片
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@TableName("BNS_FEEDBACK_PIC")
@ApiModel(value = "BnsFeedBackPic对象", description = "意见反馈信息图片")
public class BnsFeedBackPic extends Model<BnsFeedBackPic> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @TableId(value = "ID")
    private String id;

    @ApiModelProperty(value = "主模块")
    @TableField("FEED_ID")
    private String feedId;

    @ApiModelProperty(value = "子模块")
    @TableField("FILE_NAME")
    private String fileName;

    @ApiModelProperty(value = "意见描述")
    @TableField("FILEPATH")
    private String filePath;

    @ApiModelProperty(value = "反馈时间")
    @TableField("UPTM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uptm;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public LocalDateTime getUptm() {
        return uptm;
    }

    public void setUptm(LocalDateTime uptm) {
        this.uptm = uptm;
    }
}