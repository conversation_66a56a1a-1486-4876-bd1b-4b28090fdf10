package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@TableName("ST_RVEVS_R")
@ApiModel(value="StRvevsQuery对象", description="")
public class StRvevsQuery extends Model<StRvevsQuery> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "时段长")
    private Integer sttdrcd;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Integer getSttdrcd() {
        return sttdrcd;
    }

    public void setSttdrcd(Integer sttdrcd) {
        this.sttdrcd = sttdrcd;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

}
