package com.huitu.cloud.api.hsfxt.xzhq.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 蓄滞洪区县区乡镇详情
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
@ApiModel(value = "XzhqXiangQing对象", description = " 蓄滞洪区县区乡镇详情")
public class XzhqXiangQing implements Serializable {
    @ApiModelProperty(value = "行政区编码")
    private String adcd;

    @ApiModelProperty(value = "行政区名称")
    private String adnm;

    @ApiModelProperty(value = "面积")
    private Float mianji;

    @ApiModelProperty(value = "户数")
    private Integer fushu;

    @ApiModelProperty(value = "人口")
    private Integer renkou;

    @ApiModelProperty(value = "房屋间数")
    private Integer fangwujianshu;

    @ApiModelProperty(value = "耕地面积")
    private Float gendimianji;

    @ApiModelProperty(value = "GDP")
    private Float gdp;

    @ApiModelProperty(value = "淹没面积(公顷)")
    private Float yanmomianji;

    @ApiModelProperty(value = "淹没耕地面积(公顷)")
    private Float yanmogendimianji;

    @ApiModelProperty(value = "影响人口(万人)")
    private Integer yinxiangrenkou;

    @ApiModelProperty(value = "影响GDP（万元）")
    private Float yinxianggdp;

    @ApiModelProperty(value = "影响房屋(间)")
    private Integer yinxiangfanwushu;

    @ApiModelProperty(value = "洪水损失（万元）")
    private Float hongzaisunshi;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Float getMianji() {
        return mianji;
    }

    public void setMianji(Float mianji) {
        this.mianji = mianji;
    }

    public Integer getFushu() {
        return fushu;
    }

    public void setFushu(Integer fushu) {
        this.fushu = fushu;
    }

    public Integer getRenkou() {
        return renkou;
    }

    public void setRenkou(Integer renkou) {
        this.renkou = renkou;
    }

    public Integer getFangwujianshu() {
        return fangwujianshu;
    }

    public void setFangwujianshu(Integer fangwujianshu) {
        this.fangwujianshu = fangwujianshu;
    }

    public Float getGendimianji() {
        return gendimianji;
    }

    public void setGendimianji(Float gendimianji) {
        this.gendimianji = gendimianji;
    }

    public Float getGdp() {
        return gdp;
    }

    public void setGdp(Float gdp) {
        this.gdp = gdp;
    }

    public Float getYanmomianji() {
        return yanmomianji;
    }

    public void setYanmomianji(Float yanmomianji) {
        this.yanmomianji = yanmomianji;
    }

    public Float getYanmogendimianji() {
        return yanmogendimianji;
    }

    public void setYanmogendimianji(Float yanmogendimianji) {
        this.yanmogendimianji = yanmogendimianji;
    }

    public Integer getYinxiangrenkou() {
        return yinxiangrenkou;
    }

    public void setYinxiangrenkou(Integer yinxiangrenkou) {
        this.yinxiangrenkou = yinxiangrenkou;
    }

    public Float getYinxianggdp() {
        return yinxianggdp;
    }

    public void setYinxianggdp(Float yinxianggdp) {
        this.yinxianggdp = yinxianggdp;
    }

    public Integer getYinxiangfanwushu() {
        return yinxiangfanwushu;
    }

    public void setYinxiangfanwushu(Integer yinxiangfanwushu) {
        this.yinxiangfanwushu = yinxiangfanwushu;
    }

    public Float getHongzaisunshi() {
        return hongzaisunshi;
    }

    public void setHongzaisunshi(Float hongzaisunshi) {
        this.hongzaisunshi = hongzaisunshi;
    }
}
