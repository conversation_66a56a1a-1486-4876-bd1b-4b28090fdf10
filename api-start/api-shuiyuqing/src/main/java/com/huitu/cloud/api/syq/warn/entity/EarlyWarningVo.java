package com.huitu.cloud.api.syq.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@ApiModel(value="EarlyWarningVo对象", description="")
public class EarlyWarningVo implements Serializable {

//    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "市级政区名称")
    private String adnm2;

    @ApiModelProperty(value = "县级政区名称")
    private String adnm3;

    @ApiModelProperty(value = "乡镇级政区名称")
    private String adnm4;

    @ApiModelProperty(value = "行政村政区名称")
    private String adnm5;

    @ApiModelProperty(value = "自然村政区名称")
    private String adnm6;

    @ApiModelProperty(value = "河流名称")
    private String basName;

    @ApiModelProperty(value = "水库名称")
    private String resName;

    @ApiModelProperty(value = "预警ID")
    private String id;

    @ApiModelProperty(value = "预警对象编码")
    private String ewcode;

    @ApiModelProperty(value = "预警对象类型:1.村庄  2.水库  3.河道")
    private String ewtype;

    @ApiModelProperty(value = "预报时间")
    private LocalDateTime ftm;

    @ApiModelProperty(value = "预警时间")
    private LocalDateTime wtm;

    @ApiModelProperty(value = "预警开始时间")
    private LocalDateTime ntm;

    @ApiModelProperty(value = "预警等级：1.一级预警  2.二级预警  3.三级预警  4.无预警")
    private String wlevel;

    @ApiModelProperty(value = "预警来源：1.雨情  2.流量  3.水位")
    private String stype;

    @ApiModelProperty(value = "预警来源对象编码")
    private String scode;

    @ApiModelProperty(value = "预报累计降雨")
    private Double drp;

    @ApiModelProperty(value = "预报降雨开始时间")
    private LocalDateTime startdate;

    @ApiModelProperty(value = "预报降雨结束时间")
    private LocalDateTime enddate;

    @ApiModelProperty(value = "预警数据")
    private Double wdata;

    @ApiModelProperty(value = "雨量降雨步长")
    private Double step;

    @ApiModelProperty(value = "预警数据单位")
    private String unit;

    @ApiModelProperty(value = "纳雨能力")
    private Double cr;

    @ApiModelProperty(value = "一级预警指标")
    private Double level1;

    @ApiModelProperty(value = "二级预警指标")
    private Double level2;

    @ApiModelProperty(value = "三级预警指标")
    private Double level3;

    @ApiModelProperty(value = "预警状态：1.新产生  2.升级   3 持续  4 降级  5 结束")
    private String warnstate;

    @ApiModelProperty(value = "处理状态：00.未处理  10.内部预警  01.外部预警  11.内外部预警")
    private String disposestate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "场次标识")
    private String sspcd;

    @ApiModelProperty(value = "预警时间：0.过去产生 1.未来产生")
    private String sig;

    private String state;

    private LocalDateTime addtime;

    public String getAdnm2() {
        return adnm2;
    }

    public void setAdnm2(String adnm2) {
        this.adnm2 = adnm2;
    }

    public String getAdnm3() {
        return adnm3;
    }

    public void setAdnm3(String adnm3) {
        this.adnm3 = adnm3;
    }

    public String getAdnm4() {
        return adnm4;
    }

    public void setAdnm4(String adnm4) {
        this.adnm4 = adnm4;
    }

    public String getAdnm5() {
        return adnm5;
    }

    public void setAdnm5(String adnm5) {
        this.adnm5 = adnm5;
    }

    public String getAdnm6() {
        return adnm6;
    }

    public void setAdnm6(String adnm6) {
        this.adnm6 = adnm6;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEwcode() {
        return ewcode;
    }

    public void setEwcode(String ewcode) {
        this.ewcode = ewcode;
    }

    public String getEwtype() {
        return ewtype;
    }

    public void setEwtype(String ewtype) {
        this.ewtype = ewtype;
    }

    public LocalDateTime getFtm() {
        return ftm;
    }

    public void setFtm(LocalDateTime ftm) {
        this.ftm = ftm;
    }

    public LocalDateTime getWtm() {
        return wtm;
    }

    public void setWtm(LocalDateTime wtm) {
        this.wtm = wtm;
    }

    public LocalDateTime getNtm() {
        return ntm;
    }

    public void setNtm(LocalDateTime ntm) {
        this.ntm = ntm;
    }

    public String getWlevel() {
        return wlevel;
    }

    public void setWlevel(String wlevel) {
        this.wlevel = wlevel;
    }

    public String getStype() {
        return stype;
    }

    public void setStype(String stype) {
        this.stype = stype;
    }

    public String getScode() {
        return scode;
    }

    public void setScode(String scode) {
        this.scode = scode;
    }

    public Double getDrp() {
        return drp;
    }

    public void setDrp(Double drp) {
        this.drp = drp;
    }

    public LocalDateTime getStartdate() {
        return startdate;
    }

    public void setStartdate(LocalDateTime startdate) {
        this.startdate = startdate;
    }

    public LocalDateTime getEnddate() {
        return enddate;
    }

    public void setEnddate(LocalDateTime enddate) {
        this.enddate = enddate;
    }

    public Double getWdata() {
        return wdata;
    }

    public void setWdata(Double wdata) {
        this.wdata = wdata;
    }

    public Double getStep() {
        return step;
    }

    public void setStep(Double step) {
        this.step = step;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getCr() {
        return cr;
    }

    public void setCr(Double cr) {
        this.cr = cr;
    }

    public Double getLevel1() {
        return level1;
    }

    public void setLevel1(Double level1) {
        this.level1 = level1;
    }

    public Double getLevel2() {
        return level2;
    }

    public void setLevel2(Double level2) {
        this.level2 = level2;
    }

    public Double getLevel3() {
        return level3;
    }

    public void setLevel3(Double level3) {
        this.level3 = level3;
    }

    public String getWarnstate() {
        return warnstate;
    }

    public void setWarnstate(String warnstate) {
        this.warnstate = warnstate;
    }

    public String getDisposestate() {
        return disposestate;
    }

    public void setDisposestate(String disposestate) {
        this.disposestate = disposestate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSspcd() {
        return sspcd;
    }

    public void setSspcd(String sspcd) {
        this.sspcd = sspcd;
    }

    public String getSig() {
        return sig;
    }

    public void setSig(String sig) {
        this.sig = sig;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }

    public String getBasName() {
        return basName;
    }

    public void setBasName(String basName) {
        this.basName = basName;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    @Override
    public String toString() {
        return "EarlyWarningVo{" +
                "adnm2='" + adnm2 + '\'' +
                ", adnm3='" + adnm3 + '\'' +
                ", adnm4='" + adnm4 + '\'' +
                ", adnm5='" + adnm5 + '\'' +
                ", basName='" + basName + '\'' +
                ", resName='" + resName + '\'' +
                ", id='" + id + '\'' +
                ", ewcode='" + ewcode + '\'' +
                ", ewtype='" + ewtype + '\'' +
                ", ftm=" + ftm +
                ", wtm=" + wtm +
                ", ntm=" + ntm +
                ", wlevel='" + wlevel + '\'' +
                ", stype='" + stype + '\'' +
                ", scode='" + scode + '\'' +
                ", drp=" + drp +
                ", startdate=" + startdate +
                ", enddate=" + enddate +
                ", wdata=" + wdata +
                ", step=" + step +
                ", unit='" + unit + '\'' +
                ", cr=" + cr +
                ", level1=" + level1 +
                ", level2=" + level2 +
                ", level3=" + level3 +
                ", warnstate='" + warnstate + '\'' +
                ", disposestate='" + disposestate + '\'' +
                ", remark='" + remark + '\'' +
                ", sspcd='" + sspcd + '\'' +
                ", sig='" + sig + '\'' +
                ", state='" + state + '\'' +
                ", addtime=" + addtime +
                '}';
    }
}
