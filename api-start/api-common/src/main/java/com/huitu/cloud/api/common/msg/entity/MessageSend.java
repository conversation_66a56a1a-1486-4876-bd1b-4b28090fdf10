package com.huitu.cloud.api.common.msg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警消息发送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("MESSAGESEND_R")
@ApiModel(value="MessagesendR对象", description="预警消息发送记录表")
public class MessageSend extends Model<MessageSend> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "短信编号 ")
    @TableField("MSGID")
    private Integer msgId;

    @ApiModelProperty(value = "短息接收年份 ")
    @TableId(value = "CODE", type = IdType.NONE)
    private String code;

    @ApiModelProperty(value = "短信接收编号 ")
    @TableField("SMSID")
    private Integer smsId;

    @ApiModelProperty(value = "手机号码")
    @TableField("SID")
    private String sid;

    @ApiModelProperty(value = " 接收人姓名")
    @TableField("UNAME")
    private String uname;

    @ApiModelProperty(value = "编号")
    @TableField("OBJECTCD")
    private String objectcd;

    @ApiModelProperty(value = " 接收人类型")
    @TableField("OBJECTTYPE")
    private Integer objectType;

    @ApiModelProperty(value = " 发送情况代码")
    @TableField("SENDRESULT")
    private Integer sendResult;

    @ApiModelProperty(value = "发送时间 ")
    @TableField("RECTM")
    private LocalDateTime rectm;

    @ApiModelProperty(value = " 备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = " 所属政区 ")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = " 本级发送")
    @TableField("ISSEND")
    private Integer isSend;


    public Integer getMsgId() {
        return msgId;
    }

    public void setMsgId(Integer msgId) {
        this.msgId = msgId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getSmsId() {
        return smsId;
    }

    public void setSmsId(Integer smsId) {
        this.smsId = smsId;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getUname() {
        return uname;
    }

    public void setUname(String uname) {
        this.uname = uname;
    }

    public String getObjectcd() {
        return objectcd;
    }

    public void setObjectcd(String objectcd) {
        this.objectcd = objectcd;
    }

    public Integer getObjectType() {
        return objectType;
    }

    public void setObjectType(Integer objectType) {
        this.objectType = objectType;
    }

    public Integer getSendResult() {
        return sendResult;
    }

    public void setSendResult(Integer sendResult) {
        this.sendResult = sendResult;
    }

    public LocalDateTime getRectm() {
        return rectm;
    }

    public void setRectm(LocalDateTime rectm) {
        this.rectm = rectm;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getIsSend() {
        return isSend;
    }

    public void setIsSend(Integer isSend) {
        this.isSend = isSend;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "MessagesendR{" +
        "msgid=" + msgId +
        ", code=" + code +
        ", smsid=" + smsId +
        ", sid=" + sid +
        ", uname=" + uname +
        ", objectcd=" + objectcd +
        ", objecttype=" + objectType +
        ", sendresult=" + sendResult +
        ", rectm=" + rectm +
        ", remark=" + remark +
        ", adcd=" + adcd +
        ", issend=" + isSend +
        "}";
    }
}
