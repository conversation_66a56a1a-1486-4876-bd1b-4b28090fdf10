package com.huitu.cloud.api.dzsj.rvriver.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.dzsj.common.entity.*;
import com.huitu.cloud.api.dzsj.rvriver.entity.*;
import com.huitu.cloud.api.dzsj.rvrsvr.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 防洪工程河流管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-02
 */
public interface RvriverDao {
    /**
     * 查询防洪工程河流信息
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return
     */
    IPage<RvOutlineInfo> getRvriverList(Page page, @Param("map") Map<String, Object> param);
    /**
     * 防洪工程河流流域面积统计信息
     *
     */
    DtBasArea getDtBasareaInfo();
    /**
     * 防洪工程河流江河洪水统计
     *
     * @return
     */
    List<DtFloodDike> getFloodDikeList();
    /**
     * 防洪工程河流概况信息查询
     *
     * @param rvCode  河流编码
     * @return
     */
    BsnRvgcdesc getRvRiverSurveyInfo(@Param("rvCode")String rvCode);
    /**
     * 防洪江河流域统计
     *
     * @return
     */
    List<DtBasRvInfo> getDtBasRiverInfo();
    /**
     * 防洪流域关联河流信息查询
     *
     * @return
     */
    List<BasRvInfo> getBasRiverInfo();
    /**
     * 防洪工程全省江河概况
     *
     * @param page  分页参数
     * @param rvCode  河流编码
     * @return
     */
    IPage<RvExtendedInfo> getRvExtendedInfoList(Page page,@Param("rvCode")String rvCode,@Param("isMain")String isMain);
    /**
     * 防洪流域关联河流信息查询
     *
     * @param rvCode  河流编码
     * @return
     */
    List<String> getRiverStcd(@Param("rvCode")String rvCode);
    /**
     * 防洪工程河流主要防汛特征水位情况
     *
     * @param stcds 水库编码
     * @return
     */
    List<RiverFloodPrevention> getRiverFloodPreventionInfo(@Param("stcds")List<String> stcds);
    /**
     * 查询河流信息
     *
     * @param rvCode 河流编码
     * @return
     */
    RvRelatedInfo getRiverRelatedInfo(@Param("rvCode")String rvCode);
    /**
     * 查询关联流域信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjBas> getBasList(@Param("rvCode")String rvCode);
    /**
     * 查询关联政区信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjAd> getAdList(@Param("rvCode")String rvCode);
    /**
     * 查询关联河流信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjRv> getRvList(@Param("rvCode")String rvCode);
    /**
     * 查询关联河段信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjRea> getReaList(@Param("rvCode")String rvCode);
    /**
     * 查询关联水库信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjRes> getResList(@Param("rvCode")String rvCode);
    /**
     * 查询关联测站信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjSt> getStList(@Param("rvCode")String rvCode);
    /**
     * 查询关联泵站信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjPust> getPustList(@Param("rvCode")String rvCode);
    /**
     * 查询关联提防信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjDike> getDikeList(@Param("rvCode")String rvCode);
    /**
     * 查询关联湖泊信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjLk> getLkList(@Param("rvCode")String rvCode);
    /**
     * 查询关联水闸信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjWaga> getWagaList(@Param("rvCode")String rvCode);
    /**
     * 查询关联桥梁信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjBrid> getBridList(@Param("rvCode")String rvCode);
    /**
     * 查询关联管线信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjPipe> getPipeList(@Param("rvCode")String rvCode);
    /**
     * 查询关联险工险段信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjDpds> getDpdsList(@Param("rvCode")String rvCode);
    /**
     * 查询关联圩垸信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjPold> getPoldList(@Param("rvCode")String rvCode);
    /**
     * 查询关联灌区信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjIrr> getIrrList(@Param("rvCode")String rvCode);
    /**
     * 查询关联橡胶坝信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjRuda> getRudaList(@Param("rvCode")String rvCode);
    /**
     * 查询关联水利行业单位信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjWiun> getWiunList(@Param("rvCode")String rvCode);
    /**
     * 查询关联负责人信息
     *
     * @param rvCode 河流编码
     * @return
     */
    List<ObjPerson> getPersonList(@Param("rvCode")String rvCode);
    /**
     * 查询工程河流信息
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return
     */
    IPage<GcRvInfo> getGcRvriverList(Page page, @Param("map")Map<String, Object> param);

    /**
     * 根据流域编码查询所有的河流编码
     *
     * @param bscd 流域编码
     * @return
     */
    List<String> getRvCode(@Param("bscd") String bscd);
    /**
     * 查询河流关联政区信息
     *
     * @param rvCodes 河流编码集合
     * @return
     */
    List<Map<String, Object>> getAdInfo(@Param("rvCodes")List<String> rvCodes);
}
