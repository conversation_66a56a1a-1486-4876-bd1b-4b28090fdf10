package com.huitu.cloud.api.syq.skdt.service;

import com.huitu.cloud.api.syq.skdt.entity.*;

import java.util.List;

/**
 * <p>
 * 水库调度 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-09
 */
public interface SkdtService {
    /**
     * 查询水库调度涉及水库的水库最新水库水情信息
     * @param stm 开始时间 yyyy-MM-dd HH:mm:ss
     * @param etm 结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    List<BsnSkdtRvEx> getSkdtRsvrLatestInfo(String stm,String etm);
    /**
     * 查询水库调度涉及水库的库平均降雨
     * @param stm 开始时间 yyyy-MM-dd HH:mm:ss
     * @param etm 结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    List<RsvrAvgRain> getSkdtRsvrRainInfo(String stm, String etm);
    /**
     * 根据水库工程编码查询库区时段面雨量列表
     * @param resCode 水库工程编码
     * @param stm 开始时间 yyyy-MM-dd HH:mm:ss
     * @param etm 结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    List<RsvrTmAvgRain> getSkdtRsvrRainTmInfo(String resCode, String stm, String etm);

    /**
     * 根据水库编码查询水库上下游河道水情信息
     * @param stcd 水库测站编码
     * @param stm 开始时间 yyyy-MM-dd HH:mm:ss
     * @param etm 结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    List<RsvrRiverInfo> getSkdtRsvrRiverInfo(String stcd,String stm, String etm);

    /**
     * 根据水库编码查询水库的综合详情信息
     * @param stcd 水库测站编码
     * @param stm 开始时间 yyyy-MM-dd HH:mm:ss
     * @param etm 结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    SkInfo getInfoBySkStcd(String stcd, String stm, String etm);

    /**
     * 根据水库编码查询水库关联雨量站信息
     * @param resCode 水库工程编码
     * @param stm 开始时间 yyyy-MM-dd HH:mm:ss
     * @param etm 结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    List<RsvrRain> getSkdtRsvrRainInfo(String resCode,String stm, String etm);
}
