package com.huitu.cloud.api.ewci.hydrology.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.api.ewci.hydrology.entity.HyWarnIndex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 洪水预警指标响应对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警指标响应对象")
public class HyWarnIndexResponse extends HyWarnIndex {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    @TableField(value = "RVNM")
    private String rvnm;

    @ApiModelProperty(value = "发布单位类型")
    @TableField(value = "RELEASE_UNIT_TYPE")
    private String releaseUnitType;

    @ApiModelProperty(value = "发布单位名称")
    @TableField(value = "RELEASE_UNIT_NAME")
    private String releaseUnitName;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getReleaseUnitType() {
        return releaseUnitType;
    }

    public void setReleaseUnitType(String releaseUnitType) {
        this.releaseUnitType = releaseUnitType;
    }

    public String getReleaseUnitName() {
        return releaseUnitName;
    }

    public void setReleaseUnitName(String releaseUnitName) {
        this.releaseUnitName = releaseUnitName;
    }
}
