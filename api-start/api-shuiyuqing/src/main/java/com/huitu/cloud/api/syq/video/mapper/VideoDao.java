package com.huitu.cloud.api.syq.video.mapper;

import com.huitu.cloud.api.syq.video.entity.BsnCcdinfo;
import com.huitu.cloud.api.syq.video.entity.BsnVdstCodeB;
import com.huitu.cloud.api.syq.video.entity.BsnVdstinfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface VideoDao {

    /**
     * 分页查询视频站列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @return
     */
    List<BsnVdstinfo> getVideoInfo(@Param("map") Map<String, Object> param);

    /**
     * 查询视频设备信息
     *
     * @return
     */
    List<BsnCcdinfo> getEquipmentInfos();

    /**
     * 通过 查询视频设备信息
     *
     * @return
     */
    List<BsnCcdinfo> getEquipmentInfosByVdstCd(@Param("vdstCd") String vdstCd);

    /**
     * 通过 查询视频设备信息
     *
     * @return
     */
    List<BsnCcdinfo> getEquipmentInfosByVdstCds(@Param("vdstCds") List<String> vdstCds);

    /**
     * 查询单个视频站信息
     *
     * @param id 视频站编码
     * @return
     */
    BsnVdstinfo getVideoInfoById(@Param("id") String id);

    /**
     * 查询单个视频站信息
     *
     * @param code 测站编码
     * @return
     */
    List<BsnVdstinfo> getVideoInfoByCode(@Param("code") String code);

    /**
     * 查询单个视频站信息
     *
     * @param ennmcd 工程编码
     * @return
     */
    BsnVdstinfo getVideoInfoByEnnmcd(@Param("ennmcd") String ennmcd);

    /**
     * 查询测站关联单个视频站多个视频头信息
     * @param stcd
     * @return
     */
    List<BsnCcdinfo> getVideoDetailInfoByStcd(@Param("stcd") String stcd);

    /**
     * 查询存在真实视频站的关系表
     * @param vdstcd
     * @param code
     * @param tp
     * @return
     */
    List<BsnVdstCodeB> getVideoStcdExist(@Param("vdstcd") String vdstcd, @Param("code") String code, @Param("tp") String tp);
}
