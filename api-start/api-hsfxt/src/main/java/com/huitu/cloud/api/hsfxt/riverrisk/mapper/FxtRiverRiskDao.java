package com.huitu.cloud.api.hsfxt.riverrisk.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmap;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmapVo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
public interface FxtRiverRiskDao extends BaseMapper<RvRiskmap> {

    IPage<RvRiskmapVo> getRiverRiskList(Page page, @Param("map") Map<String, Object> param);
}
