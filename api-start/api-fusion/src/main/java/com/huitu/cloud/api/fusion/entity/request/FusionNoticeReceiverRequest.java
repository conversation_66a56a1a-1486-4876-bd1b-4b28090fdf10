package com.huitu.cloud.api.fusion.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huitu.cloud.entity.EntityRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 预警人工通知接收人请求对象
 */
@ApiModel(value = "预警人工通知接收人请求对象")
public class FusionNoticeReceiverRequest implements EntityRequest {

    /**
     * 平台标识
     */
    @JsonIgnore
    private String platformId;
    @ApiModelProperty(value = "开始时间，注：必填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-03 08:00:00", required = true)
    @NotNull(message = "参数[开始时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;
    @ApiModelProperty(value = "结束时间，注：必填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-04 08:00:00", required = true)
    @NotNull(message = "参数[结束时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    @Override
    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        Date stm = getStm(), etm = getEtm();
        if (stm.getTime() >= etm.getTime()) {
            throw new IllegalArgumentException("参数[结束时间]应大于参数[开始时间]");
        }
        params.put("stm", stm);
        params.put("etm", etm);
        return params;
    }
}
