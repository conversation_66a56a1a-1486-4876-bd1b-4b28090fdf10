<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.hsfxt.xzhq.mapper.XzhqDao">

    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getXzhqTreeInfo" resultType="com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqTree">
        SELECT DISTINCT XIANQUXIANGZHENBIANMA ADCD,MINGCHENG ADNM
        <if test="level != null and level == 6">
            ,'0' PADCD
        </if>
        <if test="level != null and level == 9">
            ,LEFT(XIANQUXIANGZHENBIANMA,6)+'000000000' PADCD
        </if>
        FROM XZH_XianQuXiangZhen
        WHERE 1=1
        <if test="level != null and level == 6">
            AND SUBSTRING(XIANQUXIANGZHENBIANMA,5,2) &lt;> '00' AND SUBSTRING(XIANQUXIANGZHENBIANMA,7,3) = '000'
        </if>
        <if test="level != null and level == 9">
            AND SUBSTRING(XIANQUXIANGZHENBIANMA,7,3) &lt;> '000'
        </if>
    </select>
    <select id="getXzhqParam" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicHongshuiliangji">
        SELECT  *  FROM  DIC_HONGSHUILIANGJI WHERE HONGSHUILIANGJIID=#{hl} AND BIANZHIQUID =#{bzqid}
    </select>
    <select id="getTunXzhqTreeInfo" resultType="com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqTree">
        SELECT TUNBIANMA ADCD,TUNMINGCHENG ADNM,LEFT(TUNBIANMA,9)+'000000' PADCD,LEIXING KEYINDEX
        FROM XZH_TunEx
        WHERE 1=1 AND ISNULL(${param1},'') != '' AND ISNULL(${param2},'') != '' AND ISNULL(${param3},'') != ''
        <if test="key != null and key != ''">
            AND LEIXING in (${key})
        </if>
        <if test="name != null and name != ''">
            AND CHARINDEX(#{name}, TUNMINGCHENG)>0
        </if>
    </select>
    <select id="getXzhqShjjTreeInfo" resultType="com.huitu.cloud.api.hsfxt.xzhq.entity.XzhShehuijingji">
        SELECT SORTID, KEYINDEX, YMSSINDEX, XZQHINDEX, MINGCHENG, YXHS, YXRK, YXFW, YMGD, YXGDP, YMMJ, YXTL, YXGL, HSLS,
        ANZHIDIAN, XIANLUCHANGDU, ZHUANYISHIJIAN, PID,
        (CASE WHEN (ISNULL(XZQHINDEX,'') = '') AND ( ISNULL(YMSSINDEX,'') != '') THEN YMSSINDEX ELSE MINGCHENG END)NAME
        FROM  XZH_SheHuiJingJi
        WHERE 1=1
        <if test="key != null and key != ''">
            AND KEYINDEX in (${key})
        </if>
    </select>
    <select id="getXianQuXiangZhen" resultType="com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqXiangQing">
        SELECT XIANQUXIANGZHENBIANMA ADCD, MINGCHENG ADNM, MIANJI, FUSHU, RENKOU, FANGWUJIANSHU, GENDIMIANJI, GDP, FENQU
        FROM XZH_XianQuXiangZhen
        WHERE XIANQUXIANGZHENBIANMA=#{adcd} AND FENQU=#{bzqid}
    </select>
    <select id="getZaiHaiSunShi" resultType="com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqXiangQing">
        SELECT ID, XINGZHENGQUBIANHAO, XINGZHENGQUMINGCHENG, FANGANBIANMA, FLOODRISKTYPE, FLOODZONE, FLOODMATHZONE, FLOODBREACH, FLOODQUANTITY, YANMOMIANJI, YANMOGENDIMIANJI, YINXIANGRENKOU, YINXIANGGDP, YINXIANGFANWUSHU, HONGZAISUNSHI, KEYINDEX
        FROM XZH_ZaiHaiSunShiYingXiangBiao
        WHERE XINGZHENGQUBIANHAO=#{adcd} AND KEYINDEX = #{key}
    </select>
    <select id="getXzhqTunShjjDetailInfo" resultType="com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqTunXiangqing">
        SELECT TUNBIANMA ADCD, TUNMINGCHENG ADNM, X_COOR, Y_COOR, FUSHU, RENKOU, FANGWUJIANSHU, GENDIMIANJI, GDP,
                ${param1} DDSJ,${param2} HSLS,${param3} YMSS,
               LEIXING, GB, FENQU, BEIYONG6, CUNBIANMA
        FROM   XZH_TunEx
        WHERE TUNBIANMA=#{adcd} AND LEIXING = #{key}
    </select>
    <select id="getXzhqZhuanYiFangAn" resultType="com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqZhuanYiFangAn">
        SELECT ZHUANYIBIAOID, ZHUANYIDANYUANBIANMA, ZHUANYIDANYUANMINGCHENG, ZHUANYIFANGANBIANMA, ZHUANYIFANGANMINGCHENG, ZHUANYILUXIANBIANMA, ANZHIQUBIANMA, ANZHIQUMINGCHENG, ZHUANYILUXIANMINGCHENG, ZHUANYILUXIANCHANGDU, ZHUANYISHIJIAN, FENQU, TUNBIANMA
        FROM XZH_ZhuanYiBiao
        WHERE 1=1
        <if test="adcd != null and adcd != ''">
            AND ZHUANYIDANYUANBIANMA = #{adcd}
        </if>
        <if test="bzqid != null and bzqid != ''">
            AND FENQU = #{bzqid}
        </if>
    </select>
</mapper>