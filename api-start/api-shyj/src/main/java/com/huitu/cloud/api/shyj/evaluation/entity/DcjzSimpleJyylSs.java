package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <p>
 * 重点城(集)镇调查评价设备信息-简易雨量站
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@ApiModel(value="DcjzSimpleJyylSs对象", description="重点城(集)镇调查评价设备信息-简易雨量站")
public class DcjzSimpleJyylSs extends Model<DcjzSimpleJyylSs> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "站点位置")
    @TableId(value = "ADDRESS", type = IdType.NONE)
    private String address;

    @ApiModelProperty(value = "经度")
    @TableId(value = "LGTD", type = IdType.NONE)
    private String lgtd;

    @ApiModelProperty(value = "纬度")
    @TableId(value = "LTTD", type = IdType.NONE)
    private String lttd;

    @ApiModelProperty(value = "所在流域")
    @TableId(value = "WSNM", type = IdType.NONE)
    private String wsnm;

    @ApiModelProperty(value = "设站日期")
    @TableId(value = "BDATE", type = IdType.NONE)
    private Date bdate;

    @ApiModelProperty(value = "测雨量")
    @TableId(value = "MRAIN", type = IdType.NONE)
    private String mrain;

    @ApiModelProperty(value = "语音报警")
    @TableId(value = "ALVOICE", type = IdType.NONE)
    private String alvoice;

    @ApiModelProperty(value = "光报警")
    @TableId(value = "LIGHT", type = IdType.NONE)
    private String light;

    @ApiModelProperty(value = "设置预警阈值")
    @TableId(value = "WVALUE", type = IdType.NONE)
    private String wvalue;

    @ApiModelProperty(value = "查前期雨量")
    @TableId(value = "PRAIN", type = IdType.NONE)
    private String prain;

    @ApiModelProperty(value = "描述")
    @TableId(value = "COMMENTS", type = IdType.NONE)
    private String comments;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public Date getBdate() {
        return bdate;
    }

    public void setBdate(Date bdate) {
        this.bdate = bdate;
    }

    public String getMrain() {
        return mrain;
    }

    public void setMrain(String mrain) {
        this.mrain = mrain;
    }

    public String getAlvoice() {
        return alvoice;
    }

    public void setAlvoice(String alvoice) {
        this.alvoice = alvoice;
    }

    public String getLight() {
        return light;
    }

    public void setLight(String light) {
        this.light = light;
    }

    public String getWvalue() {
        return wvalue;
    }

    public void setWvalue(String wvalue) {
        this.wvalue = wvalue;
    }

    public String getPrain() {
        return prain;
    }

    public void setPrain(String prain) {
        this.prain = prain;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public String toString() {
        return "DcjzSimpleJyylSs{" +
                "adnm='" + adnm + '\'' +
                ", address='" + address + '\'' +
                ", lgtd='" + lgtd + '\'' +
                ", lttd='" + lttd + '\'' +
                ", wsnm='" + wsnm + '\'' +
                ", bdate=" + bdate +
                ", mrain='" + mrain + '\'' +
                ", alvoice='" + alvoice + '\'' +
                ", light='" + light + '\'' +
                ", wvalue='" + wvalue + '\'' +
                ", prain='" + prain + '\'' +
                ", comments='" + comments + '\'' +
                '}';
    }
}
