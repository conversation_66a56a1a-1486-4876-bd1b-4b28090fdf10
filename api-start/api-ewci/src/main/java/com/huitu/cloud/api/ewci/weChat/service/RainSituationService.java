package com.huitu.cloud.api.ewci.weChat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.weChat.entity.WeChatRain;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface RainSituationService {
    /**
     * 分页查询累计雨量信息
     *
     * @param stType       测站归属类型
     * @return
     */
    IPage<WeChatRain> getRainByCondition(List<String> stType);

    /**
     * 查询全部累计雨量信息
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @return
     */
    List<WeChatRain> getRainByConditionAll(String stm, String etm, List<String> stType, String adcd,String threshold, String rainShowType, List<String> isOut, List<String> isFollow);

    boolean typel(List<String> stType);
}