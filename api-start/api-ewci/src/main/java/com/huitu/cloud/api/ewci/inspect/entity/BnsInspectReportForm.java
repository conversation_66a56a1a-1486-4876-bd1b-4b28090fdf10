package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.validation.constraints.StringLength;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 检查上报表单通用字段
 */
@Data
@ApiModel(value = "检查上报表单通用字段")
public class BnsInspectReportForm extends Model<BnsInspectReportForm> {

    @ApiModelProperty(value = "检查ID")
    private String inspectId;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "检察人员")
    private String inspector;

    @ApiModelProperty(value = "组别")
    private String groupid;

    @ApiModelProperty(value = "检查日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inspDate;

    @ApiModelProperty(value = "存在问题")
    private String existProblems;

    @ApiModelProperty(value = "整改要求")
    private String recAsk;

    @ApiModelProperty(value = "整改时限")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date recDate;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    @ApiModelProperty(value = "照片")
    private List<BnsInspectFile> photos;

    @ApiModelProperty(value = "视频")
    private List<BnsInspectFile> videos;

    @ApiModelProperty(value = "音频")
    private List<BnsInspectFile> audios;

    @ApiModelProperty(value = "是否覆盖（0不覆盖1覆盖）")
    @NotNull(message = "参数[是否覆盖]不能为空")
    @StringLength(min = 1, max = 1, message = "参数[是否覆盖]的长度应为1个字符")
    private String isCover;

    @ApiModelProperty(value = "是否删除（0否1是）")
    @NotNull(message = "参数[是否删除]不能为空")
    private String isDelete;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "创建人")
    private Integer creator;

}
