************ - - [30/Jun/2025:08:44:48 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/?redirect=%252FworkbenchMainFrame%252FWorkMain%253Ftmpparams%253D%257B%2522title%2522%253A%2522%25E5%25B7%25A5%25E4%25BD%259C%25E5%258F%25B0%2522%257D HTTP/1.1" 302 -
************ - - [30/Jun/2025:08:44:48 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [30/Jun/2025:08:44:48 +0800] "GET /login HTTP/1.1" 302 -
************ - - [30/Jun/2025:08:44:48 +0800] "GET /login HTTP/1.1" 302 -
************ - - [30/Jun/2025:08:44:57 +0800] "GET /login?code=0cVlNt&state=3GX2Wl HTTP/1.1" 302 -
************ - - [30/Jun/2025:08:44:57 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [30/Jun/2025:08:44:59 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1751244299416 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1751244299416 HTTP/1.1" 200 552
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1751244303685 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1751244303685 HTTP/1.1" 200 61649
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1751244303770 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1751244303770 HTTP/1.1" 200 10388
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1751244303787 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1751244303787 HTTP/1.1" 200 2009
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1751244303925 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+08:45:03&etm=&_timer304=1751244303926 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+09:00&filterCnt=6&_timer304=1751244303927 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751244303927 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751244303927 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:03 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751244303927 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1751244303925 HTTP/1.1" 200 1482
************ - - [30/Jun/2025:08:45:04 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+08:45:03&etm=&_timer304=1751244303926 HTTP/1.1" 200 156
************ - - [30/Jun/2025:08:45:04 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+09:00&filterCnt=6&_timer304=1751244303927 HTTP/1.1" 200 164
************ - - [30/Jun/2025:08:45:04 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751244303927 HTTP/1.1" 200 166
************ - - [30/Jun/2025:08:45:04 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [30/Jun/2025:08:45:04 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:08:45:04 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:08:45:04 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [30/Jun/2025:08:45:04 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751244303927 HTTP/1.1" 200 169
************ - - [30/Jun/2025:08:45:04 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751244303927 HTTP/1.1" 200 13016
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-29&_timer304=1751244304451 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/base/saas/token?_timer304=1751244304451 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1751244304461 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:04 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:05 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [30/Jun/2025:08:45:06 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:08:45:06 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:08:45:07 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:08:45:07 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [30/Jun/2025:08:45:07 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [30/Jun/2025:08:45:07 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [30/Jun/2025:08:45:07 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [30/Jun/2025:08:45:07 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [30/Jun/2025:08:45:07 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1751244304461 HTTP/1.1" 200 2009
************ - - [30/Jun/2025:08:45:08 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [30/Jun/2025:08:45:08 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [30/Jun/2025:08:45:08 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [30/Jun/2025:08:45:08 +0800] "GET /api/base/saas/token?_timer304=1751244304451 HTTP/1.1" 200 411
************ - - [30/Jun/2025:08:45:08 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751244309294 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751244309300 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751244309300 HTTP/1.1" 200 159
************ - - [30/Jun/2025:08:45:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751244309294 HTTP/1.1" 200 160
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1751244309453 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+08:45:09&etm=&_timer304=1751244309606 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751244309606 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1751244309610 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+09:00&filterCnt=6&_timer304=1751244309606 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751244309606 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751244309606 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751244309606 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [30/Jun/2025:08:45:09 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [30/Jun/2025:08:45:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:08:45:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:08:45:09 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+08:45:09&etm=&_timer304=1751244309606 HTTP/1.1" 200 156
************ - - [30/Jun/2025:08:45:09 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751244309606 HTTP/1.1" 200 166
************ - - [30/Jun/2025:08:45:09 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+09:00&filterCnt=6&_timer304=1751244309606 HTTP/1.1" 200 164
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-30+08:00&etm=2025-06-30+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751244309737 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751244309757 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:09 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751244309606 HTTP/1.1" 200 169
************ - - [30/Jun/2025:08:45:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751244309606 HTTP/1.1" 200 161
************ - - [30/Jun/2025:08:45:09 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751244309606 HTTP/1.1" 200 13016
************ - - [30/Jun/2025:08:45:09 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1751244309610 HTTP/1.1" 200 159616
************ - - [30/Jun/2025:08:45:10 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [30/Jun/2025:08:45:10 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [30/Jun/2025:08:45:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [30/Jun/2025:08:45:10 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751244309757 HTTP/1.1" 200 155
************ - - [30/Jun/2025:08:45:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [30/Jun/2025:08:45:10 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [30/Jun/2025:08:45:10 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1751244309453 HTTP/1.1" 200 144
************ - - [30/Jun/2025:08:45:11 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:11 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [30/Jun/2025:08:45:11 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************ - - [30/Jun/2025:08:45:11 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:08:45:11 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751244311279 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:11 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751244311279 HTTP/1.1" 200 155
************ - - [30/Jun/2025:08:45:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-30+08:00&etm=2025-06-30+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751244309737 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:08:45:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:08:45:12 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-29&_timer304=1751244304451 HTTP/1.1" 200 444
************ - - [30/Jun/2025:08:45:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:08:45:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:08:45:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751244313666 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751244313666 HTTP/1.1" 200 232
************ - - [30/Jun/2025:08:45:42 +0800] "OPTIONS /api/dzsj/rvrsvr/get-info-by-cd-tp?objtp=6&_timer304=1751244342331 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:42 +0800] "GET /api/dzsj/rvrsvr/get-info-by-cd-tp?objtp=6&_timer304=1751244342331 HTTP/1.1" 200 153
************ - - [30/Jun/2025:08:45:42 +0800] "OPTIONS /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:42 +0800] "POST /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 626476
************ - - [30/Jun/2025:08:45:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10800112&stm=2025-06-27+08:50&etm=2025-06-30+08:50&_timer304=1751244342917 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:42 +0800] "OPTIONS /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:42 +0800] "OPTIONS /api/syq/rsvr/select-tm-list?stcd=10800112&stm=2025-06-27+08:50&etm=2025-06-30+08:50&_timer304=1751244342917 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:42 +0800] "OPTIONS /api/syq/rsvr/select-streglat-stcd?stcd=10800112&ymdh=2025-06-30+08:50&_timer304=1751244342917 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:42 +0800] "POST /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 245
************ - - [30/Jun/2025:08:45:42 +0800] "GET /api/syq/rsvr/select-streglat-stcd?stcd=10800112&ymdh=2025-06-30+08:50&_timer304=1751244342917 HTTP/1.1" 200 153
************ - - [30/Jun/2025:08:45:42 +0800] "GET /api/syq/rsvr/select-tm-list?stcd=10800112&stm=2025-06-27+08:50&etm=2025-06-30+08:50&_timer304=1751244342917 HTTP/1.1" 200 147
************ - - [30/Jun/2025:08:45:42 +0800] "GET /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10800112&stm=2025-06-27+08:50&etm=2025-06-30+08:50&_timer304=1751244342917 HTTP/1.1" 200 158
************ - - [30/Jun/2025:08:45:43 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10800112&_timer304=1751244343076 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:45:43 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10800112&_timer304=1751244343076 HTTP/1.1" 200 553
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751244374502 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1751244374506 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751244374502 HTTP/1.1" 200 161
************ - - [30/Jun/2025:08:46:14 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1751244374506 HTTP/1.1" 200 159616
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+08:46:14&etm=&_timer304=1751244374573 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751244374573 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751244374573 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+09:00&filterCnt=6&_timer304=1751244374573 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751244374573 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-30+08:00&etm=2025-06-30+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751244374657 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751244374684 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+08:46:14&etm=&_timer304=1751244374573 HTTP/1.1" 200 156
************ - - [30/Jun/2025:08:46:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751244374573 HTTP/1.1" 200 166
************ - - [30/Jun/2025:08:46:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:08:46:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:08:46:14 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [30/Jun/2025:08:46:14 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [30/Jun/2025:08:46:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [30/Jun/2025:08:46:14 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+09:00&filterCnt=6&_timer304=1751244374573 HTTP/1.1" 200 164
************ - - [30/Jun/2025:08:46:14 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [30/Jun/2025:08:46:14 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751244374684 HTTP/1.1" 200 155
************ - - [30/Jun/2025:08:46:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751244374573 HTTP/1.1" 200 169
************ - - [30/Jun/2025:08:46:14 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751244374573 HTTP/1.1" 200 13016
************ - - [30/Jun/2025:08:46:14 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [30/Jun/2025:08:46:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [30/Jun/2025:08:46:15 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:08:46:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-30+08:00&etm=2025-06-30+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751244374657 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:08:46:16 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751244376298 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:16 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751244376298 HTTP/1.1" 200 155
************ - - [30/Jun/2025:08:46:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:08:46:18 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:19 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:19 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:19 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [30/Jun/2025:08:46:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:08:46:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751244379587 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751244379587 HTTP/1.1" 200 232
************ - - [30/Jun/2025:08:46:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:08:46:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751244384237 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751244384237 HTTP/1.1" 200 160
************ - - [30/Jun/2025:08:46:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751244384267 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:46:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751244384267 HTTP/1.1" 200 159
************ - - [30/Jun/2025:08:50:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:50:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:08:51:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751244674242 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:51:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751244674242 HTTP/1.1" 200 160
************ - - [30/Jun/2025:08:51:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751244674320 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:51:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751244674320 HTTP/1.1" 200 161
************ - - [30/Jun/2025:08:51:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:51:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:51:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:51:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751244674597 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:51:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:08:51:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:08:51:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751244674597 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:08:51:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:08:51:19 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:51:20 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:08:51:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:51:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:08:51:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751244684303 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:51:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751244684303 HTTP/1.1" 200 159
************ - - [30/Jun/2025:08:55:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:55:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:08:56:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751244974234 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:56:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751244974234 HTTP/1.1" 200 160
************ - - [30/Jun/2025:08:56:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751244974327 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:56:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751244974327 HTTP/1.1" 200 161
************ - - [30/Jun/2025:08:56:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:56:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:56:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:56:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751244974592 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:56:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:08:56:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:08:56:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751244974592 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:08:56:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:08:56:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751244984362 HTTP/1.1" 200 -
************ - - [30/Jun/2025:08:56:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751244984362 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:00:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:01:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751245275025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:01:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751245275026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:01:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751245275027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:01:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751245275025 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:01:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:01:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751245275026 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:01:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:01:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751245275027 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:01:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:01:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751245285026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:01:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751245285026 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:05:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:06:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751245575017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:06:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751245575017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:06:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751245575018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:06:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751245575017 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:06:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:06:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751245575017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:06:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:06:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751245575018 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:06:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:06:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751245586013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:06:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751245586013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:10:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:11:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751245875019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:11:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751245875019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:11:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751245875020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:11:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:11:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751245875019 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:11:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751245875019 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:11:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:11:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751245875020 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:11:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:11:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751245887013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:11:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751245887013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:15:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:16:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751246175021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:16:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751246175021 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:16:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751246177027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:16:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751246177029 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:16:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:16:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751246177027 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:16:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:16:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:16:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751246177029 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:16:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751246188015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:16:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751246188015 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:21:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751246477057 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:21:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751246477057 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:21:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:21:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751246489020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:21:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751246489020 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:22:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751246537015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:22:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751246537017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:22:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:22:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751246537015 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:22:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:22:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751246537017 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:22:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:26:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751246777049 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:26:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751246777049 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:26:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:26:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751246790014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:26:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751246790014 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:27:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751246837024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:27:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751246837025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:27:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:27:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751246837024 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:27:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:27:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751246837025 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:27:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:31:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751247077041 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:31:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751247077041 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:31:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:31:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751247091026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:31:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751247091026 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:32:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751247137015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:32:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751247137017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:32:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:32:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751247137015 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:32:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:32:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751247137017 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:32:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:36:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751247377053 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:36:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751247377053 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:36:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:36:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751247392014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:36:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751247392014 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:37:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751247437027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:37:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751247437028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:37:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:37:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751247437027 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:37:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:37:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751247437028 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:37:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:41:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751247677049 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:41:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751247677049 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:41:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:41:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751247693020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:41:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751247693020 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:42:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751247737019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:42:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751247737020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:42:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:42:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751247737019 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:42:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:42:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751247737020 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:42:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:46:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751247977046 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:46:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751247977046 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:46:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:46:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751247994028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:46:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751247994028 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:47:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751248037021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:47:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751248037023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:47:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:47:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751248037021 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:47:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:47:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:47:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751248037023 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:50:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:51:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751248275019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:51:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751248275019 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:51:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751248276020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:51:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751248276021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:51:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:51:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751248276020 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:51:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:51:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751248276021 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:51:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:09:51:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751248295016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:51:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751248295016 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:56:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751248577043 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:56:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751248577043 HTTP/1.1" 200 161
************ - - [30/Jun/2025:09:56:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:09:56:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751248596023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:56:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751248596023 HTTP/1.1" 200 159
************ - - [30/Jun/2025:09:57:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751248637017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:57:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751248637018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:09:57:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:09:57:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751248637017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:09:57:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:09:57:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751248637018 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:09:57:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:01:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751248877054 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:01:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751248877054 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:01:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:01:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751248897016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:01:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751248897016 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:02:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751248937023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:02:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751248937024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:02:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:02:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751248937023 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:02:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:02:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751248937024 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:02:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:06:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751249177048 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:06:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751249177048 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:06:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:06:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751249198015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:06:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751249198015 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:07:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751249237016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:07:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751249237017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:07:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:07:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751249237016 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:07:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:07:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751249237017 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:07:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:10:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:11:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751249475026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:11:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751249475026 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:11:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751249476017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:11:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751249476018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:11:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:11:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751249476017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:11:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:11:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751249476018 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:11:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:11:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751249499015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:11:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751249499015 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:16:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751249777040 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:16:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751249777040 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:16:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:16:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751249800014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:16:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751249800014 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:17:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751249837013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:17:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751249837014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:17:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:17:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751249837013 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:17:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:17:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751249837014 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:17:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:21:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751250077043 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:21:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751250077043 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:21:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:21:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751250101019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:21:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751250101019 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:22:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751250137024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:22:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751250137025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:22:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:22:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751250137024 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:22:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:22:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751250137025 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:22:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:26:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751250377044 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:26:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751250377044 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:26:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:26:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751250402027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:26:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751250402027 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:27:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751250437024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:27:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751250437025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:27:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:27:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751250437024 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:27:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:27:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751250437025 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:27:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:31:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:31:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751250674249 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:31:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751250674249 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:31:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751250674329 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:31:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751250674329 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:31:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751250674604 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:31:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:31:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:31:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751250674604 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:31:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:31:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751250702058 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:31:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751250702058 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:36:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751250977052 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:36:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751250977052 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:36:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:36:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751251003015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:36:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751251003015 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:37:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751251037024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:37:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751251037025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:37:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:37:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751251037024 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:37:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:37:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751251037025 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:37:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:41:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751251277039 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:41:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751251277039 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:41:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:41:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751251304016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:41:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751251304016 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:42:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751251337026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:42:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751251337027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:42:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:42:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751251337026 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:42:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:42:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751251337027 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:42:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:46:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751251577049 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:46:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751251577049 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:46:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:46:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751251605139 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:46:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751251605139 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:47:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751251637014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:47:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751251637016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:47:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:47:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751251637014 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:47:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:47:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751251637016 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:47:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:48:10 +0800] "OPTIONS /api/dzsj/rvrsvr/get-info-by-cd-tp?objtp=6&_timer304=1751251690911 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:48:10 +0800] "GET /api/dzsj/rvrsvr/get-info-by-cd-tp?objtp=6&_timer304=1751251690911 HTTP/1.1" 200 153
************ - - [30/Jun/2025:10:48:10 +0800] "OPTIONS /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:48:11 +0800] "POST /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 626476
************ - - [30/Jun/2025:10:48:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10800112&stm=2025-06-27+10:50&etm=2025-06-30+10:50&_timer304=1751251691454 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:48:11 +0800] "OPTIONS /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:48:11 +0800] "OPTIONS /api/syq/rsvr/select-streglat-stcd?stcd=10800112&ymdh=2025-06-30+10:50&_timer304=1751251691454 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:48:11 +0800] "OPTIONS /api/syq/rsvr/select-tm-list?stcd=10800112&stm=2025-06-27+10:50&etm=2025-06-30+10:50&_timer304=1751251691454 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:48:11 +0800] "POST /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 245
************ - - [30/Jun/2025:10:48:11 +0800] "GET /api/syq/rsvr/select-streglat-stcd?stcd=10800112&ymdh=2025-06-30+10:50&_timer304=1751251691454 HTTP/1.1" 200 153
************ - - [30/Jun/2025:10:48:11 +0800] "GET /api/syq/rsvr/select-tm-list?stcd=10800112&stm=2025-06-27+10:50&etm=2025-06-30+10:50&_timer304=1751251691454 HTTP/1.1" 200 147
************ - - [30/Jun/2025:10:48:11 +0800] "GET /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10800112&stm=2025-06-27+10:50&etm=2025-06-30+10:50&_timer304=1751251691454 HTTP/1.1" 200 158
************ - - [30/Jun/2025:10:48:11 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10800112&_timer304=1751251691548 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:48:11 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10800112&_timer304=1751251691548 HTTP/1.1" 200 553
************ - - [30/Jun/2025:10:50:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:50:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:50:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:10:51:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751251874239 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:51:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751251874239 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:51:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751251874321 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:51:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751251874321 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:51:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751251874590 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:51:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:51:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:51:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751251874590 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:51:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:51:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751251905173 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:51:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751251905173 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:52:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1012431
************ - - [30/Jun/2025:10:52:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 142851
************ - - [30/Jun/2025:10:55:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 142851
************ - - [30/Jun/2025:10:56:11 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:56:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:10:56:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252172130 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:56:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252172130 HTTP/1.1" 200 155
************ - - [30/Jun/2025:10:56:12 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:56:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:10:56:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:10:56:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252173647 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:56:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252173647 HTTP/1.1" 200 232
************ - - [30/Jun/2025:10:56:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751252174243 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:56:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751252174243 HTTP/1.1" 200 160
************ - - [30/Jun/2025:10:56:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751252174312 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:56:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751252174312 HTTP/1.1" 200 161
************ - - [30/Jun/2025:10:56:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751252174590 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:56:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:10:56:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:10:56:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751252174590 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:10:56:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:10:56:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751252206017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:56:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751252206017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:10:58:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:10:58:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252292886 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:58:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252292886 HTTP/1.1" 200 155
************ - - [30/Jun/2025:10:58:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:10:58:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:10:58:14 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252294374 HTTP/1.1" 200 -
************ - - [30/Jun/2025:10:58:14 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252294374 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:00:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:00:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252412868 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:00:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252412868 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:00:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:00:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:00:14 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252414371 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:00:14 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252414371 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:01:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751252477028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:01:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751252477028 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:01:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751252507018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:01:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751252507018 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:02:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:02:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252532903 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:02:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252532903 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:02:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:02:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:02:14 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252534398 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:02:14 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252534398 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:02:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:02:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751252537023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:02:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:02:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751252537024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:02:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:02:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:02:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751252537023 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:02:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:02:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751252537024 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:02:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:04:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:04:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252652874 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:04:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252652874 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:04:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:04:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:04:14 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252654378 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:04:14 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252654378 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:06:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:06:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252772895 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:06:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252772895 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:06:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:06:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:06:14 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252774381 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:06:14 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252774381 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:06:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751252777016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:06:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751252777016 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:06:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751252808018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:06:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751252808018 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:07:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751252837017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:07:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751252837018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:07:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:07:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751252837017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:07:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:07:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751252837018 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:07:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:08:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:08:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252897893 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:08:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751252897893 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:08:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:08:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:08:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252899390 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:08:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751252899390 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:11:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751253077164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:11:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751253077164 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:11:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:11:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253077906 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:11:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253077906 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:11:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:11:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:11:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253079411 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:11:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253079411 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:11:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751253109019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:11:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751253109019 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:12:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751253137028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:12:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751253137028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:12:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:12:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751253137028 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:12:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:12:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751253137028 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:12:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:13:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:13:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253197924 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:13:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253197924 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:13:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:13:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:13:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253199456 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:13:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253199456 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:15:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:15:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253317917 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:15:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253317917 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:15:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:15:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:15:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253319467 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:15:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253319467 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:16:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751253377023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:16:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751253377023 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:16:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751253410025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:16:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751253410025 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:17:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751253437149 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:17:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751253437149 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:17:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:17:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751253437149 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:17:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:17:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:17:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253437919 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:17:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253437919 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:17:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751253437149 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:17:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:17:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:17:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:17:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253439295 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:17:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253439295 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:19:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:19:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253557919 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:19:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253557919 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:19:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:19:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:19:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253559425 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:19:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253559425 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:20:13 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:20:13 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253613914 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:20:13 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253613914 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:20:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:20:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:20:15 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253615410 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:20:15 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253615410 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:21:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751253677021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:21:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751253677021 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:21:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751253698920 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:21:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751253698921 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:21:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:21:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751253698920 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:21:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:21:39 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751253698921 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:21:40 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:21:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751253711017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:21:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751253711017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:22:13 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:22:13 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253733959 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:22:13 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253733959 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:22:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:22:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:22:15 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253735467 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:22:15 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253735467 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:25:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:25:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253917908 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:25:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751253917908 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:25:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:25:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:25:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253919417 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:25:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751253919417 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:26:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751253977018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:26:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751253977018 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:26:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751254012019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:26:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751254012019 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:27:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751254037144 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:27:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751254037144 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:27:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:27:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751254037144 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:27:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:27:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:27:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254037922 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:27:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254037922 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:27:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751254037144 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:27:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:27:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:27:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:27:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254039537 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:27:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254039537 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:29:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:29:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254157914 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:29:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254157914 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:29:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:29:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:29:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254159445 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:29:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254159445 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:31:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751254277151 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:31:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751254277151 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:31:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:31:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254277896 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:31:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254277896 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:31:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:31:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:31:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254279402 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:31:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254279402 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:31:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751254313017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:31:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751254313017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:32:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751254337015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:32:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751254337016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:32:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:32:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751254337015 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:32:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:32:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751254337016 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:32:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:33:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:33:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254397905 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:33:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254397905 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:33:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:33:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:33:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254399400 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:33:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254399400 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:35:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:35:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254517902 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:35:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254517902 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:35:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:35:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:35:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254519425 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:35:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254519425 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:36:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751254577024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:36:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751254577024 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:36:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751254614018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:36:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751254614018 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:37:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751254637154 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:37:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751254637154 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:37:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:37:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751254637154 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:37:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:37:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:37:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254637929 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:37:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254637929 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:37:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751254637154 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:37:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:37:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:37:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:37:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254639294 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:37:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254639294 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:39:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:39:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254757922 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:39:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254757922 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:39:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:39:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:39:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254759433 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:39:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254759433 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:41:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751254877155 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:41:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751254877155 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:41:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:41:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254877915 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:41:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254877915 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:41:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:41:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:41:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254879412 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:41:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254879412 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:41:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751254915017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:41:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751254915017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:42:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751254937018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:42:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751254937019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:42:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:42:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751254937018 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:42:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:42:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751254937019 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:42:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:43:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:43:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254997908 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:43:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751254997908 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:43:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:43:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:43:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254999401 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:43:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751254999401 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:45:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:45:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255117905 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:45:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255117905 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:45:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:45:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:45:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255119393 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:45:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255119393 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:46:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751255177019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:46:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751255177019 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:46:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751255216017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:46:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751255216017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:47:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751255237165 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:47:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751255237166 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:47:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:47:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751255237165 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:47:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:47:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:47:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255237935 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:47:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255237935 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:47:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751255237166 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:47:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:47:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:47:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:47:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255239528 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:47:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255239528 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:49:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:49:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255357918 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:49:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255357918 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:49:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:49:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:49:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255359435 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:49:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255359435 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:51:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751255477173 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:51:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751255477173 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:51:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:51:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255477930 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:51:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255477930 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:51:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:51:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:51:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255479464 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:51:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255479464 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:51:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751255517020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:51:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751255517020 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:52:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751255537025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:52:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751255537026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:52:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:52:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751255537025 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:52:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:52:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751255537026 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:52:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:53:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:53:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255597923 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:53:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255597923 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:53:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:53:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:53:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255599455 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:53:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255599455 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:55:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:55:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255717912 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:55:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255717912 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:55:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:55:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:55:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255719422 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:55:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255719422 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:56:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751255777022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:56:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751255777022 HTTP/1.1" 200 161
************ - - [30/Jun/2025:11:56:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751255818014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:56:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751255818014 HTTP/1.1" 200 159
************ - - [30/Jun/2025:11:57:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751255837156 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:57:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751255837156 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:57:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:11:57:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751255837156 HTTP/1.1" 200 160
************ - - [30/Jun/2025:11:57:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:11:57:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:57:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255837928 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:57:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255837928 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:57:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751255837156 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:11:57:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:11:57:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:57:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:57:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255839518 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:57:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255839518 HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:59:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:11:59:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255957934 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:59:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751255957934 HTTP/1.1" 200 155
************ - - [30/Jun/2025:11:59:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:59:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:11:59:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255959473 HTTP/1.1" 200 -
************ - - [30/Jun/2025:11:59:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751255959473 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:01:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751256077161 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:01:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751256077161 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:01:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:01:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256077967 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:01:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256077967 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:01:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:01:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:01:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256079505 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:01:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256079505 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:01:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751256119022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:01:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751256119022 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:02:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751256137017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:02:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751256137019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:02:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:02:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751256137017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:02:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:02:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751256137019 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:02:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:03:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:03:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256197929 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:03:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256197929 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:03:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:03:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:03:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256199413 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:03:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256199413 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:05:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:05:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256317931 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:05:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256317931 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:05:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:05:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:05:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256319433 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:05:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256319433 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:06:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751256377022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:06:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751256377022 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:07:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751256420015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:07:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751256420015 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:07:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751256437169 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:07:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751256437170 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:07:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:07:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751256437169 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:07:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:07:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:07:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256438062 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:07:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256438062 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:07:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751256437170 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:07:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:07:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:07:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:07:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256439603 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:07:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256439603 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:09:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:09:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256557921 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:09:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256557921 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:09:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:09:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:09:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256559421 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:09:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256559421 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:11:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751256677186 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:11:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751256677186 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:11:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:11:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256677934 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:11:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256677934 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:11:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:11:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:11:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256679447 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:11:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256679447 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:12:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751256721021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:12:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751256721021 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:12:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751256737017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:12:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751256737018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:12:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:12:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751256737017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:12:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:12:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751256737018 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:12:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:13:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:13:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256797947 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:13:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256797947 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:13:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:13:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:13:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256799430 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:13:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256799430 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:15:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:15:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256917927 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:15:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751256917927 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:15:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:15:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:15:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256919474 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:15:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751256919474 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:16:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751256977020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:16:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751256977020 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:17:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751257022018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:17:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751257022018 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:17:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751257037165 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:17:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751257037165 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:17:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:17:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751257037165 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:17:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:17:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:17:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257037934 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:17:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257037934 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:17:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751257037165 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:17:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:17:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:17:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:17:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257039515 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:17:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257039515 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:19:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:19:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257157915 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:19:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257157915 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:19:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:19:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:19:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257159410 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:19:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257159410 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:21:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751257277175 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:21:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751257277175 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:21:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:21:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257277915 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:21:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257277915 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:21:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:21:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:21:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257279469 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:21:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257279469 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:22:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751257323013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:22:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751257323013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:22:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751257337027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:22:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751257337028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:22:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:22:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751257337027 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:22:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:22:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751257337028 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:22:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:23:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:23:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257397923 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:23:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257397923 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:23:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:23:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:23:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257399443 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:23:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257399443 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:25:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:25:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257517939 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:25:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257517939 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:25:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:25:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:25:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257519527 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:25:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257519527 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:26:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751257577026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:26:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751257577026 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:27:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751257624015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:27:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751257624015 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:27:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751257637152 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:27:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751257637152 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:27:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:27:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751257637152 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:27:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:27:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:27:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257637933 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:27:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257637933 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:27:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751257637152 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:27:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:27:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:27:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:27:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257639339 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:27:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257639339 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:29:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:29:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257757915 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:29:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257757915 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:29:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:29:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:29:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257759432 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:29:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257759432 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:31:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751257877156 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:31:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751257877156 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:31:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:31:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257877910 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:31:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257877910 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:31:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:31:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:31:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257879468 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:31:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257879468 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:32:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751257925019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:32:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751257925019 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:32:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751257937014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:32:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751257937015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:32:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:32:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751257937014 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:32:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:32:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751257937015 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:32:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:33:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:33:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257997919 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:33:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751257997919 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:33:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:33:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:33:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257999418 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:33:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751257999418 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:35:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:35:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258117925 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:35:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258117925 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:35:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:35:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:35:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258119451 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:35:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258119451 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:36:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751258177029 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:36:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751258177029 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:37:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751258226018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:37:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751258226018 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:37:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751258237167 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:37:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751258237167 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:37:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:37:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751258237167 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:37:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:37:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:37:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258237941 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:37:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258237941 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:37:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751258237167 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:37:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:37:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:37:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:37:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258239553 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:37:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258239553 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:39:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:39:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258357916 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:39:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258357916 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:39:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:39:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:39:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258359422 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:39:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258359422 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:41:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751258477164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:41:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751258477164 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:41:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:41:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258477913 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:41:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258477913 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:41:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:41:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:41:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258479417 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:41:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258479417 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:42:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751258527021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:42:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751258527021 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:42:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751258537017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:42:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751258537018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:42:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:42:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751258537017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:42:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:42:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751258537018 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:42:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:43:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:43:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258597906 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:43:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258597906 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:43:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:43:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:43:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258599427 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:43:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258599427 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:45:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:45:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258717921 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:45:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258717921 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:45:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:45:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:45:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258719452 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:45:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258719452 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:46:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751258777013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:46:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751258777013 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:47:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751258828027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:47:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751258828027 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:47:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751258837150 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:47:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751258837150 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:47:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:47:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751258837150 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:47:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:47:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:47:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258837917 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:47:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258837917 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:47:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751258837150 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:47:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:47:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:47:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:47:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258839498 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:47:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258839498 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:49:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:49:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258957904 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:49:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751258957904 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:49:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:49:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:49:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258959416 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:49:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751258959416 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:51:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751259077163 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:51:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751259077163 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:51:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:51:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259077900 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:51:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259077900 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:51:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:51:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:51:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259079397 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:51:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259079397 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:52:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751259129020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:52:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751259129020 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:52:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751259137021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:52:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751259137022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:52:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:52:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751259137021 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:52:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:52:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751259137022 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:52:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:53:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:53:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259197915 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:53:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259197915 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:53:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:53:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:53:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259199455 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:53:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259199455 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:55:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:55:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259317909 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:55:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259317909 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:55:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:55:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:55:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259319446 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:55:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259319446 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:56:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751259377021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:56:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751259377021 HTTP/1.1" 200 161
************ - - [30/Jun/2025:12:57:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751259430018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:57:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751259430018 HTTP/1.1" 200 159
************ - - [30/Jun/2025:12:57:17 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:57:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751259437155 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:57:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751259437155 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:57:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:12:57:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751259437155 HTTP/1.1" 200 160
************ - - [30/Jun/2025:12:57:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:12:57:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:57:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259437926 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:57:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259437926 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:57:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:57:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751259437155 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:12:57:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:12:57:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:57:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:57:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259439468 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:57:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259439468 HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:59:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:12:59:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259557920 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:59:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259557920 HTTP/1.1" 200 155
************ - - [30/Jun/2025:12:59:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:59:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:12:59:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259559422 HTTP/1.1" 200 -
************ - - [30/Jun/2025:12:59:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259559422 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:01:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751259677154 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:01:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751259677154 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:01:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:01:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259677909 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:01:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259677909 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:01:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:01:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:01:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259679432 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:01:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259679432 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:02:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751259731017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:02:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751259731017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:02:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751259737021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:02:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751259737023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:02:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:02:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751259737021 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:02:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:02:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751259737023 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:02:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:03:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:03:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259797926 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:03:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259797926 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:03:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:03:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:03:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259799436 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:03:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259799436 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:05:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:05:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259917942 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:05:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751259917942 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:05:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:05:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:05:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259919493 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:05:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751259919493 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:06:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751259977018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:06:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751259977018 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:07:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751260032027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:07:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751260032027 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:07:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751260032152 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:07:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:07:12 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:07:12 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751260032152 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:07:12 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:07:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:07:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751260032152 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:07:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:07:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:07:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260032931 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:07:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260032931 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:07:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751260032152 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:07:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:07:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:07:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:07:14 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260034534 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:07:14 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260034534 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:08:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:08:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260092147 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:08:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260092147 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:08:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:08:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:08:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260093612 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:08:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260093612 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:10:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:10:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260212136 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:10:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260212136 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:10:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:10:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:10:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260213653 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:10:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260213653 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:11:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751260274246 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:11:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751260274246 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:11:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751260274319 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:11:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751260274319 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:11:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751260274602 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:11:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:11:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:11:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751260274602 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:11:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:12:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:12:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260332123 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:12:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260332123 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:12:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751260332166 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:12:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751260332166 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:12:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:12:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:12:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260333635 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:12:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260333635 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:14:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:14:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260452134 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:14:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260452134 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:14:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:14:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:14:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260453630 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:14:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260453630 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:16:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:16:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260572134 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:16:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260572134 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:16:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:16:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:16:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260573652 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:16:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260573652 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:16:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751260574250 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:16:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751260574250 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:16:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751260574323 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:16:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751260574323 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:16:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751260574591 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:16:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:16:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:16:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751260574591 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:16:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:17:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751260632199 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:17:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751260632199 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:18:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:18:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260692139 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:18:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260692139 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:18:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:18:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:18:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260693675 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:18:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260693675 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:20:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:20:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260812144 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:20:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260812144 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:20:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:20:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:20:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260813651 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:20:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260813651 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:21:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751260874245 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:21:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751260874245 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:21:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751260874317 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:21:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751260874317 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:21:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751260874600 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:21:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:21:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:21:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751260874600 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:21:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:22:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:22:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260932124 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:22:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751260932124 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:22:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751260932226 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:22:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751260932226 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:22:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:22:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:22:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260933650 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:22:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751260933650 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:24:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:24:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261052126 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:24:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261052126 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:24:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:24:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:24:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261053643 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:24:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261053643 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:26:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:26:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261172135 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:26:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261172135 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:26:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:26:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:26:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261173678 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:26:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261173678 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:26:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751261174250 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:26:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751261174250 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:26:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751261174322 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:26:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751261174322 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:26:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751261174606 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:26:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:26:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:26:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:26:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751261174606 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:27:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751261232262 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:27:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751261232262 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:28:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:28:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261292147 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:28:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261292147 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:28:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:28:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:28:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261293640 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:28:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261293640 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:30:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:30:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261412131 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:30:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261412131 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:30:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:30:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:30:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261413646 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:30:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261413646 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:31:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751261474250 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:31:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751261474250 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:31:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751261474323 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:31:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751261474323 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:31:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751261474595 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:31:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:31:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:31:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751261474595 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:31:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:32:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:32:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261532131 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:32:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261532131 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:32:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751261532291 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:32:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751261532291 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:32:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:32:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:32:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261533667 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:32:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261533667 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:34:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:34:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261652121 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:34:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261652121 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:34:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:34:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:34:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261653646 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:34:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261653646 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:36:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:36:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261772124 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:36:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261772124 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:36:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:36:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:36:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261773654 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:36:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261773654 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:36:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751261774242 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:36:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751261774242 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:36:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751261774314 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:36:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751261774314 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:36:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751261774596 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:36:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:36:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:36:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751261774596 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:36:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:37:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751261832327 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:37:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751261832327 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:38:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:38:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261892142 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:38:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751261892142 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:38:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:38:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:38:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261893728 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:38:14 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751261893728 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:40:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:40:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262012178 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:40:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262012178 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:40:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:40:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:40:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262013645 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:40:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262013645 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:41:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751262075021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:41:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751262075021 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:41:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751262076025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:41:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751262076026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:41:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:41:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751262076025 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:41:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:41:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751262076026 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:41:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:42:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751262133021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:42:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751262133021 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:43:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:43:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262197903 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:43:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262197903 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:43:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:43:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:43:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262199438 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:43:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262199438 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:45:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:45:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262317906 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:45:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262317906 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:45:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:45:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:45:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262319412 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:45:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262319412 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:46:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751262377014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:46:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751262377014 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:47:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751262434013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:47:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751262434013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:47:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751262437151 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:47:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751262437151 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:47:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:47:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751262437151 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:47:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:47:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:47:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262437917 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:47:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262437917 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:47:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751262437151 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:47:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:47:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:47:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:47:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262439526 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:47:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262439526 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:49:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:49:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262557934 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:49:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262557934 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:49:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:49:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:49:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262559479 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:49:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262559479 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:51:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751262677163 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:51:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751262677163 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:51:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:51:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262677911 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:51:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262677911 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:51:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:51:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:51:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262679382 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:51:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262679382 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:52:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751262735013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:52:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751262735013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:52:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751262737016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:52:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751262737017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:52:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:52:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751262737016 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:52:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:52:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751262737017 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:52:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:53:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:53:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262797974 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:53:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262797974 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:53:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:53:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:53:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262799481 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:53:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262799481 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:55:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:55:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262917910 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:55:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262917910 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:55:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:55:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:55:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262919415 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:55:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262919415 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:56:13 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:56:13 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262973798 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:56:13 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751262973798 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:56:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:56:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751262975027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:56:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751262975027 HTTP/1.1" 200 161
************ - - [30/Jun/2025:13:56:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:56:15 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262975333 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:56:15 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751262975333 HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:56:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751262976017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:56:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:56:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751262976018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:56:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:13:56:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751262976017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:13:56:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:13:56:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751262976018 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:13:56:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:13:57:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751263036018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:57:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751263036018 HTTP/1.1" 200 159
************ - - [30/Jun/2025:13:59:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:13:59:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263157923 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:59:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263157923 HTTP/1.1" 200 155
************ - - [30/Jun/2025:13:59:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:59:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:13:59:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263159455 HTTP/1.1" 200 -
************ - - [30/Jun/2025:13:59:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263159455 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:01:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751263277175 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:01:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751263277175 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:01:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:01:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263277956 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:01:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263277956 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:01:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:01:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:01:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263279472 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:01:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263279472 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:02:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751263337013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:02:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751263337016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:02:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751263337014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:02:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:02:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751263337013 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:02:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751263337016 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:02:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:02:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751263337014 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:02:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:03:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:03:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263397890 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:03:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263397890 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:03:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:03:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:03:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263399459 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:03:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263399459 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:05:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:05:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263517911 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:05:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263517911 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:05:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:05:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:05:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263519391 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:05:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263519391 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:06:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751263577015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:06:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751263577015 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:07:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751263637160 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:07:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751263637161 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:07:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:07:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751263637160 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:07:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:07:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:07:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263637932 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:07:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263637932 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:07:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751263638026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:07:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751263638026 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:07:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751263637161 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:07:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:07:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:07:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:07:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263639563 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:07:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263639563 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:09:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:09:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263757920 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:09:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263757920 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:09:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:09:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:09:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263759441 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:09:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263759441 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:11:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751263877160 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:11:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751263877160 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:11:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:11:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263877902 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:11:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263877902 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:11:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:11:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:11:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263879385 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:11:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263879385 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:12:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751263937021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:12:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751263937022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:12:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:12:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751263937021 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:12:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:12:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751263937022 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:12:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:12:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751263939013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:12:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751263939013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:13:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:13:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263997924 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:13:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751263997924 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:13:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:13:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:13:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263999426 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:13:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751263999426 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:15:11 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:15:11 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264111317 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:15:11 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264111317 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:15:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:15:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:15:12 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264112920 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:15:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264112920 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:16:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751264177027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:16:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751264177027 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:17:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751264237164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:17:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751264237164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:17:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:17:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751264237164 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:17:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:17:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:17:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264238015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:17:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264238015 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:17:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751264237164 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:17:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:17:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:17:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:17:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264239623 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:17:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264239623 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:17:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751264240013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:17:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751264240013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:19:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:19:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264357934 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:19:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264357934 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:19:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:19:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:19:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264359538 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:19:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264359538 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:21:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751264477172 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:21:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751264477172 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:21:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:21:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264477969 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:21:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264477969 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:21:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:21:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:21:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264479570 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:21:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264479570 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:22:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751264537021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:22:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751264537022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:22:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:22:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751264537021 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:22:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:22:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751264537022 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:22:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:22:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751264541020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:22:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751264541020 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:23:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:23:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264597930 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:23:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264597930 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:23:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:23:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:23:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264599485 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:23:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264599485 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:25:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:25:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264717931 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:25:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264717931 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:25:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:25:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:25:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264719446 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:25:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264719446 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:26:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751264777014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:26:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751264777014 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:27:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751264837168 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:27:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751264837169 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:27:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:27:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751264837168 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:27:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:27:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:27:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264838031 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:27:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264838031 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:27:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751264837169 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:27:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:27:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:27:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:27:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264839722 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:27:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264839722 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:27:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751264842021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:27:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751264842021 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:29:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:29:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264957914 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:29:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751264957914 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:29:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:29:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:29:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264959422 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:29:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751264959422 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:31:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751265077160 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:31:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751265077160 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:31:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:31:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265077920 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:31:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265077920 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:31:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:31:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:31:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265079427 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:31:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265079427 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:32:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751265137018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:32:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751265137019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:32:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:32:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751265137018 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:32:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:32:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751265137019 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:32:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:32:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751265143020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:32:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751265143020 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:33:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:33:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265197915 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:33:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265197915 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:33:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:33:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:33:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265199439 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:33:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265199439 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:35:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:35:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265317912 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:35:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265317912 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:35:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:35:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:35:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265319440 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:35:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265319440 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:36:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:36:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265372230 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:36:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265372230 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:36:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:36:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:36:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265373701 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:36:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265373701 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:36:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751265374242 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:36:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751265374242 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:36:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751265374317 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:36:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751265374317 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:36:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751265374599 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:36:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:36:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:36:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:36:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751265374599 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:37:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751265443061 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:37:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751265443061 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:38:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:38:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265492166 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:38:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265492166 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:38:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:38:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:38:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265493701 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:38:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265493701 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:40:13 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:40:13 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265613935 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:40:13 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265613935 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:40:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:40:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:40:15 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265615494 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:40:15 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265615494 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:41:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751265677027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:41:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751265677027 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:42:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751265737025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:42:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751265737026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:42:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:42:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751265737025 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:42:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:42:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751265737026 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:42:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:42:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751265744017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:42:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751265744017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:43:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:43:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265797918 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:43:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265797918 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:43:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:43:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:43:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265799427 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:43:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265799427 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:45:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:45:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265917927 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:45:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751265917927 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:45:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:45:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:45:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265919502 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:45:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751265919502 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:46:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751265977023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:46:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751265977023 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:47:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751266037170 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:47:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:47:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751266037170 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:47:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:47:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:47:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751266037170 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:47:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:47:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:47:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266037935 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:47:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266037935 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:47:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751266037170 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:47:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:47:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:47:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:47:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266039468 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:47:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266039468 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:47:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751266045022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:47:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751266045022 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:49:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:49:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266157920 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:49:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266157920 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:49:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:49:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:49:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266159442 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:49:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266159442 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:51:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751266277168 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:51:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751266277168 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:51:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:51:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266277918 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:51:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266277918 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:51:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:51:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:51:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266279457 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:51:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266279457 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:52:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751266337020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:52:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751266337021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:52:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:52:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:52:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751266337020 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:52:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:52:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751266337021 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:52:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:52:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751266346020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:52:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751266346020 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:53:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:53:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266397926 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:53:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266397926 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:53:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:53:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:53:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266399444 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:53:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266399444 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:55:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:55:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266517949 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:55:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266517949 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:55:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:55:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:55:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266519487 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:55:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266519487 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:56:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751266577029 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:56:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751266577029 HTTP/1.1" 200 161
************ - - [30/Jun/2025:14:57:17 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:57:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751266637164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:57:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751266637164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:57:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:14:57:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751266637164 HTTP/1.1" 200 160
************ - - [30/Jun/2025:14:57:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:14:57:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:57:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266637968 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:57:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:14:57:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266637968 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:57:18 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:57:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751266637164 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:14:57:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:57:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:57:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266639862 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:57:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266639862 HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:57:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751266647019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:57:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751266647019 HTTP/1.1" 200 159
************ - - [30/Jun/2025:14:59:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:14:59:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266757924 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:59:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266757924 HTTP/1.1" 200 155
************ - - [30/Jun/2025:14:59:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:59:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:14:59:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266759450 HTTP/1.1" 200 -
************ - - [30/Jun/2025:14:59:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266759450 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:01:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751266877159 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:01:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751266877159 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:01:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:01:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266877996 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:01:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266877996 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:01:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:01:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:01:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266879510 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:01:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266879510 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:02:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751266937015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:02:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751266937016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:02:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:02:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751266937015 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:02:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:02:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751266937016 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:02:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:02:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751266948026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:02:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751266948026 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:03:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:03:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266997923 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:03:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751266997923 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:03:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:03:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:03:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266999478 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:03:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751266999478 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:05:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:05:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267117943 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:05:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267117943 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:05:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:05:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:05:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267119470 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:05:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267119470 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:06:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751267177016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:06:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751267177016 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:07:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751267237169 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:07:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751267237169 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:07:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:07:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751267237169 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:07:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:07:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:07:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267237940 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:07:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267237940 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:07:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751267237169 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:07:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:07:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:07:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:07:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267239508 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:07:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267239508 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:07:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751267249017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:07:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751267249017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:09:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:09:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267357943 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:09:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267357943 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:09:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:09:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:09:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267359484 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:09:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267359484 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:10:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:10:24 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267424112 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:10:24 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267424112 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:10:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:10:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:10:25 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267425691 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:10:25 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267425691 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:11:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751267475018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:11:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751267475018 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:11:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751267476022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:11:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751267476022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:11:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:11:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:11:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751267476022 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:11:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:11:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751267476022 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:11:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:12:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751267550027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:12:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751267550027 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:13:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:13:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267597956 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:13:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267597956 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:13:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:13:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:13:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267599528 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:13:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267599528 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:15:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:15:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267717940 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:15:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267717940 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:15:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:15:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:15:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267719490 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:15:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267719490 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:16:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751267777022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:16:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751267777022 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:17:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751267837164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:17:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751267837164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:17:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:17:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751267837164 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:17:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:17:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:17:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:17:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267838292 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:17:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267838292 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:17:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751267837164 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:17:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:17:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:17:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267839904 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:17:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267839904 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:17:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751267851021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:17:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751267851021 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:19:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:19:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267957971 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:19:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751267957971 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:19:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:19:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:19:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267959555 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:19:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751267959555 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:21:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751268077201 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:21:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751268077201 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:21:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:21:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268078021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:21:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268078021 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:21:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:21:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:21:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268079628 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:21:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268079628 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:22:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751268137024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:22:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751268137025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:22:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:22:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751268137024 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:22:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:22:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751268137025 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:22:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:22:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751268152013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:22:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751268152013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:23:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:23:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268197935 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:23:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268197935 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:23:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:23:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:23:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268199528 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:23:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268199528 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:25:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:25:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268317951 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:25:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268317951 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:25:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:25:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:25:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268319568 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:25:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268319568 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:26:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751268377013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:26:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751268377013 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:27:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751268437155 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:27:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751268437156 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:27:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:27:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751268437155 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:27:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:27:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:27:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268437896 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:27:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268437896 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:27:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:27:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751268437156 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:27:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:27:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:27:18 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268438985 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:27:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268438985 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:27:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751268453019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:27:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751268453019 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:29:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:29:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268557982 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:29:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268557982 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:29:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:29:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:29:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268559645 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:29:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268559645 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:31:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751268677163 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:31:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751268677163 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:31:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:31:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268677995 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:31:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268677995 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:31:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:31:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:31:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268679608 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:31:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268679608 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:32:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751268737027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:32:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751268737028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:32:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:32:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751268737027 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:32:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:32:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751268737028 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:32:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:32:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751268754027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:32:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751268754027 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:33:17 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:33:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:33:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268797967 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:33:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268797967 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:33:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:33:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:33:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:33:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268799567 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:33:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268799567 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:34:26 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:34:26 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268866043 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:34:26 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751268866043 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:34:26 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:34:27 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:34:27 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268867680 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:34:27 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751268867680 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:36:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751268977015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:36:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751268977015 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:37:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751269037157 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:37:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:37:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751269037157 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:37:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:37:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751269037157 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:37:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:37:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:37:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269037993 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:37:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269037993 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:37:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751269037157 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:37:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:37:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:37:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:37:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269039616 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:37:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269039616 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:37:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751269055027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:37:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751269055027 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:39:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:39:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269157928 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:39:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269157928 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:39:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:39:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:39:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269159549 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:39:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269159549 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:41:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751269277161 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:41:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751269277161 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:41:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:41:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269277954 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:41:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269277954 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:41:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:41:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:41:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269279511 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:41:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269279511 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:42:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751269337018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:42:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751269337018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:42:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:42:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751269337018 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:42:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:42:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751269337018 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:42:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:42:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751269356015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:42:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751269356015 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:43:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:43:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269398007 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:43:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269398007 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:43:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:43:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:43:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269399596 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:43:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269399596 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:45:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:45:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269518013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:45:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269518013 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:45:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:45:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:45:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269519606 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:45:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269519606 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:46:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751269577020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:46:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751269577020 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:47:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751269637176 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:47:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751269637177 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:47:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:47:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751269637176 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:47:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:47:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:47:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269638103 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:47:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269638103 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:47:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751269637177 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:47:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:47:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:47:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:47:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269639748 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:47:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269639748 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:47:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751269657025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:47:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751269657025 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:49:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:49:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269757957 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:49:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269757957 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:49:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:49:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:49:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269759556 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:49:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269759556 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:51:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751269877155 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:51:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751269877155 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:51:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:51:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269877939 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:51:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269877939 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:51:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:51:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:51:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269879554 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:51:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269879554 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:52:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751269937018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:52:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751269937019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:52:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:52:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751269937018 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:52:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:52:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751269937019 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:52:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:52:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751269958021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:52:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751269958021 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:53:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:53:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269997951 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:53:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751269997951 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:53:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:53:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:53:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269999511 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:53:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751269999511 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:55:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:55:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270117985 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:55:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270117985 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:55:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:55:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:55:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270119620 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:55:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270119620 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:56:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751270177021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:56:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751270177021 HTTP/1.1" 200 161
************ - - [30/Jun/2025:15:57:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751270237160 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:57:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751270237160 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:57:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:15:57:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751270237160 HTTP/1.1" 200 160
************ - - [30/Jun/2025:15:57:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:15:57:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:57:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270238027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:57:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270238027 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:57:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751270237160 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:15:57:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:15:57:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:57:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:57:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270239663 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:57:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270239663 HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:57:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751270259020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:57:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751270259020 HTTP/1.1" 200 159
************ - - [30/Jun/2025:15:59:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:15:59:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270357990 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:59:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270357990 HTTP/1.1" 200 155
************ - - [30/Jun/2025:15:59:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:59:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:15:59:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270359573 HTTP/1.1" 200 -
************ - - [30/Jun/2025:15:59:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270359573 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:01:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751270477159 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:01:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751270477159 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:01:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:01:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270477957 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:01:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270477957 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:01:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:01:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:01:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270479549 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:01:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270479549 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:02:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751270537018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:02:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751270537020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:02:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:02:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751270537018 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:02:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:02:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751270537020 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:02:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:02:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751270560017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:02:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751270560017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:03:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:03:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270597972 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:03:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270597972 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:03:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:03:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:03:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270599569 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:03:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270599569 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:05:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:05:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270717976 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:05:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270717976 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:05:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:05:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:05:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270719533 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:05:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270719533 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:06:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751270777025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:06:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751270777025 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:07:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751270837191 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:07:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751270837192 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:07:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:07:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751270837191 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:07:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:07:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:07:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270838057 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:07:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270838057 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:07:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751270837192 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:07:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:07:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:07:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:07:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270839759 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:07:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270839759 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:07:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751270861023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:07:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751270861023 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:09:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:09:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270957966 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:09:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751270957966 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:09:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:09:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:09:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270959582 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:09:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751270959582 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:11:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751271077158 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:11:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751271077158 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:11:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:11:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271077964 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:11:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271077964 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:11:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:11:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:11:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271079550 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:11:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271079550 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:12:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751271137021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:12:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751271137021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:12:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:12:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751271137021 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:12:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:12:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751271137021 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:12:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:12:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751271162020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:12:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751271162020 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:13:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:13:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271197950 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:13:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271197950 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:13:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:13:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:13:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271199589 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:13:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271199589 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:15:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:15:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271318007 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:15:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271318007 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:15:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:15:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:15:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271319612 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:15:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271319612 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:16:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751271377021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:16:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751271377021 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:17:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751271437164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:17:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751271437165 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:17:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:17:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751271437164 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:17:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:17:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:17:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271437979 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:17:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271437979 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:17:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751271437165 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:17:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:17:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:17:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:17:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271439528 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:17:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271439528 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:17:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751271463013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:17:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751271463013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:19:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:19:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271557950 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:19:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271557950 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:19:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:19:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:19:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271559572 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:19:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271559572 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:21:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751271677166 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:21:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751271677166 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:21:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:21:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271677976 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:21:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271677976 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:21:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:21:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:21:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271679936 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:21:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271679936 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:22:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751271737019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:22:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751271737020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:22:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:22:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751271737019 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:22:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:22:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751271737020 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:22:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:22:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751271764019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:22:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751271764019 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:23:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:23:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271797969 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:23:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271797969 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:23:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:23:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:23:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271799603 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:23:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271799603 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:25:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:25:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271917993 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:25:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751271917993 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:25:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:25:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:25:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271919591 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:25:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751271919591 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:26:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751271977018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:26:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751271977018 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:27:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751272037159 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:27:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751272037160 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:27:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:27:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751272037159 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:27:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:27:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:27:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272037988 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:27:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272037988 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:27:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751272037160 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:27:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:27:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:27:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:27:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272039629 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:27:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272039629 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:27:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751272065023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:27:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751272065023 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:29:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:29:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272157981 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:29:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272157981 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:29:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:29:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:29:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272159573 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:29:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272159573 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:31:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751272277162 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:31:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751272277162 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:31:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:31:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272277968 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:31:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272277968 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:31:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:31:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:31:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272279572 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:31:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272279572 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:32:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751272337026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:32:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751272337027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:32:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:32:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751272337026 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:32:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:32:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:32:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751272337027 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:32:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751272366025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:32:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751272366025 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:33:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:33:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272397976 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:33:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272397976 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:33:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:33:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:33:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272399563 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:33:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272399563 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:35:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:35:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272517981 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:35:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272517981 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:35:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:35:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:35:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272519570 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:35:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272519570 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:36:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751272577023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:36:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751272577023 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:37:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751272637171 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:37:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751272637172 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:37:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:37:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751272637171 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:37:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:37:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:37:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272637990 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:37:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272637990 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:37:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:37:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751272637172 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:37:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:37:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:37:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272639538 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:37:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272639538 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:37:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751272667019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:37:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751272667019 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:39:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:39:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272757963 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:39:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272757963 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:39:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:39:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:39:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272759654 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:39:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272759654 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:41:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751272877170 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:41:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751272877170 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:41:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:41:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272877966 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:41:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272877966 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:41:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:41:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:41:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272879534 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:41:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272879534 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:42:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751272937027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:42:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751272937028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:42:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:42:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751272937027 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:42:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:42:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751272937028 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:42:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:42:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751272968017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:42:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751272968017 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:43:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:43:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272997981 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:43:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751272997981 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:43:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:43:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:43:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272999581 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:43:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751272999581 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:45:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:45:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273117974 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:45:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273117974 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:45:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:45:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:45:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273119547 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:45:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273119547 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:46:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751273177022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:46:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751273177022 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:47:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751273237163 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:47:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751273237163 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:47:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:47:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751273237163 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:47:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:47:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:47:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273237961 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:47:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273237961 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:47:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751273237163 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:47:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:47:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:47:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:47:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273239586 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:47:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273239586 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:47:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751273269014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:47:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751273269014 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:49:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:49:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273357964 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:49:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273357964 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:49:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:49:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:49:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273359648 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:49:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273359648 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:51:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751273477174 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:51:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751273477174 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:51:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:51:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273477977 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:51:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273477977 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:51:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:51:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:51:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273479556 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:51:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273479556 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:52:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751273537021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:52:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:52:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:52:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751273537022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:52:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:52:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751273537021 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:52:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:52:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751273537022 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:52:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:52:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751273570021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:52:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751273570021 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:53:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:53:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273597988 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:53:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273597988 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:53:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:53:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:53:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273599583 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:53:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273599583 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:55:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:55:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273717970 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:55:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273717970 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:55:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:55:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:55:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273719620 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:55:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273719620 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:56:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751273777017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:56:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751273777017 HTTP/1.1" 200 161
************ - - [30/Jun/2025:16:57:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751273837179 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:57:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751273837180 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:57:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:16:57:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751273837179 HTTP/1.1" 200 160
************ - - [30/Jun/2025:16:57:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:16:57:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751273837180 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:16:57:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:16:57:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:57:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273838875 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:57:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273838875 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:57:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:57:20 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:57:20 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273840472 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:57:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273840472 HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:57:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751273871027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:57:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751273871027 HTTP/1.1" 200 159
************ - - [30/Jun/2025:16:59:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:16:59:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273957988 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:59:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751273957988 HTTP/1.1" 200 155
************ - - [30/Jun/2025:16:59:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:59:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:16:59:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273959565 HTTP/1.1" 200 -
************ - - [30/Jun/2025:16:59:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751273959565 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:01:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751274077166 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:01:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751274077166 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:01:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:01:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274077955 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:01:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274077955 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:01:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:01:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:01:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274079555 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:01:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274079555 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:02:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751274137017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:02:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751274137018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:02:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:02:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751274137017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:02:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:02:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:02:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751274137018 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:02:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751274172015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:02:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751274172015 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:03:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:03:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274197962 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:03:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274197962 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:03:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:03:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:03:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274199561 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:03:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274199561 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:05:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:05:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274317959 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:05:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274317959 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:05:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:05:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:05:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274319534 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:05:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274319534 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:06:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751274377022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:06:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751274377022 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:07:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751274437189 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:07:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751274437189 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:07:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:07:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751274437189 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:07:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:07:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:07:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274438002 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:07:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274438002 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:07:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751274437189 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:07:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:07:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:07:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:07:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274439696 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:07:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274439696 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:07:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751274473013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:07:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751274473013 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:09:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:09:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274557976 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:09:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274557976 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:09:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:09:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:09:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274559623 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:09:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274559623 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:11:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751274677158 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:11:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751274677158 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:11:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:11:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274677974 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:11:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274677974 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:11:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:11:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:11:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274679574 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:11:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274679574 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:12:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751274737013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:12:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751274737014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:12:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:12:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751274737013 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:12:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:12:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:12:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751274737014 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:12:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751274774024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:12:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751274774024 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:13:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:13:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274797984 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:13:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274797984 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:13:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:13:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:13:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274799584 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:13:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274799584 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:15:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:15:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274918025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:15:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751274918025 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:15:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:15:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:15:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274919563 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:15:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751274919563 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:16:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751274977021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:16:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751274977021 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:17:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751275037185 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:17:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275037186 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:17:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:17:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751275037185 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:17:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:17:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:17:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275037990 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:17:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275037990 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:17:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275037186 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:17:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:17:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:17:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:17:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275039668 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:17:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275039668 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:17:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275075022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:17:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275075022 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:19:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:19:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275157979 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:19:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275157979 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:19:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:19:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:19:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275159583 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:19:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275159583 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:21:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751275277172 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:21:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751275277172 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:21:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:21:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275278011 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:21:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275278011 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:21:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:21:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:21:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275279613 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:21:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275279613 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:22:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751275337027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:22:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275337028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:22:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:22:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751275337027 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:22:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:22:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275337028 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:22:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:22:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275376027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:22:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275376027 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:23:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:23:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275398014 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:23:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275398014 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:23:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:23:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:23:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275399620 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:23:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275399620 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:24:13 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:24:13 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275453972 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:24:13 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275453972 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:24:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:24:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:24:15 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275455571 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:24:15 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275455571 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:26:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751275577020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:26:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751275577020 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:27:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751275637197 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:27:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275637197 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:27:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:27:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751275637197 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:27:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:27:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:27:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275638074 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:27:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275638074 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:27:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275637197 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:27:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:27:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:27:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:27:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275639712 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:27:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275639712 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:27:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275677026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:27:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275677026 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:29:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:29:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275757990 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275757990 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:29:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:29:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:29:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275759601 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275759601 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:29:38 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [30/Jun/2025:17:29:38 +0800] "GET /login HTTP/1.1" 302 -
************ - - [30/Jun/2025:17:29:38 +0800] "GET /login?code=8RoOfZ&state=A83lx5 HTTP/1.1" 302 -
************ - - [30/Jun/2025:17:29:38 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [30/Jun/2025:17:29:39 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1751275779654 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:39 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1751275779654 HTTP/1.1" 200 552
************ - - [30/Jun/2025:17:29:39 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1751275779919 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:39 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1751275779919 HTTP/1.1" 200 61649
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1751275780079 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1751275780079 HTTP/1.1" 200 10388
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1751275780167 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1751275780167 HTTP/1.1" 200 2009
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1751275780542 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+17:29:40&etm=&_timer304=1751275780542 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751275780542 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+18:00&filterCnt=6&_timer304=1751275780542 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751275780542 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751275780542 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:40 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+17:29:40&etm=&_timer304=1751275780542 HTTP/1.1" 200 156
************ - - [30/Jun/2025:17:29:40 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1751275780542 HTTP/1.1" 200 1482
************ - - [30/Jun/2025:17:29:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751275780542 HTTP/1.1" 200 166
************ - - [30/Jun/2025:17:29:40 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+18:00&filterCnt=6&_timer304=1751275780542 HTTP/1.1" 200 164
************ - - [30/Jun/2025:17:29:40 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [30/Jun/2025:17:29:40 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [30/Jun/2025:17:29:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:17:29:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:17:29:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751275780542 HTTP/1.1" 200 169
************ - - [30/Jun/2025:17:29:40 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751275780542 HTTP/1.1" 200 13016
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-29&_timer304=1751275781023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/base/saas/token?_timer304=1751275781023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1751275781034 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [30/Jun/2025:17:29:41 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1751275781034 HTTP/1.1" 200 2009
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [30/Jun/2025:17:29:41 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [30/Jun/2025:17:29:42 +0800] "GET /api/base/saas/token?_timer304=1751275781023 HTTP/1.1" 200 411
************ - - [30/Jun/2025:17:29:42 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [30/Jun/2025:17:29:42 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:29:42 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [30/Jun/2025:17:29:42 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [30/Jun/2025:17:29:43 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-29&_timer304=1751275781023 HTTP/1.1" 200 444
************ - - [30/Jun/2025:17:29:49 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751275789379 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275789386 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:49 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751275789379 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:29:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275789386 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1751275792312 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1751275792312 HTTP/1.1" 200 144
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+17:29:52&etm=&_timer304=1751275792589 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+18:00&filterCnt=6&_timer304=1751275792589 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751275792589 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751275792589 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751275792589 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:17:29:52 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [30/Jun/2025:17:29:52 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751275792590 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1751275792594 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [30/Jun/2025:17:29:52 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-30+08:00&etm=2025-06-30+18:00&filterCnt=6&_timer304=1751275792589 HTTP/1.1" 200 164
************ - - [30/Jun/2025:17:29:52 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-27+17:29:52&etm=&_timer304=1751275792589 HTTP/1.1" 200 156
************ - - [30/Jun/2025:17:29:52 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1751275792589 HTTP/1.1" 200 166
************ - - [30/Jun/2025:17:29:52 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751275792590 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:29:52 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1751275792589 HTTP/1.1" 200 169
************ - - [30/Jun/2025:17:29:52 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1751275792594 HTTP/1.1" 200 159616
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275792708 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [30/Jun/2025:17:29:52 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [30/Jun/2025:17:29:52 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1751275792589 HTTP/1.1" 200 13016
************ - - [30/Jun/2025:17:29:52 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275792727 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:52 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275792727 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:29:52 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [30/Jun/2025:17:29:52 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [30/Jun/2025:17:29:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [30/Jun/2025:17:29:53 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:29:53 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275793713 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:53 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275793713 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:29:53 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275792708 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:29:54 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:54 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [30/Jun/2025:17:29:54 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:29:55 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:29:55 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275795379 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:29:55 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275795379 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:29:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:17:30:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:30:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:17:30:39 +0800] "OPTIONS /api/syq/rsvr/export-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:30:46 +0800] "POST /api/syq/rsvr/export-latest-info HTTP/1.1" 200 114785
************ - - [30/Jun/2025:17:31:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751275877164 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:31:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751275877164 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:31:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:31:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275878001 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:31:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275878001 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:31:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:31:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:31:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275879494 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:31:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275879494 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:32:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751275937016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:32:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275937017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:32:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:32:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751275937016 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:32:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:32:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751275937017 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:32:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:32:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275978025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:32:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751275978025 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:33:17 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:33:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:33:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275997981 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:33:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751275997981 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:33:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:33:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:33:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:33:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275999479 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:33:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751275999479 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:33:47 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:33:48 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:17:34:25 +0800] "OPTIONS /api/dzsj/rvrsvr/get-info-by-cd-tp?objtp=6&_timer304=1751276065865 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:25 +0800] "GET /api/dzsj/rvrsvr/get-info-by-cd-tp?objtp=6&_timer304=1751276065865 HTTP/1.1" 200 153
************ - - [30/Jun/2025:17:34:25 +0800] "OPTIONS /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:26 +0800] "POST /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 626476
************ - - [30/Jun/2025:17:34:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10800112&stm=2025-06-27+17:35&etm=2025-06-30+17:35&_timer304=1751276066503 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:26 +0800] "OPTIONS /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:26 +0800] "OPTIONS /api/syq/rsvr/select-streglat-stcd?stcd=10800112&ymdh=2025-06-30+17:35&_timer304=1751276066503 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:26 +0800] "OPTIONS /api/syq/rsvr/select-tm-list?stcd=10800112&stm=2025-06-27+17:35&etm=2025-06-30+17:35&_timer304=1751276066503 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:26 +0800] "POST /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 245
************ - - [30/Jun/2025:17:34:26 +0800] "GET /api/syq/rsvr/select-tm-list?stcd=10800112&stm=2025-06-27+17:35&etm=2025-06-30+17:35&_timer304=1751276066503 HTTP/1.1" 200 147
************ - - [30/Jun/2025:17:34:26 +0800] "GET /api/syq/rsvr/select-streglat-stcd?stcd=10800112&ymdh=2025-06-30+17:35&_timer304=1751276066503 HTTP/1.1" 200 153
************ - - [30/Jun/2025:17:34:26 +0800] "GET /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10800112&stm=2025-06-27+17:35&etm=2025-06-30+17:35&_timer304=1751276066503 HTTP/1.1" 200 158
************ - - [30/Jun/2025:17:34:26 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10800112&_timer304=1751276066657 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:26 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10800112&_timer304=1751276066657 HTTP/1.1" 200 553
************ - - [30/Jun/2025:17:34:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751276080015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751276080015 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:34:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276090016 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276090016 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:34:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276093025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:53 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:53 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:53 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276093029 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:53 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:34:53 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276093025 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:34:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:34:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:34:54 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276093029 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:34:54 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:35:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:35:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:17:35:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:35:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276117995 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:35:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276117995 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:35:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:35:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:35:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276119496 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:35:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276119496 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:36:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276177028 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:36:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276177028 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:37:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751276237195 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:37:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276237196 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:37:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:37:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751276237195 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:37:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:37:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:37:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276238070 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:37:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276238070 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:37:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276237196 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:37:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:37:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:37:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:37:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276239721 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:37:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276239721 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:37:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276279025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:37:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276279025 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:39:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:39:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276358006 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:39:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276358006 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:39:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:39:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:39:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276359508 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:39:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276359508 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:39:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751276380125 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:39:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751276380125 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:39:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276391024 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:39:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276391024 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:39:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276393019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:39:53 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:39:53 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:39:53 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276393021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:39:53 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:39:53 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276393019 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:39:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:39:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:39:54 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276393021 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:39:54 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:40:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:40:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:17:41:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276477171 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:41:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276477171 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:41:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:41:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276477978 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:41:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276477978 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:41:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:41:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:41:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276479465 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:41:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276479465 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:42:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751276537020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:42:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276537023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:42:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:42:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751276537020 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:42:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:42:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276537023 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:42:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:43:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276580015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:43:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276580015 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:43:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:43:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276597979 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:43:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276597979 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:43:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:43:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:43:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276599689 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:43:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276599689 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:43:56 +0800] "OPTIONS /api/syq/rsvr/export-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:44:33 +0800] "POST /api/syq/rsvr/export-latest-info HTTP/1.1" 200 8206
************ - - [30/Jun/2025:17:44:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751276680017 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:44:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751276680017 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:44:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276692018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:44:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276692018 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:44:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276693018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:44:53 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:44:53 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:44:53 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276693019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:44:53 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:44:53 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276693018 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:44:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:44:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:44:54 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276693019 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:44:54 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:45:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:45:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:17:45:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:45:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276718018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:45:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276718018 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:45:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:45:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:45:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276719596 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:45:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276719596 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:46:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276777015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:46:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276777015 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:47:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751276837193 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:47:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276837193 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:47:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:47:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751276837193 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:47:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:47:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:47:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276838092 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:47:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276838092 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:47:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276837193 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:47:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:47:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:47:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:47:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276839681 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:47:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276839681 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:48:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276881023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:48:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276881023 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:48:05 +0800] "OPTIONS /api/syq/rsvr/export-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:02 +0800] "POST /api/syq/rsvr/export-latest-info HTTP/1.1" 200 8206
************ - - [30/Jun/2025:17:49:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:49:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276958027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751276958027 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:49:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:49:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:49:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276959542 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751276959542 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:49:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751276980027 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751276980027 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:49:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276993022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276993023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:53 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:53 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:53 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276993026 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:53 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:49:53 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751276993023 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:49:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:49:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751276993022 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:49:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:49:54 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751276993026 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:49:54 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:49:59 +0800] "OPTIONS /api/syq/rsvr/export-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:50:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:50:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:17:50:12 +0800] "POST /api/syq/rsvr/export-latest-info HTTP/1.1" 200 114784
************ - - [30/Jun/2025:17:51:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751277077165 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:51:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751277077165 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:51:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:51:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751277078020 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:51:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751277078020 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:51:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:51:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:51:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751277079585 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:51:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751277079585 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:52:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751277137022 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:52:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751277137023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:52:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:52:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751277137022 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:52:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:52:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751277137023 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:52:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:53:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751277182019 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:53:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751277182019 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:53:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:53:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751277198021 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:53:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751277198021 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:53:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:53:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:53:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751277199508 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:53:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751277199508 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:54:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751277280018 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:54:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751277280018 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:54:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751277293013 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:54:53 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:54:53 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751277293015 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:54:53 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:54:53 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:54:53 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751277293013 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:54:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:54:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:54:53 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751277293015 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:54:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751277294023 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:54:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751277294023 HTTP/1.1" 200 159
************ - - [30/Jun/2025:17:54:54 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:55:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:55:17 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:55:18 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:55:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751277318188 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:55:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751277318188 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:55:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [30/Jun/2025:17:55:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:55:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:55:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751277319680 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:55:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751277319680 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:56:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751277377030 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:56:17 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1751277377030 HTTP/1.1" 200 161
************ - - [30/Jun/2025:17:57:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1751277437157 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:57:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751277437158 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:57:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [30/Jun/2025:17:57:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1751277437157 HTTP/1.1" 200 160
************ - - [30/Jun/2025:17:57:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [30/Jun/2025:17:57:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [30/Jun/2025:17:57:18 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751277437999 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:57:18 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1751277437999 HTTP/1.1" 200 155
************ - - [30/Jun/2025:17:57:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-30+08:00&etm=2025-06-30+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1751277437158 HTTP/1.1" 200 440467
************ - - [30/Jun/2025:17:57:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [30/Jun/2025:17:57:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:57:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:57:19 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751277439626 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:57:19 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1751277439626 HTTP/1.1" 200 232
************ - - [30/Jun/2025:17:58:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1751277483025 HTTP/1.1" 200 -
************ - - [30/Jun/2025:17:58:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1751277483025 HTTP/1.1" 200 159
