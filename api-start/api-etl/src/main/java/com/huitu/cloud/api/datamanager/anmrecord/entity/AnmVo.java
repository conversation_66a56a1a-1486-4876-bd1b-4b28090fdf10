package com.huitu.cloud.api.datamanager.anmrecord.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 * @since 2019-12-07
 */
@ApiModel(value="AnmVo", description="异常数据表")
public class AnmVo extends BsnAnmrecordr {
    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "雨量值")
    private String drp;

    @ApiModelProperty(value = "水位值")
    private String z;

    @ApiModelProperty(value = "修改值")
    private String editValue;

    @ApiModelProperty(value = "阈值")
    private String threshold;

    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "政区编码")
    private String adcd;


    @ApiModelProperty(value = "测站基础类型名称")
    private String stadtpnm;

    @ApiModelProperty(value = "县级政区名称")
    private String xadnm;

    @ApiModelProperty(value = "测站联系人")
    private String  officer;

    @ApiModelProperty(value = "移动电话号码")
    private String  mphone;

    @ApiModelProperty(value = "异常描述（不用）")
    private String  desc;

    @ApiModelProperty(value = "站类")
    private String sttp;

    @ApiModelProperty(value = "异常数据采集时间")
    private String dataTm;

    @ApiModelProperty(value = "真正表名")
    private String reltbnm;

    @ApiModelProperty(value = "累计条数")
    private Integer cnt;

    public String getReltbnm() {
        return reltbnm;
    }

    public void setReltbnm(String reltbnm) {
        this.reltbnm = reltbnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getDrp() {
        return drp;
    }

    public void setDrp(String drp) {
        this.drp = drp;
    }

    public String getEditValue() {
        return editValue;
    }

    public void setEditValue(String editValue) {
        this.editValue = editValue;
    }

    public String getThreshold() {
        return threshold;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getOfficer() {
        return officer;
    }

    public void setOfficer(String officer) {
        this.officer = officer;
    }

    public String getMphone() {
        return mphone;
    }

    public void setMphone(String mphone) {
        this.mphone = mphone;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getZ() {
        return z;
    }

    public void setZ(String z) {
        this.z = z;
    }

    public String getDataTm() {
        return dataTm;
    }

    public void setDataTm(String dataTm) {
        this.dataTm = dataTm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getCnt() {
        return cnt;
    }

    public void setCnt(Integer cnt) {
        this.cnt = cnt;
    }
}
