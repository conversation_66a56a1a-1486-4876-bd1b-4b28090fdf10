package com.huitu.cloud.api.syq.warn.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.syq.river.entity.*;
import com.huitu.cloud.api.syq.skdt.entity.RsvrRiverInfo;
import com.huitu.cloud.api.syq.warn.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
public interface SyqWarnDao extends BaseMapper<EarlyWarning1> {

    List<EwVillageVo> getVillageVoList(@Param("ad") String ad,@Param("level")int level, @Param("stm") String stm, @Param("etm") String etm);

    List<EwRivelVo> getEwRivelVoList(@Param("ad") String ad,@Param("level")int level,@Param("stm") String stm, @Param("etm") String etm);

    List<EwReserVo> getEwReserVoList(@Param("ad") String ad,@Param("level")int level,@Param("stm") String stm, @Param("etm") String etm);

    List<EarlyWarningVo> getEarlyWarning1List(@Param("ad") String ad,@Param("level")int level,@Param("stm") String stm, @Param("etm") String etm, @Param("type") String s);

    List<EarlyWarningVo> getEarlyWarning3List(@Param("ad") String ad,@Param("level")int level,@Param("stm") String stm, @Param("etm") String etm, @Param("type") String s);

    List<EarlyWarningVo> getEarlyWarning6List(@Param("ad") String ad,@Param("level")int level,@Param("stm") String stm, @Param("etm") String etm, @Param("type") String s);

    List<EarlyWarningVo> getEarlyWarningVillageList(@Param("ad") String ad,@Param("level")int level,@Param("stm") String stm, @Param("etm") String etm, @Param("tbltp") String s);

    List<EarlyWarningVo> getEarlyWarningRiverList(@Param("ad") String ad,@Param("level")int level,@Param("stm") String stm, @Param("etm") String etm, @Param("tbltp") String s);

    List<EarlyWarningVo> getEarlyWarningRsvrist(@Param("ad") String ad,@Param("level")int level,@Param("stm") String stm, @Param("etm") String etm, @Param("tbltp") String s);

    List<ShWarnGrade> getShWarnGradeByXAdcdTm(@Param("ad") String ad,@Param("level") int level,@Param("subStrLevel") int subStrLevel,@Param("stm") String stm,@Param("etm") String etm);
}
