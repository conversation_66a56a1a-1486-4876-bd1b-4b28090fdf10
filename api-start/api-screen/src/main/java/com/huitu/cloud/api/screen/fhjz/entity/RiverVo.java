package com.huitu.cloud.api.screen.fhjz.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value="河流对象", description="河流对象")
public class RiverVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "河流编码")
    private String rvCode;

    @ApiModelProperty(value = "河流名称")
    private String rvName;

    @ApiModelProperty(value = "流经区域")
    private String flowArea;

    @ApiModelProperty(value = "河流长度")
    private String rvLen;

    @ApiModelProperty(value = "河流面积")
    private String rvBasArea;

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public String getFlowArea() {
        return flowArea;
    }

    public void setFlowArea(String flowArea) {
        this.flowArea = flowArea;
    }

    public String getRvLen() {
        return rvLen;
    }

    public void setRvLen(String rvLen) {
        this.rvLen = rvLen;
    }

    public String getRvBasArea() {
        return rvBasArea;
    }

    public void setRvBasArea(String rvBasArea) {
        this.rvBasArea = rvBasArea;
    }
}
