package com.huitu.cloud.api.syq.ad.service.impl;

import com.huitu.cloud.api.syq.ad.entity.AdcdB;
import com.huitu.cloud.api.syq.ad.mapper.AdcdbDao;
import com.huitu.cloud.api.syq.ad.service.AdService;
import com.huitu.cloud.util.AdcdUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AdServiceImpl implements AdService {
    @Autowired
    private AdcdbDao adcdbDao;
    @Override
    public Map<String,AdcdB> getAdInfoList(String adcd, String adLevl) {

        Map<String,Object> param=new HashMap<>();
        if(StringUtils.isNotBlank(adcd)){
            int level= AdcdUtil.getAdLevel(adcd);
            param.put("ad",adcd.substring(0,level));
            param.put("level",level);
        }
        param.put("adLvl",adLevl);
        List<AdcdB> adList=adcdbDao.selectByAdLevel(param);
        Map<String,AdcdB> result=adList.stream().collect(Collectors.toMap(AdcdB::getAdcd, Function.identity()));
        return result;
    }

    @Override
    public Map<String,AdcdB>  getAdSonList(String adcd) {
        List<AdcdB> adList=adcdbDao.getAdSonList(adcd);
        Map<String,AdcdB> result=adList.stream().collect(Collectors.toMap(AdcdB::getAdcd, Function.identity()));
        return result;
    }
}
