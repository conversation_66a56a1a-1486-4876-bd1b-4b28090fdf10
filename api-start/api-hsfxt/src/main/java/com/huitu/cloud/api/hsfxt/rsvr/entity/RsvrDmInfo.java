package com.huitu.cloud.api.hsfxt.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 河道断面信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-1-6
 */
@ApiModel(value="RsvrDmInfo对象", description="河道断面信息")
public class RsvrDmInfo implements Serializable {

    @ApiModelProperty(value = "断面编号")
    @TableField("RSCD")
    private Double rscd;

    @ApiModelProperty(value = "距大坝距离")
    @TableField("CDIST")
    private Double cdist;

    @ApiModelProperty(value = "最高水位")
    @TableField("Z")
    private Double z;

    @ApiModelProperty(value = "最大流量")
    @TableField("Q")
    private Double q;

    @ApiModelProperty(value = "糙率")
    @TableField("VR")
    private Double vr;

    public Double getRscd() {
        return rscd;
    }

    public void setRscd(Double rscd) {
        this.rscd = rscd;
    }

    public Double getCdist() {
        return cdist;
    }

    public void setCdist(Double cdist) {
        this.cdist = cdist;
    }

    public Double getZ() {
        return z;
    }

    public void setZ(Double z) {
        this.z = z;
    }

    public Double getQ() {
        return q;
    }

    public void setQ(Double q) {
        this.q = q;
    }

    public Double getVr() {
        return vr;
    }

    public void setVr(Double vr) {
        this.vr = vr;
    }
}
