package com.huitu.cloud.api.dzsj.elwinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 规模以上机电井扩展信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-27
 */
@TableName("BSN_UPELWINFO_B")
@ApiModel(value="BsnUpelwinfoB对象", description="规模以上机电井扩展信息表")
public class BsnUpelwinfoB extends Model<BsnUpelwinfoB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "编号")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "行政区地址省(自治区、直辖市) ")
    @TableField("XzqProvince")
    private String xzqprovince;

    @ApiModelProperty(value = "地区 (市、州、盟) ")
    @TableField("XzqCity2")
    private String xzqcity2;

    @ApiModelProperty(value = "县(区、市、旗) ")
    @TableField("XzqCountyTown")
    private String xzqcountytown;

    @ApiModelProperty(value = "乡(镇) ")
    @TableField("XzqCountry")
    private String xzqcountry;

    @ApiModelProperty(value = "街(村) ")
    @TableField("XzqVillage")
    private String xzqvillage;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("Addvcd")
    private String addvcd;

    @ApiModelProperty(value = "机电井名称 ")
    @TableField("JDJName")
    private String jdjname;

    @ApiModelProperty(value = "机电井编码 ")
    @TableField("JDJNo")
    private String jdjno;

    @ApiModelProperty(value = "东经(分)")
    @TableField("LGTDM")
    private Float lgtdm;

    @ApiModelProperty(value = "东经(秒)")
    @TableField("LGTDS")
    private Float lgtds;

    @ApiModelProperty(value = "东经(度)")
    @TableField("LGTDD")
    private Float lgtdd;

    @ApiModelProperty(value = "北纬(分)")
    @TableField("LTTDM")
    private Float lttdm;

    @ApiModelProperty(value = "北纬(秒)")
    @TableField("LTTDS")
    private Float lttds;

    @ApiModelProperty(value = "北纬(度)")
    @TableField("LTTDD")
    private Float lttdd;

    @ApiModelProperty(value = "成井时间（年） ")
    @TableField("CreateYear")
    private String createyear;

    @ApiModelProperty(value = "井深（米） ")
    @TableField("JingDeep")
    private Float jingdeep;

    @ApiModelProperty(value = "地下水埋深（米） ")
    @TableField("DxsMs")
    private Float dxsMs;

    @ApiModelProperty(value = "是否为单位自备井 ")
    @TableField("IsOneSelfWell")
    private String isoneselfwell;

    @ApiModelProperty(value = "井口井管内径(毫米)")
    @TableField("InnerDiameter")
    private Float innerdiameter;

    @ApiModelProperty(value = "井壁管材料 ")
    @TableField("Jbgcl")
    private String jbgcl;

    @ApiModelProperty(value = "应用状况 ")
    @TableField("Yyzk")
    private String yyzk;

    @ApiModelProperty(value = "是否已配套机电设备 ")
    @TableField("Isptjdsb")
    private String isptjdsb;

    @ApiModelProperty(value = "水泵型号 ")
    @TableField("SbType")
    private String sbtype;

    @ApiModelProperty(value = "水泵额定扬程(米) ")
    @TableField("Sbdeyc")
    private Float Sbdeyc;

    @ApiModelProperty(value = "水泵额定流量(立方米/小时) ")
    @TableField("SbdeQ")
    private Float sbdeq;

    @ApiModelProperty(value = "水泵额定功率(千瓦) ")
    @TableField("SbdeW")
    private Float sbdew;

    @ApiModelProperty(value = "是否已安装水量计量设施 ")
    @TableField("Isazsljlss")
    private String isazsljlss;

    @ApiModelProperty(value = "水量计量设施类型 ")
    @TableField("SljlssType")
    private String sljlsstype;

    @ApiModelProperty(value = "是否为规模以上地下水水源地的水井 ")
    @TableField("IsGmys")
    private String isgmys;

    @ApiModelProperty(value = "所在地貌类型区 ")
    @TableField("LandType")
    private String landtype;

    @ApiModelProperty(value = "所取用地下水的类型 ")
    @TableField("DxsType")
    private String dxstype;

    @ApiModelProperty(value = "所在水资源三级区名称及编码 ")
    @TableField("SjSourceType")
    private String sjsourcetype;

    @ApiModelProperty(value = "水源类型 ")
    @TableField("WaterSourceType")
    private String watersourcetype;

    @ApiModelProperty(value = "主要取水用途及效益 ")
    @TableField("Zyqsytjxy")
    private String zyqsytjxy;

    @ApiModelProperty(value = "乡村实际供水人口(人) ")
    @TableField("Xcsjgsrk")
    private Integer xcsjgsrk;

    @ApiModelProperty(value = "所在灌区类型 ")
    @TableField("SzgqType")
    private String szgqtype;

    @ApiModelProperty(value = "控制灌溉面积（亩） ")
    @TableField("Kzggmj")
    private Float kzggmj;

    @ApiModelProperty(value = "实际灌溉面积（亩） ")
    @TableField("Sjggmj")
    private Float sjggmj;

    @ApiModelProperty(value = "主要取水用途")
    @TableField("Zyqsyt")
    private String zyqsyt;

    @ApiModelProperty(value = "取水量确定方法 ")
    @TableField("Qslqdff")
    private String qslqdff;

    @ApiModelProperty(value = "全年耗电量(kW·h) ")
    @TableField("Qnhdl")
    private Float qnhdl;

    @ApiModelProperty(value = "全年耗油量(升) ")
    @TableField("Qnhyl")
    private Float qnhyl;

    @ApiModelProperty(value = "全年开泵时数(小时) ")
    @TableField("Qnkbss")
    private Float qnkbss;

    @ApiModelProperty(value = "单位耗电量的取水量(立方米/kW·h) ")
    @TableField("Dwhdlqsl")
    private Float dwhdlqsl;

    @ApiModelProperty(value = "水泵单位时间出水量(立方米/小时) ")
    @TableField("Sbdwsjcsl")
    private Float sbdwsjcsl;

    @ApiModelProperty(value = "实测法取水量(万立方米) ")
    @TableField("Scfqsl")
    private Float scfqsl;

    @ApiModelProperty(value = "耗电量法取水量(万立方米) ")
    @TableField("Hdlfqsl")
    private Float hdlfqsl;

    @ApiModelProperty(value = "出水量法取水量(万立方米) ")
    @TableField("Outslfqsl")
    private Float outslfqsl;

    @ApiModelProperty(value = "是否已办理取水许可证 ")
    @TableField("Isblqsz")
    private String isblqsz;

    @ApiModelProperty(value = "取水许可证编号 ")
    @TableField("Qsxkzbh")
    private String qsxkzbh;

    @ApiModelProperty(value = "年许可取水量(万立方米) ")
    @TableField("Yearxkqsl")
    private Float yearxkqsl;

    @ApiModelProperty(value = "是否已办理采矿许可证 ")
    @TableField("isblckxkz")
    private String isblckxkz;

    @ApiModelProperty(value = "采矿许可证证号 ")
    @TableField("Ckxkzbh")
    private String ckxkzbh;

    @ApiModelProperty(value = "管理单位名称 ")
    @TableField("GldwName")
    private String gldwname;

    @ApiModelProperty(value = "管理单位隶属关系 ")
    @TableField("GldwLsgx")
    private String gldwLsgx;

    @ApiModelProperty(value = "填表人员")
    @TableField("WritePeople")
    private String writepeople;

    @ApiModelProperty(value = "WritePeoplePhone填表联系人电话")
    @TableField("WpPhone")
    private String wpphone;

    @ApiModelProperty(value = "复核人员")
    @TableField("ReviewPeople")
    private String reviewpeople;

    @ApiModelProperty(value = "复核人联系电话")
    @TableField("RpPhone")
    private String rpphone;

    @ApiModelProperty(value = "审查人员")
    @TableField("CheckPeople")
    private String checkpeople;

    @ApiModelProperty(value = "审核标志")
    @TableField("CheckState")
    private Integer CheckState;

    @ApiModelProperty(value = "地区审核")
    @TableField("CityCheck")
    private Integer citycheck;

    @ApiModelProperty(value = "省级审核")
    @TableField("ProvCheck")
    private Integer provcheck;

    @ApiModelProperty(value = "中央审核")
    @TableField("CentreCheck")
    private Integer centrecheck;

    @ApiModelProperty(value = "政区编码")
    @TableField("QxAddvcd")
    private String qxaddvcd;

    @TableField("WQSYT")
    private String wqsyt;

    @ApiModelProperty(value = "所在水源地名称")
    private String sourceLocationName;

    @ApiModelProperty(value = "所在水源地编码")
    private String sourceLocationNo;

    @ApiModelProperty(value = "水温")
    private String watert;

    @ApiModelProperty(value = "单位耗油量的取水量(立方米/升)")
    private Float dwhylqsl;

    @ApiModelProperty(value = "耗油量法取水量(万m³)")
    private Float hylfqsl;

    @ApiModelProperty(value = "矿区面积(km²)")
    private Float kqarea;

    @ApiModelProperty(value = "生产规模(万m³/a)")
    private Float scgm;

    @ApiModelProperty(value = "创建人")
    private String createpeople;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdate;

    @ApiModelProperty(value = "修改人")
    private String modifypeople;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime modifydate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getXzqprovince() {
        return xzqprovince;
    }

    public void setXzqprovince(String xzqprovince) {
        this.xzqprovince = xzqprovince;
    }

    public String getXzqcity2() {
        return xzqcity2;
    }

    public void setXzqcity2(String xzqcity2) {
        this.xzqcity2 = xzqcity2;
    }

    public String getXzqcountytown() {
        return xzqcountytown;
    }

    public void setXzqcountytown(String xzqcountytown) {
        this.xzqcountytown = xzqcountytown;
    }

    public String getXzqcountry() {
        return xzqcountry;
    }

    public void setXzqcountry(String xzqcountry) {
        this.xzqcountry = xzqcountry;
    }

    public String getXzqvillage() {
        return xzqvillage;
    }

    public void setXzqvillage(String xzqvillage) {
        this.xzqvillage = xzqvillage;
    }

    public String getAddvcd() {
        return addvcd;
    }

    public void setAddvcd(String addvcd) {
        this.addvcd = addvcd;
    }

    public String getJdjname() {
        return jdjname;
    }

    public void setJdjname(String jdjname) {
        this.jdjname = jdjname;
    }

    public String getJdjno() {
        return jdjno;
    }

    public void setJdjno(String jdjno) {
        this.jdjno = jdjno;
    }

    public Float getLgtdm() {
        return lgtdm;
    }

    public void setLgtdm(Float lgtdm) {
        this.lgtdm = lgtdm;
    }

    public Float getLgtds() {
        return lgtds;
    }

    public void setLgtds(Float lgtds) {
        this.lgtds = lgtds;
    }

    public Float getLgtdd() {
        return lgtdd;
    }

    public void setLgtdd(Float lgtdd) {
        this.lgtdd = lgtdd;
    }

    public Float getLttdm() {
        return lttdm;
    }

    public void setLttdm(Float lttdm) {
        this.lttdm = lttdm;
    }

    public Float getLttds() {
        return lttds;
    }

    public void setLttds(Float lttds) {
        this.lttds = lttds;
    }

    public Float getLttdd() {
        return lttdd;
    }

    public void setLttdd(Float lttdd) {
        this.lttdd = lttdd;
    }

    public String getCreateyear() {
        return createyear;
    }

    public void setCreateyear(String createyear) {
        this.createyear = createyear;
    }

    public Float getJingdeep() {
        return jingdeep;
    }

    public void setJingdeep(Float jingdeep) {
        this.jingdeep = jingdeep;
    }

    public Float getDxsMs() {
        return dxsMs;
    }

    public void setDxsMs(Float dxsMs) {
        this.dxsMs = dxsMs;
    }

    public String getIsoneselfwell() {
        return isoneselfwell;
    }

    public void setIsoneselfwell(String isoneselfwell) {
        this.isoneselfwell = isoneselfwell;
    }

    public Float getInnerdiameter() {
        return innerdiameter;
    }

    public void setInnerdiameter(Float innerdiameter) {
        this.innerdiameter = innerdiameter;
    }

    public String getJbgcl() {
        return jbgcl;
    }

    public void setJbgcl(String jbgcl) {
        this.jbgcl = jbgcl;
    }

    public String getYyzk() {
        return yyzk;
    }

    public void setYyzk(String yyzk) {
        this.yyzk = yyzk;
    }

    public String getIsptjdsb() {
        return isptjdsb;
    }

    public void setIsptjdsb(String isptjdsb) {
        this.isptjdsb = isptjdsb;
    }

    public String getSbtype() {
        return sbtype;
    }

    public void setSbtype(String sbtype) {
        this.sbtype = sbtype;
    }

    public Float getSbdeyc() {
        return Sbdeyc;
    }

    public void setSbdeyc(Float sbdeyc) {
        Sbdeyc = sbdeyc;
    }

    public Float getSbdeq() {
        return sbdeq;
    }

    public void setSbdeq(Float sbdeq) {
        this.sbdeq = sbdeq;
    }

    public Float getSbdew() {
        return sbdew;
    }

    public void setSbdew(Float sbdew) {
        this.sbdew = sbdew;
    }

    public String getIsazsljlss() {
        return isazsljlss;
    }

    public void setIsazsljlss(String isazsljlss) {
        this.isazsljlss = isazsljlss;
    }

    public String getSljlsstype() {
        return sljlsstype;
    }

    public void setSljlsstype(String sljlsstype) {
        this.sljlsstype = sljlsstype;
    }

    public String getIsgmys() {
        return isgmys;
    }

    public void setIsgmys(String isgmys) {
        this.isgmys = isgmys;
    }

    public String getLandtype() {
        return landtype;
    }

    public void setLandtype(String landtype) {
        this.landtype = landtype;
    }

    public String getDxstype() {
        return dxstype;
    }

    public void setDxstype(String dxstype) {
        this.dxstype = dxstype;
    }

    public String getSjsourcetype() {
        return sjsourcetype;
    }

    public void setSjsourcetype(String sjsourcetype) {
        this.sjsourcetype = sjsourcetype;
    }

    public String getWatersourcetype() {
        return watersourcetype;
    }

    public void setWatersourcetype(String watersourcetype) {
        this.watersourcetype = watersourcetype;
    }

    public String getZyqsytjxy() {
        return zyqsytjxy;
    }

    public void setZyqsytjxy(String zyqsytjxy) {
        this.zyqsytjxy = zyqsytjxy;
    }

    public Integer getXcsjgsrk() {
        return xcsjgsrk;
    }

    public void setXcsjgsrk(Integer xcsjgsrk) {
        this.xcsjgsrk = xcsjgsrk;
    }

    public String getSzgqtype() {
        return szgqtype;
    }

    public void setSzgqtype(String szgqtype) {
        this.szgqtype = szgqtype;
    }

    public Float getKzggmj() {
        return kzggmj;
    }

    public void setKzggmj(Float kzggmj) {
        this.kzggmj = kzggmj;
    }

    public Float getSjggmj() {
        return sjggmj;
    }

    public void setSjggmj(Float sjggmj) {
        this.sjggmj = sjggmj;
    }

    public String getZyqsyt() {
        return zyqsyt;
    }

    public void setZyqsyt(String zyqsyt) {
        this.zyqsyt = zyqsyt;
    }

    public String getQslqdff() {
        return qslqdff;
    }

    public void setQslqdff(String qslqdff) {
        this.qslqdff = qslqdff;
    }

    public Float getQnhdl() {
        return qnhdl;
    }

    public void setQnhdl(Float qnhdl) {
        this.qnhdl = qnhdl;
    }

    public Float getQnhyl() {
        return qnhyl;
    }

    public void setQnhyl(Float qnhyl) {
        this.qnhyl = qnhyl;
    }

    public Float getQnkbss() {
        return qnkbss;
    }

    public void setQnkbss(Float qnkbss) {
        this.qnkbss = qnkbss;
    }

    public Float getDwhdlqsl() {
        return dwhdlqsl;
    }

    public void setDwhdlqsl(Float dwhdlqsl) {
        this.dwhdlqsl = dwhdlqsl;
    }

    public Float getSbdwsjcsl() {
        return sbdwsjcsl;
    }

    public void setSbdwsjcsl(Float sbdwsjcsl) {
        this.sbdwsjcsl = sbdwsjcsl;
    }

    public Float getScfqsl() {
        return scfqsl;
    }

    public void setScfqsl(Float scfqsl) {
        this.scfqsl = scfqsl;
    }

    public Float getHdlfqsl() {
        return hdlfqsl;
    }

    public void setHdlfqsl(Float hdlfqsl) {
        this.hdlfqsl = hdlfqsl;
    }

    public Float getOutslfqsl() {
        return outslfqsl;
    }

    public void setOutslfqsl(Float outslfqsl) {
        this.outslfqsl = outslfqsl;
    }

    public String getIsblqsz() {
        return isblqsz;
    }

    public void setIsblqsz(String isblqsz) {
        this.isblqsz = isblqsz;
    }

    public String getQsxkzbh() {
        return qsxkzbh;
    }

    public void setQsxkzbh(String qsxkzbh) {
        this.qsxkzbh = qsxkzbh;
    }

    public Float getYearxkqsl() {
        return yearxkqsl;
    }

    public void setYearxkqsl(Float yearxkqsl) {
        this.yearxkqsl = yearxkqsl;
    }

    public String getIsblckxkz() {
        return isblckxkz;
    }

    public void setIsblckxkz(String isblckxkz) {
        this.isblckxkz = isblckxkz;
    }

    public String getCkxkzbh() {
        return ckxkzbh;
    }

    public void setCkxkzbh(String ckxkzbh) {
        this.ckxkzbh = ckxkzbh;
    }

    public String getGldwname() {
        return gldwname;
    }

    public void setGldwname(String gldwname) {
        this.gldwname = gldwname;
    }

    public String getGldwLsgx() {
        return gldwLsgx;
    }

    public void setGldwLsgx(String gldwLsgx) {
        this.gldwLsgx = gldwLsgx;
    }

    public String getWritepeople() {
        return writepeople;
    }

    public void setWritepeople(String writepeople) {
        this.writepeople = writepeople;
    }

    public String getWpphone() {
        return wpphone;
    }

    public void setWpphone(String wpphone) {
        this.wpphone = wpphone;
    }

    public String getReviewpeople() {
        return reviewpeople;
    }

    public void setReviewpeople(String reviewpeople) {
        this.reviewpeople = reviewpeople;
    }

    public String getRpphone() {
        return rpphone;
    }

    public void setRpphone(String rpphone) {
        this.rpphone = rpphone;
    }

    public String getCheckpeople() {
        return checkpeople;
    }

    public void setCheckpeople(String checkpeople) {
        this.checkpeople = checkpeople;
    }

    public Integer getCheckState() {
        return CheckState;
    }

    public void setCheckState(Integer checkState) {
        CheckState = checkState;
    }

    public Integer getCitycheck() {
        return citycheck;
    }

    public void setCitycheck(Integer citycheck) {
        this.citycheck = citycheck;
    }

    public Integer getProvcheck() {
        return provcheck;
    }

    public void setProvcheck(Integer provcheck) {
        this.provcheck = provcheck;
    }

    public Integer getCentrecheck() {
        return centrecheck;
    }

    public void setCentrecheck(Integer centrecheck) {
        this.centrecheck = centrecheck;
    }

    public String getQxaddvcd() {
        return qxaddvcd;
    }

    public void setQxaddvcd(String qxaddvcd) {
        this.qxaddvcd = qxaddvcd;
    }

    public String getWqsyt() {
        return wqsyt;
    }

    public void setWqsyt(String wqsyt) {
        this.wqsyt = wqsyt;
    }

    public String getSourceLocationName() {
        return sourceLocationName;
    }

    public void setSourceLocationName(String sourceLocationName) {
        this.sourceLocationName = sourceLocationName;
    }

    public String getSourceLocationNo() {
        return sourceLocationNo;
    }

    public void setSourceLocationNo(String sourceLocationNo) {
        this.sourceLocationNo = sourceLocationNo;
    }

    public String getWatert() {
        return watert;
    }

    public void setWatert(String watert) {
        this.watert = watert;
    }

    public Float getDwhylqsl() {
        return dwhylqsl;
    }

    public void setDwhylqsl(Float dwhylqsl) {
        this.dwhylqsl = dwhylqsl;
    }

    public Float getHylfqsl() {
        return hylfqsl;
    }

    public void setHylfqsl(Float hylfqsl) {
        this.hylfqsl = hylfqsl;
    }

    public Float getKqarea() {
        return kqarea;
    }

    public void setKqarea(Float kqarea) {
        this.kqarea = kqarea;
    }

    public Float getScgm() {
        return scgm;
    }

    public void setScgm(Float scgm) {
        this.scgm = scgm;
    }

    public String getCreatepeople() {
        return createpeople;
    }

    public void setCreatepeople(String createpeople) {
        this.createpeople = createpeople;
    }

    public LocalDateTime getCreatedate() {
        return createdate;
    }

    public void setCreatedate(LocalDateTime createdate) {
        this.createdate = createdate;
    }

    public String getModifypeople() {
        return modifypeople;
    }

    public void setModifypeople(String modifypeople) {
        this.modifypeople = modifypeople;
    }

    public LocalDateTime getModifydate() {
        return modifydate;
    }

    public void setModifydate(LocalDateTime modifydate) {
        this.modifydate = modifydate;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
