package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 实况雨情信息
 *
 * <AUTHOR>
 */
@ApiModel(value = "雨情数据")
public class LiveRain implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "测站类型")
    @TableField(value = "STTP")
    private String sttp;

    @ApiModelProperty(value = "测站地址")
    @TableField(value = "STLC")
    private String stlc;

    @ApiModelProperty(value = "降雨量")
    @TableField(value = "ACCP")
    private BigDecimal accp;

    @ApiModelProperty(value = "未来3小时降雨")
    @TableField(value = "RAIN3")
    private BigDecimal rain3;

    @ApiModelProperty(value = "未来24小时降雨")
    @TableField(value = "RAIN24")
    private BigDecimal rain24;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public BigDecimal getAccp() {
        return accp;
    }

    public void setAccp(BigDecimal accp) {
        this.accp = accp;
    }

    public BigDecimal getRain3() {
        return rain3;
    }

    public void setRain3(BigDecimal rain3) {
        this.rain3 = rain3;
    }

    public BigDecimal getRain24() {
        return rain24;
    }

    public void setRain24(BigDecimal rain24) {
        this.rain24 = rain24;
    }
}
