package com.huitu.cloud.api.screen.fhjz.controler;


import com.huitu.api.SuccessResponse;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.screen.fhjz.entity.DikeDetailVo;
import com.huitu.cloud.api.screen.fhjz.entity.DikeVo;
import com.huitu.cloud.api.screen.fhjz.service.DikeScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 堤防 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-16
 */

@RestController
@Api(tags = "一体机大屏（防洪减灾-堤防工程） ")
@RequestMapping("/api/screen/dike")
public class DikeScreenResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "B661C064-1554-4164-80CA-B52A17E42148";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private DikeScreenService baseService;

    @ApiOperation(value = "堤防信息列表查询",notes="堤防信息列表查询")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "dikeGrad", value = "堤防等级", required = true, dataType = "String"),
        @ApiImplicitParam(name = "dikeName", value = "堤防名称", required = false, dataType = "string")
    })
    @GetMapping(value = "dike-list")
    public ResponseEntity<SuccessResponse<List>> dikeList(@RequestParam("dikeGrad") String dikeGrad,@RequestParam("dikeName") String dikeName){
        List<DikeVo> result= baseService.queryDikeList(dikeGrad,dikeName);
            return ResponseEntity.ok(
                new SuccessResponse(result));
     }
    @ApiOperation(value = "堤防防洪详情信息列表查询",notes="堤防防洪详情信息列表查询")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "dikeGrad", value = "堤防等级", required = true, dataType = "string"),
        @ApiImplicitParam(name = "dikeName", value = "堤防名称", required = true, dataType = "string")
    })
    @GetMapping(value = "dike-detail-list")
    public ResponseEntity<SuccessResponse<List>> dikeDetailList(@RequestParam("dikeGrad") String dikeGrad,@RequestParam("dikeName") String dikeName){
        List<DikeDetailVo> result= baseService.dikeDetailList(dikeGrad,dikeName);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "查询堤防信息",notes="查询堤防信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dikeCode", value = "堤防编码", required = true, dataType = "String"),
    })
    @GetMapping(value = "queryDikeInfo")
    public ResponseEntity<SuccessResponse<Map<String,Object>>> queryDikeInfo(@RequestParam("dikeCode") String dikeCode){
        Map<String,Object> result= baseService.queryDikeInfo(dikeCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "根据政区统计堤防数量详情",notes="根据政区统计堤防数量详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
    })
    @GetMapping(value = "statisticDikeDetail")
    public ResponseEntity<SuccessResponse<String>> statisticDikeDetail(@RequestParam("adcd") String adcd){
        String result= baseService.statisticDikeDetail(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }


}







