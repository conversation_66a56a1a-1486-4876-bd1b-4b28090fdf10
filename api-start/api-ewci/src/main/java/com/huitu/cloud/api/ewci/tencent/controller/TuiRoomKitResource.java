package com.huitu.cloud.api.ewci.tencent.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.api.ApiResource;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.tencent.api.WebSocketService;
import com.huitu.cloud.api.ewci.tencent.entity.*;
import com.huitu.cloud.api.ewci.tencent.service.TuiRoomKitService;
import com.huitu.cloud.api.ewci.tencent.service.VideoConferenceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 腾讯音视频服务
 *
 * <AUTHOR>
 */
@Api(tags = "腾讯音视频服务")
@RestController
@RequestMapping("/api/tuiRoomKit")
public class TuiRoomKitResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "00000000-0000-0000-0000-000000000000";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private TuiRoomKitService baseService;

    private VideoConferenceService videoConferenceService;


    public TuiRoomKitResource(TuiRoomKitService tuiRoomKitService, VideoConferenceService videoConferenceService) {
        this.baseService = tuiRoomKitService;
        this.videoConferenceService = videoConferenceService;
    }


    /**
     * 发送 WebSocket 消息, 用于 Web 端弹窗推送
     *
     * @param vo Web端Socket消息封装实体
     * @return ResponseEntity<SuccessResponse < Boolean>>
     */
    @ApiOperation("发送WebSocket消息")
    @PostMapping(value = "sendSocketMsg")
    public ResponseEntity<SuccessResponse<Boolean>> sendSocketMsg(@RequestBody TuiRoomKitVO vo) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.sendSocketMsg(vo)));
    }

    /**
     * 发送手机壳顶栏推送消息，用于移动端顶栏推送
     *
     * @param vo 移动端顶栏推送封装实体
     * @return ResponseEntity<SuccessResponse < Boolean>>
     */
    @ApiOperation("发送移动端顶栏推送")
    @PostMapping(value = "mobPush")
    public ResponseEntity<SuccessResponse<Boolean>> mobPush(@RequestBody MobPushVO vo) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.mobPush(vo)));
    }

    @ApiOperation(value = "创建会议", notes = "作者：张宝兴")
    @PostMapping(value = "createConference")
    public ResponseEntity<SuccessResponse<Boolean>> createConference(@RequestBody VideoConference videoConference) throws Exception {
        Boolean flag = videoConferenceService.createConference(videoConference);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "查询会议中的列表", notes = "作者：张宝兴")
    @PostMapping(value = "selectMeeting-by-page")
    public ResponseEntity<SuccessResponse<Page<VideoConferenceVo>>> getMeetingList(@RequestBody PageParams params) {
        IPage<VideoConferenceVo> result = videoConferenceService.getMeetingList(params);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "查看会议详情", notes = "作者：张宝兴")
    @GetMapping("select-meeting-detail/{roomId}")
    public VideoConference getMeetingDetail(@PathVariable("roomId") String roomId) {
        return videoConferenceService.getMeetingDetail(roomId);
    }


}
