package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@ApiModel(value="历史雨情列表信息")
public class HisRain extends Rain{
    @ApiModelProperty(value = "降雨量")
    private String drp;
    @ApiModelProperty(value = "时间")
    private String tm;
    @ApiModelProperty(value = "天气状况（小时的时候有值）")
    private String wth;

    public String getWth() {
        return wth;
    }

    public void setWth(String wth) {
        this.wth = wth;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getDrp() {
        return drp;
    }

    public void setDrp(String drp) {
        this.drp = drp;
    }

}
