package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 异常站数统计条件
 *
 * <AUTHOR>
 */
@ApiModel(value = "异常站数统计条件")
public class AnmCondition implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "开始时间", example = "2021-10-25 08:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date bgtm;

    @ApiModelProperty(value = "结束时间", example = "2021-10-26 08:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endtm;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Date getBgtm() {
        return bgtm;
    }

    public void setBgtm(Date bgtm) {
        this.bgtm = bgtm;
    }

    public Date getEndtm() {
        return endtm;
    }

    public void setEndtm(Date endtm) {
        this.endtm = endtm;
    }
}
