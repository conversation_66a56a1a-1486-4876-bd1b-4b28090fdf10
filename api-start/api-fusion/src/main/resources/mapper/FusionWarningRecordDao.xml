<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.fusion.mapper.FusionWarningRecordDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarningRecordResp" useCache="false">
        SELECT
            r.WARN_ID,
            r.ADCD,
            a.ADNM,
            10 AS WARN_TYPE_ID,
            '山洪' AS WARN_TYPE_NAME,
            r.WARN_GRADE_ID,
            g.GRADE_NAME AS WARN_GRADE_NAME,
            g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME,
            r.WARN_STATUS_ID,
            s.STATUS_NAME AS WARN_STATUS_NAME,
            s.<PERSON>ORT_NAME AS WARN_STATUS_ALIAS_NAME,
            s.SHORT_NAME AS WARN_STATUS_SHORT_NAME,
            r.WARN_MODEL,
            r.WARN_TIME,
            f.CREATE_TIME AS WARN_CLOSE_TIME,
            r.WARN_MODE,
            r.WARN_NAME,
            r.WARN_DESC,
            fss.STDT AS WARN_STDT,
            a.LGTD,
            a.LTTD,
            r.REMARK,
            r.PLATFORM_ID,
            fwc.ISSUING_UNIT PLATFORM_NAME
            FROM
            EW_FUSION_WARNING_RECORD r
            LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID
            LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID
            LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30
            LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID
            LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD
            LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID
            WHERE
            1 = 1
            <if test="map.warnGradeList != null and map.warnGradeList.size() > 0">
                <foreach collection="map.warnGradeList" item="warnGrade" separator=", " open="AND r.WARN_GRADE_ID IN ("
                         close=")">
                    #{warnGrade}
                </foreach>
            </if>
            AND LEFT(r.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">AND LEFT(r.ADCD, 6) NOT IN ('220581')</if>
            AND r.WARN_TIME >= #{map.stm}
            <if test="map.etm != null">AND r.WARN_TIME &lt;= #{map.etm}</if>
            <if test="map.warnName != null and map.warnName.trim() != ''">
                AND CHARINDEX(#{map.warnName}, r.WARN_NAME) > 0
            </if>
        ORDER BY WARN_TIME DESC, LEFT(r.ADCD, 12) ASC, WARN_NAME DESC
    </select>
    <select id="getAscriptionTypeCount"
            resultType="com.huitu.cloud.api.fusion.entity.response.AscriptionTypeCountResp">
        SELECT
            COUNT(DISTINCT r.ADCD) AS villageCount,
            COUNT(1)  AS totalWarningCount,
            SUM(CASE WHEN r.WARN_GRADE_ID = 1 THEN 1 ELSE 0 END) AS level1Count,
            SUM(CASE WHEN r.WARN_GRADE_ID = 2 THEN 1 ELSE 0 END) AS level2Count
        FROM
            EW_FUSION_WARNING_RECORD r
        WHERE
            LEFT(r.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">AND LEFT(r.ADCD, 6) NOT IN ('220581')</if>
            AND r.WARN_TIME >= #{map.stm}
            <if test="map.etm != null">AND r.WARN_TIME &lt;= #{map.etm}</if>
            <if test="map.warnName != null and map.warnName.trim() != ''">
                AND CHARINDEX(#{map.warnName}, r.WARN_NAME) > 0
            </if>
    </select>

    <select id="getSnapshotMonitor"
            resultType="com.huitu.cloud.api.fusion.entity.response.FusionSnapshotMonitorResp">
        SELECT
            A.WARN_ID,
            A.ADCD,
            C.ADNM,
            DATEADD(HOUR, - B.STDT, A.WARN_TIME) AS STM,
            A.WARN_TIME AS ETM,
            B.STDT,
            B.STCD,
            D.STNM
        FROM
            EW_FUSION_WARNING_RECORD A
                LEFT JOIN EW_FUSION_SNAPSHOT_S B ON A.WARN_ID = B.WARN_ID
                LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
                LEFT JOIN ST_STBPRP_B D ON D.STCD = B.STCD
        WHERE A.WARN_ID = #{warnId}
    </select>

    <select id="getSnapshotIndex"
            resultType="com.huitu.cloud.api.fusion.entity.response.FusionSnapshotIndexResp">
        SELECT
            r.WARN_ID AS warnId,
            r.WARN_NAME AS warnName,
            a.ADNM AS warnAdnm,
            r.WARN_MODEL AS warnModel,
            r.WARN_TIME AS warnTime,
            f.CREATE_TIME AS closeTime,
            r.WARN_MODE AS warnMode,
            r.WARN_DESC AS warnDesc,
            a.ADNM AS adnm,
            s.REPRD AS reprd,
            s.RDRPT AS rdrpt,
            s.STDT as stdt,
            s.FDRPT AS fdrpt,
            s.FCDT AS fcdt,
            st.STNM AS stnm,
            s.STCD AS stcd,
            s.RACCP AS raccp,
            s.FACCP AS faccp
        FROM
            EW_FUSION_WARNING_RECORD r
                LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD
                LEFT JOIN EW_FUSION_WARNING_FLOW f ON r.WARN_ID = f.WARN_ID AND f.WARN_STATUS_ID = 30
                LEFT JOIN EW_FUSION_SNAPSHOT_S s ON r.WARN_ID = s.WARN_ID
                LEFT JOIN ST_STBPRP_B st ON s.STCD = st.STCD
        WHERE
            r.WARN_ID = #{warnId}
    </select>
    <select id="getSendingPageListBackup"
            resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarningSendingResp">
        SELECT
            COALESCE(s1.phoneNo, s2.phoneNo) AS phoneNo,
            s1.receiverName,
            s1.smsStatus,
            s1.smsSendTime,
            s2.voiceStatus,
            s2.voiceSendTime,
            s3.receiverType,
            s3.position
        FROM
            (
                SELECT
                    A.PHONE_NO AS phoneNo,
                    A.RECEIVER AS receiverName,
                    A.STATUS AS smsStatus,
                    A.SEND_TIME AS smsSendTime
                FROM
                    SMS_SENDING A
                WHERE
                    MSG_ID in (
                        SELECT
                            sms_msg.MSG_ID
                        FROM
                            SMS_MESSAGE sms_msg
                        WHERE
                            BUSINESS_KEY in (
                                SELECT
                                    rel.MSG_ID
                                FROM
                                    EW_FUSION_WARNING_RECORD r
                                        INNER JOIN EW_FUSION_REL_MESSAGE rel ON r.WARN_ID = rel.WARN_ID
                                        INNER JOIN EW_FUSION_MESSAGE msg ON rel.MSG_ID = msg.MSG_ID
                                WHERE
                                    r.WARN_ID = #{map.warnId}
                                  AND msg.PUSH_MODE = '1'))) s1
                FULL OUTER JOIN (
                SELECT
                    B.CALLED_PHONE AS phoneNo,
                    B.CALLED_NAME AS receiverName,
                    B.STATUS AS voiceStatus,
                    B.SEND_TIME AS voiceSendTime
                FROM
                    XCC_SENDING B
                WHERE
                    MSG_ID in (
                        SELECT
                            xcc_msg.MSG_ID
                        FROM
                            XCC_MESSAGE xcc_msg
                        WHERE
                            EXT_MSG_ID in (
                                SELECT
                                    rel.MSG_ID
                                FROM
                                    EW_FUSION_WARNING_RECORD r
                                        INNER JOIN EW_FUSION_REL_MESSAGE rel ON r.WARN_ID = rel.WARN_ID
                                        INNER JOIN EW_FUSION_MESSAGE msg ON rel.MSG_ID = msg.MSG_ID
                                WHERE
                                    r.WARN_ID = #{map.warnId}
                                  AND msg.PUSH_MODE = '2'))) s2 ON s1.phoneNo = s2.phoneNo
                FULL OUTER JOIN (
                SELECT
                    rec.[POSITION] AS POSITION,
                    rec.TAG AS receiverType,
                    rec.PHONE_NO AS phoneNo
                FROM
                    EW_FUSION_WARNING_RECORD r
                        INNER JOIN EW_FUSION_REL_MESSAGE rel ON r.WARN_ID = rel.WARN_ID
                        INNER JOIN EW_FUSION_MESSAGE msg ON rel.MSG_ID = msg.MSG_ID
                        LEFT JOIN EW_FUSION_RECEIVER rec ON rel.MSG_ID = rec.MSG_ID
                WHERE
                    r.WARN_ID = #{map.warnId}
                  AND msg.PUSH_MODE = '1') s3 ON s1.phoneNo = s3.phoneNo
        ORDER BY
            smsStatus ASC,
            voiceStatus ASC,
            smsSendTime DESC,
            voiceSendTime DESC
    </select>

    <select id="getSendingPageList"
            resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarningSendingResp">
        SELECT
        A.CALL_ID,
        A.PHONE_NO phoneNo,
        REC.RECEIVER AS receiverName,
        SS.[STATUS] AS smsStatus,
        SS.SEND_TIME AS smsSendTime,
        SS.[ERROR_MESSAGE] SMS_FAIL_CAUSE,
        XS.[STATUS] AS voiceStatus,
        ISNULL(XS.CALL_START_TIME, XS.SEND_TIME) AS voiceSendTime,
        XS.FAIL_CAUSE XCC_FAIL_CAUSE,
        XS.CALL_RESULT XCC_CALL_RESULT,
        XS.CALL_RESULT_TEXT XCC_CALL_RESULT_TEXT,
        XS.EXT_SEND_ID XCC_EXT_SEND_ID,
        REC.TAG AS receiverType,
        REC.[POSITION] AS [position],
        A.[STATUS] FEEDBACK_STATUS,
        A.CONFIRM_RESULT,
        A.CONFIRM_TIME
        FROM
        EW_CALL_FEEDBACK A
        LEFT JOIN EW_FUSION_REL_MESSAGE B ON B.MSG_ID = SMS_WARN_MSG_ID
        LEFT JOIN SMS_SENDING SS ON EXISTS (SELECT 8 FROM SMS_MESSAGE WHERE BUSINESS_KEY = SMS_WARN_MSG_ID AND MSG_ID = SS.MSG_ID)
        AND SS.PHONE_NO = A.PHONE_NO
        LEFT JOIN XCC_SENDING XS ON EXISTS (SELECT 8 FROM XCC_MESSAGE WHERE EXT_MSG_ID = XCC_WARN_MSG_ID AND MSG_ID = XS.MSG_ID)
        AND XS.CALLED_PHONE = A.PHONE_NO
        LEFT JOIN EW_FUSION_RECEIVER REC ON B.MSG_ID = REC.MSG_ID  AND REC.PHONE_NO = A.PHONE_NO
        WHERE
        A.CALL_TYPE = '9'
        AND B.WARN_ID = #{map.warnId}
        <if test="map.smsSendStatusList != null and map.smsSendStatusList != ''">
            AND CHARINDEX(SS.[STATUS], #{map.smsSendStatusList}) > 0
        </if>
        <if test="map.feedbackStatusList != null and map.feedbackStatusList != ''">
            AND CHARINDEX(A.[STATUS], #{map.feedbackStatusList}) > 0
        </if>
        ORDER BY
        A.CREATE_TIME DESC,
        A.PHONE_NO ASC
    </select>
    <select id="getDetailPageList"
            resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarningRecordResp">
        SELECT * FROM
        (
        SELECT A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME
        WARN_GRADE_NAME,
        D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME
        WARN_STATUS_ALIAS_NAME,
        E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE,
        WARN_NAME, WARN_DESC,
        WARN_STDT, A.LGTD, A.LTTD, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME
        FROM EW_WARNING_RECORD A
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
        LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID
        LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID
        LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID
        LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID
        = 30
        LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID
        WHERE A.WARN_TYPE_ID = 10
        <if test="map.warnGradeList != null and map.warnGradeList.size() > 0">
            <foreach collection="map.warnGradeList" item="warnGrade" separator=", " open="AND A.WARN_GRADE_ID IN ("
                     close=")">
                #{warnGrade}
            </foreach>
        </if>
        <if test="map.warnStatusList != null and map.warnStatusList.size() > 0">
            <foreach collection="map.warnStatusList" item="warnStatus" separator=", " open="AND A.WARN_STATUS_ID IN ("
                     close=")">
                #{warnStatus}
            </foreach>
        </if>
        <if test="map.warnModeList != null and map.warnModeList.size() > 0">
            <foreach collection="map.warnModeList" item="warnMode" separator=", " open="AND A.WARN_MODE IN (" close=")">
                #{warnMode}
            </foreach>
        </if>
        AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">AND LEFT(A.ADCD, 6) NOT IN ('220581')</if>
        <if test="map.bscd != null and map.bscd != ''">
            AND EXISTS(SELECT 8 FROM BSN_BAS_AD WHERE ADCD = A.ADCD AND BAS_CODE = #{map.bscd})
        </if>
        AND WARN_TIME >= #{map.stm}
        <if test="map.etm != null">AND WARN_TIME &lt;= #{map.etm}</if>
        <if test="map.warnName != null and map.warnName.trim() != ''">AND CHARINDEX(#{map.warnName}, WARN_NAME) > 0</if>
        UNION ALL
        SELECT
        r.WARN_ID,
        r.ADCD,
        a.ADNM,
        10 AS WARN_TYPE_ID,
        '山洪' AS WARN_TYPE_NAME,
        r.WARN_GRADE_ID,
        g.GRADE_NAME AS WARN_GRADE_NAME,
        g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME,
        r.WARN_STATUS_ID,
        s.STATUS_NAME AS WARN_STATUS_NAME,
        s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME,
        s.SHORT_NAME AS WARN_STATUS_SHORT_NAME,
        r.WARN_MODEL,
        r.WARN_TIME,
        f.CREATE_TIME AS WARN_CLOSE_TIME,
        r.WARN_MODE,
        r.WARN_NAME,
        r.WARN_DESC,
        fss.STDT AS WARN_STDT,
        a.LGTD,
        a.LTTD,
        r.REMARK,
        r.PLATFORM_ID,
        fwc.ISSUING_UNIT PLATFORM_NAME
        FROM
        EW_FUSION_WARNING_RECORD r
        LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID
        LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID
        LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30
        LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID
        LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD
        LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID
        WHERE
        1 = 1
        <if test="map.warnGradeList != null and map.warnGradeList.size() > 0">
            <foreach collection="map.warnGradeList" item="warnGrade" separator=", " open="AND r.WARN_GRADE_ID IN ("
                     close=")">
                #{warnGrade}
            </foreach>
        </if>
        AND LEFT(r.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">AND LEFT(r.ADCD, 6) NOT IN ('220581')</if>
        AND r.WARN_TIME >= #{map.stm}
        <if test="map.etm != null">AND r.WARN_TIME &lt;= #{map.etm}</if>
        <if test="map.warnName != null and map.warnName.trim() != ''">
            AND CHARINDEX(#{map.warnName}, r.WARN_NAME) > 0
        </if>
        ) T
        ORDER BY T.WARN_TIME DESC, LEFT(T.ADCD, 12) ASC, T.WARN_NAME DESC
    </select>
    <select id="getWarnLatestPageList" resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarningRecordResp" useCache="false">
        SELECT * FROM (
        SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID,
        WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME,
        WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM (
        SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM,
        WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME,
        A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME,
        A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK,
        COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME
        FROM EW_WARNING_RECORD A
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
        LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID
        LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID
        LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID
        LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30
        LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID
        WHERE A.WARN_TYPE_ID = 10
        <if test="map.warnGradeList != null and map.warnGradeList.size() > 0">
             <foreach collection="map.warnGradeList" item="warnGrade" separator=", " open="AND A.WARN_GRADE_ID IN (" close=")">
                 #{warnGrade}
             </foreach>
        </if>
        <if test="map.warnStatusList != null and map.warnStatusList.size() > 0">
            <foreach collection="map.warnStatusList" item="warnStatus" separator=", " open="AND A.WARN_STATUS_ID IN (" close=")">
                #{warnStatus}
            </foreach>
        </if>
        <if test="map.warnModeList != null and map.warnModeList.size() > 0">
            <foreach collection="map.warnModeList" item="warnMode" separator=", " open="AND A.WARN_MODE IN (" close=")">
                #{warnMode}
            </foreach>
        </if>
        AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">AND LEFT(A.ADCD, 6) NOT IN ('220581')</if>
        <if test="map.bscd != null and map.bscd != ''">
            AND EXISTS(SELECT 8 FROM BSN_BAS_AD WHERE ADCD = A.ADCD AND BAS_CODE = #{map.bscd})
        </if>
        AND WARN_TIME >= #{map.stm}
        <if test="map.etm != null">AND WARN_TIME &lt;= #{map.etm}</if>
        <if test="map.warnName != null and map.warnName.trim() != ''">AND CHARINDEX(#{map.warnName}, WARN_NAME) > 0</if>
        ) T WHERE T.SORTNO = 1
        UNION ALL
        SELECT
        WARN_ID,
        ADCD,
        ADNM,
        WARN_TYPE_ID,
        WARN_TYPE_NAME,
        WARN_GRADE_ID,
        WARN_GRADE_NAME,
        WARN_GRADE_ALIAS_NAME,
        WARN_STATUS_ID,
        WARN_STATUS_NAME,
        WARN_STATUS_ALIAS_NAME,
        WARN_STATUS_SHORT_NAME,
        WARN_MODEL,
        WARN_TIME,
        WARN_CLOSE_TIME,
        WARN_MODE,
        WARN_NAME,
        WARN_DESC,
        WARN_STDT,
        LGTD,
        LTTD,
        REMARK,
        CHILDREN_COUNT,
        PLATFORM_ID,
        PLATFORM_NAME
        FROM
        (
            SELECT
            ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO,
            r.WARN_ID,
            r.ADCD,
            a.ADNM,
            10 AS WARN_TYPE_ID,
            '山洪' AS WARN_TYPE_NAME,
            r.WARN_GRADE_ID,
            g.GRADE_NAME AS WARN_GRADE_NAME,
            g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME,
            r.WARN_STATUS_ID,
            s.STATUS_NAME AS WARN_STATUS_NAME,
            s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME,
            s.SHORT_NAME AS WARN_STATUS_SHORT_NAME,
            r.WARN_MODEL,
            r.WARN_TIME,
            f.CREATE_TIME AS WARN_CLOSE_TIME,
            r.WARN_MODE,
            r.WARN_NAME,
            r.WARN_DESC,
            fss.STDT AS WARN_STDT,
            a.LGTD,
            a.LTTD,
            r.REMARK,
            COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT,
            r.PLATFORM_ID,
            fwc.ISSUING_UNIT PLATFORM_NAME
            FROM
            EW_FUSION_WARNING_RECORD r
            LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID
            LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID
            LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30
            LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID
            LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD
            LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID
            WHERE
            1 = 1
            <if test="map.warnGradeList != null and map.warnGradeList.size() > 0">
                <foreach collection="map.warnGradeList" item="warnGrade" separator=", " open="AND r.WARN_GRADE_ID IN (" close=")">
                    #{warnGrade}
                </foreach>
            </if>
            AND LEFT(r.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">AND LEFT(r.ADCD, 6) NOT IN ('220581')</if>
            AND r.WARN_TIME >= #{map.stm}
            <if test="map.etm != null">AND r.WARN_TIME &lt;= #{map.etm}</if>
            <if test="map.warnName != null and map.warnName.trim() != ''">
                AND CHARINDEX(#{map.warnName}, r.WARN_NAME) > 0
            </if>
            ) T1 WHERE T1.SORTNO = 1
        ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC
    </select>

    <select id="getDigestList" resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarningDigestResponse">
        SELECT
        WARN_ID,
        A.ADCD,
        ADNM,
        WARN_TIME,
        WARN_NAME,
        WARN_DESC,
        WARN_GRADE_ID,
        A.PLATFORM_ID,
        ISSUING_UNIT PLATFORM_NAME
        FROM
        EW_FUSION_WARNING_RECORD A
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
        LEFT JOIN EW_FUSION_WARNING_CONF C ON C.PLATFORM_ID = A.PLATFORM_ID
        WHERE WARN_ID IN (<foreach collection="warnIds" item="warnId" separator=",">#{warnId}</foreach>)
        ORDER BY
        A.ADCD ASC,
        WARN_TIME DESC
    </select>

    <update id="manualConfirm">
        UPDATE EW_FUSION_WARNING_RECORD
        SET WARN_STATUS_ID = 21,
        LATEST_TIME = GETDATE()
        WHERE
        WARN_STATUS_ID &lt;
        21
        AND WARN_ID IN (<foreach collection="warnIds" item="warnId" separator="," >#{ warnId }</foreach>)
    </update>

    <select id="getRecord" resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarningRecordResp">
        SELECT
            A.WARN_ID,
            A.ADCD,
            C.ADNM,
            A.WARN_GRADE_ID,
            E.GRADE_NAME WARN_GRADE_NAME,
            E.ALIAS_NAME WARN_GRADE_ALIAS_NAME,
            A.WARN_STATUS_ID,
            F.STATUS_NAME WARN_STATUS_NAME,
            F.SHORT_NAME WARN_STATUS_SHORT_NAME,
            A.WARN_MODEL,
            A.WARN_TIME,
            G.CREATE_TIME WARN_CLOSE_TIME,
            A.WARN_MODE,
            A.WARN_NAME,
            A.WARN_DESC,
            I.STDT WARN_STDT,
            A.LGTD,
            A.LTTD,
            A.REMARK,
            A.PLATFORM_ID,
            H.ISSUING_UNIT PLATFORM_NAME
        FROM
            EW_FUSION_WARNING_RECORD A
                LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
                LEFT JOIN EW_FUSION_WARNING_GRADE E ON E.GRADE_ID = A.WARN_GRADE_ID
                LEFT JOIN EW_FUSION_WARNING_STATUS F ON F.STATUS_ID = A.WARN_STATUS_ID
                LEFT JOIN EW_FUSION_WARNING_FLOW G ON G.WARN_ID = A.WARN_ID AND G.WARN_STATUS_ID = 30
                LEFT JOIN EW_FUSION_WARNING_CONF H ON H.PLATFORM_ID = A.PLATFORM_ID
                LEFT JOIN EW_FUSION_SNAPSHOT_S I ON I.WARN_ID = A.WARN_ID
        WHERE
            A.WARN_ID = #{ warnId}
    </select>

    <select id="getWarnDetail" resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarningDetailResp">
        SELECT
            A.WARN_ID,
            A.STCD,
            A.REPRD,
            A.STDT,
            A.RDRPT,
            A.RACCP,
            A.FCDT,
            A.FDRPT,
            A.FACCP,
            A.LATEST_TIME,
            B.STNM
        FROM
            EW_FUSION_SNAPSHOT_S A
                LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
        WHERE A.WARN_ID = #{warnId}
        ORDER BY A.LATEST_TIME ASC
    </select>
</mapper>