package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * <AUTHOR>
 * @since 2019-09-16
 */
@ApiModel(value="预警关联信息对象", description="预警关联信息对象")
public class WarnRelatedInfo extends WarnrecordR{

    @ApiModelProperty(value = " 预警状态名称")
    private String warnStatusNm;
    @ApiModelProperty(value = " 预警状态简称")
    private String shortNm;
    @ApiModelProperty(value = " 预警等级名称")
    private String warnGradeNm;
    @ApiModelProperty(value = " 政区名称")
    private String adnm;
    @ApiModelProperty(value = " 区县政区编码")
    private String padcd;
    @ApiModelProperty(value = " 区县名称")
    private String padnm;
    @ApiModelProperty(value = " 发送短信条数")
    private String msgCt;
    @ApiModelProperty(value = " 是否发生山洪灾害(0:否,1:是)")
    private String hasMouTainTorrents;
    @ApiModelProperty(value = " 情况描述")
    private String remarks;


    public String getWarnStatusNm() {
        return warnStatusNm;
    }

    public void setWarnStatusNm(String warnStatusNm) {
        this.warnStatusNm = warnStatusNm;
    }

    public String getShortNm() {
        return shortNm;
    }

    public void setShortNm(String shortNm) {
        this.shortNm = shortNm;
    }

    public String getWarnGradeNm() {
        return warnGradeNm;
    }

    public void setWarnGradeNm(String warnGradeNm) {
        this.warnGradeNm = warnGradeNm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public String getPadnm() {
        return padnm;
    }

    public void setPadnm(String padnm) {
        this.padnm = padnm;
    }

    public String getMsgCt() {
        return msgCt;
    }

    public void setMsgCt(String msgCt) {
        this.msgCt = msgCt;
    }

    public String getHasMouTainTorrents() {
        return hasMouTainTorrents;
    }

    public void setHasMouTainTorrents(String hasMouTainTorrents) {
        this.hasMouTainTorrents = hasMouTainTorrents;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
