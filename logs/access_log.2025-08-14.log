0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:46 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:47 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:47 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:47 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /login?code=ZtoLZW&state=GWo29N HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:53 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:54 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [14/Aug/2025:08:35:55 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723790
************ - - [14/Aug/2025:10:31:15 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [14/Aug/2025:10:31:15 +0800] "GET /login HTTP/1.1" 302 -
************ - - [14/Aug/2025:10:31:15 +0800] "GET /login?code=2tf0sm&state=kgpDrM HTTP/1.1" 302 -
************ - - [14/Aug/2025:10:31:16 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [14/Aug/2025:10:31:16 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1755138676790 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1755138676790 HTTP/1.1" 200 552
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755138679679 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1755138679679 HTTP/1.1" 200 61784
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755138679751 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1755138679751 HTTP/1.1" 200 10388
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755138679768 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755138679768 HTTP/1.1" 200 2009
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1755138679905 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-11+10:31:19&etm=&_timer304=1755138679905 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755138679905 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-14+08:00&etm=2025-08-14+11:00&filterCnt=6&_timer304=1755138679905 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755138679905 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755138679905 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:19 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1755138679905 HTTP/1.1" 200 1482
************ - - [14/Aug/2025:10:31:20 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755138679905 HTTP/1.1" 200 166
************ - - [14/Aug/2025:10:31:20 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-14+08:00&etm=2025-08-14+11:00&filterCnt=6&_timer304=1755138679905 HTTP/1.1" 200 164
************ - - [14/Aug/2025:10:31:20 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-11+10:31:19&etm=&_timer304=1755138679905 HTTP/1.1" 200 156
************ - - [14/Aug/2025:10:31:20 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Aug/2025:10:31:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Aug/2025:10:31:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Aug/2025:10:31:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Aug/2025:10:31:20 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755138679905 HTTP/1.1" 200 169
************ - - [14/Aug/2025:10:31:20 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755138679905 HTTP/1.1" 200 13016
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-13&_timer304=1755138680346 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/base/saas/token?_timer304=1755138680346 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1755138680357 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************ - - [14/Aug/2025:10:31:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:10:31:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [14/Aug/2025:10:31:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [14/Aug/2025:10:31:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:10:31:22 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [14/Aug/2025:10:31:22 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Aug/2025:10:31:22 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [14/Aug/2025:10:31:23 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [14/Aug/2025:10:31:23 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1755138680357 HTTP/1.1" 200 2009
************ - - [14/Aug/2025:10:31:23 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [14/Aug/2025:10:31:23 +0800] "GET /api/base/saas/token?_timer304=1755138680346 HTTP/1.1" 200 411
************ - - [14/Aug/2025:10:31:23 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [14/Aug/2025:10:31:23 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [14/Aug/2025:10:31:23 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [14/Aug/2025:10:31:24 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [14/Aug/2025:10:31:25 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-13&_timer304=1755138680346 HTTP/1.1" 200 433
************ - - [14/Aug/2025:10:31:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755138686480 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755138686479 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755138686486 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755138686486 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755138686486 HTTP/1.1" 200 152
************ - - [14/Aug/2025:10:31:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755138686486 HTTP/1.1" 200 148
************ - - [14/Aug/2025:10:31:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755138686480 HTTP/1.1" 200 159
************ - - [14/Aug/2025:10:31:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755138686479 HTTP/1.1" 200 160
************ - - [14/Aug/2025:10:31:28 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1755138688668 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:28 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-11+10:31:28&etm=&_timer304=1755138688846 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755138688846 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-14+08:00&etm=2025-08-14+11:00&filterCnt=6&_timer304=1755138688846 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755138688846 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:28 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755138688846 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755138688847 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:28 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755138688851 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:28 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Aug/2025:10:31:28 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Aug/2025:10:31:28 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Aug/2025:10:31:28 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Aug/2025:10:31:28 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-14+08:00&etm=2025-08-14+11:00&filterCnt=6&_timer304=1755138688846 HTTP/1.1" 200 164
************ - - [14/Aug/2025:10:31:28 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1755138688846 HTTP/1.1" 200 166
************ - - [14/Aug/2025:10:31:28 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-11+10:31:28&etm=&_timer304=1755138688846 HTTP/1.1" 200 156
************ - - [14/Aug/2025:10:31:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755138688847 HTTP/1.1" 200 161
************ - - [14/Aug/2025:10:31:28 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1755138688846 HTTP/1.1" 200 169
************ - - [14/Aug/2025:10:31:29 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:29 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755138689012 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:29 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:29 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755138689046 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:29 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1755138688846 HTTP/1.1" 200 13016
************ - - [14/Aug/2025:10:31:29 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1755138688851 HTTP/1.1" 200 159616
************ - - [14/Aug/2025:10:31:29 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [14/Aug/2025:10:31:29 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [14/Aug/2025:10:31:29 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1755138688668 HTTP/1.1" 200 144
************ - - [14/Aug/2025:10:31:29 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755138689046 HTTP/1.1" 200 258
************ - - [14/Aug/2025:10:31:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [14/Aug/2025:10:31:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [14/Aug/2025:10:31:30 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [14/Aug/2025:10:31:30 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755138690486 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:30 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [14/Aug/2025:10:31:30 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755138690574 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:30 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755138690574 HTTP/1.1" 200 148
************ - - [14/Aug/2025:10:31:30 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1755138690486 HTTP/1.1" 200 258
************ - - [14/Aug/2025:10:31:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755138689012 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:10:31:30 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:30 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [14/Aug/2025:10:31:31 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Aug/2025:10:31:32 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1393
************ - - [14/Aug/2025:10:31:36 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 279896
************ - - [14/Aug/2025:10:31:36 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [14/Aug/2025:10:31:38 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755138698096 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:38 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755138698096 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:38 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:38 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755138698096 HTTP/1.1" 200 443
************ - - [14/Aug/2025:10:31:38 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755138698096 HTTP/1.1" 200 510
************ - - [14/Aug/2025:10:31:38 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [14/Aug/2025:10:31:38 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138698184 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:38 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138698184 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:38 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138698184 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:38 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138698184 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:38 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138698184 HTTP/1.1" 200 727
************ - - [14/Aug/2025:10:31:38 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138698184 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:10:31:38 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138698184 HTTP/1.1" 200 329
************ - - [14/Aug/2025:10:31:38 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138698184 HTTP/1.1" 200 12917
************ - - [14/Aug/2025:10:31:44 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138704163 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:44 +0800] "OPTIONS /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138704163 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:44 +0800] "OPTIONS /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138704163 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:44 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138704163 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:31:44 +0800] "GET /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138704163 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:10:31:44 +0800] "GET /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138704163 HTTP/1.1" 200 729
************ - - [14/Aug/2025:10:31:44 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138704163 HTTP/1.1" 200 329
************ - - [14/Aug/2025:10:31:44 +0800] "GET /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138704163 HTTP/1.1" 200 15587
************ - - [14/Aug/2025:10:32:08 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755138728491 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:08 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755138728491 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:08 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:08 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755138728491 HTTP/1.1" 200 443
************ - - [14/Aug/2025:10:32:08 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [14/Aug/2025:10:32:08 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755138728491 HTTP/1.1" 200 510
************ - - [14/Aug/2025:10:32:08 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138728572 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:08 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138728572 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:08 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138728572 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:08 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138728572 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:08 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138728572 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:10:32:08 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138728572 HTTP/1.1" 200 727
************ - - [14/Aug/2025:10:32:08 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138728572 HTTP/1.1" 200 329
************ - - [14/Aug/2025:10:32:09 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755138728572 HTTP/1.1" 200 12917
************ - - [14/Aug/2025:10:32:59 +0800] "OPTIONS /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138779369 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:59 +0800] "OPTIONS /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138779369 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:59 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138779369 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:59 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138779369 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:32:59 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138779369 HTTP/1.1" 200 329
************ - - [14/Aug/2025:10:32:59 +0800] "GET /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138779369 HTTP/1.1" 200 729
************ - - [14/Aug/2025:10:32:59 +0800] "GET /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138779369 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:10:33:00 +0800] "GET /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755138779369 HTTP/1.1" 200 15587
************ - - [14/Aug/2025:10:36:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755138976478 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:36:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755138976478 HTTP/1.1" 200 160
************ - - [14/Aug/2025:10:36:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755138986543 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:36:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755138986543 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:36:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755138986558 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:36:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755138986543 HTTP/1.1" 200 148
************ - - [14/Aug/2025:10:36:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755138986543 HTTP/1.1" 200 152
************ - - [14/Aug/2025:10:36:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755138986558 HTTP/1.1" 200 159
************ - - [14/Aug/2025:10:36:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755138988690 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:36:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755138988690 HTTP/1.1" 200 161
************ - - [14/Aug/2025:10:36:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:36:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:36:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:36:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755138988934 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:36:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:10:36:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:10:36:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755138988934 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:10:36:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:10:41:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755139276476 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:41:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755139276476 HTTP/1.1" 200 160
************ - - [14/Aug/2025:10:41:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755139286582 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:41:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755139286582 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:41:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755139286597 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:41:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755139286582 HTTP/1.1" 200 148
************ - - [14/Aug/2025:10:41:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755139286582 HTTP/1.1" 200 152
************ - - [14/Aug/2025:10:41:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755139286597 HTTP/1.1" 200 159
************ - - [14/Aug/2025:10:41:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755139288688 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:41:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755139288688 HTTP/1.1" 200 161
************ - - [14/Aug/2025:10:41:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:41:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:41:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755139288934 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:41:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:41:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:10:41:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:10:41:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755139288934 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:10:41:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:10:46:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755139576479 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:46:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755139576479 HTTP/1.1" 200 160
************ - - [14/Aug/2025:10:46:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755139586619 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:46:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755139586619 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:46:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755139586631 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:46:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755139586619 HTTP/1.1" 200 148
************ - - [14/Aug/2025:10:46:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755139586619 HTTP/1.1" 200 152
************ - - [14/Aug/2025:10:46:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755139586631 HTTP/1.1" 200 159
************ - - [14/Aug/2025:10:46:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755139588690 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:46:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755139588690 HTTP/1.1" 200 161
************ - - [14/Aug/2025:10:46:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:46:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:46:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755139588934 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:46:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:46:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:10:46:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:10:46:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755139588934 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:10:46:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:10:47:25 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=89A898AA-8D4D-479A-B2ED-18551D92CA86&_timer304=1755139645527 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:47:25 +0800] "GET /api/call/common/message/call-result?extSendId=89A898AA-8D4D-479A-B2ED-18551D92CA86&_timer304=1755139645527 HTTP/1.1" 200 1291
************ - - [14/Aug/2025:10:51:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755139876479 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:51:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755139876479 HTTP/1.1" 200 160
************ - - [14/Aug/2025:10:51:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755139886668 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:51:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755139886667 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:51:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755139886667 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:51:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755139886667 HTTP/1.1" 200 148
************ - - [14/Aug/2025:10:51:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755139886668 HTTP/1.1" 200 159
************ - - [14/Aug/2025:10:51:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755139886667 HTTP/1.1" 200 152
************ - - [14/Aug/2025:10:51:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755139888692 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:51:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755139888692 HTTP/1.1" 200 161
************ - - [14/Aug/2025:10:51:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:51:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755139888934 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:51:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:51:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:51:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:10:51:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:10:51:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755139888934 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:10:51:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:10:56:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755140176477 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:56:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755140176477 HTTP/1.1" 200 160
************ - - [14/Aug/2025:10:56:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755140186706 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:56:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755140186707 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:56:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755140186707 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:56:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755140186707 HTTP/1.1" 200 148
************ - - [14/Aug/2025:10:56:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755140186707 HTTP/1.1" 200 152
************ - - [14/Aug/2025:10:56:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755140186706 HTTP/1.1" 200 159
************ - - [14/Aug/2025:10:56:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755140188690 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:56:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755140188690 HTTP/1.1" 200 161
************ - - [14/Aug/2025:10:56:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:56:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:56:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:56:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755140188934 HTTP/1.1" 200 -
************ - - [14/Aug/2025:10:56:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:10:56:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:10:56:29 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755140188934 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:10:56:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:01:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755140487643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:01:27 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755140487642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:01:27 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755140487642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:01:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755140487643 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:01:27 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755140487642 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:01:27 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755140487642 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:01:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755140489638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:01:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755140489638 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:01:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755140502635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:01:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:01:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755140502638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:01:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:01:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:01:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:01:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755140502635 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:01:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:01:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755140502638 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:01:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:06:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755140777642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:06:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755140777642 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:06:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755140788641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:06:28 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755140788642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:06:28 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755140788642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:06:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755140788641 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:06:28 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755140788642 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:06:28 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755140788642 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:06:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755140789638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:06:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755140789638 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:06:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:06:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:06:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755140790637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:06:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:06:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:06:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:06:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755140790637 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:06:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:11:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755141077646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:11:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755141077646 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:11:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755141089640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:11:29 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755141089641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:11:29 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755141089641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:11:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755141089643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:11:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755141089643 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:11:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755141089640 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:11:29 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755141089641 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:11:29 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755141089641 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:11:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:11:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:11:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755141090641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:11:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:11:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:11:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:11:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755141090641 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:11:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:16:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755141390637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:16:30 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755141390638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:16:30 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755141390638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:16:30 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755141390638 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:16:30 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755141390638 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:16:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755141390637 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:16:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755141402648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:16:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755141402648 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:17:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755141462634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:17:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:17:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:17:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755141462636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:17:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:17:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:17:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755141462634 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:17:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:17:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755141462636 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:17:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:21:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755141691638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:21:31 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755141691639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:21:31 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755141691639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:21:31 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755141691639 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:21:31 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755141691639 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:21:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755141691638 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:21:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755141702641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:21:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755141702641 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:22:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755141762732 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:22:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:22:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:22:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755141762733 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:22:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:22:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:22:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755141762732 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:22:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:22:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755141762733 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:22:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:26:32 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755141992742 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:26:32 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755141992741 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:26:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755141992742 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:26:32 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755141992742 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:26:32 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755141992741 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:26:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755141992742 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:26:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755142002638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:26:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755142002638 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:27:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755142062643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:27:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:27:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:27:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755142062645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:27:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:27:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:27:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755142062643 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:27:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:27:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755142062645 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:27:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:31:33 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755142293643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:31:33 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755142293643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:31:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755142293644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:31:33 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755142293643 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:31:33 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755142293643 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:31:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755142293644 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:31:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755142302637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:31:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755142302637 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:32:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755142362645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:32:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:32:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:32:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755142362648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:32:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:32:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:32:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755142362645 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:32:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:32:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755142362648 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:32:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:36:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755142594634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:36:34 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755142594635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:36:34 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755142594635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:36:34 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755142594635 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:36:34 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755142594635 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:36:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755142594634 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:36:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755142602647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:36:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755142602647 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:37:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755142655440 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:37:35 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:37:35 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:37:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755142655480 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:37:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:37:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755142655440 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:37:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:37:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:37:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755142655480 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:37:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:41:35 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755142895635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:41:35 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755142895635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:41:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755142895637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:41:35 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755142895635 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:41:35 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755142895635 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:41:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755142895637 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:41:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755142902646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:41:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755142902646 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:42:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755142962647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:42:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:42:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:42:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:42:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755142962649 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:42:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:42:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755142962647 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:42:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:42:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755142962649 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:42:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:46:36 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755143196635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:46:36 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755143196635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:46:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755143196636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:46:36 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755143196635 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:46:36 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755143196635 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:46:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755143196636 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:46:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755143202635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:46:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755143202635 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:47:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755143262634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:47:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:47:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:47:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755143262636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:47:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:47:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:47:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755143262634 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:47:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:47:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755143262636 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:47:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:51:37 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755143497633 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:51:37 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755143497633 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:51:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755143497635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:51:37 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755143497633 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:51:37 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755143497633 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:51:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755143497635 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:51:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755143502644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:51:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755143502644 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:52:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755143562634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:52:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:52:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:52:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755143562636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:52:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:52:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:52:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755143562634 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:52:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:52:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755143562636 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:52:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:11:56:38 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755143798639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:56:38 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755143798639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:56:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755143798640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:56:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755143798640 HTTP/1.1" 200 159
************ - - [14/Aug/2025:11:56:38 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755143798639 HTTP/1.1" 200 152
************ - - [14/Aug/2025:11:56:38 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755143798639 HTTP/1.1" 200 148
************ - - [14/Aug/2025:11:56:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755143802646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:56:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755143802646 HTTP/1.1" 200 161
************ - - [14/Aug/2025:11:57:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755143862745 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:57:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:57:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:57:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755143862749 HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:57:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:11:57:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755143862745 HTTP/1.1" 200 160
************ - - [14/Aug/2025:11:57:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:11:57:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:11:57:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755143862749 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:11:57:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:01:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755144099642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:01:39 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755144099643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:01:39 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755144099643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:01:39 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755144099643 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:01:39 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755144099643 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:01:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755144099642 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:01:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755144102645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:01:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755144102645 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:02:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755144162634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:02:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:02:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:02:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755144162636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:02:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:02:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:02:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755144162634 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:02:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:02:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755144162636 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:02:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:06:40 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755144400742 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:06:40 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755144400742 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:06:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755144400743 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:06:40 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755144400742 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:06:40 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755144400742 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:06:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755144400743 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:06:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755144402634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:06:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755144402634 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:07:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755144462647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:07:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:07:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:07:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755144462649 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:07:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:07:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:07:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755144462647 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:07:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:07:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:07:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755144462649 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:11:41 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755144701744 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:11:41 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755144701744 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:11:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755144701746 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:11:41 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755144701744 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:11:41 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755144701744 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:11:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755144701746 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:11:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755144702635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:11:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755144702635 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:12:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755144762752 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:12:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:12:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:12:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755144762754 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:12:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:12:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:12:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755144762752 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:12:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:12:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755144762754 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:12:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:16:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755145002648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:16:42 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755145002649 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:16:42 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755145002649 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:16:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755145002651 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:16:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755145002648 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:16:42 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755145002649 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:16:42 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755145002649 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:16:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755145002651 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:17:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755145062634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:17:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:17:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:17:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:17:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755145062635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:17:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:17:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755145062634 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:17:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:17:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755145062635 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:17:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:21:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755145302635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:21:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755145302635 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:21:43 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755145303642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:21:43 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755145303642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:21:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755145303644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:21:43 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755145303642 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:21:43 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755145303642 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:21:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755145303644 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:22:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755145362645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:22:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:22:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755145362647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:22:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:22:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:22:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:22:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755145362645 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:22:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:22:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755145362647 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:22:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:26:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755145602738 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:26:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755145602738 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:26:44 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755145604640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:26:44 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755145604640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:26:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755145604641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:26:44 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755145604640 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:26:44 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755145604640 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:26:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755145604641 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:27:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755145662752 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:27:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:27:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:27:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755145662754 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:27:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:27:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:27:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755145662752 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:27:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:27:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755145662754 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:27:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:31:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755145902640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:31:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755145902640 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:31:45 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755145905647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:31:45 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755145905647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:31:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755145905648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:31:45 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755145905647 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:31:45 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755145905647 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:31:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755145905648 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:32:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755145962737 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:32:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:32:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:32:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755145962739 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:32:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:32:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:32:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755145962737 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:32:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:32:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755145962739 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:32:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:36:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755146202644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:36:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755146202644 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:36:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755146206647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:36:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755146206647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:36:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755146206649 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:36:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755146206647 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:36:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755146206649 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:36:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755146206647 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:37:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755146262634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:37:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:37:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:37:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:37:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:37:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755146262636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:37:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755146262634 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:37:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:37:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755146262636 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:37:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:41:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755146502749 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:41:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755146502749 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:41:47 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755146507640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:41:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755146507639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:41:47 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755146507640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:41:47 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755146507640 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:41:47 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755146507640 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:41:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755146507639 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:42:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755146562642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:42:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:42:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:42:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755146562643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:42:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:42:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:42:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755146562642 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:42:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:42:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755146562643 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:42:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:46:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755146802639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:46:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755146802639 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:46:48 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755146808635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:46:48 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755146808635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:46:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755146808636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:46:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755146808636 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:46:48 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755146808635 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:46:48 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755146808635 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:47:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755146862647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:47:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:47:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755146862648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:47:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:47:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:47:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:47:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755146862647 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:47:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:47:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755146862648 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:47:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:51:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755147102636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:51:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755147102636 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:51:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755147109636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:51:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755147109637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:51:49 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755147109637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:51:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755147109637 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:51:49 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755147109637 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:51:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755147109636 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:52:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755147162645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:52:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:52:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:52:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:52:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755147162646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:52:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:52:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755147162645 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:52:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:52:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755147162646 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:52:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:12:56:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755147402646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:56:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755147402646 HTTP/1.1" 200 161
************ - - [14/Aug/2025:12:56:50 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755147410641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:56:50 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755147410641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:56:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755147410642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:56:50 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755147410641 HTTP/1.1" 200 148
************ - - [14/Aug/2025:12:56:50 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755147410641 HTTP/1.1" 200 152
************ - - [14/Aug/2025:12:56:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755147410642 HTTP/1.1" 200 159
************ - - [14/Aug/2025:12:57:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755147462634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:57:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:57:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:57:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755147462635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:57:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:12:57:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:12:57:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755147462634 HTTP/1.1" 200 160
************ - - [14/Aug/2025:12:57:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:12:57:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755147462635 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:12:57:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:01:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755147702637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:01:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755147702637 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:01:51 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755147711643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:01:51 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755147711643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:01:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755147711645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:01:51 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755147711643 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:01:51 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755147711643 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:01:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755147711645 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:02:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755147762638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:02:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:02:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:02:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755147762639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:02:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:02:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:02:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755147762638 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:02:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:02:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755147762639 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:02:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:06:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755148002636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:06:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755148002636 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:06:52 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755148012643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:06:52 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755148012643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:06:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755148012645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:06:52 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755148012643 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:06:52 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755148012643 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:06:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755148012645 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:07:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755148062745 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:07:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:07:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:07:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755148062747 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:07:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:07:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:07:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755148062745 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:07:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:07:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755148062747 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:07:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:11:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755148302648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:11:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755148302648 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:11:53 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755148313645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:11:53 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755148313645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:11:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755148313647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:11:53 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755148313645 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:11:53 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755148313645 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:11:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755148313647 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:12:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755148362639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:12:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:12:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:12:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755148362642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:12:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:12:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:12:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755148362639 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:12:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:12:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755148362642 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:12:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:16:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755148602637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:16:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755148602637 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:16:54 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755148614636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:16:54 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755148614636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:16:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755148614637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:16:54 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755148614636 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:16:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755148614637 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:16:54 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755148614636 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:17:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755148662643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:17:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:17:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:17:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755148662644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:17:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:17:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:17:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755148662643 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:17:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:17:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755148662644 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:17:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:21:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755148902731 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:21:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755148902731 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:21:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755148915633 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:21:55 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755148915635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:21:55 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755148915635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:21:55 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755148915635 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:21:55 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755148915635 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:21:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755148915633 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:22:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755148962643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:22:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:22:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755148962644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:22:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:22:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:22:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:22:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755148962643 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:22:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:22:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755148962644 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:22:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:26:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755149202638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:26:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755149202638 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:26:56 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755149216738 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:26:56 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755149216738 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:26:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755149216739 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:26:56 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755149216738 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:26:56 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755149216738 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:26:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755149216739 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:27:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755149262635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:27:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:27:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:27:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755149262637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:27:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:27:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:27:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755149262635 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:27:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:27:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:27:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755149262637 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:31:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755149502642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:31:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755149502642 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:31:57 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755149517642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:31:57 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755149517642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:31:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755149517643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:31:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755149517643 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:31:57 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755149517642 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:31:57 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755149517642 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:32:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755149562640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:32:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:32:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755149562642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:32:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:32:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:32:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:32:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755149562640 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:32:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:32:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755149562642 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:32:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:36:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755149802734 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:36:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755149802734 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:36:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755149818646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:36:58 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755149818647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:36:58 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755149818648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:36:58 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755149818648 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:36:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755149818646 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:36:58 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755149818647 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:37:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755149862645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:37:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:37:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:37:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:37:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755149862647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:37:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:37:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755149862645 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:37:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:37:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755149862647 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:37:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:41:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755150102639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:41:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755150102639 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:41:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755150119741 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:41:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755150119742 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:41:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755150119742 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:41:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755150119742 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:41:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755150119742 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:41:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755150119741 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:42:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755150162732 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:42:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:42:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:42:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755150162734 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:42:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:42:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:42:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755150162732 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:42:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:42:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755150162734 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:42:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:46:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755150402639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:46:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755150402639 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:47:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755150420636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:47:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755150420637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:47:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755150420637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:47:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755150420636 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:47:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755150420637 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:47:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755150420637 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:47:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755150462632 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:47:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:47:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:47:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755150462634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:47:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:47:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:47:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755150462632 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:47:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:47:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755150462634 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:47:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:51:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755150702637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:51:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755150702637 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:52:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755150721634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:52:01 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755150721636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:52:01 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755150721636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:52:01 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755150721636 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:52:01 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755150721636 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:52:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755150721634 HTTP/1.1" 200 159
************ - - [14/Aug/2025:13:52:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755150762641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:52:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:52:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:52:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755150762642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:52:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:52:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:52:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755150762641 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:52:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:52:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755150762642 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:52:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:56:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755150981674 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:56:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755150981674 HTTP/1.1" 200 160
************ - - [14/Aug/2025:13:56:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755150988702 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:56:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755150988702 HTTP/1.1" 200 161
************ - - [14/Aug/2025:13:56:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:56:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:56:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755150988941 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:56:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:56:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:13:56:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:13:56:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:13:56:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755150988941 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:13:57:01 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755151021674 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:57:01 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755151021674 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:57:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755151021676 HTTP/1.1" 200 -
************ - - [14/Aug/2025:13:57:01 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755151021674 HTTP/1.1" 200 148
************ - - [14/Aug/2025:13:57:01 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755151021674 HTTP/1.1" 200 152
************ - - [14/Aug/2025:13:57:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755151021676 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:01:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755151292474 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:01:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755151292474 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:01:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755151292482 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:01:32 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:01:32 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:01:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755151292483 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:01:32 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:01:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:01:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755151292482 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:01:33 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:01:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755151292483 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:01:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:14:02:02 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755151322646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:02:02 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755151322646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:02:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755151322647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:02:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755151322647 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:02:02 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755151322646 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:02:02 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755151322646 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:06:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755151602638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:06:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755151602638 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:07:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755151623639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:07:03 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755151623640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:07:03 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755151623640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:07:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755151623639 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:07:03 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755151623640 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:07:03 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755151623640 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:07:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755151662644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:07:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:07:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:07:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:07:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755151662646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:07:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:07:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755151662644 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:07:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:07:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755151662646 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:07:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:14:10:39 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:10:40 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 279896
************ - - [14/Aug/2025:14:10:40 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:10:40 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 32146
************ - - [14/Aug/2025:14:10:42 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205102&_timer304=1755151842460 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:10:42 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755151842460 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:10:42 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205102&_timer304=1755151842460 HTTP/1.1" 200 514
************ - - [14/Aug/2025:14:10:42 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755151842460 HTTP/1.1" 200 373
************ - - [14/Aug/2025:14:10:42 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755151842515 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:10:42 +0800] "GET /api/fusion/warning/snapshot-index?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10&_timer304=1755151842515 HTTP/1.1" 200 655
************ - - [14/Aug/2025:14:10:50 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 32146
************ - - [14/Aug/2025:14:10:57 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:10:57 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 32146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /login?code=tQr7ji&state=jXSql7 HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:08 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:09 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:10 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723790
************ - - [14/Aug/2025:14:11:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755151877637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:11:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755151877637 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:11:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755151889646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:11:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755151889646 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:11:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:11:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:11:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755151890646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:11:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:11:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:11:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:11:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755151890646 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:11:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:43 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:11:44 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /login?code=6Ul6en&state=XyqQFo HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:49 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:50 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:51 +0800] "GET /v2/api-docs HTTP/1.1" 200 2725793
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:58 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:58 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:58 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:12:58 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:13:26 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:13:27 +0800] "GET /api/fusion/warning/detail?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10 HTTP/1.1" 200 378
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:13:27 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:14:15:39 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:42 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [14/Aug/2025:14:15:44 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:44 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755152144657 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:44 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755152144657 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:44 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755152144657 HTTP/1.1" 200 443
************ - - [14/Aug/2025:14:15:44 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [14/Aug/2025:14:15:44 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755152144847 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:44 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755152144847 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:44 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755152144847 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:44 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755152144847 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:44 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755152144657 HTTP/1.1" 200 510
************ - - [14/Aug/2025:14:15:44 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755152144847 HTTP/1.1" 200 329
************ - - [14/Aug/2025:14:15:44 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755152144847 HTTP/1.1" 200 727
************ - - [14/Aug/2025:14:15:44 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755152144847 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:14:15:45 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755152144847 HTTP/1.1" 200 12917
************ - - [14/Aug/2025:14:15:55 +0800] "OPTIONS /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755152155394 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:55 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755152155394 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:55 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755152155394 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:55 +0800] "OPTIONS /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755152155394 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:15:55 +0800] "GET /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755152155394 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:14:15:55 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755152155394 HTTP/1.1" 200 329
************ - - [14/Aug/2025:14:15:55 +0800] "GET /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755152155394 HTTP/1.1" 200 729
************ - - [14/Aug/2025:14:15:56 +0800] "GET /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755152155394 HTTP/1.1" 200 15587
************ - - [14/Aug/2025:14:16:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755152176485 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:16:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755152176485 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:16:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755152188688 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:16:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755152188688 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:16:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:16:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755152188940 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:16:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:16:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:16:29 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:16:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:16:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755152188940 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:16:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /login?code=d2OuZp&state=ZiqKYT HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:43 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:44 +0800] "GET /v2/api-docs HTTP/1.1" 200 2725793
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:48 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:48 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:48 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:16:48 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:17:00 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:17:00 +0800] "GET /api/fusion/warning/detail?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10 HTTP/1.1" 200 369
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:17:00 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:14:17:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755152227635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:17:07 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755152227636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:17:07 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755152227636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:17:07 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755152227636 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:17:07 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755152227636 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:17:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755152227635 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:21:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755152502637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:21:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755152502637 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:22:08 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755152528644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:22:08 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755152528644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:22:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755152528645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:22:08 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755152528644 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:22:08 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755152528644 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:22:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755152528645 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:22:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755152562636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:22:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:22:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:22:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755152562638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:22:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:22:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:22:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755152562636 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:22:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:22:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755152562638 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:22:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:14:26:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755152777637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:26:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755152777637 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:26:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755152789648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:26:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755152789648 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:26:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:26:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:26:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755152790639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:26:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:26:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:26:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:26:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:14:26:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755152790639 HTTP/1.1" 200 441332
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:29 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:29 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /login?code=UpyoOu&state=PJnMK1 HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:30 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:31 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:32 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:35 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:35 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:35 +0800] "GET /v2/api-docs HTTP/1.1" 200 2726216
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:44 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:44 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:44 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:44 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:49 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:58 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:59 +0800] "GET /api/fusion/warning/flow-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10 HTTP/1.1" 200 550
0:0:0:0:0:0:0:1 - - [14/Aug/2025:14:27:59 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:14:31:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755153102771 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:31:43 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755153102771 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:32:12 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755153132639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:32:12 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755153132639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:32:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755153132640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:32:14 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755153132639 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:32:14 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755153132639 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:32:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755153132640 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:32:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755153162741 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:32:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:32:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:32:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755153162743 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:32:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:32:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755153162741 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:32:43 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:32:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:32:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755153162743 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:32:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:14:36:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755153402647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:36:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755153402647 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:37:14 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755153434751 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:37:14 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755153434751 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:37:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755153434752 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:37:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755153434752 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:37:14 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755153434751 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:37:14 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755153434751 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:37:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755153462635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:37:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:37:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:37:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755153462637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:37:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:37:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:37:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755153462635 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:37:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:37:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755153462637 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:37:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:14:41:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755153702642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:41:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755153702642 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:42:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755153735636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:42:15 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755153735637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:42:15 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755153735637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:42:15 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755153735637 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:42:15 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755153735637 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:42:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755153735636 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:42:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755153762643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:42:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:42:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:42:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755153762645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:42:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:42:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:42:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755153762643 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:42:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:42:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755153762645 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:42:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:14:46:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755154002735 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:46:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755154002735 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:47:16 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755154036646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:47:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755154036647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:47:16 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755154036646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:47:16 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755154036646 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:47:16 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755154036646 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:47:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755154036647 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:47:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755154062744 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:47:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:47:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:47:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:47:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755154062746 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:47:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:47:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755154062744 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:47:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:47:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755154062746 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:47:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:14:51:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755154302637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:51:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755154302637 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:52:17 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755154337647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:52:17 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755154337647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:52:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755154337649 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:52:17 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755154337647 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:52:17 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755154337647 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:52:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755154337649 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:52:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755154362735 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:52:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:52:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:52:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755154362736 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:52:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:52:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:52:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755154362735 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:52:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:52:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755154362736 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:52:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:14:56:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755154602643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:56:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755154602643 HTTP/1.1" 200 161
************ - - [14/Aug/2025:14:57:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755154638635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:57:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755154638635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:57:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755154638636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:57:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755154638635 HTTP/1.1" 200 152
************ - - [14/Aug/2025:14:57:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755154638635 HTTP/1.1" 200 148
************ - - [14/Aug/2025:14:57:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755154638636 HTTP/1.1" 200 159
************ - - [14/Aug/2025:14:57:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755154662636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:57:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:57:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:57:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:57:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755154662638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:14:57:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:14:57:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755154662636 HTTP/1.1" 200 160
************ - - [14/Aug/2025:14:57:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:14:57:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755154662638 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:14:57:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:01:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755154902645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:01:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755154902645 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:02:19 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755154939636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:02:19 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755154939636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:02:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755154939637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:02:19 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755154939636 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:02:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755154939637 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:02:19 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755154939636 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:02:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755154962640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:02:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:02:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:02:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:02:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755154962641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:02:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:02:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755154962640 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:02:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:02:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755154962641 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:02:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:06:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755155202636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:06:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755155202636 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:07:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755155240648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:07:20 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755155240649 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:07:20 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755155240649 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:07:20 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755155240649 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:07:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755155240648 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:07:20 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755155240649 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:07:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755155262643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:07:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:07:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:07:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755155262645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:07:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:07:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:07:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755155262643 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:07:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:07:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755155262645 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:07:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:11:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755155502637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:11:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755155502637 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:12:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755155541639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:12:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755155541638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:12:21 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755155541639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:12:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755155541639 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:12:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755155541638 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:12:21 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755155541639 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:12:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755155562639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:12:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:12:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:12:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755155562640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:12:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:12:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:12:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755155562639 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:12:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:12:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755155562640 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:12:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:16:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755155802645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:16:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755155802645 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:17:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755155842641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:17:22 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755155842642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:17:22 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755155842642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:17:22 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755155842642 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:17:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755155842641 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:17:22 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755155842642 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:17:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755155862643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:17:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:17:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755155862644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:17:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:17:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:17:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:17:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755155862643 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:17:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:17:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755155862644 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:17:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:21:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755156102646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:21:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755156102646 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:22:23 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755156143636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:22:23 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755156143636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:22:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755156143637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:22:23 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755156143636 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:22:23 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755156143636 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:22:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755156143637 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:22:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755156162642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:22:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:22:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:22:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755156162643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:22:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:22:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755156162642 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:22:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:22:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:22:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755156162643 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:22:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:26:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755156402641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:26:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755156402641 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:27:24 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755156444648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:27:24 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755156444648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:27:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755156444649 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:27:24 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755156444648 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:27:24 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755156444648 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:27:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755156444649 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:27:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755156462731 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:27:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:27:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:27:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:27:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755156462733 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:27:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:27:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755156462731 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:27:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:27:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755156462733 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:27:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:31:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755156702637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:31:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755156702637 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:32:25 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755156745647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:32:25 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755156745647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:32:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755156745648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:32:25 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755156745647 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:32:25 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755156745647 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:32:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755156745648 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:32:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755156762645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:32:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:32:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:32:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755156762646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:32:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:32:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:32:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755156762645 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:32:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:32:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:32:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755156762646 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:36:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755157002636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:36:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755157002636 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:37:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755157046736 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:37:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755157046737 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:37:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755157046737 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:37:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755157046737 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:37:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755157046736 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:37:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755157046737 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:37:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755157062633 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:37:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:37:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:37:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755157062635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:37:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:37:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:37:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755157062633 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:37:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:37:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755157062635 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:37:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:41:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755157276484 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:41:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755157276484 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:41:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755157288690 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:41:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755157288690 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:41:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:41:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:41:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:41:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755157290647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:41:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:41:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:41:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755157290647 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:41:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:42:27 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755157347499 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:42:27 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755157347499 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:42:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755157347500 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:42:27 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755157347499 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:42:27 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755157347499 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:42:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755157347500 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:46:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755157577635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:46:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755157577635 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:46:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755157602635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:46:43 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755157602635 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:47:27 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755157647647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:47:27 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755157647647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:47:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755157647648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:47:27 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755157647647 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:47:27 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755157647647 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:47:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755157647648 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:47:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:47:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:47:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:47:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755157662638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:47:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:47:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:47:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:15:47:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755157662638 HTTP/1.1" 200 441332
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /login?code=7BIOvZ&state=MzeDFh HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:12 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:13 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:14 +0800] "GET /v2/api-docs HTTP/1.1" 200 2727717
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:18 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:18 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:18 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:18 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:21 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:29 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:30 +0800] "GET /api/fusion/warning/message-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10 HTTP/1.1" 200 6812
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:49:30 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:15:51:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755157884191 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:51:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755157884191 HTTP/1.1" 200 160
************ - - [14/Aug/2025:15:51:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755157888689 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:51:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755157888689 HTTP/1.1" 200 161
************ - - [14/Aug/2025:15:51:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:51:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:51:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755157888936 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:51:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:51:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:51:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:51:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755157888936 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:51:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:51:58 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:52:01 +0800] "GET /api/fusion/warning/detail?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10 HTTP/1.1" 200 369
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:52:01 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:52:07 +0800] "GET /api/fusion/warning/flow-list?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10 HTTP/1.1" 200 550
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:52:07 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:15:52:27 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755157947737 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:52:27 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755157947737 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:52:27 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755157947737 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:52:27 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755157947737 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:52:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755157947797 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:52:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755157947797 HTTP/1.1" 200 159
************ - - [14/Aug/2025:15:55:09 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755158109766 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:09 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755158109766 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:09 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:09 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755158109766 HTTP/1.1" 200 510
************ - - [14/Aug/2025:15:55:09 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755158109766 HTTP/1.1" 200 443
************ - - [14/Aug/2025:15:55:09 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [14/Aug/2025:15:55:09 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158109929 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:09 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158109929 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:09 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158109929 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:09 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158109929 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:09 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158109929 HTTP/1.1" 200 727
************ - - [14/Aug/2025:15:55:09 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158109929 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:15:55:09 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158109929 HTTP/1.1" 200 329
************ - - [14/Aug/2025:15:55:10 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158109929 HTTP/1.1" 200 12917
************ - - [14/Aug/2025:15:55:47 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 279896
************ - - [14/Aug/2025:15:55:48 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [14/Aug/2025:15:55:48 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [14/Aug/2025:15:55:49 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [14/Aug/2025:15:55:50 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755158150862 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:50 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755158150862 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:50 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755158150862 HTTP/1.1" 200 371
************ - - [14/Aug/2025:15:55:50 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755158150862 HTTP/1.1" 200 520
************ - - [14/Aug/2025:15:55:50 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755158150892 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:50 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755158150892 HTTP/1.1" 200 646
************ - - [14/Aug/2025:15:55:52 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:52 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4226
************ - - [14/Aug/2025:15:55:59 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755158159894 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:59 +0800] "OPTIONS /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755158159894 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:59 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755158159894 HTTP/1.1" 200 371
************ - - [14/Aug/2025:15:55:59 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1755158159894 HTTP/1.1" 200 520
************ - - [14/Aug/2025:15:55:59 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755158159944 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:55:59 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1755158159944 HTTP/1.1" 200 646
************ - - [14/Aug/2025:15:56:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755158177636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:56:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755158177636 HTTP/1.1" 200 160
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:56:21 +0800] "GET /api/fusion/warning/detail?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB HTTP/1.1" 200 370
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:56:21 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:15:56:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755158189646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:56:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755158189646 HTTP/1.1" 200 161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:56:30 +0800] "GET /api/fusion/warning/flow-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB HTTP/1.1" 200 550
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:56:30 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:15:56:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:56:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:56:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:56:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755158190637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:56:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:15:56:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:15:56:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755158190637 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:15:56:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:56:40 +0800] "GET /api/fusion/warning/message-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB HTTP/1.1" 200 5584
0:0:0:0:0:0:0:1 - - [14/Aug/2025:15:56:40 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:15:56:53 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:56:53 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4226
************ - - [14/Aug/2025:15:57:28 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755158248634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:57:28 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755158248634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:57:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755158248634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:15:57:28 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755158248634 HTTP/1.1" 200 148
************ - - [14/Aug/2025:15:57:28 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755158248634 HTTP/1.1" 200 152
************ - - [14/Aug/2025:15:57:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755158248634 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:01:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755158477644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755158477644 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:01:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755158488695 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755158488937 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:29 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755158488695 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:01:29 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:01:29 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 327038
************ - - [14/Aug/2025:16:01:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:01:30 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755158490854 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:30 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755158490854 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:30 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:30 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755158490854 HTTP/1.1" 200 443
************ - - [14/Aug/2025:16:01:30 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [14/Aug/2025:16:01:30 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158490958 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:30 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158490958 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:30 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158490958 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:30 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158490958 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:01:30 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755158490854 HTTP/1.1" 200 510
************ - - [14/Aug/2025:16:01:31 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158490958 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:16:01:31 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158490958 HTTP/1.1" 200 329
************ - - [14/Aug/2025:16:01:31 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158490958 HTTP/1.1" 200 727
************ - - [14/Aug/2025:16:01:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:01:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755158488937 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:01:31 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755158490958 HTTP/1.1" 200 12917
************ - - [14/Aug/2025:16:02:15 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755158535704 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:02:15 +0800] "OPTIONS /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755158535704 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:02:15 +0800] "OPTIONS /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755158535704 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:02:15 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755158535705 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:02:15 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755158535705 HTTP/1.1" 200 329
************ - - [14/Aug/2025:16:02:15 +0800] "GET /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755158535704 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:16:02:15 +0800] "GET /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755158535704 HTTP/1.1" 200 729
************ - - [14/Aug/2025:16:02:16 +0800] "GET /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755158535704 HTTP/1.1" 200 15587
************ - - [14/Aug/2025:16:02:28 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755158548672 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:02:28 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755158548672 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:02:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755158548675 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:02:28 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755158548672 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:02:28 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755158548672 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:02:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755158548675 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:06:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755158802641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:06:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755158802641 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:07:29 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755158849737 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:07:29 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755158849737 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:07:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755158849738 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:07:29 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755158849737 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:07:29 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755158849737 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:07:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755158849738 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:07:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755158862635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:07:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:07:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:07:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755158862637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:07:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:07:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:07:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755158862635 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:07:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:07:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755158862637 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:07:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:11:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755159102754 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:11:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755159102754 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:12:30 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755159150642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:12:30 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755159150642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:12:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755159150643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:12:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755159150643 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:12:30 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755159150642 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:12:30 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755159150642 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:12:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755159162637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:12:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:12:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:12:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:12:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755159162638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:12:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:12:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755159162637 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:12:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:12:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755159162638 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:12:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:16:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755159402644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:16:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755159402644 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:17:31 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755159451639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:17:31 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755159451639 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:17:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755159451638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:17:31 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755159451639 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:17:31 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755159451639 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:17:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755159451638 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:17:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755159462636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:17:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:17:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:17:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755159462637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:17:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:17:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:17:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755159462636 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:17:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:17:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755159462637 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:17:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:21:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755159702647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:21:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755159702647 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:22:32 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755159752637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:22:32 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755159752637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:22:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755159752638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:22:32 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755159752637 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:22:32 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755159752637 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:22:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755159752638 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:22:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755159762635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:22:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:22:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:22:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:22:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755159762637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:22:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:22:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755159762635 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:22:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:22:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755159762637 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:22:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:26:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755160002640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:26:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755160002640 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:27:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755160053636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:27:33 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755160053635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:27:33 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755160053635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:27:33 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755160053635 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:27:33 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755160053635 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:27:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755160053636 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:27:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755160062637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:27:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:27:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:27:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755160062638 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:27:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:27:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:27:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755160062637 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:27:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:27:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755160062638 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:27:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:31:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755160302757 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:31:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755160302757 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:32:34 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755160354640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:32:34 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755160354640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:32:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755160354641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:32:34 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755160354640 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:32:34 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755160354640 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:32:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755160354641 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:32:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755160362643 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:32:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:32:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:32:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755160362645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:32:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:32:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:32:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755160362643 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:32:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:32:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755160362645 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:32:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:36:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755160602637 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:36:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755160602637 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:37:35 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755160655635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:37:35 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755160655635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:37:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755160655633 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:37:35 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755160655635 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:37:35 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755160655635 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:37:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755160655633 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:37:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755160662645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:37:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:37:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:37:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:37:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755160662647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:37:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:37:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755160662645 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:37:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:37:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:37:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755160662647 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:41:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755160902640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:41:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755160902640 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:42:36 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755160956644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:42:36 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755160956644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:42:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755160956645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:42:36 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755160956644 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:42:36 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755160956644 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:42:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755160956645 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:42:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755160962634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:42:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:42:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:42:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:42:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755160962635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:42:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:42:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755160962634 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:42:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:42:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755160962635 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:42:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:46:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755161196931 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:46:36 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755161196931 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:46:36 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755161196941 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:46:36 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:46:36 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:46:36 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755161196942 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:46:36 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:46:36 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:46:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755161196941 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:46:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:46:38 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755161196942 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:46:38 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:47:37 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755161257647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:47:37 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755161257647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:47:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755161257647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:47:37 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755161257647 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:47:37 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755161257647 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:47:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755161257647 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:49:41 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755161381098 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:41 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:41 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755161381098 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:41 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755161381098 HTTP/1.1" 200 443
************ - - [14/Aug/2025:16:49:41 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [14/Aug/2025:16:49:41 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755161381098 HTTP/1.1" 200 510
************ - - [14/Aug/2025:16:49:41 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161381149 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:41 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161381149 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:41 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161381149 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:41 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161381149 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:41 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161381149 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:16:49:41 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161381149 HTTP/1.1" 200 727
************ - - [14/Aug/2025:16:49:41 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161381149 HTTP/1.1" 200 329
************ - - [14/Aug/2025:16:49:41 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161381149 HTTP/1.1" 200 12917
************ - - [14/Aug/2025:16:49:48 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755161388688 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:48 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755161388688 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:48 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:48 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755161388688 HTTP/1.1" 200 443
************ - - [14/Aug/2025:16:49:48 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [14/Aug/2025:16:49:48 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755161388688 HTTP/1.1" 200 510
************ - - [14/Aug/2025:16:49:48 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161388765 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:48 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161388765 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:48 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161388765 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:48 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161388765 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:49:48 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161388765 HTTP/1.1" 200 727
************ - - [14/Aug/2025:16:49:48 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161388765 HTTP/1.1" 200 329
************ - - [14/Aug/2025:16:49:48 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161388765 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:16:49:49 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161388765 HTTP/1.1" 200 12917
************ - - [14/Aug/2025:16:51:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755161477640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755161477640 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:51:26 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755161486096 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:26 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755161486096 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:26 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:26 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1755161486096 HTTP/1.1" 200 443
************ - - [14/Aug/2025:16:51:26 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [14/Aug/2025:16:51:26 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1755161486096 HTTP/1.1" 200 510
************ - - [14/Aug/2025:16:51:26 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161486183 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:26 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161486183 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:26 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161486183 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:26 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161486183 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:26 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161486183 HTTP/1.1" 200 727
************ - - [14/Aug/2025:16:51:26 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161486183 HTTP/1.1" 200 329
************ - - [14/Aug/2025:16:51:26 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161486183 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:16:51:26 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1755161486183 HTTP/1.1" 200 12917
************ - - [14/Aug/2025:16:51:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755161488688 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755161488688 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:51:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:28 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755161488939 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:28 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:51:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:51:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:51:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755161488939 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:51:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:16:52:37 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755161557684 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:52:37 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755161557684 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:52:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755161557684 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:52:37 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755161557684 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:52:37 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755161557684 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:52:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755161557684 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:56:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755161802743 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:56:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755161802743 HTTP/1.1" 200 161
************ - - [14/Aug/2025:16:57:38 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755161858633 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:57:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755161858634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:57:38 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755161858633 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:57:38 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755161858633 HTTP/1.1" 200 152
************ - - [14/Aug/2025:16:57:38 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755161858633 HTTP/1.1" 200 148
************ - - [14/Aug/2025:16:57:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755161858634 HTTP/1.1" 200 159
************ - - [14/Aug/2025:16:57:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755161862640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:57:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:57:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:57:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755161862641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:57:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:16:57:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755161862640 HTTP/1.1" 200 160
************ - - [14/Aug/2025:16:57:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:16:57:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:16:57:44 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755161862641 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:16:57:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:17:01:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755162077648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:01:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755162077648 HTTP/1.1" 200 160
************ - - [14/Aug/2025:17:01:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755162089641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:01:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755162089641 HTTP/1.1" 200 161
************ - - [14/Aug/2025:17:01:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:01:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:01:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755162090636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:01:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:01:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:17:01:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:17:01:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:17:01:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755162090636 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:17:02:39 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755162159641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:02:39 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755162159641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:02:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755162159642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:02:39 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755162159641 HTTP/1.1" 200 152
************ - - [14/Aug/2025:17:02:39 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755162159641 HTTP/1.1" 200 148
************ - - [14/Aug/2025:17:02:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755162159642 HTTP/1.1" 200 159
************ - - [14/Aug/2025:17:06:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755162402634 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:06:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755162402634 HTTP/1.1" 200 161
************ - - [14/Aug/2025:17:07:40 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755162460777 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:07:40 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755162460777 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:07:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755162460778 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:07:40 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755162460777 HTTP/1.1" 200 148
************ - - [14/Aug/2025:17:07:40 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755162460777 HTTP/1.1" 200 152
************ - - [14/Aug/2025:17:07:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755162460778 HTTP/1.1" 200 159
************ - - [14/Aug/2025:17:07:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755162462640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:07:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755162462641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:07:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:07:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:07:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:07:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:17:07:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755162462640 HTTP/1.1" 200 160
************ - - [14/Aug/2025:17:07:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:17:07:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755162462641 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:17:07:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:17:11:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755162702644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:11:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755162702644 HTTP/1.1" 200 161
************ - - [14/Aug/2025:17:12:41 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755162761647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:12:41 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755162761647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:12:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755162761648 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:12:41 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755162761647 HTTP/1.1" 200 152
************ - - [14/Aug/2025:17:12:41 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755162761647 HTTP/1.1" 200 148
************ - - [14/Aug/2025:17:12:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755162761648 HTTP/1.1" 200 159
************ - - [14/Aug/2025:17:12:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755162762646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:12:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:12:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:12:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755162762647 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:12:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:12:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:17:12:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755162762646 HTTP/1.1" 200 160
************ - - [14/Aug/2025:17:12:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:17:12:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755162762647 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:17:12:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:17:16:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755163002750 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:16:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755163002750 HTTP/1.1" 200 161
************ - - [14/Aug/2025:17:17:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755163062740 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:17:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:17:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:17:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755163062741 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:17:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:17:42 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755163062744 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:17:42 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755163062744 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:17:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755163062745 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:17:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:17:17:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755163062740 HTTP/1.1" 200 160
************ - - [14/Aug/2025:17:17:42 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755163062744 HTTP/1.1" 200 148
************ - - [14/Aug/2025:17:17:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755163062745 HTTP/1.1" 200 159
************ - - [14/Aug/2025:17:17:42 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755163062744 HTTP/1.1" 200 152
************ - - [14/Aug/2025:17:17:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:17:17:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755163062741 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:17:17:44 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:17:19:43 +0800] "OPTIONS /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755163182794 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:19:43 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755163182794 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:19:43 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755163182794 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:19:43 +0800] "OPTIONS /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755163182794 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:19:45 +0800] "GET /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755163182794 HTTP/1.1" 200 729
************ - - [14/Aug/2025:17:19:45 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755163182794 HTTP/1.1" 200 329
************ - - [14/Aug/2025:17:19:45 +0800] "GET /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755163182794 HTTP/1.1" 200 1382
************ - - [14/Aug/2025:17:19:45 +0800] "GET /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1755163182794 HTTP/1.1" 200 15587
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:48 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:48 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /login?code=0JWJtU&state=cX2IFL HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:49 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:50 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:50 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:50 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:50 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:51 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:51 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:51 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:51 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:51 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:51 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:51 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:53 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:53 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:19:54 +0800] "GET /v2/api-docs HTTP/1.1" 200 2728277
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:20:03 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:20:03 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:20:03 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:20:03 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:12 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:12 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /login?code=OOYbfw&state=DZsRAC HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:13 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:14 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:14 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:14 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:14 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:14 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:14 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:16 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:16 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:17 +0800] "GET /v2/api-docs HTTP/1.1" 200 2728277
************ - - [14/Aug/2025:17:21:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755163277641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:21:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755163277641 HTTP/1.1" 200 160
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:26 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:26 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:26 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:26 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:17:21:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755163289640 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:21:30 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755163289640 HTTP/1.1" 200 161
************ - - [14/Aug/2025:17:21:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:21:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755163290641 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:21:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:21:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:21:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:17:21:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:17:21:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:17:21:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755163290641 HTTP/1.1" 200 441332
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:39 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:42 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:42 +0800] "GET /api/fusion/warning/record?warnId=D8361CD9-45C1-4AE1-8B06-DC859B193B10 HTTP/1.1" 200 919
0:0:0:0:0:0:0:1 - - [14/Aug/2025:17:21:42 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [14/Aug/2025:17:22:43 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755163363636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:22:43 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755163363636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:22:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755163363635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:22:43 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755163363636 HTTP/1.1" 200 152
************ - - [14/Aug/2025:17:22:43 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755163363636 HTTP/1.1" 200 148
************ - - [14/Aug/2025:17:22:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755163363635 HTTP/1.1" 200 159
************ - - [14/Aug/2025:17:26:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755163577642 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:26:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755163577642 HTTP/1.1" 200 160
************ - - [14/Aug/2025:17:26:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755163589754 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:26:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755163589754 HTTP/1.1" 200 161
************ - - [14/Aug/2025:17:26:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:26:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:26:30 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:26:30 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755163590645 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:26:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:17:26:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:17:26:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755163590645 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:17:26:31 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:17:27:44 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755163664635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:27:44 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755163664635 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:27:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755163664636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:27:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755163664636 HTTP/1.1" 200 159
************ - - [14/Aug/2025:17:27:44 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755163664635 HTTP/1.1" 200 152
************ - - [14/Aug/2025:17:27:44 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755163664635 HTTP/1.1" 200 148
************ - - [14/Aug/2025:17:31:42 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755163902757 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:31:42 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1755163902757 HTTP/1.1" 200 161
************ - - [14/Aug/2025:17:32:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1755163962644 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:32:42 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:32:42 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:32:42 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755163962646 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:32:42 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:32:42 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [14/Aug/2025:17:32:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1755163962644 HTTP/1.1" 200 160
************ - - [14/Aug/2025:17:32:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Aug/2025:17:32:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-14+08:00&etm=2025-08-14+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1755163962646 HTTP/1.1" 200 441332
************ - - [14/Aug/2025:17:32:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [14/Aug/2025:17:32:45 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1755163965636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:32:45 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1755163965636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:32:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1755163965636 HTTP/1.1" 200 -
************ - - [14/Aug/2025:17:32:45 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1755163965636 HTTP/1.1" 200 152
************ - - [14/Aug/2025:17:32:45 +0800] "GET /api/ew/warning/is-enabled?_timer304=1755163965636 HTTP/1.1" 200 148
************ - - [14/Aug/2025:17:32:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1755163965636 HTTP/1.1" 200 159
