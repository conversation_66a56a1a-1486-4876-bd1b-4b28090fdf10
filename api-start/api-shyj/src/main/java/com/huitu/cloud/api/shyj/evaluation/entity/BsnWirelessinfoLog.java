package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警设备日志信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-19
 */
@TableName("BSN_WIRELESSINFO_LOG")
@ApiModel(value="BsnWirelessinfoLog对象", description="预警设备日志信息表")
public class BsnWirelessinfoLog extends Model<BsnWirelessinfoLog> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "广播站编码")
    @TableId(value = "RACD", type = IdType.NONE)
    private String racd;

    @ApiModelProperty(value = "播报时间")
    @TableField("RADT")
    private LocalDateTime radt;

    @ApiModelProperty(value = "类型(0-短信，1-电话，2-紧急告警信号，3-本地语言，4-控制命令，5-平安报)")
    @TableField("LOG_TP")
    private String logTp;

    @ApiModelProperty(value = "呼入号码")
    @TableField("IN_NUM")
    private String inNum;

    @ApiModelProperty(value = "内容")
    @TableField("CONN")
    private String conn;

    @ApiModelProperty(value = "接收时间")
    @TableField("SYSTM")
    private LocalDateTime systm;


    public String getRacd() {
        return racd;
    }

    public void setRacd(String racd) {
        this.racd = racd;
    }

    public LocalDateTime getRadt() {
        return radt;
    }

    public void setRadt(LocalDateTime radt) {
        this.radt = radt;
    }

    public String getLogTp() {
        return logTp;
    }

    public void setLogTp(String logTp) {
        this.logTp = logTp;
    }

    public String getInNum() {
        return inNum;
    }

    public void setInNum(String inNum) {
        this.inNum = inNum;
    }

    public String getConn() {
        return conn;
    }

    public void setConn(String conn) {
        this.conn = conn;
    }

    public LocalDateTime getSystm() {
        return systm;
    }

    public void setSystm(LocalDateTime systm) {
        this.systm = systm;
    }

    @Override
    protected Serializable pkVal() {
        return this.racd;
    }

    @Override
    public String toString() {
        return "BsnWirelessinfoLog{" +
        "racd=" + racd +
        ", radt=" + radt +
        ", logTp=" + logTp +
        ", inNum=" + inNum +
        ", conn=" + conn +
        ", systm=" + systm +
        "}";
    }
}
