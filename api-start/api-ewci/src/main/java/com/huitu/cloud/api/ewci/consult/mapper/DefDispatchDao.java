package com.huitu.cloud.api.ewci.consult.mapper;

import com.huitu.cloud.api.ewci.consult.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 防汛会商-防御调度
 * </p>
 *
 * <AUTHOR>
 */
public interface DefDispatchDao {

    /**
     * 作战图 超标准洪水淹没范围信息
     * @param rvCode
     * @return
     */
    RiverAffectadVo getRiverAffectadVo(@Param("rvCode") String rvCode);

    /**
     * 作战图 超标准洪水淹没范围信息 详细数据 县市
     * @param rvCode
     * @return
     */
    List<RiverAffectadDetailsVo> getRiverAffectadDetailsList(@Param("rvCode") String rvCode);

    /**
     * 作战图 超标准洪水淹没范围信息 详细数据
     * @param rvCode
     * @return
     */
    List<RiverAffectadDetailsChildVo> getRiverAffectadDetailsChildList(@Param("rvCode") String rvCode);

    /**
     * 作战图 全省超标准洪水淹没范围信息 详细数据
     * @return
     */
    List<RiverAffectadDetailsChildVo> getRiverAffectadDetailsAllList();

    /**
     * 作战图 提防统计 堤防情况
     * @param rvCode
     * @return
     */
    RiverDikeVo getRiverDikeVo(@Param("rvCode") String rvCode);


    /**
     * 作战图 提防统计 沙基、沙堤情况
     * @param rvCode
     * @return
     */
    RiverDikeVo getRiverBaseDikeVo(@Param("rvCode") String rvCode);

    /**
     * 作战图 提防统计 险工险段情况
     * @param rvCode
     * @return
     */
    RiverDikeVo getRiverDpdsVo(@Param("rvCode") String rvCode);

    /**
     * 作战图 提防统计详细数据 堤防政区
     * @param rvCode
     * @return
     */
    List<RiverDikeDetailsVo> getRiverDikeDetailsList(@Param("rvCode") String rvCode);

    /**
     * 作战图 提防统计详细数据 堤防情况
     * @param rvCode
     * @return
     */
    List<RiverDikeDetailsChildVo> getRiverDikeDetailsChildList(@Param("rvCode") String rvCode);

    /**
     * 作战图 全省提防统计详细数据 堤防情况
     * @return
     */
    List<RiverDikeDetailsChildVo> getRiverDikeDetailsAllList();

    /**
     * 作战图 提防统计详细数据 沙基沙堤
     * @param rvCode
     * @return
     */
    List<RiverBaseDikeVo> getRiverBaseDikeList(@Param("rvCode") String rvCode);

    /**
     * 作战图 全省提防统计详细数据 沙基沙堤
     * @return
     */
    List<RiverBaseDikeVo> getRiverBaseDikeAllList();

    /**
     * 作战图 提防统计详细数据 险工险段
     * @param rvCode
     * @return
     */
    List<RiverDpdsVo> getRiverDpdsList(@Param("rvCode") String rvCode);

    /**
     * 作战图 全省提防统计详细数据 险工险段
     * @return
     */
    List<RiverDpdsVo> getRiverDpdsAllList();

    /**
     * 作战图 穿堤建筑统计
     * @param rvCode
     * @return
     */
    RiverDtgcVo getRiverDtgcVo(@Param("rvCode") String rvCode);

    /**
     * 作战图 穿堤建筑统计详细数据
     * @param rvCode
     * @return
     */
    List<RiverDtgcVo> getRiverDtgcList(@Param("rvCode") String rvCode);

    /**
     * 作战图 全省穿堤建筑统计详细数据
     * @return
     */
    List<RiverDtgcVo> getRiverDtgcAllList();

    /**
     * 作战图 设计洪峰流量
     * @param rvCode
     * @return
     */
    List<RiverDesingVo> getRiverDesingVo(@Param("rvCode") String rvCode);

    /**
     * 作战图 洪水传播时间
     * @param rvCode
     * @return
     */
    List<RiverProVo> getRiverProList(@Param("rvCode") String rvCode);

    /**
     * 水库图层
     * @param param
     * @return
     */
    List<RsvrInfoVo> getRsvrInfoVoList(@Param("map") Map<String, Object> param);

    /**
     *
     * 河道图层
     * @param param
     * @return
     */
    List<RiverInfoVo> getRiverInfoVoList(@Param("map") Map<String, Object> param);

    /**
     * 获取河流四角坐标
     * @param rvCode
     * @return
     */
    BnsRiverBas getBnsRiverBas(@Param("rvCode") String rvCode);
}

