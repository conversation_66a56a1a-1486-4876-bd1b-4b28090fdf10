package com.huitu.cloud.api.syq.rsvr.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 调度预报成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-11
 */
@TableName("ST_REGLAT_F")
@ApiModel(value="StReglatF对象", description="调度预报成果表")
public class StReglatF extends Model<StReglatF> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "预报单位")
    @TableField("UNITNAME")
    private String unitname;

    @ApiModelProperty(value = "方案代码")
    @TableField("PLCD")
    private String plcd;

    @ApiModelProperty(value = "依据时间")
    @TableField("FYMDH")
    private LocalDateTime fymdh;

    @ApiModelProperty(value = "发布时间")
    @TableField("IYMDH")
    private LocalDateTime iymdh;

    @ApiModelProperty(value = "发生时间")
    @TableField("YMDH")
    private LocalDateTime ymdh;

    @ApiModelProperty(value = "预报水位")
    @TableField("Z")
    private BigDecimal z;

    @ApiModelProperty(value = "预报蓄水量")
    @TableField("W")
    private BigDecimal w;

    @ApiModelProperty(value = "预报出流")
    @TableField("OTQ")
    private BigDecimal otq;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getUnitname() {
        return unitname;
    }

    public void setUnitname(String unitname) {
        this.unitname = unitname;
    }

    public String getPlcd() {
        return plcd;
    }

    public void setPlcd(String plcd) {
        this.plcd = plcd;
    }

    public LocalDateTime getFymdh() {
        return fymdh;
    }

    public void setFymdh(LocalDateTime fymdh) {
        this.fymdh = fymdh;
    }

    public LocalDateTime getIymdh() {
        return iymdh;
    }

    public void setIymdh(LocalDateTime iymdh) {
        this.iymdh = iymdh;
    }

    public LocalDateTime getYmdh() {
        return ymdh;
    }

    public void setYmdh(LocalDateTime ymdh) {
        this.ymdh = ymdh;
    }

    public BigDecimal getZ() {
        return z;
    }

    public void setZ(BigDecimal z) {
        this.z = z;
    }

    public BigDecimal getW() {
        return w;
    }

    public void setW(BigDecimal w) {
        this.w = w;
    }

    public BigDecimal getOtq() {
        return otq;
    }

    public void setOtq(BigDecimal otq) {
        this.otq = otq;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "StReglatF{" +
        "stcd=" + stcd +
        ", unitname=" + unitname +
        ", plcd=" + plcd +
        ", fymdh=" + fymdh +
        ", iymdh=" + iymdh +
        ", ymdh=" + ymdh +
        ", z=" + z +
        ", w=" + w +
        ", otq=" + otq +
        "}";
    }
}
