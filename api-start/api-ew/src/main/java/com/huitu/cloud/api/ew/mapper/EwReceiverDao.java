package com.huitu.cloud.api.ew.mapper;

import com.huitu.cloud.api.ew.entity.base.EwReceiver;
import com.huitu.cloud.api.ew.entity.ext.EwSmsReceiver;
import com.huitu.cloud.api.ew.entity.ext.EwXccReceiver;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwReceiverDao {
    /**
     * 根据预警ID，获取预警短信接收人列表
     *
     * @param warnId 预警ID
     * @return 短信接收人列表
     */
    List<EwSmsReceiver> getSmsReceiverList(String warnId);

    /**
     * 根据预警ID，获取预警语音接收人列表
     *
     * @param warnId 预警ID
     * @return 语音接收人列表
     */
    List<EwXccReceiver> getXccReceiverList(String warnId);

    /**
     * 批量插入预警接收人
     *
     * @param receivers 预警接收人集合
     * @return 受影响的行数
     */
    int batchInsert(@Param("list") List<EwReceiver> receivers);
}
