package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 强降雨告警通告生成参数
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-29
 */
@ApiModel(value="QueryNotice对象", description="强降雨告警通告生成参数")
public class QueryNotice extends BsnHeavyrainfallnoticeB{
    @ApiModelProperty(value = "市名称")
    private String sadnm;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "政区编码")
    private String adcd;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getSadnm() {
        return sadnm;
    }

    public void setSadnm(String sadnm) {
        this.sadnm = sadnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
