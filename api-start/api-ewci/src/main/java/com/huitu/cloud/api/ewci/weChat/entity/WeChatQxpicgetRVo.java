package com.huitu.cloud.api.ewci.weChat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 气象云图抓取记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@TableName("BNS_QXPICGET_R")
@ApiModel(value="BnsQxpicgetR对象", description="气象云图抓取记录表")
public class WeChatQxpicgetRVo extends Model<WeChatQxpicgetRVo> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "图片编码")
    @TableId(value = "picid", type = IdType.NONE)
    private String picid;

    @ApiModelProperty(value = "抓取时间")
    private LocalDateTime tm;

    @ApiModelProperty(value = "图片类型 类型 IR1 ：红外线 VIS：可见光  WAT:水汽 GMS:立体图")
    private String pictp;

    @ApiModelProperty(value = "图片路径")
    private String picurl;

    @ApiModelProperty(value = "入库时间")
    private LocalDateTime moditime;


    public String getPicid() {
        return picid;
    }

    public void setPicid(String picid) {
        this.picid = picid;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getPictp() {
        return pictp;
    }

    public void setPictp(String pictp) {
        this.pictp = pictp;
    }

    public String getPicurl() {
        return picurl;
    }

    public void setPicurl(String picurl) {
        this.picurl = picurl;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.picid;
    }

    @Override
    public String toString() {
        return "BnsQxpicgetR{" +
        "picid=" + picid +
        ", tm=" + tm +
        ", pictp=" + pictp +
        ", picurl=" + picurl +
        ", moditime=" + moditime +
        "}";
    }
}
