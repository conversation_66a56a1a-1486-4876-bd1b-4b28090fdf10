package com.huitu.cloud.api.datamanager.anmrecord.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmByStcdEdit;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmEdit;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmStatistics;
import com.huitu.cloud.api.datamanager.anmrecord.entity.AnmVo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.BsnAnmrecordr;
import com.huitu.cloud.api.datamanager.anmrecord.entity.RzStInfo;
import com.huitu.cloud.api.datamanager.anmrecord.entity.StSwVo;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 异常数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-07
 */
public interface AnmrecordService extends IService<BsnAnmrecordr> {
    /**
     * 根据政区查询雨量数据列表
     *
     * @param adcd    政区
     * @param staflgs 数据状态
     * @param stm 开始时间
     * @param etm 结束时间
     * @return
     */
    List<AnmVo> getAnmRainRecord(String adcd, String staflgs, String stm, String etm,Integer filterCnt);

    /**
     * 根据政区查询水情异常数据
     *
     * @param adcd    政区
     * @param staflgs 数据状态
     * @param stm 开始时间
     * @param etm 结束时间
     * @param filterCnt 异常数据累计数量过滤值
     * @return
     */
    List<AnmVo> getZrAnmRecord(String adcd, String staflgs, String stm, String etm, int filterCnt);

    /**
     * 异常数据处理  包含修改，初审，终审，驳回操作
     *
     * @param anmEdit
     * @return
     */
    boolean editAnmRecord(AnmEdit anmEdit);

    /**
     * 查询河道或水库最新的5条水位数据
     *
     * @param stcd 测站编码
     * @param tbnm 数据表名
     * @return
     */
    List<StSwVo> getLatelySw(String stcd, String tbnm);

    /**
     * 查询某个时间点政区的平均政区
     *
     * @param adcd 政区编码
     * @param tm   时间
     * @return
     */
    String getAdAvg(String adcd, String tm);

    /**
     * 异常站点相关的异常数据忽略屏蔽
     *
     * @param stcd 测站编码
     * @param tbnm 屏蔽要素 雨量：st_pptn_r , 河道水位：st_river_r, 水库水位：st_rsvr_r
     * @return
     */
    String updateStaflg(String stcd, String tbnm);

    /**
     * 根据政区查询异常雨量站点
     *
     * @param adcd 政区
     * @return
     */
    List<AnmVo> getAnmRainStcd(String adcd);

    /**
     * 根据政区分页查询雨量数据列表
     *
     * @param adcd     政区
     * @param staflgs  异常数据状态
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return
     */
    IPage<AnmVo> getAnmRainRecordByPage(String adcd, String staflgs, int pageNum, int pageSize);

    /**
     * 根据政区分页查询雨量数据列表
     *
     * @param adcd     政区
     * @param staflgs  异常数据状态
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return
     */
    IPage<AnmVo> getAnmSwRecordByPage(String adcd, String staflgs, int pageNum, int pageSize);

    /**
     * 根据政区分页查询雨量数据列表
     *
     * @param stcd     测站编码
     * @param stm      开始时间
     * @param etm      结束时间
     * @param staflgs  异常数据状态
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return
     */
    IPage<AnmVo> getAnmSwRecordStcdByPage(String stcd, String stm, String etm, String staflgs, int pageNum, int pageSize);

    /**
     * 查询政区存在异常数据数量
     *
     * @param adcd    政区
     * @param staflgs 状态
     * @return
     */
    int getRecordCount(String adcd, String staflgs);

    /**
     * 根据政区分页查询水位异常站点列表
     *
     * @param adcd     政区
     * @param anmtp    异常类型
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return
     */
    IPage<RzStInfo> getAnmRzSt(String adcd, String anmtp, int pageNum, int pageSize);

    /**
     * 异常数据批量处理处理  包含修改，初审，终审，驳回操作
     *
     * @param entity
     * @return
     */
    Boolean updateBsnAnmrecordr(AnmByStcdEdit entity);

    /**
     * 获取异常站数列表
     *
     * @param adcd  政区编码
     * @param bgtm  开始时间
     * @param endtm 结束时间
     * @return java.util.List<com.huitu.cloud.api.datamanager.anmrecord.entity.AnmStatistics>
     **/
    List<AnmStatistics> getAnmStatisticsList(String adcd, Date bgtm, Date endtm);

    /**
     * 导出水情异常数据
     * @param adcd    政区
     * @param staflgs 数据状态
     * @param stm 开始时间
     * @param etm 结束时间
     * @param response 导出
     */
    void exportZrAnmRecord(String adcd, String staflgs, String stm, String etm, int filterCnt, HttpServletResponse response);

    /**
     * 导出雨情异常数据
     * @param adcd    政区
     * @param staflgs 数据状态
     * @param stm 开始时间
     * @param etm 结束时间
     * @param response 导出
     */
    void exportAnmRainRecord(String adcd, String staflgs, String stm, String etm, HttpServletResponse response);

}
