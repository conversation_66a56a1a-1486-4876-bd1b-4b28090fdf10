package com.huitu.cloud.api.ewci.constants;

import com.huitu.cloud.constants.CommConstants;

public interface SyqConstants {
    /**
     * 公共常量
     */
    class Public {

    }

    /**
     * 雨情常量
     */
    class RainConstants {
        /**
         * 吉林省政区编码
         */
        public static final String PROVINCEADCD = CommConstants.Public.PROVINCE_ADCD;
        /**
         * 雨量查询类型：queryRainTypeTwo：查询雨量大于零的所有站点  queryRainTypeOne：查询一定时间内所有上报雨量信息的站  queryRainTypeZero：查询所有雨量站信息
         */
        public static final String QUERYRAINTYPETWO = "2";
        public static final String QUERYRAINTYPEONE = "1";
        public static final String QUERYRAINTYPEZERO = "0";
    }

    /**
     * 水库常量
     */
    class RsvrConstants {
        /**
         * 水库类型 rsvrBigTypeOne：大1型水库  rsvrBigTypeTwo：大2型水库  rsvrMiddleType：中型水库
         * rsvrSmallTypeOne：小1型水库 rsvrSmallTypeTwo：小2型水库
         */
        public static final String RSVRBIGTYPEONE = "5";
        public static final String RSVRBIGTYPETWO = "4";
        public static final String RSVRMIDDLETYPE = "3";
        public static final String RSVRSMALLTYPEONE = "2";
        public static final String RSVRSMALLTYPETWO = "1";

    }

    /**
     * 视频监控站类型常量
     */
    class VideoConstants {
        /**
         * 水库类型 videoTypeEleven：大型水库  videoTypeTwelve:中型水库  videoTypeThirteen:小型水库  videoTypeTwo:主要江河
         * videoTypeThree:山洪沟  videoTypeFour:防洪城市  videoTypeSix:仓库
         */
        public static final String VIDEOTYPETWELVE = "12";
        public static final String VIDEOTYPEELEVEN = "11";
        public static final String VIDEOTYPETHIRTEEN = "13";
        public static final String VIDEOTYPETWO = "2";
        public static final String VIDEOTYPETHREE = "3";
        public static final String VIDEOTYPEFOUR = "4";
        public static final String VIDEOTYPESIX = "6";
    }


}
