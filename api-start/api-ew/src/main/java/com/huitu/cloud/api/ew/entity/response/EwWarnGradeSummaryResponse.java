package com.huitu.cloud.api.ew.entity.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description：预警等级汇总统计信息响应对象
 * @date ：2025/8/4 14:11
 */
@Data
@ApiModel(description = "预警等级汇总统计信息响应对象")
public class EwWarnGradeSummaryResponse {


    @ApiModelProperty(value = "预警总次数")
    private int totalWarnCount;

    @ApiModelProperty(value = "预警总人次")
    private int totalPcount;

    @ApiModelProperty(value = "语音外呼总人次")
    private int totalVoiceCount;

    @ApiModelProperty(value = "山洪预警次数")
    private int shWarnCount;

    @ApiModelProperty(value = "水库预警次数")
    private int rsvrWarnCount;

    @ApiModelProperty(value = "河道预警次数")
    private int riverWarnCount;

    @ApiModelProperty(value = "山洪预警人次")
    private int shPcount;

    @ApiModelProperty(value = "水库预警人次")
    private int rsvrPcount;

    @ApiModelProperty(value = "河道预警人次")
    private int riverPcount;

    @ApiModelProperty(value = "山洪反馈人次")
    private int shFcount;

    @ApiModelProperty(value = "行政区划代码（15位）")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    private String adnm;

    @ApiModelProperty(value = "一级预警数")
    private int grade1Count;

    @ApiModelProperty(value = "二级预警数")
    private int grade2Count;

    @ApiModelProperty(value = "三级预警数")
    private int grade3Count;

    @ApiModelProperty(value = "一级预警短信发送数")
    private int grade1SmsCount;

    @ApiModelProperty(value = "二级预警短信发送数")
    private int grade2SmsCount;

    @ApiModelProperty(value = "三级预警短信发送数")
    private int grade3SmsCount;

    @ApiModelProperty(value = "一级预警语音发送数")
    private int grade1VoiceCount;

    @ApiModelProperty(value = "二级预警语音发送数")
    private int grade2VoiceCount;

    @ApiModelProperty(value = "三级预警语音发送数")
    private int grade3VoiceCount;

    @ApiModelProperty(value = "一级预警语音反馈数")
    private int grade1VoiceRead;

    @ApiModelProperty(value = "二级预警语音反馈数")
    private int grade2VoiceRead;

    @ApiModelProperty(value = "三级预警语音反馈数")
    private int grade3VoiceRead;




}
