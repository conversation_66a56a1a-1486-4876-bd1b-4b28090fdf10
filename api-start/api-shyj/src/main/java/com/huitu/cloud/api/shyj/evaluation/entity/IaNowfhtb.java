package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 防洪现状评价成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@TableName("IA_A_NOWFHTB")
@ApiModel(value="IaANowfhtb对象", description="防洪现状评价成果表")
public class IaNowfhtb extends Model<IaNowfhtb> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableId(value = "ADCD", type = IdType.NONE)
    private String adcd;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "断面代码")
    @TableField("HSCD")
    private String hscd;

    @ApiModelProperty(value = "防洪能力(年)")
    @TableField("FCA")
    private Double fca;

    @ApiModelProperty(value = "极高程度危险区人口(人)")
    @TableField("MXPN")
    private Double mxpn;

    @ApiModelProperty(value = "极高程度危险区房屋(座)")
    @TableField("MXHN")
    private Double mxhn;

    @ApiModelProperty(value = "高危程度危险区人口(人)")
    @TableField("SENPN")
    private Double senpn;

    @ApiModelProperty(value = "高危程度危险区房屋(座)")
    @TableField("SENHN")
    private Double senhn;

    @ApiModelProperty(value = "危险程度危险区人口(人)")
    @TableField("WXPN")
    private Double wxpn;

    @ApiModelProperty(value = "危险程度危险区房屋(座)")
    @TableField("WXHN")
    private Double wxhn;

    @ApiModelProperty(value = "导入人")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;
    @ApiModelProperty(value = "流域名称")
    private String wsnm;
    @ApiModelProperty(value = "政区名称")
    private String adnm;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getHscd() {
        return hscd;
    }

    public void setHscd(String hscd) {
        this.hscd = hscd;
    }

    public Double getFca() {
        return fca;
    }

    public void setFca(Double fca) {
        this.fca = fca;
    }

    public Double getMxpn() {
        return mxpn;
    }

    public void setMxpn(Double mxpn) {
        this.mxpn = mxpn;
    }

    public Double getMxhn() {
        return mxhn;
    }

    public void setMxhn(Double mxhn) {
        this.mxhn = mxhn;
    }

    public Double getSenpn() {
        return senpn;
    }

    public void setSenpn(Double senpn) {
        this.senpn = senpn;
    }

    public Double getSenhn() {
        return senhn;
    }

    public void setSenhn(Double senhn) {
        this.senhn = senhn;
    }

    public Double getWxpn() {
        return wxpn;
    }

    public void setWxpn(Double wxpn) {
        this.wxpn = wxpn;
    }

    public Double getWxhn() {
        return wxhn;
    }

    public void setWxhn(Double wxhn) {
        this.wxhn = wxhn;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.adcd;
    }

    @Override
    public String toString() {
        return "IaNowfhtb{" +
                "adcd='" + adcd + '\'' +
                ", wscd='" + wscd + '\'' +
                ", hscd='" + hscd + '\'' +
                ", fca=" + fca +
                ", mxpn=" + mxpn +
                ", mxhn=" + mxhn +
                ", senpn=" + senpn +
                ", senhn=" + senhn +
                ", wxpn=" + wxpn +
                ", wxhn=" + wxhn +
                ", signer='" + signer + '\'' +
                ", audid='" + audid + '\'' +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                ", moditime=" + moditime +
                ", wsnm='" + wsnm + '\'' +
                ", adnm='" + adnm + '\'' +
                '}';
    }
}
