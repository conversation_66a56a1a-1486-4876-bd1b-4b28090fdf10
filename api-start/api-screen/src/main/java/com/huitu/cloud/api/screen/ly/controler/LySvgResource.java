package com.huitu.cloud.api.screen.ly.controler;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.api.SuccessResponse;
import com.huitu.cloud.api.screen.ly.service.SvgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 一体机大屏流域SVG江河概化图 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-16
 */

@RestController
@Api(tags = "一体机大屏流域SVG")
@RequestMapping("/api/screen/svg")
public class LySvgResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "B761C064-1554-4164-80CA-B52A17E42144";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private SvgService baseService;

    @ApiOperation(value = "查询江河概化图SVG中水库展示信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resCodeList", value = "水库编码列表", required = true, dataType = "list")
    })
    @PostMapping(value = "queryRsvrInfo")
    public ResponseEntity<SuccessResponse<List>> queryRsvrInfo(@RequestBody List<String> resCodeList) throws Exception {
        List<Map<String,Object>> result= baseService.queryRsvrInfo(resCodeList);
        return ResponseEntity.ok(
                new SuccessResponse(result));

    }

    @ApiOperation(value = "查询江河概化图SVG中河道站水位信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resCodeList", value = "河道站测站编码列表", required = true, dataType = "list")
    })
    @PostMapping(value = "queryRiverInfo")
    public ResponseEntity<SuccessResponse<List>> queryRiverInfo(@RequestBody List<String> resCodeList) throws Exception {
        List<Map<String,Object>> result= baseService.queryRiverInfo(resCodeList);
        return ResponseEntity.ok(
                new SuccessResponse(result));

    }

    @ApiOperation(value = "查询江河概化图SVG中堤防信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resCodeList", value = "堤防编码列表", required = true, dataType = "list")
    })
    @PostMapping(value = "queryDikeInfo")
    public ResponseEntity<SuccessResponse<List>> queryDikeInfo(@RequestBody List<String> resCodeList) throws Exception {
        List<Map<String,Object>> result= baseService.queryDikeInfo(resCodeList);
        return ResponseEntity.ok(
                new SuccessResponse(result));

    }

    @ApiOperation(value = "查询河流的洪水调度信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvCode", value = "河流编码", required = true, dataType = "String")
    })
    @GetMapping(value = "queryFloodControlList")
    public ResponseEntity<SuccessResponse<List>> queryFloodControlList(@RequestParam("rvCode") String rvCode,@RequestParam("type") String type){
        List<Map<String,Object>> result= baseService.queryFloodControlList(rvCode.replace(" ",""),type);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }
}







