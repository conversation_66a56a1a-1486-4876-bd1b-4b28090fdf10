package com.huitu.cloud.api.ewci.hydfcst.entity;


import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 返回河道 Map 包装类
 *
 * <AUTHOR>
 */
public class StRV implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点编码")
    private String stcd;   // 站点编码

    @ApiModelProperty(value = "预报水位")
    private BigDecimal z;  // 水位

    @ApiModelProperty(value = "预报流量")
    private BigDecimal q;  // 流量

    @ApiModelProperty(value = "发生时间")
    private String ymdh;   // 时间戳（年月日时）

    @ApiModelProperty(value = "站点名称")
    private String stnm;   // 站点名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "河流名称")
    private String rvnm;   // 河流名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "水文节点名称")
    private String hnnm;   // 水文节点名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "流域名称")
    private String bsnm;   // 水文节点名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "行政区划代码")
    private String adcd;   // 行政区划代码（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "行政区划名称")
    private String adnm;   // 行政区划名称（来自BSN_STBPRP_V）

    @ApiModelProperty(value = "警戒水位")
    private BigDecimal wrz;  // 水位（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "警戒流量")
    private BigDecimal wrq;  // 流量（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "保证水位")
    private BigDecimal grz;  // 另一水位或水尺读数（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "保证流量")
    private BigDecimal grq;  // 另一流量或水尺读数（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;  // 另一流量或水尺读数（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;  // 另一流量或水尺读数（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "纠偏后经度")
    private BigDecimal plgtd;  // 另一流量或水尺读数（来自ST_RVFCCH_B）

    @ApiModelProperty(value = "纠偏后纬度")
    private BigDecimal plttd;  // 另一流量或水尺读数（来自ST_RVFCCH_B）

    private String tmYmdh;
    private String stlc;
    private String sttp;
    private String frgrd;
    private String stazt;
    private String xadcd;
    private String xadnm;
    private String ldkel;
    private String rdke;


    public StRV(String stcd, BigDecimal z, BigDecimal q, String ymdh, String stnm, String rvnm, String hnnm,
                String bsnm, String adcd, String adnm, BigDecimal wrz, BigDecimal wrq, BigDecimal grz, BigDecimal grq,
                BigDecimal lgtd, BigDecimal lttd, BigDecimal plgtd, BigDecimal plttd, String tmYmdh
            , String stlc
            , String sttp
            , String frgrd
            , String stazt
            , String xadcd
            , String xadnm
            , String ldkel
            , String rdke) {
        this.stcd = stcd;
        this.z = z;
        this.q = q;
        this.ymdh = ymdh;
        this.stnm = stnm;
        this.rvnm = rvnm;
        this.hnnm = hnnm;
        this.bsnm = bsnm;
        this.adcd = adcd;
        this.adnm = adnm;
        this.wrz = wrz;
        this.wrq = wrq;
        this.grz = grz;
        this.grq = grq;
        this.lgtd = lgtd;
        this.lttd = lttd;
        this.plgtd = plgtd;
        this.plttd = plttd;
        this.tmYmdh = tmYmdh;
        this.stlc = stlc;
        this.sttp = sttp;
        this.frgrd = frgrd;
        this.stazt = stazt;
        this.xadcd = xadcd;
        this.xadnm = xadnm;
        this.ldkel = ldkel;
        this.rdke = rdke;
    }

    public String getTmYmdh() {
        return tmYmdh;
    }

    public void setTmYmdh(String tmYmdh) {
        this.tmYmdh = tmYmdh;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getFrgrd() {
        return frgrd;
    }

    public void setFrgrd(String frgrd) {
        this.frgrd = frgrd;
    }

    public String getStazt() {
        return stazt;
    }

    public void setStazt(String stazt) {
        this.stazt = stazt;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getLdkel() {
        return ldkel;
    }

    public void setLdkel(String ldkel) {
        this.ldkel = ldkel;
    }

    public String getRdke() {
        return rdke;
    }

    public void setRdke(String rdke) {
        this.rdke = rdke;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public BigDecimal getZ() {
        return z;
    }

    public void setZ(BigDecimal z) {
        this.z = z;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public String getYmdh() {
        return ymdh;
    }

    public void setYmdh(String ymdh) {
        this.ymdh = ymdh;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getHnnm() {
        return hnnm;
    }

    public void setHnnm(String hnnm) {
        this.hnnm = hnnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public BigDecimal getWrz() {
        return wrz;
    }

    public void setWrz(BigDecimal wrz) {
        this.wrz = wrz;
    }

    public BigDecimal getWrq() {
        return wrq;
    }

    public void setWrq(BigDecimal wrq) {
        this.wrq = wrq;
    }

    public BigDecimal getGrz() {
        return grz;
    }

    public void setGrz(BigDecimal grz) {
        this.grz = grz;
    }

    public BigDecimal getGrq() {
        return grq;
    }

    public void setGrq(BigDecimal grq) {
        this.grq = grq;
    }

    @Override
    public String toString() {
        return "StRV{" +
                "stcd='" + stcd + '\'' +
                ", z=" + z +
                ", q=" + q +
                ", ymdh='" + ymdh + '\'' +
                ", stnm='" + stnm + '\'' +
                ", rvnm='" + rvnm + '\'' +
                ", hnnm='" + hnnm + '\'' +
                ", adcd='" + adcd + '\'' +
                ", adnm='" + adnm + '\'' +
                ", wrz=" + wrz +
                ", wrq=" + wrq +
                ", grz=" + grz +
                ", grq=" + grq +
                '}';
    }
}

