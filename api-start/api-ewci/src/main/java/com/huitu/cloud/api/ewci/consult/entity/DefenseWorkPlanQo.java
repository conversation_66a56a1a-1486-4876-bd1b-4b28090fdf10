package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 防御工作方案功能查询
 *
 * <AUTHOR>
 */
@ApiModel(value = "防御工作方案功能查询")
public class DefenseWorkPlanQo<T> extends PageBean {

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String stm;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String etm;

    @ApiModelProperty(value = "防御工作方案名称")
    private String dwpName;

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getDwpName() {
        return dwpName;
    }

    public void setDwpName(String dwpName) {
        this.dwpName = dwpName;
    }

    public Map<String, Object> toQueryParam() {
        Map<String, Object> params = new HashMap<>();
        params.put("stm", getStm());
        params.put("etm", getEtm());
        params.put("dwpName", getDwpName());
        return params;
    }

    public IPage<T> toPageParam() {
        return new Page<>(Math.max(getPageNum(), 1), getPageSize() > 0 ? getPageSize() : -1);
    }
}
