package com.huitu.cloud.api.common.msg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 预警消息记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("MESSAGEINFO_R")
@ApiModel(value="消息记录表", description="消息记录表")
public class MessageInfo extends Model<MessageInfo> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "编号 发送无需传值")
    @TableId(value = "ID", type = IdType.NONE)
    private BigDecimal id;

    @ApiModelProperty(value = "短信编号， 发送无需传值")
    @TableField("MSGID")
    private Integer msgId;

    @ApiModelProperty(value = " 预警编号")
    @TableField("WARNID")
    private String warnId;

    @ApiModelProperty(value = "短信类型编号 10-内部预警消息； 20-外部预警消息 ")
    @TableField("MSGTYPEID")
    private Integer msgTypeId;

    @ApiModelProperty(value = "短信内容 ")
    @TableField("MSGCONTENT")
    private String msgContent;

    @ApiModelProperty(value = " 短信发送人")
    @TableField("SENDER")
    private String sender;

    @ApiModelProperty(value = " 短信发送方式 10-短信； 20-传真； 30-广播  ")
    @TableField("MEDIAID")
    private String mediaId;

    @ApiModelProperty(value = " 短信发出时间  ")
    @TableField("SENDTM")
    private Date sendTm;

    @ApiModelProperty(value = "备注  ")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = " 发送手段 1 网关  0 猫池")
    @TableField("SENDTYPE")
    private BigDecimal sendType;

    @ApiModelProperty(value = " 所属政区  ")
    @TableField("ADCD")
    private String adcd;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public Integer getMsgId() {
        return msgId;
    }

    public void setMsgId(Integer msgId) {
        this.msgId = msgId;
    }

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public Integer getMsgTypeId() {
        return msgTypeId;
    }

    public void setMsgTypeId(Integer msgTypeId) {
        this.msgTypeId = msgTypeId;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getMediaId() {
        return mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public Date getSendTm() {
        return sendTm;
    }

    public void setSendTm(Date sendTm) {
        this.sendTm = sendTm;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getSendType() {
        return sendType;
    }

    public void setSendType(BigDecimal sendType) {
        this.sendType = sendType;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "MessageInfo{" +
        "id=" + id +
        ", msgid=" + msgId +
        ", warnid=" + warnId +
        ", msgtypeid=" + msgTypeId +
        ", msgcontent=" + msgContent +
        ", sender=" + sender +
        ", mediaid=" + mediaId +
        ", sendtm=" + sendTm +
        ", remark=" + remark +
        ", sendtype=" + sendType +
        ", adcd=" + adcd +
        "}";
    }
}
