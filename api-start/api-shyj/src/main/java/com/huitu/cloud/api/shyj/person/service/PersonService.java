package com.huitu.cloud.api.shyj.person.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.shyj.person.entity.Person;
import com.huitu.cloud.api.shyj.person.entity.RelevantPersionInfo;

import java.util.List;

/**
 * <p>
 * 人员信息  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
public interface PersonService extends IService<Person> {
    /**
     * 分页查询人员信息
     *
     * @param adcd     政区编码
     * @param name     人员名称
     * @param depts    部门编码数组
     * @param mobile   手机号
     * @param type     值班人员（1：值班人员，0：非值班人员）
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<RelevantPersionInfo> getPersionList(String adcd, String name, List<String> depts, String mobile, String type, int pageNum, int pageSize);
    /**
     * 添加人员信息
     *
     * @param entity 人员实体类
     * @return 1 成功；0 失败
     */
    String addPerson(Person entity);
    /**
     * 修改人员信息
     *
     * @param entity 人员实体类
     * @return 1 成功；0 失败
     */
    String updatePerson(Person entity);
    /**
     * 删除人员信息
     *
     * @param personcd 人员编码
     * @return 1 成功；0 失败
     */
    String delPerson(String personcd);
}
