<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ia.mapper.BnsIaWatsuplDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.ia.entity.BnsIaWatsupl" useCache="false">
        SELECT IA.ADCD, AD.ADNM, YR, WRRT, RESACWS, DCACWS, DSACWS, ZGACWS, OTHACWS, ACWST, RRACWC, CTACWC, AGACWC,
        INACWC, OTHACWC, EEACWC, ACWCT, TS
        FROM BNS_IA_WATSUPL IA
        LEFT JOIN BSN_ADCD_B AD ON AD.ADCD = IA.ADCD
        WHERE LEFT(IA.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.yr != null and map.yr != ''">
            AND YR = #{map.yr}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(IA.ADCD, 6) NOT IN ('220581')
        </if>
        ORDER BY IA.ADCD, IA.YR ASC
    </select>
    <select id="getListByAdcd" resultType="com.huitu.cloud.api.ewci.ia.entity.BnsIaWatsupl">
        SELECT IA.ADCD,
               AD.ADNM,
               YR,
               WRRT,
               RESACWS,
               DCACWS,
               DSACWS,
               ZGACWS,
               OTHACWS,
               ACWST,
               RRACWC,
               CTACWC,
               AGACWC,
               INACWC,
               OTHACWC,
               EEACWC,
               ACWCT,
               TS
        FROM BNS_IA_WATSUPL IA
                 LEFT JOIN BSN_ADCD_B AD ON AD.ADCD = IA.ADCD
        WHERE IA.ADCD = #{adcd}
        ORDER BY IA.YR ASC
    </select>
</mapper>
