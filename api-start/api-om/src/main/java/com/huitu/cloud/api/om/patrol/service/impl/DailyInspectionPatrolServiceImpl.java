package com.huitu.cloud.api.om.patrol.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.om.patrol.entity.*;
import com.huitu.cloud.api.om.patrol.entity.AlarmCalendarOneRes;
import com.huitu.cloud.api.om.patrol.mapper.DailyInspectionDao;
import com.huitu.cloud.api.om.patrol.service.DailyInspectionPatrolService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.PageBeanUtil;
import com.huitu.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: DailyInspectionPatrolServiceImpl
 * @description: 报讯日历
 * @author: daiJie
 * @create: 2023-08-10 17:43
 **/
@Service
public class DailyInspectionPatrolServiceImpl implements DailyInspectionPatrolService {
    @Resource
    private DailyInspectionDao dailyInspectionDao;

    private static final DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

    //标准化配置汛期每天的报讯次数
    private static final Integer xunqiNum = 1;
    //标准化配置非汛期每周的报讯次数
    private static final Integer num = 1;
    //吉林省政区
    private static final String adcd="220000000000000";

    //及时率时间限定默认值
    private  String xunqiDateRangeS="08:00";
    private  String xunqiDateRangeE="10:00";
    private  String dateRangeS="08:00";
    private  String dateRangeE="10:00";

    @Override
    public Object getPage(AlarmCalendarStatisticsQuery query) {
        if (!StringUtils.isEmpty(query.getAdcd())){
            query.setAdcd(StringUtils.padRight(query.getAdcd(), 15, '0'));
        }
        int adLevel = AdcdUtil.getAdLevel(query.getAdcd());
        if (adLevel >= 6) {
            PageBeanUtil<AlarmCalendarStatisticsResCompleteVo> resPage = getResPage(query);
            return resPage;
        } else {
            PageBeanUtil<AlarmCalendarStatisticsVo> adcdPage = getAdcdPage(query);
            return adcdPage;
        }
    }

    @Override
    public List<AlarmCalendarVo> getAlarmCalendar(AlarmCalendarDTO alarmCalendarDTO) throws ParseException {
        if (!StringUtils.isEmpty(alarmCalendarDTO.getAdcd())){
            alarmCalendarDTO.setAdcd(StringUtils.padRight(alarmCalendarDTO.getAdcd(), 15, '0'));
        }
        List<AlarmCalendarVo> result = new ArrayList<>();
        //根据入参日期获取开始时间，结束时间和报讯时段
        String dateTime = alarmCalendarDTO.getDateTime();
        String year = dateTime.substring(0, 4);
        String month = dateTime.substring(5, 7);
        LocalDate startTimed = LocalDate.parse(year + "-" + month + "-01");
        LocalDate endTimed = startTimed.with(TemporalAdjusters.lastDayOfMonth());
        String startTime = startTimed.format(sdf);
        String endTime = endTimed.format(sdf);
        //根据开始结束时间获取每一天的日期
        List<AlarmCalendarTimeDTO> timeList = new ArrayList<>();
        timeList = getTimeList(timeList, startTime, endTime);
        List<String> dailyAll = getDailyAll(timeList);
        dailyAll=dailyAll.stream().sorted(Comparator.comparing(a->a)).collect(Collectors.toList());
        //获取报讯时段
        AlarmCalendarTimeDTO alarmCalendarTimeDTO = timeList.get(0);
        String dateType = alarmCalendarTimeDTO.getDateType();
        //查询单个政区和政区对应的水库数量
        AlarmCalendarStatisticsVo alarmCalendarStatistics = dailyInspectionDao.getAdcdAndResDetail(alarmCalendarDTO.getAdcd());
        //查询单个政区基本水库数据
        List<AlarmCalendarStatisticsResDetailVo> resDetailOneAdcd = dailyInspectionDao.getResDetailOneAdcd(alarmCalendarDTO.getAdcd(),"", startTime, endTime);

        //查询出除险加固和病险水库并过滤
        List<String> dangerAndSafeRes = getDangerAndSafeRes(alarmCalendarDTO.getIsDanger(), alarmCalendarDTO.getHaveSafeBuildPros());
        if (dangerAndSafeRes.size()>0){
            resDetailOneAdcd=resDetailOneAdcd.stream().filter(a->dangerAndSafeRes.contains(a.getResCode())).collect(Collectors.toList());
        }
        Integer resCount = alarmCalendarStatistics.getResCount();
        //计算出汛期每天应报讯次数
        Long xunqiShouldNum =Long.valueOf(resCount * xunqiNum);
        //计算出非汛期每周应报讯次数
        Long shouldNum = Long.valueOf(resCount * num);
        //将基本信息按时间分组
        Map<String, List<AlarmCalendarStatisticsResDetailVo>> tmMap = resDetailOneAdcd.stream().collect(Collectors.groupingBy(a -> a.getTM()));
        //设置结果集
        for (String s : dailyAll) {
            AlarmCalendarVo alarmCalendarVo = new AlarmCalendarVo();
            //汛期每天水库的实报次数
            Long xunqiCompleteNum = 0L;
            //非汛期每周水库的实报次数
            Long completeNum = 0L;
            DecimalFormat df = new DecimalFormat("#0.00");
            double rate = 0;
            String reportRate = "0.00";
            if ("1".equals(dateType)) {
                //获得每天所有水库的报讯信息
                List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVoList = tmMap.get(s);
                if (ObjectUtils.isEmpty(alarmCalendarStatisticsResDetailVoList)){
                    alarmCalendarVo.setWeek(getWeekOfDate(s));
                    alarmCalendarVo.setTM(s);
                    alarmCalendarVo.setReportRate(reportRate);
                    result.add(alarmCalendarVo);
                    continue;
                }
                if (alarmCalendarStatisticsResDetailVoList.size()==0){
                    alarmCalendarVo.setWeek(getWeekOfDate(s));
                    alarmCalendarVo.setTM(s);
                    alarmCalendarVo.setReportRate(reportRate);
                    result.add(alarmCalendarVo);
                    continue;
                }
                //按单个水库进行分组
                Map<String, List<AlarmCalendarStatisticsResDetailVo>> tmResMap = alarmCalendarStatisticsResDetailVoList.stream().collect(Collectors.groupingBy(a -> a.getResCode()));
                for (String resCode : tmResMap.keySet()) {
                    List<AlarmCalendarStatisticsResDetailVo> tmResList = tmResMap.get(resCode);
                    if (ObjectUtils.isEmpty(tmResList)) {
                        continue;
                    } else if (tmResList.size() < xunqiNum) {
                        //添加实报次数
                        xunqiCompleteNum = xunqiCompleteNum + tmResList.size();
                    } else {
                        //添加实报次数
                        xunqiCompleteNum = xunqiCompleteNum + num;
                    }
                }
                if (xunqiShouldNum > 0) {
                    rate =(double) xunqiCompleteNum*(double) 100 / (double)xunqiShouldNum;
                    reportRate = df.format(rate);
                }
            }
            if ("2".equals(dateType)) {
                //获取每周的第一天
                String mondayDateString = getMonday(s);
                //获取每周的第一天时间戳
                LocalDate mondayDate = LocalDate.parse(mondayDateString);
                Long startLong = mondayDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
                //获取当前时间的时间戳
                LocalDate endDate = LocalDate.parse(s);
                Long endLong = endDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();

                //获取当前周的水库的报讯基本信息
                List<AlarmCalendarStatisticsResDetailVo> weekList = resDetailOneAdcd.stream().filter(a -> startLong <= LocalDate.parse(a.getTM()).atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli() &&
                        endLong >= LocalDate.parse(a.getTM()).atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli()).collect(Collectors.toList());
                if (ObjectUtils.isEmpty(weekList)){
                    alarmCalendarVo.setWeek(getWeekOfDate(s));
                    alarmCalendarVo.setTM(s);
                    alarmCalendarVo.setReportRate(reportRate);
                    result.add(alarmCalendarVo);
                    continue;
                }
                if (weekList.size()==0){
                    alarmCalendarVo.setWeek(getWeekOfDate(s));
                    alarmCalendarVo.setTM(s);
                    alarmCalendarVo.setReportRate(reportRate);
                    result.add(alarmCalendarVo);
                    continue;
                }
                //按单个水库进行分组
                Map<String, List<AlarmCalendarStatisticsResDetailVo>> weekResMap = weekList.stream().collect(Collectors.groupingBy(a -> a.getResCode()));
                for (String resCode : weekResMap.keySet()) {
                    List<AlarmCalendarStatisticsResDetailVo> weekResList = weekResMap.get(resCode);
                    if (ObjectUtils.isEmpty(weekResList)) {
                        continue;
                    } else if (weekResList.size() < xunqiNum) {
                        //添加实报次数
                        completeNum = completeNum + weekResList.size();
                    } else {
                        //添加实报次数
                        completeNum = completeNum + num;
                    }
                }
                if (shouldNum > 0) {
                    rate =(double) completeNum *(double) 100 /(double) shouldNum;
                    reportRate = df.format(rate);
                }
            }
            alarmCalendarVo.setWeek(getWeekOfDate(s));
            alarmCalendarVo.setTM(s);
            alarmCalendarVo.setReportRate(reportRate);
            result.add(alarmCalendarVo);
        }


        return result;
    }

    @Override
    public List<AlarmCalendarOneRes> getAlarmCalendarOneRes(AlarmCalendarDTO alarmCalendarDTO) throws ParseException {
        //定义结果集
        List<AlarmCalendarOneRes> result = new ArrayList<>();
        //根据入参日期获取开始时间，结束时间和报讯时段
        String dateTime = alarmCalendarDTO.getDateTime();
        String year = dateTime.substring(0, 4);
        String month = dateTime.substring(5, 7);
        LocalDate startTimed = LocalDate.parse(year + "-" + month + "-01");
        LocalDate endTimed = startTimed.with(TemporalAdjusters.lastDayOfMonth());
        String startTime = startTimed.format(sdf);
        String endTime = endTimed.format(sdf);
        //根据开始结束时间获取每一天的日期
        List<AlarmCalendarTimeDTO> timeList = new ArrayList<>();
        timeList = getTimeList(timeList, startTime, endTime);
        List<String> dailyAll = getDailyAll(timeList);
        dailyAll=dailyAll.stream().sorted(Comparator.comparing(a->a)).collect(Collectors.toList());
        //获取报讯时段
        AlarmCalendarTimeDTO alarmCalendarTimeDTO = timeList.get(0);
        String dateType = alarmCalendarTimeDTO.getDateType();
        //查询单个水库基本水库数据
        List<AlarmCalendarStatisticsResDetailVo> resDetailOneAdcd = dailyInspectionDao.getResDetailOneAdcd(alarmCalendarDTO.getAdcd(),alarmCalendarDTO.getResCode(), startTime, endTime);
        //将水库基本信息按时间分组
        Map<String, List<AlarmCalendarStatisticsResDetailVo>> tmMap = resDetailOneAdcd.stream().collect(Collectors.groupingBy(a -> a.getTM()));
        //设置结果集
        for (String s : dailyAll) {
            AlarmCalendarOneRes alarmCalendarOneRes = new AlarmCalendarOneRes();
            if ("1".equals(dateType)) {
                //获得每天单个水库的报讯信息
                List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVoList = tmMap.get(s);
                if (ObjectUtils.isEmpty(alarmCalendarStatisticsResDetailVoList)){
                    alarmCalendarOneRes.setTm(s);
                    alarmCalendarOneRes.setIsReport("2");
                    result.add(alarmCalendarOneRes);
                    continue;
                }
                if (alarmCalendarStatisticsResDetailVoList.size()==0){
                    alarmCalendarOneRes.setTm(s);
                    alarmCalendarOneRes.setIsReport("2");
                    result.add(alarmCalendarOneRes);
                    continue;
                }
               if (alarmCalendarStatisticsResDetailVoList.size()>=xunqiNum){
                   alarmCalendarOneRes.setTm(s);
                   alarmCalendarOneRes.setIsReport("1");
                   result.add(alarmCalendarOneRes);
               }
            }
            if ("2".equals(dateType)) {
                //获取每周的第一天
                String mondayDateString = getMonday(s);
                //获取每周的第一天时间戳
                LocalDate mondayDate = LocalDate.parse(mondayDateString);
                Long startLong = mondayDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
                //获取当前时间的时间戳
                LocalDate endDate = LocalDate.parse(s);
                Long endLong = endDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();

                //获取当前周的水库的报讯基本信息
                List<AlarmCalendarStatisticsResDetailVo> weekList = resDetailOneAdcd.stream().filter(a -> startLong <= LocalDate.parse(a.getTM()).atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli() &&
                        endLong >= LocalDate.parse(a.getTM()).atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli()).collect(Collectors.toList());
                if (ObjectUtils.isEmpty(weekList)){
                    alarmCalendarOneRes.setTm(s);
                    alarmCalendarOneRes.setIsReport("2");
                    result.add(alarmCalendarOneRes);
                    continue;
                }
                if (weekList.size()==0){
                    alarmCalendarOneRes.setTm(s);
                    alarmCalendarOneRes.setIsReport("2");
                    result.add(alarmCalendarOneRes);
                    continue;
                }
                if (weekList.size()>=num){
                    alarmCalendarOneRes.setTm(s);
                    alarmCalendarOneRes.setIsReport("1");
                    result.add(alarmCalendarOneRes);
                }
            }
        }

        return result;
    }

    @Override
    public IPage<AlarmDetailVo> getAlarmDetail(AlarmDetailQuery alarmDetailQuery) {
        if (!StringUtils.isEmpty(alarmDetailQuery.getAdcd())){
            alarmDetailQuery.setAdcd(StringUtils.padRight(alarmDetailQuery.getAdcd(), 15, '0'));
        }
        Page page = new Page(alarmDetailQuery.getPageNum(), alarmDetailQuery.getPageSize());
        IPage<AlarmDetailVo> alarmDetail = dailyInspectionDao.getAlarmDetail(page, alarmDetailQuery);
        //查询出除险加固水库
        List<String> safeRes = dailyInspectionDao.getSafeRes();
        //查询出病险水库
        List<String> dangerRes = dailyInspectionDao.getDangerRes();
        List<AlarmDetailVo> records = alarmDetail.getRecords();

        for (AlarmDetailVo record : records) {
            //设置水库除险加固状态
            String haveSafeBuildPros="0";
            //设置水库病险状态
            String isDanger="0";
            //如果除险加固水库中有该水库则设为1
            if (safeRes.contains(record.getResCode())){
                 haveSafeBuildPros="1";
            }
            //如果病险水库中有该水库则设为1
            if (dangerRes.contains(record.getResCode())){
                 isDanger="1";
            }
            record.setHaveSafeBuildPros(haveSafeBuildPros);
            record.setIsDanger(isDanger);
        }
        return alarmDetail.setRecords(records);
    }

    @Override
    public ObservationRateVo rainForecastReportSts(StandardBaseForm form) {
        //获取小型水库数量
        Integer allResCount = dailyInspectionDao.getAllResCount();
        //获取汛期和非汛期应巡查频率
        //HashMap<String, Integer> shouldNumMap = getShouldNumMap();
        //Integer xunqiNum = shouldNumMap.get("xunqiNum");
        //Integer num = shouldNumMap.get("num");
        //获取当天
        String today = LocalDate.now().format(sdf);
        AlarmCalendarTimeDTO alarmCalendarTimeDTO=new AlarmCalendarTimeDTO();
        //获取时间的汛期和非汛期时段
        if (new Integer(1).equals(form.getDateType())){
            alarmCalendarTimeDTO = getTodayAlarmCalendarTimeDTO(today);
        }else if(new Integer(2).equals(form.getDateType())){
            alarmCalendarTimeDTO = getMonthAlarmCalendarTimeDTO(today);
        }else if(new Integer(3).equals(form.getDateType())){
            alarmCalendarTimeDTO = getQuarterAlarmCalendarTimeDTO(today);
        }else if(new Integer(4).equals(form.getDateType())){
            alarmCalendarTimeDTO = getYearAlarmCalendarTimeDTO(today);
        }
        //获取开始结束时间
        String startTime = alarmCalendarTimeDTO.getStartTime();
        String endTime = alarmCalendarTimeDTO.getEndTime();
        //获取每个水库的观测率
        List<AlarmCalendarStatisticsResVo> alarmCalendarStatisticsResVos = getAlarmCalendarStatisticsResVo(startTime, endTime, xunqiNum, num,"","","","");
        //求出观测率在100%的水库数量
        List<AlarmCalendarStatisticsResVo> height = alarmCalendarStatisticsResVos.stream().filter(a -> new Double(a.getReportRate()) == new Double(100)).collect(Collectors.toList());
        //求出观测率大于等于80%小于百分之100%的水库数量
        List<AlarmCalendarStatisticsResVo> middle = alarmCalendarStatisticsResVos.stream().filter(a -> new Double(a.getReportRate()) < new Double(100) &&new Double(a.getReportRate()) >= new Double(80)).collect(Collectors.toList());
        //求出观测率小于80%的水库数量
        List<AlarmCalendarStatisticsResVo> less = alarmCalendarStatisticsResVos.stream().filter(a -> new Double(a.getReportRate()) < new Double(80)).collect(Collectors.toList());
        Integer heightCount=0;
        Integer middleCount=0;
        if (!ObjectUtils.isEmpty(height)){
            heightCount=height.size();
        }
        if (!ObjectUtils.isEmpty(middle)){
            middleCount=middle.size();
        }
        ObservationRateVo observationRateVo = new ObservationRateVo();
        observationRateVo.setAllRes(allResCount);
        observationRateVo.setHeightCount(heightCount);
        observationRateVo.setMiddleCount(middleCount);
        observationRateVo.setLessCount(allResCount-heightCount-middleCount);
        return observationRateVo;
    }

    @Override
    public ObservationRateVo rainForecastReportTimelySts(StandardBaseForm form) {
        Integer allResCount = dailyInspectionDao.getAllResCount();
        //获取当天
        String today = LocalDate.now().format(sdf);
        AlarmCalendarTimeDTO alarmCalendarTimeDTO=new AlarmCalendarTimeDTO();
        //获取时间的汛期和非汛期时段
        if (new Integer(1).equals(form.getDateType())){
            alarmCalendarTimeDTO = getTodayAlarmCalendarTimeDTO(today);
        }else if(new Integer(2).equals(form.getDateType())){
            alarmCalendarTimeDTO = getMonthAlarmCalendarTimeDTO(today);
        }else if(new Integer(3).equals(form.getDateType())){
            alarmCalendarTimeDTO = getQuarterAlarmCalendarTimeDTO(today);
        }else if(new Integer(4).equals(form.getDateType())){
            alarmCalendarTimeDTO = getYearAlarmCalendarTimeDTO(today);
        }
        //获取开始结束时间
        String startTime = alarmCalendarTimeDTO.getStartTime();
        String endTime = alarmCalendarTimeDTO.getEndTime();
        //获取每个水库的观测率
        List<AlarmCalendarStatisticsResVo> alarmCalendarStatisticsResVos = getAlarmCalendarStatisticsResVo(startTime, endTime, xunqiNum, num,xunqiDateRangeS,xunqiDateRangeE,dateRangeS,dateRangeE);

        //求出观测率在100%的水库数量
        List<AlarmCalendarStatisticsResVo> height = alarmCalendarStatisticsResVos.stream().filter(a -> new Double(a.getReportRate()) == new Double(100)).collect(Collectors.toList());
        //求出观测率大于等于80%小于百分之100%的水库数量
        List<AlarmCalendarStatisticsResVo> middle = alarmCalendarStatisticsResVos.stream().filter(a -> new Double(a.getReportRate()) < new Double(100) &&new Double(a.getReportRate()) >= new Double(80)).collect(Collectors.toList());
        //求出观测率小于80%的水库数量
        List<AlarmCalendarStatisticsResVo> less = alarmCalendarStatisticsResVos.stream().filter(a -> new Double(a.getReportRate()) < new Double(80)).collect(Collectors.toList());
        Integer heightCount=0;
        Integer middleCount=0;
        if (!ObjectUtils.isEmpty(height)){
            heightCount=height.size();
        }
        if (!ObjectUtils.isEmpty(middle)){
            middleCount=middle.size();
        }
        ObservationRateVo observationRateVo = new ObservationRateVo();
        observationRateVo.setAllRes(allResCount);
        observationRateVo.setHeightCount(heightCount);
        observationRateVo.setMiddleCount(middleCount);
        observationRateVo.setLessCount(allResCount-heightCount-middleCount);
        return observationRateVo;
    }

    @Override
    public FloodStatisticsVo floodStatistics() {
        FloodStatisticsVo floodStatisticsVo = new FloodStatisticsVo();
        //查询出政区和政区的水库数量
        List<FloodStatisticsAdcdDetailVo> floodStatisticsAdcdDetailVoS = dailyInspectionDao.getAdcdAndResExcludeProvice(adcd).stream().filter(a->!StringUtils.isEmpty(a.getAdcd())).collect(Collectors.toList());
        //过滤出市级政区
        floodStatisticsAdcdDetailVoS=floodStatisticsAdcdDetailVoS.stream().filter(a->AdcdUtil.getAdLevel(a.getAdcd())<=4).collect(Collectors.toList());
        //获取当天
        String endTime = LocalDate.now().format(sdf);
        LocalDate endTimed = LocalDate.parse(endTime);
        //获取七天前的时间查询数据(非汛期才会用到)
        LocalDate startTimed = endTimed.plusDays(-7L);
        String startTime = startTimed.format(sdf);
        //判断当天是汛期还是非汛期
        List<AlarmCalendarTimeDTO> alarmCalendarTimeDTOS =new ArrayList<>();
        alarmCalendarTimeDTOS=getTimeList(alarmCalendarTimeDTOS,endTime,endTime);
        String dateType = alarmCalendarTimeDTOS.get(0).getDateType();
        //根据开始结束时间获取报讯基础数据
        List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVosd=dailyInspectionDao.getResDetailOneAdcd("","",startTime,endTime);
        for (FloodStatisticsAdcdDetailVo statisticsAdcdDetailVo : floodStatisticsAdcdDetailVoS) {
            //过滤车当前政区的水库
            List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVos = alarmCalendarStatisticsResDetailVosd.stream().filter(a -> a.getAdcd().substring(0, 4).equals(statisticsAdcdDetailVo.getAdcd().substring(0, 4))).collect(Collectors.toList());
            //按水库对基础数据进行分组
            Map<String, List<AlarmCalendarStatisticsResDetailVo>> resMap = alarmCalendarStatisticsResDetailVos.stream().collect(Collectors.groupingBy(a -> a.getResCode()));
            //定义每个政区的已报水库数量
            Integer completeCount=0;
            for (String s : resMap.keySet()) {
                List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVosRes = resMap.get(s);
                if ("1".equals(dateType)){
                    //获取每个水库当天的基本数据
                    alarmCalendarStatisticsResDetailVosRes = alarmCalendarStatisticsResDetailVosRes.stream().filter(a -> a.getTM().equals(endTime)).collect(Collectors.toList());
                    if (ObjectUtils.isEmpty(alarmCalendarStatisticsResDetailVosRes)){
                        //水库没有报讯信息跳出循环查询其它水库
                        continue;
                    }else if (alarmCalendarStatisticsResDetailVosRes.size() < xunqiNum) {
                        //水库有报讯但是没完成标准跳出循环查询其它水库
                        continue;
                    }else {
                        //当前水库完成报讯
                        completeCount++;
                    }
                }else if("2".equals(dateType)){
                    //获取当前日期所属周的第一天
                    String mondayDateString = getMonday(endTime);
                    //获取每周的第一天时间戳
                    LocalDate mondayDate = LocalDate.parse(mondayDateString);
                    Long startLong = mondayDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
                    //获取当前时间的时间戳
                    LocalDate endDate = LocalDate.parse(endTime);
                    Long endLong = endDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
                    //获取每个水库当前周的基本数据
                    alarmCalendarStatisticsResDetailVosRes=alarmCalendarStatisticsResDetailVosRes.stream().filter(a -> startLong <= LocalDate.parse(a.getTM()).atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli() &&
                            endLong >= LocalDate.parse(a.getTM()).atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli()).collect(Collectors.toList());
                    if (ObjectUtils.isEmpty(alarmCalendarStatisticsResDetailVosRes)){
                        //水库没有报讯信息跳出循环查询其它水库
                        continue;
                    }else if (alarmCalendarStatisticsResDetailVosRes.size() < num) {
                        //水库有报讯但是没完成标准跳出循环查询其它水库
                        continue;
                    }else {
                        //当前水库完成报讯
                        completeCount++;
                    }
                }

            }
            statisticsAdcdDetailVo.setCompleteCount(completeCount);
        }
        int shouldCount = floodStatisticsAdcdDetailVoS.stream().mapToInt(FloodStatisticsAdcdDetailVo::getShouldCount).sum();
        int completeCount = floodStatisticsAdcdDetailVoS.stream().mapToInt(FloodStatisticsAdcdDetailVo::getCompleteCount).sum();
        //计算报讯率
        DecimalFormat df = new DecimalFormat("#0.00");
        double rate = 0;
        String reportRate = "0.00";
        if (shouldCount > 0) {
            rate =(double) completeCount*(double) 100 / (double)shouldCount;
            reportRate = df.format(rate);
        }
        floodStatisticsVo.setFloodStatisticsAdcdDetailVos(floodStatisticsAdcdDetailVoS);
        floodStatisticsVo.setTodayFloodRes(completeCount);
        floodStatisticsVo.setProvinceFloodRate(reportRate);
        return floodStatisticsVo;
    }


    /**
     * 方法描述
     * 获得每个时间段的开始时间结束时间
     *
     * @return java.util.List<com.huitu.cloud.api.om.patrol.entity.AlarmCalendarTimeDTO>
     * <AUTHOR>
     * @date 2023-08-10 18:08:24
     */
    public List<AlarmCalendarTimeDTO> getTimeList(List<AlarmCalendarTimeDTO> result, String startTime, String endTime) {
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //入参的时间转化为时间戳
        LocalDate startTimed = LocalDate.parse(startTime);
        Long startLong = startTimed.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
        LocalDate endTimed = LocalDate.parse(endTime);
        Long endLong = endTimed.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
        //汛期每年的6月1日到每年的9月30日
        Integer startYear = Integer.valueOf(startTime.substring(0, 4));
        //汛期开始时间戳
        LocalDate xunqiStart = LocalDate.of(startYear, 5, 31);
        Long xunqiStartLong = xunqiStart.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
        //汛期结束时间戳
        LocalDate xunqiEnd = LocalDate.of(startYear, 9, 30);
        Long xunqiEndLong = xunqiEnd.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
        //开始时间小于汛期开始时间
        if (startLong < xunqiStartLong) {
            AlarmCalendarTimeDTO alarmCalendarTimeDTO = new AlarmCalendarTimeDTO();
            alarmCalendarTimeDTO.setStartTime(startTime);
            alarmCalendarTimeDTO.setDateType("2");
            if (endLong < xunqiStartLong) {
                alarmCalendarTimeDTO.setEndTime(endTime);
                result.add(alarmCalendarTimeDTO);
                return result;
            } else {
                alarmCalendarTimeDTO.setEndTime(xunqiStart.format(sdf));
                result.add(alarmCalendarTimeDTO);
                xunqiStart = xunqiStart.plusDays(1);
                getTimeList(result, xunqiStart.format(sdf), endTime);
            }
        }

        //开始时间大于等于汛期开始时间小于等于汛期结束时间
        if (startLong >= xunqiStartLong && startLong <= xunqiEndLong) {
            AlarmCalendarTimeDTO alarmCalendarTimeDTO = new AlarmCalendarTimeDTO();
            alarmCalendarTimeDTO.setStartTime(startTime);
            alarmCalendarTimeDTO.setDateType("1");
            if (endLong < xunqiEndLong) {
                alarmCalendarTimeDTO.setEndTime(endTime);
                result.add(alarmCalendarTimeDTO);
                return result;
            } else {
                alarmCalendarTimeDTO.setEndTime(xunqiEnd.format(sdf));
                result.add(alarmCalendarTimeDTO);
                xunqiEnd = xunqiEnd.plusDays(1);
                getTimeList(result, xunqiEnd.format(sdf), endTime);
            }
        }


        //开始时间大于汛期结束时间
        if (startLong > xunqiEndLong) {
            LocalDate nestXunqiStart = LocalDate.of(startYear + 1, 5, 31);
            Long nestXunqiStartLong = nestXunqiStart.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
            AlarmCalendarTimeDTO alarmCalendarTimeDTO = new AlarmCalendarTimeDTO();
            alarmCalendarTimeDTO.setStartTime(startTime);
            alarmCalendarTimeDTO.setDateType("2");
            if (endLong < nestXunqiStartLong) {
                alarmCalendarTimeDTO.setEndTime(endTime);
                result.add(alarmCalendarTimeDTO);
                return result;
            } else {
                alarmCalendarTimeDTO.setEndTime(nestXunqiStart.format(sdf));
                result.add(alarmCalendarTimeDTO);
                nestXunqiStart = nestXunqiStart.plusDays(1);
                getTimeList(result, nestXunqiStart.format(sdf), endTime);
            }
        }
        return result;
    }

    /**
     * 方法描述
     * 按天将时间切割
     *
     * @param result
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2023-08-11 10:08:08
     */
    public List<String> getDaily(List<AlarmCalendarTimeDTO> result) {
        result = result.stream().filter(a -> "1".equals(a.getDateType())).collect(Collectors.toList());
        return getDailyAll(result);
    }

    /**
     * 方法描述
     * 按周将时间切割
     *
     * @param result
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2023-08-11 10:08:08
     */
    public List<String> getWeek(List<AlarmCalendarTimeDTO> result) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("w");
        List<String> dailyList = new ArrayList<>();
        result = result.stream().filter(a -> "2".equals(a.getDateType())).collect(Collectors.toList());
        for (AlarmCalendarTimeDTO alarmCalendarTimeDTO : result) {
            LocalDate startDate = LocalDate.parse(alarmCalendarTimeDTO.getStartTime());
            LocalDate endDate = LocalDate.parse(alarmCalendarTimeDTO.getEndTime());
            long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
            for (int i = 0; i <= daysBetween; i++) {
                LocalDate date = startDate.plusDays(i);
                int year = date.getYear();
                String week = date.format(dateTimeFormatter);
                dailyList.add(year + week);
            }
        }
        HashSet<String> set = new HashSet<>(dailyList);
        dailyList.clear();
        dailyList.addAll(set);
        return dailyList;
    }



    /**
     * 方法描述
     * 跟政区逻辑相同维度不同，是水库维度来统计
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.huitu.cloud.api.om.patrol.entity.AlarmCalendarStatisticsVo>
     * <AUTHOR>
     * @date 2023-08-15 09:08:14
     */
//    public List<AlarmCalendarStatisticsResVo> getResPage(List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVos, AlarmCalendarStatisticsQuery query) {
//        List<AlarmCalendarStatisticsResVo> list = new ArrayList<>();
//        //根据开始时间结束时间将时间分段为汛期非汛期
//        List<AlarmCalendarTimeDTO> result = new ArrayList<>();
//        result = getTimeList(result, query.getAlarmStart(), query.getAlarmStart());
//        if (StringUtils.isNotEmpty(query.getAlarmType())) {
//            result = result.stream().filter(a -> query.getAlarmType().equals(a.getDateType())).collect(Collectors.toList());
//        }
//        //按天将时间切割
//        List<String> dailys = getDaily(result);
//        //按周将时间切割
//        List<String> weeks = getWeek(result);
//        //计算出每个水库应报讯的总次数
//        Long numTotal = 0L;
//        if (!ObjectUtils.isEmpty(dailys)) {
//            numTotal = numTotal + dailys.size() * xunqiNum;
//        }
//        if (!ObjectUtils.isEmpty(weeks)) {
//            numTotal = numTotal + weeks.size() * num;
//        }
//
//        if (ObjectUtils.isEmpty(alarmCalendarStatisticsResDetailVos)) {
//            return list;
//        }
//        if (alarmCalendarStatisticsResDetailVos.size() == 0) {
//            return list;
//        }
//        //政区代码
//        AlarmCalendarStatisticsResDetailVo alarmCalendarStatisticsResDetailVo = alarmCalendarStatisticsResDetailVos.get(0);
//        String adcd = alarmCalendarStatisticsResDetailVo.getAdcd();
//
//        //计算应报次数
//        Long shouldAlarmAllCount = numTotal;
//
//
//        //将同一政区下的数据按水库分组
//        Map<String, List<AlarmCalendarStatisticsResDetailVo>> resMap = alarmCalendarStatisticsResDetailVos.stream().collect(Collectors.groupingBy(a -> a.getResCode()));
//
//        for (String s : resMap.keySet()) {
//            //统计每个水库实报次数
//            Long completeAlarmAllCount = 0L;
//            for (String daily : dailys) {
//                List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVoResList = resMap.get(s);
//                //按天数来统计水库的报讯次数和记录是否完成标准化报讯
//                List<AlarmCalendarStatisticsResDetailVo> tmList = alarmCalendarStatisticsResDetailVoResList.stream().filter(a -> daily.equals(a.getTM())).collect(Collectors.toList());
//                //没有这个日期的数据所以不符合
//                if (ObjectUtils.isEmpty(tmList)) {
//                    continue;
//                } else if (tmList.size() < xunqiNum) {
//                    //有了但是次数没达到，不符合,添加实报次数
//                    completeAlarmAllCount = completeAlarmAllCount + tmList.size();
//                } else {
//                    //添加实报次数
//                    completeAlarmAllCount = completeAlarmAllCount + xunqiNum;
//                }
//            }
//
//            for (String week : weeks) {
//                List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVoResList = resMap.get(s);
//                //按周数来统计水库的报讯次数和记录是否完成标准化报讯
//                List<AlarmCalendarStatisticsResDetailVo> tmList = alarmCalendarStatisticsResDetailVoResList.stream().filter(a -> week.equals(a.getWeek())).collect(Collectors.toList());
//                //没有这个日期的数据所以不符合
//                if (ObjectUtils.isEmpty(tmList)) {
//                    continue;
//                } else if (tmList.size() < num) {
//                    //有了但是次数没达到，不符合,添加实报次数
//                    completeAlarmAllCount = completeAlarmAllCount + tmList.size();
//                } else {
//                    //添加实报次数
//                    completeAlarmAllCount = completeAlarmAllCount + num;
//                }
//            }
//            List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVoResList = resMap.get(s);
//            if (ObjectUtils.isEmpty(alarmCalendarStatisticsResDetailVoResList)) {
//                continue;
//            } else if (alarmCalendarStatisticsResDetailVoResList.size() == 0) {
//                continue;
//            }
//            AlarmCalendarStatisticsResDetailVo alarmCalendarStatisticsResDetailVoRes = alarmCalendarStatisticsResDetailVoResList.get(0);
//            DecimalFormat df = new DecimalFormat("#0.00");
//            double rate = 0;
//            String reportRate = "0";
//            if (shouldAlarmAllCount > 0) {
//                rate =(double) completeAlarmAllCount*(double) 100 / (double)shouldAlarmAllCount;
//                reportRate = df.format(rate);
//            }
//            AlarmCalendarStatisticsResVo alarmCalendarStatisticsResVO = new AlarmCalendarStatisticsResVo();
//            alarmCalendarStatisticsResVO.setAdcd(adcd);
//            alarmCalendarStatisticsResVO.setResName(alarmCalendarStatisticsResDetailVoRes.getResName());
//            alarmCalendarStatisticsResVO.setShouldAlarmAllCount(shouldAlarmAllCount);
//            alarmCalendarStatisticsResVO.setCompleteAlarmAllCount(completeAlarmAllCount);
//            alarmCalendarStatisticsResVO.setUnderreportAlarmAllCount(shouldAlarmAllCount - completeAlarmAllCount);
//            alarmCalendarStatisticsResVO.setReportRate(reportRate);
//            list.add(alarmCalendarStatisticsResVO);
//        }
//
//
//        return list;
//
//    }

    /**
     * 方法描述
     * 按天将时间切割通用方法
     *
     * @param result
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2023-08-11 10:08:08
     */
    public List<String> getDailyAll(List<AlarmCalendarTimeDTO> result) {
        List<String> dailyList = new ArrayList<>();
        for (AlarmCalendarTimeDTO alarmCalendarTimeDTO : result) {
            LocalDate startDate = LocalDate.parse(alarmCalendarTimeDTO.getStartTime());
            LocalDate endDate = LocalDate.parse(alarmCalendarTimeDTO.getEndTime());
            long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
            for (int i = 0; i <= daysBetween; i++) {
                LocalDate date = startDate.plusDays(i);
                dailyList.add(date.format(sdf));
            }
        }
        HashSet<String> set = new HashSet<>(dailyList);
        dailyList.clear();
        dailyList.addAll(set);
        return dailyList;
    }


    /**
     * 方法描述
     * 根据日期获得每周的第一天
     *
     * @param dateString
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2023-08-11 10:08:08
     */
    public String getMonday(String dateString) {
        LocalDate date = LocalDate.parse(dateString);
        TemporalField fieldISO = WeekFields.of(Locale.FRANCE).dayOfWeek();
        LocalDate mondayDate = date.with(fieldISO, 1);
        String mondayDateString = sdf.format(mondayDate);
        return mondayDateString;
    }



    /**
     * 获取当前日期是星期几<br> *  * @param date * @return 当前日期是星期几
     */
    public String getWeekOfDate(String dateString) throws ParseException {
        Date date = simpleDateFormat.parse(dateString);
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) w = 0;
        return weekDays[w];
    }

    /**
     * 方法描述
     *  水库维度下的报讯统计
     * <AUTHOR>
     * @date 2023-08-18 18:08:15
     * @return com.huitu.cloud.util.PageBeanUtil<com.huitu.cloud.api.om.patrol.entity.AlarmCalendarStatisticsVo>
     */
    public PageBeanUtil<AlarmCalendarStatisticsResCompleteVo> getResPage(AlarmCalendarStatisticsQuery query){
        //查询政区下的水库
        List<HashMap<String, String>> resCodes = dailyInspectionDao.getRes(query.getAdcd());
        //根据开始时间结束时间将时间分段
        List<AlarmCalendarTimeDTO> times = new ArrayList<>();
        times = getTimeList(times, query.getAlarmStart(),query.getAlarmEnd());
        //根据报讯时段筛选数据
        if (!StringUtils.isEmpty(query.getAlarmType())){
            times=times.stream().filter(a->query.getAlarmType().equals(a.getDateType())).collect(Collectors.toList());
        }
        List<AlarmCalendarTimeDTO> xunqiPeriod=times.stream().filter(a->"1".equals(a.getDateType())).collect(Collectors.toList());
        List<AlarmCalendarTimeDTO> period=times.stream().filter(a->"2".equals(a.getDateType())).collect(Collectors.toList());
        //查询出汛期的天数
        List<String> dailys = getDaily(xunqiPeriod);
        //查询出非汛期有多少周
        List<String> weeks = getWeek(period);
        //定义数据
        List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTOS =new ArrayList<>();
        //查询出汛期的数据
        if (xunqiPeriod.size()>0){
            alarmCalendarCompleteByResDTOS.addAll(dailyInspectionDao.getXunQiAlarmCalendarCompleteByResDTO(query.getAdcd(),xunqiPeriod,xunqiNum,"",""));
            alarmCalendarCompleteByResDTOS.addAll(dailyInspectionDao.getXunQiAlarmCalendarNoCompleteByResDTO(query.getAdcd(),xunqiPeriod,xunqiNum,"",""));
            for (AlarmCalendarCompleteByResDTO alarmCalendarCompleteByResDTO : alarmCalendarCompleteByResDTOS) {
                alarmCalendarCompleteByResDTO.setType("1");
            }
        }
        //查询出除险加固水库
        List<String> safeRes = dailyInspectionDao.getSafeRes();
        //查询出病险水库
        List<String> dangerRes = dailyInspectionDao.getDangerRes();
        if (period.size()>0) {
            List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTOType = dailyInspectionDao.getAlarmCalendarCompleteByResDTO(query.getAdcd(), period, num,"","");
            for (AlarmCalendarCompleteByResDTO calendarCompleteByResDTO : alarmCalendarCompleteByResDTOType) {
                calendarCompleteByResDTO.setType("2");
                alarmCalendarCompleteByResDTOS.add(calendarCompleteByResDTO);
            }
            alarmCalendarCompleteByResDTOS.addAll(dailyInspectionDao.getAlarmCalendarNoCompleteByResDTO(query.getAdcd(),period,num,"",""));
        }
        //查询出除险加固和病险水库并过滤
        List<String> dangerAndSafeRes = getDangerAndSafeRes(query.getIsDanger(), query.getHaveSafeBuildPros());
        if (dangerAndSafeRes.size()>0){
            alarmCalendarCompleteByResDTOS=alarmCalendarCompleteByResDTOS.stream().filter(a->dangerAndSafeRes.contains(a.getResCode())).collect(Collectors.toList());
        }
        //根据水库分组
        Map<String, List<AlarmCalendarCompleteByResDTO>> resMap = alarmCalendarCompleteByResDTOS.stream().collect(Collectors.groupingBy(a -> a.getResCode()));

        //计算应报水库数量
        Long shouldAlarmAllCount= Long.valueOf((dailys.size()*xunqiNum + weeks.size()*num));

        List<AlarmCalendarStatisticsResCompleteVo> resPage =new ArrayList<>();
        for (HashMap<String, String> resCoded : resCodes) {
            String resCode = resCoded.get("RES_CODE");
            String resName = resCoded.get("RES_NAME");
            String adnm = resCoded.get("ADNM");
            String adcd = resCoded.get("ADCD");
            String engScal = resCoded.get("ENG_SCAL");
            //定义水库的实际完成次数
            Long completeAlarmAllCountd=0L;
            //定义水库实际报讯的次数但未完成（标准）
            Long noCompleteAlarmAllCount=0L;
            //获得当前水库的汛期非汛期数据
            List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTOSD = resMap.get(resCode);
            if (!StringUtils.isEmpty(resCode)){
                //已完成的报讯统计
                if (!ObjectUtils.isEmpty(alarmCalendarCompleteByResDTOSD)){
                    Integer complete = alarmCalendarCompleteByResDTOSD.stream().filter(a->!ObjectUtils.isEmpty(a.getCompleteResDay())).mapToInt(AlarmCalendarCompleteByResDTO::getCompleteResDay).sum();
                    int xunqiDays = alarmCalendarCompleteByResDTOSD.stream().filter(a -> !ObjectUtils.isEmpty(a.getCompleteResDay()))
                            .filter(a -> !ObjectUtils.isEmpty(a.getType())).filter(a -> "1".equals(a.getType()))
                            .mapToInt(AlarmCalendarCompleteByResDTO::getCompleteResDay).sum();
                    completeAlarmAllCountd=completeAlarmAllCountd+(xunqiDays*xunqiNum+(complete-xunqiDays)*num);
                    //未完成的报讯统计
                    for (AlarmCalendarCompleteByResDTO alarmCalendarCompleteByResDTO : alarmCalendarCompleteByResDTOSD) {
                        if (!ObjectUtils.isEmpty(alarmCalendarCompleteByResDTO.getResCount())){
                            noCompleteAlarmAllCount=noCompleteAlarmAllCount+alarmCalendarCompleteByResDTO.getResCount();
                        }
                    }
                }
            }
            Long completeAlarmAllCount = Long.valueOf(completeAlarmAllCountd) + noCompleteAlarmAllCount;

            //计算报讯率
            DecimalFormat df = new DecimalFormat("#0.00");
            double rate = 0;
            String reportRate = "0.00";
            rate =(double) completeAlarmAllCount*(double) 100 / (double)shouldAlarmAllCount;
            reportRate = df.format(rate);
            //计算漏报次数
            Long underreportAlarmAllCount = shouldAlarmAllCount - completeAlarmAllCount;
            //赋值
            AlarmCalendarStatisticsResCompleteVo alarmCalendarStatisticsResVo = new AlarmCalendarStatisticsResCompleteVo();

            //设置水库除险加固状态
            String haveSafeBuildPros="0";
            //设置水库病险状态
            String isDanger="0";
            //如果除险加固水库中有该水库则设为1
            if (safeRes.contains(alarmCalendarStatisticsResVo.getResCode())){
                haveSafeBuildPros="1";
            }
            //如果病险水库中有该水库则设为1
            if (dangerRes.contains(alarmCalendarStatisticsResVo.getResCode())){
                isDanger="1";
            }

            alarmCalendarStatisticsResVo.setHaveSafeBuildPros(haveSafeBuildPros);
            alarmCalendarStatisticsResVo.setIsDanger(isDanger);
            alarmCalendarStatisticsResVo.setAdcd(adcd);
            alarmCalendarStatisticsResVo.setAdnm(adnm);
            alarmCalendarStatisticsResVo.setResCode(resCode);
            alarmCalendarStatisticsResVo.setResName(resName);
            alarmCalendarStatisticsResVo.setEngScal(engScal);
            alarmCalendarStatisticsResVo.setShouldAlarmAllCount(shouldAlarmAllCount);
            alarmCalendarStatisticsResVo.setCompleteAlarmAllCount(completeAlarmAllCount);
            alarmCalendarStatisticsResVo.setUnderreportAlarmAllCount(underreportAlarmAllCount);
            alarmCalendarStatisticsResVo.setReportRate(reportRate);
            resPage.add(alarmCalendarStatisticsResVo);
        }

        PageBeanUtil<AlarmCalendarStatisticsResCompleteVo> bean = new PageBeanUtil<>();
        bean.queryPager(query.getPageNum(), query.getPageSize(), resPage);
        return bean;
    }

    /**
     * 方法描述
     *  政区维度下的报讯统计
     * <AUTHOR>
     * @date 2023-08-18 18:08:15
     * @return com.huitu.cloud.util.PageBeanUtil<com.huitu.cloud.api.om.patrol.entity.AlarmCalendarStatisticsVo>
     */
    public PageBeanUtil<AlarmCalendarStatisticsVo> getAdcdPage(AlarmCalendarStatisticsQuery query){
        //查询出政区和政区的水库数量
        List<AlarmCalendarStatisticsVo> alarmCalendarStatisticsVosPage = dailyInspectionDao.getAdcdAndRes(query.getAdcd()).stream().filter(a->!StringUtils.isEmpty(a.getAdcd())).collect(Collectors.toList());
        //根据开始时间结束时间将时间分段
        List<AlarmCalendarTimeDTO> times = new ArrayList<>();
        times = getTimeList(times, query.getAlarmStart(),query.getAlarmEnd());
        //根据报讯时段筛选数据
        if (!StringUtils.isEmpty(query.getAlarmType())){
            times=times.stream().filter(a->query.getAlarmType().equals(a.getDateType())).collect(Collectors.toList());
        }
        List<AlarmCalendarTimeDTO> xunqiPeriod=times.stream().filter(a->"1".equals(a.getDateType())).collect(Collectors.toList());
        List<AlarmCalendarTimeDTO> period=times.stream().filter(a->"2".equals(a.getDateType())).collect(Collectors.toList());
        //查询出汛期的天数
        List<String> dailys = getDaily(xunqiPeriod);
        //查询出非汛期有多少周
        List<String> weeks = getWeek(period);
        //定义数据
        List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTO =new ArrayList<>();
        //查询出汛期的数据
        if (xunqiPeriod.size()>0){
            alarmCalendarCompleteByResDTO.addAll(dailyInspectionDao.getXunQiAlarmCalendarCompleteByResDTO(query.getAdcd(),xunqiPeriod,xunqiNum,"",""));
            alarmCalendarCompleteByResDTO.addAll(dailyInspectionDao.getXunQiAlarmCalendarNoCompleteByResDTO(query.getAdcd(),xunqiPeriod,xunqiNum,"",""));
            for (AlarmCalendarCompleteByResDTO calendarCompleteByResDTO : alarmCalendarCompleteByResDTO) {
                calendarCompleteByResDTO.setType("1");
            }
        }
        if (period.size()>0) {
            List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTOType = dailyInspectionDao.getAlarmCalendarCompleteByResDTO(query.getAdcd(), period, num,"","");
            for (AlarmCalendarCompleteByResDTO calendarCompleteByResDTO : alarmCalendarCompleteByResDTOType) {
                calendarCompleteByResDTO.setType("2");
                alarmCalendarCompleteByResDTO.add(calendarCompleteByResDTO);
            }
            alarmCalendarCompleteByResDTO.addAll(dailyInspectionDao.getAlarmCalendarNoCompleteByResDTO(query.getAdcd(),period,num,"",""));
        }
        //过滤调政区或水库为空的数据
        alarmCalendarCompleteByResDTO=alarmCalendarCompleteByResDTO.stream().filter(a->!ObjectUtils.isEmpty(a)).filter(a->!StringUtils.isEmpty(a.getAdcd())&&!StringUtils.isEmpty(a.getResCode())).collect(Collectors.toList());
        //查询出除险加固和病险水库并过滤
        List<String> dangerAndSafeRes = getDangerAndSafeRes(query.getIsDanger(), query.getHaveSafeBuildPros());
        if (dangerAndSafeRes.size()>0){
            alarmCalendarCompleteByResDTO=alarmCalendarCompleteByResDTO.stream().filter(a->dangerAndSafeRes.contains(a.getResCode())).collect(Collectors.toList());
        }
        //计算出每个水库应报讯的总次数
        Long numTotal = 0L;
        if (!ObjectUtils.isEmpty(dailys)) {
            numTotal = numTotal + dailys.size() ;
        }
        if (!ObjectUtils.isEmpty(weeks)) {
            numTotal = numTotal + weeks.size();
        }

        for (AlarmCalendarStatisticsVo alarmCalendarStatisticsVo : alarmCalendarStatisticsVosPage) {
            String adcd = alarmCalendarStatisticsVo.getAdcd();
            adcd=adcd.replaceAll("0+$", "");
            int length = adcd.length();
            //获取同一政区下的所有水库信息
            List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTOS = new ArrayList<>();
            for (AlarmCalendarCompleteByResDTO calendarCompleteByResDTO : alarmCalendarCompleteByResDTO) {
                if (adcd.equals(calendarCompleteByResDTO.getAdcd().substring(0,length))){
                    alarmCalendarCompleteByResDTOS.add(calendarCompleteByResDTO);
                }
            }
            //将同一政区下的水库进行分组
            Map<String, List<AlarmCalendarCompleteByResDTO>> resMap = alarmCalendarCompleteByResDTOS.stream().collect(Collectors.groupingBy(a -> a.getResCode()));
            //定义完成报讯的水库数量
            Integer resCompleteAlarmAllCount=0;
            //定义水库实际报讯的次数但未完成（标准）
            Long noCompleteAlarmAllCount=0L;
            //定义水库完成某一次的报讯的累计和
            Long completeAlarmAllCountd=0L;
            for (String s : resMap.keySet()) {
                //获得同一水库下的所有信息
                List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTOSRes = resMap.get(s);
                Integer complete = alarmCalendarCompleteByResDTOSRes.stream().filter(a->!ObjectUtils.isEmpty(a.getCompleteResDay())).mapToInt(AlarmCalendarCompleteByResDTO::getCompleteResDay).sum();
                int xunqiDays = alarmCalendarCompleteByResDTOSRes.stream().filter(a -> !ObjectUtils.isEmpty(a.getCompleteResDay()))
                        .filter(a -> !ObjectUtils.isEmpty(a.getType())).filter(a -> "1".equals(a.getType()))
                        .mapToInt(AlarmCalendarCompleteByResDTO::getCompleteResDay).sum();
                completeAlarmAllCountd=completeAlarmAllCountd+(xunqiDays*xunqiNum+(complete-xunqiDays)*num);
                if (complete>=numTotal){
                    //代表水库完成了报讯
                    resCompleteAlarmAllCount++;
                }
                for (AlarmCalendarCompleteByResDTO alarmCalendarCompleteByResDTOSRe : alarmCalendarCompleteByResDTOSRes) {
                    if (!ObjectUtils.isEmpty(alarmCalendarCompleteByResDTOSRe.getResCount())){
                        noCompleteAlarmAllCount=noCompleteAlarmAllCount+alarmCalendarCompleteByResDTOSRe.getResCount();
                    }
                }
            }
            //水库漏报次数
            Long underreportAlarmAllCount=0L;
            //计算应报讯水库次数
            Long shouldAlarmAllCount= Long.valueOf(alarmCalendarStatisticsVo.getResCount() * (dailys.size()*xunqiNum + weeks.size()*num));
            //计算标准化实际报讯次数
            Long completeAlarmAllCount = Long.valueOf(completeAlarmAllCountd) + noCompleteAlarmAllCount;
            underreportAlarmAllCount=shouldAlarmAllCount-completeAlarmAllCount;
            //计算报讯率
            DecimalFormat df = new DecimalFormat("#0.00");
            double rate = 0;
            String reportRate = "0.00";
            rate =(double) completeAlarmAllCount*(double) 100 / (double)shouldAlarmAllCount;
            reportRate = df.format(rate);
            alarmCalendarStatisticsVo.setResShouldAlarmAllCount(alarmCalendarStatisticsVo.getResCount());
            alarmCalendarStatisticsVo.setUnderreportAlarmAllCount(underreportAlarmAllCount);
            alarmCalendarStatisticsVo.setResCompleteAlarmAllCount(resCompleteAlarmAllCount);
            alarmCalendarStatisticsVo.setShouldAlarmAllCount(shouldAlarmAllCount);
            alarmCalendarStatisticsVo.setCompleteAlarmAllCount(completeAlarmAllCount);
            alarmCalendarStatisticsVo.setReportRate(reportRate);
        }
        PageBeanUtil<AlarmCalendarStatisticsVo> bean = new PageBeanUtil<>();
        bean.queryPager(query.getPageNum(), query.getPageSize(), alarmCalendarStatisticsVosPage);
        return bean;
    }


    //    public List<AlarmCalendarStatisticsVo> getAdcdPage(List<AlarmCalendarStatisticsVo> alarmCalendarStatisticsVos, List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVos, AlarmCalendarStatisticsQuery query) {
//        //查询政区和政区对应的水库数量
//        //IPage<AlarmCalendarStatisticsVo> alarmCalendarStatisticsVosPage = dailyInspectionDao.getAdcdAndRes(query, page);
//        //List<AlarmCalendarStatisticsVo> alarmCalendarStatisticsVos = alarmCalendarStatisticsVosPage.getRecords();
//        //根据时间政区和是否汛期等条件查询出每个水库的基本报讯信息
//        //List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVos = dailyInspectionDao.getResDetail(query);
//
//        //根据开始时间结束时间将时间分段为汛期非汛期
//        List<AlarmCalendarTimeDTO> result = new ArrayList<>();
//        result = getTimeList(result, query.getAlarmStart(),query.getAlarmStart());
//        if (StringUtils.isNotEmpty(query.getAlarmType())) {
//            result = result.stream().filter(a -> query.getAlarmType().equals(a.getDateType())).collect(Collectors.toList());
//        }
//        //按天将时间切割
//        List<String> dailys = getDaily(result);
//        //按周将时间切割
//        List<String> weeks = getWeek(result);
//        //计算出每个水库应报讯的总次数
//        Long numTotal = 0L;
//        if (!ObjectUtils.isEmpty(dailys)) {
//            numTotal = numTotal + dailys.size() * xunqiNum;
//        }
//        if (!ObjectUtils.isEmpty(weeks)) {
//            numTotal = numTotal + weeks.size() * num;
//        }
//
//        //将基本信息按政区分组
//        Map<String, List<AlarmCalendarStatisticsResDetailVo>> adcdMap = alarmCalendarStatisticsResDetailVos.stream().collect(Collectors.groupingBy(a -> a.getAdcd()));
//
//        //整理结果集
//        for (AlarmCalendarStatisticsVo alarmCalendarStatisticsVo : alarmCalendarStatisticsVos) {
//            List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVoAdcdList = adcdMap.get(alarmCalendarStatisticsVo.getAdcd());
//            Integer resCount = alarmCalendarStatisticsVo.getResCount();
//            //计算应报次数
//            Long shouldAlarmAllCount = resCount * numTotal;
//
//            //统计实报次数
//            Long completeAlarmAllCount = 0L;
//
//            //统计标准化实完成报讯水库数量
//            Integer resCompleteAlarmAllCount = 0;
//            //将同一政区下的数据按水库分组
//            Map<String, List<AlarmCalendarStatisticsResDetailVo>> resMap = alarmCalendarStatisticsResDetailVoAdcdList.stream().collect(Collectors.groupingBy(a -> a.getResCode()));
//
//            for (String s : resMap.keySet()) {
//                //定义一个boolean来判断这个水库是否符合标准化报讯
//                boolean flag = true;
//                List<AlarmCalendarStatisticsResDetailVo> alarmCalendarStatisticsResDetailVoResList = resMap.get(s);
//                for (String daily : dailys) {
//                    //按天数来统计水库的报讯次数和记录是否完成标准化报讯
//                    List<AlarmCalendarStatisticsResDetailVo> tmList = alarmCalendarStatisticsResDetailVoResList.stream().filter(a -> daily.equals(a.getTM())).collect(Collectors.toList());
//                    //没有这个日期的数据所以不符合
//                    if (ObjectUtils.isEmpty(tmList)) {
//                        flag = false;
//                    } else if (tmList.size() < xunqiNum) {
//                        //有了但是次数没达到，不符合
//                        flag = false;
//                        //添加实报次数
//                        completeAlarmAllCount = completeAlarmAllCount + tmList.size();
//                    } else {
//                        //添加实报次数
//                        completeAlarmAllCount = completeAlarmAllCount + xunqiNum;
//                    }
//                }
//
//                for (String week : weeks) {
//                    //按周数来统计水库的报讯次数和记录是否完成标准化报讯
//                    List<AlarmCalendarStatisticsResDetailVo> tmList = alarmCalendarStatisticsResDetailVoResList.stream().filter(a -> week.equals(a.getWeek())).collect(Collectors.toList());
//                    //没有这个日期的数据所以不符合
//                    if (ObjectUtils.isEmpty(tmList)) {
//                        flag = false;
//                    } else if (tmList.size() < num) {
//                        //有了但是次数没达到，不符合
//                        flag = false;
//                        //添加实报次数
//                        completeAlarmAllCount = completeAlarmAllCount + tmList.size();
//                    } else {
//                        //添加实报次数
//                        completeAlarmAllCount = completeAlarmAllCount + num;
//                    }
//                }
//                //如果这个水库是否符合标准化报讯
//                if (flag) {
//                    resCompleteAlarmAllCount++;
//                }
//            }
//            DecimalFormat df = new DecimalFormat("#0.00");
//            double rate = 0;
//            String reportRate = "0";
//            if (shouldAlarmAllCount > 0) {
//                rate =(double) completeAlarmAllCount*(double) 100 /(double)shouldAlarmAllCount;
//                reportRate = df.format(rate);
//            }
//            alarmCalendarStatisticsVo.setResShouldAlarmAllCount(resCount);
//            alarmCalendarStatisticsVo.setResCompleteAlarmAllCount(resCompleteAlarmAllCount);
//            alarmCalendarStatisticsVo.setShouldAlarmAllCount(shouldAlarmAllCount);
//            alarmCalendarStatisticsVo.setCompleteAlarmAllCount(completeAlarmAllCount);
//            alarmCalendarStatisticsVo.setUnderreportAlarmAllCount(shouldAlarmAllCount - completeAlarmAllCount);
//            alarmCalendarStatisticsVo.setReportRate(reportRate);
//        }
//        //alarmCalendarStatisticsVosPage.setRecords(alarmCalendarStatisticsVos)
//        return alarmCalendarStatisticsVos;
//
//    }
    /**
     * 方法描述
     * 查询出除险加固和病险水库并过滤
     * <AUTHOR>
     * @date 2023-08-26 15:08:05
     * @return java.util.List<java.lang.String>
     */
    public List<String>  getDangerAndSafeRes(String isDanger,String haveSafeBuildPros){
        List<String> result = new ArrayList<>();
        if ("1".equals(isDanger)){
            result.addAll(dailyInspectionDao.getDangerRes());
        }
        if("1".equals(haveSafeBuildPros)){
            result.addAll(dailyInspectionDao.getSafeRes());
        }
        return result;
    }

    /**
     * 方法描述
     * 获取当前日的开始结束时间
     * <AUTHOR>
     * @date 2023-08-15 18:08:17
     * @return java.util.List<com.huitu.saas.jlxsk.workbanch.pojo.dto.AlarmCalendarTimeDTO>
     */
    public AlarmCalendarTimeDTO  getTodayAlarmCalendarTimeDTO(String today){
        AlarmCalendarTimeDTO alarmCalendarTimeDTO = new AlarmCalendarTimeDTO();
        alarmCalendarTimeDTO.setStartTime(today);
        alarmCalendarTimeDTO.setEndTime(today);
        return alarmCalendarTimeDTO;
    }

    /**
     * 方法描述
     * 获取当前月的开始结束时间
     * <AUTHOR>
     * @date 2023-08-15 18:08:17
     * @return java.util.List<com.huitu.saas.jlxsk.workbanch.pojo.dto.AlarmCalendarTimeDTO>
     */
    public AlarmCalendarTimeDTO  getMonthAlarmCalendarTimeDTO(String today){
        //根据入参日期获取开始时间，结束时间和报讯时段
        AlarmCalendarTimeDTO alarmCalendarTimeDTO = new AlarmCalendarTimeDTO();
        String year = today.substring(0, 4);
        String month = today.substring(5, 7);
        LocalDate startTimed = LocalDate.parse(year + "-" + month + "-01");
        String startTime = startTimed.format(sdf);
        alarmCalendarTimeDTO.setStartTime(startTime);
        alarmCalendarTimeDTO.setEndTime(today);
        return alarmCalendarTimeDTO;
    }


    /**
     * 方法描述
     * 获取当前季度的开始结束时间
     * <AUTHOR>
     * @date 2023-08-15 18:08:17
     * @return java.util.List<com.huitu.saas.jlxsk.workbanch.pojo.dto.AlarmCalendarTimeDTO>
     */
    public AlarmCalendarTimeDTO  getQuarterAlarmCalendarTimeDTO(String today){
        //根据入参日期获取开始时间，结束时间和报讯时段
        AlarmCalendarTimeDTO alarmCalendarTimeDTO = new AlarmCalendarTimeDTO();
        LocalDate todayDate = LocalDate.parse(today);
        // 获取当前日期的季度的开始月份
        int currentMonth = todayDate.getMonthValue();
        int quarterStartMonth = ((currentMonth - 1) / 3) * 3 + 1;
        LocalDate quarterStartDate = LocalDate.of(todayDate.getYear(), quarterStartMonth,1);
        String startTime = quarterStartDate.format(sdf);
        alarmCalendarTimeDTO.setStartTime(startTime);
        alarmCalendarTimeDTO.setEndTime(today);
        return alarmCalendarTimeDTO;
    }


    /**
     * 方法描述
     * 获取当前年的开始结束时间
     * <AUTHOR>
     * @date 2023-08-15 18:08:17
     * @return java.util.List<com.huitu.saas.jlxsk.workbanch.pojo.dto.AlarmCalendarTimeDTO>
     */
    public AlarmCalendarTimeDTO  getYearAlarmCalendarTimeDTO(String today){
        //根据入参日期获取开始时间，结束时间和报讯时段
        AlarmCalendarTimeDTO alarmCalendarTimeDTO = new AlarmCalendarTimeDTO();
        LocalDate todayDate = LocalDate.parse(today);
        // 获取当前日期的季度的开始月份
        LocalDate startTimed = LocalDate.of(todayDate.getYear(),1 , 1);
        String startTime = startTimed.format(sdf);
        alarmCalendarTimeDTO.setStartTime(startTime);
        alarmCalendarTimeDTO.setEndTime(today);
        return alarmCalendarTimeDTO;
    }

    /**
     * 方法描述
     * 根据开始结束时间获取每个水库的观测率
     * <AUTHOR>
     * @date 2023-08-21 11:08:28
     * @return java.util.HashMap<java.lang.String,java.lang.Integer>
     */
    public List<AlarmCalendarStatisticsResVo>   getAlarmCalendarStatisticsResVo(String startTime, String endTime, Integer xunqiNum, Integer num, String xunqiDateRangeS, String xunqiDateRangeE, String dateRangeS, String dateRangeE){
        List<AlarmCalendarStatisticsResVo> result= new ArrayList<>();
        //根据开始结束时间划分区间段
        List<AlarmCalendarTimeDTO> alarmCalendarTimeDTOS =new ArrayList<>();
        alarmCalendarTimeDTOS = getTimeList(alarmCalendarTimeDTOS, startTime, endTime);

        //将时间段划分成单独的时间按天或周的维度
        List<String> dailys = getDaily(alarmCalendarTimeDTOS);
        List<String> weeks = getWeek(alarmCalendarTimeDTOS);

        //将时间段按汛期非汛期区分
        List<AlarmCalendarTimeDTO> xunqiPeriod=alarmCalendarTimeDTOS.stream().filter(a->"1".equals(a.getDateType())).collect(Collectors.toList());
        List<AlarmCalendarTimeDTO> period=alarmCalendarTimeDTOS.stream().filter(a->"2".equals(a.getDateType())).collect(Collectors.toList());

        //计算每个水库的应报次数
        Long numTotal = 0L;
        if (!ObjectUtils.isEmpty(dailys)) {
            numTotal = numTotal + dailys.size() * xunqiNum;
        }
        if (!ObjectUtils.isEmpty(weeks)) {
            numTotal = numTotal + weeks.size() * num;
        }
        List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTOS =new ArrayList<>();
        //查询出汛期的数据
        if (xunqiPeriod.size()>0){
            alarmCalendarCompleteByResDTOS.addAll(dailyInspectionDao.getXunQiAlarmCalendarCompleteByResDTO("",xunqiPeriod,xunqiNum,xunqiDateRangeS,xunqiDateRangeE));
            alarmCalendarCompleteByResDTOS.addAll(dailyInspectionDao.getXunQiAlarmCalendarNoCompleteByResDTO("",xunqiPeriod,xunqiNum,xunqiDateRangeS,xunqiDateRangeE));
            for (AlarmCalendarCompleteByResDTO alarmCalendarCompleteByResDTO : alarmCalendarCompleteByResDTOS) {
                alarmCalendarCompleteByResDTO.setType("1");
            }
        }
        //查询出非汛期的数据
        if (period.size()>0) {
            List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTOType =dailyInspectionDao.getAlarmCalendarCompleteByResDTO("",period, num,dateRangeS,dateRangeE);
            for (AlarmCalendarCompleteByResDTO calendarCompleteByResDTO : alarmCalendarCompleteByResDTOType) {
                calendarCompleteByResDTO.setType("2");
                alarmCalendarCompleteByResDTOS.add(calendarCompleteByResDTO);
            }
            alarmCalendarCompleteByResDTOS.addAll(dailyInspectionDao.getAlarmCalendarNoCompleteByResDTO("",period,num,dateRangeS,dateRangeE));
        }

        //根据水库分组
        Map<String, List<AlarmCalendarCompleteByResDTO>> resMap = alarmCalendarCompleteByResDTOS.stream().collect(Collectors.groupingBy(a -> a.getResCode()));
        for (String resCode : resMap.keySet()) {

            List<AlarmCalendarCompleteByResDTO> alarmCalendarCompleteByResDTOSByOneRes = resMap.get(resCode);
            if (ObjectUtils.isEmpty(alarmCalendarCompleteByResDTOSByOneRes)){
                continue;
            }
            AlarmCalendarStatisticsResVo alarmCalendarStatisticsResVo = new AlarmCalendarStatisticsResVo();
            //定义水库的实际完成次数
            Long completeAlarmAllCountd=0L;
            //定义水库实际报讯的次数但未完成（标准）
            Long noCompleteAlarmAllCount=0L;
            //统计完成的汛期和非汛期的总天数
            Integer complete = alarmCalendarCompleteByResDTOSByOneRes.stream().filter(a->!ObjectUtils.isEmpty(a.getCompleteResDay())).mapToInt(AlarmCalendarCompleteByResDTO::getCompleteResDay).sum();
            //统计完成的汛期和非汛期的总次数
            int xunqiDays = alarmCalendarCompleteByResDTOSByOneRes.stream().filter(a -> !ObjectUtils.isEmpty(a.getCompleteResDay()))
                    .filter(a -> !ObjectUtils.isEmpty(a.getType())).filter(a -> "1".equals(a.getType()))
                    .mapToInt(AlarmCalendarCompleteByResDTO::getCompleteResDay).sum();
            completeAlarmAllCountd=completeAlarmAllCountd+(xunqiDays*xunqiNum+(complete-xunqiDays)*num);
            //未完成的报讯统计
            for (AlarmCalendarCompleteByResDTO alarmCalendarCompleteByResDTO : alarmCalendarCompleteByResDTOSByOneRes) {
                if (!ObjectUtils.isEmpty(alarmCalendarCompleteByResDTO.getResCount())){
                    noCompleteAlarmAllCount=noCompleteAlarmAllCount+alarmCalendarCompleteByResDTO.getResCount();
                }
            }
            //计算出每个水库的报讯次数
            Long completeAlarmAllCount = Long.valueOf(completeAlarmAllCountd) + noCompleteAlarmAllCount;
            //计算报讯率
            DecimalFormat df = new DecimalFormat("#0.00");
            double rate = 0;
            String reportRate = "0.00";
            rate =(double) completeAlarmAllCount*(double) 100 / (double)numTotal;
            reportRate = df.format(rate);
            alarmCalendarStatisticsResVo.setResCode(resCode);
            alarmCalendarStatisticsResVo.setReportRate(reportRate);
            result.add(alarmCalendarStatisticsResVo);
        }
        return result;
    }

}

