package com.huitu.cloud.api.fusion.entity.ext;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 山洪预警消息
 */
@ApiModel(value = "山洪预警消息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FusionWarningMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "消息ID")
    @TableField(value = "MSG_ID")
    @JsonIgnore
    private String msgId;

    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARN_ID")
    private String warnId;

    @ApiModelProperty(value = "推送方式，注：1=手机短信、2=语音呼叫")
    @TableField(exist = false)
    private String pushMode;

    public FusionWarningMessage(FusionWarningMessage message) {
        this.msgId = message.getMsgId();
        this.warnId = message.getWarnId();
        this.pushMode = message.getPushMode();
    }
}