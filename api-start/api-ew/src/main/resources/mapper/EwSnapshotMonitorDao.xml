<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwSnapshotMonitorDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getSnapshotMonitor" resultType="com.huitu.cloud.api.ew.entity.response.EwSnapshotMonitorResponse">
        SELECT WARN_ID,
               WARN_GRADE_ID,
               A.ADCD,
               B.ADNM,
               WSCD,
               A.STCD,
               C.STNM,
               STM,
               ETM,
               ACCP,
               SLM_TM,
               SLM,
               SLEP
        FROM EW_SNAPSHOT_MONITOR A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN ST_STBPRP_B C ON C.STCD = A.STCD
        WHERE WARN_ID = #{warnId}
          AND WARN_GRADE_ID = #{warnGradeId}
    </select>
    <select id="getMonitorData" resultType="com.huitu.cloud.api.ew.entity.response.EwShMonitorDataResponse">
        SELECT TOP (1) A.STCD,
                       B.STNM,
                       STM,
                       ETM,
                       ACCP
        FROM EW_SNAPSHOT_MONITOR A
                 LEFT JOIN ST_STBPRP_B B ON B.STCD = A.STCD
        WHERE EXISTS(SELECT 8
                     FROM EW_REL_WARNING_MESSAGE
                     WHERE MSG_ID = #{msgId}
                       AND WARN_ID = A.WARN_ID
                       AND GRADE_ID = A.WARN_GRADE_ID)
    </select>
</mapper>