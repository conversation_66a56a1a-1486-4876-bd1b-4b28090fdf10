package com.huitu.cloud.api.syq.video.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 视频站点和各类对象或工程关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07
 */
@TableName("BSN_VDST_CODE_B")
@ApiModel(value="BsnVdstCodeB对象", description="视频站点和各类对象或工程关系表")
public class BsnVdstCodeB extends Model<BsnVdstCodeB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "视频站编码")
    @TableId(value = "VDSTCD", type = IdType.NONE)
    private String vdstcd;

    @ApiModelProperty(value = "各类编码")
    @TableField("CODE")
    private String code;

    @ApiModelProperty(value = "对象类型，部分参照dbetl的BSN_DWTYPE_B的内容  1：测站 4：河流工程 6：水库 8：堤防  30：水库风险图  31：防治区  32：河流风险图")
    @TableField("TP")
    private String tp;


    public String getVdstcd() {
        return vdstcd;
    }

    public void setVdstcd(String vdstcd) {
        this.vdstcd = vdstcd;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTp() {
        return tp;
    }

    public void setTp(String tp) {
        this.tp = tp;
    }

    @Override
    protected Serializable pkVal() {
        return this.vdstcd;
    }

    @Override
    public String toString() {
        return "BsnVdstCodeB{" +
        "vdstcd=" + vdstcd +
        ", code=" + code +
        ", tp=" + tp +
        "}";
    }
}
