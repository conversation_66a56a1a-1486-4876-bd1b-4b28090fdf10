package com.huitu.cloud.api.ewci.monitor.service.impl;

import com.huitu.cloud.api.ewci.monitor.entity.FloodPerson;
import com.huitu.cloud.api.ewci.monitor.entity.IaCGully;
import com.huitu.cloud.api.ewci.monitor.entity.InfluenceScope;
import com.huitu.cloud.api.ewci.monitor.entity.RsvrPerson;
import com.huitu.cloud.api.ewci.monitor.mapper.IaCGullyDao;
import com.huitu.cloud.api.ewci.monitor.mapper.InfluenceScopeDao;
import com.huitu.cloud.api.ewci.monitor.service.InfluenceScopeService;
import com.huitu.cloud.api.ewci.person.entity.DikePerson;
import com.huitu.cloud.api.ewci.person.entity.RiverPerson;
import com.huitu.cloud.api.ewci.person.mapper.DikePersonDao;
import com.huitu.cloud.api.ewci.person.mapper.RiverPersonDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 影响范围服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class InfluenceScopeServiceImpl implements InfluenceScopeService {

    private InfluenceScopeDao baseDao;
    private IaCGullyDao gullyDao;
    private RiverPersonDao riverPersonDao;
    private DikePersonDao dikePersonDao;

    @Autowired
    public void setBaseDao(InfluenceScopeDao baseDao) {
        this.baseDao = baseDao;
    }

    @Autowired
    public void setGullyDao(IaCGullyDao gullyDao) {
        this.gullyDao = gullyDao;
    }

    @Autowired
    public void setRiverPersonDao(RiverPersonDao riverPersonDao) {
        this.riverPersonDao = riverPersonDao;
    }

    @Autowired
    public void setDikePersonDao(DikePersonDao dikePersonDao) {
        this.dikePersonDao = dikePersonDao;
    }

    @Override
    public InfluenceScope getStatData(String adcd) {
        adcd = StringUtils.rightPad(adcd, 15, "0");
        InfluenceScope influenceScope = baseDao.getStatData(adcd);
        if (null != influenceScope) {
            influenceScope.setFullAdnm(baseDao.getFullAdnm(influenceScope.getAdcd(), 3));
        }
        return influenceScope;
    }

    @Override
    public List<FloodPerson> getFloodPersonList(String adcd) {
        return baseDao.getFloodPersonList(StringUtils.rightPad(adcd, 15, "0"));
    }

    @Override
    public List<RsvrPerson> getRsvrPersonList(String stcd) {
        return baseDao.getRsvrPersonList(stcd);
    }

    @Override
    public List<RiverPerson> getRiverPersonList(String stcd) {
        return riverPersonDao.getListByStcd(stcd);
    }

    @Override
    public List<DikePerson> getDikePersonList(String stcd) {
        return dikePersonDao.getListByStcd(stcd);
    }

    @Override
    public List<IaCGully> getGullyList(String adcd) {
        return gullyDao.getGullyList(adcd);
    }
}
