package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class RsvrDayAvg implements Serializable {
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    private String rvnm;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "平均库水位")
    private BigDecimal avrz;

    @ApiModelProperty(value = "平均入流量")
    private BigDecimal avinq;

    @ApiModelProperty(value = "平均蓄水量")
    private BigDecimal avw;

    @ApiModelProperty(value = "平均出流量")
    private BigDecimal avotq;

    @ApiModelProperty(value = "标志时间")
    private LocalDateTime idtm;
    @ApiModelProperty(value = "县级政区名称")
    @TableField("XADNM")
    private String xadnm;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public BigDecimal getAvrz() {
        return avrz;
    }

    public void setAvrz(BigDecimal avrz) {
        this.avrz = avrz;
    }

    public BigDecimal getAvinq() {
        return avinq;
    }

    public void setAvinq(BigDecimal avinq) {
        this.avinq = avinq;
    }

    public BigDecimal getAvw() {
        return avw;
    }

    public void setAvw(BigDecimal avw) {
        this.avw = avw;
    }

    public BigDecimal getAvotq() {
        return avotq;
    }

    public void setAvotq(BigDecimal avotq) {
        this.avotq = avotq;
    }

    public LocalDateTime getIdtm() {
        return idtm;
    }

    public void setIdtm(LocalDateTime idtm) {
        this.idtm = idtm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }
}
