package com.huitu.cloud.api.syq.rain.entity;

import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;


/**
 * <AUTHOR>
 */
@ApiModel(value = "QueryHisRain")
public class QueryHisRain extends PageBean {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "时间类型(1:一日，4：一旬，5：一月，0：时)",required = true)
    private String tmType;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "政区编码",required = true)
    @SqlInjection
    private String adcd;
    @ApiModelProperty(value = "测站归属类型 1、水文，2、山洪  3、气象")
    @SqlInjection
    private List<String> stType;
    @ApiModelProperty(value = "降雨阈值")
    private String threshold;
    @ApiModelProperty(value = "是否是辖区外(用户登录以后有用) 1：辖区内，2：辖区外")
    @SqlInjection
    private List<String> isOut;

    public String getThreshold() {
        return threshold;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getTmType() {
        return tmType;
    }

    public void setTmType(String tmType) {
        this.tmType = tmType;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public List<String> getIsOut() {
        return isOut;
    }

    public void setIsOut(List<String> isOut) {
        this.isOut = isOut;
    }
}
