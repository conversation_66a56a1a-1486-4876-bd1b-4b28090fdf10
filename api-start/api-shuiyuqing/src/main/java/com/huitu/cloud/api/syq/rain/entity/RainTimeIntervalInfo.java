package com.huitu.cloud.api.syq.rain.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 最近时段雨量累计情况统计信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-8-7
 */
@ApiModel(value="RainTimeIntervalInfo对象", description="最近时段雨量累计情况统计信息")
public class RainTimeIntervalInfo implements Serializable {

    @ExcelProperty("序号")
    @JsonIgnore
    private Integer sortno;
    @ApiModelProperty(value = "市名称")
    @ExcelProperty("市")
    private String sadnm;
    @ApiModelProperty(value = "政区名称")
    @ExcelProperty("县")
    private String adnm;
    @ApiModelProperty(value = "测站编码")
    @ExcelIgnore
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    @ExcelProperty("测站名称")
    private String stnm;
    @ApiModelProperty(value = "站类")
    @ExcelProperty("站类")
    private String stadtpnm;
    @ApiModelProperty(value = "最近5分钟降雨统计")
    @ExcelProperty("最近5分钟")
    private String drp5m;
    @ApiModelProperty(value = "最近10分钟降雨统计")
    @ExcelProperty("最近10分钟")
    private String drp10m;
    @ApiModelProperty(value = "最近30分钟降雨统计")
    @ExcelProperty("最近30分钟")
    private String drp30m;
    @ApiModelProperty(value = "最近1小时降雨统计")
    @ExcelProperty("最近1小时")
    private String drp1h;
    @ApiModelProperty(value = "最近3小时降雨统计")
    @ExcelProperty("最近3小时")
    private String drp3h;
    @ApiModelProperty(value = "最近6小时降雨统计")
    @ExcelProperty("最近6小时")
    private String drp6h;
    @ApiModelProperty(value = "最近12小时降雨统计")
    @ExcelProperty("最近12小时")
    private String drp12h;
    @ApiModelProperty(value = "最近24小时降雨统计")
    @ExcelProperty("最近24小时")
    private String drp24h;
    @ApiModelProperty(value = "今日累计降雨统计")
    @ExcelIgnore
    private String drpday;

    @ApiModelProperty(value = "时间")
    @ExcelIgnore
    private LocalDateTime systm;

    public Integer getSortno() {
        return sortno;
    }

    public void setSortno(Integer sortno) {
        this.sortno = sortno;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getSadnm() {
        return sadnm;
    }

    public void setSadnm(String sadnm) {
        this.sadnm = sadnm;
    }

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public String getDrp5m() {
        return drp5m;
    }

    public void setDrp5m(String drp5m) {
        this.drp5m = drp5m;
    }

    public String getDrp10m() {
        return drp10m;
    }

    public void setDrp10m(String drp10m) {
        this.drp10m = drp10m;
    }

    public String getDrp30m() {
        return drp30m;
    }

    public void setDrp30m(String drp30m) {
        this.drp30m = drp30m;
    }

    public String getDrp1h() {
        return drp1h;
    }

    public void setDrp1h(String drp1h) {
        this.drp1h = drp1h;
    }

    public String getDrp3h() {
        return drp3h;
    }

    public void setDrp3h(String drp3h) {
        this.drp3h = drp3h;
    }

    public String getDrp6h() {
        return drp6h;
    }

    public void setDrp6h(String drp6h) {
        this.drp6h = drp6h;
    }

    public String getDrp12h() {
        return drp12h;
    }

    public void setDrp12h(String drp12h) {
        this.drp12h = drp12h;
    }

    public String getDrp24h() {
        return drp24h;
    }

    public void setDrp24h(String drp24h) {
        this.drp24h = drp24h;
    }

    public String getDrpday() {
        return drpday;
    }

    public void setDrpday(String drpday) {
        this.drpday = drpday;
    }

    public LocalDateTime getSystm() {
        return systm;
    }

    public void setSystm(LocalDateTime systm) {
        this.systm = systm;
    }
}
