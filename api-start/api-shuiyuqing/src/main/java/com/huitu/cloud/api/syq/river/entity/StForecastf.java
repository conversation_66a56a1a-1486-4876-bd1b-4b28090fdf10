package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 水情预报成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-11
 */
@TableName("ST_FORECAST_F")
@ApiModel(value = "StForecastF对象", description = "水情预报成果表")
public class StForecastf extends Model<StForecastf> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "预报单位")
    @TableField("UNITNAME")
    private String unitname;

    @ApiModelProperty(value = "方案代码")
    @TableField("PLCD")
    private String plcd;

    @ApiModelProperty(value = "依据时间")
    @TableField("FYMDH")
    private LocalDateTime fymdh;

    @ApiModelProperty(value = "发布时间")
    @TableField("IYMDH")
    private LocalDateTime iymdh;

    @ApiModelProperty(value = "发生时间")
    @TableField("YMDH")
    private LocalDateTime ymdh;

    @ApiModelProperty(value = "发生时间")
    @TableField("TM")
    private LocalDateTime tm;

    @ApiModelProperty(value = "预报水位")
    @TableField("Z")
    private BigDecimal z;

    @ApiModelProperty(value = "预报流量")
    @TableField("Q")
    private BigDecimal q;

    @ApiModelProperty(value = "告警状态（2：超保证，1：超警戒，0：正常）")
    private String wnstatus;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "站类")
    @TableField("STTP")
    private String sttp;

    @ApiModelProperty(value = "报汛等级")
    @TableField("FRGRD")
    private String frgrd;

    @ApiModelProperty(value = "测站方位")
    @TableField("STAZT")
    private BigDecimal stazt;

    @ApiModelProperty(value = "警戒水位")
    @TableField("WRZ")
    private BigDecimal wrz;

    @ApiModelProperty(value = "警戒流量")
    @TableField("WRQ")
    private BigDecimal wrq;

    @ApiModelProperty(value = "保证水位")
    @TableField("GRZ")
    private BigDecimal grz;

    @ApiModelProperty(value = "保证流量")
    @TableField("GRQ")
    private BigDecimal grq;

    @ApiModelProperty(value = "超警戒水位")
    private BigDecimal zwrz;

    @ApiModelProperty(value = "超警戒流量")
    private BigDecimal qwrq;

    @ApiModelProperty(value = "超保证水位")
    private BigDecimal zgrz;

    @ApiModelProperty(value = "超保证流量")
    private BigDecimal qgrq;

    @ApiModelProperty(value = "历史最高水位")
    @TableField("OBHTZ")
    private BigDecimal obhtz;

    @ApiModelProperty(value = "历史最高水位前10")
    @TableField("htzListTop10")
    List<StRvyevsqS> htzListTop10;

    @ApiModelProperty(value = "历史最大流量")
    @TableField("OBMXQ")
    private BigDecimal obmxq;

    @ApiModelProperty(value = "历史最大流量前10")
    @TableField("mxqListTop10")
    List<StRvyevsqS> mxqListTop10;

    @ApiModelProperty(value = "偏移经度")
    @TableField("PLGTD")
    private BigDecimal plgtd;

    @ApiModelProperty(value = "偏移纬度")
    @TableField("PLTTD")
    private BigDecimal plttd;

    @ApiModelProperty(value = "起始水位")
    @TableField("BGZ")
    private BigDecimal bgz;

    @ApiModelProperty(value = "起始流量")
    @TableField("BGQ")
    private BigDecimal bgq;

    public BigDecimal getWrz() {
        return wrz;
    }

    public void setWrz(BigDecimal wrz) {
        this.wrz = wrz;
    }

    public BigDecimal getWrq() {
        return wrq;
    }

    public void setWrq(BigDecimal wrq) {
        this.wrq = wrq;
    }

    public BigDecimal getGrz() {
        return grz;
    }

    public void setGrz(BigDecimal grz) {
        this.grz = grz;
    }

    public BigDecimal getGrq() {
        return grq;
    }

    public void setGrq(BigDecimal grq) {
        this.grq = grq;
    }

    public BigDecimal getZwrz() {
        return zwrz;
    }

    public void setZwrz(BigDecimal zwrz) {
        this.zwrz = zwrz;
    }

    public BigDecimal getQwrq() {
        return qwrq;
    }

    public void setQwrq(BigDecimal qwrq) {
        this.qwrq = qwrq;
    }

    public BigDecimal getZgrz() {
        return zgrz;
    }

    public void setZgrz(BigDecimal zgrz) {
        this.zgrz = zgrz;
    }

    public BigDecimal getQgrq() {
        return qgrq;
    }

    public void setQgrq(BigDecimal qgrq) {
        this.qgrq = qgrq;
    }

    public BigDecimal getObhtz() {
        return obhtz;
    }

    public void setObhtz(BigDecimal obhtz) {
        this.obhtz = obhtz;
    }

    public BigDecimal getObmxq() {
        return obmxq;
    }

    public void setObmxq(BigDecimal obmxq) {
        this.obmxq = obmxq;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getFrgrd() {
        return frgrd;
    }

    public void setFrgrd(String frgrd) {
        this.frgrd = frgrd;
    }

    public BigDecimal getStazt() {
        return stazt;
    }

    public void setStazt(BigDecimal stazt) {
        this.stazt = stazt;
    }

    public String getUnitname() {
        return unitname;
    }

    public void setUnitname(String unitname) {
        this.unitname = unitname;
    }

    public String getPlcd() {
        return plcd;
    }

    public void setPlcd(String plcd) {
        this.plcd = plcd;
    }

    public LocalDateTime getFymdh() {
        return fymdh;
    }

    public void setFymdh(LocalDateTime fymdh) {
        this.fymdh = fymdh;
    }

    public LocalDateTime getIymdh() {
        return iymdh;
    }

    public void setIymdh(LocalDateTime iymdh) {
        this.iymdh = iymdh;
    }

    public LocalDateTime getYmdh() {
        return ymdh;
    }

    public void setYmdh(LocalDateTime ymdh) {
        this.ymdh = ymdh;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public BigDecimal getZ() {
        return z;
    }

    public void setZ(BigDecimal z) {
        this.z = z;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public String getWnstatus() {
        return wnstatus;
    }

    public void setWnstatus(String wnstatus) {
        this.wnstatus = wnstatus;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    public BigDecimal getBgz() {
        return bgz;
    }

    public void setBgz(BigDecimal bgz) {
        this.bgz = bgz;
    }

    public BigDecimal getBgq() {
        return bgq;
    }

    public void setBgq(BigDecimal bgq) {
        this.bgq = bgq;
    }

    public List<StRvyevsqS> getHtzListTop10() {
        return htzListTop10;
    }

    public void setHtzListTop10(List<StRvyevsqS> htzListTop10) {
        this.htzListTop10 = htzListTop10;
    }

    public List<StRvyevsqS> getMxqListTop10() {
        return mxqListTop10;
    }

    public void setMxqListTop10(List<StRvyevsqS> mxqListTop10) {
        this.mxqListTop10 = mxqListTop10;
    }
}
