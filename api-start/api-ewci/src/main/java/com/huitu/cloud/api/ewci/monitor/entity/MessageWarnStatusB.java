package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 预警消息发送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("MESSAGEWARNSTATUS_B")
@ApiModel(value="MessageWarnStatusB对象", description="实况预警规则表")
public class MessageWarnStatusB extends Model<MessageWarnStatusB> {
    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = " 等级：0-默认划定，2-(0-10)，3-(10-25)，4-(25-50)，5-(50-100)，6-(100-250) ")
    @TableField("LEVEL")
    private String level;

    @ApiModelProperty(value = " 所属政区 ")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "手机号码")
    @TableField("SID")
    private String sid;

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }
}
