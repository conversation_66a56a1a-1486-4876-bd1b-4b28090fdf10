package com.huitu.cloud.api.ewci.person.exception;

import com.huitu.cloud.api.ewci.person.entity.BnsDikePersonTmp;

import java.util.List;

/**
 * 堤防责任人信息导入异常
 *
 * <AUTHOR>
 */
public class DikePersonImportException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    List<BnsDikePersonTmp> errorData;

    public List<BnsDikePersonTmp> getErrorData() {
        return errorData;
    }

    public void setErrorData(List<BnsDikePersonTmp> errorData) {
        this.errorData = errorData;
    }

    public DikePersonImportException(String message) {
        super(message);
    }

    public DikePersonImportException(String message, List<BnsDikePersonTmp> errorData) {
        super(message);

        this.errorData = errorData;
    }

    public DikePersonImportException(String message, Throwable cause) {
        super(message, cause);
    }

    public DikePersonImportException(String message, List<BnsDikePersonTmp> errorData, Throwable cause) {
        super(message, cause);

        this.errorData = errorData;
    }
}
