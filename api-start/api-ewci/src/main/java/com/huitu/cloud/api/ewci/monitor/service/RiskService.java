package com.huitu.cloud.api.ewci.monitor.service;

import com.huitu.cloud.api.ewci.monitor.entity.RainData;
import com.huitu.cloud.api.ewci.monitor.entity.RiskLocation;
import com.huitu.cloud.api.ewci.monitor.entity.RiskQuery;

import java.util.List;

/**
 * 风险 周边降雨
 *
 * <AUTHOR>
 */
public interface RiskService {

    /**
     * 根据测站编码查询政区、经度、纬度
     *
     * @param stcd 测站编码
     * @return
     */
    RiskLocation getLocation(String stcd);

    /**
     * 风险 周边降雨 行政or流域查询
     *
     * @param qo
     * @return
     */
    List<RainData> getRainfallList(RiskQuery qo);

}
