package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 提交检查报告参数
 */
@Data
@ApiModel(value = "提交检查报告参数")
public class BnsInspectSubmitReports implements Serializable {

    @ApiModelProperty(value = "检查报告ID")
    private String reportId;

    @ApiModelProperty(value = "报告确认单照片")
    private List<BnsInspectFile> photos;

    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("reportId", getReportId());
        params.put("photos", getPhotos());
        return params;
    }

}
