package com.huitu.cloud.api.syq.river.entity;

import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModelProperty;
/**
 * <AUTHOR>
 */
public class QueryMaxCxxsw  {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd",required = true)
    private String etm;
    @ApiModelProperty(value = "政区编码",required = true)
    @SqlInjection
    private String adcd;

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }
}
