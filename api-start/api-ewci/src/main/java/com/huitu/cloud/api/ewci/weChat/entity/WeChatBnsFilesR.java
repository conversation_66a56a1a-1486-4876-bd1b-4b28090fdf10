package com.huitu.cloud.api.ewci.weChat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04
 */
@TableName("BNS_FILES_R")
@ApiModel(value="BnsFilesR对象", description="文件表")
public class WeChatBnsFilesR extends Model<WeChatBnsFilesR> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "文件编码")
    @TableId(value = "filecd", type = IdType.NONE)
    private String filecd;

    @ApiModelProperty(value = "文件标题")
    private String filetitle;

    @ApiModelProperty(value = "文件文号")
    private String fileno;

    @ApiModelProperty(value = "文件类型:1水旱灾害简报、2防御中心文、3防御中心电、4防御中心函、5气象信息、6其他")
    private String filetype;

    @ApiModelProperty(value = "是否紧急")
    private String fileflg;

    @ApiModelProperty(value = "创建时间")
    private String ctm;

    @ApiModelProperty(value = "拟稿人")
    private String cer;

    @ApiModelProperty(value = "审核人")
    private String cher;

    @ApiModelProperty(value = "备注")
    private String nt;

    @ApiModelProperty(value = "政区")
    private String adcd;

    @ApiModelProperty(value = "文件内容")
    private String filedesc;

    @ApiModelProperty(value = "发送人")
    private String ser;

    @ApiModelProperty(value = "发送人单位")
    private String senddept;

    public String getSenddept() {
        return senddept;
    }

    public void setSenddept(String senddept) {
        this.senddept = senddept;
    }

    public String getFilecd() {
        return filecd;
    }

    public void setFilecd(String filecd) {
        this.filecd = filecd;
    }

    public String getFiletitle() {
        return filetitle;
    }

    public void setFiletitle(String filetitle) {
        this.filetitle = filetitle;
    }

    public String getFileno() {
        return fileno;
    }

    public void setFileno(String fileno) {
        this.fileno = fileno;
    }

    public String getFiletype() {
        return filetype;
    }

    public void setFiletype(String filetype) {
        this.filetype = filetype;
    }

    public String getFileflg() {
        return fileflg;
    }

    public void setFileflg(String fileflg) {
        this.fileflg = fileflg;
    }

    public String getCtm() {
        return ctm;
    }

    public void setCtm(String ctm) {
        this.ctm = ctm;
    }

    public String getCer() {
        return cer;
    }

    public void setCer(String cer) {
        this.cer = cer;
    }

    public String getCher() {
        return cher;
    }

    public void setCher(String cher) {
        this.cher = cher;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getFiledesc() {
        return filedesc;
    }

    public void setFiledesc(String filedesc) {
        this.filedesc = filedesc;
    }

    public String getSer() {
        return ser;
    }

    public void setSer(String ser) {
        this.ser = ser;
    }

    @Override
    protected Serializable pkVal() {
        return this.filecd;
    }

    @Override
    public String toString() {
        return "BnsFilesR{" +
        "filecd=" + filecd +
        ", filetitle=" + filetitle +
        ", fileno=" + fileno +
        ", filetype=" + filetype +
        ", fileflg=" + fileflg +
        ", ctm=" + ctm +
        ", cer=" + cer +
        ", cher=" + cher +
        ", nt=" + nt +
        ", adcd=" + adcd +
        ", filedesc=" + filedesc +
        ", ser=" + ser +
        "}";
    }
}
