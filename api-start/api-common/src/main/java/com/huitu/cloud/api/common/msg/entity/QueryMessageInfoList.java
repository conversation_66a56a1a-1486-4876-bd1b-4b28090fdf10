package com.huitu.cloud.api.common.msg.entity;

import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 * @since 2019-09-22
 */
public class QueryMessageInfoList extends PageBean {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd")
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd")
    private String etm;
    @ApiModelProperty(value = "政区编码")
    @SqlInjection
    private String adcd;
    @ApiModelProperty(value = "短信类型编号 10-内部预警消息； 20-外部预警消息")
    private Integer msgTypeId;
    @ApiModelProperty(value = " 短信发送方式 10-短信； 20-传真； 30-广播  ")
    private String mediaId;
    @ApiModelProperty(value = " 预警id")
    private String warnId;
    @ApiModelProperty(value = " 发送状态 （0：失败  1：成功  2：请求发送）")
    private String status;
    @ApiModelProperty(value = " 短信编码")
    private String msgId;

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getMsgTypeId() {
        return msgTypeId;
    }

    public void setMsgTypeId(Integer msgTypeId) {
        this.msgTypeId = msgTypeId;
    }

    public String getMediaId() {
        return mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }
}
