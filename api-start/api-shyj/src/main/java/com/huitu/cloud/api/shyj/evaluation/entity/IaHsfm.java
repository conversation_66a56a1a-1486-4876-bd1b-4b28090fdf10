package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * <p>
 * 沟道历史洪痕测量点表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-18
 */
@TableName("IA_M_HSFM")
@ApiModel(value="IaMHsfm对象", description="沟道历史洪痕测量点表")
public class IaHsfm extends  RelevantInfo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "洪痕编号")
    @TableId(value = "FMCD", type = IdType.NONE)
    private String fmcd;

    @ApiModelProperty(value = "所在沟道")
    @TableField("CHANNEL")
    private String channel;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "测量方法")
    @TableField("METHOD")
    private String method;

    @ApiModelProperty(value = "是否跨县")
    @TableField("ISCTOWN")
    private String isctown;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private Double lttd;

    @ApiModelProperty(value = "高程")
    @TableField("ELE")
    private Double ele;

    @ApiModelProperty(value = "洪水场次")
    @TableField("FLOODM")
    private String floodm;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalTime moditime;


    public String getFmcd() {
        return fmcd;
    }

    public void setFmcd(String fmcd) {
        this.fmcd = fmcd;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getIsctown() {
        return isctown;
    }

    public void setIsctown(String isctown) {
        this.isctown = isctown;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public Double getEle() {
        return ele;
    }

    public void setEle(Double ele) {
        this.ele = ele;
    }

    public String getFloodm() {
        return floodm;
    }

    public void setFloodm(String floodm) {
        this.floodm = floodm;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalTime moditime) {
        this.moditime = moditime;
    }


}
