package com.huitu.cloud.api.screen.fhjz.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value="水库工程详情对象", description="水库工程详情对象")
public class RsvrDetailVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "水库编号")
    private String resCode;

    @ApiModelProperty(value = "水库名称")
    private String resName;

    @ApiModelProperty(value = "所在河流")
    private String rvName;

    @ApiModelProperty(value = "所在地")
    private String adnm;

    @ApiModelProperty(value = "防洪保护影响对象")
    private String protectObj;

    @ApiModelProperty(value = "集雨面积")
    private String watShedArea;

    @ApiModelProperty(value = "总库容")
    private String totCap;

    @ApiModelProperty(value = "主坝坝型")
    private String damTypeStr;

    @ApiModelProperty(value = "主坝长")
    private String dmszlen;

    @ApiModelProperty(value = "最大坝高")
    private String damMaxHeig;

    @ApiModelProperty(value = "坝顶高程")
    private String dmtpel;

    @ApiModelProperty(value = "校核标准")
    private String chflst;

    @ApiModelProperty(value = "校核洪水位")
    private String chfllv;

    @ApiModelProperty(value = "设计标准")
    private String dsflst;

    @ApiModelProperty(value = "设计洪水位")
    private String dsfllv;

    @ApiModelProperty(value = "正常蓄水位")
    private String normWatLev;

    @ApiModelProperty(value = "上游赔偿高程")
    private String uph;

    @ApiModelProperty(value = "历史最大入库洪峰流量")
    private String hmerfl;

    @ApiModelProperty(value = "历史最大入库洪峰流量-相应出现日期")
    private String hmerflt;

    @ApiModelProperty(value = "历史最大出库洪峰流量")
    private String mxexrsfl;

    @ApiModelProperty(value = "历史最大出库洪峰流量-相应出现日期")
    private String merftm;

    @ApiModelProperty(value = "历史最高蓄水位")
    private String hshgwtlv;

    @ApiModelProperty(value = "历史最高蓄水位-相应出现日期")
    private String hhwltm;

    @ApiModelProperty(value = "日期范围")
    private String dateRange;

    @ApiModelProperty(value = "汛限水位")
    private String fsltdz;

    @ApiModelProperty(value = "汛限库容")
    private String fsltdw;

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getProtectObj() {
        return protectObj;
    }

    public void setProtectObj(String protectObj) {
        this.protectObj = protectObj;
    }

    public String getWatShedArea() {
        return watShedArea;
    }

    public void setWatShedArea(String watShedArea) {
        this.watShedArea = watShedArea;
    }

    public String getTotCap() {
        return totCap;
    }

    public void setTotCap(String totCap) {
        this.totCap = totCap;
    }

    public String getDamTypeStr() {
        return damTypeStr;
    }

    public void setDamTypeStr(String damTypeStr) {
        this.damTypeStr = damTypeStr;
    }

    public String getDmszlen() {
        return dmszlen;
    }

    public void setDmszlen(String dmszlen) {
        this.dmszlen = dmszlen;
    }

    public String getDamMaxHeig() {
        return damMaxHeig;
    }

    public void setDamMaxHeig(String damMaxHeig) {
        this.damMaxHeig = damMaxHeig;
    }

    public String getDmtpel() {
        return dmtpel;
    }

    public void setDmtpel(String dmtpel) {
        this.dmtpel = dmtpel;
    }

    public String getChflst() {
        return chflst;
    }

    public void setChflst(String chflst) {
        this.chflst = chflst;
    }

    public String getChfllv() {
        return chfllv;
    }

    public void setChfllv(String chfllv) {
        this.chfllv = chfllv;
    }

    public String getDsflst() {
        return dsflst;
    }

    public void setDsflst(String dsflst) {
        this.dsflst = dsflst;
    }

    public String getDsfllv() {
        return dsfllv;
    }

    public void setDsfllv(String dsfllv) {
        this.dsfllv = dsfllv;
    }

    public String getNormWatLev() {
        return normWatLev;
    }

    public void setNormWatLev(String normWatLev) {
        this.normWatLev = normWatLev;
    }

    public String getUph() {
        return uph;
    }

    public void setUph(String uph) {
        this.uph = uph;
    }

    public String getHmerfl() {
        return hmerfl;
    }

    public void setHmerfl(String hmerfl) {
        this.hmerfl = hmerfl;
    }

    public String getHmerflt() {
        return hmerflt;
    }

    public void setHmerflt(String hmerflt) {
        this.hmerflt = hmerflt;
    }

    public String getMerftm() {
        return merftm;
    }

    public void setMerftm(String merftm) {
        this.merftm = merftm;
    }

    public String getHhwltm() {
        return hhwltm;
    }

    public void setHhwltm(String hhwltm) {
        this.hhwltm = hhwltm;
    }

    public String getMxexrsfl() {
        return mxexrsfl;
    }

    public void setMxexrsfl(String mxexrsfl) {
        this.mxexrsfl = mxexrsfl;
    }


    public String getHshgwtlv() {
        return hshgwtlv;
    }

    public void setHshgwtlv(String hshgwtlv) {
        this.hshgwtlv = hshgwtlv;
    }


    public String getDateRange() {
        return dateRange;
    }

    public void setDateRange(String dateRange) {
        this.dateRange = dateRange;
    }

    public String getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(String fsltdz) {
        this.fsltdz = fsltdz;
    }

    public String getFsltdw() {
        return fsltdw;
    }

    public void setFsltdw(String fsltdw) {
        this.fsltdw = fsltdw;
    }
}
