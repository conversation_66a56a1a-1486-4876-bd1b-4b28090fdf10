package com.huitu.cloud.api.ewci.soilnews.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.soilnews.entity.BsnSoilNewsB;
import com.huitu.cloud.api.ewci.soilnews.mapper.BsnSoilNewsBDao;
import com.huitu.cloud.api.ewci.soilnews.service.SoilNewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class SoilNewsServiceImpl implements SoilNewsService {

    private BsnSoilNewsBDao baseDao;

    @Autowired
    public void setBaseDao(BsnSoilNewsBDao baseDao) {
        this.baseDao = baseDao;
    }


    @Override
    public IPage<BsnSoilNewsB> getSoilNewsList(String periods, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> params = new HashMap<>();
        params.put("periods", periods);
        return baseDao.getSoilNewsList(page, params);
    }
}
