<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.datamanager.rule.mapper.BsnZrrulebDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.datamanager.rule.entity.BsnZrruleb">
        <id column="STCD" property="stcd" />
        <result column="UPV" property="upv" />
        <result column="DWV" property="dwv" />
        <result column="CHV" property="chv" />
        <result column="NT" property="nt" />
        <result column="MODITIME" property="moditime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        STCD, UPV, DWV, CHV, NT, MODITIME
    </sql>
    <select id="getZrRule" resultType="com.huitu.cloud.api.datamanager.rule.entity.ZrruleVo">
	select a.STCD, STNM,UPV, DWV, CHV, NT, a.MODITIME  from BSN_ZRRULE_B a,ST_STBPRP_B b
		where a.stcd=b.stcd
        <if test="stnm !=null and stnm !=''">
            AND CHARINDEX(#{stnm},STNM)>0
        </if>
        <if test="sttp!=null and sttp=='ZZ'">
            and (STTP='ZZ' or STTP='ZQ')
        </if>
        <if test="sttp!=null and sttp=='RR'">
            and (STTP='RR' or STTP='RQ')
        </if>
        <if test="ad !=null and ad !=''">
            AND addvcd like '${ad}%'
        </if>
        order by a.MODITIME desc
    </select>

</mapper>
