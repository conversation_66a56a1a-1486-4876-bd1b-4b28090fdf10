<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rcs.mapper.BnsRcsEngDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getSummary" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsEngSummary">
        SELECT A.ADCD, A.ADNM, (RES_COUNT + DIKE_COUNT + WAGA_COUNT + FSDA_COUNT) TOTAL_COUNT, RES_COUNT, DIKE_COUNT,
        DIKE_LENGTH, WAGA_COUNT, FSDA_COUNT
        FROM BSN_ADCD_B A,
        (SELECT COUNT(RES_CODE) RES_COUNT FROM BNS_RCS_RES
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) R,
        (SELECT COUNT(DIKE_CODE) DIKE_COUNT FROM BNS_RCS_DIKE
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) DC,
        (SELECT ISNULL(SUM(DIKE_LEN), 0) DIKE_LENGTH FROM BNS_RCS_DIKE
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) DL,
        (SELECT COUNT(WAGA_CODE) WAGA_COUNT FROM BNS_RCS_WAGA
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) W,
        (SELECT COUNT(FSDA_CODE) FSDA_COUNT FROM BNS_RCS_FSDA
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) F
        WHERE A.ADCD = #{map.adcd}
    </select>
    <select id="getStatListByAdcd" resultType="com.huitu.cloud.api.ewci.rcs.entity.RcsAdStat">
        SELECT E.ADCD, A.ADNM, E.ENTYPE, COUNT(E.ENCODE) TOTAL_COUNT
        FROM (
        SELECT LEFT(LEFT(ADCD, #{map.lowerLevel}) + '000000000000000', 15) ADCD, ENTYPE, ENCODE FROM BNS_RCS_ENG
        WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000', ENTYPE, ENCODE FROM BNS_RCS_ENG WHERE LEFT(ADCD, 6) = '220581'
        </if>
        ) E
        LEFT JOIN BSN_ADCD_B A ON A.ADCD = E.ADCD
        GROUP BY E.ADCD, A.ADNM, E.ENTYPE
        ORDER BY E.ADCD ASC
    </select>
</mapper>
