package com.huitu.cloud.api.shyj.warn.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 */
public class QueryWarnRelatedInfo extends PageBean {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "政区编码",required = true)
    private String adcd;
    @ApiModelProperty(value = "是否只查询未结束预警('1':查询未关闭的)")
    private String queryType;
    @ApiModelProperty(value = "预警状态")
    private String warnStatusId;
    @ApiModelProperty(value = "预警等级")
    private String warnGradeId;

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public String getWarnStatusId() {
        return warnStatusId;
    }

    public void setWarnStatusId(String warnStatusId) {
        this.warnStatusId = warnStatusId;
    }

    public String getWarnGradeId() {
        return warnGradeId;
    }

    public void setWarnGradeId(String warnGradeId) {
        this.warnGradeId = warnGradeId;
    }
}
