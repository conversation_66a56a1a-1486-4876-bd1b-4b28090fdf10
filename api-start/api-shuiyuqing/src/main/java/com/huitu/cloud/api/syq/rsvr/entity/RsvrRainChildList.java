package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024年6月20日
 */
@TableName("RsvrRainChildList")
@ApiModel(value="RsvrRainChildList对象", description="")
public class RsvrRainChildList extends Model<RsvrRainChildList> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "水库编码")
    @TableField(value = "RES_CODE")
    private String resCode;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "政区")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "累计雨量")
    @TableField(value = "DRPS")
    private String drps;

    @ApiModelProperty(value = "未来三小时降雨")
    @TableField(value = "FDRPS")
    private String fdrps;

    @ApiModelProperty(value = "前期24小时降雨")
    @TableField(value = "LDRPS")
    private String ldrps;

    @ApiModelProperty(value = "测站归属类别")
    @TableField(value = "STADTP")
    private String stadtp;

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getDrps() {
        return drps;
    }

    public void setDrps(String drps) {
        this.drps = drps;
    }

    public String getFdrps() {
        return fdrps;
    }

    public void setFdrps(String fdrps) {
        this.fdrps = fdrps;
    }

    public String getLdrps() {
        return ldrps;
    }

    public void setLdrps(String ldrps) {
        this.ldrps = ldrps;
    }

    public String getStadtp() {
        return stadtp;
    }

    public void setStadtp(String stadtp) {
        this.stadtp = stadtp;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
