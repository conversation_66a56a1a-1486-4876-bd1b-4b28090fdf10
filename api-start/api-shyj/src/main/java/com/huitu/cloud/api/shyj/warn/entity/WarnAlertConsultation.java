package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会商研判内容表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("WARN_ALERT_CONSULTATION")
@ApiModel(value="WarnAlertConsultation对象", description="会商研判内容表")
public class WarnAlertConsultation extends Model<WarnAlertConsultation> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "预警编码 ")
    @TableId(value = "ALERTID", type = IdType.NONE)
    private String alertId;

    @ApiModelProperty(value = " 会商内容")
    @TableField("CONSULTATIONINFO")
    private String consultationInfo;

    @ApiModelProperty(value = "填写人 ")
    @TableField("WRITER")
    private String writer;

    @ApiModelProperty(value = "记录时间")
    @TableField("WRITETIME")
    private LocalDateTime writeTime;


    public String getAlertId() {
        return alertId;
    }

    public void setAlertId(String alertId) {
        this.alertId = alertId;
    }

    public String getConsultationInfo() {
        return consultationInfo;
    }

    public void setConsultationInfo(String consultationInfo) {
        this.consultationInfo = consultationInfo;
    }

    public String getWriter() {
        return writer;
    }

    public void setWriter(String writer) {
        this.writer = writer;
    }

    public LocalDateTime getWriteTime() {
        return writeTime;
    }

    public void setWriteTime(LocalDateTime writeTime) {
        this.writeTime = writeTime;
    }

    @Override
    public String toString() {
        return "WarnAlertConsultation{" +
        "alertid=" + alertId +
        ", consultationinfo=" + consultationInfo +
        ", writer=" + writer +
        ", writetime=" + writeTime +
        "}";
    }
}
