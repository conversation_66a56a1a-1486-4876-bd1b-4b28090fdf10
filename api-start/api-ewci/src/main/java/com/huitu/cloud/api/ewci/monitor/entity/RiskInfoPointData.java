package com.huitu.cloud.api.ewci.monitor.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 风险隐患点
 *
 * <AUTHOR>
 */
@ApiModel(value = "风险隐患点")
public class RiskInfoPointData implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    private String adnm;

    @ApiModelProperty(value = "类型 1： 水库，1-1： 病险-未加固，1-2： 病险-正在加固，1-3： 无开敞式溢洪道，1-4： 病险-已加固，2： 险工险段，3： 河道，4： 山洪，5： 沙基砂堤未治理段，6： 主要江河未达标堤")
    private String ifmptp;

    @ApiModelProperty(value = "类型名称")
    private String ifmptpnm;

    @ApiModelProperty(value = "规模/级别")
    private String type;

    @ApiModelProperty(value = "风险点名称")
    private String ifmpnm;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getIfmptp() {
        return ifmptp;
    }

    public void setIfmptp(String ifmptp) {
        this.ifmptp = ifmptp;
    }

    public String getIfmptpnm() {
        return ifmptpnm;
    }

    public void setIfmptpnm(String ifmptpnm) {
        this.ifmptpnm = ifmptpnm;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIfmpnm() {
        return ifmpnm;
    }

    public void setIfmpnm(String ifmpnm) {
        this.ifmpnm = ifmpnm;
    }
}
