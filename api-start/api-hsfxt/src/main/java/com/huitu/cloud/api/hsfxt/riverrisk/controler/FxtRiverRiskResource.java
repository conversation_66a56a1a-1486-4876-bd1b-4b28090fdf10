package com.huitu.cloud.api.hsfxt.riverrisk.controler;


import com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmapVo;
import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.*;
import com.huitu.cloud.entity.PageBean;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import org.springframework.http.ResponseEntity;


import com.huitu.cloud.api.hsfxt.riverrisk.service.FxtRiverRiskService;
import com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmap;
/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */

@RestController
@Api(tags = "风险图-河道风险图接口")
@RequestMapping("/api/hsfxt/river-risk")
    public class FxtRiverRiskResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
            return "00000000-0000-0000-0000-000000000000";
            }
    @Override
    public String getVersion() {
            return "1.0";
            }
    @Autowired
    private FxtRiverRiskService baseService;

    @ApiOperation(value = "修改",notes="根据主键修改信息")
    @PutMapping(value = "update")
    public ResponseEntity<SuccessResponse<String>> updateRvRiskmap(@RequestBody  RvRiskmap entity) throws Exception {
            Boolean flag = baseService.updateById(entity);
            return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
     }
    @ApiOperation(value = "添加",notes="新增一条信息")
    @PostMapping(value = "add")
    public ResponseEntity<SuccessResponse<String>> addRvRiskmap(@RequestBody  RvRiskmap entity) throws Exception {
            Boolean flag = baseService.save(entity);
            return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
     }
    @ApiOperation(value = "分页查询",notes="分页查询【RV_RISKMAP】表")
    @PostMapping(value = "select-by-page")
    public ResponseEntity<SuccessResponse<Page<RvRiskmap>>> getRvRiskmapByPage(@RequestBody PageBean baseDao) throws Exception {
            Page<RvRiskmap> page = new Page<>(baseDao.getPageNum(),baseDao.getPageSize());
            IPage<RvRiskmap> list = baseService.page(page,null);
            return ResponseEntity.ok(
            new SuccessResponse(this, "OK", list));
     }

    @ApiOperation(value = "查询河道风险信息", notes = "查询河道风险信息")
    @GetMapping(value = "select-river-risk-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "rvcode", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "name", value = "河流名称", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "int", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "int", example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<RvRiskmapVo>>> getRiverRiskList(@RequestParam String rvcode, @RequestParam String name, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<RvRiskmapVo> list = baseService.getRiverRiskList(rvcode, name, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
}







