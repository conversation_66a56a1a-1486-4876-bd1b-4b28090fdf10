package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 需防洪治理山洪沟基本情况成果
 *
 * <AUTHOR>
 */
@ApiModel(value = "需防洪治理山洪沟基本情况成果")
public class IaCGully implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "山洪沟编码")
    @TableField(value = "GULLYCD")
    private String gullycd;

    @ApiModelProperty(value = "山洪沟名称")
    @TableField(value = "NAME")
    private String name;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "小流域代码")
    @TableField(value = "wscd")
    private String wscd;

    @ApiModelProperty(value = "小流域名称")
    @TableField(value = "WSNM")
    private String wsnm;

    @ApiModelProperty(value = "集水面积")
    @TableField(value = "CAREA")
    private Double carea;

    @ApiModelProperty(value = "沟道长度")
    @TableField(value = "CHLENGTH")
    private Double chlength;

    @ApiModelProperty(value = "沟道比降")
    @TableField(value = "CHPERCENT")
    private Double chpercent;

    @ApiModelProperty(value = "有无设防")
    @TableField(value = "FCATION")
    private String fcation;

    @ApiModelProperty(value = "现状防洪标准")
    @TableField(value = "FSTAND")
    private String fstand;

    @ApiModelProperty(value = "已有堤防防护工程长度")
    @TableField(value = "DIKELEN")
    private Double dikelen;

    @ApiModelProperty(value = "已有护岸防护工程长度")
    @TableField(value = "RTLEN")
    private Double rtlen;

    @ApiModelProperty(value = "受影响乡镇")
    @TableField(value = "TOWNS")
    private Integer towns;

    @ApiModelProperty(value = "受影响行政村")
    @TableField(value = "XZC")
    private Integer xzc;

    @ApiModelProperty(value = "受影响自然村")
    @TableField(value = "ZRC")
    private Integer zrc;

    @ApiModelProperty(value = "影响人口")
    @TableField(value = "PCOUNT")
    private Integer pcount;

    @ApiModelProperty(value = "影响耕地")
    @TableField(value = "LAND")
    private Double land;

    @ApiModelProperty(value = "影响重要公共基础设施")
    @TableField(value = "PFCOUNT")
    private Integer pfcount;

    @ApiModelProperty(value = "建国以来山洪发生次数")
    @TableField(value = "FCOUNT")
    private Integer fcount;

    @ApiModelProperty(value = "建国以来死亡（失踪）人数")
    @TableField(value = "DCOUNT")
    private Integer dcount;

    @ApiModelProperty(value = "治理措施")
    @TableField(value = "CPROGRAM")
    private String cprogram;

    public String getGullycd() {
        return gullycd;
    }

    public void setGullycd(String gullycd) {
        this.gullycd = gullycd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public Double getCarea() {
        return carea;
    }

    public void setCarea(Double carea) {
        this.carea = carea;
    }

    public Double getChlength() {
        return chlength;
    }

    public void setChlength(Double chlength) {
        this.chlength = chlength;
    }

    public Double getChpercent() {
        return chpercent;
    }

    public void setChpercent(Double chpercent) {
        this.chpercent = chpercent;
    }

    public String getFcation() {
        return fcation;
    }

    public void setFcation(String fcation) {
        this.fcation = fcation;
    }

    public String getFstand() {
        return fstand;
    }

    public void setFstand(String fstand) {
        this.fstand = fstand;
    }

    public Double getDikelen() {
        return dikelen;
    }

    public void setDikelen(Double dikelen) {
        this.dikelen = dikelen;
    }

    public Double getRtlen() {
        return rtlen;
    }

    public void setRtlen(Double rtlen) {
        this.rtlen = rtlen;
    }

    public Integer getTowns() {
        return towns;
    }

    public void setTowns(Integer towns) {
        this.towns = towns;
    }

    public Integer getXzc() {
        return xzc;
    }

    public void setXzc(Integer xzc) {
        this.xzc = xzc;
    }

    public Integer getZrc() {
        return zrc;
    }

    public void setZrc(Integer zrc) {
        this.zrc = zrc;
    }

    public Integer getPcount() {
        return pcount;
    }

    public void setPcount(Integer pcount) {
        this.pcount = pcount;
    }

    public Double getLand() {
        return land;
    }

    public void setLand(Double land) {
        this.land = land;
    }

    public Integer getPfcount() {
        return pfcount;
    }

    public void setPfcount(Integer pfcount) {
        this.pfcount = pfcount;
    }

    public Integer getFcount() {
        return fcount;
    }

    public void setFcount(Integer fcount) {
        this.fcount = fcount;
    }

    public Integer getDcount() {
        return dcount;
    }

    public void setDcount(Integer dcount) {
        this.dcount = dcount;
    }

    public String getCprogram() {
        return cprogram;
    }

    public void setCprogram(String cprogram) {
        this.cprogram = cprogram;
    }
}
