package com.huitu.cloud.api.syq.river.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel(value = "RiverWarnByAdcdTmVo",description = "河道告警时间段统计类")
public class RiverWarnByAdcdTmVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "测站编码")
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "最大动态流量")
    private BigDecimal maxQ;
    @ApiModelProperty(value = "最小动态流量")
    private BigDecimal minQ;
    @ApiModelProperty(value = "警戒流量")
    private BigDecimal wrq;
    @ApiModelProperty(value = "超警戒集合")
    private List<RiverStWarnByAdcdTmVo> warnWrzList;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public BigDecimal getMaxQ() {
        return maxQ;
    }

    public void setMaxQ(BigDecimal maxQ) {
        this.maxQ = maxQ;
    }

    public BigDecimal getMinQ() {
        return minQ;
    }

    public void setMinQ(BigDecimal minQ) {
        this.minQ = minQ;
    }

    public BigDecimal getWrq() {
        return wrq;
    }

    public void setWrq(BigDecimal wrq) {
        this.wrq = wrq;
    }

    public List<RiverStWarnByAdcdTmVo> getWarnWrzList() {
        return warnWrzList;
    }

    public void setWarnWrzList(List<RiverStWarnByAdcdTmVo> warnWrzList) {
        this.warnWrzList = warnWrzList;
    }
}
