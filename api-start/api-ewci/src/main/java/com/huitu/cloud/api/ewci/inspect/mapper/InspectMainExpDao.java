package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectMainExp;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 运行维护经费
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectMainExpDao {

    /**
     * 判断是否添加 运行维护经费
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectMainExp> getInspectMainExpList(@Param("adcd") String adcd, @Param("year") String year);

    /**
     * 添加 运行维护经费
     *
     * @param entity
     * @return
     */
    int insertInspectMainExp(BnsInspectMainExp entity);

    /**
     * 修改 运行维护经费
     *
     * @param entity
     * @return
     */
    int updateInspectMainExp(BnsInspectMainExp entity);

    /**
     * 删除 运行维护经费
     *
     * @return
     */
    int deleteInspectMainExp(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

