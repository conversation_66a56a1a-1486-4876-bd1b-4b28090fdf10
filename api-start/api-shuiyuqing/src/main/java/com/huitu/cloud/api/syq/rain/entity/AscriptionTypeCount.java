package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class AscriptionTypeCount implements Serializable {
    @ApiModelProperty(value = "水文站统计")
    private int total1;

    @ApiModelProperty(value = "山洪站统计")
    private int total2;

    @ApiModelProperty(value = "气象站统计")
    private int total3;

    @ApiModelProperty(value = "运管站统计")
    private int total4;

    @ApiModelProperty(value = "总计")
    private int total;

    public int getTotal1() {
        return total1;
    }

    public void setTotal1(int total1) {
        this.total1 = total1;
    }

    public int getTotal2() {
        return total2;
    }

    public void setTotal2(int total2) {
        this.total2 = total2;
    }

    public int getTotal3() {
        return total3;
    }

    public void setTotal3(int total3) {
        this.total3 = total3;
    }

    public int getTotal4() {
        return total4;
    }

    public void setTotal4(int total4) {
        this.total4 = total4;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}
