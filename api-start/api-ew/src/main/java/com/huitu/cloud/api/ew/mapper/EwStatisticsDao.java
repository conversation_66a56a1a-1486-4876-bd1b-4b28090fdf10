package com.huitu.cloud.api.ew.mapper;


import com.huitu.cloud.api.ew.entity.ext.EwRiverWarnGradeSummary;
import com.huitu.cloud.api.ew.entity.ext.EwRsvrWarnGradeSummary;
import com.huitu.cloud.api.ew.entity.ext.EwWarningSummary;
import com.huitu.cloud.api.ew.entity.ext.EwShWarnGradeSummary;
import com.huitu.cloud.api.ew.entity.response.EwWarnGradeStatisticsResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarnMessageStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwStatisticsDao {

    /**
     * 获取山洪监测预警汇总统计信息
     *
     * @param params 查询参数
     * @return 汇总统计信息
     */
    EwWarningSummary getShWarningSummary(@Param("map") Map<String, Object> params);

    /**
     * 获取水库监测预警汇总统计信息
     *
     * @param params 查询参数
     * @return 汇总统计信息
     */
    EwWarningSummary getRsvrWarningSummary(@Param("map") Map<String, Object> params);

    /**
     * 获取河道监测预警汇总统计信息
     *
     * @param params 查询参数
     * @return 汇总统计信息
     */
    EwWarningSummary getRiverWarningSummary(@Param("map") Map<String, Object> params);

    /**
     * 统计政区预警立即转移，准备转移的数量
     *
     * @param param
     * @return
     */
    List<EwWarnMessageStatistics> getAdWarnMessageStatistics(@Param("map") Map<String, Object> param);


    /**
     * 获取山洪预警等级汇总统计信息
     *
     * @param params 查询参数
     * @return 汇总统计信息
     */
    EwShWarnGradeSummary getShWarnGradeSummary(@Param("map") Map<String, Object> params);




    /**
     * 获取水库预警等级汇总统计信息
     *
     * @param params 查询参数
     * @return 汇总统计信息
     */
    EwRsvrWarnGradeSummary getRsvrWarnGradeSummary(@Param("map") Map<String, Object> params);



    /**
     * 获取河道等级预警汇总统计信息
     *
     * @param params 查询参数
     * @return 汇总统计信息
     */
    EwRiverWarnGradeSummary getRiverWarnGradeSummary(@Param("map") Map<String, Object> params);


    /**
     * 获取政区山洪预警等级统计列表
     * @param params 查询参数
     * @return 山洪预警等级统计列表
     */
    public List<EwWarnGradeStatisticsResponse> getAdWarnGradeList( @Param("map") Map<String, Object> params);




}
