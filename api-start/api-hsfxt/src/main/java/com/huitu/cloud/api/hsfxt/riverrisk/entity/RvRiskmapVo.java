package com.huitu.cloud.api.hsfxt.riverrisk.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@ApiModel(value="RvRiskmapVo对象", description="河道风险表")
public class RvRiskmapVo extends RvRiskmap {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "河流编码")
    private String riverCode;

    @ApiModelProperty(value = "父级河道编码")
    private String prvCode;

    @ApiModelProperty(value = "父级河道名称")
    private String prvName;

    @ApiModelProperty(value = "流域编码")
    private String basCode;

    public String getRiverCode() {
        return riverCode;
    }

    public void setRiverCode(String riverCode) {
        this.riverCode = riverCode;
    }

    public String getPrvCode() {
        return prvCode;
    }

    public void setPrvCode(String prvCode) {
        this.prvCode = prvCode;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public String getBasCode() {
        return basCode;
    }

    public void setBasCode(String basCode) {
        this.basCode = basCode;
    }
}
