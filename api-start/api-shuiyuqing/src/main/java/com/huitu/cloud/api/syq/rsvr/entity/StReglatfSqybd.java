package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;


/**
 * 多站水库水情预报返回实体类
 * <AUTHOR>
 */
@ApiModel(value="StReglatfSqybd", description="调度预报成果信息")
public class StReglatfSqybd extends  StReglatF{
    @ApiModelProperty(value = "预报入库流量")
    private String inq;
    @ApiModelProperty(value = "历史最高水位")
    private String hhrz;
    @ApiModelProperty(value = "坝顶高程")
    private String  damel;
    @ApiModelProperty(value = "死水位")
    private String ddz;
    @ApiModelProperty(value = "讯限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;
    @ApiModelProperty(value = "超讯限水位")
    @TableField("RZFSLTDZ")
    private BigDecimal rzfsltdz;
    @ApiModelProperty(value = "超正常高水位")
    @TableField("RZNORMZ")
    private BigDecimal rznormz;
    @ApiModelProperty(value = "正常高水位")
    @TableField("NORMZ")
    private BigDecimal normz;


    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public BigDecimal getRznormz() {
        return rznormz;
    }

    public void setRznormz(BigDecimal rznormz) {
        this.rznormz = rznormz;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }



    public String getInq() {
        return inq;
    }

    public void setInq(String inq) {
        this.inq = inq;
    }

    public String getHhrz() {
        return hhrz;
    }

    public void setHhrz(String hhrz) {
        this.hhrz = hhrz;
    }

    public String getDamel() {
        return damel;
    }

    public void setDamel(String damel) {
        this.damel = damel;
    }

    public String getDdz() {
        return ddz;
    }

    public void setDdz(String ddz) {
        this.ddz = ddz;
    }
}
