package com.huitu.cloud.api.ewci.consult.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.consult.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 防汛会商
 * </p>
 *
 * <AUTHOR>
 * @since 2024-4-17
 */
public interface ConsultDao {
    /**
     * 实时水情-水情统计-市县-河道 统计
     * @param param
     * @return
     */
    List<ConsultWaterAdRiverVo> getWaterAdRiverStatList(@Param("map") Map<String, Object> param);

    /**
     * 实时水情-水情统计-市县-水库 统计
     * @param param
     * @return
     */
    List<ConsultWaterAdRsvrVo> getWaterAdRsvrStatList(@Param("map") Map<String, Object> param);

    /**
     * 实时水情-水情统计-流域-河道 统计
     * @param param
     * @return
     */
    List<ConsultWaterBasRiverVo> getWaterBasRiverStatList(@Param("map") Map<String, Object> param);

    /**
     * 实时水情-水情统计-流域（19条主要江河）-河道 统计
     * @param param
     * @return
     */
    List<ConsultWaterBasRiverVo> getWaterBasZyjhRiverStatList(@Param("map") Map<String, Object> param);

    /**
     * 实时水情-水情统计-流域-水库 统计
     * @param param
     * @return
     */
    List<ConsultWaterBasRsvrVo> getWaterBasRsvrStatList(@Param("map") Map<String, Object> param);

    /**
     * 实时水情-水情统计-流域（19条主要江河）-水库 统计
     * @param param
     * @return
     */
    List<ConsultWaterBasRsvrVo> getWaterBasZyjhRsvrStatList(@Param("map") Map<String, Object> param);

    /**
     * 预警查看-24h预警-山洪预警 统计
     * @param param
     * @return
     */
    List<ConsultWarnAdVo> getWarnAdShStatList(@Param("map") Map<String, Object> param);

    /**
     * 预警查看-24h预警-河道预警 统计
     * @param param
     * @return
     */
    List<ConsultWarnAdVo> getWarnAdRiverStatList(@Param("map") Map<String, Object> param);

    /**
     * 预警查看-24h预警-水库预警 统计
     * @param param
     * @return
     */
    List<ConsultWarnAdVo> getWarnAdRsvrStatList(@Param("map") Map<String, Object> param);

    /**
     * 风险研判-超汛超警-市县-河道 统计
     * @param param
     * @return
     */
    List<ConsultRiskAdRiverVo> getRiskAdRiverStatList(@Param("map") Map<String, Object> param);

    /**
     * 风险研判-超汛超警-市县-水库 统计
     * @param param
     * @return
     */
    List<ConsultRiskAdRsvrVo> getRiskAdRsvrStatList(@Param("map") Map<String, Object> param);

    /**
     * 风险研判-超汛超警-流域-河道 统计
     * @param param
     * @return
     */
    List<ConsultRiskBasRiverVo> getRiskBasRiverStatList(@Param("map") Map<String, Object> param);

    /**
     * 风险研判-超汛超警-流域-水库 统计
     * @param param
     * @return
     */
    List<ConsultRiskBasRsvrVo> getRiskBasRsvrStatList(@Param("map") Map<String, Object> param);

    /**
     * 水情专报列表分页查询
     *
     * @param param
     * @return
     */
    IPage<BsnDefenseWorkPlanB> getPage(IPage<BsnDefenseWorkPlanB> page, @Param("map") Map<String, Object> param);

    /**
     * 获取最大期数
     *
     * @return
     */
    BsnDefenseWorkPlanB getMaxReportNo();

    /**
     * 新增水情专报
     *
     * @param entity
     * @return
     */
    int insert(BsnDefenseWorkPlanB entity);

    /**
     * 编辑水情专报
     *
     * @param entity
     * @return
     */
    int update(BsnDefenseWorkPlanB entity);

    /**
     * 修改文件路径
     *
     * @param dwpId
     * @return
     */
    int updateReportUrl(@Param("dwpId") String dwpId, @Param("fileName") String fileName, @Param("filePath") String filePath);



    BsnDefenseWorkPlanB getDocument(String dwpId);

    BsnDefenseWorkPlanB getFileById(String dwpId);


    /**
     * 修改发送状态
     *
     * @param pwdId
     * @return
     */
    int updateStatus(String pwdId);

    /**
     * 判断发文期数是否重复
     *
     * @param pwdId
     * @return
     */
    boolean isRepeatFileNo(String pwdId);
}
