package com.huitu.cloud.api.ewci.tencent.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

@ApiModel
public class PageParams extends PageBean {

    @ApiModelProperty(value = "政区编码")
    private String  adcd;

    @ApiModelProperty(value = "行政区级别")
    private Integer level;

    @ApiModelProperty(value = "当前用户id")
    private String userId;

    @ApiModelProperty(value = "开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String stm;

    @ApiModelProperty(value = "结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String etm;

    @ApiModelProperty(value = "会议主题")
    private String title;


    public void setAdcd(String adcd) {
        this.adcd = adcd;
        this.level = AdcdUtil.getAdLevel(adcd);
    }

    public String getAdcd() {
        return adcd;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
        return "PageParams{" +
                "adcd='" + adcd + '\'' +
                ", level=" + level +
                ", userId='" + userId + '\'' +
                ", stm='" + stm + '\'' +
                ", etm='" + etm + '\'' +
                ", title='" + title + '\'' +
                '}';
    }
}
