package com.huitu.cloud.api.ew.mapper;

import com.huitu.cloud.api.ew.entity.response.EwShMonitorDataResponse;
import com.huitu.cloud.api.ew.entity.response.EwSnapshotMonitorResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwSnapshotMonitorDao {

    /**
     * 根据预警ID和预警等级，获取对应的预警监测数据快照
     *
     * @param warnId      预警ID
     * @param warnGradeId 预警等级ID
     * @return 监测数据快照
     */
    EwSnapshotMonitorResponse getSnapshotMonitor(@Param("warnId") String warnId, @Param("warnGradeId") Integer warnGradeId);

    /**
     * 根据消息ID，获取对应的预警监测数据
     *
     * @param msgId 消息ID
     * @return 监测数据
     */
    EwShMonitorDataResponse getMonitorData(String msgId);
}
