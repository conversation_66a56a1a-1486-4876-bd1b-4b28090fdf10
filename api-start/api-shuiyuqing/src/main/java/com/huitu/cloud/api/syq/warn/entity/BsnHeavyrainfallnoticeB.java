package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 强降雨告警通告信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-29
 */
@TableName("BSN_HEAVYRAINFALLNOTICE_B")
@ApiModel(value="BsnHeavyrainfallnoticeB对象", description="强降雨告警通告信息表")
public class BsnHeavyrainfallnoticeB extends Model<BsnHeavyrainfallnoticeB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "强降雨通告id（参数不传）")
    @TableId(value = "HID", type = IdType.NONE)
    private String hid;

    @ApiModelProperty(value = "发送时间")
    @TableField("TM")
    private String tm;

    @ApiModelProperty(value = "年份（参数不传）")
    @TableField("YEAR")
    private String year;

    @ApiModelProperty(value = "期数")
    @TableField("HNO")
    private Integer hno;

    @ApiModelProperty(value = "通告内容")
    @TableField("HDESC")
    private String hdesc;

    @ApiModelProperty(value = "标题")
    @TableField("HTITLE")
    private String htitle;

    @ApiModelProperty(value = "编辑人员")
    @TableField("WRTER")
    private String writer;

    @ApiModelProperty(value = "审核人员")
    @TableField("VERIFY")
    private String verify;

    @ApiModelProperty(value = "签发人员")
    @TableField("ISSUE")
    private String issue;


    public String getHid() {
        return hid;
    }

    public BsnHeavyrainfallnoticeB setHid(String hid) {
        this.hid = hid;
        return this;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getTm() {
        return tm;
    }

    public String getYear() {
        return year;
    }

    public BsnHeavyrainfallnoticeB setYear(String year) {
        this.year = year;
        return this;
    }

    public Integer getHno() {
        return hno;
    }

    public BsnHeavyrainfallnoticeB setHno(Integer hno) {
        this.hno = hno;
        return this;
    }

    public String getHdesc() {
        return hdesc;
    }

    public BsnHeavyrainfallnoticeB setHdesc(String hdesc) {
        this.hdesc = hdesc;
        return this;
    }

    public String getHtitle() {
        return htitle;
    }

    public BsnHeavyrainfallnoticeB setHtitle(String htitle) {
        this.htitle = htitle;
        return this;
    }

    public String getWriter() {
        return writer;
    }

    public void setWriter(String writer) {
        this.writer = writer;
    }

    public String getVerify() {
        return verify;
    }

    public BsnHeavyrainfallnoticeB setVerify(String verify) {
        this.verify = verify;
        return this;
    }

    public String getIssue() {
        return issue;
    }

    public BsnHeavyrainfallnoticeB setIssue(String issue) {
        this.issue = issue;
        return this;
    }

    @Override
    protected Serializable pkVal() {
        return this.hid;
    }


}
