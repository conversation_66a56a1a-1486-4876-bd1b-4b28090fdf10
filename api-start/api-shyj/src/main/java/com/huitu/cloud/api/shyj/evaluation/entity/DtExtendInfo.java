package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 统计扩展信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-15
 */
@ApiModel(value="DtExtendInfo对象", description="统计扩展信息")
public class DtExtendInfo implements Serializable {
    @ApiModelProperty(value = "上级政区编码")
    private String padcd;
    @ApiModelProperty(value = "政区名称")
    private String adnm;

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
