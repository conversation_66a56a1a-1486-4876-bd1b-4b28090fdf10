package com.huitu.cloud.api.fusion.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 叫应统计响应对象
 */
@ApiModel(value = "叫应统计响应对象")
public class FusionCallStatisticsResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;
    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;
    @ApiModelProperty(value = "失败人数")
    @TableField(value = "FAIL_COUNT")
    private Integer failCount;
    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "DUTY_PHONE")
    private String dutyPhone;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }

    public String getDutyPhone() {
        return dutyPhone;
    }

    public void setDutyPhone(String dutyPhone) {
        this.dutyPhone = dutyPhone;
    }
}
