package com.huitu.cloud.api.screen.fhjz.controler;


import com.huitu.api.SuccessResponse;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.screen.fhjz.entity.ReservoirVo;
import com.huitu.cloud.api.screen.fhjz.entity.RsvrDetailVo;
import com.huitu.cloud.api.screen.fhjz.service.RealRainService;
import com.huitu.cloud.api.screen.fhjz.service.ReservoirService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 水库工程 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-16
 */

//@RestController
//@Api(tags = "一体机大屏(防洪减灾-水库工程)")
//@RequestMapping("/api/screen/reservoir")
public class ReservoirResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "B761C064-1554-4164-80CA-B52A17E42144";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private ReservoirService baseService;

    @ApiOperation(value = "水库列表查询",notes="水库列表查询")
    @GetMapping(value = "list")
    public ResponseEntity<SuccessResponse<List>> list(@RequestParam("engScal") String engScal, @RequestParam("cxxOrder") boolean cxxOrder,@RequestParam("stm") String stm,@RequestParam("etm") String etm, @RequestParam("resName") String resName) throws Exception {
            List<ReservoirVo> result= baseService.list(engScal,cxxOrder,stm,etm,resName);
            return ResponseEntity.ok(
                new SuccessResponse(result));
     }

    @ApiOperation(value = "水库详情列表查询",notes="水库详情列表查询")
    @GetMapping(value = "detail-list")
    public ResponseEntity<SuccessResponse<List>> detailList(@RequestParam("engScal") String engScal, @RequestParam("resName") String resName,@RequestParam("adcd") String adcd,@RequestParam("stm") String stm,@RequestParam("etm") String etm) throws Exception {
        List<RsvrDetailVo> result= baseService.detailList(engScal,resName,adcd,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }


    @ApiOperation(value = "水库测站查询",notes="水库测站查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "engScal", value = "水库规模", required = true, dataType = "String"),
            @ApiImplicitParam(name = "resCode", value = "水库编码", required = false, dataType = "String"),
    })
    @GetMapping(value = "sk-station-list")
    public ResponseEntity<SuccessResponse<List>> querySkStationList(@RequestParam("engScal") String engScal,@RequestParam("resCode") String resCode,@RequestParam("stm") String stm,@RequestParam("etm") String etm){
        List<Map> result= baseService.querySkStationList(engScal,resCode,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "查询水库的政区编码",notes="查询水库的政区编码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resCode", value = "水库编码", required = true, dataType = "String"),
    })
    @GetMapping(value = "querySkAdcd")
    public ResponseEntity<SuccessResponse<Map<String,Object>>> querySkAdcd(@RequestParam("resCode") String resCode){
        Map<String,Object> result= baseService.querySkAdcd(resCode);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }


    @ApiOperation(value = "根据政区统计水库数量详情",notes="根据政区统计水库数量详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
    })
    @GetMapping(value = "statisticRsvrDetail")
    public ResponseEntity<SuccessResponse<String>> statisticRsvrDetail(@RequestParam("adcd") String adcd){
        String result= baseService.statisticRsvrDetail(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

}







