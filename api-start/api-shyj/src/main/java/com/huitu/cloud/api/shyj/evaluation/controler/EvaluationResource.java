package com.huitu.cloud.api.shyj.evaluation.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import com.huitu.cloud.api.shyj.evaluation.service.EvaluationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 调查成果  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@RestController
@Api(tags = "调查成果接口")
@RequestMapping("/api/shyj/eton/dccg")
public class EvaluationResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "7b82b5bc-b561-70bc-087c-6266c1e4e59c";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private EvaluationService baseService;

    @ApiOperation(value = "查询无线广播站列表信息", notes = "查询无线广播站列表信息")
    @PostMapping(value = "select-wbrinfo-list")
    public ResponseEntity<SuccessResponse<Page<Wbrinfo>>> getWbrinfoList(@RequestBody QueryEvaluation queryEvaluation) throws Exception {
        IPage<Wbrinfo> list = baseService.getWbrinfoList(queryEvaluation.getAdcd(), queryEvaluation.getKey(), queryEvaluation.getWscd(), queryEvaluation.getWsnm(), queryEvaluation.getPageNum(), queryEvaluation.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询无线广播站工况信息", notes = "查询无线广播站工况信息")
    @GetMapping(value = "select-wireless-list")
    @ApiImplicitParams({@ApiImplicitParam(name = "racd", value = "广播站编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<BsnWirelessinfo>> getWirelessInfo(@RequestParam String racd) throws Exception {
        BsnWirelessinfo bsnWirelessinfo = baseService.getWirelessInfo(racd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", bsnWirelessinfo));
    }

    @ApiOperation(value = "查询无线广播站日志信息", notes = "查询无线广播站日志信息")
    @GetMapping(value = "select-wireless-log-list")
    @ApiImplicitParams({@ApiImplicitParam(name = "racd", value = "广播站编码", required = true, dataType = "String"), @ApiImplicitParam(name = "type", value = "类型", required = true, dataType = "String"), @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd", required = true, dataType = "String"), @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd", required = true, dataType = "String"), @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "int", example = "1"), @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "int", example = "10")})
    public ResponseEntity<SuccessResponse<Page<BsnWirelessinfoLog>>> getWirelessLogInfo(@RequestParam String racd, @RequestParam String stm, @RequestParam String etm, @RequestParam String type, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<BsnWirelessinfoLog> list = baseService.getWirelessLogInfo(racd, stm, etm, type, pageNum, pageSize);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询危险区列表信息", notes = "查询危险区列表信息")
    @PostMapping(value = "select-dan-list")
    public ResponseEntity<SuccessResponse<Page<IaDanad>>> getDanList(@RequestBody QueryIaDanad queryEvaluation) throws Exception {
        IPage<IaDanad> list = baseService.getDanList(queryEvaluation.getAdcd(), queryEvaluation.getKey(), queryEvaluation.getWscd(), queryEvaluation.getWsnm(), queryEvaluation.getName(), queryEvaluation.getAdnm(), queryEvaluation.getPrevtp(), queryEvaluation.getPageNum(), queryEvaluation.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询历史灾害列表信息", notes = "查询历史灾害列表信息")
    @PostMapping(value = "select-hsfwater-list")
    public ResponseEntity<SuccessResponse<Page<IaHsfwater>>> getHsfwaterList(@RequestBody QueryHsfwater queryEvaluation) throws Exception {
        IPage<IaHsfwater> list = baseService.getHsfwaterList(queryEvaluation.getAddress(), queryEvaluation.getAdcd(), queryEvaluation.getKey(), queryEvaluation.getWscd(), queryEvaluation.getWsnm(), queryEvaluation.getPrevtp(), queryEvaluation.getPageNum(), queryEvaluation.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询需防洪治理山洪沟列表信息", notes = "查询需防洪治理山洪沟列表信息")
    @PostMapping(value = "select-gully-list")
    public ResponseEntity<SuccessResponse<Page<IaGully>>> getGullyList(@RequestBody QueryIaGully queryIaGully) throws Exception {
        IPage<IaGully> list = baseService.getGullyList(queryIaGully.getAdcd(), queryIaGully.getKey(), queryIaGully.getName(), queryIaGully.getAdnm(), queryIaGully.getPageNum(), queryIaGully.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询塘（堰）坝工程调查成果列表信息", notes = "查询塘（堰）坝工程调查成果列表信息")
    @PostMapping(value = "select-dam-list")
    public ResponseEntity<SuccessResponse<Page<IaDaminfo>>> getDamList(@RequestBody QueryEvaluation queryEvaluation) throws Exception {
        IPage<IaDaminfo> list = baseService.getDamList(queryEvaluation.getAdcd(), queryEvaluation.getKey(), queryEvaluation.getWscd(), queryEvaluation.getWsnm(), queryEvaluation.getPageNum(), queryEvaluation.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询重要沿河村落居民户列表信息", notes = "查询重要沿河村落居民户列表信息")
    @PostMapping(value = "select-flrvvlg-list")
    public ResponseEntity<SuccessResponse<Page<IaFlrvvlg>>> getFlrvvlgList(@RequestBody QueryFlrvvlg queryEvaluation) throws Exception {
        IPage<IaFlrvvlg> list = baseService.getFlrvvlgList(queryEvaluation.getAdcd(), queryEvaluation.getKey(), queryEvaluation.getWscd(), queryEvaluation.getName(), queryEvaluation.getWsnm(), queryEvaluation.getPrevtp(), queryEvaluation.getPageNum(), queryEvaluation.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询简易雨量站列表信息", notes = "查询简易雨量站列表信息")
    @PostMapping(value = "select-srst-list")
    public ResponseEntity<SuccessResponse<Page<IaSrstinfo>>> getSrstList(@RequestBody QueryEvaluation queryEvaluation) throws Exception {
        IPage<IaSrstinfo> list = baseService.getSrstList(queryEvaluation.getAdcd(), queryEvaluation.getKey(), queryEvaluation.getWscd(), queryEvaluation.getWsnm(), queryEvaluation.getPageNum(), queryEvaluation.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询简易水位站列表信息", notes = "查询简易水位站列表信息")
    @PostMapping(value = "select-sws-list")
    public ResponseEntity<SuccessResponse<Page<IaSwstinfo>>> getSwsList(@RequestBody QueryEvaluation queryEvaluation) throws Exception {
        IPage<IaSwstinfo> list = baseService.getSwstList(queryEvaluation.getAdcd(), queryEvaluation.getKey(), queryEvaluation.getWscd(), queryEvaluation.getWsnm(), queryEvaluation.getPageNum(), queryEvaluation.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询防治区企事业单位列表信息", notes = "查询防治区企事业单位列表信息")
    @PostMapping(value = "select-bsnss-list")
    public ResponseEntity<SuccessResponse<Page<IaBsnssinfo>>> getBsnssList(@RequestBody QueryEvaluation queryEvaluation) throws Exception {
        IPage<IaBsnssinfo> list = baseService.getBsnsstList(queryEvaluation.getAdcd(), queryEvaluation.getKey(), queryEvaluation.getWscd(), queryEvaluation.getWsnm(), queryEvaluation.getPageNum(), queryEvaluation.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询重要城（集）镇居民列表信息", notes = "查询重要城（集）镇居民列表信息")
    @PostMapping(value = "select-dtresident-list")
    public ResponseEntity<SuccessResponse<Page<IaDtresident>>> getDtresidentList(@RequestBody QueryEvaluation queryEvaluation) throws Exception {
        IPage<IaDtresident> list = baseService.getDtresidentList(queryEvaluation.getAdcd(), queryEvaluation.getKey(), queryEvaluation.getWscd(), queryEvaluation.getWsnm(), queryEvaluation.getPageNum(), queryEvaluation.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询历史洪痕列表信息", notes = "查询历史洪痕列表信息")
    @PostMapping(value = "select-hsfm-list")
    public ResponseEntity<SuccessResponse<Page<IaHsfm>>> getIaHsfmList(@RequestBody QueryIaHsfm queryIaHsfm) throws Exception {
        IPage<IaHsfm> list = baseService.getIaHsfmList(queryIaHsfm.getAdcd(), queryIaHsfm.getKey(), queryIaHsfm.getPageNum(), queryIaHsfm.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询沟道横断面列表信息", notes = "查询沟道横断面列表信息")
    @PostMapping(value = "select-hsurface-list")
    public ResponseEntity<SuccessResponse<Page<IaHsurface>>> getHsurfaceList(@RequestBody QueryIaHsfm queryIaHsfm) throws Exception {
        IPage<IaHsurface> list = baseService.getHsurfaceList(queryIaHsfm.getAdcd(), queryIaHsfm.getKey(), queryIaHsfm.getPageNum(), queryIaHsfm.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询沟道纵断面列表信息", notes = "查询沟道纵断面列表信息")
    @PostMapping(value = "select-vsurface-list")
    public ResponseEntity<SuccessResponse<Page<IaVsurface>>> getVsurfaceList(@RequestBody QueryIaHsfm queryIaHsfm) throws Exception {
        IPage<IaVsurface> list = baseService.getVsurfaceList(queryIaHsfm.getAdcd(), queryIaHsfm.getKey(), queryIaHsfm.getPageNum(), queryIaHsfm.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询防治区列表信息", notes = "查询防治区列表信息")
    @PostMapping(value = "select-prevad-list")
    public ResponseEntity<SuccessResponse<Page<IaAdinfo>>> getPrevadList(@RequestBody QueryPrevad queryPrevad) throws Exception {
        IPage<IaAdinfo> list = baseService.getPrevadList(queryPrevad.getAdcd(), queryPrevad.getKey(), queryPrevad.getAdnm(), queryPrevad.getPrevtp(), queryPrevad.getPageNum(), queryPrevad.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询政区概况统计信息", notes = "查询政区概况统计信息")
    @GetMapping(value = "select-dt-ad-info-list")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<DtAdInfo>>> getDtAdInfoList(@RequestParam String adcd) throws Exception {
        List<DtAdInfo> list = baseService.getDtAdInfoList(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询调查成果统计信息", notes = "查询调查成果统计信息")
    @GetMapping(value = "select-dt-dccg-info-list")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<DtSurvey>>> getDtDccgInfoList(@RequestParam String adcd) throws Exception {
        List<DtSurvey> list = baseService.getDtDccgInfoList(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询涉水工程统计信息", notes = "查询涉水工程统计信息")
    @GetMapping(value = "select-dt-wading-info-list")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<DtWading>>> getDtWadingInfoList(@RequestParam String adcd) throws Exception {
        List<DtWading> list = baseService.getDtWadingInfoList(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询分析评价对象统计信息", notes = "查询分析评价对象统计信息")
    @GetMapping(value = "select-dt-fxpj-info-list")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<DtAnalysis>>> getDtFxpjInfoList(@RequestParam String adcd) throws Exception {
        List<DtAnalysis> list = baseService.getDtFxpjInfoList(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "调查评价汇总统计", notes = "调查评价汇总统计")
    @GetMapping(value = "select-dt-evaluation-info")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<DtAllEvaluationInfo>> getDtEvaluationInfo(@RequestParam String adcd) throws Exception {
        DtAllEvaluationInfo dtEvaluationInfo = baseService.getDtEvaluationInfo(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", dtEvaluationInfo));
    }

    @ApiOperation(value = "危险区统计本级信息查询", notes = "危险区统计本级信息查询")
    @GetMapping(value = "select-own-dan-info")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<DtIaDanad>> getOwnDtDanInfo(@RequestParam String adcd) throws Exception {
        DtIaDanad dtIaDanad = baseService.getOwnDtDanInfo(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", dtIaDanad));
    }

    @ApiOperation(value = "危险区统计下级信息查询", notes = "危险区统计下级信息查询")
    @GetMapping(value = "select-lower-dan-info")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<DtIaDanad>>> getLowerDtDanInfo(@RequestParam String adcd) throws Exception {
        List<DtIaDanad> list = baseService.getLowerDtDanInfo(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "危险区统计政区树信息查询", notes = "危险区统计政区树息查询")
    @GetMapping(value = "select-dan-info-tree")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<DtIaDanad>> getDtDanInfoTree(@RequestParam String adcd) throws Exception {
        DtIaDanad dtIaDanad = baseService.getDtDanInfoTree(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", dtIaDanad));
    }

    @ApiOperation(value = "历史灾害情况统计下级信息查询", notes = "历史灾害情况统计下级信息查询")
    @GetMapping(value = "select-lower-hsf-info")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<DtIaHsfwater>>> getLowerDtHsfwaterInfo(@RequestParam String adcd) throws Exception {
        List<DtIaHsfwater> list = baseService.getLowerDtHsfwaterInfo(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "历史灾害情况统计政区树信息查询", notes = "历史灾害情况统计政区树信息查询")
    @GetMapping(value = "select-hsf-info-tree")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<DtIaHsfwater>> getDtHsfwaterInfoTree(@RequestParam String adcd) throws Exception {
        DtIaHsfwater dtIaHsfwater = baseService.getDtHsfwaterInfoTree(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", dtIaHsfwater));
    }

    @ApiOperation(value = "需防洪治理山洪沟统计下级信息查询", notes = "需防洪治理山洪沟统计下级信息查询")
    @GetMapping(value = "select-lower-gully-info")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<DtIaGully>>> getLowerDtIaGullyInfo(@RequestParam String adcd) throws Exception {
        List<DtIaGully> list = baseService.getLowerDtIaGullyInfo(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "需防洪治理山洪沟政区树信息查询", notes = "需防洪治理山洪沟政区树信息查询")
    @GetMapping(value = "select-gully-info-tree")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<DtIaGully>> getDtGullyInfoTree(@RequestParam String adcd) throws Exception {
        DtIaGully dtIaGully = baseService.getDtGullyInfoTree(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", dtIaGully));
    }

    @ApiOperation(value = "查询导出防洪治理山洪沟政区树信息", notes = "查询导出防洪治理山洪沟政区树信息")
    @GetMapping(value = "select-gully-info-export")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<DtIaGully>> getDtGullyInfoExport(@RequestParam String adcd) throws Exception {
        DtIaGully dtIaGully = baseService.getDtGullyInfoExport(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", dtIaGully));
    }

    @ApiOperation(value = "社会经济情况统计本级信息查询", notes = "社会经济情况统计本级信息查询")
    @GetMapping(value = "select-own-vlgestat-info")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<DtIaVlgestat>> getOwnDtVlgestatInfo(@RequestParam String adcd) throws Exception {
        DtIaVlgestat dtIaVlgestat = baseService.getOwnDtVlgestatInfo(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", dtIaVlgestat));
    }

    @ApiOperation(value = "社会经济情况统计下级信息查询", notes = "社会经济情况统计下级信息查询")
    @GetMapping(value = "select-lower-vlgestat-info")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<DtIaVlgestat>>> getLowerDtVlgestatInfo(@RequestParam String adcd) throws Exception {
        List<DtIaVlgestat> list = baseService.getLowerDtVlgestatInfo(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "社会经济情况统计政区树信息查询", notes = "社会经济情况统计政区树息查询")
    @GetMapping(value = "select-vlgestat-info-tree")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<DtIaVlgestat>> getDtDanVlgestatTree(@RequestParam String adcd) throws Exception {
        DtIaVlgestat dtIaVlgestat = baseService.getDtDanVlgestatTree(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", dtIaVlgestat));
    }

    @ApiOperation(value = "重点区统计政区树信息查询", notes = "重点区统计政区树息查询")
    @PostMapping(value = "select-keyad-info-tree")
    public ResponseEntity<SuccessResponse<DtKeyAd>> getDtKeyAdTree(@RequestBody KeyAreasQuery query) throws Exception {
        DtKeyAd dtKeyAdTree = baseService.getDtKeyAdTree(query);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", dtKeyAdTree));
    }

}
