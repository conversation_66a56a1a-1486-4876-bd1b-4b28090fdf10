package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2019-09-17
 */
@ApiModel(value="预警关联防治区统计信息", description="预警关联防治区统计信息")
public class WarnPrevcntStatistics implements Serializable {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "总人口")
    private String ptcount;
    @ApiModelProperty(value = "总户数")
    private String etcount;
    @ApiModelProperty(value = "总房屋数")
    private String htcount;
    @ApiModelProperty(value = "总土地面积")
    private String ldarea;
    @ApiModelProperty(value = "总耕地面积")
    private String plarea;
    @ApiModelProperty(value = "防治区总数")
    private String prevcnt;
    @ApiModelProperty(value = "重点防治区总数")
    private String imppevcnt;
    @ApiModelProperty(value = "一般防治区总数")
    private String compevcnt;

    public String getCompevcnt() {
        return compevcnt;
    }

    public void setCompevcnt(String compevcnt) {
        this.compevcnt = compevcnt;
    }

    public String getHtcount() {
        return htcount;
    }

    public void setHtcount(String htcount) {
        this.htcount = htcount;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPtcount() {
        return ptcount;
    }

    public void setPtcount(String ptcount) {
        this.ptcount = ptcount;
    }

    public String getEtcount() {
        return etcount;
    }

    public void setEtcount(String etcount) {
        this.etcount = etcount;
    }

    public String getLdarea() {
        return ldarea;
    }

    public void setLdarea(String ldarea) {
        this.ldarea = ldarea;
    }

    public String getPlarea() {
        return plarea;
    }

    public void setPlarea(String plarea) {
        this.plarea = plarea;
    }

    public String getPrevcnt() {
        return prevcnt;
    }

    public void setPrevcnt(String prevcnt) {
        this.prevcnt = prevcnt;
    }

    public String getImppevcnt() {
        return imppevcnt;
    }

    public void setImppevcnt(String imppevcnt) {
        this.imppevcnt = imppevcnt;
    }
}
