package com.huitu.cloud.api.common.msg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.common.msg.entity.BnsMessageR;
import com.huitu.cloud.api.common.msg.entity.FailMessage;
import com.huitu.cloud.api.common.msg.entity.Message;
import com.huitu.cloud.api.common.msg.entity.MessageList;
import com.huitu.cloud.api.common.msg.entity.MessageSend;
import com.huitu.cloud.api.common.msg.entity.MessageStatistics;
import com.huitu.cloud.api.common.msg.entity.MessageVo;
import com.huitu.cloud.api.common.msg.entity.MsgBean;
import com.huitu.cloud.api.common.msg.entity.MsgSucess;
import com.huitu.cloud.api.common.msg.entity.QueryWarnMessage;
import com.huitu.cloud.api.common.msg.entity.WarnMessage;
import com.huitu.cloud.api.common.msg.mapper.MessageInfoDao;
import com.huitu.cloud.api.common.msg.service.MessageInfoService;
import com.huitu.cloud.util.AdcdUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警消息记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@Service
public class MessageInfoServiceImpl implements MessageInfoService {
    @Autowired
    private MessageInfoDao messageInfoDao;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MsgSucess sendMsg(MsgBean msgBean) {
        int maxMsgId = messageInfoDao.selectMaxMsgId(msgBean.getAdcd());
        int maxSmsId = messageInfoDao.selectMaxSmsId(msgBean.getAdcd());
        //手机号存储集合
        Map<String, String> mobileMap = new HashMap<>();
        List<MessageSend> list = new ArrayList<>();
        msgBean.setMsgId(maxMsgId);
        for (Message item : msgBean.getMsg()) {
            //一个手机号  只能发送一次
            if (StringUtils.isBlank(item.getSid()) || mobileMap.containsKey(item.getSid())) {
                continue;
            }
            MessageSend messageSend = new MessageSend();
            messageSend.setMsgId(maxMsgId);
            messageSend.setSmsId(maxSmsId);
            messageSend.setSendResult(2);
            messageSend.setRemark(msgBean.getRemark());
            messageSend.setAdcd(msgBean.getAdcd());
            messageSend.setIsSend(1);
            messageSend.setUname(item.getUname());
            messageSend.setSid(item.getSid());
            list.add(messageSend);
            maxSmsId++;
            mobileMap.put(item.getSid(), item.getSid());
        }
        messageInfoDao.insertMsgInfo(msgBean);
        //批量插入 分次入库，因为mybatis限制最多2100参数
        if (list.size() > 0) {
            int count = 0;
            if (list.size() % 170 == 0) {
                count = list.size() / 170;
            } else {
                count = list.size() / 170 + 1;
            }
            for (int i = 0; i < count; i++) {
                int min = i * 170;
                int max = (i + 1) * 170;
                if (max >= list.size()) {
                    max = list.size();
                }
                messageInfoDao.bathInsertMsgSend(list.subList(min, max));

            }
        }
        MsgSucess msgSucess = new MsgSucess();
        msgSucess.setRes("OK");
        return msgSucess;
    }

    @Override
    public List<MessageVo> getMsgByWarnId(String warnId, String status) {
        Map<String, Object> param = new HashMap<>();
        param.put("warnId", warnId);
        param.put("status", status);
        List<MessageVo> list = messageInfoDao.selectWarnMsg(param);
        return list;
    }

    @Override
    public List<MessageStatistics> getMsgStatistics(String adcd, String etm, String stm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("zero", adcd.substring(level + 2));
        param.put("size", level + 2);
        param.put("level", level);
        param.put("ad", adcd.substring(0, level));
        param.put("etm", etm);
        param.put("stm", stm);
        List<MessageStatistics> list = messageInfoDao.getMsgStatistics(param);
        return list;
    }

    @Override
    public IPage<MessageVo> getWarnMsgInfoByPage(String adcd, String etm, String mediaId, Integer msgTypeId, String warnId, String status, String stm, String msgid, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        if (adcd != null && adcd.length() > 0) {
            param.put("ad", adcd.substring(0, level));
        }
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("warnId", warnId);
        param.put("status", status);
        param.put("mediaId", mediaId);
        param.put("msgTypeId", msgTypeId);
        param.put("msgid", msgid);
        IPage<MessageVo> resultMap = messageInfoDao.getWarnMsgInfoByPage(page, param);
        List<MessageVo> list = messageInfoDao.getMobilePerTp();
        Map<String, List<MessageVo>> mobileMap = list.stream().collect(Collectors.groupingBy(MessageVo::getSid));
        resultMap.getRecords().stream().forEach(item -> {
            if (mobileMap.containsKey(item.getSid())) {
                List<String> rytpList = mobileMap.get(item.getSid()).stream().map(MessageVo::getRytps).collect(Collectors.toList());
                String join = StringUtils.join(rytpList, ",");
                item.setRytps(join);
            } else {
                item.setRytps("水旱灾害工作人员");
            }
        });

        return resultMap;
    }

    @Override
    public IPage<WarnMessage> getWarnMessagePage(QueryWarnMessage query) {
        Page page = new Page<>(query.getPageNum(), query.getPageSize());
        Map<String, Object> param = new HashMap<>();
        param.put("warnId", query.getWarnId());
        param.put("mediaId", query.getMediaId());
        param.put("status", query.getStatus());
        return messageInfoDao.getWarnMessagePage(page, param);
    }

    @Override
    public IPage<MessageList> getWarnMsgInfoList(String adcd, String stm, String etm, String mediaId, Integer msgTypeId, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        if (adcd != null && adcd.length() > 0) {
            param.put("ad", adcd.substring(0, level));
            param.put("level", level);
            param.put("zero", adcd.substring(6));
            param.put("size", 6);
        }
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("mediaId", mediaId);
        param.put("msgTypeId", msgTypeId);
        IPage<MessageList> resultMap = messageInfoDao.getWarnMsgInfoList(page, param);
        return resultMap;
    }

    @Override
    public List<FailMessage> getFailMessage(String adcd, String msgid) {
        List<FailMessage> failMessage = messageInfoDao.getFailMessage(adcd, msgid);
        return failMessage;
    }

    @Override
    @Transactional
    public String updateSendResult(List<FailMessage> messages) {
        //创建临时表
        String tableName = "##tmp";
        messageInfoDao.createTemoraryTable(tableName);
        //数据插入临时表
        int count = 0;
        if (messages.size() % 650 == 0) {
            count = messages.size() / 650;
        } else {
            count = messages.size() / 650 + 1;
        }
        for (int i = 0; i < count; i++) {
            int min = i * 650;
            int max = (i + 1) * 650;
            if (max >= messages.size()) {
                max = messages.size();
            }
            List<FailMessage> list3 = messages.subList(min, max);
            messageInfoDao.bathInsertFailMassage(list3, tableName);
        }
        //更新短信发送记录表
        messageInfoDao.updateSendResult(tableName);
        return "OK";
    }

    @Override
    public int sendCommonMsg(BnsMessageR entity) {
        entity.setTm(LocalDateTime.now());
        entity.setFlag("0");
        return messageInfoDao.insertCommonMsg(entity);
    }

    @Override
    public List<BnsMessageR> getCommonMsg(String id, String type, String adcd, String deptid, String stm, String etm, String flag) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("level", level);
        param.put("ad", adcd.substring(0, level));
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("type", type);
        param.put("id", id);
        param.put("deptid", deptid);
        param.put("flag", flag);
        List<BnsMessageR> list = messageInfoDao.getCommonMsg(param);
        return list;
    }

    @Override
    public boolean updateCommonMsgBatches(List<String> ids) {
        messageInfoDao.updateCommonMsgBatches(ids);
        return true;
    }
}
