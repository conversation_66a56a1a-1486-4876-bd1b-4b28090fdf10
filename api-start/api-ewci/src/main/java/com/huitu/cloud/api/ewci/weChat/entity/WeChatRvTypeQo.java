package com.huitu.cloud.api.ewci.weChat.entity;


import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

public class WeChatRvTypeQo {
    @ApiModelProperty(value = "查询条件 1 小（二）型  2 小（一）型  3 中型 4,5大型",required = true)
    @Valid
    @Size(max = 4, message = "集合不能大于4")
    @SqlInjection
    public List<String> rvType;

    public List<String> getRvType() {
        return rvType;
    }

    public void setRvType(List<String> rvType) {
        this.rvType = rvType;
    }
}
