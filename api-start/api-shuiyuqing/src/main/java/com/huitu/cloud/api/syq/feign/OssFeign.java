package com.huitu.cloud.api.syq.feign;

import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@FeignClient(name="dev-oss")
public interface OssFeign {

    @GetMapping(value = "/api/oss/minio/objects/file", consumes = MediaType.APPLICATION_PROBLEM_JSON_VALUE)
    Response getFile(@RequestParam("filepath") String filepath, @RequestParam("filename") String filename);


    @PostMapping("/api/oss/wopi/files/{id}/contents")
    void putFile(@PathVariable(name = "id") String id, @RequestBody byte[] content);
}
