************ - - [20/Jun/2025:09:45:33 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [20/Jun/2025:09:45:33 +0800] "GET /login HTTP/1.1" 302 -
************ - - [20/Jun/2025:09:45:33 +0800] "GET /login?code=0NqoOB&state=B8DZaA HTTP/1.1" 302 -
************ - - [20/Jun/2025:09:45:33 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [20/Jun/2025:09:45:34 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750383934614 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:35 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750383934614 HTTP/1.1" 200 552
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750383936039 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750383936039 HTTP/1.1" 200 61649
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750383936293 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750383936293 HTTP/1.1" 200 10388
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750383936393 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750383936393 HTTP/1.1" 200 2009
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750383936598 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-17+09:45:36&etm=&_timer304=1750383936599 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750383936599 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-20+08:00&etm=2025-06-20+10:00&filterCnt=6&_timer304=1750383936599 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750383936599 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750383936599 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:36 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750383936598 HTTP/1.1" 200 1482
************ - - [20/Jun/2025:09:45:36 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Jun/2025:09:45:36 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Jun/2025:09:45:36 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Jun/2025:09:45:36 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-17+09:45:36&etm=&_timer304=1750383936599 HTTP/1.1" 200 156
************ - - [20/Jun/2025:09:45:36 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750383936599 HTTP/1.1" 200 166
************ - - [20/Jun/2025:09:45:36 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Jun/2025:09:45:36 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-20+08:00&etm=2025-06-20+10:00&filterCnt=6&_timer304=1750383936599 HTTP/1.1" 200 164
************ - - [20/Jun/2025:09:45:36 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750383936599 HTTP/1.1" 200 169
************ - - [20/Jun/2025:09:45:36 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750383936599 HTTP/1.1" 200 13016
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-20&_timer304=1750383937241 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/base/saas/token?_timer304=1750383937242 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750383937259 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:37 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [20/Jun/2025:09:45:37 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [20/Jun/2025:09:45:37 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [20/Jun/2025:09:45:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:09:45:37 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:09:45:38 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [20/Jun/2025:09:45:38 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [20/Jun/2025:09:45:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:09:45:38 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [20/Jun/2025:09:45:38 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [20/Jun/2025:09:45:38 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750383937259 HTTP/1.1" 200 2009
************ - - [20/Jun/2025:09:45:38 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [20/Jun/2025:09:45:38 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [20/Jun/2025:09:45:38 +0800] "GET /api/base/saas/token?_timer304=1750383937242 HTTP/1.1" 200 411
************ - - [20/Jun/2025:09:45:38 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [20/Jun/2025:09:45:38 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 302
************ - - [20/Jun/2025:09:45:39 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-20&_timer304=1750383937241 HTTP/1.1" 200 450
************ - - [20/Jun/2025:09:45:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750383944362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750383944362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:45:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750383944362 HTTP/1.1" 200 160
************ - - [20/Jun/2025:09:45:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750383944362 HTTP/1.1" 200 159
************ - - [20/Jun/2025:09:50:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750384234461 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:50:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750384234461 HTTP/1.1" 200 160
************ - - [20/Jun/2025:09:50:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750384245358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:50:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750384245358 HTTP/1.1" 200 159
************ - - [20/Jun/2025:09:55:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750384534363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:55:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750384534363 HTTP/1.1" 200 160
************ - - [20/Jun/2025:09:55:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750384546358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:09:55:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750384546358 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:00:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750384834361 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:00:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750384834361 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:00:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750384847355 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:00:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750384847355 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:05:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750385134364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:05:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750385134364 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:05:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750385148356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:05:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750385148356 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:10:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750385434360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:10:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750385434360 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:10:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750385449363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:10:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750385449363 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:15:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750385741354 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:15:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750385741354 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:15:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750385750365 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:15:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750385750365 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:20:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750386051367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:20:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750386051367 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:21:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750386101367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:21:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750386101367 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:25:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750386352364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:25:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750386352364 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:26:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750386401361 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:26:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750386401361 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:30:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750386635368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:30:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750386635368 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:30:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750386653367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:30:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750386653367 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:35:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750386954369 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:35:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750386954369 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:36:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750387001358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:36:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750387001358 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:40:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750387255363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:40:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750387255363 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:41:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750387301362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:41:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750387301362 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:45:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750387556366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:45:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750387556366 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:46:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750387601367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:46:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750387601367 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:50:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750387857363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:50:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750387857363 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:51:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750387901366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:51:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750387901366 HTTP/1.1" 200 160
************ - - [20/Jun/2025:10:55:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750388158364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:55:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750388158364 HTTP/1.1" 200 159
************ - - [20/Jun/2025:10:56:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750388201367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:10:56:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750388201367 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:00:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750388459353 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:00:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750388459353 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:01:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750388501358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:01:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750388501358 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:06:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750388760358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:06:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750388760358 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:06:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750388801370 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:06:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750388801370 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:11:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750389061355 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:11:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750389061355 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:11:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750389101364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:11:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750389101364 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:16:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750389362368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:16:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750389362368 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:16:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750389401367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:16:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750389401367 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:21:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750389663358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:21:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750389663358 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:21:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750389701360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:21:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750389701360 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:26:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750389964354 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:26:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750389964354 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:26:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750390001369 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:26:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750390001369 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:31:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750390265368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:31:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750390265368 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:31:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750390301367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:31:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750390301367 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:36:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750390566362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:36:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750390566362 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:36:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750390601357 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:36:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750390601357 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:41:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750390867363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:41:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750390867363 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:41:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750390901361 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:41:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750390901361 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:46:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750391168357 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:46:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750391168357 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:46:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750391201366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:46:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750391201366 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:51:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750391469366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:51:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750391469366 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:51:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750391501365 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:51:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750391501365 HTTP/1.1" 200 160
************ - - [20/Jun/2025:11:56:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750391770366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:56:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750391770366 HTTP/1.1" 200 159
************ - - [20/Jun/2025:11:56:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750391801360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:11:56:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750391801360 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:01:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750392071368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:01:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750392071368 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:01:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750392101367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:01:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750392101367 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:06:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750392372362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:06:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750392372362 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:06:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750392401361 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:06:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750392401361 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:11:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750392673363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:11:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750392673363 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:11:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750392701359 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:11:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750392701359 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:16:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750392974356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:16:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750392974356 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:16:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750393001369 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:16:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750393001369 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:21:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750393275360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:21:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750393275360 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:21:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750393301358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:21:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750393301358 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:26:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750393576356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:26:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750393576356 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:26:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750393601356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:26:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750393601356 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:31:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750393877367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:31:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750393877367 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:31:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750393901360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:31:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750393901360 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:36:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750394178368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:36:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750394178368 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:36:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750394201364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:36:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750394201364 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:41:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750394479362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:41:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750394479362 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:41:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750394501354 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:41:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750394501354 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:46:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750394780368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:46:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750394780368 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:46:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750394801361 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:46:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750394801361 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:51:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750395081356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:51:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750395081356 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:51:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750395101366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:51:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750395101366 HTTP/1.1" 200 160
************ - - [20/Jun/2025:12:56:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750395382366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:56:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750395382366 HTTP/1.1" 200 159
************ - - [20/Jun/2025:12:56:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750395401367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:12:56:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750395401367 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:01:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750395683364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:01:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750395683364 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:01:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750395701367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:01:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750395701367 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:06:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750395984367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:06:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750395984367 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:06:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750396001357 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:06:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750396001357 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:11:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750396285357 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:11:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750396285357 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:11:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750396301357 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:11:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750396301357 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:16:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750396586356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:16:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750396586356 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:16:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750396601359 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:16:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750396601359 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:21:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750396887360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:21:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750396887360 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:21:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750396901361 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:21:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750396901361 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:26:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750397188359 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:26:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750397188359 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:26:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750397201364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:26:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750397201364 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:31:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750397489360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:31:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750397489360 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:31:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750397501363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:31:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750397501363 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:36:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750397790358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:36:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750397790358 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:36:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750397801366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:36:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750397801366 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:41:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750398091367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:41:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750398091367 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:41:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750398101365 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:41:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750398101365 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:46:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750398392365 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:46:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750398392365 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:46:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750398401364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:46:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750398401364 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:48:25 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750398505842 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750398505842 HTTP/1.1" 200 144
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750398506100 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-17+13:48:25&etm=&_timer304=1750398506100 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-20+08:00&etm=2025-06-20+14:00&filterCnt=6&_timer304=1750398506100 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750398506117 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750398506100 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750398506100 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750398506102 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750398506100 HTTP/1.1" 200 166
************ - - [20/Jun/2025:13:48:26 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Jun/2025:13:48:26 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-17+13:48:25&etm=&_timer304=1750398506100 HTTP/1.1" 200 156
************ - - [20/Jun/2025:13:48:26 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Jun/2025:13:48:26 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Jun/2025:13:48:26 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-20+08:00&etm=2025-06-20+14:00&filterCnt=6&_timer304=1750398506100 HTTP/1.1" 200 164
************ - - [20/Jun/2025:13:48:26 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Jun/2025:13:48:26 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750398506100 HTTP/1.1" 200 169
************ - - [20/Jun/2025:13:48:26 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750398506102 HTTP/1.1" 200 161
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-20+08:00&etm=2025-06-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750398506341 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750398506117 HTTP/1.1" 200 159491
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398506364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [20/Jun/2025:13:48:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [20/Jun/2025:13:48:26 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750398506100 HTTP/1.1" 200 13016
************ - - [20/Jun/2025:13:48:26 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398506364 HTTP/1.1" 200 155
************ - - [20/Jun/2025:13:48:26 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [20/Jun/2025:13:48:26 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:26 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [20/Jun/2025:13:48:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [20/Jun/2025:13:48:27 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:13:48:27 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398507361 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:27 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398507361 HTTP/1.1" 200 155
************ - - [20/Jun/2025:13:48:27 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-20+08:00&etm=2025-06-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750398506341 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:13:48:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:48:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:48:29 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398509142 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:29 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398509142 HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:48:50 +0800] "OPTIONS /api/syq/rain/listAdRainDiff HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:50 +0800] "OPTIONS /api/syq/rain/listRainDiff HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:50 +0800] "POST /api/syq/rain/listAdRainDiff HTTP/1.1" 200 147
************ - - [20/Jun/2025:13:48:51 +0800] "POST /api/syq/rain/listRainDiff HTTP/1.1" 200 145
************ - - [20/Jun/2025:13:48:56 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750398536049 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:56 +0800] "OPTIONS /api/shyj/warn/select-warn-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:56 +0800] "OPTIONS /api/syq/rain/get-next-avg-rain-forecast-list-by-tm?adcd=220000000000000&stm=2025-06-20+08:00:00&etm=2025-06-20+09:00:00&_timer304=1750398536049 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:56 +0800] "OPTIONS /api/syq/rain/select-forecast-by-stcd HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:48:56 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750398536049 HTTP/1.1" 200 159491
************ - - [20/Jun/2025:13:48:56 +0800] "POST /api/shyj/warn/select-warn-by-page HTTP/1.1" 200 238
************ - - [20/Jun/2025:13:49:06 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:49:06 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:49:06 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:13:49:06 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398546868 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:49:06 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398546868 HTTP/1.1" 200 155
************ - - [20/Jun/2025:13:49:07 +0800] "POST /api/syq/rain/select-forecast-by-stcd HTTP/1.1" 200 156
************ - - [20/Jun/2025:13:49:08 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:49:08 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:13:49:08 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398548372 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:49:08 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398548372 HTTP/1.1" 200 155
************ - - [20/Jun/2025:13:49:09 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:49:09 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398549139 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:49:09 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:49:09 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398549139 HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:49:09 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:49:09 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398549967 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:49:10 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398549967 HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:49:16 +0800] "GET /api/syq/rain/get-next-avg-rain-forecast-list-by-tm?adcd=220000000000000&stm=2025-06-20+08:00:00&etm=2025-06-20+09:00:00&_timer304=1750398536049 HTTP/1.1" 200 1713
************ - - [20/Jun/2025:13:50:27 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:13:50:27 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398627228 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:50:27 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398627228 HTTP/1.1" 200 155
************ - - [20/Jun/2025:13:50:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:50:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:50:28 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398628776 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:50:28 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398628776 HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:50:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750398635353 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:50:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750398635353 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:51:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750398693363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:51:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750398693363 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:52:27 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:13:52:27 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398747256 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:52:27 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398747256 HTTP/1.1" 200 155
************ - - [20/Jun/2025:13:52:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:52:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:52:28 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398748800 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:52:28 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398748800 HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:53:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750398806467 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:53:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750398806468 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:53:26 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750398806467 HTTP/1.1" 200 161
************ - - [20/Jun/2025:13:53:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:13:53:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:13:53:27 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750398806468 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:13:53:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:13:54:27 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:13:54:27 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398867233 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:54:27 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398867233 HTTP/1.1" 200 155
************ - - [20/Jun/2025:13:54:27 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:54:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:54:28 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398868750 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:54:28 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398868750 HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:56:27 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:13:56:27 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398987296 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:56:27 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750398987296 HTTP/1.1" 200 155
************ - - [20/Jun/2025:13:56:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:56:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:56:28 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398988822 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:56:28 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750398988822 HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:56:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750398994364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:56:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750398994364 HTTP/1.1" 200 159
************ - - [20/Jun/2025:13:56:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750399001363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:56:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750399001363 HTTP/1.1" 200 160
************ - - [20/Jun/2025:13:58:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750399106362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:58:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750399106363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:58:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:13:58:26 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750399106362 HTTP/1.1" 200 161
************ - - [20/Jun/2025:13:58:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:13:58:27 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:13:58:27 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399107339 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:58:27 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399107339 HTTP/1.1" 200 155
************ - - [20/Jun/2025:13:58:27 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750399106363 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:13:58:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:13:58:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:58:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:13:58:28 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399108806 HTTP/1.1" 200 -
************ - - [20/Jun/2025:13:58:28 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399108806 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:00:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:00:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399242291 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:00:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399242291 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:00:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:00:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:00:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399243795 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:00:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399243795 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:01:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750399295356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:01:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750399295356 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:01:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750399301359 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:01:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750399301359 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:03:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750399406360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:03:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750399406360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:03:26 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750399406360 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:03:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:03:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:03:27 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750399406360 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:03:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:03:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:03:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399422355 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:03:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399422355 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:03:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:03:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:03:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399423892 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:03:44 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399423892 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:05:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:05:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399542250 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:05:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399542250 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:05:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:05:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:05:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399543797 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:05:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399543797 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:06:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750399596363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:06:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750399596363 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:06:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750399601358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:06:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750399601358 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:07:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:07:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399662264 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:07:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399662264 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:07:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:07:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:07:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399663756 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:07:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399663756 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:08:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750399706355 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:08:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750399706356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:08:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:08:26 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750399706355 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:08:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:08:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:08:27 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750399706356 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:09:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:09:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399782279 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:09:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399782279 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:09:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:09:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:09:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399783826 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:09:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399783826 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:11:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750399897356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:11:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750399897356 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:11:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750399901515 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:11:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750399901515 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:11:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:11:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399902280 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:11:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750399902280 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:11:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:11:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:11:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399903785 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:11:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750399903785 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:13:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750400006358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:13:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750400006357 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:13:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:13:26 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750400006357 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:13:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:13:27 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750400006358 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:13:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:13:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:13:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400022245 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:13:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400022245 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:13:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:13:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:13:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400023788 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:13:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400023788 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:15:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:15:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400142267 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:15:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400142267 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:15:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:15:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:15:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400143805 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:15:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400143805 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:16:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750400198354 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:16:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750400198354 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:16:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750400201360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:16:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750400201360 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:17:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:17:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400262254 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:17:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400262254 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:17:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:17:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:17:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400263790 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:17:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400263790 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:18:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750400306363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:18:26 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750400306363 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:18:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750400321366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:18:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:18:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:18:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750400321366 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:18:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:19:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:19:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400382261 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:19:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400382261 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:19:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:19:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:19:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400383755 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:19:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400383755 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:21:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750400499354 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:21:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750400499354 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:21:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750400501498 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:21:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750400501498 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:21:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:21:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400502255 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:21:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400502255 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:21:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:21:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:21:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400503774 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:21:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400503774 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:23:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750400621479 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:23:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750400621479 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:23:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:23:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400622241 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:23:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400622241 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:23:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:23:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:23:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400623826 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:23:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400623826 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:24:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750400681367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:24:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:24:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:24:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750400681367 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:24:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:25:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:25:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400742246 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:25:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400742246 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:25:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:25:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:25:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400743764 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:25:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400743764 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:26:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750400800356 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:26:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750400800356 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:26:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750400801359 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:26:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750400801359 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:27:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:27:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400862296 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:27:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400862296 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:27:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:27:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:27:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400863825 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:27:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400863825 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:28:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750400921364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:28:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750400921364 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:29:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750400981371 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:29:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:29:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:29:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:29:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400982317 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:29:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750400982317 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:29:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750400981371 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:29:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:29:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:29:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:29:44 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400984181 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:29:44 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750400984181 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:31:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750401101507 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:31:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750401101508 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:31:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750401101507 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:31:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750401101508 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:31:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:31:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401102267 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:31:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401102267 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:31:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:31:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:31:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401103802 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:31:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401103802 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:33:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750401221494 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:33:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750401221494 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:33:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:33:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401222257 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:33:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401222257 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:33:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:33:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:33:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401223769 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:33:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401223769 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:34:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750401281366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:34:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:34:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:34:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750401281366 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:34:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:35:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:35:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401342260 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:35:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401342260 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:35:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:35:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:35:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401343743 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:35:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401343743 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:36:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750401401366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:36:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750401401366 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:36:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750401402366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:36:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750401402366 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:37:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:37:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401462324 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:37:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401462324 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:37:42 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:37:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:37:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:37:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401463809 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:37:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401463809 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:38:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750401521365 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:38:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750401521365 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:39:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750401581371 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:39:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:39:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:39:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:39:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:39:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750401581371 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:39:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:39:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401582839 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:39:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401582839 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:39:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:39:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:39:44 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401584381 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:39:44 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401584381 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:41:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750401701514 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:41:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750401701514 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:41:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:41:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401702287 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:41:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401702287 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:41:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:41:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750401703360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:41:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750401703360 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:41:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:41:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401703830 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:41:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401703830 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:43:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750401821496 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:43:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750401821496 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:43:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:43:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401822262 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:43:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401822262 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:43:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:43:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:43:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401823782 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:43:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401823782 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:44:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750401881373 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:44:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:44:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:44:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750401881373 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:44:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:45:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:45:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401942242 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:45:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750401942242 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:45:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:45:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:45:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401943791 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:45:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750401943791 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:46:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750402001354 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:46:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750402001354 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:46:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750402004360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:46:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750402004360 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:47:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:47:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402062258 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:47:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402062258 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:47:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:47:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:47:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402063791 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:47:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402063791 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:48:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750402121369 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:48:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750402121369 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:49:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750402181360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:49:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:49:41 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:49:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:49:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750402181360 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:49:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:49:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:49:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402182487 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:49:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402182487 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:49:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:49:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:49:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402183989 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:49:44 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402183989 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:51:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750402301502 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:51:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750402301502 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:51:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:51:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402302278 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:51:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402302278 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:51:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:51:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:51:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402303797 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:51:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402303797 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:51:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750402305365 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:51:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750402305365 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:53:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750402421497 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:53:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750402421497 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:53:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:53:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402422270 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:53:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402422270 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:53:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:53:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:53:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402423760 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:53:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402423760 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:54:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750402481360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:54:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:54:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:54:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750402481360 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:54:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:55:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:55:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402542252 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:55:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402542252 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:55:42 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:55:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:55:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402543748 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:55:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402543748 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:56:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750402601372 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:56:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750402601372 HTTP/1.1" 200 160
************ - - [20/Jun/2025:14:56:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750402606366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:56:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750402606366 HTTP/1.1" 200 159
************ - - [20/Jun/2025:14:57:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:57:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402662275 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:57:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402662275 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:57:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:57:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:57:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402663769 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:57:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402663769 HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:58:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750402721364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:58:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750402721364 HTTP/1.1" 200 161
************ - - [20/Jun/2025:14:59:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750402781373 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:59:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:14:59:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:14:59:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:14:59:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402782862 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:59:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402782862 HTTP/1.1" 200 155
************ - - [20/Jun/2025:14:59:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:14:59:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750402781373 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:14:59:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:59:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:14:59:44 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402784382 HTTP/1.1" 200 -
************ - - [20/Jun/2025:14:59:44 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402784382 HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:01:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750402901539 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:01:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750402901539 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:01:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:15:01:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402902308 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:01:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750402902308 HTTP/1.1" 200 155
************ - - [20/Jun/2025:15:01:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:01:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:01:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402903844 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:01:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750402903844 HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:01:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750402907354 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:01:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750402907354 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:03:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750403021514 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:03:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750403021514 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:03:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:15:03:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403022281 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:03:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403022281 HTTP/1.1" 200 155
************ - - [20/Jun/2025:15:03:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:03:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:03:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403023812 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:03:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403023812 HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:04:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750403081359 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:04:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:04:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:04:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750403081359 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:04:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:05:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:15:05:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403142255 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:05:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403142255 HTTP/1.1" 200 155
************ - - [20/Jun/2025:15:05:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:05:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:05:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403143765 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:05:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403143765 HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:06:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750403201364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:06:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750403201364 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:06:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750403208355 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:06:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750403208355 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:07:42 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:15:07:42 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403262301 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:07:42 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403262301 HTTP/1.1" 200 155
************ - - [20/Jun/2025:15:07:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:07:43 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:07:43 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403263789 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:07:43 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403263789 HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:08:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750403321366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:08:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750403321366 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:08:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750403338502 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:08:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:08:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:08:59 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:15:08:59 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403339292 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:08:59 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403339292 HTTP/1.1" 200 155
************ - - [20/Jun/2025:15:08:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750403338502 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:08:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:09:00 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:09:00 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:09:00 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403340776 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:09:00 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403340776 HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:09:30 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:09:30 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [20/Jun/2025:15:09:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:10:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:10:06 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750403406293 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750403406290 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750403406290 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:10:06 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750403406293 HTTP/1.1" 200 159491
************ - - [20/Jun/2025:15:10:06 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-17+15:10:06&etm=&_timer304=1750403406360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750403406360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-20+08:00&etm=2025-06-20+16:00&filterCnt=6&_timer304=1750403406360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:06 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750403406360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750403406360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750403406424 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:06 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Jun/2025:15:10:06 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Jun/2025:15:10:06 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [20/Jun/2025:15:10:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [20/Jun/2025:15:10:06 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Jun/2025:15:10:06 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Jun/2025:15:10:06 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-17+15:10:06&etm=&_timer304=1750403406360 HTTP/1.1" 200 156
************ - - [20/Jun/2025:15:10:06 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750403406360 HTTP/1.1" 200 166
************ - - [20/Jun/2025:15:10:06 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-20+08:00&etm=2025-06-20+16:00&filterCnt=6&_timer304=1750403406360 HTTP/1.1" 200 164
************ - - [20/Jun/2025:15:10:06 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403406447 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:06 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750403406360 HTTP/1.1" 200 169
************ - - [20/Jun/2025:15:10:06 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403406447 HTTP/1.1" 200 155
************ - - [20/Jun/2025:15:10:06 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [20/Jun/2025:15:10:06 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750403406360 HTTP/1.1" 200 13016
************ - - [20/Jun/2025:15:10:06 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [20/Jun/2025:15:10:06 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [20/Jun/2025:15:10:07 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:15:10:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750403406424 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:10:07 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403407566 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:07 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750403407566 HTTP/1.1" 200 155
************ - - [20/Jun/2025:15:10:08 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:10:09 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:10:09 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403409831 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:09 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750403409831 HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:10:13 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [20/Jun/2025:15:10:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:10:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750403416124 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750403416143 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750403416124 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:10:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750403416143 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:10:38 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:10:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:15:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:15:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:15:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750403706114 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:15:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750403706114 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:15:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750403706180 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:15:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750403706180 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:15:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:15:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:15:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750403706370 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:15:06 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:15:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:15:06 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:15:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750403706370 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:15:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:15:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750403716267 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:15:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750403716267 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:19:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:19:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 8206
************ - - [20/Jun/2025:15:19:44 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:19:47 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:19:52 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:19:54 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:19:56 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:19:57 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:19:57 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:19:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:19:59 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:00 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:20:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:20:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750404006702 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750404006696 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-17+15:20:06&etm=&_timer304=1750404006768 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750404006768 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-20+08:00&etm=2025-06-20+16:00&filterCnt=6&_timer304=1750404006768 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750404006858 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750404006768 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750404006768 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:06 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750404006893 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:07 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Jun/2025:15:20:07 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-17+15:20:06&etm=&_timer304=1750404006768 HTTP/1.1" 200 156
************ - - [20/Jun/2025:15:20:07 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [20/Jun/2025:15:20:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750404006696 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:20:07 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [20/Jun/2025:15:20:07 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [20/Jun/2025:15:20:07 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750404006768 HTTP/1.1" 200 166
************ - - [20/Jun/2025:15:20:07 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-20+08:00&etm=2025-06-20+16:00&filterCnt=6&_timer304=1750404006768 HTTP/1.1" 200 164
************ - - [20/Jun/2025:15:20:07 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750404006768 HTTP/1.1" 200 169
************ - - [20/Jun/2025:15:20:07 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750404006702 HTTP/1.1" 200 159491
************ - - [20/Jun/2025:15:20:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [20/Jun/2025:15:20:07 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [20/Jun/2025:15:20:07 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750404006893 HTTP/1.1" 200 155
************ - - [20/Jun/2025:15:20:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [20/Jun/2025:15:20:07 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750404006768 HTTP/1.1" 200 13016
************ - - [20/Jun/2025:15:20:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [20/Jun/2025:15:20:08 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:08 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:08 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [20/Jun/2025:15:20:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750404006858 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:20:09 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [20/Jun/2025:15:20:09 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750404009903 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:09 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750404009903 HTTP/1.1" 200 155
************ - - [20/Jun/2025:15:20:10 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:10 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:20:11 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:20:12 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750404012212 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:12 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750404012212 HTTP/1.1" 200 232
************ - - [20/Jun/2025:15:20:13 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:13 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [20/Jun/2025:15:20:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:20:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750404016376 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750404016415 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750404016376 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:20:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750404016415 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:20:37 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:20:46 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:25:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:25:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750404306387 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750404306387 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:25:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750404306465 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750404306465 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:25:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750404306790 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:06 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:25:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:25:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750404306790 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:25:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:25:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750404317849 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750404317849 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:25:54 +0800] "OPTIONS /api/dzsj/rvrsvr/get-info-by-cd-tp?objtp=6&_timer304=1750404354498 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:54 +0800] "GET /api/dzsj/rvrsvr/get-info-by-cd-tp?objtp=6&_timer304=1750404354498 HTTP/1.1" 200 153
************ - - [20/Jun/2025:15:25:54 +0800] "OPTIONS /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:54 +0800] "POST /api/syq/ststbprpb/select-by-info HTTP/1.1" 200 626476
************ - - [20/Jun/2025:15:25:55 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10800112&stm=2025-06-17+15:30&etm=2025-06-20+15:30&_timer304=1750404355132 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:55 +0800] "OPTIONS /api/syq/rsvr/select-streglat-stcd?stcd=10800112&ymdh=2025-06-20+15:30&_timer304=1750404355132 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:55 +0800] "OPTIONS /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:55 +0800] "OPTIONS /api/syq/rsvr/select-tm-list?stcd=10800112&stm=2025-06-17+15:30&etm=2025-06-20+15:30&_timer304=1750404355132 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:55 +0800] "GET /api/syq/rsvr/select-streglat-stcd?stcd=10800112&ymdh=2025-06-20+15:30&_timer304=1750404355132 HTTP/1.1" 200 153
************ - - [20/Jun/2025:15:25:55 +0800] "GET /api/syq/rsvr/select-tm-list?stcd=10800112&stm=2025-06-17+15:30&etm=2025-06-20+15:30&_timer304=1750404355132 HTTP/1.1" 200 147
************ - - [20/Jun/2025:15:25:55 +0800] "POST /api/syq/rsvr/select-day-avg-rsvr-by-page HTTP/1.1" 200 245
************ - - [20/Jun/2025:15:25:55 +0800] "GET /api/syq/rsvr/select-rsvr-avg-drna-list?stcd=10800112&stm=2025-06-17+15:30&etm=2025-06-20+15:30&_timer304=1750404355132 HTTP/1.1" 200 158
************ - - [20/Jun/2025:15:25:55 +0800] "OPTIONS /api/syq/rsvr/select-fhzb-by-stcd?stcd=10800112&_timer304=1750404355251 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:25:55 +0800] "GET /api/syq/rsvr/select-fhzb-by-stcd?stcd=10800112&_timer304=1750404355251 HTTP/1.1" 200 553
************ - - [20/Jun/2025:15:30:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:30:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750404606385 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750404606385 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:30:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750404606462 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750404606462 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:30:06 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:06 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750404606789 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:06 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:30:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:30:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750404606789 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:30:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:30:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750404617898 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750404617898 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:30:38 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:39 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1334200
************ - - [20/Jun/2025:15:30:40 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:30:48 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:30:49 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:32:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:32:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:35:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:35:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:35:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750404907362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:35:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750404907363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:35:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:35:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:35:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:35:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750404907366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:35:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750404907362 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:35:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:35:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:35:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750404907363 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:35:09 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:35:09 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750404907366 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:35:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750404918367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:35:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750404918367 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:40:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:40:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:40:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750405207368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:40:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750405207368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:40:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:40:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:40:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750405207370 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:40:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:40:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750405207368 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:40:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:40:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750405207368 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:40:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:40:08 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:40:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750405207370 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:40:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750405219362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:40:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750405219362 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:45:01 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:45:02 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:45:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750405507367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:45:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750405507369 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:45:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:45:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:45:07 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750405507370 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:45:07 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:45:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750405507367 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:45:07 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:45:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750405507369 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:45:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:45:08 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:45:08 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750405507370 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:45:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750405520368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:45:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750405520368 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:50:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750405807360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:50:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750405807360 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:50:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750405821364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:50:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750405821364 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:50:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:50:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750405841397 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:50:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:50:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:50:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:50:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750405841399 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:50:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:50:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750405841397 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:50:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:50:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750405841399 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:50:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:50:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:15:55:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750406122358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:55:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750406122358 HTTP/1.1" 200 159
************ - - [20/Jun/2025:15:55:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750406141361 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:55:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750406141361 HTTP/1.1" 200 161
************ - - [20/Jun/2025:15:56:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:56:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750406201393 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:56:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:56:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:56:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750406201394 HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:56:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:15:56:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:15:56:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750406201393 HTTP/1.1" 200 160
************ - - [20/Jun/2025:15:56:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:15:56:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:15:56:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750406201394 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:15:56:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:00:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750406408821 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:00:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:00:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:00:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750406408845 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:00:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750406408856 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:00:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:00:08 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:00:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750406408821 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:00:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:00:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750406408845 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:00:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:00:09 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750406408856 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:00:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:00:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:00:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750406423368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:00:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750406423368 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:05:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750406724357 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:05:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750406724357 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:05:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750406741367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:05:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750406741367 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:06:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:06:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750406801380 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:06:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:06:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:06:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750406801382 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:06:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:06:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750406801380 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:06:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:06:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:06:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750406801382 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:06:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:06:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:10:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750407025362 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:10:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750407025362 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:10:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750407041370 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:10:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750407041370 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:11:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:11:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750407101398 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:11:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:11:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:11:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:11:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750407101400 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:11:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:11:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750407101398 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:11:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:11:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750407101400 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:11:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:11:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:15:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750407326361 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:15:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750407326361 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:15:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750407341462 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:15:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750407341462 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:16:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750407401389 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:16:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750407401391 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:16:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:16:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750407401389 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:16:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:16:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:16:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:16:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750407401391 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:20:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:20:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750407606391 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:20:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750407606391 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:20:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750407606464 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:20:06 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750407606464 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:20:06 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750407606793 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:20:06 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:20:07 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:20:07 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750407606793 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:20:07 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:20:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750407626402 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:20:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750407626402 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:25:03 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:25:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750407907364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:25:07 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750407907364 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:25:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750407908368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:25:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750407908368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:25:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:25:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750407908368 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:25:08 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:25:09 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750407908368 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:25:09 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:25:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750407927366 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:25:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750407927366 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:30:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750408228363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:30:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750408228363 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:30:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750408241360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:30:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750408241360 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:31:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750408301385 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:31:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750408301386 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:31:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:31:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750408301385 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:31:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:31:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750408301386 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:31:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:31:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:35:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750408529367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:35:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750408529367 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:35:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750408541358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:35:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750408541358 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:36:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750408601398 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:36:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750408601399 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:36:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:36:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750408601398 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:36:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:36:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750408601399 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:36:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:36:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:40:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750408830364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:40:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750408830364 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:40:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750408841360 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:40:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750408841360 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:41:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750408901395 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:41:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750408901395 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:41:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:41:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750408901395 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:41:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:41:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750408901395 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:41:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:41:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:45:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750409131365 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:45:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750409131365 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:45:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750409141368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:45:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750409141368 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:46:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750409201384 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:46:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750409201384 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:46:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:46:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750409201384 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:46:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:46:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750409201384 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:46:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:46:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:50:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750409432367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:50:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750409432367 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:50:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750409441357 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:50:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750409441357 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:51:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750409501393 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:51:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750409501395 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:51:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:51:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750409501393 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:51:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:51:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750409501395 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:51:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:51:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:16:55:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750409733365 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:55:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750409733365 HTTP/1.1" 200 159
************ - - [20/Jun/2025:16:55:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750409741478 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:55:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750409741478 HTTP/1.1" 200 161
************ - - [20/Jun/2025:16:56:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750409801390 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:56:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750409801391 HTTP/1.1" 200 -
************ - - [20/Jun/2025:16:56:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:16:56:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750409801390 HTTP/1.1" 200 160
************ - - [20/Jun/2025:16:56:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:16:56:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750409801391 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:16:56:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:16:56:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:17:00:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750410034367 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:00:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750410034367 HTTP/1.1" 200 159
************ - - [20/Jun/2025:17:00:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750410041359 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:00:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750410041359 HTTP/1.1" 200 161
************ - - [20/Jun/2025:17:01:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750410101384 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:01:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750410101385 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:01:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:17:01:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750410101384 HTTP/1.1" 200 160
************ - - [20/Jun/2025:17:01:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:17:01:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750410101385 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:17:01:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:17:01:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:17:05:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750410335357 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:05:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750410335357 HTTP/1.1" 200 159
************ - - [20/Jun/2025:17:05:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750410341363 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:05:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750410341363 HTTP/1.1" 200 161
************ - - [20/Jun/2025:17:06:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750410401392 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:06:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750410401393 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:06:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:17:06:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750410401392 HTTP/1.1" 200 160
************ - - [20/Jun/2025:17:06:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:17:06:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750410401393 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:17:06:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:17:06:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:17:10:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750410636368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:10:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750410636368 HTTP/1.1" 200 159
************ - - [20/Jun/2025:17:10:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750410641368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:10:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750410641368 HTTP/1.1" 200 161
************ - - [20/Jun/2025:17:11:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750410701395 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:11:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750410701395 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:11:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:17:11:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750410701395 HTTP/1.1" 200 160
************ - - [20/Jun/2025:17:11:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:17:11:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750410701395 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:17:11:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:17:11:43 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:17:15:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750410937358 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:15:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750410937358 HTTP/1.1" 200 159
************ - - [20/Jun/2025:17:15:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750410941359 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:15:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750410941359 HTTP/1.1" 200 161
************ - - [20/Jun/2025:17:16:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750411001388 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:16:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750411001388 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:16:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:17:16:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750411001388 HTTP/1.1" 200 160
************ - - [20/Jun/2025:17:16:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:17:16:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750411001388 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:17:16:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:17:16:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:17:20:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750411238364 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:20:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750411238364 HTTP/1.1" 200 159
************ - - [20/Jun/2025:17:20:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750411241359 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:20:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750411241359 HTTP/1.1" 200 161
************ - - [20/Jun/2025:17:21:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750411301389 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:21:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750411301389 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:21:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:17:21:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750411301389 HTTP/1.1" 200 160
************ - - [20/Jun/2025:17:21:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:17:21:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750411301389 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:17:21:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:17:21:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [20/Jun/2025:17:25:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750411539368 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:25:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750411539368 HTTP/1.1" 200 159
************ - - [20/Jun/2025:17:25:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750411541369 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:25:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750411541369 HTTP/1.1" 200 161
************ - - [20/Jun/2025:17:25:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750411552171 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:25:52 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750411552171 HTTP/1.1" 200 -
************ - - [20/Jun/2025:17:25:52 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [20/Jun/2025:17:25:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750411552171 HTTP/1.1" 200 160
************ - - [20/Jun/2025:17:25:52 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [20/Jun/2025:17:25:53 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-20+08:00&etm=2025-06-20+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750411552171 HTTP/1.1" 200 440467
************ - - [20/Jun/2025:17:25:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [20/Jun/2025:17:25:53 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
