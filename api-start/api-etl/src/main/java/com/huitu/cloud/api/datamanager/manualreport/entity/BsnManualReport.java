package com.huitu.cloud.api.datamanager.manualreport.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 人工报讯配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-24
 */
@TableName("BSN_MANUALREPORT_B")
@ApiModel(value="BsnManualReport对象", description="人工报讯配置表")
public class BsnManualReport implements Serializable {
    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "站码")
    @TableId(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "表名")
    @TableId(value = "TBNM")
    private String tbnm;

    @ApiModelProperty(value = "开始报讯时间")
    @TableField("BGTM")
    private Date bgtm;

    @ApiModelProperty(value = "结束报讯时间")
    @TableField("EDTM")
    private Date edtm;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getTbnm() {
        return tbnm;
    }

    public void setTbnm(String tbnm) {
        this.tbnm = tbnm;
    }

    public Date getBgtm() {
        return bgtm;
    }

    public void setBgtm(Date bgtm) {
        this.bgtm = bgtm;
    }

    public Date getEdtm() {
        return edtm;
    }

    public void setEdtm(Date edtm) {
        this.edtm = edtm;
    }

}
