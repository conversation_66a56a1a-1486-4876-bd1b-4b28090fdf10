package com.huitu.cloud.api.shyj.stwarnrule.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.shyj.stwarnrule.entity.RainWarnRule;
import com.huitu.cloud.api.shyj.stwarnrule.entity.SwWarnRule;

/**
 * <p>
 * 测站预警规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-27
 */
public interface StWarnRuleService {
    /**
     * 雨量预警指标列表查询
     * @param adcd 政区
     * @param warnGradeId 预警等级
     * @param pageNum 页码
     * @param pageSize  每页大小
     * @return
     */
    IPage<RainWarnRule> getRainWarnrule(String adcd, String warnGradeId, int pageNum, int pageSize);
    /**
     * 水位预警指标列表查询
     * @param adcd 政区
     * @param warnGradeId 预警等级
     * @param pageNum 页码
     * @param pageSize  每页大小
     * @return
     */
    IPage<SwWarnRule> getSwWarnrule(String adcd, String warnGradeId, int pageNum, int pageSize);
}
