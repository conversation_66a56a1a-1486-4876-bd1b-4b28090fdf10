package com.huitu.cloud.api.shyj.warn.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.warn.entity.*;
import com.huitu.cloud.api.shyj.warn.mapper.LocWarnDao;
import com.huitu.cloud.api.shyj.warn.service.LocWarnService;
import com.huitu.cloud.api.syq.video.entity.BsnVdstinfo;
import com.huitu.cloud.api.syq.video.service.VideoService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ListUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 现地预警记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class LocWarnServiceImpl implements LocWarnService {
    @Autowired
    private LocWarnDao locWarnDao;
    @Autowired
    private VideoService videoService;


    @Override
    public IPage<LocWarnVo> pageLocWarn(LocWarnQueryForm queryForm) {
        // adlvl
        queryForm.setAdlvl(AdcdUtil.getAdLevel(queryForm.getAdcd()));

        Page<LocWarnVo> page = new Page<>(queryForm.getPageNum(), queryForm.getPageSize());
        IPage<LocWarnVo> warnPage = locWarnDao.pageLocWarn(page, queryForm);
        if (warnPage == null || CollectionUtils.isEmpty(warnPage.getRecords())) {
            return warnPage;
        }

        Map<String, LocWarnVo> locWarnMap = warnPage.getRecords().stream().collect(Collectors.toMap(LocWarnVo::getWarnid, Function.identity(), (o, n) -> o));
        List<String> warnids = warnPage.getRecords().stream().map(LocWarnVo::getWarnid).collect(Collectors.toList());

        // device
        LocWarnQueryForm subQueryForm = new LocWarnQueryForm();
        subQueryForm.setWarnids(warnids);
        List<LocWarnDeviceVo> deviceVoList = this.listLocWarnDevice(subQueryForm);
        if (CollectionUtils.isNotEmpty(deviceVoList)) {
            deviceVoList.forEach(item -> {
                LocWarnVo locWarnVo = locWarnMap.get(item.getWarnid());
                if (locWarnVo != null) {
                    locWarnVo.getDeviceList().add(item);
                }
            });
        }

        // video
        try {
            List<BsnVdstinfo> vdstinfoList = videoService.getVideoInfoByPage(queryForm.getAdcd(), null, null, null);
            if (CollectionUtils.isNotEmpty(vdstinfoList)) {
                Map<String, BsnVdstinfo> vdstinfoMap = new HashMap<>();
                vdstinfoList.forEach(item -> {
                    vdstinfoMap.put(StringUtils.trim(item.getStcd()), item);
                });

                warnPage.getRecords().forEach(item -> {
                    BsnVdstinfo bsnVdstinfo = vdstinfoMap.get(item.getStcd());
                    if (bsnVdstinfo != null) {
                        item.getVideoList().add(bsnVdstinfo);
                    }
                });
            }
        } catch (Exception ex) {

        }

        return warnPage;
    }

    @Override
    public List<StwarnrecordXdRVo> listLocWarn(LocWarnQueryForm queryForm) {
        if (queryForm.getAdcd() != null && !queryForm.getAdcd().equals("")) {
            queryForm.setAdlvl(AdcdUtil.getAdLevel(queryForm.getAdcd()));
        }

        List<StwarnrecordXdRVo> warnList = locWarnDao.listLocWarn(queryForm);
        if (CollectionUtils.isEmpty(warnList)) {
            return warnList;
        }

        // video
        try {
            List<BsnVdstinfo> vdstinfoList = videoService.getVideoInfoByPage(queryForm.getAdcd(), null, null, null);
            if (CollectionUtils.isNotEmpty(vdstinfoList)) {
                Map<String, BsnVdstinfo> vdstinfoMap = new HashMap<>();
                vdstinfoList.forEach(item -> {
                    vdstinfoMap.put(StringUtils.trim(item.getStcd()), item);
                });

                warnList.forEach(item -> {
                    BsnVdstinfo bsnVdstinfo = vdstinfoMap.get(item.getStcd());
                    if (bsnVdstinfo != null) {
                        item.getVideoList().add(bsnVdstinfo);
                    }
                });
            }
        } catch (Exception ex) {

        }

        return warnList;
    }

    @Override
    public List<LocWarnDeviceVo> listLocWarnDevice(LocWarnQueryForm queryForm) {
        return locWarnDao.listLocWarnDevice(queryForm);
    }

    @Override
    public LocWarnVo getLocWarnById(String warnid) {
        LocWarnVo locWarnVo = locWarnDao.getLocWarnById(warnid);

        List<String> warnids = new LinkedList<>();
        warnids.add(locWarnVo.getWarnid());

        // device
        LocWarnQueryForm subQueryForm = new LocWarnQueryForm();
        subQueryForm.setWarnids(warnids);
        List<LocWarnDeviceVo> deviceVoList = this.listLocWarnDevice(subQueryForm);
        locWarnVo.getDeviceList().addAll(deviceVoList);

        // video
        try {
            List<BsnVdstinfo> vdstinfoList = videoService.getVideoInfoByPage(null, null, null, null);
            if (CollectionUtils.isNotEmpty(vdstinfoList)) {
                Map<String, BsnVdstinfo> vdstinfoMap = vdstinfoList.stream().collect(Collectors.toMap(BsnVdstinfo::getStcd, Function.identity()));
                BsnVdstinfo bsnVdstinfo = vdstinfoMap.get(locWarnVo.getStcd());
                if (bsnVdstinfo != null) {
                    locWarnVo.getVideoList().add(bsnVdstinfo);
                }
            }
        } catch (Exception ex) {

        }

        return locWarnVo;
    }

    @Override
    public List<StwarnrecordXdRVo> listLocWarnDetailByStcd(String stcd, String stm, String etm) {
        List<StwarnrecordXdRVo> list = locWarnDao.LocWarnDetailData(stcd, stm, etm);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<String> idList = list.stream().map(StwarnrecordXdRVo::getStWarnId).collect(Collectors.toList());


        List<WarnRwmStatusR> statusList = new ArrayList<>();
        ListUtils.splitList(idList, 500).forEach(listTmp -> statusList.addAll(locWarnDao.LocWarnDetailStatusList(stcd, listTmp)));

        if (CollectionUtils.isNotEmpty(statusList)) {
            Map<String, List<WarnRwmStatusR>> collect = statusList.stream().collect(Collectors.groupingBy(WarnRwmStatusR::getStwarnid));
            list.forEach(item -> {
                List<WarnRwmStatusR> itemStatusList = collect.get(item.getStWarnId());
                if (CollectionUtils.isNotEmpty(itemStatusList)) {
                    // 按照rwid升序排序
                    itemStatusList.sort(Comparator.comparing(WarnRwmStatusR::getRwid));
                    item.getDeviceList().addAll(itemStatusList);
                }
            });
        }
        return list;
    }

}
