package com.huitu.cloud.api.syq.rsvr.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value="水库最大连续超汛限天数统计参数", description="水库最大连续超汛限天数统计参数")
public class RsvrContinueAlermQuery implements Serializable {

    @ApiModelProperty(value = "测站编码")
    private String adcd;

    @ApiModelProperty(value = "测站名称")
    private String stm;

    @ApiModelProperty(value = "测站政区编码")
    private String etm;

    @ApiModelProperty(value = "政区名称")
    private String stTypes;

    @ApiModelProperty(value = "区县政区编码")
    private String rvTypes;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getStTypes() {
        return stTypes;
    }

    public void setStTypes(String stTypes) {
        this.stTypes = stTypes;
    }

    public String getRvTypes() {
        return rvTypes;
    }

    public void setRvTypes(String rvTypes) {
        this.rvTypes = rvTypes;
    }
}
