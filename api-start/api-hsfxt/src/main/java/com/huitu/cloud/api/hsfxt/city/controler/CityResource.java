package com.huitu.cloud.api.hsfxt.city.controler;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.hsfxt.city.entity.*;
import com.huitu.cloud.api.hsfxt.city.service.CityService;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 风险图-城市 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-09
 */

@RestController
@Api(tags = "风险图-城市接口")
@Validated
@RequestMapping("/api/hsfxt/city")
public class CityResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "0b466e43-89fe-7a97-434e-65c469a16bfa";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    CityService cityService;

    @ApiOperation(value = "城市名称信息查询", notes = "城市名称信息查询")
    @GetMapping(value = "get-bzqy-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fxtType", value = "风险图类型", required = true, dataType = "String", example = "MT1")
    })
    public ResponseEntity<SuccessResponse<List<DicBianzhiquyu>>> getBzqyInfo(@RequestParam String fxtType) throws Exception {
        List<DicBianzhiquyu> list = cityService.getBzqyInfo(fxtType);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "洪水来源信息查询", notes = "洪水来源信息查询")
    @GetMapping(value = "get-hsly-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sortIds", value = "排序号", required = true, dataType = "String", example = "1,2")
    })
    public ResponseEntity<SuccessResponse<List<DicHongShuiLaiYuan>>> getHslyInfo(@SqlInjection @RequestParam String sortIds) throws Exception {
        List<DicHongShuiLaiYuan> list = cityService.getHslyInfo(sortIds);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "分区信息查询", notes = "分区信息查询")
    @GetMapping(value = "get-fq-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bianzhiquid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<DicJisuanfenqu>>> getFqInfo(@RequestParam String bianzhiquid) throws Exception {
        List<DicJisuanfenqu> list = cityService.getFqInfo(bianzhiquid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "溃口信息查询", notes = "溃口信息查询")
    @GetMapping(value = "get-kk-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jisuanfenquid", value = "计算分区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<DicKuikou>>> getKkInfo(@SqlInjection @RequestParam String jisuanfenquid) throws Exception {
        List<DicKuikou> list = cityService.getKkInfo(jisuanfenquid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "洪水量级查询", notes = "洪水量级查询")
    @GetMapping(value = "get-hslj-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bianzhiquid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<DicHongshuiliangji>>> getHsljInfo(@RequestParam String bianzhiquid) throws Exception {
        List<DicHongshuiliangji> list = cityService.getHsljInfo(bianzhiquid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "降雨历时查询", notes = "降雨历时查询")
    @GetMapping(value = "get-jyls-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bianzhiquid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<DicJiangyulishi>>> getJylsInfo(@RequestParam String bianzhiquid) throws Exception {
        List<DicJiangyulishi> list = cityService.getJylsInfo(bianzhiquid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "简介信息查询", notes = "简介信息查询")
    @GetMapping(value = "get-Jj-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fengxiantuid", value = "风险图类型编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bianzhiquid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<DicJianjie>>> getJjInfo(@RequestParam String fengxiantuid, @RequestParam String bianzhiquid) throws Exception {
        List<DicJianjie> list = cityService.getJjInfo(fengxiantuid, bianzhiquid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "溃口名称详情查询", notes = "溃口名称详情查询")
    @GetMapping(value = "get-kkmc-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "kuikousort", value = "溃口序号", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<DicShuxing>>> getKkmcInfo(@RequestParam String kuikousort) throws Exception {
        List<DicShuxing> list = cityService.getKkmcInfo(kuikousort);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "城市社会经济统计查询", notes = "社会经济统计查询")
    @GetMapping(value = "get-shjj-tree-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<CsShehuijingji>>> getShjjTreeInfo(@SqlInjection @RequestParam String key) throws Exception {
        List<CsShehuijingji> list = cityService.getShjjTreeInfo(key);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "城市列表查询", notes = "城市列表查询")
    @GetMapping(value = "get-cslb-tree-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "最小风险政区单位", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<CsZhijiejingjisunshi>>> getSjjjssTreeInfo(@SqlInjection @RequestParam String key, @RequestParam String name) throws Exception {
        List<CsZhijiejingjisunshi> list = cityService.getSjjjssTreeInfo(key, name);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "城市详情查询", notes = "城市详情查询")
    @GetMapping(value = "get-chxq-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<CsZhijiejingjisunshi>> getOneSjjjssInfo(@SqlInjection @RequestParam String key, @RequestParam String adcd) throws Exception {
        CsZhijiejingjisunshi info = cityService.getOneSjjjssInfo(key, adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", info));
    }

}
