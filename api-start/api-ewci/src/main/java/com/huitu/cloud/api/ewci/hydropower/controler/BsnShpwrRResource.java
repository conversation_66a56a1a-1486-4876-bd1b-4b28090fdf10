package com.huitu.cloud.api.ewci.hydropower.controler;


import com.huitu.cloud.api.ewci.hydropower.entity.BsnShpwrR;
import com.huitu.cloud.api.ewci.hydropower.entity.ShpwrRQo;
import com.huitu.cloud.api.ewci.hydropower.entity.ShpwrRVo;
import com.huitu.cloud.api.ewci.hydropower.service.BsnShpwrRService;
import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.*;
import com.huitu.cloud.entity.PageBean;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import org.springframework.http.ResponseEntity;


/**
 * <p>
 *  小水电生态放流监测
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-08
 */

@RestController
@Api(tags = "小水电生态放流监测")
@RequestMapping("/api/ewci/shpwr")
    public class BsnShpwrRResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
            return "00000000-0000-0000-0000-000000000000";
            }
    @Override
    public String getVersion() {
            return "1.0";
            }
    @Autowired
    private BsnShpwrRService baseService;

    @ApiOperation(value = "分页查询",notes="分页查询小水电生态放流监测列表")
    @PostMapping(value = "select-by-page")
    public ResponseEntity<SuccessResponse<Page<ShpwrRVo>>> getBsnShpwrRByPage(@RequestBody ShpwrRQo baseDao) throws Exception {
            IPage<ShpwrRVo> list = baseService.selectByPage(baseDao.getAdcd(), baseDao.getStnm(), baseDao.getPageNum(), baseDao.getPageSize());
            return ResponseEntity.ok(
            new SuccessResponse(this, "OK", list));
     }
}







