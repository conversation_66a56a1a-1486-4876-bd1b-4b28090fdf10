package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 累计雨量统计导出接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-29
 */
@ApiModel(value="ExportQueryRain对象", description="累计雨量统计导出接口")
public class ExportQueryRain {
    @ApiModelProperty(value = "导出文件模板 1：综合监视测站降雨，2：雨情查询累计雨量，3：综合监视预报降雨，4：气象信息预报降水")
    private int type;
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "测站归属类型 1、水文，2、山洪  3、气象")
    private List<String> stType;
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "流域编码")
    private String bscd;
    @ApiModelProperty(value = "流域名称")
    private String bsnm;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "降雨阈值范围,例：“1-10”")
    private String threshold;
    @ApiModelProperty(value = "显示类型（0：查询所有雨量站信息，1：查询一段时间内所有上报雨量的站）")
    private String rainShowType;

    @ApiModelProperty(value = "是否是辖区外(用户登录以后有用) 1：辖区内，2：辖区外")
    private List<String> isOut;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getThreshold() {
        return threshold;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }

    public String getRainShowType() {
        return rainShowType;
    }

    public void setRainShowType(String rainShowType) {
        this.rainShowType = rainShowType;
    }

    public List<String> getIsOut() {
        return isOut;
    }

    public void setIsOut(List<String> isOut) {
        this.isOut = isOut;
    }
}
