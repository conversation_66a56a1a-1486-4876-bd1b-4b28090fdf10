package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
@ApiModel(value="MoistureMeteorologyVo对象", description="墒情气象监测")
public class MoistureMeteorologyVo implements Serializable {
    @ApiModelProperty(value = "测站编码")
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "时间")
    private LocalDateTime tm;
    @ApiModelProperty(value = "风速")
    private BigDecimal WindSpeed;
    @ApiModelProperty(value = "风向（度数）")
    private BigDecimal WindDirectionNum;
    @ApiModelProperty(value = "风向")
    private String WindDirection;
    @ApiModelProperty(value = "空气温度")
    private BigDecimal AirTemp;
    @ApiModelProperty(value = "空气湿度")
    private BigDecimal AirHumidity;
    @ApiModelProperty(value = "大气压力")
    private BigDecimal AtmPress;
    @ApiModelProperty(value = "降雨量")
    private BigDecimal Rainfall;
    @ApiModelProperty(value = "太阳辐射强度")
    private BigDecimal CurrentSolarRadiationIntensity;

    @Override
    public String toString() {
        return "MoistureMeteorologyVo{" +
                "stcd='" + stcd + '\'' +
                ", stnm='" + stnm + '\'' +
                ", tm=" + tm +
                ", WindSpeed=" + WindSpeed +
                ", WindDirection='" + WindDirection + '\'' +
                ", AirTemp='" + AirTemp + '\'' +
                ", AirHumidity='" + AirHumidity + '\'' +
                ", AtmPress='" + AtmPress + '\'' +
                ", Rainfall='" + Rainfall + '\'' +
                ", CurrentSolarRadiationIntensity='" + CurrentSolarRadiationIntensity + '\'' +
                '}';
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public BigDecimal getWindSpeed() {
        return WindSpeed;
    }

    public void setWindSpeed(BigDecimal windSpeed) {
        WindSpeed = windSpeed;
    }

    public String getWindDirection() {
        return WindDirection;
    }

    public void setWindDirection(String windDirection) {
        WindDirection = windDirection;
    }

    public BigDecimal getAirTemp() {
        return AirTemp;
    }

    public void setAirTemp(BigDecimal airTemp) {
        AirTemp = airTemp;
    }

    public BigDecimal getAirHumidity() {
        return AirHumidity;
    }

    public void setAirHumidity(BigDecimal airHumidity) {
        AirHumidity = airHumidity;
    }

    public BigDecimal getAtmPress() {
        return AtmPress;
    }

    public void setAtmPress(BigDecimal atmPress) {
        AtmPress = atmPress;
    }

    public BigDecimal getRainfall() {
        return Rainfall;
    }

    public void setRainfall(BigDecimal rainfall) {
        Rainfall = rainfall;
    }

    public BigDecimal getCurrentSolarRadiationIntensity() {
        return CurrentSolarRadiationIntensity;
    }

    public void setCurrentSolarRadiationIntensity(BigDecimal currentSolarRadiationIntensity) {
        CurrentSolarRadiationIntensity = currentSolarRadiationIntensity;
    }
}
