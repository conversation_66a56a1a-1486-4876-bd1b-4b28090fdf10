<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.DanPersonTmpDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="batchInsert">
        INSERT INTO BNS_DANPERSON_TMP (ID, NO, XADCD, TOWN, XZC, ZRC, DANAME, REALNM, MOBILE)
        SELECT T.ID, T.NO, T.XADCD, T.TOWN, T.XZC, T.ZRC, T.DANAME, T.REALNM, T.MOBILE FROM (VALUES
        <foreach collection="map.list" item="item" separator=",">
            (NEWID(), #{map.batchNo}, #{map.xadcd}, #{item.town}, #{item.xzc}, #{item.zrc}, #{item.daname},
            #{item.realnm}, #{item.mobile})
        </foreach>) AS T(ID, NO, XADCD, TOWN, XZC, ZRC, DANAME, REALNM, MOBILE)
    </insert>
</mapper>
