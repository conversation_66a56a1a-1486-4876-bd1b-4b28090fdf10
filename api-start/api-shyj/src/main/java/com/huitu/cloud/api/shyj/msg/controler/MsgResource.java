package com.huitu.cloud.api.shyj.msg.controler;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.shyj.msg.entity.MsgVo;
import com.huitu.cloud.api.shyj.msg.service.MsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 政区预警记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */

@RestController
@Api(tags = "消息接口")
@RequestMapping("/api/shyj/msg")
public class MsgResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "7e674fcf-a4d7-44ea-897a-7e93a221ec9b";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private MsgService baseService;
    @ApiOperation(value = "预警短信发送统计", notes = "预警短信发送统计")
    @GetMapping(value = "get-warn-message-xian-statistics")
    public ResponseEntity<SuccessResponse<List<MsgVo>>> getMsgSendReport(@RequestParam String adcd, String stm, String etm) throws Exception {
        List<MsgVo> list = baseService.getMsgSendReport(adcd,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
}







