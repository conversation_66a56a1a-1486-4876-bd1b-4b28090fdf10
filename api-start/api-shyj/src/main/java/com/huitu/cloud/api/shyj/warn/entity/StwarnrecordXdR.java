package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 现地预警记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@TableName("STWARNRECORD_R")
@ApiModel(value="StwarnrecordXdR对象", description="现地预警记录表")
public class StwarnrecordXdR extends Model<StwarnrecordXdR> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = " 测站预警ID ")
    @TableId(value = "STWARNID", type = IdType.NONE)
    private String stwarnid;

    @ApiModelProperty(value = "村政区编码 ")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "测站预警类型名称")
    @TableField("WARNTYPEID")
    private Double warntypeid;

    @ApiModelProperty(value = " 预警等级名称 ")
    @TableField("WARNGRADEID")
    private Double warngradeid;

    @ApiModelProperty(value = " 预警名称 ")
    @TableField("STWARNNM")
    private String stwarnnm;

    @ApiModelProperty(value = "  测站预警说明 ")
    @TableField("STWARNDESC")
    private String stwarndesc;

    @ApiModelProperty(value = " 开始时间 ")
    @TableField("STWARNSTM")
    private LocalDateTime stwarnstm;

    @ApiModelProperty(value = " 结束时间")
    @TableField("STWARNETM")
    private LocalDateTime stwarnetm;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;


    public String getStwarnid() {
        return stwarnid;
    }

    public void setStwarnid(String stwarnid) {
        this.stwarnid = stwarnid;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Double getWarntypeid() {
        return warntypeid;
    }

    public void setWarntypeid(Double warntypeid) {
        this.warntypeid = warntypeid;
    }

    public Double getWarngradeid() {
        return warngradeid;
    }

    public void setWarngradeid(Double warngradeid) {
        this.warngradeid = warngradeid;
    }

    public String getStwarnnm() {
        return stwarnnm;
    }

    public void setStwarnnm(String stwarnnm) {
        this.stwarnnm = stwarnnm;
    }

    public String getStwarndesc() {
        return stwarndesc;
    }

    public void setStwarndesc(String stwarndesc) {
        this.stwarndesc = stwarndesc;
    }

    public LocalDateTime getStwarnstm() {
        return stwarnstm;
    }

    public void setStwarnstm(LocalDateTime stwarnstm) {
        this.stwarnstm = stwarnstm;
    }

    public LocalDateTime getStwarnetm() {
        return stwarnetm;
    }

    public void setStwarnetm(LocalDateTime stwarnetm) {
        this.stwarnetm = stwarnetm;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    protected Serializable pkVal() {
        return this.stwarnid;
    }

    @Override
    public String toString() {
        return "StwarnrecordXdR{" +
        "stwarnid=" + stwarnid +
        ", stcd=" + stcd +
        ", warntypeid=" + warntypeid +
        ", warngradeid=" + warngradeid +
        ", stwarnnm=" + stwarnnm +
        ", stwarndesc=" + stwarndesc +
        ", stwarnstm=" + stwarnstm +
        ", stwarnetm=" + stwarnetm +
        ", remark=" + remark +
        "}";
    }
}
