package com.huitu.cloud.api.common.msg.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


/**
 * <p>
 * 失败短信记录
 * </p>
 *
 * <AUTHOR>
 * @since 2020-9-7
 */
@ApiModel(value="FailMessage类", description="失败短信记录")
public class FailMessage implements Serializable {
    @ApiModelProperty(value = "短息接收年份 ")
    private String code;

    @ApiModelProperty(value = "短信接收编号 ")
    private Integer smsid;

    @ApiModelProperty(value = " 所属政区 ")
    private String adcd;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getSmsid() {
        return smsid;
    }

    public void setSmsid(Integer smsid) {
        this.smsid = smsid;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }
}
