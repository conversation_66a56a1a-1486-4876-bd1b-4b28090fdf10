package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-26
 */
@TableName("BSN_RAIN_ALARM")
@ApiModel(value="降雨告警列表", description="")
public class BsnRainAlarm extends Model<BsnRainAlarm> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "时段")
    @TableField("STDT")
    private Integer stdt;

    @ApiModelProperty(value = "级别")
    @TableField("ALARMGRADEID")
    private String alarmgradeid;

    @ApiModelProperty(value = "告警时间")
    @TableField("ALARMSTM")
    private LocalDateTime alarmstm;

    @ApiModelProperty(value = "告警结束时间")
    @TableField("ALARMETM")
    private LocalDateTime alarmetm;

    @ApiModelProperty(value = "告警描述")
    @TableField("ALARMDESC")
    private String alarmdesc;

    @ApiModelProperty(value = "雨量")
    @TableField("DRP")
    private Double drp;

    @ApiModelProperty(value = "超指标雨量")
    @TableField("ALERMDRP")
    private Double alermdrp;

    @ApiModelProperty(value = "备注")
    @TableField("REAMRK")
    private String reamrk;

    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    @TableField("BSNM")
    private String bsnm;

    @ApiModelProperty(value = "站址")
    @TableField("STLC")
    private String stlc;

    @ApiModelProperty(value = "站类")
    @TableField("ADDVCD")
    private String addvcd;

    @ApiModelProperty(value = "站类")
    @TableField("STTP")
    private String sttp;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private String lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private String lttd;

    @ApiModelProperty(value = "政区名称")
    @TableField("ADNM")
    private String adnm;

    @ApiModelProperty(value = "县名称")
    @TableField("XADNM")
    private String xadnm;

    @ApiModelProperty(value = "测站归属")
    @TableField("STADTPNM")
    private String stadtpnm;
    @ApiModelProperty(value = "偏移经度")
    private BigDecimal plgtd;

    @ApiModelProperty(value = "偏移纬度")
    private BigDecimal plttd;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Integer getStdt() {
        return stdt;
    }

    public void setStdt(Integer stdt) {
        this.stdt = stdt;
    }

    public String getAlarmgradeid() {
        return alarmgradeid;
    }

    public void setAlarmgradeid(String alarmgradeid) {
        this.alarmgradeid = alarmgradeid;
    }

    public LocalDateTime getAlarmstm() {
        return alarmstm;
    }

    public void setAlarmstm(LocalDateTime alarmstm) {
        this.alarmstm = alarmstm;
    }

    public LocalDateTime getAlarmetm() {
        return alarmetm;
    }

    public void setAlarmetm(LocalDateTime alarmetm) {
        this.alarmetm = alarmetm;
    }

    public String getAlarmdesc() {
        return alarmdesc;
    }

    public void setAlarmdesc(String alarmdesc) {
        this.alarmdesc = alarmdesc;
    }

    public Double getDrp() {
        return drp;
    }

    public void setDrp(Double drp) {
        this.drp = drp;
    }

    public Double getAlermdrp() {
        return alermdrp;
    }

    public void setAlermdrp(Double alermdrp) {
        this.alermdrp = alermdrp;
    }

    public String getReamrk() {
        return reamrk;
    }

    public void setReamrk(String reamrk) {
        this.reamrk = reamrk;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getAddvcd() {
        return addvcd;
    }

    public void setAddvcd(String addvcd) {
        this.addvcd = addvcd;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    @Override
    public String toString() {
        return "BsnRainAlarm{" +
                "stcd='" + stcd + '\'' +
                ", stdt=" + stdt +
                ", alarmgradeid='" + alarmgradeid + '\'' +
                ", alarmstm=" + alarmstm +
                ", alarmetm=" + alarmetm +
                ", alarmdesc='" + alarmdesc + '\'' +
                ", drp=" + drp +
                ", alermdrp=" + alermdrp +
                ", reamrk='" + reamrk + '\'' +
                ", stnm='" + stnm + '\'' +
                ", bsnm='" + bsnm + '\'' +
                ", stlc='" + stlc + '\'' +
                ", addvcd='" + addvcd + '\'' +
                ", sttp='" + sttp + '\'' +
                ", lgtd='" + lgtd + '\'' +
                ", lttd='" + lttd + '\'' +
                ", adnm='" + adnm + '\'' +
                ", xadnm='" + xadnm + '\'' +
                ", stadtpnm='" + stadtpnm + '\'' +
                '}';
    }
}
