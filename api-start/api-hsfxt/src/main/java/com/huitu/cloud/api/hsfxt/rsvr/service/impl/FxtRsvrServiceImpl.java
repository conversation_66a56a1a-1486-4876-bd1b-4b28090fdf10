package com.huitu.cloud.api.hsfxt.rsvr.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.hsfxt.rsvr.entity.KbRvsanalyG;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmInfo;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmPic;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrObjinfo;
import com.huitu.cloud.api.hsfxt.rsvr.mapper.FxtRsvrDao;
import com.huitu.cloud.api.hsfxt.rsvr.service.FxtRsvrService;
import com.huitu.cloud.util.AdcdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 风险图-小水库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
@Service
public class FxtRsvrServiceImpl implements FxtRsvrService {
    @Autowired
    FxtRsvrDao fxtRsvrDao;

    @Override
    public IPage<KbRvsanalyG> getFxtRsvrList(String adcd, String name, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("name", name);
        IPage<KbRvsanalyG> result = fxtRsvrDao.getFxtRsvrList(page, param);
        return result;
    }

    @Override
    public List<RsvrDmInfo> getFxtRsvrDmList(String cid) {
        List<RsvrDmInfo> list=fxtRsvrDao.getFxtRsvrDmList(cid);
        return list;
    }

    @Override
    public List<RsvrDmPic> getFxtRsvrDmPic(String cid) {
        List<RsvrDmPic> list=fxtRsvrDao.getFxtRsvrDmPic(cid);
        return list;
    }

    @Override
    public List<KbRvsanalyG> getFxtRsvrListByStcd(String stcd) {
        List<KbRvsanalyG> list = fxtRsvrDao.getFxtRsvrListByStcd(stcd);
        return list;
    }

    @Override
    public List<RsvrObjinfo> getFzqRsvrListByStcd(String stcd) {
        List<RsvrObjinfo> list = fxtRsvrDao.getFzqRsvrListByStcd(stcd);
        return list;
    }
}
