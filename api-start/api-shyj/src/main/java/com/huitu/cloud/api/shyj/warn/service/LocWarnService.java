package com.huitu.cloud.api.shyj.warn.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.shyj.warn.entity.LocWarnDeviceVo;
import com.huitu.cloud.api.shyj.warn.entity.LocWarnQueryForm;
import com.huitu.cloud.api.shyj.warn.entity.LocWarnVo;
import com.huitu.cloud.api.shyj.warn.entity.StwarnrecordXdRVo;

import java.util.List;

/**
 * 现地预警记录表 服务类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface LocWarnService {

    /**
     * 分页查询现地预警信息
     *
     * @param queryForm
     * @return
     */
    IPage<LocWarnVo> pageLocWarn(LocWarnQueryForm queryForm);

    /**
     * 列表查询现地预警信息
     *
     * @param queryForm
     * @return
     */
    List<StwarnrecordXdRVo> listLocWarn(LocWarnQueryForm queryForm);

    /**
     * 列表查询现地预警设备信息
     *
     * @param queryForm
     * @return
     */
    List<LocWarnDeviceVo> listLocWarnDevice(LocWarnQueryForm queryForm);

    /**
     * 查询现地预警详情信息
     *
     * @param warnid 预警id
     * @return
     */
    LocWarnVo getLocWarnById(String warnid);

    /**
     * 查询测站在一段时间内现地预警信息
     * @param
     * @return
     */
    List<StwarnrecordXdRVo> listLocWarnDetailByStcd(String stcd, String stm, String etm);
}
