package com.huitu.cloud.api.fusion.service;

import com.huitu.cloud.api.fusion.entity.request.FusionWarnMessageReq;
import com.huitu.cloud.api.fusion.entity.request.FusionWarningSummaryReq;
import com.huitu.cloud.api.fusion.entity.response.FusionWarnMessageStatisticsResp;
import com.huitu.cloud.api.fusion.entity.response.FusionWarningSummaryResp;

import java.util.List;

/**
 * 监测预警统计服务
 */
public interface FusionStatisticsService {

    /**
     * 获取监测预警汇总统计信息
     *
     * @param request 请求参数
     * @return 汇总统计信息
     */
    FusionWarningSummaryResp getWarningSummary(FusionWarningSummaryReq request);

    /**
     * 政区预警本级统计信息查询
     *
     * @param query
     * @return
     */
    FusionWarnMessageStatisticsResp getAdWarnStatistics(FusionWarnMessageReq query);

    /**
     * 政区预警下级统计信息树形列表查询
     *
     * @param query
     * @return
     */
    List<FusionWarnMessageStatisticsResp> getAdWarnStatisticsTreeList(FusionWarnMessageReq query);
}
