package com.huitu.cloud.api.fusion.entity.request;

import com.huitu.cloud.entity.EntityRequest;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 预警次数和短信条数统计
 *
 * <AUTHOR>
 */
@ApiModel(value = "预警次数和短信条数统计")
public class FusionWarnMessageReq implements EntityRequest, Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码（15位）", required = true)
    @NotBlank(message = "行政区划代码不能为空")
    @Size(min = 15, max = 15, message = "行政区划代码的长度应为15")
    @SqlInjection
    private String adcd;

    @ApiModelProperty(value = "开始时间（必填，包含此时间）", required = true)
    @NotBlank(message = "开始时间不能为空")
    @SqlInjection
    private String stm;

    @ApiModelProperty(value = "结束时间（选填，包含此时间）")
    @SqlInjection
    private String etm;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }
}
