package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;

import java.io.Serializable;

/**
 * 提防统计
 */
@ApiModel(value = "提防统计")
public class RiverDikeVo implements Serializable {

    @ApiModelProperty(value = "河长")
    @TableField(value = "REACH_LEN")
    private Double reachLen;

    @ApiModelProperty(value = "提防总长")
    @TableField(value = "DIKE_LEN")
    private Double dikeLen;

    @ApiModelProperty(value = "提防达标长度")
    @TableField(value = "UP_DIKE_LEN")
    private Double upDikeLen;

    @ApiModelProperty(value = "堤防未达标长度")
    @TableField(value = "NOT_DIKE_LEN")
    private Double notDikeLen;

    @ApiModelProperty(value = "险工险段长度")
    @TableField(value = "DPDS_LEN")
    private Double dpdsLen;

    @ApiModelProperty(value = "沙基长度")
    @TableField(value = "B_LEN")
    private Double bLen;

    @ApiModelProperty(value = "已治理沙基")
    @TableField(value = "B_UP_LEN")
    private Double bUpLen;

    @ApiModelProperty(value = "未治理沙基")
    @TableField(value = "B_NOT_LEN")
    private Double bNotLen;

    @ApiModelProperty(value = "流域面积")
    @TableField(value = "RV_BAS_AREA")
    private Double rvBasArea;

    @ApiModelProperty(value = "险工险段数")
    @TableField(value = "DPDS_NUM")
    private Integer dpdsNum;

    public Double getReachLen() {
        return reachLen;
    }

    public void setReachLen(Double reachLen) {
        this.reachLen = reachLen;
    }

    public Double getDikeLen() {
        return dikeLen;
    }

    public void setDikeLen(Double dikeLen) {
        this.dikeLen = dikeLen;
    }

    public Double getUpDikeLen() {
        return upDikeLen;
    }

    public void setUpDikeLen(Double upDikeLen) {
        this.upDikeLen = upDikeLen;
    }

    public Double getNotDikeLen() {
        return notDikeLen;
    }

    public void setNotDikeLen(Double notDikeLen) {
        this.notDikeLen = notDikeLen;
    }

    public Double getDpdsLen() {
        return dpdsLen;
    }

    public void setDpdsLen(Double dpdsLen) {
        this.dpdsLen = dpdsLen;
    }

    public Double getbLen() {
        return bLen;
    }

    public void setbLen(Double bLen) {
        this.bLen = bLen;
    }

    public Double getbUpLen() {
        return bUpLen;
    }

    public void setbUpLen(Double bUpLen) {
        this.bUpLen = bUpLen;
    }

    public Double getbNotLen() {
        return bNotLen;
    }

    public void setbNotLen(Double bNotLen) {
        this.bNotLen = bNotLen;
    }

    public Integer getDpdsNum() {
        return dpdsNum;
    }

    public void setDpdsNum(Integer dpdsNum) {
        this.dpdsNum = dpdsNum;
    }

    public Double getRvBasArea() {
        return rvBasArea;
    }

    public void setRvBasArea(Double rvBasArea) {
        this.rvBasArea = rvBasArea;
    }
}
