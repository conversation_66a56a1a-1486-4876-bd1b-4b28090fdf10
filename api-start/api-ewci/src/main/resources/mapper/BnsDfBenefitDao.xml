<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.BnsDfBenefitDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="insert">
        INSERT INTO BNS_DF_BENEFIT (ADCD, STM, ETM, XCOUNT, WCOUNT, SCOUNT, DPCOUNT, TPCOUNT, ACPCOUNT, REMARK)
        VALUES (#{adcd}, #{stm}, #{etm}, #{xcount}, #{wcount}, #{scount}, #{dpcount}, #{tpcount}, #{acpcount},
                #{remark})
    </insert>
    <update id="update">
        UPDATE BNS_DF_BENEFIT
        <set>
            <if test="xcount != null ">
                XCOUNT = #{xcount},
            </if>
            <if test="wcount != null">
                WCOUNT = #{wcount},
            </if>
            <if test="scount != null">
                SCOUNT = #{scount},
            </if>
            <if test="dpcount != null">
                DPCOUNT = #{dpcount},
            </if>
            <if test="tpcount != null">
                TPCOUNT = #{tpcount},
            </if>
            <if test="acpcount != null">
                ACPCOUNT = #{acpcount},
            </if>
            <if test="remark != null">
                REMARK = #{remark},
            </if>
        </set>
        WHERE ADCD = #{adcd} AND STM = #{stm} AND ETM = #{etm}
    </update>
    <delete id="delete">
        delete
        from BNS_DF_BENEFIT
        where ADCD = #{adcd}
          AND STM = #{stm}
          AND ETM = #{etm}
    </delete>
    <select id="getList" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsDfBenefit">
        SELECT A.ADCD,
               ADNM,
               STM,
               ETM,
               XCOUNT,
               WCOUNT,
               SCOUNT,
               DPCOUNT,
               TPCOUNT,
               ACPCOUNT,
               REMARK,
               TS
        FROM BNS_DF_BENEFIT A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        WHERE LEFT (A.ADCD , #{map.level}) = LEFT (#{map.adcd} , #{map.level})
          AND (
          (STM >= #{map.stm} AND STM &lt;= #{map.etm})
        OR (ETM >= #{map.stm} AND ETM &lt;= #{map.etm}))
        ORDER BY TS DESC
    </select>


</mapper>