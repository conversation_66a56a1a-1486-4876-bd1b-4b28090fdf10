package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 涵洞
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@ApiModel(value = "涵洞列表")
public class CulvertData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "路涵编码")
    @TableField(value = "CULCD")
    private String culcd;

    @ApiModelProperty(value = "路涵名称")
    @TableField(value = "CULNAME")
    private String culname;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "小流域代码")
    @TableField(value = "wscd")
    private String wscd;

    @ApiModelProperty(value = "照片编号")
    @TableField(value = "PICID")
    private String picid;

    @ApiModelProperty(value = "经度（°）")
    @TableField(value = "LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度（°）")
    @TableField(value = "LTTD")
    private BigDecimal lttd;

    @ApiModelProperty(value = "涵洞高（m）")
    @TableField(value = "HEIGHT")
    private BigDecimal height;

    @ApiModelProperty(value = "涵洞长（m）")
    @TableField(value = "LENGHT")
    private BigDecimal lenght;

    @ApiModelProperty(value = "涵洞宽（m）")
    @TableField(value = "WIDTH")
    private BigDecimal width;

    @ApiModelProperty(value = "类型")
    @TableField(value = "TYPE")
    private String type;

    @ApiModelProperty(value = "描述")
    @TableField(value = "COMMENTS")
    private String comments;

    public String getCulcd() {
        return culcd;
    }

    public void setCulcd(String culcd) {
        this.culcd = culcd;
    }

    public String getCulname() {
        return culname;
    }

    public void setCulname(String culname) {
        this.culname = culname;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getPicid() {
        return picid;
    }

    public void setPicid(String picid) {
        this.picid = picid;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getLenght() {
        return lenght;
    }

    public void setLenght(BigDecimal lenght) {
        this.lenght = lenght;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }
}
