package com.huitu.cloud.api.syq.river.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019-09-11
 */
public class QueryStForecastf extends PageBean {
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "告警状态（2：超保证，1：超警戒 。都选的话传“1,2”）")
    private String wnstatus;
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "测站编码")
    private List<String> stcds;
    @ApiModelProperty(value = "开始时间，格式：yyyy-MM-dd HH:mm:ss", example = "2023-10-10 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间，格式：yyyy-MM-dd HH:mm:ss", example = "2023-10-17 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getWnstatus() {
        return wnstatus;
    }

    public void setWnstatus(String wnstatus) {
        this.wnstatus = wnstatus;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public List<String> getStcds() {
        return stcds;
    }

    public void setStcds(List<String> stcds) {
        this.stcds = stcds;
    }
}
