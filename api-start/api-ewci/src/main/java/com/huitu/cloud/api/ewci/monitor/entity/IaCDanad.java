package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 危险区基本情况调查成果汇总
 *
 * <AUTHOR>
 */
@ApiModel(value = "防治区基本情况调查成果汇总")
public class IaCDanad implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "危险区代码")
    @TableField(value = "DAND")
    private String dand;

    @ApiModelProperty(value = "危险区名称")
    @TableField(value = "NAME")
    private String name;

    @ApiModelProperty(value = "小流域代码")
    @TableField(value = "wscd")
    private String wscd;

    @ApiModelProperty(value = "小流域名称")
    @TableField(value = "WSNM")
    private String wsnm;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "县级行政区划名称")
    @TableField(value = "XADNM")
    private String xadnm;

    @ApiModelProperty(value = "乡镇行政区划名称")
    @TableField(value = "XZADNM")
    private String xzadnm;

    @ApiModelProperty(value = "村行政区划名称")
    @TableField(value = "CADNM")
    private String cadnm;

    @ApiModelProperty(value = "危险区内人口")
    @TableField(value = "PTCOUNT")
    private Integer ptcount;

    @ApiModelProperty(value = "危险区内总户数")
    @TableField(value = "ETCOUNT")
    private Integer etcount;

    @ApiModelProperty(value = "危险区内Ⅰ类经济户数")
    @TableField(value = "ECOUNT1")
    private Integer ecount1;

    @ApiModelProperty(value = "危险区内Ⅱ类经济户数")
    @TableField(value = "ECOUNT2")
    private Integer ecount2;

    @ApiModelProperty(value = "危险区内Ⅲ类经济户数")
    @TableField(value = "ECOUNT3")
    private Integer ecount3;

    @ApiModelProperty(value = "危险区内Ⅳ类经济户数")
    @TableField(value = "ECOUNT4")
    private Integer ecount4;

    @ApiModelProperty(value = "危险区内总房屋数")
    @TableField(value = "HTCOUNT")
    private Integer htcount;

    @ApiModelProperty(value = "危险区内Ⅰ类房屋数")
    @TableField(value = "HCOUNT1")
    private Integer hcount1;

    @ApiModelProperty(value = "危险区内Ⅱ类房屋数")
    @TableField(value = "HCOUNT2")
    private Integer hcount2;

    @ApiModelProperty(value = "危险区内Ⅲ类房屋数")
    @TableField(value = "HCOUNT3")
    private Integer hcount3;

    @ApiModelProperty(value = "危险区内Ⅳ类房屋数")
    @TableField(value = "HCOUNT4")
    private Integer hcount4;

    public String getDand() {
        return dand;
    }

    public void setDand(String dand) {
        this.dand = dand;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getXzadnm() {
        return xzadnm;
    }

    public void setXzadnm(String xzadnm) {
        this.xzadnm = xzadnm;
    }

    public String getCadnm() {
        return cadnm;
    }

    public void setCadnm(String cadnm) {
        this.cadnm = cadnm;
    }

    public Integer getPtcount() {
        return ptcount;
    }

    public void setPtcount(Integer ptcount) {
        this.ptcount = ptcount;
    }

    public Integer getEtcount() {
        return etcount;
    }

    public void setEtcount(Integer etcount) {
        this.etcount = etcount;
    }

    public Integer getEcount1() {
        return ecount1;
    }

    public void setEcount1(Integer ecount1) {
        this.ecount1 = ecount1;
    }

    public Integer getEcount2() {
        return ecount2;
    }

    public void setEcount2(Integer ecount2) {
        this.ecount2 = ecount2;
    }

    public Integer getEcount3() {
        return ecount3;
    }

    public void setEcount3(Integer ecount3) {
        this.ecount3 = ecount3;
    }

    public Integer getEcount4() {
        return ecount4;
    }

    public void setEcount4(Integer ecount4) {
        this.ecount4 = ecount4;
    }

    public Integer getHtcount() {
        return htcount;
    }

    public void setHtcount(Integer htcount) {
        this.htcount = htcount;
    }

    public Integer getHcount1() {
        return hcount1;
    }

    public void setHcount1(Integer hcount1) {
        this.hcount1 = hcount1;
    }

    public Integer getHcount2() {
        return hcount2;
    }

    public void setHcount2(Integer hcount2) {
        this.hcount2 = hcount2;
    }

    public Integer getHcount3() {
        return hcount3;
    }

    public void setHcount3(Integer hcount3) {
        this.hcount3 = hcount3;
    }

    public Integer getHcount4() {
        return hcount4;
    }

    public void setHcount4(Integer hcount4) {
        this.hcount4 = hcount4;
    }
}
