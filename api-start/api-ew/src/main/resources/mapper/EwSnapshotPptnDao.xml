<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwSnapshotPptnDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPptnList" resultType="com.huitu.cloud.api.ew.entity.response.EwShPptnDataResponse">
        SELECT STCD,
               TM,
               DRP,
               INTV
        FROM EW_SNAPSHOT_PPTN A
        WHERE EXISTS(SELECT 8
                     FROM EW_REL_WARNING_MESSAGE
                     WHERE MSG_ID = #{msgId}
                       AND WARN_ID = A.WARN_ID
                       AND GRADE_ID = A.WARN_GRADE_ID)
        ORDER BY TM ASC
    </select>
</mapper>