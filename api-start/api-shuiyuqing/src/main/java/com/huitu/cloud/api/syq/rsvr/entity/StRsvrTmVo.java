package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <AUTHOR>
 * @since 2019-09-09
 */
@ApiModel(value="单站水库水情信息", description="水库水情表")
public class StRsvrTmVo extends StRsvrR implements Serializable {


    @ApiModelProperty(value = "正常高水位")
    @TableField("NORMZ")
    private BigDecimal normz;

    @ApiModelProperty(value = "超讯限水位")
    @TableField("RZFSLTDZ")
    private BigDecimal rzfsltdz;

    @ApiModelProperty(value = "超讯限库容")
    @TableField("WFSLTDW")
    private BigDecimal wfsltdw;

    @ApiModelProperty(value = "超正常高水位")
    @TableField("RZNORMZ")
    private BigDecimal rznormz;

    @ApiModelProperty(value = "汛限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @ApiModelProperty(value = "汛限库容")
    @TableField("FSLTDW")
    private BigDecimal fsltdw;

    @ApiModelProperty(value = "均值入库流量")
    @TableField("AVINQ")
    private BigDecimal avinq;

    @ApiModelProperty(value = "均值出库流量")
    @TableField("AVOTQ")
    private BigDecimal avotq;

    public BigDecimal getAvinq() {
        return avinq;
    }

    public void setAvinq(BigDecimal avinq) {
        this.avinq = avinq;
    }

    public BigDecimal getAvotq() {
        return avotq;
    }

    public void setAvotq(BigDecimal avotq) {
        this.avotq = avotq;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public BigDecimal getWfsltdw() {
        return wfsltdw;
    }

    public void setWfsltdw(BigDecimal wfsltdw) {
        this.wfsltdw = wfsltdw;
    }

    public BigDecimal getRznormz() {
        return rznormz;
    }

    public void setRznormz(BigDecimal rznormz) {
        this.rznormz = rznormz;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public BigDecimal getFsltdw() {
        return fsltdw;
    }

    public void setFsltdw(BigDecimal fsltdw) {
        this.fsltdw = fsltdw;
    }
}
