<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.BnsWarnStatisticsDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <!-- 整体统计-政区统计 -->
    <select id="getDistrictStatistics" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnStatistics">
        SELECT
        AA.ADNM AS ADNM,
        AA.ADCD AS ADCD,
        COUNT(BB.TYPETOTAL) AS WHOLENUMBER
        FROM
        (
        SELECT
        a.adnm AS adnm,
        a.adcd AS adcd
        FROM
        MDT_ADCDINFO_B a
        WHERE
        1 = 1
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(A.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '2'.toString()">
            AND A.ADLVL = 2
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '4'.toString()">
            AND A.ADLVL = 3
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '6'.toString()">
            AND A.ADLVL = 4
        </if> ) AA
        LEFT JOIN (
        SELECT
        ( CASE WHEN t.ADCD = '220581000000000' THEN t.ADCD ELSE A.ADCD END ) AS ADCD,
        1 AS TYPETOTAL
        FROM
        BNS_WARN_INFO T
        JOIN MDT_ADCDINFO_B a  <if test="map.level!= null and map.level == '2'.toString()">
        ON LEFT ( T.ADCD, 4 ) + '00000000000' = A.ADCD
    </if>
        <if test="map.level!= null and map.level == '4'.toString()">
            ON LEFT ( T.ADCD, 6 ) + '000000000' = A.ADCD
        </if>
        <if test="map.level!= null and map.level == '6'.toString()">
            ON LEFT ( T.ADCD, 9 ) + '000000' = A.ADCD
        </if>
        WHERE
        T.STATUS = 2
        <if test="map.stm != null and map.stm !='' and  map.tmType == 1">
            and T.RELEASE_TIME >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 1">
            and T.RELEASE_TIME &lt;= #{map.etm}
        </if>
        <if test="map.stm != null and map.stm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),T.RELEASE_TIME,121) >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),T.RELEASE_TIME,121) &lt;= #{map.etm}
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(T.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(T.ADCD, 6) NOT IN ('220581')
        </if>
        ) BB ON aa.adcd = bb.adcd
        GROUP BY
        AA.ADNM,
        AA.ADCD,
        BB.TYPETOTAL
    </select>

    <!-- 整体统计-时间统计 -->
    <select id="getTimeStatistics" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnStatistics">
        SELECT CONVERT
        ( VARCHAR ( ${map.format} ), T.RELEASE_TIME, 121 ) AS WHOLETIME,
        COUNT ( CONVERT ( VARCHAR ( ${map.format} ), T.RELEASE_TIME, 121 ) ) AS WHOLENUMBER
        FROM
        BNS_WARN_INFO T
        WHERE
        T.STATUS = 2
        <if test="map.level == '4'.toString()">
            AND LEFT(T.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(T.ADCD,#{map.level})=#{map.adcd}
        </if>
        AND CONVERT ( VARCHAR ( ${map.format} ), T.RELEASE_TIME, 121 ) >= #{map.stm}
        AND CONVERT ( VARCHAR ( ${map.format} ), T.RELEASE_TIME, 121 ) &lt;=  #{map.etm}
        GROUP BY
        CONVERT ( VARCHAR ( ${map.format} ), T.RELEASE_TIME, 121 )
        ORDER BY
        CONVERT ( VARCHAR ( ${map.format} ), T.RELEASE_TIME, 121 )
    </select>

    <!-- 类别统计-预警类型 -->
    <select id="gatStatisticsType" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnStatistics">
        SELECT
        C.TYPE_NAME AS TYPENAME,
        COUNT ( T.WARN_TYPE ) AS TYPETOTAL
        FROM
        BNS_WARN_INFO T
        LEFT JOIN BNS_WARN_TYPE_SETTINGS C ON T.WARN_TYPE = C.ID
        WHERE
        T.STATUS = 2
        <if test="map.stm != null and map.stm !='' and  map.tmType == 1">
            and t.RELEASE_TIME >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 1">
            and t.RELEASE_TIME &lt;= #{map.etm}
        </if>
        <if test="map.stm != null and map.stm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),t.RELEASE_TIME,121) >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),t.RELEASE_TIME,121) &lt;= #{map.etm}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(T.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(T.ADCD,#{map.level})=#{map.adcd}
        </if>
        GROUP BY
        C.ID,
        C.TYPE_NAME
    </select>

    <!-- 类别统计-预警等级 -->
    <select id="getWarningGrade"  resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnStatistics">
        SELECT
        CASE
        a.WARN_GRADE
        WHEN 1 THEN
        '极危险'
        WHEN 2 THEN
        '危险'
        WHEN 3 THEN
        '警戒'
        WHEN 4 THEN
        '关注'
        END AS gradeName,
        COUNT ( 1 ) AS gradeTotal
        FROM
        BNS_WARN_INFO a
        WHERE
        a.STATUS = 2
        <if test="map.stm != null and map.stm !='' and  map.tmType == 1">
            and a.RELEASE_TIME >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 1">
            and a.RELEASE_TIME &lt;= #{map.etm}
        </if>
        <if test="map.stm != null and map.stm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),a.RELEASE_TIME,121) >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),a.RELEASE_TIME,121) &lt;= #{map.etm}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(A.ADCD,#{map.level})=#{map.adcd}
        </if>
        GROUP BY
        a.WARN_GRADE
    </select>

    <!-- 柱状图-预警类型统计 -->
    <select id="getHistogramType" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnStatistics">
        SELECT
        AA.ADNM as adnm,
        AA.ADCD as adcd,
        BB.TYPEID as TYPEid,
        BB.TYPENAME as typeName,
        COUNT(BB.TYPETOTAL) AS typeTotal
        FROM
        ( SELECT a.adnm AS adnm, a.adcd AS adcd FROM MDT_ADCDINFO_B a WHERE 1=1
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(A.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '2'.toString()">
            AND A.ADLVL = 2
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '4'.toString()">
            AND A.ADLVL = 3
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '6'.toString()">
            AND A.ADLVL = 4
        </if> ) AA
        LEFT JOIN (
        SELECT
        ( CASE WHEN t.ADCD = '220581000000000' THEN t.ADCD ELSE A.ADCD END ) AS ADCD,
        C.TYPE_NAME AS TYPENAME,
        C.ID AS TYPEID,
        1 AS TYPETOTAL
        FROM
        BNS_WARN_INFO T
        LEFT JOIN BNS_WARN_TYPE_SETTINGS C ON T.WARN_TYPE = C.ID
        JOIN MDT_ADCDINFO_B a
        <if test="map.level!= null and map.level == '2'.toString()">
            ON LEFT ( T.ADCD, 4 ) + '00000000000' = A.ADCD
        </if>
        <if test="map.level!= null and map.level == '4'.toString()">
            ON LEFT ( T.ADCD, 6 ) + '000000000' = A.ADCD
        </if>
        <if test="map.level!= null and map.level == '6'.toString()">
            ON LEFT ( T.ADCD, 9 ) + '000000' = A.ADCD
        </if>
        WHERE
        T.STATUS = 2
        <if test="map.stm != null and map.stm !='' and  map.tmType == 1">
            and T.RELEASE_TIME >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 1">
            and T.RELEASE_TIME &lt;= #{map.etm}
        </if>
        <if test="map.stm != null and map.stm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),T.RELEASE_TIME,121) >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),T.RELEASE_TIME,121) &lt;= #{map.etm}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(T.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            AND LEFT(T.ADCD,#{map.level}) = #{map.adcd}
        </if>
        ) BB ON AA.ADCD = BB.ADCD
        GROUP BY
        AA.ADNM,
        AA.ADCD,
        BB.TYPEID,
        BB.TYPENAME,
        BB.TYPETOTAL
    </select>

    <!-- 查询行政区划名称做X轴 -->
    <select id="getAdcdList"  resultType="java.lang.String">
        SELECT
        a.adnm AS adnm
        FROM
        MDT_ADCDINFO_B a
        WHERE
        1=1
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(A.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '2'.toString()">
            AND A.ADLVL = 2
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '4'.toString()">
            AND A.ADLVL = 3
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '6'.toString()">
            AND A.ADLVL = 4
        </if>
    </select>

    <!-- 柱状图-预警等级统计 -->
    <select id="getHistogramGrade" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnStatistics">
        SELECT
        aa.adnm AS adnm,
        aa.adcd AS adcd,
        bb.WARN_GRADE AS gradeName,
        COUNT(bb.TYPETOTAL) AS gradeTotal
        FROM
        ( SELECT a.adnm AS adnm, a.adcd AS adcd FROM MDT_ADCDINFO_B a WHERE  1=1
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(A.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '2'.toString()">
            AND A.ADLVL = 2
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '4'.toString()">
            AND A.ADLVL = 3
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '6'.toString()">
            AND A.ADLVL = 4
        </if>  ) aa
        LEFT JOIN (
        SELECT
        ( CASE WHEN t.ADCD = '220581000000000' THEN t.ADCD ELSE A.ADCD END ) AS ADCD,
        (
        CASE
        WHEN T.WARN_GRADE= 1 THEN
        '极危险'
        WHEN T.WARN_GRADE= 2 THEN
        '危险'
        WHEN T.WARN_GRADE= 3 THEN
        '警戒'
        WHEN T.WARN_GRADE= 4 THEN
        '关注'
        END
        ) AS WARN_GRADE,
        1 AS TYPETOTAL
        FROM
        BNS_WARN_INFO T
        JOIN MDT_ADCDINFO_B a
        <if test="map.level!= null and map.level == '2'.toString()">
            ON LEFT ( T.ADCD, 4 ) + '00000000000' = A.ADCD
        </if>
        <if test="map.level!= null and map.level == '4'.toString()">
            ON LEFT ( T.ADCD, 6 ) + '000000000' = A.ADCD
        </if>
        <if test="map.level!= null and map.level == '6'.toString()">
            ON LEFT ( T.ADCD, 9 ) + '000000' = A.ADCD
        </if>
        WHERE
        T.STATUS = 2
        <if test="map.stm != null and map.stm !='' and  map.tmType == 1">
            and T.RELEASE_TIME >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 1">
            and T.RELEASE_TIME &lt;= #{map.etm}
        </if>
        <if test="map.stm != null and map.stm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),T.RELEASE_TIME,121) >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),T.RELEASE_TIME,121) &lt;= #{map.etm}
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(T.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(T.ADCD, 6) NOT IN ('220581')
        </if>
        ) bb ON aa.ADCD = bb.adcd
        GROUP BY
        aa.adnm,
        aa.adcd,
        bb.WARN_GRADE,
        bb.TYPETOTAL
    </select>

    <!-- 柱状图-发送方式统计 -->
    <select id="getSendingMethod" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnStatistics">
        SELECT SS.ADNM,SS.ADCD,SS.typeName as MODENAME, COUNT(SS.TYPETOTAL) AS MODETOTAL from(
        SELECT
        AA.ADNM as adnm,
        AA.ADCD as adcd,
        CASE
        BB.sendMode
        WHEN 1 THEN
        '运营商基站'
        WHEN 2 THEN
        '微信公众号'
        WHEN 3 THEN
        '导航'
        WHEN 4 THEN
        '吉林水利'
        WHEN 5 THEN
        '短信' ELSE NULL
        END AS typeName,
        BB.sendMode AS TYPEid,
        BB.TYPETOTAL AS TYPETOTAL
        FROM
        ( SELECT a.adnm AS adnm, a.adcd AS adcd FROM MDT_ADCDINFO_B a WHERE 1=1
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            and left(A.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '2'.toString()">
            AND A.ADLVL = 2
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '4'.toString()">
            AND A.ADLVL = 3
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null and map.level == '6'.toString()">
            AND A.ADLVL = 4
        </if> ) AA
        LEFT JOIN (
        SELECT
        ( CASE WHEN T.ADCD = '220581000000000' THEN T.ADCD ELSE A.ADCD END ) AS ADCD,
        'sendMode' = SUBSTRING ( T.SEND_MODE, b.number, charindex( '|', SEND_MODE + '|', b.number ) - b.number ),
        1 AS TYPETOTAL
        FROM
        BNS_WARN_INFO T
        INNER JOIN master.dbo.spt_values b ON b.number BETWEEN 1
        AND len( T.SEND_MODE )
        AND SUBSTRING ( '|' + T.SEND_MODE, b.number, 1 ) = '|'
        JOIN MDT_ADCDINFO_B a
        <if test="map.level!= null and map.level == '2'.toString()">
            ON LEFT ( T.ADCD, 4 ) + '00000000000' = A.ADCD
        </if>
        <if test="map.level!= null and map.level == '4'.toString()">
            ON LEFT ( T.ADCD, 6 ) + '000000000' = A.ADCD
        </if>
        <if test="map.level!= null and map.level == '6'.toString()">
            ON LEFT ( T.ADCD, 9 ) + '000000' = A.ADCD
        </if>
        WHERE
        T.STATUS = 2
        AND b.type= 'P'
        <if test="map.stm != null and map.stm !='' and  map.tmType == 1">
            and T.RELEASE_TIME >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 1">
            and T.RELEASE_TIME &lt;= #{map.etm}
        </if>
        <if test="map.stm != null and map.stm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),T.RELEASE_TIME,121) >= #{map.stm}
        </if>
        <if test="map.etm != null and map.etm !='' and  map.tmType == 2">
            AND CONVERT(VARCHAR(7),T.RELEASE_TIME,121) &lt;= #{map.etm}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(T.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level != null and map.adcd != '' and map.adcd != null">
            AND LEFT(T.ADCD,#{map.level}) = #{map.adcd}
        </if>
        ) BB ON AA.ADCD = BB.ADCD
        )SS
        GROUP BY
        SS.ADNM,SS.ADCD,SS.typeName, SS.TYPETOTAL
    </select>
</mapper>