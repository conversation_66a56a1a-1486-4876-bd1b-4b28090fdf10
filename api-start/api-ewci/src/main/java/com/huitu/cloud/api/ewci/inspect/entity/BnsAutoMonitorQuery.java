package com.huitu.cloud.api.ewci.inspect.entity;

import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.Year;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * 自动监测站下拉
 */
@ApiModel(value = "自动监测站下拉")
public class BnsAutoMonitorQuery implements Serializable {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "站点类型")
    private String sttp;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("sttp", getSttp());
        return params;
    }
}
