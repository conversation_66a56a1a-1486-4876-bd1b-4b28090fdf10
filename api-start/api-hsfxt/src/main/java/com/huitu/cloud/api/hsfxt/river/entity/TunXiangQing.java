package com.huitu.cloud.api.hsfxt.river.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 江河详情(屯详情)
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-25
 */
@ApiModel(value = "TunXiangQing对象", description = "江河详情(屯详情)")
public class TunXiangQing implements Serializable {
    @ApiModelProperty(value = "屯编码")
    private String adcd;

    @ApiModelProperty(value = "屯名称")
    private String adnm;

    @ApiModelProperty(value = "户数")
    private Integer fushu;

    @ApiModelProperty(value = "人口")
    private Integer renkou;

    @ApiModelProperty(value = "房屋间数")
    private Integer fangwujianshu;

    @ApiModelProperty(value = "耕地面积")
    private Float gendimianji;

    @ApiModelProperty(value = "淹没水深")
    private Float ymss;

    @ApiModelProperty(value = "流速")
    private Float hsls;

    @ApiModelProperty(value = "前锋到达时间")
    private Float ddsj;

    @ApiModelProperty(value = "转移方案")
    private List<ZhuanYiFangAn> zyfa;

    public List<ZhuanYiFangAn> getZyfa() {
        return zyfa;
    }

    public void setZyfa(List<ZhuanYiFangAn> zyfa) {
        this.zyfa = zyfa;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getFushu() {
        return fushu;
    }

    public void setFushu(Integer fushu) {
        this.fushu = fushu;
    }

    public Integer getRenkou() {
        return renkou;
    }

    public void setRenkou(Integer renkou) {
        this.renkou = renkou;
    }

    public Integer getFangwujianshu() {
        return fangwujianshu;
    }

    public void setFangwujianshu(Integer fangwujianshu) {
        this.fangwujianshu = fangwujianshu;
    }

    public Float getGendimianji() {
        return gendimianji;
    }

    public void setGendimianji(Float gendimianji) {
        this.gendimianji = gendimianji;
    }

    public Float getYmss() {
        return ymss;
    }

    public void setYmss(Float ymss) {
        this.ymss = ymss;
    }

    public Float getHsls() {
        return hsls;
    }

    public void setHsls(Float hsls) {
        this.hsls = hsls;
    }

    public Float getDdsj() {
        return ddsj;
    }

    public void setDdsj(Float ddsj) {
        this.ddsj = ddsj;
    }


}
