package com.huitu.cloud.api.syq.rain.remoting.api;

import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Post;
import com.huitu.cloud.api.syq.rain.remoting.model.IsoRequest;

/**
 * 等值分析服务远程调用接口
 *
 */
@BaseRequest(baseURL = "{isoServiceAddr}")
public interface IsoService {

    /**
     * 等值分析
     *
     * @param request 请求对象
     * @return 等值分析结果
     **/
    @Post(url = "/api/iso", contentType = "application/json")
    String analyse(@Body IsoRequest request);
}
