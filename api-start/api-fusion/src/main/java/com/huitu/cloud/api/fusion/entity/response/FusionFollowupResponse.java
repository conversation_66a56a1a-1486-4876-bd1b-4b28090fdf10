package com.huitu.cloud.api.fusion.entity.response;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrFormatter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huitu.cloud.api.fusion.entity.common.FusionDataFrom;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

import static cn.hutool.core.date.DatePattern.PURE_DATETIME_MS_PATTERN;

/**
 * 山洪预警后续降雨响应对象
 */
@Getter
@Setter
@ApiModel(value = "山洪预警后续降雨响应对象")
public class FusionFollowupResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ColumnWidth(10)
    @NumberFormat("#")
    @ExcelProperty(value = "序号", index = 0)
    @JsonIgnore
    private Integer sortno;

    @ExcelIgnore
    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARN_ID")
    private String warnId;

    @ColumnWidth(38)
    @ExcelProperty(value = "预警名称", index = 1)
    @ApiModelProperty(value = "预警名称")
    @TableField(value = "WARN_NAME")
    private String warnName;

    @ExcelIgnore
    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ColumnWidth(18)
    @ExcelProperty(value = "预警政区", index = 2)
    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ColumnWidth(18)
    @DateTimeFormat("yyyy-MM-dd HH:mm")
    @ExcelProperty(value = "预警时间", index = 3)
    @ApiModelProperty(value = "预警时间")
    @TableField(value = "WARN_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warnTime;

    @ExcelIgnore
    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ColumnWidth(16)
    @ExcelProperty(value = "预警测站", index = 4)
    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ColumnWidth(13)
    @ExcelProperty(value = "来源", index = 5, converter = StadtpConverter.class)
    @ApiModelProperty(value = "来源")
    @TableField(value = "STADTP")
    private String stadtp;

    @ExcelIgnore
    @ApiModelProperty(value = "重现期，单位：年")
    @TableField(value = "REPRD")
    private Integer reprd;

    @ColumnWidth(17)
    @NumberFormat("#.#")
    @ExcelProperty(value = {"监测", "累计降雨(mm)"}, index = 6)
    @ApiModelProperty(value = "监测累计降雨")
    @TableField(value = "RACCP")
    private Double raccp;

    @ColumnWidth(12)
    @NumberFormat("#")
    @ExcelProperty(value = {"监测", "阈值(mm)"}, index = 7)
    @ApiModelProperty(value = "监测降雨阈值")
    @TableField(value = "RDRPT")
    private Integer rdrpt;

    @ColumnWidth(12)
    @NumberFormat("#")
    @ExcelProperty(value = {"监测", "历时(h)"}, index = 8)
    @ApiModelProperty(value = "监测降雨历时")
    @TableField(value = "STDT")
    private Integer stdt;

    @ColumnWidth(17)
    @NumberFormat("#.#")
    @ExcelProperty(value = {"预报", "累计降雨(mm)"}, index = 9)
    @ApiModelProperty(value = "预报累计降雨")
    @TableField(value = "FACCP")
    private Double faccp;

    @ColumnWidth(12)
    @NumberFormat("#")
    @ExcelProperty(value = {"预报", "阈值(mm)"}, index = 10)
    @ApiModelProperty(value = "预报降雨阈值")
    @TableField(value = "FDRPT")
    private Integer fdrpt;

    @ColumnWidth(12)
    @NumberFormat("#")
    @ExcelProperty(value = {"预报", "历时(h)"}, index = 11)
    @ApiModelProperty(value = "预报降雨历时")
    @TableField(value = "FCDT")
    private Integer fcdt;

    @ColumnWidth(12)
    @NumberFormat("#.#")
    @ExcelProperty(value = {"预警后降雨(mm)", "30分钟"}, index = 12)
    @ApiModelProperty(value = "预警后30分钟降雨")
    private Double accp30;

    @ColumnWidth(12)
    @NumberFormat("#.#")
    @ExcelProperty(value = {"预警后降雨(mm)", "60分钟"}, index = 13)
    @ApiModelProperty(value = "预警后60分钟降雨")
    private Double accp60;

    @ColumnWidth(12)
    @NumberFormat("#.#")
    @ExcelProperty(value = {"预警后降雨(mm)", "90分钟"}, index = 14)
    @ApiModelProperty(value = "预警后90分钟降雨")
    private Double accp90;

    @ColumnWidth(12)
    @NumberFormat("#.#")
    @ExcelProperty(value = {"预警后降雨(mm)", "120分钟"}, index = 15)
    @ApiModelProperty(value = "预警后120分钟降雨")
    private Double accp120;

    /**
     * 分组键
     * <p>注：根据测站编码和预警时间生成分组键</p>
     *
     * @return 键
     */
    public String groupKey() {
        return StrFormatter.format("{}_{}", getStcd(), DateUtil.format(getWarnTime(), PURE_DATETIME_MS_PATTERN));
    }

    @SuppressWarnings({"rawtypes"})
    public static class StadtpConverter implements Converter<String> {

        @Override
        public Class supportJavaTypeKey() {
            return String.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public String convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            return null;
        }

        @Override
        public CellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            return new CellData(FusionDataFrom.get(value).getAlias());
        }
    }
}
