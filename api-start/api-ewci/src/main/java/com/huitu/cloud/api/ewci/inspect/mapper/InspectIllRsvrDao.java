package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectIllRsvr;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 病险水库
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectIllRsvrDao {

    /**
     * 判断是否添加过 病险水库
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectIllRsvr> getInspectIllRsvrList(@Param("adcd") String adcd, @Param("year") String year, @Param("resCode") String resCode);

    /**
     * 添加 病险水库
     *
     * @param entity
     * @return
     */
    int insertInspectIllRsvr(BnsInspectIllRsvr entity);

    /**
     * 修改 病险水库
     *
     * @param entity
     * @return
     */
    int updateInspectIllRsvr(BnsInspectIllRsvr entity);

    /**
     * 删除 病险水库
     *
     * @return
     */
    int deleteInspectIllRsvr(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

