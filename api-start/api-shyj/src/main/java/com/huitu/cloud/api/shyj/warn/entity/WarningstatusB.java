package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 预警状态表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("WARNINGSTATUS_B")
@ApiModel(value="WarningstatusB对象", description="预警状态表")
public class WarningstatusB extends Model<WarningstatusB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "预警状态ID")
    @TableId(value = "WARNSTATUSID", type = IdType.NONE)
    private Integer warnstatusid;

    @ApiModelProperty(value = "预警状态名称 ")
    @TableField("WARNSTATUSNM")
    private String warnstatusnm;

    @ApiModelProperty(value = "预警状态别名")
    @TableField("BYNAME")
    private String byname;

    @ApiModelProperty(value = " 预警状态")
    @TableField("SHORTNM")
    private String shortnm;


    public Integer getWarnstatusid() {
        return warnstatusid;
    }

    public void setWarnstatusid(Integer warnstatusid) {
        this.warnstatusid = warnstatusid;
    }

    public String getWarnstatusnm() {
        return warnstatusnm;
    }

    public void setWarnstatusnm(String warnstatusnm) {
        this.warnstatusnm = warnstatusnm;
    }

    public String getByname() {
        return byname;
    }

    public void setByname(String byname) {
        this.byname = byname;
    }

    public String getShortnm() {
        return shortnm;
    }

    public void setShortnm(String shortnm) {
        this.shortnm = shortnm;
    }

    @Override
    protected Serializable pkVal() {
        return this.warnstatusid;
    }

    @Override
    public String toString() {
        return "WarningstatusB{" +
        "warnstatusid=" + warnstatusid +
        ", warnstatusnm=" + warnstatusnm +
        ", byname=" + byname +
        ", shortnm=" + shortnm +
        "}";
    }
}
