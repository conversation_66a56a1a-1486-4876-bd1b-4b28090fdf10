<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.BnsWarnAccessSettingsDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="insert" parameterType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnAccessSettings">
        INSERT INTO BNS_WARN_ACCESS_SETTINGS (ID,CHANNEL_NAME, CHANNEL_LINK)
        VALUES (#{id},#{channelName}, #{channelLink})
    </insert>
    <update id="update">
        UPDATE BNS_WARN_ACCESS_SETTINGS
        <set>
            <if test="channelName != null and channelName !=''">
                CHANNEL_NAME = #{channelName},
            </if>
            <if test="channelLink != null and channelLink !=''">
                CHANNEL_LINK = #{channelLink},
            </if>
        </set>
        WHERE ID = #{id}
    </update>
    <delete id="delete">
        delete
        from BNS_WARN_ACCESS_SETTINGS
        where ID = #{id}
    </delete>
    <select id="getList" resultType="com.huitu.cloud.api.ewci.warn.entity.BnsWarnAccessSettings">
        SELECT ID,CHANNEL_NAME,CHANNEL_LINK,TS FROM BNS_WARN_ACCESS_SETTINGS
        ORDER BY TS DESC
    </select>
    <select id="getMaxId" resultType="java.lang.Long">
        SELECT (ISNULL(MAX(ID), 1000) + 1)  FROM BNS_WARN_ACCESS_SETTINGS
    </select>

</mapper>