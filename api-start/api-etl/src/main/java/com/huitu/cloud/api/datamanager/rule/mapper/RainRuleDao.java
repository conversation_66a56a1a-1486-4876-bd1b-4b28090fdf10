package com.huitu.cloud.api.datamanager.rule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.datamanager.rule.entity.BsnRainruleb;
import com.huitu.cloud.api.datamanager.rule.entity.RainRuleVo;
import com.huitu.cloud.api.datamanager.rule.entity.RainruleEx;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 雨量规则配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-06
 */
public interface RainRuleDao extends BaseMapper<BsnRainruleb> {
    /**
     *  分页查询雨量规则
     * @param page 分页类
     * @param stnm 测站查询
     * @return
     */
    IPage<RainRuleVo> getRainRule(Page page, @Param("stnm") String stnm, @Param("ad") String ad,@Param("level")int level);

    /**
     * 删除所有
     * @return
     */
    int deleteAll(@Param("ad") String ad,@Param("level")int level);

    /**
     * 一键设置所有站的雨量规则
     * @param BsnRainruleb
     * @return
     */
    int batchInsertAll(@Param("entrty") RainruleEx BsnRainruleb,@Param("level")int level);
}
