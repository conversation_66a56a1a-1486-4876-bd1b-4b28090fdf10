package com.huitu.cloud.api.syq.rain.remoting.model;

import com.alibaba.fastjson.annotation.JSONField;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 等值分析服务请求
 *
 */
public class IsoRequest {

    /**
     * 分析参数
     **/
    private Param param;

    /**
     * SHP文件
     **/
    @JSONField(name = "boundfile")
    private String boundFile;

    /**
     * 输入值
     **/
    private Input input;

    /**
     * 结果配置
     **/
    private Result result;

    public Param getParam() {
        if (null == param) {
            param = new Param();
        }
        return param;
    }

    public String getBoundFile() {
        if (StringUtils.isBlank(boundFile)) {
            boundFile = "220000000000000.shp";
        }
        return boundFile;
    }

    public void setBoundFile(String boundFile) {
        this.boundFile = boundFile;
    }

    public Input getInput() {
        if (null == input) {
            input = new Input();
        }
        return input;
    }

    public Result getResult() {
        if (null == result) {
            result = new Result(2, "geojson");
        }
        return result;
    }

    /**
     * 等值分析参数
     **/
    public static class Param {

        /**
         * 分级数组
         **/
        private List<Double> levels;
        public List<Double> getLevels() {
            if (null == levels) {
                levels = new ArrayList<>();
            }
            return levels;
        }

        public void setLevels(List<Double> levels) {
            this.levels = levels;
        }
    }

    /**
     * 输入值
     **/
    public static class Input {

        /**
         * 经纬度坐标数组，例如：[112,35, 113,34, 113,33]
         **/
        @JSONField(name = "point_coords")
        private List<Double> pointCoords;

        /**
         * 经纬度坐标值，例如：[1, 2, 3]
         **/
        @JSONField(name = "point_values")
        private List<Double> pointValues;

        public List<Double> getPointCoords() {
            if (null == pointCoords) {
                pointCoords = new ArrayList<>();
            }
            return pointCoords;
        }

        public void setPointCoords(List<Double> pointCoords) {
            this.pointCoords = pointCoords;
        }

        public List<Double> getPointValues() {
            if (null == pointValues) {
                pointValues = new ArrayList<>();
            }
            return pointValues;
        }

        public void setPointValues(List<Double> pointValues) {
            this.pointValues = pointValues;
        }
    }

    /**
     * 结果配置
     **/
    public static class Result {

        /**
         * 分析类型，1=等值线，2=等值面，10=栅格asc文件，20=栅格渲染图片
         **/
        private Integer type;
        /**
         * 响应格式
         **/
        private String format;

        public Result() {
        }

        public Result(Integer type, String format) {
            this.type = type;
            this.format = format;
        }

        public Integer getType() {
            return null == type ? 2 : type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getFormat() {
            return StringUtils.isBlank(format) ? "geojson" : format;
        }

        public void setFormat(String format) {
            this.format = format;
        }
    }
}