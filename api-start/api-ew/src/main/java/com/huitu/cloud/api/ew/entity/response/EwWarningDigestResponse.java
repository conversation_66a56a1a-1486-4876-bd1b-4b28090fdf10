package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 山洪预警摘要响应对象
 */
@ApiModel(value = "山洪预警摘要响应对象")
public class EwWarningDigestResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARN_ID")
    private String warnId;
    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;
    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;
    @ApiModelProperty(value = "预警时间")
    @TableField(value = "WARN_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warnTime;
    @ApiModelProperty(value = "预警名称")
    @TableField(value = "WARN_NAME")
    private String warnName;
    @ApiModelProperty(value = "预警描述")
    @TableField(value = "WARN_DESC")
    private String warnDesc;
    @ApiModelProperty(value = "平台ID")
    @TableField(value = "PLATFORM_ID")
    private String platformId;
    @ApiModelProperty(value = "平台名称")
    @TableField(value = "PLATFORM_NAME")
    private String platformName;

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Date getWarnTime() {
        return warnTime;
    }

    public void setWarnTime(Date warnTime) {
        this.warnTime = warnTime;
    }

    public String getWarnName() {
        return warnName;
    }

    public void setWarnName(String warnName) {
        this.warnName = warnName;
    }

    public String getWarnDesc() {
        return warnDesc;
    }

    public void setWarnDesc(String warnDesc) {
        this.warnDesc = warnDesc;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }
}
