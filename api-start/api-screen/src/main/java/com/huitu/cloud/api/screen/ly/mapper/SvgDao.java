package com.huitu.cloud.api.screen.ly.mapper;


import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
public interface SvgDao {

    List<Map<String, Object>> queryRsvrInfo(@Param("resCodeList") List<String> resCodeList);

    List<Map<String, Object>> queryRiverInfo(@Param("resCodeList") List<String> resCodeList);

    List<Map<String, Object>> queryFloodControlList(@Param("rvCode") String rvCode, @Param("type") String type);

    List<Map<String, Object>> queryDikeInfo(@Param("resCodeList") List<String> resCodeList);
}
