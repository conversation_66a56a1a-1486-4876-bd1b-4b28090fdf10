package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectWarnBroad;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 水闸工程运行
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectWarnBroadDao {


    /**
     * 添加 无线预警广播站
     *
     * @param entity
     * @return
     */
    int insertInspectWarnBroad(BnsInspectWarnBroad entity);

    /**
     * 修改 无线预警广播站
     *
     * @param entity
     * @return
     */
    int updateInspectWarnBroad(BnsInspectWarnBroad entity);

    /**
     * 删除 无线预警广播站
     *
     * @return
     */
    int deleteInspectWarnBroad(@Param("inspectId") String inspectId);

    List<BnsInspectWarnBroad> getInspectWarnBroadList(@Param("year") String year, @Param("tadcd") String tadcd);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

