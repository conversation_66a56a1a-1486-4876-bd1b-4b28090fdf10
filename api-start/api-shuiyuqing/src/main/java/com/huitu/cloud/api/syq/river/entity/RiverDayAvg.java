package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
/**
 * <AUTHOR>
 * @since 2019-09-11
 */
public class RiverDayAvg implements Serializable {
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    private String rvnm;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "平均水位")
    private BigDecimal avz;

    @ApiModelProperty(value = "平均流量")
    private BigDecimal avq;

    @ApiModelProperty(value = "标志时间")
    private LocalDateTime idtm;
    @ApiModelProperty(value = "县级政区名称")
    @TableField("XADNM")
    private String xadnm;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public BigDecimal getAvz() {
        return avz;
    }

    public void setAvz(BigDecimal avz) {
        this.avz = avz;
    }

    public BigDecimal getAvq() {
        return avq;
    }

    public void setAvq(BigDecimal avq) {
        this.avq = avq;
    }

    public LocalDateTime getIdtm() {
        return idtm;
    }

    public void setIdtm(LocalDateTime idtm) {
        this.idtm = idtm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }
}
