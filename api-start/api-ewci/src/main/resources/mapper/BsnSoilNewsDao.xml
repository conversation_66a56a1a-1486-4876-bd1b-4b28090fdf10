<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.soilnews.mapper.BsnSoilNewsBDao">
    <select id="getSoilNewsList" resultType="com.huitu.cloud.api.ewci.soilnews.entity.BsnSoilNewsB">
        SELECT FILENM, FILEURL, PERIODS, TS FROM BSN_SOILNEWS_B
        <where>
            <if test="map.periods!= null and map.periods !=''">
                and CHARINDEX(#{map.periods},CONVERT(varchar(10), PERIODS))>0
            </if>
        </where>
        ORDER BY PERIODS DESC
    </select>
</mapper>
