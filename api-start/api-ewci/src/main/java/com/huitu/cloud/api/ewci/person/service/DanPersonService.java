package com.huitu.cloud.api.ewci.person.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

/**
 * 危险区居民管理服务
 *
 * <AUTHOR>
 */
public interface DanPersonService {

    /**
     * 分页获取危险区居民信息列表
     *
     * @param query 查询条件
     * @return 分页后的信息列表
     **/
    IPage<DanPerson> getPageList(DanPersonQuery query);

    /**
     * 危险区居民汇总统计
     *
     * @param query
     * @return
     */
    List<DanSummaryVo> getDanSummaryList(DanSummaryQuery query);

    /**
     * 数据导出
     *
     * @param query  查询条件
     * @param output 输出流
     **/
    void dataExport(DanPersonQuery query, OutputStream output);

    /**
     * 数据导入
     *
     * @param xadcd 县级行政区划代码
     * @param input 输入流
     * @return 导入结果
     **/
    ImportResult<BnsDanpersonTmp> dataImport(String xadcd, InputStream input);

    /**
     * 编辑危险区居民
     *
     * @param entity
     * @return
     */
    int danPersonEdit(DanPerson entity);
}
