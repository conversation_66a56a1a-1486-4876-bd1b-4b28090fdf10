package com.huitu.cloud.api.ewci.person.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.person.entity.FsdaPersonB;
import com.huitu.cloud.api.ewci.person.entity.FsdaPersonBVo;
import com.huitu.cloud.api.ewci.person.mapper.FsdaPersonDao;
import com.huitu.cloud.api.ewci.person.service.FsdaPersonService;
import com.huitu.cloud.entity.PageBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 蓄滞洪区责任人 实现类
 *
 * <AUTHOR>
 */
@Service
public class FsdaPersonServiceImpl implements FsdaPersonService {

    private FsdaPersonDao baseDao;

    public FsdaPersonServiceImpl(FsdaPersonDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public IPage<FsdaPersonBVo> getPageList(PageBean pageBean) {
        Page page = new Page<>(pageBean.getPageNum(), pageBean.getPageSize());
        return baseDao.getPageList(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int fsdaPersonEdit(FsdaPersonBVo entity) {
        List<FsdaPersonB> list = new ArrayList<>();
        // 市级防汛行政责任人
        if (StringUtils.isNotBlank(entity.getSjRealnm()) && StringUtils.isNotBlank(entity.getSjMobile())) {
            FsdaPersonB sjPerson = new FsdaPersonB();
            sjPerson.setFsdaCode(entity.getFsdaCode());
            sjPerson.setAdcd(entity.getAdcd());
            sjPerson.setPertp("1");
            sjPerson.setSno(entity.getSno());
            sjPerson.setRealnm(entity.getSjRealnm());
            sjPerson.setPost(entity.getSjPost());
            sjPerson.setMobile(entity.getSjMobile());
            sjPerson.setTs(LocalDateTime.now());
            list.add(sjPerson);
        }
        // 县级防汛行政责任人
        if (StringUtils.isNotBlank(entity.getXjRealnm()) && StringUtils.isNotBlank(entity.getXjMobile())) {
            FsdaPersonB xjPerson = new FsdaPersonB();
            xjPerson.setFsdaCode(entity.getFsdaCode());
            xjPerson.setAdcd(entity.getAdcd());
            xjPerson.setPertp("2");
            xjPerson.setSno(entity.getSno());
            xjPerson.setRealnm(entity.getXjRealnm());
            xjPerson.setPost(entity.getXjPost());
            xjPerson.setMobile(entity.getXjMobile());
            xjPerson.setTs(LocalDateTime.now());
            list.add(xjPerson);
        }
        // 乡级防汛行政责任人
        if (StringUtils.isNotBlank(entity.getXzjRealnm()) && StringUtils.isNotBlank(entity.getXzjMobile())) {
            FsdaPersonB xzjPerson = new FsdaPersonB();
            xzjPerson.setFsdaCode(entity.getFsdaCode());
            xzjPerson.setAdcd(entity.getAdcd());
            xzjPerson.setPertp("3");
            xzjPerson.setSno(entity.getSno());
            xzjPerson.setRealnm(entity.getXzjRealnm());
            xzjPerson.setPost(entity.getXzjPost());
            xzjPerson.setMobile(entity.getXzjMobile());
            xzjPerson.setTs(LocalDateTime.now());
            list.add(xzjPerson);
        }

        Map<String, Object> param = new HashMap<>();
        param.put("fsdaCode", entity.getFsdaCode());
        param.put("adcd", entity.getAdcd());
        param.put("sno", entity.getSno());
        int insertCode = 0;
        int delCode = baseDao.delByFsdaCodeAndAdcdAndSno(param);
        if (delCode > 0) {
            insertCode = baseDao.insertList(list);
        }
        return insertCode;
    }
}
