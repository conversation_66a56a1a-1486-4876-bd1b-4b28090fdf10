package com.huitu.cloud.api.fusion.entity.ext;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 监测预警汇总统计
 *
 * <AUTHOR>
 */
public class FusionWarningSummary implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 预警次数
     */
    @TableField(value = "WCOUNT")
    private int wcount;
    /**
     * 预警人数
     */
    @TableField(value = "RCOUNT")
    private int rcount;
    /**
     * 预警人次
     */
    @TableField(value = "PCOUNT")
    private int pcount;
    /**
     * 反馈人次
     */
    @TableField(value = "FCOUNT")
    private int fcount;

    public int getWcount() {
        return wcount;
    }

    public void setWcount(int wcount) {
        this.wcount = wcount;
    }

    public int getRcount() {
        return rcount;
    }

    public void setRcount(int rcount) {
        this.rcount = rcount;
    }

    public int getPcount() {
        return pcount;
    }

    public void setPcount(int pcount) {
        this.pcount = pcount;
    }

    public int getFcount() {
        return fcount;
    }

    public void setFcount(int fcount) {
        this.fcount = fcount;
    }
}
