package com.huitu.cloud.api.hsfxt.zxhl.service.impl;

import com.huitu.cloud.api.hsfxt.city.entity.DicHongshuiliangji;
import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqXiangQing;
import com.huitu.cloud.api.hsfxt.zxhl.entity.*;
import com.huitu.cloud.api.hsfxt.zxhl.mapper.ZxhlDao;
import com.huitu.cloud.api.hsfxt.zxhl.service.ZxhlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.apache.commons.lang.StringUtils.lastIndexOf;

/**
 * <p>
 * 风险图-中小河流 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
@Service
public class ZxhlServiceImpl implements ZxhlService {
    @Autowired
    ZxhlDao zxhlDao;

    @Override
    public List<ZxhlTree> getZxhlTreeInfo(String key, String name, String hl, String bzqid) {
        List<ZxhlTree> result=new ArrayList<>();
        Map<String,Object> param=new HashMap<>();
        param.put("level",6);
        //查询县集合
        List<ZxhlTree> xianList=zxhlDao.getZxhlTreeInfo(param);
        param.put("level",9);
        //查询乡镇集合
        List<ZxhlTree> zhenList=zxhlDao.getZxhlTreeInfo(param);
        //查询屯集合
        Map<String,Object> newParam=new HashMap<>();
        newParam.put("hl",hl);
        newParam.put("bzqid",bzqid);
        DicHongshuiliangji hongshuiliangji=zxhlDao.getZxhlParam(newParam);
        if(hongshuiliangji != null){
            Map<String,Object> newParam2=new HashMap<>();
            newParam2.put("param1","DDSJ_"+hongshuiliangji.getBeizhu());
            newParam2.put("param2","HSLS_"+hongshuiliangji.getBeizhu());
            newParam2.put("param3","YMSS_"+hongshuiliangji.getBeizhu());
            //修改in需要的参数
            String key2=getParam(key);
            newParam2.put("key",key2);
            newParam2.put("name",name);
            List<ZxhlTree> tunList=zxhlDao.getTunZxhlTreeInfo(newParam2);
            //转移路线
            List<ZxhlZhuanYiFangAn> list=zxhlDao.getZxhlZhuanYiFangAn(null,bzqid);
            Map<String,List<ZxhlZhuanYiFangAn>> map=new HashMap<>();
            list.forEach(item->{
                if(map.containsKey(item.getZhuanyidanyuanbianma())){
                    List<ZxhlZhuanYiFangAn> list2=map.get(item.getZhuanyidanyuanbianma());
                    list2.add(item);
                }else{
                    List<ZxhlZhuanYiFangAn> list2=new ArrayList<>();
                    list2.add(item);
                    map.put(item.getZhuanyidanyuanbianma(),list2);
                }
            });
            tunList.forEach(item->{
                if (map.containsKey(item.getAdcd())){
                    item.setZyfa(map.get(item.getAdcd()));
                }
            });
            //三级集合合并
            List<ZxhlTree> AllList=new ArrayList<>();
            //根据屯取乡镇和区县
            Map<String,ZxhlTree> xianMap=new HashMap<>();
            Map<String,ZxhlTree> zhenMap=new HashMap<>();
            xianList.forEach(item->{
                xianMap.put(item.getAdcd(),item);
            });
            zhenList.forEach(item->{
                zhenMap.put(item.getAdcd(),item);
            });
            List<ZxhlTree> xianList2=new ArrayList<>();
            List<ZxhlTree> zhenList2=new ArrayList<>();
            tunList.forEach(item->{
                if (zhenMap.containsKey(item.getPadcd())){
                    ZxhlTree zhen=zhenMap.get(item.getPadcd());
                    //业务逻辑需要
                    zhen.setKeyindex(item.getKeyindex()+"_"+hl);
                    zhenList2.add(zhen);
                }
            });
            List<ZxhlTree> zhenList3 =zhenList2.stream().distinct().collect(Collectors.toList());
            zhenList3.forEach(item->{
                if(xianMap.containsKey(item.getPadcd())){
                    ZxhlTree xian=xianMap.get(item.getPadcd());
                    xian.setKeyindex(item.getKeyindex());
                    xianList2.add(xian);
                }
            });
            List<ZxhlTree> xianList3=xianList2.stream().distinct().collect(Collectors.toList());
            AllList.addAll(zhenList3);
            AllList.addAll(xianList3);
            AllList.addAll(tunList);
            //查询子节点集合并赋值
            AllList.forEach(item->{
                List<ZxhlTree> childList=getZxhlChildren(AllList,item.getAdcd());
                item.setChildren(childList);
                //乡镇加入影响屯总数
                if (!"000".equals(item.getAdcd().substring(7,9))){
                    item.setChildrenCount(childList.size());
                }
            });
            //获取根节点
            result=AllList.stream().filter(item->"0".equals(item.getPadcd())).collect(Collectors.toList());
            //加入根节点影响屯总数
            result.forEach(item->{
                int count=0;
                for(ZxhlTree item2:item.getChildren()){
                    count=count+item2.getChildren().size();
                }
                item.setChildrenCount(count);
            });
        }
        return result;
    }

    @Override
    public List<ZxhlShehuijingji> getZxhlShjjTreeInfo(String key) {
        List<ZxhlShehuijingji> result=new ArrayList<>();
        //修改in需要的参数
        String key2=getParam(key);
        List<ZxhlShehuijingji> list= zxhlDao.getZxhlShjjTreeInfo(key2);
        //组织树形结构
        list.forEach(item->{
            List<ZxhlShehuijingji> childList=getZxhlShjjChildren(list,item.getSortid().toString());
            item.setChildren(childList);
        });
        result =list.stream().filter(item->"0".equals(item.getPid())).collect(Collectors.toList());
        return result;
    }

    @Override
    public ZxhlXiangQing getZxhlShjjDetailInfo(String key, String adcd, String bzqid) {
        ZxhlXiangQing result=new ZxhlXiangQing();
        ZxhlXiangQing zxhlXiangQing=zxhlDao.getXianQuXiangZhen(adcd,bzqid);
        if(zxhlXiangQing!=null){
            result.setAdcd(zxhlXiangQing.getAdcd());
            result.setAdnm(zxhlXiangQing.getAdnm());
            result.setMianji(zxhlXiangQing.getMianji());
            result.setFushu(zxhlXiangQing.getFushu());
            result.setRenkou(zxhlXiangQing.getRenkou());
            result.setFangwujianshu(zxhlXiangQing.getFangwujianshu());
            result.setGendimianji(zxhlXiangQing.getGendimianji());
            result.setGdp(zxhlXiangQing.getGdp());
        }
        ZxhlXiangQing zxhlXiangQing2=zxhlDao.getZaiHaiSunShi(adcd,key);
        if(zxhlXiangQing2!=null){
            result.setYanmomianji(zxhlXiangQing2.getYanmomianji());
            result.setYanmogendimianji(zxhlXiangQing2.getYanmogendimianji());
            result.setYinxiangrenkou(zxhlXiangQing2.getYinxiangrenkou());
            result.setYinxianggdp(zxhlXiangQing2.getYinxianggdp());
            result.setYinxiangfanwushu(zxhlXiangQing2.getYinxiangfanwushu());
            result.setHongzaisunshi(zxhlXiangQing2.getHongzaisunshi());
        }
        return result;
    }

    @Override
    public ZxhlTunXiangqing getZxhlTunShjjDetailInfo(String key, String hl, String adcd, String bzqid) {
        ZxhlTunXiangqing result=null;
        Map<String,Object> newParam=new HashMap<>();
        newParam.put("hl",hl);
        newParam.put("bzqid",bzqid);
        DicHongshuiliangji hongshuiliangji=zxhlDao.getZxhlParam(newParam);
        if(hongshuiliangji != null){
            Map<String,Object> newParam2=new HashMap<>();
            newParam2.put("param1","DDSJ_"+hongshuiliangji.getBeizhu());
            newParam2.put("param2","HSLS_"+hongshuiliangji.getBeizhu());
            newParam2.put("param3","YMSS_"+hongshuiliangji.getBeizhu());
            newParam2.put("key",key);
            newParam2.put("adcd",adcd);
            result=zxhlDao.getZxhlTunShjjDetailInfo(newParam2);
            List<ZxhlZhuanYiFangAn> list=zxhlDao.getZxhlZhuanYiFangAn(adcd,bzqid);
            if (result!=null){
                result.setZyfa(list);
            }
        }
        return result;
    }

    private List<ZxhlTree> getZxhlChildren(List<ZxhlTree> list, String pid) {
        // 通过父级编码子类
        List<ZxhlTree> childList = list.stream().filter(x -> x.getPadcd().equals(pid)).collect(Collectors.toList());
        return childList;
    }

    private List<ZxhlShehuijingji> getZxhlShjjChildren(List<ZxhlShehuijingji> list, String pid) {
        // 通过父级编码子类
        List<ZxhlShehuijingji> childList = list.stream().filter(x -> x.getPid().equals(pid)).collect(Collectors.toList());
        return childList;
    }

    /**
     * 批量查询时传入字符串转换为sql底层in所需的条件转换
     *
     * @param str 传入字符串 例（1,2）
     * @return
     */
    private String getParam(String str){
        String[] s=str.split(",");
        String newStr="";
        for (int i=0;i<s.length;i++){
            newStr=newStr+"'"+s[i]+"',";
        }
        return newStr.substring(0,newStr.length()-1);
    }
}
