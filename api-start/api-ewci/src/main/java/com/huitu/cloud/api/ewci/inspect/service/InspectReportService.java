package com.huitu.cloud.api.ewci.inspect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.inspect.entity.*;
import com.huitu.cloud.api.usif.ad.entity.BsnAdcdB;

import java.util.List;

/**
 * 检查上报
 *
 * <AUTHOR>
 */
public interface InspectReportService {

    /**
     * 获取检查类型清单
     *
     * @return
     */
    List<BnsInspectTypeInfo> getBnsInspectTypeInfoList();

    /**
     * 检查重点清单
     *
     * @param query
     * @return
     */
    List<BnsInspectPointInfo> getBnsInspectPointInfoList(BnsInspectReportQuery query);

    /**
     * 检查记录统计列表
     *
     * @param query
     * @return
     */
    List<BnsInspectRecords> getBnsInspectRecordsList(BnsInspectRecordsQuery query);

    /**
     * 检查记录列表
     *
     * @param query
     * @return
     */
    IPage<BnsInspectRecordsChild> getBnsInspectRecordsChildList(BnsInspectRecordsQuery query);

    /**
     * 水库(水电站)运行水库下拉
     *
     * @param query
     * @return
     */
    List<BnsInspectRsvr> getRsvrRunList(BnsInspectRsvrQuery query);

    /**
     * 病险水库下拉
     *
     * @param query
     * @return
     */
    List<BnsInspectRsvr> getIllRsvrList(BnsInspectRsvrQuery query);

    /**
     * 在建工程下拉
     *
     * @param query
     * @return
     */
    List<BnsEngB> getConstructEngList(BnsEngBQuery query);

    /**
     * 水毁工程修复建设下拉
     *
     * @param query
     * @return
     */
    List<BnsEngB> getShgcConstructList(BnsEngBQuery query);

    /**
     * 安全度汛工程建设下拉
     *
     * @param query
     * @return
     */
    List<BnsEngB> getAqdxConstructList(BnsEngBQuery query);

    /**
     * 自动监测站下拉
     *
     * @param query
     * @return
     */
    List<BnsAutoMonitorB> getAutoMonitorList(BnsAutoMonitorQuery query);

    /**
     * 添加责任人落实
     *
     * @param entity
     * @return
     */
    int insertInspectPersonImp(BnsInspectPersonImp entity);

    /**
     * 添加方案/预案修订
     *
     * @param entity
     * @return
     */
    int insertInspectPlanRevise(BnsInspectPlanRevise entity);

    /**
     * 添加水库(水电站)运行
     *
     * @param entity
     * @return
     */
    int insertInspectRsvrRun(BnsInspectRsvrRun entity);

    /**
     * 添加 病险水库
     *
     * @param entity
     * @return
     */
    int insertInspectIllRsvr(BnsInspectIllRsvr entity);

    /**
     * 添加 河道及堤防名称
     *
     * @param entity
     * @return
     */
    int insertInspectRiverDike(BnsInspectRiverDike entity);

    /**
     * 添加 水闸工程运行
     *
     * @param entity
     * @return
     */
    int insertInspectWagaRun(BnsInspectWagaRun entity);

    /**
     * 添加 在建工程
     *
     * @param entity
     * @return
     */
    int insertInspectConEn(BnsInspectConEn entity);

    /**
     * 添加 自动监测站
     *
     * @param entity
     * @return
     */
    int insertInspectAutoMonitor(BnsInspectAutoMonitor entity);

    /**
     * 添加 简易雨量站
     *
     * @param entity
     * @return
     */
    int insertInspectSimpleRain(BnsInspectSimpleRain entity);

    /**
     * 添加 无线预警广播站
     *
     * @param entity
     * @return
     */
    int insertInspectWarnBroad(BnsInspectWarnBroad entity);

    /**
     * 添加 县级山洪灾害防御平台
     *
     * @param entity
     * @return
     */
    int insertInspectCountyPlatform(BnsInspectCountyPlatform entity);

    /**
     * 添加 运行维护经费
     *
     * @param entity
     * @return
     */
    int insertInspectMainExp(BnsInspectMainExp entity);

    /**
     * 添加 水毁工程修复建设
     *
     * @param entity
     * @return
     */
    int insertInspectShgcCons(BnsInspectShgcCons entity);

    /**
     * 添加 安全度汛工程建设
     *
     * @param entity
     * @return
     */
    int insertInspectAqdxCons(BnsInspectAqdxCons entity);

    /**
     * 修改 责任人落实
     *
     * @param entity
     * @return
     */
    int updateInspectPersonImp(BnsInspectPersonImp entity);

    /**
     * 修改方案/预案修订
     *
     * @param entity
     * @return
     */
    int updateInspectPlanRevise(BnsInspectPlanRevise entity);

    /**
     * 修改 水库(水电站)运行
     *
     * @param entity
     * @return
     */
    int updateInspectRsvrRun(BnsInspectRsvrRun entity);

    /**
     * 修改 病险水库
     *
     * @param entity
     * @return
     */
    int updateInspectIllRsvr(BnsInspectIllRsvr entity);

    /**
     * 修改  河道及堤防名称
     *
     * @param entity
     * @return
     */
    int updateInspectRiverDike(BnsInspectRiverDike entity);

    /**
     * 修改 在建工程
     *
     * @param entity
     * @return
     */
    int updateInspectConEn(BnsInspectConEn entity);

    /**
     * 修改 水闸工程运行
     *
     * @param entity
     * @return
     */
    int updateInspectWagaRun(BnsInspectWagaRun entity);

    /**
     * 修改 自动监测站
     *
     * @param entity
     * @return
     */
    int updateInspectAutoMonitor(BnsInspectAutoMonitor entity);

    /**
     * 修改  简易雨量站
     *
     * @param entity
     * @return
     */
    int updateInspectSimpleRain(BnsInspectSimpleRain entity);

    /**
     * 修改 无线预警广播站
     *
     * @param entity
     * @return
     */
    int updateInspectWarnBroad(BnsInspectWarnBroad entity);

    /**
     * 修改 县级山洪灾害防御平台
     *
     * @param entity
     * @return
     */
    int updateInspectCountyPlatform(BnsInspectCountyPlatform entity);

    /**
     * 修改 运行维护经费
     *
     * @param entity
     * @return
     */
    int updateInspectMainExp(BnsInspectMainExp entity);

    /**
     * 修改 水毁工程修复建设
     *
     * @param entity
     * @return
     */
    int updateInspectShgcCons(BnsInspectShgcCons entity);

    /**
     * 修改 安全度汛工程建设
     *
     * @param entity
     * @return
     */
    int updateInspectAqdxCons(BnsInspectAqdxCons entity);

    /**
     * 删除 责任人落实
     *
     * @return
     */
    int deleteInspectPersonImp(BnsInspectPersonImp entity);


    /**
     * 删除 方案/预案修订
     *
     * @return
     */
    int deleteInspectPlanRevise(BnsInspectPlanRevise entity);

    /**
     * 删除 水库(水电站)运行
     *
     * @return
     */
    int deleteInspectRsvrRun(BnsInspectRsvrRun entity);

    /**
     * 删除 病险水库
     *
     * @return
     */
    int deleteInspectIllRsvr(BnsInspectIllRsvr entity);

    /**
     * 删除 河道及堤防名称
     *
     * @return
     */
    int deleteInspectRiverDike(BnsInspectRiverDike entity);

    /**
     * 删除 水闸工程运行
     *
     * @return
     */
    int deleteInspectWagaRun(BnsInspectWagaRun entity);

    /**
     * 删除 在建工程
     *
     * @return
     */
    int deleteInspectConEn(BnsInspectConEn entity);

    /**
     * 删除 自动监测站
     *
     * @return
     */
    int deleteInspectAutoMonitor(BnsInspectAutoMonitor entity);

    /**
     * 删除 简易雨量站
     *
     * @return
     */
    int deleteInspectSimpleRain(BnsInspectSimpleRain entity);

    /**
     * 删除 无线预警广播站
     *
     * @return
     */
    int deleteInspectWarnBroad(BnsInspectWarnBroad entity);

    /**
     * 删除 县级山洪灾害防御平台
     *
     * @return
     */
    int deleteInspectCountyPlatform(BnsInspectCountyPlatform entity);

    /**
     * 删除 运行维护经费
     *
     * @return
     */
    int deleteInspectMainExp(BnsInspectMainExp entity);

    /**
     * 删除 水毁工程修复建设
     *
     * @return
     */
    int deleteInspectShgcCons(BnsInspectShgcCons entity);

    /**
     * 删除 安全度汛工程建设
     *
     * @return
     */
    int deleteInspectAqdxCons(BnsInspectAqdxCons entity);

    /**
     * 查询政区二级树(向下探2级)
     *
     * @param adcd
     * @return
     */
    BsnAdcdB getAdTree(String adcd);

    /**
     * 根据 adcd 递归查询 adnm
     *
     * @param adcd
     * @return
     */
    String getAdnmByAdcd(String adcd);

    /**
     * 获取检查记录详情
     *
     * @param query
     * @return
     */
    <T> T getBnsInspectRecordInfo(BnsInspectRecordInfoQuery query);

    /**
     * 获取文件列表，根据业务ID
     *
     * @param businessKey
     * @return
     */
    List<BnsInspectFile> getFile(String businessKey);

    Integer selectReportCount(BnsInspectReportAllQuery entity);

    Integer insertReport(BnsInspectReportAllQuery entity);

    IPage<BnsInspectReportsChild> getBnsInspectReportsChildList(BnsInspectReportsQuery query);

    Integer submitInspectReport(BnsInspectSubmitReports entity);

    Integer deleteInspectReport(String reportId);

    BnsInspectReportsChild getInspectReportById(String reportId) throws Exception;

    int rebuildReportDocument(String reportId);

    void rebuildReportDocumentFileAsync(String reportId);

    int deleteInspectEmgCommDev(BnsInspectEmgCommDev entity);

    int updateInspectEmgCommDev(BnsInspectEmgCommDev entity);

    int insertInspectEmgCommDev(BnsInspectEmgCommDev entity);
}
