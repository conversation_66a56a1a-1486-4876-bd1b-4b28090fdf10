package com.huitu.cloud.api.syq.rain.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
@ApiModel
public class OneDayRainQo extends PageBean {
    @ApiModelProperty(value = "日期 格式yyyy-MM-dd",required = true)
    private String date;
    @ApiModelProperty(value = "测站归属类型 1、水文，2、山洪  3、气象 ")
    private String stType;
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "管理单位")
    private String admauth;
    @ApiModelProperty(value = "测站名称")
    private String stnm;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getStType() {
        return stType;
    }

    public void setStType(String stType) {
        this.stType = stType;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdmauth() {
        return admauth;
    }

    public void setAdmauth(String admauth) {
        this.admauth = admauth;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }
}
