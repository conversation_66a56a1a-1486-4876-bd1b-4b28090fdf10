### 1.列用户存储空间
    GET http://localhost:8080/api/oss/minio/buckets
#### 1.1 Path参数
#### 1.2 Query参数
#### 1.3 Body参数
#### 1.4 Header参数
#### 1.5 请求响应
```json
{
    "link": {
        "uri": "/api/oss/minio/buckets",
        "uuid": "63fa1462-dcd7-4b99-8689-9006528b265e",
        "version": "1.0"
    },
    "code": 200,
    "message": "ok",
    "data": [
        {
            "name": "app-alarm",
            "createdTime": "2019-08-28T13:15:49.338+0000"
        }
    ]
}
```

### 2. 创建用户存储空间
    PUT http://localhost:8080/api/oss/minio/buckets

#### 2.1 Path参数
#### 2.2 Query参数
#### 2.3 Body参数
* name 用户存储空间名称
#### 2.4 Header参数
#### 2.5 请求响应
```json
{
    "link": {
        "uri": "/api/oss/minio/buckets",
        "uuid": "928d2cbb-d22f-4001-b88f-96280f93ec0c",
        "version": "1.0"
    },
    "code": 200,
    "message": "ok",
    "data": "success"
}
```

### 3. 删除用户存储空间
    DELETE http://localhost:8080/api/oss/minio/buckets

#### 3.1 Path参数
#### 3.2 Query参数
#### 3.3 Body参数
* name 用户存储空间名称
#### 3.4 Header参数
#### 3.5 请求响应
```json
{
    "link": {
        "uri": "/api/oss/minio/buckets",
        "uuid": "a38da935-d547-4291-b501-48ab925a63f3",
        "version": "1.0"
    },
    "code": 200,
    "message": "ok",
    "data": "success"
}
```

### 4. 列用户存储空间内容
     GET http://localhost:8080/api/oss/minio/{bucket-name}/objects

#### 4.1 Path参数
* bucket-name 用户存储空间名称
#### 4.2 Query参数
* path 可选，string类型，默认值为""

    目录路径
* recursive 可选，boolean类型，默认值为false

    是否递归，查询所有的目录级别
#### 4.3 Body参数
#### 4.4 Header参数

#### 4.5 请求响应
```json
{
    "link": {
        "uri": "/api/oss/minio/app-alarm/objects",
        "uuid": "505e3d2d-8380-4c9d-a3fb-e0028bc5aae0",
        "version": "1.0"
    },
    "code": 200,
    "message": "ok",
    "data": [
        {
            "objectName": "abc/接收技术框架.xsd",
            "lastModified": "2019-08-28T13:15:08.862+0000",
            "etag": "4a1d6950eaf4aa175ff5f3b5eec855df",
            "size": 7086,
            "storageClass": "STANDARD",
            "owner": {},
            "dir": false
        },
        {
            "objectName": "abc/aba/",
            "size": 0,
            "dir": true
        }
    ]
}
````

### 5.显示文档信息
    GET http://localhost:8080/api/oss/minio/{bucket-name}/objects/info?filename={filename}
#### 5.1 Path参数
* bucket-name 用户存储空间名称
#### 1.2 Query参数
* filename string类型

    文档名称

#### 5.3 Body参数
#### 5.4 Header参数
#### 5.5 请求响应
```json
{
    "link": {
        "uri": "/api/oss/minio/app-alarm/objects/info",
        "uuid": "154e7750-c6a7-4233-a864-d58fd46d3ffe",
        "version": "1.0"
    },
    "code": 200,
    "message": "ok",
    "data": {
        "bucketName": "app-alarm",
        "name": "大理成书.docx",
        "lastModified": "2019-03-21T06:58:02.000+0000",
        "length": 1107478,
        "etag": "00000000000000000000000000000000-1",
        "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    }
}
````


### 6. 下载文档
    GET http://localhost:8080/api/oss/minio/{bucket-name}/objects/file?filename={filename}

#### 6.1 Path参数
* bucket-name 用户存储空间名称
#### 6.2 Query参数
* filename string类型

    文档名称
#### 6.3 Body参数
#### 6.4 Header参数
#### 6.5 请求响应
```
下载的文件
````

### 6. 上传文档
    PUT http://localhost:8080/api/oss/minio/{bucket-name}/objects?path={path}

#### 6.1 Path参数
* bucket-name 用户存储空间名称
#### 6.2 Query参数
* path string类型

    目录路径
#### 6.3 Body参数
* data File类型

    上传的文档
#### 6.4 Header参数
#### 6.5 请求响应
```
{
    "link": {
        "uri": "/api/oss/minio/app-alarm/objects",
        "uuid": "3714de02-96a0-44aa-af2a-da96c754156b",
        "version": "1.0"
    },
    "code": 200,
    "message": "ok",
    "data": "success"
}
````

