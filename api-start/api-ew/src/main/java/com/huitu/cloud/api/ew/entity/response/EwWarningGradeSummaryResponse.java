package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 水库/河道预警等级统计
 */
@ApiModel(value = "水库/河道预警等级统计")
public class EwWarningGradeSummaryResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "一级")
    @TableField(value = "one")
    private Integer one;

    @ApiModelProperty(value = "二级")
    @TableField(value = "two")
    private Integer two;

    @ApiModelProperty(value = "三级")
    @TableField(value = "three")
    private Integer three;

    @ApiModelProperty(value = "四级")
    @TableField(value = "four")
    private Integer four;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getOne() {
        return one;
    }

    public void setOne(Integer one) {
        this.one = one;
    }

    public Integer getTwo() {
        return two;
    }

    public void setTwo(Integer two) {
        this.two = two;
    }

    public Integer getThree() {
        return three;
    }

    public void setThree(Integer three) {
        this.three = three;
    }

    public Integer getFour() {
        return four;
    }

    public void setFour(Integer four) {
        this.four = four;
    }
}
