package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2019-09-09
 */
@ApiModel(value = "水库综合信息", description = "水库水情表")
public class StRsvrVo extends StRsvrR {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水库类型")
    @TableField("RSVRTP")
    private String rsvrtp;

    @ApiModelProperty(value = "坝顶高程")
    @TableField("DAMEL")
    private BigDecimal damel;

    @ApiModelProperty(value = "校核洪水位")
    @TableField("CKFLZ")
    private BigDecimal ckflz;

    @ApiModelProperty(value = "设计洪水位")
    @TableField("DSFLZ")
    private BigDecimal dsflz;

    @ApiModelProperty(value = "正常高水位")
    @TableField("NORMZ")
    private BigDecimal normz;

    @ApiModelProperty(value = "死水位")
    @TableField("DDZ")
    private BigDecimal ddz;

    @ApiModelProperty(value = "兴利水位")
    @TableField("ACTZ")
    private BigDecimal actz;

    @ApiModelProperty(value = "总库容")
    @TableField("TTCP")
    private BigDecimal ttcp;

    @ApiModelProperty(value = "设计库容")
    @TableField("DSCP")
    private BigDecimal dscp;

    @ApiModelProperty(value = "防洪库容")
    @TableField("FLDCP")
    private BigDecimal fldcp;

    @ApiModelProperty(value = "兴利库容")
    @TableField("ACTCP")
    private BigDecimal actcp;

    @ApiModelProperty(value = "超讯限水位")
    @TableField("RZFSLTDZ")
    private BigDecimal rzfsltdz;

    @ApiModelProperty(value = "超讯限库容")
    @TableField("WFSLTDW")
    private BigDecimal wfsltdw;

    @ApiModelProperty(value = "超正常高水位")
    @TableField("RZNORMZ")
    private BigDecimal rznormz;

    @ApiModelProperty(value = "超兴利水位（实际为超正常高水位）")
    @TableField("RZACTZ")
    private BigDecimal rzactz;

    @ApiModelProperty(value = "汛限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @ApiModelProperty(value = "汛限库容")
    @TableField("FSLTDW")
    private BigDecimal fsltdw;

    @ApiModelProperty(value = "死库容")
    @TableField("DDCP")
    private BigDecimal ddcp;

    @ApiModelProperty(value = "水库KEY")
    @TableField("RES_KEY")
    private String resKey;

    @ApiModelProperty(value = "工程编码")
    @TableField("RES_CODE")
    private String resCode;

    @ApiModelProperty(value = "工程名称")
    @TableField("RES_NAME")
    private String resName;

    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    @TableField("RVNM")
    private String rvnm;

    @ApiModelProperty(value = "水系名称")
    @TableField("HNNM")
    private String hnnm;

    @ApiModelProperty(value = "流域名称")
    @TableField("BSNM")
    private String bsnm;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private BigDecimal lttd;

    @ApiModelProperty(value = "站址")
    @TableField("STLC")
    private String stlc;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField("ADNM")
    private String adnm;

    @ApiModelProperty(value = "站类")
    @TableField("STTP")
    private String sttp;

    @ApiModelProperty(value = "测站归属类型")
    @TableField("stadtp")
    private String stadtp;

    @ApiModelProperty(value = "是否是开敞式溢洪道")
    @TableField("OSFLG")
    private String osflg;

    @ApiModelProperty(value = "超汛限")
    @TableField("RZFLAG")
    private Double rzflag;

    @ApiModelProperty(value = "是否超汛限")
    @TableField("FSLTDZFLAG")
    private Integer fsltdzflag;

    @ApiModelProperty(value = "是否超设计")
    @TableField("DSFLZFLAG")
    private Integer dsflzflag;

    @ApiModelProperty(value = "超设计")
    private BigDecimal rzdsflz;

    @ApiModelProperty(value = "县级行政区划码")
    private String xadcd;

    @ApiModelProperty(value = "县级行政区划名称")
    private String xadnm;

    @ApiModelProperty(value = "乡镇级行政区划码")
    private String xzadcd;

    @ApiModelProperty(value = "乡镇级行政区划名称")
    private String xzadnm;

    @ApiModelProperty(value = "平均入流量")
    private BigDecimal avinq;

    @ApiModelProperty(value = "平均出流量")
    private BigDecimal avotq;

    @ApiModelProperty(value = "偏移经度")
    private BigDecimal plgtd;

    @ApiModelProperty(value = "偏移纬度")
    private BigDecimal plttd;

    @ApiModelProperty(value = "排序")
    private Integer sortNo;

    // 小水库风险图和调查评价数据

    @ApiModelProperty(value = "防汛检查对象")
    private String objid;

    @ApiModelProperty(value = "防治区名称")
    private String objnms;

    @ApiModelProperty(value = "总人口数")
    private BigDecimal ptcounts;

    @ApiModelProperty(value = "总户数")
    private BigDecimal etcounts;

    @ApiModelProperty(value = "总房屋数")
    private BigDecimal htcounts;

    @ApiModelProperty(value = "土地面积")
    private BigDecimal ldareas;

    @ApiModelProperty(value = "耕地面积")
    private BigDecimal plareas;

    @ApiModelProperty(value = "防洪高水位")
    @TableField("UPP_LEV_FLCO")
    private Double uppLevFlco;

    @ApiModelProperty(value = "防洪高水位对应库容")
    @TableField("UPP_LEV_CAP")
    private Double uppLevCap;

    @ApiModelProperty(value = "超防洪高库容")
    @TableField("RZ_UPP_LEV_CAP")
    private Double rzUppLevCap;

    @ApiModelProperty(value = "水位差")
    @TableField("DRZ")
    private Double drz;

    @ApiModelProperty(value = "正常库容")
    private BigDecimal normcp;

    @ApiModelProperty(value = "蓄水率")
    private BigDecimal normrate;

    @ApiModelProperty(value = "超设计水位")
    private BigDecimal dsflzs;

    @ApiModelProperty(value = "超校核水位")
    private BigDecimal ckflzs;

    @ApiModelProperty(value = "超坝顶高程")
    private BigDecimal damels;

    @ApiModelProperty(value = "人工水位")
    private String rrz;

    @ApiModelProperty(value = "自动水位")
    private String zrz;

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getRsvrtp() {
        return rsvrtp;
    }

    public void setRsvrtp(String rsvrtp) {
        this.rsvrtp = rsvrtp;
    }

    public BigDecimal getDamel() {
        return damel;
    }

    public void setDamel(BigDecimal damel) {
        this.damel = damel;
    }

    public BigDecimal getCkflz() {
        return ckflz;
    }

    public void setCkflz(BigDecimal ckflz) {
        this.ckflz = ckflz;
    }

    public BigDecimal getDsflz() {
        return dsflz;
    }

    public void setDsflz(BigDecimal dsflz) {
        this.dsflz = dsflz;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }

    public BigDecimal getDdz() {
        return ddz;
    }

    public void setDdz(BigDecimal ddz) {
        this.ddz = ddz;
    }

    public BigDecimal getActz() {
        return actz;
    }

    public void setActz(BigDecimal actz) {
        this.actz = actz;
    }

    public BigDecimal getTtcp() {
        return ttcp;
    }

    public void setTtcp(BigDecimal ttcp) {
        this.ttcp = ttcp;
    }

    public BigDecimal getDscp() {
        return dscp;
    }

    public void setDscp(BigDecimal dscp) {
        this.dscp = dscp;
    }

    public BigDecimal getFldcp() {
        return fldcp;
    }

    public void setFldcp(BigDecimal fldcp) {
        this.fldcp = fldcp;
    }

    public BigDecimal getActcp() {
        return actcp;
    }

    public void setActcp(BigDecimal actcp) {
        this.actcp = actcp;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public BigDecimal getWfsltdw() {
        return wfsltdw;
    }

    public void setWfsltdw(BigDecimal wfsltdw) {
        this.wfsltdw = wfsltdw;
    }

    public BigDecimal getRznormz() {
        return rznormz;
    }

    public void setRznormz(BigDecimal rznormz) {
        this.rznormz = rznormz;
    }

    public BigDecimal getRzactz() {
        return rzactz;
    }

    public void setRzactz(BigDecimal rzactz) {
        this.rzactz = rzactz;
    }

    public BigDecimal getDdcp() {
        return ddcp;
    }

    public void setDdcp(BigDecimal ddcp) {
        this.ddcp = ddcp;
    }

    public String getResKey() {
        return resKey;
    }

    public void setResKey(String resKey) {
        this.resKey = resKey;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getHnnm() {
        return hnnm;
    }

    public void setHnnm(String hnnm) {
        this.hnnm = hnnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getStadtp() {
        return stadtp;
    }

    public void setStadtp(String stadtp) {
        this.stadtp = stadtp;
    }

    public String getOsflg() {
        return osflg;
    }

    public void setOsflg(String osflg) {
        this.osflg = osflg;
    }

    public Double getRzflag() {
        return rzflag;
    }

    public void setRzflag(Double rzflag) {
        this.rzflag = rzflag;
    }

    public Integer getFsltdzflag() {
        return fsltdzflag;
    }

    public void setFsltdzflag(Integer fsltdzflag) {
        this.fsltdzflag = fsltdzflag;
    }

    public Integer getDsflzflag() {
        return dsflzflag;
    }

    public void setDsflzflag(Integer dsflzflag) {
        this.dsflzflag = dsflzflag;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getXzadcd() {
        return xzadcd;
    }

    public void setXzadcd(String xzadcd) {
        this.xzadcd = xzadcd;
    }

    public String getXzadnm() {
        return xzadnm;
    }

    public void setXzadnm(String xzadnm) {
        this.xzadnm = xzadnm;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public BigDecimal getFsltdw() {
        return fsltdw;
    }

    public void setFsltdw(BigDecimal fsltdw) {
        this.fsltdw = fsltdw;
    }

    public BigDecimal getAvinq() {
        return avinq;
    }

    public void setAvinq(BigDecimal avinq) {
        this.avinq = avinq;
    }

    public BigDecimal getAvotq() {
        return avotq;
    }

    public void setAvotq(BigDecimal avotq) {
        this.avotq = avotq;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    public String getObjid() {
        return objid;
    }

    public void setObjid(String objid) {
        this.objid = objid;
    }

    public String getObjnms() {
        return objnms;
    }

    public void setObjnms(String objnms) {
        this.objnms = objnms;
    }

    public BigDecimal getPtcounts() {
        return ptcounts;
    }

    public void setPtcounts(BigDecimal ptcounts) {
        this.ptcounts = ptcounts;
    }

    public BigDecimal getEtcounts() {
        return etcounts;
    }

    public void setEtcounts(BigDecimal etcounts) {
        this.etcounts = etcounts;
    }

    public BigDecimal getHtcounts() {
        return htcounts;
    }

    public void setHtcounts(BigDecimal htcounts) {
        this.htcounts = htcounts;
    }

    public BigDecimal getLdareas() {
        return ldareas;
    }

    public void setLdareas(BigDecimal ldareas) {
        this.ldareas = ldareas;
    }

    public BigDecimal getPlareas() {
        return plareas;
    }

    public void setPlareas(BigDecimal plareas) {
        this.plareas = plareas;
    }

    public Double getUppLevFlco() {
        return uppLevFlco;
    }

    public void setUppLevFlco(Double uppLevFlco) {
        this.uppLevFlco = uppLevFlco;
    }

    public Double getUppLevCap() {
        return uppLevCap;
    }

    public void setUppLevCap(Double uppLevCap) {
        this.uppLevCap = uppLevCap;
    }

    public Double getRzUppLevCap() {
        return rzUppLevCap;
    }

    public void setRzUppLevCap(Double rzUppLevCap) {
        this.rzUppLevCap = rzUppLevCap;
    }

    public BigDecimal getRzdsflz() {
        return rzdsflz;
    }

    public void setRzdsflz(BigDecimal rzdsflz) {
        this.rzdsflz = rzdsflz;
    }

    public Double getDrz() {
        return drz;
    }

    public void setDrz(Double drz) {
        this.drz = drz;
    }

    public BigDecimal getNormcp() {
        return normcp;
    }

    public void setNormcp(BigDecimal normcp) {
        this.normcp = normcp;
    }

    public BigDecimal getNormrate() {
        return normrate;
    }

    public void setNormrate(BigDecimal normrate) {
        this.normrate = normrate;
    }

    public BigDecimal getDsflzs() {
        return dsflzs;
    }

    public void setDsflzs(BigDecimal dsflzs) {
        this.dsflzs = dsflzs;
    }

    public BigDecimal getCkflzs() {
        return ckflzs;
    }

    public void setCkflzs(BigDecimal ckflzs) {
        this.ckflzs = ckflzs;
    }

    public BigDecimal getDamels() {
        return damels;
    }

    public void setDamels(BigDecimal damels) {
        this.damels = damels;
    }

    public String getRrz() {
        return rrz;
    }

    public void setRrz(String rrz) {
        this.rrz = rrz;
    }

    public String getZrz() {
        return zrz;
    }

    public void setZrz(String zrz) {
        this.zrz = zrz;
    }
}
