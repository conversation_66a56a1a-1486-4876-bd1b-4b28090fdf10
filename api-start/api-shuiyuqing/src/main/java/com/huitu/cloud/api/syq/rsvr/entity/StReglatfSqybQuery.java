package com.huitu.cloud.api.syq.rsvr.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 多站水库水情预报传入实体类
 * <AUTHOR>
 */
public class StReglatfSqybQuery {
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "1为超汛限、2为超正常高")
    private String wnstatus;

    @ApiModelProperty(value = "每页数量")
    private int pageSize;


    @ApiModelProperty(value = "页码")
    private int pageNum;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "开始时间，格式：yyyy-MM-dd HH:mm:ss", example = "2023-10-10 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间，格式：yyyy-MM-dd HH:mm:ss", example = "2023-10-17 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;

    @ApiModelProperty(value = "测站编码")
    private List<String> stcds;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getWnstatus() {
        return wnstatus;
    }

    public void setWnstatus(String wnstatus) {
        this.wnstatus = wnstatus;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public List<String> getStcds() {
        return stcds;
    }

    public void setStcds(List<String> stcds) {
        this.stcds = stcds;
    }
}
