<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.shyj.warn.mapper.LocWarnDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <!-- 分页查询现地预警 -->
    <select id="pageLocWarn" resultType="com.huitu.cloud.api.shyj.warn.entity.LocWarnVo" useCache="false">
        SELECT DISTINCT W.WARNID, W.ADCD, AD.ADNM, PD.ADCD XADCD, PD.ADNM XADNM, WARNTYPEID, W.WARNGRADEID, WARNGRADENM, W.WARNSTATUSID, WARNSTATUSNM
        , WARNSTM, WARNETM, WARNNM, WARNDESC, REMARK
        , WST.STCD, STB.STNM, STB.LGTD, STB.LTTD
        FROM WARNRECORD_R W
        LEFT JOIN WARN_STCD_R WST ON W.WARNID=WST.WARNID
        LEFT JOIN ST_STBPRP_B STB ON WST.STCD=STB.STCD
        LEFT JOIN BSN_ADCD_B AD ON W.ADCD=AD.ADCD
        LEFT JOIN BSN_ADCD_B PD ON substring(W.ADCD,1,6)+'000000000'=PD.ADCD
        LEFT JOIN WARNINGGRADE_B WG ON W.WARNGRADEID=WG.WARNGRADEID
        LEFT JOIN WARNINGSTATUS_B WS ON W.WARNSTATUSID=WS.WARNSTATUSID
        <where>
            WARNTYPEID IN (20,31,32,33,34,35,70,75)
            AND LEFT(AD.ADCD, #{form.adlvl}) = LEFT(#{form.adcd}, #{form.adlvl})
            <if test="form.stm != null">
                AND W.WARNSTM >= CONVERT(datetime,#{form.stm})
            </if>
            <if test="form.etm != null">
                AND W.WARNSTM &lt;= CONVERT(datetime,#{form.etm})
            </if>
            <if test="form.warnstatusid != null and form.warnstatusid != ''">
                AND W.WARNSTATUSID = #{form.warnstatusid}
            </if>
            <if test="form.warngradeid != null and form.warngradeid != ''">
                AND W.WARNGRADEID = #{form.warngradeid}
            </if>
        </where>
        ORDER BY W.WARNSTM DESC
    </select>

    <!-- 列表查询现地预警 -->
    <select id="listLocWarnOld" resultType="com.huitu.cloud.api.shyj.warn.entity.LocWarnVo" useCache="false">
        SELECT STB.STCD, STB.STNM, STB.LGTD, STB.LTTD, STB.ADCD, STB.ADNM, STB.XADCD, STB.XADNM
        , CASE WHEN W.WARNID IS NOT NULL THEN 2 WHEN STM.TM IS NOT NULL THEN 1 ELSE 0 END AS STATE
        , W.WARNID, W.WARNTYPEID, W.WARNGRADEID, W.WARNGRADENM, W.WARNSTATUSID, W.WARNSTATUSNM
        , W.WARNSTM, W.WARNETM, W.WARNNM, W.WARNDESC, W.REMARK
        FROM BSN_LOCWARN_ST LST INNER JOIN BSN_STBPRP_V STB ON LST.STCD=STB.STCD
        LEFT JOIN (SELECT R.STCD, MAX(TM) TM FROM ST_PPTN_R R INNER JOIN BSN_LOCWARN_ST LST ON R.STCD=LST.STCD
        <where>
            <choose>
                <when test="form.etm != null">
                    TM >= CONVERT(datetime,#{form.etm})-1 AND TM &lt;= CONVERT(datetime,#{form.etm})
                </when>
                <otherwise>
                    TM >= GETDATE()-1 AND TM &lt;= GETDATE()
                </otherwise>
            </choose>
        </where>
        GROUP BY R.STCD) STM ON STB.STCD=STM.STCD
        LEFT JOIN (SELECT DISTINCT W.WARNID, W.ADCD, WARNTYPEID
        , W.WARNGRADEID, WARNGRADENM, W.WARNSTATUSID, WARNSTATUSNM
        , WARNSTM, WARNETM, WARNNM, WARNDESC, REMARK
        , WST.STCD, ROW_NUMBER() OVER (PARTITION BY WST.STCD ORDER BY WARNSTM DESC) AS RN
        FROM WARNRECORD_R W
        LEFT JOIN WARN_STCD_R WST ON W.WARNID=WST.WARNID
        LEFT JOIN WARNINGGRADE_B WG ON W.WARNGRADEID=WG.WARNGRADEID
        LEFT JOIN WARNINGSTATUS_B WS ON W.WARNSTATUSID=WS.WARNSTATUSID
        <where>
            WARNTYPEID IN (20,31,32,33,34,35,70,75)
            <if test="form.stm != null">
                AND W.WARNSTM >= CONVERT(datetime,#{form.stm})
            </if>
            <if test="form.etm != null">
                AND W.WARNSTM &lt;= CONVERT(datetime,#{form.etm})
            </if>
            <if test="form.warnstatusid != null and form.warnstatusid != ''">
                AND W.WARNSTATUSID = #{form.warnstatusid}
            </if>
            <if test="form.warngradeid != null and form.warngradeid != ''">
                AND W.WARNGRADEID = #{form.warngradeid}
            </if>
        </where>
        ) W ON LST.STCD=W.STCD AND W.RN=1
        <where>
            LEFT(STB.ADDVCD, #{form.adlvl}) = LEFT(#{form.adcd}, #{form.adlvl})
        </where>
        ORDER BY W.STWARNSTM DESC, STATE DESC, ADCD ASC
    </select>

    <!-- 列表查询现地预警 -->
    <select id="listLocWarn" resultType="com.huitu.cloud.api.shyj.warn.entity.StwarnrecordXdRVo" useCache="false">
        SELECT STNM, ADCD, ADNM, XADCD, XADNM, LTTD, LGTD, PLTTD, PLGTD,
        STWARNID, STCD, WARNTYPEID, WARNGRADEID, STWARNNM, STWARNDESC, STWARNSTM, STWARNETM, REMARK,
        RWID, RWTP, TM, STATUS, CMD_TYPE, warningStatus FROM (
            SELECT ROW_NUMBER () OVER ( PARTITION BY A.STCD ORDER BY A.STCD, B.STWARNSTM DESC, A.ADCD, C.TM DESC) ROW_NUM, A.STCD, A.STNM, A.ADCD, A.ADNM, A.XADCD, A.XADNM, A.LTTD, A.LGTD, A.PLTTD, A.PLGTD,
            B.STWARNID, B.WARNTYPEID, B.WARNGRADEID, B.STWARNNM, B.STWARNDESC, B.STWARNSTM, B.STWARNETM, B.REMARK,
            C.RWID, C.RWTP, C.TM, C.STATUS, C.CMD_TYPE,
            CASE
            WHEN B.STCD IS NOT NULL THEN 1
            ELSE 2
            END AS warningStatus
            from BSN_STBPRP_V_YG A
            LEFT JOIN STWARNRECORD_R B ON A.STCD = B.STCD
            <if test="form.stm != null">
                AND B.STWARNSTM >= CONVERT(datetime,#{form.stm})
            </if>
            <if test="form.etm != null">
                AND B.STWARNSTM &lt;= CONVERT(datetime,#{form.etm})
            </if>
            LEFT JOIN WARN_RWM_STATUS_R C ON B.STWARNID = C.STWARNID AND C.RWID = 1
            WHERE A.COMMENTS LIKE '%现地预警%'
        ) A
        WHERE A.ROW_NUM = 1
        <if test="form.adcd!=null and form.adcd!=''">
            and  left(ADCD,#{form.adlvl})=LEFT(#{form.adcd}, #{form.adlvl})
        </if>
        <if test="form.adlvl!=null and form.adlvl ==4 ">
            and  (left(ADCD, 6) !='220581' and left(ADCD, 6) !='220381')
        </if>
        ORDER BY STWARNSTM DESC, WarnGradeID DESC, A.ADCD
    </select>

    <!-- 列表查询现地预警 -->
    <select id="LocWarnDetailData" resultType="com.huitu.cloud.api.shyj.warn.entity.StwarnrecordXdRVo" useCache="false">
        SELECT A.STWARNID, A.WARNGRADEID, A.STWARNNM, A.STWARNDESC, A.STWARNSTM, A.STWARNETM, A.REMARK,
               B.Item, B.DT
        FROM STWARNRECORD_R A
        LEFT JOIN WarnRealData_R B ON A.STCD = B.STCD AND A.STWARNSTM = B.TIME
        <where>
            <if test="stcd != null and stcd != ''">
                AND A.STCD = #{stcd}
            </if>
            <if test="stm != null">
                AND A.STWARNSTM >= CONVERT(datetime,#{stm})
            </if>
            <if test="etm != null">
                AND A.STWARNSTM &lt;= CONVERT(datetime,#{etm})
            </if>
        </where>
        ORDER BY A.STWARNSTM DESC
    </select>

    <!-- 列表查询现地预警 -->
    <select id="LocWarnDetailStatusList" resultType="com.huitu.cloud.api.shyj.warn.entity.WarnRwmStatusR" useCache="false">
        SELECT STWARNID, STCD, RWID, RWTP, TM, STATUS, CMD_TYPE
        FROM (
            SELECT *,
            ROW_NUMBER() OVER (PARTITION BY RWID ORDER BY TM DESC) AS rn
            FROM WARN_RWM_STATUS_R
            <where>
                STCD = #{stcd}
                AND STWARNID IN
                <foreach item="item" collection="idList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </where>
        ) t
        WHERE rn = 1;
    </select>

    <select id="listLocWarnDevice" resultType="com.huitu.cloud.api.shyj.warn.entity.LocWarnDeviceVo">
        SELECT * FROM (
        SELECT WARNID, STCD, DATATIME, RWID, RWTP, RWS1, RWS2, RWS3, RWS4, RWS5
        , ROW_NUMBER() OVER (PARTITION BY WARNID, RWID ORDER BY DATATIME DESC) AS RN
        FROM STWARN_RWM_R
        <where>
            <if test="form.warnids != null and form.warnids.size() > 0">
                AND WARNID IN
                <foreach collection="form.warnids" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ) X WHERE X.RN=1 ORDER BY WARNID, RWID, DATATIME
    </select>

    <select id="getLocWarnById" resultType="com.huitu.cloud.api.shyj.warn.entity.LocWarnVo">
        SELECT DISTINCT W.WARNID, W.ADCD, AD.ADNM, PD.ADCD XADCD, PD.ADNM XADNM, WARNTYPEID, W.WARNGRADEID, WARNGRADENM, W.WARNSTATUSID, WARNSTATUSNM
        , WARNSTM, WARNETM, WARNNM, WARNDESC, REMARK
        , WST.STCD, STB.STNM, STB.LGTD, STB.LTTD
        FROM WARNRECORD_R W
        LEFT JOIN WARN_STCD_R WST ON W.WARNID=WST.WARNID
        LEFT JOIN ST_STBPRP_B STB ON WST.STCD=STB.STCD
        LEFT JOIN BSN_ADCD_B AD ON W.ADCD=AD.ADCD
        LEFT JOIN BSN_ADCD_B PD ON substring(W.ADCD,1,6)+'000000000'=PD.ADCD
        LEFT JOIN WARNINGGRADE_B WG ON W.WARNGRADEID=WG.WARNGRADEID
        LEFT JOIN WARNINGSTATUS_B WS ON W.WARNSTATUSID=WS.WARNSTATUSID
        <where>
            W.WARNID = #{warnid}
        </where>
    </select>

</mapper>
