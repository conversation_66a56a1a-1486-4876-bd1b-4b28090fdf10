package com.huitu.cloud.api.ewci.person.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.person.entity.FsdaPersonBVo;
import com.huitu.cloud.api.ewci.person.entity.RiverPerson;
import com.huitu.cloud.api.ewci.person.service.FsdaPersonService;
import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 蓄滞洪区责任人
 *
 * <AUTHOR>
 */
@Api(tags = "蓄滞洪区责任人")
@RestController
@RequestMapping("/api/ewci/person/fsda")
public class FsdaPersonResource extends AbstractApiResource implements ApiResource {

    private final FsdaPersonService baseService;

    public FsdaPersonResource(FsdaPersonService baseService) {
        this.baseService = baseService;
    }

    @Override
    public String getUuid() {
        return "8e8a5276-f61a-ba16-f676-269d531cc271";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @ApiOperation(value = "分页获取蓄滞洪区责任人信息列表", notes = "作者：赵英捷")
    @PostMapping("select-page-list")
    public ResponseEntity<SuccessResponse<IPage<FsdaPersonBVo>>> getPageList(@Validated @RequestBody PageBean page) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(page)));
    }

    @ApiOperation(value = "编辑蓄滞洪区责任人", notes = "赵英捷")
    @PostMapping(value = "fsda-person-edit")
    public ResponseEntity<SuccessResponse<Integer>> fsdaPersonEdit(@RequestBody FsdaPersonBVo entity) throws Exception {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.fsdaPersonEdit(entity)));
    }
}
