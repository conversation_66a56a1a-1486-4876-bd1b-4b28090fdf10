package com.huitu.cloud.api.ewci.tencent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.tencent.entity.PageParams;
import com.huitu.cloud.api.ewci.tencent.entity.VideoConference;
import com.huitu.cloud.api.ewci.tencent.entity.VideoConferenceVo;
import com.huitu.cloud.api.ewci.tencent.entity.VideoPersonRecord;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 音视频会议表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface VideoConferenceDao extends BaseMapper<VideoConference> {

    void updateVideoConference(@Param("entity") VideoConference entity);

    /**
     * 查询会议中的列表
     * @param page 分页参数
     * @param params 额外其他参数
     * @return IPage<VideoConferenceVo>
     */
    IPage<VideoConferenceVo> getMeetingList(Page<VideoConference> page, @Param("params") PageParams params);


}
