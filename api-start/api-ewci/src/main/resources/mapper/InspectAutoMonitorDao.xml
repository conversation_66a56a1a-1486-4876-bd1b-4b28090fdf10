<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.inspect.mapper.InspectAutoMonitorDao">

    <select id="getInspectAutoMonitorList"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectAutoMonitor">
        SELECT *
        FROM BNS_INSPECT_AUTO_MONITOR
        WHERE ADCD = #{adcd} AND [YEAR] = #{year}
          AND STCD = #{stcd}
    </select>
    <select id="selectYearByInspectId" resultType="java.util.Date">
        select INSP_DATE FROM BNS_INSPECT_AUTO_MONITOR WHERE INSPECT_ID = #{inspectId}
    </select>
    <insert id="insertInspectAutoMonitor">
        INSERT INTO BNS_INSPECT_AUTO_MONITOR(INSPECT_ID,
                                             ADCD,
                                             INSPECTOR,
                                             GROUPID,
                                             INSP_DATE,
                                             STCD,
                                             EXIST_PROBLEMS,
                                             REC_ASK,
                                             REC_DATE,
                                             YEAR,
                                             LGTD,
                                             LTTD, CREATOR)
        VALUES ( #{inspectId}
               , #{adcd}
               , #{inspector}
               , #{groupid}
               , #{inspDate}
               , #{stcd}
               , #{existProblems}
               , #{recAsk}
               , #{recDate}
               , #{year}
               , #{lgtd}
               , #{lttd}, #{creator})
    </insert>
    <update id="updateInspectAutoMonitor">
        UPDATE BNS_INSPECT_AUTO_MONITOR
        <trim prefix="set" suffixOverrides=",">
            <if test="adcd != null">ADCD = #{adcd},</if>
            <if test="inspector != null">INSPECTOR = #{inspector},</if>
            <if test="groupid != null">GROUPID = #{groupid},</if>
            <if test="inspDate != null">INSP_DATE = #{inspDate},</if>
            <if test="stcd != null">STCD = #{stcd},</if>
            <if test="existProblems != null">EXIST_PROBLEMS = #{existProblems},</if>
            <if test="recAsk != null">REC_ASK = #{recAsk},</if>
            <if test="recDate != null">REC_DATE = #{recDate},</if>
            <if test="year != null">[YEAR] = #{year},</if>
            <if test="lgtd != null">LGTD = #{lgtd},</if>
            <if test="lttd != null">LTTD = #{lttd},</if>
        </trim>
        WHERE INSPECT_ID=#{inspectId} AND CREATOR = #{creator}
    </update>
    <delete id="deleteInspectAutoMonitor">
        DELETE
        FROM BNS_INSPECT_AUTO_MONITOR
        WHERE INSPECT_ID = #{inspectId}
    </delete>

</mapper>