package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警设备工况信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-19
 */
@TableName("BSN_WIRELESSINFO_R")
@ApiModel(value="BsnWirelessinfoR对象", description="预警设备工况信息表")
public class BsnWirelessinfo extends Model<BsnWirelessinfo> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "广播站编码")
    @TableId(value = "RACD", type = IdType.NONE)
    private String racd;

    @ApiModelProperty(value = "时间")
    @TableField("RADT")
    private LocalDateTime radt;

    @ApiModelProperty(value = "运行状态(0—正常；1—电池电压告警；2—信号强度告警；3—离线)")
    @TableField("RA_ST")
    private String raSt;

    @ApiModelProperty(value = "电池电压")
    @TableField("BATV")
    private Double batv;

    @ApiModelProperty(value = "充电电压")
    @TableField("CHAV")
    private Double chav;

    @ApiModelProperty(value = "信号强度")
    @TableField("SIG_ST")
    private Double sigSt;

    @ApiModelProperty(value = "功放状态(0—正常；1—无法工作)")
    @TableField("AMP_ST")
    private String ampSt;

    @ApiModelProperty(value = "接收时间")
    @TableField("SYSTM")
    private LocalDateTime systm;


    public String getRacd() {
        return racd;
    }

    public void setRacd(String racd) {
        this.racd = racd;
    }

    public LocalDateTime getRadt() {
        return radt;
    }

    public void setRadt(LocalDateTime radt) {
        this.radt = radt;
    }

    public String getRaSt() {
        return raSt;
    }

    public void setRaSt(String raSt) {
        this.raSt = raSt;
    }

    public Double getBatv() {
        return batv;
    }

    public void setBatv(Double batv) {
        this.batv = batv;
    }

    public Double getChav() {
        return chav;
    }

    public void setChav(Double chav) {
        this.chav = chav;
    }

    public Double getSigSt() {
        return sigSt;
    }

    public void setSigSt(Double sigSt) {
        this.sigSt = sigSt;
    }

    public String getAmpSt() {
        return ampSt;
    }

    public void setAmpSt(String ampSt) {
        this.ampSt = ampSt;
    }

    public LocalDateTime getSystm() {
        return systm;
    }

    public void setSystm(LocalDateTime systm) {
        this.systm = systm;
    }

    @Override
    protected Serializable pkVal() {
        return this.racd;
    }

    @Override
    public String toString() {
        return "BsnWirelessinfo{" +
        "racd=" + racd +
        ", radt=" + radt +
        ", raSt=" + raSt +
        ", batv=" + batv +
        ", chav=" + chav +
        ", sigSt=" + sigSt +
        ", ampSt=" + ampSt +
        ", systm=" + systm +
        "}";
    }
}
