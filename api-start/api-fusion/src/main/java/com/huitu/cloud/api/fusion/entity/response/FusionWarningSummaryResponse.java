package com.huitu.cloud.api.fusion.entity.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 监测预警汇总统计响应对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "监测预警汇总统计响应对象")
public class FusionWarningSummaryResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警总次数")
    private int total1;

    @ApiModelProperty(value = "预警总人次")
    private int total2;

    @ApiModelProperty(value = "反馈总人次")
    private int total3;

    @ApiModelProperty(value = "预警总人数")
    private int total4;

    @ApiModelProperty(value = "山洪预警次数")
    private int wcount1;

    @ApiModelProperty(value = "水库预警次数")
    private int wcount2;

    @ApiModelProperty(value = "河道预警次数")
    private int wcount3;

    @ApiModelProperty(value = "山洪预警人次")
    private int pcount1;

    @ApiModelProperty(value = "水库预警人次")
    private int pcount2;

    @ApiModelProperty(value = "河道预警人次")
    private int pcount3;

    @ApiModelProperty(value = "山洪反馈人次")
    private int fcount1;

    @ApiModelProperty(value = "水库反馈人次")
    private int fcount2;

    @ApiModelProperty(value = "河道反馈人次")
    private int fcount3;

    @ApiModelProperty(value = "山洪预警人数")
    private int rcount1;

    @ApiModelProperty(value = "水库预警人数")
    private int rcount2;

    @ApiModelProperty(value = "河道预警人数")
    private int rcount3;

    public int getTotal1() {
        return total1;
    }

    public void setTotal1(int total1) {
        this.total1 = total1;
    }

    public int getTotal2() {
        return total2;
    }

    public void setTotal2(int total2) {
        this.total2 = total2;
    }

    public int getTotal3() {
        return total3;
    }

    public void setTotal3(int total3) {
        this.total3 = total3;
    }

    public int getTotal4() {
        return total4;
    }

    public void setTotal4(int total4) {
        this.total4 = total4;
    }

    public int getWcount1() {
        return wcount1;
    }

    public void setWcount1(int wcount1) {
        this.wcount1 = wcount1;
    }

    public int getWcount2() {
        return wcount2;
    }

    public void setWcount2(int wcount2) {
        this.wcount2 = wcount2;
    }

    public int getWcount3() {
        return wcount3;
    }

    public void setWcount3(int wcount3) {
        this.wcount3 = wcount3;
    }

    public int getPcount1() {
        return pcount1;
    }

    public void setPcount1(int pcount1) {
        this.pcount1 = pcount1;
    }

    public int getPcount2() {
        return pcount2;
    }

    public void setPcount2(int pcount2) {
        this.pcount2 = pcount2;
    }

    public int getPcount3() {
        return pcount3;
    }

    public void setPcount3(int pcount3) {
        this.pcount3 = pcount3;
    }

    public int getFcount1() {
        return fcount1;
    }

    public void setFcount1(int fcount1) {
        this.fcount1 = fcount1;
    }

    public int getFcount2() {
        return fcount2;
    }

    public void setFcount2(int fcount2) {
        this.fcount2 = fcount2;
    }

    public int getFcount3() {
        return fcount3;
    }

    public void setFcount3(int fcount3) {
        this.fcount3 = fcount3;
    }

    public int getRcount1() {
        return rcount1;
    }

    public void setRcount1(int rcount1) {
        this.rcount1 = rcount1;
    }

    public int getRcount2() {
        return rcount2;
    }

    public void setRcount2(int rcount2) {
        this.rcount2 = rcount2;
    }

    public int getRcount3() {
        return rcount3;
    }

    public void setRcount3(int rcount3) {
        this.rcount3 = rcount3;
    }
}
