package com.huitu.cloud.api.ew.entity.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description：山洪预警等级统计列表
 * @date ：2025/8/7 13:48
 */
@Data
@ApiModel(description = "山洪预警等级统计列表")
@ColumnWidth(22)
public class EwWarnGradeStatisticsResponse implements Serializable {

    @ExcelIgnore
    @ApiModelProperty(value = "行政区划代码（15位）")
    private String adcd;

    @ExcelProperty(value = "政区", index = 0)
    @ApiModelProperty(value = "行政区划名称")
    private String adnm;

    @ExcelIgnore
    @ApiModelProperty(value = "上级政区编码")
    private String padcd;

    @ExcelIgnore
    @ApiModelProperty(value = "政区级别")
    private Integer adlvl;


    @ExcelProperty(value = {"一级预警", "行政村数量"}, index = 1)
    @ApiModelProperty(value = "一级行政村预警数")
    private int grade1Village;
    @ExcelProperty(value = {"一级预警", "自然村数量"}, index = 2)
    @ApiModelProperty(value = "一级自然村预警数")
    private int grade1NatureVillage;

    @ExcelProperty(value = {"一级预警", "预警人数"}, index = 3)
    @ApiModelProperty(value = "一级预警人数")
    private int grade1LiableCount;

    @ExcelProperty(value = {"一级预警", "预警次数"}, index = 4)
    @ApiModelProperty(value = "一级预警数")
    private int grade1Count;

    @ExcelProperty(value = {"一级预警", "短信发送人次"}, index = 5)
    @ApiModelProperty(value = "一级预警短信发送数")
    private int grade1SmsCount;

    @ExcelProperty(value = {"一级预警", "语音外呼人次"}, index = 6)
    @ApiModelProperty(value = "一级预警语音外呼人次")
    private int grade1VoiceCount;

    @ExcelProperty(value = {"一级预警", "语音外呼反馈人次"}, index = 7)
    @ApiModelProperty(value = "一级预警语音外呼反馈人次")
    private int grade1VoiceRead;

    @ExcelProperty(value = {"二级预警", "行政村数量"}, index = 8)
    @ApiModelProperty(value = "二级行政村预警数")
    private int grade2Village;

    @ExcelProperty(value = {"二级预警", "自然村数量"}, index = 9)
    @ApiModelProperty(value = "二级自然村预警数")
    private int grade2NatureVillage;

    @ExcelProperty(value = {"二级预警", "预警人数"}, index = 10)
    @ApiModelProperty(value = "二级预警人数")
    private int grade2LiableCount;

    @ExcelProperty(value = {"二级预警", "预警次数"}, index = 11)
    @ApiModelProperty(value = "二级预警数")
    private int grade2Count;

    @ExcelProperty(value = {"二级预警", "短信发送人次"}, index = 12)
    @ApiModelProperty(value = "二级预警短信发送数")
    private int grade2SmsCount;

    @ExcelProperty(value = {"二级预警", "语音外呼人次"}, index = 13)
    @ApiModelProperty(value = "二级预警语音外呼人次")
    private int grade2VoiceCount;

    @ExcelProperty(value = {"二级预警", "语音外呼反馈人次"}, index = 14)
    @ApiModelProperty(value = "二级预警语音外呼反馈人次")
    private int grade2VoiceRead;


    @ExcelProperty(value = {"三级预警", "行政村数量"}, index = 15)
    @ApiModelProperty(value = "三级行政村预警数")
    private int grade3Village;

    @ExcelProperty(value = {"三级预警", "自然村数量"}, index = 16)
    @ApiModelProperty(value = "三级自然村预警数")
    private int grade3NatureVillage;

    @ExcelProperty(value = {"三级预警", "预警人数"}, index = 17)
    @ApiModelProperty(value = "三级预警人数")
    private int grade3LiableCount;

    @ExcelProperty(value = {"三级预警", "预警次数"}, index = 18)
    @ApiModelProperty(value = "三级预警数")
    private int grade3Count;

    @ExcelProperty(value = {"三级预警", "短信发送人次"}, index = 19)
    @ApiModelProperty(value = "三级预警短信发送人次")
    private int grade3SmsCount;

    @ExcelProperty(value = {"三级预警", "语音外呼人次"}, index = 20)
    @ApiModelProperty(value = "三级预警语音外呼人次")
    private int grade3VoiceCount;

    @ExcelProperty(value = {"三级预警", "语音外呼反馈人次"}, index = 21)
    @ApiModelProperty(value = "三级预警语音外呼反馈人次")
    private int grade3VoiceRead;
}
