package com.huitu.cloud.api.datamanager.anmrecord.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 水位异常站点信息（批量处理水位异常信息）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-4-1
 */
@ApiModel(value="RzStInfo对象", description="水位异常站点信息")
public class RzStInfo implements Serializable {
    @ApiModelProperty(value = "站点编码")
    private String stcd;

    @ApiModelProperty(value = "站点名称")
    private String stnm;

    @ApiModelProperty(value = "对应表名")
    private String tbnm;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getTbnm() {
        return tbnm;
    }

    public void setTbnm(String tbnm) {
        this.tbnm = tbnm;
    }
}
