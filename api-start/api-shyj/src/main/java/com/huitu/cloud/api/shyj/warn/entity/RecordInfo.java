package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 * @since 2019-09-16
 */
@ApiModel(value="政区预警过程记录关联信息", description="政区预警过程记录关联信息")
public class RecordInfo extends WarnrecordT{
    @ApiModelProperty(value = " 预警状态名称")
    private String warnStatusNm;
    @ApiModelProperty(value = " 预警等级名称")
    private String warnGradeNm;
    @ApiModelProperty(value = " 政区名称")
    private String adnm;
    @ApiModelProperty(value = " 发送短信方式 10-短信；20-传真；30-广播。")
    private String mediaId;
    @ApiModelProperty(value = " 短信发出时间")
    private String sendTm;
    @ApiModelProperty(value = " 发送失败条数")
    private String msgct0;
    @ApiModelProperty(value = " 发送成功条数")
    private String msgct1;
    @ApiModelProperty(value = " 请求成功但未处理条数")
    private String msgct2;
    @ApiModelProperty(value = " 发送短信总条数")
    private String msgct;

    public String getWarnStatusNm() {
        return warnStatusNm;
    }

    public void setWarnStatusNm(String warnStatusNm) {
        this.warnStatusNm = warnStatusNm;
    }

    public String getWarnGradeNm() {
        return warnGradeNm;
    }

    public void setWarnGradeNm(String warnGradeNm) {
        this.warnGradeNm = warnGradeNm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getMediaId() {
        return mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public String getSendTm() {
        return sendTm;
    }

    public void setSendTm(String sendTm) {
        this.sendTm = sendTm;
    }

    public String getMsgct0() {
        return msgct0;
    }

    public void setMsgct0(String msgct0) {
        this.msgct0 = msgct0;
    }

    public String getMsgct1() {
        return msgct1;
    }

    public void setMsgct1(String msgct1) {
        this.msgct1 = msgct1;
    }

    public String getMsgct2() {
        return msgct2;
    }

    public void setMsgct2(String msgct2) {
        this.msgct2 = msgct2;
    }

    public String getMsgct() {
        return msgct;
    }

    public void setMsgct(String msgct) {
        this.msgct = msgct;
    }
}
