package com.huitu.cloud.api.ewci.hydrology.entity;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 接受洪水预警的单位
 *
 * <AUTHOR>
 */
public class HyWarnAccept implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField(value = "DEPT_ID")
    private String deptId;

    @TableField(value = "ADCD")
    private String adcd;

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }
}
