package com.huitu.cloud.api.hyst.survey.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <p>
 * 小水电概况VO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-30
 */
public class SurveyQuery extends PageBean {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水电站统计代码")
    private String hystCode;

    @ApiModelProperty(value = "时间粒度(0日1月2年)")
    @NotNull(message = "时间粒度不能为空")
    private String assPer;

    @ApiModelProperty(value = "状态（1在线2离线3达标4未达标）")
    private String status;

    @ApiModelProperty(value = "是否考核（0免考核1考核）")
    private String ass;

    @ApiModelProperty(value = "水电站名称")
    private String hystName;

    @ApiModelProperty(value = "记录时间")
    private LocalDateTime recTime;
    public String getHystCode() {
        return hystCode;
    }

    public void setHystCode(String hystCode) {
        this.hystCode = hystCode;
    }

    public String getAssPer() {
        return assPer;
    }

    public void setAssPer(String assPer) {
        this.assPer = assPer;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAss() {
        return ass;
    }

    public void setAss(String ass) {
        this.ass = ass;
    }

    public String getHystName() {
        return hystName;
    }

    public void setHystName(String hystName) {
        this.hystName = hystName;
    }

    public LocalDateTime getRecTime() {
        return recTime;
    }

    public void setRecTime(LocalDateTime recTime) {
        this.recTime = recTime;
    }
}
