package com.huitu.cloud.api.ewci.weChat.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.weChat.entity.WeChatRain;
import com.huitu.cloud.api.ewci.weChat.mapper.RainSituationDao;
import com.huitu.cloud.api.ewci.weChat.service.SituationRemarkService;
import com.huitu.cloud.api.ewci.weChat.service.RainSituationService;
import com.huitu.cloud.util.DateFormatUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class RainSituationServiceImpl implements RainSituationService {
    @Autowired
    private RainSituationDao wcRainDao;

    @Autowired
    private SituationRemarkService abstractSituationService;

    @Override
        public IPage<WeChatRain> getRainByCondition(List<String> stType) {
        int pageNum = 1;
        int pageSize = -1;
        Page page = new Page<>(pageNum, pageSize);
        // 参数异常将空数据退出
        if(typel(stType)){
            // 暂时设置为多选框条件全取消情况下清空数据
            if ((stType != null && (stType.size() == 0 || "".equals(stType.get(0))))) {
                page.setRecords(new ArrayList<WeChatRain>());
                return page;
            }
            List list = getRainByConditionAll(abstractSituationService.getTime("stm"), abstractSituationService.getTime("etm"), stType, "220000000000000", "0.1-99999", "1", null, null);
            page.setRecords(list);
            page.setTotal(list.size());
        } else {
            throw new IllegalArgumentException("参数异常");
        }
        return page;
    }

    @Override
    public List<WeChatRain> getRainByConditionAll(String stm, String etm, List<String> stType, String adcd, String threshold, String rainShowType, List<String> isOut, List<String> isFollow) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", "22");
        param.put("level", "2");
        List<WeChatRain> rainList = null;
        rainList = wcRainDao.getAccpByTmAll(param);
        Map<String, WeChatRain> rainMap = rainList.stream().collect(Collectors.toMap(WeChatRain::getStcd, Function.identity()));
        Map<String, Object> stParam = new HashMap<>();
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            stParam.put("stTypes", stTypes);
        }
        stParam.put("stType", stType);
        stParam.put("ad", "22");
        stParam.put("level", "2");
        stParam.put("adcd", adcd);
        stParam.put("userLevel", 2); // 2
        stParam.put("userAd", "220000000000000");
        List<WeChatRain> stList = wcRainDao.getRainStInfo(stParam);
        //有雨量数据集合
        List<WeChatRain> hbList = new ArrayList<>();
        //无雨量数据集合
        for (WeChatRain x : stList) {
            String stcd = x.getStcd();
                //查询一定时间内所有上报雨量信息的站
            if (rainMap.containsKey(stcd)) {
                x.setDrps(rainMap.get(stcd).getDrps());
                hbList.add(x);
            }
        }
        if (StringUtils.isNoneBlank(threshold)) {
            String[] items = threshold.split("-");
            final double min = Double.parseDouble(items[0]);
            final double max = Double.parseDouble(items[1]);
            //根据阀值过滤
            hbList = hbList.stream().filter(x -> new BigDecimal(x.getDrps()).doubleValue() >= min && new BigDecimal(x.getDrps()).doubleValue() <= max).collect(Collectors.toList());
        }
        //排序字段不能为空
        hbList.sort(new Comparator<WeChatRain>() {
            @Override
            public int compare(WeChatRain m1, WeChatRain m2) {
                double drp1 = new BigDecimal(m1.getDrps()).doubleValue();
                double drp2 = new BigDecimal(m2.getDrps()).doubleValue();
                if (drp1 - drp2 > 0) {
                    return -1;
                } else if (drp1 - drp2 < 0) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });

        //判断需不需要取未来一小时降雨信息
        try {
            String date = DateFormatUtil.beforeOrAfterHourToString(1, "yyyy-MM-dd HH:00", new Date());
            if (etm.equals(date)) {
                //查询未来一小时降雨信息
                List<Map<String, Object>> OneHourRainList = wcRainDao.getOneHourRain();
                if (OneHourRainList != null && OneHourRainList.size() > 0) {
                    Map<String, Object> OneHourRain = new HashMap<>();
                    OneHourRainList.forEach(item -> {
                        OneHourRain.put(item.get("STCD").toString(), item);
                    });
                    //将未来一小时降雨信息放入累计雨量集合中
                    hbList.forEach(item -> {
                        if (OneHourRain.containsKey(item.getStcd().trim())) {
                            Map<String, Object> rainValue = (Map<String, Object>) OneHourRain.get(item.getStcd().trim());
                            item.setOneHourRain(rainValue.get("RAIN").toString());
                        }
                    });
                    //添加累计雨量和未来一小时降雨之和的累计降雨
                    hbList.forEach(item -> {
                        if (item.getOneHourRain() != null && !"".equals(item.getOneHourRain())) {
                            if (item.getDrps() != null && !"".equals(item.getDrps())) {
                                BigDecimal a = new BigDecimal(item.getDrps());
                                BigDecimal b = new BigDecimal(item.getOneHourRain());
                                item.setDrpsrain(a.add(b).toString());
                            } else {
                                item.setDrpsrain(item.getOneHourRain());
                            }
                        } else {
                            item.setDrpsrain(item.getDrps());
                        }
                    });
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return hbList;
    }

    @Override
    public boolean typel(List<String> stType) {
        for (String s : stType) {
           if ("1".equals(s) || "2".equals(s) || "3".equals(s)) {
           } else {
               return false;
           }
        }
        return true;
    }
}