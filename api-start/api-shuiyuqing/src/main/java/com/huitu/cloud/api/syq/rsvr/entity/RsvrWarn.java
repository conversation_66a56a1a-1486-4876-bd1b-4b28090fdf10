package com.huitu.cloud.api.syq.rsvr.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
/**
 * <AUTHOR>
 * @since 2019-09-26
 */
@ApiModel(value = "RsvrWarn",description = "水库告警信息类")
public class RsvrWarn {
    @ApiModelProperty(value = "超汛限集合")
    private List<StRsvrVo> warnWfsltdwList;
    @ApiModelProperty(value = "超正常高集合")
    private List<StRsvrVo> warnRznormzList;

    public List<StRsvrVo> getWarnWfsltdwList() {
        return warnWfsltdwList;
    }

    public void setWarnWfsltdwList(List<StRsvrVo> warnWfsltdwList) {
        this.warnWfsltdwList = warnWfsltdwList;
    }

    public List<StRsvrVo> getWarnRznormzList() {
        return warnRznormzList;
    }

    public void setWarnRznormzList(List<StRsvrVo> warnRznormzList) {
        this.warnRznormzList = warnRznormzList;
    }
}
