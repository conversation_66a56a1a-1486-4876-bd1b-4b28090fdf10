package com.huitu.cloud.api.datamanager.manualreport.service.impl;

import com.huitu.cloud.api.datamanager.anmrecord.entity.River;
import com.huitu.cloud.api.datamanager.anmrecord.entity.Rsvr;
import com.huitu.cloud.api.datamanager.anmrecord.mapper.AnmrecordDao;
import com.huitu.cloud.api.datamanager.anmrecord.service.AnmrecordService;
import com.huitu.cloud.api.datamanager.manualreport.entity.BsnManualReport;
import com.huitu.cloud.api.datamanager.manualreport.entity.ManualReportVo;
import com.huitu.cloud.api.datamanager.manualreport.entity.QueryManualReport;
import com.huitu.cloud.api.datamanager.manualreport.mapper.ManualReportDao;
import com.huitu.cloud.api.datamanager.manualreport.service.ManualReportService;
import com.huitu.cloud.api.datamanager.ststop.entity.BsnStStopTab;
import com.huitu.cloud.api.datamanager.ststop.entity.BsnStstopr;
import com.huitu.cloud.api.datamanager.ststop.entity.BsnStstoprEx;
import com.huitu.cloud.api.datamanager.ststop.entity.StStopQo;
import com.huitu.cloud.api.datamanager.ststop.mapper.StstopDao;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.DateFormatUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人工报讯 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-23
 */
@Service
public class ManualReportServiceImpl implements ManualReportService {
    @Autowired
    private ManualReportDao reportDao;

    @Autowired
    private StstopDao ststopDao;

    @Autowired
    private AnmrecordDao anmrecordDao;

    @Autowired
    private AnmrecordService anmrecordService;
    @Override
    public List<ManualReportVo> getManualReportStInfo(String adcd) {
        Map<String,Object> param=new HashMap<>();
        int level= AdcdUtil.getAdLevel(adcd);
        if(level>0){
            param.put("ad",adcd.substring(0,level));
            param.put("level",level);
        }
        List<ManualReportVo> resultList=reportDao.getManualReportStInfo(param);
        return resultList;
    }

    @Override
    @Transactional
    public String addManualReportSt(StStopQo entity) {
        Date date=new Date();
        String tb=entity.getTbnm();
        List<BsnStStopTab> tbTypes=ststopDao.getTabByType(tb);
        for(BsnStStopTab item:tbTypes){
            //添加站点屏蔽记录
            BsnStstopr bsnStstopr=new BsnStstopr();
            bsnStstopr.setStcd(entity.getStcd());
            bsnStstopr.setStsttp("0");
            bsnStstopr.setUsernm(entity.getUsernm());
            bsnStstopr.setTm(date);
            bsnStstopr.setStsta("1");
            bsnStstopr.setTbnm(item.getTbnm());
            ststopDao.insert(bsnStstopr);
        }
        //将产生的异常数据 设为忽略
        anmrecordService.updateStaflg(entity.getStcd(),tb);
        //添加人工报讯站点配置记录
        BsnManualReport bsnManualReport=new BsnManualReport();
        bsnManualReport.setStcd(entity.getStcd());
        bsnManualReport.setTbnm(tb);
        bsnManualReport.setBgtm(date);
        reportDao.insertBsnManualReport(bsnManualReport);
        return "OK";
    }

    @Override
    @Transactional
    public String closeManualReportSt(String stcd,String tbnm) {
        List<BsnStstoprEx> list=ststopDao.getOneStstop(stcd,tbnm);
        if(list.size()>0){
            //变更测站某个要素的所有屏蔽的关闭状态
            ststopDao.colseStStop(stcd,tbnm);
        }
        //关闭人工报讯
        reportDao.close(stcd,tbnm);
        return "OK";
    }

    @Override
    public String manualReport(QueryManualReport entity) {
        String tbnm=entity.getTbnm();
        Date tmTwo = DateFormatUtil.formatStringToDate(entity.getTm(),"yyyy-MM-dd HH:mm:ss");
        if ("st_river_r".equals(tbnm)){
            //河道水情数据上报
            River river =new River();
            river.setStcd(entity.getStcd());
            river.setTmTwo(tmTwo);
            river.setEditvalue(entity.getZ());
            anmrecordDao.insertRiver(river);
        }else if("st_rsvr_r".equals(tbnm)){
            //水库水情数据上报
            Rsvr rsvrR=new Rsvr();
            rsvrR.setStcd(entity.getStcd());
            rsvrR.setTmTwo(tmTwo);
            rsvrR.setEditvalue(entity.getZ());
            anmrecordDao.insertRsvr(rsvrR);
        }
        return "OK";
    }
}
