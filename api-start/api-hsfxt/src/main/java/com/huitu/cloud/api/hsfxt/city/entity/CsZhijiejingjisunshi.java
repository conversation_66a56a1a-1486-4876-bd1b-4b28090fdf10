package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@TableName("CS_ZhiJieJingJiSunShi")
@ApiModel(value="CsZhijiejingjisunshi对象", description="直接经济损失")
public class CsZhijiejingjisunshi extends Model<CsZhijiejingjisunshi> {

    @ApiModelProperty(value = "序号")
    private Integer sortId;

    @ApiModelProperty(value = "查询条件")
    private String keyIndex;

    @ApiModelProperty(value = "政区编码")
    private String xzqhIndex;

    @ApiModelProperty(value = "政区名称")
    private String mingcheng;

    @ApiModelProperty(value = "淹没水深")
    private String ymss;

    @ApiModelProperty(value = "淹没面积")
    private Float ymmj;

    @ApiModelProperty(value = "淹没房屋面积")
    private Float ymfwmj;

    @ApiModelProperty(value = "受影响公路长度")
    private Float yxgl;

    @ApiModelProperty(value = "受影响铁路长度")
    private Float yxtl;

    @ApiModelProperty(value = "受影响人口总数")
    private Float yxrk;

    @ApiModelProperty(value = "受影响GDP")
    private Float yxGDP;

    @ApiModelProperty(value = "居民房屋损失")
    private Float jmfwss;

    @ApiModelProperty(value = "家庭财产损失")
    private Float jtccss;

    @ApiModelProperty(value = "工业损失")
    private Float gyss;

    @ApiModelProperty(value = "商业损失")
    private Float syss;

    @ApiModelProperty(value = "道路损失")
    private Float dlss;

    @ApiModelProperty(value = "编制区编码")
    private String fenqu;

    @ApiModelProperty(value = "上级序号")
    private String pid;

    @ApiModelProperty(value = "下级节点")
    private List<CsZhijiejingjisunshi> children;

    @ApiModelProperty(value = "影响街道总数")
    private int childrenCount;

    public int getChildrenCount() {
        return childrenCount;
    }

    public void setChildrenCount(int childrenCount) {
        this.childrenCount = childrenCount;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public Integer getSortId() {
        return sortId;
    }

    public void setSortId(Integer sortId) {
        this.sortId = sortId;
    }

    public String getKeyIndex() {
        return keyIndex;
    }

    public void setKeyIndex(String keyIndex) {
        this.keyIndex = keyIndex;
    }

    public String getXzqhIndex() {
        return xzqhIndex;
    }

    public void setXzqhIndex(String xzqhIndex) {
        this.xzqhIndex = xzqhIndex;
    }

    public String getMingcheng() {
        return mingcheng;
    }

    public void setMingcheng(String mingcheng) {
        this.mingcheng = mingcheng;
    }

    public String getYmss() {
        return ymss;
    }

    public void setYmss(String ymss) {
        this.ymss = ymss;
    }

    public Float getYmmj() {
        return ymmj;
    }

    public void setYmmj(Float ymmj) {
        this.ymmj = ymmj;
    }

    public Float getYmfwmj() {
        return ymfwmj;
    }

    public void setYmfwmj(Float ymfwmj) {
        this.ymfwmj = ymfwmj;
    }

    public Float getYxgl() {
        return yxgl;
    }

    public void setYxgl(Float yxgl) {
        this.yxgl = yxgl;
    }

    public Float getYxtl() {
        return yxtl;
    }

    public void setYxtl(Float yxtl) {
        this.yxtl = yxtl;
    }

    public Float getYxrk() {
        return yxrk;
    }

    public void setYxrk(Float yxrk) {
        this.yxrk = yxrk;
    }

    public Float getYxGDP() {
        return yxGDP;
    }

    public void setYxGDP(Float yxGDP) {
        this.yxGDP = yxGDP;
    }

    public Float getJmfwss() {
        return jmfwss;
    }

    public void setJmfwss(Float jmfwss) {
        this.jmfwss = jmfwss;
    }

    public Float getJtccss() {
        return jtccss;
    }

    public void setJtccss(Float jtccss) {
        this.jtccss = jtccss;
    }

    public Float getGyss() {
        return gyss;
    }

    public void setGyss(Float gyss) {
        this.gyss = gyss;
    }

    public Float getSyss() {
        return syss;
    }

    public void setSyss(Float syss) {
        this.syss = syss;
    }

    public Float getDlss() {
        return dlss;
    }

    public void setDlss(Float dlss) {
        this.dlss = dlss;
    }

    public String getFenqu() {
        return fenqu;
    }

    public void setFenqu(String fenqu) {
        this.fenqu = fenqu;
    }

    public List<CsZhijiejingjisunshi> getChildren() {
        return children;
    }

    public void setChildren(List<CsZhijiejingjisunshi> children) {
        this.children = children;
    }
}
