package com.huitu.cloud.api.hsfxt.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 风险图分析成果断面水位流量信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-28
 */
@TableName("KB_RVSANALY_SECRW_B")
@ApiModel(value="KbRvsanalySecrwB对象", description="风险图分析成果断面水位流量信息表")
public class KbRvsanalySecrwB extends Model<KbRvsanalySecrwB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "成果编号")
    @TableId(value = "CID", type = IdType.NONE)
    private String cid;

    @ApiModelProperty(value = "断面编号")
    @TableField("RSCD")
    private Double rscd;

    @ApiModelProperty(value = "最高水位")
    @TableField("Z")
    private Double z;

    @ApiModelProperty(value = "最大流量")
    @TableField("Q")
    private Double q;

    @ApiModelProperty(value = "糙率")
    @TableField("VR")
    private Double vr;


    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Double getRscd() {
        return rscd;
    }

    public void setRscd(Double rscd) {
        this.rscd = rscd;
    }

    public Double getZ() {
        return z;
    }

    public void setZ(Double z) {
        this.z = z;
    }

    public Double getQ() {
        return q;
    }

    public void setQ(Double q) {
        this.q = q;
    }

    public Double getVr() {
        return vr;
    }

    public void setVr(Double vr) {
        this.vr = vr;
    }

    @Override
    protected Serializable pkVal() {
        return this.cid;
    }

    @Override
    public String toString() {
        return "KbRvsanalySecrwB{" +
        "cid=" + cid +
        ", rscd=" + rscd +
        ", z=" + z +
        ", q=" + q +
        ", vr=" + vr +
        "}";
    }
}
