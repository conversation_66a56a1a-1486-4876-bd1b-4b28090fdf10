package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
@TableName("ST_PSTAT_R")
@ApiModel(value="StPstatR对象", description="降雨量统计表")
public class StPstatR extends Model<StPstatR> {

    private static final long serialVersionUID=1L;

    @TableField("IDTM")
    private LocalDateTime idtm;

    @TableField("STTDRCD")
    private String sttdrcd;

    @TableField("ACCP")
    private BigDecimal accp;

    @TableField("STCD")
    private String stcd;


    public LocalDateTime getIdtm() {
        return idtm;
    }

    public void setIdtm(LocalDateTime idtm) {
        this.idtm = idtm;
    }

    public String getSttdrcd() {
        return sttdrcd;
    }

    public void setSttdrcd(String sttdrcd) {
        this.sttdrcd = sttdrcd;
    }

    public BigDecimal getAccp() {
        return accp;
    }

    public void setAccp(BigDecimal accp) {
        this.accp = accp;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    @Override
    protected Serializable pkVal() {
        return null;
    }

    @Override
    public String toString() {
        return "StPstatR{" +
        "idtm=" + idtm +
        ", sttdrcd=" + sttdrcd +
        ", accp=" + accp +
        ", stcd=" + stcd +
        "}";
    }
}
