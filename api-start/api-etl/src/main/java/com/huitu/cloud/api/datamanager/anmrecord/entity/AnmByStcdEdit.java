package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2020-7-22
 */
@ApiModel(value = "AnmByStcdEdit", description = "异常数据-批量处理参数")
public class AnmByStcdEdit {
    @ApiModelProperty(value = "测站编码")
    private String stcd;
    @ApiModelProperty(value = "表名")
    private String tbnm;
    @ApiModelProperty(value = "备注")
    private String nt;
    @ApiModelProperty(value = "修改值")
    private String editValue;
    @ApiModelProperty(value = "操作人")
    private String doer;
    @ApiModelProperty(value = "数据状态 0:终审审批通过且入库；2终审审批未通过； 3,初审通过；4，初审未通过； 5，已修改")
    @TableField("STAFLG")
    private String staflg;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getTbnm() {
        return tbnm;
    }

    public void setTbnm(String tbnm) {
        this.tbnm = tbnm;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }

    public String getEditValue() {
        return editValue;
    }

    public void setEditValue(String editValue) {
        this.editValue = editValue;
    }

    public String getDoer() {
        return doer;
    }

    public void setDoer(String doer) {
        this.doer = doer;
    }

    public String getStaflg() {
        return staflg;
    }

    public void setStaflg(String staflg) {
        this.staflg = staflg;
    }
}