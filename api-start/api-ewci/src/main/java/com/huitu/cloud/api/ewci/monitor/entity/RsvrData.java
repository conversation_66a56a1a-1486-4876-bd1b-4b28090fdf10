package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 水库数据
 *
 * <AUTHOR>
 */
@ApiModel(value = "水库数据")
public class RsvrData implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "测站地址")
    @TableField(value = "STLC")
    private String stlc;

    @ApiModelProperty(value = "数据时间")
    @TableField("TM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime tm;

    @ApiModelProperty(value = "实时水位")
    @TableField(value = "RZ")
    private Double rz;

    @ApiModelProperty(value = "超汛限水位")
    @TableField(value = "RZFSLTDZ")
    private Double rzfsltdz;

    @ApiModelProperty(value = "汛限水位")
    @TableField(value = "FSLTDZ")
    private Double fsltdz;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public Double getRz() {
        return rz;
    }

    public void setRz(Double rz) {
        this.rz = rz;
    }

    public Double getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(Double rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public Double getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(Double fsltdz) {
        this.fsltdz = fsltdz;
    }
}
