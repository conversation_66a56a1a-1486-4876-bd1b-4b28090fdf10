package com.huitu.cloud.api.ew.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.ext.EwRiverWarningSending;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwRiverWarningSendingDao {

    /**
     * 获取预警发送记录分页列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 发送记录分页列表
     */
    IPage<EwRiverWarningSending> getSendingPageList(IPage<EwRiverWarningSending> page, @Param("map") Map<String, Object> params);
}
