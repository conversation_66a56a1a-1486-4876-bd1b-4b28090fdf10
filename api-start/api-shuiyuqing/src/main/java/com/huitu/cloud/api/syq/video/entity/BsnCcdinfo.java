package com.huitu.cloud.api.syq.video.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 视频设备信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-26
 */
@ApiModel(value="BsnCcdinfo对象", description="视频设备信息")
public class BsnCcdinfo implements Serializable {
    @ApiModelProperty(value = "摄像头编码")
    private String ccdCd;
    @ApiModelProperty(value = "摄像头名称")
    private String ccdNm;
    @ApiModelProperty(value = "视频设备ID")
    private String vdstCd;
    @ApiModelProperty(value = "通道号")
    private String ccdpsNo;
    @ApiModelProperty(value = "客户端接入服务器ID")
    private String clsvId;
    @ApiModelProperty(value = "设备端接入服务器ID")
    private String eqsvId;

    @ApiModelProperty(value = "sSupDeviceId 编码设备设备id")
    private String sdid;

    @ApiModelProperty(value = "sMsDeviceId 客户端中间件设备id")
    private String mdid;

    @ApiModelProperty(value = "sDevSupDeviceId 设备所属中间件设备id")
    private String dsdid;

    @ApiModelProperty(value = "sMsIp 客户端中间件ip")
    private String csip;

    @ApiModelProperty(value = "iMsWsPort 客户端中间件webSocket端口")
    private String csport;

    @ApiModelProperty(value = "sCameraId 视频通道数据主键")
    private String scid;

    @ApiModelProperty(value = "站址")
    private String stlc;

    @ApiModelProperty(value = "管理单位")
    private String admauth;

    @ApiModelProperty(value = "建站时间")
    private String createTm;

    @ApiModelProperty(value = "改造时间")
    private String transforTm;

    @ApiModelProperty(value = "供电系统")
    private String acsys;

    @ApiModelProperty(value = "通讯方式")
    private String comway;

    @ApiModelProperty(value = "照片路径")
    private String picurl;

    @ApiModelProperty(value = "摄像头是否展示")
    private String disflg;
    @ApiModelProperty(value = "主/副码流")
    private String otflg;

    public String getCcdCd() {
        return ccdCd;
    }

    public void setCcdCd(String ccdCd) {
        this.ccdCd = ccdCd;
    }

    public String getCcdNm() {
        return ccdNm;
    }

    public void setCcdNm(String ccdNm) {
        this.ccdNm = ccdNm;
    }

    public String getVdstCd() {
        return vdstCd;
    }

    public void setVdstCd(String vdstCd) {
        this.vdstCd = vdstCd;
    }

    public String getCcdpsNo() {
        return ccdpsNo;
    }

    public void setCcdpsNo(String ccdpsNo) {
        this.ccdpsNo = ccdpsNo;
    }

    public String getClsvId() {
        return clsvId;
    }

    public void setClsvId(String clsvId) {
        this.clsvId = clsvId;
    }

    public String getEqsvId() {
        return eqsvId;
    }

    public void setEqsvId(String eqsvId) {
        this.eqsvId = eqsvId;
    }

    public String getScid() {
        return scid;
    }

    public void setScid(String scid) {
        this.scid = scid;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getAdmauth() {
        return admauth;
    }

    public void setAdmauth(String admauth) {
        this.admauth = admauth;
    }

    public String getCreateTm() {
        return createTm;
    }

    public void setCreateTm(String createTm) {
        this.createTm = createTm;
    }

    public String getTransforTm() {
        return transforTm;
    }

    public void setTransforTm(String transforTm) {
        this.transforTm = transforTm;
    }

    public String getAcsys() {
        return acsys;
    }

    public void setAcsys(String acsys) {
        this.acsys = acsys;
    }

    public String getComway() {
        return comway;
    }

    public void setComway(String comway) {
        this.comway = comway;
    }

    public String getPicurl() {
        return picurl;
    }

    public void setPicurl(String picurl) {
        this.picurl = picurl;
    }

    public String getDisflg() {
        return disflg;
    }

    public void setDisflg(String disflg) {
        this.disflg = disflg;
    }

    public String getOtflg() {
        return otflg;
    }

    public void setOtflg(String otflg) {
        this.otflg = otflg;
    }

    public String getSdid() {
        return sdid;
    }

    public void setSdid(String sdid) {
        this.sdid = sdid;
    }

    public String getMdid() {
        return mdid;
    }

    public void setMdid(String mdid) {
        this.mdid = mdid;
    }

    public String getDsdid() {
        return dsdid;
    }

    public void setDsdid(String dsdid) {
        this.dsdid = dsdid;
    }

    public String getCsip() {
        return csip;
    }

    public void setCsip(String csip) {
        this.csip = csip;
    }

    public String getCsport() {
        return csport;
    }

    public void setCsport(String csport) {
        this.csport = csport;
    }
}
