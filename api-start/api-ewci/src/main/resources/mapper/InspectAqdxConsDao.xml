<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.inspect.mapper.InspectAqdxConsDao">

    <select id="getInspectAqdxConsList"
            resultType="com.huitu.cloud.api.ewci.inspect.entity.BnsInspectAqdxCons">
        SELECT *
        FROM BNS_INSPECT_AQDX_CONS
        WHERE ADCD = #{adcd} AND [YEAR] = #{year}
          AND EN_CODE = #{enCode}
    </select>
    <select id="selectYearByInspectId" resultType="java.util.Date">
        select INSP_DATE FROM BNS_INSPECT_AQDX_CONS  WHERE INSPECT_ID = #{inspectId}
    </select>
    <insert id="insertInspectAqdxCons">
        INSERT INTO BNS_INSPECT_AQDX_CONS(INSPECT_ID,
                                          ADCD,
                                          INSPECTOR,
                                          GROUPID,
                                          INSP_DATE,
                                          EXIST_PROBLEMS,
                                          EN_CODE,
                                          REC_ASK,
                                          REC_DATE,
                                          YEAR,
                                          LGTD,
                                          LTTD, CREATOR)
        VALUES ( #{inspectId}
               , #{adcd}
               , #{inspector}
               , #{groupid}
               , #{inspDate}
               , #{existProblems}
               , #{enCode}
               , #{recAsk}
               , #{recDate}
               , #{year}
               , #{lgtd}
               , #{lttd}, #{creator})
    </insert>
    <update id="updateInspectAqdxCons">
        UPDATE BNS_INSPECT_AQDX_CONS
        <trim prefix="set" suffixOverrides=",">
            <if test="adcd != null">ADCD = #{adcd},</if>
            <if test="inspector != null">INSPECTOR = #{inspector},</if>
            <if test="groupid != null">GROUPID = #{groupid},</if>
            <if test="inspDate != null">INSP_DATE = #{inspDate},</if>
            <if test="enCode != null">EN_CODE = #{enCode},</if>
            <if test="existProblems != null">EXIST_PROBLEMS = #{existProblems},</if>
            <if test="recAsk != null">REC_ASK = #{recAsk},</if>
            <if test="recDate != null">REC_DATE = #{recDate},</if>
            <if test="year != null">[YEAR] = #{year},</if>
            <if test="lgtd != null">LGTD = #{lgtd},</if>
            <if test="lttd != null">LTTD = #{lttd},</if>
        </trim>
        WHERE INSPECT_ID=#{inspectId} AND CREATOR = #{creator}
    </update>
    <delete id="deleteInspectAqdxCons">
        DELETE
        FROM BNS_INSPECT_AQDX_CONS
        WHERE INSPECT_ID = #{inspectId}
    </delete>

</mapper>