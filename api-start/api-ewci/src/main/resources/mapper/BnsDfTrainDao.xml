<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.base.mapper.BnsDfTrainDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="insert">
        INSERT INTO BNS_DF_TRAIN (TITLE, FILEPATH,FILENAME, STATUS,REMARK)
        VALUES (#{title}, #{filepath}, #{filename},#{status}, #{remark})
    </insert>
    <update id="update">
        UPDATE BNS_DF_TRAIN
        <set>
            <if test="title != null and title !=''">
                TITLE = #{title},
            </if>
            <if test="filepath != null and filepath !=''">
                FILEPATH = #{filepath},
            </if>
            <if test="filename != null and filename !=''">
                FILENAME = #{filename},
            </if>
            <if test="status != null and status !=''">
                STATUS = #{status},
            </if>
            <if test="remark != null and remark !=''">
                REMARK = #{remark},
            </if>
        </set>
        WHERE ID = #{id}
    </update>
    <delete id="delete">
        delete
        from BNS_DF_TRAIN
        where ID = #{id}
    </delete>
    <select id="getList" resultType="com.huitu.cloud.api.ewci.base.entity.BnsDfTrain">
        SELECT ID,TITLE,FILEPATH,FILENAME,REMARK,STATUS,TS FROM BNS_DF_TRAIN
        <where>
            <if test="map.stm != null and map.stm !=''">
                and TS>CONVERT(datetime,#{map.stm})
            </if>
            <if test="map.etm != null and map.etm !=''">
                and TS &lt;= CONVERT(datetime,#{map.etm})
            </if>
            <if test="map.title !=null and map.title !=''">
                AND CHARINDEX(#{map.title},TITLE) >0
            </if>
        </where>
        ORDER BY TS DESC
    </select>
    <select id="getDfTrainList" resultType="com.huitu.cloud.api.ewci.base.entity.BnsDfTrain">
        SELECT
            ID,
            TITLE,
            FILEPATH,
            FILENAME,
            REMARK,
            STATUS,
            TS
        FROM
            BNS_DF_TRAIN
        WHERE
            STATUS = 1
        ORDER BY
            TS DESC
    </select>
</mapper>