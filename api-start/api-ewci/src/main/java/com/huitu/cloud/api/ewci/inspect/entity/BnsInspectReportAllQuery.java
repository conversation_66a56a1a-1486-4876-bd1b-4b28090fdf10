package com.huitu.cloud.api.ewci.inspect.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 检查报告参数
 */
@Data
@ApiModel(value = "检查上报参数")
public class BnsInspectReportAllQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private String reportId;

    @ApiModelProperty(value = "年度")
    @NotBlank(message = "参数[年度]不能为空")
    private String year;

    @ApiModelProperty(value = "政区编码")
    @NotBlank(message = "参数[政区编码]不能为空")
    private String adcd;

    public Map<String, Object> toQueryParam() {
        Map<String, Object> params = new HashMap<>();
        params.put("year", getYear());
        params.put("adcd", getAdcd());
        return params;
    }
}
