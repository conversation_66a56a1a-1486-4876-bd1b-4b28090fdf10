package com.huitu.cloud.api.screen.fhjz.mapper;


import com.huitu.cloud.api.screen.fhjz.entity.ReservoirVo;
import com.huitu.cloud.api.screen.fhjz.entity.RsvrDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-19
 */
public interface ReservoirDao {

    List<ReservoirVo> getRsvrLatestData(@Param("stm") String stm, @Param("etm")String etm, @Param("engScal") String engScal,@Param("cxxOrder") boolean cxxOrder,@Param("resName") String resName);

    List<RsvrDetailVo> detailList(@Param("stm") String stm, @Param("etm")String etm, @Param("engScal") String engScal,@Param("resName") String resName,@Param("adcd") String adcd,@Param("adLevl") int adLevl);

    List<Map> querySkStationList(@Param("stm")String stm, @Param("etm") String etm,@Param("engScal") String engScal,@Param("resCode") String resCode);

    Map<String, Object> querySkAdcd(@Param("resCode") String resCode);

    List<Map> statisticRsvrDetail(@Param("adcd") String adcd, @Param("level") int level, @Param("lowLevel") int lowLevel,@Param("suffix") String suffix);
}
