package com.huitu.cloud.api.datamanager.ststop.controler;


import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.datamanager.ststop.entity.StStopQo;
import com.huitu.cloud.api.datamanager.ststop.entity.StstopVo;
import com.huitu.cloud.api.datamanager.ststop.service.StstopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 站点屏蔽记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */

@RestController
@Api(tags = "屏蔽站点维护")
@RequestMapping("/api/datamanger/ststop")
public class StstopResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "6B670F9D-9F0E-4F51-AF9A-DC278B1F02EB";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private StstopService baseService;

    @ApiOperation(value = "关闭屏蔽记录", notes = "关闭屏蔽记录")
    @GetMapping(value = "close")
    @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String")
    public ResponseEntity<SuccessResponse<String>> closeBsnStstopr(@RequestParam String stcd) {
        String flag = baseService.closeStStop(stcd);
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", flag));
    }

    @ApiOperation(value = "根据要素关闭屏蔽记录", notes = "根据要素关闭屏蔽记录")
    @GetMapping(value = "close-by-tbnm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tbnm", value = "要素编码", required = true, dataType = "String")
    })

    public ResponseEntity<SuccessResponse<String>> closeBsnStstoprByTbnm(@RequestParam String stcd, @RequestParam String tbnm) {
        String flag = baseService.closeStStopByTbnm(stcd, tbnm);
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", flag));
    }

    @ApiOperation(value = "屏蔽站点记录添加", notes = "屏蔽站点记录添加")
    @PostMapping(value = "add")
    public ResponseEntity<SuccessResponse<String>> addStstop(@RequestBody StStopQo entity) {
        String flag = baseService.saveStStop(entity);
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", flag));
    }

    @ApiOperation(value = "屏蔽站点记录查询", notes = "屏蔽站点记录查询")
    @GetMapping(value = "select-record")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isAll", value = "是否查询所有（包含未来的计划性屏蔽）， 1：是 0:否", dataType = "String"),
            @ApiImplicitParam(name = "isShow", value = "是否展示0-显示 1-不显示， 1：不显示 0:显示", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<StstopVo>>> getStStopInfos(@RequestParam String adcd, @RequestParam(required = false) String isAll, @RequestParam(required = false) String isShow) {
        List<StstopVo> list = baseService.getStStopInfos(adcd, isAll, isShow);
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", list));
    }

    @ApiOperation(value = "根据测站编码与要素校验是否存在屏蔽记录", notes = "根据测站编码与要素校验是否存在屏蔽记录 （返回值 1：存在 0：不存在）")
    @GetMapping(value = "check-by-stcd-tbnm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tbnm", value = "要素编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<String>> checkStStop(@RequestParam String stcd, @RequestParam String tbnm) {
        String flag = baseService.checkStStopByTbnmAndStcd(stcd, tbnm);
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", flag));
    }
}







