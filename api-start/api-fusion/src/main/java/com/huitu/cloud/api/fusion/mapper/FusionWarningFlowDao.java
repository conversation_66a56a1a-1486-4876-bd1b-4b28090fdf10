package com.huitu.cloud.api.fusion.mapper;

import com.huitu.cloud.api.fusion.entity.response.FusionWarningFlowResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FusionWarningFlowDao {

    /**
     * 人工确认
     *
     * @param confirmBy 创建人
     * @param warnIds   预警ID集合
     * @return 受影响的行数
     */
    int manualConfirm(@Param("confirmBy") String confirmBy, @Param("warnIds") List<String> warnIds);

    /**
     * 根据预警ID，获取预警流程列表
     *
     * @param warnId 预警ID
     * @return 流程列表
     */
    List<FusionWarningFlowResp> getFlowList(String warnId);
}
