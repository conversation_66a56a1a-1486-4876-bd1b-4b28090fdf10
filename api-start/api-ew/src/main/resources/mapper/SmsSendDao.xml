<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.SmsSendDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="saveMessage">
        INSERT INTO SMS_MESSAGE(MSG_ID, BUSINESS_KEY, SMS_TYPE, SIGN_NAME, TEMPLATE_CODE, SMS_CONTENT, SEND_TIME, SENDER, UNIT_CODE,
                                UNIT_NAME, LATEST_TIME, ACCESS_KEY_ID, PLATFORM_ID)
        VALUES (#{msgId}, #{businessKey}, #{smsType}, #{signName}, #{templateCode}, #{smsContent}, GETDATE(), #{sender}, #{unitCode},
                #{unitName}, GETDATE(), #{accessKeyId}, #{platformId})
    </insert>
    <insert id="saveReceivers">
        INSERT INTO SMS_SENDING(SEND_ID, MSG_ID, PHONE_NO, RECEIVER, MSG_EXTEND, [STATUS], TASK_ID, LATEST_TIME, PLATFORM_ID)
        SELECT SEND_ID, MSG_ID, PHONE_NO, RECEIVER, MSG_EXTEND, [STATUS], TASK_ID, LATEST_TIME, PLATFORM_ID FROM (VALUES
        <foreach collection="receivers" item="item" separator=",">
            (NEWID(), #{msgId}, #{item.phoneNo}, #{item.receiver}, #{item.msgExtend}, '0', 0, GETDATE(), #{platformId})
        </foreach>) AS T(SEND_ID, MSG_ID, PHONE_NO, RECEIVER, MSG_EXTEND, [STATUS], TASK_ID, LATEST_TIME, PLATFORM_ID)
    </insert>
    <select id="getSignName" resultType="java.lang.String">
        SELECT ISNULL(SMS_SIGN_NAME, #{defaultSignName})
        FROM EW_WARNING_CONF
        WHERE PLATFORM_ID = #{platformId}
    </select>
</mapper>