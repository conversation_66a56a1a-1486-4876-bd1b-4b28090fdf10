package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@TableName("EW_RESER")
@ApiModel(value="EwReser对象", description="")
public class EwReser extends Model<EwReser> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "预报时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "水库编码")
    @TableField("RSCD")
    private String rscd;

    @ApiModelProperty(value = "预警时间")
    @TableField("WARN_TM")
    private LocalDateTime warnTm;

    @ApiModelProperty(value = "预警级别")
    @TableField("LEVELH")
    private Double levelh;

    @ApiModelProperty(value = "预警水位值")
    @TableField("H")
    private Double h;

    @ApiModelProperty(value = "预警水位指标")
    @TableField("WARNH")
    private Double warnh;

    @TableField("QTLN")
    private Double qtln;

    @TableField("QILN")
    private Double qiln;


    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getRscd() {
        return rscd;
    }

    public void setRscd(String rscd) {
        this.rscd = rscd;
    }

    public LocalDateTime getWarnTm() {
        return warnTm;
    }

    public void setWarnTm(LocalDateTime warnTm) {
        this.warnTm = warnTm;
    }

    public Double getLevelh() {
        return levelh;
    }

    public void setLevelh(Double levelh) {
        this.levelh = levelh;
    }

    public Double getH() {
        return h;
    }

    public void setH(Double h) {
        this.h = h;
    }

    public Double getWarnh() {
        return warnh;
    }

    public void setWarnh(Double warnh) {
        this.warnh = warnh;
    }

    public Double getQtln() {
        return qtln;
    }

    public void setQtln(Double qtln) {
        this.qtln = qtln;
    }

    public Double getQiln() {
        return qiln;
    }

    public void setQiln(Double qiln) {
        this.qiln = qiln;
    }

    @Override
    protected Serializable pkVal() {
        return this.tm;
    }

    @Override
    public String toString() {
        return "EwReser{" +
        "tm=" + tm +
        ", rscd=" + rscd +
        ", warnTm=" + warnTm +
        ", levelh=" + levelh +
        ", h=" + h +
        ", warnh=" + warnh +
        ", qtln=" + qtln +
        ", qiln=" + qiln +
        "}";
    }
}
