package com.huitu.cloud.api.common.liuyu.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-09
 */
@TableName("BSN_BAS_B")
@ApiModel(value="BsnBasB对象", description="")
public class BsnBasB extends Model<BsnBasB> {

    private static final long serialVersionUID=1L;

    @TableField("BAS_NAME")
    private String basName;

    @TableField("PBAS_CODE")
    private String pbasCode;

    @TableField("BAS_CODE")
    private String basCode;

    @TableField("BAS_LEVEL")
    private Integer basLevel;

    @TableField("IS_ZYJH")
    private String isZyjh;

    @TableField("IS_ZXHL")
    private String isZxhl;

    @ApiModelProperty(value = "下级流域")
    private List<BsnBasB> children;

    private boolean disabled;

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public List<BsnBasB> getChildren() {
        return children;
    }

    public void setChildren(List<BsnBasB> children) {
        this.children = children;
    }

    public String getBasName() {
        return basName;
    }

    public void setBasName(String basName) {
        this.basName = basName;
    }

    public String getPbasCode() {
        return pbasCode;
    }

    public void setPbasCode(String pbasCode) {
        this.pbasCode = pbasCode;
    }

    public String getBasCode() {
        return basCode;
    }

    public void setBasCode(String basCode) {
        this.basCode = basCode;
    }

    public Integer getBasLevel() {
        return basLevel;
    }

    public void setBasLevel(Integer basLevel) {
        this.basLevel = basLevel;
    }

    public String getIsZyjh() {
        return isZyjh;
    }

    public void setIsZyjh(String isZyjh) {
        this.isZyjh = isZyjh;
    }

    public String getIsZxhl() {
        return isZxhl;
    }

    public void setIsZxhl(String isZxhl) {
        this.isZxhl = isZxhl;
    }

    @Override
    protected Serializable pkVal() {
        return null;
    }

    @Override
    public String toString() {
        return "BsnBasB{" +
        "basName=" + basName +
        ", pbasCode=" + pbasCode +
        ", basCode=" + basCode +
        ", basLevel=" + basLevel +
        "}";
    }
}
