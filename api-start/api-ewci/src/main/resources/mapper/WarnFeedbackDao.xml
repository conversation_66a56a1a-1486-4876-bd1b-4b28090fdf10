<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.WarnFeedbackDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getFeedbackByShortId" resultType="com.huitu.cloud.api.ewci.warn.entity.WarnCallFeedback">
        SELECT WARN_ID,WARN_TYPE, MOBILE_NO, SHORT_ID, STATUS, CREATE_TIME, CONFIRM_TIME
        FROM WARN_CALL_FEEDBACK WHERE SHORT_ID = #{shortId}
    </select>

    <update id="updateFeedback">
        UPDATE WARN_CALL_FEEDBACK
        SET STATUS       = #{status},
            CONFIRM_TIME = #{confirmTime},
            TS = GETDATE()
        WHERE SHORT_ID = #{shortId}
    </update>

    <select id="getFeedbackStatusBar" resultType="com.huitu.cloud.api.ewci.warn.entity.WarnCallFeedbackBarVo">
        SELECT A.ADNM,A.ADCD,ISNULL(C.YSTATUS,0) YSTATUS,ISNULL(C.NSTATUS,0) NSTATUS FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (
        SELECT A.ADCD, SUM(A.YSTATUS) YSTATUS, SUM(A.NSTATUS) NSTATUS FROM
        (select LEFT (LEFT (B.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD,
        CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS,
        CASE WHEN A.STATUS = '0' THEN 1 ELSE 0 END NSTATUS, CREATE_TIME
        FROM WARN_CALL_FEEDBACK A LEFT JOIN (
        SELECT WARNID, ADCD, WARNSTM WTM FROM WARNRECORD_R WHERE WARNTYPEID = 10
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
        ) B ON A.WARN_ID = B.WARNID
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        AND B.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND B.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS,
            CASE WHEN A.STATUS = '0' THEN 1 ELSE 0 END NSTATUS, CREATE_TIME
            FROM WARN_CALL_FEEDBACK A LEFT JOIN (
            SELECT WARNID, ADCD, WARNSTM WTM FROM WARNRECORD_R WHERE WARNTYPEID = 10
            UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
            UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
            ) B ON A.WARN_ID = B.WARNID
            WHERE LEFT(B.ADCD, 6) = '220581' AND B.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND B.WTM &lt; #{map.etm}
            </if>
        </if>
        ) A GROUP BY A.ADCD) C ON A.ADCD = C.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getShFeedbackStatusBar"
            resultType="com.huitu.cloud.api.ewci.warn.entity.WarnCallFeedbackBarVo">
        SELECT A.ADNM,A.ADCD,B.SORTNO,ISNULL(C.YSTATUS,0) YSTATUS,ISNULL(C.NSTATUS,0) NSTATUS FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (
        SELECT A.ADCD, SUM(A.YSTATUS) YSTATUS, SUM(A.NSTATUS) NSTATUS FROM
        (select LEFT (LEFT (B.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD,
        CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS,
        CASE WHEN A.STATUS = '0' THEN 1 ELSE 0 END NSTATUS, CREATE_TIME
        FROM EW_CALL_FEEDBACK A
        LEFT JOIN EW_WARNING_MESSAGE C ON A.SMS_WARN_MSG_ID = C.MSG_ID AND C.PUSH_MODE = '1' AND A.CALL_TYPE = '1'
        LEFT JOIN (
        SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD WHERE WARN_TYPE_ID = 10
        ) B ON C.WARN_ID = B.WARNID
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        AND B.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND B.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS,
            CASE WHEN A.STATUS = '0' THEN 1 ELSE 0 END NSTATUS, CREATE_TIME
            FROM EW_CALL_FEEDBACK A
            LEFT JOIN EW_WARNING_MESSAGE C ON A.SMS_WARN_MSG_ID = C.MSG_ID AND C.PUSH_MODE = '1' AND A.CALL_TYPE = '1'
            LEFT JOIN (
            SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD WHERE WARN_TYPE_ID = 10
            ) B ON C.WARN_ID = B.WARNID
            WHERE LEFT(B.ADCD, 6) = '220581' AND B.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND B.WTM &lt; #{map.etm}
            </if>
        </if>
        ) A GROUP BY A.ADCD) C ON A.ADCD = C.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>

    <select id="getRiverFeedbackStatusBar"
            resultType="com.huitu.cloud.api.ewci.warn.entity.WarnCallFeedbackBarVo">
        SELECT A.ADNM,A.ADCD,ISNULL(C.YSTATUS,0) YSTATUS,ISNULL(C.NSTATUS,0) NSTATUS FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (
        SELECT A.ADCD, SUM(A.YSTATUS) YSTATUS, SUM(A.NSTATUS) NSTATUS FROM
        (select LEFT (LEFT (B.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD,
        CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS,
        CASE WHEN A.STATUS = '0' THEN 1 ELSE 0 END NSTATUS, CREATE_TIME
        FROM EW_CALL_FEEDBACK A
        LEFT JOIN EW_RIVER_WARNING_MESSAGE C ON A.SMS_WARN_MSG_ID = C.MSG_ID AND C.PUSH_MODE = '1' AND A.CALL_TYPE = '2'
        LEFT JOIN (
        SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND B.STADTP IN ('1', '2')
        ) B ON C.WARN_ID = B.WARNID
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        AND B.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND B.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS,
            CASE WHEN A.STATUS = '0' THEN 1 ELSE 0 END NSTATUS, CREATE_TIME
            FROM EW_CALL_FEEDBACK A
            LEFT JOIN EW_RIVER_WARNING_MESSAGE C ON A.SMS_WARN_MSG_ID = C.MSG_ID AND C.PUSH_MODE = '1' AND A.CALL_TYPE = '2'
            LEFT JOIN (
            SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
            ) B ON C.WARN_ID = B.WARNID
            WHERE LEFT(B.ADCD, 6) = '220581' AND B.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND B.WTM &lt; #{map.etm}
            </if>
        </if>
        ) A GROUP BY A.ADCD) C ON A.ADCD = C.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>

    <select id="getRsvrFeedbackStatusBar"
            resultType="com.huitu.cloud.api.ewci.warn.entity.WarnCallFeedbackBarVo">
        SELECT A.ADNM,A.ADCD,ISNULL(C.YSTATUS,0) YSTATUS,ISNULL(C.NSTATUS,0) NSTATUS FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (
        SELECT A.ADCD, SUM(A.YSTATUS) YSTATUS, SUM(A.NSTATUS) NSTATUS FROM
        (select LEFT (LEFT (B.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD,
        CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS,
        CASE WHEN A.STATUS = '0' THEN 1 ELSE 0 END NSTATUS, CREATE_TIME
        FROM EW_CALL_FEEDBACK A
        LEFT JOIN EW_RSVR_WARNING_MESSAGE C ON A.SMS_WARN_MSG_ID = C.MSG_ID AND C.PUSH_MODE = '1' AND A.CALL_TYPE = '3'
        LEFT JOIN (
        SELECT WARN_ID WARNID, C.ADCD, WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
        LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
        WHERE C.STADTP IN ('1', '2', '4') AND H.ENG_SCAL IN ('1', '2', '3', '4', '5')
        ) B ON C.WARN_ID = B.WARNID
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        AND B.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND B.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS,
            CASE WHEN A.STATUS = '0' THEN 1 ELSE 0 END NSTATUS, CREATE_TIME
            FROM EW_CALL_FEEDBACK A
            LEFT JOIN EW_RSVR_WARNING_MESSAGE C ON A.SMS_WARN_MSG_ID = C.MSG_ID AND C.PUSH_MODE = '1' AND A.CALL_TYPE = '3'
            LEFT JOIN (
            SELECT WARN_ID WARNID, C.ADCD, WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
            LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
            LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
            LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
            LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
            LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
            LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
            WHERE C.STADTP IN ('1', '2', '4') AND H.ENG_SCAL IN ('1', '2', '3', '4', '5')
            ) B ON C.WARN_ID = B.WARNID
            WHERE LEFT(B.ADCD, 6) = '220581' AND B.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND B.WTM &lt; #{map.etm}
            </if>
        </if>
        ) A GROUP BY A.ADCD) C ON A.ADCD = C.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>

    <select id="getFeedbackStatusGauge" resultType="com.huitu.cloud.api.ewci.warn.entity.WarnCallFeedbackGaugeVo">
        SELECT COUNT(A.WARN_ID) FEEDBACKSUM, ISNULL(SUM(A.YSTATUS),0) YSTATUSSUM
        FROM (SELECT A.WARN_ID WARN_ID, CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS
        FROM WARN_CALL_FEEDBACK A LEFT JOIN (
        SELECT WARNID, ADCD, WARNSTM WTM FROM WARNRECORD_R WHERE WARNTYPEID = 10
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
        ) B ON A.WARN_ID = B.WARNID
        WHERE LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        AND B.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND B.WTM &lt; #{map.etm}
        </if>) A
    </select>
    <select id="getShFeedbackStatusGauge"
            resultType="com.huitu.cloud.api.ewci.warn.entity.WarnCallFeedbackGaugeVo">
        SELECT COUNT(A.CALL_ID) FEEDBACKSUM, ISNULL(SUM(A.YSTATUS),0) YSTATUSSUM
        FROM (SELECT A.CALL_ID CALL_ID, CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS
        FROM EW_CALL_FEEDBACK A
        LEFT JOIN EW_WARNING_MESSAGE B ON A.SMS_WARN_MSG_ID = B.MSG_ID AND B.PUSH_MODE = '1'
        LEFT JOIN (
        SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD WHERE WARN_TYPE_ID = 10
        ) C ON B.WARN_ID = C.WARNID
        WHERE LEFT(C.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND A.CALL_TYPE = '1'
        <if test="map.level == '4'.toString()">
            AND LEFT(C.ADCD, 6) NOT IN ('220581')
        </if>
        AND C.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND C.WTM &lt; #{map.etm}
        </if>) A
    </select>
    <select id="getRiverFeedbackStatusGauge"
            resultType="com.huitu.cloud.api.ewci.warn.entity.WarnCallFeedbackGaugeVo">
        SELECT COUNT(A.CALL_ID) FEEDBACKSUM, ISNULL(SUM(A.YSTATUS),0) YSTATUSSUM
        FROM (SELECT A.CALL_ID CALL_ID, CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS
        FROM EW_CALL_FEEDBACK A
        LEFT JOIN EW_RIVER_WARNING_MESSAGE B ON A.SMS_WARN_MSG_ID = B.MSG_ID AND B.PUSH_MODE = '1'
        LEFT JOIN (
        SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND B.STADTP IN ('1', '2')
        ) C ON B.WARN_ID = C.WARNID
        WHERE LEFT(C.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND A.CALL_TYPE = '2'
        <if test="map.level == '4'.toString()">
            AND LEFT(C.ADCD, 6) NOT IN ('220581')
        </if>
        AND C.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND C.WTM &lt; #{map.etm}
        </if>) A
    </select>
    <select id="getRsvrFeedbackStatusGauge"
            resultType="com.huitu.cloud.api.ewci.warn.entity.WarnCallFeedbackGaugeVo">
        SELECT COUNT(A.CALL_ID) FEEDBACKSUM, ISNULL(SUM(A.YSTATUS),0) YSTATUSSUM
        FROM (SELECT A.CALL_ID CALL_ID, CASE WHEN A.STATUS = '1' THEN 1 ELSE 0 END YSTATUS
        FROM EW_CALL_FEEDBACK A
        LEFT JOIN EW_RSVR_WARNING_MESSAGE B ON A.SMS_WARN_MSG_ID = B.MSG_ID AND B.PUSH_MODE = '1'
        LEFT JOIN (
        SELECT WARN_ID WARNID, C.ADCD, WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
        LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
        WHERE C.STADTP IN ('1', '2', '4') AND H.ENG_SCAL IN ('1', '2', '3', '4', '5')
        ) C ON B.WARN_ID = C.WARNID
        WHERE LEFT(C.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND A.CALL_TYPE = '3'
        <if test="map.level == '4'.toString()">
            AND LEFT(C.ADCD, 6) NOT IN ('220581')
        </if>
        AND C.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND C.WTM &lt; #{map.etm}
        </if>) A
    </select>

</mapper>