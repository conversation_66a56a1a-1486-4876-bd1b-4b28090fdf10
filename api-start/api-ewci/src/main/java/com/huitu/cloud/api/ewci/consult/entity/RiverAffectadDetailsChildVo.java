package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 超标准洪水淹没范围信息 详细数据
 */
@ApiModel(value = "超标准洪水淹没范围信息详细数据")
public class RiverAffectadDetailsChildVo implements Serializable {

    @ApiModelProperty(value = "岸别")
    @TableField(value = "BANK")
    private String bank;

    @ApiModelProperty(value = "乡镇政区编码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "乡镇政区名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "县市政区编码")
    @TableField(value = "XADCD")
    private String xadcd;

    @ApiModelProperty(value = "县市政区名称")
    @TableField(value = "XADNM")
    private String xadnm;

    @ApiModelProperty(value = "行政村数量")
    @TableField(value = "XZC_NUM")
    private Integer xzcNum;

    @ApiModelProperty(value = "自然屯数量")
    @TableField(value = "ZRT_NUM")
    private Integer zrtNum;

    @ApiModelProperty(value = "户数")
    @TableField(value = "HTCOUNT")
    private Integer htcount;

    @ApiModelProperty(value = "人口")
    @TableField(value = "PTCOUNT")
    private Integer ptcount;


    @ApiModelProperty(value = "乡镇政区编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "乡镇政区编码")
    @TableField(value = "RV_NAME")
    private String rvName;


    @ApiModelProperty(value = "县")
    @TableField(value = "XADCD_NUM")
    private Integer xadcdNum;

    @ApiModelProperty(value = "乡镇")
    @TableField(value = "ADCD_NUM")
    private Integer adcdNum;

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public Integer getXzcNum() {
        return xzcNum;
    }

    public void setXzcNum(Integer xzcNum) {
        this.xzcNum = xzcNum;
    }

    public Integer getZrtNum() {
        return zrtNum;
    }

    public void setZrtNum(Integer zrtNum) {
        this.zrtNum = zrtNum;
    }

    public Integer getHtcount() {
        return htcount;
    }

    public void setHtcount(Integer htcount) {
        this.htcount = htcount;
    }

    public Integer getPtcount() {
        return ptcount;
    }

    public void setPtcount(Integer ptcount) {
        this.ptcount = ptcount;
    }

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public Integer getXadcdNum() {
        return xadcdNum;
    }

    public void setXadcdNum(Integer xadcdNum) {
        this.xadcdNum = xadcdNum;
    }

    public Integer getAdcdNum() {
        return adcdNum;
    }

    public void setAdcdNum(Integer adcdNum) {
        this.adcdNum = adcdNum;
    }
}
