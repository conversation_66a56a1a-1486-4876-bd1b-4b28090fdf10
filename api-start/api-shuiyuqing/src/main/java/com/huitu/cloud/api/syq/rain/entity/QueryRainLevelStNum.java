package com.huitu.cloud.api.syq.rain.entity;

import com.huitu.cloud.validation.constraints.Option;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 */

public class QueryRainLevelStNum {

    @ApiModelProperty(value = "测站归属类型 1、水文 2、山洪 3、气象 6、运管")
    @Option(value = {"1", "2", "3", "6"}, message = "参数[测站归属类型]的值无效")
    private List<String> stType;
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true)
    private String etm;
    @ApiModelProperty(value = "政区编码")
    @SqlInjection
    private String adcd;
    @ApiModelProperty(value = "降雨级别0")
    private int rainRule0;
    @ApiModelProperty(value = "降雨级别10")
    private int rainRule1;
    @ApiModelProperty(value = "降雨级别25")
    private int rainRule2;
    @ApiModelProperty(value = "降雨级别50")
    private int rainRule3;
    @ApiModelProperty(value = "降雨级别100")
    private int rainRule4;
    @ApiModelProperty(value = "降雨级别大于250")
    private int rainRule5;

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public int getRainRule0() {
        return rainRule0;
    }

    public void setRainRule0(int rainRule0) {
        this.rainRule0 = rainRule0;
    }

    public int getRainRule1() {
        return rainRule1;
    }

    public void setRainRule1(int rainRule1) {
        this.rainRule1 = rainRule1;
    }

    public int getRainRule2() {
        return rainRule2;
    }

    public void setRainRule2(int rainRule2) {
        this.rainRule2 = rainRule2;
    }

    public int getRainRule3() {
        return rainRule3;
    }

    public void setRainRule3(int rainRule3) {
        this.rainRule3 = rainRule3;
    }

    public int getRainRule4() {
        return rainRule4;
    }

    public void setRainRule4(int rainRule4) {
        this.rainRule4 = rainRule4;
    }

    public int getRainRule5() {
        return rainRule5;
    }

    public void setRainRule5(int rainRule5) {
        this.rainRule5 = rainRule5;
    }
}
