package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 水库图层
 */
@ApiModel(value = "水库图层")
public class BnsRiverBas implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "RV_NAME")
    private String rvName;

    @ApiModelProperty(value = "测站方位")
    @TableField(value = "LEFT_UP_LGTD")
    private Double leftUpLgtd;

    @ApiModelProperty(value = "测站类型")
    @TableField(value = "LEFT_UP_LTTD")
    private Double leftUpLttd;

    @ApiModelProperty(value = "纬度")
    @TableField(value = "LEFT_DOWN_LGTD")
    private Double leftDownLgtd;

    @ApiModelProperty(value = "经度")
    @TableField(value = "LEFT_DOWN_LTTD")
    private Double leftDownLttd;

    @ApiModelProperty(value = "警戒流量")
    @TableField(value = "RIGHT_UP_LGTD")
    private Double rightUpLgtd;

    @ApiModelProperty(value = "警戒水位")
    @TableField(value = "RIGHT_UP_LTTD")
    private Double rightUpLttd;

    @ApiModelProperty(value = "警戒水位")
    @TableField(value = "RIGHT_DOWN_LGTD")
    private Double rightDownLgtd;

    @ApiModelProperty(value = "保证流量")
    @TableField(value = "RIGHT_DOWN_LTTD")
    private Double rightDownLttd;

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public Double getLeftUpLgtd() {
        return leftUpLgtd;
    }

    public void setLeftUpLgtd(Double leftUpLgtd) {
        this.leftUpLgtd = leftUpLgtd;
    }

    public Double getLeftUpLttd() {
        return leftUpLttd;
    }

    public void setLeftUpLttd(Double leftUpLttd) {
        this.leftUpLttd = leftUpLttd;
    }

    public Double getLeftDownLgtd() {
        return leftDownLgtd;
    }

    public void setLeftDownLgtd(Double leftDownLgtd) {
        this.leftDownLgtd = leftDownLgtd;
    }

    public Double getLeftDownLttd() {
        return leftDownLttd;
    }

    public void setLeftDownLttd(Double leftDownLttd) {
        this.leftDownLttd = leftDownLttd;
    }

    public Double getRightUpLgtd() {
        return rightUpLgtd;
    }

    public void setRightUpLgtd(Double rightUpLgtd) {
        this.rightUpLgtd = rightUpLgtd;
    }

    public Double getRightUpLttd() {
        return rightUpLttd;
    }

    public void setRightUpLttd(Double rightUpLttd) {
        this.rightUpLttd = rightUpLttd;
    }

    public Double getRightDownLgtd() {
        return rightDownLgtd;
    }

    public void setRightDownLgtd(Double rightDownLgtd) {
        this.rightDownLgtd = rightDownLgtd;
    }

    public Double getRightDownLttd() {
        return rightDownLttd;
    }

    public void setRightDownLttd(Double rightDownLttd) {
        this.rightDownLttd = rightDownLttd;
    }
}
