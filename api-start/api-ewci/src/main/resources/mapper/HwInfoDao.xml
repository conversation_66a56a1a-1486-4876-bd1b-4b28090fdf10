<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.device.mapper.HwInfoDao">

    <!-- 获取自动监测站正式列表 -->
    <select id="getHwInfoList" resultType="com.huitu.cloud.api.ewci.device.entity.HwInfo">
        SELECT
        st.STNM,
        st.STCD,
        st.ADDVCD,
        ad.ADNM,
        st.STTP,
        st.ADMAUTH,
        st.ESSTYM,
        st.FRGRD,
        hw.RTUBD,
        st.USFL,
        hw.COMMUNICATIONBD,
        hw.RAINBD,
        hw.RVBD,
        hw.SOLARENERGYAR,
        hw.BTTYEAR,
        hw.RTUYEAR,
        hw.RCSTAR,
        hw.RAINYEAR,
        hw.COMMUNICATIONAR,
        hw.CHARGECONTROLAR,
        hw.SPDAR,
        hw.CTNAR,
        hw.RVYEAR,
        hw.BASICRENOVATIONAR,
        hw.MODITIME,
        hw.BTTBD,
        ad.adcd,
        hw.rcstbd,
        st.esstym,
        h.status
        FROM
        (
        SELECT
        a.STCD,
        a.STNM,
        a.RVNM,
        a.HNNM,
        a.BSNM,
        a.LGTD,
        a.LTTD,
        a.STLC,
        a.ADDVCD,
        a.DTMNM,
        a.DTMEL,
        a.DTPR,
        a.STTP,
        a.FRGRD,
        a.ESSTYM,
        a.BGFRYM,
        a.ATCUNIT,
        a.ADMAUTH,
        a.LOCALITY,
        a.STBK,
        a.STAZT,
        a.DSTRVM,
        a.DRNA,
        a.PHCD,
        a.COMMENTS,
        a.MODITIME,
        1 AS usfl
        FROM
        ST_STBPRP_B a UNION ALL
        SELECT
        b.STCD,
        b.STNM,
        b.RVNM,
        b.HNNM,
        b.BSNM,
        b.LGTD,
        b.LTTD,
        b.STLC,
        b.ADDVCD,
        b.DTMNM,
        b.DTMEL,
        b.DTPR,
        b.STTP,
        b.FRGRD,
        b.ESSTYM,
        b.BGFRYM,
        b.ATCUNIT,
        b.ADMAUTH,
        b.LOCALITY,
        b.STBK,
        b.STAZT,
        b.DSTRVM,
        b.DRNA,
        b.PHCD,
        b.COMMENTS,
        b.MODITIME,
        0 AS usfl
        FROM
        ST_STBPRP_B_DIS b) st
        LEFT JOIN BNS_HWINFO_B hw ON st.STCD = hw.STCD
        LEFT JOIN BSN_ADCD_B ad ON st.addvcd+ '000000000' = ad.adcd
        LEFT JOIN BNS_HWINFO_B_HISTORY h ON h.STCD = hw.STCD
        AND ( h.status = '1' OR h.status = '2' )
        WHERE
        st.FRGRD = 5
        <if test="map.adcd != null and map.adcd !=''">
            and left(ad.adcd,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(ad.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.oLevel != '2'.toString()">
            AND (ST.ADMAUTH != '省水旱中心' OR ST.ADMAUTH IS NULL)
        </if>
        <if test="map.stnm!= null and map.stnm != '' ">
            and CHARINDEX(#{map.stnm},st.stnm) >0
        </if>
        <if test="map.sttps!=null and map.sttps!= ''">
            and st.STTP=#{map.sttps}
        </if>
        <if test="map.usfl!=null and map.usfl!= ''">
            and st.usfl=#{map.usfl}
        </if>
        <if test="map.admauth!=null and map.admauth!= ''">
            and CHARINDEX(#{map.admauth},st.admauth) >0
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '1'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.solarenergyar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '1'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.solarenergyar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '2'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.bttyear &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '2'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.bttyear &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '3'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.rtuyear &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '3'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.rtuyear &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '4'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.rainyear &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '4'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.rainyear &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '5'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.communicationar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '5'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.communicationar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '6'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.chargecontrolar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '6'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.chargecontrolar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '7'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.spdar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '7'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.spdar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '8'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.ctnar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '8'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.ctnar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '9'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.rvyear &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '9'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.rvyear &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '10'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.rcstar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '10'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.rcstar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '11'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.basicrenovationar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '11'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.basicrenovationar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        order by hw.MODITIME DESC, st.USFL DESC
    </select>

    <!-- 自动监测站站信息统计-->
    <select id="getHwCountList" resultType="com.huitu.cloud.api.ewci.device.entity.DeviceStatistics">
        SELECT
        AA.ADCD,
        AA.ADNM,
        COUNT ( BB.ADCD ) AS COUNT
        FROM
        ( SELECT A.ADCD, A.ADNM, A.padcd, A.adlvl FROM BSN_ADCD_B A WHERE
        LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        AND CHARINDEX(CAST(A.adlvl AS VARCHAR), #{map.adlvl}) > 0
        <if test="map.level !=null and map.level =='4'.toString()">
            and left(A.ADCD,6) != '220581'
        </if>
        ) AA
        LEFT JOIN (
        SELECT
        <if test="map.level !=null and map.level =='2'.toString()">
            CONCAT ( LEFT ( AD.ADCD, 4 ), '00000000000' ) AS adcd
        </if>
        <if test="map.level !=null and map.level !='2'.toString()">
            CONCAT ( LEFT ( AD.ADCD, 6 ), '000000000' ) AS adcd
        </if>
        FROM
        ( SELECT
        a.STCD,
        a.STNM,
        a.RVNM,
        a.HNNM,
        a.BSNM,
        a.LGTD,
        a.LTTD,
        a.STLC,
        a.ADDVCD,
        a.DTMNM,
        a.DTMEL,
        a.DTPR,
        a.STTP,
        a.FRGRD,
        a.ESSTYM,
        a.BGFRYM,
        a.ATCUNIT,
        a.ADMAUTH,
        a.LOCALITY,
        a.STBK,
        a.STAZT,
        a.DSTRVM,
        a.DRNA,
        a.PHCD,
        a.COMMENTS,
        a.MODITIME,
        1 AS usfl
        FROM
        ST_STBPRP_B a
        <if test="map.oLevel !=null and map.oLevel !='2'.toString()">
            WHERE  (a.ADMAUTH != '省水旱中心' OR a.ADMAUTH IS NULL)
        </if>
        UNION ALL
        SELECT
        b.STCD,
        b.STNM,
        b.RVNM,
        b.HNNM,
        b.BSNM,
        b.LGTD,
        b.LTTD,
        b.STLC,
        b.ADDVCD,
        b.DTMNM,
        b.DTMEL,
        b.DTPR,
        b.STTP,
        b.FRGRD,
        b.ESSTYM,
        b.BGFRYM,
        b.ATCUNIT,
        b.ADMAUTH,
        b.LOCALITY,
        b.STBK,
        b.STAZT,
        b.DSTRVM,
        b.DRNA,
        b.PHCD,
        b.COMMENTS,
        b.MODITIME,
        0 AS usfl
        FROM
        ST_STBPRP_B_DIS b
        <if test="map.oLevel !=null and map.oLevel !='2'.toString()">
            WHERE  (b.ADMAUTH != '省水旱中心' OR b.ADMAUTH IS NULL)
        </if>
        ) st
        LEFT JOIN BNS_HWINFO_B hw ON st.STCD = hw.STCD
        LEFT JOIN BSN_ADCD_B ad ON st.addvcd+ '000000000' = ad.adcd
        LEFT JOIN BNS_HWINFO_B_HISTORY h ON h.STCD = hw.STCD
        AND ( h.status = '1' OR h.status = '2' )
        WHERE
        st.FRGRD = 5
        AND AD.ADCD NOT LIKE '220581%'
        UNION ALL
        SELECT
        CONCAT ( LEFT ( AD.ADCD, 6 ), '000000000' ) AS adcd
        FROM
        ( SELECT
        a.STCD,
        a.STNM,
        a.RVNM,
        a.HNNM,
        a.BSNM,
        a.LGTD,
        a.LTTD,
        a.STLC,
        a.ADDVCD,
        a.DTMNM,
        a.DTMEL,
        a.DTPR,
        a.STTP,
        a.FRGRD,
        a.ESSTYM,
        a.BGFRYM,
        a.ATCUNIT,
        a.ADMAUTH,
        a.LOCALITY,
        a.STBK,
        a.STAZT,
        a.DSTRVM,
        a.DRNA,
        a.PHCD,
        a.COMMENTS,
        a.MODITIME,
        1 AS usfl
        FROM
        ST_STBPRP_B a
        <if test="map.oLevel !=null and map.oLevel !='2'.toString()">
            WHERE  (a.ADMAUTH != '省水旱中心' OR a.ADMAUTH IS NULL)
        </if>
        UNION ALL
        SELECT
        b.STCD,
        b.STNM,
        b.RVNM,
        b.HNNM,
        b.BSNM,
        b.LGTD,
        b.LTTD,
        b.STLC,
        b.ADDVCD,
        b.DTMNM,
        b.DTMEL,
        b.DTPR,
        b.STTP,
        b.FRGRD,
        b.ESSTYM,
        b.BGFRYM,
        b.ATCUNIT,
        b.ADMAUTH,
        b.LOCALITY,
        b.STBK,
        b.STAZT,
        b.DSTRVM,
        b.DRNA,
        b.PHCD,
        b.COMMENTS,
        b.MODITIME,
        0 AS usfl
        FROM
        ST_STBPRP_B_DIS b
        <if test="map.oLevel !=null and map.oLevel !='2'.toString()">
            WHERE  (b.ADMAUTH != '省水旱中心'  OR b.ADMAUTH IS NULL)
        </if>
        ) st
        LEFT JOIN BNS_HWINFO_B hw ON st.STCD = hw.STCD
        LEFT JOIN BSN_ADCD_B ad ON st.addvcd+ '000000000' = ad.adcd
        LEFT JOIN BNS_HWINFO_B_HISTORY h ON h.STCD = hw.STCD
        AND ( h.status = '1' OR h.status = '2' )
        WHERE
        st.FRGRD = 5
        AND AD.ADCD LIKE '220581%'
        ) BB ON
        LEFT( AA.ADCD, 6) = LEFT ( BB.ADCD, 6 )
        GROUP BY
        AA.ADCD,
        AA.ADNM
        ORDER BY
        AA.ADCD
    </select>

    <!-- 获取自动监测站待审核列表-省级使用 -->
    <select id="getHwPendingReviewList" resultType="com.huitu.cloud.api.ewci.device.entity.HwInfoHistory" useCache="false">
        SELECT
            hw.ID,
            hw.STNM,
            hw.STCD,
            hw.ADDVCD,
            ac.ADNM,
            hw.STTP,
            hw.ADMAUTH,
            hw.ESSTYM,
            st.FRGRD,
            hw.RTUBD,
            st.USFL,
            hw.COMMUNICATIONBD,
            hw.RAINBD,
            hw.RVBD,
            hw.SOLARENERGYAR,
            hw.BTTYEAR,
            hw.RTUYEAR,
            hw.RCSTAR,
            hw.RAINYEAR,
            hw.COMMUNICATIONAR,
            hw.CHARGECONTROLAR,
            hw.SPDAR,
            hw.CTNAR,
            hw.RVYEAR,
            hw.BASICRENOVATIONAR,
            hw.MODITIME,
            hw.BTTBD,
            ac.adcd,
            hw.status,
            hw.AUDREASON,
            hw.rcstbd
        FROM
        BNS_HWINFO_B_HISTORY hw
        LEFT JOIN ( SELECT
        a.STCD,
        a.STNM,
        a.RVNM,
        a.HNNM,
        a.BSNM,
        a.LGTD,
        a.LTTD,
        a.STLC,
        a.ADDVCD,
        a.DTMNM,
        a.DTMEL,
        a.DTPR,
        a.STTP,
        a.FRGRD,
        a.ESSTYM,
        a.BGFRYM,
        a.ATCUNIT,
        a.ADMAUTH,
        a.LOCALITY,
        a.STBK,
        a.STAZT,
        a.DSTRVM,
        a.DRNA,
        a.PHCD,
        a.COMMENTS,
        a.MODITIME,
        1 AS usfl
        FROM
        ST_STBPRP_B a
        UNION ALL
        SELECT
        b.STCD,
        b.STNM,
        b.RVNM,
        b.HNNM,
        b.BSNM,
        b.LGTD,
        b.LTTD,
        b.STLC,
        b.ADDVCD,
        b.DTMNM,
        b.DTMEL,
        b.DTPR,
        b.STTP,
        b.FRGRD,
        b.ESSTYM,
        b.BGFRYM,
        b.ATCUNIT,
        b.ADMAUTH,
        b.LOCALITY,
        b.STBK,
        b.STAZT,
        b.DSTRVM,
        b.DRNA,
        b.PHCD,
        b.COMMENTS,
        b.MODITIME,
        0 AS usfl
        FROM
        ST_STBPRP_B_DIS b) st ON st.STCD = hw.STCD
        LEFT JOIN BSN_ADCD_B ac ON hw.addvcd+ '000000000' = ac.adcd
                LEFT JOIN BSN_ADCD_B ad ON st.addvcd+ '000000000' = ad.adcd
        WHERE
        st.FRGRD = 5
        <if test="map.status !=null and map.status !=''">
                and CHARINDEX(hw.status,#{map.status}) > 0
        </if>
        <if test="map.adcd != null and map.adcd !=''">
            and left(ad.adcd,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(ad.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.oLevel !=null and map.oLevel !='2'.toString()">
            AND  (ST.ADMAUTH != '省水旱中心' OR ST.ADMAUTH IS NULL)
        </if>
        <if test="map.stnm!= null and map.stnm != '' ">
            and CHARINDEX(#{map.stnm},st.stnm) >0
        </if>
        <if test="map.sttps!=null and map.sttps!= ''">
            and st.STTP=#{map.sttps}
        </if>
        <if test="map.usfl!=null and map.usfl!= ''">
            and st.usfl=#{map.usfl}
        </if>
        <if test="map.admauth!=null and map.admauth!= ''">
            and CHARINDEX(#{map.admauth},st.admauth) >0
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '1'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.solarenergyar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '1'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.solarenergyar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '2'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.bttyear &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '2'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.bttyear &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '3'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.rtuyear &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '3'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.rtuyear &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '4'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.rainyear &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '4'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.rainyear &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '5'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.communicationar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '5'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.communicationar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '6'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.chargecontrolar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '6'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.chargecontrolar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '7'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.spdar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '7'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.spdar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '8'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.ctnar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '8'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.ctnar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '9'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.rvyear &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '9'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.rvyear &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '10'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.rcstar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '10'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.rcstar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '11'.toString() and map.startYear!=null and map.startYear!= ''">
            AND hw.basicrenovationar &gt;= #{map.startYear}
        </if>
        <if test="map.deviceType!=null and map.deviceType!= '' and map.deviceType == '11'.toString() and map.endYear!=null and map.endYear!= ''">
            AND hw.basicrenovationar &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        order by hw.MODITIME DESC
    </select>

    <select id="getHwAuditList" resultType="com.huitu.cloud.api.ewci.device.entity.HwInfoHistory">
        SELECT
            st.STNM,
            hw.STCD,
            st.ADDVCD,
            ad.ADNM,
            st.STTP,
            st.ADMAUTH,
            hw.RTUBD,
            st.USFL,
            st.FRGRD,
            hw.COMMUNICATIONBD,
            hw.RAINBD,
            hw.RVBD,
            hw.SOLARENERGYAR,
            hw.BTTYEAR,
            hw.RTUYEAR,
            hw.RCSTAR,
            hw.RAINYEAR,
            hw.COMMUNICATIONAR,
            hw.CHARGECONTROLAR,
            hw.SPDAR,
            hw.CTNAR,
            hw.RVYEAR,
            hw.BASICRENOVATIONAR,
            hw.MODITIME,
            hw.BTTBD,
            ad.adcd,
            '' AS status,
            hw.rcstbd,
            st.RVNM,
            st.HNNM,
            st.BSNM,
            st.STLC,
            st.LGTD,
            st.LTTD,
            st.DTMNM,
            st.DTMEL,
            st.DTPR,
            st.ATCUNIT,
            st.LOCALITY,
            st.ESSTYM,
            st.BGFRYM,
            st.STBK,
            st.STAZT,
            st.DSTRVM,
            st.DRNA,
            st.PHCD,
            st.COMMENTS,
            hw.BTTCT,
            hw.BTTTP,
            hw.BTTNUM,
            hw.RCSTBD,
            hw.RVCT,
            hw.RVNUM,
            hw.RAINCT,
            hw.RAINNUM,
            hw.RTUNUM,
            hw.RTUCT,
            '0' AS ID,
            hw.rcstnum,
            hw.solarenergybd,
            hw.solarenergynum,
            hw.chargecontrolbd,
            hw.chargecontrolnum
        FROM
            BNS_HWINFO_B hw
                LEFT JOIN (SELECT
        a.STCD,
        a.STNM,
        a.RVNM,
        a.HNNM,
        a.BSNM,
        a.LGTD,
        a.LTTD,
        a.STLC,
        a.ADDVCD,
        a.DTMNM,
        a.DTMEL,
        a.DTPR,
        a.STTP,
        a.FRGRD,
        a.ESSTYM,
        a.BGFRYM,
        a.ATCUNIT,
        a.ADMAUTH,
        a.LOCALITY,
        a.STBK,
        a.STAZT,
        a.DSTRVM,
        a.DRNA,
        a.PHCD,
        a.COMMENTS,
        a.MODITIME,
        1 AS usfl
        FROM
        ST_STBPRP_B a
        UNION ALL
        SELECT
        b.STCD,
        b.STNM,
        b.RVNM,
        b.HNNM,
        b.BSNM,
        b.LGTD,
        b.LTTD,
        b.STLC,
        b.ADDVCD,
        b.DTMNM,
        b.DTMEL,
        b.DTPR,
        b.STTP,
        b.FRGRD,
        b.ESSTYM,
        b.BGFRYM,
        b.ATCUNIT,
        b.ADMAUTH,
        b.LOCALITY,
        b.STBK,
        b.STAZT,
        b.DSTRVM,
        b.DRNA,
        b.PHCD,
        b.COMMENTS,
        b.MODITIME,
        0 AS usfl
        FROM
        ST_STBPRP_B_DIS b) st ON st.STCD = hw.STCD
                LEFT JOIN BSN_ADCD_B ad ON st.addvcd+ '000000000' = ad.adcd
        WHERE
            st.FRGRD = 5
          AND hw.stcd = #{stcd} UNION ALL
        SELECT
            hw.STNM,
            hw.STCD,
            hw.ADDVCD,
            ad.ADNM,
            hw.STTP,
            hw.ADMAUTH,
            hw.RTUBD,
            st.USFL,
            st.FRGRD,
            hw.COMMUNICATIONBD,
            hw.RAINBD,
            hw.RVBD,
            hw.SOLARENERGYAR,
            hw.BTTYEAR,
            hw.RTUYEAR,
            hw.RCSTAR,
            hw.RAINYEAR,
            hw.COMMUNICATIONAR,
            hw.CHARGECONTROLAR,
            hw.SPDAR,
            hw.CTNAR,
            hw.RVYEAR,
            hw.BASICRENOVATIONAR,
            hw.MODITIME,
            hw.BTTBD,
            ad.adcd,
            hw.status,
            hw.rcstbd,
            hw.RVNM,
            hw.HNNM,
            hw.BSNM,
            hw.STLC,
            hw.LGTD,
            hw.LTTD,
            hw.DTMNM,
            hw.DTMEL,
            hw.DTPR,
            hw.ATCUNIT,
            hw.LOCALITY,
            hw.ESSTYM,
            hw.BGFRYM,
            hw.STBK,
            hw.STAZT,
            hw.DSTRVM,
            hw.DRNA,
            hw.PHCD,
            hw.COMMENTS,
            hw.BTTCT,
            hw.BTTTP,
            hw.BTTNUM,
            hw.RCSTBD,
            hw.RVCT,
            hw.RVNUM,
            hw.RAINCT,
            hw.RAINNUM,
            hw.RTUNUM,
            hw.RTUCT,
            hw.ID,
            hw.rcstnum,
            hw.solarenergybd,
            hw.solarenergynum,
            hw.chargecontrolbd,
            hw.chargecontrolnum
        FROM BNS_HWINFO_B_HISTORY hw
                LEFT JOIN (SELECT
        a.STCD,
        a.STNM,
        a.RVNM,
        a.HNNM,
        a.BSNM,
        a.LGTD,
        a.LTTD,
        a.STLC,
        a.ADDVCD,
        a.DTMNM,
        a.DTMEL,
        a.DTPR,
        a.STTP,
        a.FRGRD,
        a.ESSTYM,
        a.BGFRYM,
        a.ATCUNIT,
        a.ADMAUTH,
        a.LOCALITY,
        a.STBK,
        a.STAZT,
        a.DSTRVM,
        a.DRNA,
        a.PHCD,
        a.COMMENTS,
        a.MODITIME,
        1 AS usfl
        FROM
        ST_STBPRP_B a
        UNION ALL
        SELECT
        b.STCD,
        b.STNM,
        b.RVNM,
        b.HNNM,
        b.BSNM,
        b.LGTD,
        b.LTTD,
        b.STLC,
        b.ADDVCD,
        b.DTMNM,
        b.DTMEL,
        b.DTPR,
        b.STTP,
        b.FRGRD,
        b.ESSTYM,
        b.BGFRYM,
        b.ATCUNIT,
        b.ADMAUTH,
        b.LOCALITY,
        b.STBK,
        b.STAZT,
        b.DSTRVM,
        b.DRNA,
        b.PHCD,
        b.COMMENTS,
        b.MODITIME,
        0 AS usfl
        FROM
        ST_STBPRP_B_DIS b) st ON st.STCD = hw.STCD
                LEFT JOIN BSN_ADCD_B ad ON hw.addvcd+ '000000000' = ad.adcd
        WHERE
            st.FRGRD = 5
          AND hw.status = 1
          AND hw.stcd = #{stcd}
    </select>

    <select id="getHwHistoryList" resultType="com.huitu.cloud.api.ewci.device.entity.HwInfoHistory">
        SELECT
            res.*
        FROM
            (SELECT hw.stnm,
                    hw.stcd,
                    hw.addvcd,
                    ad.adnm,
                    hw.sttp,
                    hw.admauth,
                    hw.rtubd,
                    hw.usfl,
                    hw.communicationbd,
                    hw.rainbd,
                    hw.rvbd,
                    hw.solarenergyar,
                    hw.bttyear,
                    hw.rtuyear,
                    hw.rcstar,
                    hw.rainyear,
                    hw.communicationar,
                    hw.chargecontrolar,
                    hw.spdar,
                    hw.ctnar,
                    hw.rvyear,
                    hw.basicrenovationar,
                    hw.moditime,
                    hw.bttbd,
                    ad.adcd,
                    hw.status,
                    hw.rcstbd,
                    hw.esstym,
                    hw.id
                FROM BNS_HWINFO_B_HISTORY hw
                        LEFT JOIN (SELECT
        a.STCD,
        a.STNM,
        a.RVNM,
        a.HNNM,
        a.BSNM,
        a.LGTD,
        a.LTTD,
        a.STLC,
        a.ADDVCD,
        a.DTMNM,
        a.DTMEL,
        a.DTPR,
        a.STTP,
        a.FRGRD,
        a.ESSTYM,
        a.BGFRYM,
        a.ATCUNIT,
        a.ADMAUTH,
        a.LOCALITY,
        a.STBK,
        a.STAZT,
        a.DSTRVM,
        a.DRNA,
        a.PHCD,
        a.COMMENTS,
        a.MODITIME,
        1 AS usfl
        FROM
        ST_STBPRP_B a
        UNION ALL
        SELECT
        b.STCD,
        b.STNM,
        b.RVNM,
        b.HNNM,
        b.BSNM,
        b.LGTD,
        b.LTTD,
        b.STLC,
        b.ADDVCD,
        b.DTMNM,
        b.DTMEL,
        b.DTPR,
        b.STTP,
        b.FRGRD,
        b.ESSTYM,
        b.BGFRYM,
        b.ATCUNIT,
        b.ADMAUTH,
        b.LOCALITY,
        b.STBK,
        b.STAZT,
        b.DSTRVM,
        b.DRNA,
        b.PHCD,
        b.COMMENTS,
        b.MODITIME,
        0 AS usfl
        FROM
        ST_STBPRP_B_DIS b) st ON st.STCD = hw.STCD
                        LEFT JOIN BSN_ADCD_B ad ON hw.addvcd+ '000000000' = ad.adcd
                WHERE
                    st.FRGRD = 5
                  AND hw.status = 0
                  AND hw.stcd = #{stcd}
                UNION ALL

                SELECT st.STNM,
                    hw.STCD,
                    st.ADDVCD,
                    ad.ADNM,
                    st.STTP,
                    st.ADMAUTH,
                    hw.RTUBD,
                    st.USFL,
                    hw.COMMUNICATIONBD,
                    hw.RAINBD,
                    hw.RVBD,
                    hw.SOLARENERGYAR,
                    hw.BTTYEAR,
                    hw.RTUYEAR,
                    hw.RCSTAR,
                    hw.RAINYEAR,
                    hw.COMMUNICATIONAR,
                    hw.CHARGECONTROLAR,
                    hw.SPDAR,
                    hw.CTNAR,
                    hw.RVYEAR,
                    hw.BASICRENOVATIONAR,
                    hw.MODITIME,
                    hw.BTTBD,
                    ad.adcd,
                    '0' as status,
                    hw.rcstbd,
                    st.esstym,
                    '-1' as id
                FROM
                    BNS_HWINFO_B hw
                        LEFT JOIN ( SELECT
        a.STCD,
        a.STNM,
        a.RVNM,
        a.HNNM,
        a.BSNM,
        a.LGTD,
        a.LTTD,
        a.STLC,
        a.ADDVCD,
        a.DTMNM,
        a.DTMEL,
        a.DTPR,
        a.STTP,
        a.FRGRD,
        a.ESSTYM,
        a.BGFRYM,
        a.ATCUNIT,
        a.ADMAUTH,
        a.LOCALITY,
        a.STBK,
        a.STAZT,
        a.DSTRVM,
        a.DRNA,
        a.PHCD,
        a.COMMENTS,
        a.MODITIME,
        1 AS usfl
        FROM
        ST_STBPRP_B a
        UNION ALL
        SELECT
        b.STCD,
        b.STNM,
        b.RVNM,
        b.HNNM,
        b.BSNM,
        b.LGTD,
        b.LTTD,
        b.STLC,
        b.ADDVCD,
        b.DTMNM,
        b.DTMEL,
        b.DTPR,
        b.STTP,
        b.FRGRD,
        b.ESSTYM,
        b.BGFRYM,
        b.ATCUNIT,
        b.ADMAUTH,
        b.LOCALITY,
        b.STBK,
        b.STAZT,
        b.DSTRVM,
        b.DRNA,
        b.PHCD,
        b.COMMENTS,
        b.MODITIME,
        0 AS usfl
        FROM
        ST_STBPRP_B_DIS b ) st ON st.STCD = hw.STCD
                        LEFT JOIN BSN_ADCD_B ad ON st.addvcd+ '000000000' = ad.adcd
                WHERE
                    st.FRGRD = 5
                  AND hw.stcd = #{stcd}
            ) res
        ORDER BY
            res.MODITIME DESC
    </select>

    <select id="getAdnmByAddvcd" resultType="java.lang.String">
        SELECT
            ad.ADNM
        FROM
            BSN_ADCD_B ad
        WHERE
            ad.adcd = #{addvcd} + '000000000'
    </select>

    <select id="selectAdcdByStcd" resultType="java.lang.String">
        SELECT ADCD
        FROM BSN_STADTP_B
        WHERE STCD = #{stcd}
    </select>


    <update id="updateStationExtend">
        UPDATE BSN_STADTP_B
        SET ADCD=#{adcd},
            UPDATETM=GETDATE()
        WHERE STCD = #{stcd}
    </update>

    <delete id="deleteId">
        delete
        from BNS_HWINFO_B_HISTORY
        where ID = #{id}
    </delete>

    <update id="updateHwHistory"  parameterType="com.huitu.cloud.api.ewci.device.entity.HwInfoHistory">
        UPDATE BNS_HWINFO_B_HISTORY
        SET STNM = #{stnm},
            STTP = #{sttp},
            ADMAUTH = #{admauth},
            ESSTYM = #{esstym},
            RTUBD = #{rtubd},
            COMMUNICATIONBD = #{communicationbd},
            RAINBD = #{rainbd},
            RVBD = #{rvbd},
            RCSTBD = #{rcstbd},
            SOLARENERGYAR = #{solarenergyar},
            BTTYEAR = #{bttyear},
            RTUYEAR = #{rtuyear},
            RAINYEAR = #{rainyear},
            COMMUNICATIONAR = #{communicationar},
            CHARGECONTROLAR = #{chargecontrolar},
            SPDAR = #{spdar},
            CTNAR = #{ctnar},
            RVYEAR = #{rvyear},
            RCSTAR = #{rcstar},
            BASICRENOVATIONAR = #{basicrenovationar},
            MODITIME = #{moditime},
            RTUNUM = #{rtunum},
            RTUCT = #{rtuct},
            RAINNUM = #{rainnum},
            RAINCT = #{rainct},
            BTTBD = #{bttbd},
            BTTNUM = #{bttnum},
            BTTTP = #{btttp},
            BTTCT = #{bttct},
            RVNUM = #{rvnum},
            RVCT = #{rvct},
            NT = #{nt},
            CHARGECONTROLBD = #{chargecontrolbd},
            CHARGECONTROLNUM = #{chargecontrolnum},
            SOLARENERGYBD = #{solarenergybd},
            SOLARENERGYNUM = #{solarenergynum},
            RCSTNUM = #{rcstnum},
            RVNM = #{rvnm},
            HNNM = #{hnnm},
            BSNM = #{bsnm},
            LGTD = #{lgtd},
            LTTD = #{lttd},
            STLC = #{stlc},
            ADDVCD = #{addvcd},
            DTMNM = #{dtmnm},
            DTMEL = #{dtmel},
            DTPR = #{dtpr},
            FRGRD = #{frgrd},
            BGFrym = #{bgfrym},
            ATCUNIT = #{atcunit},
            LOCALITY = #{locality},
            STBK = #{stbk},
            STAZT = #{stazt},
            DSTRVM = #{dstrvm},
            DRNA = #{drna},
            PHCD = #{phcd},
            USFL = #{usfl},
            COMMENTS = #{comments},
            CREATOR = #{creator},
            CREATED = #{created},
            AUDITED = #{audited},
            AUDITOR = #{auditor},
            AUDREASON = #{audreason},
            ADCD = #{adcd}
        WHERE stcd = #{stcd}
        and status = 1
    </update>



</mapper>
