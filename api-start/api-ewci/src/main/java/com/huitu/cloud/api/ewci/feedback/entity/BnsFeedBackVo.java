package com.huitu.cloud.api.ewci.feedback.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * <p>
 * 意见反馈信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@ApiModel(value = "BnsFeedBack对象", description = "意见反馈信息")
public class BnsFeedBackVo extends Model<BnsFeedBackVo> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @TableId(value = "ID")
    private String id;

    @ApiModelProperty(value = "主模块")
    @TableField("M_MODULE")
    private String mModule;

    @ApiModelProperty(value = "子模块")
    @TableField("C_MODULE")
    private String cModule;

    @ApiModelProperty(value = "意见描述")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "处理状态(0未处理1已处理)")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "反馈人")
    @TableField("FEEDBACK_USER")
    private String feedbackUser;

    @ApiModelProperty(value = "反馈时间")
    @TableField("FEEDBACK_TM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime feedbackTm;

    @ApiModelProperty(value = "处理人")
    @TableField("HANDLE_USER")
    private String handleUser;

    @ApiModelProperty(value = "处理时间")
    @TableField("HANDLE_TM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime handleTm;

    @ApiModelProperty(value = "处理说明")
    @TableField("HANDLE_DESC")
    private String handleDesc;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getmModule() {
        return mModule;
    }

    public void setmModule(String mModule) {
        this.mModule = mModule;
    }

    public String getcModule() {
        return cModule;
    }

    public void setcModule(String cModule) {
        this.cModule = cModule;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFeedbackUser() {
        return feedbackUser;
    }

    public void setFeedbackUser(String feedbackUser) {
        this.feedbackUser = feedbackUser;
    }

    public LocalDateTime getFeedbackTm() {
        return feedbackTm;
    }

    public void setFeedbackTm(LocalDateTime feedbackTm) {
        this.feedbackTm = feedbackTm;
    }

    public String getHandleUser() {
        return handleUser;
    }

    public void setHandleUser(String handleUser) {
        this.handleUser = handleUser;
    }

    public LocalDateTime getHandleTm() {
        return handleTm;
    }

    public void setHandleTm(LocalDateTime handleTm) {
        this.handleTm = handleTm;
    }

    public String getHandleDesc() {
        return handleDesc;
    }

    public void setHandleDesc(String handleDesc) {
        this.handleDesc = handleDesc;
    }

    @Override
    public String toString() {
        return "BnsFeedBack{" +
                "id='" + id + '\'' +
                ", mModule='" + mModule + '\'' +
                ", cModule='" + cModule + '\'' +
                ", comments='" + comments + '\'' +
                ", status='" + status + '\'' +
                ", feedbackUser='" + feedbackUser + '\'' +
                ", feedbackTm=" + feedbackTm +
                ", handleUser='" + handleUser + '\'' +
                ", handleTm=" + handleTm +
                ", handleDesc='" + handleDesc + '\'' +
                '}';
    }
}