package com.huitu.cloud.api.ewci.weChat.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.weChat.entity.WeChatRain;
import com.huitu.cloud.api.ewci.weChat.entity.WeChatRainTypeQo;
import com.huitu.cloud.api.ewci.weChat.service.RainSituationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 公众号雨情信息接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-06
 */

@RestController
@Api(tags = "公众号雨情信息查询接口")
@RequestMapping("/api/unsafe/wechat/rain")
public class RainSituationResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "6da05904-1d8a-49cb-98c2-eb8a60eda76c";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private RainSituationService rainSituationService;

    @ApiOperation(value = "累计雨量统计", notes = "累计雨量统计，条件查询")
    @PostMapping(value = "select-by-page")
    public ResponseEntity<SuccessResponse<Page<WeChatRain>>> getInfoByPage(@RequestBody  @Validated WeChatRainTypeQo stType) throws Exception {
        IPage<WeChatRain> list = rainSituationService.getRainByCondition(stType.getStType());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
}