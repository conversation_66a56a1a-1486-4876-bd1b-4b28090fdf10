package com.huitu.cloud.api.fusion.entity.ext;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 山洪预警语音接收人
 */
@ApiModel(value = "山洪预警语音接收人")
@Data
public class FusionXccReceiver extends FusionReceiver {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "发送ID")
    @TableField(value = "SEND_ID")
    private String sendId;

    @ApiModelProperty(value = "外部发送ID")
    @TableField(value = "EXT_SEND_ID")
    private String extSendId;

    @ApiModelProperty(value = "发送状态，注：0=等待发送、1=发送成功、2=发送失败")
    @TableField(value = "SEND_STATUS")
    private String sendStatus;

    @ApiModelProperty(value = "发送时间")
    @TableField(value = "SEND_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    @ApiModelProperty(value = "回执时间")
    @TableField(value = "RECEIPT_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiptTime;

    @ApiModelProperty(value = "语音通话结果，注：详见枚举值表")
    @TableField(value = "CALL_RESULT")
    private Integer callResult;

    @ApiModelProperty(value = "语音通话结果文本")
    @TableField(value = "CALL_RESULT_TEXT")
    private String callResultText;

    @ApiModelProperty(value = "失败原因")
    @TableField(value = "FAIL_CAUSE")
    private String failCause;
}
