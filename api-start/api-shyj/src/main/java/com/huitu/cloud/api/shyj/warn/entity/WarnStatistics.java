package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2019-09-17
 */
@ApiModel(value="预警统计对象", description="政区预警统计")
public class WarnStatistics implements Serializable {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "上级政区编码")
    private String padcd;
    @ApiModelProperty(value = "经度")
    private String lgtd;
    @ApiModelProperty(value = "纬度")
    private String lttd;
    @ApiModelProperty(value = "准备转移数量")
    private int total5;
    @ApiModelProperty(value = "立即转移数量")
    private int total4;
    @ApiModelProperty(value = "预警总数")
    private int allTotal;
    @ApiModelProperty(value = "关闭预警总数")
    private int closeTotal;
    @ApiModelProperty(value = "未关闭预警总数")
    private int openTotal;
    @ApiModelProperty(value = "预警短信条数")
    private int totalMassage;
    @ApiModelProperty(value = "预警统计下级对象")
    private List<WarnStatistics> children;

    public List<WarnStatistics> getChildren() {
        return children;
    }

    public void setChildren(List<WarnStatistics> children) {
        this.children = children;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }

    public int getTotal5() {
        return total5;
    }

    public void setTotal5(int total5) {
        this.total5 = total5;
    }

    public int getTotal4() {
        return total4;
    }

    public void setTotal4(int total4) {
        this.total4 = total4;
    }

    public int getAllTotal() {
        return allTotal;
    }

    public void setAllTotal(int allTotal) {
        this.allTotal = allTotal;
    }

    public int getCloseTotal() {
        return closeTotal;
    }

    public void setCloseTotal(int closeTotal) {
        this.closeTotal = closeTotal;
    }

    public int getOpenTotal() {
        return openTotal;
    }

    public void setOpenTotal(int openTotal) {
        this.openTotal = openTotal;
    }

    public int getTotalMassage() {
        return totalMassage;
    }

    public void setTotalMassage(int totalMassage) {
        this.totalMassage = totalMassage;
    }
}
