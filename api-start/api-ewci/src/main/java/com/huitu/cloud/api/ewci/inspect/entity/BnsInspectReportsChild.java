package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 检查报告
 */
@Data
@ApiModel(value = "检查报告")
public class BnsInspectReportsChild implements Serializable {

    @ApiModelProperty(value = "检查报告ID")
    private String reportId;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "生成")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "是否确认")
    private String confirmResult;

    @ApiModelProperty(value = "登录人员姓名")
    private String loginnm;

    @ApiModelProperty(value = "文件路径")
    @TableField(value = "FILE_PATH")
    private String filePath;

    @ApiModelProperty(value = "报告生成状态(1生成中2已生成)")
    @TableField(value = "STATUS")
    private String status;

    @ApiModelProperty(value = "照片")
    private List<BnsInspectFile> photos;

}
