package com.huitu.cloud.api.ewci.hydfcst.service.impl;

import com.huitu.cloud.api.ewci.hydfcst.entity.*;
import com.huitu.cloud.api.ewci.hydfcst.mapper.HydfcstDao;
import com.huitu.cloud.api.ewci.hydfcst.service.HydfcstService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 水文预报服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class HydfcstServiceImpl implements HydfcstService {

    private HydfcstDao hydfcstDao;

    @Autowired
    public HydfcstServiceImpl(HydfcstDao hydfcstDao) {
        this.hydfcstDao = hydfcstDao;
    }

    /**
     * 获取河道和水库的水文预报时间过程数据。
     *
     * @param query 查询对象
     * @return 包含河道预报数据和水库预报数据的列表
     */
    @Override
    public List<StResultMap> listHydfcstTmProcessData(HydfcstQuery query) {
        // 获取河道预报数据
        List<StForecast> hydfcstStForecastList = hydfcstDao.getHydfcstStForecastData(query.toQueryParam());
        // 获取水库预报数据
        List<StReglat> hydfcstStReglatList = hydfcstDao.getHydfcstStReglatData(query.toQueryParam());

        // 合并河道和水库预报数据的日期，并去重
        List<StResultMap> result = Stream.concat(
                        hydfcstStForecastList.stream().map(StForecast::getYmdh).distinct(),
                        hydfcstStReglatList.stream().map(StReglat::getYmdh).distinct())
                .distinct()
                .map(tm -> {
                    // 根据日期筛选河道预报数据，并将相同站点的数据合并，保留最新的数据
                    Map<String, StRV> rvMap = hydfcstStForecastList.stream()
                            .filter(f -> f.getYmdh().equals(tm))
                            .collect(Collectors.toMap(StForecast::getStcd,
                                    f -> new StRV(
                                            f.getStcd(),    // stcd: 获取站点代码
                                            f.getZ(),       // z: 获取Z值
                                            f.getQ(),       // q: 获取Q值
                                            f.getYmdh(),    // ymdh: 获取年月日小时
                                            f.getStnm(),    // stnm: 获取站点名称
                                            f.getRvnm(),    // rvnm: 获取河流名称
                                            f.getHnnm(),    // hnnm: 获取水文节点名称
                                            f.getBsnm(),    // rvnm: 获取流域名称
                                            f.getAdcd(),    // adcd: 获取行政区代码
                                            f.getAdnm(),    // adnm: 获取行政区名称
                                            f.getWrz(),     // wrz: 获取水位
                                            f.getWrq(),     // wrq: 获取流量
                                            f.getGrz(),     // grz: 获取蓄水位
                                            f.getGrq(),      // grq: 获取蓄水量
                                            f.getLgtd(),
                                            f.getLttd(),
                                            f.getPlgtd(),
                                            f.getPlttd(),
                                            f.getTmYmdh(),
                                            f.getStlc(),
                                            f.getSttp(),
                                            f.getFrgrd(),
                                            f.getStazt(),
                                            f.getXadcd(),
                                            f.getXadnm(),
                                            f.getLdkel(),
                                            f.getRdke()
                                    ),
                                    (existing, replacement) -> replacement));
                    // 根据日期筛选水库预报数据，并将相同站点的数据合并，保留最新的数据
                    Map<String, StRS> rsMap = hydfcstStReglatList.stream()
                            .filter(r -> r.getYmdh().equals(tm))
                            .collect(Collectors.toMap(StReglat::getStcd,
                                    r -> new StRS(
                                            r.getStcd(),     // stcd: 获取站点代码
                                            r.getRz(),       // rz: 获取水库水位
                                            r.getW(),        // w: 获取库容
                                            r.getOtq(),      // otq: 获取出库流量
                                            r.getYmdh(),     // ymdh: 获取年月日小时
                                            r.getFsltdw(),   // fsltdw: 获取防洪限制水位对应的水库水位
                                            r.getNormz(),    // normz: 获取正常蓄水位
                                            r.getActz(),     // actz: 获取当前蓄水位
                                            r.getDsflz(),    // dsflz: 获取死水位
                                            r.getCkflz(),    // ckflz: 获取防洪高水位
                                            r.getDamel(),    // damel: 获取大坝顶高程
                                            r.getTtcp(),     // ttcp: 获取总库容
                                            r.getBgmd(),     // bgmd: 获取坝高
                                            r.getEdmd(),     // edmd: 获取坝底高
                                            r.getFsltdz(),   // fsltdz: 获取防洪限制水位
                                            r.getStnm(),     // stnm: 获取站点名称
                                            r.getRvnm(),     // rvnm: 获取河流名称
                                            r.getHnnm(),     // hnnm: 获取水文节点名称
                                            r.getBsnm(),     // hnnm: 获取流域名称
                                            r.getAdcd(),     // adcd: 获取行政区代码
                                            r.getAdnm(),     // adnm: 获取行政区名称
                                            r.getLgtd(),
                                            r.getLttd(),
                                            r.getPlgtd(),
                                            r.getPlttd(),
                                            r.getTmYmdh(),
                                            r.getInq(),
                                            r.getRzfsltdz(),
                                            r.getRznormz(),
                                            r.getWfsltdw(),
                                            r.getStlc(),
                                            r.getSttp(),
                                            r.getFrgrd(),
                                            r.getXadcd(),
                                            r.getXadnm(),
                                            r.getRsvrtp(),
                                            r.getFldcp(),
                                            r.getActcp(),
                                            r.getResCode(),
                                            r.getResName()
                                    ),
                                    (existing, replacement) -> replacement));

                    // 返回包含日期、河道预报数据和水库预报数据的StResultMap对象
                    return new StResultMap(tm, rvMap, rsMap);
                })
                .collect(Collectors.toList());
//        result.forEach(System.out::println);
        return result;
    }

    /**
     * 获取水文预报时间批次。
     *
     * @param query 查询对象
     * @return 水文预报时间批次列表
     */
    @Override
    public List<String> listHydfcstBatch(HydfcstTmBatchQuery query) {
        return hydfcstDao.listHydfcstBatch(query.toQueryParam());
    }

    /**
     * 获取预报统计结果。
     *
     * @param query 查询对象
     * @return 预报统计结果数据
     */
    @Override
    public HydFcstStat listForecastStatistics(HydfcstStatisticsQuery query) {
        // 河道预报统计结果
        List<HydFcstRvStat> HydFcstRvStat = hydfcstDao.listHydFcstRvStat(query.toQueryParam());
        // 水库预报统计结果
        List<HydFcstRsStat> HydFcstRsStat = hydfcstDao.listHydFcstRsStat(query.toQueryParam());

        return new HydFcstStat(HydFcstRvStat, HydFcstRsStat);
    }
}
