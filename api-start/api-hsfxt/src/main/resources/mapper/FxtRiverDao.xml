<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.hsfxt.river.mapper.FxtRiverDao">

    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getTunRiverTreeInfo" resultType="com.huitu.cloud.api.hsfxt.river.entity.RiverTree">
        SELECT TUNBIANMA ADCD,TUNMINGCHENG ADNM,LEFT(TUNBIANMA,9)+'000000' PADCD,LEIXING KEYINDEX
        FROM TUNEX
        WHERE 1=1 AND ISNULL(${param1},'') != '' AND ISNULL(${param2},'') != '' AND ISNULL(${param3},'') != ''
        <if test="key != null and key != ''">
            AND LEIXING in (${key})
        </if>
        <if test="name != null and name != ''">
            AND CHARINDEX(#{name}, TUNMINGCHENG)>0
        </if>
        order by adcd
    </select>
    <select id="getRiverTreeInfo" resultType="com.huitu.cloud.api.hsfxt.river.entity.RiverTree">
        SELECT DISTINCT XIANQUXIANGZHENBIANMA ADCD,MINGCHENG ADNM
        <if test="level != null and level == 6">
            ,'0' PADCD
        </if>
        <if test="level != null and level == 9">
            ,LEFT(XIANQUXIANGZHENBIANMA,6)+'000000000' PADCD
        </if>
        FROM XianQuXiangZhen
        WHERE 1=1
        <if test="level != null and level == 6">
            AND SUBSTRING(XIANQUXIANGZHENBIANMA,5,2) &lt;> '00' AND SUBSTRING(XIANQUXIANGZHENBIANMA,7,3) = '000'
        </if>
        <if test="level != null and level == 9">
            AND SUBSTRING(XIANQUXIANGZHENBIANMA,7,3) &lt;> '000'
        </if>
        order by adcd
    </select>
    <select id="getRiverShjjTreeInfo" resultType="com.huitu.cloud.api.hsfxt.river.entity.SheHuiJingJi">
        SELECT SORTID, KEYINDEX, YMSSINDEX, XZQHINDEX, MINGCHENG, YXHS, YXRK, YXFW, YMGD, YXGDP, YMMJ, YXTL, YXGL, HSLS,
                ANZHIDIAN, XIANLUCHANGDU, ZHUANYISHIJIAN, PID,
                (CASE WHEN (ISNULL(XZQHINDEX,'') = '') AND ( ISNULL(YMSSINDEX,'') != '') THEN YMSSINDEX ELSE MINGCHENG END)NAME
        FROM  SHEHUIJINGJI
        WHERE 1=1
        <if test="key != null and key != ''">
            AND KEYINDEX in (${key})
        </if>
    </select>
    <select id="getRiverParam" resultType="com.huitu.cloud.api.hsfxt.city.entity.DicHongshuiliangji">
        SELECT  *  FROM  DIC_HONGSHUILIANGJI WHERE HONGSHUILIANGJIID=#{hl} AND BIANZHIQUID =#{bzqid}
    </select>
    <select id="getXianQuXiangZhen" resultType="com.huitu.cloud.api.hsfxt.river.entity.JiangHeXiangQing">
        SELECT XIANQUXIANGZHENBIANMA ADCD, MINGCHENG ADNM, MIANJI, FUSHU, RENKOU, FANGWUJIANSHU, GENDIMIANJI, GDP, FENQU, BEIYONG2, BEIYONG3, BEIYONG4, BEIYONG5, BEIYONG6
        FROM XIANQUXIANGZHEN
        WHERE XIANQUXIANGZHENBIANMA=#{adcd} AND FENQU=#{bzqid}
    </select>
    <select id="getZaiHaiSunShi" resultType="com.huitu.cloud.api.hsfxt.river.entity.JiangHeXiangQing">
        SELECT ID, XINGZHENGQUBIANHAO, XINGZHENGQUMINGCHENG, FANGANBIANMA, FLOODRISKTYPE, FLOODZONE, FLOODMATHZONE, FLOODBREACH, FLOODQUANTITY, YANMOMIANJI, YANMOGENDIMIANJI, YINXIANGRENKOU, YINXIANGGDP, YINXIANGFANWUSHU, HONGZAISUNSHI, BEIYONG1, BEIYONG2, BEIYONG3, BEIYONG4, BEIYONG5, BEIYONG6, KEYINDEX
        FROM ZAIHAISUNSHIYINGXIANGBIAO
        WHERE XINGZHENGQUBIANHAO=#{adcd} AND KEYINDEX = #{key}
    </select>
    <select id="getTunShjjDetailInfo" resultType="com.huitu.cloud.api.hsfxt.river.entity.TunXiangQing">
        SELECT TUNBIANMA ADCD, TUNMINGCHENG ADNM, X_COOR, Y_COOR, FUSHU, RENKOU, FANGWUJIANSHU, GENDIMIANJI, GDP,
                ${param1} DDSJ,${param2} HSLS,${param3} YMSS,
               LEIXING, GB, FENQU, ZHUANYILUXIANSHU, BEIYONG6, CUNBIANMA
        FROM TUNEX
        WHERE TUNBIANMA=#{adcd} AND LEIXING = #{key}
    </select>
    <select id="getZhuanYiFangAn" resultType="com.huitu.cloud.api.hsfxt.river.entity.ZhuanYiFangAn">
        SELECT ZHUANYIBIAOID, ZHUANYIDANYUANBIANMA, ZHUANYIDANYUANMINGCHENG, ZHUANYIFANGANBIANMA, ZHUANYIFANGANMINGCHENG, ZHUANYILUXIANBIANMA, ANZHIQUBIANMA, ANZHIQUMINGCHENG, ZHUANYILUXIANMINGCHENG, ZHUANYILUXIANCHANGDU, ZHUANYISHIJIAN, FENQU, BEIYONG2, BEIYONG3, BEIYONG4, BEIYONG5, BEIYONG6, TUNBIANMA
        FROM ZHUANYIBIAO
        WHERE 1=1
        <if test="adcd != null and adcd != ''">
            AND ZHUANYIDANYUANBIANMA = #{adcd}
        </if>
        <if test="bzqid != null and bzqid != ''">
            AND FENQU = #{bzqid}
        </if>
        ORDER BY ZHUANYIFANGANBIANMA
    </select>
</mapper>