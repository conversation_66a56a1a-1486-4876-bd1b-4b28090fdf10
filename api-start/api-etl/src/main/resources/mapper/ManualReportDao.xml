<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.datamanager.manualreport.mapper.ManualReportDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <insert id="insertBsnManualReport">
        insert into BSN_MANUALREPORT_B (STCD,TBNM,BGTM)
        values(#{stcd},#{tbnm},#{bgtm})
    </insert>
    <update id="close">
        update BSN_MANUALREPORT_B set EDTM=getdate() where STCD=#{stcd} and EDTM is null
        <if test="tbnm!=null and tbnm !=''">
            and TBNM=#{tbnm}
        </if>
    </update>


    <select id="getManualReportStInfo"
            resultType="com.huitu.cloud.api.datamanager.manualreport.entity.ManualReportVo">
        SELECT STCD,TBNM,BGTM,EDTM,STNM,ADNM,XADNM
        FROM   (SELECT A.STCD,A.TBNM,A.BGTM,A.EDTM,AA.STNM,B.ADNM XADNM,C.ADNM,
                         ROW_NUMBER() OVER (PARTITION BY A.STCD,A.TBNM ORDER BY A.BGTM DESC) RANK
                FROM BSN_MANUALREPORT_B A INNER JOIN ST_STBPRP_B AA ON A.STCD=AA.STCD
                                          INNER JOIN BSN_STADTP_B ST ON ST.STCD=A.STCD
                                          LEFT JOIN BSN_ADCD_B B ON AA.ADDVCD+'000000000'=B.ADCD
                                          LEFT JOIN BSN_ADCD_B C ON ST.ADCD=C.ADCD
                WHERE 1=1
                <if test="ad != null and ad != ''">
                    and left(AA.ADDVCD,#{level})=#{ad}
                </if>
                <if test="level!= null and level == '4'.toString()">
                    and (AA.ADDVCD !='220581' and AA.ADDVCD !='220381 ')
                </if>
             )a
        WHERE A.RANK=1
        order by EDTM,STCD
    </select>

</mapper>