package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 塘（堰）坝工程调查成果汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-18
 */
@TableName("IA_C_DAMINFO")
@ApiModel(value="IaCDaminfo对象", description="塘（堰）坝工程调查成果汇总表")
public class IaDaminfo extends  RelevantInfo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "塘堰编码")
    @TableId(value = "DAMCD", type = IdType.NONE)
    private String damcd;

    @ApiModelProperty(value = "塘堰名称")
    @TableField("DAMNAME")
    private String damname;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "照片编号")
    @TableField("PICID")
    private String picid;

    @ApiModelProperty(value = "经度(°)")
    @TableField("LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "纬度(°)")
    @TableField("LTTD")
    private Double lttd;

    @ApiModelProperty(value = "容积（m3）")
    @TableField("XHST")
    private Double xhst;

    @ApiModelProperty(value = "坝高（m）")
    @TableField("HEIGHT")
    private Double height;

    @ApiModelProperty(value = "坝长（m）")
    @TableField("WIDTH")
    private Double width;

    @ApiModelProperty(value = "挡水主坝类型")
    @TableField("MT")
    private String mt;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getDamcd() {
        return damcd;
    }

    public void setDamcd(String damcd) {
        this.damcd = damcd;
    }

    public String getDamname() {
        return damname;
    }

    public void setDamname(String damname) {
        this.damname = damname;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getPicid() {
        return picid;
    }

    public void setPicid(String picid) {
        this.picid = picid;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public Double getXhst() {
        return xhst;
    }

    public void setXhst(Double xhst) {
        this.xhst = xhst;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public String getMt() {
        return mt;
    }

    public void setMt(String mt) {
        this.mt = mt;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }


}
