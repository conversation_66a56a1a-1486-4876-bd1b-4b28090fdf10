package com.huitu.cloud.api.hyst.survey.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.hyst.survey.entity.HystStats;
import com.huitu.cloud.api.hyst.survey.entity.SurveyQuery;
import com.huitu.cloud.api.hyst.survey.entity.SurveyVo;
import com.huitu.cloud.api.hyst.survey.service.SurveyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 小水电概况
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "小水电概况")
@RequestMapping("/api/hyst/survey")
public class SurveyResource extends AbstractApiResource implements ApiResource {

    public SurveyResource(SurveyService baseService) {
        this.baseService = baseService;
    }

    @Override
    public String getUuid() {
        return "715380da-c262-4bb1-a1cb-a40e8cdbd0eb";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final SurveyService baseService;

    @ApiOperation(value = "小水电概况分页查询", notes = "作者：zyj")
    @PostMapping("select-survey-page")
    public ResponseEntity<SuccessResponse<Page<SurveyVo>>> getSurveyPage(@RequestBody SurveyQuery query) throws Exception {
        IPage<SurveyVo> page = baseService.getSurveyPage(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", page));
    }

    @ApiOperation(value = "小水电概况详情列表", notes = "作者：zyj")
    @PostMapping("select-details-list")
    public ResponseEntity<SuccessResponse<Page<SurveyVo>>> getDetailsList(@RequestBody SurveyQuery query) throws Exception {
        List<SurveyVo> list = baseService.getDetailsList(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "小水电概况统计图表", notes = "作者：zyj")
    @PostMapping("select-hyst-stats")
    public ResponseEntity<SuccessResponse<HystStats>> getHystStats(@RequestBody SurveyQuery query) throws Exception {
        HystStats data = baseService.getHystStats(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", data));
    }
}
