package com.huitu.cloud.api.hsfxt.zxhl.service;

import com.huitu.cloud.api.hsfxt.zxhl.entity.ZxhlShehuijingji;
import com.huitu.cloud.api.hsfxt.zxhl.entity.ZxhlTree;
import com.huitu.cloud.api.hsfxt.zxhl.entity.ZxhlTunXiangqing;
import com.huitu.cloud.api.hsfxt.zxhl.entity.ZxhlXiangQing;

import java.util.List;

/**
 * <p>
 * 风险图-中小河流服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
public interface ZxhlService {
    /**
     * 中小河流列表信息查询
     *
     * @param key  查询条件组合
     * @param name 最小编制区单位名称
     * @param hl 洪水量级
     * @param bzqid 编制区编码
     * @return
     */
    List<ZxhlTree> getZxhlTreeInfo(String key, String name, String hl, String bzqid);
    /**
     * 中小河流社会经济统计查询
     *
     * @param key 查询条件组合
     * @return
     */
    List<ZxhlShehuijingji> getZxhlShjjTreeInfo(String key);
    /**
     * 中小河流列表县区乡镇详情信息查询
     *
     * @param key   查询条件组合
     * @param adcd  政区编码
     * @param bzqid 编制区id
     * @return
     */
    ZxhlXiangQing getZxhlShjjDetailInfo(String key, String adcd, String bzqid);
    /**
     * 中小河流列表屯详情信息查询
     *
     * @param key   查询条件组合
     * @param hl 编制区id
     * @param adcd  政区编码
     * @param bzqid 编制区id
     * @return
     */
    ZxhlTunXiangqing getZxhlTunShjjDetailInfo(String key, String hl, String adcd, String bzqid);
}
