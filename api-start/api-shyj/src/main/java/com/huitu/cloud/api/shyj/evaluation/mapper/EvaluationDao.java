package com.huitu.cloud.api.shyj.evaluation.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 调查成果 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public interface EvaluationDao {
    /**
     * 分页查询无线广播站信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<Wbrinfo> getWbrinfoList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询无线广播站工况信息
     *
     * @param racd 广播站编码
     * @return
     */
    BsnWirelessinfo getWirelessInfo(@Param("racd") String racd);

    /**
     * 查询无线广播站日志信息
     *
     * @param page 分页page
     * @param racd 广播站编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @param type 类型
     * @return
     */
    IPage<BsnWirelessinfoLog> getWirelessLogInfo(Page page, @Param("racd") String racd, @Param("stm") String stm, @Param("etm") String etm, @Param("type") String type);

    /**
     * 分页查询危险区信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaDanad> getDanList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询历史山洪灾害信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaHsfwater> getHsfwaterList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询需防洪治理山洪沟站信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaGully> getGullyList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询需防洪治理山洪沟站信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @return
     */
    List<IaGully> getGully(@Param("map") Map<String, Object> param);

    /**
     * 查询政区概况统计信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtAdInfo> getDtAdInfoList(Map<String, Object> param);

    /**
     * 查询调查成果统计信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtSurvey> getDtDccgInfoList(Map<String, Object> param);

    /**
     * 查询涉水工程统计信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtWading> getDtWadingInfoList(Map<String, Object> param);

    /**
     * 查询分析评价对象统计信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtAnalysis> getDtFxpjInfoList(Map<String, Object> param);

    /**
     * 查询政区信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtAllEvaluationInfo> getAdInfo(Map<String, Object> param);

    /**
     * 查询危险区统计信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtIaDanad> getDtDanInfo(Map<String, Object> param);

    /**
     * 查询历史山洪统计信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtIaHsfwater> getHsfwaterInfo(Map<String, Object> param);

    /**
     * 查询需防洪治理山洪沟统计信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtIaGully> getGullyInfo(Map<String, Object> param);

    /**
     * 查询社会经济情况统计信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtIaVlgestat> getVlgestatInfo(Map<String, Object> param);

    /**
     * 查询重点区统计信息
     *
     * @param param 查询参数
     * @return
     */
    List<DtKeyAd> getDtKeyAdList(Map<String, Object> param);

    /**
     * 分页查询塘（堰）坝工程信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaDaminfo> getDamList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询重要沿河村落居民户列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaFlrvvlg> getFlrvvlgList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询简易雨量站列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaSrstinfo> getSrstList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询简易水位站列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaSwstinfo> getSwstList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询防治区企事业单位列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaBsnssinfo> getBsnsstList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询重要城（集）镇居民列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaDtresident> getDtresidentList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询历史洪痕列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaHsfm> getIaHsfmList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询查询沟道横断面列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaHsurface> getHsurfaceList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询查询沟道纵断面列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaVsurface> getVsurfaceList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页查询查询查询防治区列表信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<IaAdinfo> getPrevadList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询防治区人口，防治区个数
     *
     * @param param 查询参数
     * @return
     */
    List<DtKeyAd> getDtKeyAdListNew(Map<String, Object> param);

    /**
     * 查询危险区个数
     *
     * @param param 查询参数
     * @return
     */
    List<DtSurvey> getDtDccgInfoListNew(Map<String, Object> param);
}
