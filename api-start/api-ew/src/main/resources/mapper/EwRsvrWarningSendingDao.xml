<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwRsvrWarningSendingDao">
    <select id="getSendingPageList" resultType="com.huitu.cloud.api.ew.entity.ext.EwRsvrWarningSending">
        SELECT A.CALL_ID, A.PHONE_NO, SS.RECEIVER,
        SS.[STATUS] SMS_SEND_STATUS, SS.SEND_TIME SMS_SEND_TIME, SS.[ERROR_MESSAGE] SMS_FAIL_CAUSE,
        XS.[STATUS] XCC_SEND_STATUS, ISNULL(XS.CALL_START_TIME, XS.SEND_TIME) XCC_CALL_TIME, XS.FAIL_CAUSE XCC_FAIL_CAUSE,
        XS.CALL_RESULT XCC_CALL_RESULT, XS.CALL_RESULT_TEXT XCC_CALL_RESULT_TEXT, XS.EXT_SEND_ID XCC_EXT_SEND_ID,
        A.[STATUS] FEEDBACK_STATUS, A.CONFIRM_RESULT, A.CONFIRM_TIME
        FROM EW_CALL_FEEDBACK A
        LEFT JOIN SMS_SENDING SS ON
        EXISTS(SELECT 8 FROM SMS_MESSAGE WHERE BUSINESS_KEY = A.SMS_WARN_MSG_ID AND MSG_ID = SS.MSG_ID) AND SS.PHONE_NO = A.PHONE_NO
        LEFT JOIN XCC_SENDING XS ON XS.EXT_SEND_ID = A.CALL_ID
        LEFT JOIN EW_WARNING_PERSON WP ON WP.PHONE_NO = A.PHONE_NO
        WHERE A.CALL_TYPE = '3'
        <if test="map.smsSendStatusList != null and map.smsSendStatusList != ''">
            AND CHARINDEX(SS.[STATUS], #{map.smsSendStatusList}) > 0
        </if>
        <if test="map.feedbackStatusList != null and map.feedbackStatusList != ''">
            AND CHARINDEX(A.[STATUS], #{map.feedbackStatusList}) > 0
        </if>
        AND EXISTS(SELECT 8 FROM EW_RSVR_WARNING_MESSAGE WHERE PUSH_MODE = '1' AND WARN_ID = #{map.warnId} AND MSG_ID = A.SMS_WARN_MSG_ID)
        ORDER BY dbo.fnGetAdLevel(COALESCE(WP.ADCD, REPLICATE('9', 15))), COALESCE(WP.ADCD, REPLICATE('9', 15)), SS.RECEIVER, A.PHONE_NO ASC
    </select>
</mapper>