package com.huitu.cloud.api.ewci.project.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huitu.cloud.api.ewci.project.entity.BnsShOperation;
import com.huitu.cloud.api.ewci.project.entity.BnsShProject;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 山洪灾害项目运行效益情况和建设进度情况统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
public interface BnsShOperationDao extends BaseMapper<BnsShOperation>  {

    Boolean addOperation(BnsShOperation bnsShOperation);

    Boolean addProject(BnsShProject bnsShProject);

    Boolean updateOperation(BnsShOperation bnsShOperation);

    Boolean updateProject(BnsShProject bnsShProject);

    Boolean addOperationList(@Param("list")List<BnsShOperation> bnsShOperation);

    Boolean addProjectList(@Param("list")List<BnsShProject> bnsShOperation);

    /**
     *  运行效益情况汇总
     * @param bno 批次
     * @return
     */
    boolean summary(String bno);

    /**
     * 查询运行效益情况需要汇总数据
     * @return
     */
    List<BnsShOperation> queryAddList();

    /**
     * 查询市县上报数据
     * @param adcd 政区编码
     * @param stm 开始时间
     * @param etm 结束时间
     * @param level 政区码等级
     * @return
     */
    List<BnsShOperation> getBenefitData(@Param("adcd")String adcd, @Param("stm")String stm, @Param("etm")String etm, @Param("level")int level, @Param("bno")String bno, @Param("type")Integer type,@Param("year")String year);

    /**
     * 查询距离当前时间最近的一条数据
     * @param adcd 政区编码
     * @return
     */
    List<BnsShOperation> getCurrentTimeData(@Param("adcd")String adcd);

    /**
     * 查询批次列表
     * @param adcd 政区编码
     * @param level 政区码等级
     * @return
     */
    List<BnsShOperation> getBnoList(@Param("adcd")String adcd, @Param("level")int level);


    /**
     * 查询市县上报数据
     * @param adcd 政区编码
     * @param stm 开始时间
     * @param etm 结束时间
     * @param level 政区码等级
     * @return
     */
    List<BnsShProject> getrojectData(@Param("adcd")String adcd, @Param("stm")String stm, @Param("etm")String etm, @Param("level")int level, @Param("bno")String bno,@Param("year")String year,@Param("type")Integer type);

    /**
     * 查询批次列表
     * @param adcd 政区编码
     * @param level 政区码等级
     * @return
     */
    List<BnsShProject> getrojectList(@Param("adcd")String adcd, @Param("level")int level);

    /**
     *  运行建设（运行维护）进度情况汇总
     * @param bno 批次
     * @return
     */
    Boolean summaryRojectB(String bno);


    /**
     * 查询运行建设（运行维护）需要汇总数据
     * @return
     */
    List<BnsShProject> queryAddProList();

    /**
     * 查询距离当前时间最近的一条数据
     * @param adcd 政区编码
     * @return
     */
    List<BnsShProject> getCurrentOneData(@Param("adcd")String adcd);

}
