package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.api.ew.entity.base.EwSnapshotMonitor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 山洪预警监测数据快照响应对象
 */
@ApiModel(value = "山洪预警监测数据快照响应对象")
public class EwSnapshotMonitorResponse extends EwSnapshotMonitor {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;
    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STNM")
    private String stnm;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }
}
