package com.huitu.cloud.api.ew.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.base.EwDisasterFile;
import com.huitu.cloud.api.ew.entity.base.EwDisasterRecord;
import com.huitu.cloud.api.ew.entity.ext.EwDisasterFileDTO;
import com.huitu.cloud.api.ew.entity.response.EwDisasterRecordResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwDisasterDao {

    /**
     * 获取灾情记录分页列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 分页列表
     **/
    IPage<EwDisasterRecordResponse> getPageList(IPage<EwDisasterRecordResponse> page, @Param("map") Map<String, Object> params);

    /**
     * 根据灾情ID，获取灾情记录
     *
     * @param disasterId 灾情ID
     * @return 灾情记录
     */
    EwDisasterRecordResponse getRecord(@Param("disasterId") String disasterId);

    /**
     * 根据灾情ID集合，获取现场文件列表
     *
     * @param disasterIds 灾情ID集合
     * @return 文件列表
     */
    List<EwDisasterFile> getFileList(@Param("disasterIds") List<String> disasterIds);

    /**
     * 判断是否存在同政区、同时间范围的灾情记录
     *
     * @param record 灾情记录
     * @return true=存在，false=不存在
     */
    boolean exists(EwDisasterRecord record);

    /**
     * 新增灾情记录
     *
     * @param record 灾情记录
     * @return 受影响的行数
     */
    int insertRecord(EwDisasterRecord record);

    /**
     * 更新灾情记录
     *
     * @param record 灾情记录
     * @return 受影响的行数
     */
    int updateRecord(EwDisasterRecord record);

    /**
     * 删除灾情记录
     *
     * @param disasterId 灾情ID
     * @return 受影响的行数
     */
    int deleteRecord(@Param("disasterId") String disasterId);

    /**
     * 保存文件集合
     *
     * @param dto 文件传输对象
     * @return 受影响的行数
     */
    int saveFileList(EwDisasterFileDTO dto);

    /**
     * 根据灾情ID，删除文件
     *
     * @param disasterId 灾情ID
     * @return 受影响的行数
     */
    int deleteFileByDisasterId(@Param("disasterId") String disasterId);
}
