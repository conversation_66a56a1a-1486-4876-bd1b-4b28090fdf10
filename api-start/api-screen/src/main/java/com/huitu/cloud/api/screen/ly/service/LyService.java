package com.huitu.cloud.api.screen.ly.service;

import com.huitu.cloud.api.screen.fhjz.entity.RainCountVo;
import com.huitu.cloud.api.screen.fhjz.entity.RainVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 流域信息查询 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-15
 */
public interface LyService {

    /**
     * 查询河流基本概况信息
     *
     * @param rvCode
     * @return
     */
    Map queryRvBaseFact(String rvCode);

    List<Map<String, Object>> queryRvBranch(String rvCode, String rvName);

    Map<String, Object> querySkCountByRv(String rvCode);

    Map<String, Object> querySkFhByRv(String rvCode,String engScal);

    List<Map<String, Object>> statisticSkFhByRv(String rvCode,String engScal);

    Map<String, Object> queryDikeLenByRv(String rvCode);

    List<Map<String, Object>> queryDikeStatisticByRv(String rvCode);

    List<Map<String, Object>> queryDikeListByRv(String rvCode);

    Map<String, Object> queryHdCountByRv(String rvCode);

    Map<String, Object> queryHdWarnCountByRv(String rvCode);

    List<Map<String, Object>> queryHdStatisticByRv(String rvCode);

    Map<String, Object> queryRainCountByRv(String rvCode);

    List<Map<String, Object>> queryRainStatisticByRv(String rvCode);

    Map<String, Object> querySkStationCountByRv(String rvCode, String engScal);

    Map<String, Object> querySkStationCxxCountByRv(String rvCode, String engScal);

    List<Map<String, Object>> querySkStationStatisticByRv(String rvCode, String engScal);

    List<Map<String, Object>> queryFhCityListByRv(String rvCode);

    Map<String, Object> queryRv(String rvCode);

    Map<String, Object> queryRvSourLoc(String rvCode);
}
