package com.huitu.cloud.api.syq.rsvr.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 库(湖)站防洪指标表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-11
 */
@TableName("ST_RSVRFCCH_B")
@ApiModel(value="StRsvrfcchB对象", description="库(湖)站防洪指标表")
public class StRsvrfcchB extends Model<StRsvrfcchB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "水库类型")
    @TableField("RSVRTP")
    private String rsvrtp;

    @ApiModelProperty(value = "坝顶高程")
    @TableField("DAMEL")
    private BigDecimal damel;

    @ApiModelProperty(value = "校核洪水位")
    @TableField("CKFLZ")
    private BigDecimal ckflz;

    @ApiModelProperty(value = "设计洪水位")
    @TableField("DSFLZ")
    private BigDecimal dsflz;

    @ApiModelProperty(value = "正常高水位")
    @TableField("NORMZ")
    private BigDecimal normz;

    @ApiModelProperty(value = "死水位")
    @TableField("DDZ")
    private BigDecimal ddz;

    @ApiModelProperty(value = "兴利水位")
    @TableField("ACTZ")
    private BigDecimal actz;

    @ApiModelProperty(value = "总库容")
    @TableField("TTCP")
    private BigDecimal ttcp;

    @ApiModelProperty(value = "防洪库容")
    @TableField("FLDCP")
    private BigDecimal fldcp;

    @ApiModelProperty(value = "兴利库容")
    @TableField("ACTCP")
    private BigDecimal actcp;

    @ApiModelProperty(value = "死库容")
    @TableField("DDCP")
    private BigDecimal ddcp;

    @ApiModelProperty(value = "历史最高库水位")
    @TableField("HHRZ")
    private BigDecimal hhrz;

    @ApiModelProperty(value = "历史最大蓄水量")
    @TableField("HMXW")
    private BigDecimal hmxw;

    @ApiModelProperty(value = "历史最高库水位（蓄水量）出现时间")
    @TableField("HHRZTM")
    private LocalDateTime hhrztm;

    @ApiModelProperty(value = "历史最大入流")
    @TableField("HMXINQ")
    private BigDecimal hmxinq;

    @ApiModelProperty(value = "历史最大入流时段长")
    @TableField("RSTDR")
    private BigDecimal rstdr;

    @ApiModelProperty(value = "历史最大入流出现时间")
    @TableField("HMXINQTM")
    private LocalDateTime hmxinqtm;

    @ApiModelProperty(value = "历史最大出流")
    @TableField("HMXOTQ")
    private BigDecimal hmxotq;

    @ApiModelProperty(value = "历史最大出流出现时间")
    @TableField("HMXOTQTM")
    private LocalDateTime hmxotqtm;

    @ApiModelProperty(value = "历史最低库水位")
    @TableField("HLRZ")
    private BigDecimal hlrz;

    @ApiModelProperty(value = "历史最低库水位出现时间")
    @TableField("HLRZTM")
    private LocalDateTime hlrztm;

    @ApiModelProperty(value = "历史最小日均入流")
    @TableField("HMNINQ")
    private BigDecimal hmninq;

    @ApiModelProperty(value = "历史最小日均入流出现时间")
    @TableField("HMNINQTM")
    private LocalDateTime hmninqtm;

    @ApiModelProperty(value = "低水位告警值")
    @TableField("LAZ")
    private BigDecimal laz;

    @ApiModelProperty(value = "启动预报流量标准")
    @TableField("SFQ")
    private BigDecimal sfq;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getRsvrtp() {
        return rsvrtp;
    }

    public void setRsvrtp(String rsvrtp) {
        this.rsvrtp = rsvrtp;
    }

    public BigDecimal getDamel() {
        return damel;
    }

    public void setDamel(BigDecimal damel) {
        this.damel = damel;
    }

    public BigDecimal getCkflz() {
        return ckflz;
    }

    public void setCkflz(BigDecimal ckflz) {
        this.ckflz = ckflz;
    }

    public BigDecimal getDsflz() {
        return dsflz;
    }

    public void setDsflz(BigDecimal dsflz) {
        this.dsflz = dsflz;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }

    public BigDecimal getDdz() {
        return ddz;
    }

    public void setDdz(BigDecimal ddz) {
        this.ddz = ddz;
    }

    public BigDecimal getActz() {
        return actz;
    }

    public void setActz(BigDecimal actz) {
        this.actz = actz;
    }

    public BigDecimal getTtcp() {
        return ttcp;
    }

    public void setTtcp(BigDecimal ttcp) {
        this.ttcp = ttcp;
    }

    public BigDecimal getFldcp() {
        return fldcp;
    }

    public void setFldcp(BigDecimal fldcp) {
        this.fldcp = fldcp;
    }

    public BigDecimal getActcp() {
        return actcp;
    }

    public void setActcp(BigDecimal actcp) {
        this.actcp = actcp;
    }

    public BigDecimal getDdcp() {
        return ddcp;
    }

    public void setDdcp(BigDecimal ddcp) {
        this.ddcp = ddcp;
    }

    public BigDecimal getHhrz() {
        return hhrz;
    }

    public void setHhrz(BigDecimal hhrz) {
        this.hhrz = hhrz;
    }

    public BigDecimal getHmxw() {
        return hmxw;
    }

    public void setHmxw(BigDecimal hmxw) {
        this.hmxw = hmxw;
    }

    public LocalDateTime getHhrztm() {
        return hhrztm;
    }

    public void setHhrztm(LocalDateTime hhrztm) {
        this.hhrztm = hhrztm;
    }

    public BigDecimal getHmxinq() {
        return hmxinq;
    }

    public void setHmxinq(BigDecimal hmxinq) {
        this.hmxinq = hmxinq;
    }

    public BigDecimal getRstdr() {
        return rstdr;
    }

    public void setRstdr(BigDecimal rstdr) {
        this.rstdr = rstdr;
    }

    public LocalDateTime getHmxinqtm() {
        return hmxinqtm;
    }

    public void setHmxinqtm(LocalDateTime hmxinqtm) {
        this.hmxinqtm = hmxinqtm;
    }

    public BigDecimal getHmxotq() {
        return hmxotq;
    }

    public void setHmxotq(BigDecimal hmxotq) {
        this.hmxotq = hmxotq;
    }

    public LocalDateTime getHmxotqtm() {
        return hmxotqtm;
    }

    public void setHmxotqtm(LocalDateTime hmxotqtm) {
        this.hmxotqtm = hmxotqtm;
    }

    public BigDecimal getHlrz() {
        return hlrz;
    }

    public void setHlrz(BigDecimal hlrz) {
        this.hlrz = hlrz;
    }

    public LocalDateTime getHlrztm() {
        return hlrztm;
    }

    public void setHlrztm(LocalDateTime hlrztm) {
        this.hlrztm = hlrztm;
    }

    public BigDecimal getHmninq() {
        return hmninq;
    }

    public void setHmninq(BigDecimal hmninq) {
        this.hmninq = hmninq;
    }

    public LocalDateTime getHmninqtm() {
        return hmninqtm;
    }

    public void setHmninqtm(LocalDateTime hmninqtm) {
        this.hmninqtm = hmninqtm;
    }

    public BigDecimal getLaz() {
        return laz;
    }

    public void setLaz(BigDecimal laz) {
        this.laz = laz;
    }

    public BigDecimal getSfq() {
        return sfq;
    }

    public void setSfq(BigDecimal sfq) {
        this.sfq = sfq;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "StRsvrfcchB{" +
        "stcd=" + stcd +
        ", rsvrtp=" + rsvrtp +
        ", damel=" + damel +
        ", ckflz=" + ckflz +
        ", dsflz=" + dsflz +
        ", normz=" + normz +
        ", ddz=" + ddz +
        ", actz=" + actz +
        ", ttcp=" + ttcp +
        ", fldcp=" + fldcp +
        ", actcp=" + actcp +
        ", ddcp=" + ddcp +
        ", hhrz=" + hhrz +
        ", hmxw=" + hmxw +
        ", hhrztm=" + hhrztm +
        ", hmxinq=" + hmxinq +
        ", rstdr=" + rstdr +
        ", hmxinqtm=" + hmxinqtm +
        ", hmxotq=" + hmxotq +
        ", hmxotqtm=" + hmxotqtm +
        ", hlrz=" + hlrz +
        ", hlrztm=" + hlrztm +
        ", hmninq=" + hmninq +
        ", hmninqtm=" + hmninqtm +
        ", laz=" + laz +
        ", sfq=" + sfq +
        ", moditime=" + moditime +
        "}";
    }
}
