package com.huitu.cloud.api.syq.rain.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 墒情气象监测查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-04
 */
@ApiModel(value="MoistureMeteorologyQo对象", description="墒情气象监测查询参数")
public class MoistureMeteorologyQo extends PageBean {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = false)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = false)
    private String etm;
    @ApiModelProperty(value = "政区编码 / 政区名称")
    private String query;

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }
}
