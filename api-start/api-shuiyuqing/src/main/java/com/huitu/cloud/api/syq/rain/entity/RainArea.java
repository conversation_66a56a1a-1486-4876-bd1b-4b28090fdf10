package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 雨情笼罩面降雨
 * </p>
 *
 */
@ApiModel(value="RainArea类", description="雨情笼罩面降雨")
public class RainArea  implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码")
    private String adcd;

    @ApiModelProperty(value = "行政区名称")
    private String adnm;

    @ApiModelProperty(value = "父级政区码")
    private String padcd;

    @ApiModelProperty(value = "政区级别")
    private Integer adlvl;

    @ApiModelProperty(value = "流域编码")
    private String basCode;

    @ApiModelProperty(value = "流域名称")
    private String basName;

    @ApiModelProperty(value = "所属上级流域编码")
    private String pbasCode;

    @ApiModelProperty(value = "土地面积")
    private BigDecimal ldarea;

    @ApiModelProperty(value = "总水量（万立方米）")
    private BigDecimal totalDrps;

    @ApiModelProperty(value = "平均雨量")
    private BigDecimal avgDrps;

    @ApiModelProperty(value = ">25mm面积")
    private BigDecimal area25;

    @ApiModelProperty(value = ">50mm面积")
    private BigDecimal area50;

    @ApiModelProperty(value = ">100mm面积")
    private BigDecimal area100;

    @ApiModelProperty(value = " 下级流域")
    private List<RainArea> basChildren;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public Integer getAdlvl() {
        return adlvl;
    }

    public void setAdlvl(Integer adlvl) {
        this.adlvl = adlvl;
    }

    public String getBasCode() {
        return basCode;
    }

    public void setBasCode(String basCode) {
        this.basCode = basCode;
    }

    public String getBasName() {
        return basName;
    }

    public void setBasName(String basName) {
        this.basName = basName;
    }

    public String getPbasCode() {
        return pbasCode;
    }

    public void setPbasCode(String pbasCode) {
        this.pbasCode = pbasCode;
    }

    public BigDecimal getLdarea() {
        return ldarea;
    }

    public void setLdarea(BigDecimal ldarea) {
        this.ldarea = ldarea;
    }

    public BigDecimal getTotalDrps() {
        return totalDrps;
    }

    public void setTotalDrps(BigDecimal totalDrps) {
        this.totalDrps = totalDrps;
    }

    public BigDecimal getAvgDrps() {
        return avgDrps;
    }

    public void setAvgDrps(BigDecimal avgDrps) {
        this.avgDrps = avgDrps;
    }

    public BigDecimal getArea25() {
        return area25;
    }

    public void setArea25(BigDecimal area25) {
        this.area25 = area25;
    }

    public BigDecimal getArea50() {
        return area50;
    }

    public void setArea50(BigDecimal area50) {
        this.area50 = area50;
    }

    public BigDecimal getArea100() {
        return area100;
    }

    public void setArea100(BigDecimal area100) {
        this.area100 = area100;
    }

    public List<RainArea> getBasChildren() {
        return basChildren;
    }

    public void setBasChildren(List<RainArea> basChildren) {
        this.basChildren = basChildren;
    }
}
