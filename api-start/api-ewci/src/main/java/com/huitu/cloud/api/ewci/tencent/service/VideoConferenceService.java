package com.huitu.cloud.api.ewci.tencent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.ewci.tencent.entity.PageParams;
import com.huitu.cloud.api.ewci.tencent.entity.VideoConference;
import com.huitu.cloud.api.ewci.tencent.entity.VideoConferenceVo;

/**
 * <p>
 * 音视频会议表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface VideoConferenceService extends IService<VideoConference> {

    void updateVideoConference(VideoConference entity);

    /**
     * 创建会议
     * @param videoConference
     * @return
     */
    Boolean createConference(VideoConference videoConference);

    /**
     * 查询会议中的列表
     * @param params
     *  - status
     *  - adcd
     *  - pageNum
     *  - pageSize
     *  - userId
     * @return IPage<VideoConferenceVo>
     */
    IPage<VideoConferenceVo> getMeetingList(PageParams params);

    /**
     * 查看会议详情
     * @param roomId 房间号
     * @return VideoConference
     */
    VideoConference getMeetingDetail(String roomId);
}
