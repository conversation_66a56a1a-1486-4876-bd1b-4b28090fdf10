package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 检查文件清单
 */
@ApiModel(value = "检查文件清单")
public class BnsInspectFile implements Serializable {


    @ApiModelProperty(value = "文件ID")
    @TableField(value = "FILE_ID")
    private String fileId;

    @ApiModelProperty(value = "业务KEY-检查ID")
    @TableField(value = "BUSINESS_KEY")
    private String businessKey;

    @ApiModelProperty(value = "文件名称")
    @TableField(value = "FILE_NAME")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    @TableField(value = "FILE_PATH")
    private String filePath;

    @ApiModelProperty(value = "文件类型(1照片2视频3音频4报告)")
    @TableField(value = "FILE_TYPE")
    private String fileType;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
}
