package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 水库(水电站)运行/病险水库 水库下拉
 */
@ApiModel(value = "水库(水电站)运行/病险水库 水库下拉")
public class BnsInspectRsvr implements Serializable {

    @ApiModelProperty(value = "政区编码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "水库编码")
    @TableField(value = "RES_CODE")
    private String resCode;

    @ApiModelProperty(value = "水库名称")
    @TableField(value = "RES_NAME")
    private String resName;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }
}
