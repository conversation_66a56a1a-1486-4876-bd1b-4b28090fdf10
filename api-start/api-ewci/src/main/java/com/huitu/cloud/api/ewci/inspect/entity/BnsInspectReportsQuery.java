package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 检查记录参数
 */
@Data
@ApiModel(value = "检查报告参数")
public class BnsInspectReportsQuery extends PageBean implements Serializable {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "是否确认")
    private String confirmResult;

    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("year", getYear());
        params.put("confirmResult", getConfirmResult());
        return params;
    }

    public IPage<BnsInspectRecordsChild> toPageParam() {
        return new Page<>(Math.max(getPageNum(), 1), getPageSize() > 0 ? getPageSize() : -1);
    }
}
