2025-08-19 08:42:20,540 INFO [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 330] Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$94d7f837] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-19 08:42:21,028 INFO [main] o.s.c.b.c.PropertySourceBootstrapConfiguration [PropertySourceBootstrapConfiguration.java : 98] Located property source: CompositePropertySource {name='NACOS', propertySources=[NacosPropertySource {name='allApi-all.properties'}, NacosPropertySource {name='allApi.properties'}]}
2025-08-19 08:42:21,087 INFO [main] com.huitu.cloud.ApiApplication [SpringApplication.java : 652] The following profiles are active: all
2025-08-19 08:42:25,695 INFO [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 244] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-19 08:42:25,700 INFO [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 126] Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-19 08:42:26,185 INFO [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 182] Finished Spring Data repository scanning in 457ms. Found 0 repository interfaces.
2025-08-19 08:42:26,354 WARN [main] o.s.boot.actuate.endpoint.EndpointId [EndpointId.java : 131] Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-08-19 08:42:26,359 WARN [main] o.s.boot.actuate.endpoint.EndpointId [EndpointId.java : 131] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-19 08:42:26,441 WARN [main] o.s.boot.actuate.endpoint.EndpointId [EndpointId.java : 131] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-19 08:42:26,876 INFO [main] o.s.cloud.context.scope.GenericScope [GenericScope.java : 294] BeanFactory id=629350c5-4838-3eb3-94f8-ab15025c0c70
2025-08-19 08:42:29,012 INFO [main] o.s.b.w.e.tomcat.TomcatWebServer [TomcatWebServer.java : 90] Tomcat initialized with port(s): 8080 (http)
2025-08-19 08:42:29,046 INFO [main] o.a.coyote.http11.Http11NioProtocol [DirectJDKLog.java : 173] Initializing ProtocolHandler ["http-nio-8080"]
2025-08-19 08:42:29,066 INFO [main] o.a.catalina.core.StandardService [DirectJDKLog.java : 173] Starting service [Tomcat]
2025-08-19 08:42:29,066 INFO [main] o.a.catalina.core.StandardEngine [DirectJDKLog.java : 173] Starting Servlet engine: [Apache Tomcat/9.0.44]
2025-08-19 08:42:29,329 INFO [main] o.a.c.c.C.[Tomcat].[localhost].[/] [DirectJDKLog.java : 173] Initializing Spring embedded WebApplicationContext
2025-08-19 08:42:29,329 INFO [main] o.s.web.context.ContextLoader [ServletWebServerApplicationContext.java : 284] Root WebApplicationContext: initialization completed in 8208 ms
2025-08-19 08:42:29,359 INFO [main] c.h.c.config.HuituClientProperties [HuituClientProperties.java : 43] set huitu client properties
2025-08-19 08:42:30,970 INFO [main] c.h.d.c.DataSourceAutoConfiguration [DataSourceAutoConfiguration.java : 54] Init HuituDataSource
2025-08-19 08:42:31,949 INFO [main] c.alibaba.druid.pool.DruidDataSource [DruidDataSource.java : 990] {dataSource-1} inited
2025-08-19 08:42:46,634 INFO [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 171] Initializing ExecutorService
2025-08-19 08:42:46,634 INFO [main] com.huitu.cloud.config.AsyncConfig [AsyncConfig.java : 50] AsyncExecutor configured.
2025-08-19 08:42:46,653 INFO [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 171] Initializing ExecutorService 'asyncExecutor'
2025-08-19 08:42:52,077 INFO [main] o.s.b.a.e.web.EndpointLinksResolver [EndpointLinksResolver.java : 58] Exposing 0 endpoint(s) beneath base path '/actuator'
2025-08-19 08:42:52,898 INFO [main] c.h.c.config.HuituClientProperties [HuituClientProperties.java : 43] set huitu client properties
2025-08-19 08:42:53,136 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 08:42:56,378 INFO [main] o.s.b.a.s.s.UserDetailsServiceAutoConfiguration [UserDetailsServiceAutoConfiguration.java : 83] 

Using generated security password: 8631e549-080f-46d6-b30b-2f39ffbee982

2025-08-19 08:42:59,480 INFO [main] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 171] Initializing ExecutorService
2025-08-19 08:43:05,406 INFO [main] o.a.coyote.http11.Http11NioProtocol [DirectJDKLog.java : 173] Starting ProtocolHandler ["http-nio-8080"]
2025-08-19 08:43:05,504 INFO [main] o.s.b.w.e.tomcat.TomcatWebServer [TomcatWebServer.java : 202] Tomcat started on port(s): 8080 (http) with context path ''
2025-08-19 08:43:05,521 INFO [main] o.s.c.a.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 64] nacos registry, dev-allApi 192.168.1.33:8080 register finished
2025-08-19 08:43:05,532 INFO [main] com.huitu.cloud.ApiApplication [StartupInfoLogger.java : 59] Started ApiApplication in 46.139 seconds (JVM running for 48.265)
2025-08-19 08:43:05,546 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 184] [fixed-192.168.1.10_8848-a37353cf-b689-4011-924a-4cdb36793810] [subscribe] allApi-all.properties+DEFAULT_GROUP+a37353cf-b689-4011-924a-4cdb36793810
2025-08-19 08:43:05,548 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 78] [fixed-192.168.1.10_8848-a37353cf-b689-4011-924a-4cdb36793810] [add-listener] ok, tenant=a37353cf-b689-4011-924a-4cdb36793810, dataId=allApi-all.properties, group=DEFAULT_GROUP, cnt=1
2025-08-19 08:43:05,548 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 184] [fixed-192.168.1.10_8848-a37353cf-b689-4011-924a-4cdb36793810] [subscribe] allApi.properties+DEFAULT_GROUP+a37353cf-b689-4011-924a-4cdb36793810
2025-08-19 08:43:05,548 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 78] [fixed-192.168.1.10_8848-a37353cf-b689-4011-924a-4cdb36793810] [add-listener] ok, tenant=a37353cf-b689-4011-924a-4cdb36793810, dataId=allApi.properties, group=DEFAULT_GROUP, cnt=1
2025-08-19 08:47:53,144 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 08:52:53,154 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 08:57:53,169 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:02:53,182 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:07:53,191 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:12:53,204 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:17:53,212 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:22:53,218 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:27:53,232 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:32:53,244 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:37:53,250 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:42:53,267 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:47:53,271 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:52:53,284 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 09:57:53,296 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:02:53,319 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:07:53,333 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:12:53,340 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:17:53,357 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:22:53,362 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:27:53,369 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:32:53,380 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:37:53,395 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:42:53,414 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:47:53,419 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:52:53,426 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 10:57:53,438 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 11:02:53,444 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
2025-08-19 11:07:53,453 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://192.168.1.10:18081/oauth/cors]----true
