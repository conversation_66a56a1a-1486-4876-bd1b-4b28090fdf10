package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.api.ew.entity.base.EwWarningRecord;
import com.huitu.cloud.api.ew.entity.request.EwWarningRecordRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 山洪预警记录响应对象
 */
@ApiModel(value = "山洪预警记录响应对象")
public class EwWarningRecordResponse extends EwWarningRecord {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;
    @ApiModelProperty(value = "预警类型名称")
    @TableField(value = "WARN_TYPE_NAME")
    private String warnTypeName;
    @ApiModelProperty(value = "预警等级名称")
    @TableField(value = "WARN_GRADE_NAME")
    private String warnGradeName;
    @ApiModelProperty(value = "预警等级别名")
    @TableField(value = "WARN_GRADE_ALIAS_NAME")
    private String warnGradeAliasName;
    @ApiModelProperty(value = "预警状态名称")
    @TableField(value = "WARN_STATUS_NAME")
    private String warnStatusName;
    @ApiModelProperty(value = "预警状态别名")
    @TableField(value = "WARN_STATUS_ALIAS_NAME")
    private String warnStatusAliasName;
    @ApiModelProperty(value = "预警状态简称")
    @TableField(value = "WARN_STATUS_SHORT_NAME")
    private String warnStatusShortName;
    @ApiModelProperty(value = "预警关闭时间")
    @TableField(value = "WARN_CLOSE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warnCloseTime;
    @ApiModelProperty(value = "平台名称")
    @TableField(value = "PLATFORM_NAME")
    private String platformName;
    @ApiModelProperty(value = "子预警数量")
    @TableField(value = "CHILDREN_COUNT")
    private Integer childrenCount;
    @ApiModelProperty(value = "是否为最新预警")
    private Boolean latestWarn;
    @ApiModelProperty(value = "请求参数")
    private EwWarningRecordRequest request;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWarnTypeName() {
        return warnTypeName;
    }

    public void setWarnTypeName(String warnTypeName) {
        this.warnTypeName = warnTypeName;
    }

    public String getWarnGradeName() {
        return warnGradeName;
    }

    public void setWarnGradeName(String warnGradeName) {
        this.warnGradeName = warnGradeName;
    }

    public String getWarnGradeAliasName() {
        return warnGradeAliasName;
    }

    public void setWarnGradeAliasName(String warnGradeAliasName) {
        this.warnGradeAliasName = warnGradeAliasName;
    }

    public String getWarnStatusName() {
        return warnStatusName;
    }

    public void setWarnStatusName(String warnStatusName) {
        this.warnStatusName = warnStatusName;
    }

    public String getWarnStatusAliasName() {
        return warnStatusAliasName;
    }

    public void setWarnStatusAliasName(String warnStatusAliasName) {
        this.warnStatusAliasName = warnStatusAliasName;
    }

    public String getWarnStatusShortName() {
        return warnStatusShortName;
    }

    public void setWarnStatusShortName(String warnStatusShortName) {
        this.warnStatusShortName = warnStatusShortName;
    }

    public Date getWarnCloseTime() {
        return warnCloseTime;
    }

    public void setWarnCloseTime(Date warnCloseTime) {
        this.warnCloseTime = warnCloseTime;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Boolean getLatestWarn() {
        return latestWarn;
    }

    public void setLatestWarn(Boolean latestWarn) {
        this.latestWarn = latestWarn;
    }

    public Integer getChildrenCount() {
        return childrenCount;
    }

    public void setChildrenCount(Integer childrenCount) {
        this.childrenCount = childrenCount;
    }

    public EwWarningRecordRequest getRequest() {
        return request;
    }

    public void setRequest(EwWarningRecordRequest request) {
        this.request = request;
    }
}
