************ - - [12/Aug/2025:08:39:02 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [12/Aug/2025:08:39:02 +0800] "GET /login HTTP/1.1" 302 -
************ - - [12/Aug/2025:08:39:09 +0800] "GET /login?code=uEAMWY&state=l48pHt HTTP/1.1" 302 -
************ - - [12/Aug/2025:08:39:10 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [12/Aug/2025:08:39:11 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1754959151631 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1754959151631 HTTP/1.1" 200 552
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1754959155351 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1754959155351 HTTP/1.1" 200 61368
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1754959155451 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1754959155451 HTTP/1.1" 200 10388
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754959155470 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754959155470 HTTP/1.1" 200 2009
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1754959155599 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+08:39:15&etm=&_timer304=1754959155600 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+09:00&filterCnt=6&_timer304=1754959155600 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754959155600 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754959155600 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754959155600 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1754959155599 HTTP/1.1" 200 1482
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+08:39:15&etm=&_timer304=1754959155600 HTTP/1.1" 200 156
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+09:00&filterCnt=6&_timer304=1754959155600 HTTP/1.1" 200 164
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754959155600 HTTP/1.1" 200 166
************ - - [12/Aug/2025:08:39:15 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:08:39:15 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:39:15 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:08:39:15 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754959155600 HTTP/1.1" 200 169
************ - - [12/Aug/2025:08:39:15 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754959155600 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/base/saas/token?_timer304=1754959156072 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754959156072 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754959156082 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:16 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************ - - [12/Aug/2025:08:39:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:08:39:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:08:39:19 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [12/Aug/2025:08:39:19 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [12/Aug/2025:08:39:19 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1754959160231 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+08:39:20&etm=&_timer304=1754959160382 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+09:00&filterCnt=6&_timer304=1754959160382 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754959160382 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754959160382 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754959160382 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754959160546 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754959160387 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754959160382 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [12/Aug/2025:08:39:20 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754959160569 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:20 +0800] "GET /api/base/saas/token?_timer304=1754959156072 HTTP/1.1" 200 411
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [12/Aug/2025:08:39:20 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754959156082 HTTP/1.1" 200 2009
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [12/Aug/2025:08:39:20 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+09:00&filterCnt=6&_timer304=1754959160382 HTTP/1.1" 200 164
************ - - [12/Aug/2025:08:39:20 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+08:39:20&etm=&_timer304=1754959160382 HTTP/1.1" 200 156
************ - - [12/Aug/2025:08:39:20 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754959160382 HTTP/1.1" 200 166
************ - - [12/Aug/2025:08:39:20 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754959160382 HTTP/1.1" 200 169
************ - - [12/Aug/2025:08:39:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [12/Aug/2025:08:39:20 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754959160382 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:08:39:21 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754959160387 HTTP/1.1" 200 159616
************ - - [12/Aug/2025:08:39:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754959160382 HTTP/1.1" 200 161
************ - - [12/Aug/2025:08:39:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [12/Aug/2025:08:39:21 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [12/Aug/2025:08:39:21 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754959160569 HTTP/1.1" 200 258
************ - - [12/Aug/2025:08:39:21 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1754959160231 HTTP/1.1" 200 144
************ - - [12/Aug/2025:08:39:21 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [12/Aug/2025:08:39:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754959161537 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754959161543 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754959161544 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754959161537 HTTP/1.1" 200 160
************ - - [12/Aug/2025:08:39:21 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [12/Aug/2025:08:39:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754959161543 HTTP/1.1" 200 159
************ - - [12/Aug/2025:08:39:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754959161544 HTTP/1.1" 200 148
************ - - [12/Aug/2025:08:39:22 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [12/Aug/2025:08:39:22 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754959162330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:22 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754959162330 HTTP/1.1" 200 148
************ - - [12/Aug/2025:08:39:22 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754959162363 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:22 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754959160546 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:08:39:22 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754959162363 HTTP/1.1" 200 258
************ - - [12/Aug/2025:08:39:22 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [12/Aug/2025:08:39:22 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [12/Aug/2025:08:39:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [12/Aug/2025:08:39:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3624
************ - - [12/Aug/2025:08:39:25 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754959156072 HTTP/1.1" 200 396
************ - - [12/Aug/2025:08:39:25 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754959165412 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:25 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754959165412 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:25 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754959165412 HTTP/1.1" 200 12285
************ - - [12/Aug/2025:08:39:25 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754959165412 HTTP/1.1" 200 12285
************ - - [12/Aug/2025:08:39:25 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [12/Aug/2025:08:39:31 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [12/Aug/2025:08:39:33 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29230
************ - - [12/Aug/2025:08:39:34 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754959174282 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:34 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754959174282 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:34 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754959174282 HTTP/1.1" 200 520
************ - - [12/Aug/2025:08:39:34 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754959174282 HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:39:49 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:39:49 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:40:16 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:40:17 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 241
************ - - [12/Aug/2025:08:40:24 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:40:24 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:40:35 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:40:35 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:40:49 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:40:50 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:41:53 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:41:53 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 241
************ - - [12/Aug/2025:08:42:02 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:42:02 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:42:04 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:42:05 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 241
************ - - [12/Aug/2025:08:42:34 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:42:34 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:43:01 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:43:01 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 241
************ - - [12/Aug/2025:08:43:10 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:43:10 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 241
************ - - [12/Aug/2025:08:43:50 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:43:50 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:08:43:53 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754959433323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:43:53 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754959433323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:43:53 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754959433323 HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:43:53 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754959433323 HTTP/1.1" 200 520
************ - - [12/Aug/2025:08:44:06 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:44:06 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:44:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754959451537 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:44:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754959451537 HTTP/1.1" 200 160
************ - - [12/Aug/2025:08:44:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754959460392 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:44:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754959460392 HTTP/1.1" 200 161
************ - - [12/Aug/2025:08:44:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:44:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:44:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:44:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754959461390 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:44:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:08:44:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:08:44:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754959462378 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:44:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754959462378 HTTP/1.1" 200 159
************ - - [12/Aug/2025:08:44:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754959461390 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:08:44:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [12/Aug/2025:08:46:08 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:09 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:46:16 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:16 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 241
************ - - [12/Aug/2025:08:46:17 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:17 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:46:21 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:21 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 241
************ - - [12/Aug/2025:08:46:23 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:23 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:46:27 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002102&_timer304=1754959587835 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:27 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&warnGradeId=2&_timer304=1754959587835 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:27 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002102&_timer304=1754959587835 HTTP/1.1" 200 155
************ - - [12/Aug/2025:08:46:27 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&warnGradeId=2&_timer304=1754959587835 HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:46:28 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:29 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4083
************ - - [12/Aug/2025:08:46:32 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002103&_timer304=1754959592106 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:32 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=AE54BBD6-E320-4F10-948E-82EA02CDC265&warnGradeId=2&_timer304=1754959592106 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:32 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=AE54BBD6-E320-4F10-948E-82EA02CDC265&warnGradeId=2&_timer304=1754959592106 HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:46:32 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002103&_timer304=1754959592106 HTTP/1.1" 200 155
************ - - [12/Aug/2025:08:46:33 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:33 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4083
************ - - [12/Aug/2025:08:46:38 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212106&_timer304=1754959598106 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:38 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=AE8F9369-EB92-4E0F-BECC-D63FE530D024&warnGradeId=2&_timer304=1754959598106 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:38 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=AE8F9369-EB92-4E0F-BECC-D63FE530D024&warnGradeId=2&_timer304=1754959598106 HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:46:38 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212106&_timer304=1754959598106 HTTP/1.1" 200 520
************ - - [12/Aug/2025:08:46:39 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:46:39 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:47:05 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212106&_timer304=1754959625948 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:47:05 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=AE8F9369-EB92-4E0F-BECC-D63FE530D024&warnGradeId=2&_timer304=1754959625948 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:47:05 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=AE8F9369-EB92-4E0F-BECC-D63FE530D024&warnGradeId=2&_timer304=1754959625948 HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:47:05 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212106&_timer304=1754959625948 HTTP/1.1" 200 520
************ - - [12/Aug/2025:08:47:07 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:47:07 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:47:49 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:47:49 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:49:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754959752376 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:49:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754959752376 HTTP/1.1" 200 160
************ - - [12/Aug/2025:08:49:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754959760384 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:49:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754959760384 HTTP/1.1" 200 161
************ - - [12/Aug/2025:08:49:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:49:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:49:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:49:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754959761378 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:49:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:08:49:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:08:49:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754959761378 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:08:49:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [12/Aug/2025:08:49:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754959763385 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:49:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754959763385 HTTP/1.1" 200 159
************ - - [12/Aug/2025:08:53:45 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:47 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [12/Aug/2025:08:53:50 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754960030310 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:50 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754960030310 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:50 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:50 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754960030310 HTTP/1.1" 200 443
************ - - [12/Aug/2025:08:53:50 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:08:53:50 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754960030470 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:50 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754960030470 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:50 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754960030470 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:50 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754960030470 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:50 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754960030310 HTTP/1.1" 200 510
************ - - [12/Aug/2025:08:53:50 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754960030470 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:08:53:50 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754960030470 HTTP/1.1" 200 329
************ - - [12/Aug/2025:08:53:50 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754960030470 HTTP/1.1" 200 727
************ - - [12/Aug/2025:08:53:51 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754960030470 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:08:53:51 +0800] "OPTIONS /api/ew/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:52 +0800] "POST /api/ew/warning/sending-page-list HTTP/1.1" 200 7947
************ - - [12/Aug/2025:08:53:54 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=E60C24A4-4EC9-43F9-8A20-4538CC15ED71&_timer304=1754960034713 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:53:54 +0800] "GET /api/call/common/message/call-result?extSendId=E60C24A4-4EC9-43F9-8A20-4538CC15ED71&_timer304=1754960034713 HTTP/1.1" 200 1288
************ - - [12/Aug/2025:08:54:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754960051539 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:54:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754960051539 HTTP/1.1" 200 160
************ - - [12/Aug/2025:08:54:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754960060390 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:54:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754960060390 HTTP/1.1" 200 161
************ - - [12/Aug/2025:08:54:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:54:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:54:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:54:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754960061385 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:54:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:08:54:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:08:54:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754960061385 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:08:54:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [12/Aug/2025:08:54:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754960064383 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:54:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754960064383 HTTP/1.1" 200 159
************ - - [12/Aug/2025:08:55:10 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:55:10 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:08:55:11 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754960111752 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:55:11 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754960111752 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:55:11 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754960111752 HTTP/1.1" 200 153
************ - - [12/Aug/2025:08:55:11 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754960111752 HTTP/1.1" 200 520
************ - - [12/Aug/2025:08:55:12 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:55:13 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:55:30 +0800] "OPTIONS /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:55:30 +0800] "OPTIONS /api/ewci/bia/keyArea/select-basic-info/220602100212104?_timer304=1754960130224 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:55:30 +0800] "POST /api/shyj/eton/dccg/select-dan-list HTTP/1.1" 200 626
************ - - [12/Aug/2025:08:55:30 +0800] "GET /api/ewci/bia/keyArea/select-basic-info/220602100212104?_timer304=1754960130224 HTTP/1.1" 200 387
************ - - [12/Aug/2025:08:55:31 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:55:32 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:57:51 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754960271806 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:57:51 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754960271806 HTTP/1.1" 200 646
************ - - [12/Aug/2025:08:57:52 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:57:53 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3329
************ - - [12/Aug/2025:08:58:44 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:58:45 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3326
************ - - [12/Aug/2025:08:58:48 +0800] "OPTIONS /api/call/common/message/call-result?_timer304=1754960328699 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:58:48 +0800] "GET /api/call/common/message/call-result?_timer304=1754960328699 HTTP/1.1" 200 141
************ - - [12/Aug/2025:08:58:58 +0800] "OPTIONS /api/call/common/message/call-result?_timer304=1754960338234 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:58:58 +0800] "GET /api/call/common/message/call-result?_timer304=1754960338234 HTTP/1.1" 200 141
************ - - [12/Aug/2025:08:58:59 +0800] "OPTIONS /api/call/common/message/call-result?_timer304=1754960339378 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:58:59 +0800] "GET /api/call/common/message/call-result?_timer304=1754960339378 HTTP/1.1" 200 141
************ - - [12/Aug/2025:08:59:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754960351538 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:59:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754960351538 HTTP/1.1" 200 160
************ - - [12/Aug/2025:08:59:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754960360248 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:59:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754960360248 HTTP/1.1" 200 161
************ - - [12/Aug/2025:08:59:20 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:59:20 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:59:20 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754960360489 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:59:20 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:59:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:08:59:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:08:59:21 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754960360489 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:08:59:21 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [12/Aug/2025:08:59:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754960364448 HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:59:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754960364448 HTTP/1.1" 200 159
************ - - [12/Aug/2025:08:59:50 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:08:59:50 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3343
************ - - [12/Aug/2025:09:01:13 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:01:13 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3375
************ - - [12/Aug/2025:09:01:45 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:01:45 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3375
************ - - [12/Aug/2025:09:02:32 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:02:32 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3375
************ - - [12/Aug/2025:09:02:32 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:02:33 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3375
************ - - [12/Aug/2025:09:06:19 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:06:21 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:06:22 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754960782723 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:06:22 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754960782723 HTTP/1.1" 200 600
************ - - [12/Aug/2025:09:09:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754960951541 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:09:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754960951541 HTTP/1.1" 200 160
************ - - [12/Aug/2025:09:09:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754960960250 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:09:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754960960250 HTTP/1.1" 200 161
************ - - [12/Aug/2025:09:09:20 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:09:20 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:09:20 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:09:20 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754960960493 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:09:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:09:09:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:09:09:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [12/Aug/2025:09:09:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754960960493 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:09:09:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754960967440 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:09:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754960967440 HTTP/1.1" 200 159
************ - - [12/Aug/2025:09:09:33 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754960973686 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:09:33 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754960973686 HTTP/1.1" 200 600
************ - - [12/Aug/2025:09:10:36 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961036794 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:10:36 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961036794 HTTP/1.1" 200 600
************ - - [12/Aug/2025:09:10:55 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961055154 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:10:55 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961055154 HTTP/1.1" 200 600
************ - - [12/Aug/2025:09:11:00 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961060419 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:11:00 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961060419 HTTP/1.1" 200 600
************ - - [12/Aug/2025:09:13:43 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961223132 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:13:43 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961223132 HTTP/1.1" 200 1109
************ - - [12/Aug/2025:09:13:43 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961223996 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:13:44 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961223996 HTTP/1.1" 200 1109
************ - - [12/Aug/2025:09:14:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754961253383 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:14:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754961253383 HTTP/1.1" 200 160
************ - - [12/Aug/2025:09:14:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754961260378 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:14:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754961260378 HTTP/1.1" 200 161
************ - - [12/Aug/2025:09:14:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:14:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:14:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:14:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754961262390 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:14:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:09:14:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:09:14:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754961262390 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:09:14:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [12/Aug/2025:09:14:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754961268381 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:14:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754961268381 HTTP/1.1" 200 159
************ - - [12/Aug/2025:09:15:11 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961311002 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:15:11 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961311002 HTTP/1.1" 200 968
************ - - [12/Aug/2025:09:15:13 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:15:14 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:15:14 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961314754 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:15:14 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961314754 HTTP/1.1" 200 968
************ - - [12/Aug/2025:09:15:44 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:15:44 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:15:45 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:15:45 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:15:46 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961346516 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:15:46 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961346516 HTTP/1.1" 200 1293
************ - - [12/Aug/2025:09:18:47 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961527541 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:18:47 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961527541 HTTP/1.1" 200 1293
************ - - [12/Aug/2025:09:19:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754961553375 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:19:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754961553375 HTTP/1.1" 200 160
************ - - [12/Aug/2025:09:19:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754961560390 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:19:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754961560390 HTTP/1.1" 200 161
************ - - [12/Aug/2025:09:19:22 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:19:22 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:19:22 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:19:22 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754961562380 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:19:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:09:19:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:09:19:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754961562380 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:09:19:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [12/Aug/2025:09:19:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754961569387 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:19:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754961569387 HTTP/1.1" 200 159
************ - - [12/Aug/2025:09:20:56 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961656869 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:20:56 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961656869 HTTP/1.1" 200 1327
************ - - [12/Aug/2025:09:21:33 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:34 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3790
************ - - [12/Aug/2025:09:21:35 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:35 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3790
************ - - [12/Aug/2025:09:21:41 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:41 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:21:42 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:42 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:21:42 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:43 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:21:49 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:49 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 358999
************ - - [12/Aug/2025:09:21:49 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754961709838 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:49 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754961709838 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:49 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:49 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754961709838 HTTP/1.1" 200 443
************ - - [12/Aug/2025:09:21:49 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:09:21:49 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754961709838 HTTP/1.1" 200 510
************ - - [12/Aug/2025:09:21:49 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754961709945 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:49 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754961709945 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:49 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754961709945 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:49 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754961709945 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:49 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754961709945 HTTP/1.1" 200 329
************ - - [12/Aug/2025:09:21:49 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754961709945 HTTP/1.1" 200 727
************ - - [12/Aug/2025:09:21:49 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754961709945 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:09:21:50 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754961709945 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:09:21:50 +0800] "OPTIONS /api/ew/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:51 +0800] "POST /api/ew/warning/sending-page-list HTTP/1.1" 200 7947
************ - - [12/Aug/2025:09:21:59 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:21:59 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:09:22:01 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754961721981 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:22:01 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754961721981 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:22:02 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754961721981 HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:22:02 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754961721981 HTTP/1.1" 200 520
************ - - [12/Aug/2025:09:22:03 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:22:03 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:24:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754961859701 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754961859701 HTTP/1.1" 200 160
************ - - [12/Aug/2025:09:24:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754961860247 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:20 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754961860247 HTTP/1.1" 200 161
************ - - [12/Aug/2025:09:24:20 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:20 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:20 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754961860490 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:20 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:09:24:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:09:24:21 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754961860490 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:09:24:21 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [12/Aug/2025:09:24:22 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961862073 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:22 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754961862073 HTTP/1.1" 200 598
************ - - [12/Aug/2025:09:24:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754961869429 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754961869429 HTTP/1.1" 200 159
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754961888594 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754961888590 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754961888590 HTTP/1.1" 200 161
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+09:24:48&etm=&_timer304=1754961888652 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754961888652 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+10:00&filterCnt=6&_timer304=1754961888652 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754961888652 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754961888652 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754961888594 HTTP/1.1" 200 159616
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754961888729 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754961888774 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+09:24:48&etm=&_timer304=1754961888652 HTTP/1.1" 200 156
************ - - [12/Aug/2025:09:24:48 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754961888652 HTTP/1.1" 200 166
************ - - [12/Aug/2025:09:24:48 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+10:00&filterCnt=6&_timer304=1754961888652 HTTP/1.1" 200 164
************ - - [12/Aug/2025:09:24:48 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:24:48 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754961888652 HTTP/1.1" 200 169
************ - - [12/Aug/2025:09:24:48 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:24:48 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:09:24:48 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:09:24:48 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:48 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [12/Aug/2025:09:24:49 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [12/Aug/2025:09:24:49 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754961888774 HTTP/1.1" 200 258
************ - - [12/Aug/2025:09:24:49 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754961888652 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:09:24:49 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [12/Aug/2025:09:24:49 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:49 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [12/Aug/2025:09:24:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754961889935 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:50 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:50 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754961889935 HTTP/1.1" 200 148
************ - - [12/Aug/2025:09:24:50 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [12/Aug/2025:09:24:50 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [12/Aug/2025:09:24:50 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [12/Aug/2025:09:24:50 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754961890480 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:50 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754961888729 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:09:24:50 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754961890480 HTTP/1.1" 200 258
************ - - [12/Aug/2025:09:24:50 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:51 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [12/Aug/2025:09:24:51 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:52 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 3624
************ - - [12/Aug/2025:09:24:55 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:55 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:09:24:57 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754961897352 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:57 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754961897352 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:57 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754961897352 HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:24:57 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754961897352 HTTP/1.1" 200 520
************ - - [12/Aug/2025:09:24:58 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754961898332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:58 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754961898332 HTTP/1.1" 200 160
************ - - [12/Aug/2025:09:24:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754961898354 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754961898354 HTTP/1.1" 200 159
************ - - [12/Aug/2025:09:24:58 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:58 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754961898423 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:24:58 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754961898423 HTTP/1.1" 200 148
************ - - [12/Aug/2025:09:24:58 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:26:23 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:26:23 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:09:26:24 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754961984713 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:26:24 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754961984713 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:26:24 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754961984713 HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:26:24 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754961984713 HTTP/1.1" 200 520
************ - - [12/Aug/2025:09:26:25 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:26:26 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:26:44 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754962004771 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:26:44 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754962004771 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:26:44 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754962004771 HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:26:44 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754962004771 HTTP/1.1" 200 520
************ - - [12/Aug/2025:09:26:45 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:26:46 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3807
************ - - [12/Aug/2025:09:29:48 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754962188387 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:29:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754962188387 HTTP/1.1" 200 160
************ - - [12/Aug/2025:09:29:49 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754962189381 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:29:49 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:29:49 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:29:49 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754962189384 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:29:49 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:29:49 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754962189381 HTTP/1.1" 200 161
************ - - [12/Aug/2025:09:29:49 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:09:29:50 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:09:29:50 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754962189384 HTTP/1.1" 200 441332
************ - - [12/Aug/2025:09:29:50 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************ - - [12/Aug/2025:09:29:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754962199384 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:29:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754962199384 HTTP/1.1" 200 159
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754962315762 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754962315586 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+09:31:55&etm=&_timer304=1754962315762 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754962315592 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+10:00&filterCnt=6&_timer304=1754962315762 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754962315762 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754962315762 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754962315990 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:56 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754962316263 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:31:59 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754962315592 HTTP/1.1" 200 159616
************ - - [12/Aug/2025:09:31:59 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754962315762 HTTP/1.1" 200 166
************ - - [12/Aug/2025:09:31:59 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:09:31:59 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:09:31:59 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754962315586 HTTP/1.1" 200 161
************ - - [12/Aug/2025:09:31:59 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+09:31:55&etm=&_timer304=1754962315762 HTTP/1.1" 200 156
************ - - [12/Aug/2025:09:31:59 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+10:00&filterCnt=6&_timer304=1754962315762 HTTP/1.1" 200 164
************ - - [12/Aug/2025:09:31:59 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:31:59 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:31:59 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754962315762 HTTP/1.1" 200 169
************ - - [12/Aug/2025:09:32:00 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [12/Aug/2025:09:32:00 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [12/Aug/2025:09:32:00 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754962316263 HTTP/1.1" 200 258
************ - - [12/Aug/2025:09:32:00 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:00 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [12/Aug/2025:09:32:00 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754962315762 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:09:32:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [12/Aug/2025:09:32:02 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [12/Aug/2025:09:32:02 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [12/Aug/2025:09:32:02 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754962322483 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:02 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754962315990 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:09:32:02 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754962322483 HTTP/1.1" 200 258
************ - - [12/Aug/2025:09:32:02 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:03 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1383
************ - - [12/Aug/2025:09:32:03 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:04 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 4775
************ - - [12/Aug/2025:09:32:04 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1754962324394 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:04 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1754962324394 HTTP/1.1" 200 232
************ - - [12/Aug/2025:09:32:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754962325410 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754962325411 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:05 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754962325413 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:05 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754962325413 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:07 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754962325413 HTTP/1.1" 200 152
************ - - [12/Aug/2025:09:32:07 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754962325413 HTTP/1.1" 200 148
************ - - [12/Aug/2025:09:32:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754962325410 HTTP/1.1" 200 160
************ - - [12/Aug/2025:09:32:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754962325411 HTTP/1.1" 200 159
************ - - [12/Aug/2025:09:32:24 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754962344410 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:24 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754962344410 HTTP/1.1" 200 148
************ - - [12/Aug/2025:09:32:24 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:24 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [12/Aug/2025:09:32:53 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:53 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 358999
************ - - [12/Aug/2025:09:32:54 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754962374663 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:54 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754962374663 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:54 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:54 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754962374663 HTTP/1.1" 200 510
************ - - [12/Aug/2025:09:32:54 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754962374663 HTTP/1.1" 200 443
************ - - [12/Aug/2025:09:32:54 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:09:32:54 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754962374756 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:54 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754962374756 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:54 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754962374756 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:54 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754962374756 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:54 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754962374756 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:09:32:54 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754962374756 HTTP/1.1" 200 727
************ - - [12/Aug/2025:09:32:54 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754962374756 HTTP/1.1" 200 329
************ - - [12/Aug/2025:09:32:55 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754962374756 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:09:32:56 +0800] "OPTIONS /api/ew/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:32:57 +0800] "POST /api/ew/warning/sending-page-list HTTP/1.1" 200 7947
************ - - [12/Aug/2025:09:33:05 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205100&_timer304=1754962385592 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:33:05 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=6D3B5620-3D7D-45DD-A7B8-10DDE638586F&warnGradeId=2&_timer304=1754962385592 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:33:05 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=6D3B5620-3D7D-45DD-A7B8-10DDE638586F&warnGradeId=2&_timer304=1754962385592 HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:33:05 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205100&_timer304=1754962385592 HTTP/1.1" 200 514
************ - - [12/Aug/2025:09:33:05 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=6D3B5620-3D7D-45DD-A7B8-10DDE638586F&_timer304=1754962385643 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:33:05 +0800] "GET /api/fusion/warning/snapshot-index?warnId=6D3B5620-3D7D-45DD-A7B8-10DDE638586F&_timer304=1754962385643 HTTP/1.1" 200 655
************ - - [12/Aug/2025:09:33:06 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:33:07 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 5064
************ - - [12/Aug/2025:09:33:11 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:33:12 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:09:33:13 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754962393744 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:33:13 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754962393744 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:33:13 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754962393744 HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:33:13 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754962393744 HTTP/1.1" 200 520
************ - - [12/Aug/2025:09:33:13 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754962393781 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:33:13 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754962393781 HTTP/1.1" 200 646
************ - - [12/Aug/2025:09:33:14 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:33:15 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:09:34:28 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754962468436 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:28 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754962468436 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:28 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754962468436 HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:34:28 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754962468469 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:28 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754962468436 HTTP/1.1" 200 520
************ - - [12/Aug/2025:09:34:28 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754962468469 HTTP/1.1" 200 646
************ - - [12/Aug/2025:09:34:31 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&warnGradeId=2&_timer304=1754962471861 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:31 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002102&_timer304=1754962471861 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:31 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103002102&_timer304=1754962471861 HTTP/1.1" 200 155
************ - - [12/Aug/2025:09:34:31 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&warnGradeId=2&_timer304=1754962471861 HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:34:31 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1754962471902 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:31 +0800] "GET /api/fusion/warning/snapshot-index?warnId=B2E66C42-1E8E-4C60-8ADE-968580E801F9&_timer304=1754962471902 HTTP/1.1" 200 646
************ - - [12/Aug/2025:09:34:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754962478848 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:38 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754962478852 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:38 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754962478848 HTTP/1.1" 200 161
************ - - [12/Aug/2025:09:34:38 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754962478852 HTTP/1.1" 200 159616
************ - - [12/Aug/2025:09:34:38 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:38 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+09:34:38&etm=&_timer304=1754962478917 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:38 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754962478917 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+10:00&filterCnt=6&_timer304=1754962478917 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754962478917 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754962478917 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754962478997 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+09:34:38&etm=&_timer304=1754962478917 HTTP/1.1" 200 156
************ - - [12/Aug/2025:09:34:39 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:09:34:39 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:09:34:39 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+10:00&filterCnt=6&_timer304=1754962478917 HTTP/1.1" 200 164
************ - - [12/Aug/2025:09:34:39 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754962478917 HTTP/1.1" 200 166
************ - - [12/Aug/2025:09:34:39 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:34:39 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [12/Aug/2025:09:34:39 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754962479042 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754962478917 HTTP/1.1" 200 169
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754962478917 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:09:34:39 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754962479042 HTTP/1.1" 200 258
************ - - [12/Aug/2025:09:34:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [12/Aug/2025:09:34:39 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [12/Aug/2025:09:34:39 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [12/Aug/2025:09:34:40 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754962478997 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:09:34:40 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [12/Aug/2025:09:34:40 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [12/Aug/2025:09:34:40 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754962480676 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:40 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754962480676 HTTP/1.1" 200 258
************ - - [12/Aug/2025:09:34:40 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:41 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754962481138 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:41 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754962481138 HTTP/1.1" 200 148
************ - - [12/Aug/2025:09:34:41 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:41 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [12/Aug/2025:09:34:41 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1383
************ - - [12/Aug/2025:09:34:41 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:42 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 4775
************ - - [12/Aug/2025:09:34:48 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:48 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:09:34:48 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754962488590 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754962488590 HTTP/1.1" 200 160
************ - - [12/Aug/2025:09:34:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754962488628 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:48 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754962488629 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:48 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754962488629 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:48 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754962488629 HTTP/1.1" 200 148
************ - - [12/Aug/2025:09:34:48 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754962488629 HTTP/1.1" 200 152
************ - - [12/Aug/2025:09:34:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754962488628 HTTP/1.1" 200 159
************ - - [12/Aug/2025:09:34:49 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754962489211 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:49 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754962489211 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:49 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754962489211 HTTP/1.1" 200 153
************ - - [12/Aug/2025:09:34:49 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754962489211 HTTP/1.1" 200 520
************ - - [12/Aug/2025:09:34:49 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754962489245 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:49 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754962489245 HTTP/1.1" 200 646
************ - - [12/Aug/2025:09:34:50 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:50 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:09:34:56 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:56 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 241
************ - - [12/Aug/2025:09:34:58 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:34:59 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:09:35:03 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:35:03 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 3633
************ - - [12/Aug/2025:09:35:05 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:35:05 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 767
************ - - [12/Aug/2025:09:35:08 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:35:09 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:09:35:50 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:35:51 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:09:35:51 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:35:52 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:09:39:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754962779378 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:39:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754962779379 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:39:39 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:39:39 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:39:39 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754962779381 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:39:39 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:39:39 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754962779378 HTTP/1.1" 200 161
************ - - [12/Aug/2025:09:39:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754962779379 HTTP/1.1" 200 160
************ - - [12/Aug/2025:09:39:39 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:09:39:40 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:09:39:40 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754962779381 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:09:39:40 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:09:39:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754962789390 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:39:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754962789391 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:39:49 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754962789391 HTTP/1.1" 200 -
************ - - [12/Aug/2025:09:39:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754962789391 HTTP/1.1" 200 148
************ - - [12/Aug/2025:09:39:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754962789390 HTTP/1.1" 200 159
************ - - [12/Aug/2025:09:39:49 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754962789391 HTTP/1.1" 200 152
************ - - [12/Aug/2025:10:43:20 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [12/Aug/2025:10:43:20 +0800] "GET /login HTTP/1.1" 302 -
************ - - [12/Aug/2025:10:44:02 +0800] "GET /login?code=nwtyxI&state=rT2lZv HTTP/1.1" 302 -
************ - - [12/Aug/2025:10:44:02 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [12/Aug/2025:10:44:04 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1754966644534 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1754966644534 HTTP/1.1" 200 552
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1754966647771 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1754966647771 HTTP/1.1" 200 61368
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1754966647842 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1754966647842 HTTP/1.1" 200 10388
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754966647859 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754966647859 HTTP/1.1" 200 2009
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1754966647978 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754966647978 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+11:00&filterCnt=6&_timer304=1754966647978 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754966647978 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+10:44:07&etm=&_timer304=1754966647978 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754966647978 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:07 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1754966647978 HTTP/1.1" 200 1482
************ - - [12/Aug/2025:10:44:08 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754966647978 HTTP/1.1" 200 166
************ - - [12/Aug/2025:10:44:08 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+11:00&filterCnt=6&_timer304=1754966647978 HTTP/1.1" 200 164
************ - - [12/Aug/2025:10:44:08 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:10:44:08 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:10:44:08 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+10:44:07&etm=&_timer304=1754966647978 HTTP/1.1" 200 156
************ - - [12/Aug/2025:10:44:08 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:10:44:08 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:10:44:08 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754966647978 HTTP/1.1" 200 169
************ - - [12/Aug/2025:10:44:08 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754966647978 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754966648362 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/base/saas/token?_timer304=1754966648362 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754966648372 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:08 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:09 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************ - - [12/Aug/2025:10:44:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:10:44:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:10:44:10 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1379
************ - - [12/Aug/2025:10:44:10 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 1743
************ - - [12/Aug/2025:10:44:10 +0800] "GET /api/base/saas/token?_timer304=1754966648362 HTTP/1.1" 200 411
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [12/Aug/2025:10:44:11 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754966648372 HTTP/1.1" 200 2009
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1754966651300 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754966651434 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+11:00&filterCnt=6&_timer304=1754966651435 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+10:44:11&etm=&_timer304=1754966651434 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754966651435 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754966651435 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754966651435 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754966651439 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:10:44:11 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+10:44:11&etm=&_timer304=1754966651434 HTTP/1.1" 200 156
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:10:44:11 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754966651434 HTTP/1.1" 200 166
************ - - [12/Aug/2025:10:44:11 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+11:00&filterCnt=6&_timer304=1754966651435 HTTP/1.1" 200 164
************ - - [12/Aug/2025:10:44:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754966651435 HTTP/1.1" 200 161
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754966651565 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754966651435 HTTP/1.1" 200 169
************ - - [12/Aug/2025:10:44:11 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754966651591 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:11 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754966651435 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:10:44:11 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754966651439 HTTP/1.1" 200 159616
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [12/Aug/2025:10:44:11 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1754966651300 HTTP/1.1" 200 144
************ - - [12/Aug/2025:10:44:11 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [12/Aug/2025:10:44:12 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754966651591 HTTP/1.1" 200 258
************ - - [12/Aug/2025:10:44:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [12/Aug/2025:10:44:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [12/Aug/2025:10:44:12 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [12/Aug/2025:10:44:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [12/Aug/2025:10:44:12 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754966652698 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:12 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754966652698 HTTP/1.1" 200 258
************ - - [12/Aug/2025:10:44:13 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754966648362 HTTP/1.1" 200 396
************ - - [12/Aug/2025:10:44:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754966651565 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:10:44:13 +0800] "OPTIONS /api/syq/video/select-video-count-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:13 +0800] "OPTIONS /api/syq/video/select-video-info-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:13 +0800] "POST /api/syq/video/select-video-count-info HTTP/1.1" 200 304
************ - - [12/Aug/2025:10:44:13 +0800] "POST /api/syq/video/select-video-info-by-page HTTP/1.1" 200 63831
************ - - [12/Aug/2025:10:44:13 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [12/Aug/2025:10:44:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1383
************ - - [12/Aug/2025:10:44:13 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754966653912 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:13 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:13 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754966653912 HTTP/1.1" 200 148
************ - - [12/Aug/2025:10:44:13 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [12/Aug/2025:10:44:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754966654433 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754966654439 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:14 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754966654453 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:14 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754966654453 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754966654433 HTTP/1.1" 200 160
************ - - [12/Aug/2025:10:44:14 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754966654453 HTTP/1.1" 200 152
************ - - [12/Aug/2025:10:44:14 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754966654453 HTTP/1.1" 200 148
************ - - [12/Aug/2025:10:44:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 4775
************ - - [12/Aug/2025:10:44:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754966654439 HTTP/1.1" 200 159
************ - - [12/Aug/2025:10:44:19 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:10:44:20 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754966660371 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:20 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754966660371 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:20 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754966660371 HTTP/1.1" 200 520
************ - - [12/Aug/2025:10:44:20 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754966660371 HTTP/1.1" 200 153
************ - - [12/Aug/2025:10:44:20 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754966660436 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:20 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754966660436 HTTP/1.1" 200 646
************ - - [12/Aug/2025:10:44:21 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:44:21 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:10:46:42 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:10:46:43 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:10:46:44 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:10:48:16 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4160
************ - - [12/Aug/2025:10:49:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754966945433 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:49:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754966945433 HTTP/1.1" 200 160
************ - - [12/Aug/2025:10:49:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754966952332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:49:12 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754966952334 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:49:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754966952332 HTTP/1.1" 200 161
************ - - [12/Aug/2025:10:49:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:10:49:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:10:49:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754966952334 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:10:49:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:10:49:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754966955329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:49:15 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754966955329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:49:15 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754966955329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:49:15 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754966955329 HTTP/1.1" 200 148
************ - - [12/Aug/2025:10:49:15 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754966955329 HTTP/1.1" 200 152
************ - - [12/Aug/2025:10:49:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754966955329 HTTP/1.1" 200 159
************ - - [12/Aug/2025:10:50:58 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4167
************ - - [12/Aug/2025:10:50:59 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4167
************ - - [12/Aug/2025:10:51:04 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4167
************ - - [12/Aug/2025:10:51:05 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4167
************ - - [12/Aug/2025:10:53:26 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4167
************ - - [12/Aug/2025:10:53:42 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:53:43 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4167
************ - - [12/Aug/2025:10:54:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754967245320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:54:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754967245320 HTTP/1.1" 200 160
************ - - [12/Aug/2025:10:54:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754967252333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:54:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:54:12 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:54:12 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754967252335 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:54:12 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:54:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754967252333 HTTP/1.1" 200 161
************ - - [12/Aug/2025:10:54:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:10:54:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:10:54:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754967252335 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:10:54:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:10:54:16 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754967256330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:54:16 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754967256330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:54:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754967256331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:54:16 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754967256330 HTTP/1.1" 200 148
************ - - [12/Aug/2025:10:54:16 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754967256330 HTTP/1.1" 200 152
************ - - [12/Aug/2025:10:54:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754967256331 HTTP/1.1" 200 159
************ - - [12/Aug/2025:10:57:58 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:57:58 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4167
************ - - [12/Aug/2025:10:57:59 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:57:59 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4167
************ - - [12/Aug/2025:10:59:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754967545330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:59:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754967545330 HTTP/1.1" 200 160
************ - - [12/Aug/2025:10:59:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754967552319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:59:12 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:59:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:59:12 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754967552321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:59:12 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:59:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754967552319 HTTP/1.1" 200 161
************ - - [12/Aug/2025:10:59:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:10:59:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:10:59:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754967552321 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:10:59:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:10:59:16 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754967556367 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:59:16 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754967556367 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:59:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754967556371 HTTP/1.1" 200 -
************ - - [12/Aug/2025:10:59:16 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754967556367 HTTP/1.1" 200 148
************ - - [12/Aug/2025:10:59:16 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754967556367 HTTP/1.1" 200 152
************ - - [12/Aug/2025:10:59:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754967556371 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:01:41 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:01:41 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4167
************ - - [12/Aug/2025:11:04:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754967845331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:04:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754967845331 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:04:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754967852327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:04:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:04:12 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:04:12 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754967852329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:04:12 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:04:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754967852327 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:04:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:04:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:04:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754967852329 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:04:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:04:17 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754967857332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:04:17 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754967857332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:04:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754967857333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:04:17 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754967857332 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:04:17 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754967857332 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:04:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754967857333 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:05:31 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:05:31 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4199
************ - - [12/Aug/2025:11:06:15 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:06:15 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4216
************ - - [12/Aug/2025:11:06:53 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:06:53 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4216
************ - - [12/Aug/2025:11:07:15 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754968035081 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:07:15 +0800] "GET /api/call/common/message/call-result?extSendId=C742EF1B-DE76-46DA-9943-CB4CB7B61095&_timer304=1754968035081 HTTP/1.1" 200 598
************ - - [12/Aug/2025:11:09:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754968145322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754968145322 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:09:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754968152319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:12 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:12 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754968152322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:12 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754968152319 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:09:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:09:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:09:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754968152322 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:09:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:09:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754968158333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754968158333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754968158334 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754968158333 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:09:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754968158333 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:09:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754968158334 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:09:28 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:09:28 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4216
************ - - [12/Aug/2025:11:10:31 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:10:32 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4226
************ - - [12/Aug/2025:11:10:59 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754968259569 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:10:59 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754968259569 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:10:59 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:10:59 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754968259569 HTTP/1.1" 200 12285
************ - - [12/Aug/2025:11:10:59 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754968259569 HTTP/1.1" 200 12285
************ - - [12/Aug/2025:11:10:59 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [12/Aug/2025:11:11:07 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:11:07 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29284
************ - - [12/Aug/2025:11:11:12 +0800] "OPTIONS /api/ew/warning/correct-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754968272958 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:11:12 +0800] "GET /api/ew/warning/correct-list?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754968272958 HTTP/1.1" 200 147
************ - - [12/Aug/2025:11:11:52 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:11:52 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [12/Aug/2025:11:11:55 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:11:55 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29284
************ - - [12/Aug/2025:11:12:20 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [12/Aug/2025:11:12:20 +0800] "GET /login HTTP/1.1" 302 -
************ - - [12/Aug/2025:11:12:25 +0800] "GET /login?code=mUEdrn&state=ZPivbe HTTP/1.1" 302 -
************ - - [12/Aug/2025:11:12:25 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [12/Aug/2025:11:12:27 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1754968347927 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:27 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1754968347927 HTTP/1.1" 200 552
************ - - [12/Aug/2025:11:12:28 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1754968348024 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:28 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1754968348024 HTTP/1.1" 200 61368
************ - - [12/Aug/2025:11:12:28 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1754968348128 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:28 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1754968348128 HTTP/1.1" 200 10388
************ - - [12/Aug/2025:11:12:28 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754968348515 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:28 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754968348515 HTTP/1.1" 200 2009
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1754968349337 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+11:12:29&etm=&_timer304=1754968349337 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+12:00&filterCnt=6&_timer304=1754968349337 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754968349337 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754968349337 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754968349337 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:29 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+11:12:29&etm=&_timer304=1754968349337 HTTP/1.1" 200 156
************ - - [12/Aug/2025:11:12:29 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+12:00&filterCnt=6&_timer304=1754968349337 HTTP/1.1" 200 164
************ - - [12/Aug/2025:11:12:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754968349337 HTTP/1.1" 200 166
************ - - [12/Aug/2025:11:12:29 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:11:12:29 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1754968349337 HTTP/1.1" 200 1482
************ - - [12/Aug/2025:11:12:29 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:11:12:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:11:12:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:11:12:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754968349337 HTTP/1.1" 200 169
************ - - [12/Aug/2025:11:12:29 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754968349337 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754968350022 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/base/saas/token?_timer304=1754968350022 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754968350036 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:30 +0800] "GET /api/base/saas/token?_timer304=1754968350022 HTTP/1.1" 200 411
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [12/Aug/2025:11:12:30 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754968350036 HTTP/1.1" 200 2009
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [12/Aug/2025:11:12:30 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [12/Aug/2025:11:12:31 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [12/Aug/2025:11:12:31 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1379
************ - - [12/Aug/2025:11:12:31 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 1743
************ - - [12/Aug/2025:11:12:32 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [12/Aug/2025:11:12:32 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754968350022 HTTP/1.1" 200 396
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1754968355532 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1754968355532 HTTP/1.1" 200 144
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+11:12:35&etm=&_timer304=1754968355706 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754968355706 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+12:00&filterCnt=6&_timer304=1754968355706 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754968355706 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754968355706 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754968355707 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:11:12:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:11:12:35 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754968355711 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+11:12:35&etm=&_timer304=1754968355706 HTTP/1.1" 200 156
************ - - [12/Aug/2025:11:12:35 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+12:00&filterCnt=6&_timer304=1754968355706 HTTP/1.1" 200 164
************ - - [12/Aug/2025:11:12:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754968355706 HTTP/1.1" 200 166
************ - - [12/Aug/2025:11:12:35 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754968355707 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:12:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754968355706 HTTP/1.1" 200 169
************ - - [12/Aug/2025:11:12:35 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754968355711 HTTP/1.1" 200 159616
************ - - [12/Aug/2025:11:12:35 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754968355706 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754968355874 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:35 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [12/Aug/2025:11:12:35 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754968355908 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:36 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754968355908 HTTP/1.1" 200 258
************ - - [12/Aug/2025:11:12:36 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [12/Aug/2025:11:12:36 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [12/Aug/2025:11:12:36 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [12/Aug/2025:11:12:36 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [12/Aug/2025:11:12:36 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754968356886 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:36 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754968356886 HTTP/1.1" 200 258
************ - - [12/Aug/2025:11:12:37 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754968357040 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:37 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:37 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 245
************ - - [12/Aug/2025:11:12:37 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754968357040 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:12:37 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [12/Aug/2025:11:12:37 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754968355874 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:12:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754968357761 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754968357772 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754968357761 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:12:37 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754968357781 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:37 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754968357781 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754968357772 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:12:37 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754968357781 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:12:37 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754968357781 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:12:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1383
************ - - [12/Aug/2025:11:12:39 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 4775
************ - - [12/Aug/2025:11:12:41 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 358999
************ - - [12/Aug/2025:11:12:42 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754968362589 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:42 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968362589 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:42 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:42 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968362589 HTTP/1.1" 200 443
************ - - [12/Aug/2025:11:12:42 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754968362589 HTTP/1.1" 200 510
************ - - [12/Aug/2025:11:12:42 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:11:12:42 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968362627 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:42 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968362627 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:42 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968362627 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:42 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968362627 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:42 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968362627 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:11:12:42 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968362627 HTTP/1.1" 200 727
************ - - [12/Aug/2025:11:12:42 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968362627 HTTP/1.1" 200 329
************ - - [12/Aug/2025:11:12:43 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968362627 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:11:12:45 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754968365318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:45 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754968365318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:12:45 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754968365318 HTTP/1.1" 200 12285
************ - - [12/Aug/2025:11:12:45 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754968365318 HTTP/1.1" 200 12285
************ - - [12/Aug/2025:11:12:45 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29418
************ - - [12/Aug/2025:11:12:48 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 29284
************ - - [12/Aug/2025:11:13:08 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:13:08 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:11:13:08 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754968388930 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:13:08 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754968388930 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:13:08 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754968388930 HTTP/1.1" 200 153
************ - - [12/Aug/2025:11:13:08 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754968388930 HTTP/1.1" 200 520
************ - - [12/Aug/2025:11:13:08 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754968388964 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:13:08 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754968388964 HTTP/1.1" 200 646
************ - - [12/Aug/2025:11:13:09 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:13:10 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4226
************ - - [12/Aug/2025:11:14:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754968444442 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:14:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754968444442 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:14:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754968451322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:14:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754968451322 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:14:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:14:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:14:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:14:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754968451507 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:14:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:14:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:14:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754968451507 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:14:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:14:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754968458379 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:14:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754968458379 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:14:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754968458382 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:14:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754968458379 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:14:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754968458379 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:14:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754968458382 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:17:25 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:25 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 358999
************ - - [12/Aug/2025:11:17:27 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754968647235 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:27 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968647235 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:27 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:27 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968647235 HTTP/1.1" 200 443
************ - - [12/Aug/2025:11:17:27 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754968647235 HTTP/1.1" 200 510
************ - - [12/Aug/2025:11:17:27 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:11:17:27 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968647274 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:27 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968647274 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:27 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968647274 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:27 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968647274 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:27 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968647274 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:11:17:27 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968647274 HTTP/1.1" 200 727
************ - - [12/Aug/2025:11:17:27 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968647274 HTTP/1.1" 200 329
************ - - [12/Aug/2025:11:17:27 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968647274 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:11:17:28 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968648031 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:28 +0800] "OPTIONS /api/ew/warning/snapshot-index?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968648031 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:28 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968648031 HTTP/1.1" 200 443
************ - - [12/Aug/2025:11:17:28 +0800] "GET /api/ew/warning/snapshot-index?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968648031 HTTP/1.1" 200 322
************ - - [12/Aug/2025:11:17:28 +0800] "OPTIONS /api/ew/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:17:29 +0800] "POST /api/ew/warning/sending-page-list HTTP/1.1" 200 7947
************ - - [12/Aug/2025:11:18:06 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968686686 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:18:06 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754968686686 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:18:06 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:18:06 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754968686686 HTTP/1.1" 200 443
************ - - [12/Aug/2025:11:18:06 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754968686686 HTTP/1.1" 200 510
************ - - [12/Aug/2025:11:18:06 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:11:18:06 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968686725 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:18:06 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968686725 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:18:06 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968686725 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:18:06 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968686725 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:18:06 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968686725 HTTP/1.1" 200 727
************ - - [12/Aug/2025:11:18:06 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968686725 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:11:18:06 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968686725 HTTP/1.1" 200 329
************ - - [12/Aug/2025:11:18:07 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754968686725 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:11:18:08 +0800] "OPTIONS /api/ew/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:18:08 +0800] "POST /api/ew/warning/sending-page-list HTTP/1.1" 200 7947
************ - - [12/Aug/2025:11:19:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754968744442 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:19:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754968744442 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:19:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754968751318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:19:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754968751318 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:19:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:19:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:19:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754968751510 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:19:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:19:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:19:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:19:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754968751510 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:19:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:19:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754968758412 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:19:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754968758412 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:19:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754968758413 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:19:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754968758413 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:19:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754968758412 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:19:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754968758412 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:24:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754969044443 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:24:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754969044443 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:24:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754969051320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:24:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754969051320 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:24:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:24:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:24:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754969051512 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:24:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:24:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:24:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:24:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754969051512 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:24:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:24:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754969058449 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:24:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754969058450 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:24:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754969058450 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:24:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754969058450 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:24:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754969058450 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:24:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754969058449 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:29:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754969344443 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:29:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754969344443 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:29:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754969351319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:29:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754969351319 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:29:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:29:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:29:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754969351505 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:29:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:29:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:29:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:29:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754969351505 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:29:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:29:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754969358488 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:29:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754969358488 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:29:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754969358491 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:29:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754969358488 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:29:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754969358488 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:29:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754969358491 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:34:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754969644443 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:34:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754969644443 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:34:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754969651321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:34:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754969651321 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:34:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:34:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:34:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:34:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754969651505 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:34:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:34:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:34:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754969651505 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:34:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:34:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754969658529 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:34:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754969658529 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:34:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754969658529 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:34:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754969658529 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:34:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754969658529 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:34:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754969658529 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:39:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754969959424 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:39:19 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754969959426 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:39:19 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754969959426 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:39:19 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754969959426 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:39:19 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754969959426 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:39:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754969959424 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:39:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754969961321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:39:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754969961321 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:40:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754970021446 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:40:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:40:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:40:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754970021451 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:40:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:40:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754970021446 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:40:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:40:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:40:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754970021451 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:40:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:44:20 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754970260329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:44:20 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754970260329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:44:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754970260331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:44:20 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754970260329 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:44:20 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754970260329 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:44:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754970260331 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:44:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754970261332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:44:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754970261332 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:45:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754970321321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:45:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:45:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:45:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754970321323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:45:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:45:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:45:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754970321321 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:45:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:45:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754970321323 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:45:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:49:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754970561429 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:49:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754970561429 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:49:21 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754970561429 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:49:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754970561430 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:49:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754970561429 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:49:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754970561429 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:49:21 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754970561429 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:49:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754970561430 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:50:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754970621330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:50:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:50:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:50:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754970621332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:50:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:50:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:50:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754970621330 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:50:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:50:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754970621332 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:50:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:54:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754970861430 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:54:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754970861430 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:54:22 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754970862322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:54:22 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754970862322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:54:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754970862323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:54:22 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754970862322 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:54:22 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754970862322 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:54:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754970862323 HTTP/1.1" 200 159
************ - - [12/Aug/2025:11:55:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754970921443 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:55:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:55:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:55:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754970921444 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:55:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:55:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:11:55:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754970921443 HTTP/1.1" 200 160
************ - - [12/Aug/2025:11:55:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:11:55:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754970921444 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:11:55:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:11:59:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754971161329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:59:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754971161329 HTTP/1.1" 200 161
************ - - [12/Aug/2025:11:59:23 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754971163325 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:59:23 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754971163325 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:59:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754971163326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:11:59:23 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754971163325 HTTP/1.1" 200 148
************ - - [12/Aug/2025:11:59:23 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754971163325 HTTP/1.1" 200 152
************ - - [12/Aug/2025:11:59:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754971163326 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:00:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754971221320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:00:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:00:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:00:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754971221322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:00:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:00:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:00:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754971221320 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:00:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:00:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754971221322 HTTP/1.1" 200 442517
************ - - [12/Aug/2025:12:00:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:04:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754971461326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:04:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754971461326 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:04:24 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754971464328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:04:24 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754971464328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:04:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754971464329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:04:24 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754971464328 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:04:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754971464329 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:04:24 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754971464328 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:05:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754971521317 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:05:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:05:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:05:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754971521318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:05:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:05:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:05:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754971521317 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:05:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:05:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754971521318 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:05:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:09:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754971761420 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:09:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754971761420 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:09:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754971765319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:09:25 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754971765319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:09:25 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754971765319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:09:25 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754971765319 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:09:25 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754971765319 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:09:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754971765319 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:10:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754971821443 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:10:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:10:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:10:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754971821444 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:10:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:10:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:10:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754971821443 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:10:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:10:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754971821444 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:10:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:14:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754972061326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:14:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754972061326 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:14:26 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754972066323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:14:26 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754972066323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:14:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754972066324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:14:26 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754972066323 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:14:26 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754972066323 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:14:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754972066324 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:15:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754972121422 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:15:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:15:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:15:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754972121424 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:15:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:15:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:15:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754972121422 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:15:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:15:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754972121424 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:15:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:19:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754972361327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:19:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754972361327 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:19:27 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754972367330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:19:27 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754972367330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:19:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754972367331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:19:27 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754972367330 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:19:27 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754972367330 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:19:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754972367331 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:20:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754972421325 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:20:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:20:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:20:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754972421327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:20:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:20:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:20:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754972421325 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:20:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:20:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754972421327 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:20:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:24:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754972661442 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:24:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754972661442 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:24:28 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754972668321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:24:28 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754972668321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:24:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754972668322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:24:28 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754972668321 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:24:28 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754972668321 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:24:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754972668322 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:25:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754972721419 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:25:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:25:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:25:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754972721420 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:25:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:25:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:25:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754972721419 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:25:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:25:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754972721420 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:25:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:29:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754972961438 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:29:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754972961438 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:29:29 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754972969324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:29:29 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754972969324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:29:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754972969326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:29:29 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754972969324 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:29:29 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754972969324 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:29:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754972969326 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:30:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754973021322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:30:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:30:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:30:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:30:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754973021324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:30:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:30:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754973021322 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:30:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:30:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754973021324 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:30:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:34:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754973261324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:34:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754973261324 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:34:30 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754973270328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:34:30 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754973270328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:34:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754973270329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:34:30 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754973270328 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:34:30 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754973270328 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:34:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754973270329 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:35:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754973321326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:35:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:35:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:35:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754973321328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:35:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:35:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:35:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754973321326 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:35:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:35:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754973321328 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:35:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:39:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754973561333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:39:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754973561333 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:39:31 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754973571319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:39:31 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754973571319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:39:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754973571320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:39:31 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754973571319 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:39:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754973571320 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:39:31 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754973571319 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:40:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754973621325 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:40:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:40:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:40:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754973621327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:40:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:40:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:40:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754973621325 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:40:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:40:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754973621327 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:40:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:44:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754973861327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:44:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754973861327 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:44:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754973872322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:44:32 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754973872323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:44:32 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754973872323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:44:32 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754973872323 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:44:32 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754973872323 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:44:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754973872322 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:45:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754973921330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:45:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:45:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:45:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:45:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754973921332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:45:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:45:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754973921330 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:45:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:45:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754973921332 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:45:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:49:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754974161333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:49:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754974161333 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:49:33 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754974173450 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:49:33 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754974173450 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:49:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754974173452 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:49:33 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754974173450 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:49:33 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754974173450 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:49:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754974173452 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:50:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754974221433 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:50:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:50:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:50:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754974221435 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:50:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:50:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:50:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754974221433 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:50:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:50:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754974221435 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:50:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:54:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754974461323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:54:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754974461323 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:54:34 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754974474444 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:54:34 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754974474444 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:54:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754974474446 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:54:34 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754974474444 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:54:34 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754974474444 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:54:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754974474446 HTTP/1.1" 200 159
************ - - [12/Aug/2025:12:55:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754974521321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:55:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:55:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754974521323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:55:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:55:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:55:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:12:55:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754974521321 HTTP/1.1" 200 160
************ - - [12/Aug/2025:12:55:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:12:55:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754974521323 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:12:55:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:12:59:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754974761323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:59:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754974761323 HTTP/1.1" 200 161
************ - - [12/Aug/2025:12:59:35 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754974775321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:59:35 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754974775321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:59:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754974775323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:12:59:35 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754974775321 HTTP/1.1" 200 152
************ - - [12/Aug/2025:12:59:35 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754974775321 HTTP/1.1" 200 148
************ - - [12/Aug/2025:12:59:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754974775323 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:00:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754974821332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:00:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:00:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754974821334 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:00:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:00:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:00:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:00:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754974821332 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:00:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:00:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754974821334 HTTP/1.1" 200 442517
************ - - [12/Aug/2025:13:00:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:04:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754975061448 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:04:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754975061448 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:04:36 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754975076419 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:04:36 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754975076419 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:04:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754975076420 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:04:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754975076420 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:04:36 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754975076419 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:04:36 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754975076419 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:05:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754975121327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:05:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:05:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754975121328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:05:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:05:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:05:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:05:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754975121327 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:05:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:05:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754975121328 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:13:05:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:09:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754975361331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:09:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754975361331 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:09:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754975377446 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:09:37 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754975377447 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:09:37 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754975377447 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:09:37 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754975377447 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:09:37 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754975377447 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:09:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754975377446 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:10:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754975421333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:10:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:10:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:10:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754975421335 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:10:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:10:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:10:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754975421333 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:10:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:10:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754975421335 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:13:10:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:14:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754975661325 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:14:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754975661325 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:14:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754975678329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:14:38 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754975678331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:14:38 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754975678331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:14:38 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754975678331 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:14:38 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754975678331 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:14:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754975678329 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:15:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754975721328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:15:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:15:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:15:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754975721330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:15:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:15:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:15:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754975721328 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:15:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:15:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754975721330 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:13:15:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:19:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754975961445 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:19:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754975961445 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:19:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754975978272 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:19:38 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:19:38 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:19:38 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754975978274 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:19:38 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:19:38 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:19:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754975978272 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:19:38 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754975978365 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:19:38 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754975978365 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:19:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754975978366 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:19:38 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754975978365 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:19:38 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754975978365 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:19:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754975978366 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:19:38 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:19:39 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754975978274 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:13:19:39 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:24:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754976244442 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:24:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754976244442 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:24:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754976251319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:24:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754976251319 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:24:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:24:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:24:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754976251510 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:24:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:24:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:24:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:24:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754976251510 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:13:24:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:24:38 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754976278420 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:24:38 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754976278420 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:24:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754976278421 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:24:38 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754976278420 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:24:38 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754976278420 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:24:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754976278421 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:29:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754976544444 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:29:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754976544444 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:29:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754976551319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:29:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754976551319 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:29:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:29:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:29:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:29:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754976551512 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:29:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:29:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:29:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:29:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754976551512 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:13:29:38 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754976578454 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:29:38 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754976578454 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:29:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754976578458 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:29:38 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754976578454 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:29:38 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754976578454 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:29:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754976578458 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:34:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754976844437 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754976844437 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:34:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754976851318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754976851318 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:34:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754976851505 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:34:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:34:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754976851505 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:13:34:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:34:28 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:28 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [12/Aug/2025:13:34:29 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754976869416 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:29 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:29 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754976869416 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:29 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754976869416 HTTP/1.1" 200 443
************ - - [12/Aug/2025:13:34:29 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:13:34:29 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754976869416 HTTP/1.1" 200 510
************ - - [12/Aug/2025:13:34:29 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976869471 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:29 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976869471 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:29 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976869471 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:29 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976869471 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:29 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976869471 HTTP/1.1" 200 329
************ - - [12/Aug/2025:13:34:29 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976869471 HTTP/1.1" 200 727
************ - - [12/Aug/2025:13:34:29 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976869471 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:13:34:30 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976869471 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:13:34:38 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754976878494 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:38 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754976878494 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754976878494 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:38 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754976878494 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:34:38 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754976878494 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:34:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754976878494 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:34:39 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754976879106 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:39 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754976879106 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:39 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:39 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754976879106 HTTP/1.1" 200 443
************ - - [12/Aug/2025:13:34:39 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754976879106 HTTP/1.1" 200 510
************ - - [12/Aug/2025:13:34:39 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:13:34:39 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976879143 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:39 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976879143 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:39 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976879143 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:39 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976879143 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:34:39 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976879143 HTTP/1.1" 200 727
************ - - [12/Aug/2025:13:34:39 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976879143 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:13:34:39 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976879143 HTTP/1.1" 200 329
************ - - [12/Aug/2025:13:34:39 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754976879143 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:13:39:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754977161319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:39:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754977161319 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:39:39 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754977179330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:39:39 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754977179331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:39:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754977179331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:39:39 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754977179331 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:39:39 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754977179330 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:39:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754977179331 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:40:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754977221320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:40:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:40:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:40:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754977221323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:40:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:40:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:40:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754977221320 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:40:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:40:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754977221323 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:13:40:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:41:07 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:41:07 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:13:42:23 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212106&_timer304=1754977343518 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:42:23 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=AE8F9369-EB92-4E0F-BECC-D63FE530D024&warnGradeId=2&_timer304=1754977343518 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:42:23 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=AE8F9369-EB92-4E0F-BECC-D63FE530D024&warnGradeId=2&_timer304=1754977343518 HTTP/1.1" 200 153
************ - - [12/Aug/2025:13:42:23 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212106&_timer304=1754977343518 HTTP/1.1" 200 520
************ - - [12/Aug/2025:13:42:23 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=AE8F9369-EB92-4E0F-BECC-D63FE530D024&_timer304=1754977343553 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:42:23 +0800] "GET /api/fusion/warning/snapshot-index?warnId=AE8F9369-EB92-4E0F-BECC-D63FE530D024&_timer304=1754977343553 HTTP/1.1" 200 648
************ - - [12/Aug/2025:13:42:40 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=3406AC33-85DF-45E3-BCA7-CBF9F7765F09&warnGradeId=2&_timer304=1754977360982 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:42:40 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205103&_timer304=1754977360982 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:42:41 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602103205103&_timer304=1754977360982 HTTP/1.1" 200 514
************ - - [12/Aug/2025:13:42:41 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=3406AC33-85DF-45E3-BCA7-CBF9F7765F09&warnGradeId=2&_timer304=1754977360982 HTTP/1.1" 200 153
************ - - [12/Aug/2025:13:42:41 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=3406AC33-85DF-45E3-BCA7-CBF9F7765F09&_timer304=1754977361026 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:42:41 +0800] "GET /api/fusion/warning/snapshot-index?warnId=3406AC33-85DF-45E3-BCA7-CBF9F7765F09&_timer304=1754977361026 HTTP/1.1" 200 655
************ - - [12/Aug/2025:13:43:25 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:43:25 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [12/Aug/2025:13:44:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754977446318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:44:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754977446318 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:44:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754977452329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:44:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754977452329 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:44:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:44:13 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:44:13 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754977453327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:44:13 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:44:13 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:44:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:44:14 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754977453327 HTTP/1.1" 200 442521
************ - - [12/Aug/2025:13:44:14 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:44:40 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754977480439 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:44:40 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754977480439 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:44:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754977480440 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:44:40 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754977480439 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:44:40 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754977480439 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:44:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754977480440 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:49:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754977761329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:49:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754977761329 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:49:41 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754977781426 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:49:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754977781427 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:49:41 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754977781426 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:49:41 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754977781426 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:49:41 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754977781426 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:49:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754977781427 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:50:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754977821435 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754977821436 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:50:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754977821435 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:50:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:50:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754977821436 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:13:50:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:50:48 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754977848734 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:48 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754977848734 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:48 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:48 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754977848734 HTTP/1.1" 200 443
************ - - [12/Aug/2025:13:50:48 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:13:50:48 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754977848734 HTTP/1.1" 200 510
************ - - [12/Aug/2025:13:50:48 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754977848769 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:48 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754977848769 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:48 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754977848769 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:48 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754977848769 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:50:48 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754977848769 HTTP/1.1" 200 329
************ - - [12/Aug/2025:13:50:48 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754977848769 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:13:50:48 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754977848769 HTTP/1.1" 200 727
************ - - [12/Aug/2025:13:50:49 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754977848769 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:13:54:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754978061328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:54:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754978061328 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:54:42 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754978082318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:54:42 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754978082318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:54:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754978082319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:54:42 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754978082318 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:54:42 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754978082318 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:54:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754978082319 HTTP/1.1" 200 159
************ - - [12/Aug/2025:13:55:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754978121324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:55:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:55:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:55:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754978121326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:55:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:55:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:55:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754978121324 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:55:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:55:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754978121326 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:13:55:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:58:50 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:58:50 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 110425
************ - - [12/Aug/2025:13:59:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754978346328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:59:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754978346328 HTTP/1.1" 200 160
************ - - [12/Aug/2025:13:59:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754978352318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:59:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754978352318 HTTP/1.1" 200 161
************ - - [12/Aug/2025:13:59:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:59:13 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:59:13 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754978353332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:59:13 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:59:13 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:13:59:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:13:59:14 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754978353332 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:13:59:14 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:13:59:43 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754978383439 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:59:43 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754978383439 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:59:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754978383440 HTTP/1.1" 200 -
************ - - [12/Aug/2025:13:59:43 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754978383439 HTTP/1.1" 200 152
************ - - [12/Aug/2025:13:59:43 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754978383439 HTTP/1.1" 200 148
************ - - [12/Aug/2025:13:59:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754978383440 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:01:37 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:01:39 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [12/Aug/2025:14:01:40 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:01:40 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [12/Aug/2025:14:01:46 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:01:46 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [12/Aug/2025:14:01:47 +0800] "OPTIONS /api/ew/rsvr/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:01:47 +0800] "POST /api/ew/rsvr/warning/page-list HTTP/1.1" 200 234
************ - - [12/Aug/2025:14:01:50 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754978510869 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:01:52 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754978510869 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:01:52 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:01:52 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 78597
************ - - [12/Aug/2025:14:03:16 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754978596052 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:03:16 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754978596052 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:03:16 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754978596052 HTTP/1.1" 200 153
************ - - [12/Aug/2025:14:03:16 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754978596052 HTTP/1.1" 200 520
************ - - [12/Aug/2025:14:03:16 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754978596214 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:03:16 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754978596214 HTTP/1.1" 200 646
************ - - [12/Aug/2025:14:04:03 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978643457 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:03 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978643457 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:03 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978643457 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:04:03 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978643457 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:04:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754978644441 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754978644441 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:04:10 +0800] "OPTIONS /api/fusion/warning/sending-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754978651319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754978651319 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:04:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754978651506 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:12 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:04:12 +0800] "POST /api/fusion/warning/sending-page-list HTTP/1.1" 200 4226
************ - - [12/Aug/2025:14:04:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:04:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:04:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754978651506 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:04:23 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754978663162 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:23 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754978663162 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:23 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754978663162 HTTP/1.1" 200 153
************ - - [12/Aug/2025:14:04:23 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754978663162 HTTP/1.1" 200 520
************ - - [12/Aug/2025:14:04:23 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754978663207 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:23 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754978663207 HTTP/1.1" 200 646
************ - - [12/Aug/2025:14:04:43 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754978683478 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754978683479 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:43 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754978683478 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:43 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754978683478 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:04:43 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754978683478 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:04:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754978683479 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:04:45 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978685708 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:45 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978685708 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:04:45 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978685708 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:04:45 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978685708 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:06:38 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978798245 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:06:38 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978798245 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:06:39 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978799294 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:06:39 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978799294 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:06:40 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978800870 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:06:40 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978800870 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:06:41 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978801973 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:06:41 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978801973 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:06:42 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978802511 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:06:42 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978802511 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:06:43 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978803711 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:06:43 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=2&_timer304=1754978803711 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:06:44 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978804232 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:06:44 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978804232 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:06:44 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978804565 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:06:44 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-09+08:00&etm=2025-08-12+15:00&drps=&type=1&_timer304=1754978804565 HTTP/1.1" 200 147
************ - - [12/Aug/2025:14:07:30 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754978850315 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:07:30 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754978850315 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:07:30 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754978850315 HTTP/1.1" 200 153
************ - - [12/Aug/2025:14:07:30 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754978850315 HTTP/1.1" 200 520
************ - - [12/Aug/2025:14:07:30 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754978850359 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:07:30 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754978850359 HTTP/1.1" 200 646
************ - - [12/Aug/2025:14:09:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754978961430 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:09:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754978961430 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:09:44 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754978984424 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:09:44 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754978984424 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:09:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754978984425 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:09:44 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754978984424 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:09:44 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754978984424 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:09:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754978984425 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:10:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754979021439 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:10:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:10:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:10:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:10:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754979021441 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:10:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:10:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754979021439 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:10:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:10:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754979021441 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:10:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:14:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754979246329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:14:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754979246329 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:14:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754979252324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:14:12 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754979252324 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:14:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:14:13 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:14:13 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754979253323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:14:13 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:14:13 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:14:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:14:14 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754979253323 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:14:14 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:14:45 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754979285322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:14:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754979285323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:14:45 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754979285322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:14:45 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754979285322 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:14:45 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754979285322 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:14:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754979285323 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:19:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754979561328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:19:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754979561328 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:19:46 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754979586320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:19:46 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754979586320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:19:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754979586322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:19:46 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754979586320 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:19:46 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754979586320 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:19:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754979586322 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:20:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754979621318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:20:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:20:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:20:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:20:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754979621320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:20:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:20:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754979621318 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:20:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:20:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754979621320 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:20:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:24:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754979844441 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754979844441 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:24:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754979851320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754979851320 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:24:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754979851513 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:24:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:24:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754979851513 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:24:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:24:23 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754979863004 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:23 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754979863004 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:23 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754979863004 HTTP/1.1" 200 153
************ - - [12/Aug/2025:14:24:23 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754979863004 HTTP/1.1" 200 520
************ - - [12/Aug/2025:14:24:23 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754979863041 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:23 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754979863041 HTTP/1.1" 200 646
************ - - [12/Aug/2025:14:24:47 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754979887319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:47 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754979887319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754979887321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:24:47 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754979887319 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:24:47 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754979887319 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:24:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754979887321 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:29:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754980161322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:29:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754980161322 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:29:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754980188319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:29:48 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754980188318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:29:48 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754980188319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:29:48 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754980188319 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:29:48 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754980188318 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:29:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754980188319 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:30:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754980221330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:30:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754980221331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:30:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:30:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754980221330 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:30:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:30:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754980221331 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:30:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:34:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754980461330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:34:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754980461330 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:34:49 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754980489326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:34:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754980489326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:34:49 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754980489326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:34:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754980489326 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:34:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754980489326 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:34:49 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754980489326 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:35:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754980521331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:35:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754980521332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:35:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:35:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754980521331 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:35:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:35:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754980521332 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:35:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:39:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754980761320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:39:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754980761320 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:39:50 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754980790327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:39:50 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754980790327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:39:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754980790326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:39:50 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754980790327 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:39:50 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754980790327 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:39:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754980790326 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:40:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754980821323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:40:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754980821324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:40:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:40:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754980821323 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:40:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:40:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754980821324 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:40:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:44:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754981061333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:44:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754981061333 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:44:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754981091329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:44:51 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754981091329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:44:51 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754981091329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:44:51 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754981091329 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:44:51 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754981091329 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:44:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754981091329 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:45:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754981121318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:45:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754981121319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:45:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:45:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754981121318 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:45:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:45:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:45:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754981121319 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:49:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754981361330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:49:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754981361330 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:49:52 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754981392331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:49:52 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754981392331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:49:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754981392331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:49:52 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754981392331 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:49:52 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754981392331 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:49:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754981392331 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:50:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754981421319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:50:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754981421320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:50:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:50:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754981421319 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:50:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:50:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754981421320 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:50:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:54:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754981661465 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:54:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754981661465 HTTP/1.1" 200 161
************ - - [12/Aug/2025:14:54:53 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754981693326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:54:53 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754981693326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:54:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754981693326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:54:53 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754981693326 HTTP/1.1" 200 152
************ - - [12/Aug/2025:14:54:53 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754981693326 HTTP/1.1" 200 148
************ - - [12/Aug/2025:14:54:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754981693326 HTTP/1.1" 200 159
************ - - [12/Aug/2025:14:55:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754981721330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:55:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754981721331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:55:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:14:55:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754981721330 HTTP/1.1" 200 160
************ - - [12/Aug/2025:14:55:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:14:55:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754981721331 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:14:55:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:14:59:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754981961328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:14:59:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754981961328 HTTP/1.1" 200 161
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:08 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:08 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:08 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:08 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /login?code=M0pbJm&state=89GFJg HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:09 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:10 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:11 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723073
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:19 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:19 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:20 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:20 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:22 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:22 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId= HTTP/1.1" 200 135
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:22 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [12/Aug/2025:15:01:36 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754982096113 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:01:36 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754982096113 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:01:37 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&warnGradeId=2&_timer304=1754982096113 HTTP/1.1" 200 153
************ - - [12/Aug/2025:15:01:37 +0800] "OPTIONS /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754982097195 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:01:37 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220602100212104&_timer304=1754982096113 HTTP/1.1" 200 520
************ - - [12/Aug/2025:15:01:37 +0800] "GET /api/fusion/warning/snapshot-index?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB&_timer304=1754982097195 HTTP/1.1" 200 646
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:46 +0800] "GET /api/fusion/warning/snapshot-monitor?warnId=9F8C595A-DE59-42BC-9675-08D5954B5CBB HTTP/1.1" 200 371
0:0:0:0:0:0:0:1 - - [12/Aug/2025:15:01:46 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [12/Aug/2025:15:04:10 +0800] "GET /doc.html HTTP/1.1" 302 -
************ - - [12/Aug/2025:15:04:10 +0800] "GET /login HTTP/1.1" 302 -
************ - - [12/Aug/2025:15:04:10 +0800] "GET /login?code=OFeBIA&state=Xv6UFZ HTTP/1.1" 302 -
************ - - [12/Aug/2025:15:04:10 +0800] "GET /doc.html HTTP/1.1" 200 71645
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
************ - - [12/Aug/2025:15:04:10 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
************ - - [12/Aug/2025:15:04:11 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
************ - - [12/Aug/2025:15:04:11 +0800] "GET /swagger-resources HTTP/1.1" 200 101
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
************ - - [12/Aug/2025:15:04:11 +0800] "GET /webjars/bycdao-ui/images/api.ico HTTP/1.1" 200 67646
************ - - [12/Aug/2025:15:04:11 +0800] "GET /v2/api-docs HTTP/1.1" 200 2723076
************ - - [12/Aug/2025:15:04:16 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
************ - - [12/Aug/2025:15:04:16 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
************ - - [12/Aug/2025:15:04:16 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
************ - - [12/Aug/2025:15:04:16 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************ - - [12/Aug/2025:15:04:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754982261330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:04:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754982261330 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:04:57 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754982297425 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:04:57 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754982297425 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:04:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754982297426 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:04:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754982297425 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:04:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754982297425 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:04:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754982297426 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:05:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754982321455 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:05:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:05:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:05:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754982321457 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:05:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:05:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754982321455 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:05:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:05:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:05:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:05:23 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754982321457 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:05:43 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&drps=&type=2&_timer304=1754982343668 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:05:43 +0800] "OPTIONS /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&drps=&type=2&_timer304=1754982343668 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:05:43 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&drps=&type=2&_timer304=1754982343668 HTTP/1.1" 200 147
************ - - [12/Aug/2025:15:05:43 +0800] "GET /api/syq/rain/get-rain-by-tm?stcd=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&drps=&type=2&_timer304=1754982343668 HTTP/1.1" 200 147
************ - - [12/Aug/2025:15:09:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754982561425 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754982561425 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:09:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754982573700 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:33 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754982573700 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:09:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:33 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754982573781 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:33 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:33 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:09:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:09:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754982573781 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:09:35 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:09:45 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:45 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [12/Aug/2025:15:09:48 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:48 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754982588088 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:48 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754982588088 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:48 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754982588088 HTTP/1.1" 200 443
************ - - [12/Aug/2025:15:09:48 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754982588088 HTTP/1.1" 200 510
************ - - [12/Aug/2025:15:09:48 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:15:09:48 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982588138 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:48 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982588138 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:48 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982588138 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:48 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982588138 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:48 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982588138 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:15:09:48 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982588138 HTTP/1.1" 200 727
************ - - [12/Aug/2025:15:09:48 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982588138 HTTP/1.1" 200 329
************ - - [12/Aug/2025:15:09:48 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982588138 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:15:09:53 +0800] "OPTIONS /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754982593880 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:53 +0800] "OPTIONS /api/ew/warning/page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:53 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754982593880 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:53 +0800] "GET /api/ew/warning/snapshot-monitor?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&warnGradeId=5&_timer304=1754982593880 HTTP/1.1" 200 443
************ - - [12/Aug/2025:15:09:53 +0800] "POST /api/ew/warning/page-list HTTP/1.1" 200 1791
************ - - [12/Aug/2025:15:09:53 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220281103205001&_timer304=1754982593880 HTTP/1.1" 200 510
************ - - [12/Aug/2025:15:09:53 +0800] "OPTIONS /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982593920 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:53 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982593920 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:53 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982593920 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:53 +0800] "OPTIONS /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982593920 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:53 +0800] "GET /api/ew/warning/flow-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982593920 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:15:09:53 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982593920 HTTP/1.1" 200 329
************ - - [12/Aug/2025:15:09:53 +0800] "GET /api/ew/warning/process-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982593920 HTTP/1.1" 200 727
************ - - [12/Aug/2025:15:09:54 +0800] "GET /api/ew/warning/message-list?warnId=FA07D724-BC12-43B1-B54E-2577AC77899A&_timer304=1754982593920 HTTP/1.1" 200 12917
************ - - [12/Aug/2025:15:09:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754982599107 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754982599107 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754982599107 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:09:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754982599107 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:09:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754982599140 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:09:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754982599140 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:10:55 +0800] "OPTIONS /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1754982655310 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:10:55 +0800] "OPTIONS /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1754982655310 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:10:55 +0800] "OPTIONS /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1754982655310 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:10:55 +0800] "OPTIONS /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1754982655310 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:10:55 +0800] "GET /api/ew/warning/process-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1754982655310 HTTP/1.1" 200 729
************ - - [12/Aug/2025:15:10:55 +0800] "GET /api/ew/warning/flow-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1754982655310 HTTP/1.1" 200 1382
************ - - [12/Aug/2025:15:10:55 +0800] "GET /api/ew/warning/snapshot-index-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1754982655310 HTTP/1.1" 200 329
************ - - [12/Aug/2025:15:10:55 +0800] "GET /api/ew/warning/message-list?warnId=4ED0F3A7-3EE9-454A-96A5-E8D848345093&_timer304=1754982655310 HTTP/1.1" 200 15587
************ - - [12/Aug/2025:15:14:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754982844443 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:14:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754982844443 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:14:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754982851321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:14:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754982851321 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:14:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:14:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:14:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:14:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754982851509 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:14:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:14:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:14:12 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754982851509 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:14:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:14:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754982899148 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:14:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754982899148 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:14:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754982899148 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:14:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754982899148 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:14:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754982899180 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:14:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754982899180 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:19:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754983161462 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:19:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754983161462 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:19:59 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754983199332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:19:59 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754983199332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:19:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754983199334 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:19:59 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754983199332 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:19:59 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754983199332 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:19:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754983199334 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:20:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754983221442 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:20:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:20:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:20:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:20:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754983221443 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:20:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:20:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754983221442 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:20:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:20:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754983221443 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:20:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:24:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754983461319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:24:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754983461319 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:25:00 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754983500332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:25:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754983500333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:25:00 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754983500332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:25:00 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754983500332 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:25:00 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754983500332 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:25:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754983500333 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:25:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754983521326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:25:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:25:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:25:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:25:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754983521328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:25:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:25:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754983521326 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:25:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:25:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754983521328 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:25:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:29:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754983761325 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:29:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754983761325 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:30:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754983801333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:30:01 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754983801332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:30:01 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754983801332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:30:01 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754983801332 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:30:01 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754983801332 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:30:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754983801333 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:30:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754983821318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:30:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:30:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:30:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754983821320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:30:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:30:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:30:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754983821318 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:30:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:30:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754983821320 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:30:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+15:31:13&etm=&_timer304=1754983873669 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1754983873669 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+16:00&filterCnt=6&_timer304=1754983873669 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754983873669 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754983873669 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754983873669 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1754983873677 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:13 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+15:31:13&etm=&_timer304=1754983873669 HTTP/1.1" 200 156
************ - - [12/Aug/2025:15:31:13 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:15:31:13 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:15:31:13 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:15:31:13 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754983873669 HTTP/1.1" 200 169
************ - - [12/Aug/2025:15:31:13 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754983873669 HTTP/1.1" 200 166
************ - - [12/Aug/2025:15:31:13 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:15:31:13 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+16:00&filterCnt=6&_timer304=1754983873669 HTTP/1.1" 200 164
************ - - [12/Aug/2025:15:31:13 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1754983873677 HTTP/1.1" 200 1482
************ - - [12/Aug/2025:15:31:14 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1754983873669 HTTP/1.1" 200 146
************ - - [12/Aug/2025:15:31:14 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754983873669 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/base/saas/token?_timer304=1754983874223 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754983874225 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754983874226 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************ - - [12/Aug/2025:15:31:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:31:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:31:15 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [12/Aug/2025:15:31:15 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 2307
************ - - [12/Aug/2025:15:31:15 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [12/Aug/2025:15:31:15 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [12/Aug/2025:15:31:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1943
************ - - [12/Aug/2025:15:31:15 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [12/Aug/2025:15:31:16 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754983874226 HTTP/1.1" 200 2009
************ - - [12/Aug/2025:15:31:16 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [12/Aug/2025:15:31:16 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [12/Aug/2025:15:31:16 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [12/Aug/2025:15:31:16 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [12/Aug/2025:15:31:17 +0800] "GET /api/base/saas/token?_timer304=1754983874223 HTTP/1.1" 200 411
************ - - [12/Aug/2025:15:31:17 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [12/Aug/2025:15:31:18 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754983874225 HTTP/1.1" 200 396
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754983887038 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+15:31:26&etm=&_timer304=1754983887038 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+16:00&filterCnt=6&_timer304=1754983887038 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754983887038 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754983887038 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:15:31:27 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+15:31:26&etm=&_timer304=1754983887038 HTTP/1.1" 200 156
************ - - [12/Aug/2025:15:31:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754983887038 HTTP/1.1" 200 166
************ - - [12/Aug/2025:15:31:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:15:31:27 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:15:31:27 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+16:00&filterCnt=6&_timer304=1754983887038 HTTP/1.1" 200 164
************ - - [12/Aug/2025:15:31:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:15:31:27 +0800] "OPTIONS /api/syq/river/select-forecast-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754983887038 HTTP/1.1" 200 169
************ - - [12/Aug/2025:15:31:27 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754983887038 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:15:31:27 +0800] "POST /api/syq/river/select-forecast-by-page HTTP/1.1" 200 243
************ - - [12/Aug/2025:15:31:33 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:33 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 242
************ - - [12/Aug/2025:15:31:42 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:42 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 242
************ - - [12/Aug/2025:15:31:46 +0800] "OPTIONS /api/syq/river/select-forecast-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:46 +0800] "POST /api/syq/river/select-forecast-by-page HTTP/1.1" 200 243
************ - - [12/Aug/2025:15:31:46 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:47 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 242
************ - - [12/Aug/2025:15:31:47 +0800] "OPTIONS /api/syq/river/select-forecast-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:48 +0800] "POST /api/syq/river/select-forecast-by-page HTTP/1.1" 200 243
************ - - [12/Aug/2025:15:31:48 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:48 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 242
************ - - [12/Aug/2025:15:31:48 +0800] "OPTIONS /api/syq/river/select-forecast-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:31:49 +0800] "POST /api/syq/river/select-forecast-by-page HTTP/1.1" 200 243
************ - - [12/Aug/2025:15:32:31 +0800] "OPTIONS /api/syq/river/select-forecast-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:32:31 +0800] "POST /api/syq/river/select-forecast-by-page HTTP/1.1" 200 243
************ - - [12/Aug/2025:15:32:33 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:32:33 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 242
************ - - [12/Aug/2025:15:34:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754984044440 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:34:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754984044440 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:35:02 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754984102318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:35:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754984102319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:35:02 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754984102318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:35:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754984102319 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:35:02 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754984102318 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:35:02 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754984102318 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+15:38:12&etm=&_timer304=1754984292797 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754984292797 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+16:00&filterCnt=6&_timer304=1754984292797 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754984292797 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754984292797 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1754984292800 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:12 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:15:38:12 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:15:38:12 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754984292797 HTTP/1.1" 200 166
************ - - [12/Aug/2025:15:38:12 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+16:00&filterCnt=6&_timer304=1754984292797 HTTP/1.1" 200 164
************ - - [12/Aug/2025:15:38:12 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:15:38:12 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+15:38:12&etm=&_timer304=1754984292797 HTTP/1.1" 200 156
************ - - [12/Aug/2025:15:38:12 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:15:38:12 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1754984292800 HTTP/1.1" 200 1482
************ - - [12/Aug/2025:15:38:12 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754984292797 HTTP/1.1" 200 169
************ - - [12/Aug/2025:15:38:12 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754984292797 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754984293259 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/base/saas/token?_timer304=1754984293259 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754984293260 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-11&_timer304=1754984293259 HTTP/1.1" 200 396
************ - - [12/Aug/2025:15:38:13 +0800] "GET /api/base/saas/token?_timer304=1754984293259 HTTP/1.1" 200 411
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 287155
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 345
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [12/Aug/2025:15:38:13 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754984293260 HTTP/1.1" 200 2009
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+15:38:13&etm=&_timer304=1754984293951 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754984293951 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+16:00&filterCnt=6&_timer304=1754984293951 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754984293951 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754984293951 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-09+15:38:13&etm=&_timer304=1754984293951 HTTP/1.1" 200 156
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754984293952 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754984293954 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754984293955 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754984293951 HTTP/1.1" 200 166
************ - - [12/Aug/2025:15:38:13 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754984293955 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [12/Aug/2025:15:38:13 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:15:38:13 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-12+08:00&etm=2025-08-12+16:00&filterCnt=6&_timer304=1754984293951 HTTP/1.1" 200 164
************ - - [12/Aug/2025:15:38:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754984293951 HTTP/1.1" 200 169
************ - - [12/Aug/2025:15:38:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:38:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [12/Aug/2025:15:38:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754984293952 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:38:14 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754984293951 HTTP/1.1" 200 13016
************ - - [12/Aug/2025:15:38:14 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1754984293954 HTTP/1.1" 200 159616
************ - - [12/Aug/2025:15:38:14 +0800] "OPTIONS /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:14 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 2307
************ - - [12/Aug/2025:15:38:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:38:14 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 388
************ - - [12/Aug/2025:15:38:14 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 714
************ - - [12/Aug/2025:15:38:14 +0800] "OPTIONS /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754984294910 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:14 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754984293955 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:38:14 +0800] "POST /api/fusion/warning/latest-warn-page-list HTTP/1.1" 200 247350
************ - - [12/Aug/2025:15:38:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:38:15 +0800] "GET /api/syq/rain/select-forecast-3hours?stcd=&stm=&etm=&_timer304=1754984294910 HTTP/1.1" 200 258
************ - - [12/Aug/2025:15:38:15 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1943
************ - - [12/Aug/2025:15:38:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754984293955 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:38:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1949
************ - - [12/Aug/2025:15:38:15 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:38:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:38:16 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 5341
************ - - [12/Aug/2025:15:39:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754984344474 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:39:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754984344474 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:40:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754984403331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:40:03 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754984403332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:40:03 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754984403332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:40:03 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754984403332 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:40:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754984403331 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:40:03 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754984403332 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:43:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754984594438 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:43:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:43:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:43:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754984594440 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:43:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:43:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754984594438 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:43:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:43:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:43:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754984594440 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:43:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:45:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754984704325 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:45:04 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754984704326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:45:04 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754984704326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:45:04 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754984704326 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:45:04 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754984704326 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:45:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754984704325 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:45:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754984721429 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:45:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754984721429 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:48:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754984894323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:48:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:48:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:48:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:48:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754984894326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:48:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754984894323 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:48:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:48:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:48:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754984894326 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:48:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:50:05 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754985005326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:50:05 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754985005326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:50:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754985005327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:50:05 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754985005326 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:50:05 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754985005326 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:50:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754985005327 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:50:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754985021327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:50:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754985021327 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:53:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754985194327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:53:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:53:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:53:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754985194329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:53:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:53:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754985194327 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:53:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:53:14 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:53:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754985194329 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:53:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:55:06 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754985306321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:55:06 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754985306321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:55:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754985306323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:55:06 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754985306321 HTTP/1.1" 200 148
************ - - [12/Aug/2025:15:55:06 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754985306321 HTTP/1.1" 200 152
************ - - [12/Aug/2025:15:55:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754985306323 HTTP/1.1" 200 159
************ - - [12/Aug/2025:15:55:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754985321324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:55:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754985321324 HTTP/1.1" 200 160
************ - - [12/Aug/2025:15:58:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754985494442 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:58:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:58:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:58:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:58:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754985494444 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:58:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754985494442 HTTP/1.1" 200 161
************ - - [12/Aug/2025:15:58:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:15:58:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:15:58:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754985494444 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:15:58:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:15:59:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754985546325 HTTP/1.1" 200 -
************ - - [12/Aug/2025:15:59:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754985546325 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:00:07 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754985607321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:00:07 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754985607321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:00:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754985607322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:00:07 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754985607321 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:00:07 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754985607321 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:00:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754985607322 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:03:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754985794319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:03:14 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:03:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:03:14 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:03:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754985794321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:03:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754985794319 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:03:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:03:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:03:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754985794321 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:03:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:04:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754985846326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:04:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754985846326 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:05:08 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754985908459 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:05:08 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754985908459 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:05:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754985908460 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:05:08 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754985908459 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:05:08 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754985908459 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:05:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754985908460 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:08:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754986094318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:08:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754986094318 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:08:15 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:08:15 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:08:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754986095331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:08:15 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:08:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:08:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:08:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754986095331 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:08:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:09:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754986146324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:09:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754986146324 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:10:09 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754986209438 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:10:09 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754986209438 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:10:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754986209439 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:10:09 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754986209438 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:10:09 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754986209438 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:10:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754986209439 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:13:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754986401321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:13:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754986401321 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:14:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:14:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:14:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754986461454 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:14:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:14:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:14:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:14:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754986461454 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:14:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:15:10 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754986510326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:15:10 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754986510326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:15:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754986510327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:15:10 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754986510326 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:15:10 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754986510326 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:15:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754986510327 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:15:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754986521319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:15:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754986521319 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:18:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754986701321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:18:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754986701321 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:19:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:19:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:19:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:19:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754986761327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:19:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:19:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:19:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754986761327 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:19:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:20:11 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754986811444 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:20:11 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754986811444 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:20:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754986811446 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:20:11 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754986811444 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:20:11 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754986811444 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:20:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754986811446 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:20:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754986821318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:20:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754986821318 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:23:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754987001441 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:23:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754987001441 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:24:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:24:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:24:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754987061464 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:24:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:24:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:24:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:24:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754987061464 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:24:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:25:12 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754987112441 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:25:12 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754987112441 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:25:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754987112442 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:25:12 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754987112441 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:25:12 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754987112441 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:25:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754987112442 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:25:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754987121321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:25:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754987121321 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:28:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754987294439 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:28:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754987294439 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:28:15 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:28:15 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:28:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754987295333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:28:15 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:28:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:28:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:28:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754987295333 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:28:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:29:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754987374637 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:29:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754987374637 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:30:13 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754987413328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:30:13 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754987413328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:30:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754987413329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:30:13 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754987413328 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:30:13 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754987413328 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:30:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754987413329 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:33:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754987601318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:33:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754987601318 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:34:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:34:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:34:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:34:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754987661321 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:34:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:34:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:34:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754987661321 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:34:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:35:14 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754987714442 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:35:14 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754987714442 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:35:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754987714443 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:35:14 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754987714442 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:35:14 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754987714442 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:35:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754987714443 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:35:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754987721332 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:35:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754987721332 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:38:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754987901328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:38:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754987901328 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:39:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:39:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:39:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754987961320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:39:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:39:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:39:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:39:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754987961320 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:39:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:40:15 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754988015440 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:40:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754988015441 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:40:15 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754988015440 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:40:15 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754988015440 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:40:15 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754988015440 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:40:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754988015441 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:40:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754988021319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:40:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754988021319 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:43:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754988201326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:43:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754988201326 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:44:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:44:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754988261320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:44:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:44:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:44:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:44:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:44:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754988261320 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:44:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:45:16 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754988316326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:45:16 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754988316326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:45:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754988316328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:45:16 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754988316326 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:45:16 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754988316326 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:45:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754988316328 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:45:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754988321323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:45:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754988321323 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:48:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754988501320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:48:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754988501320 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:49:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:49:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:49:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754988561444 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:49:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:49:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:49:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:49:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754988561444 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:49:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:50:17 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754988617322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:50:17 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754988617322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:50:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754988617323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:50:17 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754988617322 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:50:17 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754988617322 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:50:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754988617323 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:50:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754988621318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:50:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754988621318 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:53:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754988801327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:53:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754988801327 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:54:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:54:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:54:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754988861331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:54:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:54:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:54:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:54:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754988861331 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:54:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:55:18 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754988918454 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:55:18 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754988918454 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:55:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754988918456 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:55:18 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754988918454 HTTP/1.1" 200 152
************ - - [12/Aug/2025:16:55:18 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754988918454 HTTP/1.1" 200 148
************ - - [12/Aug/2025:16:55:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754988918456 HTTP/1.1" 200 159
************ - - [12/Aug/2025:16:55:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754988921319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:55:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754988921319 HTTP/1.1" 200 160
************ - - [12/Aug/2025:16:58:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754989101328 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:58:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754989101328 HTTP/1.1" 200 161
************ - - [12/Aug/2025:16:58:52 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:58:52 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:58:52 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754989132624 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:58:52 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:58:52 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:16:58:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:16:58:53 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754989132624 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:16:58:53 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53060
************ - - [12/Aug/2025:16:59:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754989146327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:16:59:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754989146327 HTTP/1.1" 200 160
************ - - [12/Aug/2025:17:00:19 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754989219326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:00:19 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754989219326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:00:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754989219327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:00:19 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754989219326 HTTP/1.1" 200 148
************ - - [12/Aug/2025:17:00:19 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754989219326 HTTP/1.1" 200 152
************ - - [12/Aug/2025:17:00:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754989219327 HTTP/1.1" 200 159
************ - - [12/Aug/2025:17:03:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754989401453 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:03:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754989401453 HTTP/1.1" 200 161
************ - - [12/Aug/2025:17:04:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:04:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:04:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754989461456 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:04:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:04:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:17:04:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:17:04:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754989461456 HTTP/1.1" 200 443113
************ - - [12/Aug/2025:17:04:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53667
************ - - [12/Aug/2025:17:05:20 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754989520331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:05:20 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754989520331 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:05:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754989520333 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:05:20 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754989520331 HTTP/1.1" 200 148
************ - - [12/Aug/2025:17:05:20 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754989520331 HTTP/1.1" 200 152
************ - - [12/Aug/2025:17:05:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754989520333 HTTP/1.1" 200 159
************ - - [12/Aug/2025:17:05:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754989521323 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:05:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754989521323 HTTP/1.1" 200 160
************ - - [12/Aug/2025:17:08:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754989701326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:08:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754989701326 HTTP/1.1" 200 161
************ - - [12/Aug/2025:17:09:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:09:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:09:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754989761320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:09:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:09:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:17:09:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:17:09:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754989761320 HTTP/1.1" 200 443102
************ - - [12/Aug/2025:17:09:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53670
************ - - [12/Aug/2025:17:10:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754989821456 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:10:21 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754989821458 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:10:21 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754989821458 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:10:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754989821459 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:10:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754989821456 HTTP/1.1" 200 160
************ - - [12/Aug/2025:17:10:21 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754989821458 HTTP/1.1" 200 152
************ - - [12/Aug/2025:17:10:21 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754989821458 HTTP/1.1" 200 148
************ - - [12/Aug/2025:17:10:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754989821459 HTTP/1.1" 200 159
************ - - [12/Aug/2025:17:13:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754990001318 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:13:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754990001318 HTTP/1.1" 200 161
************ - - [12/Aug/2025:17:14:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:14:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:14:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754990061334 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:14:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:14:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:17:14:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:17:14:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754990061334 HTTP/1.1" 200 443102
************ - - [12/Aug/2025:17:14:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53670
************ - - [12/Aug/2025:17:15:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754990121319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:15:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754990121319 HTTP/1.1" 200 160
************ - - [12/Aug/2025:17:15:22 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754990122319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:15:22 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754990122319 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:15:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754990122320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:15:22 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754990122319 HTTP/1.1" 200 148
************ - - [12/Aug/2025:17:15:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754990122320 HTTP/1.1" 200 159
************ - - [12/Aug/2025:17:15:22 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754990122319 HTTP/1.1" 200 152
************ - - [12/Aug/2025:17:18:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754990301447 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:18:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754990301447 HTTP/1.1" 200 161
************ - - [12/Aug/2025:17:19:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:19:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:19:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754990361334 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:19:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:19:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:17:19:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:17:19:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53670
************ - - [12/Aug/2025:17:19:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754990361334 HTTP/1.1" 200 443102
************ - - [12/Aug/2025:17:20:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754990421325 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:20:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754990421325 HTTP/1.1" 200 160
************ - - [12/Aug/2025:17:20:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754990423329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:20:23 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754990423330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:20:23 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754990423330 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:20:23 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754990423330 HTTP/1.1" 200 148
************ - - [12/Aug/2025:17:20:23 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754990423330 HTTP/1.1" 200 152
************ - - [12/Aug/2025:17:20:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754990423329 HTTP/1.1" 200 159
************ - - [12/Aug/2025:17:23:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754990601324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:23:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754990601324 HTTP/1.1" 200 161
************ - - [12/Aug/2025:17:24:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:24:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:24:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754990661465 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:24:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:24:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:17:24:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:17:24:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754990661465 HTTP/1.1" 200 443102
************ - - [12/Aug/2025:17:24:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53670
************ - - [12/Aug/2025:17:25:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754990721476 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:25:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754990721476 HTTP/1.1" 200 160
************ - - [12/Aug/2025:17:25:24 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754990724326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:25:24 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754990724326 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:25:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754990724327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:25:24 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754990724326 HTTP/1.1" 200 152
************ - - [12/Aug/2025:17:25:24 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754990724326 HTTP/1.1" 200 148
************ - - [12/Aug/2025:17:25:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754990724327 HTTP/1.1" 200 159
************ - - [12/Aug/2025:17:28:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754990901324 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:28:21 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1754990901324 HTTP/1.1" 200 161
************ - - [12/Aug/2025:17:29:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:29:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:29:21 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:29:21 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754990961320 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:29:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [12/Aug/2025:17:29:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [12/Aug/2025:17:29:22 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-08-12+08:00&etm=2025-08-12+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1754990961320 HTTP/1.1" 200 443102
************ - - [12/Aug/2025:17:29:22 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53670
************ - - [12/Aug/2025:17:30:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754991021322 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:30:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754991021322 HTTP/1.1" 200 160
************ - - [12/Aug/2025:17:30:25 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754991025327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:30:25 +0800] "OPTIONS /api/fusion/warning/is-enabled?_timer304=1754991025327 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:30:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754991025329 HTTP/1.1" 200 -
************ - - [12/Aug/2025:17:30:25 +0800] "GET /api/fusion/warning/is-enabled?_timer304=1754991025327 HTTP/1.1" 200 152
************ - - [12/Aug/2025:17:30:25 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754991025327 HTTP/1.1" 200 148
************ - - [12/Aug/2025:17:30:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754991025329 HTTP/1.1" 200 159
