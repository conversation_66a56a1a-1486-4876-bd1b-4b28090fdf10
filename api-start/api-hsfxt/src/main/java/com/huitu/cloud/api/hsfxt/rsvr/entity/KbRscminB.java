package com.huitu.cloud.api.hsfxt.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 水库基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-28
 */
@TableName("KB_RSCMIN_B")
@ApiModel(value="KbRscminB对象", description="水库基本信息表")
public class KbRscminB implements Serializable {

    @ApiModelProperty(value = "水库编码")
    private String ennmcd;

    @ApiModelProperty(value = "坝址所在地点")
    private String dmstatpl;

    @ApiModelProperty(value = "行政区划码")
    private String dscd;

    @ApiModelProperty(value = "所在河流")
    private String rvnm;

    @ApiModelProperty(value = "工程等别")
    private String encl;

    @ApiModelProperty(value = "水准基面")
    private String lvbslv;

    @ApiModelProperty(value = "水库枢纽建筑物组成")
    private String rscci;

    @ApiModelProperty(value = "水库中心所在经度")
    private Double rslg;

    @ApiModelProperty(value = "水库中心所在纬度")
    private Double rslt;

    @ApiModelProperty(value = "水库名称")
    private String ennm;


    public String getEnnmcd() {
        return ennmcd;
    }

    public void setEnnmcd(String ennmcd) {
        this.ennmcd = ennmcd;
    }

    public String getDmstatpl() {
        return dmstatpl;
    }

    public void setDmstatpl(String dmstatpl) {
        this.dmstatpl = dmstatpl;
    }

    public String getDscd() {
        return dscd;
    }

    public void setDscd(String dscd) {
        this.dscd = dscd;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getEncl() {
        return encl;
    }

    public void setEncl(String encl) {
        this.encl = encl;
    }

    public String getLvbslv() {
        return lvbslv;
    }

    public void setLvbslv(String lvbslv) {
        this.lvbslv = lvbslv;
    }

    public String getRscci() {
        return rscci;
    }

    public void setRscci(String rscci) {
        this.rscci = rscci;
    }

    public Double getRslg() {
        return rslg;
    }

    public void setRslg(Double rslg) {
        this.rslg = rslg;
    }

    public Double getRslt() {
        return rslt;
    }

    public void setRslt(Double rslt) {
        this.rslt = rslt;
    }

    public String getEnnm() {
        return ennm;
    }

    public void setEnnm(String ennm) {
        this.ennm = ennm;
    }
}
