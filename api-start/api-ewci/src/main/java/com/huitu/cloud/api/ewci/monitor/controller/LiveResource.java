package com.huitu.cloud.api.ewci.monitor.controller;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.monitor.entity.*;
import com.huitu.cloud.api.ewci.monitor.service.LiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 实况信息
 *
 * <AUTHOR>
 */
@Api(tags = "实况信息")
@RestController
@RequestMapping("/api/ewci/monitor/live")
public class LiveResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "27b946c0-77ee-e722-07f4-ef974efbcc0f";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final LiveService baseService;

    public LiveResource(LiveService baseService) {
        this.baseService = baseService;
    }

    @ApiOperation(value = "雨情信息列表", notes = "作者：曹宝金")
    @PostMapping("select-rain-list")
    public ResponseEntity<SuccessResponse<List<LiveRain>>> getRainList(@RequestBody @Validated LiveQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRainList(query)));
    }

    @ApiOperation(value = "防治区信息列表", notes = "作者：zyj")
    @PostMapping("select-prevad-list")
    public ResponseEntity<SuccessResponse<List<IaCPrevad>>> getPrevadList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new IllegalArgumentException("危险区代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPrevadList(list)));
    }

    @ApiOperation(value = "危险区信息列表", notes = "作者：zyj")
    @PostMapping("select-danad-list")
    public ResponseEntity<SuccessResponse<List<IaCDanad>>> getDanadList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new IllegalArgumentException("危险区代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getDanadList(list)));
    }

    @ApiOperation(value = "山洪责任人信息列表", notes = "作者：zyj")
    @PostMapping("select-flood-persion-list")
    public ResponseEntity<SuccessResponse<List<FloodPerson>>> getFloodPersonList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getFloodPersonList(list)));
    }

    @ApiOperation(value = "水库责任人信息列表", notes = "作者：jiangjy")
    @PostMapping("select-rsvr-person-list")
    public ResponseEntity<SuccessResponse<List<RsvrPersonVo>>> getRsvrPersonList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRsvrPersonList(list)));
    }

    @ApiOperation(value = "防洪工程信息-水库列表", notes = "作者：zyj")
    @PostMapping("select-reservoir-list")
    public ResponseEntity<SuccessResponse<List<ReservoirData>>> getReservoirList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", null));
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getReservoirList(list)));
    }

    @ApiOperation(value = "防洪工程信息-水闸列表", notes = "作者：zyj")
    @PostMapping("select-sluice-list")
    public ResponseEntity<SuccessResponse<List<SluiceData>>> getSluiceList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", null));
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSluiceList(list)));
    }

    @ApiOperation(value = "防洪工程信息-堤防列表", notes = "作者：zyj")
    @PostMapping("select-dike-list")
    public ResponseEntity<SuccessResponse<List<DikeData>>> getDikeList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", null));
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getDikeList(list)));
    }

    @ApiOperation(value = "防洪工程信息-涵洞列表", notes = "作者：zyj")
    @PostMapping("select-culvert-list")
    public ResponseEntity<SuccessResponse<List<CulvertData>>> getCulvertList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", null));
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getCulvertList(list)));
    }

    @ApiOperation(value = "防洪工程信息-桥梁列表", notes = "作者：zyj")
    @PostMapping("select-bridge-list")
    public ResponseEntity<SuccessResponse<List<BridgeData>>> getBridgeList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", null));
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBridgeList(list)));
    }

    @ApiOperation(value = "防洪工程信息-塘堰列表", notes = "作者：zyj")
    @PostMapping("select-daminfo-list")
    public ResponseEntity<SuccessResponse<List<DaminfoData>>> getDaminfoList(@RequestBody List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", null));
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getDaminfoList(list)));
    }

    @ApiOperation(value = "生成预警文件", notes = "作者：姜金阳")
    @PostMapping("generate-warn-file")
    public ResponseEntity<SuccessResponse<String>> generateWarnFile(@RequestBody @Validated LiveWarnFileQuery query) {
        try {
            baseService.generateWarnFile(query);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", "0"));
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", "1"));
    }
    @ApiOperation(value = "获取实况预警规则", notes = "作者：姜金阳")
    @GetMapping("select-live-warn-status-list/{level}/{adcd}")
    public ResponseEntity<SuccessResponse<List<MessageWarnStatusB>>> getLiveWarnStatusList(@PathVariable(value = "level") String level, @PathVariable(value = "adcd") String adcd) {
        if (StringUtils.isBlank(adcd)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        if (StringUtils.isBlank(level)) {
            throw new IllegalArgumentException("类型不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getLiveWarnStatusList(level, adcd)));
    }
}
