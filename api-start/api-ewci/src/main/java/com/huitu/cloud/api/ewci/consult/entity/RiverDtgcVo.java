package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 作战图-穿堤建筑物
 */
@ApiModel(value = "作战图-穿堤建筑物")
public class RiverDtgcVo implements Serializable {

    @ApiModelProperty(value = "河流编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "河流名称")
    @TableField(value = "RV_NAME")
    private String rvName;

    @ApiModelProperty(value = "政区编码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "管线数量")
    @TableField(value = "PIPECT")
    private Integer pipect;

    @ApiModelProperty(value = "涵洞数量")
    @TableField(value = "LHCT")
    private Integer lhct;

    @ApiModelProperty(value = "闸门数量")
    @TableField(value = "WAGACT")
    private Integer wagact;

    @ApiModelProperty(value = "泵站数量")
    @TableField(value = "PUSTCT")
    private Integer pustct;

    @ApiModelProperty(value = "总数量")
    @TableField(value = "TOTAL")
    private Integer total;

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getPipect() {
        return pipect;
    }

    public void setPipect(Integer pipect) {
        this.pipect = pipect;
    }

    public Integer getLhct() {
        return lhct;
    }

    public void setLhct(Integer lhct) {
        this.lhct = lhct;
    }

    public Integer getWagact() {
        return wagact;
    }

    public void setWagact(Integer wagact) {
        this.wagact = wagact;
    }

    public Integer getPustct() {
        return pustct;
    }

    public void setPustct(Integer pustct) {
        this.pustct = pustct;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }
}
