package com.huitu.cloud.api.screen.fhjz.mapper;


import com.huitu.cloud.api.screen.fhjz.entity.DikeVo;
import com.huitu.cloud.api.screen.fhjz.entity.RiverDetailVo;
import com.huitu.cloud.api.screen.fhjz.entity.RiverVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
public interface RiverScreenDao {

    List<RiverVo> queryRiverList(@Param("rvName")String rvName);

    List<Map> riverStationList(@Param("stm")String stm, @Param("etm") String etm, @Param("rvCode") String rvCode);

    /**
     * 根据河道站测站编码查询河道站信息
     *
     * @param stcd
     * @return
     */
    Map<String, Object> queryRiverStationInfo(@Param("stcd") String stcd);

    /**
     * 根据河流编码查询河流信息
     *
     * @param rvCode
     * @return
     */
    Map<String, Object> queryRiverInfo(@Param("rvCode") String rvCode);

    /**
     * 查询防洪工程河流信息
     *
     * @param rvName
     * @return
     */
    List<RiverDetailVo> queryRiverDetailList(@Param("rvName") String rvName);

    /**
     * 查询水库工程总数
     *
     * @param adcd
     * @return
     */
    int queryRsvrTotal(@Param("adcd") String adcd);

    /**
     * 查询堤防工程总数
     *
     * @param adcd
     * @return
     */
    int queryDikeTotal(@Param("adcd") String adcd);

    /**
     * 查询河流工程总数
     *
     * @param adcd
     * @return
     */
    int queryRiverTotal(@Param("adcd") String adcd);

    /**
     * 查询河流详情数量
     *
     * @return
     */
    Map<String, Object> statisticRiverDetail();
}
