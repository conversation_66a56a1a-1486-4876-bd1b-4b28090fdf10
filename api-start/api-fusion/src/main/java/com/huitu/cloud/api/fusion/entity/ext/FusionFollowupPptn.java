package com.huitu.cloud.api.fusion.entity.ext;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrFormatter;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

import static cn.hutool.core.date.DatePattern.PURE_DATETIME_MS_PATTERN;

/**
 * 测站后续降雨数据
 */
@Getter
@Setter
public class FusionFollowupPptn implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 测站编码
     */
    @TableField(value = "STCD")
    private String stcd;

    /**
     * 预警时间
     */
    @TableField(value = "WTIME")
    private Date wtime;

    /**
     * 数据时间
     */
    @TableField(value = "DTIME")
    private Date dtime;

    /**
     * 时段雨量
     */
    @TableField(value = "DRP")
    private Double drp;

    /**
     * 分组键
     * <p>注：根据测站编码和预警时间生成分组键</p>
     *
     * @return 键
     */
    public String groupKey() {
        return StrFormatter.format("{}_{}", stcd, DateUtil.format(wtime, PURE_DATETIME_MS_PATTERN));
    }
}
