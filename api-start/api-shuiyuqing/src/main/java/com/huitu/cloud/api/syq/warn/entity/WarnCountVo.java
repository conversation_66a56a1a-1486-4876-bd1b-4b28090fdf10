package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

@ApiModel(value="WarnCountVo", description="告警统计对象")
public class WarnCountVo {
    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "水库告警总数量")
    private int skTotal;
    @ApiModelProperty(value = "水库超汛限数量")
    private int skCxTotal;
    @ApiModelProperty(value = "水库超正常高数量")
    private int skCgTotal;
    @ApiModelProperty(value = "河道告警总数量")
    private int hdTotal;
    @ApiModelProperty(value = "河道超警戒数量")
    private int hdCjTotal;
    @ApiModelProperty(value = "河道超保证数量")
    private int hdCbTotal;
    @ApiModelProperty(value = "雨量告警总数量")
    private int ylTotal;
    @ApiModelProperty(value = "雨量超警戒数量")
    private int ylCjTotal;
    @ApiModelProperty(value = "雨量超危险数量")
    private int ylCwTotal;

    public int getSkTotal() {
        return skTotal;
    }

    public void setSkTotal(int skTotal) {
        this.skTotal = skTotal;
    }

    public int getSkCxTotal() {
        return skCxTotal;
    }

    public void setSkCxTotal(int skCxTotal) {
        this.skCxTotal = skCxTotal;
    }

    public int getSkCgTotal() {
        return skCgTotal;
    }

    public void setSkCgTotal(int skCgTotal) {
        this.skCgTotal = skCgTotal;
    }

    public int getHdTotal() {
        return hdTotal;
    }

    public void setHdTotal(int hdTotal) {
        this.hdTotal = hdTotal;
    }

    public int getHdCjTotal() {
        return hdCjTotal;
    }

    public void setHdCjTotal(int hdCjTotal) {
        this.hdCjTotal = hdCjTotal;
    }

    public int getHdCbTotal() {
        return hdCbTotal;
    }

    public void setHdCbTotal(int hdCbTotal) {
        this.hdCbTotal = hdCbTotal;
    }

    public int getYlTotal() {
        return ylTotal;
    }

    public void setYlTotal(int ylTotal) {
        this.ylTotal = ylTotal;
    }

    public int getYlCjTotal() {
        return ylCjTotal;
    }

    public void setYlCjTotal(int ylCjTotal) {
        this.ylCjTotal = ylCjTotal;
    }

    public int getYlCwTotal() {
        return ylCwTotal;
    }

    public void setYlCwTotal(int ylCwTotal) {
        this.ylCwTotal = ylCwTotal;
    }
}
