package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 重点城(集)镇调查评价涉水工程-水库详情列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@ApiModel(value="DcjzReservoitSs对象", description="重点城(集)镇调查评价涉水工程-水库详情列表")
public class DcjzReservoitSs extends Model<DcjzReservoitSs> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "水库名称")
    @TableId(value = "RSNAME", type = IdType.NONE)
    private String rsName;

    @ApiModelProperty(value = "所在河流工程规模")
    @TableId(value = "ENG_GCGM", type = IdType.NONE)
    private String engGcgm;

    @ApiModelProperty(value = "工程等别")
    @TableId(value = "ENG_GCDJ", type = IdType.NONE)
    private String engGcdj;

    @ApiModelProperty(value = "水库类型 1=山丘水库、2=平原水库及地下水库")
    @TableId(value = "RS_TYPE", type = IdType.NONE)
    private String rsType;

    @ApiModelProperty(value = "主要挡水建筑物类型")
    @TableId(value = "MAIN_WR_TYPE", type = IdType.NONE)
    private String mainWrType;

    @ApiModelProperty(value = "挡水主坝类型")
    @TableId(value = "DAM_TYPE", type = IdType.NONE)
    private String damType;

    @ApiModelProperty(value = "总库容")
    @TableId(value = "TOT_CAP", type = IdType.NONE)
    private String totCap;

    @ApiModelProperty(value = "坝址多年平均净流量")
    @TableId(value = "MUL_AVER_RUN", type = IdType.NONE)
    private String mulAverRun;

    @ApiModelProperty(value = "主坝坝高")
    @TableId(value = "DAM_SIZE_HIG", type = IdType.NONE)
    private String damSizeHig;

    @ApiModelProperty(value = "主坝坝长")
    @TableId(value = "DAM_SIZE_LEN", type = IdType.NONE)
    private String damSizeLen;

    @ApiModelProperty(value = "最大泄洪流量")
    @TableId(value = "MAX_DIS_FLOW", type = IdType.NONE)
    private String maxDisFlow;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getRsName() {
        return rsName;
    }

    public void setRsName(String rsName) {
        this.rsName = rsName;
    }

    public String getEngGcgm() {
        return engGcgm;
    }

    public void setEngGcgm(String engGcgm) {
        this.engGcgm = engGcgm;
    }

    public String getEngGcdj() {
        return engGcdj;
    }

    public void setEngGcdj(String engGcdj) {
        this.engGcdj = engGcdj;
    }

    public String getRsType() {
        return rsType;
    }

    public void setRsType(String rsType) {
        this.rsType = rsType;
    }

    public String getMainWrType() {
        return mainWrType;
    }

    public void setMainWrType(String mainWrType) {
        this.mainWrType = mainWrType;
    }

    public String getDamType() {
        return damType;
    }

    public void setDamType(String damType) {
        this.damType = damType;
    }

    public String getTotCap() {
        return totCap;
    }

    public void setTotCap(String totCap) {
        this.totCap = totCap;
    }

    public String getMulAverRun() {
        return mulAverRun;
    }

    public void setMulAverRun(String mulAverRun) {
        this.mulAverRun = mulAverRun;
    }

    public String getDamSizeHig() {
        return damSizeHig;
    }

    public void setDamSizeHig(String damSizeHig) {
        this.damSizeHig = damSizeHig;
    }

    public String getDamSizeLen() {
        return damSizeLen;
    }

    public void setDamSizeLen(String damSizeLen) {
        this.damSizeLen = damSizeLen;
    }

    public String getMaxDisFlow() {
        return maxDisFlow;
    }

    public void setMaxDisFlow(String maxDisFlow) {
        this.maxDisFlow = maxDisFlow;
    }

    @Override
    public String toString() {
        return "DcjzReservoitSs{" +
                "adnm='" + adnm + '\'' +
                ", rsName='" + rsName + '\'' +
                ", engGcgm='" + engGcgm + '\'' +
                ", engGcdj='" + engGcdj + '\'' +
                ", rsType='" + rsType + '\'' +
                ", mainWrType='" + mainWrType + '\'' +
                ", damType='" + damType + '\'' +
                ", totCap='" + totCap + '\'' +
                ", mulAverRun='" + mulAverRun + '\'' +
                ", damSizeHig='" + damSizeHig + '\'' +
                ", damSizeLen='" + damSizeLen + '\'' +
                ", maxDisFlow='" + maxDisFlow + '\'' +
                '}';
    }
}
