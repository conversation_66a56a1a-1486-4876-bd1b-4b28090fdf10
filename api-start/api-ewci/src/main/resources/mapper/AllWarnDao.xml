<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.AllWarnDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getWarnSummary" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT COUNT(A.WARNID) WCOUNT, ISNULL(SUM(SCOUNT), 0) SCOUNT, ISNULL(SUM(PCOUNT), 0) PCOUNT FROM (
        SELECT WARNID, ADCD, WARNSTM WTM FROM WARNRECORD_R WHERE WARNTYPEID = 10
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD) A
        LEFT JOIN (SELECT WARNID, 1 SCOUNT FROM MESSAGEINFO_R GROUP BY WARNID) B ON B.WARNID = A.WARNID
        LEFT JOIN (SELECT WARNID, COUNT(0) PCOUNT FROM MESSAGESEND_R S, MESSAGEINFO_R I WHERE I.MSGID = S.MSGID AND
        I.ADCD = S.ADCD GROUP BY WARNID) C ON C.WARNID = A.WARNID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
    </select>
    <select id="getShWarnSummary" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT COUNT(A.WARNID) WCOUNT, ISNULL(SUM(SCOUNT), 0) SCOUNT, ISNULL(SUM(PCOUNT), 0) PCOUNT
        FROM (SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD WHERE WARN_TYPE_ID = 10) A
        LEFT JOIN (SELECT WARN_ID WARNID, 1 SCOUNT FROM EW_WARNING_MESSAGE GROUP BY WARN_ID) B ON B.WARNID = A.WARNID
        LEFT JOIN (SELECT WARN_ID WARNID, COUNT(0) PCOUNT FROM EW_CALL_FEEDBACK S, EW_WARNING_MESSAGE I
        WHERE S.SMS_WARN_MSG_ID = I.MSG_ID AND S.CALL_TYPE = '1' AND I.PUSH_MODE = '1' GROUP BY WARN_ID) C ON C.WARNID =
        A.WARNID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
    </select>
    <select id="getRiverWarnSummary" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT COUNT(A.WARNID) WCOUNT, ISNULL(SUM(SCOUNT), 0) SCOUNT, ISNULL(SUM(PCOUNT), 0) PCOUNT
        FROM (SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND B.STADTP
        IN ('1', '2')) A
        LEFT JOIN (SELECT WARN_ID WARNID, 1 SCOUNT FROM EW_RIVER_WARNING_MESSAGE GROUP BY WARN_ID) B ON B.WARNID = A.WARNID
        LEFT JOIN (SELECT WARN_ID WARNID, COUNT(0) PCOUNT FROM EW_CALL_FEEDBACK S, EW_RIVER_WARNING_MESSAGE I
        WHERE S.SMS_WARN_MSG_ID = I.MSG_ID AND S.CALL_TYPE = '2' AND I.PUSH_MODE = '1' GROUP BY WARN_ID) C ON C.WARNID =
        A.WARNID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
    </select>
    <select id="getRsvrWarnSummary" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT COUNT(A.WARNID) WCOUNT, ISNULL(SUM(SCOUNT), 0) SCOUNT, ISNULL(SUM(PCOUNT), 0) PCOUNT
        FROM (SELECT WARN_ID WARNID, C.ADCD, WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
        LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
        WHERE C.STADTP IN ('1', '2', '4') AND H.ENG_SCAL IN ('1', '2', '3', '4', '5')) A
        LEFT JOIN (SELECT WARN_ID WARNID, 1 SCOUNT FROM EW_RSVR_WARNING_MESSAGE GROUP BY WARN_ID) B ON B.WARNID = A.WARNID
        LEFT JOIN (SELECT WARN_ID WARNID, COUNT(0) PCOUNT FROM EW_CALL_FEEDBACK S, EW_RSVR_WARNING_MESSAGE I
        WHERE S.SMS_WARN_MSG_ID = I.MSG_ID AND S.CALL_TYPE = '3' AND I.PUSH_MODE = '1' GROUP BY WARN_ID) C ON C.WARNID =
        A.WARNID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
    </select>

    <select id="getSubWarnPersonCountList" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT A.ADCD, A.ADNM, ISNULL(C.PCOUNT, 0) PCOUNT FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.PCOUNT) PCOUNT FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, 1 PCOUNT
        FROM (SELECT WARNID, ADCD, WARNSTM WTM FROM WARNRECORD_R WHERE WARNTYPEID = 10
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD) A
        RIGHT JOIN MESSAGEINFO_R B ON B.WARNID = A.WARNID
        RIGHT JOIN MESSAGESEND_R C ON C.MSGID = B.MSGID AND C.ADCD = B.ADCD
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, COUNT(0) PCOUNT FROM (
            SELECT WARNID, ADCD, WARNSTM WTM FROM WARNRECORD_R WHERE WARNTYPEID = 10 AND LEFT(ADCD, 6) = '220581'
            UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B
            WHERE B.STCD = A.STCD AND LEFT(B.ADCD, 6) = '220581'
            UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B
            WHERE B.STCD = A.STCD AND LEFT(B.ADCD, 6) = '220581') A
            RIGHT JOIN MESSAGEINFO_R B ON B.WARNID = A.WARNID
            RIGHT JOIN MESSAGESEND_R C ON C.MSGID = B.MSGID AND C.ADCD = B.ADCD
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getShSubWarnPersonCountList" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT A.ADCD, A.ADNM, B.SORTNO,ISNULL(C.PCOUNT, 0) PCOUNT FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.PCOUNT) PCOUNT FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, 1 PCOUNT
        FROM (SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD WHERE WARN_TYPE_ID = 10) A
        RIGHT JOIN EW_WARNING_MESSAGE B ON B.WARN_ID = A.WARNID AND B.PUSH_MODE = '1'
        RIGHT JOIN EW_CALL_FEEDBACK C ON C.SMS_WARN_MSG_ID = B.MSG_ID AND C.CALL_TYPE = '1'
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, COUNT(0) PCOUNT FROM (
            SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD WHERE WARN_TYPE_ID = 10 AND LEFT(ADCD, 6) = '220581') A
            RIGHT JOIN EW_WARNING_MESSAGE B ON B.WARN_ID = A.WARNID AND B.PUSH_MODE = '1'
            RIGHT JOIN EW_CALL_FEEDBACK C ON C.SMS_WARN_MSG_ID = B.MSG_ID AND C.CALL_TYPE = '1'
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getRiverSubWarnPersonCountList"
            resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT A.ADCD, A.ADNM,B.SORTNO, ISNULL(C.PCOUNT, 0) PCOUNT FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.PCOUNT) PCOUNT FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, 1 PCOUNT
        FROM (SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND B.STADTP
        IN ('1', '2')) A
        RIGHT JOIN EW_RIVER_WARNING_MESSAGE B ON B.WARN_ID = A.WARNID AND B.PUSH_MODE = '1'
        RIGHT JOIN EW_CALL_FEEDBACK C ON C.SMS_WARN_MSG_ID = B.MSG_ID AND C.CALL_TYPE = '2'
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, COUNT(0) PCOUNT FROM (
            SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND B.STADTP IN
            ('1', '2') AND LEFT(ADCD, 6) = '220581') A
            RIGHT JOIN EW_RIVER_WARNING_MESSAGE B ON B.WARN_ID = A.WARNID AND B.PUSH_MODE = '1'
            RIGHT JOIN EW_CALL_FEEDBACK C ON C.SMS_WARN_MSG_ID = B.MSG_ID AND C.CALL_TYPE = '2'
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getRsvrSubWarnPersonCountList"
            resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT A.ADCD, A.ADNM,B.SORTNO, ISNULL(C.PCOUNT, 0) PCOUNT FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.PCOUNT) PCOUNT FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, 1 PCOUNT
        FROM (SELECT WARN_ID WARNID, C.ADCD, WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
        LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
        WHERE C.STADTP IN ('1', '2', '4') AND H.ENG_SCAL IN ('1', '2', '3', '4', '5')) A
        RIGHT JOIN EW_RSVR_WARNING_MESSAGE B ON B.WARN_ID = A.WARNID AND B.PUSH_MODE = '1'
        RIGHT JOIN EW_CALL_FEEDBACK C ON C.SMS_WARN_MSG_ID = B.MSG_ID AND C.CALL_TYPE = '3'
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, COUNT(0) PCOUNT FROM (
            SELECT WARN_ID WARNID, C.ADCD, WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
            LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
            LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
            LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
            LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
            LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
            LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
            WHERE C.STADTP IN ('1', '2', '4') AND H.ENG_SCAL IN ('1', '2', '3', '4', '5') AND LEFT(C.ADCD, 6) = '220581') A
            RIGHT JOIN EW_RSVR_WARNING_MESSAGE B ON B.WARN_ID = A.WARNID AND B.PUSH_MODE = '1'
            RIGHT JOIN EW_CALL_FEEDBACK C ON C.SMS_WARN_MSG_ID = B.MSG_ID AND C.CALL_TYPE = '3'
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getSubWarnCountList" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT A.ADCD, A.ADNM, ISNULL(C.WCOUNT, 0) WCOUNT, ISNULL(C.SCOUNT, 0) SCOUNT FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.WCOUNT) WCOUNT, SUM(T.SCOUNT) SCOUNT FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, 1 WCOUNT, SCOUNT FROM (
        SELECT WARNID, ADCD, WARNSTM WTM FROM WARNRECORD_R WHERE WARNTYPEID = 10
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD
        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD) A
        LEFT JOIN (SELECT WARNID, 1 SCOUNT FROM MESSAGEINFO_R GROUP BY WARNID) B ON B.WARNID = A.WARNID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, COUNT(0) WCOUNT, SUM(SCOUNT) SCOUNT FROM (
            SELECT WARNID, ADCD, WARNSTM WTM FROM WARNRECORD_R WHERE WARNTYPEID = 10 AND LEFT(ADCD, 6) = '220581'
            UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B
            WHERE B.STCD = A.STCD AND LEFT(B.ADCD, 6) = '220581'
            UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B
            WHERE B.STCD = A.STCD AND LEFT(B.ADCD, 6) = '220581') A
            LEFT JOIN (SELECT WARNID, 1 SCOUNT FROM MESSAGEINFO_R GROUP BY WARNID) B ON B.WARNID = A.WARNID
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getShSubWarnCountList" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT A.ADCD, A.ADNM,B.SORTNO, ISNULL(C.WCOUNT, 0) WCOUNT, ISNULL(C.SCOUNT, 0) SCOUNT FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.WCOUNT) WCOUNT, SUM(T.SCOUNT) SCOUNT FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, 1 WCOUNT, SCOUNT FROM (
        SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD WHERE WARN_TYPE_ID = 10 AND WARN_STATUS_ID != 31
        ) A
        LEFT JOIN (SELECT WARN_ID, 1 SCOUNT FROM EW_WARNING_MESSAGE GROUP BY WARN_ID) B ON B.WARN_ID = A.WARNID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, COUNT(0) WCOUNT, SUM(SCOUNT) SCOUNT FROM (
            SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD
            WHERE WARN_TYPE_ID = 10 AND WARN_STATUS_ID != 31 AND LEFT(ADCD, 6) = '220581'
            ) A
            LEFT JOIN (SELECT WARN_ID, 1 SCOUNT FROM EW_WARNING_MESSAGE GROUP BY WARN_ID) B ON B.WARN_ID = A.WARNID
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getRiverSubWarnCountList" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT A.ADCD, A.ADNM,B.SORTNO, ISNULL(C.WCOUNT, 0) WCOUNT, ISNULL(C.SCOUNT, 0) SCOUNT FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.WCOUNT) WCOUNT, SUM(T.SCOUNT) SCOUNT FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, 1 WCOUNT, SCOUNT FROM (
        SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND B.STADTP IN
        ('1', '2')
        ) A
        LEFT JOIN (SELECT WARN_ID, 1 SCOUNT FROM EW_RIVER_WARNING_MESSAGE GROUP BY WARN_ID) B ON B.WARN_ID = A.WARNID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, COUNT(0) WCOUNT, SUM(SCOUNT) SCOUNT FROM (
            SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND B.STADTP IN
            ('1', '2') AND LEFT(ADCD, 6) = '220581'
            ) A
            LEFT JOIN (SELECT WARN_ID, 1 SCOUNT FROM EW_RIVER_WARNING_MESSAGE GROUP BY WARN_ID) B ON B.WARN_ID = A.WARNID
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <select id="getRsvrSubWarnCountList" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummary">
        SELECT A.ADCD, A.ADNM,B.SORTNO, ISNULL(C.WCOUNT, 0) WCOUNT, ISNULL(C.SCOUNT, 0) SCOUNT FROM MDT_ADCDINFO_B A
        LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
        LEFT JOIN (SELECT T.ADCD, SUM(T.WCOUNT) WCOUNT, SUM(T.SCOUNT) SCOUNT FROM (
        SELECT LEFT(LEFT(A.ADCD,
        <choose>
            <when test="map.level >= 6">#{map.level}</when>
            <otherwise>#{map.level} + 2</otherwise>
        </choose>
        ) + '000000000000000', 15) ADCD, 1 WCOUNT, SCOUNT FROM (
        SELECT WARN_ID WARNID, C.ADCD, WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
        LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
        WHERE C.STADTP IN ('1', '2', '4') AND H.ENG_SCAL IN ('1', '2', '3', '4', '5')
        ) A
        LEFT JOIN (SELECT WARN_ID, 1 SCOUNT FROM EW_RSVR_WARNING_MESSAGE GROUP BY WARN_ID) B ON B.WARN_ID = A.WARNID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>) T GROUP BY T.ADCD
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD, COUNT(0) WCOUNT, SUM(SCOUNT) SCOUNT FROM (
            SELECT WARN_ID WARNID, C.ADCD, WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
            LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
            LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
            LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
            LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
            LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
            LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
            WHERE C.STADTP IN ('1', '2', '4') AND H.ENG_SCAL IN ('1', '2', '3', '4', '5') AND LEFT(C.ADCD, 6) = '220581'
            ) A
            LEFT JOIN (SELECT WARN_ID, 1 SCOUNT FROM EW_RSVR_WARNING_MESSAGE GROUP BY WARN_ID) B ON B.WARN_ID = A.WARNID
            WHERE A.WTM >= #{map.stm}
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
        </if>
        ) C ON C.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.level >= 6">
                    A.ADCD = #{map.adcd}
                </when>
                <otherwise>
                    A.PADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY B.SORTNO ASC
    </select>
    <!--    <select id="getWarnCountList" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnCount">-->
    <!--        SELECT TM, COUNT(WARNID) WCOUNT FROM (-->
    <!--        SELECT CAST((-->
    <!--        <choose>-->
    <!--            <when test='"d".equalsIgnoreCase(map.mode)'>-->
    <!--                CONVERT(VARCHAR(11), WTM, 120) + '00:00:00'-->
    <!--            </when>-->
    <!--            <otherwise>-->
    <!--                CONVERT(VARCHAR(13), WTM, 120) + ':00:00'-->
    <!--            </otherwise>-->
    <!--        </choose>-->
    <!--        ) AS DATETIME) TM, WARNID-->
    <!--        FROM (SELECT WARNID, ADCD, WARNSTM WTM FROM WARNRECORD_R WHERE WARNTYPEID = 10-->
    <!--        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD-->
    <!--        UNION ALL SELECT WARNID, ADCD, WTM FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD) A-->
    <!--        WHERE WTM >= #{map.stm}-->
    <!--        <if test="map.etm != null">-->
    <!--            AND WTM &lt; #{map.etm}-->
    <!--        </if>-->
    <!--        AND LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})-->
    <!--        <if test="map.level == '4'.toString()">-->
    <!--            AND LEFT(ADCD, 6) NOT IN ('220581')-->
    <!--        </if>) T-->
    <!--        GROUP BY TM-->
    <!--        ORDER BY TM ASC-->
    <!--    </select>-->
    <select id="getWarnCountList" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnCount">
        SELECT TM, COUNT(WARNID) WCOUNT FROM (
        SELECT CAST((
        <choose>
            <when test='"d".equalsIgnoreCase(map.mode)'>
                CONVERT(VARCHAR(11), WTM, 120) + '00:00:00'
            </when>
            <otherwise>
                CONVERT(VARCHAR(13), WTM, 120) + ':00:00'
            </otherwise>
        </choose>
        ) AS DATETIME) TM, WARNID
        FROM (SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD WHERE WARN_TYPE_ID = 10
        UNION ALL SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_RIVER_WARNING_RECORD A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND
        B.STADTP IN ('1', '2')
        UNION ALL SELECT WARN_ID WARNID, C.ADCD, WARN_TIME WTM FROM EW_RSVR_WARNING_RECORD A
        LEFT JOIN BSN_STADTP_B C ON C.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 6) + '000000000'
        LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
        WHERE C.STADTP IN ('1', '2', '4') AND H.ENG_SCAL IN ('1', '2', '3', '4', '5')) A
        WHERE WTM >= #{map.stm}
        <if test="map.etm != null">
            AND WTM &lt; #{map.etm}
        </if>
        AND LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>) T
        GROUP BY TM
        ORDER BY TM ASC
    </select>

    <select id="getShWarnList" resultType="com.huitu.cloud.api.ewci.warn.entity.ShWarnInfo">
        SELECT A.WARNID, '1' [TYPE], D.ADCD XADCD, D.ADNM XADNM, A.ADCD, C.ADNM, A.WARNGRADEID, E.WARNGRADENM,
        A.WARNSTATUSID, F.WARNSTATUSNM, A.WARNSTM, A.WARNNM, A.WARNDESC, A.ISHANDMADE, A.LGTD, A.LTTD
        FROM WARNRECORD_R A
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = LEFT(A.ADCD, 6) + '000000000'
        LEFT JOIN WARNINGGRADE_B E ON E.WARNGRADEID = A.WARNGRADEID AND E.WARNGRADETYPE = 'E'
        LEFT JOIN WARNINGSTATUS_B F ON F.WARNSTATUSID = A.WARNSTATUSID
        WHERE A.WARNTYPEID = 10 AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        AND A.WARNSTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WARNSTM &lt; #{map.etm}
        </if>
        ORDER BY A.WARNSTM DESC
    </select>
    <select id="getRiverWarnList" resultType="com.huitu.cloud.api.ewci.warn.entity.RiverRiskWarnInfo">
        SELECT A.WARNID, '2' [TYPE], C.ADCD XADCD, C.ADNM XADNM, B.ADCD, B.ADNM, A.STCD, B.STNM, B.RVNM, A.WTM, A.Z,
        A.WGRD, A.WIDX, A.EWIDX, A.[STATE], B.PLGTD LGTD, B.PLTTD LTTD FROM BSN_RIVER_RISKWARN_R A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = LEFT(B.ADCD, 6) + '000000000'
        WHERE LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        AND A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        ORDER BY A.WTM DESC, A.WGRD ASC
    </select>
    <select id="getRsvrWarnList" resultType="com.huitu.cloud.api.ewci.warn.entity.RsvrRiskWarnInfo">
        SELECT A.WARNID, '3' [TYPE], C.ADCD XADCD, C.ADNM XADNM, B.ADCD, B.ADNM, A.STCD, B.STNM, G.OBJCD RES_CODE,
        H.RES_NAME, H.ENG_SCAL, A.WTM, A.RZ, A.WGRD, A.WIDX, A.EWIDX, A.[STATE], B.PLGTD LGTD, B.PLTTD LTTD
        FROM BSN_RSVR_RISKWARN_R A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = LEFT(B.ADCD, 6) + '000000000'
        LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
        WHERE LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        AND A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        AND H.RES_CODE IS NOT NULL
        ORDER BY A.WTM DESC, A.WGRD ASC, H.ENG_SCAL ASC
    </select>
    <select id="getWarnPCount" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnNotifyCount">
        SELECT I.WARNID, COUNT(0) PCOUNT
        FROM MESSAGESEND_R S, MESSAGEINFO_R I
        WHERE I.MSGID = S.MSGID AND I.ADCD = S.ADCD
        <foreach collection="warnIds" item="warnId" open="AND I.WARNID IN (" close=")" separator=",">
            #{warnId}
        </foreach>
        GROUP BY I.WARNID
    </select>
    <select id="getWarnFCount" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnNotifyCount">
        SELECT WARN_ID WARNID, COUNT(0) FCOUNT FROM WARN_CALL_FEEDBACK
        WHERE [STATUS] = '1'
        <foreach collection="warnIds" item="warnId" open="AND WARN_ID IN (" close=")" separator=",">
            #{warnId}
        </foreach>
        GROUP BY WARN_ID
    </select>
    <select id="getWarnSummarySubitem" resultType="com.huitu.cloud.api.ewci.warn.entity.AllWarnSummarySubitem">
        SELECT ISNULL(SUM(CASE WHEN A.TP = '1' THEN 1 ELSE 0 END), 0) WCOUNT1,
        ISNULL(SUM(CASE WHEN A.TP = '2' THEN 1 ELSE 0 END), 0) WCOUNT2,
        ISNULL(SUM(CASE WHEN A.TP = '3' THEN 1 ELSE 0 END), 0) WCOUNT3,
        ISNULL(SUM(CASE WHEN A.TP = '1' THEN ISNULL(PCOUNT, 0) ELSE 0 END), 0) PCOUNT1,
        ISNULL(SUM(CASE WHEN A.TP = '2' THEN ISNULL(PCOUNT, 0) ELSE 0 END), 0) PCOUNT2,
        ISNULL(SUM(CASE WHEN A.TP = '3' THEN ISNULL(PCOUNT, 0) ELSE 0 END), 0) PCOUNT3,
        ISNULL(SUM(CASE WHEN A.TP = '1' THEN YSTATUS ELSE 0 END), 0) FCOUNT1,
        ISNULL(SUM(CASE WHEN A.TP = '2' THEN YSTATUS ELSE 0 END), 0) FCOUNT2,
        ISNULL(SUM(CASE WHEN A.TP = '3' THEN YSTATUS ELSE 0 END), 0) FCOUNT3
        FROM (
        SELECT WARNID, ADCD, WARNSTM WTM, '1' TP FROM WARNRECORD_R WHERE WARNTYPEID = 10
        UNION ALL
        SELECT WARNID, ADCD, WTM, '3' TP FROM BSN_RIVER_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND B.STADTP IN ('1', '2', '4')
        UNION ALL
        SELECT WARNID, ADCD, WTM, '2' TP FROM BSN_RSVR_RISKWARN_R A, BSN_STBPRP_V B WHERE B.STCD = A.STCD AND B.STADTP IN ('1', '2')
        ) A
        LEFT JOIN (SELECT WARNID, COUNT(0) PCOUNT FROM MESSAGESEND_R S, MESSAGEINFO_R I WHERE I.MSGID = S.MSGID AND I.ADCD = S.ADCD GROUP BY
        WARNID) C ON C.WARNID = A.WARNID
        LEFT JOIN (SELECT WARN_ID, SUM(CASE WHEN STATUS = '1' THEN 1 ELSE 0 END) YSTATUS FROM WARN_CALL_FEEDBACK GROUP BY WARN_ID) B ON
        B.WARN_ID = A.WARNID
        WHERE A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        AND LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
    </select>
</mapper>
