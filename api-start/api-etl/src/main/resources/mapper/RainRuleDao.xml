<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.datamanager.rule.mapper.RainRuleDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.datamanager.rule.entity.BsnRainruleb">
        <id column="STCD" property="stcd" />
        <result column="MNCNT" property="inv" />
        <result column="SNV" property="snv" />
        <result column="CTPM" property="ctpm5" />
        <result column="NT" property="nt" />
        <result column="MODITIME" property="moditime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        STCD, INV, SNV, CTPM5, NT, MODITIME
    </sql>
    <insert id="batchInsertAll" parameterType="com.huitu.cloud.api.datamanager.rule.entity.RainruleEx">
        insert into BSN_RAINRULE_B(STCD, INV, SNV, CTPM5, NT, MODITIME)
        select a.STCD,#{entrty.inv},#{entrty.snv},#{entrty.ctpm5},#{entrty.nt},getdate()
        from ST_STBPRP_B a,ST_STSMTASK_B b
        where a.STCD=b.STCD and b.PFL='1'
        <if test="entrty.adcd !=null and entrty.adcd !=''">
            AND left(addvcd,#{level}) = #{entrty.adcd}
        </if>
    </insert>
    <delete id="deleteAll">
        delete from BSN_RAINRULE_B
        where stcd in (select stcd from ST_STBPRP_B where  1=1
        <if test="ad !=null and ad !=''">
            AND left(addvcd,#{level}) = #{ad}
        </if>
        <!-- 吉林市通化市特殊处理 -->
        <if test="level != null and level == '4'.toString() ">
            and addvcd not in ('220284','220282','220281','220283','220221','220524','220521','220523','220582','220581')
        </if>
       )
    </delete>
    <select id="getRainRule" resultType="com.huitu.cloud.api.datamanager.rule.entity.RainRuleVo">
        select a.STCD, STNM,INV, SNV, CTPM5, NT, a.MODITIME
		from BSN_RAINRULE_B a,ST_STBPRP_B b where a.stcd=b.stcd
		<if test="stnm !=null and stnm !=''">
            AND CHARINDEX(#{stnm},STNM)>0
        </if>
        <if test="ad !=null and ad !=''">
            AND left(addvcd,#{level}) = #{ad}
        </if>
        <!-- 吉林市通化市特殊处理 -->
        <if test="level != null and level == '4'.toString() ">
            and addvcd not in ('220284','220282','220281','220283','220221','220524','220521','220523','220582','220581')
        </if>
		order by a.MODITIME desc
    </select>


</mapper>
