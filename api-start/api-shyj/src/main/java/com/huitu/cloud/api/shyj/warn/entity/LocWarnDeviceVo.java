package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 现地预警设备状态表 前端控制器
 *
 * <AUTHOR>
 * @since 2019-09-16
 */
@Data
@ApiModel(value="LocWarnDeviceVo对象", description="现地预警设备状态表")
public class LocWarnDeviceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    private String warnid;

    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "数据时间")
    private Date datatime;

    @ApiModelProperty(value = "入户报警器编号")
    private Integer rwid;

    @ApiModelProperty(value = "入户报警器类型")
    private Integer rwtp;

    @ApiModelProperty(value = "雨量警报状态")
    private Integer rws1;

    @ApiModelProperty(value = "水位警报状态")
    private Integer rws2;

    @ApiModelProperty(value = "功放开关")
    private Integer rws3;

    @ApiModelProperty(value = "一键报警状态")
    private Integer rws4;

    @ApiModelProperty(value = "平台出发报警")
    private Integer rws5;

}
