package com.huitu.cloud.api.ewci.monitor.service.impl;

import com.huitu.cloud.api.ewci.monitor.entity.IaCDanad;
import com.huitu.cloud.api.ewci.monitor.mapper.IaCDanadDao;
import com.huitu.cloud.api.ewci.monitor.service.DanadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 危险区基本情况调查成果汇总服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class DanadServiceImpl implements DanadService {

    private IaCDanadDao baseDao;

    @Autowired
    public void setBaseDao(IaCDanadDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public List<IaCDanad> getDanadList(String type, String adcd) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        params.put("adcd", StringUtils.rightPad(adcd, 15, "0"));
        return baseDao.getDanadList(params);
    }
}
