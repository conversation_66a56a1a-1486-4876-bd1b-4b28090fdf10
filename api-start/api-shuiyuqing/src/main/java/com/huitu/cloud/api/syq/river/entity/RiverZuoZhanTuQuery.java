package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
/**
 * <AUTHOR>
 * @since 2024-05-15
 */

/**
 * <AUTHOR>
 */

@ApiModel(value="RiverZuoZhanTuVo")
public class RiverZuoZhanTuQuery {
    @ApiModelProperty(value = "开始时间")
    private String stm;

    @ApiModelProperty(value = "结束时间")
    private String etm;

    @ApiModelProperty(value = "测站编码")
    private String stcd;

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Map<String, Object> toQueryParam() {
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd 08:00:00");
        LocalDateTime now = LocalDateTime.now();
        Map<String, Object> params = new HashMap<>();
        params.put("stcd", getStcd());
        if (StringUtils.isBlank(getStm())) {
            params.put("stm", now.minus(3, ChronoUnit.DAYS).format(sdf));
        } else {
            params.put("stm", getStm());
        }
        if (StringUtils.isBlank(getEtm())) {
            params.put("etm", now.format(sdf));
        } else {
            params.put("etm", getEtm());
        }
        return params;
    }
}
