package com.huitu.cloud.api.hsfxt.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 风险图分析成果断面高程信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-28
 */
@TableName("KB_RVSANALY_SECELEV_G")
@ApiModel(value="KbRvsanalySecelevG对象", description="风险图分析成果断面高程信息表")
public class KbRvsanalySecelevG extends Model<KbRvsanalySecelevG> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "成果编号")
    @TableId(value = "CID", type = IdType.NONE)
    private String cid;

    @ApiModelProperty(value = "断面编号")
    @TableField("RSCD")
    private Double rscd;

    @ApiModelProperty(value = "点序号")
    @TableField("PTNO")
    private Double ptno;

    @ApiModelProperty(value = "间距")
    @TableField("INTDIST")
    private Double intdist;

    @ApiModelProperty(value = "始距")
    @TableField("STADIST")
    private Double stadist;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private Double lttd;

    @ApiModelProperty(value = "高程")
    @TableField("AMEL")
    private Double amel;

    @ApiModelProperty(value = "备注")
    @TableField("RM")
    private String rm;


    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Double getRscd() {
        return rscd;
    }

    public void setRscd(Double rscd) {
        this.rscd = rscd;
    }

    public Double getPtno() {
        return ptno;
    }

    public void setPtno(Double ptno) {
        this.ptno = ptno;
    }

    public Double getIntdist() {
        return intdist;
    }

    public void setIntdist(Double intdist) {
        this.intdist = intdist;
    }

    public Double getStadist() {
        return stadist;
    }

    public void setStadist(Double stadist) {
        this.stadist = stadist;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public Double getAmel() {
        return amel;
    }

    public void setAmel(Double amel) {
        this.amel = amel;
    }

    public String getRm() {
        return rm;
    }

    public void setRm(String rm) {
        this.rm = rm;
    }

    @Override
    protected Serializable pkVal() {
        return this.cid;
    }

    @Override
    public String toString() {
        return "KbRvsanalySecelevG{" +
        "cid=" + cid +
        ", rscd=" + rscd +
        ", ptno=" + ptno +
        ", intdist=" + intdist +
        ", stadist=" + stadist +
        ", lgtd=" + lgtd +
        ", lttd=" + lttd +
        ", amel=" + amel +
        ", rm=" + rm +
        "}";
    }
}
