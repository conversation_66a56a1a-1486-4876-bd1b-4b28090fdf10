package com.huitu.cloud.api.hsfxt.river.controler;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.hsfxt.river.entity.JiangHeXiangQing;
import com.huitu.cloud.api.hsfxt.river.entity.RiverTree;
import com.huitu.cloud.api.hsfxt.river.entity.SheHuiJingJi;
import com.huitu.cloud.api.hsfxt.river.entity.TunXiangQing;
import com.huitu.cloud.api.hsfxt.river.service.FxtRiverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 风险图-河流 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */

@RestController
@Api(tags = "风险图-江河接口")
@RequestMapping("/api/hsfxt/river")
public class FxtRiverResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "6a9d24a5-2ed3-4c4b-b2d5-b41e3bbcdcac";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    FxtRiverService riverService;

    @ApiOperation(value = "江河列表查询", notes = "江河列表查询")
    @GetMapping(value = "get-river-tree-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合（不要洪水量级）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "最小风险政区单位", dataType = "String"),
            @ApiImplicitParam(name = "hl", value = "洪水量级", dataType = "String"),
            @ApiImplicitParam(name = "bzqid", value = "编制区编码", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RiverTree>>> getRiverTreeInfo(@RequestParam String key, @RequestParam String name,@RequestParam String hl,@RequestParam String bzqid) throws Exception {
        List<RiverTree> list = riverService.getRiverTreeInfo(key, name, hl, bzqid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "江河社会经济统计查询", notes = "江河社会经济统计查询")
    @GetMapping(value = "get-river-shjj-tree-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<SheHuiJingJi>>> getRiverShjjTreeInfo(@RequestParam String key) throws Exception {
        List<SheHuiJingJi> list = riverService.getRiverShjjTreeInfo(key);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "江河列表详情查询", notes = "江河列表详情查询")
    @GetMapping(value = "get-river-shjj-detail-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bzqid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<JiangHeXiangQing>> getRiverShjjDetailInfo(@RequestParam String key,@RequestParam String adcd,@RequestParam String bzqid) throws Exception {
        JiangHeXiangQing result = riverService.getRiverShjjDetailInfo(key,adcd,bzqid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "江河列表（屯）详情查询", notes = "江河列表（屯）详情查询")
    @GetMapping(value = "get-tun-shjj-detail-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合（不要洪水量级）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "hl", value = "洪水量级", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bzqid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<TunXiangQing>> getTunShjjDetailInfo(@RequestParam String key,@RequestParam String hl,@RequestParam String adcd,@RequestParam String bzqid) throws Exception {
        TunXiangQing result = riverService.getTunShjjDetailInfo(key,hl,adcd,bzqid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

}
