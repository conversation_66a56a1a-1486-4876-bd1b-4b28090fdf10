package com.huitu.cloud.api.shyj.evaluation.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import com.huitu.cloud.api.shyj.evaluation.mapper.AnalysisDao;
import com.huitu.cloud.api.shyj.evaluation.service.AnalysisService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 分析评价  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@Service
public class AnalysisServiceImpl implements AnalysisService {
    @Autowired
    private AnalysisDao analysisDao;

    @Override
    public IPage<IaDesntb> getDesntbList(String wsnm,String adcd, String wscd,String type, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);

        Map<String, Object> param = new HashMap<>();
        param.put("wsnm", wsnm);
        param.put("wscd", wscd);
        param.put("adcd", adcd);
        param.put("type", type);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        IPage<IaDesntb> result=analysisDao.getDesntbList(page,param);
        return result;
    }

    @Override
    public Map<String,Object> getDesntbSum(String wsnm,String adcd, String wscd,String type) {
        Map<String, Object> param = new HashMap<>();
        param.put("wsnm", wsnm);
        param.put("wscd", wscd);
        param.put("adcd", adcd);
        param.put("type", type);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        Map<String,Object> sum = new HashMap<>();
        sum.put("sum", analysisDao.getDesntbSum(param));
//        Map<String,Object> result=analysisDao.getDesntbSum(param);
        return sum;
    }

    @Override
    public void exportDesntbList(String wsnm,String adcd,String wscd, String type) {
        IPage<IaDesntb> result=getDesntbList(wsnm,adcd,wscd,type,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "设计暴雨信息");
    }

    @Override
    public IPage<IaHlsjtb> getHlsjtb(String wsnm, String radio, String adcd, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("wsnm", wsnm);
        param.put("adLevl", adLevl);
        param.put("radio", radio);
        param.put("adcd", adcd.substring(0, adLevl));
        IPage<IaHlsjtb> result=analysisDao.getHlsjtb(page,param);
        return result;
    }
    @Override
    public Map<String,Object> getHlsjtbSum(String wsnm, String radio, String adcd) {
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("wsnm", wsnm);
        param.put("adLevl", adLevl);
        param.put("radio", radio);
        param.put("adcd", adcd.substring(0, adLevl));
        Map<String,Object> sum = new HashMap<>();
        sum.put("sum", analysisDao.getHlsjtbSum(param));
        return sum;
    }

    @Override
    public void exportHlsjtb(String wsnm, String radio, String adcd) {
        IPage<IaHlsjtb> result=getHlsjtb(wsnm,radio,adcd,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "小流域汇流时间设计暴雨信息");
    }

    @Override
    public IPage<IaSdtdtb> getSdtdtb(String adcd, String wsnm,String wscd,String radio, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("wsnm", wsnm);
        param.put("wscd", wscd);
        param.put("radio", radio);
        IPage<IaSdtdtb> result=analysisDao.getSdtdtb(page,param);
        return result;
    }
    @Override
    public Map<String,Object> getSdtdtbSum(String adcd, String wsnm,String wscd,String radio) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("wsnm", wsnm);
        param.put("wscd", wscd);
        param.put("radio", radio);
        Map<String,Object> sum = new HashMap<>();
        sum.put("districtSum", analysisDao.getSdtdtbSum(param));
        sum.put("basinSum", analysisDao.getSdtdtbbasinSum(param));
        return sum;
    }

    @Override
    public void exportSdtdtb(String adcd, String wsnm,String wscd,String radio) {
        IPage<IaSdtdtb> result=getSdtdtb(adcd,wsnm,wscd,radio,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "控制断面设计洪水信息");
    }

    @Override
    public IPage<IaSwllrktb> getSwllrktb(String adcd, String wsnm,  String wscd,String type,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("wsnm", wsnm);
        param.put("adcd", adcd);
        param.put("type", type);
        param.put("wscd", wscd);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        IPage<IaSwllrktb> result=analysisDao.getSwllrktb(page,param);
        return result;
    }
    @Override
    public Map<String,Object> getSwllrktbSum(String adcd, String wsnm,  String wscd,String type) {
        Map<String, Object> param = new HashMap<>();
        param.put("wsnm", wsnm);
        param.put("adcd", adcd);
        param.put("type", type);
        param.put("wscd", wscd);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        Map<String,Object> sum = new HashMap<>();
        sum.put("districtSum", analysisDao.getDistrictSum(param));
        sum.put("basinSum", analysisDao.getBasinSum(param));
        return sum;
    }

    @Override
    public void exportSwllrktb(String adcd, String wsnm, String wscd,String type) {
        IPage<IaSwllrktb> result=getSwllrktb(adcd, wsnm, wscd,type,1, -1);
        ExcelExportUtil.execute(result.getRecords(), "控制断面水位流量人口关系信息");
    }

    @Override
    public IPage<IaNowfhtb> getNowfhtb(String adcd, String wsnm,String wscd,String type, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("wsnm", wsnm);
        param.put("type", type);
        param.put("wscd", wscd);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        IPage<IaNowfhtb> result=analysisDao.getNowfhtb(page,param);
        return result;
    }
    @Override
    public Map<String,Object> getNowfhtbSum(String adcd, String wsnm,String wscd,String type) {
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("wsnm", wsnm);
        param.put("type", type);
        param.put("wscd", wscd);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        Map<String,Object> sum = new HashMap<>();
        sum.put("districtSum", analysisDao.getNowfhtbDistrictSum(param));
        sum.put("basinSum", analysisDao.getNowfhtbBasinSum(param));
        return sum;
    }

    @Override
    public void exportNowfhtb(String adcd, String wsnm,String wscd,String type) {
        IPage<IaNowfhtb> result=getNowfhtb(adcd, wsnm, wscd,type,1, -1);
        ExcelExportUtil.execute(result.getRecords(), "防洪现状评价信息");
    }

    @Override
    public IPage<IaJygstb> getJygstb(String adcd, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        IPage<IaJygstb> result=analysisDao.getJygstb(page,param);
        return result;
    }

    @Override
    public void exportJygstb(String adcd) {
        IPage<IaJygstb> result=getJygstb(adcd,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "临界雨量经验估值法成果信息");
    }

    @Override
    public IPage<IaYjcjtb> getYjcjtb(String adcd, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        IPage<IaYjcjtb> result=analysisDao.getYjcjtb(page,param);
        return result;
    }

    @Override
    public void exportYjcjtb(String adcd) {
        IPage<IaYjcjtb> result=getYjcjtb(adcd,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "临界雨量降雨分析法成果信息");
    }

    @Override
    public IPage<IaFxcgtb> getFxcgtb(String adcd,String type, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("type", type);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        IPage<IaFxcgtb> result=analysisDao.getFxcgtb(page,param);
        return result;
    }
    @Override
    public Map<String,Object> getFxcgtbSum(String adcd,String type) {
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("type", type);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        Map<String,Object> sum = new HashMap<>();
        sum.put("districtSum", analysisDao.getFxcgtbDistrictSum(param));
        return sum;
    }

    @Override
    public void exportFxcgtb(String adcd,String type) {
        IPage<IaFxcgtb> result=getFxcgtb(adcd,type,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "临界雨量模型分析法成果信息");
    }

    @Override
    public IPage<IaDfwrule> getDfwrule(String adcd, String wsnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("wsnm", wsnm);
        IPage<IaDfwrule> result=analysisDao.getDfwrule(page,param);
        return result;
    }

    @Override
    public Map<String,Object> getDfwruleSum(String adcd, String wsnm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("wsnm", wsnm);
        Map<String,Object> sum = new HashMap<>();
        sum.put("districtSum", analysisDao.getDfwruleDistrictSum(param));
        return sum;
    }

    @Override
    public void exportDfwrule(String adcd, String wsnm) {
        IPage<IaDfwrule> result=getDfwrule(adcd,wsnm,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "预警指标时段雨量信息");
    }

    @Override
    public IPage<IaYjicr> getYjicr(String adcd, String radio, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("radio", radio);
        IPage<IaYjicr> result=analysisDao.getYjicr(page,param);
        return result;
    }
    @Override
    public Map<String,Object> getYjicrSum(String adcd, String radio) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("radio", radio);
        Map<String,Object> sum = new HashMap<>();
        sum.put("districtSum", analysisDao.getYjicrSum(param));
        return sum;
    }
    @Override
    public void exportYjicr(String adcd,String radio) {
        IPage<IaYjicr> result=getYjicr(adcd,radio,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "预警指标综合雨量信息");
    }

    @Override
    public IPage<IaWlwrule> getWlwrule(String adcd, String wsnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("wsnm", wsnm);
        IPage<IaWlwrule> result=analysisDao.getWlwrule(page,param);
        return result;
    }
    @Override
    public  Map<String,Object> getWlwruleSum(String adcd, String wsnm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("wsnm", wsnm);
        Map<String,Object> sum = new HashMap<>();
        sum.put("districtSum", analysisDao.getWlwruleSum(param));
        return sum;
    }
    @Override
    public void exportWlwrule(String adcd, String wsnm) {
        IPage<IaWlwrule> result=getWlwrule(adcd,wsnm,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "预警指标水位信息");
    }

    @Override
    public IPage<BnsIaUnitdp> getUnitdp(String adcd, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("level", AdcdUtil.getAdLevel(adcd));
        IPage<BnsIaUnitdp> result=analysisDao.getUnitdp(page,param);
        return result;
    }

    @Override
    public void exportUnitdp(String adcd) {
        IPage<BnsIaUnitdp> result=getUnitdp(adcd,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "计算单元(防灾对象)信息");
    }
    @Override
    public IPage<BnsIaUnitws> getUnitws(String wsnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("wsnm", wsnm);
        IPage<BnsIaUnitws> result=analysisDao.getUnitws(page,param);
        return result;
    }

    @Override
    public void exportUnitws(String wsnm) {
        IPage<BnsIaUnitws> result=getUnitws(wsnm,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "计算单元小流域信息");
    }
    /**
     * 查询重点城(集)镇调查评价汇总
     *
     * @param adcd     政区编码
     * @param zdadnm   成集镇名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    @Override
    public IPage<DcjzSummary> getCjzSummary(String adcd,String zdadnm,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        IPage<DcjzSummary> result = analysisDao.getCjzSummary(page, param);
        return result;
    }

    @Override
    public void exportCjzSummary(String adcd,String zdadnm) {
        IPage<DcjzSummary> result=getCjzSummary(adcd,zdadnm,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "重点城(集)镇调查评价汇总");
    }

    @Override
    public IPage<DcjzReservoitSs> getCjzReservoit(String adcd,String zdadnm,String rsName,String engGrad,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("rsName", rsName);
        param.put("engGrad", engGrad);
        IPage<DcjzReservoitSs> result = analysisDao.getCjzReservoit(page, param);
        return result;
    }

    @Override
    public void exportCjzReservoit(String adcd,String zdadnm,String rsName,String engGrad) {
        IPage<DcjzReservoitSs> result=getCjzReservoit(adcd,zdadnm,rsName,engGrad,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "涉水工程-水库信息");
    }

    @Override
    public IPage<DcjzSluiceSs> getCjzSluice(String adcd,String zdadnm,String gateName,String gateType,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("gateName", gateName);
        param.put("gateType", gateType);
        IPage<DcjzSluiceSs> result = analysisDao.getCjzSluice(page, param);
        return result;
    }

    @Override
    public void exportCjzSluice(String adcd,String zdadnm,String gateName,String gateType) {
        IPage<DcjzSluiceSs> result=getCjzSluice(adcd,zdadnm,gateName,gateType,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "涉水工程-水闸信息");
    }

    @Override
    public IPage<DcjzDfDikeSs> getDfDike(String adcd,String zdadnm,String dikeName,String dikeGrad,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("dikeName", dikeName);
        param.put("dikeGrad", dikeGrad);
        IPage<DcjzDfDikeSs> result = analysisDao.getDfDike(page, param);
        return result;
    }

    @Override
    public void exportCjzDfDike(String adcd,String zdadnm,String dikeGrad,String dikeType) {
        IPage<DcjzDfDikeSs> result=getDfDike(adcd,zdadnm,dikeGrad,dikeType,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "涉水工程-堤防信息");
    }

    @Override
    public IPage<DcjzLhRCSs> getLhZdjz(String adcd,String zdadnm,String culname,String type,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("culname", culname);
        param.put("type", type);
        IPage<DcjzLhRCSs> result = analysisDao.getLhZdjz(page, param);
        return result;
    }

    @Override
    public void exportCjzLhZdjzist(String adcd,String zdadnm,String culname,String type) {
        IPage<DcjzLhRCSs> result=getLhZdjz(adcd,zdadnm,culname,type,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "涉水工程-路涵信息");
    }

    @Override
    public IPage<DcjzQlBridgeSs> getBridgeQl(String adcd,String zdadnm,String brname,String type,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("brname", brname);
        param.put("type", type);
        IPage<DcjzQlBridgeSs> result = analysisDao.getBridgeQl(page, param);
        return result;
    }

    @Override
    public void exportCjzBridgeQl(String adcd,String zdadnm,String brname,String type) {
        IPage<DcjzQlBridgeSs> result=getBridgeQl(adcd,zdadnm,brname,type,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "涉水工程-桥梁信息");
    }

    @Override
    public IPage<DcjzTbPondSs> getPondTb(String adcd,String zdadnm,String damname,String mt,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("damname", damname);
        param.put("mt", mt);
        IPage<DcjzTbPondSs> result = analysisDao.getPondTb(page, param);
        return result;
    }

    @Override
    public void exportCjzPondTb(String adcd,String zdadnm,String damname,String mt) {
        IPage<DcjzTbPondSs> result=getPondTb(adcd,zdadnm,damname,mt,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "涉水工程-塘坝信息");
    }

    @Override
    public IPage<DcjzStationSs> getZdjczCz(String adcd,String zdadnm,String stnm,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("stnm", stnm);
        IPage<DcjzStationSs> result = analysisDao.getZdjczCz(page, param);
        for (int i = 0; i < result.getRecords().size(); i++) {
            String stn = result.getRecords().get(i).getEsstym().replaceAll("(.{4})", "$1-");
            result.getRecords().get(i).setEsstym(stn);
        }
        return result;
    }

    @Override
    public void exportZdjczCz(String adcd,String zdadnm,String stnm) {
        IPage<DcjzStationSs> result=getZdjczCz(adcd,zdadnm,stnm,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "设备信息-自动检测站");
    }


    @Override
    public IPage<DcjzRadioGbSs> getRadioGb(String adcd,String zdadnm,String address,Integer type,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("address", address);
        param.put("type", type);
        IPage<DcjzRadioGbSs> result = analysisDao.getRadioGb(page, param);
        return result;
    }

    @Override
    public void exportRadioGb(String adcd,String zdadnm,String address,Integer type) {
        IPage<DcjzRadioGbSs> result=getRadioGb(adcd,zdadnm,address,type,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "设备信息-无线预警广播");
    }

    @Override
    public IPage<DcjzSimpleJyylSs> getSimpleJyyl(String adcd,String zdadnm,String address,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("address", address);
        IPage<DcjzSimpleJyylSs> result = analysisDao.getSimpleJyyl(page, param);
        return result;
    }

    @Override
    public void exportSimpleJyyl(String adcd,String zdadnm,String address) {
        IPage<DcjzSimpleJyylSs> result=getSimpleJyyl(adcd,zdadnm,address,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "设备信息-简易雨量站");
    }

    @Override
    public IPage<DcjzWLJysw> getWLJysw(String adcd,String zdadnm,String address,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        param.put("address", address);
        IPage<DcjzWLJysw> result = analysisDao.getWLJysw(page, param);
        return result;
    }

    @Override
    public void exportWLJysw(String adcd,String zdadnm,String address) {
        IPage<DcjzWLJysw> result=getWLJysw(adcd,zdadnm,address,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "设备信息-简易水位站");
    }

    @Override
    public IPage<DcjzHdzdm> getHdzdm(String adcd,String zdadnm,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        IPage<DcjzHdzdm> result = analysisDao.getHdzdm(page, param);
        return result;
    }

    @Override
    public void exportHdzdm(String adcd,String zdadnm) {
        IPage<DcjzHdzdm> result=getHdzdm(adcd,zdadnm,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "河道纵断面信息");
    }

    @Override
    public IPage<DcjzHdhdm> getCjzHdhdm(String adcd,String zdadnm,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("zdadnm", zdadnm);
        IPage<DcjzHdhdm> result = analysisDao.getCjzHdhdm(page, param);
        return result;
    }

    @Override
    public void exportHdhdm(String adcd,String zdadnm) {
        IPage<DcjzHdhdm> result=getCjzHdhdm(adcd,zdadnm,1,-1);
        ExcelExportUtil.execute(result.getRecords(), "河道横断面信息");
    }

    @Override
    public List<IaDfwruleVo> getDfwruleByStcd(String stcd) {
        List<IaDfwruleVo> retList = new ArrayList<>();
        if (stcd == null || stcd.equals("")) {
            return retList;
        }
        List<StbprpInfoVo> stcdList = analysisDao.getStcdInfo(stcd);
        stcdList.forEach(item -> {
            int level = AdcdUtil.getAdLevel(item.getAdcd());
            List<IaDfwruleVo> list = analysisDao.getDfwruleByAdcd(item.getAdcd().substring(0, level));
            Map<String, List<IaDfwruleVo>> ruleMap = list.parallelStream().collect(Collectors.groupingBy(item1 -> item1.getAdcd() + "-" + item1.getAdnm() + "-" + item1.getStdt(), Collectors.toList()));
            ruleMap.forEach((k, v) -> {
                String[] temp = k.split("-");
                IaDfwruleVo iaDfwruleVo = new IaDfwruleVo();
                iaDfwruleVo.setAdcd(temp[0]);
                iaDfwruleVo.setAdnm(temp[1]);
                iaDfwruleVo.setStdt(Integer.valueOf(temp[2]));
                IaDfwruleVo vo30 = v.stream().filter(rule -> rule.getWarngradeid() == 5).findAny().get();
                if (vo30 != null) iaDfwruleVo.setDrpt30(vo30.getDrpt());
                IaDfwruleVo vo31 = v.stream().filter(rule -> rule.getWarngradeid() == 4).findAny().get();
                if (vo31 != null) iaDfwruleVo.setDrpt31(vo31.getDrpt());
                retList.add(iaDfwruleVo);
            });

        });
        return retList.stream().sorted(Comparator.comparing(IaDfwruleVo::getAdcd)
                .thenComparing(IaDfwruleVo::getStdt)
        ).collect(Collectors.toList());
    }

}
