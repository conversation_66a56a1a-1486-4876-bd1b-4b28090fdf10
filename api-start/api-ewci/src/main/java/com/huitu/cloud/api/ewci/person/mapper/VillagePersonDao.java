package com.huitu.cloud.api.ewci.person.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 水库淹没范围责任人Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface VillagePersonDao {

    /**
     * 分页获取水库淹没范围责任人列表
     *
     * @param page
     * @param params
     * @return
     */
    IPage<VillagePersonVo> getPageList(IPage<VillagePersonVo> page, @Param("map") Map<String, Object> params);

    /**
     * 水库淹没范围责任人统计
     *
     * @param params
     * @return
     */
    List<VillageSummaryVo> getVillageSummaryList(@Param("map") Map<String, Object> params);

    /**
     * 批量导入水库淹没范围责任人
     *
     * @param batchNo 批号
     * @return 导入失败的责任人信息
     **/
    List<BnsVillagePersonTmp> batchImport(String batchNo);

    /**
     * 根据行政区划代码，顺序删除
     *
     * @param params
     * @return
     */
    int delAdcdAndSno(@Param("map") Map<String, Object> params);

    /**
     * 添加水库淹没范围责任人
     *
     * @param list
     * @return
     */
    int insertList(@Param("list") List<VillagePersonB> list);
}
