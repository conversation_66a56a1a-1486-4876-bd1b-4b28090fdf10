package com.huitu.cloud.api.ewci.person.mapper;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 水库淹没范围责任人Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface VillagePersonTmpDao {

    /**
     * 批量插入数据
     *
     * @param params 参数
     * @return int 受影响的行数
     **/
    int batchInsert(@Param("map") Map<String, Object> params);

}
