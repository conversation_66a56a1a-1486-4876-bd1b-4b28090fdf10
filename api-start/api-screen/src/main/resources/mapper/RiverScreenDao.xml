<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.screen.fhjz.mapper.RiverScreenDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="queryRiverList" resultType="com.huitu.cloud.api.screen.fhjz.entity.RiverVo">
            select * from (
            select t1.RV_CODE,
                    t3.main_sort_no,
                    t1.rv_level,
                   case when t1.RV_NAME = '图们江' then 'TMJ'
                        when t1.RV_NAME = '鸭绿江' then 'YLJ'
                        else t1.RV_NAME end RV_NAME,
			        t2.FLOW_AREA,
			        convert(decimal(18,2),t2.RV_LEN) RV_LEN,
					convert(decimal(18,2),t2.RV_BAS_AREA) RV_BAS_AREA
            from BNS_RIVER t1
            left join ATT_RV_BASE t2 on t1.RV_CODE=t2.RV_CODE
            inner join BSN_RVINFOEX_B t3 on t1.RV_CODE=t3.RV_CODE
            ) t
            <where>
                <if test="rvName!=null and rvName!=''">
                    t.RV_NAME like '%' + #{rvName} + '%'
                </if>
            </where>
            order by ISNULL(t.main_sort_no,9999) asc,t.rv_level asc
    </select>

    <select id="queryRiverDetailList" resultType="com.huitu.cloud.api.screen.fhjz.entity.RiverDetailVo">
        select t1.RV_CODE,
            case when t1.RV_NAME = '图们江' then 'TMJ'
            when t1.RV_NAME = '鸭绿江' then 'YLJ'
            else t1.RV_NAME end RV_NAME,
            t2.FLOW_AREA,
            t2.RV_LEN,
            t2.RV_BAS_AREA,
            t2.RV_SOUR_LOC,
            t2.RV_MOU_LOC,
            t4.BAS_NAME,
            t6.RV_NAME UP_RV_NAME,
            t7.RV_LEN_PRO,
            t7.DIKE_GRAD,
            t7.DIKE_NAME,
            t7.DDZ,
            t7.AEORB,
            t7.AGEABS,
            t7.CFSOWWL,
            t7.CFSOGWL,
            t8.pro_id,
            t9.stnm,
            t9.dtmnm,
            convert(decimal(18,2),t10.OBHTZ) OBHTZ,
            convert(varchar(20),t10.OBHTZTM,23) OBHTZTM,
            convert(decimal(18,2),t10.OBMXQ) OBMXQ,
            convert(varchar(20),t10.OBMXQTM,23) OBMXQTM,
            convert(decimal(18,2),t10.RDKEL) RDKEL,
            convert(decimal(18,2),t10.LDKEL) LDKEL,
            convert(decimal(18,2),t10.WRZ) WRZ,
            convert(decimal(18,2),t10.wrq) wrq,
            convert(decimal(18,2),t10.grz) grz,
            convert(decimal(18,2),t10.grq) grq,
            (select count(1) from REL_DPDS_RV t where t.rv_code=t1.RV_CODE) xgxd_count
        from BNS_RIVER t1
        left join ATT_RV_BASE t2 on t1.RV_CODE=t2.RV_CODE
        left join REL_RV_BAS t3 on t1.RV_CODE=t3.RV_CODE
        left join OBJ_BAS t4 on t3.BAS_CODE=t4.BAS_CODE
        left join REL_RV_RV t5 on t1.RV_CODE=t5.RV_CODE
        left join BNS_RIVER t6 on t5.UP_RV_CODE=t6.RV_CODE
        left join BSN_RVINFOEX_B t7 on t1.RV_CODE=t7.RV_CODE
        left join (select * from bns_rv_pro where PRO_TYPE='3') t8 on t1.RV_CODE=t8.RV_CODE
        left join ST_STBPRP_B t9 on t8.pro_id=t9.stcd
        left join ST_RVFCCH_B t10 on t8.pro_id=t10.stcd
        where 1=1
        <if test="rvName!=null and rvName!=''">
           and t1.RV_NAME like '%'+#{rvName}+'%'
        </if>
        order by ISNULL(t7.main_sort_no,9999) asc,t1.rv_level asc,t1.RV_CODE asc
    </select>

    <select id="riverStationList" resultType="map">
        with new_river as(
        select t2.* from (
        select t.stcd,max(tm) maxtm from ST_RIVER_R t where t.tm &gt;= #{stm} and t.tm  &lt;= #{etm} and t.z is not null group by t.stcd) t1
        inner join ST_RIVER_R t2 on t1.stcd=t2.stcd and t1.maxtm = t2.tm
        )
        select case when t7.rv_code is not null then '1' else '0' end IS_MAIN_RV_ST,
        t3.stcd,t4.stnm,t3.tm,t3.z,t3.WPTN,t5.PLGTD lgtd,t5.PLTTD lttd,
        t6.WRZ,
        case when t3.Z &gt; t6.WRZ then 1 else 0 end ISWRZ
        from new_river t3
        inner join ST_STBPRP_B t4 on t3.stcd = t4.stcd
        left join BSN_STADTP_B t5 on t5.stcd = t4.stcd
        left join ST_RVFCCH_B t6 on t3.stcd = t6.stcd
        left join (select t.RV_CODE,t.pro_id from bns_rv_pro t where t.PRO_TYPE='3' and t.RV_CODE in ('AAB00006','AAE00001','ABD00000','AAB10006','ABA11006','ABA10006','AAB21006','AAB13006','ABD12006','AAE13006','AAE11006','AAB10L06','AAB11B06','AAB11306','AAE11506','AAB13306','AAE11526','AAB11B56','AAB11B66')) t7 on t3.stcd = t7.pro_id
        <where>
            <if test="rvCode!=null and rvCode!=''">
                t1.RV_CODE = #{rvCode}
            </if>
        </where>
    </select>

    <select id="queryRiverStationInfo" resultType="map">
        select t1.stcd,t1.stnm,t2.adcd
        from ST_STBPRP_B t1
        left join BSN_STBPRP_V t2 on t1.stcd=t2.stcd
        where t1.stcd=#{stcd}
    </select>

    <select id="queryRiverInfo" resultType="map">
        select t1.rv_code,
        case when t1.RV_NAME = '图们江' then 'TMJ'
					when t1.RV_NAME = '鸭绿江' then 'YLJ'
					else t1.RV_NAME end rv_name
        from BNS_RIVER t1
        where t1.RV_CODE = #{rvCode}
    </select>

    <select id="queryRsvrTotal" resultType="int">
        select count(1)
        from ATT_RES_BASE t
        where t.eng_scal is not null
    </select>

    <select id="queryDikeTotal" resultType="int">
        select count(1)
        from ATT_DIKE_BASE t1
        inner join BSN_DIKEEX_B t4 on t1.dike_code=t4.dike_code
        where t1.dike_grad is not null
    </select>

    <select id="queryRiverTotal" resultType="int">
        select count(1)
        from BNS_RIVER t1
        left join ATT_RV_BASE t2 on t1.RV_CODE=t2.RV_CODE
        inner join BSN_RVINFOEX_B t3 on t1.RV_CODE=t3.RV_CODE
    </select>
    <select id="statisticRiverDetail" resultType="map">
        select
          count(1) TOTAL,
          SUM(t.LEVEL1) LEVEL1,
            SUM(t.LEVEL2) LEVEL2,
            SUM(t.LEVEL3) LEVEL3,
            SUM(t.LEVEL4) LEVEL4,
            SUM(t.LEVEL5) LEVEL5,
            SUM(t.LEVEL6) LEVEL6,
            SUM(t.LEVEL7) LEVEL7
        from (
        select t1.RV_CODE,
                     case when t1.rv_level = '1' then 1 else 0 end LEVEL1,
                     case when t1.rv_level = '2' then 1 else 0 end LEVEL2,
                     case when t1.rv_level = '3' then 1 else 0 end LEVEL3,
                     case when t1.rv_level = '4' then 1 else 0 end LEVEL4,
                     case when t1.rv_level = '5' then 1 else 0 end LEVEL5,
                     case when t1.rv_level = '6' then 1 else 0 end LEVEL6,
                     case when t1.rv_level = '7' then 1 else 0 end LEVEL7
        from BNS_RIVER t1
        left join ATT_RV_BASE t2 on t1.RV_CODE=t2.RV_CODE
        inner join BSN_RVINFOEX_B t3 on t1.RV_CODE=t3.RV_CODE
        ) t
    </select>
</mapper>
