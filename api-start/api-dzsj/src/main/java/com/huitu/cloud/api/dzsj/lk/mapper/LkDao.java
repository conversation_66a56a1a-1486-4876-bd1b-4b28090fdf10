package com.huitu.cloud.api.dzsj.lk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.dzsj.common.entity.ObjAd;
import com.huitu.cloud.api.dzsj.common.entity.ObjBas;
import com.huitu.cloud.api.dzsj.common.entity.ObjRv;
import com.huitu.cloud.api.dzsj.common.entity.ObjWiun;
import com.huitu.cloud.api.dzsj.lk.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 湖泊基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
public interface LkDao extends BaseMapper<AttLkBase> {
    /**
     * 防洪工程湖泊区列表(分页)
     *
     * @param page
     * @param param
     * @return
     */
    IPage<LkInfo> getLkByPage(Page page, @Param("map") Map<String, Object> param);

    /**
     * 防洪工程湖泊信息
     *
     * @param lkCode
     * @return
     */
    LkRelatedInfo getLkRelatedInfo(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊关联流域信息
     *
     * @param lkCode
     * @return
     */
    List<ObjBas> getBasList(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊关联政区信息
     *
     * @param lkCode
     * @return
     */
    List<ObjAd> getAdList(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊关联河流信息
     *
     * @param lkCode
     * @return
     */
    List<ObjRv> getRvList(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊关联水利行业单位信息
     *
     * @param lkCode
     * @return
     */
    List<ObjWiun> getWiunList(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊一般信息查询
     *
     * @param lkCode 湖泊代码
     * @return
     */
    FhgcLkcmin getLkFhgcLkcminInfo(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊基本特征查询
     *
     * @param lkCode 湖泊代码
     * @return
     */
    FhgcLkbsin getLkFhgcLkbsinInfo(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊进湖水系查询
     *
     * @param lkCode 湖泊代码
     * @return
     */
    List<FhgcEnlkwtsy> getLkFhgcEnlkwtsyInfo(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊出湖水系查询
     *
     * @param lkCode 湖泊代码
     * @return
     */
    List<FhgcGolkwtsy> getLkFhgcGolkwtsyInfo(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊汛限水位查询
     *
     * @param lkCode 湖泊代码
     * @return
     */
    List<FhgcLkflwt> getLkFhgcLkflwtInfo(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊水位面积、容积关系查询
     *
     * @param lkCode 湖泊代码
     * @return
     */
    List<FhgcLkwtcprl> getLkFhgcLkwtcprlInfo(@Param("lkCode") String lkCode);

    /**
     * 防洪工程湖泊社经基本情况查询
     *
     * @param lkCode 湖泊代码
     * @return
     */
    FhgcLsebin getLkFhgcLsebinInfo(@Param("lkCode") String lkCode);
}
