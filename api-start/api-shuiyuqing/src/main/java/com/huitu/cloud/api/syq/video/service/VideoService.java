package com.huitu.cloud.api.syq.video.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.syq.video.entity.BsnCcdinfo;
import com.huitu.cloud.api.syq.video.entity.BsnVdstinfo;
import com.huitu.cloud.api.syq.video.entity.VideoCount;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface VideoService {
    /**
     * 分页查询视频站列表信息
     *
     * @param types    视频站类型集合
     * @param name     视频站名称
     * @param adcd     政区编码
     * @param bscd     流域编码
     * @return
     */
    List<BsnVdstinfo> getVideoInfoByPage(String adcd, String bscd, String name, List<String> types);
    /**
     * 查询视频站单站信息
     *
     * @param id    视频站编码
     * @return
     */
    BsnVdstinfo getVideoInfoById(String id);

    /**
     * 根据测站编码查询视频站列表信息
     *
     * @param code    测站编码
     * @return
     */
    List<BsnVdstinfo> getVideoInfoByCode(String code);
    /**
     * 查询视频站统计信息
     *
     * @param types    视频站类型集合
     * @param name     视频站名称
     * @param adcd     政区编码
     * @return
     */
    VideoCount getVideoCountInfo(String adcd, String bscd, String name, List<String> types);
    /**
     * 查询防洪工程水库对象关联视频站信息
     *
     * @param ennmcd    工程编码
     * @return
     */
    BsnVdstinfo getVideoInfoByEnnmcd(String ennmcd);
    /**
     * 视频站列表信息导出
     *
     * @param types    视频站类型集合
     * @param name     视频站名称
     * @param adcd     政区编码
     * @return
     */
    void exportVideoInfo(String adcd, String bscd, String name, List<String> types);

    /**
     * 查询测站关联单个视频站多个视频头信息
     * @param stcd
     * @return
     */
    List<BsnCcdinfo> getVideoDetailInfoByStcd(String stcd);
}
