package com.huitu.cloud.api.ewci.inspect.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.inspect.entity.*;
import com.huitu.cloud.api.ewci.inspect.service.InspectReportService;
import com.huitu.cloud.api.usif.ad.entity.BsnAdcdB;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 检查上报
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "检查上报")
@RequestMapping("/api/ewci/inspect/report")
public class InspectReportResource extends AbstractApiResource implements ApiResource {

    public InspectReportResource(InspectReportService baseService) {
        this.baseService = baseService;
    }

    @Override
    public String getUuid() {
        return "53164018-3b95-f1d9-3ebb-68c66ec664e0";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final InspectReportService baseService;


    @ApiOperation(value = "获取检查类型清单", notes = "作者：赵英捷")
    @PostMapping("select-inspect-type-list")
    public ResponseEntity<SuccessResponse<List<BnsInspectTypeInfo>>> getBnsInspectTypeInfoList() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBnsInspectTypeInfoList()));
    }

    @ApiOperation(value = "获取检查重点清单", notes = "作者：赵英捷")
    @PostMapping("select-inspect-point-list")
    public ResponseEntity<SuccessResponse<List<BnsInspectPointInfo>>> getBnsInspectPointInfoList(@RequestBody BnsInspectReportQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBnsInspectPointInfoList(query)));
    }

    @ApiOperation(value = "获取检查记录统计列表", notes = "作者：赵英捷")
    @PostMapping("select-inspect-records-list")
    public ResponseEntity<SuccessResponse<List<BnsInspectRecords>>> getBnsInspectRecordsList(@RequestBody BnsInspectRecordsQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBnsInspectRecordsList(query)));
    }

    @ApiOperation(value = "获取检查记录列表", notes = "作者：赵英捷")
    @PostMapping("select-inspect-records-child-list")
    public ResponseEntity<SuccessResponse<IPage<BnsInspectRecordsChild>>> getBnsInspectRecordsChildList(@RequestBody BnsInspectRecordsQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBnsInspectRecordsChildList(query)));
    }

    @ApiOperation(value = "水库(水电站)运行水库下拉", notes = "作者：赵英捷")
    @PostMapping("select-rsvr-Run-list")
    public ResponseEntity<SuccessResponse<List<BnsInspectRsvr>>> getRsvrRunList(@RequestBody BnsInspectRsvrQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRsvrRunList(query)));
    }

    @ApiOperation(value = "病险水库下拉", notes = "作者：赵英捷")
    @PostMapping("select-ill-rsvr-list")
    public ResponseEntity<SuccessResponse<List<BnsInspectRsvr>>> getIllRsvrList(@RequestBody BnsInspectRsvrQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getIllRsvrList(query)));
    }

    @ApiOperation(value = "在建工程下拉", notes = "作者：赵英捷")
    @PostMapping("select-construct-eng-list")
    public ResponseEntity<SuccessResponse<List<BnsEngB>>> getConstructEngList(@RequestBody BnsEngBQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getConstructEngList(query)));
    }

    @ApiOperation(value = "水毁工程修复建设下拉", notes = "作者：赵英捷")
    @PostMapping("select-shgc-construct-list")
    public ResponseEntity<SuccessResponse<List<BnsEngB>>> getShgcConstructList(@RequestBody BnsEngBQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getShgcConstructList(query)));
    }

    @ApiOperation(value = "安全度汛工程建设下拉", notes = "作者：赵英捷")
    @PostMapping("select-aqdx-construct-list")
    public ResponseEntity<SuccessResponse<List<BnsEngB>>> getAqdxConstructList(@RequestBody BnsEngBQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getAqdxConstructList(query)));
    }

    @ApiOperation(value = "自动监测站下拉", notes = "作者：赵英捷")
    @PostMapping("select-auto-monitor-list")
    public ResponseEntity<SuccessResponse<List<BnsAutoMonitorB>>> getAutoMonitorList(@RequestBody BnsAutoMonitorQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getAutoMonitorList(query)));
    }

    @ApiOperation(value = "添加/修改/删除责任人落实", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-personImp")
    public ResponseEntity<SuccessResponse<Integer>> inspectPersonImp(@RequestBody BnsInspectPersonImp entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectPersonImp(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectPersonImp(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectPersonImp(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除方案/预案修订", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-planRevise")
    public ResponseEntity<SuccessResponse<Integer>> inspectPlanRevise(@RequestBody BnsInspectPlanRevise entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectPlanRevise(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectPlanRevise(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectPlanRevise(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除方案/应急通信设备", notes = "作者：张宝兴")
    @PostMapping(value = "inspect-emgCommDev")
    public ResponseEntity<SuccessResponse<Integer>> inspectEmgCommDev(@RequestBody BnsInspectEmgCommDev entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectEmgCommDev(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectEmgCommDev(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectEmgCommDev(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除水库(水电站)运行", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-rsvrRun")
    public ResponseEntity<SuccessResponse<Integer>> inspectRsvrRun(@RequestBody BnsInspectRsvrRun entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectRsvrRun(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectRsvrRun(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectRsvrRun(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除病险水库", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-illRsvr")
    public ResponseEntity<SuccessResponse<Integer>> inspectIllRsvr(@RequestBody BnsInspectIllRsvr entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectIllRsvr(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectIllRsvr(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectIllRsvr(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除河道及堤防名称", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-riverDike")
    public ResponseEntity<SuccessResponse<Integer>> inspectRiverDike(@RequestBody BnsInspectRiverDike entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectRiverDike(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectRiverDike(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectRiverDike(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除在建工程", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-conEn")
    public ResponseEntity<SuccessResponse<Integer>> inspectConEn(@RequestBody BnsInspectConEn entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectConEn(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectConEn(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectConEn(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除水闸工程运行", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-wagaRun")
    public ResponseEntity<SuccessResponse<Integer>> inspectWagaRun(@RequestBody BnsInspectWagaRun entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectWagaRun(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectWagaRun(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectWagaRun(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除自动监测站", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-autoMonitor")
    public ResponseEntity<SuccessResponse<Integer>> inspectAutoMonitor(@RequestBody BnsInspectAutoMonitor entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectAutoMonitor(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectAutoMonitor(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectAutoMonitor(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除简易雨量站", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-simpleRain")
    public ResponseEntity<SuccessResponse<Integer>> inspectSimpleRain(@RequestBody BnsInspectSimpleRain entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectSimpleRain(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectSimpleRain(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectSimpleRain(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除无线预警广播站", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-warnBroad")
    public ResponseEntity<SuccessResponse<Integer>> inspectWarnBroad(@RequestBody BnsInspectWarnBroad entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectWarnBroad(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectWarnBroad(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectWarnBroad(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除县级山洪灾害防御平台", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-countyPlatform")
    public ResponseEntity<SuccessResponse<Integer>> inspectCountyPlatform(@RequestBody BnsInspectCountyPlatform entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectCountyPlatform(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectCountyPlatform(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectCountyPlatform(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除运行维护经费", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-mainExp")
    public ResponseEntity<SuccessResponse<Integer>> inspectMainExp(@RequestBody BnsInspectMainExp entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectMainExp(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectMainExp(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectMainExp(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除水毁工程修复建设", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-shgcCons")
    public ResponseEntity<SuccessResponse<Integer>> inspectShgcCons(@RequestBody BnsInspectShgcCons entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectShgcCons(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectShgcCons(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectShgcCons(entity)));
            }
        }
    }

    @ApiOperation(value = "添加/修改/删除安全度汛工程建设", notes = "作者：赵英捷")
    @PostMapping(value = "inspect-aqdxCons")
    public ResponseEntity<SuccessResponse<Integer>> inspectAqdxCons(@RequestBody BnsInspectAqdxCons entity) {
        if ("1".equals(entity.getIsDelete())) { // 删除
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.deleteInspectAqdxCons(entity)));
        } else {
            if (StringUtils.isNotBlank(entity.getInspectId())) { // 修改
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.updateInspectAqdxCons(entity)));
            } else { // 添加
                return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertInspectAqdxCons(entity)));
            }
        }
    }

    @ApiOperation(value = "查询政区二级树(向下探2级)", notes = "作者：张宝兴")
    @GetMapping(value = "get-ad-tree")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码 15位", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<BsnAdcdB>> getBsnAdcdbTree(@RequestParam String adcd) throws Exception {
        BsnAdcdB adBean = baseService.getAdTree(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", adBean));
    }


    @ApiOperation(value = "查询adnm", notes = "作者：张宝兴")
    @GetMapping(value = "get-adnm-by-adcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码 15位", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<String>> getAdnmByAdcd(@RequestParam String adcd) throws Exception {
        String res = baseService.getAdnmByAdcd(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", res));
    }

    @ApiOperation(value = "获取检查记录详情", notes = "作者：张宝兴")
    @PostMapping("select-inspect-record-info")
    public ResponseEntity<SuccessResponse<T>> getBnsInspectRecordInfo(@RequestBody BnsInspectRecordInfoQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBnsInspectRecordInfo(query)));
    }

    @ApiOperation(value = "查询是否生成检查报告", notes = "作者：张宝兴")
    @PostMapping(value = "select-report-count")
    public ResponseEntity<SuccessResponse<Integer>> selectReportCount(@RequestBody BnsInspectReportAllQuery entity) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.selectReportCount(entity)));
    }

    @ApiOperation(value = "生成报告", notes = "作者：张宝兴")
    @PostMapping(value = "insert-report")
    public ResponseEntity<SuccessResponse<Integer>> insertReport(@Validated @RequestBody BnsInspectReportAllQuery entity) {
        int result = baseService.insertReport(entity);
        if (result > 0) {
            baseService.rebuildReportDocumentFileAsync(entity.getReportId());
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", result));
    }

    @ApiOperation(value = "重新生成报告文档", notes = "作者：曹宝金")
    @GetMapping(value = "rebuild-report-document")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportId", value = "报告ID", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<Integer>> rebuildReportDocument(@RequestParam String reportId) {
        int result = baseService.rebuildReportDocument(reportId);
        if (result > 0) {
            baseService.rebuildReportDocumentFileAsync(reportId);
        }
        return ResponseEntity.ok(new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "获取检查报告列表", notes = "作者：张宝兴")
    @PostMapping("select-inspect-reports-child-list")
    public ResponseEntity<SuccessResponse<IPage<BnsInspectReportsChild>>> getBnsInspectReportsChildList(@RequestBody BnsInspectReportsQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getBnsInspectReportsChildList(query)));
    }


    @ApiOperation(value = "确认生成检查报告", notes = "作者：张宝兴")
    @PostMapping(value = "submit-inspect-report")
    public ResponseEntity<SuccessResponse<Integer>> submitInspectReport(@RequestBody BnsInspectSubmitReports entity) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.submitInspectReport(entity)));
    }

    @ApiOperation(value = "删除检查报告", notes = "作者：张宝兴")
    @GetMapping(value = "delete-inspect-report")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportId", value = "报告ID", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<Integer>> deleteInspectReport(@RequestParam String reportId) {
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", baseService.deleteInspectReport(reportId)));
    }

    @ApiOperation(value = "检查报告详情", notes = "作者：张宝兴")
    @GetMapping(value = "get-inspect-report-by-id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportId", value = "报告ID", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<BnsInspectReportsChild>> getInspectReportById(@RequestParam String reportId) throws Exception {
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", baseService.getInspectReportById(reportId)));
    }

}
