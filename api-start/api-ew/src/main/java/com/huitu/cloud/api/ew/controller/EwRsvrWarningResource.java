package com.huitu.cloud.api.ew.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ew.entity.ext.EwRsvrWarningSending;
import com.huitu.cloud.api.ew.entity.request.EwRsvrWarningCorrectRequest;
import com.huitu.cloud.api.ew.entity.request.EwRsvrWarningRecordRequest;
import com.huitu.cloud.api.ew.entity.request.EwRsvrWarningSendingRequest;
import com.huitu.cloud.api.ew.entity.response.EwRsvrWarningRecordResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarningGradeSummaryResponse;
import com.huitu.cloud.api.ew.exception.WarningException;
import com.huitu.cloud.api.ew.service.EwRsvrWarningService;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import com.huitu.cloud.api.usif.util.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 水库监测预警服务
 *
 * <AUTHOR>
 */
@Api(tags = "水库监测预警服务")
@RestController
@RequestMapping("/api/ew/rsvr/warning")
public class EwRsvrWarningResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "73235263-6fd7-cc85-d89a-************";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final LoginUtils loginUtils;
    private final EwRsvrWarningService baseService;

    public EwRsvrWarningResource(LoginUtils loginUtils, EwRsvrWarningService baseService) {
        this.loginUtils = loginUtils;
        this.baseService = baseService;
    }

    @ApiOperation(value = "获取水库预警分页列表", notes = "作者：曹宝金")
    @PostMapping("page-list")
    public ResponseEntity<SuccessResponse<IPage<EwRsvrWarningRecordResponse>>> getPageList(@RequestBody @Validated EwRsvrWarningRecordRequest request) {
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(request)));
        } catch (IllegalArgumentException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("获取水库预警分页列表失败", ex);
        }
    }

    @ApiOperation(value = "获取水库预警等级统计", notes = "作者：zyj")
    @PostMapping("grade-summary-list")
    public ResponseEntity<SuccessResponse<List<EwWarningGradeSummaryResponse>>> getRsvrWarningGradeSummary(@RequestBody @Validated EwRsvrWarningRecordRequest request) {
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRsvrWarningGradeSummary(request)));
        } catch (IllegalArgumentException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("获取水库预警等级统计失败", ex);
        }
    }

    @ApiOperation(value = "获取水库预警记录", notes = "作者：曹宝金")
    @GetMapping("record")
    public ResponseEntity<SuccessResponse<EwRsvrWarningRecordResponse>> getRecord(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRecord(warnId)));
    }

    @ApiOperation(value = "获取预警发送记录分页列表", notes = "作者：曹宝金")
    @PostMapping("sending-page-list")
    public ResponseEntity<SuccessResponse<IPage<EwRsvrWarningSending>>> getSendingPageList(
            @RequestBody @Validated EwRsvrWarningSendingRequest<EwRsvrWarningSending> request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSendingPageList(request)));
    }

    @ApiOperation(value = "获取测站最新水位", notes = "作者：曹宝金")
    @GetMapping("latest-rz")
    public ResponseEntity<SuccessResponse<BigDecimal>> getLatestRz(String stcd) {
        Assert.hasText(stcd, "参数[测站编码]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getLatestRz(stcd)));
    }

    @ApiOperation(value = "更正预警", notes = "作者：曹宝金")
    @PostMapping("correct")
    public ResponseEntity<SuccessResponse<Integer>> correct(@RequestBody @Validated EwRsvrWarningCorrectRequest request) {
        try {
            UserInfos user = loginUtils.getCurrentLoginUser();
            int result = baseService.correct(request, user);
            if (result <= 0) {
                return ResponseEntity.ok(new SuccessResponse<>(this, "暂无需要更正的预警", 1));
            }
            return ResponseEntity.ok(new SuccessResponse<>(this, "更正预警成功", 0));
        } catch (WarningException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("更正预警失败", ex);
        }
    }
}
