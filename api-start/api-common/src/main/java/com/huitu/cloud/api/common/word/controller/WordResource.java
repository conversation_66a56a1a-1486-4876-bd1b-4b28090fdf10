package com.huitu.cloud.api.common.word.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.common.word.entity.ExportWordQo;
import com.huitu.cloud.api.common.word.service.WordService;
import io.swagger.annotations.*;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * <p>
 * word操作 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-30
 */

@RestController
@Api(tags = "word操作")
@RequestMapping("/api/base/word")
public class WordResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "c8bffd0b-e981-4049-a38d-30982599ecd6";
    }
    @Override
    public String getVersion() {
        return "1.0";
    }
    @Autowired
    private WordService wordService;


    @ApiOperation(value = "导出(无模板)", notes = "导出(无模板)")
    @PostMapping(value = "export-word-title-text")
    public void exportNoTemplate(@RequestBody ExportWordQo exportWordQo, HttpServletResponse response) throws Exception {

        wordService.exportNoTemplate(exportWordQo, response);
    }

}
