package com.huitu.cloud.api.syq.hdfhzb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 河道站防洪指标表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
@TableName("ST_RVFCCH_B")
@ApiModel(value="StRvfcchB对象", description="河道站防洪指标表")
public class StRvfcchB extends Model<StRvfcchB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "左堤高程")
    @TableField("LDKEL")
    private BigDecimal ldkel;

    @ApiModelProperty(value = "右堤高程")
    @TableField("RDKEL")
    private BigDecimal rdkel;

    @ApiModelProperty(value = "警戒水位")
    @TableField("WRZ")
    private BigDecimal wrz;

    @ApiModelProperty(value = "警戒流量")
    @TableField("WRQ")
    private BigDecimal wrq;

    @ApiModelProperty(value = "保证水位")
    @TableField("GRZ")
    private BigDecimal grz;

    @ApiModelProperty(value = "保证流量")
    @TableField("GRQ")
    private BigDecimal grq;

    @ApiModelProperty(value = "平滩流量")
    @TableField("FLPQ")
    private BigDecimal flpq;

    @ApiModelProperty(value = "历史最高水位")
    @TableField("OBHTZ")
    private BigDecimal obhtz;

    @ApiModelProperty(value = "历史最高水位出现时间")
    @TableField("OBHTZTM")
    private LocalDateTime obhtztm;

    @ApiModelProperty(value = "调查最高水位")
    @TableField("IVHZ")
    private BigDecimal ivhz;

    @ApiModelProperty(value = "调查最高水位出现时间")
    @TableField("IVHZTM")
    private LocalDateTime ivhztm;

    @ApiModelProperty(value = "历史最大流量")
    @TableField("OBMXQ")
    private BigDecimal obmxq;

    @ApiModelProperty(value = "历史最大流量出现时间")
    @TableField("OBMXQTM")
    private LocalDateTime obmxqtm;

    @ApiModelProperty(value = "调查最大流量")
    @TableField("IVMXQ")
    private BigDecimal ivmxq;

    @ApiModelProperty(value = "调查最大流量出现时间")
    @TableField("IVMXQTM")
    private LocalDateTime ivmxqtm;

    @ApiModelProperty(value = "历史最大含沙量")
    @TableField("HMXS")
    private BigDecimal hmxs;

    @ApiModelProperty(value = "历史最大含沙量出现时间")
    @TableField("HMXSTM")
    private LocalDateTime hmxstm;

    @ApiModelProperty(value = "历史最大断面平均流速")
    @TableField("HMXAVV")
    private BigDecimal hmxavv;

    @ApiModelProperty(value = "历史最大断面平均流速出现时间")
    @TableField("HMXAVVTM")
    private LocalDateTime hmxavvtm;

    @ApiModelProperty(value = "历史最低水位")
    @TableField("HLZ")
    private BigDecimal hlz;

    @ApiModelProperty(value = "历史最低水位出现时间")
    @TableField("HLZTM")
    private LocalDateTime hlztm;

    @ApiModelProperty(value = "历史最小流量")
    @TableField("HMNQ")
    private BigDecimal hmnq;

    @ApiModelProperty(value = "历史最小流量出现时间")
    @TableField("HMNQTM")
    private LocalDateTime hmnqtm;

    @ApiModelProperty(value = "高水位告警值")
    @TableField("TAZ")
    private BigDecimal taz;

    @ApiModelProperty(value = "大流量告警值")
    @TableField("TAQ")
    private BigDecimal taq;

    @ApiModelProperty(value = "低水位告警值")
    @TableField("LAZ")
    private BigDecimal laz;

    @ApiModelProperty(value = "小流量告警值")
    @TableField("LAQ")
    private BigDecimal laq;

    @ApiModelProperty(value = "启动预报水位标准")
    @TableField("SFZ")
    private BigDecimal sfz;

    @ApiModelProperty(value = "启动预报流量标准")
    @TableField("SFQ")
    private BigDecimal sfq;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;

    @ApiModelProperty(value = "左堤高程别名")
    @TableField(exist = false)
    private String ldkelRen;

    @ApiModelProperty(value = "右堤高程别名")
    @TableField(exist = false)
    private String rdkelRen;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public BigDecimal getLdkel() {
        return ldkel;
    }

    public void setLdkel(BigDecimal ldkel) {
        this.ldkel = ldkel;
    }

    public BigDecimal getRdkel() {
        return rdkel;
    }

    public void setRdkel(BigDecimal rdkel) {
        this.rdkel = rdkel;
    }

    public BigDecimal getWrz() {
        return wrz;
    }

    public void setWrz(BigDecimal wrz) {
        this.wrz = wrz;
    }

    public BigDecimal getWrq() {
        return wrq;
    }

    public void setWrq(BigDecimal wrq) {
        this.wrq = wrq;
    }

    public BigDecimal getGrz() {
        return grz;
    }

    public void setGrz(BigDecimal grz) {
        this.grz = grz;
    }

    public BigDecimal getGrq() {
        return grq;
    }

    public void setGrq(BigDecimal grq) {
        this.grq = grq;
    }

    public BigDecimal getFlpq() {
        return flpq;
    }

    public void setFlpq(BigDecimal flpq) {
        this.flpq = flpq;
    }

    public BigDecimal getObhtz() {
        return obhtz;
    }

    public void setObhtz(BigDecimal obhtz) {
        this.obhtz = obhtz;
    }

    public LocalDateTime getObhtztm() {
        return obhtztm;
    }

    public void setObhtztm(LocalDateTime obhtztm) {
        this.obhtztm = obhtztm;
    }

    public BigDecimal getIvhz() {
        return ivhz;
    }

    public void setIvhz(BigDecimal ivhz) {
        this.ivhz = ivhz;
    }

    public LocalDateTime getIvhztm() {
        return ivhztm;
    }

    public void setIvhztm(LocalDateTime ivhztm) {
        this.ivhztm = ivhztm;
    }

    public BigDecimal getObmxq() {
        return obmxq;
    }

    public void setObmxq(BigDecimal obmxq) {
        this.obmxq = obmxq;
    }

    public LocalDateTime getObmxqtm() {
        return obmxqtm;
    }

    public void setObmxqtm(LocalDateTime obmxqtm) {
        this.obmxqtm = obmxqtm;
    }

    public BigDecimal getIvmxq() {
        return ivmxq;
    }

    public void setIvmxq(BigDecimal ivmxq) {
        this.ivmxq = ivmxq;
    }

    public LocalDateTime getIvmxqtm() {
        return ivmxqtm;
    }

    public void setIvmxqtm(LocalDateTime ivmxqtm) {
        this.ivmxqtm = ivmxqtm;
    }

    public BigDecimal getHmxs() {
        return hmxs;
    }

    public void setHmxs(BigDecimal hmxs) {
        this.hmxs = hmxs;
    }

    public LocalDateTime getHmxstm() {
        return hmxstm;
    }

    public void setHmxstm(LocalDateTime hmxstm) {
        this.hmxstm = hmxstm;
    }

    public BigDecimal getHmxavv() {
        return hmxavv;
    }

    public void setHmxavv(BigDecimal hmxavv) {
        this.hmxavv = hmxavv;
    }

    public LocalDateTime getHmxavvtm() {
        return hmxavvtm;
    }

    public void setHmxavvtm(LocalDateTime hmxavvtm) {
        this.hmxavvtm = hmxavvtm;
    }

    public BigDecimal getHlz() {
        return hlz;
    }

    public void setHlz(BigDecimal hlz) {
        this.hlz = hlz;
    }

    public LocalDateTime getHlztm() {
        return hlztm;
    }

    public void setHlztm(LocalDateTime hlztm) {
        this.hlztm = hlztm;
    }

    public BigDecimal getHmnq() {
        return hmnq;
    }

    public void setHmnq(BigDecimal hmnq) {
        this.hmnq = hmnq;
    }

    public LocalDateTime getHmnqtm() {
        return hmnqtm;
    }

    public void setHmnqtm(LocalDateTime hmnqtm) {
        this.hmnqtm = hmnqtm;
    }

    public BigDecimal getTaz() {
        return taz;
    }

    public void setTaz(BigDecimal taz) {
        this.taz = taz;
    }

    public BigDecimal getTaq() {
        return taq;
    }

    public void setTaq(BigDecimal taq) {
        this.taq = taq;
    }

    public BigDecimal getLaz() {
        return laz;
    }

    public void setLaz(BigDecimal laz) {
        this.laz = laz;
    }

    public BigDecimal getLaq() {
        return laq;
    }

    public void setLaq(BigDecimal laq) {
        this.laq = laq;
    }

    public BigDecimal getSfz() {
        return sfz;
    }

    public void setSfz(BigDecimal sfz) {
        this.sfz = sfz;
    }

    public BigDecimal getSfq() {
        return sfq;
    }

    public void setSfq(BigDecimal sfq) {
        this.sfq = sfq;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    public String getLdkelRen() {
        return ldkelRen;
    }

    public void setLdkelRen(String ldkelRen) {
        this.ldkelRen = ldkelRen;
    }

    public String getRdkelRen() {
        return rdkelRen;
    }

    public void setRdkelRen(String rdkelRen) {
        this.rdkelRen = rdkelRen;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "StRvfcchB{" +
        "stcd=" + stcd +
        ", ldkel=" + ldkel +
        ", rdkel=" + rdkel +
        ", wrz=" + wrz +
        ", wrq=" + wrq +
        ", grz=" + grz +
        ", grq=" + grq +
        ", flpq=" + flpq +
        ", obhtz=" + obhtz +
        ", obhtztm=" + obhtztm +
        ", ivhz=" + ivhz +
        ", ivhztm=" + ivhztm +
        ", obmxq=" + obmxq +
        ", obmxqtm=" + obmxqtm +
        ", ivmxq=" + ivmxq +
        ", ivmxqtm=" + ivmxqtm +
        ", hmxs=" + hmxs +
        ", hmxstm=" + hmxstm +
        ", hmxavv=" + hmxavv +
        ", hmxavvtm=" + hmxavvtm +
        ", hlz=" + hlz +
        ", hlztm=" + hlztm +
        ", hmnq=" + hmnq +
        ", hmnqtm=" + hmnqtm +
        ", taz=" + taz +
        ", taq=" + taq +
        ", laz=" + laz +
        ", laq=" + laq +
        ", sfz=" + sfz +
        ", sfq=" + sfq +
        ", moditime=" + moditime +
        "}";
    }
}
