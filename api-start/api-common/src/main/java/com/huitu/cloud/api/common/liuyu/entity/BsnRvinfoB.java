package com.huitu.cloud.api.common.liuyu.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 河流流域常量表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
@TableName("BSN_RVINFO_B")
@ApiModel(value="BsnRvinfoB对象", description="河流流域常量表")
public class BsnRvinfoB extends Model<BsnRvinfoB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "河流编码")
    @TableId(value = "RVCD", type = IdType.NONE)
    private String rvcd;

    @ApiModelProperty(value = "河流名称")
    @TableField("RVNM")
    private String rvnm;

    @ApiModelProperty(value = "上级河流编码")
    @TableField("PRVCD")
    private String prvcd;

    @ApiModelProperty(value = "排序值")
    @TableField("ORDNUM")
    private String ordnum;

    @ApiModelProperty(value = "下级流域")
    private List<BsnRvinfoB> children;

    public String getRvcd() {
        return rvcd;
    }

    public void setRvcd(String rvcd) {
        this.rvcd = rvcd;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getPrvcd() {
        return prvcd;
    }

    public void setPrvcd(String prvcd) {
        this.prvcd = prvcd;
    }

    public String getOrdnum() {
        return ordnum;
    }

    public void setOrdnum(String ordnum) {
        this.ordnum = ordnum;
    }

    @Override
    protected Serializable pkVal() {
        return this.rvcd;
    }


    public List<BsnRvinfoB> getChildren() {
        return children;
    }

    public void setChildren(List<BsnRvinfoB> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return "BsnRvinfoB{" +
        "rvcd=" + rvcd +
        ", rvnm=" + rvnm +
        ", prvcd=" + prvcd +
        ", ordnum=" + ordnum +
        "}";
    }
}
