<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.sst.mapper.BnsSrstInfoDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.sst.entity.BnsSrstInfo" useCache="false">
        SELECT ROW_NUMBER() OVER(ORDER BY A.ADCD, A.SRSTCD ASC) SORTNO,
        A.SRSTCD, A.ADDRESS, A.WSCD, B.WSNM, A.ADCD, C.ADNM, A.BDATE, A.LGTD, A.LTTD,COALESCE(D.PICTURE,0) AS PICTURES
        FROM BNS_IA_C_SRSTINFO A
        LEFT JOIN IA_C_WATA B ON B.WSCD = A.WSCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD
        LEFT JOIN (SELECT COUNT(A.SRSTCD) PICTURE, A.SRSTCD FROM BNS_IA_C_SRSTINFO A LEFT JOIN BSN_STFILE_B B ON A.SRSTCD = B.KEYID WHERE B.TYPE = '简易雨量' GROUP BY SRSTCD) D ON A.SRSTCD = D.SRSTCD
        WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.sdt != null">
            AND A.BDATE >= #{map.sdt}
        </if>
        <if test="map.edt != null">
            AND A.BDATE &lt;= #{map.edt}
        </if>
        <if test="map.address != null and map.address != ''">
            AND CHARINDEX(#{map.address}, A.ADDRESS) > 0
        </if>
        ORDER BY SORTNO ASC
    </select>
    <update id="update">
        UPDATE BNS_IA_C_SRSTINFO
        SET ADDRESS = #{info.address},
            WSCD    = #{info.wscd},
            ADCD    = #{info.adcd},
            BDATE   = #{info.bdate},
            LGTD    = #{info.lgtd},
            LTTD    = #{info.lttd}
        WHERE SRSTCD = #{info.srstcd}
    </update>
    <delete id="delete">
        DELETE
        FROM BNS_IA_C_SRSTINFO
        WHERE SRSTCD = #{srstcd}
    </delete>

    <insert id="add">
        INSERT INTO BNS_IA_C_SRSTINFO (
            SRSTCD,
            ADDRESS,
            WSCD,
            ADCD,
            BDATE,
            LGTD,
            LTTD,
            MODITIME
        )
        VALUES
        (
            #{info.srstcd},
            #{info.address},
            #{info.wscd},
            #{info.adcd},
            #{info.bdate},
            #{info.lgtd},
            #{info.lttd},
            GETDATE( )
        )
    </insert>

    <select id="getLyList" resultType="com.huitu.cloud.api.ewci.sst.entity.BnsSrstInfo">
        SELECT
        DISTINCT
        a.WSCD,
        b.WSNM
        FROM
        IA_C_WSADCD a
        JOIN IA_C_WATA b ON a.WSCD = b.WSCD
        LEFT JOIN MDT_ADCDINFO_B c ON a.ADCD = c.ADCD
        where
        LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
    </select>

    <select id="getNumber"  resultType="java.lang.Integer">
        SELECT
        COUNT(*) as sum
        FROM
        BNS_IA_C_SRSTINFO
        WHERE
        SRSTCD =  #{srstcd}
    </select>
</mapper>
