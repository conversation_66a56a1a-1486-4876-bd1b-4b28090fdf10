<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.RiverPersonDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getListByStcd" resultType="com.huitu.cloud.api.ewci.person.entity.RiverPerson">
        SELECT B.RV_NAME,
               P1.REALNM       XZ_REALNM,
               P1.POST         XZ_POST,
               P1.OFFICE_PHONE XZ_OFFICE_PHONE,
               P1.MOBILE_PHONE XZ_MOBILE_PHONE,
               P2.REALNM       JS_REALNM,
               P2.POST         JS_MOBILE,
               P2.OFFICE_PHONE JS_OFFICE_PHONE,
               P2.MOBILE_PHONE JS_MOBILE_PHONE
        FROM (SELECT DISTINCT RV_CODE, ADCD, SNO
              FROM BNS_RIVERPERSON_B
              WHERE RV_CODE IN (SELECT A.OBJCD
                                FROM BSN_OBJONLY_B A
                                         LEFT JOIN BSN_OBJONLY_B B ON B.OBJID = A.OBJID
                                WHERE A.OBJTP = '4'
                                  AND B.OBJTP = '1'
                                  AND B.OBJCD = #{stcd})) T
                 JOIN ATT_RV_BASE B ON B.RV_CODE = T.RV_CODE
                 LEFT JOIN BNS_RIVERPERSON_B P1
                           ON P1.RV_CODE = T.RV_CODE AND P1.ADCD = T.ADCD AND P1.SNO = T.SNO AND P1.PERTP = '1'
                 LEFT JOIN BNS_RIVERPERSON_B P2
                           ON P2.RV_CODE = T.RV_CODE AND P2.ADCD = T.ADCD AND P2.SNO = T.SNO AND P2.PERTP = '2'
        ORDER BY T.ADCD, T.RV_CODE ASC
    </select>
    <select id="getPageList" useCache="false" resultType="com.huitu.cloud.api.ewci.person.entity.RiverPerson">
        WITH WSLIST AS (
        <choose>
            <when test="map.basCode != null and map.basCode != ''">
                SELECT BAS_CODE, BAS_NAME, PBAS_CODE FROM BSN_BAS_B WHERE BAS_CODE = #{map.basCode}
                UNION ALL
                SELECT B.BAS_CODE, B.BAS_NAME, B.PBAS_CODE FROM WSLIST A
                INNER JOIN BSN_BAS_B B ON B.PBAS_CODE = A.BAS_CODE
            </when>
            <otherwise>
                SELECT BAS_CODE, BAS_NAME, PBAS_CODE FROM BSN_BAS_B
            </otherwise>
        </choose>
        )
        SELECT ROW_NUMBER() OVER(ORDER BY T.ADCD, T.BAS_CODE, T.RV_CODE, T.SNO ASC) SORTNO, A.ADNM, B.BAS_NAME,
        C.RV_NAME,T.RV_CODE,T.BAS_CODE,T.ADCD,T.SNO,
        P1.REALNM XZ_REALNM, P1.POST XZ_POST, P1.OFFICE_PHONE XZ_OFFICE_PHONE, P1.MOBILE_PHONE XZ_MOBILE_PHONE,
        P2.REALNM JS_REALNM, P2.POST JS_POST, P2.OFFICE_PHONE JS_OFFICE_PHONE, P2.MOBILE_PHONE JS_MOBILE_PHONE,
        COALESCE(P1.TS,P2.TS) TS
        FROM (
        SELECT DISTINCT RV_CODE, BAS_CODE, ADCD, SNO FROM BNS_RIVERPERSON_B
        WHERE LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.pernm != null and map.pernm != ''">
            and CHARINDEX(#{map.pernm},REALNM)>0
        </if>
        <if test="map.udtm !=null and map.udtm =='1'.toString()">
            and YEAR(TS) != YEAR(GETDATE())
        </if>
        ) T
        LEFT JOIN MDT_ADCDINFO_B A ON A.ADCD = T.ADCD
        LEFT JOIN BSN_BAS_B B ON B.BAS_CODE = T.BAS_CODE
        LEFT JOIN ATT_RV_BASE C ON C.RV_CODE = T.RV_CODE
        LEFT JOIN BNS_RIVERPERSON_B P1
        ON P1.ADCD = T.ADCD AND P1.RV_CODE = T.RV_CODE AND P1.SNO = T.SNO AND P1.PERTP = '1'
        LEFT JOIN BNS_RIVERPERSON_B P2
        ON P2.ADCD = T.ADCD AND P2.RV_CODE = T.RV_CODE AND P2.SNO = T.SNO AND P2.PERTP = '2'
        WHERE EXISTS(SELECT 8 BAS_CODE FROM WSLIST WHERE BAS_CODE = T.BAS_CODE)
        <if test="map.rvName != null and map.rvName != ''">
            AND CHARINDEX(#{map.rvName}, C.RV_NAME) > 0
        </if>
        ORDER BY T.ADCD, T.BAS_CODE, T.RV_CODE, T.SNO ASC
    </select>
    <select id="batchImport" statementType="CALLABLE" useCache="false"
            resultType="com.huitu.cloud.api.ewci.person.entity.BnsRiverPersonTmp">
        {CALL PROC_IMPORT_RIVER_PERSON(#{batchNo, mode=IN})}
    </select>
    <select id="getSimpleList" resultType="com.huitu.cloud.api.ewci.person.entity.PersonInfo">
        SELECT REALNM, MOBILE_PHONE MOBILE FROM BNS_RIVERPERSON_B
        WHERE MOBILE_PHONE IS NOT NULL AND LTRIM(RTRIM(MOBILE_PHONE)) != ''
        AND LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.pertps != null and map.pertps.size() != 0">
            AND PERTP IN (<foreach collection="map.pertps" item="pertp" separator=",">#{pertp}</foreach>)
        </if>
        ORDER BY ADCD, RV_CODE, PERTP, SNO ASC
    </select>
    <select id="getRiverSummaryList" resultType="com.huitu.cloud.api.ewci.person.entity.RiverSummaryVo">
        SELECT ADCD, ADNM, PADCD, ALLNUM, XZCOUNT, JSCOUNT FROM (
        <if test="map.include == '1'.toString()">
            SELECT 0 SORTNO, A.ADCD ADCD, B.ADNM ADNM, B.PADCD PADCD,A.ALLNUM ,A.XZCOUNT, A.JSCOUNT FROM (
            SELECT #{map.adcd} ADCD,COUNT(DISTINCT A.REALNM ) ALLNUM,
            COUNT(DISTINCT B.REALNM ) XZCOUNT,
            COUNT(DISTINCT C.REALNM ) JSCOUNT
            FROM BNS_RIVERPERSON_B A
            LEFT JOIN BNS_RIVERPERSON_B B ON A.ADCD = B.ADCD AND A.RV_CODE = B.RV_CODE AND A.SNO = B.SNO AND B.PERTP = '1'
            LEFT JOIN BNS_RIVERPERSON_B C ON A.ADCD = C.ADCD AND A.RV_CODE = C.RV_CODE AND A.SNO = C.SNO AND C.PERTP = '2'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">
                AND LEFT(A.ADCD, 6) NOT IN ('220581')
            </if>
            ) A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
            WHERE EXISTS (SELECT 8 FROM MDT_ADCDINFO_E WHERE ADTP = '1' AND ADCD = A.ADCD)
        </if>
        <if test="map.include == '1'.toString() and map.level &lt;= 4">
            UNION ALL
        </if>
        <if test="map.level &lt;= 4">
            SELECT B.SORTNO, A.ADCD ADCD, A.ADNM ADNM, A.PADCD PADCD,C.ALLNUM ,C.XZCOUNT, C.JSCOUNT
            FROM MDT_ADCDINFO_B A
            LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
            LEFT JOIN (SELECT ADCD, COUNT(DISTINCT AREALNM ) ALLNUM,
            COUNT(DISTINCT BREALNM ) XZCOUNT,
            COUNT(DISTINCT CREALNM ) JSCOUNT
            FROM (SELECT LEFT(LEFT(A.ADCD, #{map.lowLevel}) + '000000000000000', 15) ADCD, A.MOBILE_PHONE AMOBILE, A.REALNM AREALNM,
            B.MOBILE_PHONE BMOBILE, B.REALNM BREALNM, C.MOBILE_PHONE CMOBILE, C.REALNM CREALNM
            FROM BNS_RIVERPERSON_B A
            LEFT JOIN BNS_RIVERPERSON_B B ON A.ADCD = B.ADCD AND A.RV_CODE = B.RV_CODE AND A.SNO = B.SNO AND B.PERTP = '1'
            LEFT JOIN BNS_RIVERPERSON_B C ON A.ADCD = C.ADCD AND A.RV_CODE = C.RV_CODE AND A.SNO = C.SNO AND C.PERTP = '2'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND LEFT(A.ADCD, 6) NOT IN ('220581')
            <if test="map.level == '2'.toString()">
                UNION ALL
                SELECT '220581000000000' ADCD, A.MOBILE_PHONE AMOBILE, A.REALNM AREALNM,
                B.MOBILE_PHONE BMOBILE, B.REALNM BREALNM, C.MOBILE_PHONE CMOBILE, C.REALNM CREALNM
                FROM BNS_RIVERPERSON_B A
                LEFT JOIN BNS_RIVERPERSON_B B ON A.ADCD = B.ADCD AND A.RV_CODE = B.RV_CODE AND A.SNO = B.SNO AND B.PERTP = '1'
                LEFT JOIN BNS_RIVERPERSON_B C ON A.ADCD = C.ADCD AND A.RV_CODE = C.RV_CODE AND A.SNO = C.SNO AND C.PERTP = '2'
                WHERE LEFT(A.ADCD, 6) = '220581'
            </if>
            ) A GROUP BY ADCD) C ON C.ADCD = A.ADCD
            WHERE A.PADCD = #{map.adcd}
        </if>
        ) A
        ORDER BY SORTNO ASC
    </select>

    <delete id="delByRvCodeAndAdcdAndSno">
        DELETE
        FROM BNS_RIVERPERSON_B
        WHERE RV_CODE = #{map.rvCode}
          AND ADCD = #{map.adcd}
          AND SNO = #{map.sno}
    </delete>
    <insert id="insertList">
        INSERT INTO
        BNS_RIVERPERSON_B(RV_CODE,BAS_CODE,ADCD,PERTP,SNO,REALNM,POST,OFFICE_PHONE,MOBILE_PHONE,TS)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.rvCode},#{item.basCode},#{item.adcd},#{item.pertp},#{item.sno},#{item.realnm},#{item.post},#{item.officePhone},#{item.mobilePhone},#{item.ts})
        </foreach>
    </insert>

</mapper>
