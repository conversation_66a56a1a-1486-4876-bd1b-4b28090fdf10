<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ledger.mapper.LgProjectReserveDao">
    <!--    <cache type="com.huitu.cloud.config.RedisCache"/>-->

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.ledger.entity.LgProject" useCache="false">
        SELECT
        ROW_NUMBER ( ) OVER ( ORDER BY a.UPDATED DESC ) SORTNO,
        A.PROJECT_ID,
        A.ADCD,
        A.PROJECT_NAME,
        A.USAGE_DIRECTION,
        A.BASIN,
        A.RIVER,
        A.TRIBUTARY_SECTION_NAME,
        A.ORIGINAL_WATER_PROJECT,
        A.ORIGINAL_WATER_PROJECT_NO,
        <PERSON><PERSON>START_LOCATION,
        A.START_ORIGINAL_PILE,
        A.START_CURRENT_PILE,
        A.START_LONGITUDE,
        A.START_LATITUDE,
        A.END_LOCATION,
        A.END_ORIGINAL_PILE,
        A.END_CURRENT_PILE,
        A.END_LONGITUDE,
        A.END_LATITUDE,
        A.DAM_TOP,
        A.DAM_FRONT,
        A.DAM_BACK,
        A.SPILLWAY_INLET,
        A.SPILLWAY_CONTROL,
        A.SPILLWAY_CHANNEL,
        A.SPILLWAY_ENERGY_DISSIPATION,
        A.SPILLWAY_DRAIN,
        A.FLOOD_DIVERSION_INLET,
        A.FLOOD_DIVERSION_GATE,
        A.FLOOD_DIVERSION_CONTROL,
        A.FLOOD_DIVERSION_TUNNEL,
        A.FLOOD_DIVERSION_ENERGY,
        A.FLOOD_DIVERSION_DRAIN,
        A.OTHER_FACILITIES,
        A.EMBANKMENT,
        A.BANK_PROTECTION,
        A.CULVERT,
        A.HYDROLOGICAL_FACILITIES,
        A.DISASTER_TIME,
        A.REPORTED_TO_NATIONAL_SYSTEM,
        A.DISASTER_DAMAGE_DESCRIPTION,
        A.PRELIMINARY_WORK,
        A.PRELIMINARY_DESIGN_NO,
        A.PRELIMINARY_DESIGN_APPROVAL,
        A.PROTECTION_TARGET,
        A.TOTAL_INVESTMENT,
        A.CONSTRUCTION_COST,
        A.CENTRAL_SUBSIDY,
        A.PERFORMANCE_TARGET_RESERVOIR_PLAN,
        A.PERFORMANCE_TARGET_EMBANKMENT_PLAN,
        A.PERFORMANCE_TARGET_CULVERT_PLAN,
        A.PERFORMANCE_TARGET_HYDRO_PLAN,
        A.PERFORMANCE_TARGET_OTHER_PLAN,
        A.CONTACT_PERSON,
        A.CONTACT_PHONE,
        A.REMARKS,
        A.STATUS,
        A.CREATOR,
        A.CREATED,
        A.UPDATED,
        A.UPDATOR,
        A.DEL_FLAG,
        C.IMPLEMENTATION_YEAR,
        AD.adnm as adAdnm,
        ADV.adnm as advAdnm
        FROM
        LEDGER_PROJECT A
        LEFT JOIN LEDGER_CONSTRUCTION C ON C.PROJECT_ID = A.PROJECT_ID
        LEFT JOIN BSN_ADCD_B AD ON LEFT(A.ADCD, 4)+ '***********' = AD.adcd
        LEFT JOIN BSN_ADCD_B ADV ON a.ADCD = ADV.adcd
        WHERE A.DEL_FLAG = 1
        <if test="map.adcd != null and map.adcd !=''">
            and left(A.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.projectName!= null and map.projectName != '' ">
            and CHARINDEX(#{map.projectName},A.PROJECT_NAME) > 0
        </if>
        <if test="map.basin!= null and map.basin != '' ">
            and CHARINDEX(#{map.basin},A.BASIN) > 0
        </if>
        <if test="map.river!= null and map.river != '' ">
            and CHARINDEX(#{map.river},A.RIVER) > 0
        </if>
        <if test="map.originalWaterProject!= null and map.originalWaterProject != '' ">
            and CHARINDEX(#{map.originalWaterProject},A.ORIGINAL_WATER_PROJECT) > 0
        </if>
        <if test="map.usageDirection!=null and map.usageDirection!= ''">
            and A.USAGE_DIRECTION=#{map.usageDirection}
        </if>
        <if test="map.status!=null and map.status!= ''">
            and A.STATUS=#{map.status}
        </if>
        <if test="map.implementationYear != null and map.implementationYear !=''">
            and C.IMPLEMENTATION_YEAR=#{map.implementationYear}
        </if>
        ORDER BY
        A.UPDATED DESC
    </select>

    <select id="selectLgProjectIds" resultType="com.huitu.cloud.api.ewci.ledger.entity.LgProject">
        select * from LEDGER_PROJECT where PROJECT_ID = #{projectId}
    </select>

    <select id="getPageLgSmsList" resultType="com.huitu.cloud.api.ewci.ledger.entity.LgSms">
        SELECT
        ROW_NUMBER ( ) OVER ( ORDER BY a.CREATED DESC ) SORTNO,
        A.ID,
        A.PROJECT_ID,
        A.CONTACT_PERSON,
        A.CONTACT_PHONE,
        A.SMS_CONTENT,
        A.CREATED,
        LC.FUNDING_BATCH,
        LC.USAGE_DIRECTION,
        LC.IMPLEMENTATION_YEAR,
        LP.PROJECT_NAME,
        LP.ADCD,
        AD.adnm AS adAdnm,
        ADV.adnm AS advAdnm,
        C.SENDRESULT AS sendResult,
        C.RECTM
        FROM
        LEDGER_SMS A
        LEFT JOIN MESSAGEINFO_R B ON A.ID = B.WARNID
        LEFT JOIN MESSAGESEND_R C ON B.MSGID = C.MSGID
        LEFT JOIN LEDGER_PROJECT LP ON LP.PROJECT_ID = A.PROJECT_ID
        LEFT JOIN LEDGER_CONSTRUCTION LC ON LC.PROJECT_ID = A.PROJECT_ID
        LEFT JOIN BSN_ADCD_B AD ON LEFT(LP.ADCD, 4) + '***********' = AD.ADCD
        LEFT JOIN BSN_ADCD_B ADV ON LP.ADCD = ADV.ADCD
        WHERE A.DEL_FLAG = 1
        <if test="map.adcd != null and map.adcd !=''">
            and left(LP.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(LP.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.fundingBatch!=null and map.fundingBatch!= ''">
            and LC.FUNDING_BATCH=#{map.fundingBatch}
        </if>
        <if test="map.implementationYear!=null and map.implementationYear!= ''">
            and LC.IMPLEMENTATION_YEAR=#{map.implementationYear}
        </if>
        <if test="map.usageDirection!=null and map.usageDirection!= ''">
            and LC.USAGE_DIRECTION=#{map.usageDirection}
        </if>
        <if test="map.sendStatus!=null and map.sendStatus!= ''">
            and C.SENDRESULT=#{map.sendStatus}
        </if>
        ORDER BY A.CREATED DESC
    </select>

    <update id="deleteSms">
        update LEDGER_SMS set DEL_FLAG = 2 where ID = #{smsId}
    </update>

    <update id="retrySendSms">
        UPDATE MESSAGESEND_R
        SET SENDRESULT = '2',
            RECTM = NULL
        WHERE
            MSGID = (SELECT MSGID FROM MESSAGEINFO_R WHERE WARNID = #{smsId})
    </update>


</mapper>
