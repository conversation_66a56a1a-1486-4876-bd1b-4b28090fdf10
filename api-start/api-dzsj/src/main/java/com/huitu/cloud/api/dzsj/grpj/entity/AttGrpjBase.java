package com.huitu.cloud.api.dzsj.grpj.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 治河工程基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-19
 */
@TableName("ATT_GRPJ_BASE")
@ApiModel(value="AttGrpjBase对象", description="治河工程基础信息表")
public class AttGrpjBase extends Model<AttGrpjBase> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = " 治河工程代码")
    @TableId(value = "GRPJ_CODE", type = IdType.NONE)
    private String grpjCode;

    @ApiModelProperty(value = " 治河工程名称 ")
    @TableField("GRPJ_NAME")
    private String grpjName;

    @ApiModelProperty(value = " 治河工程几何中心点经度 ")
    @TableField("GRPJ_LONG")
    private Double grpjLong;

    @ApiModelProperty(value = " 治河工程几何中心点纬度")
    @TableField("GRPJ_LAT")
    private Double grpjLat;

    @ApiModelProperty(value = " 治河工程所在位置  ")
    @TableField("GRPJ_LOC")
    private String grpjLoc;

    @ApiModelProperty(value = "工程数量   ")
    @TableField("ENG_NUM")
    private Double engNum;

    @ApiModelProperty(value = "工程总长度 ")
    @TableField("ENG_LEN")
    private Double engLen;

    @ApiModelProperty(value = "被整治河段长度")
    @TableField("MANG_REA_LEN")
    private Double mangReaLen;

    @ApiModelProperty(value = "岸别")
    @TableField("BANK")
    private String bank;

    @ApiModelProperty(value = " 治河工程简介")
    @TableField("GRPJ_BRIN")
    private String grpjBrin;

    @ApiModelProperty(value = "备注  ")
    @TableField("NOTE")
    private String note;

    @ApiModelProperty(value = " 属性采集时间")
    @TableField("COLL_DATE")
    private LocalDateTime collDate;

    @ApiModelProperty(value = "属性更新时间 ")
    @TableField("UPD_DATE")
    private LocalDateTime updDate;


    public String getGrpjCode() {
        return grpjCode;
    }

    public void setGrpjCode(String grpjCode) {
        this.grpjCode = grpjCode;
    }

    public String getGrpjName() {
        return grpjName;
    }

    public void setGrpjName(String grpjName) {
        this.grpjName = grpjName;
    }

    public Double getGrpjLong() {
        return grpjLong;
    }

    public void setGrpjLong(Double grpjLong) {
        this.grpjLong = grpjLong;
    }

    public Double getGrpjLat() {
        return grpjLat;
    }

    public void setGrpjLat(Double grpjLat) {
        this.grpjLat = grpjLat;
    }

    public String getGrpjLoc() {
        return grpjLoc;
    }

    public void setGrpjLoc(String grpjLoc) {
        this.grpjLoc = grpjLoc;
    }

    public Double getEngNum() {
        return engNum;
    }

    public void setEngNum(Double engNum) {
        this.engNum = engNum;
    }

    public Double getEngLen() {
        return engLen;
    }

    public void setEngLen(Double engLen) {
        this.engLen = engLen;
    }

    public Double getMangReaLen() {
        return mangReaLen;
    }

    public void setMangReaLen(Double mangReaLen) {
        this.mangReaLen = mangReaLen;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getGrpjBrin() {
        return grpjBrin;
    }

    public void setGrpjBrin(String grpjBrin) {
        this.grpjBrin = grpjBrin;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public LocalDateTime getCollDate() {
        return collDate;
    }

    public void setCollDate(LocalDateTime collDate) {
        this.collDate = collDate;
    }

    public LocalDateTime getUpdDate() {
        return updDate;
    }

    public void setUpdDate(LocalDateTime updDate) {
        this.updDate = updDate;
    }

    @Override
    protected Serializable pkVal() {
        return this.grpjCode;
    }

    @Override
    public String toString() {
        return "AttGrpjBase{" +
        "grpjCode=" + grpjCode +
        ", grpjName=" + grpjName +
        ", grpjLong=" + grpjLong +
        ", grpjLat=" + grpjLat +
        ", grpjLoc=" + grpjLoc +
        ", engNum=" + engNum +
        ", engLen=" + engLen +
        ", mangReaLen=" + mangReaLen +
        ", bank=" + bank +
        ", grpjBrin=" + grpjBrin +
        ", note=" + note +
        ", collDate=" + collDate +
        ", updDate=" + updDate +
        "}";
    }
}
