package com.huitu.cloud.api.common.msg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2019-09-18
 */
@ApiModel(value="短信发送记录", description="短信发送记录信息")
public class MessageVo implements Serializable {
    private static final long serialVersionUID=1L;
    @ApiModelProperty(value = "短信编号， 发送无需传值")
    @TableField("MSGID")
    private Integer msgId;


    @ApiModelProperty(value = "短信类型编号 10-内部预警消息； 20-外部预警消息 ")
    @TableField("MSGTYPEID")
    private Integer msgTypeId;

    @ApiModelProperty(value = "短信内容 ")
    @TableField("MSGCONTENT")
    private String msgContent;

    @ApiModelProperty(value = " 短信发送人")
    @TableField("SENDER")
    private String sender;

    @ApiModelProperty(value = " 短信发送方式 10-短信； 20-传真； 30-广播  ")
    @TableField("MEDIAID")
    private String mediaId;

    @ApiModelProperty(value = " 短信发出时间 ")
    @TableField("SENDTM")
    private LocalDateTime sendTm;

    @ApiModelProperty(value = "备注 ")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = " 所属政区  ")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = " 短息接收年份 ")
    @TableId(value = "CODE", type = IdType.NONE)
    private String code;

    @ApiModelProperty(value = " 短信接收编号 ")
    @TableField("SMSID")
    private Integer smsId;

    @ApiModelProperty(value = "手机号码")
    @TableField("SID")
    private String sid;

    @ApiModelProperty(value = "发送状态")
    @TableField("SENDRESULTNM")
    private String sendResultNm;

    @ApiModelProperty(value = " 发送情况代码")
    @TableField("SENDRESULT")

    private Integer sendResult;

    @ApiModelProperty(value = " 接收人姓名")
    @TableField("UNAME")
    private String uname;

    @ApiModelProperty(value = " 山洪责任人类型")
    private String rytps;

    public Integer getMsgId() {
        return msgId;
    }

    public void setMsgId(Integer msgId) {
        this.msgId = msgId;
    }

    public Integer getMsgTypeId() {
        return msgTypeId;
    }

    public void setMsgTypeId(Integer msgTypeId) {
        this.msgTypeId = msgTypeId;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getMediaId() {
        return mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public LocalDateTime getSendTm() {
        return sendTm;
    }

    public void setSendTm(LocalDateTime sendTm) {
        this.sendTm = sendTm;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getSmsId() {
        return smsId;
    }

    public void setSmsId(Integer smsId) {
        this.smsId = smsId;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getSendResultNm() {
        return sendResultNm;
    }

    public void setSendResultNm(String sendResultNm) {
        this.sendResultNm = sendResultNm;
    }

    public Integer getSendResult() {
        return sendResult;
    }

    public void setSendResult(Integer sendResult) {
        this.sendResult = sendResult;
    }

    public String getUname() {
        return uname;
    }

    public void setUname(String uname) {
        this.uname = uname;
    }

    public String getRytps() {
        return rytps;
    }

    public void setRytps(String rytps) {
        this.rytps = rytps;
    }
}
