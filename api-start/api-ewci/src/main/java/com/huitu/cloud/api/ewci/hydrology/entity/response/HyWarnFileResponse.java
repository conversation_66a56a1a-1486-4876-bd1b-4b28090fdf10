package com.huitu.cloud.api.ewci.hydrology.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.api.ewci.hydrology.entity.HyWarnFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 洪水预警文件响应对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警文件响应对象")
public class HyWarnFileResponse extends HyWarnFile {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "签发人姓名")
    @TableField(value = "SIGNER_NAME")
    private String signerName;

    @ApiModelProperty(value = "审核人姓名")
    @TableField(value = "AUDITOR_NAME")
    private String auditorName;

    @ApiModelProperty(value = "编辑人姓名")
    @TableField(value = "EDITOR_NAME")
    private String editorName;

    public String getSignerName() {
        return signerName;
    }

    public void setSignerName(String signerName) {
        this.signerName = signerName;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public String getEditorName() {
        return editorName;
    }

    public void setEditorName(String editorName) {
        this.editorName = editorName;
    }
}
