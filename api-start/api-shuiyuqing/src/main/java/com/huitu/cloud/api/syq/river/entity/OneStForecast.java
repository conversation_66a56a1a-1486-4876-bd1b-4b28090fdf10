package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2019-09-11
 */
public class OneStForecast extends StForecastf{
    @ApiModelProperty(value = "警戒水位")
    @TableField("WRZ")
    private BigDecimal wrz;

    @ApiModelProperty(value = "警戒流量")
    @TableField("WRQ")
    private BigDecimal wrq;

    @ApiModelProperty(value = "保证水位")
    @TableField("GRZ")
    private BigDecimal grz;

    @ApiModelProperty(value = "保证流量")
    @TableField("GRQ")
    private BigDecimal grq;

    @ApiModelProperty(value = "超警戒水位")
    private BigDecimal zwrz;

    @ApiModelProperty(value = "超警戒流量")
    private BigDecimal zwrq;

    @ApiModelProperty(value = "超保证水位")
    private BigDecimal zgrz;

    @ApiModelProperty(value = "超保证流量")
    private BigDecimal zgrq;

    @ApiModelProperty(value = "历史最高水位")
    @TableField("OBHTZ")
    private BigDecimal obhtz;

    @ApiModelProperty(value = "历史最大流量")
    @TableField("OBMXQ")
    private BigDecimal obmxq;

    @ApiModelProperty(value = "起始水位")
    @TableField("BGZ")
    private BigDecimal bgz;

    @ApiModelProperty(value = "起始流量")
    @TableField("BGQ")
    private BigDecimal bgq;

    @Override
    public BigDecimal getWrz() {
        return wrz;
    }

    public void setWrz(BigDecimal wrz) {
        this.wrz = wrz;
    }

    public BigDecimal getWrq() {
        return wrq;
    }

    public void setWrq(BigDecimal wrq) {
        this.wrq = wrq;
    }

    public BigDecimal getGrz() {
        return grz;
    }

    public void setGrz(BigDecimal grz) {
        this.grz = grz;
    }

    public BigDecimal getGrq() {
        return grq;
    }

    public void setGrq(BigDecimal grq) {
        this.grq = grq;
    }

    public BigDecimal getZwrz() {
        return zwrz;
    }

    public void setZwrz(BigDecimal zwrz) {
        this.zwrz = zwrz;
    }

    public BigDecimal getZwrq() {
        return zwrq;
    }

    public void setZwrq(BigDecimal zwrq) {
        this.zwrq = zwrq;
    }

    public BigDecimal getZgrz() {
        return zgrz;
    }

    public void setZgrz(BigDecimal zgrz) {
        this.zgrz = zgrz;
    }

    public BigDecimal getZgrq() {
        return zgrq;
    }

    public void setZgrq(BigDecimal zgrq) {
        this.zgrq = zgrq;
    }

    public BigDecimal getObhtz() {
        return obhtz;
    }

    public void setObhtz(BigDecimal obhtz) {
        this.obhtz = obhtz;
    }

    public BigDecimal getObmxq() {
        return obmxq;
    }

    public void setObmxq(BigDecimal obmxq) {
        this.obmxq = obmxq;
    }

    public BigDecimal getBgz() {
        return bgz;
    }

    public void setBgz(BigDecimal bgz) {
        this.bgz = bgz;
    }

    public BigDecimal getBgq() {
        return bgq;
    }

    public void setBgq(BigDecimal bgq) {
        this.bgq = bgq;
    }
}
