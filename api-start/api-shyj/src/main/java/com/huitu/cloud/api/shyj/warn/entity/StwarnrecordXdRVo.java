package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.api.syq.video.entity.BsnVdstinfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 * 现地预警返回类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@ApiModel(value="StwarnrecordXdRVo对象", description="现地预警返回类")
public class StwarnrecordXdRVo extends StwarnrecordR{

    @ApiModelProperty(value = "县级行政编码")
    @TableField("XADCD")
    private String xadcd;

    @ApiModelProperty(value = "县级行政名称")
    @TableField("XADNM")
    private String xadnm;

    @ApiModelProperty(value = "行政编码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政名称")
    @TableField("ADNM")
    private String adnm;

    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private Double lttd;

    @ApiModelProperty(value = "偏移经度")
    @TableField("PLGTD")
    private Double plgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("PLTTD")
    private Double plttd;

    @ApiModelProperty(value = "入户报警器编号")
    @TableField("RWID")
    private Integer rwid;

    @ApiModelProperty(value = "入户报警器类型")
    @TableField("RWTP")
    private Integer rwtp;

    @ApiModelProperty(value = "操作时间")
    @TableField("TM")
    private LocalDateTime tm;

    @ApiModelProperty(value = "入户报警器状态（1=开启=红色;0=关闭=绿色;排除0和1=失联=灰色）")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "指令类型（10-自动触发报警、20-人工触发报警）")
    @TableField("CMD_TYPE")
    private Integer cmdType;

    @ApiModelProperty(value = "监测项目")
    @TableField("Item")
    private String Item;

    @ApiModelProperty(value = "监测值历时")
    @TableField("DT")
    private Double dt;

    @ApiModelProperty(value = "设备列表")
    private List<WarnRwmStatusR> deviceList = new LinkedList<>();

    @ApiModelProperty(value = "视频列表")
    private List<BsnVdstinfo> videoList = new LinkedList<>();

    @ApiModelProperty(value = "预警状态：1=有预警=显示红灯，2=无预警=显示绿灯")
    private String warningStatus;

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public Integer getRwid() {
        return rwid;
    }

    public void setRwid(Integer rwid) {
        this.rwid = rwid;
    }

    public Integer getRwtp() {
        return rwtp;
    }

    public void setRwtp(Integer rwtp) {
        this.rwtp = rwtp;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCmdType() {
        return cmdType;
    }

    public void setCmdType(Integer cmdType) {
        this.cmdType = cmdType;
    }

    public String getItem() {
        return Item;
    }

    public void setItem(String item) {
        Item = item;
    }

    public Double getDt() {
        return dt;
    }

    public void setDt(Double dt) {
        this.dt = dt;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public Double getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(Double plgtd) {
        this.plgtd = plgtd;
    }

    public Double getPlttd() {
        return plttd;
    }

    public void setPlttd(Double plttd) {
        this.plttd = plttd;
    }

    public List<WarnRwmStatusR> getDeviceList() {
        return deviceList;
    }

    public void setDeviceList(List<WarnRwmStatusR> deviceList) {
        this.deviceList = deviceList;
    }

    public List<BsnVdstinfo> getVideoList() {
        return videoList;
    }

    public void setVideoList(List<BsnVdstinfo> videoList) {
        this.videoList = videoList;
    }

    public String getWarningStatus() {
        return warningStatus;
    }

    public void setWarningStatus(String warningStatus) {
        this.warningStatus = warningStatus;
    }
}
