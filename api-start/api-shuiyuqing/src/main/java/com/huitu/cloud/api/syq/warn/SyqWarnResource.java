package com.huitu.cloud.api.syq.warn;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.syq.rain.entity.BsnRainAlarm;
import com.huitu.cloud.api.syq.rain.entity.QueryRainWarn;
import com.huitu.cloud.api.syq.rain.service.RainService;
import com.huitu.cloud.api.syq.river.entity.RiverWarnQo;
import com.huitu.cloud.api.syq.river.entity.RiverWarnVo;
import com.huitu.cloud.api.syq.river.service.RiverService;
import com.huitu.cloud.api.syq.rsvr.entity.QueryRsvrWarn;
import com.huitu.cloud.api.syq.rsvr.entity.RsvrWarn;
import com.huitu.cloud.api.syq.rsvr.service.RsvrService;
import com.huitu.cloud.api.syq.warn.entity.*;
import com.huitu.cloud.api.syq.warn.service.SyqWarnService;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(tags = "告警信息")
@Validated
@RequestMapping("/api/syq/warn")
public class SyqWarnResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "61b6d7d9-5651-4816-8cc5-67757cff16e1";
    }
    @Override
    public String getVersion() {
        return "1.0";
    }
    @Autowired
    private SyqWarnService baseService;
    @Autowired
    private RainService rainService;

    @Autowired
    private RsvrService rsvrService;
    @Autowired
    private RiverService riverService;

    @ApiOperation(value = "查询水库告警信息",notes="查询水库超汛限，超正常高的告警信息")
    @PostMapping(value = "rsvr")
    public ResponseEntity<SuccessResponse<RsvrWarn>> getStRsvrWarn(@RequestBody QueryRsvrWarn baseDao) throws Exception {

        RsvrWarn warn = rsvrService.getStRsvrWarn(baseDao.getAdcd(),baseDao.getStm(),baseDao.getEtm(),baseDao.getStnm(),
                baseDao.getBscd(),baseDao.getStType(),baseDao.getRvType());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", warn));
    }
    @ApiOperation(value = "查询河道告警信息", notes = "查询河道超警戒，超保证的告警信息")
    @PostMapping(value = "river")
    public ResponseEntity<SuccessResponse<RiverWarnVo>> getStRiverWarn(@Validated @RequestBody RiverWarnQo baseDao) throws Exception {
        RiverWarnVo riverWarnVo=riverService.getRiverWarn(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(),
                baseDao.getStnm(),baseDao.getBscd());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", riverWarnVo));
    }
    @ApiOperation(value = "查询雨量告警信息列表", notes = "查询雨量告警信息列表")
    @PostMapping(value = "rain")
    public ResponseEntity<SuccessResponse<IPage<BsnRainAlarm>>> getRainWarn(@RequestBody QueryRainWarn queryRainWarn) throws Exception {
        IPage<BsnRainAlarm> list = rainService.getRainWarn(queryRainWarn.getAdcd(),queryRainWarn.getBscd(),queryRainWarn.getStnm(),queryRainWarn.getWarnType(),queryRainWarn.getStType(),queryRainWarn.getStdt(),queryRainWarn.getPageNum(), queryRainWarn.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "查询河道水库雨量告警统计信息", notes = "查询河道水库雨量告警统计信息（跑马灯）")
    @GetMapping(value = "count")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<WarnCountVo>> getWarnCount(@SqlInjection @RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        WarnCountVo warnCountVo =baseService.getWarnCount(adcd,stm,etm) ;
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", warnCountVo));
    }
    @ApiOperation(value = "查询河道水库雨量告警统计信息以及详情信息", notes = "查询河道水库雨量告警统计信息以及详情信息（跑马灯）")
    @GetMapping(value = "count-and-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stms", value = "(针对app河道修改)开始时间 格式yyyy-MM-dd HH:mm:ss", required = false, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "rsvrStType", value = "水库数据来源类型", required = false, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<WarnInfoVo>> getWarnCountAndInfo(@SqlInjection @RequestParam String adcd, @RequestParam String stm,@RequestParam(required = false) String stms, @RequestParam String etm, @RequestParam String rsvrStType) throws Exception {
        WarnInfoVo warnCountVo =baseService.getWarnCountAndInfo(adcd,stm,stms,etm,rsvrStType) ;
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", warnCountVo));
    }
    @ApiOperation(value = "查询村庄风险预警信息", notes = "查询村庄风险预警信息（跑马灯）")
    @GetMapping(value = "risk-village")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<EwVillageVo>>> getRiskVillage(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<EwVillageVo> villageVoList =baseService.getRiskVillage(adcd,stm,etm) ;
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", villageVoList));
    }
    @ApiOperation(value = "查询河道风险预警信息", notes = "查询河道风险预警信息（跑马灯）")
    @GetMapping(value = "risk-river")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<EwRivelVo>>> getRiskRiver(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<EwRivelVo> ewRivelVoList =baseService.getRiskRiver(adcd,stm,etm) ;
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", ewRivelVoList));
    }
    @ApiOperation(value = "查询水库风险预警信息", notes = "查询水库风险预警信息（跑马灯）")
    @GetMapping(value = "risk-rsvr")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<EwReserVo>>> getRiskRsvr(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<EwReserVo> ewReserVoList =baseService.getRiskRsvr(adcd,stm,etm) ;
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", ewReserVoList));
    }
    @ApiOperation(value = "查询未来一小时风险预警信息", notes = "查询未来一小时风险预警信息（跑马灯）")
    @GetMapping(value = "risk-early-1")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "村庄河道水库类型（分别1，2，3）", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<EarlyWarningVo>>> getRiskEarly1(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String type) throws Exception {
        List<EarlyWarningVo> earlyWarning1List =baseService.getRiskEarly1(adcd,stm,etm,type) ;
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", earlyWarning1List));
    }
    @ApiOperation(value = "查询未来三小时风险预警信息", notes = "查询未来三小时风险预警信息（跑马灯）")
    @GetMapping(value = "risk-early-3")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "村庄河道水库类型（分别1，2，3）", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<EarlyWarningVo>>> getRiskEarly3(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String type) throws Exception {
        List<EarlyWarningVo> earlyWarning3List =baseService.getRiskEarly3(adcd,stm,etm,type) ;
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", earlyWarning3List));
    }
    @ApiOperation(value = "查询未来六小时风险预警信息", notes = "查询未来六小时风险预警信息（跑马灯）")
    @GetMapping(value = "risk-early-6")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "村庄河道水库类型（分别1，2，3）", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<EarlyWarningVo>>> getRiskEarly6(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String type) throws Exception {
        List<EarlyWarningVo> earlyWarning6List =baseService.getRiskEarly6(adcd,stm,etm,type) ;
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", earlyWarning6List));
    }
    @ApiOperation(value = "查询未来一三六小时风险预警信息", notes = "查询未来六小时风险预警信息（跑马灯）")
    @GetMapping(value = "risk-early")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tmType", value = "未来几小时：1：一小时 3：三小时 6：六小时", dataType = "String"),
            @ApiImplicitParam(name = "riskType", value = "风险类型。1：村庄 2：水库 3：河道", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<EarlyWarningVo>>> getRiskEarly(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String tmType, @RequestParam String riskType) throws Exception {
        List<EarlyWarningVo> earlyWarning6List =baseService.getRiskEarly(adcd,stm,etm,tmType,riskType);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", earlyWarning6List));
    }
    @ApiOperation(value = "查询强降雨告警信息列表", notes = "查询强降雨告警信息列表")
    @PostMapping(value = "heavy-rainfall-alarm")
    public ResponseEntity<SuccessResponse<IPage<HeavyRainfallAlarm>>> getHeavyRainfallAlarm(@RequestBody QueryHeavyRainfallAlarm query) throws Exception {
        IPage<HeavyRainfallAlarm> list = rainService.getHeavyRainfallAlarm(query.getAdcd(),query.getPageNum(), query.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "查询强降雨告警通告期数", notes = "查询强降雨告警通告期数")
    @GetMapping(value = "get-heavy-rainfall-notice-no")
    public ResponseEntity<SuccessResponse<Integer>> getHeavyRainfallNoticeNo() throws Exception {
        int hno = baseService.getHeavyRainfallNoticeNo();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", hno));
    }
    @ApiOperation(value = "生成强降雨告警通告",notes="生成强降雨告警通告")
    @PostMapping(value = "create-heavy-rainfall-notice")
    public ResponseEntity<SuccessResponse<Notice>> crezateHeavyRainfallNotice(@RequestBody QueryNotice query) throws Exception {
        Notice result = baseService.crezateHeavyRainfallNotice(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "发送强降雨文件后标记备注",notes="发送强降雨文件后标记备注")
    @PostMapping(value = "sign-heavy-rainfall-notice")
    public ResponseEntity<SuccessResponse<Notice>> signHeavyRainfallNotice(@RequestBody BSN_Heavyrainfallalarm_B heavyrainfallalarmB) throws Exception {
        Boolean flag = baseService.signHeavyRainfallNotice(heavyrainfallalarmB.getStcd(), heavyrainfallalarmB.getReamrk());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }

    @ApiOperation(value = "山洪快报文件生成", notes = "山洪快报文件生成")
    @GetMapping(value = "generate-sh-news")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "orgfilename", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "orgfileurl", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<String>> generateShNews(@SqlInjection @RequestParam String adcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String orgfilename, @RequestParam String orgfileurl) throws Exception {
        baseService.generateShNews(adcd, stm, etm, orgfilename, orgfileurl);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "1"));
    }

}
