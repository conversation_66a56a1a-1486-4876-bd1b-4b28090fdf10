package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectAutoMonitor;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 自动监测站
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectAutoMonitorDao {

    /**
     * 判断是否添加过 自动监测站
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectAutoMonitor> getInspectAutoMonitorList(@Param("adcd") String adcd, @Param("year") String year, @Param("stcd") String stcd);

    /**
     * 添加 自动监测站
     *
     * @param entity
     * @return
     */
    int insertInspectAutoMonitor(BnsInspectAutoMonitor entity);

    /**
     * 修改 自动监测站
     *
     * @param entity
     * @return
     */
    int updateInspectAutoMonitor(BnsInspectAutoMonitor entity);

    /**
     * 删除 自动监测站
     *
     * @return
     */
    int deleteInspectAutoMonitor(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

