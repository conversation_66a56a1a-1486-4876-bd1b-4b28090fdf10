package com.huitu.cloud.api.ewci.rsvr.service.impl;

import com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolume;
import com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolumeRealTime;
import com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolumeBase;
import com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolumeQuery;
import com.huitu.cloud.api.ewci.rsvr.mapper.RsvrMaxInflowVolumeDao;
import com.huitu.cloud.api.ewci.rsvr.service.RsvrMaxInflowVolumeService;
import com.huitu.cloud.util.AdcdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
/**
 * 最大入库洪量计算服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class RsvrMaxInflowVolumeServiceImpl implements RsvrMaxInflowVolumeService {

    private RsvrMaxInflowVolumeDao baseDao;

    @Autowired
    public void setBaseDao(RsvrMaxInflowVolumeDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public List<RsvrMaxInflowVolume> getList(RsvrMaxInflowVolumeQuery query) {
        // 处理政区数据
        int level = AdcdUtil.getAdLevel(query.getAdcd());
        query.setLevel(level);
        query.setAd(query.getAdcd().substring(0, level));

        // 基础信息表  查出基础数据，提取所有有效stcd
        // TODO 2024-08-22 17:39:19 筛选条件还未真实处理
        List<RsvrMaxInflowVolumeBase> baseList = baseDao.getBaseList(query.toQueryParam());
        List<RsvrMaxInflowVolume> TmpRsvrMaxInflowVolumeList = new ArrayList<>();
        // 收集所有有效 stcd 用于去水库实时表查询出库流量列表
        String[] stcds = baseList.stream()
                .map(RsvrMaxInflowVolumeBase::getStcd)  // 获取stcd属性
                .toArray(String[]::new);

        // 查询水库实时表所有所需有效数据
        // TODO 2024-08-22 17:39:26 筛选条件还未真实处理
        List<RsvrMaxInflowVolumeRealTime> RsvrMaxInflowVolumeRealTimes = baseDao.selectRsvrByTimeAndStcds(stcds, query.getStm(), query.getEtm());

        // 根据 stcd 对数据进行分组
        Map<String, List<RsvrMaxInflowVolumeRealTime>> groupRsvrMaxInflowVolumeRealTime = RsvrMaxInflowVolumeRealTimes.stream()
                .collect(Collectors.groupingBy(RsvrMaxInflowVolumeRealTime::getStcd));

        // 开始组装最终返回的List
        groupRsvrMaxInflowVolumeRealTime.forEach((stcd, groupRecordList) -> {
//            System.out.println("stcd: " + stcd);

            RsvrMaxInflowVolume rsvrMaxInflowVolume = new RsvrMaxInflowVolume();
            rsvrMaxInflowVolume = this.calculateMaxInq(groupRecordList, rsvrMaxInflowVolume);

            try {
                List<List<RsvrMaxInflowVolumeRealTime>> chunkList_3 = this.calculateChunks(groupRecordList, 3L);
                List<RsvrMaxInflowVolume> tmpChunkList_3 = new ArrayList<>();
                chunkList_3.forEach(chunkRecord -> {
                    try {
                        tmpChunkList_3.add(this.calculateChunkTotalInq(chunkRecord, 3L, new RsvrMaxInflowVolume()));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                });
                List<List<RsvrMaxInflowVolumeRealTime>> chunkList_7 = this.calculateChunks(groupRecordList, 7L);
                List<RsvrMaxInflowVolume> tmpChunkList_7 = new ArrayList<>();
                chunkList_7.forEach(chunkRecord -> {
                    try {
                        tmpChunkList_7.add(this.calculateChunkTotalInq(chunkRecord, 7L, new RsvrMaxInflowVolume()));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                });
                rsvrMaxInflowVolume = this.calculateDayIntervalMaxInq(tmpChunkList_3, 3L, rsvrMaxInflowVolume);
                rsvrMaxInflowVolume = this.calculateDayIntervalMaxInq(tmpChunkList_7, 7L, rsvrMaxInflowVolume);
                TmpRsvrMaxInflowVolumeList.add(rsvrMaxInflowVolume);

            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        });

        List<RsvrMaxInflowVolume> recodeList = TmpRsvrMaxInflowVolumeList.stream()
                .map(volume -> {
                    Optional<RsvrMaxInflowVolumeBase> baseOpt = baseList.stream()
                            .filter(base -> base.getStcd().equals(volume.getStcd()))
                            .findFirst();
                    baseOpt.ifPresent(base -> {
                        volume.setAdnm(base.getAdnm());
                        volume.setRsvrtp(base.getRsvrtp());
                        volume.setStnm(base.getStnm());
                        volume.setAddvcd(base.getAddvcd());
                    });
                    return volume;
                }).collect(Collectors.toList());

        // 处理错误数据
        List<RsvrMaxInflowVolume> filteredList = recodeList.stream()
                .filter(item -> item.getMaxInq_7() != null && item.getMaxInq_3() != null)
                .sorted(Comparator.comparing(RsvrMaxInflowVolume::getRsvrtp).reversed()
                        .thenComparing(RsvrMaxInflowVolume::getAdnm))
                .collect(Collectors.toList());

        // 填充sortNo字段
        for (int i = 0; i < filteredList.size(); i++) {
            filteredList.get(i).setSortNo(String.valueOf(i + 1));
        }

//        filteredList.forEach(System.out::println);
//        System.out.println("处理完成");
        return filteredList;
    }


    /**
     * 计算最大洪峰与最大洪峰发生时间
     * @param groupRecordList 分组后的list
     * @param rsvrMaxInflowVolume rsvrMaxInflowVolume
     * @return com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolume
     */
    public RsvrMaxInflowVolume calculateMaxInq(List<RsvrMaxInflowVolumeRealTime> groupRecordList, RsvrMaxInflowVolume rsvrMaxInflowVolume) {
        // 分组好的数据，先取出最大洪量的那条数据
        Optional<RsvrMaxInflowVolumeRealTime> maxInqRecord = groupRecordList.stream()
                .max(Comparator.comparing(RsvrMaxInflowVolumeRealTime::getInq));

        rsvrMaxInflowVolume.setStcd(maxInqRecord.get().getStcd());
        rsvrMaxInflowVolume.setMaxInq(maxInqRecord.get().getInq());
        rsvrMaxInflowVolume.setMaxInqTime(maxInqRecord.get().getTm());
        return rsvrMaxInflowVolume;
    }


    /**
     * 数据分块，取出3天、7天的数据列表
     * @param groupRecordList 同一个stcd分组后的数据列表
     * @param dayInterval 时间间隔  3L或者7L
     * @return List<List<RsvrMaxInflowVolumeRealTime>> 内层列表数据是每一个块的内容，比如:
     * [[2024-01-01 08:00:00 ~ 2024-01-04 08:00:00],[2024-01-02 08:00:00 ~ 2024-01-05 08:00:00]]
     */
    public List<List<RsvrMaxInflowVolumeRealTime>> calculateChunks(List<RsvrMaxInflowVolumeRealTime> groupRecordList, Long dayInterval) throws ParseException {
        List<List<RsvrMaxInflowVolumeRealTime>> chunkRecordList = new ArrayList<>();
        // 日期格式化器
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");
        // 遍历记录，从最晚的记录开始向前推算3天
        for (int i = 0; i < groupRecordList.size(); i++) {

            Date earliestDate = sdf.parse(groupRecordList.get(groupRecordList.size() - 1).getTm()); // 获取最早的时间点

            RsvrMaxInflowVolumeRealTime startRecord = groupRecordList.get(i);
            Date startDate = sdf.parse(startRecord.getTm());
            Date targetDate = new Date(startDate.getTime() - dayInterval * 24 * 60 * 60 * 1000); // 计算3天前的目标时间点

            if (targetDate.before(earliestDate)) {
                break; // 如果结束时间早于数据列表中的最早时间，停止遍历
            }
//            System.out.println("开始时间: " + startRecord.getTm());
//            System.out.println("计算的目标时间: " + sdf.format(targetDate));
//
            List<RsvrMaxInflowVolumeRealTime> chunkRecords = findAndInterpolate(groupRecordList, startDate, targetDate, sdf);

            if (chunkRecords.isEmpty()) {
//                System.out.println("无法找到或计算目标时间的数据记录。");
                return new ArrayList<>();
            }
            chunkRecordList.add(chunkRecords);
        }
        return chunkRecordList;
    }


    /**
     * 计算当前块中总洪量
     * @param chunkRecordList 块列表 比如：[2024-01-01 08:00:00 ~ 2024-01-04 08:00:00]
     * @param dayInterval 时间间隔 3L或者7L
     * @param rsvrMaxInflowVolume 传递过来的最终要组装的类
     * @return com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolume
     * @throws ParseException
     */
    private RsvrMaxInflowVolume calculateChunkTotalInq(List<RsvrMaxInflowVolumeRealTime> chunkRecordList, Long dayInterval, RsvrMaxInflowVolume rsvrMaxInflowVolume) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        BigDecimal totalInq = BigDecimal.ZERO;

        for (int i = 0; i < chunkRecordList.size() - 1; i++) {
            RsvrMaxInflowVolumeRealTime record1 = chunkRecordList.get(i);
            RsvrMaxInflowVolumeRealTime record2 = chunkRecordList.get(i + 1);

            // 2-1 计算时间差
            Date date1 = sdf.parse(record1.getTm());
            Date date2 = sdf.parse(record2.getTm());
            long diffInMillis = Math.abs(date1.getTime() - date2.getTime());
            BigDecimal diffInSeconds = new BigDecimal(TimeUnit.MILLISECONDS.toSeconds(diffInMillis));

            // 2-2 计算平均inq
            BigDecimal avgInq = record1.getInq().add(record2.getInq()).divide(new BigDecimal("2"), RoundingMode.HALF_UP);

            // 2-3 计算时间差乘以平均inq
            BigDecimal inqResult = diffInSeconds.multiply(avgInq).divide(new BigDecimal("1000000"), RoundingMode.HALF_UP); // 百万立方

            // 累加inq结果
            totalInq = totalInq.add(inqResult);
        }
        if (dayInterval == 3) {
            rsvrMaxInflowVolume.setStcd((chunkRecordList.get(0).getStcd()));
            rsvrMaxInflowVolume.setStartTime_3(chunkRecordList.get(chunkRecordList.size() - 1).getTm());
            rsvrMaxInflowVolume.setEndTime_3(chunkRecordList.get(0).getTm());
            rsvrMaxInflowVolume.setTotalInq_3(totalInq);
        }
        if (dayInterval == 7) {
            rsvrMaxInflowVolume.setStcd((chunkRecordList.get(0).getStcd()));
            rsvrMaxInflowVolume.setStartTime_7(chunkRecordList.get(chunkRecordList.size() - 1).getTm());
            rsvrMaxInflowVolume.setEndTime_7(chunkRecordList.get(0).getTm());
            rsvrMaxInflowVolume.setTotalInq_7(totalInq);
        }
        return rsvrMaxInflowVolume;
    }


    /**
     * 计算3天或7天最大洪峰，填充最大洪峰和最大洪峰发生时间的时间段
     * @param tmpChunkList 临时的带有 totalInq 属性的列表
     * @param dayInterval 时间间隔 3L或者7L
     * @param rsvrMaxInflowVolume 传递过来的最终要组装的类
     * @return com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolume
     */
    public RsvrMaxInflowVolume calculateDayIntervalMaxInq(List<RsvrMaxInflowVolume> tmpChunkList, Long dayInterval,
                                                                       RsvrMaxInflowVolume rsvrMaxInflowVolume) {
        // 找到totalInq最大的记录
        RsvrMaxInflowVolume maxRecord = tmpChunkList.stream()
                .max((r1, r2) -> {
                    if (dayInterval == 3) {
                        return r1.getTotalInq_3().compareTo(r2.getTotalInq_3());
                    }
                    if (dayInterval == 7) {
                        return r1.getTotalInq_7().compareTo(r2.getTotalInq_7());
                    }
                    return 0;
                }).orElse(null);

        // 如果maxRecord不为空，创建或更新结果Map
        if (maxRecord != null) {
            if (dayInterval == 3) {
                rsvrMaxInflowVolume.setStartTime_3(maxRecord.getStartTime_3());
                rsvrMaxInflowVolume.setEndTime_3(maxRecord.getEndTime_3());
                rsvrMaxInflowVolume.setMaxInq_3(maxRecord.getTotalInq_3());
                rsvrMaxInflowVolume.setTotalInq_3(BigDecimal.ZERO);
            } else if (dayInterval == 7) {
                rsvrMaxInflowVolume.setStartTime_7(maxRecord.getStartTime_7());
                rsvrMaxInflowVolume.setEndTime_7(maxRecord.getEndTime_7());
                rsvrMaxInflowVolume.setMaxInq_7(maxRecord.getTotalInq_7());
                rsvrMaxInflowVolume.setTotalInq_7(BigDecimal.ZERO);
            }
        }
        return rsvrMaxInflowVolume;
    }


    /**
     * 查找开始时间计算差值，并添加到结果列表
     * @param groupRecordList 分组后的列表数据
     * @param startDate 开始时间，注意：是从后往前，比如开始时间是 2024-01-04 08:00:00
     * @param targetDate 目标时间，注意这个时间应该是小于 startDate  比如： 2024-01-01 08:00:00
     * @param sdf 时间格式器
     * @return List<RsvrMaxInflowVolumeRealTime> 分块后的数据列表
     * @throws ParseException
     */
    private static List<RsvrMaxInflowVolumeRealTime> findAndInterpolate(List<RsvrMaxInflowVolumeRealTime> groupRecordList, Date startDate, Date targetDate, SimpleDateFormat sdf) throws ParseException {
        List<RsvrMaxInflowVolumeRealTime> resultList = new ArrayList<>();

        for (RsvrMaxInflowVolumeRealTime record : groupRecordList) {
            Date recordDate = sdf.parse(record.getTm());
            if (!recordDate.after(startDate) && !recordDate.before(targetDate)) {
                resultList.add(record);
            }
        }

        // 如果目标时间不存在记录，并插值
        if (resultList.stream().noneMatch(r -> r.getTm().equals(sdf.format(targetDate)))) {
            RsvrMaxInflowVolumeRealTime interpolatedRecord = interpolateRecord(groupRecordList, targetDate, sdf);
            if (interpolatedRecord != null) {
                resultList.add(interpolatedRecord);
            }
        }
        return resultList;
    }


    /**
     * 计算差值
     * @param groupRecordList 分组后的列表数据
     * @param targetDate 目标时间
     * @param sdf 时间格式器
     * @return com.huitu.cloud.api.ewci.rsvr.entity.RsvrMaxInflowVolumeRealTime 实时表有效数据
     * @throws ParseException
     */
    private static RsvrMaxInflowVolumeRealTime interpolateRecord(List<RsvrMaxInflowVolumeRealTime> groupRecordList, Date targetDate, SimpleDateFormat sdf) throws ParseException {
        RsvrMaxInflowVolumeRealTime previousRecord = null;
        RsvrMaxInflowVolumeRealTime nextRecord = null;

        // 寻找前后最接近的两条记录
        for (RsvrMaxInflowVolumeRealTime record : groupRecordList) {
            Date recordDate = sdf.parse(record.getTm());

            // 寻找目标日期之前的最接近的记录
            if (recordDate.before(targetDate)) {
                if (previousRecord == null || recordDate.after(sdf.parse(previousRecord.getTm()))) {
                    previousRecord = record;
                }
            }

            // 寻找目标日期之后的最接近的记录
            if (recordDate.after(targetDate)) {
                if (nextRecord == null || recordDate.before(sdf.parse(nextRecord.getTm()))) {
                    nextRecord = record;
                }
            }
        }
        if (previousRecord != null && nextRecord != null) {
            long timeDifference = sdf.parse(nextRecord.getTm()).getTime() - sdf.parse(previousRecord.getTm()).getTime();
            long targetDifference = targetDate.getTime() - sdf.parse(previousRecord.getTm()).getTime();

            // 线性插值计算inq
            BigDecimal interpolatedInq = previousRecord.getInq().add(
                    (nextRecord.getInq().subtract(previousRecord.getInq()))
                            .multiply(new BigDecimal(targetDifference))
                            .divide(new BigDecimal(timeDifference), BigDecimal.ROUND_HALF_UP)
            );
//            System.out.println("插值计算结果: " + interpolatedInq);
            return new RsvrMaxInflowVolumeRealTime(previousRecord.getStcd(), sdf.format(targetDate), interpolatedInq);
        }
        return null;
    }


}
