package com.huitu.cloud.api.ewci.hydrology.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnStationResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.query.HyWarnIndexQuery;
import com.huitu.cloud.api.ewci.hydrology.entity.request.HyWarnIndexRequest;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnIndexResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnUnitResponse;
import com.huitu.cloud.api.ewci.hydrology.mapper.HyWarnIndexDao;
import com.huitu.cloud.api.ewci.hydrology.mapper.HyWarnUnitDao;
import com.huitu.cloud.api.ewci.hydrology.service.HyWarnIndexService;
import com.huitu.cloud.util.AdcdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 洪水预警指标服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class HyWarnIndexServiceImpl implements HyWarnIndexService {

    private HyWarnUnitDao unitDao;
    private HyWarnIndexDao indexDao;

    @Autowired
    public void setUnitDao(HyWarnUnitDao unitDao) {
        this.unitDao = unitDao;
    }

    @Autowired
    public void setIndexDao(HyWarnIndexDao indexDao) {
        this.indexDao = indexDao;
    }

    @Override
    public List<HyWarnStationResponse> getStationList(String adcd, boolean include) {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", adcd);
        params.put("level", AdcdUtil.getAdLevel(adcd));
        params.put("include", include);
        return indexDao.getStationList(params);
    }

    @Override
    public List<HyWarnUnitResponse> getUnitList() {
        return unitDao.getUnitList();
    }

    @Override
    public IPage<HyWarnIndexResponse> getPageList(HyWarnIndexQuery query) {
        return indexDao.getPageList(query.toPageParam(), query.toQueryParam());
    }

    @Override
    public int save(HyWarnIndexRequest request) {
        return indexDao.save(request);
    }

    @Override
    public int delete(String stcd) {
        return indexDao.delete(stcd);
    }

    @Override
    public int updateUseFlag(String stcd, String usfl) {
        return indexDao.updateUseFlag(stcd, usfl);
    }
}
