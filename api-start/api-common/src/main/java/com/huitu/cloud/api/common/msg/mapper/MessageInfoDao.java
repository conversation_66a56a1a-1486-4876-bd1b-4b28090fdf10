package com.huitu.cloud.api.common.msg.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.common.msg.entity.BnsMessageR;
import com.huitu.cloud.api.common.msg.entity.FailMessage;
import com.huitu.cloud.api.common.msg.entity.MessageInfo;
import com.huitu.cloud.api.common.msg.entity.MessageList;
import com.huitu.cloud.api.common.msg.entity.MessageSend;
import com.huitu.cloud.api.common.msg.entity.MessageStatistics;
import com.huitu.cloud.api.common.msg.entity.MessageVo;
import com.huitu.cloud.api.common.msg.entity.WarnMessage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 预警消息记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public interface MessageInfoDao {
    /**
     * 根据政区查询最大短信编码
     *
     * @param adcd 政区编码
     * @return
     */
    int selectMaxMsgId(@Param("adcd") String adcd);

    /**
     * 根据政区查询最大的短信接收编码
     *
     * @param adcd 政区
     * @return
     */
    int selectMaxSmsId(@Param("adcd") String adcd);

    /**
     * 插入短信记录表
     *
     * @param item
     * @return
     */
    int insertMsgInfo(MessageInfo item);

    /**
     * 批量插入短信发送记录表
     *
     * @param list
     * @return
     */
    int bathInsertMsgSend(@Param("msg") List<MessageSend> list);

    /**
     * 短信发送记录情况
     *
     * @param param
     * @return
     */
    List<MessageVo> selectWarnMsg(Map<String, Object> param);

    /**
     * 预警短信政区统计
     *
     * @param param
     * @return
     */
    List<MessageStatistics> getMsgStatistics(Map<String, Object> param);

    /**
     * 短信发送记录情况列表
     *
     * @param param
     * @param page
     * @return
     */
    IPage<MessageVo> getWarnMsgInfoByPage(Page page, @Param("map") Map<String, Object> param);

    /**
     * 分页获取预警短信发送记录列表
     *
     * @param page
     * @param param
     * @return
     **/
    IPage<WarnMessage> getWarnMessagePage(Page page, @Param("map") Map<String, Object> param);

    /**
     * 短信信息列表
     *
     * @param param
     * @param page
     * @return
     */
    IPage<MessageList> getWarnMsgInfoList(Page page, @Param("map") Map<String, Object> param);


    /**
     * 获取短信信息列表中的失败短信记录
     *
     * @param adcd  政区编码
     * @param msgid 短信编号
     * @return
     */
    List<FailMessage> getFailMessage(@Param("adcd") String adcd, @Param("msgid") String msgid);

    /**
     * 创建临时表
     */
    @Update({"IF OBJECT_ID(N'tempdb..[${tableName}]') is not null drop  table  ${tableName}  select code,smsid,adcd into ${tableName} from MESSAGESEND_R where 1=2"})
    void createTemoraryTable(@Param("tableName") String tableName);

    /**
     * 批量插入发送失败短信进入临时表
     *
     * @param list      需要更新状态的
     * @param tableName 临时表名
     * @return
     */
    void bathInsertFailMassage(@Param("list") List<FailMessage> list, @Param("tableName") String tableName);

    /**
     * 更新短信发送记录表的数据
     *
     * @param tableName 临时表名
     * @return
     */
    void updateSendResult(@Param("tableName") String tableName);

    /**
     * 插入通用消息表
     *
     * @param item
     * @return
     */
    int insertCommonMsg(BnsMessageR item);

    List<BnsMessageR> getCommonMsg(Map<String, Object> param);

    void updateCommonMsgBatches(@Param("ids") List<String> ids);

    List<MessageVo> getMobilePerTp();
}
