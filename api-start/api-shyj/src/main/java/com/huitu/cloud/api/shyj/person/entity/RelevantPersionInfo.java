package com.huitu.cloud.api.shyj.person.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@ApiModel(value = "人员关联信息", description = "责任人关联信息")
public class RelevantPersionInfo extends Person{
    @ApiModelProperty(value = "部门名称")
    private String deptNm;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    public String getDeptNm() {
        return deptNm;
    }

    public void setDeptNm(String deptNm) {
        this.deptNm = deptNm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }
}
