package com.huitu.cloud.api.ewci.monitor.service.impl;

import com.huitu.cloud.api.ewci.monitor.entity.RainData;
import com.huitu.cloud.api.ewci.monitor.entity.RiskLocation;
import com.huitu.cloud.api.ewci.monitor.entity.RiskQuery;
import com.huitu.cloud.api.ewci.monitor.mapper.RiskDao;
import com.huitu.cloud.api.ewci.monitor.service.RiskService;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 风险 周边降雨 实现类
 *
 * <AUTHOR>
 */
@Service
public class RiskServiceImpl implements RiskService {

    private RiskDao baseDao;


    public RiskServiceImpl(RiskDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public RiskLocation getLocation(String stcd) {
        return baseDao.getLocation(stcd);
    }


    @Override
    public List<RainData> getRainfallList(RiskQuery qo) {
        List<RainData> list = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        params.put("stm", qo.getStm());
        params.put("etm", qo.getEtm());
        params.put("fstm", qo.getEtm());
        params.put("fetm1", DateUtils.addHours(qo.getEtm(), 1));
        params.put("fetm3", DateUtils.addHours(qo.getEtm(), 3));
        params.put("fetm6", DateUtils.addHours(qo.getEtm(), 6));
        if ("1".equals(qo.getType())) {
            params.put("adcd", qo.getAdcd());
            params.put("level", 12);
            list = baseDao.getRainfallByAd(params);
        } else if ("2".equals(qo.getType())) {
            params.put("adcd", qo.getAdcd());
            params.put("level", 12);
            list = baseDao.getRainfallByWs(params);
        } else if ("3".equals(qo.getType())) {
            params.put("lgtd", qo.getLgtd());
            params.put("lttd", qo.getLttd());
            params.put("range", qo.getRange());
            list = baseDao.getRainfallByRange(params);
        }
        return list;
    }
}
