package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@TableName("WarnRealData_R")
@ApiModel(value="WarnrealdataR对象", description="")
public class WarnrealdataR extends Model<WarnrealdataR> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "监测项目")
    @TableField("Item")
    private String Item;

    @ApiModelProperty(value = "监测值")
    @TableField("Value")
    private Double Value;

    @ApiModelProperty(value = "监测值单位")
    @TableField("Unit")
    private String Unit;

    @ApiModelProperty(value = "监测值历时")
    @TableField("DT")
    private Double dt;

    @ApiModelProperty(value = "监测值说明")
    private String remark;

    @TableField("Time")
    private LocalDateTime Time;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getItem() {
        return Item;
    }

    public void setItem(String Item) {
        this.Item = Item;
    }

    public Double getValue() {
        return Value;
    }

    public void setValue(Double Value) {
        this.Value = Value;
    }

    public String getUnit() {
        return Unit;
    }

    public void setUnit(String Unit) {
        this.Unit = Unit;
    }

    public Double getDt() {
        return dt;
    }

    public void setDt(Double dt) {
        this.dt = dt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getTime() {
        return Time;
    }

    public void setTime(LocalDateTime Time) {
        this.Time = Time;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "WarnrealdataR{" +
        "stcd=" + stcd +
        ", Item=" + Item +
        ", Value=" + Value +
        ", Unit=" + Unit +
        ", dt=" + dt +
        ", remark=" + remark +
        ", Time=" + Time +
        "}";
    }
}
