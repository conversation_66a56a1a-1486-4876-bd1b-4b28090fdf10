package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value="预警等级统计信息对象", description="预警等级统计信息对象")
public class WarnGradeStatistics implements Serializable {

    @ApiModelProperty(value = " 预警等级名称")
    private String warnGradeName;

    @ApiModelProperty(value = " 预警等级数量")
    private int warnGradeCount;

    public String getWarnGradeName() {
        return warnGradeName;
    }

    public void setWarnGradeName(String warnGradeName) {
        this.warnGradeName = warnGradeName;
    }

    public int getWarnGradeCount() {
        return warnGradeCount;
    }

    public void setWarnGradeCount(int warnGradeCount) {
        this.warnGradeCount = warnGradeCount;
    }
}
