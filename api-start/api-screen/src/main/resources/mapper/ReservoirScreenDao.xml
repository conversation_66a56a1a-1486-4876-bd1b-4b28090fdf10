<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.screen.fhjz.mapper.ReservoirDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getRsvrLatestData"  resultType="com.huitu.cloud.api.screen.fhjz.entity.ReservoirVo">
        with att_res_base_t as(
        select t1.res_code,t1.res_name,t1.eng_scal,t3.adnm,t3.adcd
        from ATT_RES_BASE t1 WITH(NOLOCK)
        inner join REL_RES_AD t2 WITH(NOLOCK) on t1.res_code=t2.res_code
        inner join MDT_ADCDINFO_B t3 WITH(NOLOCK) on t2.ad_code=t3.adcd
        <where>
            <if test="engScal!=null and engScal!=''">
               and CHARINDEX(t1.eng_scal,#{engScal}) > 0
            </if>
            <if test="resName!=null and resName!=''">
               and t1.res_name like '%' + #{resName} + '%'
            </if>
        </where>
        ),
        cz_t as (
        select bb.objcd,o.res_code,sb.STADTP
        from BSN_OBJONLY_B bb WITH(NOLOCK)
        inner join(
        select bo.objid,sk.res_code
        from att_res_base_t sk WITH(NOLOCK)
        inner join (select * from BSN_OBJONLY_B WITH(NOLOCK) where objtp = '6')  bo on sk.RES_CODE = bo.objcd
        ) o on bb.objid=o.objid
        inner join BSN_STADTP_B sb WITH(NOLOCK) on bb.objcd=sb.STCD
        where bb.objtp='1'
        ),
        stcdtm as (
        select stcd,max(tm) tm from ST_RSVR_R  WITH(NOLOCK)
        <where>
            <if test="stm!=null and stm!=''">
               and tm &gt;= #{stm}
            </if>
            <if test="etm!=null and etm!=''">
                and tm &lt;= #{etm}
            </if>
        </where>
        group by stcd
        ),
        rsvr as (
        select a.stcd,a.TM,
        a.rz,
        a.RWPTN,
        case when a.rwptn = 4 then convert(varchar(30),convert(decimal(18,2),a.rz)) + '↓'
        when a.rwptn = 5 then convert(varchar(30),convert(decimal(18,2),a.rz)) + '↑'
        when a.rwptn = 6 then convert(varchar(30),convert(decimal(18,2),a.rz)) + '-'
        else convert(varchar(30),convert(decimal(18,2),a.rz)) end RZ_TXT
        from ST_RSVR_R a WITH(NOLOCK) inner join stcdtm b WITH(NOLOCK) on a.tm=b.tm and a.stcd=b.stcd and a.rz>0
        ),
        rg_rsvr as (
        select
        t1.res_code,
        t3.rz,
        t3.rz_txt
        from att_res_base_t t1 with(nolock)
        inner join cz_t t2 with(nolock) on t1.res_code = t2.res_code
        inner join rsvr t3 with(nolock) on t3.STCD = t2.objcd
        where t2.stadtp in ('1','4')
        ),
        zd_rsvr as (
        select
        t1.res_code,
        t3.rz,
        t3.rz_txt
        from att_res_base_t t1 with(nolock)
        inner join cz_t t2 with(nolock) on t1.res_code = t2.res_code
        inner join rsvr t3 with(nolock) on t3.STCD = t2.objcd
        where t2.stadtp in ('2')
        ),
        rg_rsvr_g as (
        select t1.res_code,max(t1.rz) rz,
        rz_txt = (STUFF((SELECT ' ' + t2.rz_txt FROM rg_rsvr t2 WHERE t2.res_code = t1.res_code FOR xml path('')),1,1,''))
        from rg_rsvr t1
        group by t1.res_code
        ),
        zd_rsvr_g as (
        select t1.res_code,max(t1.rz) rz,
        rz_txt = (STUFF((SELECT ' ' + t2.rz_txt FROM zd_rsvr t2 WHERE t2.res_code = t1.res_code FOR xml path('')),1,1,''))
        from zd_rsvr t1
        group by t1.res_code
        ),
        RSVRFSR1 as (
        select F.STCD, F.FSLTDZ, F.FSLTDW, F.MODITIME from stcdtm  a , ST_RSVRFSR_B  F WITH(NOLOCK)
        where  F.STCD = A.STCD AND CAST ( SUBSTRING(CONVERT(VARCHAR,A.TM,112),5,8) AS int ) between CAST (BGMD AS int)	AND CAST (EDMD AS int)
        ),
        RSVRFSR2 as (
        select stcd,FSLTDZ,FSLTDW,MODITIME from ( select stcd,FSLTDZ,FSLTDW,MODITIME ,ROW_NUMBER() over(partition by stcd order by MODITIME desc) rn from RSVRFSR1 )t where rn=1
        ),
        RSVRFSR_g as (
        select t1.res_code,
        convert(decimal(18,2),max(t2.FSLTDZ)) FSLTDZ,
        max(t2.FSLTDW) FSLTDW
        from att_res_base_t t1  with(nolock)
        inner join cz_t t3  with(nolock) on t3.res_code = t1.res_code
        inner join RSVRFSR2 t2  with(nolock) on t3.objcd=t2.stcd
        group by t1.res_code
        )
        select * from (
        select t1.res_code,t1.res_name,t1.eng_scal,t1.adnm,t1.adcd,t5.MAIN_SORT_NO,
        t2.rz_txt rg_rz,
        t3.rz_txt zd_rz,
        case when t2.rz > t4.FSLTDZ or t3.rz >  t4.FSLTDZ  then 1 else 0 end cxx,
        convert(varchar(30),t4.FSLTDZ) fsltdz,
        convert(varchar(30),t4.FSLTDW) fsltdw
        from att_res_base_t t1  with(nolock)
        left join rg_rsvr_g t2 with(nolock) on t1.res_code=t2.res_code
        left join zd_rsvr_g t3 with(nolock) on t1.res_code=t3.res_code
        left join RSVRFSR_g t4  with(nolock) on t1.res_code=t4.res_code
        left join BSN_RSVRINFOEX_B t5 with(nolock)  on t1.res_code=t5.res_code
        ) t
        order by
        <if test="cxxOrder">
            t.cxx desc,
        </if>
        isnull(t.MAIN_SORT_NO,99) asc,t.eng_scal asc,left(t.adcd,6) asc
  </select>

    <select id="detailList" resultType="com.huitu.cloud.api.screen.fhjz.entity.RsvrDetailVo">
        with att_res_base_t as(
        select
            t1.res_code,t1.res_name,t1.eng_scal,t3.adnm,t3.adcd,t1.WAT_SHED_AREA,
            convert(decimal(18,4),t1.TOT_CAP/10000)   TOT_CAP,
            convert(decimal(18,2),t1.NORM_WAT_LEV) NORM_WAT_LEV,
            t4.CHFLST,
            convert(decimal(18,2),t4.CHFLLV) CHFLLV,
            t4.DSFLST,
            convert(decimal(18,2),t4.DSFLLV) DSFLLV,
            convert(decimal(18,2),t5.UPH) UPH,
            t6.HMERFL,
            convert(varchar(20),t6.HMERFLT,23) HMERFLT,
            t6.MXEXRSFL,
            convert(varchar(20),t6.MERFTM,23) MERFTM,
            convert(decimal(18,2),t6.HSHGWTLV) HSHGWTLV,
            convert(varchar(20),t6.HHWLTM,23) HHWLTM,
            t8.DAM_TYPE_STR, -- 主坝坝型
            convert(decimal(18,2),t8.DAM_MAX_HEIG) DAM_MAX_HEIG, -- 最大坝高
            convert(decimal(18,2),t9.DMSZLEN) DMSZLEN, -- 主坝长
            convert(decimal(18,2),t9.DMTPEL) DMTPEL -- 坝顶高程
        from ATT_RES_BASE t1 WITH(NOLOCK)
        inner join REL_RES_AD t2 WITH(NOLOCK) on t1.res_code=t2.res_code
        inner join MDT_ADCDINFO_B t3 WITH(NOLOCK) on t2.ad_code=t3.adcd
        left join FHGC_RSPP t4 WITH(NOLOCK) ON t1.res_code=t4.res_code
        left join BSN_RSVRINFOEX_B t5 WITH(NOLOCK) ON t2.res_code=t5.res_code
        left join FHGC_RRHNT t6 WITH(NOLOCK) ON t2.res_code=t6.res_code
        left join REL_DAM_RES t7  WITH(NOLOCK) on t2.res_code=t7.res_code
        left join ATT_DAM_BASE t8 WITH(NOLOCK) on t7.DAM_CODE=t8.DAM_CODE AND t8.IF_MAIN_DAM='1'
        left join FHGC_DAM t9 WITH(NOLOCK) on t7.DAM_CODE=t9.DAM_CODE
        <where>
            <if test="engScal!=null and engScal!=''">
                and CHARINDEX(t1.eng_scal,#{engScal}) > 0
            </if>
            <if test="adcd!=null and adcd!=''">
                and left(t3.adcd,#{adLevl}) = left(#{adcd},#{adLevl})
            </if>
            <if test="resName!=null and resName!=''">
                and t1.res_name like '%' + #{resName} + '%'
            </if>
        </where>
        ),
        cz_t as (
        select bb.objcd,o.res_code,sb.STADTP
        from BSN_OBJONLY_B bb WITH(NOLOCK)
        inner join(
        select bo.objid,sk.res_code
        from att_res_base_t sk WITH(NOLOCK)
        inner join (select * from BSN_OBJONLY_B WITH(NOLOCK) where objtp = '6')  bo on sk.RES_CODE = bo.objcd
        ) o on bb.objid=o.objid
        inner join BSN_STADTP_B sb WITH(NOLOCK) on bb.objcd=sb.STCD
        where bb.objtp='1'
        ),
        stcdtm as (
        select stcd,max(tm) tm from ST_RSVR_R  WITH(NOLOCK)
        <where>
            <if test="stm!=null and stm!=''">
                and tm &gt;= #{stm}
            </if>
            <if test="etm!=null and etm!=''">
                and tm &lt;= #{etm}
            </if>
        </where>
        group by stcd
        ),
        RSVRFSR1 as (
        select F.STCD,F.BGMD,F.EDMD,F.FSLTDZ,F.FSLTDW,F.MODITIME from stcdtm  a , ST_RSVRFSR_B  F WITH(NOLOCK)
        where  F.STCD = A.STCD AND CAST ( SUBSTRING(CONVERT(VARCHAR,A.TM,112),5,8) AS int ) between CAST (BGMD AS int)	AND CAST (EDMD AS int)
        ),
        RSVRFSR2 as (
        select stcd,BGMD,EDMD,FSLTDZ,FSLTDW,MODITIME from ( select stcd,BGMD,EDMD,FSLTDZ,FSLTDW,MODITIME ,ROW_NUMBER() over(partition by stcd order by MODITIME desc) rn from RSVRFSR1 )t where rn=1
        ),
        RSVRFSR_g as (
        select t1.res_code,
        min(t2.BGMD) BGMD,
        max(t2.EDMD) EDMD,
        case when min(t2.BGMD) is not null and max(t2.EDMD) is not null then min(t2.BGMD) + '-' + max(t2.EDMD)
        else '' end date_range,
        convert(decimal(18,2),max(t2.FSLTDZ)) FSLTDZ,
        convert(decimal(18,4),max(t2.FSLTDW)/10000) FSLTDW
        from att_res_base_t t1  with(nolock)
        inner join cz_t t3  with(nolock) on t3.res_code = t1.res_code
        inner join RSVRFSR2 t2  with(nolock) on t3.objcd=t2.stcd
        group by t1.res_code
        ),
        river_t as(
        select t1.res_code,
        t3.RV_NAME
        from att_res_base_t t1
        left join REL_RES_RV t2 on t1.RES_CODE = t2.RES_CODE
        left join ATT_RV_BASE t3 on t2.RV_CODE = t3.RV_CODE
        group by t1.RES_CODE,t3.RV_NAME
        ),
        river_t_g as (
        select t1.res_code,
        RV_NAME = (STUFF((SELECT ' ' + t2.RV_NAME FROM river_t t2 WHERE t2.res_code = t1.res_code FOR xml path('')),1,1,','))
        from river_t t1
        group by t1.res_code
        )
        select t1.*,t2.*,t3.RV_NAME,t4.FH_PROTECT_OBJ PROTECT_OBJ
        from att_res_base_t t1 with(nolock)
        left join RSVRFSR_g t2 with(nolock) on t1.res_code=t2.res_code
        left join river_t_g t3 with(nolock) on t1.res_code=t3.res_code
        left join BSN_RSVRINFOEX_B t4 with(nolock) on t1.res_code=t4.res_code
        order by isnull(t4.MAIN_SORT_NO,99) asc,t1.eng_scal asc,left(t1.adcd,6) asc
    </select>

   <select id="querySkStationList"  resultType="map">
       with att_res_base_t as(
       select t1.res_code,t1.eng_scal
       from ATT_RES_BASE t1 WITH(NOLOCK)
       where CHARINDEX(t1.eng_scal,#{engScal}) > 0
       ),
       cz_t as (
       select bb.objcd,o.res_code,o.eng_scal
       from BSN_OBJONLY_B bb WITH(NOLOCK)
       inner join(
       select bo.objid,sk.res_code,sk.eng_scal
       from att_res_base_t sk WITH(NOLOCK)
       inner join (select * from BSN_OBJONLY_B WITH(NOLOCK) where objtp = '6')  bo on sk.RES_CODE = bo.objcd
       ) o on bb.objid=o.objid
       inner join BSN_STADTP_B sb WITH(NOLOCK) on bb.objcd=sb.STCD
       where bb.objtp='1'
       ),
       stcdtm as (
       select stcd,max(tm) tm from ST_RSVR_R  WITH(NOLOCK)
       where tm &gt;= #{stm} and tm &lt;= #{etm}
       group by stcd
       ),
       rsvr as (
       select
       a.stcd,
       a.TM,
       a.rz,
       a.RWPTN
       from ST_RSVR_R a WITH(NOLOCK) inner join stcdtm b WITH(NOLOCK) on a.tm=b.tm and a.stcd=b.stcd and a.rz>0
       ),
       RSVRFSR1 as (
       select F.STCD, F.FSLTDZ, F.FSLTDW, F.MODITIME from stcdtm  a , ST_RSVRFSR_B  F WITH(NOLOCK)
       where  F.STCD = A.STCD AND CAST ( SUBSTRING(CONVERT(VARCHAR,A.TM,112),5,8) AS int ) between CAST (BGMD AS int)	AND CAST (EDMD AS int)
       ),
       RSVRFSR2 as (
       select stcd,FSLTDZ,FSLTDW,MODITIME from ( select stcd,FSLTDZ,FSLTDW,MODITIME ,ROW_NUMBER() over(partition by stcd order by MODITIME desc) rn from RSVRFSR1 )t where rn=1
       )
       select t2.RZ,t2.TM,t2.RWPTN,t3.STCD,t3.STNM,t4.PLGTD LGTD,t4.PLTTD LTTD,t1.ENG_SCAL,t5.ADCD,
              case when t2.rz > t6.FSLTDZ then 1 else 0 end cxx
       from cz_t t1
       inner join rsvr t2 with(nolock) on t1.objcd = t2.stcd
       inner join ST_STBPRP_B t3 with(nolock) on t3.stcd = t1.objcd
       left join BSN_STADTP_B t4 with(nolock) on t3.stcd = t4.stcd
       left join BSN_STBPRP_V t5 with(nolock) on t3.stcd = t5.stcd
       left join RSVRFSR2 t6  with(nolock) on t3.stcd=t6.stcd
       <where>
           <if test="resCode!=null and resCode!=''">
               and t1.res_code = #{resCode}
           </if>
       </where>
   </select>

    <select id="querySkAdcd" resultType="map">
        select t1.res_code,t3.adnm,t3.adcd,t1.res_name
        from ATT_RES_BASE t1 WITH(NOLOCK)
        inner join REL_RES_AD t2 WITH(NOLOCK) on t1.res_code=t2.res_code
            inner join MDT_ADCDINFO_B t3 WITH(NOLOCK) on t2.ad_code=t3.adcd
        where t1.res_code=#{resCode}
    </select>

    <select id="statisticRsvrDetail" resultType="map">
        with t_res as (
        select t1.res_code,t4.adnm,
                     case when LEFT(t3.adcd,6) = '220581' then LEFT(t3.adcd,6) else LEFT(t3.adcd,#{lowLevel}) end adcd ,
               case when t1.eng_scal in ('1','2') then 1 else 0 end LEVEL1,
                     case when t1.eng_scal = '3' then 1 else 0 end LEVEL2,
                     case when t1.eng_scal = '4' then 1 else 0 end LEVEL3,
                     case when t1.eng_scal = '5' then 1 else 0 end LEVEL4
        from ATT_RES_BASE t1 WITH(NOLOCK)
        inner join REL_RES_AD t2 WITH(NOLOCK) on t1.res_code=t2.res_code
        inner join MDT_ADCDINFO_B t3 WITH(NOLOCK) on t2.ad_code=t3.adcd
        inner join MDT_ADCDINFO_B t4 WITH(NOLOCK) on t4.adcd = left(t3.adcd,#{lowLevel}) + #{suffix}
        where t1.eng_scal is not null
        and LEFT(t3.adcd,#{level})= left(#{adcd}, #{level})
        )
        select t.adcd,
                    case when t.adcd = '220581' then '梅河口市' else t.adnm end adnm,
                  SUM(t.LEVEL1) LEVEL1,
                    SUM(t.LEVEL2) LEVEL2,
                    SUM(t.LEVEL3) LEVEL3,
                    SUM(t.LEVEL4) LEVEL4,
                    SUM(t.LEVEL1) + SUM(t.LEVEL2) + SUM(t.LEVEL3) + SUM(t.LEVEL4) TOTAL
        from t_res t
        group by t.adcd,t.adnm
        union all
        select left(#{adcd}, #{level}),'SUM',
                  SUM(t.LEVEL1) LEVEL1,
                    SUM(t.LEVEL2) LEVEL2,
                    SUM(t.LEVEL3) LEVEL3,
                    SUM(t.LEVEL4) LEVEL4,
                    SUM(t.LEVEL1) + SUM(t.LEVEL2) + SUM(t.LEVEL3) + SUM(t.LEVEL4) TOTAL
        from t_res t
    </select>
</mapper>
