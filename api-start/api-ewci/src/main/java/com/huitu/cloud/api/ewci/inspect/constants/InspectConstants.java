package com.huitu.cloud.api.ewci.inspect.constants;

/**
 * 防汛检查常量类
 */
public class InspectConstants {

    public static final String PERSON_IMP = "personImp";  // 责任人落实
    public static final String PLAN_REVISE = "planRevise";  // 方案/预案修订
    public static final String RSVR_RUN = "rsvrRun";  // 水库(水电站)运行
    public static final String ILL_RSVR = "illRsvr";  // 病险水库
    public static final String RIVER_DIKE = "riverDike";  // 河道及堤防运行
    public static final String CON_EN = "conEn";  // 在建工程
    public static final String WAGA_RUN = "wagaRun";  // 水闸工程运行
    public static final String AUTO_MONITOR = "autoMonitor";  // 自动监测站/预案修订
    public static final String SIMPLE_RAIN = "simpleRain";  // 简易雨量站
    public static final String WARN_BROAD = "warnBroad";  // 无线预警广播站(水电站)运行
    public static final String COUNTY_PLATFORM = "countyPlatform";  // 县级山洪灾害防御平台
    public static final String MAIN_EXP = "mainExp";  // 运行维护经费
    public static final String SHGC_CONS = "shgcCons";  // 水毁工程修复建设
    public static final String AQDX_CONS = "aqdxCons";  // 安全度汛工程建设
    public static final String EMG_COMM_DEV = "emgCommDev";  // 应急通信设备

}
