package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 防御工作方案
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@TableName("BSN_DEFENSE_WORK_PLAN_B")
@ApiModel(value="BsnDefenseWorkPlanB对象", description="防御工作方案")
public class BsnDefenseWorkPlanB extends Model<BsnDefenseWorkPlanB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "防御工作方案ID")
    @TableId(value = "DWP_ID", type = IdType.NONE)
    private String dwpId;

    @ApiModelProperty(value = "防御工作方案名称")
    @TableField("DWP_NAME")
    private String dwpName;

    @ApiModelProperty(value = "文件名称")
    @TableField("FILE_NAME")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    @TableField("FILE_PATH")
    private String filePath;

    @ApiModelProperty(value = "日期")
    @TableField("DWP_TM")
    private LocalDate dwpTm;

    @ApiModelProperty(value = "年度专报期数")
    @TableField("Y_REPORT_NO")
    private Integer yReportNo;

    @ApiModelProperty(value = "总期数")
    @TableField("TOTAL_NO")
    private Integer totalNo;

    @ApiModelProperty(value = "年份")
    @TableField("REPORT_YEAR")
    private String reportYear;

    @ApiModelProperty(value = "状态(0未发送,1已发送)")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "发送时间")
    @TableField("SEND_TIME")
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "时间戳")
    @TableField("TS")
    private LocalDateTime ts;


    public String getDwpId() {
        return dwpId;
    }

    public void setDwpId(String dwpId) {
        this.dwpId = dwpId;
    }

    public String getDwpName() {
        return dwpName;
    }

    public void setDwpName(String dwpName) {
        this.dwpName = dwpName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public LocalDate getDwpTm() {
        return dwpTm;
    }

    public void setDwpTm(LocalDate dwpTm) {
        this.dwpTm = dwpTm;
    }

    public Integer getyReportNo() {
        return yReportNo;
    }

    public void setyReportNo(Integer yReportNo) {
        this.yReportNo = yReportNo;
    }

    public Integer getTotalNo() {
        return totalNo;
    }

    public void setTotalNo(Integer totalNo) {
        this.totalNo = totalNo;
    }

    public String getReportYear() {
        return reportYear;
    }

    public void setReportYear(String reportYear) {
        this.reportYear = reportYear;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public LocalDateTime getTs() {
        return ts;
    }

    public void setTs(LocalDateTime ts) {
        this.ts = ts;
    }

    @Override
    protected Serializable pkVal() {
        return this.dwpId;
    }

    @Override
    public String toString() {
        return "BsnDefenseWorkPlanB{" +
                "dwpId=" + dwpId +
                ", dwpName=" + dwpName +
                ", fileName=" + fileName +
                ", filePath=" + filePath +
                ", dwpTm=" + dwpTm +
                ", yReportNo=" + yReportNo +
                ", totalNo=" + totalNo +
                ", reportYear=" + reportYear +
                ", status=" + status +
                ", sendTime=" + sendTime +
                ", ts=" + ts +
                "}";
    }
}
