package com.huitu.cloud.api.syq.rain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value="雨情列表信息")
public class RainByBas implements Serializable {
    /**
     * TODO: 暂时设置为-3495965216042804492L，原因：缓存ID对应不上
     * 序列化id
     */
    private static final long serialVersionUID = -3495965216042804492L;

    @ApiModelProperty(value = "流域编码")
    private String basCode;

    @ApiModelProperty(value = "流域名称")
    private String basName;

    @ApiModelProperty(value = "河流编码")
    @TableField("RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "流域序号")
    private Integer basNo;

    @ApiModelProperty(value = "平均降雨")
    private double avgRain;

    @ApiModelProperty(value = "最大降雨")
    private double maxRain;

    @ApiModelProperty(value = "前期24小时")
    @TableField("AVG_LDRPS")
    private double avgLdrps;

    @ApiModelProperty(value = "未来3小时")
    @TableField("AVG_F3DRPS")
    private double avgF3drps;

    @ApiModelProperty(value = "未来24小时")
    @TableField("AVG_F24DRPS")
    private double avgF24drps;

    @ApiModelProperty(value = "流域面积")
    @TableField("RIVER_AREA")
    private double riverArea;

    @ApiModelProperty(value = "上级流域编码")
    private String pbasCode;

    @ApiModelProperty(value = "上级流域名称")
    private String pbasName;

    @ApiModelProperty(value = "流经区县")
    @TableField("FLOW_AREA")
    private String flowArea;

    @ApiModelProperty(value = "是否主要江河")
    @TableField("IS_ZYJH")
    private String isZyjh;

    @ApiModelProperty(value = " 下级测站")
    private List<Rain> children;

    @ApiModelProperty(value = " 下级流域")
    private List<RainByBas> basChildren;

    public String getBasCode() {
        return basCode;
    }

    public void setBasCode(String basCode) {
        this.basCode = basCode;
    }

    public String getBasName() {
        return basName;
    }

    public void setBasName(String basName) {
        this.basName = basName;
    }

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public Integer getBasNo() {
        return basNo;
    }

    public void setBasNo(Integer basNo) {
        this.basNo = basNo;
    }

    public double getAvgRain() {
        return avgRain;
    }

    public void setAvgRain(double avgRain) {
        this.avgRain = avgRain;
    }

    public double getMaxRain() {
        return maxRain;
    }

    public void setMaxRain(double maxRain) {
        this.maxRain = maxRain;
    }

    public double getAvgLdrps() {
        return avgLdrps;
    }

    public void setAvgLdrps(double avgLdrps) {
        this.avgLdrps = avgLdrps;
    }

    public double getAvgF3drps() {
        return avgF3drps;
    }

    public void setAvgF3drps(double avgF3drps) {
        this.avgF3drps = avgF3drps;
    }

    public double getAvgF24drps() {
        return avgF24drps;
    }

    public void setAvgF24drps(double avgF24drps) {
        this.avgF24drps = avgF24drps;
    }

    public double getRiverArea() {
        return riverArea;
    }

    public void setRiverArea(double riverArea) {
        this.riverArea = riverArea;
    }

    public String getPbasCode() {
        return pbasCode;
    }

    public void setPbasCode(String pbasCode) {
        this.pbasCode = pbasCode;
    }

    public String getPbasName() {
        return pbasName;
    }

    public void setPbasName(String pbasName) {
        this.pbasName = pbasName;
    }

    public String getFlowArea() {
        return flowArea;
    }

    public void setFlowArea(String flowArea) {
        this.flowArea = flowArea;
    }

    public String getIsZyjh() {
        return isZyjh;
    }

    public void setIsZyjh(String isZyjh) {
        this.isZyjh = isZyjh;
    }

    public List<Rain> getChildren() {
        return children;
    }

    public void setChildren(List<Rain> children) {
        this.children = children;
    }

    public List<RainByBas> getBasChildren() {
        return basChildren;
    }

    public void setBasChildren(List<RainByBas> basChildren) {
        this.basChildren = basChildren;
    }
}
