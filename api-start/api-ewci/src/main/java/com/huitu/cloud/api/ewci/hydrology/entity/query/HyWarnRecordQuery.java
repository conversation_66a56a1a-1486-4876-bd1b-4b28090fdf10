package com.huitu.cloud.api.ewci.hydrology.entity.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnRecordResponse;
import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.validation.constraints.Option;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 洪水预警记录查询
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警记录查询")
public class HyWarnRecordQuery extends PageBean {

    @ApiModelProperty(value = "行政区划代码（15位）", required = true)
    @NotBlank(message = "参数[行政区划代码]不能为空")
    @Size(min = 15, max = 15, message = "参数[行政区划代码]的长度应为15个字符")
    private String adcd;

    @ApiModelProperty(value = "单位类型")
    @Option(value = {"0", "1"}, message = "参数[单位类型]的值无效")
    @TableField(value = "UNIT_TYPE")
    private String unitType;

    @ApiModelProperty(value = "预警等级（1=红色、2=橙色、3=黄色、4=蓝色）")
    @Option(value = {"1", "2", "3", "4"}, message = "参数[预警等级]的值无效")
    @TableField(value = "WGRD")
    private Integer wgrd;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    @TableField(value = "RVNM")
    private String rvnm;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "参数[开始时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "参数[结束时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;

    @ApiModelProperty(value = "发送单位")
    @JsonIgnore
    private String sendDept;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    public Integer getWgrd() {
        return wgrd;
    }

    public void setWgrd(Integer wgrd) {
        this.wgrd = wgrd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public String getSendDept() {
        return sendDept;
    }

    public void setSendDept(String sendDept) {
        this.sendDept = sendDept;
    }

    public Map<String, Object> toQueryParam() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("unitType", getUnitType());
        params.put("wgrd", getWgrd());
        params.put("stnm", getStnm());
        params.put("rvnm", getRvnm());
        params.put("stm", getStm());
        params.put("etm", getEtm());
        params.put("sendDept", getSendDept());
        return params;
    }

    public IPage<HyWarnRecordResponse> toPageParam() {
        return new Page<>(Math.max(getPageNum(), 1), getPageSize() > 0 ? getPageSize() : -1);
    }
}
