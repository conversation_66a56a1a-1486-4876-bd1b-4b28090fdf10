package com.huitu.cloud.api.ewci.person.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

/**
 * 江河责任人管理服务
 *
 * <AUTHOR>
 */
public interface RiverPersonService {

    /**
     * 分页获取江河责任人信息列表
     *
     * @param query 查询条件
     * @return 分页后的信息列表
     **/
    IPage<RiverPerson> getPageList(RiverPersonQuery query);

    /**
     * 江河责任人汇总统计
     *
     * @param query
     * @return
     */
    List<RiverSummaryVo> getRiverSummaryList(RiverSummaryQuery query);

    /**
     * 数据导出
     *
     * @param query  查询条件
     * @param output 输出流
     **/
    void dataExport(RiverPersonQuery query, OutputStream output);

    /**
     * 数据导入
     *
     * @param xadcd 县级行政区划代码
     * @param input 输入流
     * @return 导入结果
     **/
    ImportResult<BnsRiverPersonTmp> dataImport(String xadcd, InputStream input);

    /**
     * 获取江河责任人信息列表（仅包含姓名和手机号码）
     *
     * @param query 查询对象
     * @return 信息列表
     **/
    List<PersonInfo> getSimplePersonList(PersonQuery query);

    /**
     * 编辑江河责任人
     *
     * @param entity
     * @return
     */
    int riverPersonEdit(RiverPerson entity);
}
