package com.huitu.cloud.api.ewci.hydfcst.entity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class HydFcstStat implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "河道预报统计结果")
    private List<HydFcstRvStat> rvStats;

    @ApiModelProperty(value = "水库预报统计结果")
    private List<HydFcstRsStat> rsStats;

    public HydFcstStat(List<HydFcstRvStat> rvStats, List<HydFcstRsStat> rsStats) {
        this.rvStats = rvStats;
        this.rsStats = rsStats;
    }

    public List<HydFcstRvStat> getRvStats() {
        return rvStats;
    }

    public void setRvStats(List<HydFcstRvStat> rvStats) {
        this.rvStats = rvStats;
    }

    public List<HydFcstRsStat> getRsStats() {
        return rsStats;
    }

    public void setRsStats(List<HydFcstRsStat> rsStats) {
        this.rsStats = rsStats;
    }

    @Override
    public String toString() {
        return "HydFcstStat{" +
                "rvStats=" + rvStats +
                ", rsStats=" + rsStats +
                '}';
    }
}
