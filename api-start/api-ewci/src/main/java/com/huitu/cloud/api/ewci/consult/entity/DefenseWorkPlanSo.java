package com.huitu.cloud.api.ewci.consult.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description: 防御工作方案保存文件
 * @author: jiangjy
 * @create: 2024-4-17
 **/
@ApiModel(value = "防御工作方案保存文件")
public class DefenseWorkPlanSo extends BsnDefenseWorkPlanB {

    @ApiModelProperty(value = "防御工作方案文件-雨情信息")
    private String rainInfo;

    @ApiModelProperty(value = "防御工作方案文件-雨情信息")
    private String riverInfo;

    @ApiModelProperty(value = "防御工作方案文件-雨情信息")
    private String rsvrInfo;

    public String getRainInfo() {
        return rainInfo;
    }

    public void setRainInfo(String rainInfo) {
        this.rainInfo = rainInfo;
    }

    public String getRiverInfo() {
        return riverInfo;
    }

    public void setRiverInfo(String riverInfo) {
        this.riverInfo = riverInfo;
    }

    public String getRsvrInfo() {
        return rsvrInfo;
    }

    public void setRsvrInfo(String rsvrInfo) {
        this.rsvrInfo = rsvrInfo;
    }
}
