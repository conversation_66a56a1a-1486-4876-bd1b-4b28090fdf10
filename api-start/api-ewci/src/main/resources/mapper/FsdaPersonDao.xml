<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.FsdaPersonDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.person.entity.FsdaPersonBVo">
        SELECT ROW_NUMBER() OVER(ORDER BY T.ADCD, T.FSDA_CODE,T.SNO ASC) SORTNO, A.ADNM,
               T.ADCD,
               T.FSDA_CODE,
               B.FSDA_NAME,
               B.FSDA_LOC,
               T.SNO,
               P1.TS,
               P1.REALNM    SJ_REALNM,
               P1.MOBILE    SJ_MOBILE,
               P1.POST      SJ_POST,
               P2.REALNM    XJ_REALNM,
               P2.MOBILE    XJ_MOBILE,
               P2.POST      XJ_POST,
               P3.REALNM    XZJ_REALNM,
               P3.MOBILE    XZJ_MOBILE,
               P3.POST      XZJ_POST
        FROM (SELECT DISTINCT FSDA_CODE, ADCD, SNO
              FROM BNS_FSDAPERSON_B) T
                 LEFT JOIN MDT_ADCDINFO_B A ON A.ADCD = LEFT (T.ADCD, 6) + '000000000'
            LEFT JOIN ATT_FSDA_BASE B
        ON B.FSDA_CODE = T.FSDA_CODE
            LEFT JOIN BNS_FSDAPERSON_B P1 ON P1.FSDA_CODE = T.FSDA_CODE AND P1.SNO = T.SNO AND P1.PERTP = '1'
            LEFT JOIN BNS_FSDAPERSON_B P2 ON P2.FSDA_CODE = T.FSDA_CODE AND P2.SNO = T.SNO AND P2.PERTP = '2'
            LEFT JOIN BNS_FSDAPERSON_B P3 ON P3.FSDA_CODE = T.FSDA_CODE AND P3.SNO = T.SNO AND P3.PERTP = '3'
        ORDER BY T.ADCD, T.FSDA_CODE ASC
    </select>

    <delete id="delByFsdaCodeAndAdcdAndSno">
        DELETE
        FROM BNS_FSDAPERSON_B
        WHERE FSDA_CODE = #{map.fsdaCode}
          AND ADCD = #{map.adcd}
          AND SNO = #{map.sno}
    </delete>

    <insert id="insertList">
        INSERT INTO
        BNS_FSDAPERSON_B(FSDA_CODE,ADCD,PERTP,SNO,REALNM,POST,MOBILE,TS)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.fsdaCode},#{item.adcd},#{item.pertp},#{item.sno},#{item.realnm},#{item.post},#{item.mobile},#{item.ts})
        </foreach>
    </insert>
</mapper>
