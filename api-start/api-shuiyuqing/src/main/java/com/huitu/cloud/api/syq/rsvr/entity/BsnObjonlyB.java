package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@TableName("bsn_objonly_b")
@ApiModel(value="BsnObjonlyB对象", description="")
public class BsnObjonlyB extends Model<BsnObjonlyB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "对象标识，guid主键")
    @TableId(value = "objid", type = IdType.NONE)
    private String objid;

    @ApiModelProperty(value = "对象编码，即测站、工程类的编码")
    private String objcd;

    @ApiModelProperty(value = "对象类型，部分参照dbetl的BSN_DWTYPE_B的内容")
    private String objtp;


    public String getObjid() {
        return objid;
    }

    public void setObjid(String objid) {
        this.objid = objid;
    }

    public String getObjcd() {
        return objcd;
    }

    public void setObjcd(String objcd) {
        this.objcd = objcd;
    }

    public String getObjtp() {
        return objtp;
    }

    public void setObjtp(String objtp) {
        this.objtp = objtp;
    }

    @Override
    protected Serializable pkVal() {
        return this.objid;
    }

    @Override
    public String toString() {
        return "BsnObjonlyB{" +
        "objid=" + objid +
        ", objcd=" + objcd +
        ", objtp=" + objtp +
        "}";
    }
}
