package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 工程下拉
 */
@ApiModel(value = "工程下拉")
public class BnsEngB implements Serializable {

    @ApiModelProperty(value = "工程编码")
    private String enCode;

    @ApiModelProperty(value = "工程名称")
    private String enName;

    public String getEnCode() {
        return enCode;
    }

    public void setEnCode(String enCode) {
        this.enCode = enCode;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }
}
