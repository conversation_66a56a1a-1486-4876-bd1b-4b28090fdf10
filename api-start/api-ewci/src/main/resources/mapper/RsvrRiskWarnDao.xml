<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.RsvrRiskWarnDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getLatestList" resultType="com.huitu.cloud.api.ewci.warn.entity.RsvrRiskWarnInfo">
        SELECT WARNID, '3' [TYPE], XADCD, XADNM, ADCD, ADNM, STCD, STNM, RES_CODE, RES_NAME, ENG_SCAL, WTM, RZ, WGRD,
        WIDX, EWIDX, [STATE], LGTD, LTTD FROM (
        SELECT (ROW_NUMBER() OVER(PARTITION BY A.STCD ORDER BY A.WTM DESC)) SNO, <PERSON><PERSON>, C.<PERSON> XADCD, C.ADNM XADNM,
        B.ADCD, B.ADNM, A.STCD, B.STNM, G.OBJCD RES_CODE, H.RES_NAME, H.ENG_SCAL, A.WTM, A.RZ, A.WGRD, A.WIDX, A.EWIDX,
        A.[STATE], B.PLGTD LGTD, B.PLTTD LTTD
        FROM BSN_RSVR_RISKWARN_R A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = LEFT(B.ADCD, 6) + '000000000'
        LEFT JOIN BSN_OBJONLY_B F ON F.OBJCD = A.STCD AND F.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B G ON G.[OBJID] = F.[OBJID] AND G.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE H ON H.RES_CODE = G.OBJCD
        WHERE LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        AND A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.stadtps != null and map.stadtps.size() > 0">
            <foreach collection="map.stadtps" item="stadtp" separator=", " open="AND B.STADTP IN (" close=")">
                #{stadtp}
            </foreach>
        </if>
        AND H.RES_CODE IS NOT NULL
        <if test="map.engScals != null and map.engScals.size() > 0">
            <foreach collection="map.engScals" item="engScal" separator=", " open="AND H.ENG_SCAL IN (" close=")">
                #{engScal}
            </foreach>
        </if>
        ) T WHERE SNO = 1
        ORDER BY WGRD ASC, WTM DESC, ENG_SCAL ASC
    </select>
    <select id="allowSendWarn" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(0) > 0 THEN 1 ELSE 0 END
        FROM BSN_RSVR_RISKWARN_G
        WHERE GROUP_ID = #{groupId}
    </select>
    <select id="getWarnCount" resultType="java.lang.Integer" useCache="false">
        SELECT COUNT(0) FROM BSN_RSVR_RISKWARN_R A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        <where>
            LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.stm != null">
                AND A.WTM >= #{map.stm}
            </if>
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
            <if test="map.wgrds != null and map.wgrds.size() > 0">
                <foreach collection="map.wgrds" item="wgrd" separator=", " open="AND A.WGRD IN (" close=")">
                    #{wgrd}
                </foreach>
            </if>
            <if test="map.states != null and map.states.size() > 0">
                <foreach collection="map.states" item="state" separator=", " open="AND A.[STATE] IN (" close=")">
                    #{state}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getWarnList" resultType="com.huitu.cloud.api.ewci.warn.entity.RsvrRiskWarnInfo" useCache="false">
        SELECT A.WARNID, C.ADCD XADCD, C.ADNM XADNM, B.ADCD, B.ADNM, A.STCD, B.STNM, E.OBJCD RES_CODE, F.RES_NAME,
        F.ENG_SCAL, A.WTM, A.RZ, A.WGRD, A.WIDX, A.EWIDX, A.[STATE], B.PLGTD LGTD, B.PLTTD LTTD
        FROM BSN_RSVR_RISKWARN_R A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = LEFT(B.ADCD, 6) + '000000000'
        LEFT JOIN BSN_OBJONLY_B D ON D.OBJCD = A.STCD AND D.OBJTP = '1'
        LEFT JOIN BSN_OBJONLY_B E ON E.[OBJID] = D.[OBJID] AND E.OBJTP = '6'
        LEFT JOIN ATT_RES_BASE F ON F.RES_CODE = E.OBJCD
        <where>
            LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.stm != null">
                AND A.WTM >= #{map.stm}
            </if>
            <if test="map.etm != null">
                AND A.WTM &lt; #{map.etm}
            </if>
            <if test="map.wgrds != null and map.wgrds.size() > 0">
                <foreach collection="map.wgrds" item="wgrd" separator=", " open="AND A.WGRD IN (" close=")">
                    #{wgrd}
                </foreach>
            </if>
            <if test="map.states != null and map.states.size() > 0">
                <foreach collection="map.states" item="state" separator=", " open="AND A.[STATE] IN (" close=")">
                    #{state}
                </foreach>
            </if>
            AND F.RES_CODE IS NOT NULL
        </where>
        ORDER BY A.WTM DESC, A.WGRD ASC, F.ENG_SCAL ASC
    </select>
    <select id="sendWarn" statementType="CALLABLE" resultType="java.lang.Integer" flushCache="true" useCache="false">
        DECLARE
            @RETURN_VALUE INT;
        EXEC @RETURN_VALUE = PR_RSVR_RISKWARN_SEND_MANUAL
                             #{warnId},
                             #{receivers};
        SELECT @RETURN_VALUE;
    </select>
    <update id="ignoreWarn" flushCache="true">
        UPDATE BSN_RSVR_RISKWARN_R
        SET [STATE] = '2', TS = GETDATE()
        WHERE [STATE] = '0'
        <if test="map.wgrds != null and map.wgrds.size() > 0">
            <foreach collection="map.wgrds" item="wgrd" separator=", " open="AND WGRD IN (" close=")">
                #{wgrd}
            </foreach>
        </if>
        <if test="map.warnIds != null and map.warnIds.size() > 0">
            <foreach collection="map.warnIds" item="warnId" separator=", " open="AND WARNID IN (" close=")">
                #{warnId}
            </foreach>
        </if>
    </update>
</mapper>