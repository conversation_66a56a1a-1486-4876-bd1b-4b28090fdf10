package com.huitu.cloud.api.ewci.hydrology.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.hydrology.entity.HyWarnAccept;
import com.huitu.cloud.api.ewci.hydrology.entity.request.HyWarnFileRequest;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnDocumentResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnFileResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnRecordResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnUserResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 洪水预警核心Dao
 */
@Mapper
public interface HyWarnReleaseDao {

    /**
     * 分页获取预警记录列表
     *
     * @param page   分页对象
     * @param params 查询参数
     * @return 指标列表
     */
    IPage<HyWarnRecordResponse> getPageList(IPage<HyWarnRecordResponse> page, @Param("map") Map<String, Object> params);

    /**
     * 获取预警记录信息
     *
     * @param warnId 预警ID
     * @return 记录信息
     */
    HyWarnRecordResponse getRecordInfo(String warnId);

    /**
     * 获取预警文件信息
     *
     * @param warnId 预警ID
     * @return 文件信息
     */
    HyWarnFileResponse getFileInfo(String warnId);

    /**
     * 获取最新的发文期数
     *
     * @param yr 年份
     * @return 期数
     */
    int getNewFileNo(String yr);

    /**
     * 获取预警人员列表
     *
     * @param deptid 组织ID
     * @return 人员列表
     */
    List<HyWarnUserResponse> getUserList(String deptid);

    /**
     * 获取预警接收单位
     *
     * @return 接收单位
     */
    HyWarnAccept getAccept();

    /**
     * 获取洪水预警文档
     *
     * @param warnId 预警ID
     * @return 预警文档
     */
    HyWarnDocumentResponse getDocument(String warnId);

    /**
     * 编辑预警
     *
     * @param warnId 预警ID
     * @return 受影响的行数
     */
    int edit(String warnId);

    /**
     * 忽略预警
     *
     * @param warnId 预警ID
     * @return 受影响的行数
     */
    int ignore(String warnId);

    /**
     * 发布预警
     *
     * @param warnId 预警ID
     * @return 受影响的行数
     */
    int release(String warnId);

    /**
     * 保存洪水预警文件
     *
     * @param request 请求对象
     * @return 受影响的行数
     */
    int saveFile(HyWarnFileRequest request);

    /**
     * 更新发送人
     *
     * @param warnId 预警ID
     * @param sender 发送人
     * @return 受影响的行数
     */
    int updateSender(@Param("warnId") String warnId, @Param("sender") String sender);

    /**
     * 删除预警文件
     *
     * @param warnId 预警ID
     * @return 受影响的行数
     */
    int deleteFile(String warnId);

    /**
     * 判断发文期数是否重复
     *
     * @param warnId 预警ID
     * @return true: 是 false: 否
     */
    boolean isRepeatFileNo(String warnId);
}
