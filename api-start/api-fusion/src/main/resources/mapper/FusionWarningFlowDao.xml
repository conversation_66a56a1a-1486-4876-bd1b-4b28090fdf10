<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.fusion.mapper.FusionWarningFlowDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="manualConfirm">
        MERGE INTO EW_FUSION_WARNING_FLOW AS T USING (
        SELECT
        WARN_ID,
        21 WARN_STATUS_ID
        FROM
        EW_FUSION_WARNING_RECORD
        WHERE
        WARN_ID IN (<foreach collection="warnIds" item="warnId" separator="," >#{ warnId }</foreach>)) S ON T.WARN_ID = S.WARN_ID
        AND T.WARN_STATUS_ID = S.WARN_STATUS_ID
        WHEN NOT MATCHED THEN
        INSERT (WAR<PERSON>_ID, WARN_STATUS_ID, CREATE_BY, CREATE_TIME)
        VALUES
        (S.WARN_ID, S.WARN_STATUS_ID, #{ confirmBy }, GETDATE());
    </insert>
    <select id="getFlowList" resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarningFlowResp">
        SELECT
            WARN_ID,
            WARN_STATUS_ID,
            B.STATUS_NAME WARN_STATUS_NAME,
            B.SHORT_NAME WARN_STATUS_SHORT_NAME,
            REMARK,
            CREATE_BY,
            CREATE_TIME
        FROM
            EW_FUSION_WARNING_FLOW A
                LEFT JOIN EW_FUSION_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID
            WHERE WARN_ID = #{warnId}
        ORDER BY
            CREATE_TIME, WARN_STATUS_ID ASC
    </select>
</mapper>