package com.huitu.cloud.api.fusion.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.api.fusion.entity.response.ManualNotifyCertResp;
import com.huitu.cloud.entity.EntityRequest;
import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.validation.constraints.StringLength;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 人工通知凭证上报列表请求对象
 */
@ApiModel(value = "人工通知凭证上报列表请求对象")
public class ManualNotifyCertReq extends PageBean<ManualNotifyCertResp> implements EntityRequest {

    @ApiModelProperty(value = "行政区划代码", example = "220000000000000", required = true)
    @NotBlank(message = "参数[行政区划代码]不能为空")
    @StringLength(min = 15, max = 15, message = "参数[行政区划代码]的长度应为15个字符")
    private String adcd;

    @ApiModelProperty(value = "开始时间，注：选填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-03 08:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间，注：选填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-04 08:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;

    @ApiModelProperty(value = "创建人，注：模糊匹配")
    private String createBy;

    @ApiModelProperty(value = "工作描述，注：模糊匹配")
    private String remark;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("stm", getStm());
        if (null != getEtm()) {
            if (null != getStm() && getStm().getTime() >= getEtm().getTime()) {
                throw new IllegalArgumentException("参数[结束时间]应大于参数[开始时间]");
            }
            params.put("etm", getEtm());
        }
        params.put("createBy", getCreateBy());
        params.put("remark", getRemark());
        return params;
    }
}
