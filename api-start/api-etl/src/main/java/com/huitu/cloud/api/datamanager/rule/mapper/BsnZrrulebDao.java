package com.huitu.cloud.api.datamanager.rule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.datamanager.rule.entity.BsnZrruleb;
import com.huitu.cloud.api.datamanager.rule.entity.RainRuleVo;
import com.huitu.cloud.api.datamanager.rule.entity.ZrruleVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 水位规则配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-07
 */
public interface BsnZrrulebDao extends BaseMapper<BsnZrruleb> {
    /**
     * 查询水位规则
     * @param page
     * @param stnm 测站名称
     * @param sttp 测站类型 ZZ 河道 RR 水库
     * @return
     */
    IPage<ZrruleVo> getZrRule(Page page, @Param("stnm") String stnm, @Param("sttp") String sttp, @Param("ad") String ad);
}
