package com.huitu.cloud.api.hyst.survey.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 小水电概况 统计图表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-30
 */
public class HystStats implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总数")
    @TableId(value = "TOTALCOUNT")
    private int totalCount;

    @ApiModelProperty(value = "考核")
    @TableId(value = "EXAMINECOUNT")
    private int examineCount;

    @ApiModelProperty(value = "免考核")
    @TableId(value = "EXEMPTCOUNT")
    private int exemptCount;

    @ApiModelProperty(value = "在线")
    @TableId(value = "ONLINECOUNT")
    private int onLineCount;

    @ApiModelProperty(value = "离线")
    @TableId(value = "OUTLINECOUNT")
    private int outLineCount;

    @ApiModelProperty(value = "达标")
    @TableId(value = "UPTOSTANDARDCOUNT")
    private int upToStandardCount;

    @ApiModelProperty(value = "未达标")
    @TableId(value = "NOTUPTOSTANDARDCOUNT")
    private int notUpToStandardCount;

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getExamineCount() {
        return examineCount;
    }

    public void setExamineCount(int examineCount) {
        this.examineCount = examineCount;
    }

    public int getExemptCount() {
        return exemptCount;
    }

    public void setExemptCount(int exemptCount) {
        this.exemptCount = exemptCount;
    }

    public int getOnLineCount() {
        return onLineCount;
    }

    public void setOnLineCount(int onLineCount) {
        this.onLineCount = onLineCount;
    }

    public int getOutLineCount() {
        return outLineCount;
    }

    public void setOutLineCount(int outLineCount) {
        this.outLineCount = outLineCount;
    }

    public int getUpToStandardCount() {
        return upToStandardCount;
    }

    public void setUpToStandardCount(int upToStandardCount) {
        this.upToStandardCount = upToStandardCount;
    }

    public int getNotUpToStandardCount() {
        return notUpToStandardCount;
    }

    public void setNotUpToStandardCount(int notUpToStandardCount) {
        this.notUpToStandardCount = notUpToStandardCount;
    }
}
