package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;


import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * <AUTHOR>
 * @since 2024-05-15
 */

/**
 * <AUTHOR>
 */

@ApiModel(value="RiverZuoZhanTuVo")
public class RiverZuoZhanTuVo extends Model<RiverZuoZhanTuVo> {

    @ApiModelProperty(value = "警戒水位")
    @TableField("WRZ")
    private BigDecimal wrz;

    @ApiModelProperty(value = "保证水位")
    @TableField("GRZ")
    private BigDecimal grz;

    @ApiModelProperty(value = "超警戒水位")
    @TableField("ZWRZ")
    private BigDecimal zwrz;

    @ApiModelProperty(value = "超保证水位")
    @TableField("ZGRZ")
    private BigDecimal zgrz;

    @ApiModelProperty(value = "行政区划码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区名称")
    @TableField("ADNM")
    private String adnm;

    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "水位")
    @TableField("Z")
    private String z;

    @ApiModelProperty(value = "流量")
    @TableField("Q")
    private BigDecimal q;

    @ApiModelProperty(value = "水势")
    @TableField("WPTN")
    private String wptn;

    public BigDecimal getWrz() {
        return wrz;
    }

    public void setWrz(BigDecimal wrz) {
        this.wrz = wrz;
    }

    public BigDecimal getGrz() {
        return grz;
    }

    public void setGrz(BigDecimal grz) {
        this.grz = grz;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getZ() {
        return z;
    }

    public void setZ(String z) {
        this.z = z;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getWptn() {
        return wptn;
    }

    public void setWptn(String wptn) {
        this.wptn = wptn;
    }

    public BigDecimal getZwrz() {
        return zwrz;
    }

    public void setZwrz(BigDecimal zwrz) {
        this.zwrz = zwrz;
    }

    public BigDecimal getZgrz() {
        return zgrz;
    }

    public void setZgrz(BigDecimal zgrz) {
        this.zgrz = zgrz;
    }
}
