package com.huitu.cloud.api.shyj.dept.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.shyj.dept.entity.DeptB;
import com.huitu.cloud.api.shyj.dept.entity.RelevantDeptInfo;
import com.huitu.cloud.api.shyj.dept.mapper.DeptDao;
import com.huitu.cloud.api.shyj.dept.service.DeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 部门信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@Service
public class DeptServiceImpl extends ServiceImpl<DeptDao, DeptB>  implements DeptService {
    @Autowired
    private DeptDao deptDao;

    @Override
    public IPage<RelevantDeptInfo> getDeptList(String adcd, String deptNm, String pid, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Map<String, Object> param = new HashMap<>();
        param.put("deptNm", deptNm);
        param.put("adcd", adcd);
        param.put("pid", pid);
        IPage<RelevantDeptInfo> resultMap = deptDao.getDeptListByPage(page, param);
        return resultMap;
    }

    @Override
    public String addDept(DeptB entity) {
        Map<String, Object> param = new HashMap<>();
        param.put("checkNm", entity.getDeptnm());
        param.put("adcd", entity.getAdcd());
        List<RelevantDeptInfo> list=deptDao.getDeptList(param);
        if(list!=null && list.size()>0){
            //部门名称重复
            return "0";
        }
        DeptB dept=deptDao.selectMaxId();
        entity.setDeptcd(dept.getDeptcd());
        deptDao.insert(entity);
        return "1";
    }

    @Override
    public String updateDept(DeptB entity) {
        Map<String, Object> param = new HashMap<>();
        param.put("checkNm", entity.getDeptnm());
        param.put("adcd", entity.getAdcd());
        List<RelevantDeptInfo> list=deptDao.getDeptList(param);
        if(list!=null && list.size()>0){
            //部门名称重复
            return "0";
        }
        deptDao.updateDept(entity);
        return "1";
    }

    @Override
    public String delDept(String adcd, String deptcd) {
        deptDao.delDept(adcd,deptcd);
        return "1";
    }

    @Override
    public String checkDelDept(String adcd, String deptcd) {
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        param.put("pid", deptcd);
        List<RelevantDeptInfo> list=deptDao.getDeptList(param);
        if (list!=null && list.size()>0){
            return "0";
        }
        return "1";
    }

    @Override
    public List<RelevantDeptInfo> getDeptTreeList(String adcd) {
        Map<String, Object> param = new HashMap<>();
        param.put("adcd", adcd);
        List<RelevantDeptInfo> list=deptDao.getDeptList(param);
        list.forEach(item->{
            List<RelevantDeptInfo> childList=getChildren(list,item.getDeptcd());
            item.setChildren(childList);
        });
        String fiag="0";
        List<RelevantDeptInfo> result =list.stream().filter(x->x.getPid().equals(fiag)).collect(Collectors.toList());
        return result;
    }

    private List<RelevantDeptInfo> getChildren(List<RelevantDeptInfo> list,String pcode){
        // 通过父级编码子类
        List<RelevantDeptInfo> childList =list.stream().filter(x->x.getPid().equals(pcode)).collect(Collectors.toList());
        return childList;
    }
}
