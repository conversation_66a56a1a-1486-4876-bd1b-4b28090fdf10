package com.huitu.cloud.api.ewci.warn.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.huitu.cloud.api.ewci.warn.api.ArcgisMapService;
import com.huitu.cloud.api.ewci.warn.api.TrafficEventService;
import com.huitu.cloud.api.ewci.warn.entity.ArcgisMapParam;
import com.huitu.cloud.api.ewci.warn.entity.BnsWarnInfo;
import com.huitu.cloud.api.ewci.warn.entity.BnsWarnNavigation;
import com.huitu.cloud.api.ewci.warn.mapper.BnsWarnNavigationDao;
import com.huitu.cloud.api.ewci.warn.service.BnsWarnNavigationService;
import com.huitu.cloud.entity.Coordinates;
import com.huitu.utils.StringUtils;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预警导航记录 实现类
 *
 * <AUTHOR>
 */
@Service
public class BnsWarnNavigationServiceImpl implements BnsWarnNavigationService {

    private static final Logger logger = LoggerFactory.getLogger(BnsWarnNavigationServiceImpl.class);

    @Value("${amap.key:}")
    private String clientKey;
    @Value("${amap.secret:}")
    private String clientSecret;

    private final BnsWarnNavigationDao baseDao;
    private final ArcgisMapService arcgisMapService;
    private final TrafficEventService trafficEventService;

    public BnsWarnNavigationServiceImpl(BnsWarnNavigationDao baseDao, ArcgisMapService arcgisMapService, TrafficEventService trafficEventService) {
        this.baseDao = baseDao;
        this.arcgisMapService = arcgisMapService;
        this.trafficEventService = trafficEventService;
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addEvent(BnsWarnInfo warnInfo) {
        // 获取道路坐标
        List<List<List<String>>> list;
        try {
            list = getCoordinate(warnInfo.getWarnScope());
            if (CollectionUtils.isEmpty(list)) {
                if (logger.isInfoEnabled()) {
                    logger.info("未获取到道路坐标，参数={}", JSON.toJSONString(warnInfo.getWarnScope()));
                }
                return;
            }
            // 城市编码
            String adcode = warnInfo.getAdcd().substring(0, 6);
            // 预警导航记录list
            List<BnsWarnNavigation> navigations = new ArrayList<>();

            // 构造所需参数 map
            Map<String, String> mapParam = new HashMap<>();
            mapParam.put("adcode", adcode);
            mapParam.put("broadcastCode", "15");
            mapParam.put("clientKey", clientKey);
            mapParam.put("desc", "吉林省水利厅");
            mapParam.put("id", warnInfo.getWarnId());
            mapParam.put("locType", "2");
            mapParam.put("sourceId", "event_jilinshuili");
            mapParam.put("startDate", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            mapParam.put("stateFlag", "0");
            mapParam.put("timestamp", String.valueOf(new Date().getTime() / 1000L));
            mapParam.put("type", "910");

            list.forEach(two -> {
                two.forEach(one -> {
                    mapParam.remove("digest");
                    mapParam.put("locs", "[".concat(one.toString().concat("]")));

                    // 获取签名
                    List<String> values = mapParam.keySet().stream()
                            .sorted().map(mapParam::get).collect(Collectors.toList());
                    mapParam.put("digest", getDigest(String.join("", values)));

                    // 调用高德HTTP接口
                    JSONObject response = trafficEventService.getRoad(mapParam);
                    if (StringUtils.isEmpty(response)) {
                        if (logger.isInfoEnabled()) {
                            logger.info("调用高德交通事件接口异常，参数={}", JSON.toJSONString(mapParam));
                        }
                        return;
                    }

                    // 获取到返回code
                    Integer code = null;
                    if (Objects.nonNull(response.get("code"))) {
                        code = (Integer) response.get("code");
                    }
                    if (Objects.nonNull(response.get("errcode"))) {
                        code = (Integer) response.get("errcode");
                    }

                    // 构造预警导航记录list
                    if (Objects.nonNull(code)) {
                        // 起点和终点坐标list
                        List<String> start = Arrays.asList(one.get(0).replace("\"", "").split(","));
                        List<String> end = Arrays.asList(one.get(1).replace("\"", "").split(","));
                        // 预警导航记录
                        BnsWarnNavigation navigation = new BnsWarnNavigation();
                        navigation.setWarnId(warnInfo.getWarnId());
                        navigation.setStartLgtd(Double.parseDouble(start.get(0)));
                        navigation.setStartLttd(Double.parseDouble(start.get(1)));
                        navigation.setEndLgtd(Double.parseDouble(end.get(0)));
                        navigation.setEndLttd(Double.parseDouble(end.get(1)));
                        navigation.setWarnContent(warnInfo.getWarnContent());
                        navigation.setSendTime(LocalDateTime.now());
                        if (new Integer(0).equals(code)) {
                            navigation.setStatus("1");
                        } else {
                            navigation.setStatus("2");
                            navigation.setFailureReason(code);
                            if (logger.isInfoEnabled()) {
                                logger.info("交通事件发布失败！，返回示例={}", JSON.toJSONString(response));
                                logger.info("参数={}", JSON.toJSONString(mapParam));
                            }
                        }
                        navigations.add(navigation);
                    }
                });
            });
            // 添加预警导航记录
            if (!CollectionUtils.isEmpty(navigations)) {
                int code = baseDao.insertAllNavigation(navigations);
                if (code < 1) {
                    throw new RuntimeException("添加预警导航记录失败！");
                }
            }
        } catch (Exception ex) {
            logger.error("发送高德交通事件调用异常", ex);
        }
    }


    /**
     * 根据其他参数生成签名
     *
     * @param paramValuesStr
     * @return
     */
    private String getDigest(String paramValuesStr) {

        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            byte[] secretByte = clientSecret.getBytes(StandardCharsets.UTF_8);
            byte[] dataBytes = paramValuesStr.getBytes(StandardCharsets.UTF_8);
            SecretKey secretKey = new SecretKeySpec(secretByte, "HMACSHA256");
            mac.init(secretKey);
            byte[] doFinal = mac.doFinal(dataBytes);
            byte[] hexB = new Hex().encode(doFinal);
            return new String(hexB, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取道路坐标
     *
     * @param geometry
     * @return
     */
    public List<List<List<String>>> getCoordinate(String geometry) {
        List<List<List<String>>> threeList = new ArrayList<>();
        JSONObject response;
        ArcgisMapParam param = new ArcgisMapParam();
        param.setGeometry("{\"rings\":[" + geometry + "],\"spatialReference\":{\"wkid\":4326}}");
        try {
            response = arcgisMapService.getRoad(param);
            if (StringUtils.isEmpty(response)) {
                if (logger.isInfoEnabled()) {
                    logger.info("未获取到道路坐标，参数={}", JSON.toJSONString(param));
                }
                return threeList;
            }

            List<Map<String, Object>> newList = (List<Map<String, Object>>) response.get("features");
            if (!CollectionUtils.isEmpty(newList)) {
                newList.forEach(itme -> {
                    List<List<String>> twoList = new ArrayList<>();
                    Map geometryData = (Map) itme.get("geometry");

                    // 道路经纬度 三维数组
                    List<List<List<BigDecimal>>> list = (List<List<List<BigDecimal>>>) geometryData.get("paths");

                    // 起点和终点 经纬度数组
                    List<BigDecimal> startList = list.get(0).get(0);
                    List<BigDecimal> endList = list.get(0).get(list.get(0).size() - 1);

                    // 构造起点和终点 经纬度对象
                    Coordinates startCoordinates = new Coordinates(startList.get(0).doubleValue(), startList.get(1).doubleValue());
                    Coordinates endCoordinates = new Coordinates(endList.get(0).doubleValue(), endList.get(1).doubleValue());

                    // 构造所需 起点和终点 经纬度 数组
                    String start = "\"".concat(startCoordinates.wgs84ToGcj02().toString(6)).concat("\"");
                    String end = "\"".concat(endCoordinates.wgs84ToGcj02().toString(6)).concat("\"");
                    List<String> oneList = Arrays.asList(start, end);
                    twoList.add(oneList);
                    threeList.add(twoList);
                });
            }
        } catch (Exception ex) {
            logger.error("获取道路坐标异常", ex);
        }
        return threeList;
    }

}
