package com.huitu.cloud.api.ewci.monitor.controller;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.monitor.entity.FloodPerson;
import com.huitu.cloud.api.ewci.monitor.entity.IaCDanad;
import com.huitu.cloud.api.ewci.monitor.entity.IaCGully;
import com.huitu.cloud.api.ewci.monitor.entity.IaCPrevad;
import com.huitu.cloud.api.ewci.monitor.entity.InfluenceScope;
import com.huitu.cloud.api.ewci.monitor.entity.RsvrPerson;
import com.huitu.cloud.api.ewci.monitor.service.DanadService;
import com.huitu.cloud.api.ewci.monitor.service.InfluenceScopeService;
import com.huitu.cloud.api.ewci.monitor.service.PrevadService;
import com.huitu.cloud.api.ewci.person.entity.DikePerson;
import com.huitu.cloud.api.ewci.person.entity.RiverPerson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 影响范围
 *
 * <AUTHOR>
 */
@Api(tags = "影响范围")
@RestController
@RequestMapping("/api/ewci/monitor/scope")
public class InfluenceScopeResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "b1c53430-af76-5b31-d703-6d93ee0f8e8c";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final InfluenceScopeService baseService;
    private final PrevadService prevadService;
    private final DanadService danadService;

    public InfluenceScopeResource(InfluenceScopeService baseService, PrevadService prevadService, DanadService danadService) {
        this.baseService = baseService;
        this.prevadService = prevadService;
        this.danadService = danadService;
    }

    @ApiOperation(value = "获取汇总统计信息", notes = "作者：曹宝金")
    @GetMapping("select-stat-data/{adcd}")
    public ResponseEntity<SuccessResponse<InfluenceScope>> getStatData(@PathVariable(value = "adcd") String adcd) {
        if (StringUtils.isBlank(adcd)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getStatData(adcd)));
    }

    @ApiOperation(value = "获取山洪责任人列表", notes = "作者：曹宝金")
    @GetMapping("select-flood-persion-list/{adcd}")
    public ResponseEntity<SuccessResponse<List<FloodPerson>>> getFloodPersonList(@PathVariable(value = "adcd") String adcd) {
        if (StringUtils.isBlank(adcd)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getFloodPersonList(adcd)));
    }

    @ApiOperation(value = "获取水库责任人列表", notes = "作者：曹宝金")
    @GetMapping("select-rsvr-persion-list/{stcd}")
    public ResponseEntity<SuccessResponse<List<RsvrPerson>>> getRsvrPersonList(@PathVariable(value = "stcd") String stcd) {
        if (StringUtils.isBlank(stcd)) {
            throw new IllegalArgumentException("测站编码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRsvrPersonList(stcd)));
    }

    @ApiOperation(value = "获取江河责任人列表", notes = "作者：曹宝金")
    @GetMapping("select-river-persion-list/{stcd}")
    public ResponseEntity<SuccessResponse<List<RiverPerson>>> getRiverPersonList(@PathVariable(value = "stcd") String stcd) {
        if (StringUtils.isBlank(stcd)) {
            throw new IllegalArgumentException("测站编码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverPersonList(stcd)));
    }

    @ApiOperation(value = "获取堤防责任人列表", notes = "作者：曹宝金")
    @GetMapping("select-dike-persion-list/{stcd}")
    public ResponseEntity<SuccessResponse<List<DikePerson>>> getDikePersonList(@PathVariable(value = "stcd") String stcd) {
        if (StringUtils.isBlank(stcd)) {
            throw new IllegalArgumentException("测站编码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getDikePersonList(stcd)));
    }

    @ApiOperation(value = "获取需防洪治理山洪沟基本情况成果列表", notes = "作者：曹宝金")
    @GetMapping("select-gully-list/{adcd}")
    public ResponseEntity<SuccessResponse<List<IaCGully>>> getGullyList(@PathVariable(value = "adcd") String adcd) {
        if (StringUtils.isBlank(adcd)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getGullyList(adcd)));
    }

    @ApiOperation(value = "获取防治区基本情况调查成果汇总列表", notes = "作者：曹宝金")
    @GetMapping("select-prevad-list/{adcd}")
    public ResponseEntity<SuccessResponse<List<IaCPrevad>>> getPrevadList(@PathVariable(value = "adcd") String adcd) {
        if (StringUtils.isBlank(adcd)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", prevadService.getPrevadList(null, adcd)));
    }

    @ApiOperation(value = "获取危险区基本情况调查成果汇总列表", notes = "作者：曹宝金")
    @GetMapping("select-danad-list/{adcd}")
    public ResponseEntity<SuccessResponse<List<IaCDanad>>> getDanadList(@PathVariable(value = "adcd") String adcd) {
        if (StringUtils.isBlank(adcd)) {
            throw new IllegalArgumentException("行政区划代码不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", danadService.getDanadList(null, adcd)));
    }
}
