package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 重点区统计统计
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@ApiModel(value="DtKeyAd对象", description="重点区统计统计")
public class DtKeyAd extends DtExtendInfo{
    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = " 防治区总人口（人） ")
    private Double ptcount;

    @ApiModelProperty(value = " 防治区（个） ")
    private Double prevcnt;

    @ApiModelProperty(value = " 重点防治区（个） ")
    private Double imppevcnt;

    @ApiModelProperty(value = " 危险区（个） ")
    private Integer danct;

    @ApiModelProperty(value = " 沿河村落（个）")
    private Integer flrvvlgct;

    @ApiModelProperty(value = " 历史山洪（次） ")
    private Integer hsfwaterct;

    @ApiModelProperty(value = " 下级统计")
    private List<DtKeyAd> children;

    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    public Double getImppevcnt() {
        return imppevcnt;
    }

    public void setImppevcnt(Double imppevcnt) {
        this.imppevcnt = imppevcnt;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public List<DtKeyAd> getChildren() {
        return children;
    }

    public void setChildren(List<DtKeyAd> children) {
        this.children = children;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getPtcount() {
        return ptcount;
    }

    public void setPtcount(Double ptcount) {
        this.ptcount = ptcount;
    }

    public Double getPrevcnt() {
        return prevcnt;
    }

    public void setPrevcnt(Double prevcnt) {
        this.prevcnt = prevcnt;
    }

    public Integer getDanct() {
        return danct;
    }

    public void setDanct(Integer danct) {
        this.danct = danct;
    }

    public Integer getFlrvvlgct() {
        return flrvvlgct;
    }

    public void setFlrvvlgct(Integer flrvvlgct) {
        this.flrvvlgct = flrvvlgct;
    }

    public Integer getHsfwaterct() {
        return hsfwaterct;
    }

    public void setHsfwaterct(Integer hsfwaterct) {
        this.hsfwaterct = hsfwaterct;
    }
}
