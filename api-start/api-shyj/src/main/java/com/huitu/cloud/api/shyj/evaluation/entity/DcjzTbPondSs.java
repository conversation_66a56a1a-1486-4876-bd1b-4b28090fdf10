package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 重点城(集)镇调查评价涉水工程-塘坝详情列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@ApiModel(value="DcjzTbPondSs对象", description="重点城(集)镇调查评价涉水工程-塘坝详情列表")
public class DcjzTbPondSs extends Model<DcjzTbPondSs> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "塘堰名称")
    @TableId(value = "DAMNAME", type = IdType.NONE)
    private String damname;

    @ApiModelProperty(value = "挡水主坝类型")
    @TableId(value = "MT", type = IdType.NONE)
    private String mt;

    @ApiModelProperty(value = "坝高")
    @TableId(value = "HEIGHT", type = IdType.NONE)
    private String height;

    @ApiModelProperty(value = "坝长")
    @TableId(value = "WIDTH", type = IdType.NONE)
    private String width;

    @ApiModelProperty(value = "总库容")
    @TableId(value = "XHST", type = IdType.NONE)
        private String xhst;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getDamname() {
        return damname;
    }

    public void setDamname(String damname) {
        this.damname = damname;
    }

    public String getMt() {
        return mt;
    }

    public void setMt(String mt) {
        this.mt = mt;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getXhst() {
        return xhst;
    }

    public void setXhst(String xhst) {
        this.xhst = xhst;
    }

    @Override
    public String toString() {
        return "DcjzTbPondSs{" +
                "adnm='" + adnm + '\'' +
                ", damname='" + damname + '\'' +
                ", mt='" + mt + '\'' +
                ", height='" + height + '\'' +
                ", width='" + width + '\'' +
                ", xhst='" + xhst + '\'' +
                '}';
    }
}
