package com.huitu.cloud.api.ewci.person.exception;


import com.huitu.cloud.api.ewci.person.entity.BnsDanpersonTmp;

import java.util.List;

/**
 * 危险区居民信息导入异常
 *
 * <AUTHOR>
 */
public class DanPersonImportException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    List<BnsDanpersonTmp> errorData;

    public List<BnsDanpersonTmp> getErrorData() {
        return errorData;
    }

    public void setErrorData(List<BnsDanpersonTmp> errorData) {
        this.errorData = errorData;
    }

    public DanPersonImportException(String message) {
        super(message);
    }

    public DanPersonImportException(String message, List<BnsDanpersonTmp> errorData) {
        super(message);

        this.errorData = errorData;
    }

    public DanPersonImportException(String message, Throwable cause) {
        super(message, cause);
    }

    public DanPersonImportException(String message, List<BnsDanpersonTmp> errorData, Throwable cause) {
        super(message, cause);

        this.errorData = errorData;
    }
}
