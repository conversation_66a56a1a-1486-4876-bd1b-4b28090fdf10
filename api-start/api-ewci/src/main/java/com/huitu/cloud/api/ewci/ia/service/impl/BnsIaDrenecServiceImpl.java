package com.huitu.cloud.api.ewci.ia.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.ia.entity.*;
import com.huitu.cloud.api.ewci.ia.mapper.BnsIaDrenecDao;
import com.huitu.cloud.api.ewci.ia.mapper.BnsIaUrbanwatsDao;
import com.huitu.cloud.api.ewci.ia.service.BnsIaDrenecService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 抗旱工程及非工程能力调查表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@Service
public class BnsIaDrenecServiceImpl implements BnsIaDrenecService {

    private BnsIaDrenecDao iaDrenecDao;

    @Autowired
    public void setRcsDikeDao(BnsIaDrenecDao iaDrenecDao) {
        this.iaDrenecDao = iaDrenecDao;
    }

    @Override
    public IPage<BnsIaDrenecVo> getPageList(BnsIaDrenecQo query) {
        String adcd = StringUtils.rightPad(query.getAdcd(), 15, "0");

        Map<String, Object> params = new HashMap<>();
        params.put("adcd", adcd);
        params.put("level", AdcdUtil.getAdLevel(adcd));
        params.put("yr", query.getYr());
        return iaDrenecDao.getPageList(new Page<>(query.getPageNum(), query.getPageSize()), params);
    }

    @Override
    public List<BnsIaDrenecVo> getStatListByAdcd(String adcd, String yr) {
        adcd = StringUtils.rightPad(adcd, 15, "0");
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", adcd);
        params.put("level", level);
        params.put("yr", yr);
        if (level == 6 || adcd.equals("220581000000000")) {
            return iaDrenecDao.getStatOwnListByAdcd(params);
        }
        return iaDrenecDao.getStatListByAdcd(params);
    }

    @Override
    public List<BnsIaDrenecVo> getStatOwnListByAdcd(String adcd, String yr) {
        adcd = StringUtils.rightPad(adcd, 15, "0");

        Map<String, Object> params = new HashMap<>();
        params.put("adcd", adcd);
        params.put("yr", yr);
        return iaDrenecDao.getStatOwnListByAdcd(params);
    }

    @Override
    public void exporDroughtReliefProject(BnsIaDrenecQo query, HttpServletResponse response) {
        Page page = new Page<>(1, -1);
        String adcd = StringUtils.rightPad(query.getAdcd(), 15, "0");
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", adcd);
        params.put("level", AdcdUtil.getAdLevel(adcd));
        params.put("yr", query.getYr());
        IPage<BnsIaDrenecVo> iPage = iaDrenecDao.getPageList(page, params);
        List<BnsIaDrenecVo> list = iPage.getRecords();
        try {
            //创建XSSF工作薄
            XSSFWorkbook writeWorkbook = new XSSFWorkbook();
            //创建一个Sheet页
            XSSFSheet sheet = writeWorkbook.createSheet();
            // 设置字体
            // 内容设置 font
            XSSFFont font = writeWorkbook.createFont();
            font.setFontHeightInPoints((short) 10);
            //            font.setFontName("黑体");
            XSSFFont font1 = writeWorkbook.createFont();
            font1.setFontHeightInPoints((short) 10);
            font1.setBold(true); //字体加粗
            // 设置字体样式
            XSSFCellStyle style = writeWorkbook.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setWrapText(true);
            style.setFont(font);
            // 设置字体样式
            XSSFCellStyle style1 = writeWorkbook.createCellStyle();
            style1.setAlignment(HorizontalAlignment.CENTER);
            style1.setVerticalAlignment(VerticalAlignment.CENTER);
            style1.setWrapText(true);
            // 设置背景颜色
            style1.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            //必须设置 否则背景色不生效
            style1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style1.setFont(font1);
            style1.setBorderBottom(BorderStyle.MEDIUM);
            style1.setBorderLeft(BorderStyle.MEDIUM);
            style1.setBorderRight(BorderStyle.MEDIUM);
            style1.setBorderTop(BorderStyle.MEDIUM);
            Object[][] headerArr = new Object[][]{
                    {0, 0, "序号"}, {0, 1, "行政区划"}, {0, 2, "行政区划代码"}, {0, 3, "蓄水工程"}, {0, 6, "引堤水工程"},
                    {0, 8, "调水工程"}, {0, 10, "水井工程"}, {0, 12, "抗旱应急(备用)水源工程"}, {0, 16, "抗旱服务组织"}, {0, 20, "县级抗旱预案"}, {0, 21, "抗旱物资库"}, {0, 22, "土壤墒情站数量"},
                    {1, 3, "数量(座)"}, {1, 4, "总库容(万m³)"}, {1, 5, "现状供水能力(万m³)"}, {1, 6, "数量(处)"}, {1, 7, "现状供水能力(万m³)"}, {1, 8, "数量(处)"}, {1, 9, "现状供水能力(万m³)"}, {1, 10, "数量(处)"}, {1, 11, "现状供水能力(万m³)"}, {1, 12, "农村供水能力"},
                    {1, 14, "城镇供水能力(万m³)"}, {1, 15, "总供水能力(万m³)"}, {1, 16, "县级服务组织"}, {1, 17, "人数(人)"}, {1, 18, "应急抗旱能力"},
                    {2, 12, "人饮(万m³)"}, {2, 13, "灌溉(万m³)"}, {2, 18, "机动浇地能力(亩/天)"}, {2, 19, "应急送水能力(吨/次)"}
            };
            for (int i = 0; i < headerArr.length; i++) {
                Object[] a1 = headerArr[i];
                setCellValue(sheet, style1, Integer.parseInt(a1[0].toString()), Integer.parseInt(a1[1].toString()), a1[2].toString());
            }
            // 合并单元格，参数依次为起始列，结束列，起始行，结束行 （索引0开始）
            int[][] arr = new int[][]{
                    {0, 2, 0, 0}, {0, 2, 1, 1}, {0, 2, 2, 2}, {0, 0, 3, 5}, {0, 0, 6, 7}, {0, 0, 8, 9}, {0, 0, 10, 11}, {0, 0, 12, 15}, {0, 0, 16, 19}, {0, 2, 20, 20}, {0, 2, 21, 21}, {0, 2, 22, 22},
                    {1, 2, 3, 3}, {1, 2, 4, 4}, {1, 2, 5, 5}, {1, 2, 6, 6}, {1, 2, 7, 7}, {1, 2, 8, 8}, {1, 2, 9, 9}, {1, 2, 10, 10}, {1, 2, 11, 11}, {1, 1, 12, 13}, {1, 2, 14, 14}, {1, 2, 15, 15}, {1, 2, 16, 16}, {1, 2, 17, 17}, {1, 1, 18, 19}
            };

            for (int i = 0; i < arr.length; i++) {
                int[] a = arr[i];
                // 起始行、结束行、起始列、结束列
                CellRangeAddress cra = new CellRangeAddress(a[0], a[1], a[2], a[3]);
                // 下边框
                RegionUtil.setBorderBottom(BorderStyle.MEDIUM, cra, sheet);
                // 左边框
                RegionUtil.setBorderLeft(BorderStyle.MEDIUM, cra, sheet);
                // 右边框
                RegionUtil.setBorderRight(BorderStyle.MEDIUM, cra, sheet);
                // 上边框
                RegionUtil.setBorderTop(BorderStyle.MEDIUM, cra, sheet);
                sheet.addMergedRegion(cra);
            }
            List<String> excelProperties = Arrays.asList(new String[]{"adnm",
                    "adcd",
                    "resnum",
                    "restcp",
                    "rescws",
                    "dcnum",
                    "dccws",
                    "dsnum",
                    "dscws",
                    "wlnum",
                    "wlcws",
                    "rrdkw",
                    "rrirr",
                    "ctws",
                    "wst",
                    "ctysrvorg",
                    "ctysrvorgpp",
                    "mif",
                    "emw",
                    "ctydgp",
                    "dggmwh",
                    "ssnum"});
            for (int i = 0; i < list.size(); i++) {
                BnsIaDrenecVo bnsIaDrenecVo = list.get(i);
                for (int j = 0; j < excelProperties.size() + 1; j++) {
                    if (j == 0) {
                        setCellValue(sheet, style, i + 3, 0, (i + 1) + "");
                        continue;
                    }
                    String property = excelProperties.get(j - 1);
                    Object property1 = PropertyUtils.getProperty(bnsIaDrenecVo, property);
                    if ("Y".equals(property1)) {
                        property1 = "√";
                    } else if ("".equals(property1)) {
                        property1 = "- -";
                    }
                    if (property1 == null) {
                        setCellValue(sheet, style, i + 3, j, "");
                        continue;
                    }
                    setCellValue(sheet, style, i + 3, j, property1.toString());
                }
            }
            File file0 = new File("ee.xlsx");
            FileOutputStream outputStream = new FileOutputStream(file0);
            //将Excel写入输出流中
            writeWorkbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        ExcelExportUtil.excleDownload("ee.xlsx", "2020年抗旱工程及非工程能力调查表", null);
    }

    public void setCellValue(XSSFSheet sheet, XSSFCellStyle style, int rownum, int columnIndex, String value) {
        XSSFRow row1 = sheet.getRow(rownum);
        if (row1 == null) {
            row1 = sheet.createRow(rownum);
        }
        XSSFCell cell = row1.createCell(columnIndex);
        cell.setCellStyle(style);
        cell.setCellValue(value);

    }
}
