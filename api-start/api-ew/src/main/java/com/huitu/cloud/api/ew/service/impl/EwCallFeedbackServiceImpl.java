package com.huitu.cloud.api.ew.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.huitu.cloud.api.ew.entity.base.EwCallFeedback;
import com.huitu.cloud.api.ew.entity.base.EwManualConf;
import com.huitu.cloud.api.ew.entity.base.EwWarningConf;
import com.huitu.cloud.api.ew.entity.common.CallStatus;
import com.huitu.cloud.api.ew.entity.ext.EwNoticeReceiver;
import com.huitu.cloud.api.ew.entity.request.EwCallFeedbackRequest;
import com.huitu.cloud.api.ew.entity.request.EwCallStatisticsRequest;
import com.huitu.cloud.api.ew.entity.request.EwManualConfirmRequest;
import com.huitu.cloud.api.ew.entity.request.EwNoticeReceiverRequest;
import com.huitu.cloud.api.ew.entity.response.EwCallStatisticsResponse;
import com.huitu.cloud.api.ew.entity.response.EwNoticeReceiverResponse;
import com.huitu.cloud.api.ew.mapper.*;
import com.huitu.cloud.api.ew.service.EwCallFeedbackService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.huitu.cloud.api.ew.entity.common.CallStatus.CONFIRMED;
import static com.huitu.cloud.api.ew.entity.common.CallStatus.UNCONFIRMED;

/**
 * 叫应反馈服务 实现类
 */
@Service
public class EwCallFeedbackServiceImpl implements EwCallFeedbackService {

    private EwCallFeedbackDao feedbackDao;
    private EwWarningConfDao warningConfDao;
    private EwManualConfDao manualConfDao;
    private EwWarningFlowDao flowDao;
    private EwWarningRecordDao recordDao;

    @Autowired
    public void setFeedbackDao(EwCallFeedbackDao feedbackDao) {
        this.feedbackDao = feedbackDao;
    }

    @Autowired
    public void setWarningConfDao(EwWarningConfDao warningConfDao) {
        this.warningConfDao = warningConfDao;
    }

    @Autowired
    public void setManualConfDao(EwManualConfDao manualConfDao) {
        this.manualConfDao = manualConfDao;
    }

    @Autowired
    public void setFlowDao(EwWarningFlowDao flowDao) {
        this.flowDao = flowDao;
    }

    @Autowired
    public void setRecordDao(EwWarningRecordDao recordDao) {
        this.recordDao = recordDao;
    }

    @Override
    public EwCallFeedback getFeedback(String callId) {
        return feedbackDao.getFeedback(callId);
    }

    @Override
    public int confirm(EwCallFeedbackRequest request) {
        return feedbackDao.confirm(request.toUpdate());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int manualConfirm(EwManualConfirmRequest request) {
        int result = 0;
        List<List<String>> callIdsList = ListUtils.splitList(new ArrayList<>(request.getCallIds()), 500);
        for (List<String> callIds : callIdsList) {
            result += feedbackDao.manualConfirm(callIds);
        }
        if (result > 0) {
            List<List<String>> warnIdsList = ListUtils.splitList(new ArrayList<>(request.getWarnIds()), 500);
            for (List<String> warnIds : warnIdsList) {
                recordDao.manualConfirm(warnIds);
                flowDao.manualConfirm(request.getConfirmBy(), warnIds);
            }
        }
        return result;
    }

    @Override
    public List<EwNoticeReceiverResponse> getNotNoticeReceiverList(String platformId) {
        EwManualConf conf = manualConfDao.getConf("1");
        if (null == conf || "0".equals(conf.getManualFlag())) {
            return new ArrayList<>();
        }
        Date etm = DateUtil.offsetMinute(new Date(), -conf.getManualDelay());
        Date stm = DateUtil.offsetMinute(etm, -conf.getManualExpired());
        Map<String, Object> params = new HashMap<>();
        params.put("stm", stm);
        params.put("etm", etm);
        return getNoticeReceiverList(platformId, UNCONFIRMED, params);
    }

    @Override
    public List<EwNoticeReceiverResponse> getNotifiedReceiverList(EwNoticeReceiverRequest request) {
        return getNoticeReceiverList(request.getPlatformId(), CONFIRMED, request.toQuery());
    }

    @Override
    public List<EwCallStatisticsResponse> getCallFailStatsList(EwCallStatisticsRequest request) {
        return feedbackDao.getCallFailStatsList(request.toQuery());
    }

    private List<EwNoticeReceiverResponse> getNoticeReceiverList(String platformId, CallStatus status, Map<String, Object> params) {
        EwWarningConf conf = warningConfDao.getConf(platformId);
        if (null == conf || "0".equals(conf.getFlag())) {
            return new ArrayList<>();
        }
        String adcd = StrUtil.padAfter(platformId, 15, "0");
        params.put("adcd", adcd);
        params.put("level", AdcdUtil.getAdLevel(adcd));
        params.put("status", status.getCode());
        params.put("exclusions", conf.getExclusions());
        List<EwNoticeReceiver> sourceList = feedbackDao.getNoticeReceiverList(params);
        if (CollectionUtils.isEmpty(sourceList)) {
            return new ArrayList<>();
        }
        List<EwNoticeReceiverResponse> targetList = new ArrayList<>();
        Map<String, List<EwNoticeReceiver>> groups = sourceList.stream().collect(Collectors.groupingBy(EwNoticeReceiver::getPhoneNo));
        for (Map.Entry<String, List<EwNoticeReceiver>> group : groups.entrySet()) {
            List<EwNoticeReceiver> receivers = group.getValue();
            Optional<EwNoticeReceiver> latest = receivers.stream().max(Comparator.comparing(EwNoticeReceiver::getCreateTime));
            if (!latest.isPresent()) {
                continue;
            }
            EwNoticeReceiverResponse receiver = new EwNoticeReceiverResponse(latest.get());
            receiver.setCallIds(receivers.stream().map(EwNoticeReceiver::getCallId).collect(Collectors.toSet()));
            receiver.setWarnIds(receivers.stream().map(EwNoticeReceiver::getWarnId).collect(Collectors.toSet()));
            targetList.add(receiver);
        }
        if (UNCONFIRMED.equals(status)) {
            targetList.sort(Comparator.comparing(EwNoticeReceiver::getCreateTime).reversed());
        } else if (CONFIRMED.equals(status)) {
            targetList.sort(Comparator.comparing(EwNoticeReceiver::getConfirmTime).reversed());
        }

        return targetList;
    }
}
