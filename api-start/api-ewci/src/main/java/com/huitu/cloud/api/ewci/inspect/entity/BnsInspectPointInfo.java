package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 检查重点清单
 */
@ApiModel(value = "检查重点清单")
public class BnsInspectPointInfo implements Serializable {

    @ApiModelProperty(value = "检查重点")
    @TableField(value = "INSPECT_POINT")
    private String inspectPoint;

    @ApiModelProperty(value = "检查类型编码")
    @TableField(value = "INSPECT_CODE")
    private String inspectCode;

    @ApiModelProperty(value = "序号")
    @TableField(value = "SNO")
    private Integer sno;

    public String getInspectPoint() {
        return inspectPoint;
    }

    public void setInspectPoint(String inspectPoint) {
        this.inspectPoint = inspectPoint;
    }

    public String getInspectCode() {
        return inspectCode;
    }

    public void setInspectCode(String inspectCode) {
        this.inspectCode = inspectCode;
    }

    public Integer getSno() {
        return sno;
    }

    public void setSno(Integer sno) {
        this.sno = sno;
    }
}
