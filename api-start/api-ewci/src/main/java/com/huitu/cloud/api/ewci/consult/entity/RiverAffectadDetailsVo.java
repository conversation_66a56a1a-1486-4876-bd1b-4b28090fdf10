package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 超标准洪水淹没范围信息 详细数据
 */
@ApiModel(value = "超标准洪水淹没范围信息详细数据")
public class RiverAffectadDetailsVo implements Serializable {

    @ApiModelProperty(value = "政区编码")
    private String xadcd;

    @ApiModelProperty(value = "政区名称")
    private String xadnm;

    @ApiModelProperty(value = "子集")
    private List<RiverAffectadDetailsChildVo> children;

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public List<RiverAffectadDetailsChildVo> getChildren() {
        return children;
    }

    public void setChildren(List<RiverAffectadDetailsChildVo> children) {
        this.children = children;
    }
}

