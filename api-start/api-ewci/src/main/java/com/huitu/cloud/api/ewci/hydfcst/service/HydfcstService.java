package com.huitu.cloud.api.ewci.hydfcst.service;

import com.huitu.cloud.api.ewci.hydfcst.entity.*;

import java.util.List;

/**
 * 水文预报服务
 *
 * <AUTHOR>
 */
public interface HydfcstService {

    /**
     * 获取河道和水库的水文预报时间过程数据。
     *
     * @param query 查询对象
     * @return 包含河道预报数据和水库预报数据的列表
     */
    List<StResultMap> listHydfcstTmProcessData(HydfcstQuery query);

    /**
     * 获取水文预报时间批次。
     *
     * @param query 查询对象
     * @return 水文预报时间批次列表
     */
    List<String> listHydfcstBatch(HydfcstTmBatchQuery query);


    /**
     * 获取预报统计结果。
     *
     * @param query 查询对象
     * @return 预报统计结果数据
     */
    HydFcstStat listForecastStatistics(HydfcstStatisticsQuery query);
}
