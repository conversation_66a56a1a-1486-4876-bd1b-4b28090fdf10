package com.huitu.cloud.api.shyj.warn.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value="LocWarnQueryForm对象", description="现地预警表")
public class LocWarnQueryForm extends PageBean {

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区级别")
    private Integer adlvl;

    @ApiModelProperty(value = "预警状态ID")
    private Integer warnStatusId;

    @ApiModelProperty(value = "预警等级ID")
    private Integer warnGradeId;

    @ApiModelProperty(value = "预警ID")
    private String warnid;

    @ApiModelProperty(value = "预警ID数组")
    private List<String> warnids;


    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getWarnStatusId() {
        return warnStatusId;
    }

    public void setWarnStatusId(Integer warnStatusId) {
        this.warnStatusId = warnStatusId;
    }

    public Integer getWarnGradeId() {
        return warnGradeId;
    }

    public void setWarnGradeId(Integer warnGradeId) {
        this.warnGradeId = warnGradeId;
    }

    public String getWarnid() {
        return warnid;
    }

    public void setWarnid(String warnid) {
        this.warnid = warnid;
    }

    public List<String> getWarnids() {
        return warnids;
    }

    public void setWarnids(List<String> warnids) {
        this.warnids = warnids;
    }

    public Integer getAdlvl() {
        return adlvl;
    }

    public void setAdlvl(Integer adlvl) {
        this.adlvl = adlvl;
    }
}
