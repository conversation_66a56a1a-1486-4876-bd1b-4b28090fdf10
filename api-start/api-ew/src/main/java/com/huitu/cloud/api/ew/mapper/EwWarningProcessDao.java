package com.huitu.cloud.api.ew.mapper;

import com.huitu.cloud.api.ew.entity.response.EwWarningProcessResponse;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwWarningProcessDao {

    /**
     * 根据预警ID，获取预警过程列表
     *
     * @param warnId 预警ID
     * @return 过程列表
     */
    List<EwWarningProcessResponse> getProcessList(String warnId);

    /**
     * 新增预警过程
     *
     * @param warnId 预警ID
     * @return 受影响的行数
     */
    int insert(String warnId);
}
