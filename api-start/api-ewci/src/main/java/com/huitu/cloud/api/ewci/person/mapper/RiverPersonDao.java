package com.huitu.cloud.api.ewci.person.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 江河责任人信息表Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface RiverPersonDao {

    /**
     * 根据测站编码，获取江河责任人信息列表
     *
     * @param stcd 测站编码
     * @return 责任人列表
     **/
    List<RiverPerson> getListByStcd(String stcd);

    /**
     * 分页获取江河责任人信息列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 分页后的信息列表
     **/
    IPage<RiverPerson> getPageList(IPage<RiverPerson> page, @Param("map") Map<String, Object> params);

    /**
     * 江河责任人汇总统计
     *
     * @param params
     * @return
     */
    List<RiverSummaryVo> getRiverSummaryList(@Param("map") Map<String, Object> params);

    /**
     * 批量导入江河责任人信息
     *
     * @param batchNo 批号
     * @return 导入失败的责任人信息
     **/
    List<BnsRiverPersonTmp> batchImport(String batchNo);

    /**
     * 获取江河责任人信息列表（仅包含姓名和手机号码）
     *
     * @param params 查询参数
     * @return 信息列表
     **/
    List<PersonInfo> getSimpleList(@Param("map") Map<String, Object> params);

    /**
     * 根据河流代码，行政区划代码，河段顺序删除
     *
     * @param params
     * @return
     */
    int delByRvCodeAndAdcdAndSno(@Param("map") Map<String, Object> params);

    /**
     * 添江河责任人
     *
     * @param list
     * @return
     */
    int insertList(@Param("list") List<RiverPersonB> list);
}
