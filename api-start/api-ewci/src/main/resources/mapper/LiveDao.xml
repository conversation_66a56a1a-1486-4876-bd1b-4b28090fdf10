<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.monitor.mapper.LiveDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getRainList" resultType="com.huitu.cloud.api.ewci.monitor.entity.LiveRain">
        SELECT B.ADCD, C.ADNM, A.STCD, B.STNM, B.STTP, B.STLC, A.ACCP,
        ROUND(ISNULL(R3.RAIN3, 0), 1) RAIN3, ROUND(ISNULL(R24.RAIN24, 0), 1) RAIN24 FROM (
        SELECT STCD, SUM(DRP) ACCP FROM ST_PPTN_R
        WHERE INTV = 1 AND TM > #{map.stm} AND TM &lt;= #{map.etm} AND (STCD IN
        <foreach collection="map.ids" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR STCD IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        ) GROUP BY STCD) A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = LEFT(B.ADCD, 6) + '*********'
        LEFT JOIN (SELECT STCD, SUM(RAIN) RAIN3 FROM BSN_FORECASTRAIN_F
        WHERE RAIN_TIME BETWEEN #{map.fstm} AND #{map.fetm3}
        GROUP BY STCD) R3 ON R3.STCD = A.STCD
        LEFT JOIN (SELECT STCD, SUM(RAIN) RAIN24 FROM BSN_FORECASTRAIN_F
        WHERE RAIN_TIME BETWEEN #{map.fstm} AND #{map.fetm24}
        GROUP BY STCD) R24 ON R24.STCD = A.STCD
        ORDER BY A.ACCP DESC, A.STCD ASC
    </select>
    <select id="getPrevadList" resultType="com.huitu.cloud.api.ewci.monitor.entity.IaCPrevad">
        SELECT P.ADCD, ISNULL(A.ADNM, B.ADNM) ADNM,D.ADNM XADNM, E.ADNM XZADNM, F.ADNM CADNM, P.PTCOUNT, P.LDAREA, P.PLAREA, P.ETCOUNT, P.ECOUNT1, P.ECOUNT2,
        P.ECOUNT3, P.ECOUNT4, P.HTCOUNT, P.HCOUNT1, P.HCOUNT2, P.HCOUNT3, P.HCOUNT4, A.PREVTP
        FROM IA_C_PREVAD P
        LEFT JOIN IA_C_ADINFO A ON A.ADCD = P.ADCD
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = P.ADCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = LEFT(B.ADCD, 6) + '*********'
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(B.ADCD, 9) + '000000'
        LEFT JOIN MDT_ADCDINFO_B F ON F.ADCD = LEFT(B.ADCD, 12) + '000'
        WHERE P.ADCD IN
        (SELECT DISTINCT ADCD FROM IA_C_DANAD WHERE DAND IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR DAND IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        )
        ORDER BY P.ADCD ASC
    </select>
    <select id="getDanadList" resultType="com.huitu.cloud.api.ewci.monitor.entity.IaCDanad">
        SELECT P.DAND, P.NAME, P.WSCD, STUFF((SELECT ',' + WSNM FROM IA_C_WATA WHERE WSCD IN (SELECT FIELD FROM
        dbo.fnSplitString(P.WSCD,',')) FOR XML PATH('')), 1, 1, '') WSNM, P.ADCD, ISNULL(B.ADNM, C.ADNM) ADNM,D.ADNM XADNM, E.ADNM XZADNM, F.ADNM CADNM,
        P.PTCOUNT, P.ETCOUNT, P.ECOUNT1, P.ECOUNT2, P.ECOUNT3, P.ECOUNT4, P.HTCOUNT, P.HCOUNT1, P.HCOUNT2, P.HCOUNT3,
        P.HCOUNT4,P.STATUS
        FROM IA_C_DANAD P
        LEFT JOIN IA_C_ADINFO B ON B.ADCD = P.ADCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = P.ADCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = LEFT(C.ADCD, 6) + '*********'
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(C.ADCD, 9) + '000000'
        LEFT JOIN MDT_ADCDINFO_B F ON F.ADCD = LEFT(C.ADCD, 12) + '000'
        WHERE P.DAND IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR P.DAND IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        ORDER BY P.ADCD ASC
    </select>
    <select id="getFloodPersonList" resultType="com.huitu.cloud.api.ewci.monitor.entity.FloodPerson">
        select c.adnm xz,ad.adnm xzc,f.ADNM xadnm,t.* from
        (select a.adcd,a.zrc,
        b1.realnm xs_realnm, b1.mobile xs_mobile,b1.duty xs_duty ,b2.realnm xz_realnm,  b2.mobile xz_mobile,b2.duty xz_duty ,b3.realnm xzc_realnm,
        b3.mobile xzc_mobile,b3.duty xzc_duty,b4.realnm zrc_realnm,  b4.mobile zrc_mobile,b4.duty zrc_duty,b5.realnm jc_realnm,
        b5.mobile jc_mobile,b6.realnm yj_realnm, b6.mobile yj_mobile,b7.realnm zx_realnm,b7.mobile zx_mobile,a.sortno,
        b8.realnm gldw_realnm, b8.mobile gldw_mobile,b8.duty gldw_duty
        from
        (select distinct adcd,zrc,sortno from BSN_SH_Person_B m
        WHERE ADCD IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR ADCD IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        )a
        left join BSN_SH_Person_B b1 on a.adcd=b1.adcd and a.zrc=b1.zrc and b1.rytp='1' and a.sortno=b1.sortno
        left join BSN_SH_Person_B b2 on a.adcd=b2.adcd and a.zrc=b2.zrc and b2.rytp='2' and a.sortno=b2.sortno
        left join BSN_SH_Person_B b3 on a.adcd=b3.adcd and a.zrc=b3.zrc and b3.rytp='3' and a.sortno=b3.sortno
        left join BSN_SH_Person_B b4 on a.adcd=b4.adcd and a.zrc=b4.zrc and b4.rytp='4' and a.sortno=b4.sortno
        left join BSN_SH_Person_B b5 on a.adcd=b5.adcd and a.zrc=b5.zrc and b5.rytp='5' and a.sortno=b5.sortno
        left join BSN_SH_Person_B b6 on a.adcd=b6.adcd and a.zrc=b6.zrc and b6.rytp='6' and a.sortno=b6.sortno
        left join BSN_SH_Person_B b7 on a.adcd=b7.adcd and a.zrc=b7.zrc and b7.rytp='7' and a.sortno=b7.sortno
        left join BSN_SH_Person_B b8 on a.adcd=b8.adcd and a.zrc=b8.zrc and b8.rytp='8' and a.sortno=b8.sortno
        )t left join BSN_ADCD_B ad on ad.adcd=t.adcd
        left join BSN_ADCD_B c on ad.padcd=c.adcd
        left join (SELECT ADCD, ADNM FROM MDT_ADCDINFO_B) f ON left(ad.adcd, 6)+'*********'= f.ADCD
        order by t.adcd,sortno
        <!--SELECT c.adnm xz,ad.adnm xzc,f.ADNM xadnm,T.ADCD,
        T.ZRC,
        P1.REALNM XS_REALNM,
        P1.DUTY XS_DUTY,
        P1.MOBILE XS_MOBILE,
        P2.REALNM XZ_REALNM,
        P2.DUTY XZ_DUTY,
        P2.MOBILE XZ_MOBILE,
        P3.REALNM XZC_REALNM,
        P3.DUTY XZC_DUTY,
        P3.MOBILE XZC_MOBILE,
        P4.REALNM ZRC_REALNM,
        P4.DUTY ZRC_DUTY,
        P4.MOBILE ZRC_MOBILE,
        P5.REALNM JC_REALNM,
        P5.DUTY JC_DUTY,
        P5.MOBILE JC_MOBILE,
        P6.REALNM YJ_REALNM,
        P6.DUTY YJ_DUTY,
        P6.MOBILE YJ_MOBILE,
        P7.REALNM ZY_REALNM,
        P7.DUTY ZY_DUTY,
        P7.MOBILE ZY_MOBILE,
        P8.REALNM GLDW_REALNM,
        P8.DUTY GLDW_DUTY,
        P8.MOBILE GLDW_MOBILE
        FROM (SELECT DISTINCT ADCD, ZRC, SORTNO
        FROM BSN_SH_PERSON_B
        WHERE ADCD IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR ADCD IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        ) T
        LEFT JOIN BSN_SH_PERSON_B P1
        ON P1.ADCD = T.ADCD AND P1.ZRC = T.ZRC AND P1.SORTNO = T.SORTNO AND P1.RYTP = '1'
        LEFT JOIN BSN_SH_PERSON_B P2
        ON P2.ADCD = T.ADCD AND P2.ZRC = T.ZRC AND P2.SORTNO = T.SORTNO AND P2.RYTP = '2'
        LEFT JOIN BSN_SH_PERSON_B P3
        ON P3.ADCD = T.ADCD AND P3.ZRC = T.ZRC AND P3.SORTNO = T.SORTNO AND P3.RYTP = '3'
        LEFT JOIN BSN_SH_PERSON_B P4
        ON P4.ADCD = T.ADCD AND P4.ZRC = T.ZRC AND P4.SORTNO = T.SORTNO AND P4.RYTP = '4'
        LEFT JOIN BSN_SH_PERSON_B P5
        ON P5.ADCD = T.ADCD AND P5.ZRC = T.ZRC AND P5.SORTNO = T.SORTNO AND P5.RYTP = '5'
        LEFT JOIN BSN_SH_PERSON_B P6
        ON P6.ADCD = T.ADCD AND P6.ZRC = T.ZRC AND P6.SORTNO = T.SORTNO AND P6.RYTP = '6'
        LEFT JOIN BSN_SH_PERSON_B P7
        ON P7.ADCD = T.ADCD AND P7.ZRC = T.ZRC AND P7.SORTNO = T.SORTNO AND P7.RYTP = '7'
        LEFT JOIN BSN_SH_PERSON_B P8
        ON P8.ADCD = T.ADCD AND P8.ZRC = T.ZRC AND P8.SORTNO = T.SORTNO AND P8.RYTP = '8'
        left join BSN_ADCD_B ad on ad.adcd=t.adcd
        left join BSN_ADCD_B c on ad.padcd=c.adcd
        left join (SELECT ADCD, ADNM FROM MDT_ADCDINFO_B) f ON left(ad.adcd, 6)+'*********'= f.ADCD
        order by t.adcd,t.sortno-->
    </select>
    <select id="getReservoirList" resultType="com.huitu.cloud.api.ewci.monitor.entity.ReservoirData">
        SELECT RSCD,RS_CODE,RS_NAME,RS_TYPE,MAIN_WR_TYPE,DAM_TYPE,MAIN_FL_TYPE,MUL_AVER_RUN,ENG_GRAD,
        DAM_SIZE_HIG,DAM_SIZE_LEN,MAX_DIS_FLOW,DES_FL_STAG,TOT_CAP,COR_SUR_AREA,B.ADNM,D.ADNM XADNM, E.ADNM XZADNM, F.ADNM CADNM,A.ADCD,WSNM
        FROM IA_C_RS A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = LEFT(B.ADCD, 6) + '*********'
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(B.ADCD, 9) + '000000'
        LEFT JOIN MDT_ADCDINFO_B F ON F.ADCD = LEFT(B.ADCD, 12) + '000'
        LEFT JOIN IA_C_WATA C ON A.WSCD = C.WSCD
        WHERE RS_CODE IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR RS_CODE IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        ORDER BY RS_CODE ASC
    </select>
    <select id="getSluiceList" resultType="com.huitu.cloud.api.ewci.monitor.entity.SluiceData">
        SELECT SPCD,GATE_CODE,GATE_NAME,RV_CODE,GATE_TYPE,HOLE_NUM,HOLE_WID,FL_GATE_FLOW,RUB_DAM_HIG,
        RUB_DAM_LEN,ADNM,A.ADCD,WSNM
        FROM IA_C_SLUICE A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        LEFT JOIN IA_C_WATA C ON A.WSCD = C.WSCD
        WHERE GATE_CODE IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR GATE_CODE IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        ORDER BY GATE_CODE ASC
    </select>
    <select id="getDikeList" resultType="com.huitu.cloud.api.ewci.monitor.entity.DikeData">
        SELECT
        DIKECD,DIKE_CODE,DIKE_NAME,RV_CODE,RV_BANK,DIKE_COR_BOUN,DIKE_TYPE,DIKE_STYL,DIKE_GRAD,PLAN_FL_STA,DIKE_LEN,FL_STA_LEN,
        ELE_SYS,DES_STAG,DIKE_HIG_MAX,DIKE_WID_MAX,ENG_TASK,DIKE_HIG_MIN,DIKE_WID_MIN,DAM_CRE_BEG_ELE,DAM_CRE_EDN_ELE,B.ADNM,D.ADNM XADNM, E.ADNM XZADNM, F.ADNM CADNM,A.ADCD,WSNM
        FROM IA_C_DIKE A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = LEFT(B.ADCD, 6) + '*********'
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT(B.ADCD, 9) + '000000'
        LEFT JOIN MDT_ADCDINFO_B F ON F.ADCD = LEFT(B.ADCD, 12) + '000'
        LEFT JOIN IA_C_WATA C ON A.WSCD = C.WSCD
        WHERE DIKE_CODE IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR DIKE_CODE IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        ORDER BY DIKE_CODE ASC
    </select>
    <select id="getCulvertList" resultType="com.huitu.cloud.api.ewci.monitor.entity.CulvertData">
        SELECT CULCD,CULNAME,PICID,HEIGHT,LENGHT,WIDTH,TYPE,COMMENTS,B.ADNM,A.ADCD
        FROM IA_C_CULVERT A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        WHERE CULCD IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR CULCD IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        ORDER BY CULCD ASC
    </select>
    <select id="getBridgeList" resultType="com.huitu.cloud.api.ewci.monitor.entity.BridgeData">
        SELECT BRCD,BRNAME,PICID,LENGTH,WIDTH,HEIGHT,TYPE,COMMENTS,ADNM,A.ADCD
        FROM IA_C_BRIDGE A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        WHERE BRCD IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR BRCD IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        ORDER BY BRCD ASC
    </select>
    <select id="getDaminfoList" resultType="com.huitu.cloud.api.ewci.monitor.entity.DaminfoData">
        SELECT DAMCD,DAMNAME,PICID,XHST,HEIGHT,WIDTH,MT,COMMENTS,ADNM,A.ADCD,WSNM
        FROM IA_C_DAMINFO A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        LEFT JOIN IA_C_WATA C ON A.WSCD = C.WSCD
        WHERE DAMCD IN
        <foreach collection="list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR DAMCD IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        ORDER BY DAMCD ASC
    </select>
    <select id="getLiveWarnStatusList" resultType="com.huitu.cloud.api.ewci.monitor.entity.MessageWarnStatusB">
        SELECT LEVEL, ADCD, SID FROM MESSAGEWARNSTATUS_B
        <where>
            AND LEFT (ADCD , #{map.adLevel}) = LEFT(#{map.adcd}, #{map.adLevel})
            <if test="map.adLevel == '4'.toString()">
                AND LEFT(ADCD, 6) NOT IN ('220581')
            </if>
            <if test="map.level !=null and map.level !=''">
                AND LEVEL = #{map.level}
            </if>
        </where>
        order by LEVEL, ADCD, SID
    </select>
    <select id="getRsvrPersonList" resultType="com.huitu.cloud.api.ewci.monitor.entity.RsvrPersonVo">
        select B.RES_NAME resName,(CASE WHEN RZFSLTDZ IS NULL THEN 0 ELSE RZFSLTDZ END) RZFSLTDZ,c.ADNM adnm,case when f.ADCD = '220581*********' then f.ADNM else e.ADNM  end sadnm,f.ADNM xadnm,t.* from
        (select a.RES_CODE resCode,a.adcd,a.sortno, a.RES_LOC resLoc, a.ENG_SCAL engScal,
        b1.REALNM saveFloodRealnm, b1.DEPTNM saveFloodDeptnm, b1.POST saveFloodPost, b1.MOBILE saveFloodMobile,
        b2.REALNM rescueRealnm, b2.DEPTNM rescueDeptnm, b2.POST rescuePost, b2.MOBILE rescueMobile,
        b3.REALNM comptDeptRealnm, b3.DEPTNM comptDeptDeptnm, b3.POST comptDeptPost, b3.MOBILE comptDeptMobile,
        b4.REALNM mangUnitRealnm, b4.DEPTNM mangUnitDeptnm, b4.POST mangUnitPost, b4.MOBILE mangUnitMobile,
        b5.REALNM patrolDutyRealnm, b5.MOBILE patrolDutyMobile
        from
        (select distinct RES_CODE, SORTNO, ADCD, RES_LOC, ENG_SCAL from BNS_RSVRPERSON_B m
        <where>
            RES_CODE IN
            <foreach collection="list" item="id" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="(index % 100) == 99">) OR RES_CODE IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{id}
            </foreach>
        </where>
        )a
        left join BNS_RSVRPERSON_B b1 on a.RES_CODE=b1.RES_CODE and b1.RESPERTP='1' and a.SORTNO = b1.SORTNO
        left join BNS_RSVRPERSON_B b2 on a.RES_CODE=b2.RES_CODE and b2.RESPERTP='2' and a.SORTNO = b2.SORTNO
        left join BNS_RSVRPERSON_B b3 on a.RES_CODE=b3.RES_CODE and b3.RESPERTP='3' and a.SORTNO = b3.SORTNO
        left join BNS_RSVRPERSON_B b4 on a.RES_CODE=b4.RES_CODE and b4.RESPERTP='4' and a.SORTNO = b4.SORTNO
        left join BNS_RSVRPERSON_B b5 on a.RES_CODE=b5.RES_CODE and b5.RESPERTP='5' and a.SORTNO = b5.SORTNO
        ) t
        join ATT_RES_BASE B on t.resCode=B.RES_CODE
        LEFT JOIN (select ADCD,ADNM FROM MDT_ADCDINFO_B) c on c.ADCD=t.adcd
        LEFT JOIN (SELECT ADCD, ADNM FROM MDT_ADCDINFO_B) e ON e.ADCD = left(t.adcd, 4)+'*********00'
        LEFT JOIN (SELECT ADCD, ADNM FROM MDT_ADCDINFO_B) f ON f.ADCD = left(t.adcd, 6)+'*********'
        LEFT JOIN MDT_ADCDINFO_E g ON g.ADCD = left(t.adcd, 4)+'*********00'
        LEFT JOIN (
        SELECT A.RES_CODE,MAX(D.RZ)-MAX(E.FSLTDZ) RZFSLTDZ FROM ATT_RES_BASE A
        JOIN bsn_objonly_b B ON A.RES_CODE=B.OBJCD
        JOIN bsn_objonly_b C ON B.OBJID=C.OBJID AND C.OBJTP=1
        JOIN (SELECT A.STCD,A.RZ FROM ST_RSVR_R A JOIN (SELECT STCD,MAX(TM) TM FROM ST_RSVR_R WHERE TM>=(CASE WHEN CONVERT(varchar(2), GETDATE(), 14)>08 THEN CONVERT(varchar(100), GETDATE(), 23)+' 08:00:00' ELSE CONVERT(varchar(100), GETDATE()-1, 23)+' 08:00:00' END) GROUP BY STCD) B ON A.STCD=B.STCD AND A.TM=B.TM) D ON C.OBJCD=D.STCD
        JOIN (SELECT STCD,FSLTDZ FROM ST_RSVRFSR_B WHERE (CASE WHEN CONVERT(varchar(2), GETDATE(), 14)>08 THEN RIGHT(CONVERT(varchar(100), GETDATE(), 12),4) ELSE RIGHT(CONVERT(varchar(100), GETDATE()-1, 12),4) END) BETWEEN BGMD AND EDMD) E ON C.OBJCD=E.STCD
        JOIN ST_RSVRFCCH_B F ON C.OBJCD=F.STCD
        GROUP BY A.RES_CODE
        ) D ON B.RES_CODE=D.RES_CODE
        where   1=1  AND B.RES_NAME is not null
<!--        <if test="map.ad != null and map.ad != ''">-->
<!--            and left(c.ADCD,#{map.level})=#{map.ad}-->
<!--        </if>-->
<!--        <if test="map.level !=null and map.level =='4'.toString()">-->
<!--            and   left(c.ADCD,6) != '220581' and left(c.ADCD,6) != '220381'-->
<!--        </if>-->
        order by  g.sortno, t.adcd, B.ENG_SCAL
    </select>

</mapper>
