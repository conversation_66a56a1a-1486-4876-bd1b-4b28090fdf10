package com.huitu.cloud.api.syq.skdt.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 水库调度信息表水库库面雨量
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@ApiModel(value="RsvrAvgRain", description="水库调度库面雨量信息")
public class RsvrAvgRain implements Serializable {
    @ApiModelProperty(value = "库区面雨量")
    private String avgRain;
    @ApiModelProperty(value = "水库工程编码")
    private String resCode;
    @ApiModelProperty(value = "水库名称")
    private String resName;
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    public String getAvgRain() {
        return avgRain;
    }

    public void setAvgRain(String avgRain) {
        this.avgRain = avgRain;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }
}
