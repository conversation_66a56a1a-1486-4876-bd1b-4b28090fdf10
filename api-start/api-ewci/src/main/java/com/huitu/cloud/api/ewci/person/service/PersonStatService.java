package com.huitu.cloud.api.ewci.person.service;

import com.huitu.cloud.api.ewci.person.entity.PersonAlterStatVo;
import com.huitu.cloud.api.ewci.person.entity.PersonStatAdcdInfo;
import com.huitu.cloud.api.ewci.person.entity.PersonStatVo;

import java.util.List;

/**
 * 责任人统计
 *
 * <AUTHOR>
 */
public interface PersonStatService {

    /**
     * 责任人统计数据
     *
     * @param adcd
     * @return
     */
    List<PersonStatVo> getPersonStatList(String adcd);

    /**
     * 责任人统计政区表头
     *
     * @param adcd
     * @return
     */
    List<PersonStatAdcdInfo> getAdcdInfoList(String adcd);

    /**
     * 责任人变更统计
     *
     * @param adcd
     * @return
     */
    List<PersonAlterStatVo> getPersonAlterStatList(String adcd);
}
