package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectWagaRun;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 * 水闸工程运行
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectWagaRunDao {


    /**
     * 添加 水闸工程运行
     *
     * @param entity
     * @return
     */
    int insertInspectWagaRun(BnsInspectWagaRun entity);

    /**
     * 修改 水闸工程运行
     *
     * @param entity
     * @return
     */
    int updateInspectWagaRun(BnsInspectWagaRun entity);

    /**
     * 删除 水闸工程运行
     *
     * @return
     */
    int deleteInspectWagaRun(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

