package com.huitu.cloud.api.ewci.hydrology.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnStationResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.request.HyWarnIndexRequest;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnIndexResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 洪水预警指标Dao
 */
@Mapper
public interface HyWarnIndexDao {

    /**
     * 获取预警站列表
     *
     * @param params 查询参数
     * @return 预警站列表
     */
    List<HyWarnStationResponse> getStationList(Map<String, Object> params);

    /**
     * 分页获取预警指标列表
     *
     * @param page   分页对象
     * @param params 查询参数
     * @return 指标列表
     */
    IPage<HyWarnIndexResponse> getPageList(IPage<HyWarnIndexResponse> page, @Param("map") Map<String, Object> params);

    /**
     * 保存洪水预警指标
     *
     * @param request 请求对象
     * @return 受影响的行数
     */
    int save(HyWarnIndexRequest request);

    /**
     * 删除洪水预警指标
     *
     * @param stcd 测站编码
     * @return 受影响的行数
     */
    int delete(String stcd);

    /**
     * 更新启用标识
     *
     * @param stcd 测站编码
     * @param usfl 启用标识
     * @return 受影响的行数
     */
    int updateUseFlag(@Param("stcd") String stcd, @Param("usfl") String usfl);
}
