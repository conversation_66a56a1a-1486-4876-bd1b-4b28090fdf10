package com.huitu.cloud.api.ew.controller;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ew.entity.request.EwWarnGradeSummaryRequest;
import com.huitu.cloud.api.ew.entity.request.EwWarnMessageRequest;
import com.huitu.cloud.api.ew.entity.request.EwWarningSummaryRequest;
import com.huitu.cloud.api.ew.entity.response.EwWarnGradeStatisticsResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarnGradeSummaryResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarnMessageStatistics;
import com.huitu.cloud.api.ew.entity.response.EwWarningSummaryResponse;
import com.huitu.cloud.api.ew.service.EwStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监测预警统计服务
 *
 * <AUTHOR>
 */
@Api(tags = "监测预警统计服务")
@RestController
@RequestMapping("/api/ew/statistics")
public class EwStatisticsResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "2174a3d2-b5bb-4620-9393-f63f2a6261d9";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final EwStatisticsService baseService;

    public EwStatisticsResource(EwStatisticsService baseService) {
        this.baseService = baseService;
    }

    @ApiOperation(value = "获取监测预警汇总统计信息", notes = "作者：zyj")
    @PostMapping("select-warn-summary")
    public ResponseEntity<SuccessResponse<EwWarningSummaryResponse>> getWarningSummary(@Validated @RequestBody EwWarningSummaryRequest query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getWarningSummary(query)));
    }

    @ApiOperation(value = "政区预警统计", notes = "省市县预警状态统计概况")
    @PostMapping(value = "select-ad-warn-statistics")
    public ResponseEntity<SuccessResponse<EwWarnMessageStatistics>> getAdWarnStatistics(@RequestBody EwWarnMessageRequest query) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.getAdWarnStatistics(query)));
    }

    @ApiOperation(value = "政区预警统计下级列表", notes = "省市县预警状态统计概况下级列表（树形结构展示）")
    @PostMapping(value = "select-ad-warn-statistics-tree-list")
    public ResponseEntity<SuccessResponse<List<EwWarnMessageStatistics>>> getAdWarnStatisticsTreeList(@RequestBody EwWarnMessageRequest query) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.getAdWarnStatisticsTreeList(query)));
    }

    @ApiOperation(value = "获取山洪预警等级汇总统计信息", notes = "作者：zhhw")
    @PostMapping("select-warn-grade-summary")
    public ResponseEntity<SuccessResponse<EwWarnGradeSummaryResponse>> getWarnGradeSummary(@Validated @RequestBody EwWarnGradeSummaryRequest query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getWarnGradeSummary(query)));
    }

    @ApiOperation(value = "获取政区山洪预警等级统计列表", notes = "省市县预警统计概况下级列表")
    @PostMapping(value = "select-ad-warn-grade-list")
    public ResponseEntity<SuccessResponse<List<EwWarnGradeStatisticsResponse>>> getAdWarnGradeList(@RequestBody EwWarnGradeSummaryRequest query) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.getAdWarnGradeList(query)));
    }



    @ApiOperation(value = "导出政区山洪预警等级统计列表", notes = "省市县预警统计概况下级列表")
    @PostMapping(value = "export-ad-warn-grade-list")
    public void exportAdWarnGradeList(@RequestBody EwWarnGradeSummaryRequest query, HttpServletResponse response) {
        baseService.exportAdWarnGradeList(query, response);

    }







}
