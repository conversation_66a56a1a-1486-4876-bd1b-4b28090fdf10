package com.huitu.cloud.api.ewci.tencent.entity;

/**
 * <p>
 * 消息推送参数类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-24
 */
public class SendMsgQo {
    //推送的消息内容，json字符串
    private String message;
    //消息的主题类型
    private String topic;
    //发送者
    private String msgFrom;
    //接收者
    private String msgTo;
    //账号
    private String appname;
    //秘钥
    private String socket;



    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getMsgFrom() {
        return msgFrom;
    }

    public void setMsgFrom(String msgFrom) {
        this.msgFrom = msgFrom;
    }

    public String getMsgTo() {
        return msgTo;
    }

    public void setMsgTo(String msgTo) {
        this.msgTo = msgTo;
    }

    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }

    public String getSocket() {
        return socket;
    }

    public void setSocket(String socket) {
        this.socket = socket;
    }
}
