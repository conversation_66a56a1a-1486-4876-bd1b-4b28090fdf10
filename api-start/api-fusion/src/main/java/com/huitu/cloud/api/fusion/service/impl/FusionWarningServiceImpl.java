package com.huitu.cloud.api.fusion.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.fusion.entity.common.FusionDataFrom;
import com.huitu.cloud.api.fusion.entity.common.FusionPushMode;
import com.huitu.cloud.api.fusion.entity.ext.*;
import com.huitu.cloud.api.fusion.entity.request.AscriptionTypeCountReq;
import com.huitu.cloud.api.fusion.entity.request.FusionFollowupRequest;
import com.huitu.cloud.api.fusion.entity.request.FusionWarningRecordReq;
import com.huitu.cloud.api.fusion.entity.request.FusionWarningSendingReq;
import com.huitu.cloud.api.fusion.entity.response.*;
import com.huitu.cloud.api.fusion.mapper.*;
import com.huitu.cloud.api.fusion.service.FusionWarningService;
import com.huitu.cloud.util.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.OutputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 山洪雨量升级预警服务 实现类
 */
@Service
public class FusionWarningServiceImpl implements FusionWarningService {

    @Autowired
    private FusionWarningRecordDao recordDao;

    @Autowired
    private FusionWarningConfDao confDao;

    @Autowired
    private FusionWarningFlowDao flowDao;

    @Autowired
    private FusionMessageDao messageDao;

    @Autowired
    private FusionReceiverDao receiverDao;

    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;


    @Override
    public IPage<FusionWarningRecordResp> getPageList(FusionWarningRecordReq request) {
        return recordDao.getPageList(request.toPage(), request.toQuery());
    }

    @Override
    public AscriptionTypeCountResp getAscriptionTypeCount(AscriptionTypeCountReq request) {
        return recordDao.getAscriptionTypeCount(request.toQuery());
    }

    @Override
    public FusionSnapshotMonitorResp getSnapshotMonitor(String warnId) {
        return recordDao.getSnapshotMonitor(warnId);
    }

    @Override
    public FusionSnapshotIndexResp getSnapshotIndex(String warnId) {
        return recordDao.getSnapshotIndex(warnId);
    }

    @Override
    public IPage<FusionWarningSendingResp> getSendingPageList(FusionWarningSendingReq request) {
        return recordDao.getSendingPageList(request.toPage(), request.toQuery());
    }

    @Override
    public boolean isEnabled(String platformId) {
        return confDao.isEnabled(platformId);
    }

    @Override
    public IPage<FusionWarningRecordResp> getDetailPageList(FusionWarningRecordReq request) {
        return recordDao.getDetailPageList(request.toPage(), request.toQuery());
    }

    @Override
    public IPage<FusionWarningRecordResp> getWarnLatestPageList(FusionWarningRecordReq request) {
        IPage<FusionWarningRecordResp> page = recordDao.getWarnLatestPageList(request.toPage(), request.toQuery());
        if (null != page) {
            List<FusionWarningRecordResp> records = page.getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                records.forEach(itm -> {
                    itm.setLatestWarn(true);
                    itm.setRequest(request);
                });
            }
        }
        return page;
    }

    @Override
    public List<FusionWarningDigestResponse> getDigestList(List<String> warnIds) {
        warnIds = warnIds.stream().distinct().collect(Collectors.toList());
        if (warnIds.size() <= 500) {
            return recordDao.getDigestList(warnIds);
        }
        List<List<String>> lists = ListUtils.splitList(warnIds, 500);
        List<CompletableFuture<List<FusionWarningDigestResponse>>> futures = new ArrayList<>();
        for (List<String> list : lists) {
            futures.add(CompletableFuture.supplyAsync(() -> recordDao.getDigestList(list), asyncExecutor));
        }
        List<FusionWarningDigestResponse> digests = Collections.synchronizedList(new ArrayList<>());
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .whenComplete((v, th) -> futures.forEach(cf -> {
                    try {
                        digests.addAll(cf.get());
                    } catch (Exception ignored) {
                    }
                })).join();
        return digests;
    }

    @Override
    public List<FusionWarningDetailResp> getWarnDetail(String warnId) {
        return recordDao.getWarnDetail(warnId);
    }

    @Override
    public List<FusionWarningFlowResp> getFlowList(String warnId) {
        return flowDao.getFlowList(warnId);
    }

    @Override
    public FusionWarningRecordResp getRecord(String warnId) {
        return recordDao.getRecord(warnId);
    }

    @Override
    public List<FusionWarningMessageResp<?>> getMessageList(String warnId) {
        CompletableFuture<List<FusionWarningMessage>> sourceListFuture =
                CompletableFuture.supplyAsync(() -> messageDao.getMessageList(warnId), asyncExecutor);
        CompletableFuture<List<FusionSmsReceiver>> smsReceiverListFuture =
                CompletableFuture.supplyAsync(() -> receiverDao.getSmsReceiverList(warnId), asyncExecutor);
        CompletableFuture<List<FusionXccReceiver>> xccReceiverListFuture =
                CompletableFuture.supplyAsync(() -> receiverDao.getXccReceiverList(warnId), asyncExecutor);

        AtomicReference<List<FusionWarningMessageResp<?>>> reference = new AtomicReference<>();
        CompletableFuture.allOf(sourceListFuture, smsReceiverListFuture, xccReceiverListFuture).whenComplete((v, th) -> {
            List<FusionWarningMessageResp<?>> messageList = new ArrayList<>();
            try {
                List<FusionWarningMessage> sourceList = sourceListFuture.get();
                if (!sourceList.isEmpty()) {

                    // 预警短信接收人
                    List<FusionWarningMessage> smsMessages = sourceList.stream()
                            .filter(msg -> FusionPushMode.SMS.equalsCode(msg.getPushMode()))
                            .collect(Collectors.toList());
                    if (!smsMessages.isEmpty()) {
                        List<FusionSmsReceiver> smsReceivers = smsReceiverListFuture.get();
                        List<String> messageIdList = smsMessages.stream()
                                .map(FusionWarningMessage::getMsgId).collect(Collectors.toList());
                        List<FusionSmsReceiver> receivers = smsReceivers.stream()
                                .filter(msg -> messageIdList.contains(msg.getMsgId())).collect(Collectors.toList());
                        messageList.add(new FusionWarningMessageResp<>(smsMessages.get(0), receivers));
                    }

                    // 预警语音接收人
                    List<FusionWarningMessage> xccMessages = sourceList.stream()
                            .filter(msg -> FusionPushMode.XCC.equalsCode(msg.getPushMode()))
                            .collect(Collectors.toList());
                    if (!xccMessages.isEmpty()) {
                        List<FusionXccReceiver> xccReceivers = xccReceiverListFuture.get();
                        List<String> messageIdList = xccMessages.stream()
                                .map(FusionWarningMessage::getMsgId).collect(Collectors.toList());
                        List<FusionXccReceiver> receivers = xccReceivers.stream()
                                .filter(msg -> messageIdList.contains(msg.getMsgId())).collect(Collectors.toList());
                        messageList.add(new FusionWarningMessageResp<>(xccMessages.get(0), receivers));
                    }
                }
            } catch (Exception ignored) {
            }
            reference.set(messageList);
        }).join();
        return reference.get();
    }

    @Override
    public IPage<FusionFollowupResponse> getFollowupPageList(FusionFollowupRequest request) {
        IPage<FusionFollowupResponse> page = recordDao.getFollowupPageList(request.toPage(), request.toQuery());
        if (null != page) {
            List<FusionFollowupResponse> records = page.getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                List<CompletableFuture<Map<String, FusionFollowupData>>> futures = new ArrayList<>();
                // 提取测站及预警时间
                Map<String, List<FusionFollowupQuery>> queryGroups = records.stream()
                        .map(itm -> FusionFollowupQuery.builder()
                                .stcd(itm.getStcd())
                                .stadtp(itm.getStadtp())
                                .stm(itm.getWarnTime())
                                .etm(DateUtil.offsetMinute(itm.getWarnTime(), 120))
                                .build())
                        .distinct()
                        .collect(Collectors.groupingBy(FusionFollowupQuery::getStadtp));
                // 异步加载测站后续降雨
                for (Map.Entry<String, List<FusionFollowupQuery>> group : queryGroups.entrySet()) {
                    FusionDataFrom from = FusionDataFrom.get(group.getKey());
                    List<List<FusionFollowupQuery>> lists = ListUtils.splitList(group.getValue(), 200);
                    for (List<FusionFollowupQuery> list : lists) {
                        futures.add(CompletableFuture.supplyAsync(() -> getFollowupData(from, list), asyncExecutor));
                    }
                }
                Map<String, FusionFollowupData> followupData = Collections.synchronizedMap(new HashMap<>());
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).whenComplete((v, th) -> {
                    for (CompletableFuture<Map<String, FusionFollowupData>> future : futures) {
                        try {
                            Map<String, FusionFollowupData> result = future.get();
                            if (null != result) {
                                followupData.putAll(result);
                            }
                        } catch (Exception ignored) {
                        }
                    }
                }).join();
                // 读取并设置测站后续降雨
                if (!followupData.isEmpty()) {
                    for (FusionFollowupResponse record : records) {
                        FusionFollowupData data = followupData.get(record.groupKey());
                        if (null == data) {
                            continue;
                        }
                        record.setAccp30(data.getAccp30());
                        record.setAccp60(data.getAccp60());
                        record.setAccp90(data.getAccp90());
                        record.setAccp120(data.getAccp120());
                    }
                }
            }
        }
        return page;
    }

    @Override
    public void followupExport(FusionFollowupRequest query, OutputStream output) {
        IPage<FusionFollowupResponse> page = getFollowupPageList(query);
        List<FusionFollowupResponse> records;
        if (null != page && !CollectionUtils.isEmpty(page.getRecords())) {
            records = page.getRecords();
            int current = (int) Math.max(1L, page.getCurrent());
            int pageSize = (int) Math.max(0L, page.getSize());
            int startIndex = (current - 1) * pageSize;
            for (int i = 0; i < records.size(); i++) {
                records.get(i).setSortno(startIndex + i + 1);
            }
        } else {
            records = new ArrayList<>();
        }
        EasyExcel.write(output, FusionFollowupResponse.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("后续降雨")
                .doWrite(records);
    }

    private Map<String, FusionFollowupData> getFollowupData(FusionDataFrom from, List<FusionFollowupQuery> queries) {
        List<FusionFollowupPptn> pptns = recordDao.getFollowupPptnList(from.getTable(), from.getIntv(), queries);
        if (!CollectionUtils.isEmpty(pptns)) {
            // 按测站编码和预警时间分钟分组
            Map<String, List<FusionFollowupPptn>> groups = pptns.stream()
                    .collect(Collectors.groupingBy(FusionFollowupPptn::groupKey));
            // 分组计算累计降水量
            Map<String, FusionFollowupData> result = new HashMap<>();
            for (Map.Entry<String, List<FusionFollowupPptn>> group : groups.entrySet()) {
                FusionFollowupData data = new FusionFollowupData();
                data.setAccp30(calcAccp(group.getValue(), 30));
                data.setAccp60(calcAccp(group.getValue(), 60));
                data.setAccp90(calcAccp(group.getValue(), 90));
                data.setAccp120(calcAccp(group.getValue(), 120));
                result.put(group.getKey(), data);
            }
            return result;
        }
        return null;
    }

    private static Double calcAccp(List<FusionFollowupPptn> pptns, int offsetMinute) {
        List<Double> list = pptns.stream()
                .filter(itm -> {
                    Date etm = DateUtil.offsetMinute(itm.getWtime(), offsetMinute);
                    return null != itm.getDrp() && DateUtil.compare(itm.getDtime(), etm) <= 0;
                })
                .map(FusionFollowupPptn::getDrp).collect(Collectors.toList());
        // 因未降雨(0)和无数据(null)的概念差异，此处应返回null
        if (list.isEmpty()) {
            return null;
        }
        // 计算累计降雨并保留1位小数
        return NumberUtil.round(list.stream().mapToDouble(Double::doubleValue).sum(), 1).doubleValue();
    }

    public static void main(String[] args) {
        /*String formatStr = "yyyy-MM-dd HH:mm:ss";
        List<FusionFollowupPptn> list = new ArrayList<>();
        list.add(FusionFollowupPptn.builder()
                .wtime(DateUtil.parse("2025-08-01 08:00:00", formatStr))
                .dtime(DateUtil.parse("2025-08-01 08:30:01", formatStr))
                .drp(20D)
                .build());
        System.err.println(calcAccp(list, 30));*/

        /*List<FusionFollowupResponse> records = new ArrayList<>();
        FusionFollowupResponse record = new FusionFollowupResponse();
        record.setSortno(1);
        record.setWarnName("通化县二密镇铜山社区2025年4号预警[1时]");
        record.setAdnm("铜山社区");
        record.setWarnTime(new Date());
        record.setStnm("马当村");
        record.setStadtp("1");
        record.setRaccp(51.2D);
        record.setRdrpt(50);
        record.setStdt(1);
        record.setFaccp(33.9D);
        record.setFdrpt(30);
        record.setFcdt(3);
        record.setAccp30(10.8D);
        record.setAccp60(20.6D);
        record.setAccp90(30.4D);
        record.setAccp120(40.5D);
        records.add(record);
        try (FileOutputStream outputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\后续降雨.xlsx")) {
            EasyExcel.write(outputStream, FusionFollowupResponse.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("后续降雨")
                    .doWrite(records);
        } catch (Exception ignored) {
        }*/
    }
}
