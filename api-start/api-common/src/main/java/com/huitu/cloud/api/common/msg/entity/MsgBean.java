package com.huitu.cloud.api.common.msg.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2019-09-18
 */
@ApiModel(value="短信发送对象", description="短信发送需要的信息内容")
public class MsgBean extends MessageInfo {
    @ApiModelProperty(value = "发送人员或广播信息集合")
    private List<Message> msg;

    public List<Message> getMsg() {
        return msg;
    }

    public void setMsg(List<Message> msg) {
        this.msg = msg;
    }
}
