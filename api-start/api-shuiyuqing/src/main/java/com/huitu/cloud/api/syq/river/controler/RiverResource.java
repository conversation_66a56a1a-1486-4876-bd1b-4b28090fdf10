package com.huitu.cloud.api.syq.river.controler;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.syq.river.entity.*;
import com.huitu.cloud.api.syq.river.entity.export.ExportQueryMaxCxxsw;
import com.huitu.cloud.api.syq.river.entity.export.ExportQueryOneRiver;
import com.huitu.cloud.api.syq.river.entity.export.ExportQueryRiverDayAvg;
import com.huitu.cloud.api.syq.river.entity.export.ExportRiverQo;
import com.huitu.cloud.api.syq.river.service.RiverService;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 河道水情表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */

@RestController
@Api(tags = "河道水情信息")
@Validated
@RequestMapping("/api/syq/river")
public class
RiverResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "E1D50A7B-4476-4026-A5C5-BE873B4B0049";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private RiverService baseService;


    @ApiOperation(value = "最新河道水情信息", notes = "查询某段时间内的最新水情信息，支持分页")
    @PostMapping(value = "select-latest-by-page")
    public ResponseEntity<SuccessResponse<Page<StRiverVo>>> getStRiverByPage(@Validated @RequestBody RiverQo baseDao) throws Exception {

        IPage<StRiverVo> list = baseService.getRiverByConditon(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm(), baseDao.getStType(),
                baseDao.getStnm(), baseDao.isDataFlag(), baseDao.isWarnFlag(), baseDao.getBscd(), baseDao.getPageNum(), baseDao.getPageSize(), baseDao.getIsOut(), baseDao.getIsFollow(), baseDao.isVideoFlag());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "最新河道水情导出", notes = "最新河道水情导出")
    @PostMapping(value = "export-select-latest")
    public void exportRiverByConditon(@Validated @RequestBody ExportRiverQo exportRiverQo) throws Exception {
        baseService.exportRiverByConditon(exportRiverQo.getType(), exportRiverQo.getAdcd(), exportRiverQo.getStm(), exportRiverQo.getEtm(), exportRiverQo.getStType(),
                exportRiverQo.getStnm(), exportRiverQo.isDataFlag(), exportRiverQo.isWarnFlag(), exportRiverQo.getBsnm(), exportRiverQo.isVideoFlag());
    }

    @ApiOperation(value = "单站河道水位列表", notes = "查询一段时间内单站的水位时间列表，水位过程线以及水位列表")
    @GetMapping(value = "get-river-by-tm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RiverTmVo>>> getRiverTmList(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<RiverTmVo> list = baseService.getRiverList(stcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "单站水位流量过程信息导出", notes = "单站水位流量过程信息导出")
    @PostMapping(value = "export-get-river-by-tm")
    public void exportRiverTm(@RequestBody ExportQueryOneRiver queryOneRiver) throws Exception {
        baseService.exportRiverTm(queryOneRiver.getStm(), queryOneRiver.getEtm(), queryOneRiver.getStcd());
    }

    @ApiOperation(value = "单站河道大断面数据列表", notes = "根据施测时间单站河道大断面数据列表")
    @GetMapping(value = "get-duan-mian-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mstm", value = "施测时间 格式yyyy-MM-dd", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<StRvsectB>>> getRiverDuanMianList(@RequestParam String stcd, @RequestParam String mstm) throws Exception {
        List<StRvsectB> list = baseService.getRiverDuanMianList(stcd, mstm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "单站河道大断面的施测时间列表", notes = "查询施测时间列表")
    @GetMapping(value = "get-duan-mian-tm-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<StRvsectB>>> getRiverDuanMianTmList(@RequestParam String stcd) throws Exception {
        List<String> list = baseService.getRiverDuanMianTmList(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "单站河道大断面最新施测时间的断面数据列表", notes = "单站河道大断面最新施测时间的断面数据列表")
    @GetMapping(value = "get-duan-mian-new-time-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<StRvsectB>>> getNewRiverDuanMianList(@RequestParam String stcd) throws Exception {
        List<StRvsectB> list = baseService.getNewRiverDuanMianList(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "河道日旬月均值查询", notes = "分页查询一段时间内河道日旬月均值")
    @PostMapping(value = "select-day-avg-river-by-page")
    public ResponseEntity<SuccessResponse<Page<RiverDayAvg>>> getDayAvgRiverByPage(@RequestBody QueryRiverDayAvg riverDayAvg) throws Exception {
        IPage<RiverDayAvg> list = baseService.getDayAvgRiverByPage(riverDayAvg.getStm(), riverDayAvg.getEtm(), riverDayAvg.getStList(), riverDayAvg.getTmType(), riverDayAvg.getPageNum(), riverDayAvg.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "河道日旬月均值导出", notes = "河道日旬月均值导出")
    @PostMapping(value = "export-select-day-avg-river")
    public void exportDayAvgRiver(@RequestBody ExportQueryRiverDayAvg queryRiverDayAvg) throws Exception {
        baseService.exportDayAvgRiver(queryRiverDayAvg.getStm(), queryRiverDayAvg.getEtm(), queryRiverDayAvg.getStList(), queryRiverDayAvg.getTmType());
    }

    @ApiOperation(value = "单站水位流量关系名称查询", notes = "单站水位流量关系名称下拉列表查询")
    @GetMapping(value = "select-zq-name-by-st")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<QueryStZqrlb>>> getZqNameBySt(@RequestParam String stcd) throws Exception {
        List<QueryStZqrlb> list = baseService.getZqNameBySt(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "单站水位流量关系曲线", notes = "单站水位流量关系曲线列表信息查询")
    @PostMapping(value = "select-zq-by-st-name")
    public ResponseEntity<SuccessResponse<List<StZqrlb>>> getZq(@RequestBody QueryZqList zq) throws Exception {
        List<StZqrlb> list = baseService.getZq(zq.getStcd(), zq.getList());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }


    @ApiOperation(value = "多站河道水情预报", notes = "分页查询多站河道水情预报")
    @PostMapping(value = "select-forecast-by-page")
    public ResponseEntity<SuccessResponse<Page<StForecastf>>> getZqForecastByPage(@RequestBody QueryStForecastf stForecastf) throws Exception {
        IPage<StForecastf> list = baseService.getZqForecastByPage(
                stForecastf.getAdcd(), stForecastf.getStnm(), stForecastf.getWnstatus(), stForecastf.getStm(), stForecastf.getEtm(), stForecastf.getStcds(), stForecastf.getPageNum(), stForecastf.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "单站河道水情预报", notes = "单站河道水情预报")
    @GetMapping(value = "select-forecast-by-one-st")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<OneStForecast>>> getZqForecastByOneSt(@RequestParam String stcd) throws Exception {
        List<OneStForecast> list = baseService.getZqForecastByOneSt(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "河道设计洪水", notes = "河道设计洪水")
    @PostMapping(value = "getStDsnfld")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<StDsnfld>> getStDsnfld(@RequestBody StDsnfldQuery param) throws Exception {
        StDsnfld stDsnfld = baseService.getStDsnfld(param);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", stDsnfld));
    }

    @ApiOperation(value = "河道水情极值", notes = "河道水情极值")
    @PostMapping(value = "listStRvevs")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "sttdrcd", value = "时段标志(6-年)", required = true, dataType = "Integer")
    })
    public ResponseEntity<SuccessResponse<List<StRvevsR>>> listStRvevs(@RequestBody StRvevsQuery param) throws Exception {
        List<StRvevsR> list = baseService.listStRvevs(param);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询最大超警水位统计", notes = "查询河道最大超警戒，超保证的统计信息")
    @PostMapping(value = "select-river-max-warn-list")
    public ResponseEntity<SuccessResponse<List<StRiverVo>>> getStRiverMaxWarnList(@Validated @RequestBody QueryMaxCxxsw baseDao) throws Exception {
        List<StRiverVo> list = baseService.getStRiverMaxWarnList(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "最大超警水位统计导出", notes = "最大超警水位统计导出")
    @PostMapping(value = "export-select-river-max-warn")
    public void exportStRiverMaxWarn(@RequestBody ExportQueryMaxCxxsw queryMaxCxxsw) throws Exception {
        baseService.exportStRiverMaxWarn(queryMaxCxxsw.getAdcd(), queryMaxCxxsw.getStm(), queryMaxCxxsw.getEtm());
    }

    @ApiOperation(value = "值班报告河道信息", notes = "值班报告河道信息")
    @GetMapping(value = "select-riverInfo-by-duty")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<RiverInfoByDuty>> selectRiverInfoByDuty(@SqlInjection @RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        RiverInfoByDuty retuen = baseService.selectRiverInfoByDuty(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", retuen));
    }

    @ApiOperation(value = "统计政区下时间段内测站超警戒情况", notes = "统计政区下时间段内测站超警戒情况")
    @GetMapping(value = "select-river-warn-statistics-by-adcd-tm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RiverWarnByAdcdTmVo>>> selectWarnStatisticsByAdcdTm(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {

        List<RiverWarnByAdcdTmVo> list = baseService.selectWarnStatisticsByAdcdTm(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }


    @ApiOperation(value = "临界告警", notes = "作者：赵英捷")
    @PostMapping("select-near-warn-temp")
    public ResponseEntity<SuccessResponse<List<RiverNearWarnVo>>> selectNearWarnTemp(@Validated @RequestBody RiverNearWarnQuery query) {
        List<RiverNearWarnVo> list = baseService.selectNearWarnTemp(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "多站河道水情最新预报", notes = "多站河道水情最新预报")
    @GetMapping(value = "select-forecast-Latest-time")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bscd", value = "流域", required = false, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<StForecastf>>> getForecastLatestTime(@SqlInjection @RequestParam String adcd, @RequestParam String bscd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<StForecastf> list = baseService.getForecastLatestTime(adcd, bscd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "沙基沙堤未治理段列表", notes = "作者：赵英捷")
    @PostMapping("select-river-base-dike-list")
    public ResponseEntity<SuccessResponse<List<BnsRiverBaseDikeSVo>>> getRiverBaseDikeList() {
        List<BnsRiverBaseDikeSVo> list = baseService.getRiverBaseDikeList();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "沙基沙堤未治理段列表导出", notes = "作者：赵英捷")
    @PostMapping(value = "export-select-river-base-dike")
    public void exportRiverBaseDike(HttpServletResponse response) throws Exception {
        baseService.exportRiverBaseDike(response);
    }

    @ApiOperation(value = "主要江河未达标堤段列表", notes = "作者：赵英捷")
    @PostMapping("select-river-dike-list")
    public ResponseEntity<SuccessResponse<List<BnsRiverDikeSVo>>> getRiverDikeList() {
        List<BnsRiverDikeSVo> list = baseService.getRiverDikeList();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "主要江河未达标堤段列表导出", notes = "作者：赵英捷")
    @PostMapping(value = "export-select-river-dike-list")
    public void exportRiverDike(HttpServletResponse response) throws Exception {
        baseService.exportRiverDike(response);
    }

    @ApiOperation(value = "防洪作战图根据河道测站编码查询河道信息", notes = "作者：赵英捷")
    @PostMapping("select-river-for-zzt-by-stcd")
    public ResponseEntity<SuccessResponse<RiverZuoZhanTuVo>> getRiverForZuoZhanTu(@Validated @RequestBody RiverZuoZhanTuQuery query) {
        RiverZuoZhanTuVo data = baseService.getRiverForZuoZhanTu(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", data));
    }

}







