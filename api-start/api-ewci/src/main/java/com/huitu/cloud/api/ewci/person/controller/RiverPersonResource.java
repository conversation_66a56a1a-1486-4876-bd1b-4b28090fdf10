package com.huitu.cloud.api.ewci.person.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.exception.RiverPersonImportException;
import com.huitu.cloud.api.ewci.person.service.RiverPersonService;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * 江河责任人管理服务
 *
 * <AUTHOR>
 */
@Api(tags = "江河责任人管理")
@RestController
@RequestMapping("/api/ewci/person/river")
public class RiverPersonResource extends AbstractApiResource implements ApiResource {

    private static final Logger logger = LoggerFactory.getLogger(RiverPersonResource.class);

    @Override
    public String getUuid() {
        return "2724cca2-aaab-57e5-cbb0-283ad57bafb9";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final RiverPersonService baseService;

    public RiverPersonResource(RiverPersonService baseService) {
        this.baseService = baseService;
    }

    @ApiOperation(value = "分页获取江河责任人信息列表", notes = "作者：曹宝金")
    @PostMapping("select-page-list")
    public ResponseEntity<SuccessResponse<IPage<RiverPerson>>> getPageList(@Validated @RequestBody RiverPersonQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(query)));
    }

    @ApiOperation(value = "江河责任人汇总统计", notes = "作者：赵英捷")
    @PostMapping("select-river-per-sum-list")
    public ResponseEntity<SuccessResponse<List<RiverSummaryVo>>> getRiverSummaryList(@RequestBody RiverSummaryQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverSummaryList(query)));
    }

    @ApiOperation(value = "导入江河责任人信息", notes = "作者：曹宝金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "xadcd", value = "县级行政区划代码", required = true, dataType = "String"),
    })
    @PostMapping(value = "import", headers = "content-type=multipart/form-data")
    public ResponseEntity<SuccessResponse<ImportResult<BnsRiverPersonTmp>>> dataImport(@RequestParam String xadcd, @ApiParam(value = "file", required = true) MultipartFile data) throws Exception {
        if (AdcdUtil.getXzAdLevel(xadcd) != 3) {
            throw new RuntimeException("参数[县级行政区划代码]无效");
        }
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.dataImport(xadcd, data.getInputStream())));
        } catch (RiverPersonImportException ex) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "Error", new ImportResult<>(0, ex.getMessage(), ex.getErrorData())));
        }
    }

    @ApiOperation(value = "导出江河责任人信息", notes = "作者：曹宝金")
    @PostMapping(value = "export")
    public void dataExport(@Validated @RequestBody RiverPersonQuery query, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("江河责任人信息", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.dataExport(query, response.getOutputStream());
        } catch (Exception ex) {
            logger.error("江河责任人信息导出失败，参数：{}", JSON.toJSONString(query));
            logger.error(ex.getMessage(), ex);
        }
    }

    @ApiOperation(value = "获取江河责任人信息列表（仅包含姓名和手机号码）", notes = "作者：曹宝金")
    @PostMapping("select-simple-list")
    public ResponseEntity<SuccessResponse<List<PersonInfo>>> getSimplePersonList(@Validated @RequestBody PersonQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSimplePersonList(query)));
    }

    @ApiOperation(value = "编辑江河责任人", notes = "zyj")
    @PostMapping(value = "river-person-edit")
    public ResponseEntity<SuccessResponse<Integer>> riverPersonEdit(@RequestBody RiverPerson entity) throws Exception {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.riverPersonEdit(entity)));
    }
}
