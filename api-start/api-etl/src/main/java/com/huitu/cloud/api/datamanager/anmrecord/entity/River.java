package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 河道水情表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
@TableName("ST_RIVER_R")
@ApiModel(value="StRiverR对象", description="河道水情表")
public class River extends Model<River> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "时间")
    private String  tm;

    @ApiModelProperty(value = "水位")
    @TableField("Z")
    private String z;

    @ApiModelProperty(value = "流量")
    @TableField("Q")
    private BigDecimal q;

    @ApiModelProperty(value = "断面过水面积")
    @TableField("XSA")
    private BigDecimal xsa;

    @ApiModelProperty(value = "断面平均流速")
    @TableField("XSAVV")
    private BigDecimal xsavv;

    @ApiModelProperty(value = "断面最大流速")
    @TableField("XSMXV")
    private BigDecimal xsmxv;

    @ApiModelProperty(value = "河水特征码")
    @TableField("FLWCHRCD")
    private String flwchrcd;

    @ApiModelProperty(value = "水势")
    @TableField("WPTN")
    private String wptn;

    @ApiModelProperty(value = "测流方法")
    @TableField("MSQMT")
    private String msqmt;

    @ApiModelProperty(value = "测积方法")
    @TableField("MSAMT")
    private String msamt;

    @ApiModelProperty(value = "测速方法")
    @TableField("MSVMT")
    private String msvmt;

    @ApiModelProperty(value = "修改值")
    private String editvalue;

    private Date  tmTwo;
    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }



    public String getZ() {
        return z;
    }

    public void setZ(String z) {
        this.z = z;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public BigDecimal getXsa() {
        return xsa;
    }

    public void setXsa(BigDecimal xsa) {
        this.xsa = xsa;
    }

    public BigDecimal getXsavv() {
        return xsavv;
    }

    public void setXsavv(BigDecimal xsavv) {
        this.xsavv = xsavv;
    }

    public BigDecimal getXsmxv() {
        return xsmxv;
    }

    public void setXsmxv(BigDecimal xsmxv) {
        this.xsmxv = xsmxv;
    }

    public String getFlwchrcd() {
        return flwchrcd;
    }

    public void setFlwchrcd(String flwchrcd) {
        this.flwchrcd = flwchrcd;
    }

    public String getWptn() {
        return wptn;
    }

    public void setWptn(String wptn) {
        this.wptn = wptn;
    }

    public String getMsqmt() {
        return msqmt;
    }

    public void setMsqmt(String msqmt) {
        this.msqmt = msqmt;
    }

    public String getMsamt() {
        return msamt;
    }

    public void setMsamt(String msamt) {
        this.msamt = msamt;
    }

    public String getMsvmt() {
        return msvmt;
    }

    public void setMsvmt(String msvmt) {
        this.msvmt = msvmt;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getEditvalue() {
        return editvalue;
    }

    public void setEditvalue(String editvalue) {
        this.editvalue = editvalue;
    }

    public Date getTmTwo() {
        return tmTwo;
    }

    public void setTmTwo(Date tmTwo) {
        this.tmTwo = tmTwo;
    }

    @Override
    public String toString() {
        return "StRiverR{" +
                "stcd=" + stcd +
                ", tm=" + tm +
                ", z=" + z +
                ", q=" + q +
                ", xsa=" + xsa +
                ", xsavv=" + xsavv +
                ", xsmxv=" + xsmxv +
                ", flwchrcd=" + flwchrcd +
                ", wptn=" + wptn +
                ", msqmt=" + msqmt +
                ", msamt=" + msamt +
                ", msvmt=" + msvmt +
                "}";
    }
}
