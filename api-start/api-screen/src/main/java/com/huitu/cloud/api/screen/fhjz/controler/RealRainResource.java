package com.huitu.cloud.api.screen.fhjz.controler;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.api.SuccessResponse;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.screen.fhjz.entity.RainCountVo;
import com.huitu.cloud.api.screen.fhjz.entity.RainVo;
import com.huitu.cloud.api.screen.fhjz.service.RealRainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 一体机大屏雨量 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-16
 */

@RestController
@Api(tags = "一体机大屏(防洪减灾-全省降雨情况)")
@RequestMapping("/api/screen/rain")
public class RealRainResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "B661C064-1554-4164-80CA-B52A17E42144";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private RealRainService baseService;

    @ApiOperation(value = "降雨量分段测站数统计",notes="降雨量分段测站数统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "流域编码，如果查全省传:pro", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "查询类型，1：实时  2：预报", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "查询开始时间 YYYY-MM-DD HH:SS", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "查询结束时间 YYYY-MM-DD HH:SS", required = true, dataType = "String")
    })
    @GetMapping(value = "rain-count-statistics")
    public ResponseEntity<SuccessResponse<RainCountVo>> statisticsRainCount(String code, String type,String stm,String etm) throws Exception {
        RainCountVo result= baseService.statisticsRainCount(code,type,stm,etm);
            return ResponseEntity.ok(
                new SuccessResponse(result));
     }


    @ApiOperation(value = "降雨量统计",notes="降雨量统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "查询类型，1：实时  2：预报", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "查询开始时间 YYYY-MM-DD HH:SS", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "查询结束时间 YYYY-MM-DD HH:SS", required = true, dataType = "String"),
    })
    @GetMapping(value = "rain-value-statistics")
    public ResponseEntity<SuccessResponse<List>> statisticsRainVaule(String type,String stm,String etm) throws Exception {
        List<Map<String,Object>> result= baseService.statisticsRainValue(type,stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

    @ApiOperation(value = "土壤含水率统计",notes="土壤含水率统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stm", value = "查询开始时间 YYYY-MM-DD HH:SS", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "查询结束时间 YYYY-MM-DD HH:SS", required = true, dataType = "String")
    })
    @GetMapping(value = "soil-value-statistics")
    public ResponseEntity<SuccessResponse<List>> statisticsSoilVaule(String stm,String etm) throws Exception {
        List<Map<String,Object>> result= baseService.statisticsSoilVaule(stm,etm);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }


    @ApiOperation(value = "降雨量详情",notes="降雨量详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "流域编码，如果查全省传:pro", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stType", value = "测站类型", required = true, dataType = "String"),
            @ApiImplicitParam(name = "rstm", value = "实时查询开始时间 YYYY-MM-DD HH:SS", required = true, dataType = "String"),
            @ApiImplicitParam(name = "retm", value = "实时查询结束时间 YYYY-MM-DD HH:SS", required = true, dataType = "String"),
            @ApiImplicitParam(name = "fstm", value = "预报查询开始时间 YYYY-MM-DD HH:SS", required = true, dataType = "String"),
            @ApiImplicitParam(name = "fetm", value = "预报查询结束时间 YYYY-MM-DD HH:SS", required = true, dataType = "String"),
            @ApiImplicitParam(name = "isAnalys", value = "true,false", required = true, dataType = "Boolean")

    })
    @GetMapping(value = "rain-list")
    public ResponseEntity<SuccessResponse<List>> queryRainList(String code,String stType,String rstm,String retm,String fstm,String fetm,Boolean isAnalys) throws Exception {
        List<RainVo> result= baseService.queryRainList(code,stType,rstm,retm,fstm,fetm,isAnalys);
        return ResponseEntity.ok(
                new SuccessResponse(result));
    }

}







