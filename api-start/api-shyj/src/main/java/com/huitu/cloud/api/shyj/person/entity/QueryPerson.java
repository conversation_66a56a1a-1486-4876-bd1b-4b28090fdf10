package com.huitu.cloud.api.shyj.person.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public class Query<PERSON>erson extends PageBean {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "责任人名称")
    private String name;
    @ApiModelProperty(value = "是否值班 1：值班人员，0：非值班人员")
    private String type;
    @ApiModelProperty(value = "部门编码集合")
    private List<String> depts;
    @ApiModelProperty(value = "手机号")
    private String mobile;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getDepts() {
        return depts;
    }

    public void setDepts(List<String> depts) {
        this.depts = depts;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

}
