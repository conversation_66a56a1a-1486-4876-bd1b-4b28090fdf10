package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <p>
 * 重点城(集)镇调查评价设备信息-简易水位站
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@ApiModel(value="DcjzWLJysw对象", description="重点城(集)镇调查评价设备信息-简易水位站")
public class DcjzWLJysw extends Model<DcjzWLJysw> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "站点位置")
    @TableId(value = "ADDRESS", type = IdType.NONE)
    private String address;

    @ApiModelProperty(value = "经度")
    @TableId(value = "LGTD", type = IdType.NONE)
    private String lgtd;

    @ApiModelProperty(value = "纬度")
    @TableId(value = "LTTD", type = IdType.NONE)
    private String lttd;

    @ApiModelProperty(value = "所在流域")
    @TableId(value = "WSNM", type = IdType.NONE)
    private String wsnm;

    @ApiModelProperty(value = "设站日期")
    @TableId(value = "BDATE", type = IdType.NONE)
    private Date bdate;

    @ApiModelProperty(value = "测水位")
    @TableId(value = "MWARTER", type = IdType.NONE)
    private String mwarter;

    @ApiModelProperty(value = "语音报警")
    @TableId(value = "ALVOICE", type = IdType.NONE)
    private String alvoice;

    @ApiModelProperty(value = "光报警")
    @TableId(value = "ALIGHT", type = IdType.NONE)
    private String alight;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public Date getBdate() {
        return bdate;
    }

    public void setBdate(Date bdate) {
        this.bdate = bdate;
    }

    public String getMwarter() {
        return mwarter;
    }

    public void setMwarter(String mwarter) {
        this.mwarter = mwarter;
    }

    public String getAlvoice() {
        return alvoice;
    }

    public void setAlvoice(String alvoice) {
        this.alvoice = alvoice;
    }

    public String getAlight() {
        return alight;
    }

    public void setAlight(String alight) {
        this.alight = alight;
    }

    @Override
    public String toString() {
        return "DcjzWLJysw{" +
                "adnm='" + adnm + '\'' +
                ", address='" + address + '\'' +
                ", lgtd='" + lgtd + '\'' +
                ", lttd='" + lttd + '\'' +
                ", wsnm='" + wsnm + '\'' +
                ", bdate=" + bdate +
                ", mwarter='" + mwarter + '\'' +
                ", alvoice='" + alvoice + '\'' +
                ", alight='" + alight + '\'' +
                '}';
    }
}
