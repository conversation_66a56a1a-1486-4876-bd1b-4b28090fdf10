package com.huitu.cloud.api.ewci.monitor.service.impl;

import com.huitu.cloud.api.ewci.monitor.entity.IaCPrevad;
import com.huitu.cloud.api.ewci.monitor.mapper.IaCPrevadDao;
import com.huitu.cloud.api.ewci.monitor.service.PrevadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 防治区基本情况调查成果汇总服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class PrevadServiceImpl implements PrevadService {

    private IaCPrevadDao baseDao;

    @Autowired
    public void setBaseDao(IaCPrevadDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public List<IaCPrevad> getPrevadList(String type, String adcd) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        params.put("adcd", StringUtils.rightPad(adcd, 15, "0"));
        return baseDao.getPrevadList(params);
    }
}
