package com.huitu.cloud.api.shyj.dept.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@ApiModel(value = "部门信息", description = "部门关联信息")
public class RelevantDeptInfo extends DeptB{
    @ApiModelProperty(value = "上级部门名称")
    private String pDeptNm;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "子部门信息")
    private List<RelevantDeptInfo> children;

    public List<RelevantDeptInfo> getChildren() {
        return children;
    }

    public void setChildren(List<RelevantDeptInfo> children) {
        this.children = children;
    }

    public String getpDeptNm() {
        return pDeptNm;
    }

    public void setpDeptNm(String pDeptNm) {
        this.pDeptNm = pDeptNm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
