package com.huitu.cloud.api.xcc.v2.event.listener;

import com.huitu.cloud.api.xcc.v2.core.entity.XccDialogue;
import com.huitu.cloud.api.xcc.v2.core.entity.XccReceipt;
import com.huitu.cloud.api.xcc.v2.core.entity.common.BadType;
import com.huitu.cloud.api.xcc.v2.core.service.XccMessageService;
import com.huitu.cloud.api.xcc.v2.event.core.XccCallEvent;
import com.huitu.cloud.api.xcc.v2.event.entity.XccCallDialog;
import com.huitu.cloud.api.xcc.v2.event.entity.XccCallResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 语音呼叫通话结果监听
 */
@Component
public class XccReceiptListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(XccReceiptListener.class);

    private final XccMessageService messageService;

    public XccReceiptListener(XccMessageService messageService) {
        this.messageService = messageService;
    }

    @Async
    @EventListener(XccCallEvent.class)
    public void onEvent(XccCallEvent event) {
        XccCallResult data = event.getResult();
        if (null == data) {
            return;
        }
        XccReceipt receipt = createReceipt(data);
        List<XccCallDialog> dialogs = data.getDialogs();
        if (!dialogs.isEmpty()) {
            List<XccDialogue> items = new ArrayList<>();
            for (XccCallDialog dialog : dialogs) {
                items.add(createDialog(dialog));
            }
            receipt.setDialogs(items);
        }
        try {
            messageService.receipt(receipt);
        } catch (Exception e) {
            LOGGER.error("[XCC] Receipt listener, failed to updated the call result into the database, send id is " + data.getOutCallId(), e);

            messageService.writeBadData(BadType.RECEIPT, receipt.getSendId(), receipt);
        }
    }

    private XccReceipt createReceipt(XccCallResult result) {
        XccReceipt receipt = new XccReceipt();
        receipt.setSendId(result.getOutCallId());
        receipt.setCallId(result.getCallId());
        receipt.setCallPhone(result.getCallPhone());
        receipt.setCallStatus(result.getCallStatus());
        receipt.setCallStatusText(result.getCallStatusText());
        receipt.setCallResult(result.getCallResult());
        receipt.setCallResultText(result.getCallResultText());
        receipt.setRingTimeLength(result.getRingTimeLength());
        receipt.setCallingTimeLength(result.getCallingTimeLength());
        receipt.setCallStartTime(result.getCallStartTime());
        receipt.setCallEndTime(result.getCallEndTime());
        receipt.setAudioUrl(result.getAudioUrl());
        return receipt;
    }

    private XccDialogue createDialog(XccCallDialog dialog) {
        XccDialogue item = new XccDialogue();
        item.setRecordId(dialog.getRecordId());
        item.setInterIdx(dialog.getInterIdx());
        item.setCallId(dialog.getCallId());
        item.setNodeId(dialog.getNodeId());
        item.setNodeName(dialog.getNodeName());
        item.setPlayContent(dialog.getPlayContent());
        item.setFlowResult(dialog.getFlowResult());
        item.setKnowledgeId(dialog.getKnowledgeId());
        item.setKnowledgeName(dialog.getKnowledgeName());
        item.setBotAudioUrl(dialog.getBotAudioUrl());
        item.setUserAudioUrl(dialog.getUserAudioUrl());
        item.setTag(dialog.getTag());
        item.setSlot(dialog.getSlot());
        return item;
    }
}