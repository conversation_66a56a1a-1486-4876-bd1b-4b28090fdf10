package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;



import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 政区预警记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-16
 */
@TableName("WARNRECORD_R")
@ApiModel(value="WarnrecordR对象", description="政区预警记录表")
public class WarnrecordR implements Serializable {

    @ApiModelProperty(value = "预警ID")
    @TableId(value = "WARNID", type = IdType.NONE)
    private String warnId;

    @ApiModelProperty(value = "行政区编码 ")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "预警类型ID ")
    @TableField("WARNTYPEID")
    private Integer warnTypeId;

    @ApiModelProperty(value = "预警等级ID 4 立即转移 5 准备转移")
    @TableField("WARNGRADEID")
    private Integer warnGradeId;

    @ApiModelProperty(value = "预警状态ID 0 新产生 10 内部预警 20 外部预警 11 会商 21 跟踪督导 ")
    @TableField("WARNSTATUSID")
    private Integer warnStatusId;

    @ApiModelProperty(value = "预警开始时间")
    @TableField("WARNSTM")
    private Date warnStm;

    @ApiModelProperty(value = "预警结束时间")
    @TableField("WARNETM")
    private Date warnEtm;

    @ApiModelProperty(value = "预警名称 ")
    @TableField("WARNNM")
    private String warnNm;

    @ApiModelProperty(value = "预警说明 ")
    @TableField("WARNDESC")
    private String warnDesc;

    @ApiModelProperty(value = "内部预警时间 ")
    @TableField("INNERWARNTM")
    private Date innerWarnTm;

    @ApiModelProperty(value = "内部预警等级")
    @TableField("INNERWARNGRADEID")
    private Integer innerWarnGradeId;

    @ApiModelProperty(value = "内部预警内容 ")
    @TableField("INNERWARNDESC")
    private String innerWarnDesc;

    @ApiModelProperty(value = "内部预警发布人")
    @TableField("INNERWARNUSER")
    private String innerWarnUser;

    @ApiModelProperty(value = "外部预警时间")
    @TableField("OUTWARNTM")
    private Date outWarnTm;

    @ApiModelProperty(value = "外部预警等级")
    @TableField("OUTWARNGRADEID")
    private Integer outWarnGradeId;

    @ApiModelProperty(value = "外部预警内容")
    @TableField("OUTWARNDESC")
    private String outWarnDesc;

    @ApiModelProperty(value = "外部预警发布人")
    @TableField("OUTWARNUSER")
    private String outWarnUser;

    @ApiModelProperty(value = "县预警和乡镇预警的标识 ")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = "是否是人工创建  1:突发预警  0：系统生成")
    @TableField("ISHANDMADE")
    private String isHandMade;

    @ApiModelProperty(value = "是否闪烁  1:闪烁  0：不闪烁")
    @TableField("ISFLASH")
    private Integer isFlash;

    @ApiModelProperty(value = "经度 ")
    @TableField("LGTD")
    private String lgtd;

    @ApiModelProperty(value = "纬度 ")
    @TableField("LTTD")
    private String lttd;

    @ApiModelProperty(value = "最后修改人 ")
    @TableField("OCCURUSER")
    private String occurUser;

    @ApiModelProperty(value = "最后修改时间")
    @TableField("OCCURTM")
    private Date occurTm;

    @ApiModelProperty(value = "备注 ")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "预警模型编号 (0:山洪普查 -测站预警  1：调查评价 -村预警)")
    @TableField("MODEL")
    private String model;

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getWarnTypeId() {
        return warnTypeId;
    }

    public void setWarnTypeId(Integer warnTypeId) {
        this.warnTypeId = warnTypeId;
    }

    public Integer getWarnGradeId() {
        return warnGradeId;
    }

    public void setWarnGradeId(Integer warnGradeId) {
        this.warnGradeId = warnGradeId;
    }

    public Integer getWarnStatusId() {
        return warnStatusId;
    }

    public void setWarnStatusId(Integer warnStatusId) {
        this.warnStatusId = warnStatusId;
    }

    public Date getWarnStm() {
        return warnStm;
    }

    public void setWarnStm(Date warnStm) {
        this.warnStm = warnStm;
    }

    public Date getWarnEtm() {
        return warnEtm;
    }

    public void setWarnEtm(Date warnEtm) {
        this.warnEtm = warnEtm;
    }

    public String getWarnNm() {
        return warnNm;
    }

    public void setWarnNm(String warnNm) {
        this.warnNm = warnNm;
    }

    public String getWarnDesc() {
        return warnDesc;
    }

    public void setWarnDesc(String warnDesc) {
        this.warnDesc = warnDesc;
    }

    public Date getInnerWarnTm() {
        return innerWarnTm;
    }

    public void setInnerWarnTm(Date innerWarnTm) {
        this.innerWarnTm = innerWarnTm;
    }

    public Integer getInnerWarnGradeId() {
        return innerWarnGradeId;
    }

    public void setInnerWarnGradeId(Integer innerWarnGradeId) {
        this.innerWarnGradeId = innerWarnGradeId;
    }

    public String getInnerWarnDesc() {
        return innerWarnDesc;
    }

    public void setInnerWarnDesc(String innerWarnDesc) {
        this.innerWarnDesc = innerWarnDesc;
    }

    public String getInnerWarnUser() {
        return innerWarnUser;
    }

    public void setInnerWarnUser(String innerWarnUser) {
        this.innerWarnUser = innerWarnUser;
    }

    public Date getOutWarnTm() {
        return outWarnTm;
    }

    public void setOutWarnTm(Date outWarnTm) {
        this.outWarnTm = outWarnTm;
    }

    public Integer getOutWarnGradeId() {
        return outWarnGradeId;
    }

    public void setOutWarnGradeId(Integer outWarnGradeId) {
        this.outWarnGradeId = outWarnGradeId;
    }

    public String getOutWarnDesc() {
        return outWarnDesc;
    }

    public void setOutWarnDesc(String outWarnDesc) {
        this.outWarnDesc = outWarnDesc;
    }

    public String getOutWarnUser() {
        return outWarnUser;
    }

    public void setOutWarnUser(String outWarnUser) {
        this.outWarnUser = outWarnUser;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIsHandMade() {
        return isHandMade;
    }

    public void setIsHandMade(String isHandMade) {
        this.isHandMade = isHandMade;
    }

    public Integer getIsFlash() {
        return isFlash;
    }

    public void setIsFlash(Integer isFlash) {
        this.isFlash = isFlash;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }

    public String getOccurUser() {
        return occurUser;
    }

    public void setOccurUser(String occurUser) {
        this.occurUser = occurUser;
    }

    public Date getOccurTm() {
        return occurTm;
    }

    public void setOccurTm(Date occurTm) {
        this.occurTm = occurTm;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}