package com.huitu.cloud.api.ew.mapper;

import com.huitu.cloud.api.ew.entity.base.EwMessage;
import com.huitu.cloud.api.ew.entity.ext.EwWarningMessage;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwMessageDao {
    /**
     * 根据预警ID，获取预警消息列表
     *
     * @param warnId 预警ID
     * @return 消息列表
     */
    List<EwWarningMessage> getMessageList(String warnId);

    /**
     * 新增预警消息
     *
     * @param message 预警消息
     * @return 受影响的行数
     */
    int insert(EwMessage message);
}
