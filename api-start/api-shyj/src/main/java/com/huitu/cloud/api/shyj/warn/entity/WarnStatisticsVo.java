package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value="预警统计信息对象", description="预警统计信息对象")
public class WarnStatisticsVo implements Serializable {

    @ApiModelProperty(value = " 预警状态")
    private List<WarnStatus> WarnStatusLsit;

    @ApiModelProperty(value = " 预警等级")
    private List<WarnGradeStatistics> WarnGradeStatisticsList;

    public List<WarnStatus> getWarnStatusLsit() {
        return WarnStatusLsit;
    }

    public void setWarnStatusLsit(List<WarnStatus> warnStatusLsit) {
        WarnStatusLsit = warnStatusLsit;
    }

    public List<WarnGradeStatistics> getWarnGradeStatisticsList() {
        return WarnGradeStatisticsList;
    }

    public void setWarnGradeStatisticsList(List<WarnGradeStatistics> warnGradeStatisticsList) {
        WarnGradeStatisticsList = warnGradeStatisticsList;
    }
}
