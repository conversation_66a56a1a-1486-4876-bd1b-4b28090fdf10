package com.huitu.cloud.api.shyj.evaluation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 分析评价  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
public interface AnalysisService {
    /**
     * 分页查询设计暴雨列表信息
     *
     * @param wsnm     流域名称
     * @param adcd     政区编码
     * @param type     调查评价类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaDesntb> getDesntbList(String wsnm, String adcd, String wscd,String type, int pageNum, int pageSize);

    /**
     * 查询设计暴雨流域总数
     *
     * @param wsnm     流域名称
     * @param adcd     政区编码
     * @param type     调查评价类型
     * @return
     */
    Map<String,Object> getDesntbSum(String wsnm, String adcd, String wscd,String type);

    /**
     * 导出设计暴雨列表信息
     *
     * @param wsnm 流域名称
     * @param adcd 政区编码
     * @param type 调查评价类型
     * @return
     */
    void exportDesntbList(String wsnm, String adcd, String wscd,String type);

    /**
     * 分页查询小流域汇流时间设计暴雨时程分配表列表信息
     *
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaHlsjtb> getHlsjtb(String wsnm, String radio, String adcd,  int pageNum, int pageSize);

    /**
     * 查询小流域汇流时间设计暴雨时程信息流域统计
     *
     * @param wsnm  流域名称
     * @param radio 表
     * @param adcd 政区
     * @return
     */
    Map<String,Object> getHlsjtbSum(String wsnm, String radio, String adcd);
    /**
     * 导出小流域汇流时间设计暴雨信息
     *
     * @param wsnm 流域名称
     */
    void exportHlsjtb(String wsnm, String radio, String adcd);

    /**
     * 查询控制断面设计洪水信息
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaSdtdtb> getSdtdtb(String adcd, String wsnm,String wscd,String radio, int pageNum, int pageSize);

    /**
     * 控制断面设计洪水防治区统计
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @return
     */
    Map<String,Object> getSdtdtbSum(String adcd, String wsnm,String wscd,String radio);

    /**
     * 导出控制断面设计洪水信息
     *
     * @param adcd 流域编码
     * @param wsnm 流域名称
     */
    void exportSdtdtb(String adcd, String wsnm,String wscd,String radio);

    /**
     * 查询控制断面水位-流量-人口关系信息
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @param type     调查评价类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaSwllrktb> getSwllrktb(String adcd, String wsnm,String wscd, String type, int pageNum, int pageSize);
    /**
     * 查询控制断面水位-流量-人口关系表统计政区、小流域
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @param type     调查评价类型
     * @return
     */
    Map<String,Object> getSwllrktbSum(String adcd, String wsnm,String wscd, String type);
    /**
     * 导出断面水位-流量-人口关系信息
     *
     * @param adcd 流域编码
     * @param wsnm 流域名称
     * @param type 调查评价类型
     */
    void exportSwllrktb(String adcd, String wsnm, String wscd,String type);

    /**
     * 查询防洪现状评价表信息
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @param type     调查评价类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaNowfhtb> getNowfhtb(String adcd, String wsnm, String wscd,String type,int pageNum, int pageSize);
    /**
     * 统计防洪现状评价表政区、小流域
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @param type     调查评价类型
     * @return
     */
    Map<String,Object> getNowfhtbSum(String adcd, String wsnm, String wscd,String type);
    /**
     * 倒垂防洪现状评价信息
     *
     * @param adcd 流域编码
     * @param wsnm 流域名称
     * @param type 调查评价类型
     */
    void exportNowfhtb(String adcd, String wsnm,String wscd,String type);

    /**
     * 查询临界雨量经验估计法成果表信息
     *
     * @param adcd     流域编码
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaJygstb> getJygstb(String adcd, int pageNum, int pageSize);

    /**
     * 导出临界雨量经验估计法成果信息
     *
     * @param adcd 流域编码
     */
    void exportJygstb(String adcd);

    /**
     * 查询临界雨量降雨分析法成果表信息
     *
     * @param adcd     流域编码
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaYjcjtb> getYjcjtb(String adcd, int pageNum, int pageSize);

    /**
     * 导出临界雨量降雨分析法成果信息
     *
     * @param adcd 流域编码
     */
    void exportYjcjtb(String adcd);

    /**
     * 查询临界雨量模型分析法信息
     *
     * @param adcd     流域编码
     * @param type     调查评价类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaFxcgtb> getFxcgtb(String adcd, String type,int pageNum, int pageSize);

    /**
     * 查询临界雨量模型分析法信息防治区统计
     *
     * @param adcd     流域编码
     * @param type     调查评价类型
     * @return
     */
    Map<String,Object> getFxcgtbSum(String adcd, String type);

    /**
     * 导出临界雨量模型分析法成果信息
     *
     * @param adcd 流域编码
     * @param type 调查评价类型
     */
    void exportFxcgtb(String adcd,String type);

    /**
     * 查询预警指标时段雨量表信息
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaDfwrule> getDfwrule(String adcd, String wsnm, int pageNum, int pageSize);

    /**
     * 统计预警指标时段雨量表防治区
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @return
     */
    Map<String,Object> getDfwruleSum(String adcd, String wsnm);

    List<IaDfwruleVo> getDfwruleByStcd(String stcd);

    /**
     * 导出预警指标时段雨量信息
     *
     * @param adcd 流域编码
     * @param wsnm 流域名称
     */
    void exportDfwrule(String adcd, String wsnm);

    /**
     * 查询预警指标综合雨量信息
     *
     * @param adcd     流域编码
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaYjicr> getYjicr(String adcd, String radio, int pageNum, int pageSize);

    /**
     * 查询预警指标综合雨量信息防治区统计
     *
     * @param adcd     流域编码
     * @return
     */
    Map<String,Object> getYjicrSum(String adcd, String radio);
    /**
     * 导出预警指标综合雨量信息
     *
     * @param adcd 流域编码
     */
    void exportYjicr(String adcd, String radio);

    /**
     * 查询预警指标水位信息
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<IaWlwrule> getWlwrule(String adcd, String wsnm, int pageNum, int pageSize);
    /**
     * 查询预警指标水位信息防治区统计
     *
     * @param adcd     流域编码
     * @param wsnm     流域名称
     * @return
     */
    Map<String,Object> getWlwruleSum(String adcd, String wsnm);
    /**
     * 导出预警指标水位信息
     *
     * @param adcd 流域编码
     * @param wsnm 流域名称
     */
    void exportWlwrule(String adcd, String wsnm);

    /**
     * 查询计算单元(防灾对象)信息
     *
     * @param adcd 政区编码
     * @param pageNum 页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<BnsIaUnitdp> getUnitdp(String adcd, int pageNum, int pageSize);

    /**
     * 导出计算单元(防灾对象)信息
     *
     * @param adcd 政区编码
     */
    void exportUnitdp(String adcd);

    /**
     * 查询重点城(集)镇计算单元（小流域）信息
     *
     * @param wsnm     流域名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<BnsIaUnitws> getUnitws(String wsnm,int pageNum, int pageSize);

    /**
     * 导出重点城(集)镇计算单元（小流域）信息
     *
     * @param wsnm     流域名称
     */
    void exportUnitws(String wsnm);


    /**
     * 查询重点城(集)镇调查评价汇总
     *
     * @param adcd     政区编码
     * @param zdadnm   成集镇名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzSummary> getCjzSummary(String adcd,String zdadnm,int pageNum, int pageSize);

    /**
     * 导出重点城(集)镇调查评价汇总
     *
     * @param adcd     流域编码
     * @param zdadnm     成集镇名称
     */
    void exportCjzSummary(String adcd,String zdadnm);

    /**
     * 查询重点城(集)镇调查评价涉水工程-水库信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param rsName   水库名称
     * @param engGrad   水库规模
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzReservoitSs> getCjzReservoit(String adcd,String zdadnm,String rsName,String engGrad,int pageNum, int pageSize);

    /**
     * 导出重点城(集)镇调查评价涉水工程-水库信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param rsName   水库名称
     * @param engGrad   水库规模
     */
    void exportCjzReservoit(String adcd,String zdadnm,String rsName,String engGrad);

    /**
     * 查询重点城(集)镇调查评价涉水工程-水闸信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param gateName   水闸名称
     * @param gateType   水闸类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzSluiceSs> getCjzSluice(String adcd,String zdadnm,String gateName,String gateType,int pageNum, int pageSize);

    /**
     * 导出重点城(集)镇调查评价涉水工程-水闸信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param gateName   水闸名称
     * @param gateType   水闸类型
     */
    void exportCjzSluice(String adcd,String zdadnm,String gateName,String gateType);

    /**
     * 查询重点城(集)镇调查评价涉水工程-堤防信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param dikeName   堤防名称
     * @param dikeGrad   堤防类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzDfDikeSs> getDfDike(String adcd,String zdadnm,String dikeName,String dikeGrad,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价涉水工程-堤防信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param dikeGrad   堤防名称
     * @param dikeType   堤防类型
     */
    void exportCjzDfDike(String adcd,String zdadnm,String dikeGrad,String dikeType);

    /**
     * 查询重点城(集)镇调查评价涉水工程-路涵信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param culname   路涵名称
     * @param type   类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzLhRCSs> getLhZdjz(String adcd,String zdadnm,String culname,String type,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价涉水工程-路涵信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param culname   堤防名称
     * @param type   堤防类型
     */
    void exportCjzLhZdjzist(String adcd,String zdadnm,String culname,String type);

    /**
     * 查询重点城(集)镇调查评价涉水工程-桥梁信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param brname   桥梁名称
     * @param type   类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzQlBridgeSs> getBridgeQl(String adcd,String zdadnm,String brname,String type,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价涉水工程-桥梁信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param brname   桥梁名称
     * @param type   堤防类型
     */
    void exportCjzBridgeQl(String adcd,String zdadnm,String brname,String type);

    /**
     * 查询重点城(集)镇调查评价涉水工程-塘坝信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param damname   塘坝名称
     * @param mt   类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzTbPondSs> getPondTb(String adcd,String zdadnm,String damname,String mt,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价涉水工程-塘坝信息列表
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param damname   塘坝名称
     * @param mt   类型
     */
    void exportCjzPondTb(String adcd,String zdadnm,String damname,String mt);

    /**
     * 查询重点城(集)镇调查评价设备信息-自动检测站
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param stnm   测站名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzStationSs> getZdjczCz(String adcd,String zdadnm,String stnm,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价设备信息-自动检测站
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param stnm   测站名称
     */
    void exportZdjczCz(String adcd,String zdadnm,String stnm);


    /**
     * 查询重点城(集)镇调查评价设备信息-无线预警广播
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param address   地址
     * @param type   设备类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzRadioGbSs> getRadioGb(String adcd,String zdadnm,String address,Integer type,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价设备信息-无线预警广播
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param type   设备类型
     */
    void exportRadioGb(String adcd,String zdadnm,String address,Integer type);

    /**
     * 查询重点城(集)镇调查评价设备信息-简易雨量站
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param address   地址
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzSimpleJyylSs> getSimpleJyyl(String adcd,String zdadnm,String address,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价设备信息-简易雨量站
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param address   地址
     */
    void exportSimpleJyyl(String adcd,String zdadnm,String address);

    /**
     * 查询重点城(集)镇调查评价设备信息-简易水位站
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param address   地址
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzWLJysw> getWLJysw(String adcd,String zdadnm,String address,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价设备信息-简易水位站
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param address   地址
     */
    void exportWLJysw(String adcd,String zdadnm,String address);


    /**
     * 查询重点城(集)镇调查评价-河道纵断面
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzHdzdm> getHdzdm(String adcd,String zdadnm,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价-河道纵断面
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     */
    void exportHdzdm(String adcd,String zdadnm);

    /**
     * 查询重点城(集)镇调查评价-河道横断面
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<DcjzHdhdm> getCjzHdhdm(String adcd,String zdadnm,int pageNum, int pageSize);

    /**
     * 导出查询重点城(集)镇调查评价-河道横断面
     *
     * @param adcd     政区编码
     * @param zdadnm   城集镇名称
     */
    void exportHdhdm(String adcd,String zdadnm);

}
