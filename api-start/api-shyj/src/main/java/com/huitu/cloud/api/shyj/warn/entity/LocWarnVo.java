package com.huitu.cloud.api.shyj.warn.entity;

import com.huitu.cloud.api.syq.video.entity.BsnVdstinfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * 现地预警记录表 前端控制器
 *
 * <AUTHOR>
 * @since 2019-09-16
 */
@Data
@ApiModel(value="LocWarnVo对象", description="现地预警记录表")
public class LocWarnVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    private String warnid;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "县级政区编码")
    private String xadcd;

    @ApiModelProperty(value = "县级政区名称")
    private String xadnm;

    @ApiModelProperty(value = "预警类型ID")
    private Integer warntypeid;

    @ApiModelProperty(value = "预警类型名称")
    private String warntypenm;

    @ApiModelProperty(value = "预警等级ID")
    private Integer warngradeid;

    @ApiModelProperty(value = "预警等级名称")
    private String warngradenm;

    @ApiModelProperty(value = "预警状态ID")
    private Integer warnstatusid;

    @ApiModelProperty(value = "预警状态名称")
    private String warnstatusnm;

    @ApiModelProperty(value = "预警开始时间")
    private Date warnstm;

    @ApiModelProperty(value = "预警结束时间")
    private Date warnetm;

    @ApiModelProperty(value = "预警名称")
    private String warnnm;

    @ApiModelProperty(value = "预警描述")
    private String warndesc;

    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "纬度")
    private Double lgtd;

    @ApiModelProperty(value = "经度")
    private Double lttd;

    @ApiModelProperty(value = "状态")
    private Integer state;


    @ApiModelProperty(value = "设备列表")
    private List<LocWarnDeviceVo> deviceList = new LinkedList<>();

    @ApiModelProperty(value = "视频列表")
    private List<BsnVdstinfo> videoList = new LinkedList<>();

}
