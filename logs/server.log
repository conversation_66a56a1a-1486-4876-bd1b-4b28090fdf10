2025-08-20 10:29:33,786 INFO [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 330] Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$28fbce9a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-20 10:29:35,389 INFO [main] o.s.c.b.c.PropertySourceBootstrapConfiguration [PropertySourceBootstrapConfiguration.java : 98] Located property source: CompositePropertySource {name='NACOS', propertySources=[NacosPropertySource {name='allApi-all.properties'}, NacosPropertySource {name='allApi.properties'}]}
2025-08-20 10:29:35,447 INFO [main] com.huitu.cloud.ApiApplication [SpringApplication.java : 652] The following profiles are active: all
2025-08-20 10:29:43,145 INFO [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 244] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-20 10:29:43,155 INFO [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 126] Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-20 10:29:44,024 INFO [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 182] Finished Spring Data repository scanning in 826ms. Found 0 repository interfaces.
2025-08-20 10:29:44,269 WARN [main] o.s.boot.actuate.endpoint.EndpointId [EndpointId.java : 131] Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
2025-08-20 10:29:44,277 WARN [main] o.s.boot.actuate.endpoint.EndpointId [EndpointId.java : 131] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-20 10:29:44,371 WARN [main] o.s.boot.actuate.endpoint.EndpointId [EndpointId.java : 131] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-20 10:29:44,996 INFO [main] o.s.cloud.context.scope.GenericScope [GenericScope.java : 294] BeanFactory id=629350c5-4838-3eb3-94f8-ab15025c0c70
2025-08-20 10:29:48,770 INFO [main] o.s.b.w.e.tomcat.TomcatWebServer [TomcatWebServer.java : 90] Tomcat initialized with port(s): 8080 (http)
2025-08-20 10:29:48,827 INFO [main] o.a.coyote.http11.Http11NioProtocol [DirectJDKLog.java : 173] Initializing ProtocolHandler ["http-nio-8080"]
2025-08-20 10:29:48,858 INFO [main] o.a.catalina.core.StandardService [DirectJDKLog.java : 173] Starting service [Tomcat]
2025-08-20 10:29:48,859 INFO [main] o.a.catalina.core.StandardEngine [DirectJDKLog.java : 173] Starting Servlet engine: [Apache Tomcat/9.0.44]
2025-08-20 10:29:49,242 INFO [main] o.a.c.c.C.[Tomcat].[localhost].[/] [DirectJDKLog.java : 173] Initializing Spring embedded WebApplicationContext
2025-08-20 10:29:49,242 INFO [main] o.s.web.context.ContextLoader [ServletWebServerApplicationContext.java : 284] Root WebApplicationContext: initialization completed in 13756 ms
2025-08-20 10:29:49,281 INFO [main] c.h.c.config.HuituClientProperties [HuituClientProperties.java : 43] set huitu client properties
2025-08-20 10:29:51,761 INFO [main] c.h.d.c.DataSourceAutoConfiguration [DataSourceAutoConfiguration.java : 54] Init HuituDataSource
2025-08-20 10:29:53,347 INFO [main] c.alibaba.druid.pool.DruidDataSource [DruidDataSource.java : 990] {dataSource-1} inited
2025-08-20 10:30:16,263 INFO [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 171] Initializing ExecutorService
2025-08-20 10:30:16,263 INFO [main] com.huitu.cloud.config.AsyncConfig [AsyncConfig.java : 50] AsyncExecutor configured.
2025-08-20 10:30:16,294 INFO [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 171] Initializing ExecutorService 'asyncExecutor'
2025-08-20 10:30:24,674 INFO [main] o.s.b.a.e.web.EndpointLinksResolver [EndpointLinksResolver.java : 58] Exposing 0 endpoint(s) beneath base path '/actuator'
2025-08-20 10:30:25,846 INFO [main] c.h.c.config.HuituClientProperties [HuituClientProperties.java : 43] set huitu client properties
2025-08-20 10:30:26,254 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 10:30:30,634 INFO [main] o.s.b.a.s.s.UserDetailsServiceAutoConfiguration [UserDetailsServiceAutoConfiguration.java : 83] 

Using generated security password: 9e06b04d-0c9b-4877-9849-648accb57ff9

2025-08-20 10:30:36,186 INFO [main] o.s.cloud.commons.util.InetUtils [InetUtils.java : 170] Cannot determine local hostname
2025-08-20 10:30:36,395 INFO [main] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 171] Initializing ExecutorService
2025-08-20 10:30:37,724 INFO [main] o.s.cloud.commons.util.InetUtils [InetUtils.java : 170] Cannot determine local hostname
2025-08-20 10:30:46,022 INFO [main] o.a.coyote.http11.Http11NioProtocol [DirectJDKLog.java : 173] Starting ProtocolHandler ["http-nio-8080"]
2025-08-20 10:30:46,147 INFO [main] o.s.b.w.e.tomcat.TomcatWebServer [TomcatWebServer.java : 202] Tomcat started on port(s): 8080 (http) with context path ''
2025-08-20 10:30:46,165 INFO [main] o.s.c.a.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 64] nacos registry, dev-allApi ************:8080 register finished
2025-08-20 10:30:46,182 INFO [main] com.huitu.cloud.ApiApplication [StartupInfoLogger.java : 59] Started ApiApplication in 74.841 seconds (JVM running for 77.465)
2025-08-20 10:30:46,203 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 184] [fixed-************_8848-a37353cf-b689-4011-924a-4cdb36793810] [subscribe] allApi-all.properties+DEFAULT_GROUP+a37353cf-b689-4011-924a-4cdb36793810
2025-08-20 10:30:46,206 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 78] [fixed-************_8848-a37353cf-b689-4011-924a-4cdb36793810] [add-listener] ok, tenant=a37353cf-b689-4011-924a-4cdb36793810, dataId=allApi-all.properties, group=DEFAULT_GROUP, cnt=1
2025-08-20 10:30:46,206 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 184] [fixed-************_8848-a37353cf-b689-4011-924a-4cdb36793810] [subscribe] allApi.properties+DEFAULT_GROUP+a37353cf-b689-4011-924a-4cdb36793810
2025-08-20 10:30:46,207 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 78] [fixed-************_8848-a37353cf-b689-4011-924a-4cdb36793810] [add-listener] ok, tenant=a37353cf-b689-4011-924a-4cdb36793810, dataId=allApi.properties, group=DEFAULT_GROUP, cnt=1
2025-08-20 10:30:47,259 INFO [http-nio-8080-exec-4] o.a.c.c.C.[Tomcat].[localhost].[/] [DirectJDKLog.java : 173] Initializing Spring DispatcherServlet 'huitu_client_controller'
2025-08-20 10:30:47,259 INFO [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 525] Initializing Servlet 'huitu_client_controller'
2025-08-20 10:30:47,522 INFO [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 547] Completed initialization in 263 ms
2025-08-20 10:30:47,861 INFO [http-nio-8080-exec-4] o.a.c.c.C.[Tomcat].[localhost].[/] [DirectJDKLog.java : 173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-20 10:30:47,861 INFO [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 525] Initializing Servlet 'dispatcherServlet'
2025-08-20 10:30:47,910 INFO [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 547] Completed initialization in 49 ms
2025-08-20 10:30:52,115 INFO [http-nio-8080-exec-8] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: zcpt-apiZcpt.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-20 10:30:52,207 INFO [http-nio-8080-exec-8] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 197] Client: zcpt-apiZcpt instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=zcpt-apiZcpt,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-08-20 10:30:52,225 INFO [http-nio-8080-exec-8] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 222] Using serverListUpdater PollingServerListUpdater
2025-08-20 10:30:52,298 INFO [http-nio-8080-exec-8] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: zcpt-apiZcpt.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-20 10:30:52,307 INFO [http-nio-8080-exec-8] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 150] DynamicServerListLoadBalancer for client zcpt-apiZcpt initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=zcpt-apiZcpt,current list of Servers=[************:8089],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:8089;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:org.springframework.cloud.alibaba.nacos.ribbon.NacosServerList@9e5d34d
2025-08-20 10:30:53,252 INFO [PollingServerListUpdater-0] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: zcpt-apiZcpt.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-20 10:30:54,676 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 11:00:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:54,676 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 11:00:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:54,715 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 10:30:54,715 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 10:30:54,716 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:54,716 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:54,749 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:54,749 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:55,113 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 10:35:00(String), 2025-08-20 10:35:00(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0 and CHARINDEX(RSVRTP,'4,5,3,2,1')>0  AND left(STB.ADCD,2) ='22'(String)
2025-08-20 10:30:55,113 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 10:35:00(String), 2025-08-20 10:35:00(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0 and CHARINDEX(RSVRTP,'4,5,3,2,1')>0  AND left(STB.ADCD,2) ='22'(String)
2025-08-20 10:30:55,379 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:30:55,380 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:30:55,383 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,383 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,414 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,414 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,414 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,414 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,416 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,417 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,417 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,417 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,417 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,417 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,417 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,432 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:55,432 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:55,433 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,433 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,433 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,433 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,433 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,433 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,433 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,434 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,434 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,434 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:55,450 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 10:30:55,450 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 10:30:55,461 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,461 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,504 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 10:30:55,509 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 10:30:55,529 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 10:30:55,529 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 10:30:55,531 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,531 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,590 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:30:55,592 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:30:55,594 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,600 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:55,632 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:30:55,635 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:30:55,635 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,637 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:55,638 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 10:30:55,639 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 10:30:55,699 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:55,699 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:55,700 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,700 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,703 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:55,703 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:55,746 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? and CHARINDEX(b.STADTP,?)>0 GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:30:55,746 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:30:55,753 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:30:55,753 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String), 1,2,3,6(String)
2025-08-20 10:30:55,775 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 10:30:55,774 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 10:30:55,776 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,776 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:55,836 DEBUG [http-nio-8080-exec-9] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 10:30:55,886 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 10:30:56,514 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:30:56,605 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:30:57,117 DEBUG [http-nio-8080-exec-12] c.h.c.a.e.s.m.S.getSoilRealTimeList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.ID DEVICEID, A.CODE STCD, A.NAME STNM, A.Longitude LGTD, A.Latitude LTTD, A.Address STLC, CASE WHEN C.ADNM != D.ADNM THEN C.ADNM + D.ADNM WHEN C.ADNM = D.ADNM THEN C.ADNM ELSE NULL END ADNM, N.DEPTH, F.TM, F.WATER, CAST(F.WATER/(N.FieldCapacity*N.UnitWeight) AS DECIMAL(10, 1)) AS SWC FROM DEVICES A LEFT JOIN NODES N ON A.Id= N.DeviceId LEFT JOIN BSN_STADTP_B B ON A.Address = B.STCD LEFT JOIN MDT_ADCDINFO_B C ON LEFT(B.ADCD,6)+'000000000' = C.ADCD LEFT JOIN MDT_ADCDINFO_B D ON LEFT(B.ADCD,9)+'000000' = D.ADCD LEFT JOIN ( SELECT ROW_NUMBER() OVER(PARTITION BY R.CODE,R.DEPTH ORDER BY R.CreatedAt DESC) SNO,R.CODE CODE,R.DEPTH DEPTH ,R.CreatedAt TM,R.WATER WATER FROM DEPTHDATA R WHERE R.CreatedAt <= ? AND R.CreatedAt >= ? ) F ON A.CODE = F.CODE AND N.Depth = F.Depth AND F.SNO = 1 WHERE LEFT(B.ADCD, ?) = LEFT(?, ?) ORDER BY SWC DESC 
2025-08-20 10:30:57,178 DEBUG [http-nio-8080-exec-12] c.h.c.a.e.s.m.S.getSoilRealTimeList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 23:59:59.0(Timestamp), 2025-08-15 00:00:00.0(Timestamp), 2(Integer), 220000000000000(String), 2(Integer)
2025-08-20 10:30:57,212 DEBUG [http-nio-8080-exec-9] c.h.c.a.e.r.m.R.getRsvrStatComputeListForSummary [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT BO.OBJID, A.STCD, A.STNM, A.RSVRTP, D.TM, CONVERT(DECIMAL(13,3),ISNULL(D.tyAvw,0)) tyAvw, CONVERT(DECIMAL(13,2),ISNULL(E.lyAvw,0)) lyAvw, CONVERT(DECIMAL(13,2), case when D.tyAvw IS NULL AND E.lyAvw IS NOT NULL THEN 0 - E.lyAvw when D.tyAvw IS NOT NULL AND E.lyAvw IS NULL THEN D.tyAvw - 0 when D.tyAvw IS NULL AND E.lyAvw IS NULL THEN 0 else D.tyAvw-E.lyAvw END) lywD, CONVERT(DECIMAL(13,2), case when D.tyAvw IS NULL AND E.lyAvw != 0 THEN (0 - E.lyAvw)/E.lyAvw * 100 when E.lyAvw = 0 THEN 0 else (D.tyAvw-E.lyAvw)/E.lyAvw * 100 END) lywR, CONVERT(DECIMAL(13,2),ISNULL(F.oyAvw,0)) oyAvw, CONVERT(DECIMAL(13,2), case when D.tyAvw IS NULL AND F.oyAvw IS NOT NULL THEN 0 - E.lyAvw when D.tyAvw IS NOT NULL AND F.oyAvw IS NULL THEN D.tyAvw - 0 when D.tyAvw IS NULL AND F.oyAvw IS NULL THEN 0 else D.tyAvw-F.oyAvw END) oywD, CONVERT(DECIMAL(13,2), case when D.tyAvw IS NULL AND F.oyAvw != 0 THEN (0 - F.oyAvw)/F.oyAvw * 100 when F.oyAvw = 0 THEN 0 else (D.tyAvw-F.oyAvw)/F.oyAvw * 100 END) oywR , A.TTCP, A.DDCP, A.ACTCP, A.DDCP+A.ACTCP AS XLTTCP , A.ACTZ, A.DDZ, A.NORMZ, A.DSFLZ, A.CKFLZ, A.HHRZ, A.HLRZ FROM BSN_HYRSST_B A LEFT JOIN BSN_OBJONLY_B BO ON A.STCD = BO.OBJCD LEFT JOIN ( SELECT STCD, W AS tyAvw, TM, ROW_NUMBER() OVER (PARTITION BY STCD ORDER BY TM DESC) AS RN FROM ST_RSVR_R S WHERE DATEPART(HH,TM)=8 AND TM >= DATEADD(DD,-2,'2025-08-20 08:00:00') AND TM < '2025-08-21 08:00:00' AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD) ) D ON A.STCD = D.STCD AND D.RN = 1 LEFT JOIN ( SELECT STCD, AVG(W) lyAvw FROM ST_RSVR_R S WHERE DATEPART(HH,TM)=8 AND TM >= DATEADD(YY,-1,'2025-08-20 08:00:00') AND TM < DATEADD(YY,-1,'2025-08-21 08:00:00') AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD) GROUP BY STCD ) E ON A.STCD = E.STCD LEFT JOIN ( SELECT STCD, MYDAVW oyAvw FROM BNS_RSVRMYAV_S S WHERE YRTP = '0000' AND PRDTP = '1' AND MNTH = 7 AND DAY = 11 AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD) ) F ON A.STCD = F.STCD WHERE LEFT(A.ADCD, 2) = LEFT('220000000000000', 2) AND CHARINDEX(A.RSVRTP, '3,4,5') > 0 
2025-08-20 10:30:57,233 DEBUG [http-nio-8080-exec-9] c.h.c.a.e.r.m.R.getRsvrStatComputeListForSummary [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:57,259 DEBUG [http-nio-8080-exec-12] c.h.c.a.e.s.m.S.getSoilRealTimeList [BaseJdbcLogger.java : 143] <==      Total: 270
2025-08-20 10:30:57,300 DEBUG [Async-Executor-1] c.h.c.a.e.m.E.getShWarningSummary [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT D.PHONE_NO) RCOUNT, COUNT(DISTINCT D.CALL_ID) PCOUNT, COUNT(DISTINCT (CASE WHEN D.[STATUS] = '1' THEN D.CALL_ID ELSE NULL END)) FCOUNT FROM EW_WARNING_RECORD A WITH(NOLOCK) LEFT JOIN EW_REL_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID LEFT JOIN EW_MESSAGE C WITH(NOLOCK) ON C.MSG_ID = B.MSG_ID AND C.PUSH_MODE = '1' LEFT JOIN EW_CALL_FEEDBACK D WITH(NOLOCK) ON D.SMS_WARN_MSG_ID = C.MSG_ID WHERE A.WARN_STATUS_ID IN (0, 1, 10, 20, 21, 30) AND A.WARN_TIME >= '2025-08-20 08:00:00' AND A.WARN_TIME <= '2025-08-20 11:00:00' AND LEFT(A.ADCD, 2) = LEFT('220000000000000', 2) 
2025-08-20 10:30:57,300 DEBUG [Async-Executor-2] c.h.c.a.e.m.E.getRsvrWarningSummary [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT C.PHONE_NO) RCOUNT, COUNT(DISTINCT C.CALL_ID) PCOUNT, COUNT(DISTINCT (CASE WHEN C.[STATUS] = '1' THEN C.CALL_ID ELSE NULL END)) FCOUNT FROM EW_RSVR_WARNING_RECORD A WITH(NOLOCK) LEFT JOIN EW_RSVR_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID AND B.PUSH_MODE = '1' LEFT JOIN EW_CALL_FEEDBACK C WITH(NOLOCK) ON C.SMS_WARN_MSG_ID = B.MSG_ID WHERE CHARINDEX(A.[STATE], '0,1,2') > 0 AND A.WARN_TIME >= '2025-08-20 08:00:00' AND A.WARN_TIME <= '2025-08-20 11:00:00' AND EXISTS(SELECT 8 FROM BSN_STADTP_B WITH(NOLOCK) WHERE STADTP IN ('1', '2', '4') AND LEFT(ADCD, 2) = LEFT('220000000000000', 2) AND STCD = A.STCD ) AND EXISTS(SELECT 8 FROM ATT_RES_BASE WITH(NOLOCK) WHERE ENG_SCAL IN ('1', '2', '3', '4', '5') AND RES_CODE = A.RES_CODE) 
2025-08-20 10:30:57,312 DEBUG [Async-Executor-3] c.h.c.a.e.m.E.getRiverWarningSummary [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT C.PHONE_NO) RCOUNT, COUNT(DISTINCT C.CALL_ID) PCOUNT, COUNT(DISTINCT (CASE WHEN C.[STATUS] = '1' THEN C.CALL_ID ELSE NULL END)) FCOUNT FROM EW_RIVER_WARNING_RECORD A WITH(NOLOCK) LEFT JOIN EW_RIVER_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID AND B.PUSH_MODE = '1' LEFT JOIN EW_CALL_FEEDBACK C WITH(NOLOCK) ON C.SMS_WARN_MSG_ID = B.MSG_ID WHERE CHARINDEX(A.[STATE], '0,1,2') > 0 AND A.WARN_TIME >= '2025-08-20 08:00:00' AND A.WARN_TIME <= '2025-08-20 11:00:00' AND EXISTS(SELECT 8 FROM BSN_STADTP_B WITH(NOLOCK) WHERE STADTP IN ('1', '2') AND LEFT(ADCD, 2) = LEFT('220000000000000', 2) AND STCD = A.STCD ) 
2025-08-20 10:30:57,340 DEBUG [Async-Executor-1] c.h.c.a.e.m.E.getShWarningSummary [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:57,340 DEBUG [Async-Executor-3] c.h.c.a.e.m.E.getRiverWarningSummary [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:57,340 DEBUG [Async-Executor-2] c.h.c.a.e.m.E.getRsvrWarningSummary [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:57,364 DEBUG [Async-Executor-2] c.h.c.a.e.m.E.getRsvrWarningSummary [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:30:57,462 DEBUG [Async-Executor-3] c.h.c.a.e.m.E.getRiverWarningSummary [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:30:57,467 DEBUG [Async-Executor-1] c.h.c.a.e.m.E.getShWarningSummary [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:30:57,470 DEBUG [Async-Executor-6] c.h.c.a.e.m.E.getRiverWarningSummary [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT C.PHONE_NO) RCOUNT, COUNT(DISTINCT C.CALL_ID) PCOUNT, COUNT(DISTINCT (CASE WHEN C.[STATUS] = '1' THEN C.CALL_ID ELSE NULL END)) FCOUNT FROM EW_RIVER_WARNING_RECORD A WITH(NOLOCK) LEFT JOIN EW_RIVER_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID AND B.PUSH_MODE = '1' LEFT JOIN EW_CALL_FEEDBACK C WITH(NOLOCK) ON C.SMS_WARN_MSG_ID = B.MSG_ID WHERE CHARINDEX(A.[STATE], '0,1,2') > 0 AND A.WARN_TIME >= '2025-06-01 08:00:00' AND A.WARN_TIME <= '2025-08-20 11:00:00' AND EXISTS(SELECT 8 FROM BSN_STADTP_B WITH(NOLOCK) WHERE STADTP IN ('1', '2') AND LEFT(ADCD, 2) = LEFT('220000000000000', 2) AND STCD = A.STCD ) 
2025-08-20 10:30:57,470 DEBUG [Async-Executor-5] c.h.c.a.e.m.E.getRsvrWarningSummary [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT C.PHONE_NO) RCOUNT, COUNT(DISTINCT C.CALL_ID) PCOUNT, COUNT(DISTINCT (CASE WHEN C.[STATUS] = '1' THEN C.CALL_ID ELSE NULL END)) FCOUNT FROM EW_RSVR_WARNING_RECORD A WITH(NOLOCK) LEFT JOIN EW_RSVR_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID AND B.PUSH_MODE = '1' LEFT JOIN EW_CALL_FEEDBACK C WITH(NOLOCK) ON C.SMS_WARN_MSG_ID = B.MSG_ID WHERE CHARINDEX(A.[STATE], '0,1,2') > 0 AND A.WARN_TIME >= '2025-06-01 08:00:00' AND A.WARN_TIME <= '2025-08-20 11:00:00' AND EXISTS(SELECT 8 FROM BSN_STADTP_B WITH(NOLOCK) WHERE STADTP IN ('1', '2', '4') AND LEFT(ADCD, 2) = LEFT('220000000000000', 2) AND STCD = A.STCD ) AND EXISTS(SELECT 8 FROM ATT_RES_BASE WITH(NOLOCK) WHERE ENG_SCAL IN ('1', '2', '3', '4', '5') AND RES_CODE = A.RES_CODE) 
2025-08-20 10:30:57,470 DEBUG [Async-Executor-4] c.h.c.a.e.m.E.getShWarningSummary [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT D.PHONE_NO) RCOUNT, COUNT(DISTINCT D.CALL_ID) PCOUNT, COUNT(DISTINCT (CASE WHEN D.[STATUS] = '1' THEN D.CALL_ID ELSE NULL END)) FCOUNT FROM EW_WARNING_RECORD A WITH(NOLOCK) LEFT JOIN EW_REL_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID LEFT JOIN EW_MESSAGE C WITH(NOLOCK) ON C.MSG_ID = B.MSG_ID AND C.PUSH_MODE = '1' LEFT JOIN EW_CALL_FEEDBACK D WITH(NOLOCK) ON D.SMS_WARN_MSG_ID = C.MSG_ID WHERE A.WARN_STATUS_ID IN (0, 1, 10, 20, 21, 30) AND A.WARN_TIME >= '2025-06-01 08:00:00' AND A.WARN_TIME <= '2025-08-20 11:00:00' AND LEFT(A.ADCD, 2) = LEFT('220000000000000', 2) 
2025-08-20 10:30:57,471 DEBUG [Async-Executor-4] c.h.c.a.e.m.E.getShWarningSummary [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:57,471 DEBUG [Async-Executor-5] c.h.c.a.e.m.E.getRsvrWarningSummary [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:57,471 DEBUG [Async-Executor-6] c.h.c.a.e.m.E.getRiverWarningSummary [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:57,489 DEBUG [http-nio-8080-exec-9] c.h.c.a.e.r.m.R.getRsvrStatComputeListForSummary [BaseJdbcLogger.java : 143] <==      Total: 131
2025-08-20 10:30:57,518 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStReglaTbyInfo [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RF.*, RF.YMDH as TM , FF.INW, STB.STNM, STB.STTP, STV.PLGTD, STV.PLTTD , CASE WHEN RFC.RSVRTP = '4' OR RFC.RSVRTP = '5' THEN '5' ELSE RFC.RSVRTP END RSVRTP , FC.WNSTATUS, RFC.NORMZ, HHRZ, DAMEL, DDZ , CASE WHEN RF.Z > RFS.FSLTDZ THEN 1 ELSE 0 END AS FSLTDZ_FLAG , RF.Z - RFS.FSLTDZ AS RZFSLTDZ, RFS.FSLTDZ , RF.W - RFS.FSLTDW AS WFSLTDW, RFS.FSLTDW , RF.Z - RFC.NORMZ AS RZNORMZ, FF.Q, FF.INW , STV.OSFLG, R.RZ BGRZ, R.W BGW, R.INQ BGINQ, R.OTQ BGOTQ FROM (SELECT * FROM (SELECT *, ROW_NUMBER() OVER (PARTITION BY STCD ORDER BY FYMDH DESC, IYMDH DESC, Z DESC, YMDH ASC) SN FROM ST_REGLAT_F WHERE FYMDH >= GETDATE()-10 ) FF WHERE FF.SN=1 ) RF LEFT JOIN ST_FORECASTC_F FC ON RF.STCD=FC.STCD AND RF.UNITNAME=FC.UNITNAME AND RF.PLCD=FC.PLCD AND RF.FYMDH=FC.FYMDH AND RF.IYMDH=FC.IYMDH LEFT JOIN ST_STBPRP_B STB ON RF.STCD = STB.STCD LEFT JOIN ST_RSVRFCCH_B RFC ON RF.STCD = RFC.STCD LEFT JOIN ST_RSVRFSR_B RFS on RF.STCD = RFS.STCD AND RIGHT(CONVERT(VARCHAR,RF.YMDH,112),4) BETWEEN RFS.BGMD AND RFS.EDMD LEFT JOIN (SELECT STCD, FYMDH, MAX(IYMDH) IYMDH FROM ST_FORECAST_F WHERE FYMDH >= GETDATE()-10 GROUP BY STCD, FYMDH) F ON RF.STCD=F.STCD AND RF.FYMDH=F.FYMDH LEFT JOIN (SELECT STCD, FYMDH, IYMDH, MAX(Q) Q, AVG(Q)*datediff(second,MIN(YMDH),MAX(YMDH))/1000000 AS INW FROM ST_FORECAST_F WHERE FYMDH >= GETDATE()-10 GROUP BY STCD, FYMDH, IYMDH) FF ON RF.STCD=FF.STCD AND RF.FYMDH=FF.FYMDH AND F.IYMDH=FF.IYMDH LEFT JOIN ST_RSVR_R R ON RF.STCD=R.STCD AND RF.FYMDH=R.TM LEFT JOIN BSN_STBPRP_V STV ON RF.STCD = STV.STCD LEFT JOIN BSN_STADTP_B STT ON RF.STCD = STT.STCD WHERE STB.STTP IN ('RR','RQ') AND left(STB.ADDVCD, ?) = ? ORDER BY FSLTDZ_FLAG DESC, RSVRTP DESC, RZFSLTDZ DESC 
2025-08-20 10:30:57,547 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStReglaTbyInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 10:30:57,572 DEBUG [Async-Executor-6] c.h.c.a.e.m.E.getRiverWarningSummary [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:30:57,622 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStReglaTbyInfo [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:57,826 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 10:30:57,827 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 10:30(String), 2025-08-20 10:30(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0(String)
2025-08-20 10:30:58,015 DEBUG [Async-Executor-5] c.h.c.a.e.m.E.getRsvrWarningSummary [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:30:58,229 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 10:30:58,231 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,318 INFO [http-nio-8080-exec-3] io.lettuce.core.EpollProvider [EpollProvider.java : 68] Starting without optional epoll library
2025-08-20 10:30:58,326 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:58,327 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,328 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 10:30:58,329 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:58,330 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:30:58,331 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,331 INFO [http-nio-8080-exec-3] io.lettuce.core.KqueueProvider [KqueueProvider.java : 70] Starting without optional kqueue library
2025-08-20 10:30:58,345 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 10:30:58,346 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 10:30:58,355 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,355 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,355 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,356 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,356 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,356 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,356 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,356 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,356 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,357 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,405 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:30:58,405 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,412 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 461
2025-08-20 10:30:58,412 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,412 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,413 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,413 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,414 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:30:58,415 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 10:30:58,415 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,453 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 10:30:58,455 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 10:30:58,456 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,529 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:30:58,532 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:30:58,532 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,535 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:58,537 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 10:30:58,537 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:58,537 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,539 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:58,540 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:30:58,541 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:30:58,559 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:58,559 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,571 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:58,572 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:30:58,573 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,649 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 10:30:58,672 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:30:58,673 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:30:58,674 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,675 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:58,678 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:58,678 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,682 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:58,684 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:30:58,684 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:30:58,685 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:58,686 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,687 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:58,689 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:30:58,689 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,793 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:30:58,794 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:30:58,794 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,795 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:58,796 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:58,797 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:58,798 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:58,799 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:30:58,800 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 10:30:58,879 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:30:59,055 DEBUG [Async-Executor-4] c.h.c.a.e.m.E.getShWarningSummary [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:30:59,091 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? , ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? , ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:30:59,167 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 1(Integer), 2(Integer), 3(Integer), 5(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 1(Integer), 2(Integer), 3(Integer), 5(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:30:59,189 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:59,519 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 10:30:59,538 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:30:59,539 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:59,540 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:30:59,556 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 10:30:59,557 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 11:00(String)
2025-08-20 10:30:59,560 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:59,748 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:30:59,772 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:30:59,773 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:59,785 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:30:59,795 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:30:59,795 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:59,796 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:30:59,813 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:30:59,814 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:59,838 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:30:59,912 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:30:59,920 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:30:59,921 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:30:59,922 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:30:59,923 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:30:59,924 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:31:00,006 DEBUG [http-nio-8080-exec-6] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:31:00,007 DEBUG [http-nio-8080-exec-6] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 1(Integer), 2(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 1(Integer), 2(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:31:00,020 DEBUG [http-nio-8080-exec-6] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:31:00,036 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:31:00,037 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:31:00,038 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:31:00,040 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:31:00,041 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:31:00,042 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:31:00,043 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:31:00,044 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:31:00,046 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:31:00,200 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getAdcdContrastBytmYear [BaseJdbcLogger.java : 143] ==>  Preparing: select CONVERT(date, IDTM+'-01', 111) as IDTM, avg(ACCP) ACCP from ( select CONVERT(varchar(7), dateadd(MM,-1,IDTM), 23) as IDTM, ACCP, a.STCD from ST_PSTAT_R a left join BSN_STBPRP_V b on a.STCD = b.STCD left join ST_STSMTASK_B tk on a.STCD=tk.STCD where STTDRCD = 5 and tk.PFL='1' and b.ADCD like '22%' and CONVERT(varchar(4), dateadd(MM,-1,IDTM), 112) in ( ? , ? ) union select '8888-' + cast(MNTH as varchar) as IDTM, MYMAVP as ACCP, a.STCD from ST_PDMMYAV_S a left join BSN_STBPRP_V b on a.STCD = b.STCD left join ST_STSMTASK_B tk on a.STCD=tk.STCD where PRDTP = 4 and tk.PFL='1' and b.ADCD like '22%' ) a group by IDTM order by IDTM 
2025-08-20 10:31:00,201 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getAdcdContrastBytmYear [BaseJdbcLogger.java : 143] ==> Parameters: 2025(String), 2024(String)
2025-08-20 10:31:00,924 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:31:00,928 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getAdcdContrastBytmYear [BaseJdbcLogger.java : 143] <==      Total: 28
2025-08-20 10:31:00,930 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getAdcdContrastBytmMonth [BaseJdbcLogger.java : 143] ==>  Preparing: select CONVERT(date, IDTM, 111) IDTM, avg(ACCP) ACCP from ( select CONVERT(varchar(10), dateadd(DD,-1,IDTM), 23) IDTM, ACCP, a.STCD from ST_PSTAT_R a left join BSN_STBPRP_V b on a.STCD = b.STCD left join ST_STSMTASK_B tk on a.STCD=tk.STCD where STTDRCD = 1 and tk.PFL='1' and b.ADCD like '22%' and CONVERT(varchar(6), dateadd(DD,-1,IDTM), 112) in ( ? , ? ) union select '8888-'+CAST(MNTH as varchar)+'-'+CAST(day as varchar) as IDTM, MYDAVP, a.STCD from ST_PDDMYAV_S a left join BSN_STBPRP_V b on a.STCD = b.STCD left join ST_STSMTASK_B tk on a.STCD=tk.STCD where tk.PFL='1' and b.ADCD like '22%' and MNTH = CAST(right(?,2) as int) ) a group by IDTM order by IDTM 
2025-08-20 10:31:00,931 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getAdcdContrastBytmMonth [BaseJdbcLogger.java : 143] ==> Parameters: 202508(String), 202408(String), 202508(String)
2025-08-20 10:31:00,940 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:31:00,941 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:31:00,942 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:31:00,949 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 11:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:31:00,950 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:31:00,954 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:31:00,956 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:31:00,956 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:31:01,091 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:31:01,092 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:31:01,092 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:31:01,093 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:31:01,095 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-18 11:00:00') and TM <= CONVERT(datetime,'2025-08-19 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:31:01,095 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:31:01,287 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:31:01,289 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:31:01,289 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:31:02,318 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getAdcdContrastBytmMonth [BaseJdbcLogger.java : 143] <==      Total: 62
2025-08-20 10:31:02,322 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:31:02,337 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:31:02,337 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:31:02,344 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:31:05,571 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:31:05,573 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 1(Integer), 2(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 1(Integer), 2(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:31:05,600 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 81
2025-08-20 10:31:06,338 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ORDER BY WARN_TIME DESC, LEFT(r.ADCD, 12) ASC, WARN_NAME DESC 
2025-08-20 10:31:06,340 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getPageList [BaseJdbcLogger.java : 143] ==> Parameters: 15(Integer), 220281102203002(String), 15(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:31:06,342 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getPageList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:31:06,350 DEBUG [http-nio-8080-exec-4] c.h.c.a.f.m.F.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.ADCD, C.ADNM, DATEADD(HOUR, - B.STDT, A.WARN_TIME) AS STM, A.WARN_TIME AS ETM, B.STDT, B.STCD, D.STNM FROM EW_FUSION_WARNING_RECORD A LEFT JOIN EW_FUSION_SNAPSHOT_S B ON A.WARN_ID = B.WARN_ID LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD LEFT JOIN ST_STBPRP_B D ON D.STCD = B.STCD WHERE A.WARN_ID = ? 
2025-08-20 10:31:06,352 DEBUG [http-nio-8080-exec-4] c.h.c.a.f.m.F.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==> Parameters: B0BDA292-85FC-479A-A256-C8364B3B2E99(String)
2025-08-20 10:31:06,363 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:31:06,364 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281102203002(String)
2025-08-20 10:31:06,364 DEBUG [http-nio-8080-exec-4] c.h.c.a.f.m.F.getSnapshotMonitor [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:31:06,370 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:31:06,371 DEBUG [http-nio-8080-exec-10] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_STATUS_ID, B.STATUS_NAME WARN_STATUS_NAME, B.SHORT_NAME WARN_STATUS_SHORT_NAME, REMARK, CREATE_BY, CREATE_TIME FROM EW_FUSION_WARNING_FLOW A LEFT JOIN EW_FUSION_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID WHERE WARN_ID = ? ORDER BY CREATE_TIME, WARN_STATUS_ID ASC 
2025-08-20 10:31:06,371 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT r.WARN_ID AS warnId, r.WARN_NAME AS warnName, a.ADNM AS warnAdnm, r.WARN_MODEL AS warnModel, r.WARN_TIME AS warnTime, f.CREATE_TIME AS closeTime, r.WARN_MODE AS warnMode, r.WARN_DESC AS warnDesc, a.ADNM AS adnm, s.REPRD AS reprd, s.RDRPT AS rdrpt, s.STDT as stdt, s.FDRPT AS fdrpt, s.FCDT AS fcdt, st.STNM AS stnm, s.STCD AS stcd, s.RACCP AS raccp, s.FACCP AS faccp FROM EW_FUSION_WARNING_RECORD r LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_FLOW f ON r.WARN_ID = f.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S s ON r.WARN_ID = s.WARN_ID LEFT JOIN ST_STBPRP_B st ON s.STCD = st.STCD WHERE r.WARN_ID = ? 
2025-08-20 10:31:06,371 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:31:06,372 DEBUG [http-nio-8080-exec-10] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] ==> Parameters: B0BDA292-85FC-479A-A256-C8364B3B2E99(String)
2025-08-20 10:31:06,372 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281102203000(String)
2025-08-20 10:31:06,372 DEBUG [Async-Executor-7] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.MSG_ID, A.WARN_ID, B.PUSH_MODE FROM EW_FUSION_REL_MESSAGE A LEFT JOIN EW_FUSION_MESSAGE B ON A.MSG_ID = B.MSG_ID WHERE A.WARN_ID = ? ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:31:06,372 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==> Parameters: B0BDA292-85FC-479A-A256-C8364B3B2E99(String)
2025-08-20 10:31:06,372 DEBUG [Async-Executor-8] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.ERROR_CODE, B.[ERROR_MESSAGE], C.[STATUS] READ_STATUS, C.CONFIRM_RESULT, C.CONFIRM_TIME FROM EW_FUSION_RECEIVER A LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS (SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID) LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '9' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS (SELECT 8 FROM EW_FUSION_MESSAGE B INNER JOIN EW_FUSION_REL_MESSAGE C ON B.MSG_ID = C.MSG_ID WHERE B.PUSH_MODE = '1' AND C.WARN_ID = ? AND B.MSG_ID = A.MSG_ID) ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC 
2025-08-20 10:31:06,372 DEBUG [Async-Executor-7] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] ==> Parameters: B0BDA292-85FC-479A-A256-C8364B3B2E99(String)
2025-08-20 10:31:06,373 DEBUG [Async-Executor-9] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.EXT_SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.RECEIPT_TIME, B.CALL_RESULT, B.CALL_RESULT_TEXT, B.FAIL_CAUSE FROM EW_FUSION_RECEIVER A LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID WHERE EXISTS ( SELECT 8 FROM EW_FUSION_MESSAGE C INNER JOIN EW_FUSION_REL_MESSAGE D ON C.MSG_ID = D.MSG_ID WHERE C.PUSH_MODE = '2' AND D.WARN_ID = ? AND C.MSG_ID = A.MSG_ID) ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC 
2025-08-20 10:31:06,373 DEBUG [Async-Executor-9] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: B0BDA292-85FC-479A-A256-C8364B3B2E99(String)
2025-08-20 10:31:06,373 DEBUG [Async-Executor-8] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: B0BDA292-85FC-479A-A256-C8364B3B2E99(String)
2025-08-20 10:31:06,375 DEBUG [Async-Executor-7] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:31:06,377 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:31:06,384 DEBUG [Async-Executor-9] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] <==      Total: 12
2025-08-20 10:31:06,385 DEBUG [http-nio-8080-exec-10] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] <==      Total: 3
2025-08-20 10:31:06,387 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:31:07,508 DEBUG [Async-Executor-8] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] <==      Total: 12
2025-08-20 10:35:26,266 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 10:35:56,872 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 10:35:56,873 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:56,873 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 10:35:56,873 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 10:35(String), 2025-08-20 10:35(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 10:35:56,876 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 10:35:56,877 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 10:35:56,965 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 10:35:56,966 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:35:56,967 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:56,969 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:35:56,976 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:35:56,977 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,080 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 10:35:57,096 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:35:57,097 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,099 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:35:57,101 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:35:57,101 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,109 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:35:57,111 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:35:57,112 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,114 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:35:57,115 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:35:57,116 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,125 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:35:57,135 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 10:35:57,136 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:35:57,222 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:35:57,223 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:35:57,224 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,225 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:35:57,227 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:35:57,228 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,230 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:35:57,231 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:35:57,232 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 10:35:57,352 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,353 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,353 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,353 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,353 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,354 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,354 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,354 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,354 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,354 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,369 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 10:35:57,370 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,370 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,370 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,370 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,371 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:35:57,372 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 10:35:57,372 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,396 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 10:35:57,399 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 10:35:57,399 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,442 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 10:35:57,445 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 10:35:57,445 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:57,497 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 10:35:58,015 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 10:35:58,031 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:35:58,032 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:35:58,033 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:35:58,035 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 10:35:58,036 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 11:00(String)
2025-08-20 10:35:58,037 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:35:58,164 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 10:40:26,275 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 10:40:56,874 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 10:40:56,875 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 10:40(String), 2025-08-20 10:40(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 10:40:56,877 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 10:40:56,877 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:56,878 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 10:40:56,878 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 10:40:56,970 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 10:40:56,971 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:40:56,972 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:56,974 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:40:56,976 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:40:56,976 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,088 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 10:40:57,103 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:40:57,104 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,106 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:40:57,107 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:40:57,107 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,113 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:40:57,114 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:40:57,115 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,117 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:40:57,119 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:40:57,119 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,121 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:40:57,123 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 10:40:57,123 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:40:57,227 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:40:57,228 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:40:57,229 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,230 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:40:57,230 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:40:57,231 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,232 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:40:57,233 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:40:57,234 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 10:40:57,363 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,364 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,364 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,364 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,364 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,365 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,365 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,366 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,366 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,366 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,379 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 10:40:57,380 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,380 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,380 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,381 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,381 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:40:57,382 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 10:40:57,382 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,409 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 10:40:57,412 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 10:40:57,413 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,452 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 10:40:57,456 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 10:40:57,456 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:57,504 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 10:40:57,991 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 10:40:58,007 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:40:58,008 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:40:58,009 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:40:58,011 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 10:40:58,012 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 11:00(String)
2025-08-20 10:40:58,014 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:40:58,203 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 10:45:15,637 DEBUG [Async-Executor-11] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.ERROR_CODE, B.[ERROR_MESSAGE], C.[STATUS] READ_STATUS, C.CONFIRM_RESULT, C.CONFIRM_TIME FROM EW_FUSION_RECEIVER A LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS (SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID) LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '9' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS (SELECT 8 FROM EW_FUSION_MESSAGE B INNER JOIN EW_FUSION_REL_MESSAGE C ON B.MSG_ID = C.MSG_ID WHERE B.PUSH_MODE = '1' AND C.WARN_ID = ? AND B.MSG_ID = A.MSG_ID) ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC 
2025-08-20 10:45:15,638 DEBUG [Async-Executor-10] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.MSG_ID, A.WARN_ID, B.PUSH_MODE FROM EW_FUSION_REL_MESSAGE A LEFT JOIN EW_FUSION_MESSAGE B ON A.MSG_ID = B.MSG_ID WHERE A.WARN_ID = ? ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:45:15,637 DEBUG [http-nio-8080-exec-2] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT r.WARN_ID AS warnId, r.WARN_NAME AS warnName, a.ADNM AS warnAdnm, r.WARN_MODEL AS warnModel, r.WARN_TIME AS warnTime, f.CREATE_TIME AS closeTime, r.WARN_MODE AS warnMode, r.WARN_DESC AS warnDesc, a.ADNM AS adnm, s.REPRD AS reprd, s.RDRPT AS rdrpt, s.STDT as stdt, s.FDRPT AS fdrpt, s.FCDT AS fcdt, st.STNM AS stnm, s.STCD AS stcd, s.RACCP AS raccp, s.FACCP AS faccp FROM EW_FUSION_WARNING_RECORD r LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_FLOW f ON r.WARN_ID = f.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S s ON r.WARN_ID = s.WARN_ID LEFT JOIN ST_STBPRP_B st ON s.STCD = st.STCD WHERE r.WARN_ID = ? 
2025-08-20 10:45:15,637 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_STATUS_ID, B.STATUS_NAME WARN_STATUS_NAME, B.SHORT_NAME WARN_STATUS_SHORT_NAME, REMARK, CREATE_BY, CREATE_TIME FROM EW_FUSION_WARNING_FLOW A LEFT JOIN EW_FUSION_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID WHERE WARN_ID = ? ORDER BY CREATE_TIME, WARN_STATUS_ID ASC 
2025-08-20 10:45:15,638 DEBUG [Async-Executor-12] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.EXT_SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.RECEIPT_TIME, B.CALL_RESULT, B.CALL_RESULT_TEXT, B.FAIL_CAUSE FROM EW_FUSION_RECEIVER A LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID WHERE EXISTS ( SELECT 8 FROM EW_FUSION_MESSAGE C INNER JOIN EW_FUSION_REL_MESSAGE D ON C.MSG_ID = D.MSG_ID WHERE C.PUSH_MODE = '2' AND D.WARN_ID = ? AND C.MSG_ID = A.MSG_ID) ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC 
2025-08-20 10:45:15,638 DEBUG [Async-Executor-10] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] ==> Parameters: 83F4A359-5F1E-4E54-B791-814DC46BE6B1(String)
2025-08-20 10:45:15,638 DEBUG [Async-Executor-11] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: 83F4A359-5F1E-4E54-B791-814DC46BE6B1(String)
2025-08-20 10:45:15,638 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] ==> Parameters: 83F4A359-5F1E-4E54-B791-814DC46BE6B1(String)
2025-08-20 10:45:15,638 DEBUG [http-nio-8080-exec-2] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==> Parameters: 83F4A359-5F1E-4E54-B791-814DC46BE6B1(String)
2025-08-20 10:45:15,638 DEBUG [Async-Executor-12] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: 83F4A359-5F1E-4E54-B791-814DC46BE6B1(String)
2025-08-20 10:45:15,640 DEBUG [Async-Executor-10] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:45:15,640 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] <==      Total: 3
2025-08-20 10:45:15,641 DEBUG [http-nio-8080-exec-2] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:45:15,650 DEBUG [Async-Executor-12] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] <==      Total: 12
2025-08-20 10:45:16,753 DEBUG [Async-Executor-11] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] <==      Total: 12
2025-08-20 10:45:26,288 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 10:45:29,402 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getSendingPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT COUNT(1) FROM EW_CALL_FEEDBACK A LEFT JOIN EW_FUSION_REL_MESSAGE B ON B.MSG_ID = SMS_WARN_MSG_ID LEFT JOIN SMS_SENDING SS ON EXISTS (SELECT 8 FROM SMS_MESSAGE WHERE BUSINESS_KEY = SMS_WARN_MSG_ID AND MSG_ID = SS.MSG_ID) AND SS.PHONE_NO = A.PHONE_NO LEFT JOIN XCC_SENDING XS ON EXISTS (SELECT 8 FROM XCC_MESSAGE WHERE EXT_MSG_ID = XCC_WARN_MSG_ID AND MSG_ID = XS.MSG_ID) AND XS.CALLED_PHONE = A.PHONE_NO LEFT JOIN EW_FUSION_RECEIVER REC ON B.MSG_ID = REC.MSG_ID AND REC.PHONE_NO = A.PHONE_NO WHERE A.CALL_TYPE = '9' AND B.WARN_ID = ? 
2025-08-20 10:45:29,403 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getSendingPageList [BaseJdbcLogger.java : 143] ==> Parameters: B0BDA292-85FC-479A-A256-C8364B3B2E99(String)
2025-08-20 10:45:29,560 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getSendingPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.CALL_ID, A.PHONE_NO phoneNo, REC.RECEIVER AS receiverName, SS.[STATUS] AS smsStatus, SS.SEND_TIME AS smsSendTime, SS.[ERROR_MESSAGE] SMS_FAIL_CAUSE, XS.[STATUS] AS voiceStatus, ISNULL(XS.CALL_START_TIME, XS.SEND_TIME) AS voiceSendTime, XS.FAIL_CAUSE XCC_FAIL_CAUSE, XS.CALL_RESULT XCC_CALL_RESULT, XS.CALL_RESULT_TEXT XCC_CALL_RESULT_TEXT, XS.EXT_SEND_ID XCC_EXT_SEND_ID, REC.TAG AS receiverType, REC.[POSITION] AS [position], A.[STATUS] FEEDBACK_STATUS, A.CONFIRM_RESULT, A.CONFIRM_TIME FROM EW_CALL_FEEDBACK A LEFT JOIN EW_FUSION_REL_MESSAGE B ON B.MSG_ID = SMS_WARN_MSG_ID LEFT JOIN SMS_SENDING SS ON EXISTS (SELECT 8 FROM SMS_MESSAGE WHERE BUSINESS_KEY = SMS_WARN_MSG_ID AND MSG_ID = SS.MSG_ID) AND SS.PHONE_NO = A.PHONE_NO LEFT JOIN XCC_SENDING XS ON EXISTS (SELECT 8 FROM XCC_MESSAGE WHERE EXT_MSG_ID = XCC_WARN_MSG_ID AND MSG_ID = XS.MSG_ID) AND XS.CALLED_PHONE = A.PHONE_NO LEFT JOIN EW_FUSION_RECEIVER REC ON B.MSG_ID = REC.MSG_ID AND REC.PHONE_NO = A.PHONE_NO WHERE A.CALL_TYPE = '9' AND B.WARN_ID = ? ORDER BY A.CREATE_TIME DESC, A.PHONE_NO ASC OFFSET ? ROWS FETCH NEXT ? ROWS ONLY 
2025-08-20 10:45:29,561 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getSendingPageList [BaseJdbcLogger.java : 143] ==> Parameters: B0BDA292-85FC-479A-A256-C8364B3B2E99(String), 0(Long), 30(Long)
2025-08-20 10:45:29,917 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getSendingPageList [BaseJdbcLogger.java : 143] <==      Total: 12
2025-08-20 10:45:32,074 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT r.WARN_ID AS warnId, r.WARN_NAME AS warnName, a.ADNM AS warnAdnm, r.WARN_MODEL AS warnModel, r.WARN_TIME AS warnTime, f.CREATE_TIME AS closeTime, r.WARN_MODE AS warnMode, r.WARN_DESC AS warnDesc, a.ADNM AS adnm, s.REPRD AS reprd, s.RDRPT AS rdrpt, s.STDT as stdt, s.FDRPT AS fdrpt, s.FCDT AS fcdt, st.STNM AS stnm, s.STCD AS stcd, s.RACCP AS raccp, s.FACCP AS faccp FROM EW_FUSION_WARNING_RECORD r LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_FLOW f ON r.WARN_ID = f.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S s ON r.WARN_ID = s.WARN_ID LEFT JOIN ST_STBPRP_B st ON s.STCD = st.STCD WHERE r.WARN_ID = ? 
2025-08-20 10:45:32,075 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==> Parameters: B0BDA292-85FC-479A-A256-C8364B3B2E99(String)
2025-08-20 10:45:32,088 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:45:56,912 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 10:45:56,913 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 10:45(String), 2025-08-20 10:45(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 10:45:56,913 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 10:45:56,913 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:56,917 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 10:45:56,918 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 10:45:57,026 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 10:45:57,027 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:45:57,027 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,042 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:45:57,043 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:45:57,043 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,110 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 10:45:57,121 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:45:57,121 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,147 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:45:57,148 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:45:57,149 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,172 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:45:57,174 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:45:57,174 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,176 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:45:57,177 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:45:57,178 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,250 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:45:57,252 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 10:45:57,253 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:45:57,272 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:45:57,273 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:45:57,274 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,275 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:45:57,276 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:45:57,277 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,278 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:45:57,279 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:45:57,280 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 10:45:57,409 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,410 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,410 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,410 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,410 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,411 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,411 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,411 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,411 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,412 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,425 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 10:45:57,426 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,426 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,427 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,427 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,427 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:45:57,428 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 10:45:57,428 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,450 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 10:45:57,452 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 10:45:57,453 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,502 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 10:45:57,507 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 10:45:57,508 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:57,563 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 10:45:58,076 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 10:45:58,113 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:45:58,114 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:45:58,154 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:45:58,157 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 10:45:58,157 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 11:00(String)
2025-08-20 10:45:58,197 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:45:58,212 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 10:50:26,295 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 10:50:27,181 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? , ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? , ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:50:27,183 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 1(Integer), 2(Integer), 3(Integer), 5(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 1(Integer), 2(Integer), 3(Integer), 5(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:50:27,253 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 334
2025-08-20 10:50:29,532 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:50:29,533 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281102203001(String)
2025-08-20 10:50:29,540 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:50:29,541 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:50:29,543 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281102203000(String)
2025-08-20 10:50:29,549 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:50:29,564 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, A.STCD, C.STNM, STM, ETM, ACCP, SLM_TM, SLM, SLEP FROM EW_SNAPSHOT_MONITOR A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN ST_STBPRP_B C ON C.STCD = A.STCD WHERE WARN_ID = ? AND WARN_GRADE_ID = ? 
2025-08-20 10:50:29,565 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, A.LGTD, A.LTTD, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ORDER BY A.WARN_TIME DESC, LEFT(A.ADCD, 12) ASC, A.WARN_NAME DESC 
2025-08-20 10:50:29,565 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String), 5(Integer)
2025-08-20 10:50:29,566 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==> Parameters: 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 15(Integer), 220281102203001(String), 15(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:50:29,575 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] <==      Total: 3
2025-08-20 10:50:29,587 DEBUG [http-nio-8080-exec-4] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, WARN_STATUS_ID, B.STATUS_NAME WARN_STATUS_NAME, B.ALIAS_NAME WARN_STATUS_ALIAS_NAME, B.SHORT_NAME WARN_STATUS_SHORT_NAME, REMARK, CREATE_BY, CREATE_TIME FROM EW_WARNING_FLOW A LEFT JOIN EW_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID WHERE WARN_ID = ? ORDER BY CREATE_TIME ASC 
2025-08-20 10:50:29,587 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:50:29,589 DEBUG [http-nio-8080-exec-4] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:50:29,589 DEBUG [http-nio-8080-exec-10] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.WARN_GRADE_ID, C.GRADE_NAME WARN_GRADE_NAME, C.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_MODEL, A.WARN_TIME, A.WARN_DESC, B.WARN_STDT, B.ADCD, E.ADNM, D.WSCD, D.STCD, F.STNM, D.STM, D.ETM, D.ACCP, D.SLM_TM, D.SLM, D.SLEP, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_PROCESS A LEFT JOIN EW_WARNING_RECORD B ON B.WARN_ID = A.WARN_ID LEFT JOIN EW_WARNING_GRADE C ON C.GRADE_ID = A.WARN_GRADE_ID LEFT JOIN EW_SNAPSHOT_MONITOR D ON D.WARN_ID = A.WARN_ID AND D.WARN_GRADE_ID = A.WARN_GRADE_ID LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = B.ADCD LEFT JOIN ST_STBPRP_B F ON F.STCD = D.STCD LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_ID = ? ORDER BY A.WARN_TIME ASC 
2025-08-20 10:50:29,590 DEBUG [http-nio-8080-exec-1] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, LWATER, STDT, DRPT FROM EW_SNAPSHOT_INDEX A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD WHERE WARN_ID = ? ORDER BY WARN_GRADE_ID ASC 
2025-08-20 10:50:29,590 DEBUG [Async-Executor-13] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.MSG_ID, A.WARN_ID, A.GRADE_ID, A.STATUS_ID, B.PUSH_MODE FROM EW_REL_WARNING_MESSAGE A LEFT JOIN EW_MESSAGE B ON A.MSG_ID = B.MSG_ID WHERE A.WARN_ID = ? ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:50:29,590 DEBUG [http-nio-8080-exec-10] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:50:29,592 DEBUG [Async-Executor-14] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.ERROR_CODE, B.[ERROR_MESSAGE], C.[STATUS] READ_STATUS, C.CONFIRM_RESULT, C.CONFIRM_TIME FROM EW_RECEIVER A LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS(SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID) LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE B INNER JOIN EW_REL_WARNING_MESSAGE C ON B.MSG_ID = C.MSG_ID WHERE B.PUSH_MODE = '1' AND C.WARN_ID = ? AND B.MSG_ID = A.MSG_ID) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:50:29,593 DEBUG [Async-Executor-15] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.EXT_SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.RECEIPT_TIME, B.CALL_RESULT, B.CALL_RESULT_TEXT, B.FAIL_CAUSE FROM EW_RECEIVER A LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.XCC_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE C INNER JOIN EW_REL_WARNING_MESSAGE D ON C.MSG_ID = D.MSG_ID WHERE C.PUSH_MODE = '2' AND D.WARN_ID = ? AND C.MSG_ID = A.MSG_ID) AND (C.[STATUS] = '0' OR (C.[STATUS] = '1' AND C.CONFIRM_RESULT NOT IN ('1', '2'))) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:50:29,593 DEBUG [http-nio-8080-exec-4] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] <==      Total: 5
2025-08-20 10:50:29,592 DEBUG [http-nio-8080-exec-1] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:50:29,593 DEBUG [Async-Executor-13] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:50:29,593 DEBUG [Async-Executor-14] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:50:29,593 DEBUG [Async-Executor-15] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:50:29,603 DEBUG [http-nio-8080-exec-10] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:50:29,615 DEBUG [http-nio-8080-exec-1] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:50:29,620 DEBUG [Async-Executor-13] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] <==      Total: 5
2025-08-20 10:50:29,821 DEBUG [Async-Executor-14] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] <==      Total: 9
2025-08-20 10:50:30,130 DEBUG [Async-Executor-15] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] <==      Total: 7
2025-08-20 10:50:56,862 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 10:50:56,862 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 10:50:56,863 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 10:50(String), 2025-08-20 10:50(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 10:50:56,863 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:56,867 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 10:50:56,868 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 10:50:56,954 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 10:50:56,955 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:50:56,956 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:56,958 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:50:56,959 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:50:56,960 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,077 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 10:50:57,088 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:50:57,089 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,091 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:50:57,092 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:50:57,093 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:50:57,093 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:50:57,093 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,093 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,096 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:50:57,097 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:50:57,098 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,101 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:50:57,103 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 10:50:57,104 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:50:57,215 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:50:57,216 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:50:57,217 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,218 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:50:57,220 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:50:57,221 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,222 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:50:57,224 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:50:57,225 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 10:50:57,338 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,339 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,339 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,339 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,341 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,341 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,341 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,341 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,341 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,342 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,357 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 10:50:57,358 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,358 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,358 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,358 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,358 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:50:57,359 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 10:50:57,360 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,395 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 10:50:57,399 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 10:50:57,400 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,440 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 10:50:57,444 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 10:50:57,445 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,500 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 10:50:57,941 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 10:50:57,957 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:50:57,958 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:50:57,962 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:50:57,966 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 10:50:57,966 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 11:00(String)
2025-08-20 10:50:57,969 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:50:58,196 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 10:51:26,517 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, A.LGTD, A.LTTD, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ORDER BY A.WARN_TIME DESC, LEFT(A.ADCD, 12) ASC, A.WARN_NAME DESC 
2025-08-20 10:51:26,518 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==> Parameters: 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 15(Integer), 220281102203001(String), 15(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:51:26,524 DEBUG [http-nio-8080-exec-11] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:51:26,525 DEBUG [http-nio-8080-exec-11] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281102203001(String)
2025-08-20 10:51:26,526 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] <==      Total: 3
2025-08-20 10:51:26,528 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, A.STCD, C.STNM, STM, ETM, ACCP, SLM_TM, SLM, SLEP FROM EW_SNAPSHOT_MONITOR A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN ST_STBPRP_B C ON C.STCD = A.STCD WHERE WARN_ID = ? AND WARN_GRADE_ID = ? 
2025-08-20 10:51:26,528 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String), 5(Integer)
2025-08-20 10:51:26,530 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:26,532 DEBUG [http-nio-8080-exec-11] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:26,533 DEBUG [http-nio-8080-exec-11] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:51:26,534 DEBUG [http-nio-8080-exec-11] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281102203000(String)
2025-08-20 10:51:26,539 DEBUG [http-nio-8080-exec-11] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:26,541 DEBUG [http-nio-8080-exec-10] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, WARN_STATUS_ID, B.STATUS_NAME WARN_STATUS_NAME, B.ALIAS_NAME WARN_STATUS_ALIAS_NAME, B.SHORT_NAME WARN_STATUS_SHORT_NAME, REMARK, CREATE_BY, CREATE_TIME FROM EW_WARNING_FLOW A LEFT JOIN EW_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID WHERE WARN_ID = ? ORDER BY CREATE_TIME ASC 
2025-08-20 10:51:26,541 DEBUG [http-nio-8080-exec-6] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.WARN_GRADE_ID, C.GRADE_NAME WARN_GRADE_NAME, C.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_MODEL, A.WARN_TIME, A.WARN_DESC, B.WARN_STDT, B.ADCD, E.ADNM, D.WSCD, D.STCD, F.STNM, D.STM, D.ETM, D.ACCP, D.SLM_TM, D.SLM, D.SLEP, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_PROCESS A LEFT JOIN EW_WARNING_RECORD B ON B.WARN_ID = A.WARN_ID LEFT JOIN EW_WARNING_GRADE C ON C.GRADE_ID = A.WARN_GRADE_ID LEFT JOIN EW_SNAPSHOT_MONITOR D ON D.WARN_ID = A.WARN_ID AND D.WARN_GRADE_ID = A.WARN_GRADE_ID LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = B.ADCD LEFT JOIN ST_STBPRP_B F ON F.STCD = D.STCD LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_ID = ? ORDER BY A.WARN_TIME ASC 
2025-08-20 10:51:26,541 DEBUG [http-nio-8080-exec-10] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:51:26,541 DEBUG [http-nio-8080-exec-6] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:51:26,541 DEBUG [Async-Executor-17] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.ERROR_CODE, B.[ERROR_MESSAGE], C.[STATUS] READ_STATUS, C.CONFIRM_RESULT, C.CONFIRM_TIME FROM EW_RECEIVER A LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS(SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID) LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE B INNER JOIN EW_REL_WARNING_MESSAGE C ON B.MSG_ID = C.MSG_ID WHERE B.PUSH_MODE = '1' AND C.WARN_ID = ? AND B.MSG_ID = A.MSG_ID) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:26,541 DEBUG [Async-Executor-16] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.MSG_ID, A.WARN_ID, A.GRADE_ID, A.STATUS_ID, B.PUSH_MODE FROM EW_REL_WARNING_MESSAGE A LEFT JOIN EW_MESSAGE B ON A.MSG_ID = B.MSG_ID WHERE A.WARN_ID = ? ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:26,541 DEBUG [Async-Executor-18] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.EXT_SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.RECEIPT_TIME, B.CALL_RESULT, B.CALL_RESULT_TEXT, B.FAIL_CAUSE FROM EW_RECEIVER A LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.XCC_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE C INNER JOIN EW_REL_WARNING_MESSAGE D ON C.MSG_ID = D.MSG_ID WHERE C.PUSH_MODE = '2' AND D.WARN_ID = ? AND C.MSG_ID = A.MSG_ID) AND (C.[STATUS] = '0' OR (C.[STATUS] = '1' AND C.CONFIRM_RESULT NOT IN ('1', '2'))) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:26,543 DEBUG [Async-Executor-18] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:51:26,543 DEBUG [Async-Executor-16] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:51:26,543 DEBUG [Async-Executor-17] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:51:26,544 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, LWATER, STDT, DRPT FROM EW_SNAPSHOT_INDEX A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD WHERE WARN_ID = ? ORDER BY WARN_GRADE_ID ASC 
2025-08-20 10:51:26,544 DEBUG [http-nio-8080-exec-6] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:26,545 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==> Parameters: A6A2F82C-0983-47EC-8A96-ACAB8DE167AA(String)
2025-08-20 10:51:26,545 DEBUG [http-nio-8080-exec-10] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] <==      Total: 5
2025-08-20 10:51:26,547 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:26,568 DEBUG [Async-Executor-16] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] <==      Total: 5
2025-08-20 10:51:26,751 DEBUG [Async-Executor-17] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] <==      Total: 9
2025-08-20 10:51:27,102 DEBUG [Async-Executor-18] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] <==      Total: 7
2025-08-20 10:51:31,610 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 10:51:31,612 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 10:51(String), 2025-08-20 10:51(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0(String)
2025-08-20 10:51:31,613 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:31,613 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 10:51:31,613 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,613 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,615 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:31,616 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:51:31,617 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,624 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 10:51:31,626 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 10:51:31,683 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:51:31,684 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,823 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:31,824 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:51:31,826 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,827 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:31,829 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:31,829 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:31,829 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,829 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,831 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:31,833 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:31,833 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:51:31,834 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:51:31,834 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:51:31,834 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,889 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 10:51:31,909 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:31,960 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:31,961 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:51:31,961 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,963 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:31,964 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:31,964 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,965 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:31,968 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:51:31,968 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:51:31,981 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 10:51:31,991 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:31,992 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:31,994 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:31,995 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:51:31,996 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:32,117 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:32,117 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:51:32,118 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:32,119 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:32,121 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:32,121 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:32,124 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:32,125 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:51:32,126 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 10:51:32,174 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,175 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,175 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,176 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,176 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,176 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,176 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,176 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,177 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,177 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,197 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 461
2025-08-20 10:51:32,197 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,198 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,198 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,199 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,199 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:51:32,200 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 10:51:32,201 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:32,243 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 10:51:32,247 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 10:51:32,247 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:32,286 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 10:51:32,683 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:51:32,696 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:51:32,697 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:32,698 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:32,909 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 10:51:32,912 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:51:32,912 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:32,924 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:51:32,925 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:32,952 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:32,956 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 10:51:32,956 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 11:00(String)
2025-08-20 10:51:32,976 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:33,036 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:33,044 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:33,044 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:33,069 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:33,071 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:51:33,071 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:33,123 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:51:33,166 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:51:33,166 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:33,175 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:33,184 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:33,186 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:51:33,186 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:33,187 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:33,188 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:33,189 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:33,191 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:33,193 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:51:33,194 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:51:33,874 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:51:33,885 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:51:33,885 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:33,887 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:33,895 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 11:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:33,895 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:33,901 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:33,902 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:51:33,902 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:34,013 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:34,015 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:51:34,015 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:34,016 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:34,017 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-18 11:00:00') and TM <= CONVERT(datetime,'2025-08-19 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:51:34,018 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:34,020 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:34,021 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:51:34,022 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:51:34,690 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6512
2025-08-20 10:51:34,702 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:51:34,703 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:51:34,704 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:34,717 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getAscriptionTypeCountNewByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: select max(case when STADTP ='1' then ct else 0 end )total1, max(case when STADTP ='2' then ct else 0 end )total2, max(case when STADTP ='3' then ct else 0 end )total3, max(case when STADTP ='6' then ct else 0 end )total4, sum(ct) total from ( select stadtp, COUNT(1) ct from ( select st.STADTP from ST_STBPRP_B a LEFT join bsn_stadtp_b st on st.stcd=a.stcd LEFT JOIN ST_STSMTASK_B E on a.STCD=E.STCD where E.PFL = '1' and st.STADTP >= 0 and CHARINDEX(convert(varchar(10), abs(st.STADTP)),?)>0 and left(st.ADCD,?) = ? ) a group by STADTP ) a 
2025-08-20 10:51:34,718 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getAscriptionTypeCountNewByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:51:35,006 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getAscriptionTypeCountNewByAdcd [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:35,853 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? , ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? , ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:51:35,854 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 1(Integer), 2(Integer), 3(Integer), 5(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 1(Integer), 2(Integer), 3(Integer), 5(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:51:35,862 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:37,002 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:51:37,003 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 3(Integer), 5(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 2(Integer), 3(Integer), 5(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:51:37,014 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:37,800 DEBUG [http-nio-8080-exec-2] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:51:37,802 DEBUG [http-nio-8080-exec-2] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 3(Integer), 5(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 3(Integer), 5(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-20 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:51:37,809 DEBUG [http-nio-8080-exec-2] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:44,445 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:51:44,446 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 3(Integer), 5(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 3(Integer), 5(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:51:44,519 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 253
2025-08-20 10:51:45,467 DEBUG [http-nio-8080-exec-10] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:51:45,466 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, A.STCD, C.STNM, STM, ETM, ACCP, SLM_TM, SLM, SLEP FROM EW_SNAPSHOT_MONITOR A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN ST_STBPRP_B C ON C.STCD = A.STCD WHERE WARN_ID = ? AND WARN_GRADE_ID = ? 
2025-08-20 10:51:45,467 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==> Parameters: FA07D724-BC12-43B1-B54E-2577AC77899A(String), 5(Integer)
2025-08-20 10:51:45,467 DEBUG [http-nio-8080-exec-10] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281103205001(String)
2025-08-20 10:51:45,467 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, A.LGTD, A.LTTD, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ORDER BY A.WARN_TIME DESC, LEFT(A.ADCD, 12) ASC, A.WARN_NAME DESC 
2025-08-20 10:51:45,468 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==> Parameters: 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 15(Integer), 220281103205001(String), 15(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:51:45,470 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:45,473 DEBUG [http-nio-8080-exec-10] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:45,474 DEBUG [http-nio-8080-exec-10] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:51:45,474 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:45,475 DEBUG [http-nio-8080-exec-10] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281103205000(String)
2025-08-20 10:51:45,480 DEBUG [http-nio-8080-exec-10] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:45,495 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, WARN_STATUS_ID, B.STATUS_NAME WARN_STATUS_NAME, B.ALIAS_NAME WARN_STATUS_ALIAS_NAME, B.SHORT_NAME WARN_STATUS_SHORT_NAME, REMARK, CREATE_BY, CREATE_TIME FROM EW_WARNING_FLOW A LEFT JOIN EW_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID WHERE WARN_ID = ? ORDER BY CREATE_TIME ASC 
2025-08-20 10:51:45,495 DEBUG [http-nio-8080-exec-11] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.WARN_GRADE_ID, C.GRADE_NAME WARN_GRADE_NAME, C.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_MODEL, A.WARN_TIME, A.WARN_DESC, B.WARN_STDT, B.ADCD, E.ADNM, D.WSCD, D.STCD, F.STNM, D.STM, D.ETM, D.ACCP, D.SLM_TM, D.SLM, D.SLEP, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_PROCESS A LEFT JOIN EW_WARNING_RECORD B ON B.WARN_ID = A.WARN_ID LEFT JOIN EW_WARNING_GRADE C ON C.GRADE_ID = A.WARN_GRADE_ID LEFT JOIN EW_SNAPSHOT_MONITOR D ON D.WARN_ID = A.WARN_ID AND D.WARN_GRADE_ID = A.WARN_GRADE_ID LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = B.ADCD LEFT JOIN ST_STBPRP_B F ON F.STCD = D.STCD LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_ID = ? ORDER BY A.WARN_TIME ASC 
2025-08-20 10:51:45,496 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, LWATER, STDT, DRPT FROM EW_SNAPSHOT_INDEX A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD WHERE WARN_ID = ? ORDER BY WARN_GRADE_ID ASC 
2025-08-20 10:51:45,496 DEBUG [http-nio-8080-exec-11] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==> Parameters: FA07D724-BC12-43B1-B54E-2577AC77899A(String)
2025-08-20 10:51:45,496 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==> Parameters: FA07D724-BC12-43B1-B54E-2577AC77899A(String)
2025-08-20 10:51:45,496 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==> Parameters: FA07D724-BC12-43B1-B54E-2577AC77899A(String)
2025-08-20 10:51:45,498 DEBUG [Async-Executor-19] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.MSG_ID, A.WARN_ID, A.GRADE_ID, A.STATUS_ID, B.PUSH_MODE FROM EW_REL_WARNING_MESSAGE A LEFT JOIN EW_MESSAGE B ON A.MSG_ID = B.MSG_ID WHERE A.WARN_ID = ? ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:45,498 DEBUG [Async-Executor-21] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.EXT_SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.RECEIPT_TIME, B.CALL_RESULT, B.CALL_RESULT_TEXT, B.FAIL_CAUSE FROM EW_RECEIVER A LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.XCC_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE C INNER JOIN EW_REL_WARNING_MESSAGE D ON C.MSG_ID = D.MSG_ID WHERE C.PUSH_MODE = '2' AND D.WARN_ID = ? AND C.MSG_ID = A.MSG_ID) AND (C.[STATUS] = '0' OR (C.[STATUS] = '1' AND C.CONFIRM_RESULT NOT IN ('1', '2'))) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:45,498 DEBUG [Async-Executor-20] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.ERROR_CODE, B.[ERROR_MESSAGE], C.[STATUS] READ_STATUS, C.CONFIRM_RESULT, C.CONFIRM_TIME FROM EW_RECEIVER A LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS(SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID) LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE B INNER JOIN EW_REL_WARNING_MESSAGE C ON B.MSG_ID = C.MSG_ID WHERE B.PUSH_MODE = '1' AND C.WARN_ID = ? AND B.MSG_ID = A.MSG_ID) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:45,499 DEBUG [Async-Executor-19] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==> Parameters: FA07D724-BC12-43B1-B54E-2577AC77899A(String)
2025-08-20 10:51:45,499 DEBUG [Async-Executor-20] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: FA07D724-BC12-43B1-B54E-2577AC77899A(String)
2025-08-20 10:51:45,499 DEBUG [Async-Executor-21] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: FA07D724-BC12-43B1-B54E-2577AC77899A(String)
2025-08-20 10:51:45,499 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] <==      Total: 5
2025-08-20 10:51:45,499 DEBUG [http-nio-8080-exec-11] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:45,501 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:45,526 DEBUG [Async-Executor-19] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] <==      Total: 6
2025-08-20 10:51:45,831 DEBUG [Async-Executor-20] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] <==      Total: 15
2025-08-20 10:51:46,068 DEBUG [Async-Executor-21] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] <==      Total: 13
2025-08-20 10:51:52,808 DEBUG [http-nio-8080-exec-7] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, A.LGTD, A.LTTD, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ORDER BY A.WARN_TIME DESC, LEFT(A.ADCD, 12) ASC, A.WARN_NAME DESC 
2025-08-20 10:51:52,809 DEBUG [http-nio-8080-exec-7] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==> Parameters: 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 15(Integer), 220281103205002(String), 15(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:51:52,810 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, A.STCD, C.STNM, STM, ETM, ACCP, SLM_TM, SLM, SLEP FROM EW_SNAPSHOT_MONITOR A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN ST_STBPRP_B C ON C.STCD = A.STCD WHERE WARN_ID = ? AND WARN_GRADE_ID = ? 
2025-08-20 10:51:52,810 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:51:52,810 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==> Parameters: 6EB55445-E4FD-432A-B429-A89453193090(String), 5(Integer)
2025-08-20 10:51:52,810 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281103205002(String)
2025-08-20 10:51:52,816 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:52,816 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:52,817 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:51:52,818 DEBUG [http-nio-8080-exec-7] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:52,818 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281103205000(String)
2025-08-20 10:51:52,823 DEBUG [http-nio-8080-exec-1] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:52,845 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, WARN_STATUS_ID, B.STATUS_NAME WARN_STATUS_NAME, B.ALIAS_NAME WARN_STATUS_ALIAS_NAME, B.SHORT_NAME WARN_STATUS_SHORT_NAME, REMARK, CREATE_BY, CREATE_TIME FROM EW_WARNING_FLOW A LEFT JOIN EW_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID WHERE WARN_ID = ? ORDER BY CREATE_TIME ASC 
2025-08-20 10:51:52,845 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, LWATER, STDT, DRPT FROM EW_SNAPSHOT_INDEX A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD WHERE WARN_ID = ? ORDER BY WARN_GRADE_ID ASC 
2025-08-20 10:51:52,845 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.WARN_GRADE_ID, C.GRADE_NAME WARN_GRADE_NAME, C.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_MODEL, A.WARN_TIME, A.WARN_DESC, B.WARN_STDT, B.ADCD, E.ADNM, D.WSCD, D.STCD, F.STNM, D.STM, D.ETM, D.ACCP, D.SLM_TM, D.SLM, D.SLEP, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_PROCESS A LEFT JOIN EW_WARNING_RECORD B ON B.WARN_ID = A.WARN_ID LEFT JOIN EW_WARNING_GRADE C ON C.GRADE_ID = A.WARN_GRADE_ID LEFT JOIN EW_SNAPSHOT_MONITOR D ON D.WARN_ID = A.WARN_ID AND D.WARN_GRADE_ID = A.WARN_GRADE_ID LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = B.ADCD LEFT JOIN ST_STBPRP_B F ON F.STCD = D.STCD LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_ID = ? ORDER BY A.WARN_TIME ASC 
2025-08-20 10:51:52,845 DEBUG [Async-Executor-22] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.MSG_ID, A.WARN_ID, A.GRADE_ID, A.STATUS_ID, B.PUSH_MODE FROM EW_REL_WARNING_MESSAGE A LEFT JOIN EW_MESSAGE B ON A.MSG_ID = B.MSG_ID WHERE A.WARN_ID = ? ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:52,845 DEBUG [Async-Executor-24] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.EXT_SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.RECEIPT_TIME, B.CALL_RESULT, B.CALL_RESULT_TEXT, B.FAIL_CAUSE FROM EW_RECEIVER A LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.XCC_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE C INNER JOIN EW_REL_WARNING_MESSAGE D ON C.MSG_ID = D.MSG_ID WHERE C.PUSH_MODE = '2' AND D.WARN_ID = ? AND C.MSG_ID = A.MSG_ID) AND (C.[STATUS] = '0' OR (C.[STATUS] = '1' AND C.CONFIRM_RESULT NOT IN ('1', '2'))) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:52,845 DEBUG [Async-Executor-23] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.ERROR_CODE, B.[ERROR_MESSAGE], C.[STATUS] READ_STATUS, C.CONFIRM_RESULT, C.CONFIRM_TIME FROM EW_RECEIVER A LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS(SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID) LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE B INNER JOIN EW_REL_WARNING_MESSAGE C ON B.MSG_ID = C.MSG_ID WHERE B.PUSH_MODE = '1' AND C.WARN_ID = ? AND B.MSG_ID = A.MSG_ID) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:52,845 DEBUG [Async-Executor-22] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==> Parameters: 6EB55445-E4FD-432A-B429-A89453193090(String)
2025-08-20 10:51:52,845 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==> Parameters: 6EB55445-E4FD-432A-B429-A89453193090(String)
2025-08-20 10:51:52,845 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==> Parameters: 6EB55445-E4FD-432A-B429-A89453193090(String)
2025-08-20 10:51:52,845 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==> Parameters: 6EB55445-E4FD-432A-B429-A89453193090(String)
2025-08-20 10:51:52,845 DEBUG [Async-Executor-24] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: 6EB55445-E4FD-432A-B429-A89453193090(String)
2025-08-20 10:51:52,845 DEBUG [Async-Executor-23] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: 6EB55445-E4FD-432A-B429-A89453193090(String)
2025-08-20 10:51:52,847 DEBUG [http-nio-8080-exec-5] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:52,848 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:52,849 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] <==      Total: 5
2025-08-20 10:51:52,872 DEBUG [Async-Executor-22] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] <==      Total: 6
2025-08-20 10:51:53,252 DEBUG [Async-Executor-23] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] <==      Total: 15
2025-08-20 10:51:53,559 DEBUG [Async-Executor-24] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] <==      Total: 13
2025-08-20 10:51:58,777 DEBUG [http-nio-8080-exec-11] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, A.LGTD, A.LTTD, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ORDER BY A.WARN_TIME DESC, LEFT(A.ADCD, 12) ASC, A.WARN_NAME DESC 
2025-08-20 10:51:58,778 DEBUG [http-nio-8080-exec-5] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:51:58,778 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, A.STCD, C.STNM, STM, ETM, ACCP, SLM_TM, SLM, SLEP FROM EW_SNAPSHOT_MONITOR A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN ST_STBPRP_B C ON C.STCD = A.STCD WHERE WARN_ID = ? AND WARN_GRADE_ID = ? 
2025-08-20 10:51:58,778 DEBUG [http-nio-8080-exec-11] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] ==> Parameters: 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 15(Integer), 220281103205003(String), 15(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:51:58,778 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==> Parameters: 60E57E23-9730-482C-9F22-A0A34DB071E2(String), 5(Integer)
2025-08-20 10:51:58,778 DEBUG [http-nio-8080-exec-5] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281103205003(String)
2025-08-20 10:51:58,780 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotMonitor [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:58,783 DEBUG [http-nio-8080-exec-5] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:51:58,784 DEBUG [http-nio-8080-exec-11] c.h.c.a.e.m.E.getPageList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:51:58,784 DEBUG [http-nio-8080-exec-5] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:51:58,784 DEBUG [http-nio-8080-exec-5] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220281103205000(String)
2025-08-20 10:51:58,795 DEBUG [http-nio-8080-exec-5] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:58,803 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.WARN_GRADE_ID, C.GRADE_NAME WARN_GRADE_NAME, C.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_MODEL, A.WARN_TIME, A.WARN_DESC, B.WARN_STDT, B.ADCD, E.ADNM, D.WSCD, D.STCD, F.STNM, D.STM, D.ETM, D.ACCP, D.SLM_TM, D.SLM, D.SLEP, A.REMARK, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_PROCESS A LEFT JOIN EW_WARNING_RECORD B ON B.WARN_ID = A.WARN_ID LEFT JOIN EW_WARNING_GRADE C ON C.GRADE_ID = A.WARN_GRADE_ID LEFT JOIN EW_SNAPSHOT_MONITOR D ON D.WARN_ID = A.WARN_ID AND D.WARN_GRADE_ID = A.WARN_GRADE_ID LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = B.ADCD LEFT JOIN ST_STBPRP_B F ON F.STCD = D.STCD LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_ID = ? ORDER BY A.WARN_TIME ASC 
2025-08-20 10:51:58,805 DEBUG [Async-Executor-25] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.MSG_ID, A.WARN_ID, A.GRADE_ID, A.STATUS_ID, B.PUSH_MODE FROM EW_REL_WARNING_MESSAGE A LEFT JOIN EW_MESSAGE B ON A.MSG_ID = B.MSG_ID WHERE A.WARN_ID = ? ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:58,805 DEBUG [http-nio-8080-exec-6] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, WARN_STATUS_ID, B.STATUS_NAME WARN_STATUS_NAME, B.ALIAS_NAME WARN_STATUS_ALIAS_NAME, B.SHORT_NAME WARN_STATUS_SHORT_NAME, REMARK, CREATE_BY, CREATE_TIME FROM EW_WARNING_FLOW A LEFT JOIN EW_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID WHERE WARN_ID = ? ORDER BY CREATE_TIME ASC 
2025-08-20 10:51:58,805 DEBUG [Async-Executor-27] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.EXT_SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.RECEIPT_TIME, B.CALL_RESULT, B.CALL_RESULT_TEXT, B.FAIL_CAUSE FROM EW_RECEIVER A LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.XCC_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE C INNER JOIN EW_REL_WARNING_MESSAGE D ON C.MSG_ID = D.MSG_ID WHERE C.PUSH_MODE = '2' AND D.WARN_ID = ? AND C.MSG_ID = A.MSG_ID) AND (C.[STATUS] = '0' OR (C.[STATUS] = '1' AND C.CONFIRM_RESULT NOT IN ('1', '2'))) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:58,804 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_GRADE_ID, A.ADCD, B.ADNM, WSCD, LWATER, STDT, DRPT FROM EW_SNAPSHOT_INDEX A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD WHERE WARN_ID = ? ORDER BY WARN_GRADE_ID ASC 
2025-08-20 10:51:58,805 DEBUG [Async-Executor-26] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.ERROR_CODE, B.[ERROR_MESSAGE], C.[STATUS] READ_STATUS, C.CONFIRM_RESULT, C.CONFIRM_TIME FROM EW_RECEIVER A LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS(SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID) LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS(SELECT 8 FROM EW_MESSAGE B INNER JOIN EW_REL_WARNING_MESSAGE C ON B.MSG_ID = C.MSG_ID WHERE B.PUSH_MODE = '1' AND C.WARN_ID = ? AND B.MSG_ID = A.MSG_ID) ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:51:58,805 DEBUG [Async-Executor-25] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] ==> Parameters: 60E57E23-9730-482C-9F22-A0A34DB071E2(String)
2025-08-20 10:51:58,805 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] ==> Parameters: 60E57E23-9730-482C-9F22-A0A34DB071E2(String)
2025-08-20 10:51:58,805 DEBUG [http-nio-8080-exec-6] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] ==> Parameters: 60E57E23-9730-482C-9F22-A0A34DB071E2(String)
2025-08-20 10:51:58,805 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] ==> Parameters: 60E57E23-9730-482C-9F22-A0A34DB071E2(String)
2025-08-20 10:51:58,806 DEBUG [Async-Executor-27] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: 60E57E23-9730-482C-9F22-A0A34DB071E2(String)
2025-08-20 10:51:58,806 DEBUG [Async-Executor-26] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: 60E57E23-9730-482C-9F22-A0A34DB071E2(String)
2025-08-20 10:51:58,810 DEBUG [http-nio-8080-exec-3] c.h.c.a.e.m.E.getProcessList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:58,810 DEBUG [http-nio-8080-exec-2] c.h.c.a.e.m.E.getSnapshotIndexList [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:51:58,814 DEBUG [http-nio-8080-exec-6] c.h.c.a.e.m.E.getFlowList [BaseJdbcLogger.java : 143] <==      Total: 5
2025-08-20 10:51:58,833 DEBUG [Async-Executor-25] c.h.c.a.e.m.E.getMessageList [BaseJdbcLogger.java : 143] <==      Total: 6
2025-08-20 10:51:59,235 DEBUG [Async-Executor-26] c.h.c.a.e.m.E.getSmsReceiverList [BaseJdbcLogger.java : 143] <==      Total: 15
2025-08-20 10:51:59,473 DEBUG [Async-Executor-27] c.h.c.a.e.m.E.getXccReceiverList [BaseJdbcLogger.java : 143] <==      Total: 13
2025-08-20 10:52:02,916 DEBUG [http-nio-8080-exec-6] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:52:02,917 DEBUG [http-nio-8080-exec-6] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 3(Integer), 5(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 2(Integer), 3(Integer), 5(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:52:03,017 DEBUG [http-nio-8080-exec-6] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 334
2025-08-20 10:52:03,420 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT * FROM ( SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER() OVER(PARTITION BY A.ADCD ORDER BY WARN_TIME DESC, A.ADCD ASC) SORTNO, A.WARN_ID, A.ADCD, B.ADNM, WARN_TYPE_ID, C.[TYPE_NAME] WARN_TYPE_NAME, A.WARN_GRADE_ID, D.GRADE_NAME WARN_GRADE_NAME, D.ALIAS_NAME WARN_GRADE_ALIAS_NAME, A.WARN_STATUS_ID, E.STATUS_NAME WARN_STATUS_NAME, E.ALIAS_NAME WARN_STATUS_ALIAS_NAME, E.SHORT_NAME WARN_STATUS_SHORT_NAME, A.WARN_MODEL, WARN_TIME, F.CREATE_TIME WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, B.LGTD, B.LTTD, A.REMARK, COUNT(A.ADCD) OVER (PARTITION BY A.ADCD) CHILDREN_COUNT, A.PLATFORM_ID, G.ISSUING_UNIT PLATFORM_NAME FROM EW_WARNING_RECORD A LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD LEFT JOIN EW_WARNING_TYPE C ON C.[TYPE_ID] = WARN_TYPE_ID LEFT JOIN EW_WARNING_GRADE D ON D.GRADE_ID = WARN_GRADE_ID LEFT JOIN EW_WARNING_STATUS E ON E.STATUS_ID = WARN_STATUS_ID LEFT JOIN EW_WARNING_FLOW F ON F.WARN_ID = A.WARN_ID AND F.WARN_GRADE_ID = A.WARN_GRADE_ID AND F.WARN_STATUS_ID = 30 LEFT JOIN EW_WARNING_CONF G ON G.PLATFORM_ID = A.PLATFORM_ID WHERE A.WARN_TYPE_ID = 10 AND A.WARN_GRADE_ID IN ( ? , ? , ? , ? ) AND A.WARN_STATUS_ID IN ( ? , ? , ? , ? , ? , ? ) AND LEFT(A.ADCD, ?) = LEFT(?, ?) AND WARN_TIME >= ? AND WARN_TIME <= ? ) T WHERE T.SORTNO = 1 UNION ALL SELECT WARN_ID, ADCD, ADNM, WARN_TYPE_ID, WARN_TYPE_NAME, WARN_GRADE_ID, WARN_GRADE_NAME, WARN_GRADE_ALIAS_NAME, WARN_STATUS_ID, WARN_STATUS_NAME, WARN_STATUS_ALIAS_NAME, WARN_STATUS_SHORT_NAME, WARN_MODEL, WARN_TIME, WARN_CLOSE_TIME, WARN_MODE, WARN_NAME, WARN_DESC, WARN_STDT, LGTD, LTTD, REMARK, CHILDREN_COUNT, PLATFORM_ID, PLATFORM_NAME FROM ( SELECT ROW_NUMBER () OVER (PARTITION BY r.ADCD ORDER BY r.WARN_TIME DESC, r.ADCD ASC) SORTNO, r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, COUNT(r.ADCD) OVER (PARTITION BY r.ADCD) CHILDREN_COUNT, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND r.WARN_GRADE_ID IN ( ? , ? , ? , ? ) AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ) T1 WHERE T1.SORTNO = 1 ) t1t2t3 ORDER BY WARN_TIME DESC, WARN_GRADE_ID ASC, LEFT(ADCD, 12) ASC 
2025-08-20 10:52:03,420 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] ==> Parameters: 1(Integer), 2(Integer), 3(Integer), 5(Integer), 0(Integer), 1(Integer), 10(Integer), 20(Integer), 21(Integer), 30(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp), 1(Integer), 2(Integer), 3(Integer), 5(Integer), 2(Integer), 220000000000000(String), 2(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:52:03,488 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getWarnLatestPageList [BaseJdbcLogger.java : 143] <==      Total: 334
2025-08-20 10:52:07,755 DEBUG [http-nio-8080-exec-4] c.h.c.a.f.m.F.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.WARN_ID, A.ADCD, C.ADNM, DATEADD(HOUR, - B.STDT, A.WARN_TIME) AS STM, A.WARN_TIME AS ETM, B.STDT, B.STCD, D.STNM FROM EW_FUSION_WARNING_RECORD A LEFT JOIN EW_FUSION_SNAPSHOT_S B ON A.WARN_ID = B.WARN_ID LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = A.ADCD LEFT JOIN ST_STBPRP_B D ON D.STCD = B.STCD WHERE A.WARN_ID = ? 
2025-08-20 10:52:07,755 DEBUG [http-nio-8080-exec-4] c.h.c.a.f.m.F.getSnapshotMonitor [BaseJdbcLogger.java : 143] ==> Parameters: D6730691-2DD6-4A3F-9179-FC90EFD9BBD1(String)
2025-08-20 10:52:07,756 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:52:07,756 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220521102201101(String)
2025-08-20 10:52:07,757 DEBUG [http-nio-8080-exec-4] c.h.c.a.f.m.F.getSnapshotMonitor [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:52:07,760 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getPageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT r.WARN_ID, r.ADCD, a.ADNM, 10 AS WARN_TYPE_ID, '山洪' AS WARN_TYPE_NAME, r.WARN_GRADE_ID, g.GRADE_NAME AS WARN_GRADE_NAME, g.SHORT_NAME AS WARN_GRADE_ALIAS_NAME, r.WARN_STATUS_ID, s.STATUS_NAME AS WARN_STATUS_NAME, s.SHORT_NAME AS WARN_STATUS_ALIAS_NAME, s.SHORT_NAME AS WARN_STATUS_SHORT_NAME, r.WARN_MODEL, r.WARN_TIME, f.CREATE_TIME AS WARN_CLOSE_TIME, r.WARN_MODE, r.WARN_NAME, r.WARN_DESC, fss.STDT AS WARN_STDT, a.LGTD, a.LTTD, r.REMARK, r.PLATFORM_ID, fwc.ISSUING_UNIT PLATFORM_NAME FROM EW_FUSION_WARNING_RECORD r LEFT JOIN EW_FUSION_WARNING_GRADE g ON r.WARN_GRADE_ID = g.GRADE_ID LEFT JOIN EW_FUSION_WARNING_STATUS s ON s.STATUS_ID = r.WARN_STATUS_ID LEFT JOIN EW_FUSION_WARNING_FLOW f ON f.WARN_ID = r.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S fss ON r.WARN_ID = fss.WARN_ID LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_CONF fwc ON fwc.PLATFORM_ID = r.PLATFORM_ID WHERE 1 = 1 AND LEFT(r.ADCD, ?) = LEFT(?, ?) AND r.WARN_TIME >= ? AND r.WARN_TIME <= ? ORDER BY WARN_TIME DESC, LEFT(r.ADCD, 12) ASC, WARN_NAME DESC 
2025-08-20 10:52:07,761 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getPageList [BaseJdbcLogger.java : 143] ==> Parameters: 15(Integer), 220521102201101(String), 15(Integer), 2025-08-01 08:00:00.0(Timestamp), 2025-08-20 11:00:00.0(Timestamp)
2025-08-20 10:52:07,762 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:52:07,763 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT PlanID, TITLE, PlanURL, PUBTM, style, adcd, upuser, code, TYPE FROM BSN_PLAN_B WHERE STYLE = '6' AND CODE = ? 
2025-08-20 10:52:07,763 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] ==> Parameters: 220521102201000(String)
2025-08-20 10:52:07,767 DEBUG [http-nio-8080-exec-11] c.h.c.a.f.m.F.getPageList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:52:07,767 DEBUG [http-nio-8080-exec-2] c.h.c.a.x.p.m.P.getPlanListByAdcd [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:52:07,784 DEBUG [http-nio-8080-exec-10] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_STATUS_ID, B.STATUS_NAME WARN_STATUS_NAME, B.SHORT_NAME WARN_STATUS_SHORT_NAME, REMARK, CREATE_BY, CREATE_TIME FROM EW_FUSION_WARNING_FLOW A LEFT JOIN EW_FUSION_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID WHERE WARN_ID = ? ORDER BY CREATE_TIME, WARN_STATUS_ID ASC 
2025-08-20 10:52:07,784 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT r.WARN_ID AS warnId, r.WARN_NAME AS warnName, a.ADNM AS warnAdnm, r.WARN_MODEL AS warnModel, r.WARN_TIME AS warnTime, f.CREATE_TIME AS closeTime, r.WARN_MODE AS warnMode, r.WARN_DESC AS warnDesc, a.ADNM AS adnm, s.REPRD AS reprd, s.RDRPT AS rdrpt, s.STDT as stdt, s.FDRPT AS fdrpt, s.FCDT AS fcdt, st.STNM AS stnm, s.STCD AS stcd, s.RACCP AS raccp, s.FACCP AS faccp FROM EW_FUSION_WARNING_RECORD r LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_FLOW f ON r.WARN_ID = f.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S s ON r.WARN_ID = s.WARN_ID LEFT JOIN ST_STBPRP_B st ON s.STCD = st.STCD WHERE r.WARN_ID = ? 
2025-08-20 10:52:07,785 DEBUG [Async-Executor-28] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.MSG_ID, A.WARN_ID, B.PUSH_MODE FROM EW_FUSION_REL_MESSAGE A LEFT JOIN EW_FUSION_MESSAGE B ON A.MSG_ID = B.MSG_ID WHERE A.WARN_ID = ? ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:52:07,785 DEBUG [Async-Executor-29] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.ERROR_CODE, B.[ERROR_MESSAGE], C.[STATUS] READ_STATUS, C.CONFIRM_RESULT, C.CONFIRM_TIME FROM EW_FUSION_RECEIVER A LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS (SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID) LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '9' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS (SELECT 8 FROM EW_FUSION_MESSAGE B INNER JOIN EW_FUSION_REL_MESSAGE C ON B.MSG_ID = C.MSG_ID WHERE B.PUSH_MODE = '1' AND C.WARN_ID = ? AND B.MSG_ID = A.MSG_ID) ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC 
2025-08-20 10:52:07,785 DEBUG [Async-Executor-30] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.EXT_SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.RECEIPT_TIME, B.CALL_RESULT, B.CALL_RESULT_TEXT, B.FAIL_CAUSE FROM EW_FUSION_RECEIVER A LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID WHERE EXISTS ( SELECT 8 FROM EW_FUSION_MESSAGE C INNER JOIN EW_FUSION_REL_MESSAGE D ON C.MSG_ID = D.MSG_ID WHERE C.PUSH_MODE = '2' AND D.WARN_ID = ? AND C.MSG_ID = A.MSG_ID) ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC 
2025-08-20 10:52:07,785 DEBUG [http-nio-8080-exec-10] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] ==> Parameters: D6730691-2DD6-4A3F-9179-FC90EFD9BBD1(String)
2025-08-20 10:52:07,786 DEBUG [Async-Executor-29] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: D6730691-2DD6-4A3F-9179-FC90EFD9BBD1(String)
2025-08-20 10:52:07,786 DEBUG [Async-Executor-30] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: D6730691-2DD6-4A3F-9179-FC90EFD9BBD1(String)
2025-08-20 10:52:07,786 DEBUG [Async-Executor-28] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] ==> Parameters: D6730691-2DD6-4A3F-9179-FC90EFD9BBD1(String)
2025-08-20 10:52:07,786 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==> Parameters: D6730691-2DD6-4A3F-9179-FC90EFD9BBD1(String)
2025-08-20 10:52:07,787 DEBUG [http-nio-8080-exec-10] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:52:07,787 DEBUG [Async-Executor-28] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:52:07,789 DEBUG [http-nio-8080-exec-1] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:52:07,795 DEBUG [Async-Executor-30] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] <==      Total: 9
2025-08-20 10:52:08,714 DEBUG [Async-Executor-29] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] <==      Total: 9
2025-08-20 10:52:24,366 DEBUG [http-nio-8080-exec-3] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT WARN_ID, WARN_STATUS_ID, B.STATUS_NAME WARN_STATUS_NAME, B.SHORT_NAME WARN_STATUS_SHORT_NAME, REMARK, CREATE_BY, CREATE_TIME FROM EW_FUSION_WARNING_FLOW A LEFT JOIN EW_FUSION_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID WHERE WARN_ID = ? ORDER BY CREATE_TIME, WARN_STATUS_ID ASC 
2025-08-20 10:52:24,366 DEBUG [http-nio-8080-exec-3] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] ==> Parameters: 9DE69A61-21B3-42B3-BDD6-60114110D73C(String)
2025-08-20 10:52:24,367 DEBUG [Async-Executor-31] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.MSG_ID, A.WARN_ID, B.PUSH_MODE FROM EW_FUSION_REL_MESSAGE A LEFT JOIN EW_FUSION_MESSAGE B ON A.MSG_ID = B.MSG_ID WHERE A.WARN_ID = ? ORDER BY A.LATEST_TIME ASC 
2025-08-20 10:52:24,367 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT r.WARN_ID AS warnId, r.WARN_NAME AS warnName, a.ADNM AS warnAdnm, r.WARN_MODEL AS warnModel, r.WARN_TIME AS warnTime, f.CREATE_TIME AS closeTime, r.WARN_MODE AS warnMode, r.WARN_DESC AS warnDesc, a.ADNM AS adnm, s.REPRD AS reprd, s.RDRPT AS rdrpt, s.STDT as stdt, s.FDRPT AS fdrpt, s.FCDT AS fcdt, st.STNM AS stnm, s.STCD AS stcd, s.RACCP AS raccp, s.FACCP AS faccp FROM EW_FUSION_WARNING_RECORD r LEFT JOIN MDT_ADCDINFO_B a ON r.ADCD = a.ADCD LEFT JOIN EW_FUSION_WARNING_FLOW f ON r.WARN_ID = f.WARN_ID AND f.WARN_STATUS_ID = 30 LEFT JOIN EW_FUSION_SNAPSHOT_S s ON r.WARN_ID = s.WARN_ID LEFT JOIN ST_STBPRP_B st ON s.STCD = st.STCD WHERE r.WARN_ID = ? 
2025-08-20 10:52:24,367 DEBUG [Async-Executor-31] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] ==> Parameters: 9DE69A61-21B3-42B3-BDD6-60114110D73C(String)
2025-08-20 10:52:24,367 DEBUG [Async-Executor-32] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.ERROR_CODE, B.[ERROR_MESSAGE], C.[STATUS] READ_STATUS, C.CONFIRM_RESULT, C.CONFIRM_TIME FROM EW_FUSION_RECEIVER A LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND EXISTS (SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID) LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '9' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO WHERE EXISTS (SELECT 8 FROM EW_FUSION_MESSAGE B INNER JOIN EW_FUSION_REL_MESSAGE C ON B.MSG_ID = C.MSG_ID WHERE B.PUSH_MODE = '1' AND C.WARN_ID = ? AND B.MSG_ID = A.MSG_ID) ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC 
2025-08-20 10:52:24,367 DEBUG [Async-Executor-33] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT A.RECEIVER_ID, A.MSG_ID, A.PHONE_NO, A.RECEIVER, A.POSITION, A.TAG, A.REMARK, B.SEND_ID, B.EXT_SEND_ID, B.[STATUS] SEND_STATUS, B.SEND_TIME, B.RECEIPT_TIME, B.CALL_RESULT, B.CALL_RESULT_TEXT, B.FAIL_CAUSE FROM EW_FUSION_RECEIVER A LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID WHERE EXISTS ( SELECT 8 FROM EW_FUSION_MESSAGE C INNER JOIN EW_FUSION_REL_MESSAGE D ON C.MSG_ID = D.MSG_ID WHERE C.PUSH_MODE = '2' AND D.WARN_ID = ? AND C.MSG_ID = A.MSG_ID) ORDER BY CHARINDEX(A.TAG, 'AB1234567'), B.SEND_TIME, A.RECEIVER ASC 
2025-08-20 10:52:24,367 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] ==> Parameters: 9DE69A61-21B3-42B3-BDD6-60114110D73C(String)
2025-08-20 10:52:24,368 DEBUG [Async-Executor-33] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: 9DE69A61-21B3-42B3-BDD6-60114110D73C(String)
2025-08-20 10:52:24,368 DEBUG [Async-Executor-32] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] ==> Parameters: 9DE69A61-21B3-42B3-BDD6-60114110D73C(String)
2025-08-20 10:52:24,368 DEBUG [http-nio-8080-exec-3] c.h.c.a.f.m.F.getFlowList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:52:24,370 DEBUG [Async-Executor-31] c.h.c.a.f.m.F.getMessageList [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:52:24,370 DEBUG [http-nio-8080-exec-7] c.h.c.a.f.m.F.getSnapshotIndex [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:52:24,382 DEBUG [Async-Executor-33] c.h.c.a.f.m.F.getXccReceiverList [BaseJdbcLogger.java : 143] <==      Total: 9
2025-08-20 10:52:25,281 DEBUG [Async-Executor-32] c.h.c.a.f.m.F.getSmsReceiverList [BaseJdbcLogger.java : 143] <==      Total: 9
2025-08-20 10:54:00,068 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getRainWarn [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT a.STCD, a.STDT, a.ALARMGRADEID, a.ALARMSTM, a.ALARMETM, a.ALARMDESC, a.DRP, a.ALERMDRP, a.REAMRK, b.STNM, b.BSNM, b.STLC, b.ADDVCD, b.STTP, b.LGTD, b.LTTD, b.stadtp, b.ADNM, b.STADTPNM, b.XADNM,PLGTD,PLTTD FROM BSN_RAIN_ALARM a LEFT JOIN BSN_STBPRP_V b ON a.STCD = b.STCD WHERE 1=1 and left(b.ADDVCD,?)=? and CHARINDEX(ALARMGRADEID,?) >0 and b.stadtp in ( ? , ? , ? ) order by a.STCD desc 
2025-08-20 10:54:00,070 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getRainWarn [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String), 1,2(String), 3(String), 2(String), 1(String)
2025-08-20 10:54:00,080 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getRainWarn [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:55:26,308 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 10:56:31,908 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 10:56:31,909 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 10:56(String), 2025-08-20 10:56(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 10:56:31,909 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 10:56:31,909 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:31,911 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 10:56:31,912 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 10:56:32,001 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 10:56:32,002 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:56:32,002 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,003 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:56:32,004 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:56:32,004 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,137 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 10:56:32,142 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:56:32,143 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:56:32,143 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,144 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:56:32,144 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:56:32,145 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,145 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 11:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:56:32,145 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,147 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:56:32,147 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:56:32,147 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 10:56:32,147 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 10:56:32,148 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 10:56:32,148 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,271 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 10:56:32,272 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 10:56:32,272 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,273 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:56:32,274 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 10:56:32,274 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,276 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:56:32,277 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 10:56:32,277 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 10:56:32,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,398 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,398 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,398 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,398 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,398 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,398 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,408 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 10:56:32,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 10:56:32,410 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 10:56:32,410 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,432 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 10:56:32,433 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 10:56:32,434 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,477 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 10:56:32,479 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 10:56:32,480 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:32,532 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 10:56:33,052 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 10:56:33,065 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 10:56:33,067 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 10:56:33,099 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 10:56:33,102 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 10:56:33,102 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 11:00(String)
2025-08-20 10:56:33,163 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 10:56:33,195 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:00:26,317 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:01:31,911 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:01:31,911 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:01(String), 2025-08-20 11:01(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:01:31,911 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:01:31,912 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:31,912 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:01:31,913 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:01:32,004 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:01:32,005 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:01:32,006 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,035 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:01:32,036 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:01:32,036 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,066 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:01:32,074 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:01:32,075 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,082 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:01:32,083 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:01:32,083 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,238 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:01:32,239 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:01:32,239 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,240 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:01:32,240 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:01:32,240 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,242 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:01:32,242 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:01:32,243 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:01:32,269 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:01:32,270 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:01:32,270 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,271 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:01:32,271 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:01:32,272 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,273 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:01:32,274 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:01:32,274 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:01:32,398 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,399 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,399 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,399 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,399 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,399 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,399 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,399 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,399 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,399 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,407 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:01:32,408 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,408 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,408 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,408 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,408 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:01:32,409 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:01:32,409 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,430 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:01:32,432 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:01:32,432 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,477 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:01:32,480 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:01:32,480 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:32,533 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:01:32,998 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:01:33,007 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:01:33,008 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:01:33,009 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:01:33,011 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:01:33,012 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:01:33,013 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:01:33,187 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:05:26,326 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:06:31,910 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:06:31,910 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:06:31,915 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:06:31,915 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:06:31,915 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:06(String), 2025-08-20 11:06(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:06:31,915 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,006 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:06:32,008 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:06:32,008 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,010 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:06:32,011 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:06:32,012 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,093 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:06:32,099 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:06:32,100 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,112 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:06:32,113 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:06:32,113 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,141 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:06:32,143 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:06:32,143 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,144 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:06:32,144 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:06:32,145 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,145 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:06:32,146 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:06:32,146 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:06:32,257 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:06:32,258 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:06:32,258 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,260 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:06:32,260 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:06:32,260 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,261 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:06:32,262 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:06:32,262 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:06:32,394 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,394 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,396 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,396 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,396 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,396 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,396 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,396 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,396 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,396 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,404 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:06:32,404 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,404 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,404 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,404 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,404 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:06:32,405 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:06:32,405 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,426 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:06:32,428 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:06:32,428 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,463 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:06:32,465 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:06:32,466 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,510 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:06:32,983 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:06:32,994 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:06:32,995 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:06:32,996 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:06:32,997 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:06:32,997 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:06:32,998 DEBUG [http-nio-8080-exec-10] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:06:33,175 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:10:26,341 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:11:31,908 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:11:31,908 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:11(String), 2025-08-20 11:11(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:11:31,910 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:11:31,910 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:11:31,910 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:31,910 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:11:32,002 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:11:32,003 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:11:32,004 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,005 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:11:32,006 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:11:32,006 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,100 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:11:32,107 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:11:32,108 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,113 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:11:32,114 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:11:32,114 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,223 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:11:32,224 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:11:32,224 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,225 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:11:32,226 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:11:32,226 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,227 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:11:32,228 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:11:32,228 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:11:32,258 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:11:32,259 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:11:32,259 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,260 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:11:32,261 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:11:32,261 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,262 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:11:32,263 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:11:32,263 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:11:32,405 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,405 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,405 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,405 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,405 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,405 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,405 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,407 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,407 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,407 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,414 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:11:32,415 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,415 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,415 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,415 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,415 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:11:32,416 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:11:32,416 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,437 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:11:32,439 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:11:32,440 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,482 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:11:32,485 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:11:32,485 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:32,535 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:11:33,039 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:11:33,050 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:11:33,051 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:11:33,067 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:11:33,069 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:11:33,070 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:11:33,073 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:11:33,196 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:15:26,358 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:16:31,909 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:16:31,909 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:16(String), 2025-08-20 11:16(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:16:31,909 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:16:31,909 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:31,911 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:16:31,911 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:16:32,000 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:16:32,001 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:16:32,001 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,003 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:16:32,003 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:16:32,003 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,124 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:16:32,129 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:16:32,130 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:16:32,130 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,130 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:16:32,131 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,131 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:16:32,132 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:16:32,132 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,132 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:16:32,133 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:16:32,133 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,133 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:16:32,134 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:16:32,134 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:16:32,256 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:16:32,257 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:16:32,257 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,258 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:16:32,259 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:16:32,259 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,260 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:16:32,261 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:16:32,261 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:16:32,389 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,390 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,390 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,390 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,390 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,390 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,390 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,390 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,390 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,390 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,398 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:16:32,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:16:32,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:16:32,400 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,420 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:16:32,421 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:16:32,422 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,470 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:16:32,473 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:16:32,473 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:32,532 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:16:32,995 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:16:33,005 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:16:33,005 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:16:33,006 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:16:33,008 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:16:33,008 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:16:33,009 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:16:33,172 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:20:26,364 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:22:08,918 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:22:08,918 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:22:08,919 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:22(String), 2025-08-20 11:22(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:22:08,919 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:08,921 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:22:08,921 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:22:09,019 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:22:09,020 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:22:09,020 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,033 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:22:09,034 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:22:09,034 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,078 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:22:09,086 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:22:09,086 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,139 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:22:09,141 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:22:09,141 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,163 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:22:09,164 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:22:09,165 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,166 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:22:09,166 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:22:09,166 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,187 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:22:09,189 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:22:09,189 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:22:09,280 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:22:09,281 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:22:09,281 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,282 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:22:09,283 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:22:09,283 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,284 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:22:09,285 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:22:09,285 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:22:09,394 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,395 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,395 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,395 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,395 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,395 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,395 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,395 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,395 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,395 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,404 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:22:09,404 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,405 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,405 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,405 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,405 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:22:09,406 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:22:09,406 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,444 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:22:09,447 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:22:09,447 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,485 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:22:09,487 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:22:09,488 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:09,538 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:22:10,019 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:22:10,030 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:22:10,031 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:22:10,032 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:22:10,033 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:22:10,033 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:22:10,034 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:22:10,193 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:25:26,369 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:28:08,913 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:28:08,913 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:28(String), 2025-08-20 11:28(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:28:08,918 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:28:08,919 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:28:08,922 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:28:08,923 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,014 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:28:09,015 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:28:09,015 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,016 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:28:09,017 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:28:09,017 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,143 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:28:09,151 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:28:09,151 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,153 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:28:09,153 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:28:09,154 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:28:09,154 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,154 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:28:09,154 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,167 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:28:09,167 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:28:09,167 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,182 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:28:09,183 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:28:09,184 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:28:09,279 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:28:09,280 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:28:09,280 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,281 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:28:09,282 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:28:09,282 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,284 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:28:09,284 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:28:09,285 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:28:09,398 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,399 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,400 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,408 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:28:09,408 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,408 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,408 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,408 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,408 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:28:09,409 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:28:09,409 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,430 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:28:09,432 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:28:09,432 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,474 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:28:09,476 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:28:09,477 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:09,523 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:28:10,037 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:28:10,048 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:28:10,049 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:28:10,051 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:28:10,052 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:28:10,053 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:28:10,053 DEBUG [http-nio-8080-exec-11] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:28:10,178 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:30:26,378 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:33:08,921 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:33:08,922 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:33(String), 2025-08-20 11:33(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:33:08,923 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:33:08,923 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:08,924 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:33:08,924 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:33:09,023 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:33:09,023 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:33:09,024 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,026 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:33:09,026 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:33:09,027 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,146 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:33:09,153 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:33:09,153 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,155 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:33:09,156 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:33:09,156 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,161 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:33:09,162 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:33:09,162 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,163 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:33:09,163 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:33:09,164 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,165 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:33:09,166 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:33:09,166 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:33:09,290 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:33:09,291 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:33:09,291 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,292 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:33:09,293 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:33:09,293 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,294 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:33:09,295 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:33:09,295 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:33:09,418 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,419 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,419 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,419 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,419 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,419 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,419 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,419 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,419 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,419 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,427 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:33:09,427 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,428 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,428 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,428 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,428 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:33:09,428 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:33:09,429 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,449 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:33:09,452 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:33:09,453 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,508 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:33:09,510 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:33:09,510 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:09,556 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:33:10,076 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:33:10,088 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:33:10,088 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:33:10,100 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:33:10,102 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:33:10,102 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:33:10,149 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:33:10,205 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:35:26,401 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:38:08,920 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:38:08,920 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:38:08,920 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:08,921 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:38(String), 2025-08-20 11:38(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:38:08,922 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:38:08,923 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:38:09,013 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:38:09,014 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:38:09,014 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,016 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:38:09,017 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:38:09,017 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,146 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:38:09,150 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:38:09,150 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:38:09,150 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,151 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:38:09,152 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:38:09,152 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,153 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:38:09,154 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:38:09,154 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,154 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:38:09,154 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:38:09,155 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:38:09,155 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:38:09,156 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,283 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:38:09,284 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:38:09,285 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,285 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:38:09,286 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:38:09,286 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,287 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:38:09,289 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:38:09,289 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:38:09,404 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,404 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,404 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,404 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,414 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:38:09,414 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,414 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,414 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,415 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,415 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:38:09,415 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:38:09,415 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,436 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:38:09,438 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:38:09,438 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,477 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:38:09,479 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:38:09,480 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:09,524 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:38:10,065 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:38:10,093 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:38:10,094 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:38:10,112 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:38:10,114 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:38:10,114 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:38:10,132 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:38:10,181 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:40:26,410 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:43:08,909 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:43:08,909 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:43(String), 2025-08-20 11:43(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:43:08,910 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:43:08,911 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:08,917 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:43:08,918 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:43:09,003 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:43:09,004 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:43:09,004 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,005 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:43:09,006 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:43:09,006 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,144 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:43:09,151 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:43:09,152 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:43:09,152 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,153 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:43:09,153 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:43:09,153 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,154 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:43:09,154 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,155 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:43:09,155 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:43:09,155 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:43:09,155 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,156 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:43:09,156 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:43:09,275 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:43:09,276 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:43:09,276 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,277 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:43:09,278 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:43:09,278 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,279 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:43:09,279 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:43:09,280 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:43:09,409 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,410 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,410 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,410 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,410 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,410 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,410 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,410 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,410 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,410 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,418 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:43:09,418 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,419 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,419 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,419 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,419 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:43:09,419 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:43:09,419 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,440 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:43:09,442 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:43:09,442 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,478 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:43:09,480 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:43:09,481 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,539 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:43:09,982 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:43:09,994 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:43:09,994 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:43:09,995 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:43:09,997 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:43:09,997 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:43:09,998 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:43:10,169 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:45:26,417 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:47:16,209 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:47:16,209 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:47:16,209 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,209 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:47(String), 2025-08-20 11:47(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:47:16,213 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:47:16,214 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:47:16,300 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:47:16,301 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:47:16,301 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,303 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:47:16,303 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:47:16,304 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,425 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:47:16,433 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:47:16,433 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,436 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:47:16,437 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:47:16,437 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,440 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:47:16,441 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:47:16,441 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,443 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:47:16,444 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:47:16,444 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,445 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:47:16,446 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:47:16,447 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:47:16,562 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:47:16,563 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:47:16,563 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,564 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:47:16,564 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:47:16,565 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,566 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:47:16,568 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:47:16,568 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:47:16,702 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,702 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,702 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,702 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,702 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,704 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,704 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,704 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,704 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,704 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,712 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:47:16,713 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,713 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,713 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,713 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,713 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:47:16,714 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:47:16,714 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,735 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:47:16,737 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:47:16,737 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,790 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:47:16,793 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:47:16,794 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:16,845 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:47:17,296 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:47:17,305 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:47:17,306 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:47:17,307 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:47:17,309 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:47:17,310 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:47:17,311 DEBUG [http-nio-8080-exec-5] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:47:17,466 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:50:26,433 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:53:08,921 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:53:08,921 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:53(String), 2025-08-20 11:53(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:53:08,922 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:53:08,923 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:08,925 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:53:08,925 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:53:09,013 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:53:09,013 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:53:09,013 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,016 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:53:09,016 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:53:09,016 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,145 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:53:09,153 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:53:09,153 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,154 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:53:09,155 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:53:09,155 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,155 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:53:09,156 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:53:09,156 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,156 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:53:09,156 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:53:09,156 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,159 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:53:09,160 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:53:09,160 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:53:09,274 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:53:09,276 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:53:09,276 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,277 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:53:09,278 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:53:09,278 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,280 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:53:09,280 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:53:09,280 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:53:09,396 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,396 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,397 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,404 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:53:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,406 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:53:09,407 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:53:09,407 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,428 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:53:09,430 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:53:09,430 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,487 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:53:09,490 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:53:09,490 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:09,535 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:53:10,037 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:53:10,048 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:53:10,048 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:53:10,049 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:53:10,051 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:53:10,051 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:53:10,052 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:53:10,175 DEBUG [http-nio-8080-exec-7] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 11:55:26,441 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 11:58:08,915 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 11:58:08,916 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 11:58(String), 2025-08-20 11:58(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 11:58:08,916 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 11:58:08,917 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:08,920 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 11:58:08,920 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 11:58:09,008 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 11:58:09,009 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:58:09,010 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,011 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:58:09,011 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:58:09,012 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,144 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:58:09,144 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 11:58:09,144 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:58:09,145 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,146 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:58:09,146 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:58:09,146 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,147 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:58:09,148 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 11:58:09,148 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 11:58:09,150 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 12:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:58:09,150 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,153 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:58:09,154 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 11:58:09,154 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,280 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 11:58:09,281 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 11:58:09,281 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,282 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:58:09,283 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 11:58:09,283 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,286 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:58:09,287 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 11:58:09,287 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 11:58:09,408 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,409 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,417 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 11:58:09,418 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,418 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,418 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,418 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,418 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 11:58:09,419 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 11:58:09,419 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,440 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 11:58:09,442 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 11:58:09,442 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,477 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 11:58:09,480 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 11:58:09,480 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:09,522 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 11:58:10,159 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 11:58:10,170 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 11:58:10,171 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 11:58:10,172 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 11:58:10,174 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 11:58:10,174 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 12:00(String)
2025-08-20 11:58:10,176 DEBUG [http-nio-8080-exec-1] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 11:58:10,449 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 12:00:26,454 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 12:03:08,907 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 12:03:08,907 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 12:03(String), 2025-08-20 12:03(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 12:03:08,908 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 12:03:08,909 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:08,911 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 12:03:08,912 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 12:03:09,001 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 12:03:09,002 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 13:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 12:03:09,002 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,023 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:03:09,024 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 12:03:09,024 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,084 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 12:03:09,093 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 13:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 12:03:09,093 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,102 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:03:09,103 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 12:03:09,103 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,204 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 12:03:09,206 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 12:03:09,206 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,232 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:03:09,233 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 12:03:09,234 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,236 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:03:09,236 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 12:03:09,236 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 12:03:09,261 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 12:03:09,261 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 12:03:09,262 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,262 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:03:09,263 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 12:03:09,264 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,265 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:03:09,265 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 12:03:09,266 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 12:03:09,407 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,408 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,408 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,408 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,408 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,408 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,408 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,408 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,408 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,409 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,438 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 12:03:09,439 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,439 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,439 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,439 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,439 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:03:09,440 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 12:03:09,440 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,460 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 12:03:09,462 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 12:03:09,463 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,509 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 12:03:09,511 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 12:03:09,511 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:09,560 DEBUG [http-nio-8080-exec-6] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 12:03:09,998 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 12:03:10,008 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 12:03:10,009 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:03:10,011 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 12:03:10,013 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 12:03:10,013 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 13:00(String)
2025-08-20 12:03:10,014 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:03:10,189 DEBUG [http-nio-8080-exec-2] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 12:05:26,469 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
2025-08-20 12:08:08,907 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==>  Preparing: {call proc_get_rsvrdata_all_new_yg(?, ?, ?)} 
2025-08-20 12:08:08,907 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-17 12:08(String), 2025-08-20 12:08(String),  and STTP IN ('RR','RQ')  and CHARINDEX(STADTP,'1,4')>0  AND STB.ADCD LIKE '22%'(String)
2025-08-20 12:08:08,909 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==>  Preparing: WITH BASLIST AS ( SELECT BAS_CODE, BAS_NAME, BAS_NO, PBAS_CODE, RIVER_AREA, IS_ZYJH, IS_ZXHL FROM BSN_BAS_B WHERE PBAS_CODE = '' OR PBAS_CODE IS NULL UNION ALL SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.PBAS_CODE, B.RIVER_AREA, B.IS_ZYJH, B.IS_ZXHL FROM BSN_BAS_B B INNER JOIN BASLIST R ON B.PBAS_CODE=R.BAS_CODE ) SELECT B.BAS_CODE, B.BAS_NAME, B.BAS_NO, B.RIVER_AREA, B.PBAS_CODE, PB.BAS_NAME PBAS_NAME, RV.FLOW_AREA, RB.RV_CODE FROM BASLIST B LEFT JOIN BSN_BAS_B PB ON B.PBAS_CODE=PB.BAS_CODE LEFT JOIN BSN_BAS_RV RB ON B.BAS_CODE=RB.BAS_CODE LEFT JOIN ATT_RV_BASE RV ON RB.RV_CODE=RV.RV_CODE WHERE B.IS_ZXHL = '1' ORDER BY B.BAS_NO, B.BAS_CODE 
2025-08-20 12:08:08,909 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:08,911 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RS.RES_CODE, RS.RES_NAME, ADCD, ADNM, ENG_SCAL, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, WAT_SHED_AREA , REL.RES_CODE_RELATED TEMP_CODE, RSD.STCD, RSD.W FROM ATT_RES_BASE RS LEFT JOIN BSN_RSVR_RELATIONSHIP_B REL ON RS.RES_CODE=REL.RES_CODE LEFT JOIN REL_RES_AD RA ON RS.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD LEFT JOIN ( SELECT A.RES_CODE, A.STCD, A.W FROM BSN_RESSTCDDRNA_B A LEFT JOIN BSN_STBPRP_V_YG B ON A.STCD=B.STCD WHERE CHARINDEX(B.STADTP,?)>0 ) RSD ON RS.RES_CODE=RSD.RES_CODE WHERE RS.ENG_SCAL IN ('1','2','3','4','5') ORDER BY RS.RES_CODE 
2025-08-20 12:08:08,911 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String)
2025-08-20 12:08:09,001 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getBasList [BaseJdbcLogger.java : 143] <==      Total: 166
2025-08-20 12:08:09,002 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 13:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 12:08:09,002 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,004 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:08:09,005 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 12:08:09,005 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,131 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRainBaseList [BaseJdbcLogger.java : 143] <==      Total: 6158
2025-08-20 12:08:09,138 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-20 08:00') and TM <= CONVERT(datetime,'2025-08-20 13:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 12:08:09,139 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,141 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:08:09,142 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT STCD, SUM(DRP) DRP FROM ST_PPTN_QX_3H6M WHERE TM > (SELECT MAX(TS) FROM ST_PPTN_QX_3H6M) GROUP BY STCD ORDER BY STCD 
2025-08-20 12:08:09,142 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,144 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 12:08:09,145 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 12:08:09,145 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,147 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:08:09,148 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 12:08:09,149 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,150 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:08:09,151 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD left join (select st.BAS_CODE BAS_CODE,st.STCD STCD from BSN_BAS_ST st left join BSN_BAS_B bas on st.BAS_CODE = bas.BAS_CODE where bas.IS_ZXHL = '1' ) bs on b.STCD = bs.STCD where 1=1 AND tk.PFL='1' and CHARINDEX(b.STADTP,?)>0 and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD, bs.BAS_CODE order by b.STCD 
2025-08-20 12:08:09,151 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] ==> Parameters: 1,2,3,6(String), 2(Integer), 22(String)
2025-08-20 12:08:09,276 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast3Hours [BaseJdbcLogger.java : 143] <==      Total: 2
2025-08-20 12:08:09,277 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, SUM(DRP) DRP from ST_PPTN_QX_72H1H WHERE TM > GETDATE() and TM <= DATEADD(HOUR, 24, GETDATE()) group by STCD order by STCD 
2025-08-20 12:08:09,277 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,278 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getForecast24Hours [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:08:09,278 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD,SUM(DRP) DRPS from ST_PPTN_R WHERE 1=1 and tm> CONVERT(datetime,'2025-08-19 08:00:00') and TM <= CONVERT(datetime,'2025-08-20 08:00') AND INTV='1' and drp>=0 GROUP BY STCD 
2025-08-20 12:08:09,278 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,281 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getAccpByTmAll [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:08:09,282 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==>  Preparing: select b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM ,b.STADTPNM,b.stadtp,PLGTD,PLTTD from BSN_STBPRP_V_YG b left join ST_STSMTASK_B tk on b.STCD=tk.STCD where 1=1 AND tk.PFL='1' and left(b.ADCD,?)=? GROUP BY b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD order by b.STCD 
2025-08-20 12:08:09,282 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] ==> Parameters: 2(Integer), 22(String)
2025-08-20 12:08:09,402 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,402 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,402 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,402 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,404 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,404 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,404 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,404 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,404 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,404 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,411 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==      Total: 436
2025-08-20 12:08:09,411 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,411 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,412 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,412 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,412 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getRsvrLatestData [BaseJdbcLogger.java : 143] <==    Updates: 0
2025-08-20 12:08:09,412 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b where objtp = '1' or objtp='6' 
2025-08-20 12:08:09,413 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,434 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.R.getBsnRsvrObjOnly [BaseJdbcLogger.java : 143] <==      Total: 4211
2025-08-20 12:08:09,436 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==>  Preparing: SELECT RB.RES_CODE, RES_NAME, LOW_LEFT_LONG, LOW_LEFT_LAT, UP_RIGHT_LONG, UP_RIGHT_LAT, RES_LOC, RES_TYPE , CASE WHEN ENG_GRAD IS NULL THEN 5 ELSE ENG_GRAD END AS ENG_GRAD, ENG_SCAL, FL_LOW_LIM_LEV, RA.AD_CODE ADCD, AD.ADNM , CASE WHEN RR.RES_CODE_RELATED IS NOT NULL THEN RR.RES_CODE_RELATED ELSE RB.RES_CODE END AS RES_KEY FROM ATT_RES_BASE RB LEFT JOIN BSN_RSVR_RELATIONSHIP_B RR ON RB.RES_CODE=RR.RES_CODE LEFT JOIN REL_RES_AD RA ON RB.RES_CODE=RA.RES_CODE LEFT JOIN MDT_ADCDINFO_B AD ON RA.AD_CODE=AD.ADCD ORDER BY ENG_SCAL, ADCD 
2025-08-20 12:08:09,437 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,479 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RsvrDao.getAttResBase [BaseJdbcLogger.java : 143] <==      Total: 1303
2025-08-20 12:08:09,483 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==>  Preparing: select objid, objcd, objtp from bsn_objonly_b a inner join BSN_STBPRP_V_YG b on a.objcd = b.STCD where objtp = '1' and b.STTP in ('RR', 'RQ') 
2025-08-20 12:08:09,483 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:09,535 DEBUG [http-nio-8080-exec-3] c.h.c.a.s.r.m.RsvrDao.getBsnObjOnly [BaseJdbcLogger.java : 143] <==      Total: 2728
2025-08-20 12:08:10,073 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getRainStInfo [BaseJdbcLogger.java : 143] <==      Total: 6830
2025-08-20 12:08:10,085 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==>  Preparing: select STCD, Convert(decimal(18, 1), RAIN) RAIN from BSN_ONEHOURRAIN_R where rain >= 0 
2025-08-20 12:08:10,085 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] ==> Parameters: 
2025-08-20 12:08:10,099 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.RainDao.getOneHourRain [BaseJdbcLogger.java : 143] <==      Total: 1
2025-08-20 12:08:10,102 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==>  Preparing: select r.res_code, r.tm, r.rc from (select res_code, max(tm) tm from Reservoir_runoffcoef_r where tm >= ? and tm <= ? group by res_code) rr inner join Reservoir_runoffcoef_r r on rr.res_code=r.res_code and rr.tm=r.tm 
2025-08-20 12:08:10,102 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] ==> Parameters: 2025-08-20 08:00(String), 2025-08-20 13:00(String)
2025-08-20 12:08:10,116 DEBUG [http-nio-8080-exec-4] c.h.c.a.s.r.m.R.getStRsvrRcList [BaseJdbcLogger.java : 143] <==      Total: 0
2025-08-20 12:08:10,202 DEBUG [http-nio-8080-exec-12] c.h.c.a.s.r.m.R.getRainStInfoByBas [BaseJdbcLogger.java : 143] <==      Total: 7160
2025-08-20 12:10:26,486 INFO [pool-3-thread-1] com.huitu.checker.ApiResourceChecker [ApiResourceChecker.java : 112] check api resource[http://************:18081/oauth/cors]----true
