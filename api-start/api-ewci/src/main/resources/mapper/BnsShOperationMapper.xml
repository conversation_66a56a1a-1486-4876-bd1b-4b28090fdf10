<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.project.mapper.BnsShOperationDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!-- 通用查询映射结果 -->

    <insert id="addOperation">
        INSERT INTO BNS_SH_OPERATION
        (ADCD
        ,BNO
        ,XCOUNT
        ,WCOUNT
        ,SCOUNT
        ,DPCOUNT
        ,RSTCOUNT
        ,TPCOUNT
        ,ACPCOUNT
        ,CHCOUNT
        ,IFDCOUNT
        ,RCOUNT
        ,MDPSCOUNT
        ,MPPSCOUNT
        ,CDPSCOUNT
        ,CPPSCOUNT
        ,ODPSCOUNT
        ,OPPSCOUNT
        ,REMARK
        ,CREATED
        ,COMPLETED
        ,TS
        ,YEAR)
        VALUES
        (#{adcd}
        ,'00000000000000'
        ,#{xcount}
        ,#{wcount}
        ,#{scount}
        ,#{dpcount}
        ,#{rstcount}
        ,#{tpcount}
        ,#{acpcount}
        ,#{chcount}
        ,#{ifdcount}
        ,#{rcount}
        ,#{mdpscount}
        ,#{mppscount}
        ,#{cdpscount}
        ,#{cppscount}
        ,#{odpscount}
        ,#{oppscount}
        ,#{remark}
        ,GETDATE()
        ,null
        ,GETDATE()
        ,#{year})
    </insert>

    <insert id="addOperationList">
        INSERT INTO BNS_SH_OPERATION
        (ADCD
        ,BNO
        ,XCOUNT
        ,WCOUNT
        ,SCOUNT
        ,DPCOUNT
        ,RSTCOUNT
        ,TPCOUNT
        ,ACPCOUNT
        ,CHCOUNT
        ,IFDCOUNT
        ,RCOUNT
        ,MDPSCOUNT
        ,MPPSCOUNT
        ,CDPSCOUNT
        ,CPPSCOUNT
        ,ODPSCOUNT
        ,OPPSCOUNT
        ,REMARK
        ,CREATED
        ,COMPLETED
        ,TS
        ,YEAR)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        ( #{item.adcds}
        ,'00000000000000'
        ,#{item.xcount}
        ,#{item.wcount}
        ,#{item.scount}
        ,#{item.dpcount}
        ,#{item.rstcount}
        ,#{item.tpcount}
        ,#{item.acpcount}
        ,#{item.chcount}
        ,#{item.ifdcount}
        ,#{item.rcount}
        ,#{item.mdpscount}
        ,#{item.mppscount}
        ,#{item.cdpscount}
        ,#{item.cppscount}
        ,#{item.odpscount}
        ,#{item.oppscount}
        ,#{item.remark}
        ,GETDATE()
        ,null
        ,GETDATE()
        ,#{item.year})
         </foreach>
    </insert>

    <update id="updateOperation">
        UPDATE BNS_SH_OPERATION
        SET XCOUNT = #{xcount}
        ,WCOUNT = #{wcount}
        ,SCOUNT = #{scount}
        ,DPCOUNT = #{dpcount}
        ,RSTCOUNT = #{rstcount}
        ,TPCOUNT = #{tpcount}
        ,ACPCOUNT = #{acpcount}
        ,CHCOUNT = #{chcount}
        ,IFDCOUNT = #{ifdcount}
        ,RCOUNT = #{rcount}
        ,MDPSCOUNT = #{mdpscount}
        ,MPPSCOUNT = #{mppscount}
        ,CDPSCOUNT = #{cdpscount}
        ,CPPSCOUNT = #{cppscount}
        ,ODPSCOUNT = #{odpscount}
        ,OPPSCOUNT = #{oppscount}
        ,REMARK = #{remark}
        ,COMPLETED = #{completed}
        ,TS = GETDATE()
        ,YEAR = #{year}
        WHERE adcd = #{adcd} and bno = #{bno}
    </update>

    <update id="summary">
        UPDATE BNS_SH_OPERATION
        SET
            BNO = #{bno}
          ,COMPLETED = GETDATE()
          ,TS = GETDATE()
        WHERE bno = '00000000000000'
    </update>

    <select id="queryAddList"  resultType="com.huitu.cloud.api.ewci.project.entity.BnsShOperation">
        SELECT
        b.adcd AS ADCDS,
        a.*
        FROM
        (
        SELECT
        *
        FROM
        BNS_SH_OPERATION
        WHERE
        1 = 1
        and left(ADCD,2)= 22
        AND ADCD like '22%'
        AND BNO = '00000000000000'
        ) a
        RIGHT JOIN (
        SELECT
        a.ADNM,
        b.adcd
        FROM
        [dbo].[MDT_ADCDINFO_B] a
        JOIN [dbo].[MDT_ADCDINFO_E] b ON a.ADCD = b.ADCD
        WHERE
        b.adcd NOT IN (
        '220000000000000',
        '220100000000000',
        '220112000000000',
        '220122000000000',
        '220182000000000',
        '220183000000000',
        '220184000000000',
        '220202000000000',
        '220203000000000',
        '220204000000000',
        '220211000000000',
        '220300000000000',
        '220322000000000',
        '220382000000000',
        '220400000000000',
        '220502000000000',
        '220503000000000',
        '220600000000000',
        '220700000000000',
        '220722000000000',
        '220723000000000',
        '220781000000000',
        '220800000000000',
        '222299000000000',
        '222400000000000'
        )
        ) b ON a.ADCD = b.adcd
    </select>

    <update id="updateProject">
        UPDATE BNS_SH_PROJECT
        SET BNO = #{bno}
        ,CS_FUNDS1 = #{csFunds1}
        ,CS_FUNDS2 = #{csFunds2}
        ,CS_APPROVED = #{csApproved}
        ,CS_BIDTP = #{csBidtp}
        ,CS_IMPL_UNIT = #{csImplUnit}
        ,CS_INVESTED = #{csInvested}
        ,CS_PROGRESS = #{csProgress}
        ,CS_PAID = #{csPaid}
        ,CS_INITIALIZED = #{csInitialized}
        ,CS_COMPLETED = #{csCompleted}
        ,CS_PREDICT_TIME = #{csPredictTime}
        ,OP_FUNDS1 = #{opFunds1}
        ,OP_FUNDS2 = #{opFunds2}
        ,OP_APPROVED = #{opApproved}
        ,OP_BIDTP = #{opBidtp}
        ,OP_IMPL_UNIT = #{opImplUnit}
        ,OP_INVESTED = #{opInvested}
        ,OP_PROGRESS = #{opProgress}
        ,OP_PAID = #{opPaid}
        ,OP_INITIALIZED = #{opInitialized}
        ,OP_COMPLETED = #{opCompleted}
        ,OP_PREDICT_TIME = #{opPredictTime}
        ,REMARK = #{remark}
        ,TS = GETDATE()
        ,YEAR = #{year}
        WHERE adcd = #{adcd} and bno = #{bno}
    </update>

    <insert id="addProject">
        INSERT INTO BNS_SH_PROJECT
        (ADCD
        ,BNO
        ,CS_FUNDS1
        ,CS_FUNDS2
        ,CS_APPROVED
        ,CS_BIDTP
        ,CS_IMPL_UNIT
        ,CS_INVESTED
        ,CS_PROGRESS
        ,CS_PAID
        ,CS_INITIALIZED
        ,CS_COMPLETED
        ,CS_PREDICT_TIME
        ,OP_FUNDS1
        ,OP_FUNDS2
        ,OP_APPROVED
        ,OP_BIDTP
        ,OP_IMPL_UNIT
        ,OP_INVESTED
        ,OP_PROGRESS
        ,OP_PAID
        ,OP_INITIALIZED
        ,OP_COMPLETED
        ,OP_PREDICT_TIME
        ,REMARK
        ,CREATED
        ,COMPLETED
        ,TS
        ,YEAR)
        VALUES
        (#{adcd}
        ,'00000000000000'
        ,#{csFunds1}
        ,#{csFunds2}
        ,#{csApproved}
        ,#{csBidtp}
        ,#{csImplUnit}
        ,#{csInvested}
        ,#{csProgress}
        ,#{csPaid}
        ,#{csInitialized}
        ,#{csCompleted}
        ,#{csPredictTime}
        ,#{opFunds1}
        ,#{opFunds2}
        ,#{opApproved}
        ,#{opBidtp}
        ,#{opImplUnit}
        ,#{opInvested}
        ,#{opProgress}
        ,#{opPaid}
        ,#{opInitialized}
        ,#{opCompleted}
        ,#{opPredictTime}
        ,#{remark}
        ,GETDATE()
        ,null
        ,GETDATE()
        ,#{year})
    </insert>

    <insert id="addProjectList">
        INSERT INTO BNS_SH_PROJECT
        (ADCD
        ,BNO
        ,CS_FUNDS1
        ,CS_FUNDS2
        ,CS_APPROVED
        ,CS_BIDTP
        ,CS_IMPL_UNIT
        ,CS_INVESTED
        ,CS_PROGRESS
        ,CS_PAID
        ,CS_INITIALIZED
        ,CS_COMPLETED
        ,CS_PREDICT_TIME
        ,OP_FUNDS1
        ,OP_FUNDS2
        ,OP_APPROVED
        ,OP_BIDTP
        ,OP_IMPL_UNIT
        ,OP_INVESTED
        ,OP_PROGRESS
        ,OP_PAID
        ,OP_INITIALIZED
        ,OP_COMPLETED
        ,OP_PREDICT_TIME
        ,REMARK
        ,CREATED
        ,COMPLETED
        ,TS
        ,YEAR)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (#{item.adcds}
        ,'00000000000000'
        ,#{item.csFunds1}
        ,#{item.csFunds2}
        ,#{item.csApproved}
        ,#{item.csBidtp}
        ,#{item.csImplUnit}
        ,#{item.csInvested}
        ,#{item.csProgress}
        ,#{item.csPaid}
        ,#{item.csInitialized}
        ,#{item.csCompleted}
        ,#{item.csPredictTime}
        ,#{item.opFunds1}
        ,#{item.opFunds2}
        ,#{item.opApproved}
        ,#{item.opBidtp}
        ,#{item.opImplUnit}
        ,#{item.opInvested}
        ,#{item.opProgress}
        ,#{item.opPaid}
        ,#{item.opInitialized}
        ,#{item.opCompleted}
        ,#{item.opPredictTime}
        ,#{item.remark}
        ,GETDATE()
        ,null
        ,GETDATE()
        ,#{item.year})
        </foreach>
    </insert>

    <select id="getBenefitData" resultType="com.huitu.cloud.api.ewci.project.entity.BnsShOperation">
        <if test="type != null and type != '' and type == 1 ">
        SELECT
        *
        FROM
        (
        SELECT NULL AS
        adnm,
        NULL AS adcd,
        NULL AS adcds,
        NULL AS bno,
        SUM ( a.XCOUNT ) AS XCOUNT,
        SUM ( a.wcount ) AS wcount,
        SUM ( a.scount ) AS scount,
        SUM ( a.dpcount ) AS dpcount,
        SUM ( a.rstcount ) AS rstcount,
        SUM ( a.tpcount ) AS tpcount,
        SUM ( a.acpcount ) AS acpcount,
        SUM ( a.chcount ) AS chcount,
        SUM ( a.ifdcount ) AS ifdcount,
        SUM ( a.rcount ) AS rcount,
        SUM ( a.mdpscount ) AS mdpscount,
        SUM ( a.mppscount ) AS mppscount,
        SUM ( a.cdpscount ) AS cdpscount,
        SUM ( a.cppscount ) AS cppscount,
        SUM ( a.ODPSCOUNT ) AS ODPSCOUNT,
        SUM ( a.OPPSCOUNT ) AS OPPSCOUNT,
        NULL AS REMARK,
        NULL AS CREATED,
        NULL AS COMPLETED,
        NULL AS ts,
        NULL as year
        FROM
        ( SELECT * FROM BNS_SH_OPERATION WHERE 1 = 1
        <if test="level != null and adcd != '' and adcd != null and type == 1 ">
            and left(ADCD,#{level})=#{adcd}
        </if>
        <if test="bno != null and bno != ''">
            AND BNO = #{bno}
        </if>
        <if test="year != null and year != ''">
            AND year = #{year}
        </if>
        <if test="stm != null and stm != '' and type == 1">
            and TS >= '${stm}'
        </if>
        <if test="etm != null and etm != '' and type == 1">
            and TS &lt;= '${etm}'
        </if>) a
        RIGHT JOIN (
        SELECT
        a.ADNM,
        b.adcd
        FROM
        MDT_ADCDINFO_B a
        JOIN MDT_ADCDINFO_E b ON a.ADCD = b.ADCD
        WHERE
        b.adcd NOT IN (
        '220000000000000',
        '220100000000000',
        '220112000000000',
        '220122000000000',
        '220182000000000',
        '220183000000000',
        '220184000000000',
        '220202000000000',
        '220203000000000',
        '220204000000000',
        '220211000000000',
        '220300000000000',
        '220322000000000',
        '220382000000000',
        '220400000000000',
        '220502000000000',
        '220503000000000',
        '220600000000000',
        '220700000000000',
        '220722000000000',
        '220723000000000',
        '220781000000000',
        '220800000000000',
        '222299000000000',
        '222400000000000'
        )
        <if test="level != null and adcd != '' and adcd != null and type == 1 ">
            and left(b.ADCD,#{level})=#{adcd}
        </if>
        ) b ON a.ADCD = b.adcd
        WHERE
        1 = 1
        UNION
        SELECT
        b.adnm,
        b.adcd AS adcds,
        a.*
        FROM
        (
        </if>
        SELECT * FROM BNS_SH_OPERATION WHERE 1 = 1
        <if test="level != null and adcd != '' and adcd != null and type == 1 ">
            and left(ADCD,#{level})=#{adcd}
        </if>
        <if test="level != null and adcd != '' and adcd != null and type == 2 ">
            and ADCD = #{adcd}
        </if>
        <if test="year != null and year != ''">
            AND year = #{year}
        </if>
        <if test="bno != null and bno != ''">
            AND BNO = #{bno}
        </if>
        <if test="stm != null and stm != '' and type == 1">
            and TS >= '${stm}'
        </if>
        <if test="etm != null and etm != '' and type == 1">
            and TS &lt;= '${etm}'
        </if>
        <if test="type != null and type != '' and type == 1 ">
            ) a
        RIGHT JOIN (
        SELECT
        a.ADNM,
        b.adcd
        FROM
        MDT_ADCDINFO_B a
        JOIN MDT_ADCDINFO_E b ON a.ADCD = b.ADCD
        WHERE
        b.adcd NOT IN (
        '220000000000000',
        '220100000000000',
        '220112000000000',
        '220122000000000',
        '220182000000000',
        '220183000000000',
        '220184000000000',
        '220202000000000',
        '220203000000000',
        '220204000000000',
        '220211000000000',
        '220300000000000',
        '220322000000000',
        '220382000000000',
        '220400000000000',
        '220502000000000',
        '220503000000000',
        '220600000000000',
        '220700000000000',
        '220722000000000',
        '220723000000000',
        '220781000000000',
        '220800000000000',
        '222299000000000',
        '222400000000000'
        )
        <if test="level != null and adcd != '' and adcd != null and type == 1 ">
            and left(b.ADCD,#{level})=#{adcd}
        </if>
        <if test="level != null and adcd != '' and adcd != null and type == 2 ">
            and b.ADCD = #{adcd}
        </if>
        ) b ON a.ADCD = b.adcd
        ) c
        ORDER BY
        c.adcd ASC
        </if>
    </select>

    <select id="getCurrentTimeData" resultType="com.huitu.cloud.api.ewci.project.entity.BnsShOperation">
        SELECT TOP
        1 *
        FROM
            BNS_SH_OPERATION
        WHERE
        1 = 1
        <if test="adcd != '' and adcd != null">
            and ADCD =  #{adcd}
        </if>
        AND ts &lt;= getdate( )
        ORDER BY
        ts DESC
    </select>

    <select id="getBnoList" resultType="com.huitu.cloud.api.ewci.project.entity.BnsShOperation">
        SELECT DISTINCT
        bno
        FROM
        BNS_SH_OPERATION
        WHERE
        <if test="level != null and adcd != '' and adcd != null ">
            left(ADCD,#{level})=#{adcd}
        </if>
        ORDER BY
        bno ASC
    </select>

    <select id="getrojectData" resultType="com.huitu.cloud.api.ewci.project.entity.BnsShProject">
        <if test="type == 1 ">
        SELECT
        *
        FROM
        (
        SELECT NULL AS
        adnm,
        NULL AS adcds,
        NULL AS ADCD,
        NULL AS BNO,
        SUM ( a.CS_FUNDS1 ) AS CS_FUNDS1,
        SUM ( a.CS_FUNDS2 ) AS CS_FUNDS2,
        NULL AS CS_APPROVED,
        NULL AS CS_BIDTP,
        NULL AS CS_IMPL_UNIT,
        SUM ( a.CS_INVESTED ) AS CS_INVESTED,
        NULL AS CS_PROGRESS,
        SUM ( a.CS_PAID ) AS CS_PAID,
        NULL AS CS_INITIALIZED,
        NULL AS CS_COMPLETED,
        NULL AS CS_PREDICT_TIME,
        SUM ( a.OP_FUNDS1 ) AS OP_FUNDS1,
        SUM ( a.OP_FUNDS2 ) AS OP_FUNDS2,
        NULL AS OP_APPROVED,
        NULL AS OP_BIDTP,
        NULL AS OP_IMPL_UNIT,
        SUM ( a.OP_INVESTED ) AS OP_INVESTED,
        NULL AS OP_PROGRESS,
        SUM ( a.OP_PAID ) AS OP_PAID,
        NULL AS OP_INITIALIZED,
        NULL AS OP_COMPLETED,
        NULL AS OP_PREDICT_TIME,
        NULL AS REMARK,
        NULL AS CREATED,
        NULL AS COMPLETED,
        NULL AS TS,
        NULL AS YEAR
        FROM
        ( SELECT * FROM BNS_SH_PROJECT WHERE 1 = 1
        <if test="level != null and adcd != '' and adcd != null">
            and left(ADCD,#{level})=#{adcd}
        </if>
        <if test="year != null and year != ''">
            AND year = #{year}
        </if>
        <if test="bno != null and bno != ''">
            AND BNO = #{bno}
        </if>
        <if test="stm != null and stm != ''">
            and TS >= '${stm}'
        </if>
        <if test="etm != null and etm != ''">
            and TS &lt;= '${etm}'
        </if>
        ) a
        RIGHT JOIN (
        SELECT
        a.ADNM,
        b.adcd
        FROM
        [dbo].[MDT_ADCDINFO_B] a
        JOIN [dbo].[MDT_ADCDINFO_E] b ON a.ADCD = b.ADCD
        WHERE
        b.adcd NOT IN (
        '220000000000000',
        '220100000000000',
        '220112000000000',
        '220122000000000',
        '220182000000000',
        '220183000000000',
        '220184000000000',
        '220202000000000',
        '220203000000000',
        '220204000000000',
        '220211000000000',
        '220300000000000',
        '220322000000000',
        '220382000000000',
        '220400000000000',
        '220502000000000',
        '220503000000000',
        '220600000000000',
        '220700000000000',
        '220722000000000',
        '220723000000000',
        '220781000000000',
        '220800000000000',
        '222299000000000',
        '222400000000000'
        )
        <if test="level != null and adcd != '' and adcd != null">
            and left(b.ADCD,#{level})=#{adcd}
        </if>
        ) b ON a.ADCD = b.adcd
        WHERE
        1 = 1
        <if test="bno != null and bno != ''">
            AND a.BNO = #{bno}
        </if>
        UNION
        SELECT
        b.adnm,
        b.adcd AS adcds,
        a.*
        FROM
        (
        </if>
        SELECT * FROM BNS_SH_PROJECT WHERE 1 = 1
        <if test="level != null and adcd != '' and adcd != null and type == 2">
            and ADCD = #{adcd}
        </if>
        <if test="level != null and adcd != '' and adcd != null and type == 1">
            and left(ADCD,#{level})=#{adcd}
        </if>
        <if test="bno != null and bno != ''">
            AND BNO = #{bno}
        </if>
        <if test="year != null and year != ''">
            AND year = #{year}
        </if>
        <if test="stm != null and stm != ''">
            and TS >= '${stm}'
        </if>
        <if test="etm != null and etm != ''">
            and TS &lt;= '${etm}'
        </if>
        <if test="type != null and type != '' and type == 1 ">
        ) a
        RIGHT JOIN (
        SELECT
        a.ADNM,
        b.adcd
        FROM
        [dbo].[MDT_ADCDINFO_B] a
        JOIN [dbo].[MDT_ADCDINFO_E] b ON a.ADCD = b.ADCD
        WHERE
        b.adcd NOT IN (
        '220000000000000',
        '220100000000000',
        '220112000000000',
        '220122000000000',
        '220182000000000',
        '220183000000000',
        '220184000000000',
        '220202000000000',
        '220203000000000',
        '220204000000000',
        '220211000000000',
        '220300000000000',
        '220322000000000',
        '220382000000000',
        '220400000000000',
        '220502000000000',
        '220503000000000',
        '220600000000000',
        '220700000000000',
        '220722000000000',
        '220723000000000',
        '220781000000000',
        '220800000000000',
        '222299000000000',
        '222400000000000'
        )
        <if test="level != null and adcd != '' and adcd != null">
            and left(b.ADCD,#{level})=#{adcd}
        </if>
        ) b ON a.ADCD = b.adcd
        ) c
        ORDER BY
        c.adcds ASC
        </if>
    </select>

    <select id="getrojectList" resultType="com.huitu.cloud.api.ewci.project.entity.BnsShProject">
        SELECT DISTINCT
        bno
        FROM
        BNS_SH_PROJECT
        WHERE
        <if test="level != null and adcd != '' and adcd != null ">
            left(ADCD,#{level})=#{adcd}
        </if>
        ORDER BY
        bno ASC
    </select>

    <update id="summaryRojectB">
        UPDATE BNS_SH_PROJECT
        SET
            BNO = #{bno}
          ,COMPLETED = GETDATE()
          ,TS = GETDATE()
        WHERE bno = '00000000000000'
    </update>

    <select id="queryAddProList"  resultType="com.huitu.cloud.api.ewci.project.entity.BnsShProject">
        SELECT
            b.adcd AS ADCDS,
            a.*
        FROM
            (
                SELECT
                    *
                FROM
                    BNS_SH_PROJECT
                WHERE
                    1 = 1
                  and left(ADCD,2)= 22
                  AND ADCD like '22%'
                  AND BNO = '00000000000000'
            ) a
                RIGHT JOIN (
                SELECT
                    a.ADNM,
                    b.adcd
                FROM
                    [dbo].[MDT_ADCDINFO_B] a
                    JOIN [dbo].[MDT_ADCDINFO_E] b ON a.ADCD = b.ADCD
                WHERE
                    b.adcd NOT IN (
                    '220000000000000',
                    '220100000000000',
                    '220112000000000',
                    '220122000000000',
                    '220182000000000',
                    '220183000000000',
                    '220184000000000',
                    '220202000000000',
                    '220203000000000',
                    '220204000000000',
                    '220211000000000',
                    '220300000000000',
                    '220322000000000',
                    '220382000000000',
                    '220400000000000',
                    '220502000000000',
                    '220503000000000',
                    '220600000000000',
                    '220700000000000',
                    '220722000000000',
                    '220723000000000',
                    '220781000000000',
                    '220800000000000',
                    '222299000000000',
                    '222400000000000'
                    )
            ) b ON a.ADCD = b.adcd
    </select>

    <select id="getCurrentOneData" resultType="com.huitu.cloud.api.ewci.project.entity.BnsShProject">
        SELECT TOP
        1 *
        FROM
        BNS_SH_PROJECT
        WHERE
        1 = 1
        <if test="adcd != '' and adcd != null">
            and ADCD =  #{adcd}
        </if>
        AND ts &lt;= getdate( )
        ORDER BY
        ts DESC
    </select>
</mapper>
