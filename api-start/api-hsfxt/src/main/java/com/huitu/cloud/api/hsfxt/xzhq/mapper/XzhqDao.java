package com.huitu.cloud.api.hsfxt.xzhq.mapper;

import com.huitu.cloud.api.hsfxt.city.entity.DicHongshuiliangji;
import com.huitu.cloud.api.hsfxt.xzhq.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 风险图-蓄滞洪区 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
public interface XzhqDao {
    /**
     * 查询县和乡镇信息
     *
     * @param param 查询参数
     * @return
     */
    List<XzhqTree> getXzhqTreeInfo(Map<String, Object> param);
    /**
     * 查询屯信息需要的洪水量级参数
     *
     * @param param 查询参数
     * @return
     */
    DicHongshuiliangji getXzhqParam(Map<String, Object> param);
    /**
     * 查询屯信息
     *
     * @param param 查询参数
     * @return
     */
    List<XzhqTree> getTunXzhqTreeInfo(Map<String, Object> param);
    /**
     * 蓄滞洪区社会经济统计查询
     *
     * @param key 查询条件组合
     * @return
     */
    List<XzhShehuijingji> getXzhqShjjTreeInfo(@Param("key") String key);
    /**
     * 查询县区乡镇信息
     *
     * @param adcd  政区编码
     * @param bzqid 编制区编码
     * @return
     */
    XzhqXiangQing getXianQuXiangZhen(@Param("adcd")String adcd, @Param("bzqid")String bzqid);
    /**
     * 查询灾害损失信息
     *
     * @param adcd 政区编码
     * @param key  查询条件组合
     * @return
     */
    XzhqXiangQing getZaiHaiSunShi(@Param("adcd")String adcd, @Param("key")String key);
    /**
     * 查询蓄滞洪区屯详细信息
     *
     * @param newParam2 查询参数
     * @return
     */
    XzhqTunXiangqing getXzhqTunShjjDetailInfo(Map<String, Object> newParam2);
    /**
     * 查询蓄滞洪区转移方案信息
     *
     * @param adcd  政区编码
     * @param bzqid 编制区编码
     * @return
     */
    List<XzhqZhuanYiFangAn> getXzhqZhuanYiFangAn(@Param("adcd")String adcd, @Param("bzqid")String bzqid);
}
