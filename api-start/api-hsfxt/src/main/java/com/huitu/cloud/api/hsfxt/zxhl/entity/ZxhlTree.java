package com.huitu.cloud.api.hsfxt.zxhl.entity;

import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqZhuanYiFangAn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
/**
 * <p>
 * 中小河流树形列表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
@ApiModel(value = "XzhqTree对象", description = " 中小河流树形列表")
public class ZxhlTree implements Serializable {
    @ApiModelProperty(value = "行政区编码")
    private String adcd;
    @ApiModelProperty(value = "行政区名称")
    private String adnm;
    @ApiModelProperty(value = "上级编码")
    private String padcd;
    @ApiModelProperty(value = "下级节点集合")
    private List<ZxhlTree> children;
    @ApiModelProperty(value = "影响屯总数")
    private int childrenCount;
    @ApiModelProperty(value = "查询详情参数组合")
    private String keyindex;
    @ApiModelProperty(value = "转移方案")
    private List<ZxhlZhuanYiFangAn> zyfa;

    public List<ZxhlZhuanYiFangAn> getZyfa() {
        return zyfa;
    }

    public void setZyfa(List<ZxhlZhuanYiFangAn> zyfa) {
        this.zyfa = zyfa;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public List<ZxhlTree> getChildren() {
        return children;
    }

    public void setChildren(List<ZxhlTree> children) {
        this.children = children;
    }

    public int getChildrenCount() {
        return childrenCount;
    }

    public void setChildrenCount(int childrenCount) {
        this.childrenCount = childrenCount;
    }

    public String getKeyindex() {
        return keyindex;
    }

    public void setKeyindex(String keyindex) {
        this.keyindex = keyindex;
    }
}
