package com.huitu.cloud.api.common.msg.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value = "预警短信发送记录", description = "预警短信发送记录信息")
public class WarnMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = " 短信接收人")
    @TableField("RECEIVER")
    private String receiver;

    @ApiModelProperty(value = " 手机号码")
    @TableField("MOBILENO")
    private String mobileNo;

    @ApiModelProperty(value = " 发送时间")
    @TableField("SENDTM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTm;

    @ApiModelProperty(value = " 发送结果")
    @TableField("SENDRESULT")
    private String sendResult;

    @ApiModelProperty(value = " 反馈结果")
    @TableField("FEEDBACK")
    private String feedback;

    @ApiModelProperty(value = " 确认时间")
    @TableField("CONFIRM_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public Date getSendTm() {
        return sendTm;
    }

    public void setSendTm(Date sendTm) {
        this.sendTm = sendTm;
    }

    public String getSendResult() {
        return sendResult;
    }

    public void setSendResult(String sendResult) {
        this.sendResult = sendResult;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }
}
