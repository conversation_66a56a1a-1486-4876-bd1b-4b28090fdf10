<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.feedback.mapper.BnsFeedBackDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPage" resultType="com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackVo">
        SELECT ID,M_MODULE,C_MODULE,COMMENTS,STATUS,FEEDBACK_USER,FEEDBACK_TM,HANDLE_USER,HANDLE_TM,HANDLE_DESC
        FROM BNS_FEEDBACK_B
        <where>
            <if test="map.stm != null and map.stm !=''">
                and FEEDBACK_TM > CONVERT(datetime,#{map.stm})
            </if>
            <if test="map.etm != null and map.etm !=''">
                and FEEDBACK_TM &lt;= CONVERT(datetime,#{map.etm})
            </if>
            <if test="map.mModule !=null and map.mModule !=''">
                AND CHARINDEX(#{map.mModule},M_MODULE) >0
            </if>
            <if test="map.cModule !=null and map.cModule !=''">
                AND CHARINDEX(#{map.cModule},C_MODULE) >0
            </if>
            <if test="map.status !=null and map.status !=''">
                AND STATUS = #{map.status}
            </if>
        </where>
        ORDER BY FEEDBACK_TM DESC
    </select>

    <insert id="insertFeedBack">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            select NEWID() as id
        </selectKey>
        INSERT INTO BNS_FEEDBACK_B (ID, M_MODULE, C_MODULE, COMMENTS, STATUS, FEEDBACK_USER, FEEDBACK_TM)
        VALUES (#{id}, #{mModule}, #{cModule}, #{comments}, '0', #{feedbackUser}, GETDATE())
    </insert>

    <update id="handleFeedBack">
        UPDATE BNS_FEEDBACK_B
        set HANDLE_USER = #{handleUser},
            HANDLE_TM   = GETDATE(),
            HANDLE_DESC = #{handleDesc},
            STATUS = '1'
        WHERE ID = #{id}
          AND STATUS = '0'
    </update>

</mapper>