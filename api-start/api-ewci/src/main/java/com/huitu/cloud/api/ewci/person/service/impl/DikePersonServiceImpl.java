package com.huitu.cloud.api.ewci.person.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.exception.DikePersonImportException;
import com.huitu.cloud.api.ewci.person.mapper.DikePersonDao;
import com.huitu.cloud.api.ewci.person.mapper.DikePersonTmpDao;
import com.huitu.cloud.api.ewci.person.service.DikePersonService;
import com.huitu.cloud.util.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 堤防责任人管理服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class DikePersonServiceImpl implements DikePersonService {

    private static final Logger logger = LoggerFactory.getLogger(RiverPersonServiceImpl.class);

    private DikePersonDao baseDao;
    private DikePersonTmpDao tmpDao;

    @Autowired
    public void setBaseDao(DikePersonDao baseDao) {
        this.baseDao = baseDao;
    }

    @Autowired
    public void setTmpDao(DikePersonTmpDao tmpDao) {
        this.tmpDao = tmpDao;
    }

    @Override
    public IPage<DikePerson> getPageList(DikePersonQuery query) {
        return baseDao.getPageList(query.toPageParam(), query.toQueryParam());
    }

    @Override
    public List<DikeSummaryVo> getDikePersonSummaryList(DikeSummaryQuery query) {
        if (query.getLevel() > 6) {
            throw new RuntimeException("行政区划代码无效，仅支持到县以上级别（含县）");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", query.getAdcd());
        params.put("level", query.getLevel());
        params.put("include", query.isInclude() ? 1 : 0);
        params.put("lowLevel", query.getLowLevel());
        List<DikeSummaryVo> source = baseDao.getDikePersonSummaryList(params);

        if (CollectionUtils.isEmpty(source)) {
            return new ArrayList<>();
        }
        if (!query.isInclude()) {
            // 不包含下级，无需创建树
            return source;
        }
        Map<String, DikeSummaryVo> center = new LinkedHashMap<>(source.size());
        source.forEach(ad -> center.put(ad.getAdcd(), ad));
        List<DikeSummaryVo> target = new ArrayList<>();
        for (DikeSummaryVo node : source) {
            if (center.containsKey(node.getPadcd())) {
                DikeSummaryVo parent = center.get(node.getPadcd());
                if (null == parent.getChildren()) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            } else {
                target.add(node);
            }
        }
        return target;
    }

    @Override
    public void dataExport(DikePersonQuery query, OutputStream output) {
        IPage<DikePerson> page = baseDao.getPageList(query.toPageParam(), query.toQueryParam());
        EasyExcel.write(output, DikePerson.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("堤防责任人信息")
                .doWrite(page.getRecords());
    }

    @Override
    public void dataExport(OutputStream output) {
        List<DikePersonP> list = baseDao.getAllList();
        EasyExcel.write(output, DikePersonP.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("堤防责任人信息（全省）")
                .doWrite(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult<BnsDikePersonTmp> dataImport(String xadcd, InputStream input) {
        List<BnsDikePersonTmp> tmpData;
        try {
            tmpData = EasyExcel.read(input, BnsDikePersonTmp.class, null)
                    .headRowNumber(3).sheet().doReadSync();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);

            throw new DikePersonImportException("Excel文件格式不正确，解析失败", ex);
        }

        if (CollectionUtils.isEmpty(tmpData)) {
            throw new DikePersonImportException("Excel文件中不包含任何数据，请检查");
        }
        // 过滤空数据，任何一项有数据即为有效
        tmpData = tmpData.stream().filter(this::nonNulProperty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tmpData)) {
            throw new DikePersonImportException("Excel文件中数据无效，请检查");
        }

        // 去掉多余的空格
        tmpData.forEach(this::trimProperty);

        try {
            String batchNo = batchInsertTmpData(xadcd, tmpData);
            List<BnsDikePersonTmp> errorData = baseDao.batchImport(batchNo);
            if (!CollectionUtils.isEmpty(errorData)) {
                ImportResult<BnsDikePersonTmp> result = new ImportResult<>();
                result.setStatus(tmpData.size() == errorData.size() ? 0 : 2);
                result.setMessage(String.format("数据（%s）导入失败，请检查", result.getStatus() == 0 ? "全部" : "部分"));
                result.setData(errorData);
                return result;
            }
        } catch (Exception ex) {
            logger.error("堤防责任人信息入库失败", ex);

            throw new DikePersonImportException("数据未能成功入库，导入失败", tmpData, ex);
        }
        return new ImportResult<>();
    }

    @Override
    public List<PersonInfo> getSimplePersonList(PersonQuery query) {
        List<PersonInfo> persons = baseDao.getSimpleList(query.toQueryParam());
        if (!CollectionUtils.isEmpty(persons)) {
            persons = persons.stream().distinct().collect(Collectors.toList());
        }
        return persons;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int dikePersonEdit(DikePerson entity) {
        List<DikePersonB> list = new ArrayList<>();
        // 行政责任人
        if (StringUtils.isNotBlank(entity.getXzRealnm()) && StringUtils.isNotBlank(entity.getXzMobile())) {
            DikePersonB xzPerson = new DikePersonB();
            xzPerson.setDikeCode(entity.getDikeCode());
            xzPerson.setAdcd(entity.getAdcd());
            xzPerson.setRvCode(entity.getRvCode());
            xzPerson.setDikeName(entity.getDikeName());
            xzPerson.setStartLgtd(entity.getStartLgtd());
            xzPerson.setStartLttd(entity.getStartLttd());
            xzPerson.setEndLgtd(entity.getEndLgtd());
            xzPerson.setEndLttd(entity.getEndLttd());
            xzPerson.setDikeType(entity.getDikeType());
            xzPerson.setDikeLen(entity.getDikeLen());
            xzPerson.setDikeBank(entity.getDikeBank());
            xzPerson.setPertp("1");
            xzPerson.setRealnm(entity.getXzRealnm());
            xzPerson.setPost(entity.getXzPost());
            xzPerson.setMobile(entity.getXzMobile());
            xzPerson.setTcount(entity.getTcount());
            xzPerson.setTs(LocalDateTime.now());
            list.add(xzPerson);
        }

        // 技术责任人
        if (StringUtils.isNotBlank(entity.getJsRealnm()) && StringUtils.isNotBlank(entity.getJsMobile())) {
            DikePersonB jsPerson = new DikePersonB();
            jsPerson.setDikeCode(entity.getDikeCode());
            jsPerson.setAdcd(entity.getAdcd());
            jsPerson.setRvCode(entity.getRvCode());
            jsPerson.setDikeName(entity.getDikeName());
            jsPerson.setStartLgtd(entity.getStartLgtd());
            jsPerson.setStartLttd(entity.getStartLttd());
            jsPerson.setEndLgtd(entity.getEndLgtd());
            jsPerson.setEndLttd(entity.getEndLttd());
            jsPerson.setDikeType(entity.getDikeType());
            jsPerson.setDikeLen(entity.getDikeLen());
            jsPerson.setDikeBank(entity.getDikeBank());
            jsPerson.setPertp("2");
            jsPerson.setRealnm(entity.getJsRealnm());
            jsPerson.setPost(entity.getJsPost());
            jsPerson.setMobile(entity.getJsMobile());
            jsPerson.setTcount(entity.getTcount());
            jsPerson.setTs(LocalDateTime.now());
            list.add(jsPerson);
        }

        // 村级责任人
        if (StringUtils.isNotBlank(entity.getCjRealnm()) && StringUtils.isNotBlank(entity.getCjMobile())) {
            DikePersonB cjPerson = new DikePersonB();
            cjPerson.setDikeCode(entity.getDikeCode());
            cjPerson.setAdcd(entity.getAdcd());
            cjPerson.setRvCode(entity.getRvCode());
            cjPerson.setDikeName(entity.getDikeName());
            cjPerson.setStartLgtd(entity.getStartLgtd());
            cjPerson.setStartLttd(entity.getStartLttd());
            cjPerson.setEndLgtd(entity.getEndLgtd());
            cjPerson.setEndLttd(entity.getEndLttd());
            cjPerson.setDikeType(entity.getDikeType());
            cjPerson.setDikeLen(entity.getDikeLen());
            cjPerson.setDikeBank(entity.getDikeBank());
            cjPerson.setPertp("3");
            cjPerson.setRealnm(entity.getCjRealnm());
            cjPerson.setPost(entity.getCjPost());
            cjPerson.setMobile(entity.getCjMobile());
            cjPerson.setTcount(entity.getTcount());
            cjPerson.setTs(LocalDateTime.now());
            list.add(cjPerson);
        }

        int insertCode = 0;
        int delCode = baseDao.delByDikeCode(entity.getDikeCode());
        if (delCode > 0) {
            insertCode = baseDao.insertList(list);
        }
        return insertCode;
    }

    /**
     * 批量插入临时数据
     *
     * @param xadcd   县级行政区划代码
     * @param persons 责任人信息集合
     * @return 批号
     **/
    private String batchInsertTmpData(String xadcd, List<BnsDikePersonTmp> persons) {
        String batchNo = UUID.randomUUID().toString();
        if (!CollectionUtils.isEmpty(persons)) {
            Map<String, Object> params = new HashMap<>();
            params.put("batchNo", batchNo);
            params.put("xadcd", xadcd);
            List<List<BnsDikePersonTmp>> lists = ListUtils.splitList(persons, 80);
            for (List<BnsDikePersonTmp> list : lists) {
                params.put("list", list);
                tmpDao.batchInsert(params);
            }
        }
        return batchNo;
    }

    /**
     * 属性不为空
     *
     * @param tmp 临时信息
     * @return true: 非空  false: 空
     **/
    private boolean nonNulProperty(BnsDikePersonTmp tmp) {
        return StringUtils.isNotBlank(tmp.getAdnm())
                || StringUtils.isNotBlank(tmp.getTown())
                || StringUtils.isNotBlank(tmp.getRvName())
                || StringUtils.isNotBlank(tmp.getDikeName())
                || Objects.isNull(tmp.getStartLgtd())
                || Objects.isNull(tmp.getStartLttd())
                || Objects.isNull(tmp.getEndLgtd())
                || Objects.isNull(tmp.getEndLttd())
                || StringUtils.isNotBlank(tmp.getDikeType())
                || Objects.isNull(tmp.getDikeLen())
                || StringUtils.isNotBlank(tmp.getDikeBank())
                || StringUtils.isNotBlank(tmp.getXzRealnm())
                || StringUtils.isNotBlank(tmp.getXzPost())
                || StringUtils.isNotBlank(tmp.getXzMobile())
                || StringUtils.isNotBlank(tmp.getJsRealnm())
                || StringUtils.isNotBlank(tmp.getJsPost())
                || StringUtils.isNotBlank(tmp.getJsMobile())
                || StringUtils.isNotBlank(tmp.getCjRealnm())
                || StringUtils.isNotBlank(tmp.getCjPost())
                || StringUtils.isNotBlank(tmp.getCjMobile())
                || Objects.isNull(tmp.getTcount());
    }

    /**
     * 去掉字符串属性的空格（两端）
     *
     * @param tmp 临时信息
     **/
    private void trimProperty(BnsDikePersonTmp tmp) {
        tmp.setAdnm(StringUtils.trim(tmp.getAdnm()));
        tmp.setTown(StringUtils.trim(tmp.getTown()));
        tmp.setRvName(StringUtils.trim(tmp.getRvName()));
        tmp.setDikeName(StringUtils.trim(tmp.getDikeName()));
        tmp.setDikeType(StringUtils.trim(tmp.getDikeType()));
        tmp.setDikeBank(StringUtils.trim(tmp.getDikeBank()));
        tmp.setXzRealnm(StringUtils.trim(tmp.getXzRealnm()));
        tmp.setXzPost(StringUtils.trim(tmp.getXzPost()));
        tmp.setXzMobile(StringUtils.trim(tmp.getXzMobile()));
        tmp.setJsRealnm(StringUtils.trim(tmp.getJsRealnm()));
        tmp.setJsPost(StringUtils.trim(tmp.getJsPost()));
        tmp.setJsMobile(StringUtils.trim(tmp.getJsMobile()));
        tmp.setCjRealnm(StringUtils.trim(tmp.getCjRealnm()));
        tmp.setCjPost(StringUtils.trim(tmp.getCjPost()));
        tmp.setCjMobile(StringUtils.trim(tmp.getCjMobile()));
    }
}
