package com.huitu.cloud.api.screen.fhjz.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value="水库对象", description="水库对象")
public class ReservoirVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "水库编号")
    private String resCode;

    @ApiModelProperty(value = "水库名称")
    private String resName;

    @ApiModelProperty(value = "水库规模 ")
    private String engScal;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "人工测站水位")
    private String rgRz;

    @ApiModelProperty(value = "自动测站水位")
    private String zdRz;

    @ApiModelProperty(value = "汛限水位")
    private String fsltdz;

    @ApiModelProperty(value = "汛限库容")
    private String fsltdw;

    @ApiModelProperty(value = "超汛限")
    private String cxx;


    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getEngScal() {
        return engScal;
    }

    public void setEngScal(String engScal) {
        this.engScal = engScal;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getRgRz() {
        return rgRz;
    }

    public void setRgRz(String rgRz) {
        this.rgRz = rgRz;
    }

    public String getZdRz() {
        return zdRz;
    }

    public void setZdRz(String zdRz) {
        this.zdRz = zdRz;
    }

    public String getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(String fsltdz) {
        this.fsltdz = fsltdz;
    }

    public String getFsltdw() {
        return fsltdw;
    }

    public void setFsltdw(String fsltdw) {
        this.fsltdw = fsltdw;
    }

    public String getCxx() {
        return cxx;
    }

    public void setCxx(String cxx) {
        this.cxx = cxx;
    }
}
