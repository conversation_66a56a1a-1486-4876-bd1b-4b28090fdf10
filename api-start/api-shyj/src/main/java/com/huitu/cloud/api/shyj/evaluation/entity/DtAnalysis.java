package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 分析评价对象统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-15
 */
@TableName("DT_ANALYSIS_S")
@ApiModel(value="DtAnalysisS对象", description="分析评价对象统计表")
public class DtAnalysis extends DtExtendInfo{

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "时段雨量预警指标对象总数")
    @TableField("DFWRULECT")
    private Integer dfwrulect;

    @ApiModelProperty(value = "水位预警指标对象总数")
    @TableField("WLWRULECT")
    private Integer wlwrulect;

    @ApiModelProperty(value = "综合雨量预警指标对象总数")
    @TableField("YJICRCT")
    private Integer yjicrct;

    @ApiModelProperty(value = "防洪现状对象总数")
    @TableField("NOWFHTBCT")
    private Integer nowfhtbct;

    @ApiModelProperty(value = "临界雨量模型分析法对象总数")
    @TableField("FXCGTBCT")
    private Integer fxcgtbct;

    @ApiModelProperty(value = "临界雨量经验估计法对象总数")
    @TableField("JYGSTBCT")
    private Integer jygstbct;

    @ApiModelProperty(value = "临界雨量降雨分析法对象总数")
    @TableField("YJCJTBCT")
    private Integer yjcjtbct;

    @ApiModelProperty(value = "控制断面设计洪水对象总数")
    @TableField("SDTDTBCT")
    private Integer sdtdtbct;

    @ApiModelProperty(value = "控制断面水位-流量-人口关系对象总数")
    @TableField("SWLLRKTBCT")
    private Integer swllrktbct;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getDfwrulect() {
        return dfwrulect;
    }

    public void setDfwrulect(Integer dfwrulect) {
        this.dfwrulect = dfwrulect;
    }

    public Integer getWlwrulect() {
        return wlwrulect;
    }

    public void setWlwrulect(Integer wlwrulect) {
        this.wlwrulect = wlwrulect;
    }

    public Integer getYjicrct() {
        return yjicrct;
    }

    public void setYjicrct(Integer yjicrct) {
        this.yjicrct = yjicrct;
    }

    public Integer getNowfhtbct() {
        return nowfhtbct;
    }

    public void setNowfhtbct(Integer nowfhtbct) {
        this.nowfhtbct = nowfhtbct;
    }

    public Integer getFxcgtbct() {
        return fxcgtbct;
    }

    public void setFxcgtbct(Integer fxcgtbct) {
        this.fxcgtbct = fxcgtbct;
    }

    public Integer getJygstbct() {
        return jygstbct;
    }

    public void setJygstbct(Integer jygstbct) {
        this.jygstbct = jygstbct;
    }

    public Integer getYjcjtbct() {
        return yjcjtbct;
    }

    public void setYjcjtbct(Integer yjcjtbct) {
        this.yjcjtbct = yjcjtbct;
    }

    public Integer getSdtdtbct() {
        return sdtdtbct;
    }

    public void setSdtdtbct(Integer sdtdtbct) {
        this.sdtdtbct = sdtdtbct;
    }

    public Integer getSwllrktbct() {
        return swllrktbct;
    }

    public void setSwllrktbct(Integer swllrktbct) {
        this.swllrktbct = swllrktbct;
    }

}
