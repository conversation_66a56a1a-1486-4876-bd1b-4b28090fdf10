package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-09
 */
@TableName("att_res_base")
@ApiModel(value="ResBase对象", description="")
public class ResBase extends Model<ResBase> {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水库KEY")
    private String resKey;

    @ApiModelProperty(value = "水库编码")
    private String resCode;

    @ApiModelProperty(value = "水库名称")
    private String resName;

    @ApiModelProperty(value = "左下经度")
    private BigDecimal lowLeftLong;

    @ApiModelProperty(value = "左下纬度")
    private BigDecimal lowLeftLat;

    @ApiModelProperty(value = "右上经度")
    private BigDecimal upRightLong;

    @ApiModelProperty(value = "右上纬度")
    private BigDecimal upRightLat;

    @ApiModelProperty(value = "所在位置")
    private String resLoc;

    @ApiModelProperty(value = "水库类型")
    private Integer resType;

    @ApiModelProperty(value = "工程等别")
    private Integer engGrad;

    @ApiModelProperty(value = "工程规模")
    private Integer engScal;

    @ApiModelProperty(value = "汛限水位")
    private BigDecimal flLowLimLev;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;


    public String getResKey() {
        return resKey;
    }

    public void setResKey(String resKey) {
        this.resKey = resKey;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public BigDecimal getLowLeftLong() {
        return lowLeftLong;
    }

    public void setLowLeftLong(BigDecimal lowLeftLong) {
        this.lowLeftLong = lowLeftLong;
    }

    public BigDecimal getLowLeftLat() {
        return lowLeftLat;
    }

    public void setLowLeftLat(BigDecimal lowLeftLat) {
        this.lowLeftLat = lowLeftLat;
    }

    public BigDecimal getUpRightLong() {
        return upRightLong;
    }

    public void setUpRightLong(BigDecimal upRightLong) {
        this.upRightLong = upRightLong;
    }

    public BigDecimal getUpRightLat() {
        return upRightLat;
    }

    public void setUpRightLat(BigDecimal upRightLat) {
        this.upRightLat = upRightLat;
    }

    public String getResLoc() {
        return resLoc;
    }

    public void setResLoc(String resLoc) {
        this.resLoc = resLoc;
    }

    public Integer getResType() {
        return resType;
    }

    public void setResType(Integer resType) {
        this.resType = resType;
    }

    public Integer getEngGrad() {
        return engGrad;
    }

    public void setEngGrad(Integer engGrad) {
        this.engGrad = engGrad;
    }

    public Integer getEngScal() {
        return engScal;
    }

    public void setEngScal(Integer engScal) {
        this.engScal = engScal;
    }

    public BigDecimal getFlLowLimLev() {
        return flLowLimLev;
    }

    public void setFlLowLimLev(BigDecimal flLowLimLev) {
        this.flLowLimLev = flLowLimLev;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
