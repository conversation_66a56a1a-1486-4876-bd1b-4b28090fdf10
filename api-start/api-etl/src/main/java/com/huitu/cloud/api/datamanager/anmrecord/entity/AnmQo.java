package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
public class AnmQo extends PageBean {
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "异常数据状态 多个值以逗号隔开" ,example = "1,2,3,4,5,6")
    private String staflgs;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getStaflgs() {
        return staflgs;
    }

    public void setStaflgs(String staflgs) {
        this.staflgs = staflgs;
    }
}
