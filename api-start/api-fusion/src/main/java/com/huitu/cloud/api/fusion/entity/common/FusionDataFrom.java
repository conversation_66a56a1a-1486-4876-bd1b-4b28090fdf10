package com.huitu.cloud.api.fusion.entity.common;

/**
 * 数据来源
 */
public enum FusionDataFrom {
    HYD("1", "水文", "ST_PPTN_R", 1D),
    MTF("2", "山洪", "BSN_YCRAIN_R", 0.5D),
    MET("3", "气象", "BSN_YCRAIN_QX_R", 0.5D),
    OTHER("X", "其他", "BSN_YCRAIN_R", 0.5D);

    /**
     * 代码
     */
    private String code;
    /**
     * 别名
     */
    private String alias;
    /**
     * 数据表
     */
    private String table;
    /**
     * 时段长
     */
    private Double intv;

    FusionDataFrom(String code, String alias, String table, Double intv) {
        this.code = code;
        this.alias = alias;
        this.table = table;
        this.intv = intv;
    }

    public String getCode() {
        return code;
    }

    public String getAlias() {
        return alias;
    }

    public String getTable() {
        return table;
    }

    public Double getIntv() {
        return intv;
    }

    /**
     * 判断代码是否相等
     *
     * @param code 代码
     * @return true: 相等, false: 不相等
     */
    public boolean equalsCode(String code) {
        return this.code.equalsIgnoreCase(code);
    }

    /**
     * 根据代码，获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static FusionDataFrom get(String code) {
        if (null != code) {
            FusionDataFrom[] values = values();
            for (FusionDataFrom value : values) {
                if (value.equalsCode(code)) {
                    return value;
                }
            }
        }
        return FusionDataFrom.OTHER;
    }
}
