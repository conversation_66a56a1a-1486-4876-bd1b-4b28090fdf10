<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.device.mapper.VdstInfoDao">

    <select id="getVdstInfoList" resultType="com.huitu.cloud.api.ewci.device.entity.VdstInfo">
        SELECT
            a.adcd,
            a.VDSTCD,
            a.VDSTNM,
            ad.adnm,
            a.VDSTTP,
            a.ADDRESS,
            a.ESTABLISHMENTTIME,
            a.QJBD,
            a.CHARGEBD,
            a.QJAR,
            a.DCAR,
            a.SOLARENERGYAR,
            a.ROUTERAR,
            a.CHARGEAR,
            a.SPDAR,
            a.RAINPROOFAR,
            a.VERTICALPOLEAR,
            a.BASICRENOVATIONAR,
            a.MODITIME,
            a.USFL,
            h.status,
            a.admauth
        FROM
          BSN_VDSTINFO_B a
        LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = a.adcd
        LEFT JOIN BSN_VDSTINFO_B_HISTORY h ON h.VDSTCD = a.VDSTCD and (h.status = '1' or h.status = '2')
        WHERE
        1=1
        <if test="map.oLevel != '2'.toString()">
            AND (A.ADMAUTH != '省水旱中心' OR A.ADMAUTH IS NULL)
        </if>
        <if test="map.admauth!=null and map.admauth!= ''">
            and CHARINDEX(#{map.admauth},a.admauth) >0
        </if>
        <if test="map.adcd != null and map.adcd !=''">
            and left(ad.adcd,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(ad.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.usfl!=null and map.usfl!= ''">
            and a.usfl=#{map.usfl}
        </if>
        <if test="map.vdsttp !=null and  map.vdsttp.size() >0 ">
            and a.VDSTTP in
            <foreach collection="map.vdsttp" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="map.vdstnm !=null and map.vdstnm !=''">
            and A.VDSTNM like '%${map.vdstnm}%'
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '1'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.QJAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '1'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.QJAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '2'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.DCAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '2'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.DCAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '3'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.SOLARENERGYAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '3'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.SOLARENERGYAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '4'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.ROUTERAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '4'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.ROUTERAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '5'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.CHARGEAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '5'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.CHARGEAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '6'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.SPDAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '6'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.SPDAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '7'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.RAINPROOFAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '7'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.RAINPROOFAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '8'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.VERTICALPOLEAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '8'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.VERTICALPOLEAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '9'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.BASICRENOVATIONAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '9'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.BASICRENOVATIONAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        ORDER BY
        a.MODITIME DESC, a.USFL DESC
    </select>
    <select id="getVdstPendingReviewList" resultType="com.huitu.cloud.api.ewci.device.entity.VdstPPHistoryInfo">
        SELECT
        a.id,
        a.adcd,
        a.VDSTCD,
        a.VDSTNM,
        ad.adnm,
        a.VDSTTP,
        a.ADDRESS,
        a.ESTABLISHMENTTIME,
        a.QJBD,
        a.CHARGEBD,
        a.QJAR,
        a.DCAR,
        a.SOLARENERGYAR,
        a.ROUTERAR,
        a.CHARGEAR,
        a.SPDAR,
        a.RAINPROOFAR,
        a.VERTICALPOLEAR,
        a.BASICRENOVATIONAR,
        a.MODITIME,
        a.USFL,
        a.status,
        a.audreason,
        a.admauth
        FROM
        BSN_VDSTINFO_B_HISTORY a
        LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = a.adcd
        WHERE
        1=1
        <if test="map.oLevel != '2'.toString()">
            AND (A.ADMAUTH != '省水旱中心' OR A.ADMAUTH IS NULL)
        </if>
        <if test="map.admauth!=null and map.admauth!= ''">
            and CHARINDEX(#{map.admauth},a.admauth) >0
        </if>
        <if test="map.adcd != null and map.adcd !=''">
            and left(ad.adcd,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(ad.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.usfl!=null and map.usfl!= ''">
            and a.usfl=#{map.usfl}
        </if>
        <if test="map.status !=null and map.status !=''">
            and CHARINDEX(a.status,#{map.status}) > 0
        </if>
        <if test="map.vdsttp !=null and  map.vdsttp.size() >0 ">
            and a.VDSTTP in
            <foreach collection="map.vdsttp" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="map.vdstnm !=null and map.vdstnm !=''">
            and a.vdstnm like '%${map.vdstnm}%'
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '1'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.QJAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '1'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.QJAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '2'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.DCAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '2'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.DCAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '3'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.SOLARENERGYAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '3'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.SOLARENERGYAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '4'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.ROUTERAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '4'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.ROUTERAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '5'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.CHARGEAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '5'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.CHARGEAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '6'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.SPDAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '6'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.SPDAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '7'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.RAINPROOFAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '7'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.RAINPROOFAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '8'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.VERTICALPOLEAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '8'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.VERTICALPOLEAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '9'.toString() and map.startYear!=null and map.startYear!= ''">
            AND a.BASICRENOVATIONAR &gt;= #{map.startYear}
        </if>
        <if test="map.videoType!=null and map.videoType!= '' and map.videoType == '9'.toString() and map.endYear != null and map.endYear!= '' ">
            and a.BASICRENOVATIONAR &lt; CAST((CAST(#{map.endYear} as int) + 1) as VARCHAR)
        </if>
        ORDER BY
        a.MODITIME DESC
    </select>
    <select id="getVdstAuditList" resultType="com.huitu.cloud.api.ewci.device.entity.VdstInfoHistory">
        select
            '0' as ID,
            b.VDSTCD,
            ad.adnm,
            b.VDSTNM,
            b.LGTD,
            b.LTTD,
            b.VDSTTP,
            b.SERVICENM,
            b.STCD,
            b.ADCD,
            b.VDIP,
            b.VDPORT,
            b.VDUSER,
            b.VDPWD,
            b.VDMGER,
            b.VDMTEL,
            b.UDTM,
            b.NT,
            b.ENNMCD,
            b.LKTP,
            b.ADDRESS,
            b.ESTABLISHMENTTIME,
            b.USFL,
            b.QJBD,
            b.QJAR,
            b.DCAR,
            b.SOLARENERGYAR,
            b.ROUTERAR,
            b.CHARGEBD,
            b.CHARGEAR,
            b.SPDAR,
            b.RAINPROOFAR,
            b.VERTICALPOLEAR,
            b.BASICRENOVATIONAR,
            b.MODITIME,
            b.SOURCEID,
            b.CREATOR,
            b.CREATED,
            b.AUDITED,
            b.AUDITOR,
            '' as AUDREASON,
            '' as STATUS, b.admauth from BSN_VDSTINFO_B b
                                  LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = b.adcd
        where b.VDSTCD = #{vdstcd} UNION ALL
        select
            a.ID,
            a.VDSTCD,
            ad.adnm,
            a.VDSTNM,
            a.LGTD,
            a.LTTD,
            a.VDSTTP,
            a.SERVICENM,
            a.STCD,
            a.ADCD,
            a.VDIP,
            a.VDPORT,
            a.VDUSER,
            a.VDPWD,
            a.VDMGER,
            a.VDMTEL,
            a.UDTM,
            a.NT,
            a.ENNMCD,
            a.LKTP,
            a.ADDRESS,
            a.ESTABLISHMENTTIME,
            a.USFL,
            a.QJBD,
            a.QJAR,
            a.DCAR,
            a.SOLARENERGYAR,
            a.ROUTERAR,
            a.CHARGEBD,
            a.CHARGEAR,
            a.SPDAR,
            a.RAINPROOFAR,
            a.VERTICALPOLEAR,
            a.BASICRENOVATIONAR,
            a.MODITIME,
            a.SOURCEID,
            a.CREATOR,
            a.CREATED,
            a.AUDITED,
            a.AUDITOR,
            a.AUDREASON,
            a.STATUS, a.admauth from BSN_VDSTINFO_B_HISTORY a
                              LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = a.adcd
        where a.VDSTCD = #{vdstcd}  and a.STATUS = '1'
    </select>
    <select id="getVdstHistoryList" resultType="com.huitu.cloud.api.ewci.device.entity.VdstInfoHistory">
        select res.* from (select
                               '0' as ID,
                               b.VDSTCD,
                               ad.adnm,
                               b.VDSTNM,
                               b.LGTD,
                               b.LTTD,
                               b.VDSTTP,
                               b.SERVICENM,
                               b.STCD,
                               b.ADCD,
                               b.VDIP,
                               b.VDPORT,
                               b.VDUSER,
                               b.VDPWD,
                               b.VDMGER,
                               b.VDMTEL,
                               b.UDTM,
                               b.NT,
                               b.ENNMCD,
                               b.LKTP,
                               b.ADDRESS,
                               b.ESTABLISHMENTTIME,
                               b.USFL,
                               b.QJBD,
                               b.QJAR,
                               b.DCAR,
                               b.SOLARENERGYAR,
                               b.ROUTERAR,
                               b.CHARGEBD,
                               b.CHARGEAR,
                               b.SPDAR,
                               b.RAINPROOFAR,
                               b.VERTICALPOLEAR,
                               b.BASICRENOVATIONAR,
                               b.MODITIME,
                               b.SOURCEID,
                               b.CREATOR,
                               b.CREATED,
                               b.AUDITED,
                               b.AUDITOR,
                               '' as AUDREASON,
                               '' as STATUS, b.admauth from BSN_VDSTINFO_B b
                                                     LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = b.adcd
                           where b.VDSTCD = #{vdstcd} UNION ALL
                           select
                               a.ID,
                               a.VDSTCD,
                               ad.adnm,
                               a.VDSTNM,
                               a.LGTD,
                               a.LTTD,
                               a.VDSTTP,
                               a.SERVICENM,
                               a.STCD,
                               a.ADCD,
                               a.VDIP,
                               a.VDPORT,
                               a.VDUSER,
                               a.VDPWD,
                               a.VDMGER,
                               a.VDMTEL,
                               a.UDTM,
                               a.NT,
                               a.ENNMCD,
                               a.LKTP,
                               a.ADDRESS,
                               a.ESTABLISHMENTTIME,
                               a.USFL,
                               a.QJBD,
                               a.QJAR,
                               a.DCAR,
                               a.SOLARENERGYAR,
                               a.ROUTERAR,
                               a.CHARGEBD,
                               a.CHARGEAR,
                               a.SPDAR,
                               a.RAINPROOFAR,
                               a.VERTICALPOLEAR,
                               a.BASICRENOVATIONAR,
                               a.MODITIME,
                               a.SOURCEID,
                               a.CREATOR,
                               a.CREATED,
                               a.AUDITED,
                               a.AUDITOR,
                               a.AUDREASON,
                               a.STATUS, a.admauth from BSN_VDSTINFO_B_HISTORY a
                                                 LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = a.adcd
                           where a.VDSTCD = #{vdstcd}  and a.STATUS = '0'

                          ) res order by res.MODITIME desc
    </select>

    <!--    视频监控站信息统计-->
    <select id="getVdstCountList" resultType="com.huitu.cloud.api.ewci.device.entity.DeviceStatistics">
        SELECT
        AA.ADCD,
        AA.ADNM,
        COUNT ( BB.ADCD ) AS COUNT
        FROM
        ( SELECT A.ADCD, A.ADNM, A.padcd, A.adlvl FROM BSN_ADCD_B A WHERE
        LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        AND CHARINDEX(CAST(A.adlvl AS VARCHAR), #{map.adlvl}) > 0
        <if test="map.level !=null and map.level =='4'.toString()">
            and left(A.ADCD,6) != '220581'
        </if>
        ) AA
        LEFT JOIN (
        SELECT
        <if test="map.level !=null and map.level =='2'.toString()">
            CONCAT ( LEFT ( A.ADCD, 4 ), '00000000000' ) AS adcd
        </if>
        <if test="map.level !=null and map.level !='2'.toString()">
            CONCAT ( LEFT ( A.ADCD, 6 ), '000000000' ) AS adcd
        </if>
        FROM
        BSN_VDSTINFO_B a
        LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = a.adcd
        LEFT JOIN BSN_VDSTINFO_B_HISTORY h ON h.VDSTCD = a.VDSTCD and (h.status = '1' or h.status = '2')
        WHERE
        A.ADCD NOT LIKE '220581%'
        UNION ALL
        SELECT
        CONCAT ( LEFT ( A.ADCD, 6 ), '000000000' ) AS adcd
        FROM
        BSN_VDSTINFO_B a
        LEFT JOIN BSN_ADCD_B ad ON ad.ADCD = a.adcd
        LEFT JOIN BSN_VDSTINFO_B_HISTORY h ON h.VDSTCD = a.VDSTCD and (h.status = '1' or h.status = '2')
        WHERE
        A.ADCD LIKE '220581%'
        ) BB ON
        LEFT( AA.ADCD, 6) = LEFT ( BB.ADCD, 6 )
        GROUP BY
        AA.ADCD,
        AA.ADNM
        ORDER BY
        AA.ADCD
    </select>

    <delete id="deleteId">
        delete
        from BSN_VDSTINFO_B_HISTORY
        where ID = #{id}
    </delete>

</mapper>
