package com.huitu.cloud.api.syq.video.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 视频站信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-26
 */
@ApiModel(value="BsnVdstinfoB对象", description="视频站信息")
public class BsnVdstinfo  implements Serializable {
    @ApiModelProperty(value = "视频站编码")
    private String vdstCd;
    @ApiModelProperty(value = "视频站名称")
    private String vdstNm;
    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;
    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;
    @ApiModelProperty(value = "类型")
    private String vdstTp;
    @ApiModelProperty(value = "服务商名称")
    private String serviceNm;
    @ApiModelProperty(value = "关联测站编码")
    private String stcd;
    @ApiModelProperty(value = "行政区划码")
    private String adcd;
    @ApiModelProperty(value = "视频IP地址")
    private String vdIp;
    @ApiModelProperty(value = "视频端口")
    private String vdport;
    @ApiModelProperty(value = "视频用户名")
    private String vduser;
    @ApiModelProperty(value = "视频密码")
    private String vdpwd;
    @ApiModelProperty(value = "测站管理员")
    private String vdmger;
    @ApiModelProperty(value = "管理员电话")
    private String vdmtel;
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime udtm;
    @ApiModelProperty(value = "备注")
    private String nt;
    @ApiModelProperty(value = "工程编码")
    private String  ennmcd;
    @ApiModelProperty(value = "连接类型 1、3G 2、4G 3、专线")
    private String lktp;

    @ApiModelProperty(value = "视频监视设备集合")
    private List<BsnCcdinfo> equipments;
    @ApiModelProperty(value = "行政区名称")
    private String adnm;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "站类")
    private String sttp;
    @ApiModelProperty(value = "排序")
    private int sort;

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public List<BsnCcdinfo> getEquipments() {
        return equipments;
    }

    public void setEquipments(List<BsnCcdinfo> equipments) {
        this.equipments = equipments;
    }

    public String getVdstCd() {
        return vdstCd;
    }

    public void setVdstCd(String vdstCd) {
        this.vdstCd = vdstCd;
    }

    public String getVdstNm() {
        return vdstNm;
    }

    public void setVdstNm(String vdstNm) {
        this.vdstNm = vdstNm;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getVdstTp() {
        return vdstTp;
    }

    public void setVdstTp(String vdstTp) {
        this.vdstTp = vdstTp;
    }

    public String getServiceNm() {
        return serviceNm;
    }

    public void setServiceNm(String serviceNm) {
        this.serviceNm = serviceNm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getVdIp() {
        return vdIp;
    }

    public void setVdIp(String vdIp) {
        this.vdIp = vdIp;
    }

    public String getVdport() {
        return vdport;
    }

    public void setVdport(String vdport) {
        this.vdport = vdport;
    }

    public String getVduser() {
        return vduser;
    }

    public void setVduser(String vduser) {
        this.vduser = vduser;
    }

    public String getVdpwd() {
        return vdpwd;
    }

    public void setVdpwd(String vdpwd) {
        this.vdpwd = vdpwd;
    }

    public String getVdmger() {
        return vdmger;
    }

    public void setVdmger(String vdmger) {
        this.vdmger = vdmger;
    }

    public String getVdmtel() {
        return vdmtel;
    }

    public void setVdmtel(String vdmtel) {
        this.vdmtel = vdmtel;
    }

    public LocalDateTime getUdtm() {
        return udtm;
    }

    public void setUdtm(LocalDateTime udtm) {
        this.udtm = udtm;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }

    public String getEnnmcd() {
        return ennmcd;
    }

    public void setEnnmcd(String ennmcd) {
        this.ennmcd = ennmcd;
    }

    public String getLktp() {
        return lktp;
    }

    public void setLktp(String lktp) {
        this.lktp = lktp;
    }
}
