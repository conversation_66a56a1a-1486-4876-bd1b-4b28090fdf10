package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 洪水来源
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@TableName("DicHongShuiLaiYuan")
@ApiModel(value = "DicHongShuiLaiYuan对象", description = "洪水来源")
public class DicHongShuiLaiYuan extends Model<DicHongShuiLaiYuan> {

    @ApiModelProperty(value = "排序号")
    private Integer sortid;

    @ApiModelProperty(value = "洪水来源编码")
    private String hongshuilaiyuanid;

    @ApiModelProperty(value = "洪水来源名称")
    private String hongshuilaiyuanmingcheng;

    public Integer getSortid() {
        return sortid;
    }

    public void setSortid(Integer sortid) {
        this.sortid = sortid;
    }

    public String getHongshuilaiyuanid() {
        return hongshuilaiyuanid;
    }

    public void setHongshuilaiyuanid(String hongshuilaiyuanid) {
        this.hongshuilaiyuanid = hongshuilaiyuanid;
    }

    public String getHongshuilaiyuanmingcheng() {
        return hongshuilaiyuanmingcheng;
    }

    public void setHongshuilaiyuanmingcheng(String hongshuilaiyuanmingcheng) {
        this.hongshuilaiyuanmingcheng = hongshuilaiyuanmingcheng;
    }
}
