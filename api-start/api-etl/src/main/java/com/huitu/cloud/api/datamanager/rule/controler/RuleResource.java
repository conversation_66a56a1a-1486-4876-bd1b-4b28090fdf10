package com.huitu.cloud.api.datamanager.rule.controler;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.datamanager.rule.entity.*;
import com.huitu.cloud.api.datamanager.rule.service.RuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 雨量规则配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-06
 */

@RestController
@Api(tags = "异常数据过滤规则")
@RequestMapping("/api/datamanger/rule")
    public class RuleResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
            return "B661C064-1554-4164-80CA-B52A17E42140";
            }
    @Override
    public String getVersion() {
            return "1.0";
            }
    @Autowired
    private RuleService baseService;

    @ApiOperation(value = "雨量规则修改",notes="雨量规则修改")
    @PostMapping(value = "rain-update")
    public ResponseEntity<SuccessResponse<String>> updateRainrule(@RequestBody BsnRainruleb entity) throws Exception {
            Boolean flag = baseService.updateById(entity);
            return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
     }
    @ApiOperation(value = "雨量规则添加",notes="新增一条雨量信息")
    @PostMapping(value = "rain-add")
    public ResponseEntity<SuccessResponse<String>> addRainrule(@RequestBody BsnRainruleb entity) throws Exception {
        Boolean flag = baseService.save(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }
    @ApiOperation(value = "雨量规则一键设置添加",notes="雨量规则一键设置添加")
    @PostMapping(value = "rain-add-all")
    public ResponseEntity<SuccessResponse<String>> addAllRainrule(@RequestBody RainruleEx entity) throws Exception {
        Boolean flag = baseService.saveAll(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }
    @ApiOperation(value = "雨量规则删除",notes="雨量规则删除")
    @GetMapping(value = "rain-delete")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码",  required = true,dataType = "String")
    })
    public ResponseEntity<SuccessResponse<String>> delRainrule(@RequestParam String stcd) throws Exception {
        Boolean flag = baseService.del(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }
    @ApiOperation(value = "雨量规则查询",notes="雨量规则查询")
    @GetMapping(value = "select-rain-rule")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stnm", value = "测站名称",  dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<IPage<RainRuleVo>>> getRainrule(@RequestParam String stnm,@RequestParam String adcd,@RequestParam int pageNum,@RequestParam int pageSize) throws Exception {
            IPage<RainRuleVo> page = baseService.getRainRule(stnm,adcd,pageNum,pageSize);
            return ResponseEntity.ok(
            new SuccessResponse(this, "OK", page));
     }
    @ApiOperation(value = "水位规则修改",notes="水位规则修改")
    @PostMapping(value = "zr-update")
    public ResponseEntity<SuccessResponse<String>> updateZrrule(@RequestBody BsnZrruleb entity) throws Exception {
        Boolean flag = baseService.updateZrById(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }
    @ApiOperation(value = "水位规则添加",notes="新增一条水位规则信息")
    @PostMapping(value = "zr-add")
    public ResponseEntity<SuccessResponse<String>> addZrrule(@RequestBody BsnZrruleb entity) throws Exception {
        Boolean flag = baseService.saveZr(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }
    @ApiOperation(value = "水位规则删除",notes="水位规则删除")
    @GetMapping(value = "zr-delete")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码",  required = true,dataType = "String")
    })
    public ResponseEntity<SuccessResponse<String>> delZrrule(@RequestParam String stcd) throws Exception {
        Boolean flag = baseService.delZr(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }
    @ApiOperation(value = "水位规则查询",notes="水位规则查询")
    @PostMapping(value = "select-zr-rule")
    public ResponseEntity<SuccessResponse<IPage<ZrruleVo>>> getZrrule(@Validated @RequestBody ZrRuleQo zrRuleQo) throws Exception {
        IPage<ZrruleVo> page = baseService.getZrRule(zrRuleQo.getSttp(),zrRuleQo.getStnm(),zrRuleQo.getAdcd(),zrRuleQo.getPageNum(),zrRuleQo.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", page));
    }
    @ApiOperation(value = "导出雨量规则数据",notes="导出雨量规则数据")
    @GetMapping(value = "export-rain-rule")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stnm", value = "测站名称",  dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String")
    })
    public void exportRainrule(@RequestParam String stnm, @RequestParam String adcd, HttpServletResponse response) throws Exception {
        baseService.exportRainRule(stnm,adcd,response);

    }
    @ApiOperation(value = "导出水位规则规则数据",notes="导出水位规则规则数据")
    @GetMapping(value = "export-zr-rule")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stnm", value = "测站名称",  dataType = "String"),
            @ApiImplicitParam(name = "sttp", value = "测站类型  ZZ:河道  RR:水库",  dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String")
    })
    public void exportZrnrule(@RequestParam String stnm, @RequestParam String sttp,String adcd, HttpServletResponse response) throws Exception {
        baseService.exportZrRule(sttp,stnm,adcd,response);

    }
    @ApiOperation(value = "根据测站编码校验测站否配置雨量规则",notes="根据测站编码校验测站的雨量规则是否配置( 返回结果 1:存在  0：代表不存在)")
    @GetMapping(value = "check-rain-rule")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码",  required = true,dataType = "String")
    })
    public ResponseEntity<SuccessResponse<String>> checkRainRuleIsExist(@RequestParam String stcd) throws Exception {
       String result=baseService.checkRainRuleIsExist(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }
    @ApiOperation(value = "根据测站编码校验水位规则是否配置",notes="根据测站编码校验水位规则是否配置( 返回结果 1:存在  0：代表不存在)")
    @GetMapping(value = "check-zr-rule")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码",  required = true,dataType = "String")
    })
    public ResponseEntity<SuccessResponse<String>> checkZrRuleIsExist(@RequestParam String stcd) throws Exception {
        String result=baseService.checkZrRuleIsExist(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }
}







