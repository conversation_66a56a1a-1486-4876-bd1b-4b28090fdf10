package com.huitu.cloud.api.ewci.tencent.api;


import com.dtflys.forest.annotation.*;
import com.huitu.cloud.api.ewci.tencent.entity.SendMsgQo;

@BaseRequest(baseURL = "{webSocketServiceAddr}")
public interface WebSocketService {

    /**
     * 发送Scoket消息，调用 WebSocketAPI 接口
     *
     * @param request
     * @return
     */
    @Post(url = "/api/msg/send", contentType = "application/json")
    String sendMsg(@Body SendMsgQo request);
}
