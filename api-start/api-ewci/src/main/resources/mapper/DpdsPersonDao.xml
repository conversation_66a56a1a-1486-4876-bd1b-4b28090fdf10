<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.DpdsPersonDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.person.entity.DpdsPerson">
        SELECT ROW_NUMBER() OVER(ORDER BY T.ADCD, T.DPDS_NAME ASC) SORTNO, A.ADNM,T.ADCD,T.DPDS_CODE, T.DPDS_NAME, T.PROBLEM,T.MEASURE,
        P1.REALNM XZ_REALNM, P1.MOBILE XZ_MOBILE,P1.TS, P2.REALNM JS_REALNM, P2.MOBILE JS_MOBILE,
        COALESCE(P1.TS,P2.TS) TS
        FROM (
        SELECT DISTINCT DPDS_CODE, ADCD, DPDS_NAME, PROBLEM, MEASURE FROM BNS_DPDSPERSON_B
        WHERE LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.pernm != null and map.pernm != ''">
            AND CHARINDEX(#{map.pernm}, REALNM) > 0
        </if>
        <if test="map.udtm !=null and map.udtm =='1'.toString()">
            and YEAR(TS) != YEAR(GETDATE())
        </if>
        ) T
        LEFT JOIN MDT_ADCDINFO_B A ON A.ADCD = LEFT (T.ADCD, 6) + '000000000'
        LEFT JOIN BNS_DPDSPERSON_B P1 ON P1.DPDS_CODE = T.DPDS_CODE AND P1.PERTP = '1'
        LEFT JOIN BNS_DPDSPERSON_B P2 ON P2.DPDS_CODE = T.DPDS_CODE AND P2.PERTP = '2'
        <where>
            <if test="map.dpdsName != null and map.dpdsName != ''">
                AND CHARINDEX(#{map.dpdsName}, T.DPDS_NAME) > 0
            </if>
        </where>
        ORDER BY T.ADCD, T.DPDS_NAME ASC
    </select>

    <select id="batchImport" statementType="CALLABLE" useCache="false"
            resultType="com.huitu.cloud.api.ewci.person.entity.BnsDpdspersonTmp">
        {CALL PROC_IMPORT_DPDS_PERSON(#{batchNo, mode=IN})}
    </select>
    <select id="getDpdsSummaryList" resultType="com.huitu.cloud.api.ewci.person.entity.DpdsSummaryVo">
        SELECT ADCD, ADNM, PADCD, ALLNUM, XZCOUNT, JSCOUNT FROM (
        <if test="map.include == '1'.toString()">
            SELECT 0 SORTNO, A.ADCD ADCD, B.ADNM ADNM, B.PADCD PADCD,A.ALLNUM ,A.XZCOUNT, A.JSCOUNT FROM (
            SELECT #{map.adcd} ADCD,COUNT(DISTINCT A.REALNM )
            ALLNUM,
            COUNT(DISTINCT B.REALNM ) XZCOUNT,
            COUNT(DISTINCT C.REALNM ) JSCOUNT
            FROM BNS_DPDSPERSON_B A
            LEFT JOIN BNS_DPDSPERSON_B B ON A.ADCD = B.ADCD AND A.DPDS_CODE = B.DPDS_CODE AND A.DPDS_NAME = B.DPDS_NAME
            AND B.PERTP = '1'
            LEFT JOIN BNS_DPDSPERSON_B C ON A.ADCD = C.ADCD AND A.DPDS_CODE = C.DPDS_CODE AND A.DPDS_NAME = C.DPDS_NAME
            AND C.PERTP = '2'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">
                AND LEFT(A.ADCD, 6) NOT IN ('220581')
            </if>
            ) A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
            WHERE EXISTS (SELECT 8 FROM MDT_ADCDINFO_E WHERE ADTP = '1' AND ADCD = A.ADCD)
        </if>
        <if test="map.include == '1'.toString() and map.level &lt;= 4">
            UNION ALL
        </if>
        <if test="map.level &lt;= 4">
            SELECT B.SORTNO, A.ADCD ADCD, A.ADNM ADNM, A.PADCD PADCD,C.ALLNUM ,C.XZCOUNT, C.JSCOUNT
            FROM MDT_ADCDINFO_B A
            LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
            LEFT JOIN (SELECT ADCD, COUNT(DISTINCT AREALNM ) ALLNUM,
            COUNT(DISTINCT BREALNM ) XZCOUNT,
            COUNT(DISTINCT CREALNM ) JSCOUNT
            FROM (SELECT LEFT(LEFT(A.ADCD, #{map.lowLevel}) + '000000000000000', 15) ADCD, A.MOBILE AMOBILE, A.REALNM
            AREALNM,
            B.MOBILE BMOBILE, B.REALNM BREALNM, C.MOBILE CMOBILE, C.REALNM CREALNM
            FROM BNS_DPDSPERSON_B A
            LEFT JOIN BNS_DPDSPERSON_B B ON A.ADCD = B.ADCD AND A.DPDS_CODE = B.DPDS_CODE AND A.DPDS_NAME = B.DPDS_NAME
            AND B.PERTP = '1'
            LEFT JOIN BNS_DPDSPERSON_B C ON A.ADCD = C.ADCD AND A.DPDS_CODE = C.DPDS_CODE AND A.DPDS_NAME = C.DPDS_NAME
            AND C.PERTP = '2'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND LEFT(A.ADCD, 6) NOT IN ('220581')
            <if test="map.level == '2'.toString()">
                UNION ALL
                SELECT '220581000000000' ADCD, A.MOBILE AMOBILE, A.REALNM AREALNM,
                B.MOBILE BMOBILE, B.REALNM BREALNM, C.MOBILE CMOBILE, C.REALNM CREALNM
                FROM BNS_DPDSPERSON_B A
                LEFT JOIN BNS_DPDSPERSON_B B ON A.ADCD = B.ADCD AND A.DPDS_CODE = B.DPDS_CODE AND A.DPDS_NAME =
                B.DPDS_NAME AND B.PERTP = '1'
                LEFT JOIN BNS_DPDSPERSON_B C ON A.ADCD = C.ADCD AND A.DPDS_CODE = C.DPDS_CODE AND A.DPDS_NAME =
                C.DPDS_NAME AND C.PERTP = '2'
                WHERE LEFT(A.ADCD, 6) = '220581'
            </if>
            ) A GROUP BY ADCD) C ON C.ADCD = A.ADCD
            WHERE A.PADCD = #{map.adcd}
        </if>
        ) A
        ORDER BY SORTNO ASC
    </select>
    <update id="updateDpds">
        UPDATE
            BNS_DPDSPERSON_B
        SET
            ADCD =#{map.adcd},
            DPDS_NAME =#{map.dpdsName},
            PROBLEM =#{map.problem},
            MEASURE =#{map.measure},
            REALNM =#{map.realnm},
            MOBILE =#{map.mobile},
            TS = GETDATE()
        WHERE
            DPDS_CODE =#{map.dpdsCode}
          and PERTP = #{map.pertp}
    </update>
    <delete id="deleteDpds">
        delete from BNS_DPDSPERSON_B where DPDS_CODE =#{dpdsCode}
    </delete>
    <insert id="insertDpds">
        insert into BNS_DPDSPERSON_B(
        ADCD,
        DPDS_CODE,
        DPDS_NAME,
        MEASURE,
        MOBILE,
        PERTP,
        PROBLEM,
        REALNM,
        TS)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.adcd}
            ,  #{item.dpdsCode}
            ,  #{item.dpdsName}
            ,  #{item.measure}
            ,  #{item.mobile}
            ,  #{item.pertp}
            ,  #{item.problem}
            ,  #{item.realnm}
            ,  GETDATE())
        </foreach>
    </insert>
</mapper>
