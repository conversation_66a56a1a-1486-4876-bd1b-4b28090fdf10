<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwSnapshotIndexDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getSnapshotIndexList" resultType="com.huitu.cloud.api.ew.entity.response.EwSnapshotIndexResponse">
        SELECT WARN_ID,
               WARN_GRADE_ID,
               A.ADCD,
               B.ADNM,
               WSCD,
               LWATER,
               STDT,
               DRPT
        FROM EW_SNAPSHOT_INDEX A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
        WHERE WARN_ID = #{warnId}
        ORDER BY WARN_GRADE_ID ASC
    </select>
    <select id="getSnapshotIndex" resultType="com.huitu.cloud.api.ew.entity.response.EwSnapshotIndexResponse">
        SELECT WARN_ID,
               WARN_GRADE_ID,
               A.ADCD,
               B.ADNM,
               WSCD,
               LWATER,
               STDT,
               DRPT
        FROM EW_SNAPSHOT_INDEX A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
        WHERE WARN_ID = #{warnId}
          AND WARN_GRADE_ID = #{warnGradeId}
    </select>
    <select id="getVillageList" resultType="com.huitu.cloud.api.ew.entity.response.EwShVillageDataResponse">
        SELECT A.ADCD,
               XZ.ADNM                                                      XZNM,
               XZC.ADNM                                                     XZCNM,
               (CASE WHEN ZRC.ADCD != XZC.ADCD THEN ZRC.ADNM ELSE NULL END) ZRCNM,
               LWATER,
               STDT,
               DRPT,
               MODEL
        FROM EW_SNAPSHOT_INDEX A
                 LEFT JOIN MDT_ADCDINFO_B XZ ON XZ.ADCD = LEFT(A.ADCD, 9) + '000000'
                 LEFT JOIN MDT_ADCDINFO_B XZC ON XZC.ADCD = LEFT(A.ADCD, 12) + '000'
                 LEFT JOIN MDT_ADCDINFO_B ZRC ON ZRC.ADCD = A.ADCD
        WHERE EXISTS(SELECT 8
                     FROM EW_REL_WARNING_MESSAGE
                     WHERE MSG_ID = #{msgId}
                       AND WARN_ID = A.WARN_ID
                       AND GRADE_ID = A.WARN_GRADE_ID)
        ORDER BY A.ADCD ASC
    </select>
</mapper>