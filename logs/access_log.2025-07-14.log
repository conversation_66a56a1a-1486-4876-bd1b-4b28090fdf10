************ - - [14/Jul/2025:08:48:37 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [14/Jul/2025:08:48:37 +0800] "GET /login HTTP/1.1" 302 -
************ - - [14/Jul/2025:08:48:59 +0800] "GET /login?code=Wz7rcf&state=Z2DQQA HTTP/1.1" 302 -
************ - - [14/Jul/2025:08:49:00 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [14/Jul/2025:08:49:01 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1752454141850 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:05 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1752454141850 HTTP/1.1" 200 552
************ - - [14/Jul/2025:08:49:05 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1752454145926 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1752454145926 HTTP/1.1" 200 61649
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1752454146028 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1752454146028 HTTP/1.1" 200 10388
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752454146068 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752454146068 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752454146444 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+08:49:06&etm=&_timer304=1752454146445 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752454146445 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752454146445 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+09:00&filterCnt=6&_timer304=1752454146445 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752454146445 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:06 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752454146444 HTTP/1.1" 200 1482
************ - - [14/Jul/2025:08:49:06 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752454146445 HTTP/1.1" 200 166
************ - - [14/Jul/2025:08:49:06 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:08:49:06 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:08:49:06 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+08:49:06&etm=&_timer304=1752454146445 HTTP/1.1" 200 156
************ - - [14/Jul/2025:08:49:06 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:08:49:06 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+09:00&filterCnt=6&_timer304=1752454146445 HTTP/1.1" 200 164
************ - - [14/Jul/2025:08:49:06 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:08:49:06 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752454146445 HTTP/1.1" 200 169
************ - - [14/Jul/2025:08:49:06 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752454146445 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752454147752 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/base/saas/token?_timer304=1752454147752 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752454147776 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:07 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [14/Jul/2025:08:49:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:08:49:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:08:49:09 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [14/Jul/2025:08:49:10 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752454147776 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [14/Jul/2025:08:49:10 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [14/Jul/2025:08:49:10 +0800] "GET /api/base/saas/token?_timer304=1752454147752 HTTP/1.1" 200 411
************ - - [14/Jul/2025:08:49:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752454151789 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454151790 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454151790 HTTP/1.1" 200 159
************ - - [14/Jul/2025:08:49:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752454151789 HTTP/1.1" 200 160
************ - - [14/Jul/2025:08:49:13 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752454147752 HTTP/1.1" 200 413
************ - - [14/Jul/2025:08:49:19 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+08:49:19&etm=&_timer304=1752454159404 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:19 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752454159404 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752454159404 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+09:00&filterCnt=6&_timer304=1752454159404 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:19 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752454159404 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752454159404 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:19 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:08:49:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:08:49:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:08:49:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752454159404 HTTP/1.1" 200 166
************ - - [14/Jul/2025:08:49:19 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+08:49:19&etm=&_timer304=1752454159404 HTTP/1.1" 200 156
************ - - [14/Jul/2025:08:49:19 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:08:49:19 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+09:00&filterCnt=6&_timer304=1752454159404 HTTP/1.1" 200 164
************ - - [14/Jul/2025:08:49:19 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752454159404 HTTP/1.1" 200 1482
************ - - [14/Jul/2025:08:49:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752454159404 HTTP/1.1" 200 169
************ - - [14/Jul/2025:08:49:19 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752454159404 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:08:49:20 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752454160040 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:20 +0800] "OPTIONS /api/base/saas/token?_timer304=1752454160040 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:20 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752454160050 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:20 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752454160040 HTTP/1.1" 200 413
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [14/Jul/2025:08:49:20 +0800] "GET /api/base/saas/token?_timer304=1752454160040 HTTP/1.1" 200 411
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [14/Jul/2025:08:49:20 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752454160050 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:08:49:20 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [14/Jul/2025:08:49:24 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1752454164735 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:24 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1752454164735 HTTP/1.1" 200 146
************ - - [14/Jul/2025:08:49:29 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752454169310 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454169319 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:49:29 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752454169310 HTTP/1.1" 200 160
************ - - [14/Jul/2025:08:49:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454169319 HTTP/1.1" 200 159
************ - - [14/Jul/2025:08:54:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752454460206 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:54:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752454460206 HTTP/1.1" 200 160
************ - - [14/Jul/2025:08:54:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454470208 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:54:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454470208 HTTP/1.1" 200 159
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+08:57:06&etm=&_timer304=1752454626508 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752454626508 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752454626508 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+09:00&filterCnt=6&_timer304=1752454626508 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752454626508 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:07 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752454626508 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:09 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+08:57:06&etm=&_timer304=1752454626508 HTTP/1.1" 200 156
************ - - [14/Jul/2025:08:57:09 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:08:57:09 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+09:00&filterCnt=6&_timer304=1752454626508 HTTP/1.1" 200 164
************ - - [14/Jul/2025:08:57:09 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:08:57:09 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752454626508 HTTP/1.1" 200 166
************ - - [14/Jul/2025:08:57:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:08:57:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:08:57:09 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752454626508 HTTP/1.1" 200 169
************ - - [14/Jul/2025:08:57:10 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752454626508 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:08:57:10 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752454626508 HTTP/1.1" 200 1482
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752454632592 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/base/saas/token?_timer304=1752454632592 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752454632603 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:12 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:13 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [14/Jul/2025:08:57:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:08:57:13 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:08:57:14 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [14/Jul/2025:08:57:14 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [14/Jul/2025:08:57:14 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [14/Jul/2025:08:57:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Jul/2025:08:57:14 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752454632603 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:08:57:14 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [14/Jul/2025:08:57:15 +0800] "GET /api/base/saas/token?_timer304=1752454632592 HTTP/1.1" 200 411
************ - - [14/Jul/2025:08:57:15 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [14/Jul/2025:08:57:15 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [14/Jul/2025:08:57:15 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [14/Jul/2025:08:57:15 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [14/Jul/2025:08:57:15 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [14/Jul/2025:08:57:15 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [14/Jul/2025:08:57:16 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752454632592 HTTP/1.1" 200 413
************ - - [14/Jul/2025:08:57:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752454637282 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752454637282 HTTP/1.1" 200 160
************ - - [14/Jul/2025:08:57:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454637720 HTTP/1.1" 200 -
************ - - [14/Jul/2025:08:57:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454637720 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:02:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752454926206 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:02:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752454926206 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:02:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454940203 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:02:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752454940203 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:07:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752455226202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:07:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752455226202 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:07:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752455241198 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:07:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752455241198 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:12:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752455526303 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:12:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752455526303 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:12:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752455542208 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:12:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752455542208 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:17:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752455826197 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:17:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752455826197 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:17:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752455843200 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:17:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752455843200 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:22:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752456126196 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:22:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752456126196 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:22:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752456144202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:22:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752456144202 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:27:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752456445293 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:27:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752456445293 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:27:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752456458202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:27:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752456458202 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:32:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752456746303 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:32:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752456746303 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:33:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752456818197 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:33:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752456818197 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:37:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752457047205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:37:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752457047205 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:38:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752457118195 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:38:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752457118195 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:42:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752457348199 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:42:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752457348199 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:43:28 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752457408490 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:43:28 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752457408490 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:47:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752457649304 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:47:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752457649304 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:47:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752457653966 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:47:33 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752457653966 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:52:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752457950201 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:52:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752457950201 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:53:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752458018198 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:53:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752458018198 HTTP/1.1" 200 160
************ - - [14/Jul/2025:09:57:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752458251197 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:57:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752458251197 HTTP/1.1" 200 159
************ - - [14/Jul/2025:09:58:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752458318314 HTTP/1.1" 200 -
************ - - [14/Jul/2025:09:58:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752458318314 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:02:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752458552335 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:02:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752458552335 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:03:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752458618322 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:03:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752458618322 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:07:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752458853205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:07:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752458853205 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:08:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752458918194 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:08:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752458918194 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:12:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752459154197 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:12:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752459154197 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:13:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752459218207 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:13:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752459218207 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:17:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752459455320 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:17:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752459455320 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:18:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752459518208 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:18:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752459518208 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:22:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752459756195 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:22:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752459756195 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:23:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752459818207 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:23:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752459818207 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:27:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752460057203 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:27:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752460057203 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:28:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752460118199 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:28:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752460118199 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:32:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752460358319 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:32:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752460358319 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:33:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752460418202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:33:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752460418202 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:37:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752460659195 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:37:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752460659195 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:38:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752460718327 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:38:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752460718327 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:42:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752460960200 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:42:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752460960200 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:43:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752461018195 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:43:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752461018195 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:47:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752461261200 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:47:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752461261200 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:48:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752461318196 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:48:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752461318196 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:52:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752461562202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:52:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752461562202 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:53:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752461618334 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:53:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752461618334 HTTP/1.1" 200 160
************ - - [14/Jul/2025:10:57:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752461863205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:57:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752461863205 HTTP/1.1" 200 159
************ - - [14/Jul/2025:10:58:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752461918207 HTTP/1.1" 200 -
************ - - [14/Jul/2025:10:58:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752461918207 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:02:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462164205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:02:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462164205 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:03:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752462218209 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:03:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752462218209 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:07:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462465206 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:07:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462465206 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:08:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752462518330 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:08:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752462518330 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:12:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462766196 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:12:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462766196 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752462804704 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+11:13:24&etm=&_timer304=1752462804704 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752462804704 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752462804705 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+12:00&filterCnt=6&_timer304=1752462804705 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752462804705 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:24 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+11:13:24&etm=&_timer304=1752462804704 HTTP/1.1" 200 156
************ - - [14/Jul/2025:11:13:24 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:11:13:24 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:11:13:24 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752462804704 HTTP/1.1" 200 1482
************ - - [14/Jul/2025:11:13:24 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:11:13:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752462804704 HTTP/1.1" 200 166
************ - - [14/Jul/2025:11:13:25 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+12:00&filterCnt=6&_timer304=1752462804705 HTTP/1.1" 200 164
************ - - [14/Jul/2025:11:13:25 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:11:13:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752462804705 HTTP/1.1" 200 169
************ - - [14/Jul/2025:11:13:25 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752462804705 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752462806715 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/base/saas/token?_timer304=1752462806715 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752462806730 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:26 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [14/Jul/2025:11:13:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:11:13:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:11:13:29 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [14/Jul/2025:11:13:30 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Jul/2025:11:13:30 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [14/Jul/2025:11:13:30 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [14/Jul/2025:11:13:30 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [14/Jul/2025:11:13:30 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [14/Jul/2025:11:13:30 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [14/Jul/2025:11:13:31 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752462806730 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:11:13:31 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [14/Jul/2025:11:13:31 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [14/Jul/2025:11:13:31 +0800] "GET /api/base/saas/token?_timer304=1752462806715 HTTP/1.1" 200 411
************ - - [14/Jul/2025:11:13:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [14/Jul/2025:11:13:32 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [14/Jul/2025:11:13:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752462814475 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462814485 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:13:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462814485 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:13:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752462814475 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:13:36 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752462806715 HTTP/1.1" 200 413
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752462915537 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+11:15:15&etm=&_timer304=1752462915537 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+12:00&filterCnt=6&_timer304=1752462915537 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752462915537 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752462915537 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752462915537 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:15 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:11:15:15 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:11:15:15 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+11:15:15&etm=&_timer304=1752462915537 HTTP/1.1" 200 156
************ - - [14/Jul/2025:11:15:15 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752462915537 HTTP/1.1" 200 166
************ - - [14/Jul/2025:11:15:15 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752462915537 HTTP/1.1" 200 1482
************ - - [14/Jul/2025:11:15:15 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+12:00&filterCnt=6&_timer304=1752462915537 HTTP/1.1" 200 164
************ - - [14/Jul/2025:11:15:15 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:11:15:15 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:11:15:15 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752462915537 HTTP/1.1" 200 169
************ - - [14/Jul/2025:11:15:15 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752462915537 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752462916517 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/base/saas/token?_timer304=1752462916517 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "GET /api/base/saas/token?_timer304=1752462916517 HTTP/1.1" 200 411
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752462916517 HTTP/1.1" 200 413
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752462916575 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [14/Jul/2025:11:15:16 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [14/Jul/2025:11:15:21 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [14/Jul/2025:11:15:22 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [14/Jul/2025:11:15:30 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752462927141 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462927142 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:15:30 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [14/Jul/2025:11:15:45 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [14/Jul/2025:11:15:45 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752462916575 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:11:15:45 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [14/Jul/2025:11:15:45 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [14/Jul/2025:11:15:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752462927141 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:15:45 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [14/Jul/2025:11:15:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752462927142 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:15:45 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Jul/2025:11:15:45 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [14/Jul/2025:11:15:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:11:15:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:11:15:45 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10118
************ - - [14/Jul/2025:11:20:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752463215202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:20:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752463215202 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:20:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752463245309 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:20:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752463245309 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:25:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752463515205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:25:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752463515205 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:25:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752463546322 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:25:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752463546322 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:30:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752463815312 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:30:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752463815312 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:30:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752463847198 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:30:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752463847198 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:35:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752464115194 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:35:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752464115194 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:35:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752464148305 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:35:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752464148305 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:40:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752464415196 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:40:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752464415196 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:40:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752464449328 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:40:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752464449328 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:45:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752464738321 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:45:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752464738321 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:45:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752464750200 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:45:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752464750200 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:50:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752465051197 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:50:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752465051197 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:51:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752465098331 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:51:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752465098331 HTTP/1.1" 200 160
************ - - [14/Jul/2025:11:55:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752465352206 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:55:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752465352206 HTTP/1.1" 200 159
************ - - [14/Jul/2025:11:56:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752465398201 HTTP/1.1" 200 -
************ - - [14/Jul/2025:11:56:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752465398201 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:00:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752465653206 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:00:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752465653206 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:01:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752465698197 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:01:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752465698197 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:05:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752465954196 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:05:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752465954196 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:06:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752465998330 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:06:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752465998330 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:10:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752466255198 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:10:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752466255198 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:11:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752466298203 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:11:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752466298203 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:15:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752466556198 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:15:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752466556198 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:16:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752466598201 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:16:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752466598201 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:20:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752466857207 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:20:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752466857207 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:21:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752466898197 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:21:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752466898197 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:25:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752467158311 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:25:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752467158311 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:26:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752467198201 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:26:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752467198201 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:30:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752467459194 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:30:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752467459194 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:31:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752467498201 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:31:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752467498201 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:36:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752467760320 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:36:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752467760320 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:36:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752467798199 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:36:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752467798199 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:41:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752468061307 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:41:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752468061307 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:41:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752468098205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:41:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752468098205 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:46:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752468362207 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:46:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752468362207 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:46:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752468398206 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:46:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752468398206 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:51:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752468663313 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:51:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752468663313 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:51:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752468698199 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:51:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752468698199 HTTP/1.1" 200 160
************ - - [14/Jul/2025:12:56:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752468964314 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:56:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752468964314 HTTP/1.1" 200 159
************ - - [14/Jul/2025:12:56:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752468998197 HTTP/1.1" 200 -
************ - - [14/Jul/2025:12:56:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752468998197 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:01:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752469265207 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:01:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752469265207 HTTP/1.1" 200 159
************ - - [14/Jul/2025:13:01:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752469298328 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:01:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752469298328 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:06:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752469566312 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:06:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752469566312 HTTP/1.1" 200 159
************ - - [14/Jul/2025:13:06:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752469598205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:06:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752469598205 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:11:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752469867324 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:11:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752469867324 HTTP/1.1" 200 159
************ - - [14/Jul/2025:13:11:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752469898203 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:11:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752469898203 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:16:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752470168201 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:16:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752470168201 HTTP/1.1" 200 159
************ - - [14/Jul/2025:13:16:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752470198320 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:16:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752470198320 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:21:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752470460112 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:21:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752470460112 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:21:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752470469199 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:21:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752470469199 HTTP/1.1" 200 159
************ - - [14/Jul/2025:13:24:22 +0800] "GET /api/usif/menu/select-menu-list-by-role?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&_timer304=1752470662527 HTTP/1.1" 200 17499
************ - - [14/Jul/2025:13:24:22 +0800] "GET /api/usif/menu/select-mobile-menu-list?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&userId=5440&_timer304=1752470662527 HTTP/1.1" 200 12329
************ - - [14/Jul/2025:13:24:22 +0800] "POST /api/base/perliable/select-rsvr-warn-by-page HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:24:24 +0800] "GET /api/base/saas/token?_timer304=1752470663670 HTTP/1.1" 200 411
************ - - [14/Jul/2025:13:24:27 +0800] "GET /api/usif/ad/get-ad-by-sid?sid=220000000000000&hideloading=true&_timer304=1752470667080 HTTP/1.1" 200 145
************ - - [14/Jul/2025:13:24:27 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752470667589 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:24:27 +0800] "POST /api/ewci/warn/info/select-unaudit-count HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:24:27 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752470667592 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:24:27 +0800] "POST /api/usif/user/save-mobile-login-info HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:24:28 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:25:02 +0800] "GET /api/usif/menu/add-mobile-menu-hits?userId=5440&menuurl=RsvrWarningPerson&_timer304=1752470702203 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:25:02 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220000000000000&basCode=&_timer304=1752470702203 HTTP/1.1" 200 226
************ - - [14/Jul/2025:13:25:02 +0800] "GET /api/ewci/base/mal/write/271?_timer304=1752470702153 HTTP/1.1" 200 146
************ - - [14/Jul/2025:13:25:03 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9267
************ - - [14/Jul/2025:13:25:23 +0800] "GET /api/usif/menu/select-menu-list-by-role?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&_timer304=1752470723485 HTTP/1.1" 200 17499
************ - - [14/Jul/2025:13:25:23 +0800] "GET /api/usif/menu/select-mobile-menu-list?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&userId=5440&_timer304=1752470723485 HTTP/1.1" 200 12329
************ - - [14/Jul/2025:13:25:23 +0800] "POST /api/base/perliable/select-rsvr-warn-by-page HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:25:23 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220000000000000&basCode=&_timer304=1752470723591 HTTP/1.1" 200 226
************ - - [14/Jul/2025:13:25:23 +0800] "GET /api/usif/menu/add-mobile-menu-hits?userId=5440&menuurl=RsvrWarningPerson&_timer304=1752470723591 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:25:24 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9267
************ - - [14/Jul/2025:13:25:28 +0800] "POST /api/usif/user/save-mobile-login-info HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:25:28 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752470728491 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:25:28 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752470728495 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:25:28 +0800] "POST /api/ewci/warn/info/select-unaudit-count HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:25:29 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:26:20 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1752470779779 HTTP/1.1" 200 12285
************ - - [14/Jul/2025:13:26:22 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220500000000000&basCode=&_timer304=1752470781784 HTTP/1.1" 200 222
************ - - [14/Jul/2025:13:26:23 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9047
************ - - [14/Jul/2025:13:26:56 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9238
************ - - [14/Jul/2025:13:26:56 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9384
************ - - [14/Jul/2025:13:26:56 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9559
************ - - [14/Jul/2025:13:26:58 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9492
************ - - [14/Jul/2025:13:27:18 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9044
************ - - [14/Jul/2025:13:27:25 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 8891
************ - - [14/Jul/2025:13:27:25 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 8930
************ - - [14/Jul/2025:13:27:25 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 8968
************ - - [14/Jul/2025:13:27:25 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9506
************ - - [14/Jul/2025:13:27:25 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9584
************ - - [14/Jul/2025:13:27:25 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 6778
************ - - [14/Jul/2025:13:27:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:27:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:27:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:27:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:27:27 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:28:29 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752470908842 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:28:29 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:29:33 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9047
************ - - [14/Jul/2025:13:29:33 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9238
************ - - [14/Jul/2025:13:30:21 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9384
************ - - [14/Jul/2025:13:30:21 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9044
************ - - [14/Jul/2025:13:30:21 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 8891
************ - - [14/Jul/2025:13:30:21 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9559
************ - - [14/Jul/2025:13:30:21 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 8930
************ - - [14/Jul/2025:13:30:21 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9492
************ - - [14/Jul/2025:13:30:22 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 8968
************ - - [14/Jul/2025:13:30:22 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:22 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:23 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9506
************ - - [14/Jul/2025:13:30:23 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9584
************ - - [14/Jul/2025:13:30:23 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 6778
************ - - [14/Jul/2025:13:30:23 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:24 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:24 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:24 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:24 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:24 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:25 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:26 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752471023499 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:30:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:27 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 252
************ - - [14/Jul/2025:13:30:37 +0800] "POST /api/base/perliable/select-rsvr-warn-by-page HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:30:37 +0800] "GET /api/usif/menu/select-menu-list-by-role?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&_timer304=1752471037805 HTTP/1.1" 200 17499
************ - - [14/Jul/2025:13:30:38 +0800] "GET /api/usif/menu/select-mobile-menu-list?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&userId=5440&_timer304=1752471037805 HTTP/1.1" 200 12329
************ - - [14/Jul/2025:13:30:38 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220000000000000&basCode=&_timer304=1752471037878 HTTP/1.1" 200 226
************ - - [14/Jul/2025:13:30:38 +0800] "GET /api/usif/menu/add-mobile-menu-hits?userId=5440&menuurl=RsvrWarningPerson&_timer304=1752471037878 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:30:38 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9267
************ - - [14/Jul/2025:13:30:42 +0800] "POST /api/usif/user/save-mobile-login-info HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:30:43 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752471042814 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:30:43 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752471042810 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:30:43 +0800] "POST /api/ewci/warn/info/select-unaudit-count HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:30:44 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:30:46 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220500000000000&basCode=&_timer304=1752471045964 HTTP/1.1" 200 222
************ - - [14/Jul/2025:13:30:47 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9047
************ - - [14/Jul/2025:13:33:40 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9047
************ - - [14/Jul/2025:13:33:44 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:33:44 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752471223205 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:34:17 +0800] "GET /api/usif/menu/select-menu-list-by-role?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&_timer304=1752471257461 HTTP/1.1" 200 17499
************ - - [14/Jul/2025:13:34:17 +0800] "GET /api/usif/menu/select-mobile-menu-list?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&userId=5440&_timer304=1752471257461 HTTP/1.1" 200 12329
************ - - [14/Jul/2025:13:34:17 +0800] "POST /api/base/perliable/select-rsvr-warn-by-page HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:34:18 +0800] "GET /api/usif/menu/add-mobile-menu-hits?userId=5440&menuurl=RsvrWarningPerson&_timer304=1752471258691 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:34:18 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220000000000000&basCode=&_timer304=1752471258690 HTTP/1.1" 200 226
************ - - [14/Jul/2025:13:34:19 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9267
************ - - [14/Jul/2025:13:34:23 +0800] "POST /api/usif/user/save-mobile-login-info HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:34:23 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752471263200 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:34:23 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752471263196 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:34:23 +0800] "POST /api/ewci/warn/info/select-unaudit-count HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:34:24 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:35:30 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220500000000000&basCode=&_timer304=1752471330430 HTTP/1.1" 200 222
************ - - [14/Jul/2025:13:35:31 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9047
************ - - [14/Jul/2025:13:37:24 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752471444205 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:37:25 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:39:18 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752471558194 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:40:25 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752471625208 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:40:26 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:43:31 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752471806198 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:43:31 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:44:18 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752471858193 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:46:32 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:46:32 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752471992205 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:47:12 +0800] "GET /api/usif/menu/select-menu-list-by-role?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&_timer304=1752472032568 HTTP/1.1" 200 17499
************ - - [14/Jul/2025:13:47:12 +0800] "GET /api/usif/menu/select-mobile-menu-list?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&userId=5440&_timer304=1752472032568 HTTP/1.1" 200 12329
************ - - [14/Jul/2025:13:47:13 +0800] "GET /api/usif/menu/add-mobile-menu-hits?userId=5440&menuurl=RsvrWarningPerson&_timer304=1752472032670 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:47:13 +0800] "POST /api/base/perliable/select-rsvr-warn-by-page HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:47:13 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220000000000000&basCode=&_timer304=1752472032670 HTTP/1.1" 200 226
************ - - [14/Jul/2025:13:47:14 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9267
************ - - [14/Jul/2025:13:47:17 +0800] "POST /api/usif/user/save-mobile-login-info HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:47:17 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752472037573 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:47:17 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752472037577 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:47:17 +0800] "POST /api/ewci/warn/info/select-unaudit-count HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:47:18 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220500000000000&basCode=&_timer304=1752472038104 HTTP/1.1" 200 220
************ - - [14/Jul/2025:13:47:18 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:47:19 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9047
************ - - [14/Jul/2025:13:47:27 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220581000000000&basCode=&_timer304=1752472046793 HTTP/1.1" 200 219
************ - - [14/Jul/2025:13:47:27 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9364
************ - - [14/Jul/2025:13:47:51 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220500000000000&basCode=&_timer304=1752472070866 HTTP/1.1" 200 220
************ - - [14/Jul/2025:13:47:52 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9047
************ - - [14/Jul/2025:13:50:18 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752472218207 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:50:19 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:51:47 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [14/Jul/2025:13:51:47 +0800] "GET /login HTTP/1.1" 302 -
************ - - [14/Jul/2025:13:51:47 +0800] "GET /login?code=cwi5p3&state=ngyqT6 HTTP/1.1" 302 -
************ - - [14/Jul/2025:13:51:48 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [14/Jul/2025:13:51:51 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1752472311408 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:51 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1752472311408 HTTP/1.1" 200 552
************ - - [14/Jul/2025:13:51:52 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1752472312300 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:52 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1752472312300 HTTP/1.1" 200 61649
************ - - [14/Jul/2025:13:51:52 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1752472312772 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:52 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1752472312772 HTTP/1.1" 200 10388
************ - - [14/Jul/2025:13:51:52 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472312847 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:52 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472312847 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752472313762 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:51:53&etm=&_timer304=1752472313762 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472313762 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472313762 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472313762 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472313762 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:53 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:51:53&etm=&_timer304=1752472313762 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:51:53 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752472313762 HTTP/1.1" 200 1482
************ - - [14/Jul/2025:13:51:53 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:13:51:53 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:13:51:53 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472313762 HTTP/1.1" 200 166
************ - - [14/Jul/2025:13:51:53 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472313762 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:51:53 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:51:53 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:51:53 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472313762 HTTP/1.1" 200 169
************ - - [14/Jul/2025:13:51:54 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472313762 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752472314854 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/base/saas/token?_timer304=1752472314854 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472314875 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:54 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:51:55 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [14/Jul/2025:13:51:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:13:51:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:13:51:56 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:51:56 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [14/Jul/2025:13:51:57 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [14/Jul/2025:13:51:57 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [14/Jul/2025:13:51:57 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Jul/2025:13:51:57 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472314875 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:13:51:57 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [14/Jul/2025:13:51:57 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [14/Jul/2025:13:51:57 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [14/Jul/2025:13:51:57 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [14/Jul/2025:13:51:57 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [14/Jul/2025:13:51:57 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [14/Jul/2025:13:51:59 +0800] "GET /api/base/saas/token?_timer304=1752472314854 HTTP/1.1" 200 411
************ - - [14/Jul/2025:13:52:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752472321362 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:52:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752472321363 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:52:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752472321362 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:52:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752472321363 HTTP/1.1" 200 159
************ - - [14/Jul/2025:13:52:02 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752472314854 HTTP/1.1" 200 413
************ - - [14/Jul/2025:13:52:13 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752472333195 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1752472470252 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:54:30&etm=&_timer304=1752472470521 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472470521 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472470521 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472470521 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472470521 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752472470522 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752472470529 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472470710 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-14+08:00&etm=2025-07-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752472470685 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [14/Jul/2025:13:54:33 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:54:30&etm=&_timer304=1752472470521 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:54:33 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:54:33 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:54:33 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:13:54:33 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:13:54:33 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472470521 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:54:33 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472470521 HTTP/1.1" 200 166
************ - - [14/Jul/2025:13:54:33 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472470521 HTTP/1.1" 200 169
************ - - [14/Jul/2025:13:54:33 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [14/Jul/2025:13:54:34 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752472470529 HTTP/1.1" 200 159616
************ - - [14/Jul/2025:13:54:34 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [14/Jul/2025:13:54:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [14/Jul/2025:13:54:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752472470522 HTTP/1.1" 200 161
************ - - [14/Jul/2025:13:54:34 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472470521 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:13:54:34 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472470710 HTTP/1.1" 404 167
************ - - [14/Jul/2025:13:54:35 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [14/Jul/2025:13:54:35 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472475317 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:35 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472475317 HTTP/1.1" 404 167
************ - - [14/Jul/2025:13:54:35 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752472475340 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:35 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-14+08:00&etm=2025-07-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752472470685 HTTP/1.1" 200 441740
************ - - [14/Jul/2025:13:54:35 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1752472475491 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:35 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:35 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752472475340 HTTP/1.1" 200 232
************ - - [14/Jul/2025:13:54:35 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 257
************ - - [14/Jul/2025:13:54:35 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1752472475491 HTTP/1.1" 200 427
************ - - [14/Jul/2025:13:54:36 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1752472470252 HTTP/1.1" 200 144
************ - - [14/Jul/2025:13:54:50 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220500000000000&basCode=&_timer304=1752472490584 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:54:50 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 257
************ - - [14/Jul/2025:13:54:50 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220500000000000&basCode=&_timer304=1752472490584 HTTP/1.1" 200 409
************ - - [14/Jul/2025:13:55:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752472514674 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:14 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752472514675 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752472514674 HTTP/1.1" 200 161
************ - - [14/Jul/2025:13:55:14 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752472514675 HTTP/1.1" 200 159616
************ - - [14/Jul/2025:13:55:14 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:55:14&etm=&_timer304=1752472514727 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472514727 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472514727 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:14 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472514727 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472514727 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-14+08:00&etm=2025-07-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752472514792 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:55:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:55:14 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:13:55:14 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [14/Jul/2025:13:55:14 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:13:55:14 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [14/Jul/2025:13:55:14 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:55:14&etm=&_timer304=1752472514727 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:55:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472514727 HTTP/1.1" 200 166
************ - - [14/Jul/2025:13:55:14 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472514817 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:14 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472514727 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:55:14 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472514817 HTTP/1.1" 404 167
************ - - [14/Jul/2025:13:55:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472514727 HTTP/1.1" 200 169
************ - - [14/Jul/2025:13:55:14 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472514727 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:13:55:14 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [14/Jul/2025:13:55:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [14/Jul/2025:13:55:15 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [14/Jul/2025:13:55:15 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-14+08:00&etm=2025-07-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752472514792 HTTP/1.1" 200 441740
************ - - [14/Jul/2025:13:55:15 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472515973 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:15 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472515973 HTTP/1.1" 404 167
************ - - [14/Jul/2025:13:55:16 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752472516053 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:16 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752472516053 HTTP/1.1" 200 232
************ - - [14/Jul/2025:13:55:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752472525208 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752472525209 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752472525209 HTTP/1.1" 200 159
************ - - [14/Jul/2025:13:55:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752472525208 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:55:29 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1752472529628 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:29 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 257
************ - - [14/Jul/2025:13:55:29 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1752472529628 HTTP/1.1" 200 427
************ - - [14/Jul/2025:13:55:50 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [14/Jul/2025:13:55:50 +0800] "GET /login HTTP/1.1" 302 -
************ - - [14/Jul/2025:13:55:51 +0800] "GET /login?code=1eHqnF&state=wwnmIm HTTP/1.1" 302 -
************ - - [14/Jul/2025:13:55:51 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [14/Jul/2025:13:55:51 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1752472551955 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:51 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1752472551955 HTTP/1.1" 200 552
************ - - [14/Jul/2025:13:55:52 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1752472552269 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:52 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1752472552269 HTTP/1.1" 200 61649
************ - - [14/Jul/2025:13:55:52 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1752472552437 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:52 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1752472552437 HTTP/1.1" 200 10388
************ - - [14/Jul/2025:13:55:52 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472552525 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:52 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472552525 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:13:55:52 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752472552732 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:52 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:55:52&etm=&_timer304=1752472552732 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472552732 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472552732 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:52 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472552732 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472552732 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:52 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:13:55:52 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:55:52 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:13:55:52 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:55:52 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:55:52&etm=&_timer304=1752472552732 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:55:52 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472552732 HTTP/1.1" 200 166
************ - - [14/Jul/2025:13:55:52 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472552732 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:55:52 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752472552732 HTTP/1.1" 200 1482
************ - - [14/Jul/2025:13:55:52 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472552732 HTTP/1.1" 200 169
************ - - [14/Jul/2025:13:55:52 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472552732 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:13:55:53 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752472553322 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:53 +0800] "OPTIONS /api/base/saas/token?_timer304=1752472553322 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:53 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472553335 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:55:53 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [14/Jul/2025:13:55:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:13:55:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:13:55:54 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [14/Jul/2025:13:55:54 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [14/Jul/2025:13:55:54 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Jul/2025:13:55:54 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [14/Jul/2025:13:55:54 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [14/Jul/2025:13:55:54 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [14/Jul/2025:13:55:54 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [14/Jul/2025:13:55:54 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [14/Jul/2025:13:55:54 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472553335 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:13:55:54 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [14/Jul/2025:13:55:56 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:55:56 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [14/Jul/2025:13:55:57 +0800] "GET /api/base/saas/token?_timer304=1752472553322 HTTP/1.1" 200 411
************ - - [14/Jul/2025:13:55:58 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752472553322 HTTP/1.1" 200 413
************ - - [14/Jul/2025:13:56:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752472562206 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:56:02 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752472562205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:56:02 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752472562205 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:56:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752472562206 HTTP/1.1" 200 159
************ - - [14/Jul/2025:13:56:21 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752472581201 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:56:22 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:58:12 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:58:11&etm=&_timer304=1752472691507 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:12 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472691507 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:12 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752472691507 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472691507 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472691507 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:12 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472691507 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:14 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:13:58:14 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:13:58:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:58:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:58:14 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:58:11&etm=&_timer304=1752472691507 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:58:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472691507 HTTP/1.1" 200 166
************ - - [14/Jul/2025:13:58:14 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472691507 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:58:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472691507 HTTP/1.1" 200 169
************ - - [14/Jul/2025:13:58:14 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472691507 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:13:58:15 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752472691507 HTTP/1.1" 200 1482
************ - - [14/Jul/2025:13:58:15 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752472695645 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:15 +0800] "OPTIONS /api/base/saas/token?_timer304=1752472695645 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:15 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472695654 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [14/Jul/2025:13:58:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:13:58:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1752472697364 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472697546 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:58:17&etm=&_timer304=1752472697546 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472697546 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472697546 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752472697546 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472697546 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752472697559 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472697723 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-14+08:00&etm=2025-07-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752472697702 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:17 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [14/Jul/2025:13:58:17 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [14/Jul/2025:13:58:18 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1752472695654 HTTP/1.1" 200 2009
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:58:18 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:13:58:18 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+14:00&filterCnt=6&_timer304=1752472697546 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:58:18 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472697546 HTTP/1.1" 200 166
************ - - [14/Jul/2025:13:58:18 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+13:58:17&etm=&_timer304=1752472697546 HTTP/1.1" 200 156
************ - - [14/Jul/2025:13:58:19 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472697546 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:13:58:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472697546 HTTP/1.1" 200 169
************ - - [14/Jul/2025:13:58:19 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752472697546 HTTP/1.1" 200 161
************ - - [14/Jul/2025:13:58:19 +0800] "GET /api/base/saas/token?_timer304=1752472695645 HTTP/1.1" 200 411
************ - - [14/Jul/2025:13:58:19 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:58:19 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752472697559 HTTP/1.1" 200 159616
************ - - [14/Jul/2025:13:58:19 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [14/Jul/2025:13:58:19 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [14/Jul/2025:13:58:19 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [14/Jul/2025:13:58:19 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1752472697364 HTTP/1.1" 200 144
************ - - [14/Jul/2025:13:58:19 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [14/Jul/2025:13:58:19 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472697723 HTTP/1.1" 404 167
************ - - [14/Jul/2025:13:58:19 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [14/Jul/2025:13:58:20 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [14/Jul/2025:13:58:20 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472700256 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:20 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752472700256 HTTP/1.1" 404 167
************ - - [14/Jul/2025:13:58:20 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752472700276 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:20 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-14+08:00&etm=2025-07-14+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752472697702 HTTP/1.1" 200 441740
************ - - [14/Jul/2025:13:58:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752472700276 HTTP/1.1" 200 232
************ - - [14/Jul/2025:13:58:20 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752472695645 HTTP/1.1" 200 413
************ - - [14/Jul/2025:13:58:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752472701401 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752472701407 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752472701407 HTTP/1.1" 200 159
************ - - [14/Jul/2025:13:58:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752472701401 HTTP/1.1" 200 160
************ - - [14/Jul/2025:13:58:24 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1752472704675 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:24 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1752472704675 HTTP/1.1" 200 427
************ - - [14/Jul/2025:13:58:25 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 954499
************ - - [14/Jul/2025:13:58:32 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220500000000000&basCode=&_timer304=1752472712257 HTTP/1.1" 200 -
************ - - [14/Jul/2025:13:58:32 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220500000000000&basCode=&_timer304=1752472712257 HTTP/1.1" 200 409
************ - - [14/Jul/2025:13:58:32 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 90378
************ - - [14/Jul/2025:13:58:48 +0800] "GET /api/usif/menu/select-menu-list-by-role?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&_timer304=1752472727972 HTTP/1.1" 200 17499
************ - - [14/Jul/2025:13:58:48 +0800] "GET /api/usif/menu/select-mobile-menu-list?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&userId=5440&_timer304=1752472727972 HTTP/1.1" 200 12329
************ - - [14/Jul/2025:13:58:48 +0800] "POST /api/base/perliable/select-rsvr-warn-by-page HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:58:48 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220000000000000&basCode=&_timer304=1752472728078 HTTP/1.1" 200 226
************ - - [14/Jul/2025:13:58:48 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 24478
************ - - [14/Jul/2025:13:58:52 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752472732977 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:58:53 +0800] "POST /api/usif/user/save-mobile-login-info HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:58:53 +0800] "POST /api/ewci/warn/info/select-unaudit-count HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:58:53 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752472732980 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:58:54 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:58:55 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220500000000000&basCode=&_timer304=1752472735508 HTTP/1.1" 200 220
************ - - [14/Jul/2025:13:58:56 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 23386
************ - - [14/Jul/2025:13:59:06 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 23123
************ - - [14/Jul/2025:13:59:07 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 23037
************ - - [14/Jul/2025:13:59:19 +0800] "GET /api/usif/menu/select-menu-list-by-role?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&_timer304=1752472759132 HTTP/1.1" 200 17499
************ - - [14/Jul/2025:13:59:19 +0800] "GET /api/usif/menu/select-mobile-menu-list?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&userId=5440&_timer304=1752472759132 HTTP/1.1" 200 12329
************ - - [14/Jul/2025:13:59:19 +0800] "POST /api/base/perliable/select-rsvr-warn-by-page HTTP/1.1" 200 248
************ - - [14/Jul/2025:13:59:19 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220000000000000&basCode=&_timer304=1752472759242 HTTP/1.1" 200 226
************ - - [14/Jul/2025:13:59:19 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 24478
************ - - [14/Jul/2025:13:59:24 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752472764140 HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:59:24 +0800] "POST /api/usif/user/save-mobile-login-info HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:59:24 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752472764143 HTTP/1.1" 200 164
************ - - [14/Jul/2025:13:59:24 +0800] "POST /api/ewci/warn/info/select-unaudit-count HTTP/1.1" 200 158
************ - - [14/Jul/2025:13:59:25 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:13:59:28 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220500000000000&basCode=&_timer304=1752472767974 HTTP/1.1" 200 220
************ - - [14/Jul/2025:13:59:28 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 23386
************ - - [14/Jul/2025:14:01:57 +0800] "OPTIONS /api/ewci/base/mal/write/297?_timer304=1752472917065 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:01:57 +0800] "GET /api/ewci/base/mal/write/297?_timer304=1752472917065 HTTP/1.1" 200 146
************ - - [14/Jul/2025:14:01:57 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+14:01:57&etm=&_timer304=1752472917174 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:01:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472917174 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:01:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+15:00&filterCnt=6&_timer304=1752472917174 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:01:57 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472917174 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:01:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472917174 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:01:57 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:14:01:57 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:14:01:57 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:14:01:57 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:14:01:57 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472917174 HTTP/1.1" 200 166
************ - - [14/Jul/2025:14:01:57 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+14:01:57&etm=&_timer304=1752472917174 HTTP/1.1" 200 156
************ - - [14/Jul/2025:14:01:57 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+15:00&filterCnt=6&_timer304=1752472917174 HTTP/1.1" 200 164
************ - - [14/Jul/2025:14:01:57 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472917174 HTTP/1.1" 200 169
************ - - [14/Jul/2025:14:01:57 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472917174 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:14:01:57 +0800] "OPTIONS /api/shyj/msg/get-warn-message-xian-statistics?adcd=220000000000000&_timer304=1752472917417 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:01:59 +0800] "GET /api/shyj/msg/get-warn-message-xian-statistics?adcd=220000000000000&_timer304=1752472917417 HTTP/1.1" 200 5690
************ - - [14/Jul/2025:14:02:01 +0800] "OPTIONS /api/ewci/base/mal/write/403?_timer304=1752472921098 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:02:01 +0800] "GET /api/ewci/base/mal/write/403?_timer304=1752472921098 HTTP/1.1" 200 146
************ - - [14/Jul/2025:14:02:01 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+14:02:01&etm=&_timer304=1752472921229 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:02:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472921229 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:02:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+15:00&filterCnt=6&_timer304=1752472921229 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:02:01 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472921229 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:02:01 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472921229 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:02:01 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1752472921229 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:02:01 +0800] "OPTIONS /api/ewci/ast/station/select-station-list HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:02:01 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [14/Jul/2025:14:02:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:14:02:01 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [14/Jul/2025:14:02:01 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [14/Jul/2025:14:02:01 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+14:02:01&etm=&_timer304=1752472921229 HTTP/1.1" 200 156
************ - - [14/Jul/2025:14:02:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752472921229 HTTP/1.1" 200 166
************ - - [14/Jul/2025:14:02:01 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+15:00&filterCnt=6&_timer304=1752472921229 HTTP/1.1" 200 164
************ - - [14/Jul/2025:14:02:01 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1752472921229 HTTP/1.1" 200 12285
************ - - [14/Jul/2025:14:02:01 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752472921229 HTTP/1.1" 200 169
************ - - [14/Jul/2025:14:02:01 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752472921229 HTTP/1.1" 200 13016
************ - - [14/Jul/2025:14:02:05 +0800] "POST /api/ewci/ast/station/select-station-list HTTP/1.1" 200 3905746
************ - - [14/Jul/2025:14:02:06 +0800] "POST /api/ewci/ast/station/select-station-list HTTP/1.1" 200 3905746
************ - - [14/Jul/2025:14:03:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752472991411 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:03:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752472991411 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:03:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752473001483 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:03:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752473001483 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:08:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752473292204 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:08:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752473292204 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:08:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752473302198 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:08:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752473302198 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:13:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752473592208 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:13:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752473592208 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:13:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752473603198 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:13:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752473603198 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:18:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752473892208 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:18:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752473892208 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:18:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752473904202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:18:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752473904202 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:23:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752474192202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:23:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752474192202 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:23:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752474205195 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:23:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752474205195 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:28:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752474506202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:28:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752474506202 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:28:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752474518209 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:28:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752474518209 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:33:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752474807206 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:33:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752474807206 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:34:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752474878205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:34:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752474878205 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:38:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752475108195 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:38:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752475108195 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:39:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752475178201 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:39:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752475178201 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:43:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752475409206 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:43:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752475409206 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:44:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752475478198 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:44:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752475478198 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:48:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752475710200 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:48:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752475710200 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:49:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752475778203 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:49:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752475778203 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:53:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752476011200 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:53:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752476011200 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:54:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752476078205 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:54:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752476078205 HTTP/1.1" 200 160
************ - - [14/Jul/2025:14:58:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752476312204 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:58:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752476312204 HTTP/1.1" 200 159
************ - - [14/Jul/2025:14:59:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752476378202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:14:59:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752476378202 HTTP/1.1" 200 160
************ - - [14/Jul/2025:15:03:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752476613208 HTTP/1.1" 200 -
************ - - [14/Jul/2025:15:03:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752476613208 HTTP/1.1" 200 159
************ - - [14/Jul/2025:15:04:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752476678195 HTTP/1.1" 200 -
************ - - [14/Jul/2025:15:04:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752476678195 HTTP/1.1" 200 160
************ - - [14/Jul/2025:15:08:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752476914202 HTTP/1.1" 200 -
************ - - [14/Jul/2025:15:08:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752476914202 HTTP/1.1" 200 159
************ - - [14/Jul/2025:15:09:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752476978204 HTTP/1.1" 200 -
************ - - [14/Jul/2025:15:09:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752476978204 HTTP/1.1" 200 160
************ - - [14/Jul/2025:15:30:27 +0800] "GET /api/usif/menu/select-mobile-menu-list?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&userId=5440&_timer304=1752478222421 HTTP/1.1" 200 12329
************ - - [14/Jul/2025:15:30:27 +0800] "GET /api/usif/menu/select-menu-list-by-role?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&_timer304=1752478222421 HTTP/1.1" 200 17499
************ - - [14/Jul/2025:15:30:27 +0800] "POST /api/base/perliable/select-rsvr-warn-by-page HTTP/1.1" 200 248
************ - - [14/Jul/2025:15:30:28 +0800] "GET /api/base/saas/token?_timer304=1752478223307 HTTP/1.1" 200 411
************ - - [14/Jul/2025:15:30:29 +0800] "GET /api/usif/ad/get-ad-by-sid?sid=220000000000000&hideloading=true&_timer304=1752478224301 HTTP/1.1" 200 145
************ - - [14/Jul/2025:15:30:32 +0800] "POST /api/ewci/warn/info/select-unaudit-count HTTP/1.1" 200 158
************ - - [14/Jul/2025:15:30:32 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752478228056 HTTP/1.1" 200 163
************ - - [14/Jul/2025:15:30:32 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752478228077 HTTP/1.1" 200 164
************ - - [14/Jul/2025:15:30:32 +0800] "POST /api/usif/user/save-mobile-login-info HTTP/1.1" 200 158
************ - - [14/Jul/2025:15:30:33 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************* - - [14/Jul/2025:15:30:52 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:30:52 +0800] "GET /login HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:31:01 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:31:01 +0800] "GET /login HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:31:14 +0800] "GET /login?code=KABdqf&state=Q011RM HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:31:14 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:31:26 +0800] "OPTIONS /api/usif/user/select-by-username?username=220000&_timer304=1752478281988 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:26 +0800] "GET /api/usif/user/select-by-username?username=220000&_timer304=1752478281988 HTTP/1.1" 200 524
************* - - [14/Jul/2025:15:31:26 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=1&_timer304=1752478282072 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:26 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=1&_timer304=1752478282072 HTTP/1.1" 200 41606
************* - - [14/Jul/2025:15:31:26 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=5&_timer304=1752478282125 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:26 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=5&_timer304=1752478282125 HTTP/1.1" 200 8569
************* - - [14/Jul/2025:15:31:26 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1752478282163 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:26 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1752478282163 HTTP/1.1" 200 5130
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752478282478 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+15:31:22&etm=&_timer304=1752478282478 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+16:00&filterCnt=6&_timer304=1752478282478 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752478282478 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752478282478 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752478282478 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:27 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752478282478 HTTP/1.1" 200 1524
************* - - [14/Jul/2025:15:31:27 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [14/Jul/2025:15:31:27 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [14/Jul/2025:15:31:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752478282478 HTTP/1.1" 200 166
************* - - [14/Jul/2025:15:31:27 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-14+08:00&etm=2025-07-14+16:00&filterCnt=6&_timer304=1752478282478 HTTP/1.1" 200 164
************* - - [14/Jul/2025:15:31:27 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-11+15:31:22&etm=&_timer304=1752478282478 HTTP/1.1" 200 156
************* - - [14/Jul/2025:15:31:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:31:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:31:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:31:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:31:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752478282478 HTTP/1.1" 200 169
************* - - [14/Jul/2025:15:31:27 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752478282478 HTTP/1.1" 200 13016
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752478283413 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/base/saas/token?_timer304=1752478283413 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1752478283446 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************* - - [14/Jul/2025:15:31:28 +0800] "GET /api/base/saas/token?_timer304=1752478283413 HTTP/1.1" 200 411
************* - - [14/Jul/2025:15:31:28 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 297
************* - - [14/Jul/2025:15:31:28 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [14/Jul/2025:15:31:28 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [14/Jul/2025:15:31:28 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [14/Jul/2025:15:31:28 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [14/Jul/2025:15:31:28 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 34365
************* - - [14/Jul/2025:15:31:29 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************* - - [14/Jul/2025:15:31:29 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [14/Jul/2025:15:31:29 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [14/Jul/2025:15:31:29 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [14/Jul/2025:15:31:29 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************* - - [14/Jul/2025:15:31:29 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1752478283446 HTTP/1.1" 200 5130
************* - - [14/Jul/2025:15:31:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [14/Jul/2025:15:31:29 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [14/Jul/2025:15:31:30 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-13&_timer304=1752478283413 HTTP/1.1" 200 413
************* - - [14/Jul/2025:15:31:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752478291919 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:36 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752478291912 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:31:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752478291912 HTTP/1.1" 200 160
************* - - [14/Jul/2025:15:31:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752478291919 HTTP/1.1" 200 159
************* - - [14/Jul/2025:15:32:08 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/?redirect=%252FworkbenchMainFrame%252FWorkMain%253Ftmpparams%253D%257B%2522title%2522%253A%2522%25E5%25B7%25A5%25E4%25BD%259C%25E5%258F%25B0%2522%257D HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:32:08 +0800] "GET /login HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:32:21 +0800] "GET /login?code=v3Gl95&state=f1XlEd HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:32:21 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/?redirect=%252FworkbenchMainFrame%252FWorkMain%253Ftmpparams%253D%257B%2522title%2522%253A%2522%25E5%25B7%25A5%25E4%25BD%259C%25E5%258F%25B0%2522%257D HTTP/1.1" 302 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/usif/user/select-by-username?username=220000&_timer304=1752219135584 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/usif/user/select-by-username?username=220000&_timer304=1752219135584 HTTP/1.1" 200 524
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=1&_timer304=1752219135639 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=1&_timer304=1752219135639 HTTP/1.1" 200 41606
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=5&_timer304=1752219135673 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=5&_timer304=1752219135673 HTTP/1.1" 200 8569
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1752219135698 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1752219135698 HTTP/1.1" 200 5130
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752219135967 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+15:32:15&etm=&_timer304=1752219135967 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752219135967 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+16:00&filterCnt=6&_timer304=1752219135967 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752219135967 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752219135967 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752219135967 HTTP/1.1" 200 1524
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+15:32:15&etm=&_timer304=1752219135967 HTTP/1.1" 200 156
************* - - [14/Jul/2025:15:32:25 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [14/Jul/2025:15:32:25 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752219135967 HTTP/1.1" 200 166
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+16:00&filterCnt=6&_timer304=1752219135967 HTTP/1.1" 200 164
************* - - [14/Jul/2025:15:32:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:32:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:32:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:32:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752219135967 HTTP/1.1" 200 169
************* - - [14/Jul/2025:15:32:25 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752219135967 HTTP/1.1" 200 13016
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-10&_timer304=1752219136748 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/base/saas/token?_timer304=1752219136748 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "GET /api/base/saas/token?_timer304=1752219136748 HTTP/1.1" 200 411
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1752219136770 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:26 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************* - - [14/Jul/2025:15:32:27 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [14/Jul/2025:15:32:27 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 301
************* - - [14/Jul/2025:15:32:27 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [14/Jul/2025:15:32:27 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [14/Jul/2025:15:32:27 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [14/Jul/2025:15:32:28 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 34365
************* - - [14/Jul/2025:15:32:28 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [14/Jul/2025:15:32:28 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [14/Jul/2025:15:32:28 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [14/Jul/2025:15:32:28 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1752219136770 HTTP/1.1" 200 5130
************* - - [14/Jul/2025:15:32:28 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************* - - [14/Jul/2025:15:32:28 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************* - - [14/Jul/2025:15:32:29 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-10&_timer304=1752219136748 HTTP/1.1" 200 414
************* - - [14/Jul/2025:15:32:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 4000
************* - - [14/Jul/2025:15:32:29 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 4000
************* - - [14/Jul/2025:15:32:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752219145517 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752219145528 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:32:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752219145517 HTTP/1.1" 200 160
************* - - [14/Jul/2025:15:32:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752219145528 HTTP/1.1" 200 159
************* - - [14/Jul/2025:15:37:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752219435662 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:37:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752219435662 HTTP/1.1" 200 160
************* - - [14/Jul/2025:15:37:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752219445797 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:37:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752219445797 HTTP/1.1" 200 159
************* - - [14/Jul/2025:15:42:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752219735672 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:42:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752219735672 HTTP/1.1" 200 160
************* - - [14/Jul/2025:15:42:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752219746067 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:42:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752219746067 HTTP/1.1" 200 159
************* - - [14/Jul/2025:15:47:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752220035709 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:47:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752220035709 HTTP/1.1" 200 160
************* - - [14/Jul/2025:15:47:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752220046318 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:47:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752220046318 HTTP/1.1" 200 159
************* - - [14/Jul/2025:15:51:13 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1752220263163 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:13 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1752220263163 HTTP/1.1" 200 146
************* - - [14/Jul/2025:15:51:16 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1752220266736 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:16 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1752220266736 HTTP/1.1" 200 146
************* - - [14/Jul/2025:15:51:18 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1752220268911 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:18 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1752220268911 HTTP/1.1" 200 146
************* - - [14/Jul/2025:15:51:22 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752220272717 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:22 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+15:51:12&etm=&_timer304=1752220272717 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:22 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [14/Jul/2025:15:51:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752220272717 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:22 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752220272717 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752220272717 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:22 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+16:00&filterCnt=6&_timer304=1752220272717 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:22 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [14/Jul/2025:15:51:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:51:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:51:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:51:22 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:51:22 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752220272717 HTTP/1.1" 200 1524
************* - - [14/Jul/2025:15:51:22 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+15:51:12&etm=&_timer304=1752220272717 HTTP/1.1" 200 156
************* - - [14/Jul/2025:15:51:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752220272717 HTTP/1.1" 200 166
************* - - [14/Jul/2025:15:51:22 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+16:00&filterCnt=6&_timer304=1752220272717 HTTP/1.1" 200 164
************* - - [14/Jul/2025:15:51:22 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752220272717 HTTP/1.1" 200 169
************* - - [14/Jul/2025:15:51:22 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752220272717 HTTP/1.1" 200 13016
************* - - [14/Jul/2025:15:51:23 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-10&_timer304=1752220273643 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:23 +0800] "OPTIONS /api/base/saas/token?_timer304=1752220273643 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:23 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-10&_timer304=1752220273643 HTTP/1.1" 200 414
************* - - [14/Jul/2025:15:51:23 +0800] "GET /api/base/saas/token?_timer304=1752220273643 HTTP/1.1" 200 411
************* - - [14/Jul/2025:15:51:23 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1752220273661 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************* - - [14/Jul/2025:15:51:23 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 301
************* - - [14/Jul/2025:15:51:23 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [14/Jul/2025:15:51:24 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************* - - [14/Jul/2025:15:51:24 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************* - - [14/Jul/2025:15:51:24 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [14/Jul/2025:15:51:24 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [14/Jul/2025:15:51:24 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [14/Jul/2025:15:51:24 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [14/Jul/2025:15:51:24 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1752220273661 HTTP/1.1" 200 5130
************* - - [14/Jul/2025:15:51:24 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [14/Jul/2025:15:51:24 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 34365
************* - - [14/Jul/2025:15:51:24 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 4000
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 4000
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1752220275707 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1752220275707 HTTP/1.1" 200 144
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+15:51:15&etm=&_timer304=1752220275895 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752220275895 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+16:00&filterCnt=6&_timer304=1752220275895 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752220275895 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752220275895 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:51:25 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+15:51:15&etm=&_timer304=1752220275895 HTTP/1.1" 200 156
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752220275895 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752220275904 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:15:51:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752220275895 HTTP/1.1" 200 166
************* - - [14/Jul/2025:15:51:25 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+16:00&filterCnt=6&_timer304=1752220275895 HTTP/1.1" 200 164
************* - - [14/Jul/2025:15:51:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752220275895 HTTP/1.1" 200 161
************* - - [14/Jul/2025:15:51:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752220275895 HTTP/1.1" 200 169
************* - - [14/Jul/2025:15:51:25 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752220275904 HTTP/1.1" 200 159616
************* - - [14/Jul/2025:15:51:25 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752220275895 HTTP/1.1" 200 13016
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-11+08:00&etm=2025-07-11+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752220276123 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:25 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************* - - [14/Jul/2025:15:51:25 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752220276154 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:26 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752220276154 HTTP/1.1" 404 167
************* - - [14/Jul/2025:15:51:26 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [14/Jul/2025:15:51:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [14/Jul/2025:15:51:26 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-11+08:00&etm=2025-07-11+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752220276123 HTTP/1.1" 200 441740
************* - - [14/Jul/2025:15:51:26 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [14/Jul/2025:15:51:26 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752220277070 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:26 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752220277070 HTTP/1.1" 404 167
************* - - [14/Jul/2025:15:51:26 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752220277156 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:27 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752220277156 HTTP/1.1" 200 232
************* - - [14/Jul/2025:15:51:27 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1752220277682 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:27 +0800] "OPTIONS /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:27 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 954499
************* - - [14/Jul/2025:15:51:27 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220000000000000&basCode=&_timer304=1752220277682 HTTP/1.1" 200 427
************* - - [14/Jul/2025:15:51:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752220282555 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752220282565 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752220282555 HTTP/1.1" 200 160
************* - - [14/Jul/2025:15:51:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752220282565 HTTP/1.1" 200 159
************* - - [14/Jul/2025:15:51:33 +0800] "OPTIONS /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220500000000000&basCode=&_timer304=1752220283394 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:51:33 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 90378
************* - - [14/Jul/2025:15:51:33 +0800] "GET /api/dzsj/rvrsvr/get-dt-gc-info?adcd=220500000000000&basCode=&_timer304=1752220283394 HTTP/1.1" 200 409
************* - - [14/Jul/2025:15:56:23 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752220572684 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:56:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752220575740 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:56:25 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220500000000000&basCode=&stm=2025-07-11+08:00&etm=2025-07-11+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752220576032 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:56:28 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752220575740 HTTP/1.1" 200 161
************* - - [14/Jul/2025:15:56:28 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [14/Jul/2025:15:56:28 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752220572684 HTTP/1.1" 200 160
************* - - [14/Jul/2025:15:56:28 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [14/Jul/2025:15:56:28 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [14/Jul/2025:15:56:29 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220500000000000&basCode=&stm=2025-07-11+08:00&etm=2025-07-11+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752220576032 HTTP/1.1" 200 441740
************* - - [14/Jul/2025:15:56:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752220582619 HTTP/1.1" 200 -
************* - - [14/Jul/2025:15:56:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752220582619 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:04:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752221045945 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:04:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221045946 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:04:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752221045947 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:04:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220500000000000&basCode=&stm=2025-07-11+08:00&etm=2025-07-11+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752221045950 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:04:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [14/Jul/2025:16:04:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752221045945 HTTP/1.1" 200 161
************* - - [14/Jul/2025:16:04:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752221045947 HTTP/1.1" 200 160
************* - - [14/Jul/2025:16:04:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221045946 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:04:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [14/Jul/2025:16:04:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [14/Jul/2025:16:04:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220500000000000&basCode=&stm=2025-07-11+08:00&etm=2025-07-11+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752221045950 HTTP/1.1" 200 441740
************* - - [14/Jul/2025:16:06:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752221172675 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:06:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752221172675 HTTP/1.1" 200 160
************* - - [14/Jul/2025:16:06:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752221175742 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:06:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752221175742 HTTP/1.1" 200 161
************* - - [14/Jul/2025:16:06:25 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220500000000000&basCode=&stm=2025-07-11+08:00&etm=2025-07-11+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752221176035 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:06:25 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [14/Jul/2025:16:06:26 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [14/Jul/2025:16:06:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [14/Jul/2025:16:06:26 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220500000000000&basCode=&stm=2025-07-11+08:00&etm=2025-07-11+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752221176035 HTTP/1.1" 200 441740
************* - - [14/Jul/2025:16:09:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221346069 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:09:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221346069 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:11:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752221472560 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:11:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752221472560 HTTP/1.1" 200 160
************* - - [14/Jul/2025:16:11:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752221475731 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:11:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752221475731 HTTP/1.1" 200 161
************* - - [14/Jul/2025:16:11:25 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220500000000000&basCode=&stm=2025-07-11+08:00&etm=2025-07-11+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752221476047 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:11:25 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************* - - [14/Jul/2025:16:11:26 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51884
************* - - [14/Jul/2025:16:11:26 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [14/Jul/2025:16:11:26 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220500000000000&basCode=&stm=2025-07-11+08:00&etm=2025-07-11+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752221476047 HTTP/1.1" 200 441740
************* - - [14/Jul/2025:16:14:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221646136 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221646136 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:14:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752221655116 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:25 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752221655123 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:25 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1752221655116 HTTP/1.1" 200 161
************* - - [14/Jul/2025:16:14:25 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1752221655123 HTTP/1.1" 200 159616
************* - - [14/Jul/2025:16:14:25 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+16:14:15&etm=&_timer304=1752221655229 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752221655229 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+17:00&filterCnt=6&_timer304=1752221655229 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:25 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752221655229 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752221655229 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:25 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-11+08:00&etm=2025-07-11+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752221655343 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:25 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+16:14:15&etm=&_timer304=1752221655229 HTTP/1.1" 200 156
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [14/Jul/2025:16:14:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752221655229 HTTP/1.1" 200 166
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:16:14:25 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752221655370 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:25 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+17:00&filterCnt=6&_timer304=1752221655229 HTTP/1.1" 200 164
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:16:14:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752221655229 HTTP/1.1" 200 169
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************* - - [14/Jul/2025:16:14:25 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752221655370 HTTP/1.1" 404 167
************* - - [14/Jul/2025:16:14:25 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752221655229 HTTP/1.1" 200 13016
************* - - [14/Jul/2025:16:14:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************* - - [14/Jul/2025:16:14:26 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************* - - [14/Jul/2025:16:14:26 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752221656341 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:26 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1752221656341 HTTP/1.1" 404 167
************* - - [14/Jul/2025:16:14:26 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752221656379 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:26 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-07-11+08:00&etm=2025-07-11+17:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1752221655343 HTTP/1.1" 200 441740
************* - - [14/Jul/2025:16:14:26 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1752221656379 HTTP/1.1" 200 232
************* - - [14/Jul/2025:16:14:26 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1752221657074 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+16:14:17&etm=&_timer304=1752221657205 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752221657205 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+17:00&filterCnt=6&_timer304=1752221657205 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752221657205 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752221657205 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [14/Jul/2025:16:14:27 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-07-08+16:14:17&etm=&_timer304=1752221657205 HTTP/1.1" 200 156
************* - - [14/Jul/2025:16:14:27 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [14/Jul/2025:16:14:27 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1752221657235 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:16:14:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:16:14:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:16:14:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [14/Jul/2025:16:14:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1752221657205 HTTP/1.1" 200 166
************* - - [14/Jul/2025:16:14:27 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-07-11+08:00&etm=2025-07-11+17:00&filterCnt=6&_timer304=1752221657205 HTTP/1.1" 200 164
************* - - [14/Jul/2025:16:14:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1752221657205 HTTP/1.1" 200 169
************* - - [14/Jul/2025:16:14:27 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1752221657235 HTTP/1.1" 200 1524
************* - - [14/Jul/2025:16:14:27 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1752221657205 HTTP/1.1" 200 13016
************* - - [14/Jul/2025:16:14:27 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1752221657074 HTTP/1.1" 200 146
************* - - [14/Jul/2025:16:14:27 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-10&_timer304=1752221657684 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "OPTIONS /api/base/saas/token?_timer304=1752221657684 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1752221657700 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:27 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************* - - [14/Jul/2025:16:14:27 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 301
************* - - [14/Jul/2025:16:14:27 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [14/Jul/2025:16:14:28 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [14/Jul/2025:16:14:28 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [14/Jul/2025:16:14:29 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************* - - [14/Jul/2025:16:14:29 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************* - - [14/Jul/2025:16:14:29 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [14/Jul/2025:16:14:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 4000
************* - - [14/Jul/2025:16:14:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 4000
************* - - [14/Jul/2025:16:14:30 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [14/Jul/2025:16:14:30 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [14/Jul/2025:16:14:30 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1752221657700 HTTP/1.1" 200 5130
************* - - [14/Jul/2025:16:14:30 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [14/Jul/2025:16:14:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 34365
************* - - [14/Jul/2025:16:14:31 +0800] "GET /api/base/saas/token?_timer304=1752221657684 HTTP/1.1" 200 411
************* - - [14/Jul/2025:16:14:32 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-07-10&_timer304=1752221657684 HTTP/1.1" 200 414
************* - - [14/Jul/2025:16:14:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752221664753 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752221664753 HTTP/1.1" 200 160
************* - - [14/Jul/2025:16:14:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221664810 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:14:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221664810 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:19:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752221955057 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:19:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752221955057 HTTP/1.1" 200 160
************* - - [14/Jul/2025:16:19:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221965059 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:19:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752221965059 HTTP/1.1" 200 159
************ - - [14/Jul/2025:16:24:23 +0800] "GET /api/usif/menu/select-menu-list-by-role?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&_timer304=1752222254038 HTTP/1.1" 200 17499
************ - - [14/Jul/2025:16:24:23 +0800] "POST /api/base/perliable/select-rsvr-warn-by-page HTTP/1.1" 200 248
************ - - [14/Jul/2025:16:24:23 +0800] "GET /api/usif/menu/select-mobile-menu-list?systemCode=4&roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&userId=5440&_timer304=1752222254038 HTTP/1.1" 200 12329
************ - - [14/Jul/2025:16:24:24 +0800] "GET /api/base/saas/token?_timer304=1752222254403 HTTP/1.1" 200 411
************ - - [14/Jul/2025:16:24:24 +0800] "GET /api/usif/ad/get-ad-by-sid?sid=220000000000000&hideloading=true&_timer304=1752222254606 HTTP/1.1" 200 145
************* - - [14/Jul/2025:16:24:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752222254886 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:24:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752222254886 HTTP/1.1" 200 160
************ - - [14/Jul/2025:16:24:28 +0800] "GET /api/usif/user/update-mobile-lastly-online?_timer304=1752222259055 HTTP/1.1" 200 163
************ - - [14/Jul/2025:16:24:28 +0800] "POST /api/ewci/warn/info/select-unaudit-count HTTP/1.1" 200 158
************ - - [14/Jul/2025:16:24:28 +0800] "GET /api/om/res/patrol/record/message/unread-list?_timer304=1752222259057 HTTP/1.1" 200 164
************ - - [14/Jul/2025:16:24:28 +0800] "POST /api/usif/user/save-mobile-login-info HTTP/1.1" 200 158
************ - - [14/Jul/2025:16:24:29 +0800] "POST /api/ewci/warn/send/select-app-warnsend-list HTTP/1.1" 200 163
************ - - [14/Jul/2025:16:24:31 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220000000000000&basCode=&_timer304=1752222261610 HTTP/1.1" 200 226
************ - - [14/Jul/2025:16:24:31 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 24478
************ - - [14/Jul/2025:16:24:32 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1752222262918 HTTP/1.1" 200 12285
************ - - [14/Jul/2025:16:24:34 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220500000000000&basCode=&_timer304=1752222264502 HTTP/1.1" 200 220
************ - - [14/Jul/2025:16:24:34 +0800] "POST /api/dzsj/rvrsvr/get-rvrsvr-info-by-param HTTP/1.1" 200 23386
************* - - [14/Jul/2025:16:24:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752222265889 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:24:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752222265889 HTTP/1.1" 200 159
************ - - [14/Jul/2025:16:24:46 +0800] "GET /api/base/saas/token?_timer304=1752222276943 HTTP/1.1" 200 411
************ - - [14/Jul/2025:16:24:46 +0800] "GET /api/usif/ad/get-ad-by-sid?sid=220000000000000&hideloading=true&_timer304=1752222277115 HTTP/1.1" 200 145
************ - - [14/Jul/2025:16:24:58 +0800] "GET /api/ewci/base/mal/write/271?_timer304=1752222288837 HTTP/1.1" 200 146
************ - - [14/Jul/2025:16:24:58 +0800] "GET /api/usif/menu/add-mobile-menu-hits?userId=5440&menuurl=RsvrWarningPerson&_timer304=1752222288837 HTTP/1.1" 200 156
************ - - [14/Jul/2025:16:24:58 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220000000000000&basCode=&_timer304=1752222288837 HTTP/1.1" 200 226
************ - - [14/Jul/2025:16:24:59 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9267
************ - - [14/Jul/2025:16:25:02 +0800] "GET /api/dzsj/rvrsvr/get-rsvr-count?adcd=220500000000000&basCode=&_timer304=1752222292543 HTTP/1.1" 200 220
************ - - [14/Jul/2025:16:25:03 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9047
************ - - [14/Jul/2025:16:25:15 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9238
************ - - [14/Jul/2025:16:25:15 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9384
************ - - [14/Jul/2025:16:25:15 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9559
************ - - [14/Jul/2025:16:25:15 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9492
************ - - [14/Jul/2025:16:25:30 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 9044
************ - - [14/Jul/2025:16:25:30 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 8891
************* - - [14/Jul/2025:16:29:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752222554892 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:29:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752222554892 HTTP/1.1" 200 160
************* - - [14/Jul/2025:16:29:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752222566888 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:29:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752222566888 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:34:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752222865894 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:34:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752222865894 HTTP/1.1" 200 160
************* - - [14/Jul/2025:16:34:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752222867896 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:34:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752222867896 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:39:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752223155899 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:39:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752223155899 HTTP/1.1" 200 160
************* - - [14/Jul/2025:16:39:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752223168891 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:39:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752223168891 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:44:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752223469886 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:44:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752223469886 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:45:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752223525896 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:45:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752223525896 HTTP/1.1" 200 160
************* - - [14/Jul/2025:16:49:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1752223770886 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:49:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1752223770886 HTTP/1.1" 200 159
************* - - [14/Jul/2025:16:50:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1752223825888 HTTP/1.1" 200 -
************* - - [14/Jul/2025:16:50:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1752223825888 HTTP/1.1" 200 160
