package com.huitu.cloud.api.hsfxt.xzhq.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 蓄滞洪区社会经济统计
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
@TableName("XZH_SheHuiJingJi")
@ApiModel(value="XzhShehuijingji对象", description="蓄滞洪区社会经济统计")
public class XzhShehuijingji implements Serializable {

    @ApiModelProperty(value = "排序号")
    private Integer sortid;

    @ApiModelProperty(value = "查询条件")
    private String keyindex;

    @ApiModelProperty(value = "类型")
    private String ymssindex;

    @ApiModelProperty(value = "行政区编码")
    private String xzqhindex;

    @ApiModelProperty(value = "行政区名称")
    private String mingcheng;

    @ApiModelProperty(value = "影响户数")
    private Integer yxhs;

    @ApiModelProperty(value = "影响人口")
    private Integer yxrk;

    @ApiModelProperty(value = "影响房屋")
    private Integer yxfw;

    @ApiModelProperty(value = "淹没耕地")
    private Float ymgd;

    @ApiModelProperty(value = "影响GDP")
    private Float yxgdp;

    @ApiModelProperty(value = "淹没面积")
    private Float ymmj;

    @ApiModelProperty(value = "xxx")
    private Float yxtl;

    @ApiModelProperty(value = "xxx")
    private Float yxgl;

    @ApiModelProperty(value = "流速")
    private Float hsls;

    @ApiModelProperty(value = "安置点")
    private String anzhidian;

    @ApiModelProperty(value = "线路长度")
    private Float xianluchangdu;

    @ApiModelProperty(value = "转移时间")
    private Float zhuanyishijian;

    @ApiModelProperty(value = "节点名称")
    private String name;

    @ApiModelProperty(value = "上级序号")
    private String pid;

    @ApiModelProperty(value = "下级节点")
    private List<XzhShehuijingji> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public List<XzhShehuijingji> getChildren() {
        return children;
    }

    public void setChildren(List<XzhShehuijingji> children) {
        this.children = children;
    }

    public Integer getSortid() {
        return sortid;
    }

    public void setSortid(Integer sortid) {
        this.sortid = sortid;
    }

    public String getKeyindex() {
        return keyindex;
    }

    public void setKeyindex(String keyindex) {
        this.keyindex = keyindex;
    }

    public String getYmssindex() {
        return ymssindex;
    }

    public void setYmssindex(String ymssindex) {
        this.ymssindex = ymssindex;
    }

    public String getXzqhindex() {
        return xzqhindex;
    }

    public void setXzqhindex(String xzqhindex) {
        this.xzqhindex = xzqhindex;
    }

    public String getMingcheng() {
        return mingcheng;
    }

    public void setMingcheng(String mingcheng) {
        this.mingcheng = mingcheng;
    }

    public Integer getYxhs() {
        return yxhs;
    }

    public void setYxhs(Integer yxhs) {
        this.yxhs = yxhs;
    }

    public Integer getYxrk() {
        return yxrk;
    }

    public void setYxrk(Integer yxrk) {
        this.yxrk = yxrk;
    }

    public Integer getYxfw() {
        return yxfw;
    }

    public void setYxfw(Integer yxfw) {
        this.yxfw = yxfw;
    }

    public Float getYmgd() {
        return ymgd;
    }

    public void setYmgd(Float ymgd) {
        this.ymgd = ymgd;
    }

    public Float getYxgdp() {
        return yxgdp;
    }

    public void setYxgdp(Float yxgdp) {
        this.yxgdp = yxgdp;
    }

    public Float getYmmj() {
        return ymmj;
    }

    public void setYmmj(Float ymmj) {
        this.ymmj = ymmj;
    }

    public Float getYxtl() {
        return yxtl;
    }

    public void setYxtl(Float yxtl) {
        this.yxtl = yxtl;
    }

    public Float getYxgl() {
        return yxgl;
    }

    public void setYxgl(Float yxgl) {
        this.yxgl = yxgl;
    }

    public Float getHsls() {
        return hsls;
    }

    public void setHsls(Float hsls) {
        this.hsls = hsls;
    }

    public String getAnzhidian() {
        return anzhidian;
    }

    public void setAnzhidian(String anzhidian) {
        this.anzhidian = anzhidian;
    }

    public Float getXianluchangdu() {
        return xianluchangdu;
    }

    public void setXianluchangdu(Float xianluchangdu) {
        this.xianluchangdu = xianluchangdu;
    }

    public Float getZhuanyishijian() {
        return zhuanyishijian;
    }

    public void setZhuanyishijian(Float zhuanyishijian) {
        this.zhuanyishijian = zhuanyishijian;
    }


}
