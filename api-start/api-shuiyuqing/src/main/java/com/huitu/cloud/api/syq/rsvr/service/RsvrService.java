package com.huitu.cloud.api.syq.rsvr.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.syq.rsvr.entity.*;
import com.huitu.cloud.api.xxjh.smallreser.entity.BsnRsvrrecorderfileR;
import com.huitu.cloud.validation.constraints.SqlInjection;
import org.springframework.validation.annotation.Validated;

import java.io.OutputStream;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 水库水情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-09
 */
@Validated
public interface RsvrService extends IService<StRsvrR> {
    /**
     * 最新水库水情查询
     *
     * @param adcd     测站
     * @param stm      开始时间
     * @param etm      结束时间
     * @param stnm     测站名称
     * @param bscd     流域编码
     * @param stType   测站归属类型 1、 水文 2 、山洪    4、 小水库报讯
     * @param rvtp     水库类型  4,5 大型  3 中型 1、小二型  2、小一型
     * @param dataFlag
     * @param warnFlag 是否超汛限
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param isOut    辖区内与辖区外
     * @return
     */
    IPage<StRsvrVo> getRsvrLatestByConditon(@SqlInjection String adcd, @SqlInjection String stm, @SqlInjection String etm, @SqlInjection String stnm, @SqlInjection String bscd, @SqlInjection List<String> stType, @SqlInjection String rvtp,
                                            boolean dataFlag, boolean warnFlag, int pageNum, int pageSize, @SqlInjection List<String> isOut, @SqlInjection List<String> isFollow, boolean warnOpenFlag, Double drz, boolean videoFlag);

    /**
     * 水位数据对比分析
     *
     */
    IPage<StRsvrVo> getRsvrCompareZList(@SqlInjection String adcd, @SqlInjection String stm, @SqlInjection String etm, @SqlInjection String stnm, @SqlInjection String bscd, @SqlInjection List<String> stType, @SqlInjection String rvtp,
                                            int pageNum, int pageSize, Double drz);

    void ExportRsvrCompareZList(@SqlInjection String adcd, @SqlInjection String stm, @SqlInjection String etm, @SqlInjection String stnm, @SqlInjection String bscd, @SqlInjection List<String> stType, @SqlInjection String rvtp,
                                int pageNum, int pageSize, Double drz);
    /**
     * 查询单站实时水位列表
     *
     * @param stcd
     * @param stm
     * @param etm
     * @return
     */
    List<StRsvrTmVo> getRsvrByStcdAndTm(String stcd, String stm, String etm);

    /**
     * 导出水库水情预报实时数据
     *
     * @param stcd
     * @param stm
     * @param etm
     */
    void exportTmList(String stcd, String stm, String etm);


    /**
     * 根据测站编码查询 库（湖）容曲线表用于存储水库（湖）的施策时间
     *
     * @param stcd 测站编码
     * @return
     */
    List<String> getStZvarLbByStcd(String stcd);

    /**
     * 根据测站编码以及施策时间
     *
     * @param stcd 测站编码
     * @param mstm 实测时间
     * @return
     */
    List<StZvarlB> getStZvarLbByStcdAndMstm(String stcd, String mstm);

    /**
     * 根据测站编码查询水库防洪指标
     *
     * @param stcd
     * @return
     */
    StRsvrfcchB getStRsvrfcchbByStcd(String stcd);

    /**
     * 根据测站编码查询水库汛限水位
     *
     * @param stcd
     * @return
     */
    List<StRsvrfsrB> getStRsvrfsrbByStcd(String stcd);

    /**
     * 多站水库水情预报
     *
     * @param adcd     测站编码
     * @param wnstatus 超讯 超正常标识
     * @param stnm     测站名称
     * @param stm      开始时间
     * @param etm      结束时间
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return
     */
    IPage<StReglatfSqyb> getStReglaTbyInfo(String adcd, String wnstatus, String stnm, Date stm, Date etm,List<String> stcds, int pageNum, int pageSize);

    /**
     * 查询最新水库水情信息
     *
     * @param stm      开始时间
     * @param etm      结束时间
     * @param stList   测站编码集合
     * @param tmType   时间类型
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return
     */
    IPage<RsvrDayAvg> getDayAvgRsvrByPage(String stm, String etm, List<String> stList, String tmType, int pageNum, int pageSize);

    /**
     * 单站水库水情预报
     *
     * @param stcd 测站编码
     * @param ymdh 预报开始时间（发生时间）
     * @return
     */
    List<StReglatfSqybd> getStReglaTbyAdcd(String stcd, String ymdh);

    /**
     * 导出水库水情预报数据
     *
     * @param stcd
     * @param ymdh
     */
    void exportStreglatList(String stcd, String ymdh);

    /**
     * 查询最新水库水情水库类型统计信息
     *
     * @param adcd     测站
     * @param stm      开始时间
     * @param etm      结束时间
     * @param stnm     测站名称
     * @param bsnm     流域
     * @param stType   测站归属类型 1 水文 2 山洪 4 小水库报讯
     * @param rvType   水库类型  4,5 大型  3 中型 1、小二型  2、小一型
     * @param dataFlag
     * @param warnFlag 是否超汛限
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param isOut    辖区内与辖区外
     * @return
     */
    RsvrTypeCount getStRsvrCountInfo(@SqlInjection String adcd, @SqlInjection String stm, @SqlInjection String etm, @SqlInjection String stnm, @SqlInjection String bsnm, @SqlInjection List<String> stType, @SqlInjection String rvType, boolean dataFlag, boolean warnFlag, int pageNum, int pageSize, @SqlInjection List<String> isOut);

    /**
     * 查询水库告警信息
     *
     * @param adcd   测站
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stnm   测站名称
     * @param bscd   流域编码
     * @param stType 测站归属类型 1 水文 2 山洪
     * @param rvType 水库类型  4,5 大型  3 中型 1、小二型  2、小一型
     * @return
     */
    RsvrWarn getStRsvrWarn(@SqlInjection String adcd, @SqlInjection String stm, @SqlInjection String etm, @SqlInjection String stnm, @SqlInjection String bscd, @SqlInjection List<String> stType, @SqlInjection String rvType);

    /**
     * 查看所有水库站时间范围内最大的超汛限水位时间及相关特征值
     *
     * @param adcd    测站
     * @param stm     开始时间
     * @param etm     结束时间
     * @param stTypes 测站归属类型  1、水文，2、山洪  3、气象 多个值以逗号隔开
     * @param rvTypes 水库规模集合(1:大一 2：大二  3：中 4：小一  5：小二) 多个值以逗号隔开
     * @return
     */
    StRsvrVoOfMaxRzfsltdz getStRsvrVoMaxRzfsltdz(String adcd, String stm, String etm, String stTypes, String rvTypes);

    /**
     * 值班报告查询水库信息
     *
     * @param adcd 测站
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */

    RsvtInfoByDuty getStRsvrVoVyDuty(@SqlInjection String adcd, @SqlInjection String stm, @SqlInjection String etm, @SqlInjection String rsvrStType);

    /**
     * 最新水库水情导出
     *
     * @param adcd     测站
     * @param stm      开始时间
     * @param etm      结束时间
     * @param stnm     测站名称
     * @param bsnm     流域
     * @param stType   测站归属类型 1、 水文 2 、山洪    4、 小水库报讯
     * @param rvType   水库类型  4,5 大型  3 中型 1、小二型  2、小一型
     * @param dataFlag
     * @param warnFlag 是否超汛限
     * @param isOut    辖区内与辖区外
     * @param type     导出类型 1：综合监视水库水情  2：最新水库水情查询
     * @param videoFlag 是否有视频
     */
    void ExportStRsvrLatestList(@SqlInjection String adcd, @SqlInjection String stm, @SqlInjection String etm, @SqlInjection String stnm, @SqlInjection String bsnm, @SqlInjection List<String> stType, @SqlInjection String rvType, boolean dataFlag, boolean warnFlag, boolean warnOpenFlag, @SqlInjection List<String> isOut, @SqlInjection String type, Double drz, boolean videoFlag);

    /**
     * 导出水库日旬月均值水情信息
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stList 测站编码集合
     * @param tmType 时间类型
     * @return
     */
    void ExportDayAvgRsvrByPage(String stm, String etm, List<String> stList, String tmType);

    /**
     * 查看所有水库站时间范围内最大的超汛限水位时间及相关特征值
     *
     * @param adcd 测站
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    void exportStRsvrVoMaxRzfsltdz(String adcd, String stm, String etm, String stTypes, String rvTypes);

    /**
     * 根据测站字符串查询小水库风险图和调查评价数据
     *
     * @param stcd 测站
     * @return
     */
    List<ObjOnlyIACAdinfo> getObjOnlyIACAdinfoByStcd(String stcd);

    /**
     * 水库水情查看
     *
     * @param bgtm   开始时间
     * @param endtm  结束时间
     * @param addvcd 政区编码
     * @param stnm   水库名称
     * @return
     */
    List<ReservoirWaterinfo> getReservoirWaterRegimePage(String bgtm, String endtm, String addvcd, String stnm);

    /**
     * 水库水情查看
     *
     * @param tm   时间
     * @param stcd 测站编码
     * @return
     */
    List<ReservoirWaterinfo> getReservoirWaterRegimePhoto(String tm, String stcd);


    /**
     * 水库水情查看照片对比
     *
     * @param stcd 测站编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    List<BsnRsvrrecorderfileR> getPhotoComparison(String stcd, String stm, String etm);


    /**
     * 水库最大连续超汛限天数统计
     *
     * @param adcd    政区编码
     * @param stm     开始时间
     * @param etm     结束时间
     * @param stTypes 数据来源（1=水文、2=山洪、4=人工报讯小型水库、5=运管人工，6=运管自动）多个值以英文逗号隔开
     * @param rvTypes 水库规模（1=小(二)型、2=小(一)型、3=中型、4=大(二)型、5=大(一)型）多个值以英文逗号隔开
     * @return
     */
    List<RsvrContinueAlermVo> getRsvrContinueAlerm(String adcd, String stm, String etm, String stTypes, String rvTypes);

    void exportRsvrContinueMaxAlermDays(RsvrContinueAlermQuery query, OutputStream output);
    /**
     * 统计政区下时间段内测站超警戒情况
     *
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<RsvrWarnByAdcdTmVo> selectWarnStatisticsByAdcdTm(String adcd, String stm, String etm);

    /**
     * 多站水库水情最新预报
     *
     * @param adcd
     * @param bscd
     * @param stm
     * @param etm
     * @return
     */
    List<StReglatfSqyb> getForecastLatestTime(String adcd, String bscd, String stm, String etm);

    /**
     * 纳雨能力分析
     *
     * @param resCode 水库工程编码
     * @return
     */
    List<ReservoirRain> getContainRainList(String resCode);

    /**
     * 查询测站对应水库集水面积范围内测站的平均雨量
     *
     * @param stcd
     * @param stm
     * @param etm
     * @return
     */
    List<RsvrAvgDrnaVo> getRsvrAvgDrnaByStcd(String stcd, String stm, String etm);

    /**
     * 水库集水面积内测站降雨列表
     *
     * @param basCode
     * @param stm
     * @param etm
     * @param stTypes
     * @return
     */
    List<RsvrRainList> getStRsvrRainList(String basCode, String stm, String etm, String stTypes);

    /**
     * 水库降雨展开查询雨量站
     * @param resCode
     * @param stm
     * @param etm
     * @param stTypes
     * @return
     */
    List<RsvrRainChildList> getRsvrRainChildList(String resCode, String stm, String etm, String stTypes);

    /**
     * 导出水库集水面积内测站降雨列表
     *
     * @param adcd
     * @param stm
     * @param etm
     * @param stTypes
     */
    void exportStRsvrRainList(String adcd, String stm, String etm, List<String> stTypes);

    /**
     * 临界告警
     *
     * @param query
     * @return
     */
    List<RsvrNearWarnVo> selectNearWarnTemp(RsvrNearWarnQuery query);

    /**
     * 防洪作战图批量查询水库水位
     *
     * @param query
     * @return
     */
    IPage<RsvrZuoZhanTuVo> getRsvrZuoZhanTuList(RsvrZuoZhanTuQuery query);

    /**
     * 防洪作战图根据水库测站编码查询水库信息
     *
     * @param query
     * @return
     */
    RsvrZuoZhanTuVo getRsvrZuoZhanTu(RsvrZuoZhanTuQuery query);

}
