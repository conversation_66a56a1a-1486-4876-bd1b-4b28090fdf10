<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwRelWarningMessageDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    
    <select id="getWarningFlag" resultType="com.huitu.cloud.api.ew.entity.ext.EwWarningFlag">
        SELECT TOP (1) WARN_ID,
                       GRADE_ID WARN_GRADE_ID
        FROM EW_REL_WARNING_MESSAGE
        WHERE MSG_ID = #{msgId}
    </select>
    <insert id="insert">
        INSERT INTO EW_REL_WARNING_MESSAGE(MSG_ID, WARN_ID, GRADE_ID, STATUS_ID, LATEST_TIME)
        VALUES (#{msgId}, #{warnId}, #{gradeId}, #{statusId}, GETDATE())
    </insert>
</mapper>
