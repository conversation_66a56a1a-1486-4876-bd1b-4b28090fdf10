package com.huitu.cloud.api.ewci.hydropower.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="ShpwrRVo对象", description="小水电集合")
public class ShpwrRVo extends BsnShpwrR {
    @ApiModelProperty(value = "下级行政区划名称")
    private String xadnm;

    @ApiModelProperty(value = "下级行政区划")
    private String xadcd;

    @ApiModelProperty(value = "行政区划名称")
    private String adnm;

    @ApiModelProperty(value = "下级行政区划")
    private String adcd;

    @ApiModelProperty(value = "测站编码")
    private String stnm;

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }
}
