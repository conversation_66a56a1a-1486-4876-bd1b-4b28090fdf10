package com.huitu.cloud.api.common.liuyu.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-09
 */
@TableName("BSN_BAS_ST")
@ApiModel(value="BsnBasSt对象", description="")
public class BsnBasSt extends Model<BsnBasSt> {

    private static final long serialVersionUID=1L;

    @TableField("STCD")
    private String stcd;

    @TableField("BAS_CODE")
    private String basCode;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getBasCode() {
        return basCode;
    }

    public void setBasCode(String basCode) {
        this.basCode = basCode;
    }

    @Override
    protected Serializable pkVal() {
        return null;
    }

    @Override
    public String toString() {
        return "BsnBasSt{" +
        "stcd=" + stcd +
        ", basCode=" + basCode +
        "}";
    }
}
