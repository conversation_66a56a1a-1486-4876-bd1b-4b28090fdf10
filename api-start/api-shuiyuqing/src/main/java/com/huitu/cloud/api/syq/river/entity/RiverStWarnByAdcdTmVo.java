package com.huitu.cloud.api.syq.river.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel(value = "RiverStWarnByAdcdTmVo",description = "河道告警时间段统计类")
public class RiverStWarnByAdcdTmVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测站编码")
    private String stcd;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "时间")
    private LocalDateTime tm;
    @ApiModelProperty(value = "水位")
    private BigDecimal z;
    @ApiModelProperty(value = "警戒水位")
    private BigDecimal wrz;
    @ApiModelProperty(value = "流量")
    private BigDecimal q;
    @ApiModelProperty(value = "超警戒水位")
    private BigDecimal zwrz;
    @ApiModelProperty(value = "警戒流量")
    private BigDecimal wrq;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public BigDecimal getZ() {
        return z;
    }

    public void setZ(BigDecimal z) {
        this.z = z;
    }

    public BigDecimal getWrz() {
        return wrz;
    }

    public void setWrz(BigDecimal wrz) {
        this.wrz = wrz;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public BigDecimal getZwrz() {
        return zwrz;
    }

    public void setZwrz(BigDecimal zwrz) {
        this.zwrz = zwrz;
    }

    public BigDecimal getWrq() {
        return wrq;
    }

    public void setWrq(BigDecimal wrq) {
        this.wrq = wrq;
    }
}
