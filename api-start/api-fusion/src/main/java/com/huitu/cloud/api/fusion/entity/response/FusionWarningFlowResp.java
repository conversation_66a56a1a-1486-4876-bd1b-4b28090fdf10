package com.huitu.cloud.api.fusion.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 山洪预警流程响应对象
 */
@ApiModel(value = "山洪预警流程响应对象")
@Data
public class FusionWarningFlowResp implements Serializable {
    private static final long serialVersionUID = 1L;

   @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARN_ID")
    private String warnId;

    @ApiModelProperty(value = "预警状态ID")
    @TableField(value = "WARN_STATUS_ID")
    private Integer warnStatusId;

    @ApiModelProperty(value = "备注")
    @TableField(value = "REMARK")
    private String remark;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_BY")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "预警状态名称")
    @TableField(exist = false)
    private String warnStatusName;

    @ApiModelProperty(value = "预警状态简称")
    @TableField(exist = false)
    private String warnStatusShortName;

}
