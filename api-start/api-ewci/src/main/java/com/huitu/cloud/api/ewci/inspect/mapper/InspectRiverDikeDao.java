package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectRiverDike;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 河道及堤防名称
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectRiverDikeDao {


    /**
     * 添加 河道及堤防名称
     *
     * @param entity
     * @return
     */
    int insertInspectRiverDike(BnsInspectRiverDike entity);

    /**
     * 修改  河道及堤防名称
     *
     * @param entity
     * @return
     */
    int updateInspectRiverDike(BnsInspectRiverDike entity);

    /**
     * 删除 河道及堤防名称
     *
     * @return
     */
    int deleteInspectRiverDike(@Param("inspectId") String inspectId);

    List<BnsInspectRiverDike> getInspectRiverDikeList(@Param("adcd") String adcd, @Param("year") String year,@Param("riverDikeName")  String riverDikeName);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

