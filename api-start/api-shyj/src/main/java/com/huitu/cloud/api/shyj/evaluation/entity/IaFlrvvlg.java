package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 重要沿河村落居民户调查成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-18
 */
@TableName("IA_C_FLRVVLG")
@ApiModel(value="IaCFlrvvlg对象", description="重要沿河村落居民户调查成果表")
public class IaFlrvvlg extends  RelevantInfo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "沿河村落居民户编码")
    private String avrcd;

    @ApiModelProperty(value = "户主名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "基准点经度")
    @TableField("BLGTD")
    private Double blgtd;

    @ApiModelProperty(value = "基准点纬度")
    @TableField("BLTTD")
    private Double blttd;

    @ApiModelProperty(value = "基准点高程")
    @TableField("BELE")
    private Double bele;

    @ApiModelProperty(value = "家庭人口")
    @TableField("PTCOUNT")
    private Double ptcount;

    @ApiModelProperty(value = "建筑面积")
    @TableField("AREA")
    private Double area;

    @ApiModelProperty(value = "建筑类型")
    @TableField("BTYPE")
    private String btype;

    @ApiModelProperty(value = "结构形式")
    @TableField("STYPE")
    private String stype;

    @ApiModelProperty(value = "宅基经度")
    @TableField("LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "宅基纬度")
    @TableField("LTTD")
    private Double lttd;

    @ApiModelProperty(value = "宅基高程")
    @TableField("HELE")
    private String hele;

    @ApiModelProperty(value = "临水")
    @TableField("BWATER")
    private String bwater;

    @ApiModelProperty(value = "切坡")
    @TableField("BHILL")
    private String bhill;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getAvrcd() {
        return avrcd;
    }

    public void setAvrcd(String avrcd) {
        this.avrcd = avrcd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public Double getBlgtd() {
        return blgtd;
    }

    public void setBlgtd(Double blgtd) {
        this.blgtd = blgtd;
    }

    public Double getBlttd() {
        return blttd;
    }

    public void setBlttd(Double blttd) {
        this.blttd = blttd;
    }

    public Double getBele() {
        return bele;
    }

    public void setBele(Double bele) {
        this.bele = bele;
    }

    public Double getPtcount() {
        return ptcount;
    }

    public void setPtcount(Double ptcount) {
        this.ptcount = ptcount;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getBtype() {
        return btype;
    }

    public void setBtype(String btype) {
        this.btype = btype;
    }

    public String getStype() {
        return stype;
    }

    public void setStype(String stype) {
        this.stype = stype;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public String getHele() {
        return hele;
    }

    public void setHele(String hele) {
        this.hele = hele;
    }

    public String getBwater() {
        return bwater;
    }

    public void setBwater(String bwater) {
        this.bwater = bwater;
    }

    public String getBhill() {
        return bhill;
    }

    public void setBhill(String bhill) {
        this.bhill = bhill;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

}
