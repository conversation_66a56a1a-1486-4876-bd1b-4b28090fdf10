package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 重点城(集)镇调查评价涉水工程-堤防详情列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@ApiModel(value="DcjzDfDikeSs对象", description="重点城(集)镇调查评价涉水工程-堤防详情列表")
public class DcjzDfDikeSs extends Model<DcjzDfDikeSs> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "堤防名称")
    @TableId(value = "DIKE_NAME", type = IdType.NONE)
    private String dikeName;

    @ApiModelProperty(value = "河流岸别")
    @TableId(value = "RV_BANK", type = IdType.NONE)
    private String rvBank;

    @ApiModelProperty(value = "堤防跨界情况")
    @TableId(value = "DIKE_COR_BOUN", type = IdType.NONE)
    private String dikeCorBoun;

    @ApiModelProperty(value = "堤防类型")
    @TableId(value = "DIKE_TYPE", type = IdType.NONE)
    private String dikeType;

    @ApiModelProperty(value = "堤防型式")
    @TableId(value = "DIKE_STYL", type = IdType.NONE)
        private String dikeStyl;

    @ApiModelProperty(value = "堤防级别")
    @TableId(value = "DIKE_GRAD", type = IdType.NONE)
    private String dikeGrad;

    @ApiModelProperty(value = "规划防洪(潮)标准［重现期］（年）")
    @TableId(value = "PLAN_FL_STA", type = IdType.NONE)
    private String planFlSta;

    @ApiModelProperty(value = "堤防长度(m)")
    @TableId(value = "DIKE_LEN", type = IdType.NONE)
    private String dikeLen;

    @ApiModelProperty(value = "达到规划防洪（潮）标准的长度(m)")
    @TableId(value = "FL_STA_LEN", type = IdType.NONE)
    private String flStaLen;

    @ApiModelProperty(value = "高程系统")
    @TableId(value = "ELE_SYS", type = IdType.NONE)
    private String eleSys;

    @ApiModelProperty(value = "设计水（高潮）位(m)")
    @TableId(value = "DES_STAG", type = IdType.NONE)
    private String desStag;

    @ApiModelProperty(value = "堤顶高程起点高程(m)")
    @TableId(value = "DAM_CRE_BEG_ELE", type = IdType.NONE)
    private String damCreBegEle;

    @ApiModelProperty(value = "堤顶高程终点高程(m)")
    @TableId(value = "DAM_CRE_EDN_ELE", type = IdType.NONE)
    private String damCreEdnEle;

    @ApiModelProperty(value = "堤防高度(m)：（最大值）")
    @TableId(value = "DIKE_HIG_MAX", type = IdType.NONE)
    private String dikeHigMax;

    @ApiModelProperty(value = "堤顶宽度(m)：（最大值）")
    @TableId(value = "DIKE_WID_MAX", type = IdType.NONE)
    private String dikeWidMax;

    @ApiModelProperty(value = "堤防高度(m)：（最小值）")
    @TableId(value = "DIKE_HIG_MIN", type = IdType.NONE)
    private String dikeHigMin;

    @ApiModelProperty(value = "堤顶宽度(m)：（最小值）")
    @TableId(value = "DIKE_WID_MIN", type = IdType.NONE)
    private String dikeWidMin;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getDikeCorBoun() {
        return dikeCorBoun;
    }

    public void setDikeCorBoun(String dikeCorBoun) {
        this.dikeCorBoun = dikeCorBoun;
    }

    public String getDikeName() {
        return dikeName;
    }

    public void setDikeName(String dikeName) {
        this.dikeName = dikeName;
    }

    public String getRvBank() {
        return rvBank;
    }

    public void setRvBank(String rvBank) {
        this.rvBank = rvBank;
    }

    public String getDikeType() {
        return dikeType;
    }

    public void setDikeType(String dikeType) {
        this.dikeType = dikeType;
    }

    public String getDikeStyl() {
        return dikeStyl;
    }

    public void setDikeStyl(String dikeStyl) {
        this.dikeStyl = dikeStyl;
    }

    public String getDikeGrad() {
        return dikeGrad;
    }

    public void setDikeGrad(String dikeGrad) {
        this.dikeGrad = dikeGrad;
    }

    public String getPlanFlSta() {
        return planFlSta;
    }

    public void setPlanFlSta(String planFlSta) {
        this.planFlSta = planFlSta;
    }

    public String getDikeLen() {
        return dikeLen;
    }

    public void setDikeLen(String dikeLen) {
        this.dikeLen = dikeLen;
    }

    public String getFlStaLen() {
        return flStaLen;
    }

    public void setFlStaLen(String flStaLen) {
        this.flStaLen = flStaLen;
    }

    public String getEleSys() {
        return eleSys;
    }

    public void setEleSys(String eleSys) {
        this.eleSys = eleSys;
    }

    public String getDesStag() {
        return desStag;
    }

    public void setDesStag(String desStag) {
        this.desStag = desStag;
    }

    public String getDamCreBegEle() {
        return damCreBegEle;
    }

    public void setDamCreBegEle(String damCreBegEle) {
        this.damCreBegEle = damCreBegEle;
    }

    public String getDamCreEdnEle() {
        return damCreEdnEle;
    }

    public void setDamCreEdnEle(String damCreEdnEle) {
        this.damCreEdnEle = damCreEdnEle;
    }

    public String getDikeHigMax() {
        return dikeHigMax;
    }

    public void setDikeHigMax(String dikeHigMax) {
        this.dikeHigMax = dikeHigMax;
    }

    public String getDikeWidMax() {
        return dikeWidMax;
    }

    public void setDikeWidMax(String dikeWidMax) {
        this.dikeWidMax = dikeWidMax;
    }

    public String getDikeHigMin() {
        return dikeHigMin;
    }

    public void setDikeHigMin(String dikeHigMin) {
        this.dikeHigMin = dikeHigMin;
    }

    public String getDikeWidMin() {
        return dikeWidMin;
    }

    public void setDikeWidMin(String dikeWidMin) {
        this.dikeWidMin = dikeWidMin;
    }

    @Override
    public String toString() {
        return "DcjzDfDikeSs{" +
                "adnm='" + adnm + '\'' +
                ", dikeName='" + dikeName + '\'' +
                ", rvBank='" + rvBank + '\'' +
                ", dikeCorBoun='" + dikeCorBoun + '\'' +
                ", dikeType='" + dikeType + '\'' +
                ", dikeStyl='" + dikeStyl + '\'' +
                ", dikeGrad='" + dikeGrad + '\'' +
                ", planFlSta='" + planFlSta + '\'' +
                ", dikeLen='" + dikeLen + '\'' +
                ", flStaLen='" + flStaLen + '\'' +
                ", eleSys='" + eleSys + '\'' +
                ", desStag='" + desStag + '\'' +
                ", damCreBegEle='" + damCreBegEle + '\'' +
                ", damCreEdnEle='" + damCreEdnEle + '\'' +
                ", dikeHigMax='" + dikeHigMax + '\'' +
                ", dikeWidMax='" + dikeWidMax + '\'' +
                ", dikeHigMin='" + dikeHigMin + '\'' +
                ", dikeWidMin='" + dikeWidMin + '\'' +
                '}';
    }
}
