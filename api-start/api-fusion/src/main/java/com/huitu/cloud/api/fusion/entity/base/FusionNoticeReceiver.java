package com.huitu.cloud.api.fusion.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警人工通知接收人
 */
public class FusionNoticeReceiver implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "手机号码")
    @TableField("PHONE_NO")
    private String phoneNo;
    @ApiModelProperty(value = "接收人姓名")
    @TableField("RECEIVER")
    private String receiver;
    @ApiModelProperty(value = "接收人职务")
    @TableField("POSITION")
    private String position;
    @ApiModelProperty(value = "标签，详情请查看数据库设计文档")
    @TableField("TAG：110A=县级领导、110B=水旱灾害防御工作人员、1101=县级责任人、1102=乡镇责任人、1103=行政村责任人、1104=自然村责任人、1105=监测责任人、1106=预警责任人、1107=转移责任人")
    private String tag;
    @ApiModelProperty(value = "叫应ID")
    @TableField(value = "CALL_ID")
    @JsonIgnore
    private String callId;
    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARN_ID")
    @JsonIgnore
    private String warnId;
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @ApiModelProperty(value = "处理时间")
    @TableField(value = "CONFIRM_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    public FusionNoticeReceiver() {
    }

    public FusionNoticeReceiver(FusionNoticeReceiver obj) {
        this.phoneNo = obj.getPhoneNo();
        this.receiver = obj.getReceiver();
        this.position = obj.getPosition();
        this.tag = obj.getTag();
        this.callId = obj.getCallId();
        this.warnId = obj.getWarnId();
        this.createTime = obj.getCreateTime();
        this.confirmTime = obj.getConfirmTime();
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getCallId() {
        return callId;
    }

    public void setCallId(String callId) {
        this.callId = callId;
    }

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }
}
