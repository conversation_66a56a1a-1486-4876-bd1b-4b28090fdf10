package com.huitu.cloud.api.ewci.weChat.mapper;

import com.huitu.cloud.api.ewci.weChat.entity.WeChatRain;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RainSituationDao{

    /**
     * 查询所有测站累计雨量
     *
     * @param param
     * @return
     */
    List<WeChatRain> getAccpByTmAll(Map<String, Object> param);

    /**
     * 查询所有雨量测站
     *
     * @param param
     * @return
     */
    List<WeChatRain> getRainStInfo(@Param("map") Map<String, Object> param);

    /**
     * 获取未来一小时降雨信息
     *
     * @return
     */
    List<Map<String, Object>> getOneHourRain();

}
