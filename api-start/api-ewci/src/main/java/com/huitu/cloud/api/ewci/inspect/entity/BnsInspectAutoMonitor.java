package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 自动监测站
 */
@Data
@ApiModel(value = "自动监测站")
public class BnsInspectAutoMonitor extends BnsInspectReportForm {

    @ApiModelProperty(value = "站点编码")
    private String stcd;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;

    @ApiModelProperty(value = "站点名称")
    private String stnm;

    @ApiModelProperty(value = "站点类型")
    private String sttp;
}
