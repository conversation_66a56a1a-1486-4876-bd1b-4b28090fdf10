package com.huitu.cloud.api.ew.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ew.entity.ext.EwRiverWarningSending;
import com.huitu.cloud.api.ew.entity.request.EwRiverWarningCorrectRequest;
import com.huitu.cloud.api.ew.entity.request.EwRiverWarningRecordRequest;
import com.huitu.cloud.api.ew.entity.request.EwRiverWarningSendingRequest;
import com.huitu.cloud.api.ew.entity.response.EwRiverWarningRecordResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarningGradeSummaryResponse;
import com.huitu.cloud.api.ew.exception.WarningException;
import com.huitu.cloud.api.ew.service.EwRiverWarningService;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import com.huitu.cloud.api.usif.util.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 河道监测预警服务
 *
 * <AUTHOR>
 */
@Api(tags = "河道监测预警服务")
@RestController
@RequestMapping("/api/ew/river/warning")
public class EwRiverWarningResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "5e7cb13c-de99-b776-e383-08f2b16d335d";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final LoginUtils loginUtils;
    private final EwRiverWarningService baseService;

    public EwRiverWarningResource(LoginUtils loginUtils, EwRiverWarningService baseService) {
        this.loginUtils = loginUtils;
        this.baseService = baseService;
    }

    @ApiOperation(value = "获取河道预警分页列表", notes = "作者：曹宝金")
    @PostMapping("page-list")
    public ResponseEntity<SuccessResponse<IPage<EwRiverWarningRecordResponse>>> getPageList(@RequestBody @Validated EwRiverWarningRecordRequest request) {
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(request)));
        } catch (IllegalArgumentException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("获取河道预警分页列表失败", ex);
        }
    }

    @ApiOperation(value = "河道预警等级统计", notes = "作者：zyj")
    @PostMapping("grade-summary-list")
    public ResponseEntity<SuccessResponse<List<EwWarningGradeSummaryResponse>>> getRiverWarningGradeSummary(@RequestBody @Validated EwRiverWarningRecordRequest request) {
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRiverWarningGradeSummary(request)));
        } catch (IllegalArgumentException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("获取河道预警等级统计失败", ex);
        }
    }

    @ApiOperation(value = "获取河道预警记录", notes = "作者：曹宝金")
    @GetMapping("record")
    public ResponseEntity<SuccessResponse<EwRiverWarningRecordResponse>> getRecord(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRecord(warnId)));
    }

    @ApiOperation(value = "获取预警发送记录分页列表", notes = "作者：曹宝金")
    @PostMapping("sending-page-list")
    public ResponseEntity<SuccessResponse<IPage<EwRiverWarningSending>>> getSendingPageList(
            @RequestBody @Validated EwRiverWarningSendingRequest<EwRiverWarningSending> request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSendingPageList(request)));
    }

    @ApiOperation(value = "获取测站最新水位", notes = "作者：曹宝金")
    @GetMapping("latest-z")
    public ResponseEntity<SuccessResponse<BigDecimal>> getLatestZ(String stcd) {
        Assert.hasText(stcd, "参数[测站编码]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getLatestZ(stcd)));
    }

    @ApiOperation(value = "更正预警", notes = "作者：曹宝金")
    @PostMapping("correct")
    public ResponseEntity<SuccessResponse<Integer>> correct(@RequestBody @Validated EwRiverWarningCorrectRequest request) {
        try {
            UserInfos user = loginUtils.getCurrentLoginUser();
            int result = baseService.correct(request, user);
            if (result <= 0) {
                return ResponseEntity.ok(new SuccessResponse<>(this, "暂无需要更正的预警", 1));
            }
            return ResponseEntity.ok(new SuccessResponse<>(this, "更正预警成功", 0));
        } catch (WarningException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("更正预警失败", ex);
        }
    }
}
