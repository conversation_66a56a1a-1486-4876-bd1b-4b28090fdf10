package com.huitu.cloud.api.shyj.dept.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 预警响应部门信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("DEPT_B")
@ApiModel(value = "DeptB对象", description = "预警响应部门信息表")
public class DeptB extends Model<DeptB> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "所属部门编号")
    @TableField("PID")
    private String pid;

    @ApiModelProperty(value = "部门ID")
    @TableId(value = "DEPTCD", type = IdType.NONE)
    private String deptcd;

    @ApiModelProperty(value = "部门名称")
    @TableField("DEPTNM")
    private String deptnm;

    @ApiModelProperty(value = "传真号码")
    @TableField("FAXCODE")
    private String faxcode;

    @ApiModelProperty(value = "广播编码")
    @TableField("BROADCAST")
    private String broadcast;

    @ApiModelProperty(value = "部门职责")
    @TableField("DUTY")
    private String duty;

    @ApiModelProperty(value = "所属行政区")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "排序字段")
    @TableField("SORTNO")
    private BigDecimal sortno;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getDeptcd() {
        return deptcd;
    }

    public void setDeptcd(String deptcd) {
        this.deptcd = deptcd;
    }

    public String getDeptnm() {
        return deptnm;
    }

    public void setDeptnm(String deptnm) {
        this.deptnm = deptnm;
    }

    public String getFaxcode() {
        return faxcode;
    }

    public void setFaxcode(String faxcode) {
        this.faxcode = faxcode;
    }

    public String getBroadcast() {
        return broadcast;
    }

    public void setBroadcast(String broadcast) {
        this.broadcast = broadcast;
    }

    public String getDuty() {
        return duty;
    }

    public void setDuty(String duty) {
        this.duty = duty;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public BigDecimal getSortno() {
        return sortno;
    }

    public void setSortno(BigDecimal sortno) {
        this.sortno = sortno;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    protected Serializable pkVal() {
        return this.deptcd;
    }

    @Override
    public String toString() {
        return "DeptB{" +
                "pid=" + pid +
                ", deptcd=" + deptcd +
                ", deptnm=" + deptnm +
                ", faxcode=" + faxcode +
                ", broadcast=" + broadcast +
                ", duty=" + duty +
                ", adcd=" + adcd +
                ", sortno=" + sortno +
                ", remark=" + remark +
                "}";
    }
}
