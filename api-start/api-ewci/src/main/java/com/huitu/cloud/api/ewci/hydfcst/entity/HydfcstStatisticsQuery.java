package com.huitu.cloud.api.ewci.hydfcst.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * 水文预报查询条件
 *
 * <AUTHOR>
 */
@ApiModel(value = "水文预报查询条件")
public class HydfcstStatisticsQuery {

    @ApiModelProperty(value = "预报时间", required = true)
    @NotNull(message = "参数[预报时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private String fymdh;

    @ApiModelProperty(value = "流域编码", required = false)
    private String bscd;

    public @NotNull(message = "参数[预报时间]不能为空") String getFymdh() {
        return fymdh;
    }

    public void setFymdh(@NotNull(message = "参数[预报时间]不能为空") String fymdh) {
        this.fymdh = fymdh;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public Map<String, Object> toQueryParam() {
        Map<String, Object> params = new HashMap<>();
        params.put("fymdh", getFymdh());
        params.put("bscd", getBscd());
        return params;
    }
}
