package com.huitu.cloud.api.ewci.person.exception;

import com.huitu.cloud.api.ewci.person.entity.BnsRiverPersonTmp;

import java.util.List;

/**
 * 江河责任人信息导入异常
 *
 * <AUTHOR>
 */
public class RiverPersonImportException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    List<BnsRiverPersonTmp> errorData;

    public List<BnsRiverPersonTmp> getErrorData() {
        return errorData;
    }

    public void setErrorData(List<BnsRiverPersonTmp> errorData) {
        this.errorData = errorData;
    }

    public RiverPersonImportException(String message) {
        super(message);
    }

    public RiverPersonImportException(String message, List<BnsRiverPersonTmp> errorData) {
        super(message);

        this.errorData = errorData;
    }

    public RiverPersonImportException(String message, Throwable cause) {
        super(message, cause);
    }

    public RiverPersonImportException(String message, List<BnsRiverPersonTmp> errorData, Throwable cause) {
        super(message, cause);

        this.errorData = errorData;
    }
}
