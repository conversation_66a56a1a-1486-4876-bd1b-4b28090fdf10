package com.huitu.cloud.api.shyj.warn.controler;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.shyj.warn.entity.*;
import com.huitu.cloud.api.shyj.warn.service.WarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 政区预警记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-16
 */

@RestController
@Api(tags = "预警信息接口")
@RequestMapping("/api/shyj/warn")
public class WarnResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "b8a50c0f-1f5f-4758-bc4c-ef6d02f53e1e";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private WarnService baseService;

    @ApiOperation(value = "预警处理 包含内部预警，外部预警 ,会商 , 启动响应，结束预警 ", notes = "预警处理")
    @PostMapping(value = "update")
    public ResponseEntity<SuccessResponse<String>> updateWarnrecordr(@RequestBody WarnrecordrEx entity) throws Exception {
        String flag = baseService.updateWarn(entity);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "创建突发预警", notes = "创建突发预警")
    @PostMapping(value = "add")
    public ResponseEntity<SuccessResponse<StatusVo>> addWarnrecordr(@RequestBody WarnrecordR entity) throws Exception {
        String flag = baseService.saveWarn(entity);
        StatusVo statusVo = new StatusVo();
        statusVo.setRes(flag);
        statusVo.setWarnId(entity.getWarnId());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", statusVo));
    }

    @ApiOperation(value = "分页查询当前政区预警信息", notes = "分页查询当前政区预警信息")
    @PostMapping(value = "select-warn-by-page")
    public ResponseEntity<SuccessResponse<Page<WarnRelatedInfo>>> getWarnByPage(@RequestBody QueryWarnRelatedInfo warnRelatedInfo) throws Exception {
        IPage<WarnRelatedInfo> list = baseService.getWarnByPage(warnRelatedInfo.getStm(), warnRelatedInfo.getEtm(), warnRelatedInfo.getAdcd(), warnRelatedInfo.getQueryType(), warnRelatedInfo.getWarnGradeId(), warnRelatedInfo.getWarnStatusId(), warnRelatedInfo.getPageNum(), warnRelatedInfo.getPageSize());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询当前政区预警信息统计", notes = "查询当前政区预警信息统计")
    @PostMapping(value = "select-warn-by-count")
    public ResponseEntity<SuccessResponse<WarnStatisticsVo>> getWarnByCount(@RequestBody QueryWarnRelatedInfo warnRelatedInfo) throws Exception {
        WarnStatisticsVo list = baseService.getWarnByCount(warnRelatedInfo.getStm(), warnRelatedInfo.getEtm(), warnRelatedInfo.getAdcd(), warnRelatedInfo.getQueryType(), warnRelatedInfo.getWarnGradeId(), warnRelatedInfo.getWarnStatusId());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "当前政区预警信息导出", notes = "当前政区预警信息导出")
    @PostMapping(value = "export-warn-by-page")
    public void ExportWarnByPage(@RequestBody ExportWarnRelatedInfo warnRelatedInfo) throws Exception {
        baseService.ExportWarnByPage(warnRelatedInfo.getStm(), warnRelatedInfo.getEtm(), warnRelatedInfo.getAdcd(), warnRelatedInfo.getQueryType(), warnRelatedInfo.getWarnGradeId(), warnRelatedInfo.getWarnStatusId(), warnRelatedInfo.getType());
    }

    @ApiOperation(value = "单条预警信息", notes = "根据预警id单条预警信息")
    @GetMapping(value = "get-by-warnId")
    @ApiImplicitParams({@ApiImplicitParam(name = "warnId", value = "预警编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnRelatedInfo>> getWarnById(@RequestParam String warnId) throws Exception {
        WarnRelatedInfo warnRelatedInfo = baseService.getWarnById(warnId);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", warnRelatedInfo));
    }

    @ApiOperation(value = "单条预警信息", notes = "根据预警id单条预警信息")
    @GetMapping(value = "unsafe/get-by-warnId")
    @ApiImplicitParams({@ApiImplicitParam(name = "warnId", value = "预警编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnRelatedInfo>> getUnsafeWarnById(@RequestParam String warnId) throws Exception {
        WarnRelatedInfo warnRelatedInfo = baseService.getWarnById(warnId);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", warnRelatedInfo));
    }

    @ApiOperation(value = "政区预警统计", notes = "省市县预警状态统计概况")
    @PostMapping(value = "select-ad-warn-statistics")
    public ResponseEntity<SuccessResponse<WarnStatistics>> getAdWarnStatistics(@RequestBody QueryWarnStatistics queryWarnStatistics) throws Exception {
        WarnStatistics warnStatistics = baseService.getAdWarnStatistics(queryWarnStatistics.getStm(), queryWarnStatistics.getEtm(), queryWarnStatistics.getAdcd(), queryWarnStatistics.getQueryType());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", warnStatistics));
    }

    @ApiOperation(value = "政区预警统计下级列表", notes = "省市县预警状态统计概况下级列表（树形结构展示）")
    @PostMapping(value = "select-ad-warn-statistics-tree-list")
    public ResponseEntity<SuccessResponse<List<WarnStatistics>>> getAdWarnStatisticsTreeList(@RequestBody QueryWarnStatistics queryWarnStatistics) throws Exception {
        List<WarnStatistics> warnStatisticsList = baseService.getAdWarnStatisticsTreeList(queryWarnStatistics.getStm(), queryWarnStatistics.getEtm(), queryWarnStatistics.getAdcd(), queryWarnStatistics.getQueryType());
        return ResponseEntity.ok(new SuccessResponse(this, "OK", warnStatisticsList));
    }

    @ApiOperation(value = "危险区统计信息", notes = "危险区统计信息查询")
    @GetMapping(value = "select-warn-danad-statistics")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnDanadStatistics>> getWarnDanadStatistics(@RequestParam String adcd) throws Exception {
        WarnDanadStatistics danadStatistics = baseService.getWarnDanadStatistics(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", danadStatistics));
    }

    @ApiOperation(value = "防治区统计信息", notes = "防治区统计信息查询")
    @GetMapping(value = "select-warn-prevcnt-statistics")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnPrevcntStatistics>> getWarnPrevcntStatistics(@RequestParam String adcd) throws Exception {
        WarnPrevcntStatistics prevcntStatistics = baseService.getWarnPrevcntStatistics(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", prevcntStatistics));
    }

    @ApiOperation(value = "政区统计信息", notes = "政区统计信息查询")
    @GetMapping(value = "select-warn-ad-statistics")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnAdStatistics>> getWarnAdStatistics(@RequestParam String adcd) throws Exception {
        WarnAdStatistics adStatistics = baseService.getWarnAdStatistics(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", adStatistics));
    }

    @ApiOperation(value = "涉水工程统计信息", notes = "涉水工程统计信息查询")
    @GetMapping(value = "select-warn-wading-statistics")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnWadingStatistics>> getWarnWadingStatistics(@RequestParam String adcd) throws Exception {
        WarnWadingStatistics wadingStatistics = baseService.getWarnWadingStatistics(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", wadingStatistics));
    }

    @ApiOperation(value = "预警设施统计信息", notes = "预警设施统计信息查询")
    @GetMapping(value = "select-warn-jczbxlx-statistics")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnJczbxlxStatistics>> getWarnJczbxlxStatistics(@RequestParam String adcd) throws Exception {
        WarnJczbxlxStatistics jczbxlxStatistics = baseService.getWarnJczbxlxStatistics(adcd);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", jczbxlxStatistics));
    }

    @ApiOperation(value = "预警关联的测站或村预警列表", notes = "预警关联的测站或村预警列表")
    @GetMapping(value = "get-st-ad-warn-by-warnId")
    @ApiImplicitParams({@ApiImplicitParam(name = "warnId", value = "预警编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<StwarnRelatedInfo>>> getStAdWarnById(@RequestParam String warnId) throws Exception {
        List<StwarnRelatedInfo> stwarnRelatedList = baseService.getStAdWarnById(warnId);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", stwarnRelatedList));
    }

    @ApiOperation(value = "预警关联的测站或村预警列表", notes = "预警关联的测站或村预警列表")
    @GetMapping(value = "unsafe/get-st-ad-warn-by-warnId")
    @ApiImplicitParams({@ApiImplicitParam(name = "warnId", value = "预警编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<StwarnRelatedInfo>>> getUnsafeStAdWarnById(@RequestParam String warnId) throws Exception {
        List<StwarnRelatedInfo> stwarnRelatedList = baseService.getStAdWarnById(warnId);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", stwarnRelatedList));
    }

    @ApiOperation(value = "查询预警状态", notes = "预警状态列表")
    @GetMapping(value = "select-warn-status")
    @ApiImplicitParams({@ApiImplicitParam(name = "isClose", value = "是否查询已结束状态(true:查询所有状态;false:查询非关闭的所有状态)", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<WarningstatusB>>> getWarnStatusList(@RequestParam Boolean isClose) throws Exception {
        List<WarningstatusB> warnStatusList = baseService.getWarnStatusList(isClose);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", warnStatusList));
    }

    @ApiOperation(value = "查询预警等级", notes = "预警等级列表")
    @GetMapping(value = "select-warn-grade")
    public ResponseEntity<SuccessResponse<List<WarninggradeB>>> getWarnGradeList() throws Exception {
        List<WarninggradeB> warnGradeList = baseService.getWarnGradeList();
        return ResponseEntity.ok(new SuccessResponse(this, "OK", warnGradeList));
    }

    @ApiOperation(value = "查询预警类型", notes = "预警类型列表")
    @GetMapping(value = "select-warn-type")
    public ResponseEntity<SuccessResponse<List<WarningtypeB>>> getWarnTypeList() throws Exception {
        List<WarningtypeB> warnTypeList = baseService.getWarnTypeList();
        return ResponseEntity.ok(new SuccessResponse(this, "OK", warnTypeList));
    }

    @ApiOperation(value = "预警操作记录信息查询", notes = "查询预警的操作记录信息")
    @GetMapping(value = "select-warn-record-by-warnId")
    @ApiImplicitParams({@ApiImplicitParam(name = "warnId", value = "预警编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<List<RecordInfo>>> getWarnRecordById(@RequestParam String warnId) throws Exception {
        List<RecordInfo> recordInfos = baseService.getWarnRecordById(warnId);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", recordInfos));
    }

    @ApiOperation(value = "统计政区下的预警总个数以及市县个数", notes = "统计政区下的预警总个数以及市县个数(用于前端业务门户)")
    @GetMapping(value = "select-warn-by-ywmh")
    @ApiImplicitParams({@ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"), @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"), @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnInfoByYwmh>> getWarnInfoByYwmh(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        WarnInfoByYwmh warnInfoByYwmh = baseService.getWarnInfoByYwmh(adcd, stm, etm);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", warnInfoByYwmh));
    }

    @ApiOperation(value = "根据预警ID查询预警反馈", notes = "根据预警ID查询预警反馈")
    @GetMapping(value = "select-warn-feedback-by-warnId")
    @ApiImplicitParams({@ApiImplicitParam(name = "warnId", value = "预警编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnRelatedInfo>> getWarnFeedbackById(@RequestParam String warnId) throws Exception {
        WarnFeedback feedback = baseService.getWarnFeedbackById(warnId);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", feedback));
    }

    @ApiOperation(value = "添加或修改预警反馈", notes = "添加或修改预警反馈")
    @PostMapping(value = "addorupdate-warn-feedback")
    public ResponseEntity<SuccessResponse<Integer>> addOrUpdateWarnFeedback(@RequestBody WarnFeedback feedback) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.addOrUpdateWarnFeedback(feedback)));
    }

    @ApiOperation(value = "根据预警ID查询预警上报", notes = "根据预警ID查询预警上报")
    @GetMapping(value = "select-warn-disasterSituationReport-by-warnId")
    @ApiImplicitParams({@ApiImplicitParam(name = "warnId", value = "预警编码", required = true, dataType = "String")})
    public ResponseEntity<SuccessResponse<WarnDisasterSituationReport>> getWarnDisAsterSituationById(@RequestParam String warnId) throws Exception {
        WarnDisasterSituationReport report = baseService.getWarnDisAsterSituationById(warnId);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", report));
    }

    @ApiOperation(value = "添加或修改预警上报", notes = "添加或修改预警上报")
    @PostMapping(value = "addorupdate-warn-disasterSituationReport")
    public ResponseEntity<SuccessResponse<Integer>> addOrUpdatearnDisAsterSituation(@RequestBody WarnDisasterSituationReport report) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.addOrUpdatearnDisAsterSituation(report)));
    }

    @ApiOperation(value = "获取水旱责任人", notes = "赵英捷")
    @PostMapping(value = "select-person-list")
    public ResponseEntity<SuccessResponse<Page<WarnShPersonVo>>> getShPersonList(@RequestBody WarnShPersonQuery query) throws Exception {
        IPage<WarnShPersonVo> iPage = baseService.getShPersonList(query);
        return ResponseEntity.ok(new SuccessResponse(this, "OK", iPage));
    }
}







