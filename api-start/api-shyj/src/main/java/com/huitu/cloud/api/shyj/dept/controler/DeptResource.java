package com.huitu.cloud.api.shyj.dept.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.shyj.dept.entity.DeptB;
import com.huitu.cloud.api.shyj.dept.entity.QueryDept;
import com.huitu.cloud.api.shyj.dept.entity.RelevantDeptInfo;
import com.huitu.cloud.api.shyj.dept.service.DeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 部门信息  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@RestController
@Api(tags = "防汛部门信息接口")
@RequestMapping("/api/shyj/dept")
public class DeptResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "286b6f17-c447-7b83-2803-a3b3d509a965";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }
    @Autowired
    private DeptService baseService;

    @ApiOperation(value = "查询部门信息", notes = "查询部门信息列表")
    @PostMapping(value = "select-dept-list")
    public ResponseEntity<SuccessResponse<Page<RelevantDeptInfo>>> getDeptList(@RequestBody QueryDept queryDept) throws Exception {
        IPage<RelevantDeptInfo> list = baseService.getDeptList(queryDept.getAdcd(),queryDept.getDeptNm(),queryDept.getPid(),queryDept.getPageNum(),queryDept.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "添加部门信息", notes = "添加部门信息")
    @PostMapping(value = "add-dept")
    public ResponseEntity<SuccessResponse<String>> addDept(@RequestBody DeptB entity) throws Exception {
        String flag = baseService.addDept(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "更新部门信息", notes = "更新部门信息")
    @PostMapping(value = "update-dept")
    public ResponseEntity<SuccessResponse<String>> updateDept(@RequestBody DeptB entity) throws Exception {
        String flag = baseService.updateDept(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "删除部门信息", notes = "删除部门信息")
    @GetMapping(value = "del-dept")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "deptcd", value = "部门编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<String>> delDept(@RequestParam String adcd,@RequestParam String deptcd) throws Exception {
        String flag = baseService.delDept(adcd,deptcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "验证是否删除部门信息", notes = "验证是否删除部门信息")
    @GetMapping(value = "check-del-dept")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "deptcd", value = "部门编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<String>> checkDelDept(@RequestParam String adcd,@RequestParam String deptcd) throws Exception {
        String flag = baseService.checkDelDept(adcd,deptcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "查询部门信息树形列表", notes = "查询部门信息树形列表")
    @GetMapping(value = "select-dept-tree-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RelevantDeptInfo>>> getDeptTreeList(@RequestParam String adcd) throws Exception {
        List<RelevantDeptInfo> list = baseService.getDeptTreeList(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

}
