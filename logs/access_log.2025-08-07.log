************ - - [07/Aug/2025:09:08:28 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [07/Aug/2025:09:08:28 +0800] "GET /login HTTP/1.1" 302 -
************ - - [07/Aug/2025:09:08:28 +0800] "GET /login?code=NKsi9b&state=b2HokN HTTP/1.1" 302 -
************ - - [07/Aug/2025:09:08:29 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [07/Aug/2025:09:08:29 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1754528909965 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:32 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1754528909965 HTTP/1.1" 200 552
************ - - [07/Aug/2025:09:08:32 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1754528912938 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1754528912938 HTTP/1.1" 200 61649
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1754528913005 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1754528913005 HTTP/1.1" 200 10388
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754528913020 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754528913020 HTTP/1.1" 200 2009
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1754528913161 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754528913162 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754528913162 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-04+09:08:33&etm=&_timer304=1754528913162 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-07+08:00&etm=2025-08-07+10:00&filterCnt=6&_timer304=1754528913162 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754528913162 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1754528913161 HTTP/1.1" 200 1482
************ - - [07/Aug/2025:09:08:33 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754528913162 HTTP/1.1" 200 166
************ - - [07/Aug/2025:09:08:33 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-04+09:08:33&etm=&_timer304=1754528913162 HTTP/1.1" 200 156
************ - - [07/Aug/2025:09:08:33 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-07+08:00&etm=2025-08-07+10:00&filterCnt=6&_timer304=1754528913162 HTTP/1.1" 200 164
************ - - [07/Aug/2025:09:08:33 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [07/Aug/2025:09:08:33 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [07/Aug/2025:09:08:33 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [07/Aug/2025:09:08:33 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [07/Aug/2025:09:08:33 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754528913162 HTTP/1.1" 200 169
************ - - [07/Aug/2025:09:08:33 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754528913162 HTTP/1.1" 200 13016
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-06&_timer304=1754528913829 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/base/saas/token?_timer304=1754528913829 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:33 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1754528913848 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [07/Aug/2025:09:08:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [07/Aug/2025:09:08:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [07/Aug/2025:09:08:36 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [07/Aug/2025:09:08:36 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************ - - [07/Aug/2025:09:08:36 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [07/Aug/2025:09:08:36 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [07/Aug/2025:09:08:37 +0800] "GET /api/base/saas/token?_timer304=1754528913829 HTTP/1.1" 200 411
************ - - [07/Aug/2025:09:08:37 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [07/Aug/2025:09:08:37 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [07/Aug/2025:09:08:37 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [07/Aug/2025:09:08:37 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1754528913848 HTTP/1.1" 200 2009
************ - - [07/Aug/2025:09:08:37 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [07/Aug/2025:09:08:37 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [07/Aug/2025:09:08:37 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 10554
************ - - [07/Aug/2025:09:08:38 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 332
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754528919598 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754528919602 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754528919608 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754528919608 HTTP/1.1" 200 148
************ - - [07/Aug/2025:09:08:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754528919602 HTTP/1.1" 200 159
************ - - [07/Aug/2025:09:08:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754528919598 HTTP/1.1" 200 160
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/ewci/base/mal/write/168?_timer304=1754528919777 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-04+09:08:39&etm=&_timer304=1754528919916 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754528919916 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754528919916 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-07+08:00&etm=2025-08-07+10:00&filterCnt=6&_timer304=1754528919916 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754528919916 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754528919916 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [07/Aug/2025:09:08:39 +0800] "OPTIONS /api/hsfxt/city/get-bzqy-info?fxtType=MT2&_timer304=1754528919965 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:39 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [07/Aug/2025:09:08:39 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [07/Aug/2025:09:08:39 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-04+09:08:39&etm=&_timer304=1754528919916 HTTP/1.1" 200 156
************ - - [07/Aug/2025:09:08:39 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754528919916 HTTP/1.1" 200 166
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-07+08:00&etm=2025-08-07+10:00&filterCnt=6&_timer304=1754528919916 HTTP/1.1" 200 164
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754528919916 HTTP/1.1" 200 169
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/hsfxt/city/get-bzqy-info?fxtType=MT2&_timer304=1754528919965 HTTP/1.1" 200 1708
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1754528919916 HTTP/1.1" 200 12285
************ - - [07/Aug/2025:09:08:40 +0800] "OPTIONS /api/hsfxt/city/get-hslj-info?bianzhiquid=A1&_timer304=1754528920045 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:40 +0800] "OPTIONS /api/hsfxt/city/get-fq-info?bianzhiquid=A1&_timer304=1754528920045 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/hsfxt/city/get-fq-info?bianzhiquid=A1&_timer304=1754528920045 HTTP/1.1" 200 507
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/hsfxt/city/get-hslj-info?bianzhiquid=A1&_timer304=1754528920045 HTTP/1.1" 200 798
************ - - [07/Aug/2025:09:08:40 +0800] "OPTIONS /api/hsfxt/city/get-kk-info?jisuanfenquid=MA1,MA2&_timer304=1754528920082 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754528919916 HTTP/1.1" 200 13016
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/hsfxt/city/get-kk-info?jisuanfenquid=MA1,MA2&_timer304=1754528920082 HTTP/1.1" 200 319
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-08-06&_timer304=1754528913829 HTTP/1.1" 200 428
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/ewci/base/mal/write/168?_timer304=1754528919777 HTTP/1.1" 200 146
************ - - [07/Aug/2025:09:08:40 +0800] "OPTIONS /api/hsfxt/river/get-river-tree-info?key=MT2_A1_MA1_B1,MT2_A1_MA2_B2&bzqid=A1&hl=L1&name=&_timer304=1754528920497 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:40 +0800] "GET /api/hsfxt/river/get-river-tree-info?key=MT2_A1_MA1_B1,MT2_A1_MA2_B2&bzqid=A1&hl=L1&name=&_timer304=1754528920497 HTTP/1.1" 200 95587
************ - - [07/Aug/2025:09:08:47 +0800] "OPTIONS /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754528927044 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:47 +0800] "OPTIONS /api/common/liuyu/get-river-tree?_timer304=1754528927044 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:47 +0800] "GET /api/common/liuyu/get-river-tree?_timer304=1754528927044 HTTP/1.1" 200 3688
************ - - [07/Aug/2025:09:08:47 +0800] "OPTIONS /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754528927334 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:47 +0800] "GET /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754528927044 HTTP/1.1" 200 36688
************ - - [07/Aug/2025:09:08:47 +0800] "GET /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754528927334 HTTP/1.1" 200 36688
************ - - [07/Aug/2025:09:08:54 +0800] "OPTIONS /api/risk/river/get-tlpu-tree-list/AAB10L46/100?_timer304=1754528934491 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:54 +0800] "OPTIONS /api/risk/river/get-tlpu-info/AAB10L46/100?_timer304=1754528934491 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:08:54 +0800] "GET /api/risk/river/get-tlpu-info/AAB10L46/100?_timer304=1754528934491 HTTP/1.1" 200 260
************ - - [07/Aug/2025:09:08:54 +0800] "GET /api/risk/river/get-tlpu-tree-list/AAB10L46/100?_timer304=1754528934491 HTTP/1.1" 200 1987
************ - - [07/Aug/2025:09:11:50 +0800] "OPTIONS /api/hsfxt/city/get-bzqy-info?fxtType=MT2&_timer304=1754529110551 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:11:50 +0800] "GET /api/hsfxt/city/get-bzqy-info?fxtType=MT2&_timer304=1754529110551 HTTP/1.1" 200 1708
************ - - [07/Aug/2025:09:11:50 +0800] "OPTIONS /api/hsfxt/city/get-fq-info?bianzhiquid=A1&_timer304=1754529110573 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:11:50 +0800] "OPTIONS /api/hsfxt/city/get-hslj-info?bianzhiquid=A1&_timer304=1754529110573 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:11:50 +0800] "GET /api/hsfxt/city/get-fq-info?bianzhiquid=A1&_timer304=1754529110573 HTTP/1.1" 200 507
************ - - [07/Aug/2025:09:11:50 +0800] "GET /api/hsfxt/city/get-hslj-info?bianzhiquid=A1&_timer304=1754529110573 HTTP/1.1" 200 798
************ - - [07/Aug/2025:09:11:50 +0800] "OPTIONS /api/hsfxt/city/get-kk-info?jisuanfenquid=MA1,MA2&_timer304=1754529110594 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:11:50 +0800] "GET /api/hsfxt/city/get-kk-info?jisuanfenquid=MA1,MA2&_timer304=1754529110594 HTTP/1.1" 200 319
************ - - [07/Aug/2025:09:11:50 +0800] "OPTIONS /api/hsfxt/river/get-river-tree-info?key=MT2_A1_MA1_B1,MT2_A1_MA2_B2&bzqid=A1&hl=L1&name=&_timer304=1754529110908 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:11:50 +0800] "GET /api/hsfxt/river/get-river-tree-info?key=MT2_A1_MA1_B1,MT2_A1_MA2_B2&bzqid=A1&hl=L1&name=&_timer304=1754529110908 HTTP/1.1" 200 95587
************ - - [07/Aug/2025:09:11:54 +0800] "OPTIONS /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754529114210 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:11:54 +0800] "OPTIONS /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754529114494 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:11:54 +0800] "GET /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754529114210 HTTP/1.1" 200 36688
************ - - [07/Aug/2025:09:11:55 +0800] "GET /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754529114494 HTTP/1.1" 200 36688
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754529277139 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-07+08:00&etm=2025-08-07+10:00&filterCnt=6&_timer304=1754529277139 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-04+09:14:37&etm=&_timer304=1754529277139 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754529277139 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/hsfxt/city/get-bzqy-info?fxtType=MT2&_timer304=1754529277228 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754529277139 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:38 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:40 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-08-07+08:00&etm=2025-08-07+10:00&filterCnt=6&_timer304=1754529277139 HTTP/1.1" 200 164
************ - - [07/Aug/2025:09:14:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1754529277139 HTTP/1.1" 200 166
************ - - [07/Aug/2025:09:14:40 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [07/Aug/2025:09:14:40 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1754529277139 HTTP/1.1" 200 13016
************ - - [07/Aug/2025:09:14:40 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [07/Aug/2025:09:14:40 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-08-04+09:14:37&etm=&_timer304=1754529277139 HTTP/1.1" 200 156
************ - - [07/Aug/2025:09:14:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [07/Aug/2025:09:14:40 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [07/Aug/2025:09:14:40 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1754529277139 HTTP/1.1" 200 169
************ - - [07/Aug/2025:09:14:40 +0800] "GET /api/hsfxt/city/get-bzqy-info?fxtType=MT2&_timer304=1754529277228 HTTP/1.1" 200 1708
************ - - [07/Aug/2025:09:14:40 +0800] "OPTIONS /api/hsfxt/city/get-fq-info?bianzhiquid=A1&_timer304=1754529280842 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:40 +0800] "OPTIONS /api/hsfxt/city/get-hslj-info?bianzhiquid=A1&_timer304=1754529280842 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:40 +0800] "GET /api/hsfxt/city/get-fq-info?bianzhiquid=A1&_timer304=1754529280842 HTTP/1.1" 200 507
************ - - [07/Aug/2025:09:14:40 +0800] "GET /api/hsfxt/city/get-hslj-info?bianzhiquid=A1&_timer304=1754529280842 HTTP/1.1" 200 798
************ - - [07/Aug/2025:09:14:40 +0800] "OPTIONS /api/hsfxt/city/get-kk-info?jisuanfenquid=MA1,MA2&_timer304=1754529280878 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:40 +0800] "GET /api/hsfxt/city/get-kk-info?jisuanfenquid=MA1,MA2&_timer304=1754529280878 HTTP/1.1" 200 319
************ - - [07/Aug/2025:09:14:41 +0800] "OPTIONS /api/hsfxt/river/get-river-tree-info?key=MT2_A1_MA1_B1,MT2_A1_MA2_B2&bzqid=A1&hl=L1&name=&_timer304=1754529281318 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:41 +0800] "GET /api/hsfxt/river/get-river-tree-info?key=MT2_A1_MA1_B1,MT2_A1_MA2_B2&bzqid=A1&hl=L1&name=&_timer304=1754529281318 HTTP/1.1" 200 95587
************ - - [07/Aug/2025:09:14:43 +0800] "OPTIONS /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754529283570 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:43 +0800] "OPTIONS /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754529283861 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:44 +0800] "GET /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754529283570 HTTP/1.1" 200 36688
************ - - [07/Aug/2025:09:14:44 +0800] "GET /api/hsfxt/river-risk/select-river-risk-list?rvcode=&name=&pageNum=1&pageSize=-1&_timer304=1754529283861 HTTP/1.1" 200 36688
************ - - [07/Aug/2025:09:14:47 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1754529287652 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1754529287653 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:47 +0800] "OPTIONS /api/ew/warning/is-enabled?_timer304=1754529287654 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:14:49 +0800] "GET /api/ew/warning/is-enabled?_timer304=1754529287654 HTTP/1.1" 200 148
************ - - [07/Aug/2025:09:14:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1754529287653 HTTP/1.1" 200 159
************ - - [07/Aug/2025:09:14:49 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1754529287652 HTTP/1.1" 200 160
************ - - [07/Aug/2025:09:15:49 +0800] "OPTIONS /api/risk/river/get-tlpu-info/AAB10L46/100?_timer304=1754529349499 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:15:49 +0800] "OPTIONS /api/risk/river/get-tlpu-tree-list/AAB10L46/100?_timer304=1754529349499 HTTP/1.1" 200 -
************ - - [07/Aug/2025:09:15:49 +0800] "GET /api/risk/river/get-tlpu-info/AAB10L46/100?_timer304=1754529349499 HTTP/1.1" 200 260
************ - - [07/Aug/2025:09:15:49 +0800] "GET /api/risk/river/get-tlpu-tree-list/AAB10L46/100?_timer304=1754529349499 HTTP/1.1" 200 1987
