package com.huitu.cloud.api.duty.record.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
/**
 * <p>
 * 值班记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-15
 */
@ApiModel(value="RecordPersonById对象", description="值班信息Id集合")
public class RecordPersonById {
    @ApiModelProperty(value = "带班领导id集合")
    private List<String> leaderId;
    @ApiModelProperty(value = "主班id集合")
    private List<String> zhuId;
    @ApiModelProperty(value = "副班id集合")
    private List<String> fuId;
    @ApiModelProperty(value = "司机id集合")
    private List<String> driverId;
    @ApiModelProperty(value = "应急值班id集合")
    private List<String> yingjiId;

    public List<String> getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(List<String> leaderId) {
        this.leaderId = leaderId;
    }

    public List<String> getZhuId() {
        return zhuId;
    }

    public void setZhuId(List<String> zhuId) {
        this.zhuId = zhuId;
    }

    public List<String> getFuId() {
        return fuId;
    }

    public void setFuId(List<String> fuId) {
        this.fuId = fuId;
    }

    public List<String> getDriverId() {
        return driverId;
    }

    public void setDriverId(List<String> driverId) {
        this.driverId = driverId;
    }

    public List<String> getYingjiId() {
        return yingjiId;
    }

    public void setYingjiId(List<String> yingjiId) {
        this.yingjiId = yingjiId;
    }

}
