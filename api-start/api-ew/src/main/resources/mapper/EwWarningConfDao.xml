<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwWarningConfDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getConf" resultType="com.huitu.cloud.api.ew.entity.base.EwWarningConf">
        SELECT PLATFORM_ID,
               WARN_MODEL,
               WARN_TIMEOUT,
               ISSUER,
               ISSUING_UNIT,
               RECEIVING_UNIT,
               SMS_SIGN_NAME,
               EXCLUSIONS,
               FLAG
        FROM EW_WARNING_CONF
        WHERE PLATFORM_ID = #{platformId}
    </select>
    <select id="isEnabled" resultType="java.lang.Boolean">
        SELECT CAST(FLAG AS BIT)
        FROM EW_WARNING_CONF
        WHERE PLATFORM_ID = #{platformId}
    </select>
</mapper>