<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.adjoin.mapper.BnsAdjoinMessageDao">
    <select id="getMessageList" resultType="com.huitu.cloud.api.ewci.adjoin.entity.BnsAdjoinMessage">
        SELECT R.MSGID, R.XADCD, M.[MESSAGE], R.TS
        FROM BNS_ADJOIN_RECEIVER R
                 LEFT JOIN BNS_ADJOIN_MESSAGE M ON M.MSGID = R.MSGID
        WHERE R.USERID = #{map.userid}
          AND R.FLAG = #{map.flag}
          AND R.TS &gt;= #{map.stm}
        ORDER BY R.TS DESC
    </select>

    <update id="setReaded">
        UPDATE BNS_ADJOIN_RECEIVER SET FLAG = '1', TS = GETDATE()
        WHERE FLAG = '0' AND USERID = #{userid} AND (MSGID IN
        <foreach collection="msgids" item="msgid" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR MSGID IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{msgid}
        </foreach>)
    </update>
</mapper>
