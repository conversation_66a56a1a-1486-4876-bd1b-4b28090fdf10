<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.screen.fhjz.mapper.DikeScreenDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="queryDikeList" resultType="com.huitu.cloud.api.screen.fhjz.entity.DikeVo">
            select t1.dike_code,
                         t1.dike_name,
                         t2.bank,
                         t1.start_loc,
                         t1.end_loc,
                         t1.dike_grad,
                         convert(decimal(18,2),t1.dike_len/1000) dike_len
            from ATT_DIKE_BASE t1
            inner join BSN_DIKEEX_B t4 on t1.dike_code=t4.dike_code
            left join FHGC_DKCMIN t2 on t1.dike_code=t2.Dike_code
            left join REL_DIKE_AD t3  on t1.dike_code=t3.DIKE_CODE
            <where>
                <if test="dikeGrad!=null and dikeGrad!=''">
                    and CHARINDEX(t1.dike_grad,#{dikeGrad}) > 0
                </if>
                <if test="dikeName!=null and dikeName!=''">
                    and t1.dike_name like '%' + #{dikeName} + '%'
                </if>
            </where>
            order by ISNULL(t4.main_sort_no,9999) asc,t1.dike_grad asc,left(t3.ad_code,6) asc
    </select>

    <select id="queryDikeDetailList" resultType="com.huitu.cloud.api.screen.fhjz.entity.DikeDetailVo">
        select t1.dike_code,
                     t1.dike_name,
                     t5.rv_name,
                     t2.bank,
                     t1.start_loc,
                     t1.end_loc,
                     t1.DIKE_PATT,
                     t1.dike_grad,
                     t1.dike_len,
                    convert(decimal(18,2),t1.DIKE_HEIG_MAX) DIKE_HEIG_MAX,
                    convert(decimal(18,2),t1.DIKE_HEIG_MIN) DIKE_HEIG_MIN,
                    convert(decimal(18,2),t1.DIKE_TOP_WID_MIN) DIKE_TOP_WID_MIN,
                    convert(decimal(18,2),t1.DIKE_TOP_WID_MAX) DIKE_TOP_WID_MAX,
                     t6.FLCTSDLN,
                     t6.CNCLGTNB,
                     t8.PLAN_STANDARD
        from ATT_DIKE_BASE t1
        inner join BSN_DIKEEX_B t8 on t1.dike_code=t8.dike_code
        left join FHGC_DKCMIN t2 on t1.dike_code=t2.Dike_code
        left join REL_DIKE_AD t3  on t1.dike_code=t3.DIKE_CODE
        left join REL_DIKE_RV t4 on t1.dike_code=t4.dike_code
        left join ATT_RV_BASE t5 on t4.RV_CODE=t5.RV_CODE
        left join FHGC_BNBSIN t6 on t1.dike_code=t6.dike_code
        <where>
            <if test="dikeGrad!=null and dikeGrad!=''">
                and CHARINDEX(t1.dike_grad,#{dikeGrad}) > 0
            </if>
            <if test="dikeName!=null and dikeName!=''">
                and t1.dike_name like '%' +#{dikeName}+ '%'
            </if>
        </where>
        order by ISNULL(t8.main_sort_no,9999) asc,t1.dike_grad asc,left(t3.ad_code,6) asc
    </select>

    <select id="queryDikeInfo" resultType="map">
        select t1.dike_code,t1.dike_name
        from ATT_DIKE_BASE t1
        where t1.dike_code=#{dikeCode}
    </select>

   <select id="statisticDikeDetail" resultType="map">
       with t_res as (
        select t1.dike_code,
                     t5.adnm,
                     case when LEFT(t4.adcd,6) = '220581' then LEFT(t4.adcd,6) else LEFT(t4.adcd,#{lowLevel}) end adcd,
                     case when t1.dike_grad = '1' then 1 else 0 end LEVEL1,
                     case when t1.dike_grad = '2' then 1 else 0 end LEVEL2,
                     case when t1.dike_grad = '3' then 1 else 0 end LEVEL3,
                     case when t1.dike_grad = '4' then 1 else 0 end LEVEL4,
                     case when t1.dike_grad = '5' then 1 else 0 end LEVEL5,
                     case when t1.dike_grad not in ('1','2','3','4','5') then 1 else 0 end LEVEL6
        from ATT_DIKE_BASE t1 WITH(NOLOCK)
        inner join BSN_DIKEEX_B t2 WITH(NOLOCK)  on t1.dike_code=t2.dike_code
        left join REL_DIKE_AD t3 WITH(NOLOCK)  on t1.dike_code=t3.DIKE_CODE
        inner join MDT_ADCDINFO_B t4 WITH(NOLOCK) on t3.ad_code=t4.adcd
        inner join MDT_ADCDINFO_B t5 WITH(NOLOCK) on t5.adcd = left(t4.adcd,#{lowLevel}) + #{suffix}
        where t1.dike_grad is not null
        and LEFT(t4.adcd,#{level})= left(#{adcd}, #{level})
        )
        select t.adcd,
                    case when t.adcd = '220581' then '梅河口市' else t.adnm end adnm,
                  SUM(t.LEVEL1) LEVEL1,
                    SUM(t.LEVEL2) LEVEL2,
                    SUM(t.LEVEL3) LEVEL3,
                    SUM(t.LEVEL4) LEVEL4,
                    SUM(t.LEVEL5) LEVEL5,
                    SUM(t.LEVEL6) LEVEL6,
                    SUM(t.LEVEL1) + SUM(t.LEVEL2) + SUM(t.LEVEL3) + SUM(t.LEVEL4)+ SUM(t.LEVEL5)+ SUM(t.LEVEL6) TOTAL
        from t_res t
        group by t.adcd,t.adnm
        union all
        select left(#{adcd}, #{level}),'SUM',
                  SUM(t.LEVEL1) LEVEL1,
                    SUM(t.LEVEL2) LEVEL2,
                    SUM(t.LEVEL3) LEVEL3,
                    SUM(t.LEVEL4) LEVEL4,
                    SUM(t.LEVEL5) LEVEL5,
                    SUM(t.LEVEL6) LEVEL6,
                    SUM(t.LEVEL1) + SUM(t.LEVEL2) + SUM(t.LEVEL3) + SUM(t.LEVEL4)+ SUM(t.LEVEL5)+ SUM(t.LEVEL6) TOTAL
        from t_res t
   </select>
</mapper>
