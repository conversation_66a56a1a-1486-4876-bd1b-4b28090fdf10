package com.huitu.cloud.api.ewci.monitor.mapper;

import com.huitu.cloud.api.ewci.monitor.entity.IaCPrevad;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 防治区基本情况调查成果汇总Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface IaCPrevadDao {

    /**
     * 获取防治区基本情况调查成果汇总列表
     *
     * @param params 查询参数
     * @return 汇总列表
     **/
    List<IaCPrevad> getPrevadList(@Param("map") Map<String, Object> params);
}
