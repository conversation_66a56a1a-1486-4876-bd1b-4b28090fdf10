package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 无线广播站信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("IA_C_WBRINFO")
@ApiModel(value = "IaCWbrinfo对象", description = "无线广播站信息")
public class Wbrinfo extends RelevantInfo {
    @ApiModelProperty(value = "无线预警广播站编码")
    @TableField("WBRCD")
    private String wbrcd;
    @ApiModelProperty(value = "站点位置")
    @TableField("ADDRESS")
    private String address;
    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;
    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;
    @ApiModelProperty(value = "设备类型（1：“I 型”，2：“Ⅱ型”）")
    @TableField("TYPE")
    private String type;
    @ApiModelProperty(value = "建设日期")
    @TableField("BDATE")
    private String bdate;
    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private BigDecimal lgtd;
    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private BigDecimal lttd;
    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;
    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;
    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;
    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;
    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;

    @ApiModelProperty(value = "名称")
    @TableField("WBRNM")
    private String wbrnm;

    @ApiModelProperty(value = "SIM卡号")
    @TableField("SIMID")
    private String simid;

    @ApiModelProperty(value = "管理员")
    @TableField("ADMIN")
    private String admin;

    @ApiModelProperty(value = "管理员联系方式")
    @TableField("ADMINSIM")
    private String adminsim;

    @ApiModelProperty(value = "设备厂商编码")
    @TableField("DEVICEID")
    private String deviceid;

    @ApiModelProperty(value = "设备厂商名称")
    private String denm;

    public String getWbrnm() {
        return wbrnm;
    }

    public void setWbrnm(String wbrnm) {
        this.wbrnm = wbrnm;
    }

    public String getSimid() {
        return simid;
    }

    public void setSimid(String simid) {
        this.simid = simid;
    }

    public String getAdmin() {
        return admin;
    }

    public void setAdmin(String admin) {
        this.admin = admin;
    }

    public String getAdminsim() {
        return adminsim;
    }

    public void setAdminsim(String adminsim) {
        this.adminsim = adminsim;
    }

    public String getDeviceid() {
        return deviceid;
    }

    public void setDeviceid(String deviceid) {
        this.deviceid = deviceid;
    }

    public String getDenm() {
        return denm;
    }

    public void setDenm(String denm) {
        this.denm = denm;
    }

    public String getWbrcd() {
        return wbrcd;
    }

    public void setWbrcd(String wbrcd) {
        this.wbrcd = wbrcd;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBdate() {
        return bdate;
    }

    public void setBdate(String bdate) {
        this.bdate = bdate;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

}
