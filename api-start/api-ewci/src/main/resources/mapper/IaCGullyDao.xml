<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.monitor.mapper.IaCGullyDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getGullyList" resultType="com.huitu.cloud.api.ewci.monitor.entity.IaCGully">
        SELECT A.GULLYCD,
               NAME,
               A.ADCD,
               B.ADNM,
               A.WSCD,
               C.WSNM,
               CAREA,
               CH<PERSON>NGTH,
               CH<PERSON>ERCENT,
               FCATION,
               FSTAND,
               DIKELEN,
               RTLEN,
               TOWNS,
               XZC,
               ZRC,
               PCOUNT,
               LAND,
               PFCOUNT,
               FCOUNT,
               DCOUNT,
               CPROGRAM
        FROM IA_C_GULLY A
                 LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
                 LEFT JOIN IA_C_WATA C ON C.WSCD = A.WSCD
        WHERE GULLYCD IN (SELECT GULLYCD FROM BNS_REL_GULLY_AD WHERE LEFT(B.ADCD, 12) = LEFT(#{adcd}, 12))
        ORDER BY A.ADCD, GULLYCD ASC
    </select>
</mapper>
