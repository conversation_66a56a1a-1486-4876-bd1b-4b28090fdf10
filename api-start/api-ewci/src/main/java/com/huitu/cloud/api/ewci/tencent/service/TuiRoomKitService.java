package com.huitu.cloud.api.ewci.tencent.service;

import com.huitu.cloud.api.ewci.tencent.entity.MobPushVO;
import com.huitu.cloud.api.ewci.tencent.entity.TuiRoomKitVO;

/**
 * 腾讯音视频服务
 *
 * <AUTHOR>
 */
public interface TuiRoomKitService {

    /**
     * 发送 WebSocket 消息, 用于 Web 端弹窗推送
     *
     * @param vo Web端Socket消息封装实体
     * @return Boolean
     */
    Boolean sendSocketMsg(TuiRoomKitVO vo);

    /**
     * 发送手机壳顶栏推送消息，用于移动端顶栏推送
     *
     * @param vo 移动端顶栏推送封装实体
     * @return Boolean
     */
    Boolean mobPush(MobPushVO vo);
}
