package com.huitu.cloud.api.ewci.person.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;

import java.io.OutputStream;
import java.util.List;

/**
 * 中小河流责任人
 *
 * <AUTHOR>
 */
public interface BasPersonService {

    /**
     * 分页获取中小河流责任人列表
     *
     * @param query
     * @return
     */
    IPage<BasPersonVo> getPageList(BasPersonQuery query);

    /**
     * 中小河流责任人统计
     *
     * @param query
     * @return
     */
    List<BasSummaryVo> getBasSummaryList(BasSummaryQuery query);

    /**
     * 中小河流责任人导出
     *
     * @param query  查询条件
     * @param output 输出流
     **/
    void dataExport(BasPersonQuery query, OutputStream output);

    /**
     * 编辑中小河流责任人
     *
     * @param entity
     * @return
     */
    int basPersonEdit(BasPersonVo entity);

    /**
     * 根据政区编码查询河流编码
     *
     * @param adcd
     * @return
     */
    List<String> getBasCodeList(String adcd);

    /**
     * 未上报
     *
     * @param adcd
     * @return
     */
    List<BasFailReportVo> getBasFailReportList(String adcd);

    void exportBasFailReport(BasPersonQuery query, OutputStream output);
}
