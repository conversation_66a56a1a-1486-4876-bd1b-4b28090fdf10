package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 山洪预警影响村屯响应对象
 */
@ApiModel(value = "山洪预警影响村屯响应对象")
public class EwShVillageDataResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;
    @ApiModelProperty(value = "乡镇名称")
    @TableField(value = "XZNM")
    private String xznm;
    @ApiModelProperty(value = "行政村名称")
    @TableField(value = "XZCNM")
    private String xzcnm;
    @ApiModelProperty(value = "自然村名称")
    @TableField(value = "ZRCNM")
    private String zrcnm;
    @ApiModelProperty(value = "土壤含水率")
    @TableField(value = "LWATER")
    private BigDecimal lwater;
    @ApiModelProperty(value = "阈值历时")
    @TableField(value = "STDT")
    private Integer stdt;
    @ApiModelProperty(value = "雨量阈值")
    @TableField(value = "DRPT")
    private Integer drpt;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getXznm() {
        return xznm;
    }

    public void setXznm(String xznm) {
        this.xznm = xznm;
    }

    public String getXzcnm() {
        return xzcnm;
    }

    public void setXzcnm(String xzcnm) {
        this.xzcnm = xzcnm;
    }

    public String getZrcnm() {
        return zrcnm;
    }

    public void setZrcnm(String zrcnm) {
        this.zrcnm = zrcnm;
    }

    public BigDecimal getLwater() {
        return lwater;
    }

    public void setLwater(BigDecimal lwater) {
        this.lwater = lwater;
    }

    public Integer getStdt() {
        return stdt;
    }

    public void setStdt(Integer stdt) {
        this.stdt = stdt;
    }

    public Integer getDrpt() {
        return drpt;
    }

    public void setDrpt(Integer drpt) {
        this.drpt = drpt;
    }
}
