package com.huitu.cloud.api.syq.river.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 水位流量关系曲线表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-11
 */
@TableName("ST_ZQRL_B")
@ApiModel(value="StZqrlB对象", description="水位流量关系曲线表")
public class StZqrlb implements Serializable{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "曲线名称")
    @TableField("LNNM")
    private String lnnm;

    @ApiModelProperty(value = "启用时间")
    @TableField("BGTM")
    private LocalDateTime bgtm;

    @ApiModelProperty(value = "点序号")
    @TableField("PTNO")
    private Integer ptno;

    @ApiModelProperty(value = "水位")
    @TableField("Z")
    private BigDecimal z;

    @ApiModelProperty(value = "流量")
    @TableField("Q")
    private BigDecimal q;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getLnnm() {
        return lnnm;
    }

    public void setLnnm(String lnnm) {
        this.lnnm = lnnm;
    }

    public LocalDateTime getBgtm() {
        return bgtm;
    }

    public void setBgtm(LocalDateTime bgtm) {
        this.bgtm = bgtm;
    }

    public Integer getPtno() {
        return ptno;
    }

    public void setPtno(Integer ptno) {
        this.ptno = ptno;
    }

    public BigDecimal getZ() {
        return z;
    }

    public void setZ(BigDecimal z) {
        this.z = z;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }



}
