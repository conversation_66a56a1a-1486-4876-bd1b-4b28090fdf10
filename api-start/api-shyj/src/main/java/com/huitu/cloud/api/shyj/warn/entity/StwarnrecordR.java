package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 村(测站)预警记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("STWARNRECORD_R")
@ApiModel(value="StwarnrecordR对象", description="村(测站)预警记录表")
public class StwarnrecordR extends Model<StwarnrecordR> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = " 测站预警ID ")
    @TableId(value = "STWARNID", type = IdType.NONE)
    private String stWarnId;

    @ApiModelProperty(value = "村政区编码 ")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "测站预警类型")
    @TableField("WARNTYPEID")
    private Integer warnTypeId;

    @ApiModelProperty(value = " 预警等级")
    @TableField("WARNGRADEID")
    private Integer warnGradeId;

    @ApiModelProperty(value = " 预警名称")
    @TableField("STWARNNM")
    private String stWarnNm;

    @ApiModelProperty(value = "  测站预警说明 ")
    @TableField("STWARNDESC")
    private String stWarnDesc;

    @ApiModelProperty(value = " 开始时间 ")
    @TableField("STWARNSTM")
    private LocalDateTime stWarnStm;

    @ApiModelProperty(value = " 结束时间")
    @TableField("STWARNETM")
    private LocalDateTime stWarnEtm;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    public String getStWarnId() {
        return stWarnId;
    }

    public void setStWarnId(String stWarnId) {
        this.stWarnId = stWarnId;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Integer getWarnTypeId() {
        return warnTypeId;
    }

    public void setWarnTypeId(Integer warnTypeId) {
        this.warnTypeId = warnTypeId;
    }

    public Integer getWarnGradeId() {
        return warnGradeId;
    }

    public void setWarnGradeId(Integer warnGradeId) {
        this.warnGradeId = warnGradeId;
    }

    public String getStWarnNm() {
        return stWarnNm;
    }

    public void setStWarnNm(String stWarnNm) {
        this.stWarnNm = stWarnNm;
    }

    public String getStWarnDesc() {
        return stWarnDesc;
    }

    public void setStWarnDesc(String stWarnDesc) {
        this.stWarnDesc = stWarnDesc;
    }

    public LocalDateTime getStWarnStm() {
        return stWarnStm;
    }

    public void setStWarnStm(LocalDateTime stWarnStm) {
        this.stWarnStm = stWarnStm;
    }

    public LocalDateTime getStWarnEtm() {
        return stWarnEtm;
    }

    public void setStWarnEtm(LocalDateTime stWarnEtm) {
        this.stWarnEtm = stWarnEtm;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
