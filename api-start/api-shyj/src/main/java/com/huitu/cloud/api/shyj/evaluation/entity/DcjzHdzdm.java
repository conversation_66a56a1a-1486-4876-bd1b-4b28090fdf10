package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <p>
 * 重点城(集)镇调查评价-河道纵断面
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@ApiModel(value="DcjzHdzdmw对象", description="重点城(集)镇调查评价-河道纵断面")
public class DcjzHdzdm extends Model<DcjzHdzdm> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "位置")
    @TableId(value = "ADDRESS", type = IdType.NONE)
    private String address;

    @ApiModelProperty(value = "沟道")
    @TableId(value = "CHANNEL", type = IdType.NONE)
    private String channel;

    @ApiModelProperty(value = "是否跨县")
    @TableId(value = "ISCTOWN", type = IdType.NONE)
    private String isctown;

    @ApiModelProperty(value = "控制点经度")
    @TableId(value = "CLGTD", type = IdType.NONE)
    private Double clgtd;

    @ApiModelProperty(value = "控制点纬度")
    @TableId(value = "CLTTD", type = IdType.NONE)
    private Double clttd;

    @ApiModelProperty(value = "控制点高程")
    @TableId(value = "CELE", type = IdType.NONE)
    private String cele;

    @ApiModelProperty(value = "高程系统")
    @TableId(value = "ELETYPE", type = IdType.NONE)
    private String eletype;

    @ApiModelProperty(value = "测量方法")
    @TableId(value = "METHOD", type = IdType.NONE)
    private String method;

    @ApiModelProperty(value = "测量点名称")
    @TableId(value = "PNAME", type = IdType.NONE)
    private String pname;

    @ApiModelProperty(value = "高程")
    @TableId(value = "ELE", type = IdType.NONE)
    private String ele;

    @ApiModelProperty(value = "经度")
    @TableId(value = "LGTD", type = IdType.NONE)
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableId(value = "LTTD", type = IdType.NONE)
    private Double lttd;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getIsctown() {
        return isctown;
    }

    public void setIsctown(String isctown) {
        this.isctown = isctown;
    }

    public Double getClgtd() {
        return clgtd;
    }

    public void setClgtd(Double clgtd) {
        this.clgtd = clgtd;
    }

    public Double getClttd() {
        return clttd;
    }

    public void setClttd(Double clttd) {
        this.clttd = clttd;
    }

    public String getCele() {
        return cele;
    }

    public void setCele(String cele) {
        this.cele = cele;
    }

    public String getEletype() {
        return eletype;
    }

    public void setEletype(String eletype) {
        this.eletype = eletype;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getPname() {
        return pname;
    }

    public void setPname(String pname) {
        this.pname = pname;
    }

    public String getEle() {
        return ele;
    }

    public void setEle(String ele) {
        this.ele = ele;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    @Override
    public String toString() {
        return "DcjzHdzdm{" +
                "adnm='" + adnm + '\'' +
                ", address='" + address + '\'' +
                ", channel='" + channel + '\'' +
                ", isctown='" + isctown + '\'' +
                ", clgtd=" + clgtd +
                ", clttd=" + clttd +
                ", cele='" + cele + '\'' +
                ", eletype='" + eletype + '\'' +
                ", method='" + method + '\'' +
                ", pname='" + pname + '\'' +
                ", ele='" + ele + '\'' +
                ", lgtd=" + lgtd +
                ", lttd=" + lttd +
                '}';
    }
}
