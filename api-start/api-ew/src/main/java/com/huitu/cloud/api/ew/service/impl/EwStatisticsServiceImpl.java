package com.huitu.cloud.api.ew.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.huitu.cloud.api.ew.entity.ext.EwRiverWarnGradeSummary;
import com.huitu.cloud.api.ew.entity.ext.EwRsvrWarnGradeSummary;
import com.huitu.cloud.api.ew.entity.ext.EwWarningSummary;
import com.huitu.cloud.api.ew.entity.ext.EwShWarnGradeSummary;
import com.huitu.cloud.api.ew.entity.request.EwWarnGradeSummaryRequest;
import com.huitu.cloud.api.ew.entity.request.EwWarnMessageRequest;
import com.huitu.cloud.api.ew.entity.request.EwWarningSummaryRequest;
import com.huitu.cloud.api.ew.entity.response.EwWarnGradeStatisticsResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarnGradeSummaryResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarnMessageStatistics;
import com.huitu.cloud.api.ew.entity.response.EwWarningSummaryResponse;
import com.huitu.cloud.api.ew.mapper.EwStatisticsDao;
import com.huitu.cloud.api.ew.service.EwStatisticsService;
import com.huitu.cloud.constants.CommConstants;
import com.huitu.cloud.util.AdcdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 预警统计服务 实现类
 */
@Slf4j
@Service
public class EwStatisticsServiceImpl implements EwStatisticsService {

    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;
    private final EwStatisticsDao baseDao;

    public EwStatisticsServiceImpl(EwStatisticsDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public EwWarningSummaryResponse getWarningSummary(EwWarningSummaryRequest request) {
        Map<String, Object> params = request.toQuery();
        CompletableFuture<EwWarningSummary> shFuture = CompletableFuture.supplyAsync(() -> baseDao.getShWarningSummary(params), asyncExecutor);
        CompletableFuture<EwWarningSummary> rsvrFuture = CompletableFuture.supplyAsync(() -> baseDao.getRsvrWarningSummary(params), asyncExecutor);
        CompletableFuture<EwWarningSummary> riverFuture = CompletableFuture.supplyAsync(() -> baseDao.getRiverWarningSummary(params), asyncExecutor);

        AtomicReference<EwWarningSummaryResponse> reference = new AtomicReference<>();
        CompletableFuture.allOf(shFuture, rsvrFuture, riverFuture).whenComplete((v, th) -> {
            EwWarningSummaryResponse response = new EwWarningSummaryResponse();
            try {
                // 山洪监测预警
                EwWarningSummary shSummary = shFuture.get();
                if (null != shSummary) {
                    response.setWcount1(shSummary.getWcount());
                    response.setPcount1(shSummary.getPcount());
                    response.setFcount1(shSummary.getFcount());
                    response.setRcount1(shSummary.getRcount());
                }
                // 水库监测预警
                EwWarningSummary rsvrSummary = rsvrFuture.get();
                if (null != rsvrSummary) {
                    response.setWcount2(rsvrSummary.getWcount());
                    response.setPcount2(rsvrSummary.getPcount());
                    response.setFcount2(rsvrSummary.getFcount());
                    response.setRcount2(rsvrSummary.getRcount());
                }
                // 河道监测预警
                EwWarningSummary riverSummary = riverFuture.get();
                if (null != riverSummary) {
                    response.setWcount3(riverSummary.getWcount());
                    response.setPcount3(riverSummary.getPcount());
                    response.setFcount3(riverSummary.getFcount());
                    response.setRcount3(riverSummary.getRcount());
                }
                // 汇总统计
                response.setTotal1(response.getWcount1() + response.getWcount2() + response.getWcount3());
                response.setTotal2(response.getPcount1() + response.getPcount2() + response.getPcount3());
                response.setTotal3(response.getFcount1() + response.getFcount2() + response.getFcount3());
                response.setTotal4(response.getRcount1() + response.getRcount2() + response.getRcount3());
            } catch (Exception ignored) {
            }
            reference.set(response);
        }).join();
        return reference.get();
    }

    @Override
    public EwWarnMessageStatistics getAdWarnStatistics(EwWarnMessageRequest query) {
        int level = AdcdUtil.getAdLevel(query.getAdcd());
        Map<String, Object> param = new HashMap<>();
        param.put("stm", query.getStm());
        param.put("etm", query.getEtm());
        param.put("ad", query.getAdcd().substring(0, level));
        param.put("zero", query.getAdcd().substring(level));
        param.put("level", level);
        param.put("level2", level);
        //统计本级政区预警信息
        List<EwWarnMessageStatistics> list = baseDao.getAdWarnMessageStatistics(param);
        EwWarnMessageStatistics warnStatistics = null;
        if (!list.isEmpty()) {
            warnStatistics = list.get(0);
        }
        return warnStatistics;
    }

    @Override
    public List<EwWarnMessageStatistics> getAdWarnStatisticsTreeList(EwWarnMessageRequest query) {
        int level = AdcdUtil.getAdLevel(query.getAdcd());
        Map<String, Object> param = new HashMap<>();
        param.put("stm", query.getStm());
        param.put("etm", query.getEtm());
        param.put("ad", query.getAdcd().substring(0, level));
        param.put("zero", query.getAdcd().substring(level + (level < 6 ? 2 : 3)));
        param.put("level", level);
        param.put("level2", level + (level < 6 ? 2 : 3));
        //统计下级政区预警信息
        List<EwWarnMessageStatistics> list = baseDao.getAdWarnMessageStatistics(param);

        if (level == 2) {
            param.put("zero", query.getAdcd().substring(level + 4));
            param.put("level2", level + 4);
            List<EwWarnMessageStatistics> list2 = baseDao.getAdWarnMessageStatistics(param);
            List<EwWarnMessageStatistics> list3 = new ArrayList<>();
            for (EwWarnMessageStatistics x : list2) {
                if (!CommConstants.Public.MEIHEKOU_ADCD.equals(x.getAdcd()) && !CommConstants.Public.GONGZHULING_ADCD.equals(x.getAdcd())) {
                    list3.add(x);
                } else {
                    list.add(x);
                }
            }
            list.forEach(x -> {
                List<EwWarnMessageStatistics> childList = getChildren(list3, x.getAdcd());
                x.setChildren(childList);
            });
        }
        return list;
    }

    /**
     * 获取树的子节点
     */
    private List<EwWarnMessageStatistics> getChildren(List<EwWarnMessageStatistics> list, String pcode) {
        // 通过父级编码子类
        List<EwWarnMessageStatistics> childList = list.stream().filter(item -> item.getPadcd().equals(pcode)).collect(Collectors.toList());
        return childList;
    }

    @Override
   public  EwWarnGradeSummaryResponse getWarnGradeSummary(EwWarnGradeSummaryRequest query){
        //山洪等级预警统计
        Map<String, Object> params = query.toQuery();
        CompletableFuture<EwShWarnGradeSummary> shFuture = CompletableFuture.supplyAsync(() -> baseDao.getShWarnGradeSummary(params), asyncExecutor);
        CompletableFuture<EwRsvrWarnGradeSummary> rsvrFuture = CompletableFuture.supplyAsync(() -> baseDao.getRsvrWarnGradeSummary(params), asyncExecutor);
        CompletableFuture<EwRiverWarnGradeSummary> riverFuture = CompletableFuture.supplyAsync(() -> baseDao.getRiverWarnGradeSummary(params), asyncExecutor);

        AtomicReference<EwWarnGradeSummaryResponse> reference = new AtomicReference<>();
        CompletableFuture.allOf(shFuture, rsvrFuture, riverFuture).whenComplete((v, th) -> {
            EwWarnGradeSummaryResponse response = new EwWarnGradeSummaryResponse();
            try {
                // 山洪监测预警
                EwShWarnGradeSummary shWarnGradeSummary = shFuture.get();
                if (null != shWarnGradeSummary) {
                    response.setAdcd(shWarnGradeSummary.getAdcd());
                    response.setAdnm(shWarnGradeSummary.getAdnm());
                    response.setGrade1Count(shWarnGradeSummary.getGrade1Count());
                    response.setGrade2Count(shWarnGradeSummary.getGrade2Count());
                    response.setGrade3Count(shWarnGradeSummary.getGrade3Count());
                    response.setGrade1SmsCount(shWarnGradeSummary.getGrade1SmsCount());
                    response.setGrade2SmsCount(shWarnGradeSummary.getGrade2SmsCount());
                    response.setGrade3SmsCount(shWarnGradeSummary.getGrade3SmsCount());
                    response.setGrade1VoiceCount(shWarnGradeSummary.getGrade1VoiceCount());
                    response.setGrade2VoiceCount(shWarnGradeSummary.getGrade2VoiceCount());
                    response.setGrade3VoiceCount(shWarnGradeSummary.getGrade3VoiceCount());
                    response.setGrade1VoiceRead(shWarnGradeSummary.getGrade1VoiceRead());
                    response.setGrade2VoiceRead(shWarnGradeSummary.getGrade2VoiceRead());
                    response.setGrade3VoiceRead(shWarnGradeSummary.getGrade3VoiceRead());
                    response.setShWarnCount(shWarnGradeSummary.getGrade1Count()+shWarnGradeSummary.getGrade2Count()+shWarnGradeSummary.getGrade3Count());
                    response.setShPcount(shWarnGradeSummary.getGrade1SmsCount()+shWarnGradeSummary.getGrade2SmsCount()+shWarnGradeSummary.getGrade3SmsCount());
                }
                // 水库监测预警
                EwRsvrWarnGradeSummary rsvrSummary = rsvrFuture.get();
                if (null != rsvrSummary) {
                   if(StrUtil.isBlank(response.getAdcd())) response.setAdcd(rsvrSummary.getAdcd());
                    if(StrUtil.isBlank(response.getAdnm())) response.setAdnm(rsvrSummary.getAdnm());
                    response.setRsvrWarnCount(rsvrSummary.getWarnCount());
                    response.setRsvrPcount(rsvrSummary.getSmsCount());
                }
                // 河道监测预警
                EwRiverWarnGradeSummary riverSummary = riverFuture.get();
                if (null != riverSummary) {
                    if(StrUtil.isBlank(response.getAdcd()))response.setAdcd(riverSummary.getAdcd());
                    if(StrUtil.isBlank(response.getAdnm())) response.setAdnm(riverSummary.getAdnm());
                    response.setRiverWarnCount(riverSummary.getWarnCount());
                    response.setRiverPcount(riverSummary.getSmsCount());
                }
                // 汇总统计
                response.setTotalWarnCount(response.getShWarnCount() + response.getRsvrWarnCount()+ response.getRiverWarnCount());
                response.setTotalPcount(response.getShPcount() + response.getRsvrPcount() + response.getRiverPcount());
                response.setTotalVoiceCount(response.getGrade1VoiceCount() + response.getGrade2VoiceCount() + response.getGrade3VoiceCount());
            } catch (Exception ignored) {
            }
            reference.set(response);
        }).join();
        return reference.get();

    }

    @Override
    public List<EwWarnGradeStatisticsResponse> getAdWarnGradeList(EwWarnGradeSummaryRequest query) {
        //山洪等级预警统计
        Map<String, Object> params = query.toQuery();
        return  baseDao.getAdWarnGradeList(params);
    }

    @Override
    public void exportAdWarnGradeList(EwWarnGradeSummaryRequest query, HttpServletResponse response) {
        List<EwWarnGradeStatisticsResponse> list=null;
        try {
            list=getAdWarnGradeList(query);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("政区山洪预警等级统计列表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            EasyExcel.write(response.getOutputStream(), EwWarnGradeStatisticsResponse.class)
                    .sheet("政区山洪预警等级统计列表")
                    .doWrite(list);
        } catch (Exception e) {
             log.error("政区山洪预警等级统计列表导出失败，参数为:{},异常信息:{}", JSON.toJSONString(query), e);
        }

    }


}
