package com.huitu.cloud.api.shyj.dept.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.shyj.dept.entity.DeptB;
import com.huitu.cloud.api.shyj.dept.entity.RelevantDeptInfo;

import java.util.List;

/**
 * <p>
 * 部门信息  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public interface DeptService extends IService<DeptB> {
    /**
     * 分页查询部门信息
     *
     * @param adcd     政区编码
     * @param deptNm   部门名称
     * @param pid      上级部门编码
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<RelevantDeptInfo> getDeptList(String adcd, String deptNm, String pid, int pageNum, int pageSize);

    /**
     * 添加部门信息
     *
     * @param entity 部门实体类
     * @return 1 成功；0 失败
     */
    String addDept(DeptB entity);

    /**
     * 更新部门信息
     *
     * @param entity 部门实体类
     * @return 1 成功；0 失败
     */
    String updateDept(DeptB entity);

    /**
     * 删除部门信息
     *
     * @param adcd   政区编码
     * @param deptcd 政区编码
     * @return 1 成功；0 失败
     */
    String delDept(String adcd, String deptcd);
    /**
     * 验证是否删除部门信息
     *
     * @param adcd   政区编码
     * @param deptcd 政区编码
     * @return 1 成功；0 失败
     */
    String checkDelDept(String adcd, String deptcd);
    /**
     * 分页查询部门信息
     *
     * @param adcd     政区编码
     * @return
     */
    List<RelevantDeptInfo> getDeptTreeList(String adcd);
}
