<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ia.mapper.BnsIaDrenecDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.ewci.ia.entity.BnsIaDrenec">
        <id column="ADCD" property="adcd" />
        <result column="YR" property="yr" />
        <result column="RESNUM" property="resnum" />
        <result column="RESTCP" property="restcp" />
        <result column="RESCWS" property="rescws" />
        <result column="DCNUM" property="dcnum" />
        <result column="DCCWS" property="dccws" />
        <result column="DSNUM" property="dsnum" />
        <result column="DSCWS" property="dscws" />
        <result column="WLNUM" property="wlnum" />
        <result column="WLCWS" property="wlcws" />
        <result column="RRDKW" property="rrdkw" />
        <result column="RRIRR" property="rrirr" />
        <result column="CTWS" property="ctws" />
        <result column="WST" property="wst" />
        <result column="CTYSRVORG" property="ctysrvorg" />
        <result column="CTYSRVORGPP" property="ctysrvorgpp" />
        <result column="MIF" property="mif" />
        <result column="EMW" property="emw" />
        <result column="CTYDGP" property="ctydgp" />
        <result column="DGGMWH" property="dggmwh" />
        <result column="SSNUM" property="ssnum" />
        <result column="TS" property="ts" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ADCD, YR, RESNUM, RESTCP, RESCWS, DCNUM, DCCWS, DSNUM, DSCWS, WLNUM, WLCWS, RRDKW, RRIRR, CTWS, WST, CTYSRVORG, CTYSRVORGPP, MIF, EMW, CTYDGP, DGGMWH, SSNUM, TS
    </sql>
    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.ia.entity.BnsIaDrenecVo">
        select D.ADCD, D.YR, D.RESNUM, D.RESTCP, D.RESCWS, D.DCNUM, D.DCCWS, D.DSNUM, D.DSCWS, D.WLNUM, D.WLCWS, D.RRDKW, D.RRIRR, D.CTWS, D.WST, D.CTYSRVORG, D.CTYSRVORGPP, D.MIF, D.EMW, D.CTYDGP, D.DGGMWH, D.SSNUM, D.TS, A.adnm
        from BNS_IA_DRENEC D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE LEFT(D.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.yr !=null and map.yr !=''">
            AND D.yr = #{map.yr}
        </if>
        order by D.adcd asc
    </select>

    <select id="getStatListByAdcd" resultType="com.huitu.cloud.api.ewci.ia.entity.BnsIaDrenecVo">
        select D.ADCD, D.YR, D.RESNUM, D.RESTCP, D.RESCWS, D.DCNUM, D.DCCWS, D.DSNUM, D.DSCWS, D.WLNUM, D.WLCWS, D.RRDKW, D.RESNUM + D.DCNUM + D.DSNUM + D.WLNUM resNumCount, D.RESCWS + D.DCCWS + D.DSCWS + D.WLCWS dsCwsCount, D.RRIRR, D.CTWS, D.WST, D.CTYSRVORG, D.CTYSRVORGPP, D.MIF, D.EMW, D.CTYDGP, D.DGGMWH, D.SSNUM, D.TS, A.adnm
        from BNS_IA_DRENEC D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE A.PADCD = #{map.adcd}
        <if test="map.yr !=null and map.yr !=''">
            AND D.yr = #{map.yr}
        </if>
        order by D.adcd asc
    </select>

    <select id="getStatOwnListByAdcd" resultType="com.huitu.cloud.api.ewci.ia.entity.BnsIaDrenecVo">
        select D.ADCD, D.YR, D.RESNUM, D.RESTCP, D.RESCWS, D.DCNUM, D.DCCWS, D.DSNUM, D.DSCWS, D.WLNUM, D.WLCWS, D.RRDKW, D.RRIRR, D.CTWS, D.WST, D.CTYSRVORG, D.CTYSRVORGPP, D.MIF, D.EMW, D.CTYDGP, D.DGGMWH, D.SSNUM, D.TS, A.adnm
        from BNS_IA_DRENEC D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE D.ADCD = #{map.adcd}
        <if test="map.yr !=null and map.yr !=''">
            AND D.yr = #{map.yr}
        </if>
        order by D.adcd asc
    </select>

</mapper>
