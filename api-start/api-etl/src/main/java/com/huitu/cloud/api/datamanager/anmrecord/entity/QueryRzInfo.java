package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 水位异常站点信息查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2020-4-1
 */
@ApiModel(value="QueryRzInfo对象", description="水位异常站点信息查询参数")
public class QueryRzInfo extends PageBean {
    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "异常类型(雨情：'0,1'  水情：‘3,4,5’)")
    private String anmtp;

    public String getAnmtp() {
        return anmtp;
    }

    public void setAnmtp(String anmtp) {
        this.anmtp = anmtp;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }
}
