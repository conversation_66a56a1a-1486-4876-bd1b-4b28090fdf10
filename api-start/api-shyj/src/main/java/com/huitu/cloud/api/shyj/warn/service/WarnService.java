package com.huitu.cloud.api.shyj.warn.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.shyj.warn.entity.*;

import java.util.List;

/**
 * <p>
 * 政区预警记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-16
 */
public interface WarnService extends IService<WarnrecordR> {
    /**
     * 分页查询预警信息
     *
     * @param stm          开始时间
     * @param etm          结束时间
     * @param adcd         政区编码
     * @param queryType    是否只查询未结束预警
     * @param warnGradeId  预警等级
     * @param warnStatusId 预警状态
     * @param pageNum      页码
     * @param pageSize     每页个数
     * @return
     */
    IPage<WarnRelatedInfo> getWarnByPage(String stm, String etm, String adcd, String queryType, String warnGradeId, String warnStatusId, int pageNum, int pageSize);

    /**
     * 查询预警信息统计
     *
     * @param stm          开始时间
     * @param etm          结束时间
     * @param adcd         政区编码
     * @param queryType    是否只查询未结束预警
     * @param warnGradeId  预警等级
     * @param warnStatusId 预警状态
     * @return
     */
    WarnStatisticsVo getWarnByCount(String stm, String etm, String adcd, String queryType, String warnGradeId, String warnStatusId);


    /**
     * 查询单条预警信息
     *
     * @param warnId 预警id
     * @return
     */
    WarnRelatedInfo getWarnById(String warnId);

    /**
     * 政区预警本级统计信息查询
     *
     * @param stm       开始时间
     * @param etm       结束时间
     * @param adcd      政区编码
     * @param queryType 是否只查询未结束预警
     * @return
     */
    WarnStatistics getAdWarnStatistics(String stm, String etm, String adcd, String queryType);

    /**
     * 政区预警下级统计信息树形列表查询
     *
     * @param stm       开始时间
     * @param etm       结束时间
     * @param adcd      政区编码
     * @param queryType 是否只查询未结束预警
     * @return
     */
    List<WarnStatistics> getAdWarnStatisticsTreeList(String stm, String etm, String adcd, String queryType);

    /**
     * 预警处理接口
     *
     * @param warnrecordR 预警实体类
     * @return 30 表预警以被处理 0 成功并改版状态  1  成功不改变状态
     */
    String updateWarn(WarnrecordrEx warnrecordR);

    /**
     * 创建突发预警
     *
     * @param warnrecordR 预警实体类
     * @return 0 成功
     */
    String saveWarn(WarnrecordR warnrecordR);

    /**
     * 预警关联危险区统计信息查询
     *
     * @param adcd 政区编码
     * @return
     */
    WarnDanadStatistics getWarnDanadStatistics(String adcd);

    /**
     * 预警关联防治区统计信息查询
     *
     * @param adcd 政区编码
     * @return
     */
    WarnPrevcntStatistics getWarnPrevcntStatistics(String adcd);

    /**
     * 预警关联政区统计信息查询
     *
     * @param adcd 政区编码
     * @return
     */
    WarnAdStatistics getWarnAdStatistics(String adcd);

    /**
     * 预警关联涉水工程统计信息查询
     *
     * @param adcd 政区编码
     * @return
     */
    WarnWadingStatistics getWarnWadingStatistics(String adcd);

    /**
     * 预警关联预警设施统计信息查询
     *
     * @param adcd 政区编码
     * @return
     */
    WarnJczbxlxStatistics getWarnJczbxlxStatistics(String adcd);

    /**
     * 预警关联的测站或村预警列表
     *
     * @param warnId 预警id
     * @return
     */
    List<StwarnRelatedInfo> getStAdWarnById(String warnId);

    /**
     * 预警状态列表
     *
     * @param isClose 是否查询已关闭状态("0":查询非关闭的所有状态)
     * @return
     */
    List<WarningstatusB> getWarnStatusList(Boolean isClose);

    /**
     * 预警等级列表
     *
     * @return
     */
    List<WarninggradeB> getWarnGradeList();

    /**
     * 预警类型列表
     *
     * @return
     */
    List<WarningtypeB> getWarnTypeList();

    /**
     * 查询预警的操作记录信息
     *
     * @param warnId 预警id
     * @return
     */
    List<RecordInfo> getWarnRecordById(String warnId);

    /**
     * 统计政区下的预警总个数以及市县个数
     *
     * @param adcd 政区编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    WarnInfoByYwmh getWarnInfoByYwmh(String adcd, String stm, String etm);

    /**
     * 导出预警信息
     *
     * @param stm          开始时间
     * @param etm          结束时间
     * @param adcd         政区编码
     * @param queryType    是否只查询未结束预警
     * @param warnGradeId  预警等级
     * @param warnStatusId 预警状态
     * @param type         导出类型 1：综合监视山洪列表 2：山洪预警中的预警信息导出
     */
    void ExportWarnByPage(String stm, String etm, String adcd, String queryType, String warnGradeId, String warnStatusId, String type);
    /**
     * 根据预警ID查询预警反馈
     *
     * @param warnId 预警id
     * @return
     */
    WarnFeedback getWarnFeedbackById(String warnId);
    /**
     * 添加或修改预警反馈
     *
     * @return
     */
    Integer addOrUpdateWarnFeedback(WarnFeedback feedback);
    /**
     * 根据预警ID查询预警上报
     *
     * @param warnId 预警id
     * @return
     */
    WarnDisasterSituationReport getWarnDisAsterSituationById(String warnId);
    /**
     * 添加或修改预警上报
     *
     * @return
     */
    Integer addOrUpdatearnDisAsterSituation(WarnDisasterSituationReport feedback);

    /**
     * 获取水旱责任人
     * @param query
     * @return
     */
    IPage<WarnShPersonVo> getShPersonList(WarnShPersonQuery query);

}