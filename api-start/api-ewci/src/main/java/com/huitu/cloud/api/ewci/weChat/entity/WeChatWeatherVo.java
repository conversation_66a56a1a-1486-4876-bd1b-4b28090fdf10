package com.huitu.cloud.api.ewci.weChat.entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value="WeatherForecast对象", description="天气预报表(移动端用)")
public class WeChatWeatherVo implements Serializable {
    @ApiModelProperty(value = "编码")
    private int id;

    @ApiModelProperty(value = "时间")
    private String tm;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "天气预报")
    private String weather;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getWeather() {
        return weather;
    }

    public void setWeather(String weather) {
        this.weather = weather;
    }
}
