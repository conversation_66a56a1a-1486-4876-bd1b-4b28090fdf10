package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 超标准洪水淹没范围信息
 */
@ApiModel(value = "超标准洪水淹没范围信息")
public class RiverAffectadVo implements Serializable {

    @ApiModelProperty(value = "河流编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "县数量")
    @TableField(value = "XADCD_NUM")
    private Integer xadcdNum;

    @ApiModelProperty(value = "乡镇数量")
    @TableField(value = "ADCD_NUM")
    private Integer adcdNum;

    @ApiModelProperty(value = "行政村数量")
    @TableField(value = "XZC_NUM")
    private Integer xzcNum;

    @ApiModelProperty(value = "自然屯数量")
    @TableField(value = "ZRT_NUM")
    private Integer zrtNum;

    @ApiModelProperty(value = "户数")
    @TableField(value = "HTCOUNT")
    private Integer htcount;

    @ApiModelProperty(value = "人口")
    @TableField(value = "PTCOUNT")
    private Integer ptcount;

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public Integer getXadcdNum() {
        return xadcdNum;
    }

    public void setXadcdNum(Integer xadcdNum) {
        this.xadcdNum = xadcdNum;
    }

    public Integer getAdcdNum() {
        return adcdNum;
    }

    public void setAdcdNum(Integer adcdNum) {
        this.adcdNum = adcdNum;
    }

    public Integer getXzcNum() {
        return xzcNum;
    }

    public void setXzcNum(Integer xzcNum) {
        this.xzcNum = xzcNum;
    }

    public Integer getZrtNum() {
        return zrtNum;
    }

    public void setZrtNum(Integer zrtNum) {
        this.zrtNum = zrtNum;
    }

    public Integer getHtcount() {
        return htcount;
    }

    public void setHtcount(Integer htcount) {
        this.htcount = htcount;
    }

    public Integer getPtcount() {
        return ptcount;
    }

    public void setPtcount(Integer ptcount) {
        this.ptcount = ptcount;
    }
}
