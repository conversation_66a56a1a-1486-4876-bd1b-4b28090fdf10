package com.huitu.cloud.api.common.liuyu.service;

import com.huitu.cloud.api.common.liuyu.entity.BsnBasB;
import com.huitu.cloud.api.common.liuyu.entity.BsnRvinfoB;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 河流流域常量表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
public interface BsnRvinfobService extends IService<BsnBasB> {
    /**
     * 查询所有流域 树形结构返回 暂时使用，等待整理所有流域数据后修改
     * @return
     */
    BsnBasB getRiverTree();
    /**
     * 查询所有流域 以及当前政区下的站 测站拼接到流域下 树形结构返回 暂时使用，等待整理所有流域数据后修改
     * @param adcd 当前政区
     * @return
     */
    BsnBasB getRiverAndStTree(String adcd,String sttp);
}
