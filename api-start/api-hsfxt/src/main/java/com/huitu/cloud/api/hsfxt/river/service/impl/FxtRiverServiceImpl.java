package com.huitu.cloud.api.hsfxt.river.service.impl;

import com.huitu.cloud.api.hsfxt.city.entity.DicHongshuiliangji;
import com.huitu.cloud.api.hsfxt.river.entity.*;
import com.huitu.cloud.api.hsfxt.river.mapper.FxtRiverDao;
import com.huitu.cloud.api.hsfxt.river.service.FxtRiverService;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 风险图-河流 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
@Service
public class FxtRiverServiceImpl implements FxtRiverService {
    @Autowired
    FxtRiverDao riverDao;

    @Override
    public List<RiverTree> getRiverTreeInfo(String key, String name,String hl,String bzqid) {
        List<RiverTree> result=new ArrayList<>();
        Map<String,Object> param=new HashMap<>();
        param.put("level",6);
        //查询县集合
        List<RiverTree> xianList=riverDao.getRiverTreeInfo(param);
        param.put("level",9);
        //查询乡镇集合
        List<RiverTree> zhenList=riverDao.getRiverTreeInfo(param);
        //查询屯集合
        Map<String,Object> newParam=new HashMap<>();
        newParam.put("hl",hl);
        newParam.put("bzqid",bzqid);
        DicHongshuiliangji hongshuiliangji=riverDao.getRiverParam(newParam);
        if(hongshuiliangji != null){
            Map<String,Object> newParam2=new HashMap<>();
            List<RiverTree> tunList=null;
            if(!hongshuiliangji.getBeizhu().equals("kuiba")){
                newParam2.put("param1","DDSJ_"+hongshuiliangji.getBeizhu());
                newParam2.put("param2","HSLS_"+hongshuiliangji.getBeizhu());
                newParam2.put("param3","YMSS_"+hongshuiliangji.getBeizhu());
                //修改in需要的参数
                String key2=getParam(key);
                newParam2.put("key",key2);
                newParam2.put("name",name);
                 tunList=riverDao.getTunRiverTreeInfo(newParam2);
            }else {
                 tunList=new ArrayList<>();
            }
            //转移路线
            List<ZhuanYiFangAn> list=riverDao.getZhuanYiFangAn(null,bzqid);
            Map<String,List<ZhuanYiFangAn>> map=new HashMap<>();
            list.forEach(item->{
                if(map.containsKey(item.getZhuanyidanyuanbianma())){
                    List<ZhuanYiFangAn> list2=map.get(item.getZhuanyidanyuanbianma());
                    list2.add(item);
                }else{
                    List<ZhuanYiFangAn> list2=new ArrayList<>();
                    list2.add(item);
                    map.put(item.getZhuanyidanyuanbianma(),list2);
                }
            });
            tunList.forEach(item->{
                if (map.containsKey(item.getAdcd())){
                    item.setZyfa(map.get(item.getAdcd()));
                }
            });
            //三级集合合并
            List<RiverTree> AllList=new ArrayList<>();
            //根据屯取乡镇和区县
            Map<String,RiverTree> xianMap=new HashMap<>();
            Map<String,RiverTree> zhenMap=new HashMap<>();
            xianList.forEach(item->{
                xianMap.put(item.getAdcd(),item);
            });
            zhenList.forEach(item->{
                zhenMap.put(item.getAdcd(),item);
            });
            List<RiverTree> xianList2=new ArrayList<>();
            List<RiverTree> zhenList2=new ArrayList<>();
            tunList.forEach(item->{
                if (zhenMap.containsKey(item.getPadcd())){
                    RiverTree zhen=zhenMap.get(item.getPadcd());
                    zhen.setKeyindex(item.getKeyindex()+"_"+hl);
                    zhenList2.add(zhen);
                }
            });
            List<RiverTree> zhenList3 =zhenList2.stream().distinct().collect(Collectors.toList());
            zhenList3.forEach(item->{
                if(xianMap.containsKey(item.getPadcd())){
                    RiverTree xian=xianMap.get(item.getPadcd());
                    xian.setKeyindex(item.getKeyindex());
                    xianList2.add(xian);
                }
            });
            List<RiverTree> xianList3=xianList2.stream().distinct().collect(Collectors.toList());
            AllList.addAll(zhenList3);
            AllList.addAll(xianList3);
            AllList.addAll(tunList);
            //查询子节点集合并赋值
            AllList.forEach(item->{
                List<RiverTree> childList=getRiverChildren(AllList,item.getAdcd());
                item.setChildren(childList);
                //乡镇加入影响屯总数
                if (!"000".equals(item.getAdcd().substring(7,9))){
                    item.setChildrenCount(childList.size());
                }
            });
            //获取根节点
            result=AllList.stream().filter(item->"0".equals(item.getPadcd())).collect(Collectors.toList());
            //加入根节点影响屯总数
            result.forEach(item->{
                int count=0;
                for(RiverTree item2:item.getChildren()){
                    count=count+item2.getChildren().size();
                }
                item.setChildrenCount(count);
            });
        }
        return result;
    }

    @Override
    public List<SheHuiJingJi> getRiverShjjTreeInfo(String key) {
        List<SheHuiJingJi> result=new ArrayList<>();
        //修改in需要的参数
        String key2=getParam(key);
        List<SheHuiJingJi> list= riverDao.getRiverShjjTreeInfo(key2);
        //组织树形结构
        list.forEach(item->{
            List<SheHuiJingJi> childList=getRiverShjjChildren(list,item.getSortid().toString());
            item.setChildren(childList);
        });
        result =list.stream().filter(item->"0".equals(item.getPid())).collect(Collectors.toList());
        return result;
    }

    @Override
    public JiangHeXiangQing getRiverShjjDetailInfo(String key, String adcd, String bzqid) {
        JiangHeXiangQing result=new JiangHeXiangQing();
        JiangHeXiangQing jiangHeXiangQing=riverDao.getXianQuXiangZhen(adcd,bzqid);
        if(jiangHeXiangQing!=null){
            result.setAdcd(jiangHeXiangQing.getAdcd());
            result.setAdnm(jiangHeXiangQing.getAdnm());
            result.setMianji(jiangHeXiangQing.getMianji());
            result.setFushu(jiangHeXiangQing.getFushu());
            result.setRenkou(jiangHeXiangQing.getRenkou());
            result.setFangwujianshu(jiangHeXiangQing.getFangwujianshu());
            result.setGendimianji(jiangHeXiangQing.getGendimianji());
            result.setGdp(jiangHeXiangQing.getGdp());
        }
        JiangHeXiangQing jiangHeXiangQing2=riverDao.getZaiHaiSunShi(adcd,key);
       if(jiangHeXiangQing2!=null){
           result.setYanmomianji(jiangHeXiangQing2.getYanmomianji());
           result.setYanmogendimianji(jiangHeXiangQing2.getYanmogendimianji());
           result.setYinxiangrenkou(jiangHeXiangQing2.getYinxiangrenkou());
           result.setYinxianggdp(jiangHeXiangQing2.getYinxianggdp());
           result.setYinxiangfanwushu(jiangHeXiangQing2.getYinxiangfanwushu());
           result.setHongzaisunshi(jiangHeXiangQing2.getHongzaisunshi());
       }
        return result;
    }

    @Override
    public TunXiangQing getTunShjjDetailInfo(String key, String hl, String adcd, String bzqid) {
        TunXiangQing result=null;
        Map<String,Object> newParam=new HashMap<>();
        newParam.put("hl",hl);
        newParam.put("bzqid",bzqid);
        DicHongshuiliangji hongshuiliangji=riverDao.getRiverParam(newParam);
        if(hongshuiliangji != null){
            Map<String,Object> newParam2=new HashMap<>();
            newParam2.put("param1","DDSJ_"+hongshuiliangji.getBeizhu());
            newParam2.put("param2","HSLS_"+hongshuiliangji.getBeizhu());
            newParam2.put("param3","YMSS_"+hongshuiliangji.getBeizhu());
            newParam2.put("key",key);
            newParam2.put("adcd",adcd);
            result=riverDao.getTunShjjDetailInfo(newParam2);
            List<ZhuanYiFangAn> list=riverDao.getZhuanYiFangAn(adcd,bzqid);
            if (result!=null){
                result.setZyfa(list);
            }
        }
        return result;
    }


    private List<RiverTree> getRiverChildren(List<RiverTree> list, String pid) {
        // 通过父级编码子类
        List<RiverTree> childList = list.stream().filter(x -> x.getPadcd().equals(pid)).collect(Collectors.toList());
        return childList;
    }
    private List<SheHuiJingJi> getRiverShjjChildren(List<SheHuiJingJi> list, String pid) {
        // 通过父级编码子类
        List<SheHuiJingJi> childList = list.stream().filter(x -> x.getPid().equals(pid)).collect(Collectors.toList());
        return childList;
    }

    /**
     * 批量查询时传入字符串转换为sql底层in所需的条件转换
     *
     * @param str 传入字符串 例（1,2）
     * @return
     */
    private String getParam(String str){
        String[] s=str.split(",");
        String newStr="";
        for (int i=0;i<s.length;i++){
            newStr=newStr+"'"+s[i]+"',";
        }
        return newStr.substring(0,newStr.length()-1);
    }

}
