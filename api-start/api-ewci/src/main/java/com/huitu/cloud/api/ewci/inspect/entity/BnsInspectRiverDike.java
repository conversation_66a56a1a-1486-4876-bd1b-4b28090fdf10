package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 河道及堤防名称
 */
@Data
@ApiModel(value = "河道及堤防名称")
public class BnsInspectRiverDike extends BnsInspectReportForm {

    @ApiModelProperty(value = "河流及堤防名称")
    private String riverDikeName;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;
}
