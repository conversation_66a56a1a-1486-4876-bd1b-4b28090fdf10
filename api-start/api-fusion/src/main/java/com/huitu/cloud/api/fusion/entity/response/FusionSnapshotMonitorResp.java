package com.huitu.cloud.api.fusion.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 山洪预警监测数据快照响应对象
 */
@Data
@ApiModel(value = "山洪预警监测数据快照响应对象")
public class FusionSnapshotMonitorResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARN_ID")
    private String warnId;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "开始时间")
    @TableField(value = "STM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;

    @ApiModelProperty(value = "结束时间")
    @TableField(value = "ETM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "阈值历时")
    @TableField(value = "STDT")
    private Integer stdt;
}
