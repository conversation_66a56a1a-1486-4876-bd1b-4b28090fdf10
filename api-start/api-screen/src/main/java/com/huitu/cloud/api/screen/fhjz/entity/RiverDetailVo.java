package com.huitu.cloud.api.screen.fhjz.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value="河流防洪工程详细列表", description="河流防洪工程详细列表")
public class RiverDetailVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "河流编码")
    private String rvCode;

    @ApiModelProperty(value = "河流名称")
    private String rvName;

    @ApiModelProperty(value = "所属水系")
    private String basName;

    @ApiModelProperty(value = "流经区域")
    private String flowArea;

    @ApiModelProperty(value = "河流长度")
    private String rvLen;

    @ApiModelProperty(value = "河流面积")
    private String rvBasArea;

    @ApiModelProperty(value = "河流级别")
    private String rvLevel;

    @ApiModelProperty(value = "河源位置")
    private String rvSourLoc;

    @ApiModelProperty(value = "河口位置")
    private String rvMouLoc;

    @ApiModelProperty(value = "汇入河流")
    private String upRvName;

    @ApiModelProperty(value = "河流长度(省内)")
    private String rvLenPro;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "水文基面高程系")
    private String dtmnm;

    @ApiModelProperty(value = "实测最高水位")
    private String obhtz;

    @ApiModelProperty(value = "实测最高水位出现日期")
    private String obhtztm;

    @ApiModelProperty(value = "实测最大流量")
    private String obmxq;

    @ApiModelProperty(value = "实测最大流量出现时间")
    private String obmxqtm;

    @ApiModelProperty(value = "右岸堤顶高程")
    private String rdkel;

    @ApiModelProperty(value = "左岸堤顶高程")
    private String ldkel;

    @ApiModelProperty(value = "警戒水位")
    private String wrz;

    @ApiModelProperty(value = "警戒流量")
    private String wrq;

    @ApiModelProperty(value = "保证水位")
    private String grz;

    @ApiModelProperty(value = "保证流量")
    private String grq;

    @ApiModelProperty(value = "险工险段（处）")
    private String xgxdCount;

    @ApiModelProperty(value = "所在堤段")
    private String dikeName;

    @ApiModelProperty(value = "堤段级别")
    private String dikeGrad;

    @ApiModelProperty(value = "堤段设计水位")
    private String ddz;

    @ApiModelProperty(value = "河滩地平均高程")
    private String aeorb;

    @ApiModelProperty(value = "背水侧地面平均高程")
    private String ageabs;

    @ApiModelProperty(value = "警戒水位相应洪水标准")
    private String cfsowwl;

    @ApiModelProperty(value = "保证水位相应洪水标准")
    private String cfsogwl;

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public String getBasName() {
        return basName;
    }

    public void setBasName(String basName) {
        this.basName = basName;
    }

    public String getFlowArea() {
        return flowArea;
    }

    public void setFlowArea(String flowArea) {
        this.flowArea = flowArea;
    }

    public String getRvLen() {
        return rvLen;
    }

    public void setRvLen(String rvLen) {
        this.rvLen = rvLen;
    }

    public String getRvBasArea() {
        return rvBasArea;
    }

    public void setRvBasArea(String rvBasArea) {
        this.rvBasArea = rvBasArea;
    }

    public String getRvLevel() {
        return rvLevel;
    }

    public void setRvLevel(String rvLevel) {
        this.rvLevel = rvLevel;
    }

    public String getRvSourLoc() {
        return rvSourLoc;
    }

    public void setRvSourLoc(String rvSourLoc) {
        this.rvSourLoc = rvSourLoc;
    }

    public String getRvMouLoc() {
        return rvMouLoc;
    }

    public void setRvMouLoc(String rvMouLoc) {
        this.rvMouLoc = rvMouLoc;
    }

    public String getUpRvName() {
        return upRvName;
    }

    public void setUpRvName(String upRvName) {
        this.upRvName = upRvName;
    }

    public String getRvLenPro() {
        return rvLenPro;
    }

    public void setRvLenPro(String rvLenPro) {
        this.rvLenPro = rvLenPro;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getDtmnm() {
        return dtmnm;
    }

    public void setDtmnm(String dtmnm) {
        this.dtmnm = dtmnm;
    }

    public String getObhtz() {
        return obhtz;
    }

    public void setObhtz(String obhtz) {
        this.obhtz = obhtz;
    }

    public String getObmxq() {
        return obmxq;
    }

    public void setObmxq(String obmxq) {
        this.obmxq = obmxq;
    }

    public String getObhtztm() {
        return obhtztm;
    }

    public void setObhtztm(String obhtztm) {
        this.obhtztm = obhtztm;
    }

    public String getObmxqtm() {
        return obmxqtm;
    }

    public void setObmxqtm(String obmxqtm) {
        this.obmxqtm = obmxqtm;
    }

    public String getRdkel() {
        return rdkel;
    }

    public void setRdkel(String rdkel) {
        this.rdkel = rdkel;
    }

    public String getLdkel() {
        return ldkel;
    }

    public void setLdkel(String ldkel) {
        this.ldkel = ldkel;
    }

    public String getWrz() {
        return wrz;
    }

    public void setWrz(String wrz) {
        this.wrz = wrz;
    }

    public String getWrq() {
        return wrq;
    }

    public void setWrq(String wrq) {
        this.wrq = wrq;
    }

    public String getGrz() {
        return grz;
    }

    public void setGrz(String grz) {
        this.grz = grz;
    }

    public String getGrq() {
        return grq;
    }

    public void setGrq(String grq) {
        this.grq = grq;
    }

    public String getXgxdCount() {
        return xgxdCount;
    }

    public void setXgxdCount(String xgxdCount) {
        this.xgxdCount = xgxdCount;
    }

    public String getDikeName() {
        return dikeName;
    }

    public void setDikeName(String dikeName) {
        this.dikeName = dikeName;
    }

    public String getDikeGrad() {
        return dikeGrad;
    }

    public void setDikeGrad(String dikeGrad) {
        this.dikeGrad = dikeGrad;
    }

    public String getDdz() {
        return ddz;
    }

    public void setDdz(String ddz) {
        this.ddz = ddz;
    }

    public String getAeorb() {
        return aeorb;
    }

    public void setAeorb(String aeorb) {
        this.aeorb = aeorb;
    }

    public String getAgeabs() {
        return ageabs;
    }

    public void setAgeabs(String ageabs) {
        this.ageabs = ageabs;
    }

    public String getCfsowwl() {
        return cfsowwl;
    }

    public void setCfsowwl(String cfsowwl) {
        this.cfsowwl = cfsowwl;
    }

    public String getCfsogwl() {
        return cfsogwl;
    }

    public void setCfsogwl(String cfsogwl) {
        this.cfsogwl = cfsogwl;
    }
}
