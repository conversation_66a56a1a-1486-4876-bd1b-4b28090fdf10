package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 政区预警过程记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-16
 */
@TableName("WARNRECORD_T")
@ApiModel(value="WarnrecordT对象", description="政区预警过程记录表")
public class WarnrecordT implements Serializable {


    @ApiModelProperty(value = " 预警ID")
    @TableField("WARNID")
    private String warnId;

    @ApiModelProperty(value = " 行政区编码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "预警类型ID ")
    @TableField("WARNTYPEID")
    private Integer warnTypeId;

    @ApiModelProperty(value = "预警等级ID ")
    @TableField("WARNGRADEID")
    private Integer warnGradeId;

    @ApiModelProperty(value = "  预警状态ID ")
    @TableField("WARNSTATUSID")
    private Integer warnStatusId;

    @ApiModelProperty(value = " 预警开始时间 ")
    @TableField("WARNSTM")
    private LocalDateTime warnStm;

    @ApiModelProperty(value = "预警结束时间")
    @TableField("WARNETM")
    private LocalDateTime warnEtm;

    @ApiModelProperty(value = " 预警名称")
    @TableField("WARNNM")
    private String warnNm;

    @ApiModelProperty(value = " 预警说明  ")
    @TableField("WARNDESC")
    private String warnDesc;

    @ApiModelProperty(value = "县预警和县镇预警的标识")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = " 系统时间 ")
    @TableField("SYSTM")
    private LocalDateTime sysTm;

    @ApiModelProperty(value = " 备注 ")
    @TableField("REMARK")
    private String remark;

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getWarnTypeId() {
        return warnTypeId;
    }

    public void setWarnTypeId(Integer warnTypeId) {
        this.warnTypeId = warnTypeId;
    }

    public Integer getWarnGradeId() {
        return warnGradeId;
    }

    public void setWarnGradeId(Integer warnGradeId) {
        this.warnGradeId = warnGradeId;
    }

    public Integer getWarnStatusId() {
        return warnStatusId;
    }

    public void setWarnStatusId(Integer warnStatusId) {
        this.warnStatusId = warnStatusId;
    }

    public LocalDateTime getWarnStm() {
        return warnStm;
    }

    public void setWarnStm(LocalDateTime warnStm) {
        this.warnStm = warnStm;
    }

    public LocalDateTime getWarnEtm() {
        return warnEtm;
    }

    public void setWarnEtm(LocalDateTime warnEtm) {
        this.warnEtm = warnEtm;
    }

    public String getWarnNm() {
        return warnNm;
    }

    public void setWarnNm(String warnNm) {
        this.warnNm = warnNm;
    }

    public String getWarnDesc() {
        return warnDesc;
    }

    public void setWarnDesc(String warnDesc) {
        this.warnDesc = warnDesc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public LocalDateTime getSysTm() {
        return sysTm;
    }

    public void setSysTm(LocalDateTime sysTm) {
        this.sysTm = sysTm;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
