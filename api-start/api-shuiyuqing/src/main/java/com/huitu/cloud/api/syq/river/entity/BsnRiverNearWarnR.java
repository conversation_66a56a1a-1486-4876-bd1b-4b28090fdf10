package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 河道临近告警表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-4-24
 */
@TableName("BSN_RIVERNEARWARN_R")
@ApiModel(value = "BsnRiverNearWarnR对象", description = "河道临近告警表")
public class BsnRiverNearWarnR extends Model<BsnRiverNearWarnR> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "水位")
    @TableField("Z")
    private BigDecimal z;

    @ApiModelProperty(value = "流量")
    @TableField("Q")
    private BigDecimal q;

    @ApiModelProperty(value = "水势")
    @TableField("WPTN")
    private String wptn;

    @ApiModelProperty(value = "超临近警戒水位")
    @TableField("ENWRZ")
    private BigDecimal enwrz;

    @ApiModelProperty(value = "距警戒水位")
    @TableField("DWRZ")
    private BigDecimal dwrz;

    @ApiModelProperty(value = "超临近保证水位")
    @TableField("ENGRZ")
    private BigDecimal engrz;

    @ApiModelProperty(value = "距保证水位")
    @TableField("DGRZ")
    private BigDecimal dgrz;

    @ApiModelProperty(value = "水位变幅")
    @TableField("CHV")
    private BigDecimal chv;

    @ApiModelProperty(value = "超水位变幅")
    @TableField("ECHV")
    private BigDecimal echv;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public BigDecimal getZ() {
        return z;
    }

    public void setZ(BigDecimal z) {
        this.z = z;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public String getWptn() {
        return wptn;
    }

    public void setWptn(String wptn) {
        this.wptn = wptn;
    }

    public BigDecimal getEnwrz() {
        return enwrz;
    }

    public void setEnwrz(BigDecimal enwrz) {
        this.enwrz = enwrz;
    }

    public BigDecimal getEngrz() {
        return engrz;
    }

    public void setEngrz(BigDecimal engrz) {
        this.engrz = engrz;
    }

    public BigDecimal getDgrz() {
        return dgrz;
    }

    public void setDgrz(BigDecimal dgrz) {
        this.dgrz = dgrz;
    }

    public BigDecimal getDwrz() {
        return dwrz;
    }

    public void setDwrz(BigDecimal dwrz) {
        this.dwrz = dwrz;
    }

    public BigDecimal getChv() {
        return chv;
    }

    public void setChv(BigDecimal chv) {
        this.chv = chv;
    }

    public BigDecimal getEchv() {
        return echv;
    }

    public void setEchv(BigDecimal echv) {
        this.echv = echv;
    }

    @Override
    public String toString() {
        return "BsnRiverNearWarnR{" +
                "stcd='" + stcd + '\'' +
                ", tm=" + tm +
                ", z=" + z +
                ", q=" + q +
                ", wptn='" + wptn + '\'' +
                ", enwrz=" + enwrz +
                ", dwrz=" + dwrz +
                ", engrz=" + engrz +
                ", dgrz=" + dgrz +
                ", chv=" + chv +
                ", echv=" + echv +
                '}';
    }
}
