package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel
public class WarnShPersonVo implements Serializable {
    @ApiModelProperty(value = "人员编码")
    @TableId(value = "userid")
    private String userid;
    @ApiModelProperty(value = "真实姓名")
    private String realnm;
    @ApiModelProperty(value = "移动电话")
    private String mobile;

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getRealnm() {
        return realnm;
    }

    public void setRealnm(String realnm) {
        this.realnm = realnm;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
