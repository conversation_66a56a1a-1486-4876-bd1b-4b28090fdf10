<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.DanPersonDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.person.entity.DanPerson">
        SELECT ROW_NUMBER() OVER(ORDER BY B.ADCD, A<PERSON>, A.REALNM ASC) SORTNO, C.ADNM TOWN, D.ADNM XZC, A.ZRC, B.NAME
        DANAME, A.REALNM, A.MOBILE, A.TS, A.ID, E.ADNM xadnm, A.TS
        FROM BNS_DANPERSON_B A
        LEFT JOIN IA_C_DANAD B ON B.DAND = A.DAND
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = LEFT (B.ADCD, 9) + '000000'
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = LEFT (B.ADCD, 12) + '000'
        LEFT JOIN MDT_ADCDINFO_B E ON E.ADCD = LEFT (B.ADCD, 6) + '000000000'
        WHERE LEFT(A.XADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(A.XADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.daname!= null and map.daname !=''">
            AND CHARINDEX(#{map.daname}, B.NAME) > 0
        </if>
        <if test="map.zrc!= null and map.zrc !=''">
            AND CHARINDEX(#{map.zrc}, A.ZRC) > 0
        </if>
        ORDER BY SORTNO ASC
    </select>
    <select id="batchImport" statementType="CALLABLE" useCache="false" parameterType="string"
            resultType="com.huitu.cloud.api.ewci.person.entity.BnsDanpersonTmp">
        {CALL PROC_IMPORT_DAN_PERSON(#{batchNo, mode=IN})}
    </select>
    <select id="getDanSummaryList" resultType="com.huitu.cloud.api.ewci.person.entity.DanSummaryVo">
        SELECT ADCD, ADNM, PADCD, ALLNUM FROM (
        <if test="map.include == '1'.toString()">
            SELECT 0 SORTNO, A.ADCD ADCD, B.ADNM ADNM, B.PADCD PADCD,A.ALLNUM FROM (
            SELECT #{map.adcd} ADCD,COUNT(DISTINCT CASE WHEN A.MOBILE IS NOT NULL THEN A.MOBILE ELSE A.REALNM END)
            ALLNUM
            FROM BNS_DANPERSON_B A
            WHERE LEFT(A.XADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">
                AND LEFT(A.XADCD, 6) NOT IN ('220581')
            </if>
            ) A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
            WHERE EXISTS (SELECT 8 FROM MDT_ADCDINFO_E WHERE ADTP = '1' AND ADCD = A.ADCD)
        </if>
        <if test="map.include == '1'.toString() and map.level &lt;= 4">
            UNION ALL
        </if>
        <if test="map.level &lt;= 4">
            SELECT B.SORTNO, A.ADCD ADCD, A.ADNM ADNM, A.PADCD PADCD,C.ALLNUM
            FROM MDT_ADCDINFO_B A
            LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
            LEFT JOIN (SELECT ADCD, COUNT(DISTINCT CASE WHEN AMOBILE IS NOT NULL THEN AMOBILE ELSE AREALNM END) ALLNUM
            FROM (SELECT LEFT(LEFT(A.XADCD, #{map.lowLevel}) + '000000000000000', 15) ADCD, A.MOBILE AMOBILE, A.REALNM
            AREALNM
            FROM BNS_DANPERSON_B A
            WHERE LEFT(A.XADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND LEFT(A.XADCD, 6) NOT IN ('220581')
            <if test="map.level == '2'.toString()">
                UNION ALL
                SELECT '220581000000000' ADCD, A.MOBILE AMOBILE, A.REALNM AREALNM
                FROM BNS_DANPERSON_B A
                WHERE LEFT(A.XADCD, 6) = '220581'
            </if>
            ) A GROUP BY ADCD) C ON C.ADCD = A.ADCD
            WHERE A.PADCD = #{map.adcd}
        </if>
        ) A
        ORDER BY SORTNO ASC
    </select>

    <update id="danPersonEdit">
        UPDATE BNS_DANPERSON_B
        SET REALNM =#{realnm},
            MOBILE = #{mobile},
            TS = GETDATE()
        WHERE ID = #{id}
    </update>

</mapper>
