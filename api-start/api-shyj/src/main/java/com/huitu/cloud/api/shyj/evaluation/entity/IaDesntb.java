package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设计暴雨成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@TableName("IA_A_DESNTB")
@ApiModel(value = "IaADesntb对象", description = "设计暴雨成果表")
public class IaDesntb extends Model<IaDesntb> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "小流域代码")
    @TableId(value = "WSCD", type = IdType.NONE)
    private String wscd;

    @ApiModelProperty(value = "历时-(Min)")
    @TableField("INTV")
    private Double intv;

    @ApiModelProperty(value = "均值")
    @TableField("HVALUE")
    private Double hvalue;

    @ApiModelProperty(value = "变差系数(Cv)")
    @TableField("CV")
    private Double cv;

    @ApiModelProperty(value = "Cs/Cv")
    @TableField("CSVC")
    private Double csvc;

    @ApiModelProperty(value = "可能最大暴雨(PMP)")
    @TableField("PMP")
    private Double pmp;

    @ApiModelProperty(value = "100 年(1% H) 重现期雨量值")
    @TableField("H1")
    private Double h1;

    @ApiModelProperty(value = "50 年(2% H) 重现期雨量值")
    @TableField("H2")
    private Double h2;

    @ApiModelProperty(value = "20 年(5% H) 重现期雨量值")
    @TableField("H5")
    private Double h5;

    @ApiModelProperty(value = "10 年(10% H) 重现期雨量值")
    @TableField("H10")
    private Double h10;

    @ApiModelProperty(value = "5 年(20% H) 重现期雨量值")
    @TableField("H20")
    private Double h20;

    @ApiModelProperty(value = "导入人")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;

    @ApiModelProperty(value = "流域名称")
    private String wsnm;

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public Double getIntv() {
        return intv;
    }

    public void setIntv(Double intv) {
        this.intv = intv;
    }

    public Double getHvalue() {
        return hvalue;
    }

    public void setHvalue(Double hvalue) {
        this.hvalue = hvalue;
    }

    public Double getCv() {
        return cv;
    }

    public void setCv(Double cv) {
        this.cv = cv;
    }

    public Double getCsvc() {
        return csvc;
    }

    public void setCsvc(Double csvc) {
        this.csvc = csvc;
    }

    public Double getPmp() {
        return pmp;
    }

    public void setPmp(Double pmp) {
        this.pmp = pmp;
    }

    public Double getH1() {
        return h1;
    }

    public void setH1(Double h1) {
        this.h1 = h1;
    }

    public Double getH2() {
        return h2;
    }

    public void setH2(Double h2) {
        this.h2 = h2;
    }

    public Double getH5() {
        return h5;
    }

    public void setH5(Double h5) {
        this.h5 = h5;
    }

    public Double getH10() {
        return h10;
    }

    public void setH10(Double h10) {
        this.h10 = h10;
    }

    public Double getH20() {
        return h20;
    }

    public void setH20(Double h20) {
        this.h20 = h20;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.wscd;
    }

    @Override
    public String toString() {
        return "IaDesntb{" +
                "wscd=" + wscd +
                ", intv=" + intv +
                ", hvalue=" + hvalue +
                ", cv=" + cv +
                ", csvc=" + csvc +
                ", pmp=" + pmp +
                ", h1=" + h1 +
                ", h2=" + h2 +
                ", h5=" + h5 +
                ", h10=" + h10 +
                ", h20=" + h20 +
                ", signer=" + signer +
                ", audid=" + audid +
                ", status=" + status +
                ", remark=" + remark +
                ", moditime=" + moditime +
                "}";
    }
}
