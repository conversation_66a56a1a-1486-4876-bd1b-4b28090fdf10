package com.huitu.cloud.api.ewci.device.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.ewci.device.entity.*;
import com.huitu.cloud.api.ewci.device.mapper.*;
import com.huitu.cloud.api.ewci.device.service.IHwInfoService;
import com.huitu.cloud.api.ewci.device.util.LocalDateTimeConverter;
import com.huitu.cloud.api.ewci.sst.entity.PictureQo;
import com.huitu.cloud.api.ewci.sst.mapper.BnsPictureInfoDao;
import com.huitu.cloud.api.syq.stbprb.entity.StStbprpB;
import com.huitu.cloud.api.syq.stbprb.mapper.StStbprpbDao;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import com.huitu.cloud.api.usif.util.LoginUtils;
import com.huitu.cloud.api.xxjh.smallreser.entity.BsnStadtpB;
import com.huitu.cloud.api.xxjh.smallreser.mapper.SmallReserDao;
import com.huitu.cloud.entity.LoginUserInfo;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.IPAndUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;


/**
 * 设备信息维护 ServiceImpl
 *
 * <AUTHOR>
 */
@Service
public class HwInfoServiceImpl extends ServiceImpl<HwInfoDao, HwInfo> implements IHwInfoService {

    @Autowired
    private HwInfoDao baseDao;

    @Autowired
    private HwInfoHistoryDao hwHistoryDao;

    @Autowired
    private StStbprpbDao stStbprpbDao;

    @Autowired
    private StStbprpbDisDao stStbprpbDisDao;

    @Autowired
    private SmallReserDao smallReserDao;

    @Autowired
    private StFilebPicHistoryDao stFilebPicHistoryDao;

    @Autowired
    private HwInfoPicDao hwInfoPicDao;

    @Autowired
    private HwInfoPicHistoryDao hwInfoPicHistoryDao;

    @Autowired
    private BnsPictureInfoDao bnsPictureInfoDao;

    @Autowired
    private LoginUtils loginUtils;

    /**
     * 获取自动监测站正式列表
     *
     * @param query
     * @return
     */
    @Override
    public IPage<HwInfo> getHwInfoList(HwInfoQuery query) {
        UserInfos loginUser = loginUtils.getCurrentLoginUser();
        Page page = new Page<>(query.getPageNum(), query.getPageSize());
        int level = AdcdUtil.getAdLevel(query.getAdcd());
        Map<String, Object> params = query.toQueryParam();
        params.put("level", level);
        params.put("oLevel", AdcdUtil.getAdLevel(loginUser.getAdcd()));
        params.put("adcd", query.getAdcd().substring(0, level));

        return baseDao.getHwInfoList(page, params);
    }

    /**
     * 获取自动监测站待审核列表-省级使用status传1  市县级使用status传（1,2）
     *
     * @param query
     * @return
     */
    @Override
    public IPage<HwInfoHistory> getHwPendingReviewList(HwInfoQuery query) {
        UserInfos loginUser = loginUtils.getCurrentLoginUser();
        Page page = new Page<>(query.getPageNum(), query.getPageSize());
        int level = AdcdUtil.getAdLevel(query.getAdcd());
        Map<String, Object> params = query.toQueryParam();
        params.put("level", AdcdUtil.getAdLevel(query.getAdcd()));
        params.put("oLevel", AdcdUtil.getAdLevel(loginUser.getAdcd()));
        params.put("adcd", query.getAdcd().substring(0, level));
        if (Objects.isNull(query.getStatus())) {
            throw new IllegalArgumentException("status参数必传。");
        }
        params.put("status", query.getStatus());
        return baseDao.getHwPendingReviewList(page, params);
    }

    /**
     * 更新测站设备信息
     *
     * @param info 前端提交上来的数据 (对象中要包含adcd的值)
     * @return 受影响的行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateHwInfo(HwInfo info) {

        // 1. 验证历史表中是否有待审核的记录，如果有，直接提示。
        QueryWrapper<HwInfoHistory> validHistoryWrapper = new QueryWrapper<>();
        validHistoryWrapper.eq("stcd", info.getStcd());
        validHistoryWrapper.eq("status", "1"); // 待审核状态
        int valid = hwHistoryDao.selectCount(validHistoryWrapper);
        if (valid > 0) {
            throw new IllegalArgumentException("当前测站存在待审核数据。");
        }

        // 2. 在历史表中新增一条数据、创建历史表
        HwInfo hwInfoTemp = baseDao.selectOne(new QueryWrapper<HwInfo>().eq("stcd", info.getStcd()));

        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
        HwInfoHistory historyNew = new HwInfoHistory();
        BeanUtils.copyProperties(hwInfoTemp, historyNew);
        // 拷贝测站基本信息
        StStbprpB stStbprpB = stStbprpbDao.selectOne(new QueryWrapper<StStbprpB>().eq("stcd", info.getStcd()));
        if (Objects.nonNull(stStbprpB)) { // 正式测站
            BeanUtils.copyProperties(stStbprpB, historyNew);
        } else {  // 废弃测站
            StStbprpbDis stStbprpbDis = stStbprpbDisDao.selectOne(new QueryWrapper<StStbprpbDis>().eq("stcd", info.getStcd()));
            BeanUtils.copyProperties(stStbprpbDis, historyNew);
        }
        historyNew.setId(UUID.randomUUID().toString());
        historyNew.setStatus("1");
        historyNew.setCreated(LocalDateTime.now());
        historyNew.setCreator(loginUserInfo.getUserId());
        historyNew.setModitime(LocalDateTime.now());
        hwHistoryDao.insert(historyNew);

        // 3.1 处理站点图片、设备图片开始=================
        if (Objects.nonNull(info.getStpics())) {
            info.getStpics().forEach(stPic -> {
                StFilebPicHistory stFilebPicHistory = new StFilebPicHistory();
                stFilebPicHistory.setKeyid(historyNew.getStcd());
                stFilebPicHistory.setFilename(stPic.getFilename());
                stFilebPicHistory.setFilesize(stPic.getFilesize());
                stFilebPicHistory.setFilepath(stPic.getFilepath());
                stFilebPicHistory.setType("测站信息");
                stFilebPicHistory.setSourceid(historyNew.getId());
                stFilebPicHistory.setId(UUID.randomUUID().toString());

                stFilebPicHistoryDao.insert(stFilebPicHistory);
            });
        }

        if (Objects.nonNull(info.getHwpics())) {
            info.getHwpics().forEach(hwPic -> {
                HwInfoPicHistory hwInfoPicHistory = new HwInfoPicHistory();
                hwInfoPicHistory.setSourceid(historyNew.getId());
                hwInfoPicHistory.setCreated(LocalDateTime.now());
                hwInfoPicHistory.setKeyid(historyNew.getStcd());
                hwInfoPicHistory.setFilename(hwPic.getFilename());
                hwInfoPicHistory.setFilesize(hwPic.getFilesize());
                hwInfoPicHistory.setFilepath(hwPic.getFilepath());
                hwInfoPicHistory.setId(UUID.randomUUID().toString());
                hwInfoPicHistoryDao.insert(hwInfoPicHistory);
            });
        }
        // 3.1 处理站点图片、设备图片结束=================

        // 4. 根据前端传递过来的字段更新历史表数据
        if (Objects.nonNull(info.getStatus())
                && Objects.equals(info.getStatus(), "2")) { // 代表是从审核不通过入口编辑的
            hwHistoryDao.delete(new QueryWrapper<HwInfoHistory>()
                    .eq("stcd", info.getStcd())
                    .eq("status", info.getStatus()));
            info.setStatus("1");
            info.setAudreason(null);
        }
        info.setModitime(LocalDateTime.now());
        BeanUtils.copyProperties(info, historyNew);

        baseDao.updateHwHistory(historyNew);

//        hwHistoryDao.update(historyNew, new UpdateWrapper<HwInfoHistory>()
//                .eq("stcd", info.getStcd())
//                .eq("status", "1")
//                .setSql("bdate = NULL")
//        );


        // 5. 验证是否是省级账号，如果是省级，自动调一下审核通过接口。
        // 获取账号是省级=1、市县级=2/3
        int adLevl = AdcdUtil.getXzAdLevel(loginUserInfo.getUserAd());
        if (adLevl == 1) {
            info.setStatus("1");
            this.auditHwInfo(info);
        }
        return 1;
    }

    /**
     * 审核接口
     *
     * @param info 前端提交上来的数据，含审核状态、不通过原因  对象中至少包含（stcd测站编码、status1=通过 status2=不通过、audreason审核原因）
     * @return 受影响的行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditHwInfo(HwInfo info) {
        // 审核流程前验证
        if (Objects.isNull(info.getStcd()) || Objects.isNull(info.getStatus())) {
            throw new IllegalArgumentException("测站编码或审核状态不存在。");
        }
        if (Objects.equals(info.getStatus(), "2") && Objects.isNull(info.getAudreason())) {
            throw new IllegalArgumentException("审核不通过时需填写原因。");
        }
        QueryWrapper<HwInfoHistory> validHistoryWrapper = new QueryWrapper<>();
        validHistoryWrapper.eq("stcd", info.getStcd());
        validHistoryWrapper.eq("status", "1");
        HwInfoHistory hwInfoHistory = hwHistoryDao.selectOne(validHistoryWrapper);
        if (Objects.isNull(hwInfoHistory)) {
            throw new IllegalArgumentException("待审核的记录不存在。");
        }
        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);

        // 进入审核流程
        if (Objects.equals(info.getStatus(), "1")) {  // 审核通过

            // 将正式表数据先插入到历史表中  状态设置为0 表示历史 设置审核人  设置审核时间
            QueryWrapper<HwInfo> hwInfoQueryWrapper = new QueryWrapper<>();
            hwInfoQueryWrapper.eq("stcd", info.getStcd());
            HwInfo hwInfo = baseDao.selectOne(hwInfoQueryWrapper);
            String uuid = UUID.randomUUID().toString();
            HwInfoHistory history = new HwInfoHistory();
            BeanUtils.copyProperties(hwInfo, history);
            history.setId(uuid);
            history.setStatus("0");
            history.setAuditor(loginUserInfo.getUserId());
            history.setAudited(LocalDateTime.now());
            // 测站信息数据插入到历史表中
            // 拷贝测站基本信息
            StStbprpB stStbprpB = stStbprpbDao.selectOne(new QueryWrapper<StStbprpB>().eq("stcd", info.getStcd()));
            if (Objects.nonNull(stStbprpB)) { // 正式测站
                BeanUtils.copyProperties(stStbprpB, history);
            } else {  // 废弃测站
                StStbprpbDis stStbprpbDis = stStbprpbDisDao.selectOne(new QueryWrapper<StStbprpbDis>().eq("stcd", info.getStcd()));
                BeanUtils.copyProperties(stStbprpbDis, history);
            }

            hwHistoryDao.insert(history);

            // 删除正式表的数据
            QueryWrapper<HwInfo> deleleQueryWrapper = new QueryWrapper<>();
            deleleQueryWrapper.eq("stcd", info.getStcd());
            baseDao.delete(deleleQueryWrapper);

            // 将历史表数据插入到正式表中
            HwInfo hwInfoNew = new HwInfo();
            BeanUtils.copyProperties(hwInfoHistory, hwInfoNew);
            hwInfoNew.setSourceid(hwInfoHistory.getId());
            hwInfoNew.setModitime(LocalDateTime.now());
            hwInfoNew.setAuditor(loginUserInfo.getUserId());
            hwInfoNew.setAudited(LocalDateTime.now());
            baseDao.insert(hwInfoNew);

            // 删除历史表中当前数据
            hwHistoryDao.deleteById(hwInfoHistory.getId());


            // 处理设备图片开始==================
            // 将正式表的设备图片转移到历史表中
            hwInfoPicDao.selectList(new QueryWrapper<HwInfoPic>()
                    .eq("keyid", info.getStcd())
            ).stream().forEach(hwInfoPic -> {
                HwInfoPicHistory hwInfoPicHistory = new HwInfoPicHistory();
                hwInfoPicHistory.setId(UUID.randomUUID().toString());
                hwInfoPicHistory.setSourceid(uuid);
                hwInfoPicHistory.setCreated(LocalDateTime.now());
                hwInfoPicHistory.setKeyid(info.getStcd());
                hwInfoPicHistory.setFilename(hwInfoPic.getFilename());
                hwInfoPicHistory.setFilesize(hwInfoPic.getFilesize());
                hwInfoPicHistory.setFilepath(hwInfoPic.getFilepath());
                hwInfoPicHistoryDao.insert(hwInfoPicHistory);
            });
            // 删除正式表的数据
            hwInfoPicDao.delete(new QueryWrapper<HwInfoPic>().eq("keyid", info.getStcd()));
            // 将历史表中的数据插入到正式表中
            hwInfoPicHistoryDao.selectList(new QueryWrapper<HwInfoPicHistory>()
                    .eq("sourceid", hwInfoHistory.getId())).stream().forEach(
                    hwInfoPicHistory -> {
                        HwInfoPic hwInfoPic = new HwInfoPic();
                        hwInfoPic.setId(hwInfoPicHistory.getId());
                        hwInfoPic.setSourceid(hwInfoPicHistory.getSourceid());
                        hwInfoPic.setCreated(hwInfoPicHistory.getCreated());
                        hwInfoPic.setKeyid(hwInfoPicHistory.getKeyid());
                        hwInfoPic.setFilename(hwInfoPicHistory.getFilename());
                        hwInfoPic.setFilesize(hwInfoPicHistory.getFilesize());
                        hwInfoPic.setFilepath(hwInfoPicHistory.getFilepath());
                        hwInfoPicDao.insert(hwInfoPic);
                        hwInfoPicHistoryDao.deleteById(hwInfoPicHistory.getId());
                    }
            );
            // 处理设备图片结束==================

            // 处理测站图片开始==================
            // 将正式表的测站图片转移到历史表中
            List<PictureQo> stPic = bnsPictureInfoDao.getPicture(info.getStcd(), "测站信息");
            stPic.stream().forEach(pictureQo -> {
                StFilebPicHistory stFilebPicHistory = new StFilebPicHistory();
                stFilebPicHistory.setId(UUID.randomUUID().toString());
                stFilebPicHistory.setKeyid(info.getStcd());
                stFilebPicHistory.setFilename(pictureQo.getFilename());
                stFilebPicHistory.setFilesize(Objects.nonNull(pictureQo.getFilesize()) ? Long.parseLong(pictureQo.getFilesize()) : 0);
                stFilebPicHistory.setFilepath(pictureQo.getFilepath());
                stFilebPicHistory.setType(pictureQo.getType());
                stFilebPicHistory.setShid(pictureQo.getShid());
                stFilebPicHistory.setSourceid(uuid);
                stFilebPicHistoryDao.insert(stFilebPicHistory);
            });
            // 删除正式表的数据
            bnsPictureInfoDao.deletePicture(info.getStcd(), "测站信息");
            // 将历史表中的数据插入到正式表中
            stFilebPicHistoryDao.selectList(
                            new QueryWrapper<StFilebPicHistory>()
                                    .eq("sourceid", hwInfoHistory.getId())).stream()
                    .forEach(stFilebPicHistory -> {
                        List<PictureQo> infoList = new ArrayList<>();
                        PictureQo pictureQo = new PictureQo();
                        pictureQo.setFilename(stFilebPicHistory.getFilename());
                        pictureQo.setFilepath(stFilebPicHistory.getFilepath());
                        pictureQo.setShid(stFilebPicHistory.getShid());
                        pictureQo.setFilesize(String.valueOf(stFilebPicHistory.getFilesize()));
                        pictureQo.setKeyid(stFilebPicHistory.getKeyid());
                        pictureQo.setType(stFilebPicHistory.getType());
                        pictureQo.setId(bnsPictureInfoDao.getMaxId());
                        infoList.add(pictureQo);
                        bnsPictureInfoDao.addPicture(infoList);
                        // 删除历史表中的数据
                        stFilebPicHistoryDao.deleteById(stFilebPicHistory.getId());
                    });

            // 处理测站图片结束==================

            // 单独处理测站更新信息
            if (Objects.equals(hwInfoNew.getUsfl(), "1")) { // 正常测站表
                UpdateWrapper<StStbprpB> updateWrapper = Wrappers.update();

                updateWrapper.set("stnm", hwInfoNew.getStnm());
                updateWrapper.set("rvnm", hwInfoNew.getRvnm());
                updateWrapper.set("hnnm", hwInfoNew.getHnnm());
                updateWrapper.set("bsnm", hwInfoNew.getBsnm());
                updateWrapper.set("lgtd", hwInfoNew.getLgtd());
                updateWrapper.set("lttd", hwInfoNew.getLttd());
                updateWrapper.set("stlc", hwInfoNew.getStlc());
                updateWrapper.set("addvcd", hwInfoNew.getAddvcd());
                updateWrapper.set("dtmnm", hwInfoNew.getDtmnm());
                updateWrapper.set("dtmel", hwInfoNew.getDtmel());
                updateWrapper.set("dtpr", hwInfoNew.getDtpr());
                updateWrapper.set("sttp", hwInfoNew.getSttp());
                updateWrapper.set("frgrd", hwInfoNew.getFrgrd());
                updateWrapper.set("esstym", hwInfoNew.getEsstym());
                updateWrapper.set("bgfrym", hwInfoNew.getBgfrym());
                updateWrapper.set("atcunit", hwInfoNew.getAtcunit());
                updateWrapper.set("admauth", hwInfoNew.getAdmauth());
                updateWrapper.set("locality", hwInfoNew.getLocality());
                updateWrapper.set("stbk", hwInfoNew.getStbk());
                updateWrapper.set("stazt", hwInfoNew.getStazt());
                updateWrapper.set("dstrvm", hwInfoNew.getDstrvm());
                updateWrapper.set("drna", hwInfoNew.getDrna());
                updateWrapper.set("phcd", hwInfoNew.getPhcd());
                updateWrapper.set("usfl", hwInfoNew.getUsfl());
                updateWrapper.set("comments", hwInfoNew.getComments());
                updateWrapper.set("moditime", LocalDateTime.now());
                updateWrapper.eq("stcd", info.getStcd());
                stStbprpbDao.update(null, updateWrapper);
            } else { // 废弃测站表
                UpdateWrapper<StStbprpbDis> updateWrapper = Wrappers.update();

                updateWrapper.set("stnm", hwInfoNew.getStnm());
                updateWrapper.set("rvnm", hwInfoNew.getRvnm());
                updateWrapper.set("hnnm", hwInfoNew.getHnnm());
                updateWrapper.set("bsnm", hwInfoNew.getBsnm());
                updateWrapper.set("lgtd", hwInfoNew.getLgtd());
                updateWrapper.set("lttd", hwInfoNew.getLttd());
                updateWrapper.set("stlc", hwInfoNew.getStlc());
                updateWrapper.set("addvcd", hwInfoNew.getAddvcd());
                updateWrapper.set("dtmnm", hwInfoNew.getDtmnm());
                updateWrapper.set("dtmel", hwInfoNew.getDtmel());
                updateWrapper.set("dtpr", hwInfoNew.getDtpr());
                updateWrapper.set("sttp", hwInfoNew.getSttp());
                updateWrapper.set("frgrd", hwInfoNew.getFrgrd());
                updateWrapper.set("esstym", hwInfoNew.getEsstym());
                updateWrapper.set("bgfrym", hwInfoNew.getBgfrym());
                updateWrapper.set("atcunit", hwInfoNew.getAtcunit());
                updateWrapper.set("admauth", hwInfoNew.getAdmauth());
                updateWrapper.set("locality", hwInfoNew.getLocality());
                updateWrapper.set("stbk", hwInfoNew.getStbk());
                updateWrapper.set("stazt", hwInfoNew.getStazt());
                updateWrapper.set("dstrvm", hwInfoNew.getDstrvm());
                updateWrapper.set("drna", hwInfoNew.getDrna());
                updateWrapper.set("phcd", hwInfoNew.getPhcd());
                updateWrapper.set("usfl", hwInfoNew.getUsfl());
                updateWrapper.set("comments", hwInfoNew.getComments());
                updateWrapper.set("moditime", LocalDateTime.now());
                updateWrapper.eq("stcd", info.getStcd());
                stStbprpbDisDao.update(null, updateWrapper);
//                StStbprpbDis stStbprpbDis = new StStbprpbDis();
//                BeanUtils.copyProperties(hwInfoNew, stStbprpbDis);
//                stStbprpbDis.setModitime(LocalDateTime.now());
//                stStbprpbDis.setUsfl("0");
//                stStbprpbDisDao.update(stStbprpbDis, new UpdateWrapper<StStbprpbDis>().eq("stcd", info.getStcd()));
            }

            // 更新测站扩展表数据
            if (StringUtils.isBlank(info.getAdcd())) {
                info.setAdcd(info.getAddvcd() + "000000000");
            }
            baseDao.updateStationExtend(info);

        } else {  // 审核不通过
            HwInfoHistory historyNew = new HwInfoHistory();
            hwHistoryDao.update(historyNew, new UpdateWrapper<HwInfoHistory>()
                    .eq("stcd", info.getStcd())
                    .eq("status", "1")
                    .set("audited", LocalDateTime.now())
                    .set("auditor", loginUserInfo.getUserId())
                    .set("audreason", info.getAudreason())
                    .set("status", "2")
                    .set("moditime", LocalDateTime.now())
            );
        }

    }

    /**
     * 正式数据编辑回显接口
     *
     * @param stcd 测站编码
     * @return HwInfo
     */
    @Override
    public HwInfo getHwInfoByStcd(String stcd) {
        QueryWrapper<HwInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("stcd", stcd);
        HwInfo hwInfo = baseDao.selectOne(queryWrapper);
        if (Objects.isNull(hwInfo)) {
            // 新建测站设备表数据
            LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
            hwInfo = new HwInfo();
            hwInfo.setStcd(stcd);
            hwInfo.setCreator(loginUserInfo.getUserId());
            hwInfo.setCreated(LocalDateTime.now());
            baseDao.insert(hwInfo);
        }

        // 实时查询测站基本信息表的数据开始
        int count = stStbprpbDao.selectCount(new QueryWrapper<StStbprpB>()
                .eq("stcd", stcd));
        if (count > 0) { // 正常测站
            QueryWrapper<StStbprpB> stStbprpBQueryWrapper = new QueryWrapper<>();
            stStbprpBQueryWrapper.eq("stcd", stcd);
            StStbprpB stStbprpB = stStbprpbDao.selectOne(stStbprpBQueryWrapper);
            stStbprpB.setUsfl("1");
            BeanUtils.copyProperties(stStbprpB, hwInfo);
        } else { // 废弃测站
            QueryWrapper<StStbprpbDis> stStbprpbDisQueryWrapper = new QueryWrapper<>();
            stStbprpbDisQueryWrapper.eq("stcd", stcd);
            StStbprpbDis stStbprpbDis = stStbprpbDisDao.selectOne(stStbprpbDisQueryWrapper);
            stStbprpbDis.setUsfl("0");
            BeanUtils.copyProperties(stStbprpbDis, hwInfo);
        }
        // 实时查询测站基本信息表的数据结束

        QueryWrapper<BsnStadtpB> bsnStadtpBQueryWrapper = new QueryWrapper<>();
        bsnStadtpBQueryWrapper.eq("stcd", stcd);
        BsnStadtpB bsnStadtpbOne = smallReserDao.selectOne(bsnStadtpBQueryWrapper);
        if (Objects.nonNull(bsnStadtpbOne)) {
            hwInfo.setAdcd(bsnStadtpbOne.getAdcd());
        }
        // 处理政区名称 根据 st.addvcd+ '000000000' = ad.adcd
        String adnm = baseDao.getAdnmByAddvcd(hwInfo.getAddvcd());
        hwInfo.setAdnm(adnm);

        // 处理站点图片、处理设备图片
        List<HwPictureInfo> stpics = new ArrayList<>();
        bnsPictureInfoDao.getPicture(stcd, "测站信息")
                .forEach(pictureQo -> {
                    HwPictureInfo hwPictureInfo = new HwPictureInfo();
                    hwPictureInfo.setFilename(pictureQo.getFilename());
                    hwPictureInfo.setFilesize(Objects.nonNull(pictureQo.getFilesize()) ? Long.parseLong(pictureQo.getFilesize()) : 0);
                    hwPictureInfo.setFilepath(pictureQo.getFilepath());
                    stpics.add(hwPictureInfo);
                });
        hwInfo.setStpics(stpics);
        List<HwPictureInfo> hwpics = new ArrayList<>();
        hwInfoPicDao.selectList(new QueryWrapper<HwInfoPic>()
                .eq("keyid", stcd)).forEach(hwInfoPic -> {
            HwPictureInfo hwPictureInfo = new HwPictureInfo();
            hwPictureInfo.setFilename(hwInfoPic.getFilename());
            hwPictureInfo.setFilesize(hwInfoPic.getFilesize());
            hwPictureInfo.setFilepath(hwInfoPic.getFilepath());
            hwpics.add(hwPictureInfo);
        });
        hwInfo.setHwpics(hwpics);

        // 单独处理adcd
        String adcd = baseDao.selectAdcdByStcd(stcd);
        hwInfo.setAdcd(adcd);

        return hwInfo;
    }

    /**
     * 待处理数据编辑回显接口
     *
     * @param stcd 测站编码
     * @return HwInfo
     */
    @Override
    public HwInfoHistory getHwInfoHistoryByStcd(String stcd, String status, String id) {
        HwInfoHistory hwInfo = null;
        if (Objects.isNull(id)) {
            if (Objects.isNull(status)) {
                status = "2";
            }
            QueryWrapper<HwInfoHistory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("stcd", stcd);
            queryWrapper.eq("status", status);
            hwInfo = hwHistoryDao.selectOne(queryWrapper);
        } else if (Objects.equals(id, "-1")) { // 当前数据取正式表的数据
            HwInfo hwInfoByStcd = this.getHwInfoByStcd(stcd);
            hwInfo = new HwInfoHistory();
            BeanUtils.copyProperties(hwInfoByStcd, hwInfo);
            return hwInfo;
        } else {
            hwInfo = hwHistoryDao.selectById(id);
            stcd = hwInfo.getStcd();
        }

        if (Objects.isNull(hwInfo)) {
            throw new IllegalArgumentException("测站信息不存在相应数据。");
        }

        String addvcd = null;
        // 处理政区名称 根据 st.addvcd+ '000000000' = ad.adcd
//        StStbprpB stStbprpB = stStbprpbDao.selectOne(new QueryWrapper<StStbprpB>().eq("stcd", stcd));
//        if (Objects.nonNull(stStbprpB)) {
//            addvcd = stStbprpB.getAddvcd();
//        } else {
//            addvcd = stStbprpbDisDao.selectOne(new QueryWrapper<StStbprpbDis>().eq("stcd", stcd)).getAddvcd();
//        }

        String adnm = baseDao.getAdnmByAddvcd(hwInfo.getAddvcd());

//        String adnm = baseDao.getAdnmByAddvcd(addvcd);
        hwInfo.setAdnm(adnm);

        // 处理站点图片、处理设备图片
        List<HwPictureInfo> stpics = new ArrayList<>();
        stFilebPicHistoryDao.selectList(new QueryWrapper<StFilebPicHistory>()
                .eq("keyid", stcd)
                .eq("sourceid", hwInfo.getId())).forEach(stFilebPicHistory -> {
            HwPictureInfo hwPictureInfo = new HwPictureInfo();
            hwPictureInfo.setFilename(stFilebPicHistory.getFilename());
            hwPictureInfo.setFilesize(stFilebPicHistory.getFilesize());
            hwPictureInfo.setFilepath(stFilebPicHistory.getFilepath());
            stpics.add(hwPictureInfo);

        });
        hwInfo.setStpics(stpics);

        List<HwPictureInfo> hwpics = new ArrayList<>();
        hwInfoPicHistoryDao.selectList(new QueryWrapper<HwInfoPicHistory>().eq("keyid", stcd).eq("sourceid", hwInfo.getId()))
                .forEach(hwInfoPicHistory -> {
                    HwPictureInfo hwPictureInfo = new HwPictureInfo();
                    hwPictureInfo.setFilename(hwInfoPicHistory.getFilename());
                    hwPictureInfo.setFilesize(hwInfoPicHistory.getFilesize());
                    hwPictureInfo.setFilepath(hwInfoPicHistory.getFilepath());
                    hwpics.add(hwPictureInfo);

                });
        hwInfo.setHwpics(hwpics);

        // 单独处理adcd
        String adcd = baseDao.selectAdcdByStcd(stcd);
        hwInfo.setAdcd(adcd);

        return hwInfo;
    }


    /**
     * 获取设备待审核信息数量
     *
     * @param status 类型：1=自动监测站，2=视频监控站，3=无线预警广播站"
     * @return
     */
    @Override
    public Integer getInfoReviewCount(String status) {
        QueryWrapper<HwInfoHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", status);
        return hwHistoryDao.selectCount(queryWrapper);
    }

    /**
     * 获取审核对比数据
     *
     * @param stcd 测站编码
     * @return List<HwInfoHistory>
     */
    @Override
    public List<HwInfoHistory> getHwAuditList(String stcd) {
        QueryWrapper<HwInfoHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("stcd", stcd);
        queryWrapper.eq("status", 1);
        HwInfoHistory hwInfoHistory = hwHistoryDao.selectOne(queryWrapper);
        if (Objects.isNull(hwInfoHistory)) {
            System.out.println("站点待审核数据不存在。");
        }
        List<HwInfoHistory> hwAuditList = baseDao.getHwAuditList(stcd);
        if (hwAuditList.size() != 2) {
            System.out.println("当前站点不存在待审核数据。");
        }
        hwAuditList.forEach(hwInfoHistoryTemp -> {
            if (Objects.equals(hwInfoHistoryTemp.getStatus(), "1")) {  // 待审核的
                List<HwPictureInfo> stpics = new ArrayList<>();
                stFilebPicHistoryDao.selectList(new QueryWrapper<StFilebPicHistory>()
                        .eq("keyid", stcd)
                        .eq("sourceid", hwInfoHistoryTemp.getId())).forEach(stFilebPicHistory -> {
                    HwPictureInfo hwPictureInfo = new HwPictureInfo();
                    hwPictureInfo.setFilename(stFilebPicHistory.getFilename());
                    hwPictureInfo.setFilesize(stFilebPicHistory.getFilesize());
                    hwPictureInfo.setFilepath(stFilebPicHistory.getFilepath());
                    stpics.add(hwPictureInfo);

                });
                hwInfoHistoryTemp.setStpics(stpics);

                List<HwPictureInfo> hwpics = new ArrayList<>();
                hwInfoPicHistoryDao.selectList(new QueryWrapper<HwInfoPicHistory>().eq("keyid", stcd).eq("sourceid", hwInfoHistoryTemp.getId()))
                        .forEach(hwInfoPicHistory -> {
                            HwPictureInfo hwPictureInfo = new HwPictureInfo();
                            hwPictureInfo.setFilename(hwInfoPicHistory.getFilename());
                            hwPictureInfo.setFilesize(hwInfoPicHistory.getFilesize());
                            hwPictureInfo.setFilepath(hwInfoPicHistory.getFilepath());
                            hwpics.add(hwPictureInfo);
                        });
                hwInfoHistoryTemp.setHwpics(hwpics);
            } else { // 正式的
                List<HwPictureInfo> stpics = new ArrayList<>();
                bnsPictureInfoDao.getPicture(stcd, "测站信息")
                        .forEach(pictureQo -> {
                            HwPictureInfo hwPictureInfo = new HwPictureInfo();
                            hwPictureInfo.setFilename(pictureQo.getFilename());
                            hwPictureInfo.setFilesize(Objects.nonNull(pictureQo.getFilesize()) ? Long.parseLong(pictureQo.getFilesize()) : 0);
                            hwPictureInfo.setFilepath(pictureQo.getFilepath());
                            stpics.add(hwPictureInfo);
                        });
                hwInfoHistoryTemp.setStpics(stpics);
                List<HwPictureInfo> hwpics = new ArrayList<>();
                hwInfoPicDao.selectList(new QueryWrapper<HwInfoPic>()
                        .eq("keyid", stcd)).forEach(hwInfoPic -> {
                    HwPictureInfo hwPictureInfo = new HwPictureInfo();
                    hwPictureInfo.setFilename(hwInfoPic.getFilename());
                    hwPictureInfo.setFilesize(hwInfoPic.getFilesize());
                    hwPictureInfo.setFilepath(hwInfoPic.getFilepath());
                    hwpics.add(hwPictureInfo);
                });
                hwInfoHistoryTemp.setHwpics(hwpics);
            }
        });
        return hwAuditList;
    }

    /**
     * 获取历史数据
     *
     * @param stcd 测站编码
     * @return List<HwInfoHistory>
     */
    @Override
    public List<HwInfoHistory> getHwHistoryList(String stcd) {
        if (baseDao.selectCount(new QueryWrapper<HwInfo>().eq("stcd", stcd)) == 0) {
            LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
            HwInfo hwInfo = new HwInfo();
            hwInfo.setStcd(stcd);
            hwInfo.setCreated(LocalDateTime.now());
            hwInfo.setCreator(loginUserInfo.getUserId());
            hwInfo.setUsfl("1");
            baseDao.insert(hwInfo);
        }
        return baseDao.getHwHistoryList(stcd);
    }

    @Override
    public List<DeviceStatistics> getHwCountList(String adcd) {
        UserInfos loginUser = loginUtils.getCurrentLoginUser();
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("level", level);
        param.put("oLevel", AdcdUtil.getAdLevel(loginUser.getAdcd()));
        param.put("adcd", adcd);
        param.put("adlvl", level == 2 ? "1,2" : "2,3");
        List<DeviceStatistics> list = baseDao.getHwCountList(param);
        return list;
    }

    /**
     * 导出无线预警广播站维护信息
     *
     * @param query    查询条件
     * @param response 响应
     */
    @Override
    public void exporthwList(HwInfoQuery query, HttpServletResponse response) {
        Page page = new Page<>(query.getPageNum(), query.getPageSize());
        int level = AdcdUtil.getAdLevel(query.getAdcd());
        Map<String, Object> params = query.toQueryParam();
        params.put("level", level);
        params.put("adcd", query.getAdcd().substring(0, level));
        IPage<HwInfo> wbrInfonList = baseDao.getHwInfoList(page, params);
        List<HwInfo> records = wbrInfonList.getRecords();
        // **增加序号列，从 1 开始**
        for (int i = 0; i < records.size(); i++) {
            records.get(i).setSerialNumber(i + 1);
        }
        try {
            if (response == null) {
                response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            }
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("自动监测站维护信息", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 注册 LocalDateTime 转换器
            EasyExcel.write(response.getOutputStream(), HwInfo.class)
                    .registerConverter(new LocalDateTimeConverter())
                    .sheet("数据")
                    .doWrite(records);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int deleteId(String id) {
        return baseDao.deleteId(id);
    }
}
