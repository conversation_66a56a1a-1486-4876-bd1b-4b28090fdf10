package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 无线预警广播站
 */
@Data
@ApiModel(value = "无线预警广播站")
public class BnsInspectWarnBroad extends BnsInspectReportForm {

    @ApiModelProperty(value = "村屯政区编码")
    private String tadcd;

    @ApiModelProperty(value = "无线预警广播站名称")
    private String wbrnm;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;

    @ApiModelProperty(value = "村屯政区名称")
    private String tadnm;
}
