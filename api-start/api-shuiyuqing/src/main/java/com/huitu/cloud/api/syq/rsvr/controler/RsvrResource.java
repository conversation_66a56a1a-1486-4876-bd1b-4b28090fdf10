package com.huitu.cloud.api.syq.rsvr.controler;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.syq.rain.controler.RainResource;
import com.huitu.cloud.api.syq.rain.entity.QueryRainTimeInterval;
import com.huitu.cloud.api.syq.rsvr.entity.*;
import com.huitu.cloud.api.syq.rsvr.service.RsvrService;
import com.huitu.cloud.api.xxjh.smallreser.entity.BsnRsvrrecorderfileR;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * <p>
 * 水库水情表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-09
 */

@RestController
@Api(tags = "水库水情信息")
@Validated
@RequestMapping("/api/syq/rsvr")
public class RsvrResource extends AbstractApiResource implements ApiResource {

    private static final Logger logger = LoggerFactory.getLogger(RsvrResource.class);

    @Override
    public String getUuid() {
        return "1abc003c-d9e1-45d1-8921-9ef9c6207f1f";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private RsvrService baseService;

    @ApiOperation(value = "查询最新水库水情信息", notes = "根据选择条件查询一段时间范围内的最新水库水情信息")
    @PostMapping(value = "select-latest-info")
    public ResponseEntity<SuccessResponse<Page<StRsvrVo>>> getStRsvrLatestList(@RequestBody RsvrQuery baseDao) throws Exception {
        IPage<StRsvrVo> list = baseService.getRsvrLatestByConditon(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm(), baseDao.getStnm(),
                baseDao.getBscd(), baseDao.getStType(), baseDao.getRvType(), baseDao.isDataFlag(), baseDao.isWarnFlag(),
                baseDao.getPageNum(), baseDao.getPageSize(), baseDao.getIsOut(), baseDao.getIsFollow(), baseDao.isWarnOpenFlag(), baseDao.getDrz(), baseDao.isVideoFlag());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "水位数据对比分析", notes = "zyj")
    @PostMapping(value = "select-rsvr-compare-z-list")
    public ResponseEntity<SuccessResponse<Page<StRsvrVo>>> getRsvrCompareZList(@RequestBody RsvrQuery baseDao) throws Exception {
        IPage<StRsvrVo> list = baseService.getRsvrCompareZList(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm(), baseDao.getStnm(),
                baseDao.getBscd(), baseDao.getStType(), baseDao.getRvType(),
                baseDao.getPageNum(), baseDao.getPageSize(), baseDao.getDrz());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "水位数据对比分析导出", notes = "zyj")
    @PostMapping(value = "export-rsvr-compare-z")
    public void ExportRsvrCompareZList(@RequestBody RsvrQuery baseDao) throws Exception {
        baseService.ExportRsvrCompareZList(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm(), baseDao.getStnm(),
                baseDao.getBscd(), baseDao.getStType(), baseDao.getRvType(),
                baseDao.getPageNum(), baseDao.getPageSize(), baseDao.getDrz());
    }

    @ApiOperation(value = "最新水库水情信息导出", notes = "根据选择条件查询一段时间范围内的最新水库水情信息导出")
    @PostMapping(value = "export-latest-info")
    public void ExportStRsvrLatestList(@RequestBody ExportRsvr baseDao) throws Exception {
        baseService.ExportStRsvrLatestList(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm(), baseDao.getStnm(),
                baseDao.getBsnm(), baseDao.getStType(), baseDao.getRvType(), baseDao.isDataFlag(), baseDao.isWarnFlag(),
                baseDao.isWarnOpenFlag(), baseDao.getIsOut(), baseDao.getType(), baseDao.getDrz(), baseDao.isVideoFlag());
    }

    @ApiOperation(value = "查询单站水库时间列表", notes = "根据测站编码水库时段列表 用于过程线")
    @GetMapping(value = "select-tm-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<StRsvrTmVo>>> getTmListByStcd(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<StRsvrTmVo> list = baseService.getRsvrByStcdAndTm(stcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询测站对应水库集水面积范围内测站的平均雨量", notes = "查询测站对应水库集水面积范围内测站的平均雨量")
    @GetMapping(value = "select-rsvr-avg-drna-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrAvgDrnaVo>>> getRsvrAvgDrnaByStcd(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<RsvrAvgDrnaVo> list = baseService.getRsvrAvgDrnaByStcd(stcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出水库水情预报实时数据", notes = "赵英捷")
    @GetMapping(value = "export-tm-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public void exportTmList(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        baseService.exportTmList(stcd, stm, etm);
    }

    @ApiOperation(value = "查询单站库（湖）容曲线表的施策时间列表", notes = "根据测站编码查询 库（湖）容曲线表施策时间")
    @GetMapping(value = "select-stzvarlb-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<String>>> getStZvarLbByStcd(@RequestParam String stcd) throws Exception {
        List<String> list = baseService.getStZvarLbByStcd(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }


    @ApiOperation(value = "查询 库（湖）容曲线表", notes = "根据测站编码以及施策时间 查询 库（湖）容曲线表")
    @GetMapping(value = "select-stzvarlb-by-stcd-mstm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mstm", value = "施策时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")

    })
    public ResponseEntity<SuccessResponse<List<StZvarlB>>> getStZvarLbByStcdAndMstm(@RequestParam String stcd, @RequestParam String mstm) throws Exception {
        List<StZvarlB> list = baseService.getStZvarLbByStcdAndMstm(stcd, mstm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询单站库（湖）防洪指标", notes = "根据测站编码查询 库（湖）防洪指标")
    @GetMapping(value = "select-fhzb-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<StRsvrfcchB>> getStRsvrfcchbByStcd(@RequestParam String stcd) throws Exception {
        StRsvrfcchB result = baseService.getStRsvrfcchbByStcd(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "查询单站水库汛限水位", notes = "根据测站编码查询水库汛限水位")
    @GetMapping(value = "select-xxsw-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<StRsvrfsrB>> getStRsvrfsrbByStcd(@RequestParam String stcd) throws Exception {
        List<StRsvrfsrB> result = baseService.getStRsvrfsrbByStcd(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "多站水库水情预报", notes = "多站水库水情预报")
    @PostMapping(value = "select-StReglat-by-info")
    public ResponseEntity<SuccessResponse<IPage<StReglatfSqyb>>> getStReglaTbyInfo(@RequestBody StReglatfSqybQuery baseDao) throws Exception {
        IPage<StReglatfSqyb> list = baseService.getStReglaTbyInfo(
                baseDao.getAdcd(), baseDao.getWnstatus(), baseDao.getStnm(), baseDao.getStm(), baseDao.getEtm(),baseDao.getStcds(), baseDao.getPageNum(), baseDao.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "水库日旬月均值查询", notes = "分页查询一段时间内水库日旬月均值")
    @PostMapping(value = "select-day-avg-rsvr-by-page")
    public ResponseEntity<SuccessResponse<Page<RsvrDayAvg>>> getDayAvgRsvrByPage(@RequestBody QueryRsvrDayAvg rsvrDayAvg) throws Exception {
        IPage<RsvrDayAvg> list = baseService.getDayAvgRsvrByPage(rsvrDayAvg.getStm(), rsvrDayAvg.getEtm(), rsvrDayAvg.getStList(), rsvrDayAvg.getTmType(), rsvrDayAvg.getPageNum(), rsvrDayAvg.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "水库日旬月均值导出", notes = "导出一段时间内水库日旬月均值")
    @PostMapping(value = "export-day-avg-rsvr-by-page")
    public void ExportDayAvgRsvrByPage(@RequestBody QueryRsvrDayAvg rsvrDayAvg) throws Exception {
        baseService.ExportDayAvgRsvrByPage(rsvrDayAvg.getStm(), rsvrDayAvg.getEtm(), rsvrDayAvg.getStList(), rsvrDayAvg.getTmType());
    }

    @ApiOperation(value = "单站水库水情预报", notes = "单站水库水情预报")
    @GetMapping(value = "select-streglat-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "ymdh", value = "预报开始时间（发生时间）", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<StReglatfSqybd>>> getStReglaTbyAdcd(@RequestParam String stcd, @RequestParam String ymdh) throws Exception {
        List<StReglatfSqybd> list = baseService.getStReglaTbyAdcd(stcd, ymdh);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出水库水情预报数据", notes = "赵英捷")
    @GetMapping(value = "export-streglat-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "ymdh", value = "预报开始时间（发生时间）", required = true, dataType = "String")
    })
    public void exportTmList(@RequestParam String stcd, @RequestParam String ymdh) throws Exception {
        baseService.exportStreglatList(stcd, ymdh);
    }

    @ApiOperation(value = "查询最新水库水情水库类型统计信息(分页参数pageSize传-1查询所有的统计)", notes = "根据选择条件查询一段时间范围内的最新水库水情的水库类型统计信息")
    @PostMapping(value = "select-rsvr-count-info")
    public ResponseEntity<SuccessResponse<RsvrTypeCount>> getStRsvrCountInfo(@RequestBody RsvrQuery baseDao) throws Exception {

        RsvrTypeCount rsvrTypeCount = baseService.getStRsvrCountInfo(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm(), baseDao.getStnm(),
                baseDao.getBsnm(), baseDao.getStType(), baseDao.getRvType(), baseDao.isDataFlag(), baseDao.isWarnFlag(), baseDao.getPageNum(), baseDao.getPageSize(), baseDao.getIsOut());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", rsvrTypeCount));
    }

    @ApiOperation(value = "水库最大超汛限水位统计", notes = "查看所有水库站时间范围内最大的超汛限水位时间及相关特征值。")
    @GetMapping(value = "select-strsvr-maxrzfsltdz")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stTypes", value = "测站归属类型  1、水文，2、山洪  3、气象 多个值以逗号隔开", dataType = "String"),
            @ApiImplicitParam(name = "rvTypes", value = "水库规模集合(1:大一 2：大二  3：中 4：小一  5：小二) 多个值以逗号隔开", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<StRsvrVoOfMaxRzfsltdz>> getStRsvrVoMaxRzfsltdz(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String stTypes, @RequestParam String rvTypes) throws Exception {
        StRsvrVoOfMaxRzfsltdz param = baseService.getStRsvrVoMaxRzfsltdz(adcd, stm, etm, stTypes, rvTypes);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", param));
    }

    @ApiOperation(value = "导出水库最大超汛限水位统计", notes = "导出所有水库站时间范围内最大的超汛限水位时间及相关特征值。")
    @GetMapping(value = "export-strsvr-maxrzfsltdz")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stTypes", value = "测站归属类型  1、水文，2、山洪  3、气象 多个值以逗号隔开", dataType = "String"),
            @ApiImplicitParam(name = "rvTypes", value = "水库规模集合(1:大一 2：大二  3：中 4：小一  5：小二) 多个值以逗号隔开", dataType = "String")
    })
    public void exportStRsvrVoMaxRzfsltdz(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String stTypes, @RequestParam String rvTypes) throws Exception {
        baseService.exportStRsvrVoMaxRzfsltdz(adcd, stm, etm, stTypes, rvTypes);

    }

    @ApiOperation(value = "值班报告水库信息", notes = "值班报告水库信息")
    @GetMapping(value = "select-rsvtinfo-duty")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码 15位", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<RsvtInfoByDuty>> getStRsvrVoVyDuty(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {

        RsvtInfoByDuty param = baseService.getStRsvrVoVyDuty(adcd, stm, etm, "");
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", param));
    }

    @ApiOperation(value = "根据测站字符串查询小水库风险图和调查评价数据", notes = "根据测站字符串查询小水库风险图和调查评价数据")
    @GetMapping(value = "get-objOnlyIACAdinfo-by-stcd")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ObjOnlyIACAdinfo>>> getObjOnlyIACAdinfoByStcd(@RequestParam String stcd) throws Exception {
        List<ObjOnlyIACAdinfo> list = baseService.getObjOnlyIACAdinfoByStcd(stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "水库水情查看", notes = "水库水情查看")
    @GetMapping(value = "select-eservoir-list-page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bgtm", value = "开始时间", dataType = "String"),
            @ApiImplicitParam(name = "endtm", value = "结束时间", dataType = "String"),
            @ApiImplicitParam(name = "addvcd", value = "政区编码", dataType = "String"),
            @ApiImplicitParam(name = "stnm", value = "水库名称", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ReservoirWaterinfo>>> getReservoirWaterRegimePage(@RequestParam String bgtm, @RequestParam String endtm, @RequestParam String addvcd, String stnm) throws Exception {
        List<ReservoirWaterinfo> list = baseService.getReservoirWaterRegimePage(bgtm, endtm, addvcd, stnm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "水库水情查看照片", notes = "水库水情查看照片")
    @GetMapping(value = "select-eservoir-list-photo")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tm", value = "时间", dataType = "String"),
            @ApiImplicitParam(name = "stcd", value = "测站编码", dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<ReservoirWaterinfo>>> getReservoirWaterRegimePhoto(@RequestParam String tm, @RequestParam String stcd) throws Exception {
        List<ReservoirWaterinfo> list = baseService.getReservoirWaterRegimePhoto(tm, stcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }


    @ApiOperation(value = "水库水情查看照片对比", notes = "水库水情查看照片对比")
    @GetMapping(value = "select-eservoir-list-comparison")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间", dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间", dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<BsnRsvrrecorderfileR>>> getPhotoComparison(@RequestParam String stcd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        if (StringUtils.isBlank(stcd)) {
            throw new IllegalArgumentException("测站不能为空");
        }
        if (StringUtils.isBlank(stm)) {
            throw new IllegalArgumentException("开始时间不能为空");
        }
        if (StringUtils.isBlank(etm)) {
            throw new IllegalArgumentException("结束时间不能为空");
        }
        List<BsnRsvrrecorderfileR> list = baseService.getPhotoComparison(stcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }


    @ApiOperation(value = "水库最大连续超汛限天数统计", notes = "水库最大连续超汛限次数统计（当天24小时超汛限记为一天）")
    @GetMapping(value = "continue-max-alarm-days")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码 15位", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stTypes", value = "数据来源（1=水文、2=山洪、4=人工报讯小型水库、5=运管人工、6=运管自动）多个值以英文逗号隔开", dataType = "String"),
            @ApiImplicitParam(name = "rvTypes", value = "水库规模（1=小(二)型、2=小(一)型、3=中型、4=大(二)型、5=大(一)型）多个值以英文逗号隔开", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrContinueAlermVo>>> getRsvrContinueMaxAlermDays(@RequestParam String adcd, @RequestParam String stm, @RequestParam String etm, @RequestParam String stTypes, @RequestParam String rvTypes) throws Exception {
        List<RsvrContinueAlermVo> list = baseService.getRsvrContinueAlerm(adcd, stm, etm, stTypes, rvTypes);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出水库最大连续超汛限天数统计", notes = "作者：赵英捷")
    @PostMapping(value = "export-continue-max-alarm-days")
    public void exportRsvrContinueMaxAlermDays(@Validated @RequestBody RsvrContinueAlermQuery query, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("水库连续最大超汛限统计", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.exportRsvrContinueMaxAlermDays(query, response.getOutputStream());
        } catch (Exception ex) {
            logger.error("水库连续最大超汛限统计导出失败，参数：{}", JSON.toJSONString(query));
            logger.error(ex.getMessage(), ex);
        }
    }

    @ApiOperation(value = "统计政区下时间段内测站超警戒情况", notes = "统计政区下时间段内测站超警戒情况")
    @GetMapping(value = "select-rsvr-warn-statistics-by-adcd-tm")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrWarnByAdcdTmVo>>> selectWarnStatisticsByAdcdTm(@SqlInjection @RequestParam String adcd, @RequestParam String stm, @RequestParam String etm) throws Exception {

        List<RsvrWarnByAdcdTmVo> list = baseService.selectWarnStatisticsByAdcdTm(adcd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "多站水库水情最新预报", notes = "多站水库水情最新预报")
    @GetMapping(value = "select-forecast-Latest-time")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bscd", value = "流域", required = false, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<StReglatfSqyb>>> getForecastLatestTime(@SqlInjection @RequestParam String adcd, @RequestParam String bscd, @RequestParam String stm, @RequestParam String etm) throws Exception {
        List<StReglatfSqyb> list = baseService.getForecastLatestTime(adcd, bscd, stm, etm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "纳雨能力分析", notes = "纳雨能力分析")
    @GetMapping(value = "select-contain-rain-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resCode", value = "水库工程编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ReservoirRain>>> getContainRainList(@SqlInjection @RequestParam String resCode) throws Exception {
        List<ReservoirRain> list = baseService.getContainRainList(resCode);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "水库集水面积内测站降雨列表", notes = "水库集水面积内测站降雨列表")
    @GetMapping(value = "select-rsvr-rain-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "basCode", value = "流域编码", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stTypes", value = "数据来源（1、水文，2、山洪 3、气象 4、人工报讯小型水库 5、运管人工、6、运管自动）多个值以英文逗号隔开", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrRainList>>> getStRsvrRainList(@RequestParam String basCode, @RequestParam String stm, @RequestParam String etm, @RequestParam String stTypes) throws Exception {

        List<RsvrRainList> param = baseService.getStRsvrRainList(basCode, stm, etm, stTypes);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", param));
    }

    @ApiOperation(value = "水库降雨展开查询雨量站", notes = "zyj")
    @GetMapping(value = "select-rsvr-rain-child-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resCode", value = "水库编码", dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 格式yyyy-MM-dd HH:mm:ss", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stTypes", value = "数据来源（1、水文，2、山洪 3、气象 4、人工报讯小型水库 5、运管人工、6、运管自动）多个值以英文逗号隔开", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<RsvrRainChildList>>> getRsvrRainChildList(@RequestParam String resCode, @RequestParam String stm, @RequestParam String etm, @RequestParam String stTypes) throws Exception {

        List<RsvrRainChildList> param = baseService.getRsvrRainChildList(resCode, stm, etm, stTypes);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", param));
    }

    @ApiOperation(value = "水库集水面积内测站降雨列表导出", notes = "水库集水面积内测站降雨列表导出")
    @PostMapping(value = "export-rsvr-rain-list")
    public void exportStRsvrRainList(@RequestBody ExportRsvr baseDao) throws Exception {
        baseService.exportStRsvrRainList(baseDao.getAdcd(), baseDao.getStm(), baseDao.getEtm(), baseDao.getStType());
    }


    @ApiOperation(value = "临界告警", notes = "作者：赵英捷")
    @PostMapping("select-near-warn-temp")
    public ResponseEntity<SuccessResponse<List<RsvrNearWarnVo>>> selectNearWarnTemp(@Validated @RequestBody RsvrNearWarnQuery query) {
        List<RsvrNearWarnVo> list = baseService.selectNearWarnTemp(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "防洪作战图批量查询水库水位", notes = "作者：zyj")
    @PostMapping("select-rsvr-by-stcds")
    public ResponseEntity<SuccessResponse<Page<RsvrZuoZhanTuVo>>> getRsvrZuoZhanTuList(@Validated @RequestBody RsvrZuoZhanTuQuery query) {
        IPage<RsvrZuoZhanTuVo> page = baseService.getRsvrZuoZhanTuList(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", page));
    }

    @ApiOperation(value = "防洪作战图根据水库测站编码查询水库信息", notes = "作者：赵英捷")
    @PostMapping("select-rsvr-for-zzt-by-stcd")
    public ResponseEntity<SuccessResponse<RsvrZuoZhanTuVo>> getRsvrZuoZhanTu(@Validated @RequestBody RsvrZuoZhanTuQuery query) {
        RsvrZuoZhanTuVo data = baseService.getRsvrZuoZhanTu(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", data));
    }
}







