package com.huitu.cloud.api.ewci.hydropower.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.hydropower.entity.BsnShpwrR;
import com.huitu.cloud.api.ewci.hydropower.entity.ShpwrRVo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-08
 */
public interface BsnShpwrRDao extends BaseMapper<BsnShpwrR> {

    /**
     * 查询所有小水电生态放流监测数据
     *
     * @param param
     * @return
     */
    IPage<ShpwrRVo> selectByPage(Page page, @Param("map") Map<String, Object> param);
}
