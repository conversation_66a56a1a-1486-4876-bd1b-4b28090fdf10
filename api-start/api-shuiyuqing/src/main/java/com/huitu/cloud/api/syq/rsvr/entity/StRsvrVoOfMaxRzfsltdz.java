package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2019-10-09
 */
public class StRsvrVoOfMaxRzfsltdz {
    @ApiModelProperty(value = "小型水库数量")
    @TableField("SMALLNUM")
    private Integer smallNum;
    @ApiModelProperty(value = "中型水库数量")
    @TableField("MIDDLENUM")
    private Integer middleNum;
    @ApiModelProperty(value = "小型水库数量")
    @TableField("BIGNUM")
    private Integer bigNum;
    @ApiModelProperty(value = "返回数据集合")
    @TableField("STRSVRVOLIST")
    private List<StRsvrMax> strsvrvolist;

    public Integer getSmallNum() {
        return smallNum;
    }

    public void setSmallNum(Integer smallNum) {
        this.smallNum = smallNum;
    }

    public Integer getMiddleNum() {
        return middleNum;
    }

    public void setMiddleNum(Integer middleNum) {
        this.middleNum = middleNum;
    }

    public Integer getBigNum() {
        return bigNum;
    }

    public void setBigNum(Integer bigNum) {
        this.bigNum = bigNum;
    }

    public List<StRsvrMax> getStrsvrvolist() {
        return strsvrvolist;
    }

    public void setStrsvrvolist(List<StRsvrMax> strsvrvolist) {
        this.strsvrvolist = strsvrvolist;
    }
}
