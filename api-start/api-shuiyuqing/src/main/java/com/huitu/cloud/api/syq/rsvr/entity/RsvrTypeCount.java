package com.huitu.cloud.api.syq.rsvr.entity;

import io.swagger.annotations.ApiModelProperty;
/**
 * <AUTHOR>
 */
public class RsvrTypeCount {
    @ApiModelProperty(value = "水库总数")
    private int rsvrCount;
    @ApiModelProperty(value = "大型水库总数")
    private int bigRsvrCount;
    @ApiModelProperty(value = "中型水库总数")
    private int middleRsvrCount;
    @ApiModelProperty(value = "小一型水库总数")
    private int smallRsvrCount1;
    @ApiModelProperty(value = "小二型水库总数")
    private int smallRsvrCount2;

    public int getRsvrCount() {
        return rsvrCount;
    }

    public void setRsvrCount(int rsvrCount) {
        this.rsvrCount = rsvrCount;
    }

    public int getBigRsvrCount() {
        return bigRsvrCount;
    }

    public void setBigRsvrCount(int bigRsvrCount) {
        this.bigRsvrCount = bigRsvrCount;
    }

    public int getMiddleRsvrCount() {
        return middleRsvrCount;
    }

    public void setMiddleRsvrCount(int middleRsvrCount) {
        this.middleRsvrCount = middleRsvrCount;
    }

    public int getSmallRsvrCount1() {
        return smallRsvrCount1;
    }

    public void setSmallRsvrCount1(int smallRsvrCount1) {
        this.smallRsvrCount1 = smallRsvrCount1;
    }

    public int getSmallRsvrCount2() {
        return smallRsvrCount2;
    }

    public void setSmallRsvrCount2(int smallRsvrCount2) {
        this.smallRsvrCount2 = smallRsvrCount2;
    }
}
