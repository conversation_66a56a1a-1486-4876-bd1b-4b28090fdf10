<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.fusion.mapper.FusionStatisticsDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getShWarningSummary" resultType="com.huitu.cloud.api.fusion.entity.ext.FusionWarningSummary">
        SELECT
        COUNT(DISTINCT A.WARN_ID) WCOUNT,
        COUNT(DISTINCT D.PHONE_NO) RCOUNT,
        COUNT(DISTINCT D.CALL_ID) PCOUNT,
        COUNT(DISTINCT (CASE WHEN D.[STATUS] = '1' THEN D.CALL_ID ELSE NULL END)) FCOUNT
        FROM
        EW_FUSION_WARNING_RECORD A WITH (NOLOCK)
        LEFT JOIN EW_FUSION_REL_MESSAGE B WITH (NOLOCK) ON B.WARN_ID = A.WARN_ID
        LEFT JOIN EW_FUSION_MESSAGE C WITH (NOLOCK) ON C.MSG_ID = B.MSG_ID
        AND C.PUSH_MODE = '1'
        LEFT JOIN EW_CALL_FEEDBACK D WITH (NOLOCK) ON D.SMS_WARN_MSG_ID = C.MSG_ID
        WHERE
        A.WARN_STATUS_ID IN (0, 10, 20, 21, 30) AND A.WARN_TIME >= '${map.stm}'
        <if test="map.etm != null">AND A.WARN_TIME &lt;= '${map.etm}'</if>
        AND LEFT(A.ADCD, ${map.level}) = LEFT('${map.adcd}', ${map.level})
        <if test="map.level == '4'.toString()">AND LEFT(A.ADCD, 6) NOT IN ('220581')</if>
    </select>

    <select id="getRsvrWarningSummary" resultType="com.huitu.cloud.api.fusion.entity.ext.FusionWarningSummary">
        SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT C.PHONE_NO) RCOUNT, COUNT(DISTINCT C.CALL_ID) PCOUNT,
        COUNT(DISTINCT (CASE WHEN C.[STATUS] = '1' THEN C.CALL_ID ELSE NULL END)) FCOUNT
        FROM EW_RSVR_WARNING_RECORD A WITH(NOLOCK)
        LEFT JOIN EW_RSVR_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID AND B.PUSH_MODE = '1'
        LEFT JOIN EW_CALL_FEEDBACK C WITH(NOLOCK) ON C.SMS_WARN_MSG_ID = B.MSG_ID
        WHERE CHARINDEX(A.[STATE], '0,1,2') > 0 AND A.WARN_TIME >= '${map.stm}'
        <if test="map.etm != null">AND A.WARN_TIME &lt;= '${map.etm}'</if>
        AND EXISTS(SELECT 8 FROM BSN_STADTP_B WITH(NOLOCK) WHERE STADTP IN ('1', '2', '4')
        AND LEFT(ADCD, ${map.level}) = LEFT('${map.adcd}', ${map.level}) AND STCD = A.STCD
        <if test="map.level == '4'.toString()">AND LEFT(ADCD, 6) NOT IN ('220581')</if>)
        AND EXISTS(SELECT 8 FROM ATT_RES_BASE WITH(NOLOCK) WHERE ENG_SCAL IN ('1', '2', '3', '4', '5') AND RES_CODE = A.RES_CODE)
    </select>

    <select id="getRiverWarningSummary" resultType="com.huitu.cloud.api.fusion.entity.ext.FusionWarningSummary">
        SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT C.PHONE_NO) RCOUNT, COUNT(DISTINCT C.CALL_ID) PCOUNT,
        COUNT(DISTINCT (CASE WHEN C.[STATUS] = '1' THEN C.CALL_ID ELSE NULL END)) FCOUNT
        FROM EW_RIVER_WARNING_RECORD A WITH(NOLOCK)
        LEFT JOIN EW_RIVER_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID AND B.PUSH_MODE = '1'
        LEFT JOIN EW_CALL_FEEDBACK C WITH(NOLOCK) ON C.SMS_WARN_MSG_ID = B.MSG_ID
        WHERE CHARINDEX(A.[STATE], '0,1,2') > 0 AND A.WARN_TIME >= '${map.stm}'
        <if test="map.etm != null">AND A.WARN_TIME &lt;= '${map.etm}'</if>
        AND EXISTS(SELECT 8 FROM BSN_STADTP_B WITH(NOLOCK) WHERE STADTP IN ('1', '2')
        AND LEFT(ADCD, ${map.level}) = LEFT('${map.adcd}', ${map.level}) AND STCD = A.STCD
        <if test="map.level == '4'.toString()">AND LEFT(ADCD, 6) NOT IN ('220581')</if>)
    </select>

    <select id="getAdWarnMessageStatistics"
            resultType="com.huitu.cloud.api.fusion.entity.response.FusionWarnMessageStatisticsResp">
        SELECT AD.ADCD,AD.ADNM,AD.PADCD, COUNT(DISTINCT A.WARNID) allTotal, COUNT(DISTINCT CALL_ID) totalMassage
        FROM (SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_FUSION_WARNING_RECORD
        WHERE WARN_STATUS_ID IN (0, 10, 20, 21, 30)) A
        LEFT JOIN (SELECT WARN_ID WARNID, CALL_ID FROM EW_CALL_FEEDBACK S, EW_FUSION_MESSAGE I, EW_FUSION_REL_MESSAGE R
        WHERE S.SMS_WARN_MSG_ID = I.MSG_ID AND I.MSG_ID = R.MSG_ID AND S.CALL_TYPE = '1' AND I.PUSH_MODE = '1') C ON
        C.WARNID = A.WARNID
        LEFT JOIN BSN_ADCD_B AD ON SUBSTRING(A.ADCD, 1, ${map.level2}) + '${map.zero}' = AD.ADCD
        WHERE A.WTM >= '${map.stm}'
        <if test="map.etm != null">
            AND A.WTM &lt;= '${map.etm}'
        </if>
        <if test="map.ad != null and map.ad !=''">
            AND SUBSTRING(A.ADCD, 1, ${map.level}) = '${map.ad}'
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY AD.ADCD, AD.ADNM, AD.PADCD
    </select>
</mapper>