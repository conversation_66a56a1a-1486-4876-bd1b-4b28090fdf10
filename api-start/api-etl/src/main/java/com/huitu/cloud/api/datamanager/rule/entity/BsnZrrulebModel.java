package com.huitu.cloud.api.datamanager.rule.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

@ColumnWidth(15)
public class BsnZrrulebModel {
    @ExcelProperty("测站编码")
    private String stcd;
    @ExcelProperty("测站名称")
    private String stnm;
    @ExcelProperty(value = "水位上限")
    private Double upv;
    @ExcelProperty(value = "水位下限")
    private Double dwv;
    @ExcelProperty(value = "变幅")
    private Double chv;
    @ExcelProperty(value = "备注")
    private String nt;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public Double getUpv() {
        return upv;
    }

    public void setUpv(Double upv) {
        this.upv = upv;
    }

    public Double getDwv() {
        return dwv;
    }

    public void setDwv(Double dwv) {
        this.dwv = dwv;
    }

    public Double getChv() {
        return chv;
    }

    public void setChv(Double chv) {
        this.chv = chv;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }
}
