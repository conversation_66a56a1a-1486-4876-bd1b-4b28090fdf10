package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 异常数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-07
 */
@TableName("BSN_ANMRECORD_R")
@ApiModel(value="BsnAnmrecordR对象", description="异常数据表")
public class BsnAnmrecordr extends Model<BsnAnmrecordr> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "记录编码 主键")
    @TableId(value = "REID", type = IdType.AUTO)
    private String reid;

    @ApiModelProperty(value = "产生时间")
    @TableField("TM")
    private Date tm;

    @ApiModelProperty(value = "数据源代码")
    @TableField("nocd")
    private String nocd;

    @ApiModelProperty(value = "数据表名")
    @TableField("TBNM")
    private String tbnm;

    @ApiModelProperty(value = "记录json")
    @TableField("DATAJS")
    private String datajs;

    @ApiModelProperty(value = "异常类型")
    @TableField("ANMTP")
    private String anmtp;

    @ApiModelProperty(value = "数据状态 0，终审审批通过且入库；1，终审审批通过入库失败；2终审审批未通过；3,初审通过；4，初审未通过； 5，已修改；6，新入异常表；9，不需要修改")
    @TableField("STAFLG")
    private String staflg;

    @ApiModelProperty(value = "修改人")
    @TableField("UPUSER")
    private String upuser;

    @ApiModelProperty(value = "修改时间")
    @TableField("UPTM")
    private Date uptm;

    @ApiModelProperty(value = "初审审批人")
    @TableField("FADUSER")
    private String faduser;

    @ApiModelProperty(value = "初审审批时间")
    @TableField("FADTM")
    private Date fadtm;

    @ApiModelProperty(value = "终审审批人")
    @TableField("RADUSER")
    private String raduser;

    @ApiModelProperty(value = "终审审批时间")
    @TableField("RADTM")
    private Date radtm;

    @ApiModelProperty(value = "备注")
    @TableField("NT")
    private String nt;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private Date moditime;

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String  stcd;

    @ApiModelProperty(value = "异常描述")
    @TableField("REDESC")
    private String  redesc;

    public String getRedesc() {
        return redesc;
    }

    public void setRedesc(String redesc) {
        this.redesc = redesc;
    }

    public String getReid() {
        return reid;
    }

    public void setReid(String reid) {
        this.reid = reid;
    }

    public Date getTm() {
        return tm;
    }

    public void setTm(Date tm) {
        this.tm = tm;
    }

    public String getNocd() {
        return nocd;
    }

    public void setNocd(String nocd) {
        this.nocd = nocd;
    }

    public String getTbnm() {
        return tbnm;
    }

    public void setTbnm(String tbnm) {
        this.tbnm = tbnm;
    }

    public String getDatajs() {
        return datajs;
    }

    public void setDatajs(String datajs) {
        this.datajs = datajs;
    }

    public String getAnmtp() {
        return anmtp;
    }

    public void setAnmtp(String anmtp) {
        this.anmtp = anmtp;
    }

    public String getStaflg() {
        return staflg;
    }

    public void setStaflg(String staflg) {
        this.staflg = staflg;
    }

    public String getUpuser() {
        return upuser;
    }

    public void setUpuser(String upuser) {
        this.upuser = upuser;
    }

    public Date getUptm() {
        return uptm;
    }

    public void setUptm(Date uptm) {
        this.uptm = uptm;
    }

    public String getFaduser() {
        return faduser;
    }

    public void setFaduser(String faduser) {
        this.faduser = faduser;
    }

    public Date getFadtm() {
        return fadtm;
    }

    public void setFadtm(Date fadtm) {
        this.fadtm = fadtm;
    }

    public String getRaduser() {
        return raduser;
    }

    public void setRaduser(String raduser) {
        this.raduser = raduser;
    }

    public Date getRadtm() {
        return radtm;
    }

    public void setRadtm(Date radtm) {
        this.radtm = radtm;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }

    public Date getModitime() {
        return moditime;
    }

    public void setModitime(Date moditime) {
        this.moditime = moditime;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    @Override
    protected Serializable pkVal() {
        return this.reid;
    }

    @Override
    public String toString() {
        return "BsnAnmrecordR{" +
        "reid=" + reid +
        ", tm=" + tm +
        ", nocd=" + nocd +
        ", tbnm=" + tbnm +
        ", datajs=" + datajs +
        ", anmtp=" + anmtp +
        ", staflg=" + staflg +
        ", upuser=" + upuser +
        ", uptm=" + uptm +
        ", faduser=" + faduser +
        ", fadtm=" + fadtm +
        ", raduser=" + raduser +
        ", radtm=" + radtm +
        ", nt=" + nt +
        ", moditime=" + moditime +
         ", stcd=" + stcd +
        "}";
    }

}
