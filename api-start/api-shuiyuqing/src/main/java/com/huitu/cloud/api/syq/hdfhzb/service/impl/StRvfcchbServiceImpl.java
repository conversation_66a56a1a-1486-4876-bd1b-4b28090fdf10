package com.huitu.cloud.api.syq.hdfhzb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.syq.hdfhzb.entity.StRvfcchB;
import com.huitu.cloud.api.syq.hdfhzb.mapper.StRvfcchbDao;
import com.huitu.cloud.api.syq.hdfhzb.service.StRvfcchbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 河道站防洪指标表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
@Service
public class StRvfcchbServiceImpl extends ServiceImpl<StRvfcchbDao, StRvfcchB> implements StRvfcchbService {

    @Autowired
    private StRvfcchbDao baseDao;

    @Override
    public StRvfcchB getByStcd(String stcd) {
        return baseDao.getByStcd(stcd);
    }
}
