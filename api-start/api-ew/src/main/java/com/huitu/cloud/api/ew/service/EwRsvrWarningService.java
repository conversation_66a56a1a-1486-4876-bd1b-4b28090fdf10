package com.huitu.cloud.api.ew.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.ext.EwRsvrWarningSending;
import com.huitu.cloud.api.ew.entity.request.EwRsvrWarningCorrectRequest;
import com.huitu.cloud.api.ew.entity.request.EwRsvrWarningRecordRequest;
import com.huitu.cloud.api.ew.entity.request.EwRsvrWarningSendingRequest;
import com.huitu.cloud.api.ew.entity.response.EwRsvrWarningRecordResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarningGradeSummaryResponse;
import com.huitu.cloud.api.usif.user.entity.UserInfos;

import java.math.BigDecimal;
import java.util.List;

/**
 * 水库预警服务
 */
public interface EwRsvrWarningService {

    /**
     * 获取预警分页列表
     *
     * @param request 请求参数
     * @return 预警分页列表
     **/
    IPage<EwRsvrWarningRecordResponse> getPageList(EwRsvrWarningRecordRequest request);

    /**
     * 根据预警ID，获取预警记录
     *
     * @param warnId 预警ID
     * @return 预警记录
     */
    EwRsvrWarningRecordResponse getRecord(String warnId);

    /**
     * 获取预警发送记录分页列表
     *
     * @param request 请求参数
     * @return 发送记录分页列表
     */
    IPage<EwRsvrWarningSending> getSendingPageList(EwRsvrWarningSendingRequest<EwRsvrWarningSending> request);

    /**
     * 获取预警标识
     *
     * @param msgId 消息ID
     * @return 预警标识
     */
    String getWarningFlag(String msgId);

    /**
     * 水库预警等级统计
     *
     * @param request
     * @return
     */
    List<EwWarningGradeSummaryResponse> getRsvrWarningGradeSummary(EwRsvrWarningRecordRequest request);

    /**
     * 获取测站最新水位
     *
     * @param stcd 测站编码
     * @return 最新水位
     */
    BigDecimal getLatestRz(String stcd);

    /**
     * 更正预警
     *
     * @param request 请求参数
     * @param user    用户信息
     * @return 受影响的行数
     */
    int correct(EwRsvrWarningCorrectRequest request, UserInfos user);
}
