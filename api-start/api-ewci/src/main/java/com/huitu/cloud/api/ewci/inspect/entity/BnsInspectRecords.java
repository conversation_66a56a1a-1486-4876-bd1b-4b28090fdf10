package com.huitu.cloud.api.ewci.inspect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;

import java.io.Serializable;
import java.util.Date;

/**
 * 检查记录
 */
@ApiModel(value = "检查记录")
public class BnsInspectRecords implements Serializable {

    @ApiModelProperty(value = "组别id")
    @TableField(value = "GROUPID")
    private String groupid;

    @ApiModelProperty(value = "组别名称")
    @TableField(value = "GROUPNM")
    private String groupnm;

    @ApiModelProperty(value = "政区编码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "年份")
    @TableField(value = "YEAR")
    private String year;

    @ApiModelProperty(value = "数量")
    @TableField(value = "NUM")
    private Integer num;

    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    public String getGroupnm() {
        return groupnm;
    }

    public void setGroupnm(String groupnm) {
        this.groupnm = groupnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }
}
