package com.huitu.cloud.api.ewci.hydfcst.entity;


import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Map;

/**
 * 返回总 Map 包装实体
 */
public class StResultMap implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "时间")
    String tm;

    @ApiModelProperty(value = "河道Map")
    Map<String, StRV> rvMap;

    @ApiModelProperty(value = "水库Map")
    Map<String, StRS> rsMap;

    public StResultMap(String tm, Map<String, StRV> rvMap, Map<String, StRS> rsMap) {
        this.tm = tm;
        this.rvMap = rvMap;
        this.rsMap = rsMap;
    }

    public String getTm() {
        return tm;
    }

    public void setTm(String tm) {
        this.tm = tm;
    }

    public Map<String, StRV> getRvMap() {
        return rvMap;
    }

    public void setRvMap(Map<String, StRV> rvMap) {
        this.rvMap = rvMap;
    }

    public Map<String, StRS> getRsMap() {
        return rsMap;
    }

    public void setRsMap(Map<String, StRS> rsMap) {
        this.rsMap = rsMap;
    }

    @Override
    public String toString() {
        return "StResultMap{" +
                "tm='" + tm + '\'' +
                ", rvMap=" + rvMap +
                ", rsMap=" + rsMap +
                '}';
    }
}