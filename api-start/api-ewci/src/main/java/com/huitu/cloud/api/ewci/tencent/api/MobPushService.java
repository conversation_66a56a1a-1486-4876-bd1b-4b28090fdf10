package com.huitu.cloud.api.ewci.tencent.api;

import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;
import com.huitu.cloud.api.ewci.tencent.entity.MobPushVO;

@BaseRequest(baseURL = "{saasServiceAddr}")
public interface MobPushService {

    /**
     * 发送Scoket消息， 调用 Saas 平台接口
     *
     * @param request
     * @return
     */
    @Post(url = "/api/app/push/appPush/createPush",
            contentType = "application/json",
            headers = {
                    "Token-Cloud: ${token}",
                    "Content-Type: application/json"
            })
    String sendMsg(@Body MobPushVO request, @Var("token") String token);
}
