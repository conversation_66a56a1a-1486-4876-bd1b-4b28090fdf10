package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 水库责任人
 *
 * <AUTHOR>
 */
@ApiModel(value = "水库责任人")
public class RsvrPerson implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水库名称")
    @TableField(value = "RES_NAME")
    private String resName;

    @ApiModelProperty(value = "水库工程规模")
    @TableField("ENG_SCAL")
    private String engScal;

    @ApiModelProperty(value = "安全度汛行政责任人-姓名")
    @TableField("AQDX_REALNM")
    private String aqdxRealnm;

    @ApiModelProperty(value = "安全度汛行政责任人-手机号码")
    @TableField("AQDX_MOBILE")
    private String aqdxMobile;

    @ApiModelProperty(value = "抢险技术责任人-姓名")
    @TableField("QXJS_REALNM")
    private String qxjsRealnm;

    @ApiModelProperty(value = "抢险技术责任人-手机号码")
    @TableField("QXJS_MOBILE")
    private String qxjsMobile;

    @ApiModelProperty(value = "主管部门责任人-姓名")
    @TableField("ZGBM_REALNM")
    private String zgbmRealnm;

    @ApiModelProperty(value = "主管部门责任人-手机号码")
    @TableField("ZGBM_MOBILE")
    private String zgbmMobile;

    @ApiModelProperty(value = "管理单位责任人-姓名")
    @TableField("GLDW_REALNM")
    private String gldwRealnm;

    @ApiModelProperty(value = "管理单位责任人-手机号码")
    @TableField("GLDW_MOBILE")
    private String gldwMobile;

    @ApiModelProperty(value = "巡查值守责任人-姓名")
    @TableField("XCZS_REALNM")
    private String xczsRealnm;

    @ApiModelProperty(value = "巡查值守责任人-手机号码")
    @TableField("XCZS_MOBILE")
    private String xczsMobile;

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getEngScal() {
        return engScal;
    }

    public void setEngScal(String engScal) {
        this.engScal = engScal;
    }

    public String getAqdxRealnm() {
        return aqdxRealnm;
    }

    public void setAqdxRealnm(String aqdxRealnm) {
        this.aqdxRealnm = aqdxRealnm;
    }

    public String getAqdxMobile() {
        return aqdxMobile;
    }

    public void setAqdxMobile(String aqdxMobile) {
        this.aqdxMobile = aqdxMobile;
    }

    public String getQxjsRealnm() {
        return qxjsRealnm;
    }

    public void setQxjsRealnm(String qxjsRealnm) {
        this.qxjsRealnm = qxjsRealnm;
    }

    public String getQxjsMobile() {
        return qxjsMobile;
    }

    public void setQxjsMobile(String qxjsMobile) {
        this.qxjsMobile = qxjsMobile;
    }

    public String getZgbmRealnm() {
        return zgbmRealnm;
    }

    public void setZgbmRealnm(String zgbmRealnm) {
        this.zgbmRealnm = zgbmRealnm;
    }

    public String getZgbmMobile() {
        return zgbmMobile;
    }

    public void setZgbmMobile(String zgbmMobile) {
        this.zgbmMobile = zgbmMobile;
    }

    public String getGldwRealnm() {
        return gldwRealnm;
    }

    public void setGldwRealnm(String gldwRealnm) {
        this.gldwRealnm = gldwRealnm;
    }

    public String getGldwMobile() {
        return gldwMobile;
    }

    public void setGldwMobile(String gldwMobile) {
        this.gldwMobile = gldwMobile;
    }

    public String getXczsRealnm() {
        return xczsRealnm;
    }

    public void setXczsRealnm(String xczsRealnm) {
        this.xczsRealnm = xczsRealnm;
    }

    public String getXczsMobile() {
        return xczsMobile;
    }

    public void setXczsMobile(String xczsMobile) {
        this.xczsMobile = xczsMobile;
    }
}
