package com.huitu.cloud.api.shyj.evaluation.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import com.huitu.cloud.api.shyj.evaluation.mapper.EvaluationDao;
import com.huitu.cloud.api.shyj.evaluation.service.EvaluationService;
import com.huitu.cloud.constants.CommConstants;
import com.huitu.cloud.util.AdcdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 调查成果  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */

@Service
public class EvaluationServiceImpl implements EvaluationService {
    @Autowired
    private EvaluationDao evaluationDao;

    @Override
    public IPage<Wbrinfo> getWbrinfoList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("wscd", wscd);
        param.put("wsnm", wsnm);
        IPage<Wbrinfo> resultMap = evaluationDao.getWbrinfoList(page, param);
        return resultMap;
    }

    @Override
    public BsnWirelessinfo getWirelessInfo(String racd) {
        BsnWirelessinfo bsnWirelessinfo=evaluationDao.getWirelessInfo(racd);
        return bsnWirelessinfo;
    }

    @Override
    public IPage<BsnWirelessinfoLog> getWirelessLogInfo(String racd, String stm, String etm, String type, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        IPage<BsnWirelessinfoLog> list=evaluationDao.getWirelessLogInfo(page,racd,stm,etm,type);
        return list;
    }

    @Override
    public IPage<IaDanad> getDanList(String adcd, String key, String wscd, String wsnm, String name,String adnm,String prevtp,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("wscd", wscd);
        param.put("wsnm", wsnm);
        param.put("name", name);
        param.put("adnm", adnm);
        param.put("prevtp", prevtp);
        IPage<IaDanad> resultMap = evaluationDao.getDanList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaHsfwater> getHsfwaterList(String address,String adcd, String key, String wscd, String wsnm,String prevtp, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("address", address);
        param.put("wscd", wscd);
        param.put("wsnm", wsnm);
        param.put("prevtp", prevtp);
        IPage<IaHsfwater> resultMap = evaluationDao.getHsfwaterList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaGully> getGullyList(String adcd, String key, String name, String adnm,int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("name", name);
        param.put("adnm", adnm);
        IPage<IaGully> resultMap = evaluationDao.getGullyList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaDaminfo> getDamList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("wscd", wscd);
        param.put("wsnm", wsnm);
        IPage<IaDaminfo> resultMap = evaluationDao.getDamList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaFlrvvlg> getFlrvvlgList(String adcd, String key, String wscd,String name, String wsnm,String prevtp, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("wscd", wscd);
        param.put("name", name);
        param.put("wsnm", wsnm);
        param.put("prevtp", prevtp);
        IPage<IaFlrvvlg> resultMap = evaluationDao.getFlrvvlgList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaSrstinfo> getSrstList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("wscd", wscd);
        param.put("wsnm", wsnm);
        IPage<IaSrstinfo> resultMap = evaluationDao.getSrstList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaSwstinfo> getSwstList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("wscd", wscd);
        param.put("wsnm", wsnm);
        IPage<IaSwstinfo> resultMap = evaluationDao.getSwstList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaBsnssinfo> getBsnsstList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("wscd", wscd);
        param.put("wsnm", wsnm);
        IPage<IaBsnssinfo> resultMap = evaluationDao.getBsnsstList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaDtresident> getDtresidentList(String adcd, String key, String wscd, String wsnm, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        param.put("wscd", wscd);
        param.put("wsnm", wsnm);
        IPage<IaDtresident> resultMap = evaluationDao.getDtresidentList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaHsfm> getIaHsfmList(String adcd, String key, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        IPage<IaHsfm> resultMap = evaluationDao.getIaHsfmList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaHsurface> getHsurfaceList(String adcd, String key, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        IPage<IaHsurface> resultMap = evaluationDao.getHsurfaceList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaVsurface> getVsurfaceList(String adcd, String key, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("key", key);
        IPage<IaVsurface> resultMap = evaluationDao.getVsurfaceList(page, param);
        return resultMap;
    }

    @Override
    public IPage<IaAdinfo> getPrevadList(String adcd, String key, String adnm, String prevtp, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("adnm", adnm);
        param.put("key", key);
        param.put("prevtp", prevtp);
        IPage<IaAdinfo> resultMap = evaluationDao.getPrevadList(page, param);
        return resultMap;
    }

    @Override
    public List<DtAdInfo> getDtAdInfoList(String adcd) {
        Map<String, Object> param = setLevelZeroMap(adcd);
        List<DtAdInfo> resultList = evaluationDao.getDtAdInfoList(param);
        if ("2".equals(param.get("level").toString())){
            param.put("level2",6);
            param.put("zero","000000000");
            //当省统计市时，同时统计出县，然后把两个特殊县的数据取出来放入市的集合中
            List<DtAdInfo> resultList2 = evaluationDao.getDtAdInfoList(param);
            resultList2.forEach(item->{
                if (CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())||CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    resultList.add(item);
                }
            });
        }
        return resultList;
    }

    @Override
    public List<DtSurvey> getDtDccgInfoList(String adcd) {
        Map<String, Object> param = setLevelZeroMap(adcd);
        List<DtSurvey> resultList = evaluationDao.getDtDccgInfoList(param);
        if ("2".equals(param.get("level").toString())){
            param.put("level2",6);
            param.put("zero","000000000");
            //当省统计市时，同时统计出县，然后把两个特殊县的数据取出来放入市的集合中
            List<DtSurvey> resultList2 = evaluationDao.getDtDccgInfoList(param);
            resultList2.forEach(item->{
                if (CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())||CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    resultList.add(item);
                }
            });
        }
        return resultList;
    }

    @Override
    public List<DtWading> getDtWadingInfoList(String adcd) {
        Map<String, Object> param = setLevelZeroMap(adcd);
        List<DtWading> resultList = evaluationDao.getDtWadingInfoList(param);
        if ("2".equals(param.get("level").toString())){
            param.put("level2",6);
            param.put("zero","000000000");
            //当省统计市时，同时统计出县，然后把两个特殊县的数据取出来放入市的集合中
            List<DtWading> resultList2 = evaluationDao.getDtWadingInfoList(param);
            resultList2.forEach(item->{
                if (CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())||CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    resultList.add(item);
                }
            });
        }
        return resultList;
    }

    @Override
    public List<DtAnalysis> getDtFxpjInfoList(String adcd) {
        Map<String, Object> param = setLevelZeroMap(adcd);
        List<DtAnalysis> resultList = evaluationDao.getDtFxpjInfoList(param);
        if ("2".equals(param.get("level").toString())){
            param.put("level2",6);
            param.put("zero","000000000");
            //当省统计市时，同时统计出县，然后把两个特殊县的数据取出来放入市的集合中
            List<DtAnalysis> resultList2 = evaluationDao.getDtFxpjInfoList(param);
            resultList2.forEach(item->{
                if (CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())||CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    resultList.add(item);
                }
            });
        }
        return resultList;
    }

    @Override
    public DtAllEvaluationInfo getDtEvaluationInfo(String adcd) {
        DtAllEvaluationInfo result = null;
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        int adlvl = 0;
        //省级查询
        if (level == 2) {
            adlvl = 3;
            param.put("adlvl", adlvl);
            List<DtAllEvaluationInfo> list = evaluationDao.getAdInfo(param);
            List<DtAllEvaluationInfo> adList1 = new ArrayList<>();
            List<DtAllEvaluationInfo> adList2 = new ArrayList<>();
            List<DtAllEvaluationInfo> adList3 = new ArrayList<>();
            list.forEach(item -> {
                if ("1".equals(item.getAdlvl())) {
                    adList1.add(item);
                }
                if ("2".equals(item.getAdlvl())) {
                    adList2.add(item);
                }
                if ("3".equals(item.getAdlvl())) {
                    adList3.add(item);
                }
            });
            //省统计的时候把两个特殊县的统计放在市上
            List<DtAllEvaluationInfo> adList33=new ArrayList<>();
            adList3.forEach(item->{
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    adList33.add(item);
                }else{
                    adList2.add(item);
                }
            });
            param.put("level2", level);
            param.put("zero", adcd.substring(level));
            //行政区基本情况统计(省)
            List<DtAdInfo> prevcntList1 = evaluationDao.getDtAdInfoList(param);
            //调查成果统计（省）
            List<DtSurvey> surveyList1 = evaluationDao.getDtDccgInfoList(param);
            //涉水工程统计（省）
            List<DtWading> wadingList1 = evaluationDao.getDtWadingInfoList(param);
            //分析评价对象统计（省）
            List<DtAnalysis> analysisList1 = evaluationDao.getDtFxpjInfoList(param);
            param.put("level2", level + 2);
            param.put("zero", adcd.substring(level + 2));
            //行政区基本情况统计(市)
            List<DtAdInfo> prevcntList2 = evaluationDao.getDtAdInfoList(param);
            //调查成果统计（市）
            List<DtSurvey> surveyList2 = evaluationDao.getDtDccgInfoList(param);
            //涉水工程统计（市）
            List<DtWading> wadingList2 = evaluationDao.getDtWadingInfoList(param);
            //分析评价对象统计（市）
            List<DtAnalysis> analysisList2 = evaluationDao.getDtFxpjInfoList(param);
            param.put("level2", level + 4);
            param.put("zero", adcd.substring(level + 4));
            //行政区基本情况统计(县)
            List<DtAdInfo> prevcntList3 = evaluationDao.getDtAdInfoList(param);
            //省统计的时候把两个特殊县的统计放在市上
            List<DtAdInfo> prevcntList33= new ArrayList<>();
            prevcntList3.forEach(item->{
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    prevcntList33.add(item);
                }else{
                    prevcntList2.add(item);
                }
            });
            //调查成果统计（县）
            List<DtSurvey> surveyList3 = evaluationDao.getDtDccgInfoList(param);
            //省统计的时候把两个特殊县的统计放在市上
            List<DtSurvey> surveyList33= new ArrayList<>();
            surveyList3.forEach(item->{
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    surveyList33.add(item);
                }else{
                    surveyList2.add(item);
                }
            });
            //涉水工程统计（县）
            List<DtWading> wadingList3 = evaluationDao.getDtWadingInfoList(param);
            //省统计的时候把两个特殊县的统计放在市上
            List<DtWading> wadingList33= new ArrayList<>();
            wadingList3.forEach(item->{
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    wadingList33.add(item);
                }else{
                    wadingList2.add(item);
                }
            });
            //分析评价对象统计（县）
            List<DtAnalysis> analysisList3 = evaluationDao.getDtFxpjInfoList(param);
            //省统计的时候把两个特殊县的统计放在市上
            List<DtAnalysis> analysisList33= new ArrayList<>();
            analysisList3.forEach(item->{
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    analysisList33.add(item);
                }else{
                    analysisList2.add(item);
                }
            });
            //调查评价信息统计汇总(省市县)
            List<DtAllEvaluationInfo> evaluationProList = syntheticData(adList1, prevcntList1, surveyList1, wadingList1, analysisList1);
            List<DtAllEvaluationInfo> evaluationCityList = syntheticData(adList2, prevcntList2, surveyList2, wadingList2, analysisList2);
            List<DtAllEvaluationInfo> evaluationCouList = syntheticData(adList33, prevcntList33, surveyList33, wadingList33, analysisList33);
            result = evaluationProList.get(0);
            evaluationCityList.forEach(item -> {
                List<DtAllEvaluationInfo> childrenList = getEvaluationChildren(evaluationCouList, item.getAdcd());
                item.setChildren(childrenList);
            });
            result.setChildren(evaluationCityList);
        }
        //市级查询
        else if(level == 4){
            adlvl = 3;
            param.put("adlvl", adlvl);
            List<DtAllEvaluationInfo> list = evaluationDao.getAdInfo(param);
            List<DtAllEvaluationInfo> adList2 = new ArrayList<>();
            List<DtAllEvaluationInfo> adList3 = new ArrayList<>();
            list.forEach(item -> {
                if ("2".equals(item.getAdlvl())) {
                    adList2.add(item);
                }
                if ("3".equals(item.getAdlvl())) {
                    adList3.add(item);
                }
            });
            param.put("level2", level);
            param.put("zero", adcd.substring(level));
            //行政区基本情况统计(市)
            List<DtAdInfo> prevcntList1 = evaluationDao.getDtAdInfoList(param);
            //调查成果统计（市）
            List<DtSurvey> surveyList1 = evaluationDao.getDtDccgInfoList(param);
            //涉水工程统计（市）
            List<DtWading> wadingList1 = evaluationDao.getDtWadingInfoList(param);
            //分析评价对象统计（市）
            List<DtAnalysis> analysisList1 = evaluationDao.getDtFxpjInfoList(param);
            param.put("level2", level + 2);
            param.put("zero", adcd.substring(level + 2));
            //行政区基本情况统计(县)
            List<DtAdInfo> prevcntList2 = evaluationDao.getDtAdInfoList(param);
            //调查成果统计（县）
            List<DtSurvey> surveyList2 = evaluationDao.getDtDccgInfoList(param);
            //涉水工程统计（县）
            List<DtWading> wadingList2 = evaluationDao.getDtWadingInfoList(param);
            //分析评价对象统计（县）
            List<DtAnalysis> analysisList2 = evaluationDao.getDtFxpjInfoList(param);
            //调查评价信息统计汇总(市县)
            List<DtAllEvaluationInfo> evaluationCityList = syntheticData(adList2, prevcntList1, surveyList1, wadingList1, analysisList1);
            List<DtAllEvaluationInfo> evaluationCouList = syntheticData(adList3, prevcntList2, surveyList2, wadingList2, analysisList2);
            result = evaluationCityList.get(0);
            result.setChildren(evaluationCouList);
        }
        //县级查询
        else if(level == 6){
            adlvl = 4;
            param.put("adlvl", adlvl);
            List<DtAllEvaluationInfo> list = evaluationDao.getAdInfo(param);
            List<DtAllEvaluationInfo> adList2 = new ArrayList<>();
            List<DtAllEvaluationInfo> adList3 = new ArrayList<>();
            list.forEach(item -> {
                if ("3".equals(item.getAdlvl())) {
                    adList2.add(item);
                }
                if ("4".equals(item.getAdlvl())) {
                    adList3.add(item);
                }
            });
            param.put("level2", level);
            param.put("zero", adcd.substring(level));
            //行政区基本情况统计(县)
            List<DtAdInfo> prevcntList1 = evaluationDao.getDtAdInfoList(param);
            //调查成果统计（县）
            List<DtSurvey> surveyList1 = evaluationDao.getDtDccgInfoList(param);
            //涉水工程统计（县）
            List<DtWading> wadingList1 = evaluationDao.getDtWadingInfoList(param);
            //分析评价对象统计（县）
            List<DtAnalysis> analysisList1 = evaluationDao.getDtFxpjInfoList(param);
            param.put("level2", level + 3);
            param.put("flag","1");
            param.put("zero", adcd.substring(level + 3));
            //行政区基本情况统计(乡)
            List<DtAdInfo> prevcntList2 = evaluationDao.getDtAdInfoList(param);
            //调查成果统计（乡）
            List<DtSurvey> surveyList2 = evaluationDao.getDtDccgInfoList(param);
            //涉水工程统计（乡）
            List<DtWading> wadingList2 = evaluationDao.getDtWadingInfoList(param);
            //分析评价对象统计（乡）
            List<DtAnalysis> analysisList2 = evaluationDao.getDtFxpjInfoList(param);
            //调查评价信息统计汇总(县乡)
            List<DtAllEvaluationInfo> evaluationCouList = syntheticData(adList2, prevcntList1, surveyList1, wadingList1, analysisList1);
            List<DtAllEvaluationInfo> evaluationTownList = syntheticData(adList3, prevcntList2, surveyList2, wadingList2, analysisList2);
            result = evaluationCouList.get(0);
            result.setChildren(evaluationTownList);
        }
        return result;
    }

    @Override
    public DtIaDanad getOwnDtDanInfo(String adcd) {
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        List<DtIaDanad> resultList=getDanInfo(adcd,level,level,adMap);
        DtIaDanad dtIaDanad =resultList.get(0);
        return dtIaDanad;
    }

    @Override
    public List<DtIaDanad> getLowerDtDanInfo(String adcd) {
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        List<DtIaDanad> resultList=new ArrayList<>();
        if (level<=4){
            resultList=getDanInfo(adcd,level,level+2,adMap);
            //省统计的时候把两个特殊县的统计放在市上
            if(level==2){
                List<DtIaDanad>  resultList2=getDanInfo(adcd,level,level+4,adMap);
                for (DtIaDanad item:resultList2){
                    if(CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())||CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                        resultList.add(item);
                    }
                }
            }
        }else{
            resultList=getDanInfo(adcd,level,level+3,adMap);
        }
        return resultList;
    }

    @Override
    public DtIaDanad getDtDanInfoTree(String adcd) {
        DtIaDanad reIaDanad=null;
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        //省级查询
        if (level==2){
            //省级统计
            List<DtIaDanad> dtDanList1=getDanInfo(adcd,level,level,adMap);
            //市级统计
            List<DtIaDanad> dtDanList2=getDanInfo(adcd,level,level+2,adMap);
            //县级统计
            List<DtIaDanad> dtDanList3=getDanInfo(adcd,level,level+4,adMap);
            //省统计的时候把两个特殊县的统计放在市上
            List<DtIaDanad> dtDanList33=new ArrayList<>();
            dtDanList3.forEach(item->{
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    dtDanList33.add(item);
                }else{
                    dtDanList2.add(item);
                }
            });
            if (dtDanList1!=null && dtDanList1.size()>0){
                reIaDanad=dtDanList1.get(0);
                dtDanList2.forEach(item->{
                    List<DtIaDanad> childList=getDanChildren(dtDanList33,item.getAdcd());
                    item.setChildren(childList);
                });
                reIaDanad.setChildren(dtDanList2);
            }
        }
        //市级查询
        else if(level==4){
            //市级统计
            List<DtIaDanad> dtDanList1=getDanInfo(adcd,level,level,adMap);
            //县级统计
            List<DtIaDanad> dtDanList2=getDanInfo(adcd,level,level+2,adMap);
            if (dtDanList1!=null && dtDanList1.size()>0){
                reIaDanad=dtDanList1.get(0);
                reIaDanad.setChildren(dtDanList2);
            }
        }
        //县级查询
        else if(level==6 || level==9 || level==12){
            //县级统计
            List<DtIaDanad> dtDanList1=getDanInfo(adcd,level,level,adMap);
            //乡镇统计
            List<DtIaDanad> dtDanList2=getDanInfo(adcd,level,level+3,adMap);
            if (dtDanList1!=null && dtDanList1.size()>0){
                reIaDanad=dtDanList1.get(0);
                reIaDanad.setChildren(dtDanList2);
            }
        }
        return reIaDanad;
    }

    @Override
    public List<DtIaHsfwater> getLowerDtHsfwaterInfo(String adcd) {
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        List<DtIaHsfwater> resultList=new ArrayList<>();
        if (level<=4){
            resultList=getHsfwaterInfo(adcd,level,level+2,adMap);
            //省统计的时候把两个特殊县的统计放在市上
            if(level==2){
                List<DtIaHsfwater>  resultList2=getHsfwaterInfo(adcd,level,level+4,adMap);
                for (DtIaHsfwater item:resultList2){
                    if(CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())||CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                        resultList.add(item);
                    }
                }
            }
        }else{
            resultList=getHsfwaterInfo(adcd,level,level+3,adMap);
        }
        return resultList;
    }

    @Override
    public DtIaHsfwater getDtHsfwaterInfoTree(String adcd) {
        DtIaHsfwater dtIaHsfwater=null;
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        //省级查询
        if (level==2){
            //省级统计
            List<DtIaHsfwater> dtIaHsfwaterList1=getHsfwaterInfo(adcd,level,level,adMap);
            //市级统计
            List<DtIaHsfwater> dtIaHsfwaterList2=getHsfwaterInfo(adcd,level,level+2,adMap);
            //县级统计
            List<DtIaHsfwater> dtIaHsfwaterList3=getHsfwaterInfo(adcd,level,level+4,adMap);
            //省统计的时候把两个特殊县的统计放在市上
            List<DtIaHsfwater> dtIaHsfwaterList33=new ArrayList<>();
            dtIaHsfwaterList3.forEach(item->{
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    dtIaHsfwaterList33.add(item);
                }else{
                    dtIaHsfwaterList2.add(item);
                }
            });
            if (dtIaHsfwaterList1!=null && dtIaHsfwaterList1.size()>0){
                dtIaHsfwater=dtIaHsfwaterList1.get(0);
                dtIaHsfwaterList2.forEach(item->{
                    List<DtIaHsfwater> childList=getHsfwaterChildren(dtIaHsfwaterList33,item.getAdcd());
                    item.setChildren(childList);
                });
                dtIaHsfwater.setChildren(dtIaHsfwaterList2);
            }
        }
        //市级查询
        else if(level==4){
            //市级统计
            List<DtIaHsfwater> dtIaHsfwaterList1=getHsfwaterInfo(adcd,level,level,adMap);
            //县级统计
            List<DtIaHsfwater> dtIaHsfwaterList2=getHsfwaterInfo(adcd,level,level+2,adMap);
            if (dtIaHsfwaterList1!=null && dtIaHsfwaterList1.size()>0){
                dtIaHsfwater=dtIaHsfwaterList1.get(0);
                dtIaHsfwater.setChildren(dtIaHsfwaterList2);
            }
        }
        //县级查询
        else if(level==6){
            //县级统计
            List<DtIaHsfwater> dtIaHsfwaterList1=getHsfwaterInfo(adcd,level,level,adMap);
            //乡镇统计
            List<DtIaHsfwater> dtIaHsfwaterList2=getHsfwaterInfo(adcd,level,level+3,adMap);
            if (dtIaHsfwaterList1!=null && dtIaHsfwaterList1.size()>0){
                dtIaHsfwater=dtIaHsfwaterList1.get(0);
                dtIaHsfwater.setChildren(dtIaHsfwaterList2);
            }
        }
        return dtIaHsfwater;
    }

    @Override
    public List<DtIaGully> getLowerDtIaGullyInfo(String adcd) {
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        List<DtIaGully> resultList=new ArrayList<>();
        if (level<=4){
            resultList=getGullyInfo(adcd,level,level+2,adMap);
            //省统计的时候把两个特殊县的统计放在市上
            if(level==2){
                List<DtIaGully>  resultList2=getGullyInfo(adcd,level,level+4,adMap);
                for (DtIaGully item:resultList2){
                    if(CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())||CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                        resultList.add(item);
                    }
                }
            }
        }else{
            resultList=getGullyInfo(adcd,level,level+3,adMap);
        }
        return resultList;
    }

    @Override
    public DtIaGully getDtGullyInfoTree(String adcd) {
        DtIaGully dtIaGully=null;
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        //省级查询
        if (level==2){
            //省级统计
            List<DtIaGully> dtIaGullyList1=getGullyInfo(adcd,level,level,adMap);
            //市级统计
            List<DtIaGully> dtIaGullyList2=getGullyInfo(adcd,level,level+2,adMap);
            //县级统计
            List<DtIaGully> dtIaGullyList3=getGullyInfo(adcd,level,level+4,adMap);
            //省统计的时候把两个特殊县的统计放在市上
            List<DtIaGully> dtIaGullyList33=new ArrayList<>();
            dtIaGullyList3.forEach(item->{
                item.setIslazychild(true);
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    dtIaGullyList33.add(item);
                }else{
                    dtIaGullyList2.add(item);
                }
            });
            if (dtIaGullyList1!=null && dtIaGullyList1.size()>0){
                dtIaGully=dtIaGullyList1.get(0);
                dtIaGullyList2.forEach(item->{
                    List<DtIaGully> childList=getGullyChildren(dtIaGullyList33,item.getAdcd());
                    item.setChildren(childList);
                });
                dtIaGully.setChildren(dtIaGullyList2);
            }
        }
        //市级查询
        else if(level==4){
            //市级统计
            List<DtIaGully> dtIaGullyList1=getGullyInfo(adcd,level,level,adMap);
            //县级统计
            List<DtIaGully> dtIaGullyList2=getGullyInfo(adcd,level,level+2,adMap);
            dtIaGullyList2.forEach(item->{
                item.setIslazychild(true);
            });
            if (dtIaGullyList1!=null && dtIaGullyList1.size()>0){
                dtIaGully=dtIaGullyList1.get(0);
                dtIaGully.setChildren(dtIaGullyList2);
            }
        }
        //县级查询
        else if(level==6){
            //县级统计
            List<DtIaGully> dtIaGullyList1=getGullyInfo(adcd,level,level,adMap);
            //乡镇统计
            List<DtIaGully> dtIaGullyList2=getGullyInfo(adcd,level,level+3,adMap);
            dtIaGullyList2.forEach(item->{
                item.setIslazychild(true);
            });
            if (dtIaGullyList1!=null && dtIaGullyList1.size()>0){
                dtIaGully=dtIaGullyList1.get(0);
                dtIaGully.setChildren(dtIaGullyList2);
            }
        }
        return dtIaGully;
    }

    @Override
    public DtIaGully getDtGullyInfoExport(String adcd) {
        DtIaGully dtIaGully=null;
        int level= AdcdUtil.getAdLevel(adcd);

        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<IaGully> resultMap = evaluationDao.getGully(param);

        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        //省级查询
        if (level==2){
            //省级统计
            List<DtIaGully> dtIaGullyList1=getGullyInfo(adcd,level,level,adMap);
            //市级统计
            List<DtIaGully> dtIaGullyList2=getGullyInfo(adcd,level,level+2,adMap);
            //县级统计
            List<DtIaGully> dtIaGullyList3=getGullyInfo(adcd,level,level+4,adMap);
            // 镇
            List<DtIaGully> dtIaGullyList4=getGullyInfo(adcd,level,level+7,adMap);
            //省统计的时候把两个特殊县的统计放在市上
            List<DtIaGully> dtIaGullyList33=new ArrayList<>();
            List<DtIaGully> dtIaGullyList44=new ArrayList<>();
            dtIaGullyList4.forEach(item->{
                item.setIslazychild(true);
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    dtIaGullyList44.add(item);
                }else{
                    dtIaGullyList3.add(item);
                }
            });
            dtIaGullyList3.forEach(item->{
                item.setIslazychild(true);
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    dtIaGullyList33.add(item);
                }else{
                    dtIaGullyList2.add(item);
                }
            });
            if (dtIaGullyList1!=null && dtIaGullyList1.size()>0){
                dtIaGully=dtIaGullyList1.get(0);
                dtIaGullyList2.forEach(item->{
                    if(item.getAdcd().equals("220581000000000")){
                            List<DtIaGully> child=getGullyChildren(dtIaGullyList44,item.getAdcd());
                            item.setChildren(child);
                            child.forEach(b->{
                                List<IaGully> bb=getGullyIaGully(resultMap,b.getAdcd());
                                b.setChildrenGully(bb);
                            });
                    }else{
                        List<DtIaGully> childList=getGullyChildren(dtIaGullyList33,item.getAdcd());
                        item.setChildren(childList);
                        childList.forEach(a->{
                            List<DtIaGully> child=getGullyChildren(dtIaGullyList44,a.getAdcd());
                            a.setChildren(child);
                            child.forEach(b->{
                                List<IaGully> bb=getGullyIaGully(resultMap,b.getAdcd());
                                b.setChildrenGully(bb);
                            });
                        });
                    }
                });
                dtIaGully.setChildren(dtIaGullyList2);
            }
        }
        //市级查询
        else if(level==4){
            //市级统计
            List<DtIaGully> dtIaGullyList1=getGullyInfo(adcd,level,level,adMap);
            //县级统计
            List<DtIaGully> dtIaGullyList2=getGullyInfo(adcd,level,level+2,adMap);

            List<DtIaGully> dtIaGullyList3=getGullyInfo(adcd,level,level+5,adMap);

            dtIaGullyList3.forEach(item->{
                List<IaGully> bb=getGullyIaGully(resultMap,item.getAdcd());
                item.setChildrenGully(bb);
            });
            dtIaGullyList2.forEach(item->{
                List<DtIaGully> child=getGullyChildren(dtIaGullyList3,item.getAdcd());
                item.setChildren(child);
                item.setIslazychild(true);
            });
            if (dtIaGullyList1!=null && dtIaGullyList1.size()>0){
                dtIaGully=dtIaGullyList1.get(0);
                dtIaGully.setChildren(dtIaGullyList2);
            }
        }
        //县级查询
        else if(level==6){
            //县级统计
            List<DtIaGully> dtIaGullyList1=getGullyInfo(adcd,level,level,adMap);
            //乡镇统计
            List<DtIaGully> dtIaGullyList2=getGullyInfo(adcd,level,level+3,adMap);
            dtIaGullyList2.forEach(item->{
                List<IaGully> bb=getGullyIaGully(resultMap,item.getAdcd());
                item.setChildrenGully(bb);
                item.setIslazychild(true);
            });
            if (dtIaGullyList1!=null && dtIaGullyList1.size()>0){
                dtIaGully=dtIaGullyList1.get(0);
                dtIaGully.setChildren(dtIaGullyList2);
            }
        }
        return dtIaGully;
    }


    @Override
    public DtIaVlgestat getOwnDtVlgestatInfo(String adcd) {
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        List<DtIaVlgestat> resultList=getVlgestatInfo(adcd,level,level,adMap);
        DtIaVlgestat dtIaVlgestat =resultList.get(0);
        return dtIaVlgestat;
    }

    @Override
    public List<DtIaVlgestat> getLowerDtVlgestatInfo(String adcd) {
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        List<DtIaVlgestat> resultList=getVlgestatInfo(adcd,level,level+2,adMap);
        //省统计的时候把两个特殊县的统计放在市上
        if(level==2){
            List<DtIaVlgestat>  resultList2=getVlgestatInfo(adcd,level,level+4,adMap);
            for (DtIaVlgestat item:resultList2){
                if(CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())||CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    resultList.add(item);
                }
            }
        }
        return resultList;
    }

    @Override
    public DtIaVlgestat getDtDanVlgestatTree(String adcd) {
        DtIaVlgestat dtIaVlgestat=null;
        int level= AdcdUtil.getAdLevel(adcd);
        //获取政区信息map
        Map<String,DtAllEvaluationInfo> adMap=getAdInfo(adcd,level);
        //省级查询
        if (level==2){
            //省级统计
            List<DtIaVlgestat> dtVlgestatList1=getVlgestatInfo(adcd,level,level,adMap);
            //市级统计
            List<DtIaVlgestat> dtVlgestatList2=getVlgestatInfo(adcd,level,level+2,adMap);
            //县级统计
            List<DtIaVlgestat> dtVlgestatList3=getVlgestatInfo(adcd,level,level+4,adMap);
            //省统计的时候把两个特殊县的统计放在市上
            List<DtIaVlgestat> dtVlgestatList33=new ArrayList<>();
            dtVlgestatList3.forEach(item->{
                if(!CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())&&!CommConstants.Public.GONGZHULING_ADCD.equals(item.getAdcd())){
                    dtVlgestatList33.add(item);
                }else{
                    dtVlgestatList2.add(item);
                }
            });
            if (dtVlgestatList1!=null && dtVlgestatList1.size()>0){
                dtIaVlgestat=dtVlgestatList1.get(0);
                dtVlgestatList2.forEach(item->{
                    List<DtIaVlgestat> childList=getVlgestatChildren(dtVlgestatList33,item.getAdcd());
                    item.setChildren(childList);
                });
                dtIaVlgestat.setChildren(dtVlgestatList2);
            }
        }
        //市级查询
        else if(level==4){
            //市级统计
            List<DtIaVlgestat> dtVlgestatList1=getVlgestatInfo(adcd,level,level,adMap);
            //县级统计
            List<DtIaVlgestat> dtVlgestatList2=getVlgestatInfo(adcd,level,level+2,adMap);
            if (dtVlgestatList1!=null && dtVlgestatList1.size()>0){
                dtIaVlgestat=dtVlgestatList1.get(0);
                dtIaVlgestat.setChildren(dtVlgestatList2);
            }
        }
        //县级查询
        else if(level==6){
            //县级统计
            List<DtIaVlgestat> dtVlgestatList1=getVlgestatInfo(adcd,level,level,adMap);
            if (dtVlgestatList1!=null && dtVlgestatList1.size()>0){
                dtIaVlgestat=dtVlgestatList1.get(0);
            }
        }
        return dtIaVlgestat;
    }

    @Override
    public DtKeyAd getDtKeyAdTree(KeyAreasQuery query) {
        DtKeyAd dtKeyAd=null;
        int level= AdcdUtil.getAdLevel(query.getAdcd());
        //省级查询
        if (level==2){
            //省级统计
            List<DtKeyAd> dtKeyAdList1=gettKeyAdInfo(query.getAdcd(),level,level,query.getFzqType());
            //市级统计
            List<DtKeyAd> dtKeyAdList2=gettKeyAdInfo(query.getAdcd(),level,level+2,query.getFzqType());
            //县级统计
            List<DtKeyAd> dtKeyAdList3=gettKeyAdInfo(query.getAdcd(),level,level+4,query.getFzqType());
            //乡镇统计
            List<DtKeyAd> dtKeyAdList4=gettKeyAdInfo(query.getAdcd(),level,level+7,query.getFzqType());
            //省统计的时候把两个特殊县的统计放在市上
            List<DtKeyAd> dtKeyAdList33=new ArrayList<>();

            Iterator<DtKeyAd> iterator3 = dtKeyAdList3.iterator();
            while(iterator3.hasNext()) {
                DtKeyAd item = iterator3.next();
                if (CommConstants.Public.MEIHEKOU_ADCD.equals(item.getAdcd())) {
                    dtKeyAdList2.add(item);
                    iterator3.remove();
                }
            }

            Iterator<DtKeyAd> iterator4 = dtKeyAdList4.iterator();
            while(iterator4.hasNext()) {
                DtKeyAd item = iterator4.next();
                if (CommConstants.Public.MEIHEKOU_ADCD.equals(item.getPadcd())) {
                    dtKeyAdList3.add(item);
                    iterator4.remove();
                }
            }
            if (dtKeyAdList1!=null && dtKeyAdList1.size()>0){
                dtKeyAd=dtKeyAdList1.get(0);
                dtKeyAdList3.forEach(item->{
                    List<DtKeyAd> childList=getKeyAdChildren(dtKeyAdList4,item.getAdcd());
                    item.setChildren(childList);
                });
                dtKeyAdList2.forEach(item->{
                    List<DtKeyAd> childList=getKeyAdChildren(dtKeyAdList3,item.getAdcd());
                    item.setChildren(childList);
                });
                dtKeyAd.setChildren(dtKeyAdList2);
            }
        }
        //市级查询
        else if(level==4){
            //市级统计
            List<DtKeyAd> dtKeyAdList1=gettKeyAdInfo(query.getAdcd(),level,level,query.getFzqType());
            //县级统计
            List<DtKeyAd> dtKeyAdList2=gettKeyAdInfo(query.getAdcd(),level,level+2,query.getFzqType());
            //乡镇统计
            List<DtKeyAd> dtKeyAdList3=gettKeyAdInfo(query.getAdcd(),level,level+5,query.getFzqType());
            if (dtKeyAdList1!=null && dtKeyAdList1.size()>0){
                dtKeyAd=dtKeyAdList1.get(0);
                dtKeyAdList2.forEach(item->{
                    List<DtKeyAd> childList=getKeyAdChildren(dtKeyAdList3,item.getAdcd());
                    item.setChildren(childList);
                });
                dtKeyAd.setChildren(dtKeyAdList2);
            }
        }
        //县级查询
        else if(level==6){
            //县级统计
            List<DtKeyAd> dtKeyAdList1=gettKeyAdInfo(query.getAdcd(),level,level,query.getFzqType());
            //乡镇统计
            List<DtKeyAd> dtKeyAdList2=gettKeyAdInfo(query.getAdcd(),level,level+3,query.getFzqType());
            if (dtKeyAdList1!=null && dtKeyAdList1.size()>0){
                dtKeyAd=dtKeyAdList1.get(0);
                dtKeyAd.setChildren(dtKeyAdList2);
            }
        }
        return dtKeyAd;
    }

    /**
     * 获取政区信息map
     */
    private Map<String,DtAllEvaluationInfo> getAdInfo(String adcd,int level){
        Map<String, Object> param = new HashMap<>();
        param.put("ad",adcd.substring(0,level));
        param.put("level",level);

        int adLevel = AdcdUtil.getXzAdLevel(adcd);
//        int adlvl = 4;
        int adlvl = (adLevel + 3) >= 6 ? 6 : (adLevel + 3);
        param.put("adlvl", adlvl);
        List<DtAllEvaluationInfo> list = evaluationDao.getAdInfo(param);
        Map<String,DtAllEvaluationInfo> adMap=new HashMap<>();
        list.forEach(item->{
            adMap.put(item.getAdcd(),item);
        });
        return adMap;
    }
    /**
     * 查询危险区统计信息
     */
    private List<DtIaDanad> getDanInfo(String adcd, int level, int level2, Map<String,DtAllEvaluationInfo> adMap){
        Map<String, Object> param = new HashMap<>();
        param.put("ad",adcd.substring(0,level));
        param.put("level",level);
        param.put("level2",level2);
        param.put("zero",adcd.substring(level2));
        if(level2>=9){
            param.put("flag","1");
        }
        List<DtIaDanad> resultList=new ArrayList<>();
        List<DtIaDanad> dtIaDanads =evaluationDao.getDtDanInfo(param);
        //将政区信息合并到统计结果中
        dtIaDanads.forEach(item->{
            if (adMap.containsKey(item.getAdcd())){
                DtAllEvaluationInfo evaluationInfo=adMap.get(item.getAdcd());
                item.setAdnm(evaluationInfo.getAdnm());
                item.setPadcd(evaluationInfo.getPadcd());
                resultList.add(item);
            }
        });
        return resultList;
    }
    /**
     * 查询历史山洪灾害统计信息
     */
    private List<DtIaHsfwater> getHsfwaterInfo(String adcd, int level, int level2, Map<String,DtAllEvaluationInfo> adMap){
        Map<String, Object> param = new HashMap<>();
        param.put("ad",adcd.substring(0,level));
        param.put("level",level);
        param.put("level2",level2);
        param.put("zero",adcd.substring(level2));
        if(level2>=9){
            param.put("flag","1");
        }
        List<DtIaHsfwater> resultList=new ArrayList<>();
        List<DtIaHsfwater> dtIaHsfwaters =evaluationDao.getHsfwaterInfo(param);
        //将政区信息合并到统计结果中
        dtIaHsfwaters.forEach(item->{
            if (adMap.containsKey(item.getAdcd())){
                DtAllEvaluationInfo evaluationInfo=adMap.get(item.getAdcd());
                item.setAdnm(evaluationInfo.getAdnm());
                item.setPadcd(evaluationInfo.getPadcd());
                resultList.add(item);
            }
        });
        return resultList;
    }
    /**
     * 查询社会经济情况统计信息
     */
    private List<DtIaVlgestat> getVlgestatInfo(String adcd, int level, int level2, Map<String,DtAllEvaluationInfo> adMap){
        Map<String, Object> param = new HashMap<>();
        param.put("ad",adcd.substring(0,level));
        param.put("level",level);
        param.put("level2",level2);
        param.put("zero",adcd.substring(level2));
        if (level2==6){
            param.put("flag","1");
        }
        List<DtIaVlgestat> resultList=new ArrayList<>();
        List<DtIaVlgestat> dtIaVlgestats =evaluationDao.getVlgestatInfo(param);
        //将政区信息合并到统计结果中
        dtIaVlgestats.forEach(item->{
            if (adMap.containsKey(item.getAdcd())){
                DtAllEvaluationInfo evaluationInfo=adMap.get(item.getAdcd());
                item.setAdnm(evaluationInfo.getAdnm());
                item.setPadcd(evaluationInfo.getPadcd());
                resultList.add(item);
            }
        });
        return resultList;
    }
    /**
     * 查询需防洪治理山洪沟统计信息
     */
    private List<DtIaGully> getGullyInfo(String adcd, int level, int level2, Map<String,DtAllEvaluationInfo> adMap){
        Map<String, Object> param = new HashMap<>();
        param.put("ad",adcd.substring(0,level));
        param.put("level",level);
        param.put("level2",level2);
        param.put("zero",adcd.substring(level2));
        if(level2>=9){
            param.put("flag","1");
        }
        List<DtIaGully> resultList=new ArrayList<>();
        List<DtIaGully> dtIaGullies =evaluationDao.getGullyInfo(param);
        //将政区信息合并到统计结果中
        dtIaGullies.forEach(item->{
            item.setIslazychild(false);
            if (adMap.containsKey(item.getAdcd())){
                DtAllEvaluationInfo evaluationInfo=adMap.get(item.getAdcd());
                item.setAdnm(evaluationInfo.getAdnm());
                item.setPadcd(evaluationInfo.getPadcd());
                resultList.add(item);
            }
        });
        return resultList;
    }
    /**
     * 查询重点区统计信息
     */
    private List<DtKeyAd> gettKeyAdInfo(String adcd, int level, int level2,List<String> fzqType){
        Map<String, Object> param = new HashMap<>();
        param.put("ad",adcd.substring(0,level));
        param.put("level",level);
        param.put("level2",level2);
        param.put("zero",adcd.substring(level2));
        param.put("fzqType",fzqType);
        if(level2>=9){
            param.put("flag","1");
        }
        Map<String, DtSurvey> map2 = new HashMap<>();
        List<DtKeyAd> dtKeyAds =evaluationDao.getDtKeyAdListNew(param);
        //调查成果统计
        List<DtSurvey> surveyList = evaluationDao.getDtDccgInfoListNew(param);
        surveyList.forEach(item->{
            map2.put(item.getAdcd(),item);
        });
        dtKeyAds.forEach(item->{
            if (map2.containsKey(item.getAdcd())){
                DtSurvey dtSurvey=map2.get(item.getAdcd());
                item.setFlrvvlgct(dtSurvey.getFlrvvlgct());
                item.setDanct(dtSurvey.getDanadct());
                item.setHsfwaterct(dtSurvey.getHsfwaterct());
            }
        });
        return dtKeyAds;
    }
    /**
     * 调查成果统计中4类调查成果信息与政区信息合并结果集
     */
    private List<DtAllEvaluationInfo> syntheticData(List<DtAllEvaluationInfo> list, List<DtAdInfo> list1, List<DtSurvey> list2, List<DtWading> list3, List<DtAnalysis> list4) {
        List<DtAllEvaluationInfo> resultList = new ArrayList<>();
        String flag = "1";
        if (list != null && list.size() > 0) {
            Map<String, DtAllEvaluationInfo> evaluation = new HashMap<>();
            list.forEach(item -> {
                evaluation.put(item.getAdcd(), item);
            });
            //政区信息汇总
            list1.forEach(item -> {
                if (evaluation.get(item.getAdcd()) != null && evaluation.get(item.getAdcd()).toString().length() > 0) {
                    DtAllEvaluationInfo evaluationInfo = evaluation.get(item.getAdcd());
                    evaluationInfo.setFlag(flag);
                    evaluationInfo.setPtcount(item.getPtcount());
                    evaluationInfo.setHtcount(item.getHtcount());
                    evaluationInfo.setLdarea(item.getLdarea());
                    evaluationInfo.setPlarea(item.getPlarea());
                    evaluationInfo.setPrevcnt(item.getPrevcnt());
                    evaluationInfo.setImppevcnt(item.getImppevcnt());
                }
            });
            //调查成果信息汇总
            list2.forEach(item -> {
                if (evaluation.get(item.getAdcd()) != null && evaluation.get(item.getAdcd()).toString().length() > 0) {
                    DtAllEvaluationInfo evaluationInfo = evaluation.get(item.getAdcd());
                    evaluationInfo.setFlag(flag);
                    evaluationInfo.setFlrvvlgct(item.getFlrvvlgct());
                    evaluationInfo.setDanadct(item.getDanadct());
                    evaluationInfo.setDtresidentct(item.getDtresidentct());
                    evaluationInfo.setBsnssinfoct(item.getBsnssinfoct());
                    evaluationInfo.setHsfwaterct(item.getHsfwaterct());
                    evaluationInfo.setHsurfacect(item.getHsurfacect());
                    evaluationInfo.setVsurfacect(item.getVsurfacect());
                    evaluationInfo.setGullyct(item.getGullyct());
                }
            });
            //涉水工程信息汇总
            list3.forEach(item -> {
                if (evaluation.get(item.getAdcd()) != null && evaluation.get(item.getAdcd()).toString().length() > 0) {
                    DtAllEvaluationInfo evaluationInfo = evaluation.get(item.getAdcd());
                    evaluationInfo.setFlag(flag);
                    evaluationInfo.setRsst(item.getRsst());
                    evaluationInfo.setSlust(item.getSlust());
                    evaluationInfo.setDikst(item.getDikst());
                    evaluationInfo.setDamst(item.getDamst());
                    evaluationInfo.setCulst(item.getCulst());
                    evaluationInfo.setBrist(item.getBrist());
                }
            });
            //分析评价信息汇总
            list4.forEach(item -> {
                if (evaluation.get(item.getAdcd()) != null && evaluation.get(item.getAdcd()).toString().length() > 0) {
                    DtAllEvaluationInfo evaluationInfo = evaluation.get(item.getAdcd());
                    evaluationInfo.setFlag(flag);
                    evaluationInfo.setDfwrulect(item.getDfwrulect());
                    evaluationInfo.setWlwrulect(item.getWlwrulect());
                    evaluationInfo.setYjicrct(item.getYjicrct());
                    evaluationInfo.setNowfhtbct(item.getNowfhtbct());
                    evaluationInfo.setFxcgtbct(item.getFxcgtbct());
                    evaluationInfo.setJygstbct(item.getJygstbct());
                    evaluationInfo.setYjcjtbct(item.getYjcjtbct());
                    evaluationInfo.setSdtdtbct(item.getSdtdtbct());
                    evaluationInfo.setSwllrktbct(item.getSwllrktbct());
                }
            });
            for (Map.Entry<String, DtAllEvaluationInfo> entry : evaluation.entrySet()) {
                DtAllEvaluationInfo evaluationInfo = entry.getValue();
                if ("1".equals(evaluationInfo.getFlag())) {
                    resultList.add(evaluationInfo);
                }
            }
        }
        return resultList;
    }


    private Map<String, Object> setLevelZeroMap(String adcd) {
        Map<String, Object> param = new HashMap<>();
        if (adcd != null && adcd.length() > 0) {
            int level = AdcdUtil.getAdLevel(adcd);
            param.put("ad", adcd.substring(0, level));
            param.put("level", level);
            if (level <= 4) {
                param.put("level2", level + 2);
                param.put("zero", adcd.substring(level + 2));
            } else {
                //flag用来避免查询乡镇统计时统计到县里的数据
                param.put("flag","1");
                param.put("level2", level + 3);
                param.put("zero", adcd.substring(level + 3));
            }
        }
        return param;
    }

    private List<DtAllEvaluationInfo> getEvaluationChildren(List<DtAllEvaluationInfo> list, String padcd) {
        // 通过父级编码子类
        List<DtAllEvaluationInfo> childList = list.stream().filter(x -> x.getPadcd().equals(padcd)).collect(Collectors.toList());
        return childList;
    }
    private List<DtIaDanad> getDanChildren(List<DtIaDanad> list, String padcd) {
        // 通过父级编码子类
        List<DtIaDanad> childList = list.stream().filter(x -> x.getPadcd().equals(padcd)).collect(Collectors.toList());
        return childList;
    }
    private List<DtIaHsfwater> getHsfwaterChildren(List<DtIaHsfwater> list, String padcd) {
        // 通过父级编码子类
        List<DtIaHsfwater> childList = list.stream().filter(x -> x.getPadcd().equals(padcd)).collect(Collectors.toList());
        return childList;
    }
    private List<DtIaGully> getGullyChildren(List<DtIaGully> list, String padcd) {
        // 通过父级编码子类
        List<DtIaGully> childList = list.stream().filter(x -> x.getPadcd().equals(padcd)).collect(Collectors.toList());
        return childList;
    }
    private List<IaGully> getGullyIaGully(List<IaGully> list, String padcd) {
        // 通过父级编码子类
        List<IaGully> childList = list.stream().filter(x -> x.getPadcd().equals(padcd)).collect(Collectors.toList());
        return childList;
    }
    private List<DtIaGully> getGullyChildrenList(List<DtIaGully> list, String adcd,int level) {
        // 通过父级编码子类
        List<DtIaGully> childList = list.stream().filter(x -> x.getPadcd().substring(0,level).equals(adcd)).collect(Collectors.toList());
        return childList;
    }
    private List<DtIaVlgestat> getVlgestatChildren(List<DtIaVlgestat> list, String padcd) {
        // 通过父级编码子类
        List<DtIaVlgestat> childList = list.stream().filter(x -> x.getPadcd().equals(padcd)).collect(Collectors.toList());
        return childList;
    }
    private List<DtKeyAd> getKeyAdChildren(List<DtKeyAd> list, String padcd) {
        // 通过父级编码子类
        List<DtKeyAd> childList = list.stream().filter(x -> x.getPadcd().equals(padcd)).collect(Collectors.toList());
        return childList;
    }
}
