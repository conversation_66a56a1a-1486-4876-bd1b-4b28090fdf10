package com.huitu.cloud.api.ewci.hydfcst.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * 水文预报查询条件
 *
 * <AUTHOR>
 */
@ApiModel(value = "水文预报查询条件")
public class HydfcstQuery {

    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "参数[开始时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private String stm;

    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "参数[结束时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private String etm;

    @ApiModelProperty(value = "流域编码", required = false)
    private String bscd;

    public @NotNull(message = "参数[开始时间]不能为空") String getStm() {
        return stm;
    }

    public void setStm(@NotNull(message = "参数[开始时间]不能为空") String stm) {
        this.stm = stm;
    }

    public @NotNull(message = "参数[结束时间]不能为空") String getEtm() {
        return etm;
    }

    public void setEtm(@NotNull(message = "参数[结束时间]不能为空") String etm) {
        this.etm = etm;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public Map<String, Object> toQueryParam() {
        Map<String, Object> params = new HashMap<>();
        params.put("stm", getStm());
        params.put("etm", getEtm());
        params.put("bscd", getBscd());
        return params;
    }
}
