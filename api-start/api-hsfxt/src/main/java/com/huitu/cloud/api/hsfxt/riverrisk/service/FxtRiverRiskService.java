package com.huitu.cloud.api.hsfxt.riverrisk.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmap;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huitu.cloud.api.hsfxt.riverrisk.entity.RvRiskmapVo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
public interface FxtRiverRiskService extends IService<RvRiskmap> {

    IPage<RvRiskmapVo> getRiverRiskList(String rvcode, String name, int pageNum, int pageSize);
}
