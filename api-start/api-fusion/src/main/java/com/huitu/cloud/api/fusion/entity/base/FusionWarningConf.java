package com.huitu.cloud.api.fusion.entity.base;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 预警配置
 */
@ApiModel(value = "预警配置")
public class FusionWarningConf implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "平台ID")
    @TableField(value = "PLATFORM_ID")
    private String platformId;
    @ApiModelProperty(value = "预警超时时间，单位：分钟，默认1440分钟")
    @TableField(value = "WARN_TIMEOUT")
    private Short warnTimeout;
    @ApiModelProperty(value = "预警发布人")
    @TableField(value = "ISSUER")
    private String issuer;
    @ApiModelProperty(value = "预警发布单位")
    @TableField(value = "ISSUING_UNIT")
    private String issuingUnit;
    @ApiModelProperty(value = "预警接收单位")
    @TableField(value = "RECEIVING_UNIT")
    private String receivingUnit;
    @ApiModelProperty(value = "短信签名")
    @TableField(value = "SMS_SIGN_NAME")
    private String smsSignName;
    @ApiModelProperty(value = "被排除的平台，用于处理市本级等特殊情况")
    @TableField(value = "EXCLUSIONS")
    private String exclusions;
    @ApiModelProperty(value = "预警标识，注：1=启用、0=禁用")
    @TableField(value = "FLAG")
    private String flag;

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public Short getWarnTimeout() {
        return warnTimeout;
    }

    public void setWarnTimeout(Short warnTimeout) {
        this.warnTimeout = warnTimeout;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getIssuingUnit() {
        return issuingUnit;
    }

    public void setIssuingUnit(String issuingUnit) {
        this.issuingUnit = issuingUnit;
    }

    public String getReceivingUnit() {
        return receivingUnit;
    }

    public void setReceivingUnit(String receivingUnit) {
        this.receivingUnit = receivingUnit;
    }

    public String getSmsSignName() {
        return smsSignName;
    }

    public void setSmsSignName(String smsSignName) {
        this.smsSignName = smsSignName;
    }

    public String getExclusions() {
        return exclusions;
    }

    public void setExclusions(String exclusions) {
        this.exclusions = exclusions;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
}
