package com.huitu.cloud.api.syq.river.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.syq.river.entity.*;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 河道水情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */
public interface RiverService extends IService<StRiverR> {
    /**
     * 查询最新河道水情信息
     *
     * @param adcd      政区编码 必录项 15位
     * @param stm       开始时间
     * @param etm       结束时间
     * @param stType    1 水文 2 山洪 3 气象
     * @param stnm      测站名称
     * @param dataFlag  是否展有数位的 1 显示有数据 0 无数据
     * @param warnFlag  是否显示超警戒
     * @param bscd      流域编码
     * @param pageNum   页码
     * @param pageSize  每页数量
     * @param isOut     辖区内外
     * @param videoFlag 仅有视频
     * @return
     */
    IPage<StRiverVo> getRiverByConditon(String adcd, String stm, String etm, List<String> stType, String stnm, boolean dataFlag, boolean warnFlag, String bscd, int pageNum, int pageSize, List<String> isOut, List<String> isFollow, boolean videoFlag);

    /**
     * 查询单站水位列表
     *
     * @param stcd 测站
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    List<RiverTmVo> getRiverList(String stcd, String stm, String etm);

    /**
     * 根据测站编码与施测时间 查询河道单站的大断面列表
     *
     * @param stcd 测站编码
     * @param mstm 施测时间
     * @return
     */
    List<StRvsectB> getRiverDuanMianList(String stcd, String mstm);

    /**
     * 根据测站编码 查询大断面施测时间
     *
     * @param stcd
     * @return
     */
    List<String> getRiverDuanMianTmList(String stcd);

    /**
     * 分页查询一段时间内河道日旬月均值
     *
     * @param stm      开始时间
     * @param etm      结束时间
     * @param stList   测站编码集合
     * @param tmType   时间类型
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return
     */
    IPage<RiverDayAvg> getDayAvgRiverByPage(String stm, String etm, List<String> stList, String tmType, int pageNum, int pageSize);

    /**
     * 单站水位流量关系名称下拉列表查询
     *
     * @param stcd
     * @return
     */
    List<QueryStZqrlb> getZqNameBySt(String stcd);

    /**
     * 单站水位流量关系曲线列表信息查询
     *
     * @param stcd 测站编码
     * @param list 曲线名称时间对象数组
     * @return
     */
    List<StZqrlb> getZq(String stcd, List<QueryStZqrlb> list);

    /**
     * 分页查询多站河道水情预报
     *
     * @param adcd     政区编码
     * @param stnm     测站名称
     * @param wnstatus 告警状态
     * @param stm      开始时间
     * @param etm      结束时间
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return
     */
    IPage<StForecastf> getZqForecastByPage(String adcd, String stnm, String wnstatus, Date stm, Date etm, List<String> stcds, int pageNum, int pageSize);

    /**
     * 单站河道水情预报
     *
     * @param stcd 测站编码
     * @return
     */
    List<OneStForecast> getZqForecastByOneSt(String stcd);

    /**
     * 查询设计洪水
     *
     * @param param
     * @return
     */
    StDsnfld getStDsnfld(StDsnfldQuery param);

    /**
     * 查询河道水情极值
     *
     * @param param
     * @return
     */
    List<StRvevsR> listStRvevs(StRvevsQuery param);

    /**
     * 查询水库告警信息
     *
     * @param adcd   政区编码
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站类型 1.水文 2.山洪
     * @param stnm   测站名称
     * @param bscd   流域编码
     * @return
     */
    RiverWarnVo getRiverWarn(String adcd, String stm, String etm, List<String> stType, String stnm, String bscd);

    /**
     * 查询最大超警水位统计
     *
     * @param adcd 政区编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    List<StRiverVo> getStRiverMaxWarnList(String adcd, String stm, String etm);

    /**
     * 查询最大超警水位统计
     *
     * @param adcd 政区编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    RiverInfoByDuty selectRiverInfoByDuty(String adcd, String stm, String etm);

    /**
     * 最新河道水情信息导出
     *
     * @param adcd      政区编码 必录项 15位
     * @param stm       开始时间
     * @param etm       结束时间
     * @param stType    1 水文 2 山洪 3 气象
     * @param stnm      测站名称
     * @param dataFlag  是否展有数位的 1 显示有数据 0 无数据
     * @param warnFlag  是否显示超警戒
     * @param videoFlag 仅有视频
     * @param bsnm      流域
     * @param type      导出文件模板
     * @return
     */
    void exportRiverByConditon(int type, String adcd, String stm, String etm, List<String> stType, String stnm, boolean dataFlag, boolean warnFlag, String bsnm, boolean videoFlag);

    /**
     * 河道日旬月均值导出
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stList 测站编码集合
     * @param tmType 时间类型
     * @return
     */
    void exportDayAvgRiver(String stm, String etm, List<String> stList, String tmType);

    /**
     * 最大超警水位统计导出
     *
     * @param adcd 政区编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    void exportStRiverMaxWarn(String adcd, String stm, String etm);

    /**
     * 单站水位流量过程信息导出
     *
     * @param stcd 测站
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    void exportRiverTm(String stm, String etm, String stcd);

    /**
     * 根据测站编码查询最新施测时间的河道单站的大断面列表
     *
     * @param stcd 测站编码
     * @return
     */
    List<StRvsectB> getNewRiverDuanMianList(String stcd);

    /**
     * 统计政区下时间段内测站超警戒情况
     *
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<RiverWarnByAdcdTmVo> selectWarnStatisticsByAdcdTm(String adcd, String stm, String etm);

    /**
     * 临界告警
     *
     * @param query
     * @return
     */
    List<RiverNearWarnVo> selectNearWarnTemp(RiverNearWarnQuery query);

    /**
     * 多站河道水情最新预报
     *
     * @param adcd
     * @param bscd
     * @param stm
     * @param etm
     * @return
     */
    List<StForecastf> getForecastLatestTime(String adcd, String bscd, String stm, String etm);

    /**
     * 沙基沙堤未治理段列表
     *
     * @return
     */
    List<BnsRiverBaseDikeSVo> getRiverBaseDikeList();

    /**
     * 导出沙基沙堤未治理段列表
     */
    void exportRiverBaseDike(HttpServletResponse response);

    /**
     * 主要江河未达标堤段列表
     *
     * @return
     */
    List<BnsRiverDikeSVo> getRiverDikeList();

    /**
     * 导出主要江河未达标堤段列表
     */
    void exportRiverDike(HttpServletResponse response);

    /**
     * 防洪作战图根据河道测站编码查询河道信息
     *
     * @param query
     * @return
     */
    RiverZuoZhanTuVo getRiverForZuoZhanTu(RiverZuoZhanTuQuery query);

}
