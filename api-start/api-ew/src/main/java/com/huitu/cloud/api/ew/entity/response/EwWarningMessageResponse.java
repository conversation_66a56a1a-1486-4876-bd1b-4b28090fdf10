package com.huitu.cloud.api.ew.entity.response;

import com.huitu.cloud.api.ew.entity.base.EwReceiver;
import com.huitu.cloud.api.ew.entity.ext.EwWarningMessage;
import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * 山洪预警消息响应对象
 */
@ApiModel(value = "山洪预警消息响应对象")
public class EwWarningMessageResponse<T extends EwReceiver> extends EwWarningMessage {
    private static final long serialVersionUID = 1L;

    public EwWarningMessageResponse() {
        super();
    }

    public EwWarningMessageResponse(EwWarningMessage message) {
        super(message);
    }

    public EwWarningMessageResponse(EwWarningMessage message, List<T> receivers) {
        super(message);

        this.receivers = receivers;
    }

    /**
     * 接收人集合
     */
    private List<T> receivers;

    public List<T> getReceivers() {
        return receivers;
    }

    public void setReceivers(List<T> receivers) {
        this.receivers = receivers;
    }
}
