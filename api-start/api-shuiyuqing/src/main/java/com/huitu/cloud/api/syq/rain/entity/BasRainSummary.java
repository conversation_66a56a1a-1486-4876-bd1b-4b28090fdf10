package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 流域降雨汇总
 *
 * <AUTHOR>
 */
@ApiModel(value = "流域降雨汇总")
public class BasRainSummary implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流域编码")
    @TableField(value = "BAS_CODE")
    private String basCode;

    @ApiModelProperty(value = "流域名称")
    @TableField(value = "BAS_NAME")
    private String basName;

    @ApiModelProperty(value = "流域级别")
    @TableField(value = "BAS_LEVEL")
    private Integer basLevel;

    @ApiModelProperty(value = "是否主要江河")
    @TableField("IS_ZYJH")
    private String isZyjh;

    @ApiModelProperty(value = "是否中小河流")
    @TableField("IS_ZXHL")
    private String isZxhl;

    @ApiModelProperty(value = "流域面积")
    @TableField(value = "RIVER_AREA")
    private String riverArea;

    @ApiModelProperty(value = "父级流域编码")
    @TableField(value = "PBAS_CODE")
    private String pbasCode;

    @ApiModelProperty(value = "平均降雨")
    @TableField(value = "AVG_DRP")
    private String avgDrp;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "最大降雨")
    @TableField(value = "MAX_DRP")
    private String maxDrp;

    @ApiModelProperty(value = "下级流域集合")
    private List<BasRainSummary> children;

    public String getBasCode() {
        return basCode;
    }

    public void setBasCode(String basCode) {
        this.basCode = basCode;
    }

    public String getBasName() {
        return basName;
    }

    public void setBasName(String basName) {
        this.basName = basName;
    }

    public Integer getBasLevel() {
        return basLevel;
    }

    public void setBasLevel(Integer basLevel) {
        this.basLevel = basLevel;
    }

    public String getIsZyjh() {
        return isZyjh;
    }

    public void setIsZyjh(String isZyjh) {
        this.isZyjh = isZyjh;
    }

    public String getIsZxhl() {
        return isZxhl;
    }

    public void setIsZxhl(String isZxhl) {
        this.isZxhl = isZxhl;
    }

    public String getRiverArea() {
        return riverArea;
    }

    public void setRiverArea(String riverArea) {
        this.riverArea = riverArea;
    }

    public String getPbasCode() {
        return pbasCode;
    }

    public void setPbasCode(String pbasCode) {
        this.pbasCode = pbasCode;
    }

    public String getAvgDrp() {
        return avgDrp;
    }

    public void setAvgDrp(String avgDrp) {
        this.avgDrp = avgDrp;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getMaxDrp() {
        return maxDrp;
    }

    public void setMaxDrp(String maxDrp) {
        this.maxDrp = maxDrp;
    }

    public List<BasRainSummary> getChildren() {
        return children;
    }

    public void setChildren(List<BasRainSummary> children) {
        this.children = children;
    }
}
