<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwWarningStatusDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getAllList" resultType="com.huitu.cloud.api.ew.entity.base.EwWarningStatus">
        SELECT STATUS_ID, STATUS_NAME, ALIAS_NAME, SHORT_NAME
        FROM EW_WARNING_STATUS
        ORDER BY STATUS_ID ASC
    </select>
</mapper>