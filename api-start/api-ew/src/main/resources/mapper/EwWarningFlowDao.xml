<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwWarningFlowDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getFlowList" resultType="com.huitu.cloud.api.ew.entity.response.EwWarningFlowResponse">
        SELECT WARN_ID,
               WARN_GRADE_ID,
               WARN_STATUS_ID,
               B.STATUS_NAME WARN_STATUS_NAME,
               B.ALIAS_NAME  WARN_STATUS_ALIAS_NAME,
               B.SHORT_NAME  WARN_STATUS_SHORT_NAME,
               REMARK,
               CREATE_BY,
               CREATE_TIME
        FROM EW_WARNING_FLOW A
                 LEFT JOIN EW_WARNING_STATUS B ON B.STATUS_ID = WARN_STATUS_ID
        WHERE WARN_ID = #{warnId}
        ORDER BY CREATE_TIME ASC
    </select>
    <insert id="insert">
        INSERT INTO EW_WARNING_FLOW(WARN_ID, WARN_GRADE_ID, WARN_STATUS_ID, REMARK, CREATE_BY, CREATE_TIME)
        SELECT WARN_ID, WARN_GRADE_ID, #{map.warnStatusId}, #{map.remark}, #{map.createBy}, GETDATE()
        FROM EW_WARNING_RECORD
        WHERE WARN_ID = #{map.warnId}
    </insert>
    <insert id="batchInsert">
        INSERT INTO EW_WARNING_FLOW(WARN_ID, WARN_GRADE_ID, WARN_STATUS_ID, REMARK, CREATE_BY, CREATE_TIME)
        SELECT WARN_ID, WARN_GRADE_ID, #{map.warnStatusId}, #{map.remark}, #{map.createBy}, GETDATE()
        FROM EW_WARNING_RECORD
        WHERE CHARINDEX(WARN_ID, #{map.warnIds}) > 0
    </insert>
    <insert id="manualConfirm">
        MERGE INTO EW_WARNING_FLOW AS T USING
        (SELECT WARN_ID, WARN_GRADE_ID, 21 WARN_STATUS_ID FROM EW_WARNING_RECORD
        WHERE WARN_ID IN (<foreach collection="warnIds" item="warnId" separator=",">#{warnId}</foreach>)
        ) S ON T.WARN_ID = S.WARN_ID AND T.WARN_GRADE_ID = S.WARN_GRADE_ID AND T.WARN_STATUS_ID = S.WARN_STATUS_ID
        WHEN NOT MATCHED THEN
        INSERT (WARN_ID, WARN_GRADE_ID, WARN_STATUS_ID, CREATE_BY, CREATE_TIME)
        VALUES (S.WARN_ID, S.WARN_GRADE_ID, S.WARN_STATUS_ID, #{confirmBy}, GETDATE());
    </insert>
</mapper>