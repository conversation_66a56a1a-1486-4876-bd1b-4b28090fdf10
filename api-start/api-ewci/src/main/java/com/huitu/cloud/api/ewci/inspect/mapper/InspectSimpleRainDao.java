package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectSimpleRain;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 简易雨量站
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectSimpleRainDao {



    /**
     * 添加 简易雨量站
     *
     * @param entity
     * @return
     */
    int insertInspectSimpleRain(BnsInspectSimpleRain entity);

    /**
     * 修改  简易雨量站
     *
     * @param entity
     * @return
     */
    int updateInspectSimpleRain(BnsInspectSimpleRain entity);

    /**
     * 删除 简易雨量站
     *
     * @return
     */
    int deleteInspectSimpleRain(@Param("inspectId") String inspectId);

    List<BnsInspectSimpleRain> getInspectSimpleRainList(@Param("year") String year, @Param("tadcd") String tadcd);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

