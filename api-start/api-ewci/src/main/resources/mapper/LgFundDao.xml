<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ledger.mapper.LgFundDao">
    <!--    <cache type="com.huitu.cloud.config.RedisCache"/>-->
    <select id="getPageList" resultType="com.huitu.cloud.api.ewci.ledger.entity.LgFund" useCache="false">
        SELECT
        *
        FROM
        (
        SELECT DISTINCT
        STUFF(
        (
        SELECT
        ',' + bb.adnm
        FROM
        (
        SELECT
        a.PROJECT_ID,
        A.ADCD,
        A.FUNDING_BATCH,
        A.USAGE_DIRECTION,
        A.IMPLEMENTATION_YEAR,
        ad.adnm
        FROM
        LEDGER_REL_PROJECT_FUND a
        LEFT JOIN LEDGER_CONSTRUCTION b ON a.PROJECT_ID = b.PROJECT_ID
        LEFT JOIN LEDGER_PROJECT c ON c.PROJECT_ID = b.PROJECT_ID
        LEFT JOIN BSN_ADCD_B ad ON c.ADCD = ad.adcd
        WHERE
        a.ADCD = #{map.adcdAll}
        ) bb
        WHERE
        b.ADCD = bb.ADCD
        AND b.FUNDING_BATCH = bb.FUNDING_BATCH
        AND b.USAGE_DIRECTION = bb.USAGE_DIRECTION
        AND b.IMPLEMENTATION_YEAR = bb.IMPLEMENTATION_YEAR FOR XML PATH ('')
        ),
        1,
        1,
        ''
        ) AS adcdNames,
        A.ADCD,
        A.FUNDING_BATCH,
        A.USAGE_DIRECTION,
        A.IMPLEMENTATION_YEAR,
        A.TOTAL_FUNDS_ALLOCATED,
        A.CENTRAL_SUBSIDY_FUNDS,
        A.LOCAL_MATCHING_FUNDS,
        A.TOTAL_INVESTMENT_COMPLETED,
        A.CENTRAL_SUBSIDY_INVESTMENT,
        A.LOCAL_MATCHING_INVESTMENT,
        A.TOTAL_SETTLED_FUNDS,
        A.CENTRAL_SUBSIDY_SETTLED,
        A.LOCAL_MATCHING_SETTLED,
        A.TOTAL_PAID_FUNDS,
        A.CENTRAL_SUBSIDY_PAID,
        A.LOCAL_MATCHING_PAID,
        A.PERFORMANCE_TARGET_RESERVOIR,
        A.PERFORMANCE_TARGET_DIKE,
        A.PERFORMANCE_TARGET_GATE,
        A.PERFORMANCE_TARGET_HYDROLOGICAL,
        A.PERFORMANCE_TARGET_OTHER,
        CC.performanceTargetReservoirR,
        CC.performanceTargetDikeR,
        CC.performanceTargetGateR,
        CC.performanceTargetHydrologicalR,
        CC.performanceTargetOtherR,
        CC.totalFundsAllocatedR,
        CC.centralSubsidyFundsR,
        CC.localMatchingFundsR,
        CC.totalInvestmentCompletedR,
        CC.centralSubsidyInvestmentR,
        CC.localMatchingInvestmentR,
        CC.totalSettledFundsR,
        CC.centralSubsidySettledR,
        CC.localMatchingSettledR,
        CC.totalPaidFundsR,
        CC.centralSubsidyPaidR,
        CC.localMatchingPaidR,
        CC.performanceTargetReservoirPlan,
        CC.performanceTargetEmbankmentPlan,
        CC.performanceTargetCulvertPlan,
        CC.performanceTargetHydroPlan,
        CC.performanceTargetOtherPlan,
        (
        SELECT
        COUNT(1)
        FROM
        LEDGER_REL_PROJECT_FUND
        WHERE
        A.ADCD = ADCD
        AND A.USAGE_DIRECTION = USAGE_DIRECTION
        AND A.IMPLEMENTATION_YEAR = IMPLEMENTATION_YEAR
        AND A.FUNDING_BATCH = FUNDING_BATCH
        ) AS projectCount,
        A.CREATED,
        A.UPDATED,
        A.REMARKS
        FROM
        LEDGER_FUND A
        LEFT JOIN LEDGER_REL_PROJECT_FUND B ON A.ADCD = B.ADCD
        AND A.USAGE_DIRECTION = B.USAGE_DIRECTION
        AND A.IMPLEMENTATION_YEAR = B.IMPLEMENTATION_YEAR
        AND A.FUNDING_BATCH = B.FUNDING_BATCH
        LEFT JOIN (
        SELECT
        A.ADCD,
        A.FUNDING_BATCH,
        A.USAGE_DIRECTION,
        A.IMPLEMENTATION_YEAR,
        SUM(c.PERFORMANCE_TARGET_RESERVOIR) AS performanceTargetReservoirR,
        SUM(c.PERFORMANCE_TARGET_DIKE) AS performanceTargetDikeR,
        SUM(c.PERFORMANCE_TARGET_GATE) AS performanceTargetGateR,
        SUM(c.PERFORMANCE_TARGET_HYDROLOGICAL) AS performanceTargetHydrologicalR,
        SUM(c.PERFORMANCE_TARGET_OTHER) AS performanceTargetOtherR,
        SUM(c.TOTAL_FUNDS_ALLOCATED) AS totalFundsAllocatedR,
        SUM(c.CENTRAL_SUBSIDY_FUNDS) AS centralSubsidyFundsR,
        SUM(c.LOCAL_MATCHING_FUNDS) AS localMatchingFundsR,
        SUM(c.TOTAL_INVESTMENT_COMPLETED) AS totalInvestmentCompletedR,
        SUM(c.CENTRAL_SUBSIDY_INVESTMENT) AS centralSubsidyInvestmentR,
        SUM(c.LOCAL_MATCHING_INVESTMENT) AS localMatchingInvestmentR,
        SUM(c.TOTAL_SETTLED_FUNDS) AS totalSettledFundsR,
        SUM(c.CENTRAL_SUBSIDY_SETTLED) AS centralSubsidySettledR,
        SUM(c.LOCAL_MATCHING_SETTLED) AS localMatchingSettledR,
        SUM(c.TOTAL_PAID_FUNDS) AS totalPaidFundsR,
        SUM(c.CENTRAL_SUBSIDY_PAID) AS centralSubsidyPaidR,
        SUM(c.LOCAL_MATCHING_PAID) AS localMatchingPaidR,
        SUM(d.PERFORMANCE_TARGET_RESERVOIR_PLAN) AS performanceTargetReservoirPlan,
        SUM(d.PERFORMANCE_TARGET_EMBANKMENT_PLAN) AS performanceTargetEmbankmentPlan,
        SUM(d.PERFORMANCE_TARGET_CULVERT_PLAN) AS performanceTargetCulvertPlan,
        SUM(d.PERFORMANCE_TARGET_HYDRO_PLAN) AS performanceTargetHydroPlan,
        SUM(d.PERFORMANCE_TARGET_OTHER_PLAN) AS performanceTargetOtherPlan
        FROM
        LEDGER_REL_PROJECT_FUND a
        LEFT JOIN LEDGER_FUND b ON a.ADCD = b.ADCD
        AND A.FUNDING_BATCH = B.FUNDING_BATCH
        AND A.USAGE_DIRECTION = B.USAGE_DIRECTION
        AND A.IMPLEMENTATION_YEAR = B.IMPLEMENTATION_YEAR
        AND b.ADCD = #{map.adcdAll}
        LEFT JOIN LEDGER_CONSTRUCTION c ON c.PROJECT_ID = a.PROJECT_ID
        LEFT JOIN LEDGER_PROJECT d ON c.PROJECT_ID = d.PROJECT_ID
        GROUP BY
        A.ADCD,
        A.FUNDING_BATCH,
        A.USAGE_DIRECTION,
        A.IMPLEMENTATION_YEAR
        ) CC ON CC.ADCD = A.ADCD
        AND A.USAGE_DIRECTION = CC.USAGE_DIRECTION
        AND A.IMPLEMENTATION_YEAR = CC.IMPLEMENTATION_YEAR
        AND A.FUNDING_BATCH = CC.FUNDING_BATCH
        LEFT JOIN LEDGER_CONSTRUCTION FF ON FF.PROJECT_ID = B.PROJECT_ID
        WHERE 1=1
        <if test="map.adcd != null and map.adcd !=''">
            and left(FF.ADCD,#{map.level})=#{map.adcd}
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(FF.ADCD, 6) NOT IN ('220581')
        </if>
        ) AA
        WHERE 1=1
        <if test="map.adcdAll != null and map.adcdAll !=''">
            and AA.ADCD = #{map.adcdAll}
        </if>
        <if test="map.fundingBatch!=null and map.fundingBatch!= ''">
            and AA.FUNDING_BATCH=#{map.fundingBatch}
        </if>
        <if test="map.implementationYear!=null and map.implementationYear!= ''">
            and AA.IMPLEMENTATION_YEAR=#{map.implementationYear}
        </if>
        <if test="map.usageDirection!=null and map.usageDirection!= ''">
            and AA.USAGE_DIRECTION=#{map.usageDirection}
        </if>
        ORDER BY
        AA.UPDATED DESC,
        AA.CREATED DESC
    </select>

    <update id="updateLgRemarks">
        UPDATE LEDGER_FUND
        SET REMARKS = #{map.remarks}
        WHERE
            ADCD = #{map.adcd}
            AND FUNDING_BATCH = #{map.fundingBatch}
            AND USAGE_DIRECTION = #{map.usageDirection}
            AND IMPLEMENTATION_YEAR = #{map.implementationYear}
    </update>

    <delete id="deleteDelFlag">
        DELETE LEDGER_FUND
        WHERE
            ADCD = #{map.adcd}
          AND FUNDING_BATCH = #{map.fundingBatch}
          AND USAGE_DIRECTION = #{map.usageDirection}
          AND IMPLEMENTATION_YEAR = #{map.implementationYear}
    </delete>
</mapper>
