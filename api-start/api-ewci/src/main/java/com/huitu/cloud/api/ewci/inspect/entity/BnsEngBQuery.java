package com.huitu.cloud.api.ewci.inspect.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.Year;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 工程下拉参数
 */
@ApiModel(value = "工程下拉参数")
public class BnsEngBQuery implements Serializable {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "检查日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inspDate;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Date getInspDate() {
        return inspDate;
    }

    public void setInspDate(Date inspDate) {
        this.inspDate = inspDate;
    }
    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("year", String.valueOf(Year.from(getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())));
        return params;
    }
}
