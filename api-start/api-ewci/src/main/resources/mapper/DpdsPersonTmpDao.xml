<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.DpdsPersonTmpDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="batchInsert">
        INSERT INTO BNS_DPDSPERSON_TMP (ID, NO, XADCD, ADNM, DPDS_NAME, PROBLEM, MEASURE, XZ_REALNM, XZ_MOBILE,
        JS_REALNM, JS_MOBILE)
        SELECT T.ID, T.NO, T.XADCD, T.ADNM, T.DPDS_NAME, T.PROBLEM, T.MEASURE, T.XZ_REALNM, T.XZ_MOBILE, T.JS_REALNM,
        T.J<PERSON>_MOBILE FROM (VALUES
        <foreach collection="map.list" item="item" separator=",">
            (NEWID(), #{map.batchNo}, #{map.xadcd}, #{item.adnm}, #{item.dpdsName}, #{item.problem}, #{item.measure},
            #{item.xzRealnm}, #{item.xzMobile}, #{item.jsRealnm}, #{item.jsMobile})
        </foreach>) AS T(ID, NO, XADCD, ADNM, DPDS_NAME, PROBLEM, MEASURE, XZ_REALNM, XZ_MOBILE, JS_REALNM, JS_MOBILE)
    </insert>
</mapper>
