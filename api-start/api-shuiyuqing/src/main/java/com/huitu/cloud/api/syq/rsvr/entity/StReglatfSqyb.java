package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 多站水库水情预报返回实体类
 * <AUTHOR>
 */
public class StReglatfSqyb extends  StReglatF{

    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "站类")
    private String sttp;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "规模 1：小(二)型、2：小(一)型、3：中型、4：大(二)型、 5：大(一)型、 9：其它。")
    private String rsvrtp;
    @ApiModelProperty(value = "告警状态 1为超汛限、2为超正常高，告警时数据行字体使用红色展示")
    private String wnstatus;
    @ApiModelProperty(value = "坝顶高程")
    @TableField("DAMEL")
    private BigDecimal damel;
    @ApiModelProperty(value = "正常高水位")
    @TableField("NORMZ")
    private BigDecimal normz;
    @ApiModelProperty(value = "死水位")
    @TableField("DDZ")
    private BigDecimal ddz;
    @ApiModelProperty(value = "历史最高库水位")
    @TableField("HHRZ")
    private BigDecimal hhrz;

    @ApiModelProperty(value = "汛限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;
    @ApiModelProperty(value = "超汛限水位")
    private BigDecimal rzfsltdz;
    @ApiModelProperty(value = "超正常高水位")
    private BigDecimal rznormz;

    @ApiModelProperty(value = "汛限库容")
    private BigDecimal fsltdw;
    @ApiModelProperty(value = "超汛限库容")
    private BigDecimal wfsltdw;
    @ApiModelProperty(value = "预报入流")
    private BigDecimal q;
    @ApiModelProperty(value = "预报来水量")
    private BigDecimal inw;

    @ApiModelProperty(value = "偏移经度")
    @TableField("PLGTD")
    private BigDecimal plgtd;
    @ApiModelProperty(value = "偏移纬度")
    @TableField("PLTTD")
    private BigDecimal plttd;

    @ApiModelProperty(value = "是否是开敞式溢洪道")
    @TableField("OSFLG")
    private String osflg;

    @ApiModelProperty(value = "起始水位")
    @TableField("BGRZ")
    private BigDecimal bgrz;
    @ApiModelProperty(value = "起始蓄水量")
    @TableField("BGW")
    private BigDecimal bgw;
    @ApiModelProperty(value = "起始入库")
    @TableField("BGINQ")
    private BigDecimal bginq;
    @ApiModelProperty(value = "起始出库")
    @TableField("BGOTQ")
    private BigDecimal bgotq;

    @ApiModelProperty(value = "发生时间")
    @TableField("TM")
    private LocalDateTime tm;


    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public BigDecimal getQ() {
        return q;
    }

    public void setQ(BigDecimal q) {
        this.q = q;
    }

    public BigDecimal getInw() {
        return inw;
    }

    public void setInw(BigDecimal inw) {
        this.inw = inw;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public BigDecimal getRznormz() {
        return rznormz;
    }

    public void setRznormz(BigDecimal rznormz) {
        this.rznormz = rznormz;
    }

    public BigDecimal getFsltdw() {
        return fsltdw;
    }

    public void setFsltdw(BigDecimal fsltdw) {
        this.fsltdw = fsltdw;
    }

    public BigDecimal getWfsltdw() {
        return wfsltdw;
    }

    public void setWfsltdw(BigDecimal wfsltdw) {
        this.wfsltdw = wfsltdw;
    }

    public BigDecimal getDamel() {
        return damel;
    }

    public void setDamel(BigDecimal damel) {
        this.damel = damel;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }

    public BigDecimal getDdz() {
        return ddz;
    }

    public void setDdz(BigDecimal ddz) {
        this.ddz = ddz;
    }

    public BigDecimal getHhrz() {
        return hhrz;
    }

    public void setHhrz(BigDecimal hhrz) {
        this.hhrz = hhrz;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getRsvrtp() {
        return rsvrtp;
    }

    public void setRsvrtp(String rsvrtp) {
        this.rsvrtp = rsvrtp;
    }

    public String getWnstatus() {
        return wnstatus;
    }

    public void setWnstatus(String wnstatus) {
        this.wnstatus = wnstatus;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    public String getOsflg() {
        return osflg;
    }

    public void setOsflg(String osflg) {
        this.osflg = osflg;
    }

    public BigDecimal getBgrz() {
        return bgrz;
    }

    public void setBgrz(BigDecimal bgrz) {
        this.bgrz = bgrz;
    }

    public BigDecimal getBgw() {
        return bgw;
    }

    public void setBgw(BigDecimal bgw) {
        this.bgw = bgw;
    }

    public BigDecimal getBginq() {
        return bginq;
    }

    public void setBginq(BigDecimal bginq) {
        this.bginq = bginq;
    }

    public BigDecimal getBgotq() {
        return bgotq;
    }

    public void setBgotq(BigDecimal bgotq) {
        this.bgotq = bgotq;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

}
