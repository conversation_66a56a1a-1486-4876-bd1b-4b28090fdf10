package com.huitu.cloud.api.datamanager.manualreport.controler;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.datamanager.manualreport.entity.BsnManualReport;
import com.huitu.cloud.api.datamanager.manualreport.entity.ManualReportVo;
import com.huitu.cloud.api.datamanager.manualreport.entity.QueryManualReport;
import com.huitu.cloud.api.datamanager.manualreport.service.ManualReportService;
import com.huitu.cloud.api.datamanager.ststop.entity.StStopQo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 人工报讯 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-23
 */

@RestController
@Api(tags = "人工报讯维护")
@RequestMapping("/api/datamanger/manualreport")
public class ManualReportResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "9981980f-5a91-489e-93c6-d72953968172";
    }
    @Override
    public String getVersion() {
        return "1.0";
    }
    @Autowired
    private ManualReportService reportService;

    @ApiOperation(value = "人工报讯站点记录查询",notes="人工报讯站点记录查询")
    @GetMapping(value = "select-manual-report-st-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  required = true,dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ManualReportVo>>> getManualReportStInfo(@RequestParam String  adcd) throws Exception {
        List<ManualReportVo> list=reportService.getManualReportStInfo(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "人工报讯站点记录添加",notes="人工报讯站点记录添加(传站码，用户和屏蔽要素（表名）即可)")
    @PostMapping(value = "add")
    public ResponseEntity<SuccessResponse<String>> addManualReportSt(@RequestBody StStopQo entity) throws Exception {
        String flag = reportService.addManualReportSt(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "关闭站点人工报讯设置",notes="关闭站点人工报讯设置")
    @GetMapping(value = "close")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码",  required = true,dataType = "String"),
            @ApiImplicitParam(name = "tbnm", value = "表名",  required = true,dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<String>> closeManualReportSt(@RequestParam String stcd,@RequestParam String tbnm) throws Exception {
        String flag = reportService.closeManualReportSt(stcd,tbnm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "人工报讯",notes="人工报讯")
    @PostMapping(value = "manualreport")
    public ResponseEntity<SuccessResponse<String>> manualReport(@RequestBody QueryManualReport entity) throws Exception {
        String flag = reportService.manualReport(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }
}
