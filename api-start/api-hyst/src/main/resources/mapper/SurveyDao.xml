<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.hyst.survey.mapper.SurveyDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getSurveyPage" resultType="com.huitu.cloud.api.hyst.survey.entity.SurveyVo">
        SELECT P.HYST_CODE,
        P.HYST_NAME,
        P.ADCD,
        P.ADNM,
        P.HYST_TP,
        P.ECO_ONLINE,
        P.ECO_FLOW,
        P.ECOFLOWNUM,
        P.APP_ECO_FLOW,
        P.REC_TIME,
        P.ACQTIME,
        P.ASS_PER,
        P.ASS
        FROM (SELECT
        A.HYST_CODE,
        B.HYST_NAME,
        B<PERSON>,
        <PERSON><PERSON>H<PERSON>_TP,
        D<PERSON>,
        <PERSON><PERSON>_<PERSON>L<PERSON>,
        <PERSON><PERSON>,
        C.<PERSON>,
        B.APP_ECO_FLOW,
        A.REC_TIME,
        C.ACQTIME,
        A.ASS_PER,
        CASE WHEN E.EXEMPT_START &lt;= #{map.etm} AND E.EXEMPT_END >=
        #{map.etm} THEN '0' ELSE '1' END ASS
        FROM
        HYST_ATT_EAI A
        LEFT JOIN HYST_ATT_BASE B ON A.HYST_CODE = B.HYST_CODE
        LEFT JOIN
        (SELECT ROW_NUMBER () OVER ( PARTITION BY HYST_CODE ORDER BY REC_TIME DESC ) AS NO,HYST_CODE ,ECO_FLOW AS
        ECOFLOWNUM,REC_TIME as ACQTIME FROM HYST_ATT_EBF) C ON
        A.HYST_CODE =
        C.HYST_CODE AND C.NO = 1
        LEFT JOIN MDT_ADCDINFO_B D ON B.ADCD = D.ADCD
        LEFT JOIN HYST_ATT_EXEMPT E ON A.HYST_CODE = E.HYST_CODE AND #{map.etm} >= E.EXEMPT_START
        AND #{map.etm} &lt;= E.EXEMPT_END
        WHERE A.ASS_PER = #{map.assPer} AND A.ASS_START = #{map.stm}
        <if test="map.hystName !=null and map.hystName !=''">
            AND CHARINDEX(#{map.hystName},B.HYST_NAME) >0
        </if>
        <if test="map.ecoOnline !=null and map.ecoOnline !=''">
            AND A.ECO_ONLINE = #{map.ecoOnline}
        </if>
        <if test="map.ecoflow !=null and map.ecoflow !=''">
            AND A.ECO_FLOW = #{map.ecoflow} AND A.ECO_ONLINE = '1'
        </if>
        ) P
        WHERE 1=1
        <if test="map.ass !=null and map.ass !=''">
            AND P.ASS = #{map.ass}
        </if>
        ORDER BY P.ECOFLOWNUM DESC
    </select>
    <select id="getDetailsList" resultType="com.huitu.cloud.api.hyst.survey.entity.SurveyVo">
        SELECT A.HYST_CODE,
               B.HYST_NAME,
               B.ADCD,
               B.HYST_TP,
               D.ADNM,
               A.ECO_ONLINE,
               A.ECO_FLOW,
               A.REC_TIME,
               CASE
                   WHEN E.EXEMPT_START &lt;= A.ASS_START AND E.EXEMPT_END >= A.ASS_END THEN '0'
                   ELSE '1' END ASS
        FROM HYST_ATT_EAI A
                 LEFT JOIN HYST_ATT_BASE B ON A.HYST_CODE = B.HYST_CODE
                 LEFT JOIN MDT_ADCDINFO_B D ON B.ADCD = D.ADCD
                 LEFT JOIN HYST_ATT_EXEMPT E ON A.HYST_CODE = E.HYST_CODE AND A.ASS_START >= E.EXEMPT_START AND
                                                A.ASS_END &lt;= E.EXEMPT_END
        WHERE A.HYST_CODE = #{map.hystCode}
          AND A.ASS_PER = #{map.assPer}
          AND A.ASS_START >= #{map.stm}
        ORDER BY A.REC_TIME DESC
    </select>
    <select id="getAssPer" resultType="java.lang.String">
        SELECT ASS_PER
        FROM HYST_ATT_EAI
        WHERE HYST_CODE = #{hystCode}
          AND REC_TIME = #{recTime}
    </select>
    <select id="getHystStats" resultType="com.huitu.cloud.api.hyst.survey.entity.HystStats">
        SELECT COUNT(Q.HYST_CODE)                   TOTALCOUNT,
               SUM(Q.ONLINE)                        ONLINECOUNT,
               (COUNT(Q.HYST_CODE) - SUM(Q.ONLINE)) OUTLINECOUNT,
               SUM(Q.FLOW)                          UPTOSTANDARDCOUNT,
               (SUM(Q.ONLINE) - SUM(Q.FLOW))        NOTUPTOSTANDARDCOUNT,
               SUM(Q.EXEMPT)                        EXEMPTCOUNT,
               (COUNT(Q.HYST_CODE) - SUM(Q.EXEMPT)) EXAMINECOUNT
        FROM (select A.HYST_CODE,
                     CASE WHEN A.ECO_ONLINE = '1' THEN 1 ELSE 0 END                    ONLINE,
                     CASE WHEN A.ECO_ONLINE = '1' AND A.ECO_FLOW = '1' THEN 1 ELSE 0 END FLOW,
                     CASE WHEN E.HYST_CODE IS NOT NULL THEN 1 ELSE 0 END               EXEMPT
              from HYST_ATT_EAI A
                       LEFT JOIN
                   HYST_ATT_EXEMPT E
                   ON A.HYST_CODE = E.HYST_CODE AND A.ASS_END >= E.EXEMPT_START AND A.ASS_END &lt;= E.EXEMPT_END
              WHERE A.ASS_PER = #{map.assPer}
                AND A.ASS_END = #{map.stm}) Q
    </select>

</mapper>