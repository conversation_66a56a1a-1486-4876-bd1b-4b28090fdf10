package com.huitu.cloud.api.common.liuyu.controler;




import com.huitu.cloud.api.common.liuyu.entity.BsnBasB;
import org.springframework.web.bind.annotation.RequestMapping;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import org.springframework.http.ResponseEntity;


import com.huitu.cloud.api.common.liuyu.service.BsnRvinfobService;
import com.huitu.cloud.api.common.liuyu.entity.BsnRvinfoB;
/**
 * <p>
 * 河流流域常量表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-06
 */

@RestController
@Api(tags = "流域信息")
@RequestMapping("/api/common/liuyu")
    public class BsnRvinfobResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
            return "4125f001-3c3e-4f68-bee3-c9a561d8d63d";
            }
    @Override
    public String getVersion() {
            return "1.0";
            }
    @Autowired
    private BsnRvinfobService baseService;


    @ApiOperation(value = "查询流域信息",notes="查询所有流域信息，以树形结构数据返回")
    @GetMapping(value = "get-river-tree")
    public ResponseEntity<SuccessResponse<BsnBasB>> getBsnRvinfoTree() throws Exception {
        BsnBasB rvinfoBean =baseService.getRiverTree();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", rvinfoBean));
    }
    @ApiOperation(value = "查询当前政区，流域以及下级测站",notes="根据当前政区编码查询下级测站，拼接到流域上，以树形结构数据返回")
    @GetMapping(value = "get-river-st-tree")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码 15位", required = true, dataType = "String"),
            @ApiImplicitParam(name = "sttp", value = "测站类型 ZZ：河道 PP：雨量 RR ：水库    ", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<BsnBasB>> getBsnRvninfoTreeAndSt(@RequestParam String adcd,@RequestParam String sttp) throws Exception {
        BsnBasB rvinfo =baseService.getRiverAndStTree(adcd,sttp);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", rvinfo));
    }

}







