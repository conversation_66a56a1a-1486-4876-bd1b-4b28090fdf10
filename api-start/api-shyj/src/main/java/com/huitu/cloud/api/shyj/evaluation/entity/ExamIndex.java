package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 预警反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@TableName("EXAM_INDEX")
@ApiModel(value="ExamIndex对象", description="考核指标")
public class ExamIndex implements Serializable {
    @ApiModelProperty(value = "考核类型")
    @TableId(value = "EXAM_TYPE")
    private String examType;

    @ApiModelProperty(value = "考核项")
    @TableField("EXAM_OPTION")
    private String examOption;

    @ApiModelProperty(value = "评分占比")
    @TableField("SCORE_RATIO")
    private double scoreRatio;


    public String getExamType() {
        return examType;
    }
    public void setExamType(String examType) {
        this.examType = examType;
    }

    public String getExamOption() {
        return examOption;
    }
    public void setExamOption(String examOption) {
        this.examOption = examOption;
    }

    public double getScoreRatio() {
        return scoreRatio;
    }
    public void setScoreRatio(double scoreRatio) {
        this.scoreRatio = scoreRatio;
    }
}