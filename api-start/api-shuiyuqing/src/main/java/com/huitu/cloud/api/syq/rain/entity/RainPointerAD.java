package com.huitu.cloud.api.syq.rain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ApiModel(value="雨情测站村庄关系")
public class RainPointerAD implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "行政编码")
    @TableField(value = "ADCD")
    private String adcd;
    @ApiModelProperty(value = "政区名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "防治区类型(1一般防治区2重点防治区)")
    @TableField(value = "PREVTP")
    private String prevtp;

    @ApiModelProperty(value = "经度")
    @TableField(value = "LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField(value = "LTTD")
    private BigDecimal lttd;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPrevtp() {
        return prevtp;
    }

    public void setPrevtp(String prevtp) {
        this.prevtp = prevtp;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }
}
