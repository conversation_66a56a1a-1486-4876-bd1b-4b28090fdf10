package com.huitu.cloud.api.syq.rain.entity;


import com.huitu.cloud.entity.PageBean;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class RainTmsQo extends PageBean {
    @ApiModelProperty(value = "测站归属类型 1、水文，2、山洪  3、气象")
    private List<String> stType;
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "场次降雨归属政区编码",required = true)
    private String tmsad;
    @ApiModelProperty(value = "场次降雨编码",required = true)
    private String tmsid;
    @ApiModelProperty(value = "流域名称")
    private String bsnm;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "降雨阈值范围,例：“1-10”")
    private String threshold;

    public List<String> getStType() {
        return stType;
    }

    public void setStType(List<String> stType) {
        this.stType = stType;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getTmsid() {
        return tmsid;
    }

    public void setTmsid(String tmsid) {
        this.tmsid = tmsid;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getThreshold() {
        return threshold;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }

    public String getTmsad() {
        return tmsad;
    }

    public void setTmsad(String tmsad) {
        this.tmsad = tmsad;
    }
}
