<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rsvr.mapper.RsvrDischargeFloodDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getList" statementType="CALLABLE"
            resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrDischargeFloodDetail">
        {CALL proc_get_rsvrdata_discharge_flood(#{adcd, mode=IN}, #{rsvrtps, mode=IN}, #{stm, mode=IN}, #{etm, mode=IN})}
    </select>
</mapper>
