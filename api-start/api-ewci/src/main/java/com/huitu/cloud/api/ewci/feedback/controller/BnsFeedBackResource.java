package com.huitu.cloud.api.ewci.feedback.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBack;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackPic;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackQuery;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackVo;
import com.huitu.cloud.api.ewci.feedback.service.BnsFeedBackService;
import com.huitu.cloud.api.ewci.warn.entity.BnsWarnInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 意见反馈信息
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "意见反馈信息")
@RequestMapping("/api/ewci/feedback/info")
public class BnsFeedBackResource extends AbstractApiResource implements ApiResource {

    public BnsFeedBackResource(BnsFeedBackService baseService) {
        this.baseService = baseService;
    }

    @Override
    public String getUuid() {
        return "6d9bd151-6d58-693b-3d55-1e0d577ff0c2";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final BnsFeedBackService baseService;


    @ApiOperation(value = "分页查询", notes = "作者：zyj")
    @PostMapping("select-info-page")
    public ResponseEntity<SuccessResponse<Page<BnsFeedBackVo>>> getPage(@Validated @RequestBody BnsFeedBackQuery query) {
        IPage<BnsFeedBackVo> page = baseService.getPage(query);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", page));
    }

    @ApiOperation(value = "添加", notes = "作者：zyj")
    @PostMapping(value = "insert")
    public ResponseEntity<SuccessResponse<Integer>> insertFeedBack(@RequestBody BnsFeedBack entity) throws Exception {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.insertFeedBack(entity)));
    }

    @ApiOperation(value = "处理", notes = "作者：zyj")
    @PostMapping(value = "handle")
    public ResponseEntity<SuccessResponse<Integer>> handleFeedBack(@RequestBody BnsFeedBack entity) throws Exception {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.handleFeedBack(entity)));
    }

    @ApiOperation(value = "意见反馈详情查看图片", notes = "作者：zyj")
    @GetMapping("select-feedback-pic-list/{id}")
    public ResponseEntity<SuccessResponse<List<BnsFeedBackPic>>> getFeedBackPicList(@PathVariable("id") String id) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getFeedBackPicList(id)));
    }
}
