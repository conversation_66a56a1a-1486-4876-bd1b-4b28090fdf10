package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * <p>
 * 计算单元（小流域）信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-09
 */
@TableName("BNS_IA_UNITWS")
@ApiModel(value="BnsIaUnitws对象", description="计算单元（小流域）信息表")
public class BnsIaUnitws  extends Model<BnsIaUnitws> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "流域代码")
    @TableId(value = "WSCD", type = IdType.NONE)
    private String wscd;

    @ApiModelProperty(value = "流域名称")
    @TableId(value = "WSNM", type = IdType.NONE)
    private String wsnm;

    @ApiModelProperty(value = "面积(km2)")
    @TableId(value = "AREA", type = IdType.NONE)
    private String area;

    @ApiModelProperty(value = "主沟道长度(km)")
    @TableId(value = "CHLENGTH", type = IdType.NONE)
    private String chlength;

    @ApiModelProperty(value = "主沟道比降(‰)")
    @TableId(value = "CHPERCENT", type = IdType.NONE)
    private String chpercent;

    @ApiModelProperty(value = "植被覆盖率(%)")
    @TableId(value = "VEGCOVERAGE", type = IdType.NONE)
    private String vegcoverage;

    @ApiModelProperty(value = "土壤类型")
    @TableId(value = "SJY", type = IdType.NONE)
    private String sjy;

    @ApiModelProperty(value = "设计暴雨计算方法")
    @TableId(value = "DESNTB", type = IdType.NONE)
    private String desntb;

    @ApiModelProperty(value = "设计洪水计算方法")
    @TableId(value = "SDTDTB", type = IdType.NONE)
    private String sdtdtb;

    @ApiModelProperty(value = "预警指标计算方法")
    @TableId(value = "COMMENTS", type = IdType.NONE)
    private String comments;

    @ApiModelProperty(value = "汇流时间(h)")
    @TableId(value = "FLOWTM", type = IdType.NONE)
    private String flowtm;

    @ApiModelProperty(value = "备注")
    @TableId(value = "WRULETB", type = IdType.NONE)
    private String wruletb;

    @ApiModelProperty(value = "时间戳")
    @TableField("TS")
    private LocalDateTime ts;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getChlength() {
        return chlength;
    }

    public void setChlength(String chlength) {
        this.chlength = chlength;
    }

    public String getChpercent() {
        return chpercent;
    }

    public void setChpercent(String chpercent) {
        this.chpercent = chpercent;
    }

    public String getVegcoverage() {
        return vegcoverage;
    }

    public void setVegcoverage(String vegcoverage) {
        this.vegcoverage = vegcoverage;
    }

    public String getSjy() {
        return sjy;
    }

    public void setSjy(String sjy) {
        this.sjy = sjy;
    }

    public String getDesntb() {
        return desntb;
    }

    public void setDesntb(String desntb) {
        this.desntb = desntb;
    }

    public String getSdtdtb() {
        return sdtdtb;
    }

    public void setSdtdtb(String sdtdtb) {
        this.sdtdtb = sdtdtb;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getFlowtm() {
        return flowtm;
    }

    public void setFlowtm(String flowtm) {
        this.flowtm = flowtm;
    }

    public String getWruletb() {
        return wruletb;
    }

    public void setWruletb(String wruletb) {
        this.wruletb = wruletb;
    }

    public LocalDateTime getTs() {
        return ts;
    }

    public void setTs(LocalDateTime ts) {
        this.ts = ts;
    }

    @Override
    public String toString() {
        return "BnsIaUnitws{" +
                "wscd='" + wscd + '\'' +
                ", wsnm='" + wsnm + '\'' +
                ", area='" + area + '\'' +
                ", chlength='" + chlength + '\'' +
                ", chpercent='" + chpercent + '\'' +
                ", vegcoverage='" + vegcoverage + '\'' +
                ", sjy='" + sjy + '\'' +
                ", desntb='" + desntb + '\'' +
                ", sdtdtb='" + sdtdtb + '\'' +
                ", comments='" + comments + '\'' +
                ", flowtm='" + flowtm + '\'' +
                ", wruletb='" + wruletb + '\'' +
                ", ts=" + ts +
                '}';
    }
}
