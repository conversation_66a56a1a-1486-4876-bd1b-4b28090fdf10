<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.shyj.evaluation.mapper.ExamDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getExamIndexList" resultType="com.huitu.cloud.api.shyj.evaluation.entity.ExamIndex">
        select EXAM_TYPE,EXAM_OPTION,SCORE_RATIO
        from EXAM_INDEX
    </select>
    <select id="getExamProvinceScoreListByAdcd" resultType="com.huitu.cloud.api.shyj.evaluation.entity.ExamProvinceScore">
        select ADCD,SCORE,VCODE
        from EXAM_PROVINCE_SCORE A
        where substring(A.ADCD,1,#{level})=#{ad} AND CONVERT(VARCHAR(7), <PERSON><PERSON><PERSON><PERSON>, 23)=#{ym}
    </select>
    <insert id="addExamProvinceScoreByAdcd">
        INSERT INTO EXAM_PROVINCE_SCORE(ADCD,YM,SCORE,OCCURUSER,OCCURTM,VCODE)
        values(#{adcd},#{ym},#{score},#{occurUser},getdate(),#{vcode})
    </insert>
    <update id="updateExamProvinceScoreByAdcd">
        UPDATE EXAM_PROVINCE_SCORE SET SCORE = #{score},OCCURUSER = #{occurUser},OCCURTM = getdate(),VCODE = #{vcode}
        WHERE ADCD=#{adcd} AND YM=#{ym} AND VCODE=#{vcode}
    </update>
    <select id="getExamCityScoreListByYm" resultType="com.huitu.cloud.api.shyj.evaluation.entity.ExamCityScore">
        select YYID,SCORE,VCODE
        from EXAM_CITY_SCORE A
        where YM=#{month}
    </select>
    <insert id="addExamCityScoreByAdcd">
        INSERT INTO EXAM_CITY_SCORE(YYID,YM,SCORE,OCCURUSER,OCCURTM,VCODE)
        values(#{yyid},#{ym},#{score},#{occurUser},getdate(),#{vcode})
    </insert>
    <select id="getExamIndexById" resultType="com.huitu.cloud.api.shyj.evaluation.entity.ExamIndex">
        select EXAM_TYPE,EXAM_OPTION,SCORE_RATIO from EXAM_INDEX where EXAM_TYPE = #{examType} and EXAM_OPTION = #{examOption}
    </select>
    <select id="getExamIndexListPage" resultType="com.huitu.cloud.api.shyj.evaluation.entity.ExamIndex">
        select EXAM_TYPE,EXAM_OPTION,SCORE_RATIO from EXAM_INDEX
        <where>
            <if test="map.type!=null and map.type!=''">
                EXAM_OPTION = #{map.type}
            </if>
        </where>
        order by EXAM_OPTION, EXAM_TYPE
    </select>
    <insert id="saveExamIndex">
        insert into EXAM_INDEX(EXAM_TYPE,EXAM_OPTION,SCORE_RATIO) values (#{examType}, #{examOption}, #{scoreRatio})
    </insert>
    <update id="updateExamCityScoreByAdcd">
        UPDATE EXAM_CITY_SCORE SET SCORE = #{score},OCCURUSER = #{occurUser},OCCURTM = getdate(),VCODE = #{vcode}
        WHERE YYID=#{yyid} AND YM=#{ym} AND VCODE=#{vcode}
    </update>
    <update id="updateExamIndex">
        UPDATE EXAM_INDEX SET SCORE_RATIO = #{scoreRatio} WHERE EXAM_TYPE=#{examType} AND EXAM_OPTION=#{examOption}
    </update>
    <delete id="deleteExamIndex">
        delete from EXAM_INDEX where EXAM_TYPE = #{examType} and EXAM_OPTION = #{examOption}
    </delete>

</mapper>
