package com.huitu.cloud.api.ewci.person.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.exception.VillagePersonImportException;
import com.huitu.cloud.api.ewci.person.service.VillagePersonService;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * 水库淹没范围责任人
 *
 * <AUTHOR>
 */
@Api(tags = "水库淹没范围责任人")
@RestController
@RequestMapping("/api/ewci/person/village")
public class VillagePersonResource extends AbstractApiResource implements ApiResource {

    private static final Logger logger = LoggerFactory.getLogger(VillagePersonResource.class);

    public VillagePersonResource(VillagePersonService baseService) {
        this.baseService = baseService;
    }

    @Override
    public String getUuid() {
        return "c248e234-8628-7577-82d9-0a9dac807d76";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final VillagePersonService baseService;

    @ApiOperation(value = "分页获取水库淹没范围责任人列表", notes = "作者：赵英捷")
    @PostMapping("select-page-list")
    public ResponseEntity<SuccessResponse<IPage<VillagePersonVo>>> getPageList(@Validated @RequestBody VillagePersonQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(query)));
    }

    @ApiOperation(value = "水库淹没范围责任人统计", notes = "作者：赵英捷")
    @PostMapping("select-village-per-sum-list")
    public ResponseEntity<SuccessResponse<List<VillageSummaryVo>>> getVillageSummaryList(@RequestBody VillageSummaryQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getVillageSummaryList(query)));
    }

    @ApiOperation(value = "导出水库淹没范围责任人信息", notes = "作者：赵英捷")
    @PostMapping(value = "export")
    public void dataExport(@Validated @RequestBody VillagePersonQuery query, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("水库淹没范围责任人信息", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.dataExport(query, response.getOutputStream());
        } catch (Exception ex) {
            logger.error("水库淹没范围责任人信息导出失败，参数：{}", JSON.toJSONString(query));
            logger.error(ex.getMessage(), ex);
        }
    }

    @ApiOperation(value = "导入水库淹没范围责任人信息", notes = "作者：赵英捷")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "xadcd", value = "县级行政区划代码", required = true, dataType = "String"),
    })
    @PostMapping(value = "import", headers = "content-type=multipart/form-data")
    public ResponseEntity<SuccessResponse<ImportResult<BnsVillagePersonTmp>>> dataImport(@RequestParam String xadcd, @ApiParam(value = "file", required = true) MultipartFile data) throws Exception {
        if (AdcdUtil.getXzAdLevel(xadcd) != 3) {
            throw new RuntimeException("参数[县级行政区划代码]无效");
        }
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.dataImport(xadcd, data.getInputStream())));
        } catch (VillagePersonImportException ex) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "Error", new ImportResult<>(0, ex.getMessage(), ex.getErrorData())));
        }
    }

    @ApiOperation(value = "编辑江河责任人", notes = "赵英捷")
    @PostMapping(value = "village-person-edit")
    public ResponseEntity<SuccessResponse<Integer>> villagePersonEdit(@RequestBody VillagePersonVo entity) throws Exception {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.villagePersonEdit(entity)));
    }
}
