package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * <p>
 * 沟道纵断面成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-18
 */
@TableName("IA_M_VSURFACE")
@ApiModel(value="IaMVsurface对象", description="沟道纵断面成果表")
public class IaVsurface extends  RelevantInfo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "纵断面编码")
    @TableId(value = "VECD", type = IdType.NONE)
    private String vecd;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "沟道")
    @TableField("CHANNEL")
    private String channel;

    @ApiModelProperty(value = "位置")
    @TableField("ADDRESS")
    private String address;

    @ApiModelProperty(value = "是否跨县")
    @TableField("ISCTOWN")
    private String isctown;

    @ApiModelProperty(value = "控制点高程")
    @TableField("CELE")
    private Double cele;

    @ApiModelProperty(value = "控制点经度")
    @TableField("CLGTD")
    private Double clgtd;

    @ApiModelProperty(value = "控制点纬度")
    @TableField("CLTTD")
    private Double clttd;

    @ApiModelProperty(value = "高程系统")
    @TableField("ELETYPE")
    private String eletype;

    @ApiModelProperty(value = "测量方法")
    @TableField("METHOD")
    private String method;

    @ApiModelProperty(value = "导入者姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalTime moditime;


    public String getVecd() {
        return vecd;
    }

    public void setVecd(String vecd) {
        this.vecd = vecd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getIsctown() {
        return isctown;
    }

    public void setIsctown(String isctown) {
        this.isctown = isctown;
    }

    public Double getCele() {
        return cele;
    }

    public void setCele(Double cele) {
        this.cele = cele;
    }

    public Double getClgtd() {
        return clgtd;
    }

    public void setClgtd(Double clgtd) {
        this.clgtd = clgtd;
    }

    public Double getClttd() {
        return clttd;
    }

    public void setClttd(Double clttd) {
        this.clttd = clttd;
    }

    public String getEletype() {
        return eletype;
    }

    public void setEletype(String eletype) {
        this.eletype = eletype;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalTime moditime) {
        this.moditime = moditime;
    }


}
