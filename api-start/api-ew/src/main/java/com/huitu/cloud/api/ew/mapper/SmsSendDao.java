package com.huitu.cloud.api.ew.mapper;

import com.huitu.cloud.api.ew.entity.sms.SmsMessage;
import com.huitu.cloud.api.ew.entity.sms.SmsReceiver;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@Mapper
public interface SmsSendDao {

    /**
     * 获取预警短信签名
     *
     * @param platformId      平台唯一标识
     * @param defaultSignName 默认签名
     * @return 短信签名
     */
    String getSignName(@Param("platformId") String platformId, @Param("defaultSignName") String defaultSignName);

    /**
     * 保存短信消息
     *
     * @param message 消息
     * @return 受影响的行数
     */
    int saveMessage(SmsMessage message);

    /**
     * 保存短信接收人
     *
     * @param msgId      消息ID
     * @param receivers  短信接收人
     * @param platformId 平台唯一标识
     * @return 受影响的行数
     */
    int saveReceivers(@Param("msgId") String msgId, @Param("receivers") Collection<SmsReceiver> receivers, @Param("platformId") String platformId);
}
