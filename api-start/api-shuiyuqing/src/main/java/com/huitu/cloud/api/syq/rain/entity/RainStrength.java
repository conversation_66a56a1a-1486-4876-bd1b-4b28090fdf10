package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
/**
 * <AUTHOR>
 */
public class RainStrength {
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "累计雨量")
    private BigDecimal drps;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime tm;

    @ApiModelProperty(value = "超警指标")
    private BigDecimal warnvalue;

    @ApiModelProperty(value = "测站预警指标对应类型")
    private String stadtp;

    @ApiModelProperty(value = "测站地址")
    private String stlc;

    @ApiModelProperty(value = "测站归属类型")
    private String frgrd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "测站归属类别名称")
    private String stadtpnm;

    @ApiModelProperty(value = "归属县名称")
    private String xadnm;

    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    @ApiModelProperty(value = "偏移经度")
    private BigDecimal plgtd;

    @ApiModelProperty(value = "偏移纬度")
    private BigDecimal plttd;

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public BigDecimal getDrps() {
        return drps;
    }

    public void setDrps(BigDecimal drps) {
        this.drps = drps;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getFrgrd() {
        return frgrd;
    }

    public void setFrgrd(String frgrd) {
        this.frgrd = frgrd;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    public BigDecimal getWarnvalue() {
        return warnvalue;
    }

    public void setWarnvalue(BigDecimal warnvalue) {
        this.warnvalue = warnvalue;
    }

    public String getStadtp() {
        return stadtp;
    }

    public void setStadtp(String stadtp) {
        this.stadtp = stadtp;
    }
}
