package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 水库图层
 */
@ApiModel(value = "水库图层")
public class RsvrInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "工程规模（1：大一型 2：大二型 3：中型 4：小一型 5：小二型）")
    @TableField("ENG_SCAL")
    private String engScal;

    @ApiModelProperty(value = "总库容")
    @TableField(value = "TOT_CAP")
    private Double totCap;

    @ApiModelProperty(value = "纬度")
    @TableField(value = "LTTD")
    private Double lttd;

    @ApiModelProperty(value = "经度")
    @TableField(value = "LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "控制面积")
    @TableField(value = "WAT_SHED_AREA")
    private Double watShedArea;

    @ApiModelProperty(value = "设计洪水位")
    @TableField(value = "DSFLLV")
    private Double dsfllv;

    @ApiModelProperty(value = "校核洪水位")
    @TableField(value = "CHFLLV")
    private Double chfllv;

    @ApiModelProperty(value = "历史运用最高水位")
    @TableField(value = "HHRZ")
    private Double hhrz;

    @ApiModelProperty(value = "历史运用最高水位日期")
    @TableField(value = "HHRZTM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime hhrztm;

    @ApiModelProperty(value = "最大入库流量")
    @TableField(value = "HMXINQ")
    private Double hmxinq;

    @ApiModelProperty(value = "最大入库流量日期")
    @TableField(value = "HMXINQTM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime hmxinqtm;

    @ApiModelProperty(value = "最大出库流量")
    @TableField(value = "HMXOTQ")
    private Double hmxotq;

    @ApiModelProperty(value = "最大出库流量日期")
    @TableField(value = "HMXOTQTM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime hmxotqtm;

    @ApiModelProperty(value = "汛限水位")
    @TableField(value = "FSLTDZ")
    private Double fsltdz;

    @ApiModelProperty(value = "实时水位")
    @TableField(value = "RZ")
    private Double rz;

    @ApiModelProperty(value = "水势")
    @TableField(value = "RWPTN")
    private Double rwptn;

    @ApiModelProperty(value = "上游赔偿高程")
    @TableField(value = "UPH")
    private Double uph;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public Double getTotCap() {
        return totCap;
    }

    public void setTotCap(Double totCap) {
        this.totCap = totCap;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getWatShedArea() {
        return watShedArea;
    }

    public void setWatShedArea(Double watShedArea) {
        this.watShedArea = watShedArea;
    }

    public Double getDsfllv() {
        return dsfllv;
    }

    public void setDsfllv(Double dsfllv) {
        this.dsfllv = dsfllv;
    }

    public Double getChfllv() {
        return chfllv;
    }

    public void setChfllv(Double chfllv) {
        this.chfllv = chfllv;
    }

    public Double getHhrz() {
        return hhrz;
    }

    public void setHhrz(Double hhrz) {
        this.hhrz = hhrz;
    }

    public LocalDateTime getHhrztm() {
        return hhrztm;
    }

    public void setHhrztm(LocalDateTime hhrztm) {
        this.hhrztm = hhrztm;
    }

    public Double getHmxinq() {
        return hmxinq;
    }

    public void setHmxinq(Double hmxinq) {
        this.hmxinq = hmxinq;
    }

    public LocalDateTime getHmxinqtm() {
        return hmxinqtm;
    }

    public void setHmxinqtm(LocalDateTime hmxinqtm) {
        this.hmxinqtm = hmxinqtm;
    }

    public Double getHmxotq() {
        return hmxotq;
    }

    public void setHmxotq(Double hmxotq) {
        this.hmxotq = hmxotq;
    }

    public LocalDateTime getHmxotqtm() {
        return hmxotqtm;
    }

    public void setHmxotqtm(LocalDateTime hmxotqtm) {
        this.hmxotqtm = hmxotqtm;
    }

    public Double getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(Double fsltdz) {
        this.fsltdz = fsltdz;
    }

    public Double getRz() {
        return rz;
    }

    public void setRz(Double rz) {
        this.rz = rz;
    }

    public Double getRwptn() {
        return rwptn;
    }

    public void setRwptn(Double rwptn) {
        this.rwptn = rwptn;
    }

    public Double getUph() {
        return uph;
    }

    public void setUph(Double uph) {
        this.uph = uph;
    }

    public String getEngScal() {
        return engScal;
    }

    public void setEngScal(String engScal) {
        this.engScal = engScal;
    }
}
