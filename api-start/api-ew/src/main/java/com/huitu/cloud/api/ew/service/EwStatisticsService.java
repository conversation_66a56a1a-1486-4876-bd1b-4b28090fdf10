package com.huitu.cloud.api.ew.service;

import com.huitu.cloud.api.ew.entity.request.EwWarnGradeSummaryRequest;
import com.huitu.cloud.api.ew.entity.request.EwWarnMessageRequest;
import com.huitu.cloud.api.ew.entity.request.EwWarningSummaryRequest;
import com.huitu.cloud.api.ew.entity.response.EwWarnGradeStatisticsResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarnGradeSummaryResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarnMessageStatistics;
import com.huitu.cloud.api.ew.entity.response.EwWarningSummaryResponse;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监测预警统计服务
 */
public interface EwStatisticsService {

    /**
     * 获取监测预警汇总统计信息
     *
     * @param request 请求参数
     * @return 汇总统计信息
     */
    EwWarningSummaryResponse getWarningSummary(EwWarningSummaryRequest request);

    /**
     * 政区预警本级统计信息查询
     *
     * @param query
     * @return
     */
    EwWarnMessageStatistics getAdWarnStatistics(EwWarnMessageRequest query);

    /**
     * 政区预警下级统计信息树形列表查询
     *
     * @param query
     * @return
     */
    List<EwWarnMessageStatistics> getAdWarnStatisticsTreeList(EwWarnMessageRequest query);


    /**
     * 获取山洪预警等级汇总统计信息
     * @param query
     * @return
     */

    EwWarnGradeSummaryResponse  getWarnGradeSummary(EwWarnGradeSummaryRequest query);


    /**
     * 获取政区山洪预警等级统计列表
     *
     * @param query
     * @return
     */
    public List<EwWarnGradeStatisticsResponse> getAdWarnGradeList(EwWarnGradeSummaryRequest query);


    /**
     * 导出政区山洪预警等级统计列表
     * @param query
     * @param response
     */
    public void exportAdWarnGradeList(EwWarnGradeSummaryRequest query, HttpServletResponse response);
}
