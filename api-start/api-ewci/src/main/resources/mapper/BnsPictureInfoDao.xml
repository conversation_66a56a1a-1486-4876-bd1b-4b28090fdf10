<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.sst.mapper.BnsPictureInfoDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <delete id="deletePicture">
        DELETE
        FROM BSN_STFILE_B
        WHERE KEYID = #{keyid}
          and TYPE = #{type}
    </delete>

    <insert id="addPicture">
        insert into BSN_STFILE_B (ID
        ,KEYID
        ,FILENAME
        ,FILESIZE
        ,FILEPATH
        ,TYPE
        ,SHID) values
        <foreach collection="info" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.keyid},
            #{item.filename},
            #{item.filesize},
            #{item.filepath},
            #{item.type},
            #{item.shid}
            )
        </foreach>
    </insert>
    <select id="getMaxId" resultType="java.lang.Integer">
        select max(id) + 1
        from BSN_STFILE_B
    </select>
    <select id="getPicture" resultType="com.huitu.cloud.api.ewci.sst.entity.PictureQo">
        SELECT id,
               keyid,
               filename,
               filesize,
               filepath,
               type
        from BSN_STFILE_B
        where KEYID = #{keyid}
          AND TYPE = #{type}
    </select>
</mapper>
