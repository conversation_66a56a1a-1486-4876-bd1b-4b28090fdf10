<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.hydropower.mapper.BsnShpwrRDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.ewci.hydropower.entity.BsnShpwrR">
        <result column="WPTN" property="wptn" />
        <result column="Z" property="z" />
        <result column="MSVMT" property="msvmt" />
        <result column="XSAVV" property="xsavv" />
        <result column="XSMXV" property="xsmxv" />
        <result column="STCD" property="stcd" />
        <result column="Q" property="q" />
        <result column="MSQMT" property="msqmt" />
        <result column="TM" property="tm" />
        <result column="MSAMT" property="msamt" />
        <result column="XSA" property="xsa" />
        <result column="FLWCHRCD" property="flwchrcd" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        WPTN, Z, MSVMT, XSAVV, XSMXV, STCD, Q, MSQMT, TM, MSAMT, XSA, FLWCHRCD
    </sql>
    <select id="selectByPage" resultType="com.huitu.cloud.api.ewci.hydropower.entity.ShpwrRVo">
        select STCD, STNM, TM, Z, b.adcd xadcd, b.adnm xadnm
        from (
        select a.STCD, a.STNM, a.ADDVCD, b.tm, Convert(decimal(18,2),b.Z) Z, DENSE_RANK() over(partition by a.STCD order by b.TM desc) rank from BSN_STBPRP_SHPWR_B a
        left join BSN_SHPWR_R b on a.STCD = b.STCD
        <where>
            <if test="map.ad !=null and map.ad !=''">
                and left(a.ADDVCD,#{map.level})=#{map.ad}
            </if>
            <if test="map.stnm != null and map.stnm !=''">
                AND CHARINDEX(#{map.stnm}, a.stnm) >0
            </if>
            <if test="map.level!= null and map.level == '4'.toString()">
                and a.addvcd !='220581'
            </if>
        </where>
        ) a
        left join BSN_ADCD_B b on LEFT(a.ADDVCD + '000000000000000', 15) = b.adcd
        where a.rank=1 order by a.TM desc

    </select>

</mapper>
