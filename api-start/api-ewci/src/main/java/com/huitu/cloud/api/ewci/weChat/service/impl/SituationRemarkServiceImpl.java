package com.huitu.cloud.api.ewci.weChat.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.bia.entity.BsnObjonlyBs;
import com.huitu.cloud.api.ewci.bia.entity.StRiverVos;
import com.huitu.cloud.api.ewci.bia.mapper.EvaluationDisplayDao;
import com.huitu.cloud.api.ewci.constants.SyqConstants;
import com.huitu.cloud.api.ewci.weChat.entity.*;
import com.huitu.cloud.api.ewci.weChat.mapper.SituationRemarkDao;
import com.huitu.cloud.api.ewci.weChat.service.SituationRemarkService;

import com.huitu.cloud.constants.CommConstants;
import com.huitu.cloud.entity.LoginUserInfo;
import com.huitu.cloud.util.*;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("ALL")
@Service
public class SituationRemarkServiceImpl implements SituationRemarkService {
    @Autowired
    private SituationRemarkDao situationRemarkDao;
    @Autowired
    private EvaluationDisplayDao evaluationDisplayDao;

    @Override
    public Map<String, Object> getRainByCondition() {
        Map<String, Object> map = new HashMap();
        List<String> stType = Arrays.asList("1", "2", "3");
        List<String> isOut = Arrays.asList("1");
        Page page = new Page<>(1, -1);
        List list = getRainByConditionAll(getTime("stm"), getTime("etm"), stType, "220000000000000", "", null, null, "1", isOut, null, null);
        page.setRecords(list);
        page.setTotal(list.size());
        map.put("stm", getTime("stm"));
        map.put("etm", getTime("etm"));
        map.put("list", page);
        return map;
    }

    @Override
    public List<WeChatRain> getRainByConditionAll(String stm, String etm, List<String> stType, String adcd, String bsnm, String stnm, String threshold, String rainShowType, List<String> isOut, List<String> isFollow, List<Integer> forecastHour) {
        int level = AdcdUtil.getAdLevel(adcd);
        List listMap = new ArrayList();
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<WeChatRain> rainList = null;
        rainList = situationRemarkDao.getAccpByTmAll(param);

        Map<String, WeChatRain> rainMap = rainList.stream().collect(Collectors.toMap(WeChatRain::getStcd, Function.identity()));
        Map<String, Object> stParam = new HashMap<>();
        String stTypes = "";
        if (stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
            stParam.put("stTypes", stTypes);
        }
        stParam.put("stType", stType);
        stParam.put("ad", adcd.substring(0, level));
        stParam.put("level", level);
        stParam.put("adcd", adcd);
        stParam.put("bsnm", bsnm);
        stParam.put("stnm", stnm);
        //获取登录用户信息 查询域外
        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
        if (org.apache.commons.lang.StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
            String userAd = loginUserInfo.getUserAd();
            int level2 = AdcdUtil.getAdLevel(userAd);
            stParam.put("userLevel", level2);
            stParam.put("userAd", userAd);
            stParam.put("userId", loginUserInfo.getUserId());
        }
        //添加是否查询域外（省级）
        if (isOut != null && isOut.size() == 1) {
            if (isOut.get(0).equals("1")) {
                //查询域内
                stParam.put("isOut", "1");
            } else {
                //查询域外
                stParam.put("isOut", "2");
            }
        }
        List<WeChatRain> stList = situationRemarkDao.getRainStInfo(stParam);
        stList = filterRainListByBsnm(stList, bsnm);
        //有雨量数据集合
        List<WeChatRain> hbList = new ArrayList<>();
        //无雨量数据集合
        List<WeChatRain> noRainList = new ArrayList<>();
        Map<String, WeChatAdcdB> adMap = null;
        if (level == 2) {//增加市级名称
            adMap = getAdInfoList(adcd, "2");
        }
        for (WeChatRain x : stList) {
            String stcd = x.getStcd();
            if (level == 2) {//增加市级名称
                if (x.getAdcd() != null && x.getAdcd().length() == 15) {
                    String adCity = x.getAdcd().substring(0, 4) + adcd.substring(4);
                    if (adMap.containsKey(adCity)) {
                        WeChatAdcdB adcdb = adMap.get(adCity);
                        x.setCadnm(adcdb.getAdnm());
                    }
                }
            }
            if (SyqConstants.RainConstants.QUERYRAINTYPEONE.equals(rainShowType)) {
                //查询一定时间内所有上报雨量信息的站
                if (rainMap.containsKey(stcd)) {
                    x.setDrps(rainMap.get(stcd).getDrps());
                    hbList.add(x);
                }

            } else if (SyqConstants.RainConstants.QUERYRAINTYPEZERO.equals(rainShowType)) {
                //查询所有雨量站信息
                if (rainMap.containsKey(stcd)) {
                    x.setDrps(rainMap.get(stcd).getDrps());
                    hbList.add(x);
                } else {
                    noRainList.add(x);
                }
            }
        }
        if (StringUtils.isNoneBlank(threshold)) {
            String[] items = threshold.split("-");
            final double min = Double.parseDouble(items[0]);
            final double max = Double.parseDouble(items[1]);
            //根据阀值过滤
            hbList = hbList.stream().filter(x -> new BigDecimal(x.getDrps()).doubleValue() >= min && new BigDecimal(x.getDrps()).doubleValue() <= max).collect(Collectors.toList());
        }
        //排序字段不能为空
        //Collections.sort(hbList, Comparator.comparingDouble(Rain::getDrps).reversed().thenComparing(Rain::getStcd));
        hbList.sort(new Comparator<WeChatRain>() {
            @Override
            public int compare(WeChatRain m1, WeChatRain m2) {
                double drp1 = new BigDecimal(m1.getDrps()).doubleValue();
                double drp2 = new BigDecimal(m2.getDrps()).doubleValue();
                if (drp1 - drp2 > 0) {
                    return -1;
                } else if (drp1 - drp2 < 0) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });

        //判断需不需要取未来一小时降雨信息
        try {
            String date = DateFormatUtil.beforeOrAfterHourToString(1, "yyyy-MM-dd HH:00", new Date());
            if (etm.equals(date)) {
                //查询未来一小时降雨信息
                List<Map<String, Object>> OneHourRainList = situationRemarkDao.getOneHourRain();
                if (OneHourRainList != null && OneHourRainList.size() > 0) {
                    Map<String, Object> OneHourRain = new HashMap<>();
                    OneHourRainList.forEach(item -> {
                        OneHourRain.put(item.get("STCD").toString(), item);
                    });
                    //将未来一小时降雨信息放入累计雨量集合中
                    hbList.forEach(item -> {
                        if (OneHourRain.containsKey(item.getStcd().trim())) {
                            Map<String, Object> rainValue = (Map<String, Object>) OneHourRain.get(item.getStcd().trim());
                            item.setOneHourRain(rainValue.get("RAIN").toString());
                        }
                    });
                    //添加累计雨量和未来一小时降雨之和的累计降雨
                    hbList.forEach(item -> {
                        if (item.getOneHourRain() != null && !"".equals(item.getOneHourRain())) {
                            if (item.getDrps() != null && !"".equals(item.getDrps())) {
                                BigDecimal a = new BigDecimal(item.getDrps());
                                BigDecimal b = new BigDecimal(item.getOneHourRain());
                                item.setDrpsrain(a.add(b).toString());
                            } else {
                                item.setDrpsrain(item.getOneHourRain());
                            }
                        } else {
                            item.setDrpsrain(item.getDrps());
                        }
                    });
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return hbList;
    }

    @Override
    public Map<String, WeChatAdcdB> getAdInfoList(String adcd, String adLevl) {
        Map<String, Object> param = new HashMap<>();
        if (org.apache.commons.lang.StringUtils.isNotBlank(adcd)) {
            int level = AdcdUtil.getAdLevel(adcd);
            param.put("ad", adcd.substring(0, level));
            param.put("level", level);
        }
        param.put("adLvl", adLevl);
        List<WeChatAdcdB> adList = situationRemarkDao.selectByAdLevel(param);
        Map<String, WeChatAdcdB> result = adList.stream().collect(Collectors.toMap(WeChatAdcdB::getAdcd, Function.identity()));
        return result;
    }

    @Override
    public WeChatRainByYwmh getRainInfoByYwmh() {
        //查询所有雨量超警
        WeChatRainByDutyRecort rainByDutyRecort = getRainInfoByDuty("220000000000000", getTime("stm"), getTime("etm"));
        WeChatRainByYwmh rainByYwmh = new WeChatRainByYwmh();
        rainByYwmh.setRainList(rainByDutyRecort.getRainList());
        rainByYwmh.setCityAvgRain(rainByDutyRecort.getCityAvgRain());
        rainByYwmh.setProvinceAvgRain(rainByDutyRecort.getProvinceAvgRain());
        rainByYwmh.setRainCount(rainByDutyRecort.getRainCount());
        rainByYwmh.setSwMaxRain(rainByDutyRecort.getSwMaxRain());
        rainByYwmh.setShMaxRain(rainByDutyRecort.getShMaxRain());
        rainByYwmh.setQxMaxRain(rainByDutyRecort.getQxMaxRain());
        IPage<WeChatBsnRainAlarm> iPage = getRainWarn("220000000000000", "", "", "", null, 1, -1);
        List<WeChatBsnRainAlarm> rainAlarmList = iPage.getRecords();
        Map<String, Long> rainMap = rainAlarmList.stream().collect(Collectors.groupingBy(WeChatBsnRainAlarm::getAlarmgradeid, Collectors.counting()));
        //雨量超警赋值
        rainByYwmh.setYlTotal(rainAlarmList.size());
        //超警
        rainByYwmh.setYlCjTotal(rainMap.get("1") != null ? rainMap.get("1").intValue() : 0);
        //超危险
        rainByYwmh.setYlCwTotal(rainMap.get("2") != null ? rainMap.get("2").intValue() : 0);
        return rainByYwmh;
    }

    @Override
    public WeChatRainByDutyRecort getRainInfoByDuty(String adcd, String stm, String etm) {
        Map<String, Object> param = new HashMap<>();
        WeChatRainByDutyRecort rainByDutyRecort = new WeChatRainByDutyRecort();
        int level = 2;
        //  2、山洪 1、水文 3、气象 过滤掉已屏蔽站点
        List<String> stType = Arrays.asList("1", "2", "3");
        List<WeChatRain> rainList = getRainByConditionAll(stm, etm, stType, adcd, null, null, "0.1-99999", "1", null, null, null);
        rainByDutyRecort.setRainCount(rainList.size());
        if (rainList.size() == 0) {
            rainByDutyRecort.setRainList(rainList);
            return rainByDutyRecort;
        } else {
            Map<String, List<WeChatRain>> rainMap = rainList.stream().collect(Collectors.groupingBy(WeChatRain::getStadtp));
            //水文最大值降雨站信息对象
            WeChatRain swRain = null;
            //气象最大值降雨站信息对象
            WeChatRain qxRain = null;
            //山洪最大值降雨站信息对象
            WeChatRain shRain = null;
            if (rainMap.containsKey("1")) {
                swRain = rainMap.get("1").get(0);
            }
            if (rainMap.containsKey("2")) {
                shRain = rainMap.get("2").get(0);
            }
            if (rainMap.containsKey("3")) {
                qxRain = rainMap.get("3").get(0);
            }
            rainByDutyRecort.setSwMaxRain(swRain);
            rainByDutyRecort.setShMaxRain(shRain);
            rainByDutyRecort.setQxMaxRain(qxRain);
            if (rainList.size() > 10) {
                rainList = rainList.subList(0, 10);
            }
        }
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("zero", adcd.substring(level));
        param.put("level2", level);
        //省级雨量平均值
        List<WeChatAdAvgRain> listProvince = situationRemarkDao.getRainAdAvg(param);
        param.put("level2", level + 2);
        param.put("zero", adcd.substring(level + 2));
        //梅河口市和公主岭市作为地级市来处理 220581，220381 市平均值去掉
        param.put("exceptFlag", true);
        //市级雨量平均值
        List<WeChatAdAvgRain> listCity = situationRemarkDao.getRainAdAvg(param);
        param.remove("exceptFlag");
        //县平均雨量值 begin
        param.put("level2", level + 4);
        param.put("zero", adcd.substring(level + 4));
        List<WeChatAdAvgRain> listCounty = situationRemarkDao.getRainAdAvg(param);
        Map<String, List<WeChatAdAvgRain>> map = listCounty.stream().collect(Collectors.groupingBy(WeChatAdAvgRain::getAdcd));
        List<WeChatAdAvgRain> mhkList = map.get(CommConstants.Public.MEIHEKOU_ADCD);
        List<WeChatAdAvgRain> gzlList = map.get(CommConstants.Public.GONGZHULING_ADCD);
        if (gzlList != null) {
            listCity.addAll(gzlList);
        }
        if (mhkList != null) {
            listCity.addAll(mhkList);
        }
        listCounty = listCounty.stream().sorted((u1, u2) -> Double.valueOf(u2.getAvgRain()).compareTo(Double.valueOf(u1.getAvgRain()))).collect(Collectors.toList());
        String provinceAvgRain = "0";
        if (listProvince != null && listProvince.get(0) != null && listProvince.get(0).getAvgRain() != null) {
            provinceAvgRain = listProvince.get(0).getAvgRain();
        }
        rainByDutyRecort.setProvinceAvgRain(provinceAvgRain);
        rainByDutyRecort.setCityAvgRain(listCity);
        rainByDutyRecort.setRainList(rainList);
        return rainByDutyRecort;
    }

    @Override
    public IPage<WeChatBsnRainAlarm> getRainWarn(String adcd, String bsnm, String stnm, String warnType, List<String> stType, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals(""))) || (warnType != null && warnType.equals(""))) {
            page.setRecords(new ArrayList<WeChatBsnRainAlarm>());
            return page;
        }
        Map<String, Object> param = new HashMap<>();
        int level = AdcdUtil.getAdLevel(adcd);
        param.put("adcd", level);
        param.put("bsnm", bsnm);
        param.put("stnm", stnm);
        param.put("stType", stType);
        param.put("ad", adcd.substring(0, level));
        param.put("warnType", warnType);
        IPage<WeChatBsnRainAlarm> list = situationRemarkDao.getRainWarn(page, param);
        List<WeChatBsnRainAlarm> stList = list.getRecords();
        List<WeChatBsnRainAlarm> stList1 = filterRainWarnListByBsnm(stList, bsnm);
        list.setRecords(stList1);
        return list;
    }

    @Override
    public IPage<WeChatStRsvrVo> getNoRsvrLatest() {
        WeChatRsvrQuery baseDao = new WeChatRsvrQuery();
        baseDao.setWarnFlag(true);
        List<String> rvType = Arrays.asList("4,5", "3", "2", "1");
        List<String> stType = Arrays.asList("1", "4");
        List<String> isOut = Arrays.asList("1");
        baseDao.setRvType(rvType);
        baseDao.setIsOut(isOut);
        baseDao.setIsOut(stType);
        return getRsvrLatestByConditon(baseDao);
    }

    @Override
    public IPage<WeChatStRsvrVo> getTyRsvrByConditon(List<String> rvType) {
        Page<WeChatStRsvrVo> page = new Page<WeChatStRsvrVo>(1, -1);
        if (rvType.size() > 0 && rvType.size() <= 4) {
                for (int i = 0; i < rvType.size(); i++) {
                    if("1".equals(rvType.get(i)) || "2".equals(rvType.get(i)) || "3".equals(rvType.get(i)) || "4,5".equals(rvType.get(i))){
                    } else {
                        throw new IllegalArgumentException("参数异常");
                    }
                }
        } else {
            return page;
        }
        WeChatRsvrQuery baseDao = new WeChatRsvrQuery();
        baseDao.setRvType(rvType);
        baseDao.setWarnFlag(false);
        baseDao.setIsOut(Arrays.asList("1"));
        return getRsvrLatestByConditon(baseDao);
    }

    @Override
    public IPage<WeChatStRsvrVo> getRsvrLatestByConditon(WeChatRsvrQuery baseDao) {
        //构造参数
        int pageNum = 1;
        int pageSize = -1;
        String stm = getTime("stm");
        String etm = getTime("etm");
        Page page = new Page<>(pageNum, pageSize);
        String stTypes = "";
        // 是否为运管
        boolean isYg = false;
        if (baseDao.getStType() != null && baseDao.getStType().contains("YG")) { // 测站类型是否包括运管选项
            isYg = true;
            // 清除YG，只取标识
            baseDao.setStType(baseDao.getStType().stream().filter(item -> !item.equals("YG")).collect(Collectors.toList()));
        }
        if (baseDao.getStType() != null && baseDao.getStType().size() > 0) {
            for (String type : baseDao.getStType()) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
        }
        int adLevl = AdcdUtil.getAdLevel("220000000000000");

        String whereSql = " and CHARINDEX(STTP,'RR,RQ')>0  ";
        if (org.apache.commons.lang.StringUtils.isNotBlank(stTypes) && isYg) {
            whereSql += " and (CHARINDEX(STADTP,'" + stTypes + "')>0 or left(aa.stcd, 2) = 'YG')";
        } else if (org.apache.commons.lang.StringUtils.isNotBlank(stTypes) && !isYg) {
            whereSql += " and CHARINDEX(STADTP,'" + stTypes + "')>0";
        } else if (org.apache.commons.lang.StringUtils.isBlank(stTypes) && isYg) {
            whereSql += " and left(aa.stcd, 2) = 'YG'";
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(String.join(",", baseDao.getRvType()))) {
            whereSql += " and CHARINDEX(RSVRTP,'" + baseDao.getRvType() + "')>0";
        }
        if (baseDao.isWarnFlag()) {
            whereSql += " AND rzFlag>0 AND STB.OSFLG != '1' ";
        }
        if (baseDao.getIsOut().get(0).equals("1")) {
            //查询域内
            whereSql += "  AND left(STB.ADCD," + adLevl + ") ='" + "22" + "'";
        }
        //优化速度 通过存储过程整理最新水库数据
        Date date = new Date();
        IPage<WeChatStRsvrVo> result = situationRemarkDao.getRsvrLatestData(page, getTime("stm"), getTime("etm"), whereSql);
        List<WeChatStRsvrVo> allList1 = result.getRecords();
        List<WeChatStRsvrVo> allList = filterRsvrListByBsnm(allList1, "");
        //对水库重启排序
        List<WeChatStRsvrVo> newArray = this.sortRsvr(allList);
        result.setTotal(newArray.size());
        result.setCurrent(pageNum);
        result.setRecords(newArray);
        if (pageSize > 0) {
            int min = (pageNum - 1) * pageSize;
            int max = (pageNum) * pageSize;
            if (max >= newArray.size()) {
                max = newArray.size();
            }
            //每页的数据
            List<WeChatStRsvrVo> oneList = newArray.subList(min, max);
            result.setRecords(oneList);
        }
        return result;
    }

    @Override
    public IPage<StRiverVos> getTyRiver(List<String> stType) {
        Page<StRiverVos> page = new Page<StRiverVos>(1, -1);
        if (stType.size() > 0 && stType.size() <= 2) {
            for (int i = 0; i < stType.size(); i++) {
                if ("1".equals(stType.get(i)) || "2".equals(stType.get(i))) {
                } else {
                    throw new IllegalArgumentException("参数异常");
                }
            }
        } else {
            return page;
        }
        WeChatRiverQo baseDao = new WeChatRiverQo();
        baseDao.setStType(stType);
        baseDao.setWarnFlag(false);
        baseDao.setIsOut(Arrays.asList("1"));
        return getRiverByConditon(baseDao);
    }

    @Override
    public IPage<StRiverVos> getNoRiver() {
        WeChatRiverQo baseDao = new WeChatRiverQo();
        baseDao.setWarnFlag(true);
        List<String> stType = Arrays.asList("1","4");
        List<String> isOut = Arrays.asList("1");
        baseDao.setIsOut(isOut);
        baseDao.setIsOut(stType);
        return getRiverByConditon(baseDao);
    }

    @Override
    public IPage<StRiverVos> getRiverByConditon(WeChatRiverQo baseDao) {
        //构造参数
        int pageNum = 1;
        int pageSize = -1;
        Page<StRiverVos> page = new Page<StRiverVos>(pageNum, pageSize);
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if ((baseDao.getStType() != null && (baseDao.getStType().size() == 0 || baseDao.getStType().get(0).equals("")))) {
            List<StRiverVos> stList = new ArrayList<>();
            page.setRecords(stList);
            return page;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("stm", getTime("stm"));
        param.put("etm", getTime("etm"));
        param.put("stType", baseDao.getStType());
        param.put("adcd", "220000000000000");
        param.put("ad", "22");
        param.put("dataFlag", true);
        param.put("warnFlag", baseDao.isWarnFlag());
        param.put("bsnm", "");
        param.put("stnm", "");
        param.put("adLevl", "2");
        param.put("userAd", "220000000000000");
        param.put("userLevel", 2);
        param.put("userId", 1);
        param.put("isOut", "1");

        //查询结果对象返回map 可以根据service接口需要，转化成自己需要实体返回类型
        IPage<StRiverVos> result = situationRemarkDao.getRiverByCondition(page, param);
        List<StRiverVos> stList = result.getRecords();
        List<StRiverVos> stList1 = filterRiverListByBsnm(stList, "");
        result.setRecords(stList1);
        return result;
    }

    private List<WeChatRain> filterRainListByBsnm(List<WeChatRain> stList, String bsnm) {
        if (bsnm != null && !bsnm.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<WeChatBsnBasStBTo> bsList = situationRemarkDao.getRiverTree(bsnm);
            List<String> stcdList = bsList.stream().map(WeChatBsnBasStBTo::getStcd).collect(Collectors.toList());
            List<WeChatRain> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            stList = intersection;
            return intersection;
        } else {
            return stList;
        }
    }

    private List<WeChatBsnRainAlarm> filterRainWarnListByBsnm(List<WeChatBsnRainAlarm> stList, String bsnm) {
        if (bsnm != null && !bsnm.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<WeChatBsnBasStBTo> bsList = situationRemarkDao.getRiverTree(bsnm);
            List<String> stcdList = bsList.stream().map(WeChatBsnBasStBTo::getStcd).collect(Collectors.toList());
            List<WeChatBsnRainAlarm> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            stList = intersection;
            return intersection;
        } else {
            return stList;
        }
    }

    private List<WeChatStRsvrVo> filterRsvrListByBsnm(List<WeChatStRsvrVo> stList, String bsnm) {
        if (bsnm != null && !bsnm.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<WeChatBsnBasStBTo> bsList = situationRemarkDao.getRiverTree(bsnm);
            List<String> stcdList = bsList.stream().map(WeChatBsnBasStBTo::getStcd).collect(Collectors.toList());
            List<WeChatStRsvrVo> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            stList = intersection;
            return intersection;
        } else {
            return stList;
        }
    }

    private List<StRiverVos> filterRiverListByBsnm(List<StRiverVos> stList, String bsnm) {
        if (bsnm != null && !bsnm.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<WeChatBsnBasStBTo> bsList = situationRemarkDao.getRiverTree(bsnm);
            List<String> stcdList = bsList.stream().map(WeChatBsnBasStBTo::getStcd).collect(Collectors.toList());
            List<StRiverVos> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            stList = intersection;
            return intersection;
        } else {
            return stList;
        }
    }

    /**
     * 重新排序 同一水库的人工和自动站显示在一起，显示为人工站在上
     *
     * @param list 水情列表数据集合
     * @return
     */
    private List<WeChatStRsvrVo> sortRsvr(List<WeChatStRsvrVo> list) {
        List<WeChatStRsvrVo> newArray = new ArrayList<>();
        Map<String, List<WeChatStRsvrVo>> stnmMap = new HashMap<>();
        //查询同一水库既有人工又有自动的站的对应关系
        List<BsnObjonlyBs> bList = evaluationDisplayDao.getBsnObjOnly();
        Map<String, String> objcdMap = new HashMap<>();
        for (BsnObjonlyBs item : bList) {
            //存储测站对应统一标识id
            objcdMap.put(item.getObjcd(), item.getObjid());
        }
        //是否存在同一水库既有人工又有自动的
        boolean twoFlag = false;
        //存储同一水库既有人工又有自动的
        Map<String, List<WeChatStRsvrVo>> stnmTwoMap = new HashMap<>();
        for (WeChatStRsvrVo item : list) {
            //以相同标识 判定同一个水库的人工与自动站关联
            String stcd = item.getStcd();
            String key = "";
            if (objcdMap.containsKey(stcd)) {
                //同一个水库存在人工与自动站关联 以标识为主键
                key = objcdMap.get(stcd);
            } else {
                //同一个水库不存在人工与自动站关联 以测站为主键标识
                key = stcd;
            }
            if (stnmMap.containsKey(key)) {
                twoFlag = true;
                List<WeChatStRsvrVo> stmList = stnmMap.get(key);
                //1、山洪改为自动，水文和人工报汛改为人工，默认显示人工，山洪自动站（5），人工为山洪人工站（3）和所有水文站
                //2、同一水库的人工和自动站显示在一起，显示为人工站在上
                // stadtp， 2代表人工 1,4 代表自动
                if (item.getStadtp() != null && (item.getStadtp().equals("4") || item.getStadtp().equals("1"))) {
                    //人工在前
                    stmList.add(0, item);
                } else {
                    //自动在后
                    stmList.add(item);
                }
                stnmTwoMap.put(key, stmList);
                stnmMap.put(key, stmList);
            } else {
                List<WeChatStRsvrVo> stmList = new ArrayList<>();
                stmList.add(item);
                stnmMap.put(key, stmList);
                //同一水库的人工和自动站 只添加一次
                newArray.add(item);
            }
        }
        List<WeChatStRsvrVo> result = new ArrayList<>();
        int sortNo = 1;
        if (twoFlag) {
            // 2021年6月8日 姜金阳 修改：手动加入sortNo排序，将同一个水库存在人工与自动站关联情况合并为同一个排序值
            for (WeChatStRsvrVo item : newArray) {
                //以相同标识 判定同一个水库的人工与自动站关联
                String stcd = item.getStcd();
                String key = "";
                if (objcdMap.containsKey(stcd)) {
                    //同一个水库存在人工与自动站关联 以标识为主键
                    key = objcdMap.get(stcd);
                } else {
                    //同一个水库不存在人工与自动站关联 以测站为主键标识
                    key = stcd;
                }
                if (stnmTwoMap.containsKey(key)) {
                    List<WeChatStRsvrVo> stmList = stnmTwoMap.get(key);
//                    int finalSortNo = sortNo;
//                    stmList.stream().map(p -> { p.setSortNo(finalSortNo); return p; }).collect(Collectors.toList());
                    if (stmList.size() > 0) {
                        stmList.get(0).setSortNo(sortNo);
                    }
                    //同一个水库的人工与自动站 一起插入
                    result.addAll(stmList);
                } else {
                    item.setSortNo(sortNo);
                    result.add(item);
                }
                sortNo++;
            }
            return result;
        } else {
            for (int i = 0; i < newArray.size(); i++) {
                newArray.get(i).setSortNo(i + 1);
            }
            return newArray;
        }
    }

    public String getTime(String tm) {
        // 格式化时间
        SimpleDateFormat stm = new SimpleDateFormat("yyyy-MM-dd 08:00");
        SimpleDateFormat etm = new SimpleDateFormat("yyyy-MM-dd HH:00");
        // 获取当前系统时间
        Calendar now = Calendar.getInstance();
        //开始时间
        if (tm.equals("stm")) {
            if (now.get(Calendar.HOUR_OF_DAY) > 8) {
                return stm.format(now.getTime());
            } else {
                now.add(Calendar.DATE, -1);
                return stm.format(now.getTime());
            }
        } else if (tm.equals("etm")) {//结束时间
            if (now.get(Calendar.MINUTE) > 0) {
                now.add(Calendar.HOUR_OF_DAY, 1);
                return etm.format(now.getTime());
            } else {
                return etm.format(now.getTime());
            }
        } else {
            return "";
        }
    }

    @Override
    public WeChatWeatherVo getWeatherForecast() {
        WeChatWeatherVo weatherForecast=situationRemarkDao.getWeatherForecast();
        return weatherForecast;
    }

    @Override
    public List<WeChatQxpicgetRVo> getWxytList(int tms, String picType) {
        return situationRemarkDao.getWxytList(-(tms),picType);
    }

    @Override
    public List<WeChatBnsFilesAndUrls> getLastestBnsFileurlsr() {
        return situationRemarkDao.getLastestBnsFileurlsr("天气公报");
    }
}