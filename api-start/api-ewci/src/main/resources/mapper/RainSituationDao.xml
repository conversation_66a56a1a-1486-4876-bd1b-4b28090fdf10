<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.weChat.mapper.RainSituationDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getAccpByTmAll" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatRain">
        select STCD,SUM(DRP) DRPS from ST_PPTN_R
        WHERE 1=1
        <if test="stm != null and stm !=''">
            and tm> CONVERT(datetime,'${stm}')
        </if>
        <if test="etm != null and etm !=''">
            and TM &lt;= CONVERT(datetime,'${etm}')
        </if>
        AND INTV='1' and drp>=0
        GROUP BY STCD
    </select>
    <select id="getRainStInfo" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatRain">
        select
        b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM
        ,b.STADTPNM,b.stadtp,PLGTD,PLTTD
        <if test="map.forecastHour != null">
            <foreach collection="map.forecastHour" index="index" item="item">
                ,round(isnull(RAIN${item}, 0), 1) RAIN${item}
            </foreach>
        </if>
        from BSN_STBPRP_V b
        left join ST_STSMTASK_B tk on b.STCD=tk.STCD
        <if test="map.forecastHour != null">
            <foreach collection="map.forecastHour" index="index" item="item">
                left join (SELECT STCD STCD${item},SUM(RAIN) RAIN${item} FROM BSN_FORECASTRAIN_F WHERE RAIN_TIME BETWEEN
                '${map.start_time}' AND
                '${map.endTimeList[index]}'
                GROUP BY STCD) k${item} on b.STCD=STCD${item}
            </foreach>
        </if>
        <if test="map.isFollow != null and map.isFollow != ''">
            left join BSN_USER_FOLLOWST follow on follow.stcd=b.stcd
        </if>
        where 1=1
        AND tk.PFL='1'
        <if test="map.isOut == null and map.ad != null and map.ad != ''">
            and left(b.ADCD,#{map.level})=#{map.ad}
            <if test="map.level !=null and map.level =='4'.toString()">
                and left(b.ADCD,6) != '220581'
            </if>
        </if>
        <if test="map.stnm != null and  map.stnm != ''">
            AND CHARINDEX(#{map.stnm},stnm) >0
        </if>
        <if test="map.bsnm !=null and  map.bsnm !=''">
            AND ltrim(rtrim(b.BSNM))=#{map.bsnm}
        </if>
        <if test="map.stTypes != null and  map.stTypes !='' ">
            and CHARINDEX(b.STADTP,#{map.stTypes})>0
        </if>
        <if test="map.isOut != null and map.isOut == '1'.toString() ">
            and left(b.ADCD,#{map.level})=#{map.ad}
            <if test="map.level !=null and map.level =='4'.toString()">
                and left(b.ADCD,6) != '220581'
            </if>
        </if>
        <if test="map.isOut != null and map.isOut == '2'.toString() ">
            and left(b.ADCD,2) != '22'
        </if>
        <if test="map.isOut != null and map.isOut == '3'.toString() ">
            and ( left(b.ADCD,2) != '22' or ( left(b.ADCD,#{map.level})=#{map.ad}
            <if test="map.level !=null and map.level =='4'.toString()">
                and left(b.ADCD,6) != '220581'
            </if>
            ))
        </if>
        <if test="map.isFollow != null and map.isFollow == '1'.toString() ">
            and follow.userid=#{map.userId}
        </if>
        <if test="map.isFollow != null and map.isFollow == '2'.toString() ">
            and isnull(follow.userid,0)!=#{map.userId}
        </if>
        GROUP BY
        b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD
        <if test="map.forecastHour != null">
            <foreach collection="map.forecastHour" index="index" item="item">
                ,RAIN${item}
            </foreach>
        </if>
        order by b.STCD
    </select>

    <select id="getOneHourRain" resultType="java.util.Map">
        select STCD, Convert(decimal (18, 1), RAIN) RAIN
        from BSN_ONEHOURRAIN_R
        where rain >= 0
    </select>

</mapper>
