package com.huitu.cloud.api.ewci.weChat.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 文件和路径信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04
 */
@ApiModel(value="BnsFilesAndUrls对象", description="文件和路径信息")
public class WeChatBnsFilesAndUrls extends WeChatBnsFilesR {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "文件链接")
    private String fileurl;

    @ApiModelProperty(value = "文件新名称")
    private String filenewnm;

    public String getFileurl() {
        return fileurl;
    }

    public void setFileurl(String fileurl) {
        this.fileurl = fileurl;
    }

    public String getFilenewnm() {
        return filenewnm;
    }

    public void setFilenewnm(String filenewnm) {
        this.filenewnm = filenewnm;
    }
}
