package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel(value = "RsvrDrnaVo",description = "水库集水面积范围内测站的平均雨量")
public class RsvrAvgDrnaVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "蓄水量")
    @TableField("W")
    private BigDecimal w;

    @ApiModelProperty(value = "雨量")
    @TableField("DRP")
    private BigDecimal drp;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public BigDecimal getW() {
        return w;
    }

    public void setW(BigDecimal w) {
        this.w = w;
    }

    public BigDecimal getDrp() {
        return drp;
    }

    public void setDrp(BigDecimal drp) {
        this.drp = drp;
    }
}
