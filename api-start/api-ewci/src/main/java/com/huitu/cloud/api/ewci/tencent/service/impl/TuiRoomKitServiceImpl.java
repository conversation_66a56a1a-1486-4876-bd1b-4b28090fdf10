package com.huitu.cloud.api.ewci.tencent.service.impl;

import com.alibaba.fastjson.JSON;
import com.huitu.cloud.api.base.saas.entity.SaasToken;
import com.huitu.cloud.api.base.saas.service.SaasTokenService;
import com.huitu.cloud.api.ewci.tencent.api.MobPushService;
import com.huitu.cloud.api.ewci.tencent.api.WebSocketService;
import com.huitu.cloud.api.ewci.tencent.entity.*;
import com.huitu.cloud.api.ewci.tencent.service.TuiRoomKitService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 腾讯音视频服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class TuiRoomKitServiceImpl implements TuiRoomKitService {

    private static final Logger logger = LoggerFactory.getLogger(TuiRoomKitServiceImpl.class);

    @Value("${huitu.app.push.app-id:}")
    private String appId;

    private WebSocketService webSocketService;

    private SaasTokenService saasTokenService;

    private MobPushService mobPushService;

    public TuiRoomKitServiceImpl(WebSocketService webSocketService, SaasTokenService saasTokenService,
                                 MobPushService mobPushService) {
        this.webSocketService = webSocketService;
        this.saasTokenService = saasTokenService;
        this.mobPushService = mobPushService;
    }

    /**
     * 发送 WebSocket 消息, 用于 Web 端弹窗推送
     *
     * @param vo Web端Socket消息封装实体
     * @return Boolean
     */
    @Override
    public Boolean sendSocketMsg(TuiRoomKitVO vo) {
        JSONObject jsonObject = JSONObject.fromObject(vo);

        vo.getUsersVOList().forEach(v -> {
            jsonObject.put("msgTo", v.getUid());
            jsonObject.put("msgFrom", vo.getCreateId());
            SendMsgQo sendMsgQo = new SendMsgQo();
            sendMsgQo.setMessage(jsonObject.toString());
            sendMsgQo.setTopic("TuiRoomKit");
            sendMsgQo.setMsgFrom(vo.getCreateId());
            sendMsgQo.setMsgTo(v.getUid());
            String res = webSocketService.sendMsg(sendMsgQo);
            logger.info("WebSocket推送结果={}", res);
        });
        return true;
    }

    /**
     * 发送手机壳顶栏推送消息，用于移动端顶栏推送
     *
     * @param vo 移动端顶栏推送封装实体
     * @return Boolean
     */
    @Override
    public Boolean mobPush(MobPushVO vo) {
        SaasToken saasToken = saasTokenService.getToken(false);
        String token = saasToken.getToken();

        // 组装扩展数据信息
        MobPushExtraVO mobPushExtraVO = new MobPushExtraVO();
        mobPushExtraVO.setCreateName(vo.getExtraTemp().getCreateName());
        mobPushExtraVO.setRoomId(vo.getExtraTemp().getRoomId());
        mobPushExtraVO.setTitle(vo.getExtraTemp().getTitle());
        String jsonString = JSON.toJSONString(mobPushExtraVO);

        // 组装推送主体
        MobPushVO mobPushVO = new MobPushVO();
        mobPushVO.setAppId(appId);
        mobPushVO.setUserId(vo.getUserId());
        mobPushVO.setContent(vo.getContent());
        mobPushVO.setTitle(vo.getTitle());
        mobPushVO.setTag(vo.getTag());

        // 需要 String 类型的值
        mobPushVO.setExtra(jsonString);
        String res = mobPushService.sendMsg(mobPushVO, token);
        logger.info("手机壳推送结果={}", res);
        return true;
    }

}
