<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ledger.mapper.LgFiledDao">

    <select id="selectLgFile"  resultType="com.huitu.cloud.api.ewci.ledger.entity.LgFile">
        SELECT
            A.ID,
            A.PROJECT_ID,
            A.FILE_NAME,
            A.FILE_PATH,
            A.FILE_TYPE
        FROM
            LEDGER_FILE A
        where A.PROJECT_ID = #{projectId}
    </select>

    <delete id="delProjectFilesByProjectId">
        DELETE
        FROM
            LEDGER_FILE
        WHERE
            PROJECT_ID = #{projectId} AND FILE_TYPE IN (11,12,13,14)
    </delete>
    <delete id="delFileLg">
        DELETE
        FROM
            LEDGER_FILE
        WHERE
            PROJECT_ID = #{projectId}
        AND FILE_TYPE IN (21,22,23,34,25,26)
    </delete>


</mapper>
