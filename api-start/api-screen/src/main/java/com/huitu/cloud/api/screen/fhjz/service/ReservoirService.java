package com.huitu.cloud.api.screen.fhjz.service;

import com.huitu.cloud.api.screen.fhjz.entity.ReservoirVo;
import com.huitu.cloud.api.screen.fhjz.entity.RsvrDetailVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 实时降雨 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-15
 */
public interface ReservoirService {

    /**
     * 水库列表查询
     * @param engScal 水库类型列表
     * @param cxxOrder 是否按照超汛限排序
     * @param stm 开始查询时间
     * @param etm 结束查询时间
     * @param resName 水库名称
     * @return
     */
    List<ReservoirVo> list(String engScal, boolean cxxOrder,String stm,String etm,String resName);

    /**
     * 防洪水库工程详情列表
     *
     * @param engScal 水库规模
     * @param resName 水库名称
     * @param adcd 政区编码
     * @param stm 开始查询时间
     * @param etm 结束查询时间
     * @return
     */
    List<RsvrDetailVo> detailList(String engScal, String resName, String adcd,String stm,String etm);

    /**
     * 查询水库站信息，用于地图上叠加
     *
     * @param engScal
     * @param resCode
     * @param stm 开始查询时间
     * @param etm 结束查询时间
     * @return
     */
    List<Map> querySkStationList(String engScal, String resCode,String stm,String etm);

    /**
     * 查询政区编码
     *
     * @param resCode
     * @return
     */
    Map<String, Object> querySkAdcd(String resCode);

    /**
     * 查询水库数量详情
     *
     * @param adcd
     * @return
     */
    String statisticRsvrDetail(String adcd);
}
