package com.huitu.cloud.api.ewci.consult.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description: 风险研判-超汛超警-市县-水库实体
 * @author: jiangjy
 * @create: 2024-4-17
 **/
@ApiModel(value = "风险研判-超汛超警-市县-水库实体")
public class ConsultRiskAdRsvrVo {

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "正常水位站数量")
    private Integer rz;

    @ApiModelProperty(value = "超讯限水位站数量")
    private Integer rzfsltdz;

    @ApiModelProperty(value = "超设计水位站数量")
    private Integer dsflz;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getRz() {
        return rz;
    }

    public void setRz(Integer rz) {
        this.rz = rz;
    }

    public Integer getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(Integer rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public Integer getDsflz() {
        return dsflz;
    }

    public void setDsflz(Integer dsflz) {
        this.dsflz = dsflz;
    }
}

