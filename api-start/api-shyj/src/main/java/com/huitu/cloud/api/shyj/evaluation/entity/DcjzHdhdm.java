package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 重点城(集)镇调查评价-河道横断面
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@ApiModel(value="DcjzHdhdm对象", description="重点城(集)镇调查评价-河道横断面")
public class DcjzHdhdm extends Model<DcjzHdhdm> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "位置")
    @TableId(value = "ADDRESS", type = IdType.NONE)
    private String address;

    @ApiModelProperty(value = "沟道")
    @TableId(value = "CHANNEL", type = IdType.NONE)
    private String channel;

    @ApiModelProperty(value = "断面标识")
    @TableId(value = "DMIDENTIT", type = IdType.NONE)
    private String dmidentit;

    @ApiModelProperty(value = "断面形态")
    @TableId(value = "DMFORM", type = IdType.NONE)
    private String dmform;

    @ApiModelProperty(value = "是否跨县")
    @TableId(value = "ISCTOWN", type = IdType.NONE)
    private String isctown;

    @ApiModelProperty(value = "河床底质")
    @TableId(value = "TEXTURE", type = IdType.NONE)
    private String texture;


    @ApiModelProperty(value = "基点经度")
    @TableId(value = "BASELGTD", type = IdType.NONE)
    private Double baselgtd;

    @ApiModelProperty(value = "基点纬度")
    @TableId(value = "BASELTTD", type = IdType.NONE)
    private Double baselttd;

    @ApiModelProperty(value = "基点高程")
    @TableId(value = "BASEELE", type = IdType.NONE)
    private String baseele;

    @ApiModelProperty(value = "断面方位角")
    @TableId(value = "AZIMUTH", type = IdType.NONE)
    private String azimuth;

    @ApiModelProperty(value = "历史最高水位")
    @TableId(value = "HMZ", type = IdType.NONE)
    private String hmz;

    @ApiModelProperty(value = "成灾水位")
    @TableId(value = "CZZ", type = IdType.NONE)
    private String czz;

    @ApiModelProperty(value = "测量方法")
    @TableId(value = "METHOD", type = IdType.NONE)
    private String method;

    @ApiModelProperty(value = "起点距")
    @TableId(value = "CDISTANCE", type = IdType.NONE)
    private String cdistance;

    @ApiModelProperty(value = "高程")
    @TableId(value = "ELE", type = IdType.NONE)
    private String ele;

    @ApiModelProperty(value = "经度")
    @TableId(value = "LGTD", type = IdType.NONE)
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableId(value = "LTTD", type = IdType.NONE)
    private Double lttd;

    @ApiModelProperty(value = "糙率")
    @TableId(value = "COEFF", type = IdType.NONE)
    private String coeff;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDmidentit() {
        return dmidentit;
    }

    public void setDmidentit(String dmidentit) {
        this.dmidentit = dmidentit;
    }

    public String getDmform() {
        return dmform;
    }

    public void setDmform(String dmform) {
        this.dmform = dmform;
    }

    public String getIsctown() {
        return isctown;
    }

    public void setIsctown(String isctown) {
        this.isctown = isctown;
    }

    public String getTexture() {
        return texture;
    }

    public void setTexture(String texture) {
        this.texture = texture;
    }

    public Double getBaselgtd() {
        return baselgtd;
    }

    public void setBaselgtd(Double baselgtd) {
        this.baselgtd = baselgtd;
    }

    public Double getBaselttd() {
        return baselttd;
    }

    public void setBaselttd(Double baselttd) {
        this.baselttd = baselttd;
    }

    public String getBaseele() {
        return baseele;
    }

    public void setBaseele(String baseele) {
        this.baseele = baseele;
    }

    public String getAzimuth() {
        return azimuth;
    }

    public void setAzimuth(String azimuth) {
        this.azimuth = azimuth;
    }

    public String getHmz() {
        return hmz;
    }

    public void setHmz(String hmz) {
        this.hmz = hmz;
    }

    public String getCzz() {
        return czz;
    }

    public void setCzz(String czz) {
        this.czz = czz;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getCdistance() {
        return cdistance;
    }

    public void setCdistance(String cdistance) {
        this.cdistance = cdistance;
    }

    public String getEle() {
        return ele;
    }

    public void setEle(String ele) {
        this.ele = ele;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public String getCoeff() {
        return coeff;
    }

    public void setCoeff(String coeff) {
        this.coeff = coeff;
    }

    @Override
    public String toString() {
        return "DcjzHdhdm{" +
                "adnm='" + adnm + '\'' +
                ", address='" + address + '\'' +
                ", channel='" + channel + '\'' +
                ", dmidentit='" + dmidentit + '\'' +
                ", dmform='" + dmform + '\'' +
                ", isctown='" + isctown + '\'' +
                ", texture='" + texture + '\'' +
                ", baselgtd=" + baselgtd +
                ", baselttd=" + baselttd +
                ", baseele='" + baseele + '\'' +
                ", azimuth='" + azimuth + '\'' +
                ", hmz='" + hmz + '\'' +
                ", czz='" + czz + '\'' +
                ", method='" + method + '\'' +
                ", cdistance='" + cdistance + '\'' +
                ", ele='" + ele + '\'' +
                ", lgtd=" + lgtd +
                ", lttd=" + lttd +
                ", coeff='" + coeff + '\'' +
                '}';
    }
}
