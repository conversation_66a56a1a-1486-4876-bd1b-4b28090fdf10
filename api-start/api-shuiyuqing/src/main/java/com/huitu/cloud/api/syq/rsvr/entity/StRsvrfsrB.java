package com.huitu.cloud.api.syq.rsvr.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 库(湖)站汛限水位表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-11
 */
@TableName("ST_RSVRFSR_B")
@ApiModel(value="StRsvrfsrB对象", description="库(湖)站汛限水位表")
public class StRsvrfsrB extends Model<StRsvrfsrB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD", type = IdType.NONE)
    private String stcd;

    @ApiModelProperty(value = "开始月日")
    @TableField("BGMD")
    private String bgmd;

    @ApiModelProperty(value = "结束月日")
    @TableField("EDMD")
    private String edmd;

    @ApiModelProperty(value = "汛限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @ApiModelProperty(value = "汛限库容")
    @TableField("FSLTDW")
    private BigDecimal fsltdw;

    @ApiModelProperty(value = "汛期类别 1 主汛期 2 后汛期 3 过渡期 4 其他")
    @TableField("FSTP")
    private String fstp;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getBgmd() {
        return bgmd;
    }

    public void setBgmd(String bgmd) {
        this.bgmd = bgmd;
    }

    public String getEdmd() {
        return edmd;
    }

    public void setEdmd(String edmd) {
        this.edmd = edmd;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public BigDecimal getFsltdw() {
        return fsltdw;
    }

    public void setFsltdw(BigDecimal fsltdw) {
        this.fsltdw = fsltdw;
    }

    public String getFstp() {
        return fstp;
    }

    public void setFstp(String fstp) {
        this.fstp = fstp;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return this.stcd;
    }

    @Override
    public String toString() {
        return "StRsvrfsrB{" +
        "stcd=" + stcd +
        ", bgmd=" + bgmd +
        ", edmd=" + edmd +
        ", fsltdz=" + fsltdz +
        ", fsltdw=" + fsltdw +
        ", fstp=" + fstp +
        ", moditime=" + moditime +
        "}";
    }
}
