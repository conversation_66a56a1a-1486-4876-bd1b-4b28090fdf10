<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.monitor.mapper.InfluenceScopeDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getFullAdnm" resultType="java.lang.String">
        WITH ADLIST AS (
            SELECT ADCD, PADCD, ADNM, ADLVL
            FROM MDT_ADCDINFO_B
            WHERE ADCD = #{adcd}
            UNION ALL
            SELECT B.ADCD, B.PADCD, B.ADNM, B.ADLVL
            FROM ADLIST A
                     INNER JOIN MDT_ADCDINFO_B B ON B.ADCD = A.PADCD)
        SELECT ISNULL((SELECT ADNM + '' FROM ADLIST WHERE ADLVL &gt;= #{adlvl} ORDER BY ADLVL ASC FOR XML PATH ('')), '')
    </select>
    <select id="getStatData" resultType="com.huitu.cloud.api.ewci.monitor.entity.InfluenceScope">
        SELECT A.ADCD,
               A.ADNM,
               ISNULL(ADINFO.PREVTP, '0') PREVTP,
               ISNULL(GULLY.CNT, 0)       GULLY_COUNT,
               ISNULL(DIKE.CNT, 0)        DIKE_COUNT,
               ISNULL(RES.CNT, 0)         RES_COUNT,
               ISNULL(DAM.CNT, 0)         DAM_COUNT,
               ISNULL(BRID.CNT, 0)        BRID_COUNT,
               ISNULL(WAGA.CNT, 0)        WAGA_COUNT,
               ISNULL(CUL.CNT, 0)         CUL_COUNT
        FROM MDT_ADCDINFO_B A
                 LEFT JOIN IA_C_ADINFO ADINFO ON ADINFO.ADCD = A.ADCD
                 LEFT JOIN (SELECT ADCD, COUNT(1) CNT FROM BNS_REL_GULLY_AD GROUP BY ADCD) GULLY ON GULLY.ADCD = A.ADCD
                 LEFT JOIN (SELECT LEFT (AD_CODE, 12) ADCD, COUNT (1) CNT
                            FROM REL_DIKE_AD
                            GROUP BY LEFT (AD_CODE, 12)) DIKE ON DIKE.ADCD = LEFT (A.ADCD, 12)
            LEFT JOIN (SELECT LEFT (AD_CODE, 12) ADCD, COUNT (1) CNT FROM REL_RES_AD GROUP BY LEFT (AD_CODE, 12)) RES
        ON RES.ADCD = LEFT (A.ADCD, 12)
            LEFT JOIN (SELECT LEFT (ADCD, 12) ADCD, COUNT (1) CNT FROM IA_C_DAMINFO GROUP BY LEFT (ADCD, 12)) DAM ON DAM.ADCD = LEFT (A.ADCD, 12)
            LEFT JOIN (SELECT LEFT (AD_CODE, 12) ADCD, COUNT (1) CNT FROM REL_BRID_AD GROUP BY LEFT (AD_CODE, 12)) BRID ON BRID.ADCD = LEFT (A.ADCD, 12)
            LEFT JOIN (SELECT LEFT (AD_CODE, 12) ADCD, COUNT (1) CNT FROM REL_WAGA_AD GROUP BY LEFT (AD_CODE, 12)) WAGA ON WAGA.ADCD = LEFT (A.ADCD, 12)
            LEFT JOIN (SELECT LEFT (ADCD, 12) ADCD, COUNT (1) CNT FROM IA_C_CULVERT GROUP BY LEFT (ADCD, 12)) CUL ON CUL.ADCD = LEFT (A.ADCD, 12)
        WHERE A.ADCD = #{adcd}
    </select>
    <select id="getFloodPersonList" resultType="com.huitu.cloud.api.ewci.monitor.entity.FloodPerson">
        SELECT T.ADCD,
               T.ZRC,
               P1.REALNM XS_REALNM,
               P1.DUTY   XS_DUTY,
               P1.MOBILE XS_MOBILE,
               P2.REALNM XZ_REALNM,
               P2.DUTY   XZ_DUTY,
               P2.MOBILE XZ_MOBILE,
               P3.REALNM XZC_REALNM,
               P3.DUTY   XZC_DUTY,
               P3.MOBILE XZC_MOBILE,
               P4.REALNM ZRC_REALNM,
               P4.DUTY   ZRC_DUTY,
               P4.MOBILE ZRC_MOBILE,
               P5.REALNM JC_REALNM,
               P5.DUTY   JC_DUTY,
               P5.MOBILE JC_MOBILE,
               P6.REALNM YJ_REALNM,
               P6.DUTY   YJ_DUTY,
               P6.MOBILE YJ_MOBILE,
               P7.REALNM ZY_REALNM,
               P7.DUTY   ZY_DUTY,
               P7.MOBILE ZY_MOBILE,
               P8.REALNM GLDW_REALNM,
               P8.DUTY   GLDW_DUTY,
               P8.MOBILE GLDW_MOBILE
        FROM (SELECT DISTINCT ADCD, ZRC, SORTNO
              FROM BSN_SH_PERSON_B
              WHERE LEFT (ADCD, 12) = LEFT (#{adcd}, 12)) T
                 LEFT JOIN BSN_SH_PERSON_B P1
                           ON P1.ADCD = T.ADCD AND P1.ZRC = T.ZRC AND P1.SORTNO = T.SORTNO AND P1.RYTP = '1'
                 LEFT JOIN BSN_SH_PERSON_B P2
                           ON P2.ADCD = T.ADCD AND P2.ZRC = T.ZRC AND P2.SORTNO = T.SORTNO AND P2.RYTP = '2'
                 LEFT JOIN BSN_SH_PERSON_B P3
                           ON P3.ADCD = T.ADCD AND P3.ZRC = T.ZRC AND P3.SORTNO = T.SORTNO AND P3.RYTP = '3'
                 LEFT JOIN BSN_SH_PERSON_B P4
                           ON P4.ADCD = T.ADCD AND P4.ZRC = T.ZRC AND P4.SORTNO = T.SORTNO AND P4.RYTP = '4'
                 LEFT JOIN BSN_SH_PERSON_B P5
                           ON P5.ADCD = T.ADCD AND P5.ZRC = T.ZRC AND P5.SORTNO = T.SORTNO AND P5.RYTP = '5'
                 LEFT JOIN BSN_SH_PERSON_B P6
                           ON P6.ADCD = T.ADCD AND P6.ZRC = T.ZRC AND P6.SORTNO = T.SORTNO AND P6.RYTP = '6'
                 LEFT JOIN BSN_SH_PERSON_B P7
                           ON P7.ADCD = T.ADCD AND P7.ZRC = T.ZRC AND P7.SORTNO = T.SORTNO AND P7.RYTP = '7'
                 LEFT JOIN BSN_SH_PERSON_B P8
                           ON P8.ADCD = T.ADCD AND P8.ZRC = T.ZRC AND P8.SORTNO = T.SORTNO AND P8.RYTP = '8'
        ORDER BY T.SORTNO ASC
    </select>
    <select id="getRsvrPersonList" resultType="com.huitu.cloud.api.ewci.monitor.entity.RsvrPerson">
        SELECT B.RES_NAME,
               T.ENG_SCAL,
               P1.REALNM AQDX_REALNM,
               P1.MOBILE AQDX_MOBILE,
               P2.REALNM QXJS_REALNM,
               P2.MOBILE QXJS_MOBILE,
               P3.REALNM ZGBM_REALNM,
               P3.MOBILE ZGBM_MOBILE,
               P4.REALNM GLDW_REALNM,
               P4.MOBILE GLDW_MOBILE,
               P5.REALNM XCZS_REALNM,
               P5.MOBILE XCZS_MOBILE
        FROM (SELECT DISTINCT RES_CODE, SORTNO, ENG_SCAL
              FROM BNS_RSVRPERSON_B
              WHERE RES_CODE IN (SELECT A.OBJCD
                                 FROM BSN_OBJONLY_B A
                                          LEFT JOIN BSN_OBJONLY_B B ON B.OBJID = A.OBJID
                                 WHERE A.OBJTP = '6'
                                   AND B.OBJTP = '1'
                                   AND B.OBJCD = #{stcd})) T
                 JOIN ATT_RES_BASE B ON B.RES_CODE = T.RES_CODE
                 LEFT JOIN BNS_RSVRPERSON_B P1
                           ON P1.RES_CODE = T.RES_CODE AND P1.SORTNO = T.SORTNO AND P1.RESPERTP = '1'
                 LEFT JOIN BNS_RSVRPERSON_B P2
                           ON P2.RES_CODE = T.RES_CODE AND P2.SORTNO = T.SORTNO AND P2.RESPERTP = '2'
                 LEFT JOIN BNS_RSVRPERSON_B P3
                           ON P3.RES_CODE = T.RES_CODE AND P3.SORTNO = T.SORTNO AND P3.RESPERTP = '3'
                 LEFT JOIN BNS_RSVRPERSON_B P4
                           ON P4.RES_CODE = T.RES_CODE AND P4.SORTNO = T.SORTNO AND P4.RESPERTP = '4'
                 LEFT JOIN BNS_RSVRPERSON_B P5
                           ON P5.RES_CODE = T.RES_CODE AND P5.SORTNO = T.SORTNO AND P5.RESPERTP = '5'
        ORDER BY T.RES_CODE, T.SORTNO ASC
    </select>
</mapper>
