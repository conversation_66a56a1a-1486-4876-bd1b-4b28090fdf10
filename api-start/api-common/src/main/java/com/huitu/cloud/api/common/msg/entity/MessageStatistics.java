package com.huitu.cloud.api.common.msg.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 预警消息政区统计
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
@ApiModel(value = "预警消息统计信息类", description = "预警消息统计信息")
public class MessageStatistics implements Serializable {
    @ApiModelProperty(value = "政区编码 ")
    private String adcd;
    @ApiModelProperty(value = "政区名称 ")
    private String adnm;
    @ApiModelProperty(value = "短信发送失败条数 ")
    private String msgct0;
    @ApiModelProperty(value = "短信发送成功条数 ")
    private String msgct1;
    @ApiModelProperty(value = "短信请求发送但未处理条数 ")
    private String msgct2;
    @ApiModelProperty(value = "短信总条数 ")
    private String msgct;
    @ApiModelProperty(value = "真实发送计费短信条数（70个字符算一次计费短信）")
    private int realct;

    public int getRealct() {
        return realct;
    }

    public void setRealct(int realct) {
        this.realct = realct;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getMsgct0() {
        return msgct0;
    }

    public void setMsgct0(String msgct0) {
        this.msgct0 = msgct0;
    }

    public String getMsgct1() {
        return msgct1;
    }

    public void setMsgct1(String msgct1) {
        this.msgct1 = msgct1;
    }

    public String getMsgct2() {
        return msgct2;
    }

    public void setMsgct2(String msgct2) {
        this.msgct2 = msgct2;
    }

    public String getMsgct() {
        return msgct;
    }

    public void setMsgct(String msgct) {
        this.msgct = msgct;
    }
}
