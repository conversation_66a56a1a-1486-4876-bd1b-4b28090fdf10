package com.huitu.cloud.api.hsfxt.rsvr.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.hsfxt.rsvr.entity.KbRvsanalyG;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmInfo;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrDmPic;
import com.huitu.cloud.api.hsfxt.rsvr.entity.RsvrObjinfo;

import java.util.List;

/**
 * <p>
 * 风险图-小水库  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
public interface FxtRsvrService {
    /**
     * 查询小水库风险信息
     *
     * @param adcd     政区编码
     * @param name     水库名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<KbRvsanalyG> getFxtRsvrList(String adcd, String name, int pageNum, int pageSize);

    /**
     * 查询小水库河道断面信息
     *
     * @param cid 成果编码
     * @return
     */
    List<RsvrDmInfo> getFxtRsvrDmList(String cid);

    /**
     * 小水库河道断面图
     *
     * @param cid 成果编码
     * @return
     */
    List<RsvrDmPic> getFxtRsvrDmPic(String cid);

    /**
     * 查询测站对应的风险信息（测站编码是否可以对应上cid，如果对应则返回水库信息）
     * @param stcd 测站编码
     * @return
     */
    List<KbRvsanalyG> getFxtRsvrListByStcd(String stcd);

    /**
     * 查询测站防治区信息
     * @param stcd 测站编码
     * @return
     */
    List<RsvrObjinfo> getFzqRsvrListByStcd(String stcd);
}
