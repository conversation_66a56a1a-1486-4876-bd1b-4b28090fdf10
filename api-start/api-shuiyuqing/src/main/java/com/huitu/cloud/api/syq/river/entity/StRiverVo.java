package com.huitu.cloud.api.syq.river.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * <AUTHOR>
 * @since 2019-09-11
 */
/**
 * <AUTHOR>
 */

@ApiModel(value="StRiverVo", description="河道水情表")
public class StRiverVo extends StRiverR {

    @ApiModelProperty(value = "左堤高程")
    @TableField("LDKEL")
    private BigDecimal ldkel;

    @ApiModelProperty(value = "右堤高程")
    @TableField("RDKEL")
    private BigDecimal rdkel;

    @ApiModelProperty(value = "警戒水位")
    @TableField("WRZ")
    private BigDecimal wrz;

    @ApiModelProperty(value = "警戒流量")
    @TableField("WRQ")
    private BigDecimal wrq;

    @ApiModelProperty(value = "保证水位")
    @TableField("GRZ")
    private BigDecimal grz;

    @ApiModelProperty(value = "保证流量")
    @TableField("GRQ")
    private BigDecimal grq;

    @ApiModelProperty(value = "超警戒水位")
    private BigDecimal zwrz;

    @ApiModelProperty(value = "超警戒流量")
    private BigDecimal zwrq;

    @ApiModelProperty(value = "超保证水位")
    private BigDecimal zgrz;

    @ApiModelProperty(value = "超保证流量")
    private BigDecimal zgrq;

    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;

    @ApiModelProperty(value = "河流名称")
    @TableField("RVNM")
    private String rvnm;

    @ApiModelProperty(value = "水系名称")
    @TableField("HNNM")
    private String hnnm;

    @ApiModelProperty(value = "流域名称")
    @TableField("BSNM")
    private String bsnm;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private BigDecimal lttd;

    @ApiModelProperty(value = "站址")
    @TableField("STLC")
    private String stlc;
    @ApiModelProperty(value = "行政区划码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "站类")
    @TableField("STTP")
    private String sttp;

    @ApiModelProperty(value = "报汛等级")
    @TableField("FRGRD")
    private String frgrd;

    @ApiModelProperty(value = "测站方位")
    @TableField("STAZT")
    private Integer stazt;

    @ApiModelProperty(value = "流域级别")
    @TableField("流域级别(1,主要江河;2,中小河流;3,其他)")
    private Integer stbstp;

    @ApiModelProperty(value = "县级行政区划码")
    private String xadcd;

    @ApiModelProperty(value = "县级行政区划名称")
    private String xadnm;

    @ApiModelProperty(value = "乡镇级行政区划码")
    private String xzadcd;

    @ApiModelProperty(value = "乡镇级行政区划名称")
    private String xzadnm;

    @ApiModelProperty(value = "行政区名称")
    @TableField("ADNM")
    private String adnm;
    @ApiModelProperty(value = "偏移经度")
    private BigDecimal plgtd;

    @ApiModelProperty(value = "偏移纬度")
    private BigDecimal plttd;

    public BigDecimal getLdkel() {
        return ldkel;
    }

    public void setLdkel(BigDecimal ldkel) {
        this.ldkel = ldkel;
    }

    public BigDecimal getRdkel() {
        return rdkel;
    }

    public void setRdkel(BigDecimal rdkel) {
        this.rdkel = rdkel;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public BigDecimal getWrz() {
        return wrz;
    }

    public void setWrz(BigDecimal wrz) {
        this.wrz = wrz;
    }

    public BigDecimal getWrq() {
        return wrq;
    }

    public void setWrq(BigDecimal wrq) {
        this.wrq = wrq;
    }

    public BigDecimal getGrz() {
        return grz;
    }

    public void setGrz(BigDecimal grz) {
        this.grz = grz;
    }

    public BigDecimal getGrq() {
        return grq;
    }

    public void setGrq(BigDecimal grq) {
        this.grq = grq;
    }
    public BigDecimal getZwrz() {
        return zwrz;
    }

    public void setZwrz(BigDecimal zwrz) {
        this.zwrz = zwrz;
    }

    public BigDecimal getZwrq() {
        return zwrq;
    }

    public void setZwrq(BigDecimal zwrq) {
        this.zwrq = zwrq;
    }

    public BigDecimal getZgrz() {
        return zgrz;
    }

    public void setZgrz(BigDecimal zgrz) {
        this.zgrz = zgrz;
    }

    public BigDecimal getZgrq() {
        return zgrq;
    }

    public void setZgrq(BigDecimal zgrq) {
        this.zgrq = zgrq;
    }

    public String getStnm() {
        return stnm.trim();
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRvnm() {
        return rvnm;
    }

    public void setRvnm(String rvnm) {
        this.rvnm = rvnm;
    }

    public String getHnnm() {
        return hnnm;
    }

    public void setHnnm(String hnnm) {
        this.hnnm = hnnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getFrgrd() {
        return frgrd;
    }

    public void setFrgrd(String frgrd) {
        this.frgrd = frgrd;
    }

    public Integer getStbstp() {
        return stbstp;
    }

    public void setStbstp(Integer stbstp) {
        this.stbstp = stbstp;
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getXzadcd() {
        return xzadcd;
    }

    public void setXzadcd(String xzadcd) {
        this.xzadcd = xzadcd;
    }

    public String getXzadnm() {
        return xzadnm;
    }

    public void setXzadnm(String xzadnm) {
        this.xzadnm = xzadnm;
    }

    public BigDecimal getPlgtd() {
        return plgtd;
    }

    public void setPlgtd(BigDecimal plgtd) {
        this.plgtd = plgtd;
    }

    public BigDecimal getPlttd() {
        return plttd;
    }

    public void setPlttd(BigDecimal plttd) {
        this.plttd = plttd;
    }

    public Integer getStazt() {
        return stazt;
    }

    public void setStazt(Integer stazt) {
        this.stazt = stazt;
    }
}
