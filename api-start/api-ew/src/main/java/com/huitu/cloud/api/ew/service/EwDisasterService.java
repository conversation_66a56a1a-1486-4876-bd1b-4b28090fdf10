package com.huitu.cloud.api.ew.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.request.EwDisasterQueryRequest;
import com.huitu.cloud.api.ew.entity.request.EwDisasterSaveRequest;
import com.huitu.cloud.api.ew.entity.response.EwDisasterRecordResponse;

import java.io.OutputStream;

/**
 * 灾情反馈服务
 *
 * <AUTHOR>
 */
public interface EwDisasterService {

    /**
     * 获取灾情记录分页列表
     *
     * @param request 请求参数
     * @return 分页列表
     **/
    IPage<EwDisasterRecordResponse> getPageList(EwDisasterQueryRequest request);

    /**
     * 数据导出
     *
     * @param query  查询条件
     * @param output 输出流
     **/
    void dataExport(EwDisasterQueryRequest query, OutputStream output);

    /**
     * 根据灾情ID，获取灾情记录
     *
     * @param disasterId 灾情ID
     * @return 灾情记录
     */
    EwDisasterRecordResponse getRecord(String disasterId);

    /**
     * 保存灾情记录
     *
     * @param request 请求参数
     * @return 受影响的行数
     */
    int saveDisaster(EwDisasterSaveRequest request);

    /**
     * 删除灾情记录
     *
     * @param disasterId 灾情ID
     * @return 受影响的行数
     */
    int deleteDisaster(String disasterId);
}
