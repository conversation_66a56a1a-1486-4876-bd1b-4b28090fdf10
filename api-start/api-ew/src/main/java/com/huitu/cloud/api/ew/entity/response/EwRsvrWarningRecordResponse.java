package com.huitu.cloud.api.ew.entity.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.api.ew.entity.base.EwRsvrWarningRecord;
import com.huitu.cloud.api.ew.entity.common.RsvrWarnGrade;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 水库预警记录响应对象
 */
@ApiModel(value = "水库预警记录响应对象")
public class EwRsvrWarningRecordResponse extends EwRsvrWarningRecord {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "县级行政区划代码")
    @TableField(value = "XADCD")
    private String xadcd;

    @ApiModelProperty(value = "县级行政区划名称")
    @TableField(value = "XADNM")
    private String xadnm;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "水库编码")
    @TableField(value = "RES_CODE")
    private String resCode;

    @ApiModelProperty(value = "水库名称")
    @TableField(value = "RES_NAME")
    private String resName;

    @ApiModelProperty(value = "水库地址")
    @TableField(value = "RES_LOC")
    private String resLoc;

    @ApiModelProperty(value = "水库规模，注：1=大（1）型、2=大（2）型、3=中型、4=小（1）型、5=小（2）型")
    @TableField(value = "ENG_SCAL")
    private String engScal;

    @ApiModelProperty(value = "经度")
    @TableField(value = "LGTD")
    private String lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField(value = "LTTD")
    private String lttd;

    /**
     * 预警等级名称
     */
    public String getWarnGradeName() {
        return RsvrWarnGrade.get(getWarnGradeId()).getName();
    }

    /**
     * 预警等级别名
     */
    public String getWarnGradeAliasName() {
        return RsvrWarnGrade.get(getWarnGradeId()).getAlias();
    }

    /**
     * 预警等级简称
     */
    public String getWarnGradeShortName() {
        return RsvrWarnGrade.get(getWarnGradeId()).getShortened();
    }

    public String getXadcd() {
        return xadcd;
    }

    public void setXadcd(String xadcd) {
        this.xadcd = xadcd;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }
    
    public String getResLoc() {
        return resLoc;
    }

    public void setResLoc(String resLoc) {
        this.resLoc = resLoc;
    }

    public String getEngScal() {
        return engScal;
    }

    public void setEngScal(String engScal) {
        this.engScal = engScal;
    }

    public String getLgtd() {
        return lgtd;
    }

    public void setLgtd(String lgtd) {
        this.lgtd = lgtd;
    }

    public String getLttd() {
        return lttd;
    }

    public void setLttd(String lttd) {
        this.lttd = lttd;
    }
}
