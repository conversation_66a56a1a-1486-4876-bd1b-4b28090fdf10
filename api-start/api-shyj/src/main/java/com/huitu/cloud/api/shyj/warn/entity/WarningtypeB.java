package com.huitu.cloud.api.shyj.warn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 预警类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@TableName("WARNINGTYPE_B")
@ApiModel(value="WarningtypeB对象", description="预警类型表")
public class WarningtypeB extends Model<WarningtypeB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "预警类型ID")
    @TableId(value = "WARNTYPEID", type = IdType.NONE)
    private Integer warntypeid;

    @ApiModelProperty(value = "预警类型名称 ")
    @TableField("WARNTYPENM")
    private String warntypenm;


    public Integer getWarntypeid() {
        return warntypeid;
    }

    public void setWarntypeid(Integer warntypeid) {
        this.warntypeid = warntypeid;
    }

    public String getWarntypenm() {
        return warntypenm;
    }

    public void setWarntypenm(String warntypenm) {
        this.warntypenm = warntypenm;
    }

    @Override
    protected Serializable pkVal() {
        return this.warntypeid;
    }

    @Override
    public String toString() {
        return "WarningtypeB{" +
        "warntypeid=" + warntypeid +
        ", warntypenm=" + warntypenm +
        "}";
    }
}
