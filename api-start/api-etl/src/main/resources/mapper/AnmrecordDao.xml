<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.datamanager.anmrecord.mapper.AnmrecordDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.datamanager.anmrecord.entity.BsnAnmrecordr">
        <id column="REID" property="reid"/>
        <result column="TM" property="tm"/>
        <result column="NOCD" property="nocd"/>
        <result column="TBNM" property="tbnm"/>
        <result column="DATAJS" property="datajs"/>
        <result column="ANMTP" property="anmtp"/>
        <result column="STAFLG" property="staflg"/>
        <result column="UPUSER" property="upuser"/>
        <result column="UPTM" property="uptm"/>
        <result column="FADUSER" property="faduser"/>
        <result column="FADTM" property="fadtm"/>
        <result column="RADUSER" property="raduser"/>
        <result column="RADTM" property="radtm"/>
        <result column="NT" property="nt"/>
        <result column="MODITIME" property="moditime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        REID, TM, nocd, TBNM, DATAJS, ANMTP, STAFLG, UPUSER, UPTM, FADUSER, FADTM, RADUSER, RADTM, NT, MODITIME
    </sql>
    <update id="updateAnmrecordrStaflg">
        update BSN_ANMRECORD_R set STAFLG='7' where STAFLG='6' and stcd=#{stcd} and  CHARINDEX(ANMTP,#{anmtp})>0
    </update>
    <update id="updateBsnAnmrecordr">
        UPDATE  A SET A.TM=B.TM,
         A.NOCD=B.NOCD,
         A.TBNM=B.TBNM,
         A.DATAJS=B.DATAJS,
         A.ANMTP=B.ANMTP,
         A.STAFLG=B.STAFLG,
         A.UPUSER=B.UPUSER,
         A.UPTM=B.UPTM,
         A.FADUSER=B.FADUSER,
         A.FADTM=B.FADTM,
         A.RADUSER=B.RADUSER,
         A.RADTM=B.RADTM,
         A.NT=B.NT,
         A.MODITIME=B.MODITIME,
         A.stcd=B.stcd
        FROM  BSN_ANMRECORD_R A ,${tableName} B
        WHERE A.REID=B.REID
    </update>
    <select id="getBsnAnmrecordr" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.AnmVo">
        select REID, b.TM, nocd, TBNM, DATAJS, ANMTP, STAFLG, UPUSER, UPTM, FADUSER, FADTM, RADUSER, RADTM, b.NT,
        b.MODITIME,b.STCD,b.REDESC, b.cnt
        from
        ( select ROW_NUMBER() over(partition by stcd order by tm desc) rn, count(stcd) over(partition by stcd) cnt,
        * from BSN_ANMRECORD_R
        where 1=1
        <if test="anmtp !=null and anmtp!='' ">
            and CHARINDEX(ANMTP,#{anmtp})>0
        </if>
        <if test="staflgs !=null and staflgs!='' ">
            and CHARINDEX(STAFLG,#{staflgs})>0
        </if>
        <choose>
            <when test="(stm != null and stm !='') or (etm != null and etm !='')">
                <if test="stm != null and stm !=''">
                    and tm>=CONVERT(datetime,#{stm})
                </if>
                <if test="etm != null and etm !=''">
                    and tm &lt;= CONVERT(datetime,#{etm})
                </if>
            </when>
            <otherwise>
                and TM > DATEADD(DD,-7,GETDATE())
            </otherwise>
        </choose>
        )b where rn=1
           <if test="filterCnt != null">
               and b.cnt >= #{filterCnt}
           </if>
        ORDER BY TM DESC,stcd
    </select>
    <select id="getRainStInfo" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.RainStInfo">
        SELECT A.STCD,A.STNM,A.STTP,st.ADCD,C.ADNM,D.STADTPNM,B.ADNM XADNM,e.OFFICER,e.MPHONE,f.SNV,f.INV,F.CTPM5 FROM
        st_stbprp_b a
        inner join bsn_stadtp_b st on st.stcd=a.stcd
        LEFT JOIN bsn_adcd_b b on a.addvcd+'000000000'=b.adcd
        LEFT JOIN bsn_adcd_b C ON ST.ADCD=C.ADCD
        LEFT JOIN bsn_adtpinfo_b D ON ST.STADTP=D.STADTP
        left join ST_STSMTASK_B e on a.STCD=e.STCD
        left join BSN_RAINRULE_B f on a.stcd=f.stcd
        where 1=1 and E.PFL='1'
        <if test="ad!= null and ad != ''">
            and left(a.addvcd,#{level})=#{ad}
        </if>
        <if test="level!= null and level == '4'.toString()">
            and (a.addvcd !='220581' and a.addvcd !='220381')
        </if>
        <if test="stcds!= null and stcds != ''">
            and a.stcd in(${stcds})
        </if>
    </select>
    <select id="getZrStInfo" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.ZrStInfo">
        SELECT A.STCD,A.STNM,A.STTP,st.ADCD,C.ADNM,D.STADTPNM,B.ADNM XADNM,e.OFFICER,e.MPHONE,f.CHV,f.UPV,f.DWV FROM
        st_stbprp_b a
        inner join bsn_stadtp_b st on st.stcd=a.stcd
        LEFT JOIN bsn_adcd_b b on a.addvcd+'000000000'=b.adcd
        LEFT JOIN bsn_adcd_b C ON ST.ADCD=C.ADCD
        LEFT JOIN bsn_adtpinfo_b D ON ST.STADTP=D.STADTP
        left join ST_STSMTASK_B e on a.STCD=e.STCD
        left join BSN_ZRRULE_B f on a.stcd=f.stcd
        where 1=1 and CHARINDEX(sttp,'ZZ,ZQ,RR,RQ')>0
        <if test="ad!= null and ad != ''">
            and left(a.addvcd,#{level})=#{ad}
        </if>
        <if test="level!= null and level == '4'.toString()">
            and (a.addvcd !='220581' and a.addvcd !='220381')
        </if>
        <if test="stcds!= null and stcds != ''">
            and a.stcd in(${stcds})
        </if>
    </select>
    <insert id="insertRain" parameterType="hashmap">
         MERGE INTO BSN_YCRAIN_R  a
            USING (SELECT #{stcd} stcd,#{tm}  tm ,#{editvalue} drp,#{intv} intv,getdate() systm ) b
            ON (a.stcd=b.stcd and a.tm=b.tm)
            WHEN MATCHED THEN
                UPDATE
                SET a.drp = b.drp
            WHEN NOT MATCHED THEN
                 INSERT(stcd,tm,drp,intv,systm) VALUES (b.stcd,b.tm,b.drp,b.intv,b.systm);
    </insert>
    <insert id="insertRiver" parameterType="com.huitu.cloud.api.datamanager.anmrecord.entity.River">
        MERGE INTO ST_RIVER_R  a
	   USING ( select #{stcd} stcd, #{tmTwo} tm, #{editvalue} z, #{q} q, #{xsa} xsa, #{xsavv} xsavv, #{xsmxv} xsmxv, #{flwchrcd} flwchrcd, #{wptn} wptn, #{msqmt} msqmt, #{msamt} msamt, #{msvmt} msvmt )b
      ON (a.stcd=b.stcd and a.tm=b.tm)
      WHEN MATCHED THEN
        UPDATE
        SET a.Q = b.Q, a.Z=b.Z,a.XSA=b.XSA,a.XSAVV=b.XSAVV, a.XSMXV=b.XSMXV, a.FLWCHRCD=b.FLWCHRCD, a.WPTN=b.WPTN, a.MSQMT=b.MSQMT, a.MSAMT=b.MSAMT,a.MSVMT=b.MSVMT
      WHEN NOT MATCHED THEN
        INSERT(STCD, TM, Z, Q,XSA, XSAVV, XSMXV, FLWCHRCD, WPTN, MSQMT, MSAMT,MSVMT)
        VALUES (b.STCD, b.TM, b.Z, b.Q,b.XSA, b.XSAVV, b.XSMXV, b.FLWCHRCD, b.WPTN, b.MSQMT, b.MSAMT,b.MSVMT);

    </insert>
    <insert id="insertRsvr" parameterType="hashmap">
       	MERGE INTO ST_RSVR_R  a
	  USING (select #{stcd} stcd, #{tmTwo} tm, #{editvalue} rz, #{inq} inq, #{w} w, #{otq} otq, #{rwchrcd} rwchrcd ,
            #{rwptn} rwptn , #{inqdr} inqdr, #{msqmt} msqmt, #{blrz} blrz )b
      ON (a.stcd=b.stcd and a.tm=b.tm)
      WHEN MATCHED THEN
        UPDATE
        SET a.RZ=b.RZ ,a.INQ=b.INQ,a.W=b.W,a.OTQ=b.OTQ,a.RWPTN=b.RWPTN
      WHEN NOT MATCHED THEN
        INSERT(STCD, TM, RZ,INQ, W, OTQ, RWCHRCD, RWPTN, INQDR, MSQMT, BLRZ)
        VALUES (b.STCD, b.TM, b.RZ,b.INQ, b.W, b.OTQ, b.RWCHRCD, b.RWPTN, b.INQDR, b.MSQMT, b.BLRZ);
    </insert>
    <insert id="insertAnmLog" parameterType="com.huitu.cloud.api.datamanager.anmrecord.entity.BsnAnmrecordlogr">
        insert into BSN_ANMRECORDLOG_R(REID, TM, ODATAJS, NDATAJS, NT, MODITIME, DOER) values
        (#{reid},#{tm},#{odatajs},#{ndatajs},#{nt},#{moditime},#{doer})
    </insert>
    <insert id="batchInsertBsnAnmrecordr">
        insert into
        ${tableName}(REID,TM,NOCD,TBNM,DATAJS,ANMTP,STAFLG,UPUSER,UPTM,FADUSER,FADTM,RADUSER,RADTM,NT,MODITIME,STCD)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.reid}, #{item.tm}, #{item.nocd}, #{item.tbnm}, #{item.datajs}
            , #{item.anmtp}, #{item.staflg}, #{item.upuser}, #{item.uptm}, #{item.faduser}
            , #{item.fadtm}, #{item.raduser}, #{item.radtm}, #{item.nt}, #{item.moditime}
            , #{item.stcd})
        </foreach>
    </insert>
    <update id="batchUpdateBsnAnmrecordr">
        UPDATE T SET
            TM=S.TM,
            NOCD=S.NOCD,
            TBNM=S.TBNM,
            DATAJS=S.DATAJS,
            ANMTP=S.ANMTP,
            STAFLG=S.STAFLG,
            UPUSER=S.UPUSER,
            UPTM=S.UPTM,
            FADUSER=S.FADUSER,
            FADTM=S.FADTM,
            RADUSER=S.RADUSER,
            RADTM=S.RADTM,
            NT=S.NT,
            MODITIME=S.MODITIME,
            stcd=S.stcd
        FROM (VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.reid}, #{item.tm}, #{item.nocd}, #{item.tbnm}, #{item.datajs}
            , #{item.anmtp}, #{item.staflg}, #{item.upuser}, #{item.uptm}, #{item.faduser}
            , #{item.fadtm}, #{item.raduser}, #{item.radtm}, #{item.nt}, #{item.moditime}
            , #{item.stcd})
        </foreach>
        ) AS S(REID,TM,NOCD,TBNM,DATAJS,ANMTP,STAFLG,UPUSER,UPTM,FADUSER,FADTM,RADUSER,RADTM,NT,MODITIME,STCD), BSN_ANMRECORD_R T
        WHERE T.REID = S.REID
    </update>
    <insert id="batchInsertAnmLog">
        insert into BSN_ANMRECORDLOG_R(REID, TM, ODATAJS, NDATAJS, NT, MODITIME, DOER) values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.reid},#{item.tm},#{item.odatajs},#{item.ndatajs},#{item.nt},#{item.moditime},#{item.doer})
        </foreach>
    </insert>
    <insert id="batchInsertRain">
        MERGE INTO BSN_YCRAIN_R a
        USING (
        <foreach collection="list" index="index" item="item" open="" close="" separator="union">
            SELECT #{item.stcd} stcd,#{item.tm} tm ,#{item.editvalue} drp,#{item.intv} intv,getdate() systm
        </foreach>
        ) b
        ON (a.stcd=b.stcd and a.tm=b.tm)
        WHEN MATCHED THEN
        UPDATE
        SET a.drp = b.drp
        WHEN NOT MATCHED THEN
        INSERT(stcd,tm,drp,intv,systm) VALUES (b.stcd,b.tm,b.drp,b.intv,b.systm);
    </insert>
    <insert id="batchInsertRiver">
        MERGE INTO ST_RIVER_R a
        USING (
        <foreach collection="list" index="index" item="item" open="" close="" separator="union">
            select #{item.stcd} stcd, #{item.tmTwo} tm, #{item.editvalue} z, #{item.q} q, #{item.xsa} xsa, #{item.xsavv} xsavv, #{item.xsmxv} xsmxv,
            #{item.flwchrcd} flwchrcd, #{item.wptn} wptn, #{item.msqmt} msqmt, #{item.msamt} msamt, #{item.msvmt} msvmt
        </foreach>
        )b
        ON (a.stcd=b.stcd and a.tm=b.tm)
        WHEN MATCHED THEN
        UPDATE
        SET a.Q = b.Q, a.Z=b.Z,a.XSA=b.XSA,a.XSAVV=b.XSAVV, a.XSMXV=b.XSMXV, a.FLWCHRCD=b.FLWCHRCD, a.WPTN=b.WPTN,
        a.MSQMT=b.MSQMT, a.MSAMT=b.MSAMT,a.MSVMT=b.MSVMT
        WHEN NOT MATCHED THEN
        INSERT(STCD, TM, Z, Q,XSA, XSAVV, XSMXV, FLWCHRCD, WPTN, MSQMT, MSAMT,MSVMT)
        VALUES (b.STCD, b.TM, b.Z, b.Q,b.XSA, b.XSAVV, b.XSMXV, b.FLWCHRCD, b.WPTN, b.MSQMT, b.MSAMT,b.MSVMT);
    </insert>
    <insert id="batchInsertRsvr">
        MERGE INTO ST_RSVR_R a
        USING (
        <foreach collection="list" index="index" item="item" open="" close="" separator="union">
            select #{item.stcd} stcd, #{item.tmTwo} tm, #{item.editvalue} rz, #{item.inq} inq, #{item.w} w, #{item.otq} otq, #{item.rwchrcd} rwchrcd ,
            #{item.rwptn} rwptn , #{item.inqdr} inqdr, #{item.msqmt} msqmt, #{item.blrz} blrz
        </foreach>
        )b
        ON (a.stcd=b.stcd and a.tm=b.tm)
        WHEN MATCHED THEN
        UPDATE
        SET a.RZ=b.RZ ,a.INQ=b.INQ,a.W=b.W,a.OTQ=b.OTQ,a.RWPTN=b.RWPTN
        WHEN NOT MATCHED THEN
        INSERT(STCD, TM, RZ,INQ, W, OTQ, RWCHRCD, RWPTN, INQDR, MSQMT, BLRZ)
        VALUES (b.STCD, b.TM, b.RZ,b.INQ, b.W, b.OTQ, b.RWCHRCD, b.RWPTN, b.INQDR, b.MSQMT, b.BLRZ);
    </insert>
    <select id="getRiverSw" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.StSwVo">
         select top 5 stcd,tm,z from ST_RIVER_R
         where stcd=#{stcd} order by tm desc
    </select>
    <select id="getRsvrSw" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.StSwVo">
          select top 5 stcd,tm,rz z from ST_RSVR_R where stcd=#{stcd}
		  and tm>DATEADD(DD,-30,getdate()) order by tm desc
    </select>
    <select id="getAdAvg" resultType="java.lang.String">
         select Convert(decimal(18,1),AVG(DRP)) avgDrp  from (select STCD,TM,DRP from st_pptn_r
		   where  tm>= CONVERT(datetime,#{tm})  and tm &lt;DATEADD(MINUTE,1,#{tm}) ) A,ST_STBPRP_B B
		    WHERE  A.STCD=B.STCD AND ADDVCD=LEFT(#{adcd},6)
    </select>
    <select id="getRiverSwByTm" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.StSwVo">
         select top 1 stcd,tm,isnull(z,0)z from ST_RIVER_R WITH(NOLOCK)
         where stcd=#{stcd} AND tm>=DATEADD(HH,-2,#{tm})  order by tm desc
    </select>
    <select id="getRsvrSwByTm" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.StSwVo">
          select top 1 stcd,tm,isnull(rz,0) z from ST_RSVR_R WITH(NOLOCK) where stcd=#{stcd}
		  and tm>=DATEADD(HH,-2,#{tm}) order by tm desc
    </select>
    <select id="getAnmrecordrByPage" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.AnmVo">
        select REID, a.TM, nocd, a.TBNM, DATAJS, ANMTP, STAFLG, UPUSER, UPTM, FADUSER, FADTM, RADUSER, RADTM, a.NT,
        a.MODITIME,a.STCD,a.REDESC,
        STNM,st.ADCD,C.ADNM,D.STADTPNM,ad.ADNM XADNM,e.OFFICER,e.MPHONE,z.type reltbnm
        from
        BSN_ANMRECORD_R a
        inner join st_stbprp_b stb on stb.stcd=a.stcd
        inner join bsn_stadtp_b st on st.stcd=a.stcd
        LEFT JOIN bsn_adcd_b ad on stb.addvcd+'000000000'=ad.adcd
        LEFT JOIN bsn_adcd_b C ON ST.ADCD=C.ADCD
        LEFT JOIN bsn_adtpinfo_b D ON ST.STADTP=D.STADTP
        left join ST_STSMTASK_B e on a.STCD=e.STCD
        left join BSN_STSTOPTAB_B z on z.tbnm=a.TBNM
        where 1=1
        <if test="map.tbnm !=null and map.tbnm!='' ">
            and CHARINDEX(tbnm,#{map.tbnm})>0
        </if>
        <if test="map.anmtp !=null and map.anmtp!='' ">
            and CHARINDEX(ANMTP,#{map.anmtp})>0
        </if>
        <if test="map.staflgs !=null and map.staflgs!='' ">
            and CHARINDEX(STAFLG,#{map.staflgs})>0
        </if>
        <if test="map.ad!= null and map.ad != ''">
            and left(stb.addvcd,#{map.level})=#{map.ad}
        </if>
        <if test="map.level!= null and map.level == '4'.toString()">
            and (stb.addvcd !='220581' and stb.addvcd !='220381')
        </if>
        ORDER BY TM DESC,a.stcd
    </select>

    <select id="getAnmrecordrStcdByPage" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.AnmVo">
        select REID, a.TM, nocd, a.TBNM, DATAJS, ANMTP, STAFLG, UPUSER, UPTM, FADUSER, FADTM, RADUSER, RADTM, a.NT,
        a.MODITIME,a.STCD,a.REDESC,
        STNM,st.ADCD,C.ADNM,D.STADTPNM,ad.ADNM XADNM,e.OFFICER,e.MPHONE,z.type reltbnm
        from
        BSN_ANMRECORD_R a
        inner join st_stbprp_b stb on stb.stcd=a.stcd
        inner join bsn_stadtp_b st on st.stcd=a.stcd
        LEFT JOIN bsn_adcd_b ad on stb.addvcd+'000000000'=ad.adcd
        LEFT JOIN bsn_adcd_b C ON ST.ADCD=C.ADCD
        LEFT JOIN bsn_adtpinfo_b D ON ST.STADTP=D.STADTP
        left join ST_STSMTASK_B e on a.STCD=e.STCD
        left join BSN_STSTOPTAB_B z on z.tbnm=a.TBNM
        where 1=1
        <if test="map.stcd!= null and map.stcd != ''">
            and a.STCD = #{map.stcd}
        </if>
        <if test="map.anmtp !=null and map.anmtp!='' ">
            and CHARINDEX(ANMTP,#{map.anmtp})>0
        </if>
        <if test="map.staflgs !=null and map.staflgs!='' ">
            and CHARINDEX(STAFLG,#{map.staflgs})>0
        </if>
        <if test="map.ad!= null and map.ad != ''">
            and left(stb.addvcd,#{map.level})=#{map.ad}
        </if>
        <if test="map.stm != null and map.stm !=''">
            and a.tm>=CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and a.tm &lt;= CONVERT(datetime,#{map.etm})
        </if>
        ORDER BY TM DESC,a.stcd
    </select>

    <select id="getAnmrecordrByExpt" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.AnmVo">
        select REID, a.TM, nocd, TBNM, DATAJS, ANMTP, STAFLG, UPUSER, UPTM, FADUSER, FADTM, RADUSER, RADTM, b.NT,
        b.MODITIME,a.STCD,REDESC,
        STNM,st.ADCD,C.ADNM,D.STADTPNM,ad.ADNM XADNM,e.OFFICER,e.MPHONE
        from
        (select min(tm) tm ,stcd from BSN_ANMRECORD_R group by stcd) a
        inner join BSN_ANMRECORD_R b on a.stcd=b.stcd and a.tm=b.tm
        inner join st_stbprp_b stb on stb.stcd=a.stcd
        inner join bsn_stadtp_b st on st.stcd=a.stcd
        LEFT JOIN bsn_adcd_b ad on stb.addvcd+'000000000'=ad.adcd
        LEFT JOIN bsn_adcd_b C ON ST.ADCD=C.ADCD
        LEFT JOIN bsn_adtpinfo_b D ON ST.STADTP=D.STADTP
        left join ST_STSMTASK_B e on a.STCD=e.STCD
        where STAFLG='6' and anmtp='1'
        <if test="map.ad!= null and map.ad != ''">
            and left(stb.addvcd,#{map.level})=#{map.ad}
        </if>
        <if test="map.level!= null and map.level == '4'.toString()">
            and (stb.addvcd !='220581' and stb.addvcd !='220381')
        </if>
        order by a.tm desc
    </select>
    <select id="getCountByAd" resultType="java.lang.Integer">
        SELECT count(1)
        FROM st_stbprp_b a,BSN_ANMRECORD_R b
        where a.stcd=b.stcd
        <if test="ad!= null and ad != ''">
            and left(a.addvcd,#{level})=#{ad}
        </if>
        <if test="level!= null and level == '4'.toString()">
            and (a.addvcd !='220581' and a.addvcd !='220381')
        </if>
        <if test="staflgs !=null and staflgs!='' ">
            and CHARINDEX(STAFLG,#{staflgs})>0
        </if>
        and b.TM > DATEADD(DD,-7,GETDATE())
    </select>
    <select id="getAnmRzSt" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.RzStInfo">
        select distinct a.stcd,b.type tbnm,stb.stnm
        from BSN_ANMRECORD_R a
        inner join st_stbprp_b stb on stb.stcd=a.stcd
        left join BSN_STSTOPTAB_B b on a.TBNM=b.tbnm
        where 1=1
        <if test="map.anmtp !=null and map.anmtp!='' ">
            and CHARINDEX(ANMTP,#{map.anmtp})>0
        </if>
        <if test="map.staflgs !=null and map.staflgs!='' ">
            and CHARINDEX(STAFLG,#{map.staflgs})>0
        </if>
        <if test="map.ad!= null and map.ad != ''">
            and left(stb.addvcd,#{map.level})=#{map.ad}
        </if>
        <if test="map.level!= null and map.level == '4'.toString()">
            and (stb.addvcd !='220581' and stb.addvcd !='220381')
        </if>
        order by a.stcd,tbnm,stb.stnm
    </select>
    <select id="getAnmrecordrs" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.BsnAnmrecordr">
        select REID,TM,NOCD,a.TBNM,DATAJS,ANMTP,STAFLG,UPUSER,UPTM,FADUSER,FADTM,RADUSER,RADTM,NT,MODITIME,stcd
        from BSN_ANMRECORD_R a left join BSN_STSTOPTAB_B b on a.TBNM=b.tbnm
        where STAFLG='6'
        <if test="stcd !=null and stcd!='' ">
            and stcd=#{stcd}
        </if>
        <if test="tbnm !=null and tbnm!='' ">
            and b.type=#{tbnm}
        </if>
    </select>

    <select id="getAnmStatisticsList" resultType="com.huitu.cloud.api.datamanager.anmrecord.entity.AnmStatistics">
        SELECT a.ADCD, COUNT(a.STCD) ABNORMAL
        FROM (SELECT DISTINCT LEFT(t.NOCD, 6) + '000000000' ADCD, t.STCD FROM BSN_ANMRECORD_R t
        WHERE t.NOCD LIKE '${adcd}%' AND t.tm &gt; #{bgtm} AND t.tm &lt;= #{endtm}) a
        GROUP BY a.ADCD
    </select>
</mapper>
