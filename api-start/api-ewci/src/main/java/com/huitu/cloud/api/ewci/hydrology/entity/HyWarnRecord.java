package com.huitu.cloud.api.ewci.hydrology.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 洪水预警记录
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警记录")
public class HyWarnRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARNID")
    private String warnId;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "预警时间")
    @TableField(value = "WTM")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wtm;

    @ApiModelProperty(value = "预警值")
    @TableField(value = "WVAL")
    private Double wval;

    @ApiModelProperty(value = "预警指标类型")
    @TableField(value = "WTYPE")
    private String wtype;

    @ApiModelProperty(value = "预警等级（1=红色、2=橙色、3=黄色、4=蓝色）")
    @TableField(value = "WGRD")
    private Integer wgrd;

    @ApiModelProperty(value = "预警指标")
    @TableField(value = "WIDX")
    private Double widx;

    @ApiModelProperty(value = "超预警指标值")
    @TableField(value = "EWIDX")
    private Double ewidx;

    @ApiModelProperty(value = "状态（0=待编辑、1=待签发、2=已签发、3=已忽略）")
    @TableField(value = "STATE")
    private String state;

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public Date getWtm() {
        return wtm;
    }

    public void setWtm(Date wtm) {
        this.wtm = wtm;
    }

    public Double getWval() {
        return wval;
    }

    public void setWval(Double wval) {
        this.wval = wval;
    }

    public String getWtype() {
        return wtype;
    }

    public void setWtype(String wtype) {
        this.wtype = wtype;
    }

    public Integer getWgrd() {
        return wgrd;
    }

    public void setWgrd(Integer wgrd) {
        this.wgrd = wgrd;
    }

    public Double getWidx() {
        return widx;
    }

    public void setWidx(Double widx) {
        this.widx = widx;
    }

    public Double getEwidx() {
        return ewidx;
    }

    public void setEwidx(Double ewidx) {
        this.ewidx = ewidx;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
