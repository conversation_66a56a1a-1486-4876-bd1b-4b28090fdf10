package com.huitu.cloud.api.ewci.hydfcst.mapper;

import com.huitu.cloud.api.ewci.hydfcst.entity.*;

import java.util.List;
import java.util.Map;

/**
 * 水文预报数据访问接口
 */
public interface HydfcstDao {

    /**
     * 获取河道预报数据
     *
     * @param params 查询参数
     * @return 河道预报数据列表
     */
    List<StForecast> getHydfcstStForecastData(Map<String, Object> params);

    /**
     * 获取水库预报数据
     *
     * @param params 查询参数
     * @return 水库预报数据列表
     */
    List<StReglat> getHydfcstStReglatData(Map<String, Object> params);

    /**
     * 获取水文预报时间批次。
     *
     * @param params 查询对象
     * @return 水文预报时间批次列表
     */
    List<String> listHydfcstBatch(Map<String, Object> params);


    /**
     * 河道预报统计结果。
     *
     * @param queryParam 查询对象
     * @return 河道预报统计结果数据
     */
    List<HydFcstRvStat> listHydFcstRvStat(Map<String, Object> queryParam);

    /**
     * 水库预报统计结果。
     *
     * @param queryParam 查询对象
     * @return 水库预报统计结果数据
     */
    List<HydFcstRsStat> listHydFcstRsStat(Map<String, Object> queryParam);
}
