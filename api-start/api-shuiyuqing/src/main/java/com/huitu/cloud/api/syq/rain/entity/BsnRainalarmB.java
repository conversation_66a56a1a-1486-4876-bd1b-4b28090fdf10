package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-26
 */
@TableName("BSN_RAINALARM_B")
@ApiModel(value="BSN_RAINALARM_B", description="降雨告警指标设置")
public class BsnRainalarmB extends Model<BsnRainalarmB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "编号")
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    @ApiModelProperty(value = "超警1小时")
    @TableField("WarnDrp1h")
    private Double warndrp1h;

    @ApiModelProperty(value = "超危1小时")
    @TableField("MoveDrp1h")
    private Double movedrp1h;

    @ApiModelProperty(value = "超警3小时")
    @TableField("WarnDrp3h")
    private Double warndrp3h;

    @ApiModelProperty(value = "超危3小时")
    @TableField("MoveDrp3h")
    private Double movedrp3h;

    @ApiModelProperty(value = "超警6小时")
    @TableField("WarnDrp6h")
    private Double warndrp6h;

    @ApiModelProperty(value = "超危6小时")
    @TableField("MoveDrp6h")
    private Double movedrp6h;

    @ApiModelProperty(value = "超警12小时")
    @TableField("WarnDrp12h")
    private Double warndrp12h;

    @ApiModelProperty(value = "超危12小时")
    @TableField("MoveDrp12h")
    private Double movedrp12h;

    @ApiModelProperty(value = "超警24小时")
    @TableField("WarnDrp24h")
    private Double warndrp24h;

    @ApiModelProperty(value = "超危24小时")
    @TableField("MoveDrp24h")
    private Double movedrp24h;

    @ApiModelProperty(value = "备注")
    @TableField("Remarks")
    private String remarks;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Double getWarndrp1h() {
        return warndrp1h;
    }

    public void setWarndrp1h(Double warndrp1h) {
        this.warndrp1h = warndrp1h;
    }

    public Double getMovedrp1h() {
        return movedrp1h;
    }

    public void setMovedrp1h(Double movedrp1h) {
        this.movedrp1h = movedrp1h;
    }

    public Double getWarndrp3h() {
        return warndrp3h;
    }

    public void setWarndrp3h(Double warndrp3h) {
        this.warndrp3h = warndrp3h;
    }

    public Double getMovedrp3h() {
        return movedrp3h;
    }

    public void setMovedrp3h(Double movedrp3h) {
        this.movedrp3h = movedrp3h;
    }

    public Double getWarndrp6h() {
        return warndrp6h;
    }

    public void setWarndrp6h(Double warndrp6h) {
        this.warndrp6h = warndrp6h;
    }

    public Double getMovedrp6h() {
        return movedrp6h;
    }

    public void setMovedrp6h(Double movedrp6h) {
        this.movedrp6h = movedrp6h;
    }

    public Double getWarndrp12h() {
        return warndrp12h;
    }

    public void setWarndrp12h(Double warndrp12h) {
        this.warndrp12h = warndrp12h;
    }

    public Double getMovedrp12h() {
        return movedrp12h;
    }

    public void setMovedrp12h(Double movedrp12h) {
        this.movedrp12h = movedrp12h;
    }

    public Double getWarndrp24h() {
        return warndrp24h;
    }

    public void setWarndrp24h(Double warndrp24h) {
        this.warndrp24h = warndrp24h;
    }

    public Double getMovedrp24h() {
        return movedrp24h;
    }

    public void setMovedrp24h(Double movedrp24h) {
        this.movedrp24h = movedrp24h;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
