<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.RiverPersonTmpDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="batchInsert">
        INSERT INTO BNS_RIVERPERSON_TMP (ID, NO, XADCD, BAS_NAME, RV_NAME, ADNM, XZ_REALNM, XZ_POST, XZ_OFFICE_PHONE,
        XZ_MOBILE_PHONE, JS_REALNM, JS_POST, JS_OFFICE_PHONE, JS_MOBILE_PHONE)
        SELECT T.ID, T.NO, T.XADCD, T.BAS_NAME, T.RV_NAME, T.ADNM, T.XZ_REALNM, T.XZ_POST, T.XZ_OFFICE_PHONE,
        T.XZ_MOBILE_PHONE, T.JS_REALNM, T.JS_POST, T.JS_OFFICE_PHONE, T.JS_MOBILE_PHONE FROM (VALUES
        <foreach collection="map.list" item="item" separator=",">
            (NEWID(), #{map.batchNo}, #{map.xadcd}, #{item.basName}, #{item.rvName}, #{item.adnm}, #{item.xzRealnm},
            #{item.xzPost}, #{item.xzOfficePhone}, #{item.xzMobilePhone}, #{item.jsRealnm}, #{item.jsPost},
            #{item.jsOfficePhone}, #{item.jsMobilePhone})
        </foreach>) AS T(ID, NO, XADCD, BAS_NAME, RV_NAME, ADNM, XZ_REALNM, XZ_POST, XZ_OFFICE_PHONE, XZ_MOBILE_PHONE,
        JS_REALNM, JS_POST, JS_OFFICE_PHONE, JS_MOBILE_PHONE)
    </insert>
</mapper>
