package com.huitu.cloud.api.fusion.controller;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.fusion.entity.request.FusionWarnMessageReq;
import com.huitu.cloud.api.fusion.entity.request.FusionWarningSummaryReq;
import com.huitu.cloud.api.fusion.entity.response.FusionWarnMessageStatisticsResp;
import com.huitu.cloud.api.fusion.entity.response.FusionWarningSummaryResp;
import com.huitu.cloud.api.fusion.service.FusionStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 监测预警统计服务
 *
 * <AUTHOR>
 */
@Api(tags = "001监测预警统计服务")
@RestController
@RequestMapping("/api/fusion/statistics")
public class FusionStatisticsResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "2174a3d2-b5bb-4620-9393-f63f2a6261d9";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private  FusionStatisticsService baseService;

    @ApiOperation(value = "获取监测预警汇总统计信息", notes = "作者：zhangbx")
    @PostMapping("select-warn-summary")
    public ResponseEntity<SuccessResponse<FusionWarningSummaryResp>> getWarningSummary(@Validated @RequestBody FusionWarningSummaryReq query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getWarningSummary(query)));
    }

    @ApiOperation(value = "政区预警统计", notes = "作者：zhangbx")
    @PostMapping(value = "select-ad-warn-statistics")
    public ResponseEntity<SuccessResponse<FusionWarnMessageStatisticsResp>> getAdWarnStatistics(@RequestBody FusionWarnMessageReq query) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.getAdWarnStatistics(query)));
    }

    @ApiOperation(value = "政区预警统计下级列表", notes = "作者：zhangbx")
    @PostMapping(value = "select-ad-warn-statistics-tree-list")
    public ResponseEntity<SuccessResponse<List<FusionWarnMessageStatisticsResp>>> getAdWarnStatisticsTreeList(@RequestBody FusionWarnMessageReq query) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.getAdWarnStatisticsTreeList(query)));
    }
}
