<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.VillagePersonDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" useCache="false" resultType="com.huitu.cloud.api.ewci.person.entity.VillagePersonVo">
        SELECT ROW_NUMBER() OVER(ORDER BY A.ADCD, B.RES_CODE) SORTNO,A.ADCD, B.RES_CODE, B.RES_NAME, D1.ADNM XADNM, D2.ADNM ZADNM, D3.ADNM XZADNM, D4.ADNM ZRADNM, C.XZREALNM
        XZREALNM, C.XZMOBILE XZMOBILE, C.CT<PERSON> CTREALNM, C.CTMOBI<PERSON> CTMOBILE, C.<PERSON>N<PERSON> SNO,C.TS TS
        FROM
        (SELECT A.objcd ADCD, B.objcd RES_CODE FROM bsn_objonly_b A
        LEFT JOIN bsn_objonly_b B ON A.objid = B.objid AND B.objtp = '6' WHERE A.objtp = '31' AND B.objcd IS NOT NULL
        ) A
        LEFT JOIN ATT_RES_BASE B ON A.RES_CODE = B.RES_CODE
        LEFT JOIN (SELECT T.ADCD ADCD, T.SNO, B1.REALNM XZREALNM, B1.MOBILE XZMOBILE, B2.REALNM CTREALNM, B2.MOBILE CTMOBILE,
        COALESCE(B1.TS,B2.TS) TS
                   FROM (SELECT DISTINCT M.ADCD,M.SNO SNO FROM BNS_VILLAGEPERSON_B M
                            <where>
                                <if test="map.realnm != null and map.realnm != ''">
                                    and CHARINDEX(#{map.realnm}, M.REALNM) >0
                                </if>
                                <if test="map.mobile != null and map.mobile != ''">
                                    AND CHARINDEX(#{map.mobile}, M.MOBILE) >0
                                </if>
                            </where>
                           ) T
                    LEFT JOIN BNS_VILLAGEPERSON_B B1 ON T.ADCD = B1.ADCD AND T.SNO = B1.SNO AND B1.PERTP = '1'
                    LEFT JOIN BNS_VILLAGEPERSON_B B2 ON T.ADCD = B2.ADCD AND T.SNO = B2.SNO AND B2.PERTP = '2'
                    ) C ON A.ADCD = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B D1 ON D1.ADCD = left (A.ADCD, 6)+'000000000'
        LEFT JOIN MDT_ADCDINFO_B D2 ON D2.ADCD = left (A.ADCD, 9)+'000000'
        LEFT JOIN MDT_ADCDINFO_B D3 ON D3.ADCD = left (A.ADCD, 12)+'000'
        LEFT JOIN MDT_ADCDINFO_B D4 ON D4.ADCD = A.ADCD AND D4.ADLVL = '6'
        WHERE B.RES_CODE IS NOT NULL AND C.ADCD IS NOT NULL
        <if test="map.adcd != null and map.adcd != ''">
            and left(A.ADCD,#{map.level})=left(#{map.adcd},#{map.level})
        </if>
        <if test="map.level !=null and map.level =='4'.toString()">
            and left(A.ADCD,6) != '220581' and left(A.ADCD,6) != '220381'
        </if>
        <if test="map.resName != null and map.resName != ''">
            AND CHARINDEX(#{map.resName}, B.RES_NAME) >0
        </if>
        <if test="map.scals != null and map.scals != ''">
            AND B.ENG_SCAL = #{map.scals}
        </if>
        <if test="map.udtm !=null and map.udtm =='1'.toString()">
            and YEAR(TS) != YEAR(GETDATE())
        </if>
        ORDER BY A.ADCD , B.RES_CODE
    </select>
    <select id="getVillageSummaryList" resultType="com.huitu.cloud.api.ewci.person.entity.VillageSummaryVo">
        SELECT ADCD, ADNM, PADCD, ALLNUM, XZCOUNT, CTCOUNT FROM (
        <if test="map.include == '1'.toString()">
            SELECT 0 SORTNO, A.ADCD ADCD, B.ADNM ADNM, B.PADCD PADCD,A.ALLNUM ,A.XZCOUNT, A.CTCOUNT FROM (
            SELECT #{map.adcd} ADCD,COUNT(DISTINCT A.REALNM+A.MOBILE) ALLNUM,
            COUNT(DISTINCT B.REALNM+B.MOBILE) XZCOUNT,
            COUNT(DISTINCT C.REALNM+C.MOBILE) CTCOUNT
            FROM BNS_VILLAGEPERSON_B A
            LEFT JOIN BNS_VILLAGEPERSON_B B ON A.ADCD = B.ADCD AND A.SNO = B.SNO AND B.PERTP = '1'
            LEFT JOIN BNS_VILLAGEPERSON_B C ON A.ADCD = C.ADCD AND A.SNO = C.SNO AND C.PERTP = '2'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">
                AND LEFT(A.ADCD, 6) NOT IN ('220581')
            </if>
            ) A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = A.ADCD
            WHERE EXISTS (SELECT 8 FROM MDT_ADCDINFO_E WHERE ADTP = '1' AND ADCD = A.ADCD)
        </if>
        <if test="map.include == '1'.toString() and map.level &lt;= 4">
            UNION ALL
        </if>
        <if test="map.level &lt;= 4">
            SELECT B.SORTNO, A.ADCD ADCD, A.ADNM ADNM, A.PADCD PADCD,C.ALLNUM ,C.XZCOUNT, C.CTCOUNT
            FROM MDT_ADCDINFO_B A
            LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
            LEFT JOIN (SELECT ADCD, COUNT(DISTINCT AREALNM+AMOBILE ) ALLNUM,
            COUNT(DISTINCT BREALNM+BMOBILE) XZCOUNT,
            COUNT(DISTINCT CREALNM+CMOBILE) CTCOUNT
            FROM (SELECT LEFT(LEFT(A.ADCD, #{map.lowLevel}) + '000000000000000', 15) ADCD, A.MOBILE AMOBILE, A.REALNM
            AREALNM,
            B.MOBILE BMOBILE, B.REALNM BREALNM, C.MOBILE CMOBILE, C.REALNM CREALNM
            FROM BNS_VILLAGEPERSON_B A
            LEFT JOIN BNS_VILLAGEPERSON_B B ON A.ADCD = B.ADCD AND A.SNO = B.SNO AND B.PERTP = '1'
            LEFT JOIN BNS_VILLAGEPERSON_B C ON A.ADCD = C.ADCD AND A.SNO = C.SNO AND C.PERTP = '2'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND LEFT(A.ADCD, 6) NOT IN ('220581')
            <if test="map.level == '2'.toString()">
                UNION ALL
                SELECT '220581000000000' ADCD, A.MOBILE AMOBILE, A.REALNM AREALNM,
                B.MOBILE BMOBILE, B.REALNM BREALNM, C.MOBILE CMOBILE, C.REALNM CREALNM
                FROM BNS_VILLAGEPERSON_B A
                LEFT JOIN BNS_VILLAGEPERSON_B B ON A.ADCD = B.ADCD AND A.SNO = B.SNO AND B.PERTP = '1'
                LEFT JOIN BNS_VILLAGEPERSON_B C ON A.ADCD = C.ADCD AND A.SNO = C.SNO AND C.PERTP = '2'
                WHERE LEFT(A.ADCD, 6) = '220581'
            </if>
            ) A GROUP BY ADCD) C ON C.ADCD = A.ADCD
            WHERE A.PADCD = #{map.adcd}
        </if>
        ) A
        ORDER BY SORTNO ASC
    </select>
    <select id="batchImport" statementType="CALLABLE" useCache="false"
            resultType="com.huitu.cloud.api.ewci.person.entity.BnsVillagePersonTmp">
        {CALL PROC_IMPORT_VILLAGE_PERSON(#{batchNo, mode=IN})}
    </select>
    <insert id="insertList">
        INSERT INTO
        BNS_VILLAGEPERSON_B(ADCD,PERTP,SNO,REALNM,MOBILE,TS)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.adcd},#{item.pertp},#{item.sno},#{item.realnm},#{item.mobile},#{item.ts})
        </foreach>
    </insert>
    <delete id="delAdcdAndSno">
        DELETE
        FROM BNS_VILLAGEPERSON_B
        WHERE ADCD = #{map.adcd}
          AND SNO = #{map.sno}
    </delete>
</mapper>
