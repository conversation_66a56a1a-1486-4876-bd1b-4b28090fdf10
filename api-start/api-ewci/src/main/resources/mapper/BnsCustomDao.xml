<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.custom.mapper.BnsCustomDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getCustomListByUserId" resultType="com.huitu.cloud.api.ewci.custom.entity.BnsCustom">
        SELECT USER_ID, MODULE, SNO, TS FROM BNS_CUSTOM_B
        WHERE USER_ID = #{userId}
        ORDER BY SNO ASC
    </select>

    <insert id="insert">
        INSERT INTO BNS_CUSTOM_B (USER_ID, MODULE, SNO)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userId},#{item.module},#{item.sno})
        </foreach>
    </insert>

    <delete id="delete">
        delete
        from BNS_CUSTOM_B
        where USER_ID = #{userId}
    </delete>

</mapper>