package com.huitu.cloud.api.hyst.survey.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.util.DateUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.hyst.survey.entity.HystStats;
import com.huitu.cloud.api.hyst.survey.entity.SurveyQuery;
import com.huitu.cloud.api.hyst.survey.entity.SurveyVo;
import com.huitu.cloud.api.hyst.survey.mapper.SurveyDao;
import com.huitu.cloud.api.hyst.survey.service.SurveyService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小水电概况 实现类
 *
 * <AUTHOR>
 */
@Service
public class SurveyServiceImpl implements SurveyService {


    private SurveyDao baseDao;

    public SurveyServiceImpl(SurveyDao baseDao) {
        this.baseDao = baseDao;
    }


    @Override
    public IPage<SurveyVo> getSurveyPage(SurveyQuery query) throws Exception {
        Page page = new Page<>(query.getPageNum(), query.getPageSize());
        Map<String, Object> param = new HashMap<>();
        Map<String, Object> dateMap = getStmAndEtm(query.getAssPer());
        if (!CollectionUtils.isEmpty(dateMap)) {
            dateMap.forEach(param::putIfAbsent);
        }
        if ("1".equals(query.getStatus())) {
            param.put("ecoOnline", '1');
        } else if ("2".equals(query.getStatus())) {
            param.put("ecoOnline", '0');
        } else if ("3".equals(query.getStatus())) {
            param.put("ecoflow", '1');
        } else if ("4".equals(query.getStatus())) {
            param.put("ecoflow", '0');
        }
        param.put("hystName", query.getHystName());
        param.put("ass", query.getAss());
        param.put("assPer", query.getAssPer());
        IPage<SurveyVo> resultMap = baseDao.getSurveyPage(page, param);
        return resultMap;
    }

    @Override
    public List<SurveyVo> getDetailsList(SurveyQuery query) throws Exception {
        String assPer = baseDao.getAssPer(query.getHystCode(), query.getRecTime());
        Map<String, Object> param = new HashMap<>();
        if ("1".equals(assPer)) {
            param.put("stm", DateUtils.parseDate(DateFormatUtils.format(new Date(), "yyyy-MM-01")));
        } else if ("2".equals(assPer)) {
            param.put("stm", DateUtils.parseDate(DateFormatUtils.format(new Date(), "yyyy-01-01")));
        }
        param.put("hystCode", query.getHystCode());
        param.put("assPer", String.valueOf(Integer.parseInt(assPer) - 1));
        return baseDao.getDetailsList(param);
    }

    @Override
    public HystStats getHystStats(SurveyQuery query) throws Exception {
        Map<String, Object> param = new HashMap<>();
        Map<String, Object> dateMap = getStmAndEtm(query.getAssPer());
        if (!CollectionUtils.isEmpty(dateMap)) {
            dateMap.forEach(param::putIfAbsent);
        }
        param.put("assPer", query.getAssPer());
        return baseDao.getHystStats(param);
    }

    public Map<String, Object> getStmAndEtm(String assPer) throws Exception {
        Map<String, Object> map = new HashMap<>();
        if ("0".equals(assPer)) {
            map.put("stm", DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
        } else if ("1".equals(assPer)) {
            map.put("stm", DateFormatUtils.format(new Date(), "yyyy-MM-01"));
        } else if ("2".equals(assPer)) {
            map.put("stm", DateFormatUtils.format(new Date(), "yyyy-01-01"));
        }
        map.put("etm", DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
        return map;
    }
}
