package com.huitu.cloud.api.hsfxt.city.service.impl;

import com.huitu.cloud.api.hsfxt.city.entity.*;
import com.huitu.cloud.api.hsfxt.city.mapper.CityDao;
import com.huitu.cloud.api.hsfxt.city.service.CityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 风险图-城市 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-09
 */
@Service
public class CityServiceImpl implements CityService {
    @Autowired
    CityDao cityDao;


    @Override
    public List<DicBianzhiquyu> getBzqyInfo(String fxtType) {
        List<DicBianzhiquyu> list = cityDao.getBzqyInfo(fxtType);
        return list;
    }

    @Override
    public List<DicHongShuiLaiYuan> getHslyInfo(String sortIds) {
        //修改in需要的参数
        String param =getParam(sortIds);
        List<DicHongShuiLaiYuan> list = cityDao.getHslyInfo(param);
        return list;
    }

    @Override
    public List<DicJisuanfenqu> getFqInfo(String bianzhiquid) {
        List<DicJisuanfenqu> list = cityDao.getFqInfo(bianzhiquid);
        return list;
    }

    @Override
    public List<DicKuikou> getKkInfo(String jisuanfenquid) {
        //修改in需要的参数
        String param =getParam(jisuanfenquid);
        List<DicKuikou> list = cityDao.getKkInfo(param);
        return list;
    }

    @Override
    public List<DicHongshuiliangji> getHsljInfo(String bianzhiquid) {
        List<DicHongshuiliangji> list = cityDao.getHsljInfo(bianzhiquid);
        return list;
    }

    @Override
    public List<DicJiangyulishi> getJylsInfo(String bianzhiquid) {
        List<DicJiangyulishi> list = cityDao.getJylsInfo(bianzhiquid);
        return list;
    }

    @Override
    public List<DicJianjie> getJjInfo(String fengxiantuid, String bianzhiquid) {
        List<DicJianjie> list = cityDao.getJjInfo(fengxiantuid, bianzhiquid);
        return list;
    }

    @Override
    public List<DicShuxing> getKkmcInfo(String kuikousort) {
        List<DicShuxing> list = cityDao.getKkmcInfo(kuikousort);
        return list;
    }

    @Override
    public List<CsShehuijingji> getShjjTreeInfo(String key) {
        List<CsShehuijingji> result=new ArrayList<>();
        //修改in需要的参数
        String param =getParam(key);
        List<CsShehuijingji> list = cityDao.getShjjTreeInfo(param);
        //组织树形结构
        list.forEach(item->{
            List<CsShehuijingji> childList=getShjjChildren(list,item.getSortid().toString());
            item.setChildren(childList);
        });
        result =list.stream().filter(item->"0".equals(item.getPid())).collect(Collectors.toList());
        return result;
    }

    @Override
    public List<CsZhijiejingjisunshi> getSjjjssTreeInfo(String key, String name) {
        List<CsZhijiejingjisunshi> result=new ArrayList<>();
        Map<String,Object> param=new HashMap<>();
        //修改in需要的参数
        String key2 =getParam(key);
        param.put("key",key2);
        param.put("name",name);
        List<CsZhijiejingjisunshi> list=cityDao.getSjjjssTreeInfo(param);
        List<CsZhijiejingjisunshi> list2=new ArrayList<>();
        List<CsZhijiejingjisunshi> list3=new ArrayList<>();
        //按名称查询取出上级放入结果
        if (name!=null && name!=""){
            Map<String,CsZhijiejingjisunshi> allMap=new HashMap<>();
            param.put("name",null);
            List<CsZhijiejingjisunshi> listAll=cityDao.getSjjjssTreeInfo(param);
            listAll.forEach(item->{
                allMap.put(item.getSortId().toString(),item);
            });
            list.forEach(item->{
                if (allMap.containsKey(item.getPid())){
                    CsZhijiejingjisunshi shangJi=allMap.get(item.getPid());
                    list2.add(shangJi);
                }
            });
            list3 =list2.stream().distinct().collect(Collectors.toList());
        }
        list3.addAll(list);
        //获取子节点
        list3.forEach(item->{
            List<CsZhijiejingjisunshi> childs=getSjjjssChildren(list,item.getSortId().toString());
            item.setChildren(childs);
            item.setChildrenCount(childs.size());
        });
        result=list3.stream().filter(item->"0".equals(item.getPid())).collect(Collectors.toList());
        return result;
    }

    @Override
    public CsZhijiejingjisunshi getOneSjjjssInfo(String key, String adcd) {
        CsZhijiejingjisunshi zhijiejingjisunshi=new CsZhijiejingjisunshi();
        Map<String,Object> param=new HashMap<>();
        //修改in需要的参数
        String key2 =getParam(key);
        param.put("key",key2);
        param.put("adcd",adcd);
        List<CsZhijiejingjisunshi> list=cityDao.getSjjjssTreeInfo(param);
        if (list!=null && list.size()>0){
            zhijiejingjisunshi=list.get(0);
        }
        return zhijiejingjisunshi;
    }


    private List<CsZhijiejingjisunshi> getSjjjssChildren(List<CsZhijiejingjisunshi> list, String pid) {
        // 通过父级编码子类
        List<CsZhijiejingjisunshi> childList = list.stream().filter(x -> x.getPid().equals(pid)).collect(Collectors.toList());
        return childList;
    }

    private List<CsShehuijingji> getShjjChildren(List<CsShehuijingji> list, String pid) {
        // 通过父级编码子类
        List<CsShehuijingji> childList = list.stream().filter(x -> x.getPid().equals(pid)).collect(Collectors.toList());
        return childList;
    }

    /**
     * 批量查询时传入字符串转换为sql底层in所需的条件转换
     *
     * @param str 传入字符串 例（1,2）
     * @return
     */
    private String getParam(String str){
        String[] s=str.split(",");
        String newStr="";
        for (int i=0;i<s.length;i++){
            newStr=newStr+"'"+s[i]+"',";
        }
        return newStr.substring(0,newStr.length()-1);
    }
}
