<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.VillagePersonTmpDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="batchInsert">
        INSERT INTO BNS_VILLAGEPERSON_TMP (ID, NO, XADCD, RES_NAME, XADNM, ZADNM, XZADNM, ZRADNM, XZ_REALNM,
        XZ_MOBILE, CT_REALNM, CT_MOBILE)
        SELECT T.ID, T.NO, T.XADCD, T.RES_NAME, T.XADNM, T.ZADNM, T.XZADNM, T.ZRADNM, T.XZ_R<PERSON>M,
        T.XZ_MOBILE, T.CT_<PERSON>L<PERSON>, T.CT_MOBILE FROM (VALUES
        <foreach collection="map.list" item="item" separator=",">
            (NEWID(), #{map.batchNo}, #{map.xadcd}, #{item.resName}, #{item.xadnm}, #{item.zadnm}, #{item.xzadnm},
            #{item.zradnm}, #{item.xzRealnm}, #{item.xzMobile}, #{item.ctRealnm}, #{item.ctMobile})
        </foreach>) AS T(ID, NO, XADCD, RES_NAME, XADNM, ZADNM, XZADNM, ZRADNM, XZ_REALNM, XZ_MOBILE,
        CT_REALNM, CT_MOBILE)
    </insert>
</mapper>
