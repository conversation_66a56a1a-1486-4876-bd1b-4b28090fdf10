package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 洪水量级
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-10
 */
@TableName("Dic_HongShuiLiangJi")
@ApiModel(value="DicHongshuiliangji对象", description="洪水量级")
public class DicHongshuiliangji extends Model<DicHongshuiliangji> {

    @ApiModelProperty(value = "洪水量级编码")
    private String hongshuiliangjiid;

    @ApiModelProperty(value = "洪水量级名称")
    private String hongshuiliangjimingcheng;

    @ApiModelProperty(value = "编制区编码")
    private String bianzhiquid;

    @ApiModelProperty(value = "备注")
    private String beizhu;

    @ApiModelProperty(value = "排序号")
    private Integer sortid;

    @ApiModelProperty(value = "备注3")
    private String beizhu3;


    public String getHongshuiliangjiid() {
        return hongshuiliangjiid;
    }

    public void setHongshuiliangjiid(String hongshuiliangjiid) {
        this.hongshuiliangjiid = hongshuiliangjiid;
    }

    public String getHongshuiliangjimingcheng() {
        return hongshuiliangjimingcheng;
    }

    public void setHongshuiliangjimingcheng(String hongshuiliangjimingcheng) {
        this.hongshuiliangjimingcheng = hongshuiliangjimingcheng;
    }

    public String getBianzhiquid() {
        return bianzhiquid;
    }

    public void setBianzhiquid(String bianzhiquid) {
        this.bianzhiquid = bianzhiquid;
    }

    public String getBeizhu() {
        return beizhu;
    }

    public void setBeizhu(String beizhu) {
        this.beizhu = beizhu;
    }

    public Integer getSortid() {
        return sortid;
    }

    public void setSortid(Integer sortid) {
        this.sortid = sortid;
    }

    public String getBeizhu3() {
        return beizhu3;
    }

    public void setBeizhu3(String beizhu3) {
        this.beizhu3 = beizhu3;
    }
}
