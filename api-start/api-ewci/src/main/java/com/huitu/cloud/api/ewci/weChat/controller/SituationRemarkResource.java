package com.huitu.cloud.api.ewci.weChat.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.bia.entity.StRiverVos;
import com.huitu.cloud.api.ewci.weChat.entity.*;
import com.huitu.cloud.api.ewci.weChat.service.SituationRemarkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 公众号汛情摘要接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-06
 */
@RestController
@Api(tags = "公众号汛情摘要接口")
@RequestMapping("/api/unsafe/wechat/situationRemark")
public class SituationRemarkResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "bb6415a1-6a17-4daa-9df2-21c68bf190e4";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private SituationRemarkService baseService;


    @ApiOperation(value = "test")
    @GetMapping(value = "test")
    public ResponseEntity<SuccessResponse<Map<String, Object>>> test() throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("test", "testxxx");
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", map));
    }

    @ApiOperation(value = "累计雨量统计")
    @GetMapping(value = "select-rainlist")
    public ResponseEntity<SuccessResponse<Map<String, Object>>> getRainList() throws Exception {
        Map<String, Object> map = baseService.getRainByCondition();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", map));
    }

    @ApiOperation(value = "业务门户降雨统计", notes = "业务门户降雨统计。")
    @GetMapping(value = "select-rain-ywmh")
    public ResponseEntity<SuccessResponse<WeChatRainByYwmh>> getRainInfoByYwmh() throws Exception {
        WeChatRainByYwmh list = baseService.getRainInfoByYwmh();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询最新水库水情信息")
    @GetMapping(value = "select-rsvrlist")
    public ResponseEntity<SuccessResponse<Page<WeChatStRsvrVo>>> getStRsvrLatestList() throws Exception {
        IPage<WeChatStRsvrVo> list = baseService.getNoRsvrLatest();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "最新河道水情信息", notes = "查询某段时间内的最新水情信息，支持分页")
    @GetMapping(value = "select-riverlist")
    public ResponseEntity<SuccessResponse<Page<StRiverVos>>> getStRiverByPage() throws Exception {
        IPage<StRiverVos> list = baseService.getNoRiver();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询最新水库水情信息" , notes = "查询最新水库水情信息，条件查询")
    @PostMapping(value = "select-tyRsvrlist")
    public ResponseEntity<SuccessResponse<Page<WeChatStRsvrVo>>> getTyStRsvrLatestList(@RequestBody @Validated WeChatRvTypeQo rvType) throws Exception {
        IPage<WeChatStRsvrVo> list = baseService.getTyRsvrByConditon(rvType.getRvType());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "最新河道水情信息" , notes = "最新河道水情信息，条件查询")
    @PostMapping(value = "select-riverTylist")
    public ResponseEntity<SuccessResponse<Page<StRiverVos>>> getStRiverByPage(@RequestBody @Validated WeChatStTypeQo stType ) throws Exception {
        IPage<StRiverVos> list = baseService.getTyRiver(stType.getStType());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "根据文件标题获取文件最新的一个文件信息（移动天气趋势：获取最近的标题中带天气公报的文件并展示）")
    @PostMapping(value = "select-latest-bnsfiler")
    public ResponseEntity<SuccessResponse<List<WeChatBnsFilesAndUrls>>> getLastestBnsFileurlsr() throws Exception {
        List<WeChatBnsFilesAndUrls> list = baseService.getLastestBnsFileurlsr();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询最新天气预报",notes="查询最新天气预报")
    @GetMapping(value = "weather")
    public ResponseEntity<SuccessResponse<WeChatWeatherVo>> getWeatherForecast() throws Exception {
        WeChatWeatherVo weather=baseService.getWeatherForecast();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", weather));
    }

    @ApiOperation(value = "根据类型与时段查询卫星云图",notes="根据类型与时段查询卫星云图")
    @GetMapping(value = "get-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tms", value = "时段类型 ", required = true, dataType = "int",example = "12"),
            @ApiImplicitParam(name = "picType", value = "云图类型 类型 IR1 ：红外线 VIS：可见光  WAT:水汽 GMS:立体图", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<WeChatQxpicgetRVo>>> getWxytList(@RequestParam Integer tms, @RequestParam String picType) throws Exception {
        List<WeChatQxpicgetRVo> list = baseService.getWxytList(tms,picType);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

}