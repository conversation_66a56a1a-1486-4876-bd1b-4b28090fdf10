package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 作战图-洪水传播时间
 */
@ApiModel(value = "作战图-洪水传播时间")
public class RiverProVo implements Serializable {

    @ApiModelProperty(value = "河流编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "河流名称")
    @TableField(value = "RV_NAME")
    private String rvName;

    @ApiModelProperty(value = "序号")
    @TableField(value = "SNO")
    private Integer sno;

    @ApiModelProperty(value = "开始站点名称")
    @TableField(value = "STAR_NAME")
    private String starName;

    @ApiModelProperty(value = "结束站点名称")
    @TableField(value = "END_NAME")
    private String endName;

    @ApiModelProperty(value = "经度")
    @TableField(value = "LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField(value = "LTTD")
    private Double lttd;

    @ApiModelProperty(value = "洪水传播距离")
    @TableField(value = "PRO_DIST")
    private Double proDist;

    @ApiModelProperty(value = "洪水传播时间")
    @TableField(value = "PRO_HOUR")
    private String proHour;

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }

    public Integer getSno() {
        return sno;
    }

    public void setSno(Integer sno) {
        this.sno = sno;
    }

    public String getStarName() {
        return starName;
    }

    public void setStarName(String starName) {
        this.starName = starName;
    }

    public String getEndName() {
        return endName;
    }

    public void setEndName(String endName) {
        this.endName = endName;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public Double getProDist() {
        return proDist;
    }

    public void setProDist(Double proDist) {
        this.proDist = proDist;
    }

    public String getProHour() {
        return proHour;
    }

    public void setProHour(String proHour) {
        this.proHour = proHour;
    }
}
