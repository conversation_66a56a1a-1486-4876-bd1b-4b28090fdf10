package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警指标时段雨量成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@TableName("IA_A_DFWRULE")
@ApiModel(value="IaADfwrule对象", description="预警指标时段雨量成果表")
public class IaDfwrule extends Model<IaDfwrule> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "预警等级")
    @TableField("WARNGRADEID")
    private Integer warngradeid;

    @ApiModelProperty(value = "土壤含水量")
    @TableField("LWATER")
    private Double lwater;

    @ApiModelProperty(value = "阈值历时")
    @TableField("STDT")
    private Integer stdt;

    @ApiModelProperty(value = "雨量阈值")
    @TableField("DRPT")
    private Integer drpt;

    @ApiModelProperty(value = "分析方法")
    @TableField("CALMATH")
    private String calmath;

    @ApiModelProperty(value = "导入人")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;
    @ApiModelProperty(value = "流域名称")
    private String wsnm;
    @ApiModelProperty(value = "政区名称")
    private String adnm;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public Integer getWarngradeid() {
        return warngradeid;
    }

    public void setWarngradeid(Integer warngradeid) {
        this.warngradeid = warngradeid;
    }

    public Double getLwater() {
        return lwater;
    }

    public void setLwater(Double lwater) {
        this.lwater = lwater;
    }

    public Integer getStdt() {
        return stdt;
    }

    public void setStdt(Integer stdt) {
        this.stdt = stdt;
    }

    public Integer getDrpt() {
        return drpt;
    }

    public void setDrpt(Integer drpt) {
        this.drpt = drpt;
    }

    public String getCalmath() {
        return calmath;
    }

    public void setCalmath(String calmath) {
        this.calmath = calmath;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }

    @Override
    protected Serializable pkVal() {
        return null;
    }

    @Override
    public String toString() {
        return "IaDfwrule{" +
        "adcd=" + adcd +
        ", wscd=" + wscd +
        ", warngradeid=" + warngradeid +
        ", lwater=" + lwater +
        ", stdt=" + stdt +
        ", drpt=" + drpt +
        ", calmath=" + calmath +
        ", signer=" + signer +
        ", audid=" + audid +
        ", status=" + status +
        ", remark=" + remark +
        ", moditime=" + moditime +
        "}";
    }
}
