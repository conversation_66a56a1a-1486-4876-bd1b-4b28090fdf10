package com.huitu.cloud.api.xcc.core.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;

@ApiModel(description = "数据传输对象")
@Component
@Scope("prototype")
public class ApiResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("传输状态码")
    private int code;
    @ApiModelProperty("数据消息")
    private String message;

    @ApiModelProperty("接口唯一编码")
    private String uuid;
    @ApiModelProperty("访问路径")
    private String uri;
    @ApiModelProperty("接口版本")
    private String version;

    public ApiResponse() {
    }

    public ApiResponse(HttpServletRequest request, int code, String message) {
        this.uri = request.getRequestURI();
        this.code = code;
        this.message = message;
    }

    public ApiResponse(ApiResource api, int code, String message) {
        this.uri = api.getUri();
        this.uuid = api.getUuid();
        this.version = api.getVersion();
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
