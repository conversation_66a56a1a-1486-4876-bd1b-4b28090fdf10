package com.huitu.cloud.api.ewci.feedback.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBack;
import com.huitu.cloud.api.ewci.feedback.entity.BnsFeedBackVo;
import com.huitu.cloud.api.ewci.warn.entity.BnsWarnInfo;
import com.huitu.cloud.api.ewci.warn.entity.BnsWarnOperation;
import com.huitu.cloud.api.ewci.warn.entity.WarnInfoAdcdInfo;
import com.huitu.cloud.api.ewci.warn.entity.WarnInfoUserInfos;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 意见反馈信息 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface BnsFeedBackDao {

    /**
     * 意见反馈信息分页查询
     *
     * @param page
     * @param param
     * @return
     */
    IPage<BnsFeedBackVo> getPage(Page page, @Param("map") Map<String, Object> param);


    /**
     * 添加意见反馈
     *
     * @param entity
     * @return
     */
    int insertFeedBack(BnsFeedBack entity);


    /**
     * 处理意见反馈
     *
     * @param entity
     * @return
     */
    int handleFeedBack(BnsFeedBack entity);

}
