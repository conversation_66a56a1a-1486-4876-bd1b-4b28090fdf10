package com.huitu.cloud.api.ewci.hydrology.entity.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnIndexResponse;
import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.validation.constraints.Option;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;


/**
 * 洪水预警指标查询
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警指标查询")
public class HyWarnIndexQuery extends PageBean {

    @ApiModelProperty(value = "行政区划代码（15位）", required = true)
    @NotBlank(message = "参数[行政区划代码]不能为空")
    @Size(min = 15, max = 15, message = "参数[行政区划代码]的长度应为15")
    private String adcd;

    @ApiModelProperty(value = "单位类型")
    @Option(value = {"0", "1"}, message = "参数[单位类型]的值无效")
    @TableField(value = "UNIT_TYPE")
    private String unitType;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public Map<String, Object> toQueryParam() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("unitType", getUnitType());
        params.put("stnm", getStnm());
        return params;
    }

    public IPage<HyWarnIndexResponse> toPageParam() {
        return new Page<>(Math.max(getPageNum(), 1), getPageSize() > 0 ? getPageSize() : -1);
    }
}
