package com.huitu.cloud.api.fusion.entity.ext;

import lombok.*;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.Date;

/**
 * 测站后续降雨查询
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FusionFollowupQuery {

    /**
     * 测站编码
     */
    private String stcd;

    /**
     * 来源
     */
    private String stadtp;

    /**
     * 开始时间
     */
    private Date stm;

    /**
     * 结束时间
     */
    private Date etm;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        FusionFollowupQuery that = (FusionFollowupQuery) obj;
        return new EqualsBuilder()
                .append(stcd, that.getStcd())
                .append(stm, that.getStm())
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(stcd)
                .append(stm)
                .toHashCode();
    }
}
