package com.huitu.cloud.api.ewci.monitor.service;

import com.huitu.cloud.api.ewci.monitor.entity.FloodPerson;
import com.huitu.cloud.api.ewci.monitor.entity.IaCGully;
import com.huitu.cloud.api.ewci.monitor.entity.InfluenceScope;
import com.huitu.cloud.api.ewci.monitor.entity.RsvrPerson;
import com.huitu.cloud.api.ewci.person.entity.DikePerson;
import com.huitu.cloud.api.ewci.person.entity.RiverPerson;

import java.util.List;

/**
 * 影响范围服务
 *
 * <AUTHOR>
 */
public interface InfluenceScopeService {

    /**
     * 获取影响范围汇总统计信息
     *
     * @param adcd 政区编码
     * @return 汇总统计信息
     **/
    InfluenceScope getStatData(String adcd);

    /**
     * 获取山洪责任人列表
     *
     * @param adcd 政区编码
     * @return 责任人列表
     **/
    List<FloodPerson> getFloodPersonList(String adcd);

    /**
     * 获取水库责任人列表
     *
     * @param stcd 测站编码
     * @return 责任人列表
     **/
    List<RsvrPerson> getRsvrPersonList(String stcd);

    /**
     * 获取江河责任人列表
     *
     * @param stcd 测站编码
     * @return 责任人列表
     **/
    List<RiverPerson> getRiverPersonList(String stcd);

    /**
     * 获取堤防责任人列表
     *
     * @param stcd 测站编码
     * @return 责任人列表
     **/
    List<DikePerson> getDikePersonList(String stcd);

    /**
     * 获取需防洪治理山洪沟基本情况成果列表
     *
     * @param adcd 政区编码
     * @return 成果列表
     **/
    List<IaCGully> getGullyList(String adcd);
}
