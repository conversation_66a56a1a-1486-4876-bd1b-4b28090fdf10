package com.huitu.cloud.api.syq.warn.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.syq.feign.OssFeign;
import com.huitu.cloud.api.syq.rain.entity.BsnRainAlarm;
import com.huitu.cloud.api.syq.rain.entity.Rain;
import com.huitu.cloud.api.syq.rain.mapper.RainDao;
import com.huitu.cloud.api.syq.rain.service.RainService;
import com.huitu.cloud.api.syq.river.entity.RiverInfoByDuty;
import com.huitu.cloud.api.syq.river.entity.RiverWarnVo;
import com.huitu.cloud.api.syq.river.entity.StRiverVo;
import com.huitu.cloud.api.syq.river.service.RiverService;
import com.huitu.cloud.api.syq.rsvr.entity.RsvrWarn;
import com.huitu.cloud.api.syq.rsvr.entity.RsvtInfoByDuty;
import com.huitu.cloud.api.syq.rsvr.entity.StRsvrVo;
import com.huitu.cloud.api.syq.rsvr.service.RsvrService;
import com.huitu.cloud.api.syq.warn.entity.*;
import com.huitu.cloud.api.syq.warn.mapper.SyqWarnDao;
import com.huitu.cloud.api.syq.warn.service.SyqWarnService;
import com.huitu.cloud.api.syq.warn.util.WorderToNewWordUtils;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.DateFormatUtil;
import com.huitu.cloud.util.UUIDFactory;
import feign.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblBorders;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SyqWarnServiceImpl implements SyqWarnService {
    @Autowired
    private RainService rainService;

    @Autowired
    private RainDao rainDao;

    @Autowired
    private RsvrService rsvrService;
    @Autowired
    private RiverService riverService;
    @Autowired
    private SyqWarnDao syqWarnDao;

    @Autowired
    private OssFeign ossFeign;

    @Override
    public WarnCountVo getWarnCount(String adcd,String stm,String etm) {
        //河道超警统计
        RiverWarnVo riverWarnVo=riverService.getRiverWarn(adcd,stm,etm,null,"","");
        //水库超警统计
        RsvrWarn rsvrWarn=rsvrService.getStRsvrWarn(adcd,stm,etm,"","",null,"");
        //查询所有雨量超警
        IPage<BsnRainAlarm> iPage=rainService.getRainWarn(adcd,"","","",null, null,1,-1);
        List<BsnRainAlarm> rainAlarmList=iPage.getRecords();
        Map<String,Long> rainMap=rainAlarmList.stream().collect(Collectors.groupingBy(BsnRainAlarm::getAlarmgradeid,Collectors.counting()));
        WarnCountVo warnCountVo=new WarnCountVo();
        //河道超警赋值
        warnCountVo.setHdTotal(riverWarnVo.getWarnGrzList().size()+riverWarnVo.getWarnWrzList().size());
        warnCountVo.setHdCjTotal(riverWarnVo.getWarnWrzList().size());
        warnCountVo.setHdCbTotal(riverWarnVo.getWarnGrzList().size());
        //水库超警赋值
        warnCountVo.setSkTotal(rsvrWarn.getWarnWfsltdwList().size()+rsvrWarn.getWarnRznormzList().size());
        warnCountVo.setSkCxTotal(rsvrWarn.getWarnWfsltdwList().size());
        warnCountVo.setSkCgTotal(rsvrWarn.getWarnRznormzList().size());
        //雨量超警赋值
        warnCountVo.setYlTotal(rainAlarmList.size());
        //超警
        warnCountVo.setYlCjTotal(rainMap.get("1")!=null?rainMap.get("1").intValue():0);
        //超危险
        warnCountVo.setYlCwTotal(rainMap.get("2")!=null?rainMap.get("2").intValue():0);

        return warnCountVo;
    }

    @Override
    public WarnInfoVo getWarnCountAndInfo(String adcd, String stm,String stms, String etm, String rsvrStType) { // stms:针对app河道修改
        if (StringUtils.isEmpty(stms)){ // stms:针对app河道修改
            stms = stm;
        }
        //水库超警统计
        RsvtInfoByDuty rsvtInfoByDuty=rsvrService.getStRsvrVoVyDuty(adcd,stm,etm,rsvrStType);
        //河流超警戒
        RiverInfoByDuty riverInfoByDuty=riverService.selectRiverInfoByDuty(adcd, stms, etm);

        //查询所有雨量超警
        List<String> stType=new ArrayList<>();
        stType.add("1");
        stType.add("2");
        stType.add("3");
        stType.add("4");
        stType.add("5");
        IPage<BsnRainAlarm> iPage=rainService.getRainWarn(adcd,"","","",stType, null,1,-1);
        List<BsnRainAlarm> rainAlarmList=iPage.getRecords();
        Map<String,List<BsnRainAlarm>> rainMap=rainAlarmList.stream().collect(Collectors.groupingBy(BsnRainAlarm::getAlarmgradeid,Collectors.toList()));


        WarnInfoVo  warnInfoVo=new WarnInfoVo();
        //水库超讯
        warnInfoVo.setSkTotal(rsvtInfoByDuty.getTotal());
        //大型
        warnInfoVo.setBigRsvrCount(rsvtInfoByDuty.getBigRsvrCount());
        warnInfoVo.setBigList(rsvtInfoByDuty.getBigList());
        //中型
        warnInfoVo.setMiddleRsvrCount(rsvtInfoByDuty.getMiddleRsvrCount());
        warnInfoVo.setMiddleList(rsvtInfoByDuty.getMiddleAllList());
        //小型
        warnInfoVo.setSmallRsvrCount(rsvtInfoByDuty.getSmallRsvrCount());
        warnInfoVo.setSmallList(rsvtInfoByDuty.getSmallList());

        //雨量超警赋值
        warnInfoVo.setYlTotal(rainAlarmList.size());
        //超警
        warnInfoVo.setYlCjTotal(rainMap.get("1")!=null?rainMap.get("1").size():0);
        warnInfoVo.setYlCjList(rainMap.get("1"));
        //超危险
        warnInfoVo.setYlCwTotal(rainMap.get("2")!=null?rainMap.get("2").size():0);
        warnInfoVo.setYlCwList(rainMap.get("2"));

        //河流超警戒
        warnInfoVo.setHdTotal(riverInfoByDuty.getZwrzNum());
        warnInfoVo.setRiverList(riverInfoByDuty.getRiverVoList());
        return warnInfoVo;
    }

    @Override
    public int getHeavyRainfallNoticeNo() {
        int hno=rainDao.getHeavyRainfallNoticeNo();
        return hno;
    }

    @Override
    public Boolean signHeavyRainfallNotice(String stcd, String rightFillStr) {
        rainDao.signHeavyRainfallNotice(stcd, rightFillStr);
        return true;
    }

    @Override
    public RiskWarningsVo getRisks(String adcd, String stm, String etm) {
        int level= AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0,level);
        List<EwVillageVo> villageVoList = syqWarnDao.getVillageVoList(ad, level, stm, etm);
        List<EwRivelVo> ewRivelVoList = syqWarnDao.getEwRivelVoList(ad, level, stm, etm);
        List<EwReserVo> ewReserVoList = syqWarnDao.getEwReserVoList(ad, level, stm, etm);
        List<EarlyWarningVo> earlyWarningVillageVo1List = syqWarnDao.getEarlyWarning1List(ad, level, stm, etm, "1");
        List<EarlyWarningVo> earlyWarningEwRivelVo1List = syqWarnDao.getEarlyWarning1List(ad, level, stm, etm, "2");
        List<EarlyWarningVo> earlyWarningEwReserVo1List = syqWarnDao.getEarlyWarning1List(ad, level, stm, etm, "3");
        List<EarlyWarningVo> earlyWarningVillageVo3List = syqWarnDao.getEarlyWarning3List(ad, level, stm, etm, "1");
        List<EarlyWarningVo> earlyWarningEwRivelVo3List = syqWarnDao.getEarlyWarning3List(ad, level, stm, etm, "2");
        List<EarlyWarningVo> earlyWarningEwReserVo3List = syqWarnDao.getEarlyWarning3List(ad, level, stm, etm, "3");
        List<EarlyWarningVo> earlyWarningVillageVo6List = syqWarnDao.getEarlyWarning6List(ad, level, stm, etm, "1");
        List<EarlyWarningVo> earlyWarningEwRivelVo6List = syqWarnDao.getEarlyWarning6List(ad, level, stm, etm, "2");
        List<EarlyWarningVo> earlyWarningEwReserVo6List = syqWarnDao.getEarlyWarning6List(ad, level, stm, etm, "3");
        RiskWarningsVo riskWarningsVo = new RiskWarningsVo();
        riskWarningsVo.setVillageVoList(villageVoList);
        riskWarningsVo.setEwRivelVoList(ewRivelVoList);
        riskWarningsVo.setEwReserVoList(ewReserVoList);
        riskWarningsVo.setEarlyWarningVillageVo1List(earlyWarningVillageVo1List);
        riskWarningsVo.setEarlyWarningVillageVo3List(earlyWarningVillageVo3List);
        riskWarningsVo.setEarlyWarningVillageVo6List(earlyWarningVillageVo6List);
        riskWarningsVo.setEarlyWarningEwRivelVo1List(earlyWarningEwRivelVo1List);
        riskWarningsVo.setEarlyWarningEwRivelVo3List(earlyWarningEwRivelVo3List);
        riskWarningsVo.setEarlyWarningEwRivelVo6List(earlyWarningEwRivelVo6List);
        riskWarningsVo.setEarlyWarningEwReserVo1List(earlyWarningEwReserVo1List);
        riskWarningsVo.setEarlyWarningEwReserVo3List(earlyWarningEwReserVo3List);
        riskWarningsVo.setEarlyWarningEwReserVo6List(earlyWarningEwReserVo6List);
        return riskWarningsVo;
    }

    @Override
    public List<EwVillageVo> getRiskVillage(String adcd, String stm, String etm) {
        int level= AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0,level);
        List<EwVillageVo> villageVoList = syqWarnDao.getVillageVoList(ad, level, stm, etm);
        return villageVoList;
    }

    @Override
    public List<EwRivelVo> getRiskRiver(String adcd, String stm, String etm) {
        int level= AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0,level);
        List<EwRivelVo> ewRivelVoList = syqWarnDao.getEwRivelVoList(ad, level, stm, etm);
        return ewRivelVoList;
    }

    @Override
    public List<EwReserVo> getRiskRsvr(String adcd, String stm, String etm) {
        int level= AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0,level);
        List<EwReserVo> ewReserVoList = syqWarnDao.getEwReserVoList(ad, level, stm, etm);
        return ewReserVoList;
    }

    @Override
    public List<EarlyWarningVo> getRiskEarly1(String adcd, String stm, String etm, String type) {
        int level= AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0,level);
        List<EarlyWarningVo> earlyWarning1List = syqWarnDao.getEarlyWarning1List(ad, level, stm, etm, type);
        return earlyWarning1List;
    }

    @Override
    public List<EarlyWarningVo> getRiskEarly3(String adcd, String stm, String etm, String type) {
        int level= AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0,level);
        List<EarlyWarningVo> earlyWarning3List = syqWarnDao.getEarlyWarning3List(ad, level, stm, etm, type);
        return earlyWarning3List;
    }

    @Override
    public List<EarlyWarningVo> getRiskEarly6(String adcd, String stm, String etm, String type) {
        int level= AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0,level);
        List<EarlyWarningVo> earlyWarning6List = syqWarnDao.getEarlyWarning6List(ad, level, stm, etm, type);
        return earlyWarning6List;
    }

    @Override
    public List<EarlyWarningVo> getRiskEarly(String adcd, String stm, String etm, String tmType, String riskType) {
        int level= AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0,level);
        List<EarlyWarningVo> earlyWarning1List = null;
        if (tmType.equals("1") && riskType.equals("1")) {
            earlyWarning1List = syqWarnDao.getEarlyWarningVillageList(ad, level, stm, etm, "1");
        } else if (tmType.equals("1") && riskType.equals("2")) {
            earlyWarning1List = syqWarnDao.getEarlyWarningRsvrist(ad, level, stm, etm, "1");
        } else if (tmType.equals("1") && riskType.equals("3")) {
            earlyWarning1List = syqWarnDao.getEarlyWarningRiverList(ad, level, stm, etm, "1");
        } else if (tmType.equals("3") && riskType.equals("1")) {
            earlyWarning1List = syqWarnDao.getEarlyWarningVillageList(ad, level, stm, etm, "2");
        } else if (tmType.equals("3") && riskType.equals("2")) {
            earlyWarning1List = syqWarnDao.getEarlyWarningRsvrist(ad, level, stm, etm, "2");
        } else if (tmType.equals("3") && riskType.equals("3")) {
            earlyWarning1List = syqWarnDao.getEarlyWarningRiverList(ad, level, stm, etm, "2");
        } else if (tmType.equals("6") && riskType.equals("1")) {
            earlyWarning1List = syqWarnDao.getEarlyWarningVillageList(ad, level, stm, etm, "6");
        } else if (tmType.equals("6") && riskType.equals("2")) {
            earlyWarning1List = syqWarnDao.getEarlyWarningRsvrist(ad, level, stm, etm, "6");
        } else if (tmType.equals("6") && riskType.equals("3")) {
            earlyWarning1List = syqWarnDao.getEarlyWarningRiverList(ad, level, stm, etm, "6");
        } else {
            earlyWarning1List = new ArrayList<>();
        }
        return earlyWarning1List;
    }

    @Override
    public Notice crezateHeavyRainfallNotice(QueryNotice query) {
        Notice result=new Notice();
        //通告存储
        String id= UUIDFactory.createUUID();
        String adcd=query.getAdcd();
        query.setHid(id);
        rainDao.insertHeavyRainfallNotice(query);
        //生成通告
        Map<String, String> dataMap = new HashMap<String, String>();
        //通告时间
        Date dtm= DateFormatUtil.formatStringToDate(query.getTm(),"yyyy-MM-dd HH:mm:ss");
        LocalDateTime ldt = dtm.toInstant()
                .atZone( ZoneId.systemDefault() )
                .toLocalDateTime();
        int year=ldt.getYear();
        int month=ldt.getMonthValue();
        int day=ldt.getDayOfMonth();
        int hour=ldt.getHour();
        int minute=ldt.getMinute();
        //编号
        dataMap.put("hno", query.getHno()+"");
        //年份
        dataMap.put("y", year+"");
        //月份
        dataMap.put("m", month+"");
        //日
        dataMap.put("d", day+"");
        //时
        dataMap.put("h", hour+"");
        //分
        dataMap.put("mm", minute+"");
        //标题
        dataMap.put("htitle", query.getHtitle()+"");
        //政区名称
        String adnm = "";
        if (query.getSadnm() != null && !query.getSadnm().equals("")) {
            // 如果是省市方式，上级是吉林省开头那就只保存市的名称
            if (query.getSadnm().equals("吉林省")) {
                if (query.getAdnm().equals("梅河口市") || query.getAdnm().equals("长白山")) {
                    adnm = query.getAdnm().equals("梅河口市") ? "梅河口市水利局" : "长白山管委会，";
                } else if(query.getAdnm().equals("长春市") || query.getAdnm().equals("白山市")) {
                    adnm = query.getAdnm() + "水务局";
                } else {
                    adnm = query.getAdnm() + "水利局";
                }
            } else {
                // 如果是市县方式
                if (query.getSadnm().equals("梅河口市") || query.getSadnm().equals("长白山")) {
                    adnm = (query.getSadnm().equals("梅河口市") ? "梅河口市水利局" : "长白山管委会、") + query.getAdnm() + "水利局";
                } else if (query.getSadnm().equals("长春市") || query.getSadnm().equals("白山市")) {
                    adnm = query.getSadnm() + "水务局、" + query.getAdnm() + "水利局";
                } else {
                    adnm = query.getSadnm() + "水利局、" + query.getAdnm() + "水利局";
                }
            }
        }
        dataMap.put("adnm", adnm);
        //编辑人
        dataMap.put("writer", query.getWriter()!=null?query.getWriter():"");
        //审核人
        dataMap.put("verify", query.getVerify()!=null?query.getVerify():"");
        //签发人
        dataMap.put("issue", query.getIssue()!=null?query.getIssue():"");
        //通告内容
        String hdesc=query.getHdesc().replaceAll("\n","\r");
        dataMap.put("hdesc", hdesc);
        //生成强降雨通告
        String path=WorderToNewWordUtils.changWord(null,"","",dataMap);
        //查询发送单位
        List<DeptVo> list=rainDao.getReviceDept();
        Map<String,DeptVo> map=new HashMap<>();
        list.forEach(item->{
            map.put(item.getAdcd(),item);
        });
        int level = AdcdUtil.getAdLevel(adcd);
        List<ReceiveDept> deptList=new ArrayList<>();
        if(level==4){
            ReceiveDept dept=new ReceiveDept();
            dept.setAdcd(map.get(adcd).getAdcd());
            dept.setReer(map.get(adcd).getDeptid());
            deptList.add(dept);
        }else if(level==6){
            ReceiveDept dept=new ReceiveDept();
            dept.setAdcd(map.get(adcd).getAdcd());
            dept.setReer(map.get(adcd).getDeptid());
            deptList.add(dept);
            ReceiveDept dept2=new ReceiveDept();
            String adcd2=adcd.substring(0,4)+"00000000000";
            dept2.setAdcd(map.get(adcd2).getAdcd());
            dept2.setReer(map.get(adcd2).getDeptid());
            deptList.add(dept2);
        }
        //返回需要文件发送以及文件上传发送的参数
        result.setFilerelurl(path);
        String fileName=path.substring(3,path.length());
        result.setFileurl("/upload/"+fileName);
        result.setFilename(fileName);
        result.setHtitle(query.getHtitle());
        result.setWriter(query.getWriter());
        result.setIssue(query.getIssue());
        result.setVerify(query.getVerify());
        result.setReceiveDeptList(deptList);
        return  result;
    }

    @Override
    public void generateShNews(String adcd, String stm, String etm, String orgfilename,String orgfileurl) throws Exception {
        Map<String, String> textMap = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter ymdh = DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时");
        DateTimeFormatter mdh = DateTimeFormatter.ofPattern("MM月dd日HH时");
        LocalDateTime stmDateTime = LocalDateTime.parse(stm, formatter);
        LocalDateTime etmDateTime = LocalDateTime.parse(etm, formatter);
        textMap.put("stm_yyyy", String.valueOf(stmDateTime.getYear()));
        textMap.put("stm_mm", String.valueOf(stmDateTime.getMonth().getValue()));
        textMap.put("stm_dd", String.valueOf(stmDateTime.getDayOfMonth()));
        textMap.put("stm_hh", String.valueOf(stmDateTime.getHour()));
        textMap.put("etm_yyyy", String.valueOf(etmDateTime.getYear()));
        textMap.put("etm_mm", String.valueOf(etmDateTime.getMonth().getValue()));
        textMap.put("etm_dd", String.valueOf(etmDateTime.getDayOfMonth()));
        textMap.put("etm_hh", String.valueOf(etmDateTime.getHour()));

        String noDataTmStr = ymdh.format(stmDateTime) + "至" + mdh.format(etmDateTime);

        List<String> rainStType = new ArrayList<>();
        rainStType.add("1");
        rainStType.add("2");
        rainStType.add("3");
        List<Rain> rainList = rainService.getRainByConditionAll(stm, etm, rainStType, adcd, null, null, null, "1", null, null, null, null);
        double avg = -1;
        if (rainList.size() > 0) {
            // 取平均降雨量（全部雨量 / 降雨量大于0的站数）
            double drpsTotal = rainList.parallelStream().collect(Collectors.summarizingDouble(item -> Double.parseDouble(item.getDrps()))).getSum();
            long rainCount = rainList.stream().filter(item -> Double.parseDouble(item.getDrps()) > 0).count();
            avg = drpsTotal == 0 ? 0 : new BigDecimal(drpsTotal / rainCount).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            textMap.put("rain_avg", String.valueOf(avg));
        }

        List<String> rsvrStType = new ArrayList<>();
        rsvrStType.add("1");
        rsvrStType.add("4");
        List<String> rsvrIsOut = new ArrayList<>();
        rsvrIsOut.add("1");
        IPage<StRsvrVo> result = rsvrService.getRsvrLatestByConditon(adcd, stm, etm, "", "", rsvrStType, "4,5,3,2,1", true, true, 1, -1, rsvrIsOut, null, false, null, false);
        List<StRsvrVo> rsvrList = result.getRecords();

        List<String> riverIsOut = new ArrayList<>();
        riverIsOut.add("1");
        List<String> riverStType = new ArrayList<>();
        riverStType.add("1");
        riverStType.add("2");
        IPage<StRiverVo> pageRecord = riverService.getRiverByConditon(adcd,stm,etm,riverStType,"",true,true,"",1,-1,riverIsOut,null, false);
        List<StRiverVo> riverList = pageRecord.getRecords();

        int level = AdcdUtil.getAdLevel(adcd);
        int subStrLevel = 2;
        if (level == 2 || level == 4) {
            subStrLevel = 6;
        } else if (level == 6) {
            subStrLevel = 9;
        }
        List<ShWarnGrade> shWarnList = syqWarnDao.getShWarnGradeByXAdcdTm(adcd.substring(0, level), level, subStrLevel, stm, etm);

        int xzlevel = AdcdUtil.getXzAdLevel(adcd);
        String adnmKeys = "";
        if (xzlevel == 1) {
            adnmKeys = "省";
            long count = shWarnList.stream().map(item -> {
                if (item.getAdcd().substring(0, 6).equals("220581")) {
                    return item.getAdcd().substring(0, 6);
                } else {
                    return item.getAdcd().substring(0, 4);
                }
            }).distinct().count();

            textMap.put("shwarn_sadcd_count", String.valueOf(count));
            textMap.put("shwarn_xadcd_count", String.valueOf(shWarnList.size()));
        } else if (xzlevel == 2) {
            adnmKeys = "市";
            textMap.put("shwarn_xadcd_count", String.valueOf(shWarnList.size()));
        } else if (xzlevel == 3) {
            adnmKeys = "县";
        }

        if (shWarnList.size() > 0) {
            textMap.put("shwarn_pre", shWarnList.stream().map(ShWarnGrade::getPrepare).reduce(Integer::sum).get().toString());
            textMap.put("shwarn_imm", shWarnList.stream().map(ShWarnGrade::getImmediately).reduce(Integer::sum).get().toString());
        }

        textMap.put("rain_one_stnm", rainList.size() >= 1 ? rainList.get(0).getStnm() : "XX");
        textMap.put("rain_one_drp", rainList.size() >= 1 ? rainList.get(0).getDrps() : "XX");
        textMap.put("rain_two_stnm", rainList.size() >= 2 ? rainList.get(1).getStnm() : "XX");
        textMap.put("rain_two_drp", rainList.size() >= 2 ? rainList.get(1).getDrps() : "XX");
        textMap.put("rain_three_stnm", rainList.size() >= 3 ? rainList.get(2).getStnm() : "XX");
        textMap.put("rain_three_drp", rainList.size() >= 3 ? rainList.get(2).getDrps() : "XX");

        textMap.put("rsvr_warn_count", String.valueOf(rsvrList.size()));

        textMap.put("river_warn_count", String.valueOf(riverList.size()));

        textMap.put("shwarn_flag", "");
        textMap.put("rain_flag", "");
        textMap.put("rsvr_flag", "");
        textMap.put("river_flag", "");

        Response feignResponse = ossFeign.getFile(orgfileurl, orgfilename);
        Response.Body body = feignResponse.body();
        InputStream inputStream = body.asInputStream();
        XWPFDocument document = new XWPFDocument(inputStream);

        // 获取表格
        List<XWPFTable> tables = document.getTables();
        for (XWPFTable table : tables) {

            CTTblBorders borders = table.getCTTbl().getTblPr().addNewTblBorders();
            CTBorder hBorder = borders.addNewInsideH();
            hBorder.setVal(STBorder.Enum.forString("single"));  // 线条类型
            hBorder.setSz(new BigInteger("1")); // 线条大小
            hBorder.setColor("000000"); // 设置颜色

            CTBorder vBorder = borders.addNewInsideV();
            vBorder.setVal(STBorder.Enum.forString("single"));
            vBorder.setSz(new BigInteger("1"));
            vBorder.setColor("000000");

            CTBorder lBorder = borders.addNewLeft();
            lBorder.setVal(STBorder.Enum.forString("single"));
            lBorder.setSz(new BigInteger("1"));
            lBorder.setColor("000000");

            CTBorder rBorder = borders.addNewRight();
            rBorder.setVal(STBorder.Enum.forString("single"));
            rBorder.setSz(new BigInteger("1"));
            rBorder.setColor("000000");

            CTBorder tBorder = borders.addNewTop();
            tBorder.setVal(STBorder.Enum.forString("single"));
            tBorder.setSz(new BigInteger("1"));
            tBorder.setColor("000000");

            CTBorder bBorder = borders.addNewBottom();
            bBorder.setVal(STBorder.Enum.forString("single"));
            bBorder.setSz(new BigInteger("1"));
            bBorder.setColor("000000");

            List<XWPFTableRow> rows = table.getRows();
            // 第一行是表头，第二行用于判断关键字属于哪类表格
            if (rows.size() >= 2) {
                XWPFTableRow row = rows.get(1);
                if (row.getCell(0).getText().equals("${shwarn_adnm}") && row.getCell(1).getText().equals("${shwarn_info}")) {
                    if (shWarnList.size() == 0) {
                        deleteTable(table);
                    } else {
                        table.removeRow(1);
                        shWarnList.forEach(item -> {
                            XWPFTableRow nextRow = table.createRow();
                            nextRow.getCell(0).setText(item.getAdnm());
                            nextRow.getCell(1).setText(item.getPrepare()+"个准备转移，"+item.getImmediately()+"个立即转移");
                        });
                    }
                } else if (row.getCell(0).getText().equals("${rsvr_stnm}") && row.getCell(1).getText().equals("${rsvr_rz}")) {
                    if (rsvrList.size() == 0) {
                        deleteTable(table);
                    } else {
                        table.removeRow(1);
                        rsvrList.forEach(item -> {
                            XWPFTableRow nextRow = table.createRow();
                            nextRow.getCell(0).setText(item.getStnm());
                            nextRow.getCell(1).setText(item.getRz());
                            nextRow.getCell(2).setText(String.valueOf(item.getRzfsltdz()));
                            nextRow.getCell(3).setText(item.getXadnm());
                        });
                    }
                } else if (row.getCell(0).getText().equals("${river_stnm}") && row.getCell(1).getText().equals("${river_z}")) {
                    if (riverList.size() == 0) {
                        deleteTable(table);
                    } else {
                        table.removeRow(1);
                        riverList.forEach(item -> {
                            XWPFTableRow nextRow = table.createRow();
                            nextRow.getCell(0).setText(item.getStnm());
                            nextRow.getCell(1).setText(item.getZ());
                            nextRow.getCell(2).setText(String.valueOf(item.getZwrz()));
                            nextRow.getCell(3).setText(item.getXadnm());
                        });
                    }
                }
            }
        }

        //生成匹配模式的正则表达式
        String patternString = "\\$\\{(" + StringUtils.join(textMap.keySet(), "|") + ")\\}";
        Pattern pattern = Pattern.compile(patternString);

        //获取段落集合
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            // 判断段落里面是否有${***_flag}，并且判断是否有数据
            boolean shWarn_replaceFlag = paragraph.getText().contains("${shwarn_flag}") && shWarnList.size() == 0;
            boolean rain_replaceFlag = paragraph.getText().contains("${rain_flag}") && rainList.size() == 0;
            boolean rsvr_replaceFlag = paragraph.getText().contains("${rsvr_flag}") && rsvrList.size() == 0;
            boolean river_replaceFlag = paragraph.getText().contains("${river_flag}") && riverList.size() == 0;

            String noDataStrPre = noDataTmStr + "，全" + adnmKeys + "无";
            String dataStr = "";

            if (shWarn_replaceFlag || rain_replaceFlag || rsvr_replaceFlag || river_replaceFlag) {
                if (shWarn_replaceFlag) {
                    dataStr = noDataStrPre+"预警。";
                } else if (rain_replaceFlag) {
                    dataStr = noDataStrPre+"降雨。";
                } else if (rsvr_replaceFlag) {
                    dataStr = noDataStrPre+"超汛限水库。";
                } else if (river_replaceFlag) {
                    dataStr = noDataStrPre+"超警戒河道站。";
                }
            } else {
                String text = paragraph.getText();
                Matcher matcher = pattern.matcher(text);

                StringBuffer sb = new StringBuffer();
                while (matcher.find()) {
                    matcher.appendReplacement(sb, textMap.get(matcher.group(1)));
                }
                matcher.appendTail(sb);
                dataStr = sb.toString();
            }
            List<XWPFRun> runs = paragraph.getRuns();
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                if (i == 0) {
                    run.setText(dataStr, 0);
                } else {
                    run.setText("", 0);
                }
            }
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();//二进制OutputStream
        document.write(baos);//文档写入流
        byte[] body_data = baos.toByteArray();
        ossFeign.putFile(orgfileurl.replace("/", "HTJLUP"), body_data);
    }
    /**
     * 删除表格
     * @param table 表格对象
     */
    public static void deleteTable(XWPFTable table){
        List<XWPFTableRow> rows = table.getRows();
        int rowLength = rows.size();
        for (int i = 0; i < rowLength; i++) {
            table.removeRow(0);
        }
    }
}
