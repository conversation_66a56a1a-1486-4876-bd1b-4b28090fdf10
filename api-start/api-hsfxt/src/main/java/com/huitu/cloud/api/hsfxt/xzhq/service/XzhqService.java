package com.huitu.cloud.api.hsfxt.xzhq.service;

import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhShehuijingji;
import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqTree;
import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqTunXiangqing;
import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqXiangQing;

import java.util.List;

/**
 * <p>
 * 风险图-蓄滞洪区  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
public interface XzhqService {
    /**
     * 蓄滞洪区列表信息查询
     *
     * @param key  查询条件组合
     * @param name 最小编制区单位名称
     * @param hl 洪水量级
     * @param bzqid 编制区编码
     * @return
     */
    List<XzhqTree> getXzhqTreeInfo(String key, String name, String hl, String bzqid);
    /**
     * 蓄滞洪区社会经济统计查询
     *
     * @param key 查询条件组合
     * @return
     */
    List<XzhShehuijingji> getXzhqShjjTreeInfo(String key);
    /**
     * 蓄滞洪区列表县区乡镇详情信息查询
     *
     * @param key   查询条件组合
     * @param adcd  政区编码
     * @param bzqid 编制区id
     * @return
     */
    XzhqXiangQing getXzhqShjjDetailInfo(String key, String adcd, String bzqid);
    /**
     * 蓄滞洪区列表屯详情信息查询
     *
     * @param key   查询条件组合
     * @param hl 编制区id
     * @param adcd  政区编码
     * @param bzqid 编制区id
     * @return
     */
    XzhqTunXiangqing getXzhqTunShjjDetailInfo(String key, String hl, String adcd, String bzqid);
}
