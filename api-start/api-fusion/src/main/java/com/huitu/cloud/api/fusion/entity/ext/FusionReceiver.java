package com.huitu.cloud.api.fusion.entity.ext;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预警接收人表
 */
@TableName("EW_FUSION_RECEIVER")
@ApiModel(value = "EwReceiver对象", description = "预警接收人表")
@Data
public class FusionReceiver implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "接收人ID")
    @TableId(value = "RECEIVER_ID", type = IdType.NONE)
    private String receiverId;

    @ApiModelProperty(value = "消息ID")
    @TableField("MSG_ID")
    private String msgId;

    @ApiModelProperty(value = "手机号码")
    @TableField("PHONE_NO")
    private String phoneNo;

    @ApiModelProperty(value = "接收人姓名")
    @TableField("RECEIVER")
    private String receiver;

    @ApiModelProperty(value = "接收人职务")
    @TableField("POSITION")
    private String position;

    @ApiModelProperty(value = "标签，详情请查看数据库设计文档")
    @TableField("TAG")
    private String tag;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "最后更新时间")
    @TableField("LATEST_TIME")
    private LocalDateTime latestTime;
}