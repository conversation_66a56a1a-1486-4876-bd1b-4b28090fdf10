package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectRsvrRun;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 方案/预案修订
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectRsvrRunDao {

    /**
     * 判断是否添加过 水库(水电站)运行
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectRsvrRun> getInspectRsvrRunList(@Param("adcd") String adcd, @Param("year") String year, @Param("resCode") String resCode);

    /**
     * 添加水库(水电站)运行
     *
     * @param entity
     * @return
     */
    int insertInspectRsvrRun(BnsInspectRsvrRun entity);

    /**
     * 修改 水库(水电站)运行
     *
     * @param entity
     * @return
     */
    int updateInspectRsvrRun(BnsInspectRsvrRun entity);

    /**
     * 删除 水库(水电站)运行
     *
     * @return
     */
    int deleteInspectRsvrRun(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

