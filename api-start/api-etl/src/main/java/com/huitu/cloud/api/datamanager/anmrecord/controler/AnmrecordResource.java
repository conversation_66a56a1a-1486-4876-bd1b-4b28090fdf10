package com.huitu.cloud.api.datamanager.anmrecord.controler;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.datamanager.anmrecord.entity.*;
import com.huitu.cloud.api.datamanager.anmrecord.service.AnmrecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 异常数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-07
 */

@RestController
@Api(tags = "异常数据维护")
@RequestMapping("/api/datamanger/anmrecord")
public class AnmrecordResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "43901982-ACCD-4ACB-BDE5-995D42C33979";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private AnmrecordService baseService;

    @ApiOperation(value = "异常数据处理，包含修改，初审，终审，驳回操作", notes = "异常数据处理，包含修改，初审，终审，驳回操作")
    @PostMapping(value = "update")
    public ResponseEntity<SuccessResponse<String>> updateBsnAnmrecordr(@RequestBody AnmEdit entity) throws Exception {
        Boolean flag = baseService.editAnmRecord(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }

    @ApiOperation(value = "根据测站编码批量处理异常数据处理，修改 操作", notes = "根据测站编码批量处理异常数据处理，修改 操作")
    @PostMapping(value = "update-by-stcd")
    public ResponseEntity<SuccessResponse<String>> updateBsnAnmrecordr(@RequestBody AnmByStcdEdit entity) throws Exception {
        Boolean flag = baseService.updateBsnAnmrecordr(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }

    @ApiOperation(value = "查询雨量异常数据列表", notes = "查询雨量异常数据列表")
    @GetMapping(value = "select-rain-exception")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "staflgs", value = "异常数据状态 多个值以逗号隔开", required = true, dataType = "String", example = "1,2,3,4,5,6"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", dataType = "String"),
            @ApiImplicitParam(name = "filterCnt", value = "异常数据累计数量过滤值", dataType = "Integer", required = false, example = "5")
    })
    public ResponseEntity<SuccessResponse<List<AnmVo>>> getAnmRainRecord(@RequestParam String adcd, @RequestParam String staflgs, @RequestParam String stm, @RequestParam String etm, @RequestParam(required = false, defaultValue = "5")Integer filterCnt) throws Exception {
        List<AnmVo> list = baseService.getAnmRainRecord(adcd, staflgs, stm, etm, filterCnt);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出雨量异常数据列表", notes = "导出雨量异常数据列表")
    @GetMapping(value = "export-rain-exception")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "staflgs", value = "异常数据状态 多个值以逗号隔开", required = true, dataType = "String", example = "1,2,3,4,5,6"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", dataType = "String")
    })
    public void exportAnmRainRecord(@RequestParam String adcd, @RequestParam String staflgs, @RequestParam String stm, @RequestParam String etm, HttpServletResponse response) throws Exception {
        baseService.exportAnmRainRecord(adcd, staflgs, stm, etm, response);
    }

    @ApiOperation(value = "分页查询雨量异常数据列表", notes = "查询雨量异常数据列表（分页）")
    @PostMapping(value = "select-rain-exception-by-page")
    public ResponseEntity<SuccessResponse<IPage<AnmVo>>> getAnmRainRecordByPage(@RequestBody AnmQo anmQo) throws Exception {
        IPage<AnmVo> iPage = baseService.getAnmRainRecordByPage(anmQo.getAdcd(), anmQo.getStaflgs(), anmQo.getPageNum(), anmQo.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "查询水情异常数据列表", notes = "查询水情异常数据列表")
    @GetMapping(value = "select-zr-exception")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "staflgs", value = "异常数据状态 多个值以逗号隔开", required = true, dataType = "String", example = "1,2,3,4,5,6"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", dataType = "String"),
            @ApiImplicitParam(name = "filterCnt", value = "异常数据累计数量过滤值", dataType = "int")
    })
    public ResponseEntity<SuccessResponse<List<AnmVo>>> getZrAnmRecord(@RequestParam String adcd, @RequestParam String staflgs, @RequestParam String stm, @RequestParam String etm, @RequestParam int filterCnt) throws Exception {
        List<AnmVo> list = baseService.getZrAnmRecord(adcd, staflgs, stm, etm, filterCnt);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "导出水情异常数据列表", notes = "导出水情异常数据列表")
    @GetMapping(value = "export-zr-exception")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "staflgs", value = "异常数据状态 多个值以逗号隔开", required = true, dataType = "String", example = "1,2,3,4,5,6"),
            @ApiImplicitParam(name = "stm", value = "开始时间 yyyy-MM-dd HH:hh:ss", dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间 yyyy-MM-dd HH:hh:ss", dataType = "String"),
            @ApiImplicitParam(name = "filterCnt", value = "异常数据累计数量过滤值", dataType = "int")
    })
    public void exportZrAnmRecord(@RequestParam String adcd, @RequestParam String staflgs, @RequestParam String stm, @RequestParam String etm, @RequestParam int filterCnt, HttpServletResponse response) throws Exception {
        baseService.exportZrAnmRecord(adcd, staflgs, stm, etm, filterCnt, response);
    }

    @ApiOperation(value = "分页查询水位异常数据列表", notes = "分页查询水位异常数据列表")
    @PostMapping(value = "select-zr-exception-by-page")
    public ResponseEntity<SuccessResponse<IPage<AnmVo>>> getAnmZrRecordByPage(@RequestBody AnmQo anmQo) throws Exception {
        IPage<AnmVo> iPage = baseService.getAnmSwRecordByPage(anmQo.getAdcd(), anmQo.getStaflgs(), anmQo.getPageNum(), anmQo.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "根据测站分页查询水位异常数据列表", notes = "根据测站分页查询水位异常数据列表")
    @PostMapping(value = "select-zr-exception-stcd-by-page")
    public ResponseEntity<SuccessResponse<IPage<AnmVo>>> getAnmZrRecordStcdByPage(@RequestBody AnmStQo anmStQo) throws Exception {
        IPage<AnmVo> iPage = baseService.getAnmSwRecordStcdByPage(anmStQo.getStcd(),anmStQo.getStm(),anmStQo.getEtm(),anmStQo.getStaflgs(), anmStQo.getPageNum(), anmStQo.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", iPage));
    }

    @ApiOperation(value = "查询批量处理异常站点列表", notes = "用于批量处理水位异常数据")
    @PostMapping(value = "select-rz-exception-st")
    public ResponseEntity<SuccessResponse<IPage<RzStInfo>>> getAnmRzSt(@RequestBody QueryRzInfo queryRzInfo) throws Exception {
        IPage<RzStInfo> list = baseService.getAnmRzSt(queryRzInfo.getAdcd(), queryRzInfo.getAnmtp(), queryRzInfo.getPageNum(), queryRzInfo.getPageSize());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询雨量异常站点列表", notes = "查询雨量异常数据站点列表")
    @GetMapping(value = "select-rain-exception-st")
    @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")
    public ResponseEntity<SuccessResponse<List<AnmVo>>> getAnmRainSt(@RequestParam String adcd) throws Exception {
        List<AnmVo> list = baseService.getAnmRainStcd(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询水库或河道最新的5条水位数据", notes = "查询水库或河道最新的5条水位数据")
    @GetMapping(value = "select-zr-lately")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stcd", value = "测站编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tbnm", value = "数据表名 河道：st_river_r 水库：st_rsvr_r", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<StSwVo>>> getLatelySw(@RequestParam String stcd, @RequestParam String tbnm) throws Exception {
        List<StSwVo> list = baseService.getLatelySw(stcd, tbnm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询同时间的政区平均降雨", notes = "查询同时间的政区平均降雨")
    @GetMapping(value = "select-rain-adavg")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "tm", value = "时间 yyyy-MM-dd HH:mm", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<String>>> getAdAvg(@RequestParam String adcd, @RequestParam String tm) throws Exception {
        String avgDrp = baseService.getAdAvg(adcd, tm);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", avgDrp));
    }

    @ApiOperation(value = "查询政区异常数据的数量", notes = "查询政区异常数据的数量  ")
    @GetMapping(value = "select-data-count")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "staflgs", value = "异常数据状态 多个值以逗号隔开", required = true, dataType = "String", example = "1,2,3,4,5,6")
    })
    public ResponseEntity<SuccessResponse<Integer>> getIsExist(@RequestParam String adcd, @RequestParam String staflgs) throws Exception {
        int ct = baseService.getRecordCount(adcd, staflgs);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", ct));
    }

    @ApiOperation(value = "查询政区异常站的数量", notes = "查询政区异常站的数量")
    @PostMapping(value = "select-station-count")
    public ResponseEntity<SuccessResponse<List<AnmStatistics>>> getAnmStatisticsList(@RequestBody AnmCondition condition) {
        List<AnmStatistics> list = baseService.getAnmStatisticsList(condition.getAdcd(), condition.getBgtm(), condition.getEndtm());
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
}







