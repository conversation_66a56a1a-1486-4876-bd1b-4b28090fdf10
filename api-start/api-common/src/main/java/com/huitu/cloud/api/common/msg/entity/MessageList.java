package com.huitu.cloud.api.common.msg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-9-21
 */
@ApiModel(value="短信信息列表", description="短信信息列表信息")
public class MessageList extends MessageVo{

    @ApiModelProperty(value = " 所属政区名称  ")
    @TableField("ADNM")
    private String adnm;

    @ApiModelProperty(value = "发送成功条数")
    @TableField("SUCCOUNT")
    private Integer sucCount;

    @ApiModelProperty(value = "发送总条数")
    @TableField("TOTAL")
    private Integer total;

    @ApiModelProperty(value = "失败短信集合 ")
    private List<FailMessage> messages;

    public List<FailMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<FailMessage> messages) {
        this.messages = messages;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getSucCount() {
        return sucCount;
    }

    public void setSucCount(Integer sucCount) {
        this.sucCount = sucCount;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
