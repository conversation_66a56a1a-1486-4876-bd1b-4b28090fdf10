package com.huitu.cloud.api.ewci.weChat.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
@ApiModel(value = "政区面平均雨量信息")
public class WeChatAdAvgRain extends Model<WeChatAdAvgRain> {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "上级政区编码")
    private String padcd;

    @ApiModelProperty(value = "政区级别")
    private String adlvl;

    @ApiModelProperty(value = "下级政区面平均雨量信息")
    private List<WeChatAdAvgRain> children;

    @ApiModelProperty(value = "平均降雨")
    private String avgRain;

    @ApiModelProperty(value = "最大降雨")
    private String maxRain;

    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "测站类型")
    private String stadtpnm;

    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    @ApiModelProperty(value = "最大降雨加测站名称（导出用）")
    private String drpstnm;

    public String getStadtpnm() {
        return stadtpnm;
    }

    public void setStadtpnm(String stadtpnm) {
        this.stadtpnm = stadtpnm;
    }

    public String getDrpstnm() {
        return drpstnm;
    }

    public void setDrpstnm(String drpstnm) {
        this.drpstnm = drpstnm;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public String getAdlvl() {
        return adlvl;
    }

    public void setAdlvl(String adlvl) {
        this.adlvl = adlvl;
    }

    public List<WeChatAdAvgRain> getChildren() {
        return children;
    }

    public void setChildren(List<WeChatAdAvgRain> children) {
        this.children = children;
    }

    public String getAvgRain() {
        return avgRain;
    }

    public void setAvgRain(String avgRain) {
        this.avgRain = avgRain;
    }

    public String getMaxRain() {
        return maxRain;
    }

    public void setMaxRain(String maxRain) {
        this.maxRain = maxRain;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }
}
