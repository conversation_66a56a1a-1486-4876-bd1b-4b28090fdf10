package com.huitu.cloud.api.syq.warn.entity;

import com.huitu.cloud.api.syq.rain.entity.BsnRainAlarm;
import com.huitu.cloud.api.syq.river.entity.StRiverVo;
import com.huitu.cloud.api.syq.rsvr.entity.StRsvrVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel
public class ShWarnGrade {
    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "准备转移次数")
    private Integer prepare;

    @ApiModelProperty(value = "立即转移次数")
    private Integer immediately;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Integer getPrepare() {
        return prepare;
    }

    public void setPrepare(Integer prepare) {
        this.prepare = prepare;
    }

    public Integer getImmediately() {
        return immediately;
    }

    public void setImmediately(Integer immediately) {
        this.immediately = immediately;
    }
}
