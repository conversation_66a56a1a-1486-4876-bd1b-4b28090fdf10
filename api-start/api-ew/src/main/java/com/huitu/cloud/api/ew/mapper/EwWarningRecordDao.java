package com.huitu.cloud.api.ew.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.base.EwWarningRecord;
import com.huitu.cloud.api.ew.entity.ext.EwMessageReceiver;
import com.huitu.cloud.api.ew.entity.response.EwWarningCorrectResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarningDigestResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarningRecordResponse;
import com.huitu.cloud.api.ew.entity.sms.SmsReceiver;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface EwWarningRecordDao {

    /**
     * 获取完整政区名称
     *
     * @param params 查询参数
     * @return 完整政区名称
     */
    String getFullAdnm(@Param("map") Map<String, Object> params);

    /**
     * 获取预警分页列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 预警分页列表
     **/
    IPage<EwWarningRecordResponse> getPageList(IPage<EwWarningRecordResponse> page, @Param("map") Map<String, Object> params);

    /**
     * 获取最新的山洪预警分页列表
     *
     * @param page   分页参数
     * @param params 查询参数
     * @return 预警分页列表
     **/
    IPage<EwWarningRecordResponse> getLatestPageList(IPage<EwWarningRecordResponse> page, @Param("map") Map<String, Object> params);

    /**
     * 获取预警摘要列表
     *
     * @param warnIds 预警ID集合
     * @return 摘要列表
     */
    List<EwWarningDigestResponse> getDigestList(@Param("warnIds") List<String> warnIds);

    /**
     * 根据预警ID和预警等级，获取预警记录
     *
     * @param warnId      预警ID
     * @param warnGradeId 预警等级ID
     * @return 预警记录
     */
    EwWarningRecordResponse getRecord(@Param("warnId") String warnId, @Param("warnGradeId") Integer warnGradeId);

    /**
     * 获取山洪责任人列表
     *
     * @param adcd 村级行政区划代码
     * @return 责任人列表
     */
    List<EwMessageReceiver> getMfdPersonList(String adcd);

    /**
     * 获取地方水旱灾害防御人员列表
     *
     * @param adcd  行政区划代码
     * @param level 行政区划级别
     * @return 防御人员列表
     */
    List<EwMessageReceiver> getFddpPersonList(@Param("adcd") String adcd, @Param("level") int level);

    /**
     * 获取待更正的预警记录列表
     *
     * @param warnId 当前预警ID
     * @return 预警记录列表
     */
    List<EwWarningCorrectResponse> getCorrectList(String warnId);

    /**
     * 获取预警更正消息接收人
     *
     * @param warnIds 预警ID集合
     * @return 接收人
     */
    List<SmsReceiver> getCorrectReceivers(String warnIds);

    /**
     * 获取突发预警消息参数
     *
     * @param warnId 预警ID
     * @return 消息参数
     */
    String getSuddenMessageParameter(String warnId);

    /**
     * 创建预警
     *
     * @param record 预警记录
     * @return 受影响的行数
     */
    int create(@Param("record") EwWarningRecord record);

    /**
     * 更新预警状态
     *
     * @param warnId       预警ID
     * @param warnStatusId 预警状态
     * @return 受影响的行数
     */
    int updateStatus(@Param("warnId") String warnId, @Param("warnStatusId") int warnStatusId);

    /**
     * 更正预警
     *
     * @param warnIds 预警ID集合
     * @return 受影响的行数
     */
    int correct(String warnIds);

    /**
     * 关闭（解除）预警
     *
     * @param warnId 预警ID
     * @return 受影响的行数
     */
    int close(String warnId);

    /**
     * 人工确认
     *
     * @param warnIds 预警ID集合
     * @return 受影响的行数
     */
    int manualConfirm(@Param("warnIds") List<String> warnIds);
}
