<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.base.mapper.BnsMenuAccessLogDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getMenuList" resultType="com.huitu.cloud.api.ewci.base.entity.BnsMenuInfo">
        WITH MENU_LIST AS (SELECT MENUCODE, MENUNAME, PID, SYSTEMCODE
                           FROM MGR_MENU
                           WHERE MENUCODE = #{menuId}
                           UNION ALL
                           SELECT A.MENUCODE, A.MENUNAME, A.PID, A.SYSTEMCODE
                           FROM MGR_MENU A
                                    INNER JOIN MENU_LIST B ON A.MENUCODE = B.PID)
        SELECT SYSTEMCODE SYSTEM_ID, MENUCODE MENU_ID, MENUNAME MENU_NAME, PID PARENT_ID
        FROM MENU_LIST
    </select>
    <insert id="insert" parameterType="com.huitu.cloud.api.ewci.base.entity.BnsMenuAccessLog">
        INSERT INTO BNS_MENU_ACCESS_LOG (SYSTEM_ID, MENU_ID, ROOT_ID, USER_ID, DEPT_ID, GROUP_ID, ADCD, REMARK, TS)
        VALUES (#{systemId}, #{menuId}, #{rootId}, #{userId}, #{deptId}, #{groupId}, #{adcd}, #{remark}, #{ts})
    </insert>

    <select id="getSyqktjBar" resultType="com.huitu.cloud.api.ewci.base.entity.BnsSyqktj">
        SELECT U.num num,U.ADCD,U.menuCode,Q.MenuName menuName,W.ADNM ADNM FROM(
        SELECT count(O.logId) num, O.menuCode,O.ADCD FROM(
        SELECT A.MenuCode menuCode,B.logId,B.ADCD from MGR_MENU A LEFT JOIN
        (
        select ID logId,LEFT (LEFT (ADCD, #{map.lowerLevel}) + '000000000000000', 15) ADCD ,ROOT_ID,TS from
        BNS_MENU_ACCESS_LOG where
        LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.stm != null and map.stm !=''">
            and TS>CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and TS &lt;= CONVERT(datetime,#{map.etm})
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT ID logId, '220581000000000' ADCD,ROOT_ID,TS FROM BNS_MENU_ACCESS_LOG WHERE
            LEFT (ADCD, 6) = '220581'
            <if test="map.stm != null and map.stm !=''">
                and TS>CONVERT(datetime,#{map.stm})
            </if>
            <if test="map.etm != null and map.etm !=''">
                and TS &lt;= CONVERT(datetime,#{map.etm})
            </if>
        </if>
        ) B ON A.MenuCode=B.ROOT_ID where 1=1
        <if test="map.pid !=null and map.pid !=''">
            AND A.PID = #{map.pid}
        </if>
        <if test="map.systemCode !=null and map.systemCode !=''">
            AND A.SYSTEMCODE = #{map.systemCode}
        </if>
        ) O GROUP BY O.menuCode,O.ADCD) U LEFT JOIN MGR_MENU Q ON U.menuCode = Q.MenuCode LEFT JOIN MDT_ADCDINFO_B W ON
        U.ADCD = W.ADCD ORDER BY  Q.SORTNO ASC
    </select>
    <select id="getAdnmList" resultType="java.lang.String">
        SELECT B.ADNM
        FROM (select DISTINCT LEFT (LEFT (ADCD, #{map.lowerLevel}) + '000000000000000', 15) ADCD
        from MDT_ADCDINFO_B
        where LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>

        ) A LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        <if test="map.level != '2'.toString()">
            ORDER BY A.ADCD ASC
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL
            SELECT ADNM
            from MDT_ADCDINFO_B
            WHERE ADCD= '220581000000000'
        </if>
    </select>
    <select id="getDeptUserList" resultType="java.util.Map">
        SELECT B.ADNM as adnm, A.ADCD as adcd, A.USERID as userId
        FROM (SELECT LEFT (LEFT (ADCD, #{map.lowerLevel}) + '000000000000000', 15) ADCD, USERID
        FROM BNS_DEPTINFO_B A LEFT JOIN BSN_UGRELATION B
        ON A.DEPTID = B.DEPTID
        WHERE B.UserID IS NOT NULL
        AND LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '2'.toString() or map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL SELECT '220581000000000' ADCD,USERID FROM BNS_DEPTINFO_B A LEFT JOIN BSN_UGRELATION B
            ON A.DEPTID = B.DEPTID
            WHERE B.UserID IS NOT NULL
            and LEFT (ADCD, 6) = '220581'
        </if>
        ) A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        LEFT JOIN MDT_ADCDINFO_E E ON B.ADCD = E.ADCD
        ORDER BY E.SORTNO ASC
    </select>

</mapper>