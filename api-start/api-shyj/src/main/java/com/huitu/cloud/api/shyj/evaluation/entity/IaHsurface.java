package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * <p>
 * 沟道横断面成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-18
 */
@TableName("IA_M_HSURFACE")
@ApiModel(value="IaMHsurface对象", description="沟道横断面成果表")
public class IaHsurface extends  RelevantInfo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "横断面编码")
    @TableId(value = "HECD", type = IdType.NONE)
    private String hecd;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "沟道")
    @TableField("CHANNEL")
    private String channel;

    @ApiModelProperty(value = "位置")
    @TableField("ADDRESS")
    private String address;

    @ApiModelProperty(value = "是否跨县")
    @TableField("ISCTOWN")
    private String isctown;

    @ApiModelProperty(value = "断面标识")
    @TableField("DMIDENTIT")
    private String dmidentit;

    @ApiModelProperty(value = "断面形态")
    @TableField("DMFORM")
    private String dmform;

    @ApiModelProperty(value = "河床底质")
    @TableField("TEXTURE")
    private String texture;

    @ApiModelProperty(value = "坐标系")
    @TableField("COORDINATE")
    private String coordinate;

    @ApiModelProperty(value = "高程系统")
    @TableField("ELETYPE")
    private String eletype;

    @ApiModelProperty(value = "基点高程")
    @TableField("BASEELE")
    private Double baseele;

    @ApiModelProperty(value = "基点经度")
    @TableField("BASELGTD")
    private Double baselgtd;

    @ApiModelProperty(value = "基点纬度")
    @TableField("BASELTTD")
    private Double baselttd;

    @ApiModelProperty(value = "断面方位角")
    @TableField("AZIMUTH")
    private Double azimuth;

    @ApiModelProperty(value = "历史最高水位")
    @TableField("HMZ")
    private Double hmz;

    @ApiModelProperty(value = "成灾水位")
    @TableField("CZZ")
    private Double czz;

    @ApiModelProperty(value = "测量方法")
    @TableField("METHOD")
    private String method;

    @ApiModelProperty(value = "纵断面编码")
    @TableField("VECD")
    private String vecd;

    @ApiModelProperty(value = "导入者姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalTime moditime;


    public String getHecd() {
        return hecd;
    }

    public void setHecd(String hecd) {
        this.hecd = hecd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getIsctown() {
        return isctown;
    }

    public void setIsctown(String isctown) {
        this.isctown = isctown;
    }

    public String getDmidentit() {
        return dmidentit;
    }

    public void setDmidentit(String dmidentit) {
        this.dmidentit = dmidentit;
    }

    public String getDmform() {
        return dmform;
    }

    public void setDmform(String dmform) {
        this.dmform = dmform;
    }

    public String getTexture() {
        return texture;
    }

    public void setTexture(String texture) {
        this.texture = texture;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getEletype() {
        return eletype;
    }

    public void setEletype(String eletype) {
        this.eletype = eletype;
    }

    public Double getBaseele() {
        return baseele;
    }

    public void setBaseele(Double baseele) {
        this.baseele = baseele;
    }

    public Double getBaselgtd() {
        return baselgtd;
    }

    public void setBaselgtd(Double baselgtd) {
        this.baselgtd = baselgtd;
    }

    public Double getBaselttd() {
        return baselttd;
    }

    public void setBaselttd(Double baselttd) {
        this.baselttd = baselttd;
    }

    public Double getAzimuth() {
        return azimuth;
    }

    public void setAzimuth(Double azimuth) {
        this.azimuth = azimuth;
    }

    public Double getHmz() {
        return hmz;
    }

    public void setHmz(Double hmz) {
        this.hmz = hmz;
    }

    public Double getCzz() {
        return czz;
    }

    public void setCzz(Double czz) {
        this.czz = czz;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getVecd() {
        return vecd;
    }

    public void setVecd(String vecd) {
        this.vecd = vecd;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalTime moditime) {
        this.moditime = moditime;
    }


}
