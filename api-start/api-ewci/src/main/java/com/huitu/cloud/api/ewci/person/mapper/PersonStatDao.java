package com.huitu.cloud.api.ewci.person.mapper;

import com.huitu.cloud.api.ewci.person.entity.PersonAlterStatVo;
import com.huitu.cloud.api.ewci.person.entity.PersonInfoStat;
import com.huitu.cloud.api.ewci.person.entity.PersonStatAdcdInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 责任人统计 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface PersonStatDao {

    /**
     * 获取各类责任人信息list
     */
    List<PersonInfoStat> getPersonInfoList(@Param("map") Map<String, Object> params);

    /**
     * 根绝上级政区编码获取政区信息
     *
     * @param padcd
     * @return
     */
    List<PersonStatAdcdInfo> getAdcdInfoList(@Param("padcd") String padcd);

    /**
     * 责任人变更统计
     *
     * @param adcd
     * @return
     */
    List<PersonAlterStatVo> getPersonAlterStatList(@Param("adcd") String adcd, @Param("level") int level);
}
