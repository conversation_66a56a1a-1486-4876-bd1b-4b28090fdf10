package com.huitu.cloud.api.ewci.consult.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.consult.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 防汛会商-防御调度服务
 *
 * <AUTHOR>
 */
public interface DefDispatchService {

    /**
     * 作战图 超标准洪水淹没范围信息
     *
     * @param query
     * @return
     */
    RiverAffectadVo getRiverAffectadVo(RiverZztQuery query);

    /**
     * 作战图 超标准洪水淹没范围信息 详细数据
     *
     * @param query
     * @return
     */
    List<RiverAffectadDetailsVo> getRiverAffectadDetailsList(RiverZztQuery query);

    /**
     * 作战图 全省超标准洪水淹没范围信息 详细数据
     *
     * @return
     */
    List<RiverAffectadDetailsChildVo> getRiverAffectadDetailsAllList();

    /**
     * 作战图提防统计
     *
     * @param query
     * @return
     */
    RiverDikeVo getRiverDikeVo(RiverZztQuery query);

    /**
     * 作战图提防统计详细数据堤防
     *
     * @param query
     * @return
     */
    List<RiverDikeDetailsVo> getRiverDikeDetailsList(RiverZztQuery query);

    /**
     * 作战图 全省提防统计详细数据 堤防情况
     *
     * @return
     */
    List<RiverDikeDetailsChildVo> getRiverDikeDetailsAllList();

    /**
     * 作战图 提防统计详细数据 沙基沙堤
     *
     * @param query
     * @return
     */
    List<RiverBaseDikeVo> getRiverBaseDikeList(RiverZztQuery query);

    /**
     * 作战图 全省提防统计详细数据 沙基沙堤
     *
     * @return
     */
    List<RiverBaseDikeVo> getRiverBaseDikeAllList();

    /**
     * 作战图 提防统计详细数据 险工险段
     *
     * @param query
     * @return
     */
    List<RiverDpdsVo> getRiverDpdsList(RiverZztQuery query);

    /**
     * 作战图 全省提防统计详细数据 险工险段
     *
     * @return
     */
    List<RiverDpdsVo> getRiverDpdsAllList();

    /**
     * 作战图 穿堤建筑统计
     *
     * @param query
     * @return
     */
    RiverDtgcVo getRiverDtgcVo(RiverZztQuery query);

    /**
     * 作战图 穿堤建筑统计详细数据
     *
     * @param query
     * @return
     */
    List<RiverDtgcVo> getRiverDtgcList(RiverZztQuery query);


    /**
     * 作战图 全省穿堤建筑统计详细数据
     *
     * @return
     */
    List<RiverDtgcVo> getRiverDtgcAllList();

    /**
     * 作战图 设计洪峰流量
     *
     * @param query
     * @return
     */
    List<RiverDesingVo> getRiverDesingVo(RiverZztQuery query);

    /**
     * 作战图 洪水传播时间
     *
     * @param query
     * @return
     */
    List<RiverProVo> getRiverProList(RiverZztQuery query);

    /**
     * 水库图层
     *
     * @param query
     * @return
     */
    List<RsvrInfoVo> getRsvrInfoVoList(RiverZztQuery query);

    /**
     * 河道图层
     *
     * @param query
     * @return
     */
    List<RiverInfoVo> getRiverInfoVoList(RiverZztQuery query);

    /**
     * 获取河流四角坐标
     *
     * @param query
     * @return
     */
    BnsRiverBas getBnsRiverBas(RiverZztQuery query);
}
