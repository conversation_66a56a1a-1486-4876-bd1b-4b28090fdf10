<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.BasPersonDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getPageList" useCache="false" resultType="com.huitu.cloud.api.ewci.person.entity.BasPersonVo">
        SELECT ROW_NUMBER() OVER(ORDER BY T.ADCD, T.BAS_CODE, T.SNO ASC) SORTNO, A.ADNM XADNM, B.BAS_NAME,
        T.BAS_CODE,T.ADCD,T.SNO,
        P1.REALNM XJ_REALNM, P1.POST XJ_POST, P1.OFFICE_PHONE XJ_OFFICE_PHONE, P1.MOBILE_PHONE XJ_MOBILE_PHONE,
        P2.REALNM XZ_REALNM, P2.POST XZ_POST, P2.OFFICE_PHONE XZ_OFFICE_PHONE, P2.MOBILE_PHONE XZ_MOBILE_PHONE,
        COALESCE(P1.TS,P2.TS) TS
        FROM (
        SELECT DISTINCT BAS_CODE, ADCD, SNO FROM BNS_BASPERSON_B
        WHERE LEFT (ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.realnm != null and map.realnm != ''">
            and CHARINDEX(#{map.realnm},REALNM)>0
        </if>
        <if test="map.mobile != null and map.mobile != ''">
            and CHARINDEX(#{map.mobile},MOBILE_PHONE)>0
        </if>
        <if test="map.udtm !=null and map.udtm =='1'.toString()">
            and YEAR(TS) != YEAR(GETDATE())
        </if>
        ) T
        LEFT JOIN MDT_ADCDINFO_B A ON A.ADCD = LEFT (T.ADCD, 6)+'000000000'
        LEFT JOIN BSN_BAS_B B ON B.BAS_CODE = T.BAS_CODE
        LEFT JOIN BNS_BASPERSON_B P1 ON P1.ADCD = T.ADCD AND P1.BAS_CODE = T.BAS_CODE AND P1.SNO = T.SNO AND P1.PERTP =
        '1'
        LEFT JOIN BNS_BASPERSON_B P2 ON P2.ADCD = T.ADCD AND P2.BAS_CODE = T.BAS_CODE AND P2.SNO = T.SNO AND P2.PERTP =
        '2'
        WHERE 1=1
        <if test="map.basName != null and map.basName != ''">
            AND CHARINDEX(#{map.basName}, B.BAS_NAME) > 0
        </if>
        ORDER BY T.ADCD, T.BAS_CODE, T.SNO ASC
    </select>

    <select id="getBasSummaryList" resultType="com.huitu.cloud.api.ewci.person.entity.BasSummaryVo">
        SELECT ADCD, ADNM, PADCD, ALLNUM, XJCOUNT, XZCOUNT FROM (
        <if test="map.include == '1'.toString()">
            SELECT 0 SORTNO, A.ADCD ADCD, B.ADNM ADNM, B.PADCD PADCD,A.ALLNUM ,A.XJCOUNT, A.XZCOUNT FROM (
            SELECT #{map.adcd} ADCD,COUNT(DISTINCT A.REALNM) ALLNUM,
            COUNT(DISTINCT B.REALNM) XJCOUNT,
            COUNT(DISTINCT C.REALNM) XZCOUNT
            FROM BNS_BASPERSON_B A
            LEFT JOIN BNS_BASPERSON_B B ON A.BAS_CODE = B.BAS_CODE AND A.ADCD = B.ADCD AND A.SNO = B.SNO AND B.PERTP =
            '1'
            LEFT JOIN BNS_BASPERSON_B C ON A.BAS_CODE = C.BAS_CODE AND A.ADCD = C.ADCD AND A.SNO = C.SNO AND C.PERTP =
            '2'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
            <if test="map.level == '4'.toString()">
                AND LEFT(A.ADCD, 6) NOT IN ('220581')
            </if>
            ) A
            LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = LEFT (A.ADCD, 6)+'000000000'
            WHERE EXISTS (SELECT 8 FROM MDT_ADCDINFO_E WHERE ADTP = '1' AND ADCD = A.ADCD)
        </if>
        <if test="map.include == '1'.toString() and map.level &lt;= 4">
            UNION ALL
        </if>
        <if test="map.level &lt;= 4">
            SELECT B.SORTNO, A.ADCD ADCD, A.ADNM ADNM, A.PADCD PADCD,C.ALLNUM ,C.XJCOUNT, C.XZCOUNT
            FROM MDT_ADCDINFO_B A
            LEFT JOIN MDT_ADCDINFO_E B ON B.ADCD = A.ADCD
            LEFT JOIN (SELECT ADCD, COUNT(DISTINCT AREALNM ) ALLNUM,
            COUNT(DISTINCT BREALNM) XJCOUNT,
            COUNT(DISTINCT CREALNM) XZCOUNT
            FROM (SELECT LEFT(LEFT(A.ADCD, #{map.lowLevel}) + '000000000000000', 15) ADCD, A.REALNM AREALNM,
            B.REALNM BREALNM, C.REALNM CREALNM
            FROM BNS_BASPERSON_B A
            LEFT JOIN BNS_BASPERSON_B B ON A.BAS_CODE = B.BAS_CODE AND A.ADCD = B.ADCD AND A.SNO = B.SNO AND B.PERTP =
            '1'
            LEFT JOIN BNS_BASPERSON_B C ON A.BAS_CODE = C.BAS_CODE AND A.ADCD = C.ADCD AND A.SNO = C.SNO AND C.PERTP =
            '2'
            WHERE LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level}) AND LEFT(A.ADCD, 6) NOT IN ('220581')
            <if test="map.level == '2'.toString()">
                UNION ALL
                SELECT '220581000000000' ADCD, A.REALNM AREALNM,
                B.REALNM BREALNM, C.REALNM CREALNM
                FROM BNS_BASPERSON_B A
                LEFT JOIN BNS_BASPERSON_B B ON A.BAS_CODE = B.BAS_CODE AND A.ADCD = B.ADCD AND A.SNO = B.SNO AND B.PERTP
                = '1'
                LEFT JOIN BNS_BASPERSON_B C ON A.BAS_CODE = C.BAS_CODE AND A.ADCD = C.ADCD AND A.SNO = C.SNO AND C.PERTP
                = '2'
                WHERE LEFT(A.ADCD, 6) = '220581'
            </if>
            ) A GROUP BY ADCD) C ON C.ADCD = A.ADCD
            WHERE A.PADCD = #{map.adcd}
        </if>
        ) A
        ORDER BY SORTNO ASC
    </select>

    <insert id="insertList">
        INSERT INTO
        BNS_BASPERSON_B(BAS_CODE,ADCD,PERTP,REALNM,POST,OFFICE_PHONE,MOBILE_PHONE,SNO,TS)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.basCode},#{item.adcd},#{item.pertp},#{item.realnm},#{item.post},#{item.officePhone},#{item.mobilePhone},#{item.sno},#{item.ts})
        </foreach>
    </insert>
    <delete id="delete">
        DELETE
        FROM BNS_BASPERSON_B
        WHERE ADCD = #{map.adcd}
          AND SNO = #{map.sno}
          AND BAS_CODE = #{map.basCode}
    </delete>
    <select id="getBasCodeList" resultType="java.lang.String">
        SELECT DISTINCT BAS_CODE FROM BNS_REL_BAS_AD WHERE LEFT(ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
    </select>
    <select id="getBasFailReportList" resultType="com.huitu.cloud.api.ewci.person.entity.BasFailReportVo">
        SELECT ROW_NUMBER() OVER(ORDER BY A.BAS_CODE) SORTNO,A.BAS_CODE, B.BAS_NAME, C.ADCD, M.ADNM
        FROM (SELECT DISTINCT BAS_CODE FROM BNS_REL_BAS_AD) A
        LEFT JOIN BSN_BAS_B B ON A.BAS_CODE = B.BAS_CODE
            LEFT JOIN BNS_REL_BAS_AD C ON A.BAS_CODE = C.BAS_CODE
        LEFT JOIN MDT_ADCDINFO_B M ON C.ADCD = M.ADCD
        where not exists
        (select 8
        from (SELECT P.BAS_CODE
        FROM BNS_BASPERSON_B P
        ) C
        WHERE A.BAS_CODE = C.BAS_CODE)
        AND LEFT(C.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(C.ADCD, 6) NOT IN ('220581')
        </if>
    </select>
</mapper>
