package com.huitu.cloud.api.ewci.person.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.exception.DpdsPersonImportException;
import com.huitu.cloud.api.ewci.person.mapper.DpdsPersonDao;
import com.huitu.cloud.api.ewci.person.mapper.DpdsPersonTmpDao;
import com.huitu.cloud.api.ewci.person.service.DpdsPersonService;
import com.huitu.cloud.util.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 险工险段责任人管理服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class DpdsPersonServiceImpl implements DpdsPersonService {

    private static final Logger logger = LoggerFactory.getLogger(DpdsPersonServiceImpl.class);

    private DpdsPersonDao baseDao;
    private DpdsPersonTmpDao tmpDao;

    @Autowired
    public void setBaseDao(DpdsPersonDao baseDao) {
        this.baseDao = baseDao;
    }

    @Autowired
    public void setTmpDao(DpdsPersonTmpDao tmpDao) {
        this.tmpDao = tmpDao;
    }

    @Override
    public IPage<DpdsPerson> getPageList(DpdsPersonQuery query) {
        return baseDao.getPageList(query.toPageParam(), query.toQueryParam());
    }

    @Override
    public List<DpdsSummaryVo> getDpdsSummaryList(DpdsSummaryQuery query) {
        if (query.getLevel() > 6) {
            throw new RuntimeException("行政区划代码无效，仅支持到县以上级别（含县）");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", query.getAdcd());
        params.put("level", query.getLevel());
        params.put("include", query.isInclude() ? 1 : 0);
        params.put("lowLevel", query.getLowLevel());
        List<DpdsSummaryVo> source = baseDao.getDpdsSummaryList(params);

        if (CollectionUtils.isEmpty(source)) {
            return new ArrayList<>();
        }
        if (!query.isInclude()) {
            // 不包含下级，无需创建树
            return source;
        }
        Map<String, DpdsSummaryVo> center = new LinkedHashMap<>(source.size());
        source.forEach(ad -> center.put(ad.getAdcd(), ad));
        List<DpdsSummaryVo> target = new ArrayList<>();
        for (DpdsSummaryVo node : source) {
            if (center.containsKey(node.getPadcd())) {
                DpdsSummaryVo parent = center.get(node.getPadcd());
                if (null == parent.getChildren()) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            } else {
                target.add(node);
            }
        }
        return target;
    }

    @Override
    public void dataExport(DpdsPersonQuery query, OutputStream output) {
        IPage<DpdsPerson> page = baseDao.getPageList(query.toPageParam(), query.toQueryParam());
        EasyExcel.write(output, DpdsPerson.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("险工险段责任人信息")
                .doWrite(page.getRecords());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult<BnsDpdspersonTmp> dataImport(String xadcd, InputStream input) {
        List<BnsDpdspersonTmp> tmpData;
        try {
            tmpData = EasyExcel.read(input, BnsDpdspersonTmp.class, null)
                    .headRowNumber(2).sheet().doReadSync();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);

            throw new DpdsPersonImportException("Excel文件格式不正确，解析失败", ex);
        }

        if (CollectionUtils.isEmpty(tmpData)) {
            throw new DpdsPersonImportException("Excel文件中不包含任何数据，请检查");
        }
        // 过滤空数据，任何一项有数据即为有效
        tmpData = tmpData.stream().filter(this::nonNulProperty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tmpData)) {
            throw new DpdsPersonImportException("Excel文件中数据无效，请检查");
        }

        // 去掉多余的空格
        tmpData.forEach(this::trimProperty);

        try {
            String batchNo = batchInsertTmpData(xadcd, tmpData);
            List<BnsDpdspersonTmp> errorData = baseDao.batchImport(batchNo);
            if (!CollectionUtils.isEmpty(errorData)) {
                ImportResult<BnsDpdspersonTmp> result = new ImportResult<>();
                result.setStatus(tmpData.size() == errorData.size() ? 0 : 2);
                result.setMessage(String.format("数据（%s）导入失败，请检查", result.getStatus() == 0 ? "全部" : "部分"));
                result.setData(errorData);
                return result;
            }
        } catch (Exception ex) {
            logger.error("险工险段责任人信息入库失败", ex);

            throw new DpdsPersonImportException("数据未能成功入库，导入失败", tmpData, ex);
        }
        return new ImportResult<>();
    }

    /**
     * 批量插入临时数据
     *
     * @param xadcd   县级行政区划代码
     * @param persons 责任人信息集合
     * @return 批号
     **/
    private String batchInsertTmpData(String xadcd, List<BnsDpdspersonTmp> persons) {
        String batchNo = UUID.randomUUID().toString();
        if (!CollectionUtils.isEmpty(persons)) {
            Map<String, Object> params = new HashMap<>();
            params.put("batchNo", batchNo);
            params.put("xadcd", xadcd);
            List<List<BnsDpdspersonTmp>> lists = ListUtils.splitList(persons, 200);
            for (List<BnsDpdspersonTmp> list : lists) {
                params.put("list", list);
                tmpDao.batchInsert(params);
            }
        }
        return batchNo;
    }

    /**
     * 属性不为空
     *
     * @param tmp 临时信息
     * @return true: 非空  false: 空
     **/
    private boolean nonNulProperty(BnsDpdspersonTmp tmp) {
        return StringUtils.isNotBlank(tmp.getAdnm())
                || StringUtils.isNotBlank(tmp.getDpdsName())
                || StringUtils.isNotBlank(tmp.getProblem())
                || StringUtils.isNotBlank(tmp.getMeasure())
                || StringUtils.isNotBlank(tmp.getXzRealnm())
                || StringUtils.isNotBlank(tmp.getXzMobile())
                || StringUtils.isNotBlank(tmp.getJsRealnm())
                || StringUtils.isNotBlank(tmp.getJsMobile());
    }

    /**
     * 去掉字符串属性的空格（两端）
     *
     * @param tmp 临时信息
     **/
    private void trimProperty(BnsDpdspersonTmp tmp) {
        tmp.setAdnm(StringUtils.trim(tmp.getAdnm()));
        tmp.setDpdsName(StringUtils.trim(tmp.getDpdsName()));
        tmp.setProblem(StringUtils.trim(tmp.getProblem()));
        tmp.setMeasure(StringUtils.trim(tmp.getMeasure()));
        tmp.setXzRealnm(StringUtils.trim(tmp.getXzRealnm()));
        tmp.setXzMobile(StringUtils.trim(tmp.getXzMobile()));
        tmp.setJsRealnm(StringUtils.trim(tmp.getJsRealnm()));
        tmp.setJsMobile(StringUtils.trim(tmp.getJsMobile()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDpds(DpdsPerson entity) {
        List<DpdsPerson> list = new ArrayList<>();
        // 安全度汛行政责任人
        if (StringUtils.isNotBlank(entity.getXzRealnm()) && StringUtils.isNotBlank(entity.getXzMobile())) {
            DpdsPerson saveFlood = new DpdsPerson();
            saveFlood.setAdcd(entity.getAdcd());
            saveFlood.setRealnm(entity.getXzRealnm());
            saveFlood.setMobile(entity.getXzMobile());
            saveFlood.setDpdsCode(entity.getDpdsCode());
            saveFlood.setDpdsName(entity.getDpdsName());
            saveFlood.setProblem(entity.getProblem());
            saveFlood.setPertp("1");
            saveFlood.setMeasure(entity.getMeasure());
            saveFlood.setTs(LocalDateTime.now());
            list.add(saveFlood);
        }

        // 抢险技术责任人
        if (StringUtils.isNotBlank(entity.getJsRealnm()) && StringUtils.isNotBlank(entity.getJsMobile())) {
            DpdsPerson rescue = new DpdsPerson();
            rescue.setAdcd(entity.getAdcd());
            rescue.setPertp("2");
            rescue.setRealnm(entity.getJsRealnm());
            rescue.setMobile(entity.getJsMobile());
            rescue.setDpdsName(entity.getDpdsName());
            rescue.setDpdsCode(entity.getDpdsCode());
            rescue.setProblem(entity.getProblem());
            rescue.setMeasure(entity.getMeasure());
            rescue.setTs(LocalDateTime.now());
            list.add(rescue);
        }

        Map<String, Object> param = new HashMap<>();
        param.put("dpdsCode", entity.getDpdsCode());
        int insertCode = 0;
        int delCode = baseDao.deleteDpds(entity.getDpdsCode());
        if (delCode > 0) {
            insertCode = baseDao.insertDpds(list);
        }
        return insertCode;
    }

}
