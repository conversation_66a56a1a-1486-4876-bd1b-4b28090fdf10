package com.huitu.cloud.api.syq.skdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 水库调度信息表水库水情信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@ApiModel
public class BsnSkdtRvEx extends BsnSkdtRv {
    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private Date tm;
    @ApiModelProperty(value = "库上水位")
    @TableField("RZ")
    private String rz;
    @ApiModelProperty(value = "入库流量")
    @TableField("INQ")
    private BigDecimal inq;
    @ApiModelProperty(value = "出库流量")
    @TableField("OTQ")
    private BigDecimal otq;
    @ApiModelProperty(value = "蓄水量")
    @TableField("W")
    private BigDecimal w;
    @ApiModelProperty(value = "库水水势")
    @TableField("RWPTN")
    private String rwptn;

    @ApiModelProperty(value = "超讯限水位")
    @TableField("RZFSLTDZ")
    private BigDecimal rzfsltdz;
    @ApiModelProperty(value = "汛限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private BigDecimal lgtd;
    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private BigDecimal lttd;

    @ApiModelProperty(value = "防洪库容")
    @TableField("FLDCP")
    private BigDecimal fldcp;

    @ApiModelProperty(value = "集水面积")
    @TableField("DRNA")
    private Integer drna;

    @ApiModelProperty(value = "可（调）用防洪库容")
    @TableField("USABLEW")
    private BigDecimal usablew;

    @ApiModelProperty(value = "纳雨能力")
    private BigDecimal nyDrp;


    public Date getTm() {
        return tm;
    }

    public void setTm(Date tm) {
        this.tm = tm;
    }

    public String getRz() {
        return rz;
    }

    public void setRz(String rz) {
        this.rz = rz;
    }

    public BigDecimal getInq() {
        return inq;
    }

    public void setInq(BigDecimal inq) {
        this.inq = inq;
    }


    public BigDecimal getOtq() {
        return otq;
    }

    public void setOtq(BigDecimal otq) {
        this.otq = otq;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public BigDecimal getW() {
        return w;
    }

    public void setW(BigDecimal w) {
        this.w = w;
    }

    public BigDecimal getFldcp() {
        return fldcp;
    }

    public void setFldcp(BigDecimal fldcp) {
        this.fldcp = fldcp;
    }

    public Integer getDrna() {
        return drna;
    }

    public void setDrna(Integer drna) {
        this.drna = drna;
    }

    public BigDecimal getUsablew() {
        return usablew;
    }

    public void setUsablew(BigDecimal usablew) {
        this.usablew = usablew;
    }

    public BigDecimal getNyDrp() {
        return nyDrp;
    }

    public void setNyDrp(BigDecimal nyDrp) {
        this.nyDrp = nyDrp;
    }

    public String getRwptn() {
        return rwptn;
    }

    public void setRwptn(String rwptn) {
        this.rwptn = rwptn;
    }
}
