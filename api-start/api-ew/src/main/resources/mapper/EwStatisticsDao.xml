<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwStatisticsDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getShWarningSummary" resultType="com.huitu.cloud.api.ew.entity.ext.EwWarningSummary">
        SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT D.PHONE_NO) RCOUNT, COUNT(DISTINCT D.CALL_ID) PCOUNT,
        COUNT(DISTINCT (CASE WHEN D.[STATUS] = '1' THEN D.CALL_ID ELSE NULL END)) FCOUNT
        FROM EW_WARNING_RECORD A WITH(NOLOCK)
        LEFT JOIN EW_REL_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID
        LEFT JOIN EW_MESSAGE C WITH(NOLOCK) ON C.MSG_ID = B.MSG_ID AND C.PUSH_MODE = '1'
        LEFT JOIN EW_CALL_FEEDBACK D WITH(NOLOCK) ON D.SMS_WARN_MSG_ID = C.MSG_ID
        WHERE A.WARN_STATUS_ID IN (0, 1, 10, 20, 21, 30) AND A.WARN_TIME >= '${map.stm}'
        <if test="map.etm != null">AND A.WARN_TIME &lt;= '${map.etm}'</if>
        AND LEFT(A.ADCD, ${map.level}) = LEFT('${map.adcd}', ${map.level})
        <if test="map.level == '4'.toString()">AND LEFT(A.ADCD, 6) NOT IN ('220581')</if>
    </select>
    <select id="getRsvrWarningSummary" resultType="com.huitu.cloud.api.ew.entity.ext.EwWarningSummary">
        SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT C.PHONE_NO) RCOUNT, COUNT(DISTINCT C.CALL_ID) PCOUNT,
        COUNT(DISTINCT (CASE WHEN C.[STATUS] = '1' THEN C.CALL_ID ELSE NULL END)) FCOUNT
        FROM EW_RSVR_WARNING_RECORD A WITH(NOLOCK)
        LEFT JOIN EW_RSVR_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID AND B.PUSH_MODE = '1'
        LEFT JOIN EW_CALL_FEEDBACK C WITH(NOLOCK) ON C.SMS_WARN_MSG_ID = B.MSG_ID
        WHERE CHARINDEX(A.[STATE], '0,1,2') > 0 AND A.WARN_TIME >= '${map.stm}'
        <if test="map.etm != null">AND A.WARN_TIME &lt;= '${map.etm}'</if>
        AND EXISTS(SELECT 8 FROM BSN_STADTP_B WITH(NOLOCK) WHERE STADTP IN ('1', '2', '4')
        AND LEFT(ADCD, ${map.level}) = LEFT('${map.adcd}', ${map.level}) AND STCD = A.STCD
        <if test="map.level == '4'.toString()">AND LEFT(ADCD, 6) NOT IN ('220581')</if>)
        AND EXISTS(SELECT 8 FROM ATT_RES_BASE WITH(NOLOCK) WHERE ENG_SCAL IN ('1', '2', '3', '4', '5') AND RES_CODE = A.RES_CODE)
    </select>
    <select id="getRiverWarningSummary" resultType="com.huitu.cloud.api.ew.entity.ext.EwWarningSummary">
        SELECT COUNT(DISTINCT A.WARN_ID) WCOUNT, COUNT(DISTINCT C.PHONE_NO) RCOUNT, COUNT(DISTINCT C.CALL_ID) PCOUNT,
        COUNT(DISTINCT (CASE WHEN C.[STATUS] = '1' THEN C.CALL_ID ELSE NULL END)) FCOUNT
        FROM EW_RIVER_WARNING_RECORD A WITH(NOLOCK)
        LEFT JOIN EW_RIVER_WARNING_MESSAGE B WITH(NOLOCK) ON B.WARN_ID = A.WARN_ID AND B.PUSH_MODE = '1'
        LEFT JOIN EW_CALL_FEEDBACK C WITH(NOLOCK) ON C.SMS_WARN_MSG_ID = B.MSG_ID
        WHERE CHARINDEX(A.[STATE], '0,1,2') > 0 AND A.WARN_TIME >= '${map.stm}'
        <if test="map.etm != null">AND A.WARN_TIME &lt;= '${map.etm}'</if>
        AND EXISTS(SELECT 8 FROM BSN_STADTP_B WITH(NOLOCK) WHERE STADTP IN ('1', '2')
        AND LEFT(ADCD, ${map.level}) = LEFT('${map.adcd}', ${map.level}) AND STCD = A.STCD
        <if test="map.level == '4'.toString()">AND LEFT(ADCD, 6) NOT IN ('220581')</if>)
    </select>
    <select id="getAdWarnMessageStatistics"
            resultType="com.huitu.cloud.api.ew.entity.response.EwWarnMessageStatistics">
        SELECT AD.ADCD,AD.ADNM,AD.PADCD, COUNT(DISTINCT A.WARNID) allTotal, COUNT(DISTINCT CALL_ID) totalMassage
        FROM (SELECT WARN_ID WARNID, ADCD, WARN_TIME WTM FROM EW_WARNING_RECORD
        WHERE WARN_TYPE_ID = 10 AND WARN_STATUS_ID IN (0, 1, 10, 20, 21, 30)) A
        LEFT JOIN (SELECT WARN_ID WARNID, CALL_ID FROM EW_CALL_FEEDBACK S, EW_MESSAGE I, EW_REL_WARNING_MESSAGE R
        WHERE S.SMS_WARN_MSG_ID = I.MSG_ID AND I.MSG_ID = R.MSG_ID AND S.CALL_TYPE = '1' AND I.PUSH_MODE = '1') C ON C.WARNID = A.WARNID
        LEFT JOIN BSN_ADCD_B AD ON SUBSTRING(A.ADCD, 1, ${map.level2}) + '${map.zero}' = AD.ADCD
        WHERE A.WTM >= '${map.stm}'
        <if test="map.etm != null">
            AND A.WTM &lt;= '${map.etm}'
        </if>
        <if test="map.ad != null and map.ad !=''">
            AND SUBSTRING(A.ADCD, 1, ${map.level}) = '${map.ad}'
        </if>
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        GROUP BY AD.ADCD, AD.ADNM, AD.PADCD
    </select>



    <select id="getShWarnGradeSummary" resultType="com.huitu.cloud.api.ew.entity.ext.EwShWarnGradeSummary">
     WITH FilteredWarnings AS (
            SELECT  WARN_ID,WARN_GRADE_ID,
            <if  test="null!=map.adlvl   and  map.adlvl==1">
                LEFT(ADCD, 2) + '0000000000000' AS ADCD  -- 省级代码
            </if>
            <if  test="null!=map.adlvl   and  map.adlvl==2">
                CASE
                WHEN LEFT(ADCD,6) = '220581' THEN '220581000000000'
                ELSE LEFT(ADCD,4) + '00000000000'
                END AS ADCD  -- 市级代码
            </if>
            <if  test="null!=map.adlvl   and  map.adlvl==3">
                LEFT(ADCD,6) + '000000000' AS ADCD  -- 县级代码
            </if>
            <if  test="null!=map.adlvl   and  map.adlvl==4">
                LEFT(ADCD,9) + '000000' AS ADCD  -- 乡镇级代码
            </if>

            FROM EW_FUSION_WARNING_RECORD WITH (NOLOCK)
            WHERE WARN_STATUS_ID IN (0, 1, 10, 20, 21, 30)  -- 状态过滤
            AND WARN_TIME BETWEEN #{map.stm} AND #{map.etm}
            <if  test="null!=map.adlvl   and  map.adlvl==1">
                AND LEFT(ADCD, 2) + '0000000000000'= #{map.adcd}  -- 省级代码
            </if>

            <if  test="null!=map.adlvl   and  map.adlvl==2">
                AND CASE
                WHEN LEFT(ADCD,6) = '220581' THEN '220581000000000'
                ELSE LEFT(ADCD,4) + '00000000000'
                END = #{map.adcd}  -- 提前过滤F.ADCD条件
            </if>
            <if  test="null!=map.adlvl   and  map.adlvl==3">
                AND LEFT(ADCD, 6) + '000000000'= #{map.adcd}  -- 县级代码
            </if>
            <if  test="null!=map.adlvl   and  map.adlvl==4">
                AND LEFT(ADCD, 9) + '000000'= #{map.adcd}  -- 乡镇级代码
            </if>

        ),
        WarningStats AS (
            SELECT
            F.ADCD,
            F.ADNM,
            PC.WARN_GRADE_ID,
            COUNT(DISTINCT PC.WARN_ID) AS warn_count,
            COUNT(DISTINCT CASE WHEN C.PUSH_MODE = '1' THEN D.CALL_ID END) AS sms_count,
            COUNT(DISTINCT CASE WHEN C.PUSH_MODE = '2' THEN E.XCC_WARN_MSG_ID + E.PHONE_NO END) AS voice_count,
            COUNT(DISTINCT CASE WHEN C.PUSH_MODE = '2' AND E.STATUS = '1' AND E.CONFIRM_RESULT = '3' THEN E.XCC_WARN_MSG_ID + E.PHONE_NO END) AS voice_read
            FROM FilteredWarnings PC
            JOIN EW_FUSION_REL_MESSAGE B WITH (NOLOCK) ON B.WARN_ID = PC.WARN_ID
            JOIN EW_FUSION_MESSAGE C WITH (NOLOCK) ON C.MSG_ID = B.MSG_ID
            JOIN MDT_ADCDINFO_B F ON F.ADCD = PC.ADCD
            LEFT JOIN EW_CALL_FEEDBACK D WITH (NOLOCK) ON D.SMS_WARN_MSG_ID = C.MSG_ID
            LEFT JOIN EW_CALL_FEEDBACK E WITH (NOLOCK) ON E.XCC_WARN_MSG_ID = C.MSG_ID
            GROUP BY F.ADCD,F.ADNM, PC.WARN_GRADE_ID
        )
        SELECT
        ADCD, ADNM,
        -- 各级预警数量统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN warn_count ELSE 0 END) AS grade1_count,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN warn_count ELSE 0 END) AS grade2_count,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN warn_count ELSE 0 END) AS grade3_count,
        -- 短信发送统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN sms_count ELSE 0 END) AS grade1_sms_count,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN sms_count ELSE 0 END) AS grade2_sms_count,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN sms_count ELSE 0 END) AS grade3_sms_count,
        -- 语音发送统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN voice_count ELSE 0 END) AS grade1_voice_count,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN voice_count ELSE 0 END) AS grade2_voice_count,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN voice_count ELSE 0 END) AS grade3_voice_count,
        -- 语音已读统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN voice_read ELSE 0 END) AS grade1_voice_read,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN voice_read ELSE 0 END) AS grade2_voice_read,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN voice_read ELSE 0 END) AS grade3_voice_read
        FROM WarningStats
        GROUP BY ADCD, ADNM
    </select>


    <select id="getRsvrWarnGradeSummary" resultType="com.huitu.cloud.api.ew.entity.ext.EwRsvrWarnGradeSummary">
        WITH FilteredST AS (
        SELECT
        ST.STCD,ST.STADTP,
        <if  test="null!=map.adlvl   and  map.adlvl==1">
            LEFT(ST.ADCD, 2) + '0000000000000' AS ADCD  -- 省级代码
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==2">
            CASE
            WHEN LEFT(ST.ADCD,6) = '220581' THEN '220581000000000'
            ELSE LEFT(ST.ADCD,4) + '00000000000'
            END AS ADCD -- 市级代码
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==3">
            LEFT(ST.ADCD,6) + '000000000' AS ADCD  -- 县级代码
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==4">
            LEFT(ST.ADCD,9) + '000000' AS ADCD  -- 乡镇级代码
        </if>

        FROM BSN_STADTP_B ST
        WHERE ST.STADTP IN ('1', '2', '4')
        <if  test="null!=map.adlvl   and  map.adlvl==1">
            AND LEFT(ST.ADCD, 2) + '0000000000000'= #{map.adcd}  -- 省级代码
        </if>

        <if  test="null!=map.adlvl   and  map.adlvl==2">
            AND CASE
            WHEN LEFT(ST.ADCD,6) = '220581' THEN '220581000000000'
            ELSE LEFT(ST.ADCD,4) + '00000000000'
            END = #{map.adcd}  -- 提前过滤F.ADCD条件
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==3">
             AND LEFT(ST.ADCD, 6) + '000000000'= #{map.adcd}  -- 县级代码
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==4">
           AND LEFT(ST.ADCD, 9) + '000000'= #{map.adcd}  -- 乡镇级代码
        </if>
        )
        SELECT F.ADCD,F.ADNM,COUNT(DISTINCT A.WARN_ID) AS WARN_COUNT,
        COUNT(DISTINCT C.CALL_ID ) AS sms_count
        FROM FilteredST ST
        INNER JOIN EW_RSVR_WARNING_RECORD A ON A.STCD = ST.STCD AND A.STATE IN ('0', '1', '2')
        AND A.WARN_TIME BETWEEN #{map.stm} AND #{map.etm}
        INNER JOIN ATT_RES_BASE R ON R.RES_CODE = A.RES_CODE AND R.ENG_SCAL IN ('1', '2', '3', '4', '5')
        LEFT JOIN EW_RSVR_WARNING_MESSAGE B ON B.WARN_ID = A.WARN_ID  AND B.PUSH_MODE = '1'
        LEFT JOIN EW_CALL_FEEDBACK C ON C.SMS_WARN_MSG_ID = B.MSG_ID
        INNER JOIN MDT_ADCDINFO_B F ON F.ADCD = ST.ADCD  -- 直接使用预处理值
        GROUP BY F.ADCD, F.ADNM
    </select>


    <select id="getRiverWarnGradeSummary" resultType="com.huitu.cloud.api.ew.entity.ext.EwRiverWarnGradeSummary">
      WITH FilteredST AS (
        SELECT
            ST.STCD,ST.STADTP,
        <if  test="null!=map.adlvl   and  map.adlvl==1">
            LEFT(ST.ADCD, 2) + '0000000000000' AS ADCD  -- 省级代码
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==2">
            CASE
            WHEN LEFT(ST.ADCD,6) = '220581' THEN '220581000000000'
            ELSE LEFT(ST.ADCD,4) + '00000000000'
            END AS ADCD -- 市级代码
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==3">
            LEFT(ST.ADCD,6) + '000000000' AS ADCD  -- 县级代码
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==4">
            LEFT(ST.ADCD,9) + '000000' AS ADCD  -- 乡镇级代码
        </if>
        FROM BSN_STADTP_B ST
        WHERE ST.STADTP  IN ('1', '2')
        <if  test="null!=map.adlvl   and  map.adlvl==1">
            AND LEFT(ST.ADCD, 2) + '0000000000000'= #{map.adcd}  -- 省级代码
        </if>

        <if  test="null!=map.adlvl   and  map.adlvl==2">
            AND CASE
            WHEN LEFT(ST.ADCD,6) = '220581' THEN '220581000000000'
            ELSE LEFT(ST.ADCD,4) + '00000000000'
            END = #{map.adcd}  -- 提前过滤F.ADCD条件
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==3">
            AND LEFT(ST.ADCD, 6) + '000000000'= #{map.adcd}  -- 县级代码
        </if>
        <if  test="null!=map.adlvl   and  map.adlvl==4">
            AND LEFT(ST.ADCD, 9) + '000000'= #{map.adcd}  -- 乡镇级代码
        </if>
       )
      SELECT F.ADCD,F.ADNM,COUNT(DISTINCT A.WARN_ID) AS WARN_COUNT,
        COUNT(DISTINCT C.CALL_ID ) AS sms_count
     FROM FilteredST ST
     INNER JOIN EW_RIVER_WARNING_RECORD A ON A.STCD = ST.STCD AND A.STATE IN ('0', '1', '2')
     AND A.WARN_TIME BETWEEN #{map.stm} AND #{map.etm}
     LEFT JOIN EW_RIVER_WARNING_MESSAGE B ON B.WARN_ID = A.WARN_ID
     LEFT JOIN EW_CALL_FEEDBACK C ON C.SMS_WARN_MSG_ID = B.MSG_ID AND B.PUSH_MODE = '1'
     INNER JOIN MDT_ADCDINFO_B F ON F.ADCD = ST.ADCD  -- 直接使用预处理值
     GROUP BY F.ADCD, F.ADNM
    </select>


    <select id="getAdWarnGradeList" resultType="com.huitu.cloud.api.ew.entity.response.EwWarnGradeStatisticsResponse">
        WITH FilteredWarnings AS (
        SELECT A.ADCD, A.WARN_ID,A.WARN_GRADE_ID,
        <if test="null!=map.adlvl   and  map.adlvl==1">
            LEFT(A.ADCD, 2) + '0000000000000' AS PADCD1, -- 省级代码
            CASE
            WHEN LEFT(ADCD,6) = '220581' THEN '220581000000000'
            ELSE LEFT(ADCD,4) + '00000000000'
            END AS PADCD2, -- 市级代码
        </if>

        <if test="null!=map.adlvl   and  map.adlvl==2">
            CASE
            WHEN LEFT(ADCD,6) = '220581' THEN '220581000000000'
            ELSE LEFT(ADCD,4) + '00000000000'
            END AS PADCD1, -- 市级代码
            LEFT(ADCD,6) + '000000000' AS PADCD2, -- 县级代码
        </if>
        <if test="null!=map.adlvl   and  map.adlvl==3">
            LEFT(ADCD,6) + '000000000' AS PADCD1, -- 县级代码
            LEFT(ADCD,9) + '000000' AS PADCD2, -- 乡镇级代码
        </if>
        <if test="null!=map.adlvl   and  map.adlvl==4">
            LEFT(ADCD,9) + '000000' AS PADCD1, -- 乡镇级代码
        </if>
        LEFT(A.ADCD, 12) + '000' AS ADCD12 -- 行政村级代码
        FROM EW_FUSION_WARNING_RECORD A WITH (NOLOCK)
        WHERE A.WARN_STATUS_ID IN (0,1,10,20,21,30)
        AND A.WARN_TIME BETWEEN #{map.stm} AND #{map.etm}

        <if test="null!=map.adlvl   and  map.adlvl==1">
            AND LEFT(ADCD, 2) + '0000000000000'= #{map.adcd} -- 省级代码
        </if>

        <if test="null!=map.adlvl   and  map.adlvl==2">
            AND CASE
            WHEN LEFT(ADCD,6) = '220581' THEN '220581000000000'
            ELSE LEFT(ADCD,4) + '00000000000'
            END = #{map.adcd} -- 提前过滤F.ADCD条件
        </if>
        <if test="null!=map.adlvl   and  map.adlvl==3">
            AND LEFT(ADCD, 6) + '000000000'= #{map.adcd} -- 县级代码
        </if>
        <if test="null!=map.adlvl   and  map.adlvl==4">
            AND LEFT(ADCD, 9) + '000000'= #{map.adcd} -- 乡镇级代码
        </if>
        ),
        PadcdMapping AS (

        SELECT A.*, F1.ADCD AS PROV_ADCD, F1.ADNM AS PROV_ADNM, F1.PADCD AS PROV_PADCD, F1.ADLVL AS PROV_ADLVL
        FROM FilteredWarnings A
        LEFT JOIN MDT_ADCDINFO_B F1 WITH (NOLOCK) ON F1.ADCD = A.PADCD1
        <if test="null!=map.adlvl   and  map.adlvl!=4">
            UNION ALL
            SELECT A.*, F2.ADCD AS PROV_ADCD, F2.ADNM AS PROV_ADNM, F2.PADCD AS PROV_PADCD, F2.ADLVL AS PROV_ADLVL
            FROM FilteredWarnings A
            LEFT JOIN MDT_ADCDINFO_B F2 WITH (NOLOCK) ON F2.ADCD = A.PADCD2
        </if>
        ),
        WarningStats AS (
        SELECT
        PC.PROV_ADCD AS ADCD,
        PC.PROV_ADNM AS ADNM,
        PC.PROV_PADCD AS PADCD,
        PC.PROV_ADLVL AS ADLVL,
        PC.WARN_GRADE_ID,
        COUNT(DISTINCT PC.ADCD12) village_count,
        COUNT(DISTINCT PC.WARN_ID) AS warn_count,
        COUNT(DISTINCT D.PHONE_NO) AS liable_count,
        COUNT(DISTINCT PC.ADCD) AS nature_village_count,
        COUNT(DISTINCT CASE WHEN C.PUSH_MODE = '1' THEN D.CALL_ID END) AS sms_count,
        COUNT(DISTINCT CASE WHEN C.PUSH_MODE = '2' THEN E.XCC_WARN_MSG_ID + E.PHONE_NO END) AS voice_count,
        COUNT(DISTINCT CASE WHEN C.PUSH_MODE = '2' AND E.STATUS = '1' AND E.CONFIRM_RESULT = '3' THEN E.XCC_WARN_MSG_ID + E.PHONE_NO END) AS voice_read
        FROM PadcdMapping PC
        JOIN EW_FUSION_REL_MESSAGE B WITH (NOLOCK) ON B.WARN_ID = PC.WARN_ID
        JOIN EW_FUSION_MESSAGE C WITH (NOLOCK) ON C.MSG_ID = B.MSG_ID
        LEFT JOIN EW_CALL_FEEDBACK D WITH (NOLOCK) ON D.SMS_WARN_MSG_ID = C.MSG_ID
        LEFT JOIN EW_CALL_FEEDBACK E WITH (NOLOCK) ON E.XCC_WARN_MSG_ID = C.MSG_ID
        GROUP BY PC.PROV_ADCD, PC.PROV_ADNM, PC.PROV_PADCD, PC.PROV_ADLVL, PC.WARN_GRADE_ID
        )
        SELECT
        ADCD, ADNM, PADCD, ADLVL,
        -- 各级预警数量统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN warn_count ELSE 0 END) AS grade1_count,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN warn_count ELSE 0 END) AS grade2_count,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN warn_count ELSE 0 END) AS grade3_count,
        -- 预警人数统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN liable_count ELSE 0 END) AS grade1_liable_count,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN liable_count ELSE 0 END) AS grade2_liable_count,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN liable_count ELSE 0 END) AS grade3_liable_count,
        -- 自然屯统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN nature_village_count ELSE 0 END) AS grade1_nature_village,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN nature_village_count ELSE 0 END) AS grade2_nature_village,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN nature_village_count ELSE 0 END) AS grade3_nature_village,
        -- 行政村统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN village_count ELSE 0 END) AS grade1_village,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN village_count ELSE 0 END) AS grade2_village,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN village_count ELSE 0 END) AS grade3_village,
        -- 短信发送统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN sms_count ELSE 0 END) AS grade1_sms_count,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN sms_count ELSE 0 END) AS grade2_sms_count,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN sms_count ELSE 0 END) AS grade3_sms_count,
        -- 语音发送统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN voice_count ELSE 0 END) AS grade1_voice_count,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN voice_count ELSE 0 END) AS grade2_voice_count,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN voice_count ELSE 0 END) AS grade3_voice_count,
        -- 语音已读统计
        SUM(CASE WHEN WARN_GRADE_ID = '1' THEN voice_read ELSE 0 END) AS grade1_voice_read,
        SUM(CASE WHEN WARN_GRADE_ID = '2' THEN voice_read ELSE 0 END) AS grade2_voice_read,
        SUM(CASE WHEN WARN_GRADE_ID = '3' THEN voice_read ELSE 0 END) AS grade3_voice_read
        FROM WarningStats
        GROUP BY ADCD, ADNM, PADCD, ADLVL
    </select>

</mapper>