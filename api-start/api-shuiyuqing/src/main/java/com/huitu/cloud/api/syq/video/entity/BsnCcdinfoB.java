package com.huitu.cloud.api.syq.video.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 视频站摄像头信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07
 */
@TableName("BSN_CCDINFO_B")
@ApiModel(value="BsnCcdinfoB对象", description="视频站摄像头信息表")
public class BsnCcdinfoB extends Model<BsnCcdinfoB> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "摄像头编码")
    @TableId(value = "CCDCD", type = IdType.NONE)
    private String ccdcd;

    @ApiModelProperty(value = "摄像头名称")
    @TableField("CCDNM")
    private String ccdnm;

    @ApiModelProperty(value = "视频站编码")
    @TableField("VDSTCD")
    private String vdstcd;

    @ApiModelProperty(value = "通道号")
    @TableField("CCDPSNO")
    private String ccdpsno;

    @ApiModelProperty(value = "客户端接入服务器ID")
    @TableField("CLSVID")
    private String clsvid;

    @ApiModelProperty(value = "设备端接入服务器ID")
    @TableField("EQSVID")
    private String eqsvid;

    @ApiModelProperty(value = "sSupDeviceId 编码设备设备id")
    @TableField("SDID")
    private String sdid;

    @ApiModelProperty(value = "sMsDeviceId 客户端中间件设备id")
    @TableField("MDID")
    private String mdid;

    @ApiModelProperty(value = "sDevSupDeviceId 设备所属中间件设备id")
    @TableField("DSDID")
    private String dsdid;

    @ApiModelProperty(value = "sMsIp 客户端中间件ip")
    @TableField("CSIP")
    private String csip;

    @ApiModelProperty(value = "iMsWsPort 客户端中间件webSocket端口")
    @TableField("CSPORT")
    private String csport;

    @ApiModelProperty(value = "摄像头是否展示")
    @TableField("DISFLG")
    private String disflg;

    @ApiModelProperty(value = "主/副码流")
    @TableField("OTFLG")
    private String otflg;


    public String getCcdcd() {
        return ccdcd;
    }

    public void setCcdcd(String ccdcd) {
        this.ccdcd = ccdcd;
    }

    public String getCcdnm() {
        return ccdnm;
    }

    public void setCcdnm(String ccdnm) {
        this.ccdnm = ccdnm;
    }

    public String getVdstcd() {
        return vdstcd;
    }

    public void setVdstcd(String vdstcd) {
        this.vdstcd = vdstcd;
    }

    public String getCcdpsno() {
        return ccdpsno;
    }

    public void setCcdpsno(String ccdpsno) {
        this.ccdpsno = ccdpsno;
    }

    public String getClsvid() {
        return clsvid;
    }

    public void setClsvid(String clsvid) {
        this.clsvid = clsvid;
    }

    public String getEqsvid() {
        return eqsvid;
    }

    public void setEqsvid(String eqsvid) {
        this.eqsvid = eqsvid;
    }

    public String getSdid() {
        return sdid;
    }

    public void setSdid(String sdid) {
        this.sdid = sdid;
    }

    public String getMdid() {
        return mdid;
    }

    public void setMdid(String mdid) {
        this.mdid = mdid;
    }

    public String getDsdid() {
        return dsdid;
    }

    public void setDsdid(String dsdid) {
        this.dsdid = dsdid;
    }

    public String getCsip() {
        return csip;
    }

    public void setCsip(String csip) {
        this.csip = csip;
    }

    public String getCsport() {
        return csport;
    }

    public void setCsport(String csport) {
        this.csport = csport;
    }

    public String getDisflg() {
        return disflg;
    }

    public void setDisflg(String disflg) {
        this.disflg = disflg;
    }

    public String getOtflg() {
        return otflg;
    }

    public void setOtflg(String otflg) {
        this.otflg = otflg;
    }

    @Override
    protected Serializable pkVal() {
        return this.ccdcd;
    }

    @Override
    public String toString() {
        return "BsnCcdinfoB{" +
        "ccdcd=" + ccdcd +
        ", ccdnm=" + ccdnm +
        ", vdstcd=" + vdstcd +
        ", ccdpsno=" + ccdpsno +
        ", clsvid=" + clsvid +
        ", eqsvid=" + eqsvid +
        ", sdid=" + sdid +
        ", mdid=" + mdid +
        ", dsdid=" + dsdid +
        ", csip=" + csip +
        ", csport=" + csport +
        ", disflg=" + disflg +
        ", otflg=" + otflg +
        "}";
    }
}
