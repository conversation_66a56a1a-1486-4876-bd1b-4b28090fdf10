package com.huitu.cloud.api.ewci.consult.service.impl;

import com.huitu.cloud.api.ewci.consult.entity.*;
import com.huitu.cloud.api.ewci.consult.mapper.DefDispatchDao;
import com.huitu.cloud.api.ewci.consult.service.DefDispatchService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 防汛会商-防御调度服务 实现类
 *
 * <AUTHOR>
 */
@Service
public class DefDispatchServiceImpl implements DefDispatchService {

    private DefDispatchDao baseDao;

    public DefDispatchServiceImpl(DefDispatchDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public RiverAffectadVo getRiverAffectadVo(RiverZztQuery query) {
        return baseDao.getRiverAffectadVo(query.getRvCode());
    }

    @Override
    public List<RiverAffectadDetailsVo> getRiverAffectadDetailsList(RiverZztQuery query) {
        List<RiverAffectadDetailsVo> list = baseDao.getRiverAffectadDetailsList(query.getRvCode());
        List<RiverAffectadDetailsChildVo> listChilds = baseDao.getRiverAffectadDetailsChildList(query.getRvCode());
        list.forEach(i -> {
            List<RiverAffectadDetailsChildVo> childs = new ArrayList<>();
            List<RiverAffectadDetailsChildVo> listChild = listChilds.stream().filter(c -> i.getXadcd().equals(c.getXadcd())).collect(Collectors.toList());
            // 合计
            RiverAffectadDetailsChildVo vo = new RiverAffectadDetailsChildVo();
            vo.setBank("合计");
            vo.setAdnm(String.valueOf(listChild.stream().map(RiverAffectadDetailsChildVo::getAdcd).filter(StringUtils::isNotBlank).distinct().count()));
            vo.setXzcNum(listChild.stream().mapToInt(RiverAffectadDetailsChildVo::getXzcNum).sum());
            vo.setZrtNum(listChild.stream().mapToInt(RiverAffectadDetailsChildVo::getZrtNum).sum());
            vo.setHtcount(listChild.stream().mapToInt(RiverAffectadDetailsChildVo::getHtcount).sum());
            vo.setPtcount(listChild.stream().mapToInt(RiverAffectadDetailsChildVo::getPtcount).sum());
            childs.add(vo);

            childs.addAll(listChild);
            i.setChildren(childs);
        });
        return list;
    }

    @Override
    public List<RiverAffectadDetailsChildVo> getRiverAffectadDetailsAllList() {
        return baseDao.getRiverAffectadDetailsAllList();
    }

    @Override
    public RiverDikeVo getRiverDikeVo(RiverZztQuery query) {
        RiverDikeVo dike = baseDao.getRiverDikeVo(query.getRvCode());
        RiverDikeVo base = baseDao.getRiverBaseDikeVo(query.getRvCode());
        RiverDikeVo dpds = baseDao.getRiverDpdsVo(query.getRvCode());
        dike.setbLen(base.getbLen());
        dike.setbUpLen(base.getbUpLen());
        dike.setbNotLen(base.getbNotLen());
        dike.setDpdsLen(dpds.getDpdsLen());
        dike.setDpdsNum(dpds.getDpdsNum());
        return dike;
    }

    @Override
    public List<RiverDikeDetailsVo> getRiverDikeDetailsList(RiverZztQuery query) {
        List<RiverDikeDetailsVo> list = baseDao.getRiverDikeDetailsList(query.getRvCode());
        List<RiverDikeDetailsChildVo> listChilds = baseDao.getRiverDikeDetailsChildList(query.getRvCode());
        list.forEach(i -> {
            List<RiverDikeDetailsChildVo> listChild = listChilds.stream().filter(c -> i.getAdcd().equals(c.getAdcd())).collect(Collectors.toList());
            i.setChildren(listChild);
        });
        return list;
    }

    @Override
    public List<RiverDikeDetailsChildVo> getRiverDikeDetailsAllList() {
        return baseDao.getRiverDikeDetailsAllList();
    }

    @Override
    public List<RiverBaseDikeVo> getRiverBaseDikeList(RiverZztQuery query) {
        return baseDao.getRiverBaseDikeList(query.getRvCode());
    }

    @Override
    public List<RiverBaseDikeVo> getRiverBaseDikeAllList() {
        return baseDao.getRiverBaseDikeAllList();
    }

    @Override
    public List<RiverDpdsVo> getRiverDpdsList(RiverZztQuery query) {
        return baseDao.getRiverDpdsList(query.getRvCode());
    }

    @Override
    public List<RiverDpdsVo> getRiverDpdsAllList() {
        return baseDao.getRiverDpdsAllList();
    }

    @Override
    public RiverDtgcVo getRiverDtgcVo(RiverZztQuery query) {
        return baseDao.getRiverDtgcVo(query.getRvCode());
    }

    @Override
    public List<RiverDtgcVo> getRiverDtgcList(RiverZztQuery query) {
        return baseDao.getRiverDtgcList(query.getRvCode());
    }

    @Override
    public List<RiverDtgcVo> getRiverDtgcAllList() {
        return baseDao.getRiverDtgcAllList();
    }

    @Override
    public List<RiverDesingVo> getRiverDesingVo(RiverZztQuery query) {
        return baseDao.getRiverDesingVo(query.getRvCode());
    }

    @Override
    public List<RiverProVo> getRiverProList(RiverZztQuery query) {
        return baseDao.getRiverProList(query.getRvCode());
    }

    @Override
    public List<RsvrInfoVo> getRsvrInfoVoList(RiverZztQuery query) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", query.getStm());
        param.put("etm", query.getEtm());
        param.put("list", query.getList());
        return baseDao.getRsvrInfoVoList(param);
    }

    @Override
    public List<RiverInfoVo> getRiverInfoVoList(RiverZztQuery query) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", query.getStm());
        param.put("etm", query.getEtm());
        param.put("list", query.getList());
        return baseDao.getRiverInfoVoList(param);
    }

    @Override
    public BnsRiverBas getBnsRiverBas(RiverZztQuery query) {
        return baseDao.getBnsRiverBas(query.getRvCode());
    }
}
