package com.huitu.cloud.api.syq.skdt.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 调度水库关联的雨量站信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@ApiModel
public class RsvrRain implements Serializable {
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "流域")
    private String bsnm;
    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    @ApiModelProperty(value = "累计雨量")
    private String drps;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getBsnm() {
        return bsnm;
    }

    public void setBsnm(String bsnm) {
        this.bsnm = bsnm;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public String getDrps() {
        return drps;
    }

    public void setDrps(String drps) {
        this.drps = drps;
    }
}
