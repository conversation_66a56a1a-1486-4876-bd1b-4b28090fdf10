package com.huitu.cloud.api.screen.fhjz.service.impl;

import com.huitu.cloud.api.screen.fhjz.entity.DikeDetailVo;
import com.huitu.cloud.api.screen.fhjz.entity.DikeVo;
import com.huitu.cloud.api.screen.fhjz.mapper.DikeScreenDao;
import com.huitu.cloud.api.screen.fhjz.service.DikeScreenService;
import com.huitu.cloud.util.AdcdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 堤防获取 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Service
public class DikeScreenServiceImpl implements DikeScreenService {

    private static final Logger logger = LoggerFactory.getLogger(DikeScreenServiceImpl.class);

    @Autowired
    private DikeScreenDao dikeScreenDao;

    @Override
    public List<DikeVo> queryDikeList(String dikeGrad,String dikeName) {
        return dikeScreenDao.queryDikeList(dikeGrad,dikeName);
    }

    @Override
    public List<DikeDetailVo> dikeDetailList(String dikeGrad,String dikeName) {
        return dikeScreenDao.queryDikeDetailList(dikeGrad,dikeName);
    }

    @Override
    public Map<String, Object> queryDikeInfo(String dikeCode) {
        return dikeScreenDao.queryDikeInfo(dikeCode);
    }

    @Override
    public String statisticDikeDetail(String adcd) {
        int level = AdcdUtil.getAdLevel(adcd);
        int lowLevel = 0;
        String suffix = "";
        if (level >= 6) {
            lowLevel = level + 3;
        } else {
            lowLevel = level + 2;
        }
        if(lowLevel == 2){
            suffix = "0000000000000";
        }else if(lowLevel == 4){
            suffix = "00000000000";
        }else if(lowLevel == 6){
            suffix = "000000000";
        }else if(lowLevel == 9){
            suffix = "000000";
        }
        List<Map>  detailList = dikeScreenDao.statisticDikeDetail(adcd,level,lowLevel,suffix);
        String title = "";
        if(level == 2){
            title = "全省";
        }else if(level == 4){
            title = "全市";
        }else if(level == 6){
            title = "全县";
        }
        String totalDetail = "";
        StringBuffer subDetail = new StringBuffer("");
        // 组装前台显示内容
        if(detailList!=null && detailList.size()>0){
            for(Map<String,Object> item:detailList){
                if("SUM".equals(item.get("adnm"))){
                    totalDetail = String.format("堤防共%s处，其中1级%s处，2级%s处，3级%s处，4级%s处，5级%s处，5级及以下%s处；", String.valueOf(item.get("TOTAL")),String.valueOf(item.get("LEVEL1")),String.valueOf(item.get("LEVEL2")),String.valueOf(item.get("LEVEL3")),String.valueOf(item.get("LEVEL4")),String.valueOf(item.get("LEVEL5")),String.valueOf(item.get("LEVEL6")));
                }else{
                    subDetail.append(String.format("【%s】堤防共%s处，其中1级%s处，2级%s处，3级%s处，4级%s处，5级%s处，5级及以下%s处；",String.valueOf(item.get("adnm")), String.valueOf(item.get("TOTAL")),String.valueOf(item.get("LEVEL1")),String.valueOf(item.get("LEVEL2")),String.valueOf(item.get("LEVEL3")),String.valueOf(item.get("LEVEL4")),String.valueOf(item.get("LEVEL5")),String.valueOf(item.get("LEVEL6"))));
                }
            }
        }
        return title + totalDetail + subDetail.toString();
    }
}
