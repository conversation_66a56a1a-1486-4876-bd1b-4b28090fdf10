package com.huitu.cloud.api.ewci.hydrology.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 洪水预警文档
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警文档")
public class HyWarnDocument implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警ID")
    @TableField(value = "WARNID")
    private String warnId;

    @ApiModelProperty(value = "文件名称")
    @TableField(value = "FILE_NAME")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    @TableField(value = "FILE_PATH")
    private String filePath;

    public String getWarnId() {
        return warnId;
    }

    public void setWarnId(String warnId) {
        this.warnId = warnId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
