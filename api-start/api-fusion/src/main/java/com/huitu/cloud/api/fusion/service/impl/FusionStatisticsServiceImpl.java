package com.huitu.cloud.api.fusion.service.impl;

import com.huitu.cloud.api.fusion.entity.ext.FusionWarningSummary;
import com.huitu.cloud.api.fusion.entity.request.FusionWarnMessageReq;
import com.huitu.cloud.api.fusion.entity.request.FusionWarningSummaryReq;
import com.huitu.cloud.api.fusion.entity.response.FusionWarnMessageStatisticsResp;
import com.huitu.cloud.api.fusion.entity.response.FusionWarningSummaryResp;
import com.huitu.cloud.api.fusion.mapper.FusionStatisticsDao;
import com.huitu.cloud.api.fusion.service.FusionStatisticsService;
import com.huitu.cloud.constants.CommConstants;
import com.huitu.cloud.util.AdcdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 预警统计服务 实现类
 */
@Slf4j
@Service
public class FusionStatisticsServiceImpl implements FusionStatisticsService {

    @Resource(name = "asyncExecutor")
    private Executor asyncExecutor;

    @Autowired
    private FusionStatisticsDao baseDao;

    @Override
    public FusionWarningSummaryResp getWarningSummary(FusionWarningSummaryReq request) {
        Map<String, Object> params = request.toQuery();
        CompletableFuture<FusionWarningSummary> shFuture = CompletableFuture.supplyAsync(() -> baseDao.getShWarningSummary(params), asyncExecutor);
        CompletableFuture<FusionWarningSummary> rsvrFuture = CompletableFuture.supplyAsync(() -> baseDao.getRsvrWarningSummary(params), asyncExecutor);
        CompletableFuture<FusionWarningSummary> riverFuture = CompletableFuture.supplyAsync(() -> baseDao.getRiverWarningSummary(params), asyncExecutor);

        AtomicReference<FusionWarningSummaryResp> reference = new AtomicReference<>();
        CompletableFuture.allOf(shFuture, rsvrFuture, riverFuture).whenComplete((v, th) -> {
            FusionWarningSummaryResp response = new FusionWarningSummaryResp();
            try {
                // 山洪监测预警
                FusionWarningSummary shSummary = shFuture.get();
                if (null != shSummary) {
                    response.setWcount1(shSummary.getWcount());
                    response.setPcount1(shSummary.getPcount());
                    response.setFcount1(shSummary.getFcount());
                    response.setRcount1(shSummary.getRcount());
                }
                // 水库监测预警
                FusionWarningSummary rsvrSummary = rsvrFuture.get();
                if (null != rsvrSummary) {
                    response.setWcount2(rsvrSummary.getWcount());
                    response.setPcount2(rsvrSummary.getPcount());
                    response.setFcount2(rsvrSummary.getFcount());
                    response.setRcount2(rsvrSummary.getRcount());
                }
                // 河道监测预警
                FusionWarningSummary riverSummary = riverFuture.get();
                if (null != riverSummary) {
                    response.setWcount3(riverSummary.getWcount());
                    response.setPcount3(riverSummary.getPcount());
                    response.setFcount3(riverSummary.getFcount());
                    response.setRcount3(riverSummary.getRcount());
                }
                // 汇总统计
                response.setTotal1(response.getWcount1() + response.getWcount2() + response.getWcount3());
                response.setTotal2(response.getPcount1() + response.getPcount2() + response.getPcount3());
                response.setTotal3(response.getFcount1() + response.getFcount2() + response.getFcount3());
                response.setTotal4(response.getRcount1() + response.getRcount2() + response.getRcount3());
            } catch (Exception ignored) {
            }
            reference.set(response);
        }).join();
        return reference.get();
    }

    @Override
    public FusionWarnMessageStatisticsResp getAdWarnStatistics(FusionWarnMessageReq query) {
        int level = AdcdUtil.getAdLevel(query.getAdcd());
        Map<String, Object> param = new HashMap<>();
        param.put("stm", query.getStm());
        param.put("etm", query.getEtm());
        param.put("ad", query.getAdcd().substring(0, level));
        param.put("zero", query.getAdcd().substring(level));
        param.put("level", level);
        param.put("level2", level);
        //统计本级政区预警信息
        List<FusionWarnMessageStatisticsResp> list = baseDao.getAdWarnMessageStatistics(param);
        FusionWarnMessageStatisticsResp warnStatistics = null;
        if (!list.isEmpty()) {
            warnStatistics = list.get(0);
        }
        return warnStatistics;
    }

    @Override
    public List<FusionWarnMessageStatisticsResp> getAdWarnStatisticsTreeList(FusionWarnMessageReq query) {
        int level = AdcdUtil.getAdLevel(query.getAdcd());
        Map<String, Object> param = new HashMap<>();
        param.put("stm", query.getStm());
        param.put("etm", query.getEtm());
        param.put("ad", query.getAdcd().substring(0, level));
        param.put("zero", query.getAdcd().substring(level + (level < 6 ? 2 : 3)));
        param.put("level", level);
        param.put("level2", level + (level < 6 ? 2 : 3));
        //统计下级政区预警信息
        List<FusionWarnMessageStatisticsResp> list = baseDao.getAdWarnMessageStatistics(param);

        if (level == 2) {
            param.put("zero", query.getAdcd().substring(level + 4));
            param.put("level2", level + 4);
            List<FusionWarnMessageStatisticsResp> list2 = baseDao.getAdWarnMessageStatistics(param);
            List<FusionWarnMessageStatisticsResp> list3 = new ArrayList<>();
            for (FusionWarnMessageStatisticsResp x : list2) {
                if (!CommConstants.Public.MEIHEKOU_ADCD.equals(x.getAdcd()) && !CommConstants.Public.GONGZHULING_ADCD.equals(x.getAdcd())) {
                    list3.add(x);
                } else {
                    list.add(x);
                }
            }
            list.forEach(x -> {
                List<FusionWarnMessageStatisticsResp> childList = getChildren(list3, x.getAdcd());
                x.setChildren(childList);
            });
        }
        return list;
    }

    /**
     * 获取树的子节点
     */
    private List<FusionWarnMessageStatisticsResp> getChildren(List<FusionWarnMessageStatisticsResp> list, String pcode) {
        // 通过父级编码子类
        List<FusionWarnMessageStatisticsResp> childList = list.stream().filter(item -> item.getPadcd().equals(pcode)).collect(Collectors.toList());
        return childList;
    }
}
