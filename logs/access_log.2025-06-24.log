************ - - [24/Jun/2025:13:51:07 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [24/Jun/2025:13:51:07 +0800] "GET /login HTTP/1.1" 302 -
************ - - [24/Jun/2025:13:51:14 +0800] "GET /login?code=wwxncq&state=NcYb4t HTTP/1.1" 302 -
************ - - [24/Jun/2025:13:51:14 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [24/Jun/2025:13:51:16 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750744276228 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:17 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750744276228 HTTP/1.1" 200 552
************ - - [24/Jun/2025:13:51:17 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750744277790 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:17 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750744277790 HTTP/1.1" 200 61649
************ - - [24/Jun/2025:13:51:18 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750744278063 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:18 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750744278063 HTTP/1.1" 200 10388
************ - - [24/Jun/2025:13:51:18 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750744278479 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:18 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750744278479 HTTP/1.1" 200 2009
************ - - [24/Jun/2025:13:51:18 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750744278994 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+13:51:18&etm=&_timer304=1750744278994 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+14:00&filterCnt=6&_timer304=1750744278994 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750744278994 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750744278994 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750744278994 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:13:51:19 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750744278994 HTTP/1.1" 200 1482
************ - - [24/Jun/2025:13:51:19 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [24/Jun/2025:13:51:19 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+13:51:18&etm=&_timer304=1750744278994 HTTP/1.1" 200 156
************ - - [24/Jun/2025:13:51:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750744278994 HTTP/1.1" 200 166
************ - - [24/Jun/2025:13:51:19 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+14:00&filterCnt=6&_timer304=1750744278994 HTTP/1.1" 200 164
************ - - [24/Jun/2025:13:51:19 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [24/Jun/2025:13:51:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:13:51:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750744278994 HTTP/1.1" 200 169
************ - - [24/Jun/2025:13:51:19 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750744278994 HTTP/1.1" 200 13016
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750744279956 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/base/saas/token?_timer304=1750744279956 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750744279972 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:19 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [24/Jun/2025:13:51:20 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:13:51:20 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:13:51:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [24/Jun/2025:13:51:20 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [24/Jun/2025:13:51:20 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [24/Jun/2025:13:51:20 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************ - - [24/Jun/2025:13:51:20 +0800] "GET /api/base/saas/token?_timer304=1750744279956 HTTP/1.1" 200 411
************ - - [24/Jun/2025:13:51:21 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [24/Jun/2025:13:51:21 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:13:51:21 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750744279972 HTTP/1.1" 200 2009
************ - - [24/Jun/2025:13:51:21 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [24/Jun/2025:13:51:21 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [24/Jun/2025:13:51:21 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [24/Jun/2025:13:51:21 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [24/Jun/2025:13:51:21 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 304
************ - - [24/Jun/2025:13:51:22 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750744279956 HTTP/1.1" 200 442
************ - - [24/Jun/2025:13:51:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750744286382 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750744286383 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:51:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750744286382 HTTP/1.1" 200 160
************ - - [24/Jun/2025:13:51:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750744286383 HTTP/1.1" 200 159
************ - - [24/Jun/2025:13:56:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750744576475 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:56:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750744576475 HTTP/1.1" 200 160
************ - - [24/Jun/2025:13:56:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750744587354 HTTP/1.1" 200 -
************ - - [24/Jun/2025:13:56:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750744587354 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:01:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750744875947 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:01:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750744875947 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:01:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750744887393 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:01:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750744887393 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:06:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750745175947 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:06:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750745175947 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:06:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750745187429 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:06:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750745187429 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:11:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750745475947 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:11:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750745475947 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:11:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750745487466 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:11:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750745487466 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:16:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750745775947 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:16:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750745775947 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:16:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750745787502 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:16:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750745787502 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:21:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750746088345 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:21:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750746088345 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:21:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750746094353 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:21:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750746094353 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:26:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750746389351 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:26:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750746389351 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:27:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750746454352 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:27:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750746454352 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:31:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750746690354 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:31:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750746690354 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:32:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750746754343 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:32:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750746754343 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:36:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750746991350 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:36:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750746991350 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:37:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750747054349 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:37:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750747054349 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:41:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750747292356 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:41:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750747292356 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:42:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750747354357 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:42:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750747354357 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:46:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750747593350 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:46:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750747593350 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:47:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750747654350 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:47:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750747654350 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:51:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750747894345 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:51:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750747894345 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:52:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750747954355 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:52:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750747954355 HTTP/1.1" 200 160
************ - - [24/Jun/2025:14:56:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750748195344 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:56:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750748195344 HTTP/1.1" 200 159
************ - - [24/Jun/2025:14:57:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750748254345 HTTP/1.1" 200 -
************ - - [24/Jun/2025:14:57:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750748254345 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:01:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750748477350 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:01:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750748477350 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:01:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750748496354 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:01:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750748496354 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:06:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750748797345 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:06:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750748797345 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:07:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750748854355 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:07:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750748854355 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/ewci/base/mal/write/371?_timer304=1750749048687 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "GET /api/ewci/base/mal/write/371?_timer304=1750749048687 HTTP/1.1" 200 146
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+15:10:48&etm=&_timer304=1750749048945 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750749048945 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+16:00&filterCnt=6&_timer304=1750749048945 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750749048945 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750749048945 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-bnolist?adcd=220000000000000&_timer304=1750749048946 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-24&etm=2025-06-25&type=1&year=2025&_timer304=1750749048946 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [24/Jun/2025:15:10:48 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750749048964 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750749048964 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:10:48 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:15:10:48 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:15:10:48 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+15:10:48&etm=&_timer304=1750749048945 HTTP/1.1" 200 156
************ - - [24/Jun/2025:15:10:48 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+16:00&filterCnt=6&_timer304=1750749048945 HTTP/1.1" 200 164
************ - - [24/Jun/2025:15:10:49 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750749048945 HTTP/1.1" 200 166
************ - - [24/Jun/2025:15:10:49 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750749048945 HTTP/1.1" 200 169
************ - - [24/Jun/2025:15:10:49 +0800] "GET /api/ewci/project/yxjsindex-benefit-bnolist?adcd=220000000000000&_timer304=1750749048946 HTTP/1.1" 200 498
************ - - [24/Jun/2025:15:10:49 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750749048964 HTTP/1.1" 200 12285
************ - - [24/Jun/2025:15:10:49 +0800] "GET /api/ewci/project/yxjsindex-benefit-data?adcd=220000000000000&bno=00000000000000&stm=2025-05-24&etm=2025-06-25&type=1&year=2025&_timer304=1750749048946 HTTP/1.1" 200 12281
************ - - [24/Jun/2025:15:10:49 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750749048964 HTTP/1.1" 200 12285
************ - - [24/Jun/2025:15:10:49 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750749048945 HTTP/1.1" 200 13016
************ - - [24/Jun/2025:15:11:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750749075953 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:11:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750749075953 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:11:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750749097400 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:11:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750749097400 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:16:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750749398462 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:16:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750749398462 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:17:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750749454350 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:17:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750749454350 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:21:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750749699367 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:21:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750749699367 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:22:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750749754350 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:22:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750749754350 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:26:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750750000347 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:26:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750750000347 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:27:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750750054348 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:27:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750750054348 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:31:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750750301343 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:31:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750750301343 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:32:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750750354346 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:32:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750750354346 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:36:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750750602348 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:36:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750750602348 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:37:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750750654352 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:37:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750750654352 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:41:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750750903346 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:41:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750750903346 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:42:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750750954357 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:42:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750750954357 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750751114451 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750751114451 HTTP/1.1" 200 144
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+15:45:14&etm=&_timer304=1750751114652 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750751114652 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+16:00&filterCnt=6&_timer304=1750751114652 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750751114652 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750751114652 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750751114652 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750751114658 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [24/Jun/2025:15:45:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:15:45:14 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:15:45:14 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+15:45:14&etm=&_timer304=1750751114652 HTTP/1.1" 200 156
************ - - [24/Jun/2025:15:45:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750751114652 HTTP/1.1" 200 166
************ - - [24/Jun/2025:15:45:14 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [24/Jun/2025:15:45:14 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+16:00&filterCnt=6&_timer304=1750751114652 HTTP/1.1" 200 164
************ - - [24/Jun/2025:15:45:14 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750751114652 HTTP/1.1" 200 169
************ - - [24/Jun/2025:15:45:14 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750751114652 HTTP/1.1" 200 161
************ - - [24/Jun/2025:15:45:14 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750751114652 HTTP/1.1" 200 13016
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-24+08:00&etm=2025-06-24+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750751114901 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750751114658 HTTP/1.1" 200 159491
************ - - [24/Jun/2025:15:45:14 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751114924 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:14 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751114924 HTTP/1.1" 200 155
************ - - [24/Jun/2025:15:45:15 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [24/Jun/2025:15:45:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [24/Jun/2025:15:45:15 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [24/Jun/2025:15:45:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [24/Jun/2025:15:45:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [24/Jun/2025:15:45:16 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:15:45:16 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751116008 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:16 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751116008 HTTP/1.1" 200 155
************ - - [24/Jun/2025:15:45:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-24+08:00&etm=2025-06-24+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750751114901 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:15:45:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:45:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:45:18 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751118149 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:45:18 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751118149 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:15:46:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750751177346 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:46:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750751177346 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:46:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750751204455 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:46:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750751204455 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:47:15 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:47:16 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:15:47:16 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751236300 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:47:16 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751236300 HTTP/1.1" 200 155
************ - - [24/Jun/2025:15:47:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:47:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:47:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:47:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:47:18 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751238096 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:47:18 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751238096 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:15:49:15 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:49:16 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:15:49:16 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751356142 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:49:16 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751356142 HTTP/1.1" 200 155
************ - - [24/Jun/2025:15:49:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:49:16 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:49:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:49:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:49:17 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751357774 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:49:17 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751357774 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:15:50:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750751415348 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:50:15 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:50:15 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:50:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750751415350 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:50:15 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:50:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750751415348 HTTP/1.1" 200 161
************ - - [24/Jun/2025:15:50:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [24/Jun/2025:15:50:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:15:50:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750751415350 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:15:50:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [24/Jun/2025:15:51:15 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:51:16 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:15:51:16 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751476304 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:51:16 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751476304 HTTP/1.1" 200 155
************ - - [24/Jun/2025:15:51:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:51:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:51:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:51:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:51:18 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751478220 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:51:18 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751478220 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:15:51:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750751505344 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:51:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750751505344 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:52:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750751554473 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:52:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750751554473 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:53:15 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:53:16 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:15:53:16 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751596249 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:53:16 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751596249 HTTP/1.1" 200 155
************ - - [24/Jun/2025:15:53:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:53:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:53:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:53:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:53:17 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751597842 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:53:17 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751597842 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:15:55:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750751715350 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:15 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:15 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:15 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750751715352 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:15 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:15 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750751715350 HTTP/1.1" 200 161
************ - - [24/Jun/2025:15:55:15 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [24/Jun/2025:15:55:15 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:15:55:16 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:15:55:16 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751716188 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:16 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751716188 HTTP/1.1" 200 155
************ - - [24/Jun/2025:15:55:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+16:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750751715352 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:15:55:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [24/Jun/2025:15:55:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:55:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:55:17 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751717875 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:55:17 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751717875 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:15:56:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750751806347 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:56:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750751806347 HTTP/1.1" 200 159
************ - - [24/Jun/2025:15:57:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750751830237 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:57:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750751830237 HTTP/1.1" 200 160
************ - - [24/Jun/2025:15:57:16 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:57:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:15:57:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751837175 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:57:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750751837175 HTTP/1.1" 200 155
************ - - [24/Jun/2025:15:57:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:57:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:57:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:57:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:15:57:18 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751838767 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:57:18 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750751838767 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:15:58:23 +0800] "OPTIONS /api/ewci/base/mal/write/193?_timer304=1750751903751 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:23 +0800] "GET /api/ewci/base/mal/write/193?_timer304=1750751903751 HTTP/1.1" 200 146
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+15:58:23&etm=&_timer304=1750751904021 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750751904021 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+16:00&filterCnt=6&_timer304=1750751904021 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750751904021 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750751904021 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+15:58:23&etm=&_timer304=1750751904021 HTTP/1.1" 200 156
************ - - [24/Jun/2025:15:58:24 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [24/Jun/2025:15:58:24 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750751904021 HTTP/1.1" 200 166
************ - - [24/Jun/2025:15:58:24 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+16:00&filterCnt=6&_timer304=1750751904021 HTTP/1.1" 200 164
************ - - [24/Jun/2025:15:58:24 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [24/Jun/2025:15:58:24 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:15:58:24 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750751904021 HTTP/1.1" 200 169
************ - - [24/Jun/2025:15:58:24 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750751904021 HTTP/1.1" 200 13016
************ - - [24/Jun/2025:15:58:24 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750751904723 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750751904723 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:15:58:24 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750751904923 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:24 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750751904923 HTTP/1.1" 200 777
************ - - [24/Jun/2025:15:58:25 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:25 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:15:58:28 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:28 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:15:58:41 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:41 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:15:58:41 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750751921625 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:41 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750751921625 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:15:58:41 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750751921665 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:41 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750751921665 HTTP/1.1" 200 777
************ - - [24/Jun/2025:15:58:41 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:42 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:15:58:45 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:45 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:15:58:56 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:56 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:15:58:56 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750751936847 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:56 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750751936847 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:15:58:56 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750751936880 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:56 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750751936880 HTTP/1.1" 200 777
************ - - [24/Jun/2025:15:58:56 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:15:58:57 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:16:01:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750752077345 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:01:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750752077345 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:01:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750752107346 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:01:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750752107346 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:03:18 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:03:19 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:16:03:19 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752199439 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:03:19 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752199439 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:16:03:19 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752199493 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:03:19 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752199493 HTTP/1.1" 200 777
************ - - [24/Jun/2025:16:03:19 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:03:19 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:16:03:58 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:03:59 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:04:15 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:04:16 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:05:10 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:05:10 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:05:16 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:05:16 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:06:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750752408346 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:06:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750752408346 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:06:58 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:07:02 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:16:07:02 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752422933 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:07:02 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752422933 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:16:07:02 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752422983 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:07:03 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752422983 HTTP/1.1" 200 777
************ - - [24/Jun/2025:16:07:03 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:07:05 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:16:08:30 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:08:30 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:09:39 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:09:44 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:11:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750752713355 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:11:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750752713355 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:12:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750752724816 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:12:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750752724816 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:12:06 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:12:07 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:16:12:07 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752727126 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:12:07 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752727126 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:16:12:07 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752727165 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:12:07 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752727165 HTTP/1.1" 200 777
************ - - [24/Jun/2025:16:12:07 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:12:07 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:16:15:26 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:26 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:16:15:26 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752926742 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:26 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752926742 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:16:15:26 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752926812 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:26 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752926812 HTTP/1.1" 200 777
************ - - [24/Jun/2025:16:15:26 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:27 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:16:15:33 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:34 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:16:15:34 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752934168 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:34 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752934168 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:16:15:34 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752934214 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:34 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752934214 HTTP/1.1" 200 777
************ - - [24/Jun/2025:16:15:34 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:34 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:16:15:37 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:38 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:15:41 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:42 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:16:15:42 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752942251 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:42 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750752942251 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:16:15:42 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752942294 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:42 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750752942294 HTTP/1.1" 200 777
************ - - [24/Jun/2025:16:15:42 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:42 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:16:15:43 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:15:43 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:16:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750752977342 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:16:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750752977342 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:16:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750753013427 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:16:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750753013427 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:18:10 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:18:10 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 27680
************ - - [24/Jun/2025:16:18:10 +0800] "OPTIONS /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750753090936 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:18:10 +0800] "GET /api/base/perliable/select-res-info-list/220000000000000?_timer304=1750753090936 HTTP/1.1" 200 204143
************ - - [24/Jun/2025:16:18:11 +0800] "OPTIONS /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750753091006 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:18:11 +0800] "GET /api/base/perliable/select-rsvr-fail-report-list/220000000000000?_timer304=1750753091006 HTTP/1.1" 200 777
************ - - [24/Jun/2025:16:18:11 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:18:11 +0800] "POST /api/base/perliable/select-rsvr-per-by-page1 HTTP/1.1" 200 1069125
************ - - [24/Jun/2025:16:20:07 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:20:08 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:20:11 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:20:11 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:20:13 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:20:14 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:20:16 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:20:16 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:20:17 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:20:18 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:20:19 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:20:19 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:20:20 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:20:21 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:20:29 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:20:30 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:21:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750753275950 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:21:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750753275950 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:21:18 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:21:28 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:21:33 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:21:40 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:21:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750753314353 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:21:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750753314353 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:26:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750753615355 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:26:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750753615355 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:27:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750753654357 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:27:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750753654357 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:31:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750753916476 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:32:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750753916476 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:32:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750753954352 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:32:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750753954352 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:32:45 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:32:46 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:32:59 +0800] "OPTIONS /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:33:00 +0800] "POST /api/base/perliable/select-rsvr-per-sum-list HTTP/1.1" 200 2361
************ - - [24/Jun/2025:16:36:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750754177355 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:36:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750754177355 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:37:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750754220346 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:37:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750754220346 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:41:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750754477355 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:41:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750754477355 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:42:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750754521473 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:42:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750754521473 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:47:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750754822347 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:47:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750754822347 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:47:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750754854346 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:47:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750754854346 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:52:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750755123345 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:52:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750755123345 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:52:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750755154469 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:52:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750755154469 HTTP/1.1" 200 160
************ - - [24/Jun/2025:16:57:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750755424353 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:57:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750755424353 HTTP/1.1" 200 159
************ - - [24/Jun/2025:16:57:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750755454349 HTTP/1.1" 200 -
************ - - [24/Jun/2025:16:57:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750755454349 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:02:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750755725349 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:02:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750755725349 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:02:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750755754354 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:02:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750755754354 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:07:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750756026465 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:07:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750756026465 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:07:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750756054352 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:07:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750756054352 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:12:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750756327470 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:12:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750756327470 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:12:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750756354347 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:12:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750756354347 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:17:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750756628343 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:17:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750756628343 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:17:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750756654352 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:17:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750756654352 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750756809715 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+17:20:09&etm=&_timer304=1750756809715 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750756809715 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+18:00&filterCnt=6&_timer304=1750756809715 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750756809715 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750756809715 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750756809717 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750756809721 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750756809734 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:09 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+17:20:09&etm=&_timer304=1750756809715 HTTP/1.1" 200 156
************ - - [24/Jun/2025:17:20:10 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+18:00&filterCnt=6&_timer304=1750756809715 HTTP/1.1" 200 164
************ - - [24/Jun/2025:17:20:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750756809715 HTTP/1.1" 200 166
************ - - [24/Jun/2025:17:20:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:17:20:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [24/Jun/2025:17:20:10 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [24/Jun/2025:17:20:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:17:20:10 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750756809717 HTTP/1.1" 200 161
************ - - [24/Jun/2025:17:20:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750756809715 HTTP/1.1" 200 169
************ - - [24/Jun/2025:17:20:10 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750756809721 HTTP/1.1" 200 159491
************ - - [24/Jun/2025:17:20:10 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:11 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750756809715 HTTP/1.1" 200 13016
************ - - [24/Jun/2025:17:20:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [24/Jun/2025:17:20:11 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750756809715 HTTP/1.1" 200 144
************ - - [24/Jun/2025:17:20:12 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:20:13 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [24/Jun/2025:17:20:13 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750756809734 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:17:20:13 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:17:20:13 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:17:20:13 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750756813586 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:13 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750756813590 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:13 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750756813586 HTTP/1.1" 200 155
************ - - [24/Jun/2025:17:20:13 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750756813590 HTTP/1.1" 200 155
************ - - [24/Jun/2025:17:20:13 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:13 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:14 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:20:14 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:15 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:20:15 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:15 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:15 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:20:16 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:20:16 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750756816145 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:16 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:20:16 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750756816236 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:16 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750756816236 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:17:20:16 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750756816145 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:17:20:16 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:16 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:20:17 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [24/Jun/2025:17:20:17 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [24/Jun/2025:17:21:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750756875951 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:21:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750756875951 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:21:38 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750756898417 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:21:38 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750756898417 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:21:38 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750756898417 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:21:38 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750756898417 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:21:38 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750756898417 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:21:38 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750756898417 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:22:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750756928402 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:22:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750756928402 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:25:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757109263 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:25:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757109263 HTTP/1.1" 200 161
************ - - [24/Jun/2025:17:25:09 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:25:09 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:25:09 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757109619 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:25:09 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:25:09 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [24/Jun/2025:17:25:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:25:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [24/Jun/2025:17:25:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757109619 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:17:26:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750757175954 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750757175954 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:26:30 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757190366 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:30 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757190366 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:30 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757190366 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:30 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+13:51&_timer304=1750757190368 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:30 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757190366 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:26:30 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757190366 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:26:30 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757190366 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:26:30 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+13:51&_timer304=1750757190368 HTTP/1.1" 200 1285
************ - - [24/Jun/2025:17:26:36 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+13:51&_timer304=1750757196417 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:36 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757196417 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:36 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757196417 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:36 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757196417 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:36 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757196417 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:26:36 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+13:51&_timer304=1750757196417 HTTP/1.1" 200 1285
************ - - [24/Jun/2025:17:26:36 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757196417 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:26:36 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757196417 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:26:40 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757200679 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:40 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757200679 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:40 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+13:51&_timer304=1750757200679 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:40 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757200679 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:26:40 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757200679 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:26:40 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757200679 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:26:40 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757200679 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:26:40 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+13:51&_timer304=1750757200679 HTTP/1.1" 200 1285
************ - - [24/Jun/2025:17:27:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750757228455 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:27:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750757228455 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:30:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757409260 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:09 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757409260 HTTP/1.1" 200 161
************ - - [24/Jun/2025:17:30:09 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:09 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:09 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757409617 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:09 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:09 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [24/Jun/2025:17:30:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:30:10 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757409617 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:17:30:10 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [24/Jun/2025:17:30:27 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [24/Jun/2025:17:30:27 +0800] "GET /login HTTP/1.1" 302 -
************ - - [24/Jun/2025:17:30:27 +0800] "GET /login?code=6gLGJV&state=MSZlji HTTP/1.1" 302 -
************ - - [24/Jun/2025:17:30:27 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [24/Jun/2025:17:30:28 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750757428674 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:28 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750757428674 HTTP/1.1" 200 552
************ - - [24/Jun/2025:17:30:28 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750757428928 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750757428928 HTTP/1.1" 200 61649
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750757429140 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750757429140 HTTP/1.1" 200 10388
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750757429186 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750757429186 HTTP/1.1" 200 2009
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750757429375 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+17:30:29&etm=&_timer304=1750757429375 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750757429375 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+18:00&filterCnt=6&_timer304=1750757429375 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750757429375 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750757429375 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [24/Jun/2025:17:30:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:17:30:29 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [24/Jun/2025:17:30:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750757429375 HTTP/1.1" 200 166
************ - - [24/Jun/2025:17:30:29 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+17:30:29&etm=&_timer304=1750757429375 HTTP/1.1" 200 156
************ - - [24/Jun/2025:17:30:29 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+18:00&filterCnt=6&_timer304=1750757429375 HTTP/1.1" 200 164
************ - - [24/Jun/2025:17:30:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:17:30:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750757429375 HTTP/1.1" 200 169
************ - - [24/Jun/2025:17:30:29 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750757429375 HTTP/1.1" 200 1482
************ - - [24/Jun/2025:17:30:29 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750757429375 HTTP/1.1" 200 13016
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/base/saas/token?_timer304=1750757429951 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750757429951 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750757429963 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:29 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:30 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:30 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [24/Jun/2025:17:30:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:30:31 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:30:31 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:30:31 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************ - - [24/Jun/2025:17:30:31 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [24/Jun/2025:17:30:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [24/Jun/2025:17:30:31 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [24/Jun/2025:17:30:32 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [24/Jun/2025:17:30:32 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [24/Jun/2025:17:30:32 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750757429963 HTTP/1.1" 200 2009
************ - - [24/Jun/2025:17:30:32 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [24/Jun/2025:17:30:32 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [24/Jun/2025:17:30:32 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [24/Jun/2025:17:30:33 +0800] "GET /api/base/saas/token?_timer304=1750757429951 HTTP/1.1" 200 411
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750757434200 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750757434200 HTTP/1.1" 200 144
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+17:30:34&etm=&_timer304=1750757434391 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750757434391 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750757434391 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+18:00&filterCnt=6&_timer304=1750757434391 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750757434391 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750757434396 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757434392 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [24/Jun/2025:17:30:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:17:30:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757434526 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [24/Jun/2025:17:30:34 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+17:30:34&etm=&_timer304=1750757434391 HTTP/1.1" 200 156
************ - - [24/Jun/2025:17:30:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750757434391 HTTP/1.1" 200 166
************ - - [24/Jun/2025:17:30:34 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757434546 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:34 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+18:00&filterCnt=6&_timer304=1750757434391 HTTP/1.1" 200 164
************ - - [24/Jun/2025:17:30:34 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757434392 HTTP/1.1" 200 161
************ - - [24/Jun/2025:17:30:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750757434391 HTTP/1.1" 200 169
************ - - [24/Jun/2025:17:30:34 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [24/Jun/2025:17:30:34 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750757434396 HTTP/1.1" 200 159491
************ - - [24/Jun/2025:17:30:34 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [24/Jun/2025:17:30:34 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750757434391 HTTP/1.1" 200 13016
************ - - [24/Jun/2025:17:30:34 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757434546 HTTP/1.1" 200 155
************ - - [24/Jun/2025:17:30:34 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [24/Jun/2025:17:30:34 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [24/Jun/2025:17:30:35 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [24/Jun/2025:17:30:36 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:17:30:36 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757436060 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:36 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757436060 HTTP/1.1" 200 155
************ - - [24/Jun/2025:17:30:36 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************ - - [24/Jun/2025:17:30:36 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757434526 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:17:30:37 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:30:38 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750757429951 HTTP/1.1" 200 442
************ - - [24/Jun/2025:17:30:38 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:30:38 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750757438465 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:38 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750757438465 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:17:30:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750757439354 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750757439354 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750757439354 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:30:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750757439354 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757441345 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750757441351 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757441345 HTTP/1.1" 200 161
************ - - [24/Jun/2025:17:30:41 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750757441351 HTTP/1.1" 200 159491
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+17:30:41&etm=&_timer304=1750757441438 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750757441438 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+18:00&filterCnt=6&_timer304=1750757441438 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750757441438 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750757441438 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757441588 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-21+17:30:41&etm=&_timer304=1750757441438 HTTP/1.1" 200 156
************ - - [24/Jun/2025:17:30:41 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-24+08:00&etm=2025-06-24+18:00&filterCnt=6&_timer304=1750757441438 HTTP/1.1" 200 164
************ - - [24/Jun/2025:17:30:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750757441438 HTTP/1.1" 200 166
************ - - [24/Jun/2025:17:30:41 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [24/Jun/2025:17:30:41 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [24/Jun/2025:17:30:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:17:30:41 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [24/Jun/2025:17:30:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [24/Jun/2025:17:30:41 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [24/Jun/2025:17:30:41 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750757441438 HTTP/1.1" 200 169
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757441643 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:41 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757441643 HTTP/1.1" 200 155
************ - - [24/Jun/2025:17:30:41 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750757441438 HTTP/1.1" 200 13016
************ - - [24/Jun/2025:17:30:41 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [24/Jun/2025:17:30:41 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:42 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:42 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [24/Jun/2025:17:30:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [24/Jun/2025:17:30:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757441588 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:17:30:43 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:17:30:43 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757443192 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:43 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757443192 HTTP/1.1" 200 155
************ - - [24/Jun/2025:17:30:43 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:30:44 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:44 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:30:44 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:45 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:30:45 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750757445668 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:45 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750757445668 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:17:30:46 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:46 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:46 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [24/Jun/2025:17:30:46 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [24/Jun/2025:17:30:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750757450876 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750757450876 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:30:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750757450946 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:30:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750757450946 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:31:04 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:05 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:17:31:05 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757465319 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:05 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750757465319 HTTP/1.1" 200 155
************ - - [24/Jun/2025:17:31:05 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:06 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:06 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:31:06 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:31:06 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:06 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:31:06 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750757466918 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:06 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750757466918 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:17:31:08 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:08 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:08 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [24/Jun/2025:17:31:08 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [24/Jun/2025:17:31:28 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757488897 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:28 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757488897 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:28 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757488897 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:28 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757488897 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:31:28 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750757488910 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:28 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757488897 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:31:28 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757488897 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:31:28 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750757488910 HTTP/1.1" 200 1285
************ - - [24/Jun/2025:17:31:41 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757501486 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:41 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757501486 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:41 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757501486 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:41 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750757501486 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:31:41 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757501486 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:31:41 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757501486 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:31:41 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757501486 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:31:41 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750757501486 HTTP/1.1" 200 1285
************ - - [24/Jun/2025:17:35:37 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757737239 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:37 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757737239 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:37 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757737239 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:37 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757737239 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:35:37 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757737239 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:35:37 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757737239 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:35:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750757740876 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750757740876 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:35:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757741038 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750757741038 HTTP/1.1" 200 161
************ - - [24/Jun/2025:17:35:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757741469 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [24/Jun/2025:17:35:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:35:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750757741469 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:17:35:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [24/Jun/2025:17:35:46 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757746239 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:46 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757746239 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:46 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757746239 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:46 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750757746239 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:46 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750757746239 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:35:46 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750757746239 HTTP/1.1" 200 1285
************ - - [24/Jun/2025:17:35:46 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750757746239 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:35:46 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750757746239 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:35:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750757750977 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:35:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750757750977 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:40:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750758040880 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:40:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750758040880 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:40:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750758041034 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:40:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750758041034 HTTP/1.1" 200 161
************ - - [24/Jun/2025:17:40:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:40:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750758041467 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:40:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:40:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:40:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [24/Jun/2025:17:40:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:40:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [24/Jun/2025:17:40:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750758041467 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:17:40:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750758051023 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:40:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750758051023 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:45:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750758340878 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:45:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750758340878 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:45:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750758341032 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:45:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750758341032 HTTP/1.1" 200 161
************ - - [24/Jun/2025:17:45:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:45:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750758341479 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:45:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:45:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:45:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [24/Jun/2025:17:45:41 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:45:42 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750758341479 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:17:45:42 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [24/Jun/2025:17:45:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750758351357 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:45:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750758351357 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:50:07 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750758606529 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:07 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750758606529 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:07 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750758606529 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:07 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750758606529 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:08 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750758606529 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:50:08 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750758606529 HTTP/1.1" 200 1146
************ - - [24/Jun/2025:17:50:08 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750758606529 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:50:08 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750758606529 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:50:17 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750758617991 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:17 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750758617991 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:17 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750758617991 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:18 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750758617991 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:18 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750758617991 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:50:18 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750758617991 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:50:18 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750758617991 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:50:18 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750758617991 HTTP/1.1" 200 1146
************ - - [24/Jun/2025:17:50:38 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750758638049 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:38 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750758638049 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:38 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750758638049 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:38 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750758638049 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:38 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750758638049 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:50:38 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750758638049 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:50:38 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750758638049 HTTP/1.1" 200 1146
************ - - [24/Jun/2025:17:50:38 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750758638049 HTTP/1.1" 200 520
************ - - [24/Jun/2025:17:50:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750758640877 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:41 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750758641031 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:41 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750758641031 HTTP/1.1" 200 161
************ - - [24/Jun/2025:17:50:41 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:41 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:41 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:41 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750758641464 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:41 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [24/Jun/2025:17:50:42 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:50:43 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [24/Jun/2025:17:50:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750758640877 HTTP/1.1" 200 160
************ - - [24/Jun/2025:17:50:43 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-24+08:00&etm=2025-06-24+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750758641464 HTTP/1.1" 200 440467
************ - - [24/Jun/2025:17:50:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750758651404 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:50:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750758651404 HTTP/1.1" 200 159
************ - - [24/Jun/2025:17:52:11 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:52:12 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 151
************ - - [24/Jun/2025:17:52:12 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750758732076 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:52:12 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750758732076 HTTP/1.1" 200 155
************ - - [24/Jun/2025:17:52:12 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:52:12 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:52:12 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:52:13 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:52:13 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [24/Jun/2025:17:52:13 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************ - - [24/Jun/2025:17:52:13 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750758733614 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:52:13 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750758733614 HTTP/1.1" 200 1513
************ - - [24/Jun/2025:17:53:29 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:53:29 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:53:30 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [24/Jun/2025:17:53:30 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [24/Jun/2025:17:53:33 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750758813585 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:53:33 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750758813585 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:53:33 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750758813585 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:53:33 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750758813585 HTTP/1.1" 200 -
************ - - [24/Jun/2025:17:53:33 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750758813585 HTTP/1.1" 200 838
************ - - [24/Jun/2025:17:53:33 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750758813585 HTTP/1.1" 200 519
************ - - [24/Jun/2025:17:53:33 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-24+17:30&_timer304=1750758813585 HTTP/1.1" 200 1146
************ - - [24/Jun/2025:17:53:33 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750758813585 HTTP/1.1" 200 520
