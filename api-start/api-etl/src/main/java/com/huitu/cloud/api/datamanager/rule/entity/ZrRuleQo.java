package com.huitu.cloud.api.datamanager.rule.entity;

import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
public class ZrRuleQo extends PageBean {
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "测站类型  ZZ:河道  RR:水库")
    private String sttp;

    @ApiModelProperty(value = "政区编码")
    @SqlInjection
    private String adcd;

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getSttp() {
        return sttp;
    }

    public void setSttp(String sttp) {
        this.sttp = sttp;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }
}
