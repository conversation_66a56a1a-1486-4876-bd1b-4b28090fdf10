package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * 无线广播站信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@ApiModel(value="关联信息", description="调查评价关联信息")
public class RelevantInfo extends DtExtendInfo implements Serializable {
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "流域名称")
    private String wsnm;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }
}
