package com.huitu.cloud.api.ewci.project.service.impl;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.ewci.project.entity.BnsShOperation;
import com.huitu.cloud.api.ewci.project.entity.BnsShProject;
import com.huitu.cloud.api.ewci.project.mapper.BnsShOperationDao;
import com.huitu.cloud.api.ewci.project.service.BnsShOperationService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 山洪灾害项目运行效益情况和建设进度情况统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@Service
public class BnsShOperationServiceImpl  extends ServiceImpl<BnsShOperationDao, BnsShOperation> implements BnsShOperationService {

//    @Autowired
    @Resource
    private BnsShOperationDao bnsShOperationDao;

    @Override
    public Boolean addorupdate(BnsShOperation entity) {
        Boolean a = false;
        if(entity.getScount() == null){
            entity.setScount(0.0);
        }
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        String bno = sdf.format(d);
        if(StringUtils.isEmpty(entity.getYear())){
            entity.setYear(bno);
        }
        try {
            if("00000000000000".equals(entity.getBno())){
                a = bnsShOperationDao.updateOperation(entity);
            }else{
                a = bnsShOperationDao.addOperation(entity);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return a;
    }

    @Override
    public boolean summary() {
        List<BnsShOperation> list1 = bnsShOperationDao.queryAddList();
        List<BnsShOperation> list = new ArrayList<>();
        int sum = 0;
        Date date = new Date();
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy");
        String bno1 = sdf1.format(date);
        for (int i = 0; i < list1.size(); i++) {
            if(list1.get(i).getAdcd() == null){
                list.add(list1.get(i));
                if(list1.get(i).getScount() == null){
                    list.get(sum).setScount(0.0);
                }
                if(list1.get(i).getYear() == null){
                    list.get(sum).setYear(bno1);
                }
                sum++;
            }
        }
        boolean a = bnsShOperationDao.addOperationList(list);
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String bno = sdf.format(d);
        boolean summary =  bnsShOperationDao.summary(bno);
        return summary;
    }

    @Override
    public List<BnsShOperation> getBenefitData(String adcd,String stm,String etm,String bno , Integer type,String year){
        List<BnsShOperation> list = new ArrayList<>();
        int level = AdcdUtil.getAdLevel(adcd);
        // 1-- 查询数据列表，， 2，查询单条数据用于新增，有数据择回显修改
        if(type == 1){
            list =  bnsShOperationDao.getBenefitData(adcd.substring(0,level), stm, etm, level, bno,type,year);
        } else if(type == 2){
            list =  bnsShOperationDao.getBenefitData(adcd, null, null, level, bno,type,year);
        }
        return list;
    }

    public void setCellValue(XSSFSheet sheet, XSSFCellStyle style, int rownum, int columnIndex, String value) {
        XSSFRow row1 = sheet.getRow(rownum);
        if (row1 == null) {
            row1 = sheet.createRow(rownum);
        }
        XSSFCell cell = row1.createCell(columnIndex);
        cell.setCellStyle(style);
        cell.setCellValue(value);
    }

    @Override
    public List<BnsShOperation> getCurrentTimeData(String adcd){
        return  bnsShOperationDao.getCurrentTimeData(adcd);
    }

    @Override
    public List<BnsShOperation> getBnoList(String adcd){
        int level = AdcdUtil.getAdLevel(adcd);
        List<BnsShOperation> list = new ArrayList<>();
        List<BnsShOperation> list1 = bnsShOperationDao.getBnoList(adcd.substring(0,level), level);
        Integer a = 0;
        for (int i = 0; i < list1.size(); i++) {
            if("00000000000000".equals(list1.get(i).getBno())){
                a++;
            }
        }
        if(a == 0){
            BnsShOperation bnsShOperation = new BnsShOperation();
            bnsShOperation.setBno("00000000000000");
            list.add(0,bnsShOperation);
            list.addAll(list1);
        }else{
            list.addAll(list1);
        }
        return list;
    }

    @Override
    public List<BnsShProject> getrojectData(String adcd,String stm,String etm,String bno,String year,Integer type){
        int level = AdcdUtil.getAdLevel(adcd);
        List<BnsShProject> list = new ArrayList<>();
        if(type == 1){
            list = bnsShOperationDao.getrojectData(adcd.substring(0,level), stm, etm, level, bno,year,type);
        } else if(type == 2){
            list = bnsShOperationDao.getrojectData(adcd, null, null, level, bno,year,type);
        }
        return list;
    }

    @Override
    public List<BnsShProject> getrojectList(String adcd){
        int level = AdcdUtil.getAdLevel(adcd);
        List<BnsShProject> list = new ArrayList<>();
        List<BnsShProject> list1 = bnsShOperationDao.getrojectList(adcd.substring(0,level), level);
        Integer a = 0;
        for (int i = 0; i < list1.size(); i++) {
            if("00000000000000".equals(list1.get(i).getBno())){
                a++;
            }
        }
        if(a == 0){
            BnsShProject bnsShProject = new BnsShProject();
            bnsShProject.setBno("00000000000000");
            list.add(0,bnsShProject);
            list.addAll(list1);
        }else{
            list.addAll(list1);
        }
        return list;
    }

    @Override
    public Boolean updateRojectBRoject(BnsShProject entity) {
        Boolean a = false;
        if(entity.getCsFunds1() == null){
            entity.setCsFunds1(0.0);
        }
        if(entity.getCsFunds2() == null){
            entity.setCsFunds2(0.0);
        }

        if(entity.getCsInvested() == null){
            entity.setCsInvested(0.0);
        }

        if(entity.getCsPaid() == null){
            entity.setCsPaid(0.0);
        }

        if(entity.getOpFunds1() == null){
            entity.setOpFunds1(0.0);
        }

        if(entity.getOpFunds2() == null){
            entity.setOpFunds2(0.0);
        }

        if(entity.getOpInvested() == null){
            entity.setOpInvested(0.0);
        }

        if(entity.getOpPaid() == null){
            entity.setOpPaid(0.0);
        }
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        String bno = sdf.format(d);
        if(StringUtils.isEmpty(entity.getYear())){
            entity.setYear(bno);
        }
        try {
            if("00000000000000".equals(entity.getBno())){
                a = bnsShOperationDao.updateProject(entity);
            }else{
                a = bnsShOperationDao.addProject(entity);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return a;
    }

    @Override
    public Boolean summaryRojectB() {
        List<BnsShProject> list1 = bnsShOperationDao.queryAddProList();
        List<BnsShProject> list = new ArrayList<>();
        int sum = 0;
        Date date = new Date();
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy");
        String bno1 = sdf1.format(date);
        for (int i = 0; i < list1.size(); i++) {
            if(list1.get(i).getAdcd() == null){
                list.add(list1.get(i));
                if(list1.get(i).getCsFunds1() == null){
                    list.get(sum).setCsFunds1(0.0);
                }
                if(list1.get(i).getCsFunds2() == null){
                    list.get(sum).setCsFunds2(0.0);
                }

                if(list1.get(i).getCsInvested() == null){
                    list.get(sum).setCsInvested(0.0);
                }

                if(list1.get(i).getCsPaid() == null){
                    list.get(sum).setCsPaid(0.0);
                }

                if(list1.get(i).getOpFunds1() == null){
                    list.get(sum).setOpFunds1(0.0);
                }

                if(list1.get(i).getOpFunds2() == null){
                    list.get(sum).setOpFunds2(0.0);
                }

                if(list1.get(i).getOpInvested() == null){
                    list.get(sum).setOpInvested(0.0);
                }

                if(list1.get(i).getOpPaid() == null){
                    list.get(sum).setOpPaid(0.0);
                }
                if(list1.get(i).getYear() == null){
                    list.get(sum).setYear(bno1);
                }
                sum++;
            }

        }
        boolean a = bnsShOperationDao.addProjectList(list);
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String bno = sdf.format(d);
        Boolean summary =  bnsShOperationDao.summaryRojectB(bno);
        return summary;
    }

    @Override
    public List<BnsShProject> getCurrentOneData(String adcd){
        return  bnsShOperationDao.getCurrentOneData(adcd);
    }

}

















