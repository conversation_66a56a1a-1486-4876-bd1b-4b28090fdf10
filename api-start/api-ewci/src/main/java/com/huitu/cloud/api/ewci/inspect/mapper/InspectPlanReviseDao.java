package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectPlanRevise;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 方案/预案修订
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectPlanReviseDao {

    /**
     * 判断当前政区当年是否添加过方案/预案修订
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectPlanRevise> getInspectPlanReviseList(@Param("adcd") String adcd, @Param("year") String year);

    /**
     * 添加方案/预案修订
     *
     * @param entity
     * @return
     */
    int insertInspectPlanRevise(BnsInspectPlanRevise entity);

    /**
     * 修改方案/预案修订
     *
     * @param entity
     * @return
     */
    int updateInspectPlanRevise(BnsInspectPlanRevise entity);

    /**
     * 删除 方案/预案修订
     *
     * @return
     */
    int deleteInspectPlanRevise(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

