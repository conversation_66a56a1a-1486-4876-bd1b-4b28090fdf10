package com.huitu.cloud.api.shyj.stwarnrule.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 雨量测站预警规则维护类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-03
 */
@ApiModel
public class RainWarnRule extends RainWarnruleSave {
    @ApiModelProperty(value = " 测站名称 ")
    private String stnm;

    @ApiModelProperty(value = "预警等级名称 ")
    private String warngradenm;

    @ApiModelProperty(value = "  政区名称 ")
    private String adnm;

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getWarngradenm() {
        return warngradenm;
    }

    public void setWarngradenm(String warngradenm) {
        this.warngradenm = warngradenm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
