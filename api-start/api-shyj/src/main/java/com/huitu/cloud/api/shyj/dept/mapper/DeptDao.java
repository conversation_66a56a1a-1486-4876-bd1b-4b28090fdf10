package com.huitu.cloud.api.shyj.dept.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.dept.entity.DeptB;
import com.huitu.cloud.api.shyj.dept.entity.RelevantDeptInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
/**
 * <p>
 * 部门信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public interface DeptDao extends BaseMapper<DeptB> {
    /**
     * 分页查询部门信息
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @param page  分页page必须放在第一位
     * @return
     */
    IPage<RelevantDeptInfo> getDeptListByPage(Page page, @Param("map") Map<String, Object> param);
    /**
     * 查询部门信息列表
     *
     * @param param map对象必须注解 否则在xml无法解析属性 xml解析属性使用 注解名称.属性 示例：map.stm
     * @return
     */
    List<RelevantDeptInfo> getDeptList(Map<String, Object> param);
    /**
     * 更新部门信息
     *
     * @param entity 部门实体类
     * @return
     */
    void updateDept(DeptB entity);
    /**
     * 删除部门信息
     *
     * @param adcd   政区编码
     * @param deptcd 部门编码
     * @return
     */
    void delDept(@Param("adcd") String adcd, @Param("deptcd") String deptcd);
    /**
     * 查询新增id
     *
     * @return
     */
    DeptB selectMaxId();
}
