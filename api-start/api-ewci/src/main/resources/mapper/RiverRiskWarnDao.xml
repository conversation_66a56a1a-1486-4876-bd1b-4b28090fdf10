<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.warn.mapper.RiverRiskWarnDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getLatestList" resultType="com.huitu.cloud.api.ewci.warn.entity.RiverRiskWarnInfo">
        SELECT WARNID, '2' [TYPE], XADCD, XADNM, ADCD, ADNM, STCD, STNM, RVNM, WTM, Z, WGRD, WIDX, EWIDX, [STATE], LGTD,
        LTTD FROM (
        SELECT (ROW_NUMBER() OVER(PARTITION BY A.STCD ORDER BY A.WTM DESC)) SNO, <PERSON><PERSON>, C.<PERSON>ADCD, C.ADNM XADNM,
        B.AD<PERSON>, B.<PERSON>, A.STCD, B.STNM, B.RVNM, A<PERSON>, A<PERSON>Z, A.WGRD, A.WIDX, A.EWIDX, A.[STATE], B.PLGTD LGTD,
        B.PLTTD LTTD FROM BSN_RIVER_RISKWARN_R A
        LEFT JOIN BSN_STBPRP_V B ON B.STCD = A.STCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = LEFT(B.ADCD, 6) + '000000000'
        WHERE LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(B.ADCD, 6) NOT IN ('220581')
        </if>
        AND A.WTM >= #{map.stm}
        <if test="map.etm != null">
            AND A.WTM &lt; #{map.etm}
        </if>
        <if test="map.stadtps != null and map.stadtps.size() > 0">
            <foreach collection="map.stadtps" item="stadtp" separator=", " open="AND B.STADTP IN (" close=")">
                #{stadtp}
            </foreach>
        </if>
        ) T WHERE SNO = 1
        ORDER BY WGRD ASC, WTM DESC
    </select>
</mapper>