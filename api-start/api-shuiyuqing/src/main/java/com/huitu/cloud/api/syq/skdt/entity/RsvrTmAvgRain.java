package com.huitu.cloud.api.syq.skdt.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel
public class RsvrTmAvgRain implements Serializable {
    @ApiModelProperty(value = "水库工程编码")
    private String resCode;
    @ApiModelProperty(value = "库区平均降雨")
    private String avgRain;
    @ApiModelProperty(value = "时段")
    private Date tm;

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getAvgRain() {
        return avgRain;
    }

    public void setAvgRain(String avgRain) {
        this.avgRain = avgRain;
    }

    public Date getTm() {
        return tm;
    }

    public void setTm(Date tm) {
        this.tm = tm;
    }
}
