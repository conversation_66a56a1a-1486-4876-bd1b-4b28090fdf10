package com.huitu.cloud.api.ewci.person.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.person.entity.*;
import com.huitu.cloud.api.ewci.person.exception.DikePersonImportException;
import com.huitu.cloud.api.ewci.person.service.DikePersonService;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * 堤防责任人管理服务
 *
 * <AUTHOR>
 */
@Api(tags = "堤防责任人管理")
@RestController
@RequestMapping("/api/ewci/person/dike")
public class DikePersonResource extends AbstractApiResource implements ApiResource {

    private static final Logger logger = LoggerFactory.getLogger(DikePersonResource.class);

    @Override
    public String getUuid() {
        return "5e5fccc4-35e4-8b57-f6df-ae205bd43378";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final DikePersonService baseService;

    public DikePersonResource(DikePersonService baseService) {
        this.baseService = baseService;
    }

    @ApiOperation(value = "分页获取堤防责任人信息列表", notes = "作者：曹宝金")
    @PostMapping("select-page-list")
    public ResponseEntity<SuccessResponse<IPage<DikePerson>>> getPageList(@Validated @RequestBody DikePersonQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(query)));
    }

    @ApiOperation(value = "堤防责任人汇总统计", notes = "作者：赵英捷")
    @PostMapping("select-dike-per-sum-list")
    public ResponseEntity<SuccessResponse<List<DikeSummaryVo>>> getDikePersonSummaryList(@RequestBody DikeSummaryQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getDikePersonSummaryList(query)));
    }

    @ApiOperation(value = "导入堤防责任人信息", notes = "作者：曹宝金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "xadcd", value = "县级行政区划代码", required = true, dataType = "String"),
    })
    @PostMapping(value = "import", headers = "content-type=multipart/form-data")
    public ResponseEntity<SuccessResponse<ImportResult<BnsDikePersonTmp>>> dataImport(@RequestParam String xadcd, @ApiParam(value = "file", required = true) MultipartFile data) throws Exception {
        if (AdcdUtil.getXzAdLevel(xadcd) != 3) {
            throw new RuntimeException("参数[县级行政区划代码]无效");
        }
        try {
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.dataImport(xadcd, data.getInputStream())));
        } catch (DikePersonImportException ex) {
            return ResponseEntity.ok(new SuccessResponse<>(this, "Error", new ImportResult<>(0, ex.getMessage(), ex.getErrorData())));
        }
    }

    @ApiOperation(value = "导出堤防责任人信息", notes = "作者：曹宝金")
    @PostMapping(value = "export")
    public void dataExport(@Validated @RequestBody DikePersonQuery query, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("堤防责任人信息", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.dataExport(query, response.getOutputStream());
        } catch (Exception ex) {
            logger.error("堤防责任人信息导出失败，参数：{}", JSON.toJSONString(query));
            logger.error(ex.getMessage(), ex);
        }
    }

    @ApiOperation(value = "获取堤防责任人信息列表（仅包含姓名和手机号码）", notes = "作者：曹宝金")
    @PostMapping("select-simple-list")
    public ResponseEntity<SuccessResponse<List<PersonInfo>>> getSimplePersonList(@Validated @RequestBody PersonQuery query) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSimplePersonList(query)));
    }

    @ApiOperation(value = "导出堤防责任人信息（全省）", notes = "作者：曹宝金")
    @PostMapping(value = "export-all")
    public void dataExportAll(HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode("堤防责任人信息（全省）", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            baseService.dataExport(response.getOutputStream());
        } catch (Exception ex) {
            logger.error("堤防责任人信息（全省）导出失败", ex);
        }
    }

    @ApiOperation(value = "编辑堤防责任人", notes = "zyj")
    @PostMapping(value = "dike-person-edit")
    public ResponseEntity<SuccessResponse<Integer>> dikePersonEdit(@RequestBody DikePerson entity) throws Exception {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.dikePersonEdit(entity)));
    }
}
