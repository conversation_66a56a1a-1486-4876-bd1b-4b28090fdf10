package com.huitu.cloud.api.ewci.person.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.person.entity.FsdaPersonBVo;
import com.huitu.cloud.entity.PageBean;

/**
 * 蓄滞洪区责任人
 *
 * <AUTHOR>
 */
public interface FsdaPersonService {

    /**
     * 分页查询蓄滞洪区责任人
     *
     * @param page
     * @return
     */
    IPage<FsdaPersonBVo> getPageList(PageBean page);

    /**
     * 编辑 蓄滞洪区责任人
     *
     * @param entity
     * @return
     */
    int fsdaPersonEdit(FsdaPersonBVo entity);

}
