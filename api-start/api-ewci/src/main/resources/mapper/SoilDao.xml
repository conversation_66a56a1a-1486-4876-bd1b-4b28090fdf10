<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.soil.mapper.SoilDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getSoilList" resultType="com.huitu.cloud.api.ewci.soil.entity.SoilListVo">
        select A.Code,A.Address STLC,A.Name stnm, B.PLGTD lgtd,B.PLTTD lttd,
        CASE WHEN C.ADNM != D.ADNM AND D.ADNM != E.ADNM THEN C.ADNM + D.ADNM + E.ADNM
        WHEN C.ADNM != D.ADNM AND D.ADNM = E.ADNM THEN C.ADNM + D.ADNM
        WHEN C.ADNM = D.ADNM THEN C.ADNM
        ELSE NULL
        END ADNM from Devices_ZB A
        LEFT JOIN BSN_STADTP_B B ON A.Address = B.STCD
        LEFT JOIN MDT_ADCDINFO_B C ON LEFT(B.ADCD,6)+'000000000' = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B D ON LEFT(B.ADCD,9)+'000000' = D.ADCD
        LEFT JOIN MDT_ADCDINFO_B E ON LEFT(B.ADCD,12)+'000' = E.ADCD
        where
        ( A.Code in
        <foreach collection="map.list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR A.Code IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>
        )
        <if test="map.adcd != null and map.adcd != ''">
            and left(A.ADCD,#{map.level})=left(#{map.adcd},#{map.level})
        </if>
        <if test="map.level !=null and map.level =='4'.toString()">
            and left(A.ADCD,6) != '220581' and left(A.ADCD,6) != '220381'
        </if>
    </select>
    <select id="getIsoJobs" resultType="com.huitu.cloud.api.ewci.soil.entity.IsoJobs">
        select *
        from IsoJobs
        where  jobId = #{jobId}
    </select>

    <select id="getIsoJobsList" resultType="com.huitu.cloud.api.ewci.soil.entity.IsoJobs">
        SELECT * FROM IsoJobs WHERE YEAR(createTime) = #{year} AND pdfPath IS NOT NULL
        ORDER BY createTime DESC
    </select>

    <select id="getSoilRealTimeList" resultType="com.huitu.cloud.api.ewci.soil.entity.SoilRealTimeVo">
        SELECT A.ID DEVICEID, A.CODE STCD, A.NAME STNM, A.Longitude LGTD, A.Latitude LTTD, A.Address STLC,
        CASE WHEN C.ADNM != D.ADNM THEN C.ADNM + D.ADNM
             WHEN C.ADNM = D.ADNM THEN C.ADNM
             ELSE NULL
        END ADNM, N.DEPTH, F.TM, F.WATER, CAST(F.WATER/(N.FieldCapacity*N.UnitWeight) AS DECIMAL(10, 1)) AS SWC
        FROM DEVICES A
        LEFT JOIN NODES N ON A.Id= N.DeviceId
        LEFT JOIN BSN_STADTP_B B ON A.Address = B.STCD
        LEFT JOIN MDT_ADCDINFO_B C ON LEFT(B.ADCD,6)+'000000000' = C.ADCD
        LEFT JOIN MDT_ADCDINFO_B D ON LEFT(B.ADCD,9)+'000000' = D.ADCD
        LEFT JOIN (
        SELECT ROW_NUMBER() OVER(PARTITION BY R.CODE,R.DEPTH ORDER BY R.CreatedAt DESC) SNO,R.CODE CODE,R.DEPTH DEPTH
        ,R.CreatedAt TM,R.WATER WATER FROM DEPTHDATA R WHERE R.CreatedAt &lt;= #{map.etm} AND R.CreatedAt >= #{map.stm}
        ) F ON A.CODE = F.CODE AND N.Depth = F.Depth AND F.SNO = 1
        WHERE LEFT(B.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.stnm != null and map.stnm != ''">
            AND CHARINDEX(#{map.stnm}, A.NAME) >0
        </if>
        ORDER BY SWC DESC
    </select>
    <select id="getSoilDisposi" resultType="com.huitu.cloud.api.ewci.soil.entity.SoilDisposi">
        SELECT  TOP 1 *
        FROM BNS_SOIL_DISPOSI
        WHERE #{etm} BETWEEN
                  CAST(CONCAT(YEAR(#{etm}), '-', SUBSTRING(STAR_TM, 1, 2), '-', SUBSTRING(STAR_TM, 3, 2)) as date)
                  AND
                  CAST(CONCAT(YEAR(#{etm}), '-', SUBSTRING(END_TM, 1, 2), '-', SUBSTRING(END_TM, 3, 2)) as date)
        ORDER BY TS DESC
    </select>
    <select id="getSoilDisposiList" resultType="com.huitu.cloud.api.ewci.soil.entity.SoilDisposi">
        SELECT * FROM BNS_SOIL_DISPOSI
    </select>
    <select id="getAdcdList" resultType="com.huitu.cloud.api.ewci.soil.entity.AreaResultVo">
        SELECT ADCD,ADNM,PADCD FROM MDT_ADCDINFO_B WHERE
        (ADCD IN
        <foreach collection="map.list" item="id" index="index" open="(" close=")">
            <if test="index != 0">
                <choose>
                    <when test="(index % 100) == 99">) OR ADCD IN (</when>
                    <otherwise>,</otherwise>
                </choose>
            </if>
            #{id}
        </foreach>)
        <if test="map.adcd != null and map.adcd != ''">
            and left(ADCD,#{map.level})=left(#{map.adcd},#{map.level})
        </if>
        <if test="map.level !=null and map.level =='4'.toString()">
            and left(ADCD,6) != '220581' and left(ADCD,6) != '220381'
        </if>
    </select>

    <select id="getAdcdListByBscd" resultType="com.huitu.cloud.api.ewci.soil.entity.AreaResultVo">
        WITH SUBQRY(BAS_CODE) AS (
        SELECT BAS_CODE
        FROM BSN_BAS_B
        <where>
            <if test="bscd != null and bscd !=''">
                BAS_CODE = #{bscd}
            </if>
        </where>
        UNION ALL
        SELECT b.BAS_CODE
        FROM BSN_BAS_B b
        JOIN SUBQRY s ON b.PBAS_CODE = s.BAS_CODE
        )
        SELECT  c.ADCD,c.ADNM,c.PADCD
        FROM (
        SELECT  st.STCD
        FROM SUBQRY sq
        INNER JOIN BSN_BAS_ST st ON sq.BAS_CODE = st.BAS_CODE
        GROUP BY st.STCD
        ) a
        INNER JOIN BSN_STADTP_B b ON a.STCD = b.STCD
        INNER JOIN MDT_ADCDINFO_B c ON LEFT(b.ADCD, 6) + '000000000' = c.ADCD
        OR LEFT(b.ADCD, 4) + '00000000000' = c.ADCD
        OR LEFT(b.ADCD,2) + '0000000000000' = c.ADCD
        GROUP BY  c.ADCD,c.ADNM,c.PADCD
    </select>


    <select id="getSoilTabsBaseInfo" resultType="com.huitu.cloud.api.ewci.soil.entity.SoilTabsBaseInfoVo">
        SELECT A.CODE, A.UnitWeight, A.FieldCapacity, B.Name GROUPNAME, A.Phone
        FROM Devices A
                 LEFT JOIN Groups B ON A.GroupId = B.ID
        WHERE A.Address = #{stcd}
    </select>
    <select id="getAreaList" resultType="com.huitu.cloud.api.ewci.soil.entity.SoilArea">
        SELECT B.ADCD, B.ADNM, B.PADCD, A.Upland
        FROM Area A
                 LEFT JOIN MDT_ADCDINFO_B B ON A.CODE + '000000000' = B.ADCD
        WHERE 1=1
        <if test="map.adcd != null and map.adcd != ''">
            and left(B.ADCD,#{map.level})=left(#{map.adcd},#{map.level})
        </if>
        <if test="map.level !=null and map.level =='4'.toString()">
            and left(B.ADCD,6) != '220581' and left(B.ADCD,6) != '220381'
        </if>
        ORDER BY B.ADCD
    </select>
    <select id="getSoilTabsNodesList" resultType="com.huitu.cloud.api.ewci.soil.entity.SoilTabsNodesVo">
        SELECT Depth, UnitWeight,FieldCapacity FROM Nodes
        WHERE DeviceId = #{deviceId}
        ORDER BY Depth ASC
    </select>
    <select id="getStcdList" resultType="java.lang.String">
        WITH SUBQRY(BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE) AS (
        　　SELECT BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE FROM BSN_BAS_B
        <if test="bscd != null and bscd !=''">
            WHERE BAS_CODE = #{bscd}
        </if>
        <if test="bscd == null or bscd ==''">
            WHERE PBAS_CODE = ''
        </if>
        　　UNION ALL
        　　SELECT BSN_BAS_B.BAS_CODE,BSN_BAS_B.BAS_NAME,BSN_BAS_B.BAS_LEVEL,BSN_BAS_B.PBAS_CODE FROM BSN_BAS_B,SUBQRY
        WHERE BSN_BAS_B.PBAS_CODE = SUBQRY.BAS_CODE
        )
        SELECT DISTINCT B.STCD FROM SUBQRY A INNER JOIN BSN_BAS_ST B
        ON A.BAS_CODE = B.BAS_CODE
    </select>
</mapper>
