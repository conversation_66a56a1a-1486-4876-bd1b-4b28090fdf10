package com.huitu.cloud.api.shyj.stwarnrule.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 测站预警规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-03
 */
@TableName("STWARNRULE_B")
@ApiModel(value="StwarnruleB2对象", description="测站预警规则表")
public class Stwarnrule implements Serializable {
    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = " 规则ID ")
    @TableId(value = "STRULEID", type = IdType.NONE)
    private Double struleid;

    @ApiModelProperty(value = " 测站代码 ")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "测站超警指标类型标识 R:雨量 Z:水位 ")
    @TableField("STINDEX")
    private String stindex;

    @ApiModelProperty(value = "预警类型ID")
    @TableField("WARNTYPEID")
    private Double warntypeid;

    @ApiModelProperty(value = " 预警等级ID ")
    @TableField("WARNGRADEID")
    private Double warngradeid;

    @ApiModelProperty(value = "指标阈值 ")
    @TableField("STTHRESHOLD")
    private Double stthreshold;

    @ApiModelProperty(value = "  指标单位 ")
    @TableField("STINDEXUNIT")
    private String stindexunit;

    @ApiModelProperty(value = "  阈值历时")
    @TableField("STDT")
    private Double stdt;

    @ApiModelProperty(value = "  指标生效时间")
    @TableField("STRULEVALIDTIME")
    private Date strulevalidtime;

    @ApiModelProperty(value = " 指标描述 ")
    @TableField("STINDEXNM")
    private String stindexnm;

    @ApiModelProperty(value = "  备注 ")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "  政区名称 ")
    private String adnm;

    public Double getStruleid() {
        return struleid;
    }

    public void setStruleid(Double struleid) {
        this.struleid = struleid;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStindex() {
        return stindex;
    }

    public void setStindex(String stindex) {
        this.stindex = stindex;
    }

    public Double getWarntypeid() {
        return warntypeid;
    }

    public void setWarntypeid(Double warntypeid) {
        this.warntypeid = warntypeid;
    }

    public Double getWarngradeid() {
        return warngradeid;
    }

    public void setWarngradeid(Double warngradeid) {
        this.warngradeid = warngradeid;
    }

    public Double getStthreshold() {
        return stthreshold;
    }

    public void setStthreshold(Double stthreshold) {
        this.stthreshold = stthreshold;
    }

    public String getStindexunit() {
        return stindexunit;
    }

    public void setStindexunit(String stindexunit) {
        this.stindexunit = stindexunit;
    }

    public Double getStdt() {
        return stdt;
    }

    public void setStdt(Double stdt) {
        this.stdt = stdt;
    }

    public Date getStrulevalidtime() {
        return strulevalidtime;
    }

    public void setStrulevalidtime(Date strulevalidtime) {
        this.strulevalidtime = strulevalidtime;
    }

    public String getStindexnm() {
        return stindexnm;
    }

    public void setStindexnm(String stindexnm) {
        this.stindexnm = stindexnm;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }
}
