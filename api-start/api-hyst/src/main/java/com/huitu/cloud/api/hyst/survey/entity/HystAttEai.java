package com.huitu.cloud.api.hyst.survey.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 小水电下泄流量考核信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-30
 */
@TableName("HYST_ATT_EAI")
@ApiModel(value = "HystAttEai对象", description = "小水电下泄流量考核信息表")
public class HystAttEai extends Model<HystAttEai> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水电站统计代码")
    @TableId(value = "HYST_CODE")
    private String hystCode;

    @ApiModelProperty(value = "监测设备在线率是否合格，1为合格、0为不合格")
    @TableField("ECO_ONLINE")
    private String ecoOnline;

    @ApiModelProperty(value = "下泄流量达标率是否合格，1为合格、0为不合格")
    @TableField("ECO_FLOW")
    private String ecoFlow;

    @ApiModelProperty(value = "考核周期，按天（0）、月（1）、年（2），三站考核周期")
    @TableField("ASS_PER")
    private String assPer;

    @ApiModelProperty(value = "考核开始日期")
    @TableField("ASS_START")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assStart;

    @ApiModelProperty(value = "考核结束日期")
    @TableField("ASS_END")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assEnd;

    @ApiModelProperty(value = "记录时间")
    @TableField("REC_TIME")
    private LocalDateTime recTime;

    public String getHystCode() {
        return hystCode;
    }

    public void setHystCode(String hystCode) {
        this.hystCode = hystCode;
    }

    public String getEcoOnline() {
        return ecoOnline;
    }

    public void setEcoOnline(String ecoOnline) {
        this.ecoOnline = ecoOnline;
    }

    public String getEcoFlow() {
        return ecoFlow;
    }

    public void setEcoFlow(String ecoFlow) {
        this.ecoFlow = ecoFlow;
    }

    public String getAssPer() {
        return assPer;
    }

    public void setAssPer(String assPer) {
        this.assPer = assPer;
    }

    public Date getAssStart() {
        return assStart;
    }

    public void setAssStart(Date assStart) {
        this.assStart = assStart;
    }

    public Date getAssEnd() {
        return assEnd;
    }

    public void setAssEnd(Date assEnd) {
        this.assEnd = assEnd;
    }

    public LocalDateTime getRecTime() {
        return recTime;
    }

    public void setRecTime(LocalDateTime recTime) {
        this.recTime = recTime;
    }
}