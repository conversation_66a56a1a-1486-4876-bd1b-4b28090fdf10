package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 异常数据修改日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-09
 */
@TableName("BSN_ANMRECORDLOG_R")
@ApiModel(value="BsnAnmrecordlogR对象", description="异常数据修改日志表")
public class BsnAnmrecordlogr extends Model<BsnAnmrecordlogr> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "异常表标识字段")
    @TableId(value = "REID", type = IdType.NONE)
    private String reid;

    @ApiModelProperty(value = "修改时间")
    @TableField("TM")
    private Date tm;

    @ApiModelProperty(value = "原记录json")
    @TableField("ODATAJS")
    private String odatajs;

    @ApiModelProperty(value = "新记录json")
    @TableField("NDATAJS")
    private String ndatajs;

    @ApiModelProperty(value = "备注")
    @TableField("NT")
    private String nt;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private Date moditime;

    @ApiModelProperty(value = "操作人")
    @TableField("DOER")
    private String doer;


    public String getReid() {
        return reid;
    }

    public void setReid(String reid) {
        this.reid = reid;
    }


    public Date getTm() {
        return tm;
    }

    public void setTm(Date tm) {
        this.tm = tm;
    }

    public String getOdatajs() {
        return odatajs;
    }

    public void setOdatajs(String odatajs) {
        this.odatajs = odatajs;
    }

    public String getNdatajs() {
        return ndatajs;
    }

    public void setNdatajs(String ndatajs) {
        this.ndatajs = ndatajs;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }

    public Date getModitime() {
        return moditime;
    }

    public void setModitime(Date moditime) {
        this.moditime = moditime;
    }

    public String getDoer() {
        return doer;
    }

    public void setDoer(String doer) {
        this.doer = doer;
    }

    @Override
    protected Serializable pkVal() {
        return this.reid;
    }

    @Override
    public String toString() {
        return "BsnAnmrecordlogR{" +
        "reid=" + reid +
        ", tm=" + tm +
        ", odatajs=" + odatajs +
        ", ndatajs=" + ndatajs +
        ", nt=" + nt +
        ", moditime=" + moditime +
        ", doer=" + doer +
        "}";
    }
}
