package com.huitu.cloud.api.syq.rain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value="雨情摘要信息")
public class RainSummary implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "同期降雨")
    private double accp;

    @ApiModelProperty(value = "去年同期降雨")
    private double lyAccp;

    @ApiModelProperty(value = "多年同期降雨")
    private double myAccp;

    @ApiModelProperty(value = "汛期同期降雨")
    private double fldAccp;

    @ApiModelProperty(value = "去年汛期同期降雨")
    private double lyFldAccp;

    @ApiModelProperty(value = "多年汛期同期降雨")
    private double myFldAccp;

    @ApiModelProperty(value = "较去年同期")
    private Double lyRate;

    @ApiModelProperty(value = "较多年同期")
    private Double myRate;

    @ApiModelProperty(value = "汛期较去年同期")
    private Double lyFldRate;

    @ApiModelProperty(value = "汛期较多年同期")
    private Double myFldRate;


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public double getAccp() {
        return accp;
    }

    public void setAccp(double accp) {
        this.accp = accp;
    }

    public double getLyAccp() {
        return lyAccp;
    }

    public void setLyAccp(double lyAccp) {
        this.lyAccp = lyAccp;
    }

    public double getMyAccp() {
        return myAccp;
    }

    public void setMyAccp(double myAccp) {
        this.myAccp = myAccp;
    }

    public double getFldAccp() {
        return fldAccp;
    }

    public void setFldAccp(double fldAccp) {
        this.fldAccp = fldAccp;
    }

    public double getLyFldAccp() {
        return lyFldAccp;
    }

    public void setLyFldAccp(double lyFldAccp) {
        this.lyFldAccp = lyFldAccp;
    }

    public double getMyFldAccp() {
        return myFldAccp;
    }

    public void setMyFldAccp(double myFldAccp) {
        this.myFldAccp = myFldAccp;
    }

    public Double getLyRate() {
        return lyRate;
    }

    public void setLyRate(Double lyRate) {
        this.lyRate = lyRate;
    }

    public Double getMyRate() {
        return myRate;
    }

    public void setMyRate(Double myRate) {
        this.myRate = myRate;
    }

    public Double getLyFldRate() {
        return lyFldRate;
    }

    public void setLyFldRate(Double lyFldRate) {
        this.lyFldRate = lyFldRate;
    }

    public Double getMyFldRate() {
        return myFldRate;
    }

    public void setMyFldRate(Double myFldRate) {
        this.myFldRate = myFldRate;
    }
}
