package com.huitu.cloud.api.ew.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ew.entity.base.EwWarningGrade;
import com.huitu.cloud.api.ew.entity.base.EwWarningStatus;
import com.huitu.cloud.api.ew.entity.ext.EwMessageReceiver;
import com.huitu.cloud.api.ew.entity.ext.EwWarningSending;
import com.huitu.cloud.api.ew.entity.request.*;
import com.huitu.cloud.api.ew.entity.response.*;
import com.huitu.cloud.api.ew.exception.WarningException;
import com.huitu.cloud.api.ew.service.EwCallFeedbackService;
import com.huitu.cloud.api.ew.service.EwWarningService;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import com.huitu.cloud.api.usif.util.LoginUtils;
import com.huitu.cloud.util.AdcdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 山洪监测预警服务
 *
 * <AUTHOR>
 */
@Api(tags = "山洪监测预警服务")
@RestController
@RequestMapping("/api/ew/warning")
public class EwWarningResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "778b9d8e-7507-4229-888d-b1063c9711d1";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    private final LoginUtils loginUtils;
    private final EwWarningService baseService;
    private final EwCallFeedbackService feedbackService;

    public EwWarningResource(LoginUtils loginUtils, EwWarningService baseService, EwCallFeedbackService feedbackService) {
        this.loginUtils = loginUtils;
        this.baseService = baseService;
        this.feedbackService = feedbackService;
    }

    @ApiOperation(value = "获取预警等级列表", notes = "作者：曹宝金")
    @GetMapping("grade-list")
    public ResponseEntity<SuccessResponse<List<EwWarningGrade>>> getGradeList() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getGradeList()));
    }

    @ApiOperation(value = "获取预警状态列表", notes = "作者：曹宝金")
    @GetMapping("status-list")
    public ResponseEntity<SuccessResponse<List<EwWarningStatus>>> getStatusList() {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getStatusList()));
    }

    @ApiOperation(value = "获取完整政区名称", notes = "作者：曹宝金")
    @PostMapping("full-adnm")
    public ResponseEntity<SuccessResponse<String>> getFullAdnm(@RequestBody @Validated EwFullAdnmRequest request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getFullAdnm(request)));
    }

    @ApiOperation(value = "获取山洪预警分页列表", notes = "作者：曹宝金")
    @PostMapping("page-list")
    public ResponseEntity<SuccessResponse<IPage<EwWarningRecordResponse>>> getPageList(@RequestBody @Validated EwWarningRecordRequest request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getPageList(request)));
    }

    @ApiOperation(value = "获取最新的山洪预警分页列表", notes = "作者：jiangjy")
    @PostMapping("latest-page-list")
    public ResponseEntity<SuccessResponse<IPage<EwWarningRecordResponse>>> getLatestPageList(@RequestBody @Validated EwWarningRecordRequest request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getLatestPageList(request)));
    }

    @ApiOperation(value = "获取山洪预警记录", notes = "作者：曹宝金")
    @GetMapping("record")
    public ResponseEntity<SuccessResponse<EwWarningRecordResponse>> getRecord(String warnId, Integer warnGradeId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getRecord(warnId, warnGradeId)));
    }

    @ApiOperation(value = "创建突发预警", notes = "作者：曹宝金")
    @PostMapping("create")
    public ResponseEntity<SuccessResponse<Integer>> create(@RequestBody @Validated EwWarningCreateRequest request) {
        if (AdcdUtil.getAdLevel(request.getAdcd()) < 12) {
            throw new WarningException("参数[行政区划代码]的级别应为村级，如：社区、行政村、自然村（屯、社、队、组）等");
        }
        try {
            return ResponseEntity.ok(
                    new SuccessResponse<>(this, "OK", baseService.create(request, loginUtils.getCurrentLoginUser())));
        } catch (Exception ex) {
            throw new WarningException("创建突发预警失败", ex);
        }
    }

    @ApiOperation(value = "判断平台是否启用了山洪监测预警", notes = "作者：曹宝金")
    @GetMapping("is-enabled")
    public ResponseEntity<SuccessResponse<Boolean>> isEnabled() {
        boolean enabled = false;
        try {
            String platformId = getPlatformId();
            if (StringUtils.isNotBlank(platformId)) {
                enabled = baseService.isEnabled(platformId);
            }
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", enabled));
    }

    @ApiOperation(value = "关闭（解除）预警", notes = "作者：曹宝金")
    @PostMapping("close")
    public ResponseEntity<SuccessResponse<Integer>> close(@RequestBody @Validated EwWarningCloseRequest request) {
        try {
            return ResponseEntity.ok(
                    new SuccessResponse<>(this, "OK", baseService.close(request, loginUtils.getCurrentLoginUser())));
        } catch (WarningException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("关闭（解除）预警失败", ex);
        }
    }

    @ApiOperation(value = "获取山洪预警过程列表", notes = "作者：曹宝金")
    @GetMapping("process-list")
    public ResponseEntity<SuccessResponse<List<EwWarningProcessResponse>>> getProcessList(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getProcessList(warnId)));
    }

    @ApiOperation(value = "获取山洪预警流程列表", notes = "作者：曹宝金")
    @GetMapping("flow-list")
    public ResponseEntity<SuccessResponse<List<EwWarningFlowResponse>>> getFlowList(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getFlowList(warnId)));
    }

    @ApiOperation(value = "获取山洪预警消息列表", notes = "作者：曹宝金")
    @GetMapping("message-list")
    public ResponseEntity<SuccessResponse<List<EwWarningMessageResponse<?>>>> getMessageList(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getMessageList(warnId)));
    }

    @ApiOperation(value = "获取山洪预警指标快照列表", notes = "作者：曹宝金")
    @GetMapping("snapshot-index-list")
    public ResponseEntity<SuccessResponse<List<EwSnapshotIndexResponse>>> getSnapshotIndexList(String warnId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSnapshotIndexList(warnId)));
    }

    @ApiOperation(value = "获取山洪预警指标快照", notes = "作者：曹宝金")
    @GetMapping("snapshot-index")
    public ResponseEntity<SuccessResponse<EwSnapshotIndexResponse>> getSnapshotIndex(String warnId, Integer warnGradeId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");
        Assert.notNull(warnGradeId, "参数[预警等级ID]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSnapshotIndex(warnId, warnGradeId)));
    }

    @ApiOperation(value = "获取山洪预警监测数据快照", notes = "作者：曹宝金")
    @GetMapping("snapshot-monitor")
    public ResponseEntity<SuccessResponse<EwSnapshotMonitorResponse>> getSnapshotMonitor(String warnId, Integer warnGradeId) {
        Assert.hasText(warnId, "参数[预警ID]不能为空");
        Assert.notNull(warnGradeId, "参数[预警等级ID]不能为空");

        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSnapshotMonitor(warnId, warnGradeId)));
    }

    @ApiOperation(value = "获取预警发送记录分页列表", notes = "作者：曹宝金")
    @PostMapping("sending-page-list")
    public ResponseEntity<SuccessResponse<IPage<EwWarningSending>>> getSendingPageList(
            @RequestBody @Validated EwWarningSendingRequest<EwWarningSending> request) {
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getSendingPageList(request)));
    }

    @ApiOperation(value = "获取预警消息接收人列表", notes = "作者：曹宝金")
    @GetMapping("message-receiver-list")
    public ResponseEntity<SuccessResponse<List<EwMessageReceiver>>> getMessageReceiverList(String adcd) {
        UserInfos user = loginUtils.getCurrentLoginUser();
        return ResponseEntity.ok(
                new SuccessResponse<>(this, "OK", baseService.getMessageReceiverList(adcd, user.getAdcd())));
    }

    @ApiOperation(value = "获取待更正的预警记录列表", notes = "作者：曹宝金")
    @GetMapping("correct-list")
    public ResponseEntity<SuccessResponse<List<EwWarningCorrectResponse>>> getCorrectList(String warnId) {
        if (StringUtils.isBlank(warnId)) {
            throw new WarningException("参数[预警ID]不能为空");
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.getCorrectList(warnId)));
    }

    @ApiOperation(value = "更正预警", notes = "作者：曹宝金")
    @PostMapping("correct")
    public ResponseEntity<SuccessResponse<Integer>> correct(@RequestBody @Validated EwWarningCorrectRequest request) {
        try {
            UserInfos user = loginUtils.getCurrentLoginUser();
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", baseService.correct(request, user)));
        } catch (WarningException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new WarningException("预警更正失败，请稍后重试", ex);
        }
    }

    @ApiOperation(value = "人工通知", notes = "作者：曹宝金")
    @PostMapping("manual-confirm")
    public ResponseEntity<SuccessResponse<Integer>> manualConfirm(@RequestBody @Validated EwManualConfirmRequest request) {
        try {
            request.setConfirmBy(loginUtils.getCurrentLoginUser().getLoginnm());
            return ResponseEntity.ok(new SuccessResponse<>(this, "OK", feedbackService.manualConfirm(request)));
        } catch (Exception ex) {
            throw new WarningException("人工通知失败，请稍后重试", ex);
        }
    }

    @ApiOperation(value = "获取等待人工通知的预警接收人列表", notes = "作者：曹宝金")
    @GetMapping("not-notice-receiver-list")
    public ResponseEntity<SuccessResponse<List<EwNoticeReceiverResponse>>> getNotNoticeReceiverList() {
        List<EwNoticeReceiverResponse> result = new ArrayList<>();
        try {
            String platformId = getPlatformId();
            if (StringUtils.isNotBlank(platformId)) {
                result = feedbackService.getNotNoticeReceiverList(platformId);
            }
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", result));
    }

    @ApiOperation(value = "获取已人工通知的预警接收人列表", notes = "作者：曹宝金")
    @PostMapping("notified-receiver-list")
    public ResponseEntity<SuccessResponse<List<EwNoticeReceiverResponse>>> getNotifiedReceiverList(@RequestBody @Validated EwNoticeReceiverRequest request) {
        List<EwNoticeReceiverResponse> result = new ArrayList<>();
        try {
            String platformId = getPlatformId();
            if (StringUtils.isNotBlank(platformId)) {
                request.setPlatformId(platformId);
                result = feedbackService.getNotifiedReceiverList(request);
            }
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", result));
    }

    @ApiOperation(value = "获取预警摘要列表", notes = "作者：曹宝金")
    @PostMapping("digest-list")
    public ResponseEntity<SuccessResponse<List<EwWarningDigestResponse>>> getDigestList(@RequestBody List<String> warnIds) {
        List<EwWarningDigestResponse> result = new ArrayList<>();
        try {
            if (!CollectionUtils.isEmpty(warnIds)) {
                result = baseService.getDigestList(warnIds);
            }
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", result));
    }

    @ApiOperation(value = "获取山洪监测预警叫应失败统计列表", notes = "作者：曹宝金")
    @PostMapping("call-fail-stats-list")
    public ResponseEntity<SuccessResponse<List<EwCallStatisticsResponse>>> getCallFailStatsList(@RequestBody @Validated EwCallStatisticsRequest request) {
        List<EwCallStatisticsResponse> result = new ArrayList<>();
        try {
            result = feedbackService.getCallFailStatsList(request);
        } catch (Exception ignored) {
        }
        return ResponseEntity.ok(new SuccessResponse<>(this, "OK", result));
    }

    private String getPlatformId() {
        UserInfos info = loginUtils.getCurrentLoginUser();
        if (StringUtils.isBlank(info.getAdcd()) || AdcdUtil.getAdLevel(info.getAdcd()) <= 2) {
            // 未设置政区或省本级
            return null;
        }
        return info.getAdcd().substring(0, 6);
    }
}
