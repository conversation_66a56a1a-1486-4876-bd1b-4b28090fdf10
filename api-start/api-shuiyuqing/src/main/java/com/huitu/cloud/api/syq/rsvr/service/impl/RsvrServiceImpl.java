package com.huitu.cloud.api.syq.rsvr.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.syq.constants.SyqConstants;
import com.huitu.cloud.api.syq.rain.entity.BsnBasStBTo;
import com.huitu.cloud.api.syq.rain.entity.Rain;
import com.huitu.cloud.api.syq.rain.mapper.RainDao;
import com.huitu.cloud.api.syq.rain.service.RainService;
import com.huitu.cloud.api.syq.rsvr.entity.*;
import com.huitu.cloud.api.syq.rsvr.mapper.RsvrDao;
import com.huitu.cloud.api.syq.rsvr.service.RsvrService;
import com.huitu.cloud.api.syq.video.entity.BsnVdstCodeB;
import com.huitu.cloud.api.syq.video.mapper.VideoDao;
import com.huitu.cloud.api.xxjh.smallreser.entity.BsnRsvrrecorderfileR;
import com.huitu.cloud.entity.LoginUserInfo;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import com.huitu.cloud.util.IPAndUserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 水库水情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-09
 */
@Service
public class RsvrServiceImpl extends ServiceImpl<RsvrDao, StRsvrR> implements RsvrService {

    @Autowired
    private RainDao rainDao;
    @Autowired
    private RainService rainService;
    @Autowired
    private VideoDao videoDao;


    @Override
    public IPage<StRsvrVo> getRsvrLatestByConditon(String adcd, String stm, String etm, String stnm, String bscd, List<String> stType, String rvtp, boolean dataFlag, boolean warnFlag, int pageNum, int pageSize, List<String> isOut, List<String> isFollow, boolean warnOpenFlag, Double drz, boolean videoFlag) {
        Page page = new Page<>(pageNum, pageSize);
        // 暂时设置为多选框条件全取消情况下清空数据 2021年7月25日 姜金阳
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals(""))) || (isOut != null && (isOut.size() == 0 || isOut.get(0).equals(""))) || (rvtp != null && rvtp.equals(""))) {
            page.setRecords(new ArrayList<StRsvrVo>());
            return page;
        }
        String stTypes = "";
        // 是否为运管
        boolean isYg = false;
        if (stType != null && stType.contains("YG")) { // 测站类型是否包括运管选项
            isYg = true;
            // 清除YG，只取标识
            stType = stType.stream().filter(item -> !item.equals("YG")).collect(Collectors.toList());
        }
        if (stType != null && stType.size() > 0) {
            for (String type : stType) {
                stTypes = stTypes + type + ",";
            }
            stTypes = stTypes.substring(0, stTypes.length() - 1);
        }
        int adLevl = AdcdUtil.getAdLevel(adcd);

        String whereSql = " and STTP IN ('RR','RQ') ";
//        if (StringUtils.isNotBlank(stnm)) {
//            whereSql += " and CHARINDEX('" + stnm + "',stnm)>0";
//        }
        if (StringUtils.isNotBlank(stTypes) && isYg) {
            whereSql += " and (CHARINDEX(STADTP,'" + stTypes + "')>0 or left(aa.stcd, 2) = 'YG')";
        } else if (StringUtils.isNotBlank(stTypes) && !isYg) {
            whereSql += " and CHARINDEX(STADTP,'" + stTypes + "')>0";
        } else if (StringUtils.isBlank(stTypes) && isYg) {
            whereSql += " and left(aa.stcd, 2) = 'YG'";
        }
        if (StringUtils.isNotBlank(rvtp)) {
            whereSql += " and CHARINDEX(RSVRTP,'" + rvtp + "')>0";
        }
//        if (warnFlag) {
//            if (warnOpenFlag) {
//                whereSql += " AND rzFlag>0 ";
//            } else {
//                whereSql += " AND rzFlag>0 AND STB.OSFLG != '1' ";
//            }
//        }

        //获取登录用户信息 查询域外
        LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
        String userAd = loginUserInfo.getUserAd();
        int level = AdcdUtil.getAdLevel(userAd);
        String userAdSub = userAd.substring(0, level);
        String notMhk = " and left(STB.ADCD,6) != '220581' ";

        //添加是否查询域外（省级）
        if (isOut != null && isOut.size() == 1) {
            if (isOut.get(0).equals("1")) {
                //查询域内
                whereSql += "  AND left(STB.ADCD," + adLevl + ") ='" + adcd.substring(0, adLevl) + "'";
                if (adLevl == 4) {
                    whereSql += notMhk;
                }
            } else {
                //查询域外
                whereSql += "  AND left(STB.ADCD, 2) !='22'";
            }
        } else if (isOut != null && isOut.size() == 2) {
            whereSql += "  AND ( left(STB.ADCD, 2) !='22' or ( STB.ADCD LIKE '" + adcd.substring(0, adLevl) + "%' " + (adLevl == 4 ? notMhk : "") + " )" + " )";
        } else if (isOut == null && StringUtils.isNotBlank(adcd)) {
            whereSql += "  AND STB.ADCD LIKE '" + adcd.substring(0, adLevl) + "%'";
            if (adLevl == 4) {
                whereSql += notMhk;
            }
        }
        // 添加是否关注站点参数
        if (isFollow != null && isFollow.size() == 1) {
            if (isFollow.get(0).equals("1")) {
                //查询关注站点
                whereSql += "   and  exists (select 8 from BSN_USER_FOLLOWST follow WHERE follow.STCD=AA.STCD and follow.userid =" + loginUserInfo.getUserId() + " )";
            } else {
                //查询未关注站点
                whereSql += "   and  not  exists (select 8 from BSN_USER_FOLLOWST follow WHERE follow.STCD=AA.STCD and follow.userid =" + loginUserInfo.getUserId() + " )";
            }
        }

        //优化速度 通过存储过程整理最新水库数据
        IPage<StRsvrVo> result = baseMapper.getRsvrLatestData(page, stm, etm, whereSql);
        List<StRsvrVo> allList1 = result.getRecords();
        // TRK@20240518: 整合工程数据
        allList1 = this.mergePrjRsvr(allList1, adcd, stnm, stType, rvtp, warnFlag, warnOpenFlag, isOut, isFollow, videoFlag);

        List<StRsvrVo> allList = filterRainListByBscd(allList1, bscd);
        //对水库重启排序
        List<StRsvrVo> newArray = this.sortRsvr(allList);
        // TRK@20240623: 人工与自动水位差过滤
        if (drz != null && drz >= 0) {
            newArray = newArray.stream().filter(item -> item.getDrz() != null && item.getDrz() > drz).collect(Collectors.toList());
        }

        result.setTotal(newArray.size());
        result.setCurrent(pageNum);
        result.setRecords(newArray);
        if (pageSize > 0) {
            int min = (pageNum - 1) * pageSize;
            int max = (pageNum) * pageSize;
            if (max >= newArray.size()) {
                max = newArray.size();
            }
            //每页的数据
            List<StRsvrVo> oneList = newArray.subList(min, max);
            result.setRecords(oneList);
        }
        result.getRecords().forEach(i -> {
            if (i.getRz() != null && i.getDsflz() != null) {
                i.setRzdsflz((new BigDecimal(i.getRz()).subtract(i.getDsflz())).setScale(2, RoundingMode.HALF_UP));
            }
        });
        return result;
    }

    @Override
    public IPage<StRsvrVo> getRsvrCompareZList(String adcd, String stm, String etm, String stnm, String bscd, List<String> stType, String rvtp, int pageNum, int pageSize, Double drz) {
        IPage<StRsvrVo> page = getRsvrLatestByConditon(adcd, stm, etm, stnm, bscd, stType, rvtp, true, false, pageNum, pageSize, Arrays.asList("1"), null, false, drz, false);
        Map<String, StRsvrVo> map = new HashMap<>();
        page.getRecords().forEach(i -> {
            if (StringUtils.isNotEmpty(i.getStnm()) && i.getStnm().contains("自")) {
                i.setZrz(i.getRz());
            } else {
                i.setRrz(i.getRz());
            }
            if (map.containsKey(i.getResCode())) {
                if (StringUtils.isNotEmpty(map.get(i.getResCode()).getStnm()) && map.get(i.getResCode()).getStnm().contains("自")) {
                    map.get(i.getResCode()).setRrz(i.getRrz());
                } else {
                    map.get(i.getResCode()).setZrz(i.getZrz());
                }
            } else {
                map.put(i.getResCode(), i);
            }
        });
        page.setRecords(new ArrayList<>(map.values()));
        // 排序
        page.getRecords().sort((o1, o2) -> Double.compare(o2.getDrz(), o1.getDrz()));
        return page;
    }

    @Override
    public void ExportRsvrCompareZList(String adcd, String stm, String etm, String stnm, String bscd, List<String> stType, String rvtp, int pageNum, int pageSize, Double drz) {
        IPage<StRsvrVo> page = getRsvrCompareZList(adcd, stm, etm, stnm, bscd, stType, rvtp, pageNum, pageSize, drz);
        List<StRsvrVo> list = page.getRecords();
        list.forEach(i -> {
            i.setDrz(Double.valueOf(String.format("%.2f", i.getDrz())));
        });
        ExcelExportUtil.execute(list, "水位数据对比分析");
    }

    /**
     * 拼接工程数据
     *
     * @param list
     */
    private List<StRsvrVo> mergePrjRsvr(List<StRsvrVo> list, String adcd, String stnm, List<String> stType, String rvtp, boolean warnFlag, boolean warnOpenFlag, List<String> isOut, List<String> isFollow, boolean videoFlag) {
        // 为空初始化，拼接工程数据
        if (list == null) {
            list = new LinkedList<>();
        }

        List<BsnObjonlyB> rsvrObjList = baseMapper.getBsnRsvrObjOnly();
        Map<String, String> rsvrStaRelMap = rsvrObjList.stream().filter(item -> "1".equals(item.getObjtp())).collect(Collectors.toMap(item -> item.getObjcd().trim(), item -> item.getObjid().trim(), (o, n) -> o));
        Map<String, String> rsvrPrjRelMap = rsvrObjList.stream().filter(item -> "6".equals(item.getObjtp())).collect(Collectors.toMap(item -> item.getObjid().trim(), item -> item.getObjcd().trim(), (o, n) -> o));

        List<ResBase> resBaseList = baseMapper.getAttResBase();
        Map<String, ResBase> resBaseMap = resBaseList.stream().collect(Collectors.toMap(item -> item.getResCode().trim(), Function.identity(), (o, n) -> o));

        // 工程编码
        List<ResBase> resMergeList = new LinkedList<>();
        list.forEach(item -> {
            if (StringUtils.isEmpty(item.getStcd())) {
                return;
            }

            String itemStcd = item.getStcd().trim();
            if (!rsvrStaRelMap.containsKey(itemStcd)) {
                return;
            }
            if (!rsvrPrjRelMap.containsKey(rsvrStaRelMap.get(itemStcd))) {
                return;
            }
            ResBase resBase = resBaseMap.get(rsvrPrjRelMap.get(rsvrStaRelMap.get(itemStcd)));
            if (resBase == null) {
                return;
            }

            resMergeList.add(resBase);
            item.setResKey(resBase.getResKey());
            item.setResCode(resBase.getResCode());
            item.setResName(resBase.getResName());
            item.setRsvrtp(String.valueOf(6 - resBase.getEngScal()));
        });

        // 拼接工程数据-关注查询不拼接
        if (CollectionUtils.isEmpty(isFollow)) {
            int adLevel = AdcdUtil.getAdLevel(adcd);
            String adValid = adcd.substring(0, adLevel);

            resBaseList.removeAll(resMergeList);
            List<StRsvrVo> finalList = list;
            resBaseList.forEach(item -> {
                if (StringUtils.isEmpty(item.getAdcd())) {
                    return;
                }

                if (CollectionUtils.isNotEmpty(isOut)) {
                    if (isOut.size() == 2) {
                        // 非省内 或 区域内（市级排除梅河口）
                        if (!(item.getAdcd().indexOf("22") != 0 || (item.getAdcd().indexOf(adValid) == 0 && !(adLevel == 4 && item.getAdcd().indexOf("220581") == 0)))) {
                            return;
                        }
                    } else if (isOut.size() == 1) {
                        // 仅区域内（市级排除梅河口）
                        if (isOut.get(0).equals("1")) {
                            if (item.getAdcd().indexOf(adValid) != 0) {
                                return;
                            }
                            if (adLevel == 4) {
                                if (item.getAdcd().indexOf("220581") == 0) {
                                    return;
                                }
                            }
                        }
                        // 仅区域外
                        else {
                            if (item.getAdcd().indexOf("22") == 0) {
                                return;
                            }
                        }
                    }
                } else {
                    // 仅区域内
                    if (item.getAdcd().indexOf(adValid) != 0) {
                        return;
                    }
                    if (adLevel == 4) {
                        if (item.getAdcd().indexOf("220581") == 0) {
                            return;
                        }
                    }
                }

                StRsvrVo stRsvrVo = new StRsvrVo();
                stRsvrVo.setResKey(item.getResKey());
                stRsvrVo.setResCode(item.getResCode());
                stRsvrVo.setResName(item.getResName());
                stRsvrVo.setStnm(item.getResName());
                stRsvrVo.setAdcd(item.getAdcd());
                stRsvrVo.setAdnm(item.getAdnm());
                if (item.getLowLeftLong() != null && item.getLowLeftLat() != null && item.getUpRightLong() != null && item.getUpRightLat() != null) {
                    stRsvrVo.setPlgtd(item.getLowLeftLong().add(item.getUpRightLong()).divide(new BigDecimal(2)));
                    stRsvrVo.setPlgtd(item.getLowLeftLat().add(item.getUpRightLat()).divide(new BigDecimal(2)));
                }
                if (item.getEngScal() != null) {
                    stRsvrVo.setRsvrtp(String.valueOf(6 - item.getEngScal()));
                }
                stRsvrVo.setXadnm(item.getAdnm());
                finalList.add(stRsvrVo);
            });
        }

        // 结果过滤
        List<StRsvrVo> result = new LinkedList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        List<String> videoCodeStrs;
        if (videoFlag) {
            List<BsnVdstCodeB> videoStcdCodes = videoDao.getVideoStcdExist(null, null, "6");
            videoCodeStrs = videoStcdCodes.stream().map(BsnVdstCodeB::getCode).collect(Collectors.toList());
        } else {
            videoCodeStrs = null;
        }

        list.forEach(item -> {
            // 省内仅保留工程关联记录
            if (StringUtils.isNotEmpty(item.getAdcd()) && item.getAdcd().indexOf("22") == 0 && StringUtils.isEmpty(item.getResCode())) {
                return;
            }

            // stnm
            if (StringUtils.isNotEmpty(stnm)) {
                if (StringUtils.isEmpty(item.getStnm()) || item.getStnm().indexOf(stnm) < 0) {
                    return;
                }
            }
            // rvtp
            if (StringUtils.isNotEmpty(rvtp)) {
                if (StringUtils.isEmpty(item.getRsvrtp()) || rvtp.indexOf(item.getRsvrtp()) < 0) {
                    return;
                }
            }
            // warnFlag
            if (warnFlag) {
                // 仅保留报讯数据
                if (item.getTm() == null || item.getRzflag() == null) {
                    return;
                }
                if (warnOpenFlag) {
                    if (item.getRzflag() <= 0) {
                        return;
                    }
                } else {
                    if (item.getRzflag() <= 0 || "1".equals(item.getOsflg())) {
                        return;
                    }
                }
            }
            if (videoFlag) {
                if (!videoCodeStrs.contains(item.getResCode())) {
                    return;
                }
            }
            result.add(item);
        });
        // 填充设计库容
        this.fillRsvrDscp(result);
        return result;
    }

    /**
     * 填充设计库容
     *
     * @param list
     */
    private void fillRsvrDscp(List<StRsvrVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, List<StZvarlB>> zvarlMap = this.getStZvarLbMap();
        if (MapUtils.isEmpty(zvarlMap)) {
            return;
        }

        list.forEach(item -> {
            if (item.getStcd() == null || item.getDsflz() == null) {
                return;
            }

            List<StZvarlB> zvarlList = zvarlMap.get(item.getStcd());
            if (CollectionUtils.isEmpty(zvarlList)) {
                return;
            }

            try {
                Double dsflz = Double.parseDouble(item.getDsflz().toString());
                for (int i = 1; i < zvarlList.size(); i++) {
                    Double rz0 = Double.parseDouble(zvarlList.get(i - 1).getRz().toString());
                    Double rz1 = Double.parseDouble(zvarlList.get(i).getRz().toString());
                    if (dsflz >= rz0 && dsflz <= rz1) {
                        Double w0 = Double.parseDouble(zvarlList.get(i - 1).getW().toString());
                        Double w1 = Double.parseDouble(zvarlList.get(i).getW().toString());
                        if (rz1 > rz0) {
                            Double dscp = w0 + (w1 - w0) / (rz1 - rz0) * (dsflz - rz0);
                            item.setDscp(new BigDecimal(dscp).setScale(2, RoundingMode.HALF_UP));
                            break;
                        }
                    }
                }
            } catch (Exception ex) {
                log.debug(ex.toString());
            }
        });
    }

    private Map<String, List<StZvarlB>> getStZvarLbMap() {
        List<StZvarlB> list = baseMapper.getStZvarLbLastList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().collect(Collectors.groupingBy(StZvarlB::getStcd));
    }

    private List<StRsvrVo> filterRainListByBscd(List<StRsvrVo> stList, String bscd) {
        if (bscd != null && !bscd.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<BsnBasStBTo> bsList = rainDao.getRiverTree(bscd);
            List<String> stcdList = bsList.stream().map(BsnBasStBTo::getStcd).collect(Collectors.toList());
            List<StRsvrVo> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            stList = intersection;
            return intersection;
        } else {
            return stList;
        }
    }

    /**
     * 重新排序 同一水库的人工和自动站显示在一起，显示为人工站在上
     *
     * @param list 水情列表数据集合
     * @return
     */
    private List<StRsvrVo> sortRsvr(List<StRsvrVo> list) {
        /* 勾选【仅超汛限】和【开敞式】后， */
        // 是否存在全部超汛限水库数据
        boolean warnFlag = list.stream().allMatch(item -> null != item.getRzfsltdz() && item.getRzfsltdz().compareTo(new BigDecimal(0)) > 0);
        if (warnFlag) {
            // 是否存在开敞式溢洪道数据
            boolean hasOpen = list.stream().anyMatch(item -> item.getOsflg().equals("1"));
            if (hasOpen) {
                list = list.stream().sorted(Comparator.comparing(StRsvrVo::getOsflg)
                        .thenComparing(StRsvrVo::getRsvrtp, Comparator.reverseOrder())
                        .thenComparing(StRsvrVo::getRzfsltdz, Comparator.reverseOrder())
                )
                        .collect(Collectors.toList());
            }
        }
        //todo 吉林业主要求 lxl 修改 2020-06-10
        List<StRsvrVo> newArray = new ArrayList<>();
        Map<String, List<StRsvrVo>> stnmMap = new HashMap<>();
        //查询同一水库既有人工又有自动的站的对应关系
        List<BsnObjonlyB> bList = baseMapper.getBsnObjOnly();
        Map<String, String> objcdMap = new HashMap<>();
        for (BsnObjonlyB item : bList) {
            //存储测站对应统一标识id
            objcdMap.put(item.getObjcd(), item.getObjid());
        }
        //是否存在同一水库既有人工又有自动的
        boolean twoFlag = false;
        //存储同一水库既有人工又有自动的
        Map<String, List<StRsvrVo>> stnmTwoMap = new HashMap<>();
        for (StRsvrVo item : list) {
            //以相同标识 判定同一个水库的人工与自动站关联
            String stcd = item.getStcd();
            String key = "";
            if (objcdMap.containsKey(stcd)) {
                //同一个水库存在人工与自动站关联 以标识为主键
                //key = objcdMap.get(stcd);
                key = item.getResKey();
            } else {
                //同一个水库不存在人工与自动站关联 以测站为主键标识
                key = stcd;
                // TRK@20240520: 补充key逻辑
                if (StringUtils.isEmpty(key)) {
                    key = item.getResKey();
                }
            }
            if (stnmMap.containsKey(key)) {
                twoFlag = true;
                List<StRsvrVo> stmList = stnmMap.get(key);
                //1、山洪改为自动，水文和人工报汛改为人工，默认显示人工，山洪自动站（5），人工为山洪人工站（3）和所有水文站
                //2、同一水库的人工和自动站显示在一起，显示为人工站在上
                // stadtp， 2代表人工 1,4 代表自动
                if (item.getStadtp() != null && (item.getStadtp().equals("4") || item.getStadtp().equals("1"))) {
                    //人工在前
                    stmList.add(0, item);
                } else {
                    //自动在后
                    stmList.add(item);
                }
                stnmTwoMap.put(key, stmList);
                stnmMap.put(key, stmList);
            } else {
                List<StRsvrVo> stmList = new ArrayList<>();
                stmList.add(item);
                stnmMap.put(key, stmList);
                //同一水库的人工和自动站 只添加一次
                newArray.add(item);
            }

        }
        List<StRsvrVo> result = new ArrayList<>();
        int sortNo = 1;
        if (twoFlag) {
            // 2021年6月8日 姜金阳 修改：手动加入sortNo排序，将同一个水库存在人工与自动站关联情况合并为同一个排序值
            for (StRsvrVo item : newArray) {
                //以相同标识 判定同一个水库的人工与自动站关联
                String stcd = item.getStcd();
                String key = "";
                if (objcdMap.containsKey(stcd)) {
                    //同一个水库存在人工与自动站关联 以标识为主键
                    //key = objcdMap.get(stcd);
                    key = item.getResKey();
                } else {
                    //同一个水库不存在人工与自动站关联 以测站为主键标识
                    key = stcd;
                    // TRK@20240520: 补充key逻辑
                    if (StringUtils.isEmpty(key)) {
                        key = item.getResKey();
                    }
                }
                if (stnmTwoMap.containsKey(key)) {
                    List<StRsvrVo> stmList = stnmTwoMap.get(key);
                    if (stmList.size() > 0) {
                        stmList.get(0).setSortNo(sortNo);
                        // TRK@20240623: 人工与自动水位差
                        if (stmList.size() > 1) {
                            try {
                                Map<String, List<StRsvrVo>> stmRsvrMap = stmList.stream().collect(Collectors.groupingBy(StRsvrVo::getResCode));
                                for (List<StRsvrVo> stmRsvrList : stmRsvrMap.values()) {
                                    if (stmRsvrList != null && stmRsvrList.size() > 1) {
                                        Double drz = Math.abs(Double.parseDouble(stmRsvrList.get(1).getRz()) - Double.parseDouble(stmRsvrList.get(0).getRz()));
                                        for (int i = 2; i < stmRsvrList.size(); i++) {
                                            for (int j = 0; j < i; j++) {
                                                Double tempDrz = Math.abs(Double.parseDouble(stmRsvrList.get(i).getRz()) - Double.parseDouble(stmRsvrList.get(j).getRz()));
                                                if (tempDrz > drz) {
                                                    drz = tempDrz;
                                                }
                                            }
                                        }
                                        Double finalDrz = drz;
                                        stmRsvrList.forEach(rsvrVo -> rsvrVo.setDrz(finalDrz));
                                    }
                                }
                            } catch (Exception ex) {
                                log.trace(ex.getMessage());
                            }
                        }
                    }
                    //同一个水库的人工与自动站 一起插入
                    result.addAll(stmList);
                } else {
                    item.setSortNo(sortNo);
                    result.add(item);
                }
                sortNo++;
            }
            return result;
        } else {
            for (int i = 0; i < newArray.size(); i++) {
                newArray.get(i).setSortNo(i + 1);
            }
            return newArray;
        }

    }


    @Override
    public List<StRsvrTmVo> getRsvrByStcdAndTm(String stcd, String stm, String etm) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stcd", stcd);
        List<StRsvrTmVo> list = baseMapper.getTmList(param);
        return list;
    }

    @Override
    public void exportTmList(String stcd, String stm, String etm) {
        List<StRsvrTmVo> result = getRsvrByStcdAndTm(stcd, stm, etm);
        result.forEach(item -> {
            if (item.getInq() == null) {
                item.setInq(item.getAvinq());
            }
            if (item.getOtq() == null) {
                item.setOtq(item.getAvotq());
            }
        });
        ExcelExportUtil.execute(result, "水库水情预报实时数据");
    }

    @Override
    public List<StZvarlB> getStZvarLbByStcdAndMstm(String stcd, String mstm) {
        Map<String, Object> param = new HashMap<>();
        param.put("stcd", stcd);
        param.put("mstm", mstm);
        List<StZvarlB> list = baseMapper.getStZvarLbByStcdAndMstm(param);
        return list;
    }

    @Override
    public StRsvrfcchB getStRsvrfcchbByStcd(String stcd) {
        StRsvrfcchB rsvrfcchb = baseMapper.selectStRsvrfcchbByStcd(stcd);
        return rsvrfcchb;
    }

    @Override
    public List<StRsvrfsrB> getStRsvrfsrbByStcd(String stcd) {
        List<StRsvrfsrB> list = baseMapper.selectStRsvrfsrbByStcd(stcd);
        return list;
    }

    @Override
    public IPage<RsvrDayAvg> getDayAvgRsvrByPage(String stm, String etm, List<String> stList, String tmType, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        String stcds = "";
        for (String item : stList) {
            stcds += "'" + item + "'" + ",";
        }
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("tmType", tmType);
        if (stcds.length() > 0) {
            param.put("stcds", stcds.substring(0, stcds.length() - 1));
        } else {
            //登录用户信息
            LoginUserInfo loginUserInfo = IPAndUserUtil.getUser(null);
            if (StringUtils.isNotBlank(loginUserInfo.getUserAd())) {
                int adLevl = AdcdUtil.getAdLevel(loginUserInfo.getUserAd());
                param.put("ad", loginUserInfo.getUserAd().substring(0, adLevl));
                param.put("level", adLevl);
            }
        }
        //查询结果对象返回map 可以根据service接口需要，转化成自己需要实体返回类型
        IPage<RsvrDayAvg> resultMap = baseMapper.getDayAvgRsvrByPage(page, param);
        return resultMap;
    }

    @Override
    public List<String> getStZvarLbByStcd(String stcd) {
        List<String> list = baseMapper.getStZvarLbByStcd(stcd);
        return list;
    }

    @Override
    public IPage<StReglatfSqyb> getStReglaTbyInfo(String adcd, String wnstatus, String stnm, Date stm, Date etm, List<String> stcds, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stnm", stnm);
        param.put("wnstatus", wnstatus);
        param.put("ad", adcd.substring(0, adLevl));
        param.put("adLevl", adLevl);
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stcds", stcds);
        IPage<StReglatfSqyb> result = baseMapper.getStReglaTbyInfo(page, param);
        return result;
    }

    @Override
    public List<StReglatfSqybd> getStReglaTbyAdcd(String stcd, String ymdh) {
        List<StReglatfSqybd> list = baseMapper.getStReglaTbyAdcd(stcd, ymdh);
        return list;
    }

    @Override
    public void exportStreglatList(String stcd, String ymdh) {
        List<StReglatfSqybd> result = getStReglaTbyAdcd(stcd, ymdh);
        ExcelExportUtil.execute(result, "水库水情预报数据");
    }

    @Override
    public RsvrTypeCount getStRsvrCountInfo(String adcd, String stm, String etm, String stnm, String bsnm, List<String> stType, String rvType, boolean dataFlag, boolean warnFlag, int pageNum, int pageSize, List<String> isOut) {
        //查询水库信息
        IPage<StRsvrVo> result = getRsvrLatestByConditon(adcd, stm, etm, stnm, bsnm, stType, rvType, dataFlag, warnFlag, 1, -1, isOut, null, false, null, false);
        List<StRsvrVo> list = result.getRecords();
        int rsvrCount = 0;
        int bigRsvrCount = 0;
        int middleRsvrCount = 0;
        int smallRsvrCount1 = 0;
        int smallRsvrCount2 = 0;
        for (StRsvrVo x : list) {
            if (x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRBIGTYPETWO) || x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRBIGTYPEONE)) {
                bigRsvrCount++;
            } else if (x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRMIDDLETYPE)) {
                middleRsvrCount++;
            } else if (x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRSMALLTYPEONE)) {
                smallRsvrCount1++;
            } else if (x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRSMALLTYPETWO)) {
                smallRsvrCount2++;
            }
            rsvrCount = rsvrCount + 1;
        }
        RsvrTypeCount rsvrTypeCount = new RsvrTypeCount();
        rsvrTypeCount.setBigRsvrCount(bigRsvrCount);
        rsvrTypeCount.setMiddleRsvrCount(middleRsvrCount);
        rsvrTypeCount.setSmallRsvrCount1(smallRsvrCount1);
        rsvrTypeCount.setSmallRsvrCount2(smallRsvrCount2);
        rsvrTypeCount.setRsvrCount(rsvrCount);
        return rsvrTypeCount;
    }

    @Override
    public RsvrWarn getStRsvrWarn(String adcd, String stm, String etm, String stnm, String bscd, List<String> stType, String rvType) {
        //超汛限集合
        List<StRsvrVo> warnWfsltdwList = new ArrayList<>();
        //超正常高集合
        List<StRsvrVo> warnRznormzList = new ArrayList<>();
        if ((stType != null && (stType.size() == 0 || stType.get(0).equals(""))) || (rvType != null && rvType.equals(""))) {
            RsvrWarn warn = new RsvrWarn();
            warn.setWarnRznormzList(warnRznormzList);
            warn.setWarnWfsltdwList(warnWfsltdwList);
            return warn;
        }
        IPage<StRsvrVo> result = getRsvrLatestByConditon(adcd, stm, etm, stnm, bscd, stType, rvType, true, true, 1, -1, null, null, false, null, false);
        List<StRsvrVo> list = result.getRecords();
        list.forEach(x -> {
            //超正常高
            double zgrz = x.getRznormz() != null ? x.getRznormz().doubleValue() : 0;
            //水库在超正常高 就不在超汛限中存储
            if (zgrz > 0) {
                warnRznormzList.add(x);
            } else {
                warnWfsltdwList.add(x);
            }
        });
        RsvrWarn warn = new RsvrWarn();
        warn.setWarnRznormzList(warnRznormzList);
        warn.setWarnWfsltdwList(warnWfsltdwList);
        return warn;
    }

    @Override
    public StRsvrVoOfMaxRzfsltdz getStRsvrVoMaxRzfsltdz(String adcd, String stm, String etm, String stTypes, String rvTypes) {
        if ((stTypes != null && (stTypes.equals(""))) || (rvTypes != null && rvTypes.equals(""))) {
            StRsvrVoOfMaxRzfsltdz ret = new StRsvrVoOfMaxRzfsltdz();
            ret.setStrsvrvolist(new ArrayList<StRsvrMax>());
            return ret;
        }
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> map = new HashMap<>();
        map.put("adcd", adcd);
        map.put("stm", stm);
        map.put("etm", etm);
        map.put("stTypes", stTypes);
        map.put("rvTypes", rvTypes);
        map.put("ad", adcd.substring(0, adLevl));
        map.put("adLevl", adLevl);
        List<StRsvrMax> list = baseMapper.getStRsvrVoOfMaxRzfsltdz(map);
        list.forEach(item -> {
            item.setRzfsltdz(item.getRzfsltdz() == null ? null : new BigDecimal(item.getRzfsltdz().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
            item.setRz(item.getRz() == null ? null : new BigDecimal(item.getRz()).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
            item.setFsltdz(item.getFsltdz() == null ? null : new BigDecimal(item.getFsltdz().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
            item.setW(item.getW() == null ? null : new BigDecimal(item.getW().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
            item.setFsltdw(item.getFsltdw() == null ? null : new BigDecimal(item.getFsltdw().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
            item.setWfsltdw(item.getWfsltdw() == null ? null : new BigDecimal(item.getWfsltdw().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
            item.setMaxinq(item.getMaxinq() == null ? null : new BigDecimal(item.getMaxinq().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
            item.setMaxotq(item.getMaxotq() == null ? null : new BigDecimal(item.getMaxotq().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
        });
//        list = list.stream().sorted(Comparator.comparing(StRsvrMax::getRsvrtp, Comparator.reverseOrder()).thenComparing(StRsvrMax::getRzfsltdz ,Comparator.reverseOrder())).collect(Collectors.toList());
        // 查询超汛限站点时段内最大入库流量数据
        List<StRsvrMax> maxinqList = baseMapper.getStRsvrVoOfMaxInqfsltdz(map);
        // 查询超汛限站点时段内最大出库流量数据
        List<StRsvrMax> maxotqList = baseMapper.getStRsvrVoOfMaxOtqfsltdz(map);

        //小型水库数量
        int smallNum = 0;
        //中型水库数量
        int middleNum = 0;
        //大型水库数量
        int bigNum = 0;
        List<StRsvrMax> stRsvrVoList = new ArrayList<>();
        for (StRsvrMax x : list) {
            StRsvrMax stRsvrMaxInq = maxinqList.stream().filter(item -> item.getStcd().equals(x.getStcd())).findFirst().orElse(null);
            StRsvrMax stRsvrMaxOtq = maxotqList.stream().filter(item -> item.getStcd().equals(x.getStcd())).findFirst().orElse(null);
            x.setMaxinq((stRsvrMaxInq == null || stRsvrMaxInq.getMaxinq() == null) ? null : new BigDecimal(stRsvrMaxInq.getMaxinq().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
            x.setMaxinqtm((stRsvrMaxInq == null || stRsvrMaxInq.getMaxinqtm() == null) ? null : stRsvrMaxInq.getMaxinqtm());
            x.setMaxotq((stRsvrMaxOtq == null || stRsvrMaxOtq.getMaxotq() == null) ? null : new BigDecimal(stRsvrMaxOtq.getMaxotq().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
            x.setMaxotqtm((stRsvrMaxOtq == null || stRsvrMaxOtq.getMaxotqtm() == null) ? null : stRsvrMaxOtq.getMaxotqtm());
            if (x.getRzfsltdz() != null && Double.parseDouble(x.getRzfsltdz() + "") > 0) {
                stRsvrVoList.add(x);
                if (SyqConstants.RsvrConstants.RSVRSMALLTYPETWO.equals(x.getRsvrtp()) || SyqConstants.RsvrConstants.RSVRSMALLTYPEONE.equals(x.getRsvrtp())) {
                    smallNum++;
                } else if (SyqConstants.RsvrConstants.RSVRMIDDLETYPE.equals(x.getRsvrtp())) {
                    middleNum++;
                } else if (SyqConstants.RsvrConstants.RSVRBIGTYPETWO.equals(x.getRsvrtp()) || SyqConstants.RsvrConstants.RSVRBIGTYPEONE.equals(x.getRsvrtp())) {
                    bigNum++;
                }
            }
        }
        // 查询同一水库既有人工又有自动的站的对应关系
        List<BsnObjonlyB> objList = baseMapper.getBsnObjOnly();
        // 数据的测站列表
        List<String> stcdList = stRsvrVoList.stream().map(StRsvrMax::getStcd).collect(Collectors.toList());
        // 过滤出对应关系中存在数据列表中的测站的数据并组成 stcd:ID 的 map
        Map<String, String> cdidMap = objList.stream().filter(i -> stcdList.contains(i.getObjcd())).collect(Collectors.toMap(BsnObjonlyB::getObjcd, BsnObjonlyB::getObjid));
        // 包含测站的关系列表中ID和测站码列表的map
        Map<String, List<String>> objidMapList = objList.stream().filter(i -> stcdList.contains(i.getObjcd())).collect(Collectors.groupingBy(BsnObjonlyB::getObjid, Collectors.mapping(BsnObjonlyB::getObjcd, Collectors.toList())));
        // 关系ID和测站对应数据列表的map
        Map<String, List<StRsvrMax>> objListMap = new HashMap<>();
        objidMapList.forEach((key, vlist) -> {
            // 过滤出关系ID对应的测站列表并按照人工站在前自动站在后的顺序排序
            List<StRsvrMax> currentStRsvrVoList = stRsvrVoList.stream().filter(stRsvrMax -> vlist.contains(stRsvrMax.getStcd())).sorted((o1, o2) -> {
                if (o1.getStadtp().equals("4") || o1.getStadtp().equals("1")) {
                    return -1;
                } else {
                    return 1;
                }
            })
                    .collect(Collectors.toList());
            objListMap.put(key, currentStRsvrVoList);
        });
        // 查重编码列表
        List<String> stcdIdList = new ArrayList<>();
        List<StRsvrMax> stRsvrMaxes = new ArrayList<>();
        int sortNo = 1;
        for (StRsvrMax item : stRsvrVoList) {
            // 是否存在关系ID
            if (cdidMap.containsKey(item.getStcd())) {
                String objid = cdidMap.get(item.getStcd());
                // 是否存在关系ID对应的测站列表
                if (objListMap.containsKey(objid)) {
                    // 是否已经添加过stcd或关系ID码
                    if (!stcdIdList.contains(objid)) {
                        List<StRsvrMax> rsvrMaxes = objListMap.get(objid);
                        if (rsvrMaxes.size() > 0) {
                            // 列表第一个设置序号
                            rsvrMaxes.get(0).setSortNo(sortNo);
                            sortNo++;
                        }
                        // 将列表插入
                        stRsvrMaxes.addAll(rsvrMaxes);
                        stcdIdList.add(objid);
                    }
                }
            } else {
                // 没有对应关系ID，直接插入
                item.setSortNo(sortNo);
                stRsvrMaxes.add(item);
                sortNo++;
            }
        }

        Map<String, Long> rsvrtpCntMap = stRsvrMaxes.stream().filter(i -> i.getSortNo() != null).collect(Collectors.groupingBy(StRsvrMax::getRsvrtp, Collectors.counting()));
        bigNum = Math.toIntExact(rsvrtpCntMap.containsValue(SyqConstants.RsvrConstants.RSVRBIGTYPETWO) ? rsvrtpCntMap.get(SyqConstants.RsvrConstants.RSVRBIGTYPETWO) : 0)
                +
                (Math.toIntExact(rsvrtpCntMap.containsValue(SyqConstants.RsvrConstants.RSVRBIGTYPEONE) ? rsvrtpCntMap.get(SyqConstants.RsvrConstants.RSVRBIGTYPEONE) : 0));
        middleNum = Math.toIntExact(rsvrtpCntMap.containsValue(SyqConstants.RsvrConstants.RSVRMIDDLETYPE) ? rsvrtpCntMap.get(SyqConstants.RsvrConstants.RSVRMIDDLETYPE) : 0);
        smallNum = Math.toIntExact(rsvrtpCntMap.containsValue(SyqConstants.RsvrConstants.RSVRSMALLTYPETWO) ? rsvrtpCntMap.get(SyqConstants.RsvrConstants.RSVRSMALLTYPETWO) : 0)
                + Math.toIntExact(rsvrtpCntMap.containsValue(SyqConstants.RsvrConstants.RSVRSMALLTYPEONE) ? rsvrtpCntMap.get(SyqConstants.RsvrConstants.RSVRSMALLTYPEONE) : 0);
        StRsvrVoOfMaxRzfsltdz stRsvrVoOfMaxRzfsltdz = new StRsvrVoOfMaxRzfsltdz();
        stRsvrVoOfMaxRzfsltdz.setBigNum(bigNum);
        stRsvrVoOfMaxRzfsltdz.setMiddleNum(middleNum);
        stRsvrVoOfMaxRzfsltdz.setSmallNum(smallNum);
        stRsvrVoOfMaxRzfsltdz.setStrsvrvolist(stRsvrMaxes);
        return stRsvrVoOfMaxRzfsltdz;
    }

    @Override
    public RsvtInfoByDuty getStRsvrVoVyDuty(String adcd, String stm, String etm, String rsvrStType) {
        List<String> stType = new ArrayList<>();
        if ("".equals(rsvrStType)) {
            stType.add("1");
            stType.add("2");
            stType.add("3");
            stType.add("4");
            stType.add("5");
        } else {
            stType = Arrays.asList(rsvrStType.split(","));
        }

        IPage<StRsvrVo> result = getRsvrLatestByConditon(adcd, stm, etm, "", "", stType, null, true, true, 1, -1, null, null, false, null, false);
        List<StRsvrVo> list = result.getRecords();
        int bigRsvrCount = 0;
        int middleRsvrCount = 0;
        int smallRsvrCount = 0;
        List<StRsvrVo> bigRsvrList = new ArrayList<>();
        List<StRsvrVo> middleRsvrList = new ArrayList<>();
        List<StRsvrVo> smallRsvrList = new ArrayList<>();
        //todo 吉林业主要求 lxl 修改 2020-06-11
        //按照吉林要求 同政区与同名称的算作同一水库的人工和自动站，两个都超汛限 ，算作一座水库超讯，并取人工站
        // 在水情查询中已经对同一水库排序，人工站排在上面 所以在此处只需要去同一水库的第一值即可
        //超汛限的水库总数 同一水库只取一次 相加
        Map<String, StRsvrVo> stnmMap = new HashMap<>();
        //水库总数
        int total = 0;
        //查询同一水库既有人工又有自动的站的对应关系
        List<BsnObjonlyB> bList = baseMapper.getBsnObjOnly();
        Map<String, String> objcdMap = new HashMap<>();
        for (BsnObjonlyB item : bList) {
            //存储测站对应统一标识id
            objcdMap.put(item.getObjcd(), item.getObjid());
        }
        for (StRsvrVo x : list) {
            //测站对应统一标识id 判定同一个水库的人工与自动站关联
            String key = objcdMap.containsKey(x.getStcd()) ? objcdMap.get(x.getStcd()) : x.getStcd();
            if (stnmMap.containsKey(key)) {
                //同一水库只取一次 后面直接跳过
                continue;
            } else {
                //同一水库只取一次 相加
                total++;
                stnmMap.put(key, x);
            }
            if (x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRBIGTYPETWO) || x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRBIGTYPEONE)) {
                bigRsvrCount++;
                bigRsvrList.add(x);
            } else if (x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRMIDDLETYPE)) {
                middleRsvrCount++;
                middleRsvrList.add(x);
            } else if (x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRSMALLTYPEONE) || x.getRsvrtp().equals(SyqConstants.RsvrConstants.RSVRSMALLTYPETWO)) {
                smallRsvrCount++;
                smallRsvrList.add(x);
            }
        }
        String middleRsvrName = "";
        for (int i = 0; i < middleRsvrList.size(); i++) {
            if (i < 3) {
                middleRsvrName += middleRsvrList.get(i).getStnm() + ",";
            } else {
                break;
            }
        }
        RsvtInfoByDuty rsvtInfoByDuty = new RsvtInfoByDuty();
        //水库站超汛限总座数
        rsvtInfoByDuty.setTotal(total);
        //大型水库超汛限座数
        rsvtInfoByDuty.setBigRsvrCount(bigRsvrCount);
        //中型水库超汛限座数
        rsvtInfoByDuty.setMiddleRsvrCount(middleRsvrCount);
        //小型水库超汛限座数
        rsvtInfoByDuty.setSmallRsvrCount(smallRsvrCount);
        //大型水库超汛限集合
        rsvtInfoByDuty.setBigList(bigRsvrList);
        if (bigRsvrList.size() > 0) {
            rsvtInfoByDuty.setBigRsvr(bigRsvrList.get(0).getRzfsltdz() + "-" + bigRsvrList.get(bigRsvrList.size() - 1).getRzfsltdz());
        }
        if (middleRsvrList.size() > 0) {
            rsvtInfoByDuty.setMiddleRsvr(middleRsvrList.get(0).getRzfsltdz() + "-" + middleRsvrList.get(middleRsvrList.size() - 1).getRzfsltdz());
        }
        if (smallRsvrList.size() > 0) {
            rsvtInfoByDuty.setSmallRsvr(smallRsvrList.get(0).getRzfsltdz() + "-" + smallRsvrList.get(smallRsvrList.size() - 1).getRzfsltdz());
        }
        if (middleRsvrName != "" && middleRsvrName.length() > 1) {
            rsvtInfoByDuty.setMiddleRsvrName(middleRsvrName.substring(0, middleRsvrName.length() - 1));
        }
        //中型水库超汛限集合
        rsvtInfoByDuty.setMiddleAllList(middleRsvrList);
        //小型水库的超汛限集合
        rsvtInfoByDuty.setSmallList(smallRsvrList);
        if (middleRsvrList.size() > 3) {
            middleRsvrList = middleRsvrList.subList(0, 3);
        }
        rsvtInfoByDuty.setMiddleList(middleRsvrList);
        return rsvtInfoByDuty;
    }

    @Override
    public void ExportStRsvrLatestList(String adcd, String stm, String etm, String stnm, String bsnm, List<String> stType, String rvType, boolean dataFlag, boolean warnFlag, boolean warnOpenFlag, List<String> isOut, String type, Double drz, boolean videoFlag) {
        IPage<StRsvrVo> result = getRsvrLatestByConditon(adcd, stm, etm, stnm, bsnm, stType, rvType, dataFlag, warnFlag, 0, -1, isOut, null, warnOpenFlag, drz, videoFlag);
        List<StRsvrVo> list = result.getRecords();
        list.forEach(i -> {
            // 正常库容
            if ((i.getDdcp() != null && i.getDdcp().compareTo(BigDecimal.ZERO) > 0)
                    && (i.getActcp() != null && i.getActcp().compareTo(BigDecimal.ZERO) > 0)) {
                i.setNormcp(i.getDdcp().add(i.getActcp()).setScale(2, RoundingMode.HALF_UP));
            }
            // 蓄水率
            if ((i.getW() != null && i.getW().compareTo(BigDecimal.ZERO) > 0)
                    && (i.getDdcp() != null && i.getDdcp().compareTo(BigDecimal.ZERO) > 0)
                    && (i.getActcp() != null && i.getActcp().compareTo(BigDecimal.ZERO) > 0)) {
                i.setNormrate((i.getW().divide(i.getDdcp().add(i.getActcp()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))).setScale(2, RoundingMode.HALF_UP));
            }
            // 超设计水位
            if ((i.getRz() != null && new BigDecimal(i.getRz()).compareTo(BigDecimal.ZERO) > 0)
                    && (i.getDsflz() != null && i.getDsflz().compareTo(BigDecimal.ZERO) > 0)
                    && (new BigDecimal(i.getRz()).subtract(i.getDsflz())).compareTo(BigDecimal.ZERO) > 0) {
                i.setDsflzs(new BigDecimal(i.getRz()).subtract(i.getDsflz()).setScale(2, RoundingMode.HALF_UP));
            }
            // 超校核水位
            if ((i.getRz() != null && new BigDecimal(i.getRz()).compareTo(BigDecimal.ZERO) > 0)
                    && (i.getCkflz() != null && i.getCkflz().compareTo(BigDecimal.ZERO) > 0)
                    && (new BigDecimal(i.getRz()).subtract(i.getCkflz())).compareTo(BigDecimal.ZERO) > 0) {
                i.setCkflzs(new BigDecimal(i.getRz()).subtract(i.getCkflz()).setScale(2, RoundingMode.HALF_UP));
            }
            // 超坝顶高程
            if ((i.getRz() != null && new BigDecimal(i.getRz()).compareTo(BigDecimal.ZERO) > 0)
                    && (i.getDamel() != null && i.getDamel().compareTo(BigDecimal.ZERO) > 0)
                    && (new BigDecimal(i.getRz()).subtract(i.getDamel())).compareTo(BigDecimal.ZERO) > 0) {
                i.setDamels(new BigDecimal(i.getRz()).subtract(i.getDamel()).setScale(2, RoundingMode.HALF_UP));
            }
            if ("1".equals(i.getOsflg())) {
                i.setOsflg("是");
            }
            if ("0".equals(i.getOsflg())) {
                i.setOsflg("否");
            }
            if (i.getRznormz() != null && i.getRznormz().compareTo(BigDecimal.ZERO) < 0){
                i.setRznormz(null);
            }

        });
        if ("1".equals(type)) {
            ExcelExportUtil.execute(list, "综合监视水库水情");
        } else if ("2".equals(type)) {
            list.stream().forEach(item -> {
                String adnm = "";
                if (StringUtils.isBlank(item.getXzadnm())) {
                    adnm = item.getXadnm();
                } else {
                    if (item.getXzadnm().equals(item.getXadnm())) {
                        adnm = item.getXadnm();
                    } else {
                        adnm = item.getXadnm() + " - " + item.getXzadnm();
                    }
                }
                item.setAdnm(adnm);
            });
            ExcelExportUtil.execute(list, "最新水库水情");
        }
    }

    @Override
    public void ExportDayAvgRsvrByPage(String stm, String etm, List<String> stList, String tmType) {
        IPage<RsvrDayAvg> result = getDayAvgRsvrByPage(stm, etm, stList, tmType, 0, -1);
        List<RsvrDayAvg> list = result.getRecords();
        ExcelExportUtil.execute(list, "水库日旬月均值查询");
    }

    @Override
    public void exportStRsvrVoMaxRzfsltdz(String adcd, String stm, String etm, String stTypes, String rvTypes) {
        StRsvrVoOfMaxRzfsltdz entity = this.getStRsvrVoMaxRzfsltdz(adcd, stm, etm, stTypes, rvTypes);
        List<StRsvrMax> list = entity.getStrsvrvolist();
        ExcelExportUtil.execute(list, "水库最大超汛限水位统计");
    }

    @Override
    public List<ObjOnlyIACAdinfo> getObjOnlyIACAdinfoByStcd(String stcd) {
        List<ObjOnlyIACAdinfo> list = baseMapper.getObjOnlyIACAdinfoByStcd(stcd);
        return list;
    }

    @Override
    public List<ReservoirWaterinfo> getReservoirWaterRegimePage(String bgtm, String endtm, String addvcd, String stnm) {
        String adcd = addvcd.replaceAll("0+$", "");
        Map<String, Object> param = new HashMap<>();
        param.put("bgtm", bgtm);
        param.put("endtm", endtm);
        param.put("addvcd", adcd);
        param.put("stnm", stnm);
        List<ReservoirWaterinfo> result = baseMapper.getReservoirWaterRegimePage(param);
        return result;
    }

    @Override
    public List<ReservoirWaterinfo> getReservoirWaterRegimePhoto(String tm, String stcd) {
        List<ReservoirWaterinfo> result = baseMapper.getReservoirWaterRegimePhoto(tm, stcd);
        return result;
    }

    @Override
    public List<BsnRsvrrecorderfileR> getPhotoComparison(String stcd, String stm, String etm) {
        List<BsnRsvrrecorderfileR> result = baseMapper.getPhotoComparison(stcd, stm, etm);
        return result;
    }

    @Override
    public List<RsvrContinueAlermVo> getRsvrContinueAlerm(String adcd, String stm, String etm, String stTypes, String rvTypes) {
        List<RsvrContinueAlermVo> retList = new ArrayList<>();
        int adlevel = AdcdUtil.getAdLevel(adcd);
        String ad = adcd.substring(0, adlevel);
        List<RsvrContinueAlermVo> list = baseMapper.getRsvrContinueAlerm(ad, adlevel, stm, etm, stTypes, rvTypes);
        Map<String, List<RsvrContinueAlermVo>> stcdMap = list.stream().collect(Collectors.groupingBy(RsvrContinueAlermVo::getStcd));
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd"); //定义时间格式
        stcdMap.forEach((key, value) -> {
            Map<String, RsvrContinueAlermVo> tmMap = value.stream().collect(Collectors.toMap(RsvrContinueAlermVo::getTm, Function.identity()));
            LocalDate dateBegin = LocalDate.parse(stm.substring(0, 10), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate dateEnd = LocalDate.parse(etm.substring(0, 10), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate date_index = dateBegin;
            int max_total = 0;
            int current_max_total = 0;
            ArrayList<Integer> maxList = new ArrayList<>();
            while (date_index.compareTo(dateEnd) <= 0) {
                boolean isCurrent = tmMap.containsKey(date_index.format(dtf));
                boolean isNext = tmMap.containsKey(date_index.plusDays(1).format(dtf));
                if (isCurrent) {
                    max_total += 1;
                }
                if (date_index.isEqual(dateEnd)) {
                    current_max_total = max_total;
                }
                if (!isNext && max_total != 0) {
                    maxList.add(max_total);
                    max_total = 0;
                }
                date_index = date_index.plusDays(1);
            }
            //取Double的List聚合中的最大值
            Integer max = maxList.stream().max(Comparator.comparing(Integer::intValue)).get();
            RsvrContinueAlermVo vo = new RsvrContinueAlermVo();
            RsvrContinueAlermVo org = value.get(0);
            if (org != null) {
                vo.setAdcd(org.getAdcd());
                vo.setAdnm(org.getAdnm());
                vo.setStcd(org.getStcd());
                vo.setStnm(org.getStnm());
                vo.setXadcd(org.getXadcd());
                vo.setXadnm(org.getXadnm());
                vo.setOsflg(org.getOsflg());
                vo.setRsvrtp(org.getRsvrtp());
                vo.setFzdays(value.size());
                vo.setContfzdays(max);
                vo.setCurrentContfzdays(current_max_total);
            }
            retList.add(vo);
        });
        List<RsvrContinueAlermVo> sortList = retList.stream().sorted(Comparator.comparing(RsvrContinueAlermVo::getCurrentContfzdays, Comparator.reverseOrder())
                .thenComparing(RsvrContinueAlermVo::getRsvrtp, Comparator.reverseOrder()))
                .collect(Collectors.toList());

        return sortRsvrContinueAlermVo(sortList);
    }

    @Override
    public void exportRsvrContinueMaxAlermDays(RsvrContinueAlermQuery query, OutputStream output) {
        List<RsvrContinueAlermVo> list = getRsvrContinueAlerm(query.getAdcd(), query.getStm(), query.getEtm(), query.getStTypes(), query.getRvTypes());
        for (int i = 0; i < list.size(); i++) {
            // 序号
            list.get(i).setSortNo(i + 1);
            if (StringUtils.isEmpty(list.get(i).getOsflg())) {
                list.get(i).setOsflgs("未知");
            }
            if ("1".equals(list.get(i).getOsflg())) {
                list.get(i).setOsflgs("是");
            }
            if ("0".equals(list.get(i).getOsflg())) {
                list.get(i).setOsflgs("否");
            }
            if ("0".equals(list.get(i).getRsvrtp())) {
                list.get(i).setRsvrtps("塘坝");
            }
            if ("1".equals(list.get(i).getRsvrtp())) {
                list.get(i).setRsvrtps("小二");
            }
            if ("2".equals(list.get(i).getRsvrtp())) {
                list.get(i).setRsvrtps("小一");
            }
            if ("3".equals(list.get(i).getRsvrtp())) {
                list.get(i).setRsvrtps("中");
            }
            if ("4".equals(list.get(i).getRsvrtp()) || "5".equals(list.get(i).getRsvrtp())) {
                list.get(i).setRsvrtps("大");
            }
        }
        EasyExcel.write(output, RsvrContinueAlermVo.class)
                .excelType(ExcelTypeEnum.XLSX)
                .autoCloseStream(Boolean.FALSE)
                .sheet("水库连续最大超汛限统计")
                .doWrite(list);
    }

    /**
     * 重新排序 同一水库的人工和自动站显示在一起，显示为人工站在上
     *
     * @param list 水情列表数据集合
     * @return
     */
    private List<RsvrContinueAlermVo> sortRsvrContinueAlermVo(List<RsvrContinueAlermVo> list) {
        List<RsvrContinueAlermVo> newArray = new ArrayList<>();
        Map<String, List<RsvrContinueAlermVo>> stnmMap = new HashMap<>();
        //查询同一水库既有人工又有自动的站的对应关系
        List<BsnObjonlyB> bList = baseMapper.getBsnObjOnly();
        Map<String, String> objcdMap = new HashMap<>();
        for (BsnObjonlyB item : bList) {
            //存储测站对应统一标识id
            objcdMap.put(item.getObjcd(), item.getObjid());
        }
        //是否存在同一水库既有人工又有自动的
        boolean twoFlag = false;
        //存储同一水库既有人工又有自动的
        Map<String, List<RsvrContinueAlermVo>> stnmTwoMap = new HashMap<>();
        for (RsvrContinueAlermVo item : list) {
            //以相同标识 判定同一个水库的人工与自动站关联
            String stcd = item.getStcd();
            String key = "";
            if (objcdMap.containsKey(stcd)) {
                //同一个水库存在人工与自动站关联 以标识为主键
                key = objcdMap.get(stcd);
            } else {
                //同一个水库不存在人工与自动站关联 以测站为主键标识
                key = stcd;
            }
            if (stnmMap.containsKey(key)) {
                twoFlag = true;
                List<RsvrContinueAlermVo> stmList = stnmMap.get(key);
                //1、山洪改为自动，水文和人工报汛改为人工，默认显示人工，山洪自动站（5），人工为山洪人工站（3）和所有水文站
                //2、同一水库的人工和自动站显示在一起，显示为人工站在上
                // stadtp， 2代表人工 1,4 代表自动
                if (item.getStadtp() != null && (item.getStadtp().equals("4") || item.getStadtp().equals("1"))) {
                    //人工在前
                    stmList.add(0, item);
                } else {
                    //自动在后
                    stmList.add(item);
                }
                stnmTwoMap.put(key, stmList);
                stnmMap.put(key, stmList);
            } else {
                List<RsvrContinueAlermVo> stmList = new ArrayList<>();
                stmList.add(item);
                stnmMap.put(key, stmList);
                //同一水库的人工和自动站 只添加一次
                newArray.add(item);
            }

        }
        List<RsvrContinueAlermVo> result = new ArrayList<>();
        int sortNo = 1;
        if (twoFlag) {
            // 手动加入sortNo排序，将同一个水库存在人工与自动站关联情况合并为同一个排序值
            for (RsvrContinueAlermVo item : newArray) {
                //以相同标识 判定同一个水库的人工与自动站关联
                String stcd = item.getStcd();
                String key = "";
                if (objcdMap.containsKey(stcd)) {
                    //同一个水库存在人工与自动站关联 以标识为主键
                    key = objcdMap.get(stcd);
                } else {
                    //同一个水库不存在人工与自动站关联 以测站为主键标识
                    key = stcd;
                }
                if (stnmTwoMap.containsKey(key)) {
                    List<RsvrContinueAlermVo> stmList = stnmTwoMap.get(key);
                    if (stmList.size() > 0) {
                        stmList.get(0).setSortNo(sortNo);
                    }
                    //同一个水库的人工与自动站 一起插入
                    result.addAll(stmList);
                } else {
                    item.setSortNo(sortNo);
                    result.add(item);
                }
                sortNo++;
            }
            return result;
        } else {
            for (int i = 0; i < newArray.size(); i++) {
                newArray.get(i).setSortNo(i + 1);
            }
            return newArray;
        }

    }

    @Override
    public List<RsvrWarnByAdcdTmVo> selectWarnStatisticsByAdcdTm(String adcd, String stm, String etm) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        List<RsvrStWarnByAdcdTmVo> list = baseMapper.selectWarnStatisticsByAdcdTm(param);
        Map<String, List<RsvrStWarnByAdcdTmVo>> listMap = list.stream().collect(Collectors.groupingBy(RsvrStWarnByAdcdTmVo::getStcd));

        List<RsvrWarnByAdcdTmVo> result = new ArrayList<>();
        listMap.forEach((k, stcdList) -> {
            // 汛限水位
            BigDecimal fsltdz = stcdList.get(0).getFsltdz();
            // 最大动态水位
            double maxRz = stcdList.stream().mapToDouble(item -> item.getRz() == null ? 0 : item.getRz().doubleValue()).max().orElse(0);
            // 最大动态水位
            double minRz = stcdList.stream().mapToDouble(item -> item.getRz() == null ? 0 : item.getRz().doubleValue()).min().orElse(0);

//            Map<String, List<RsvrStWarnByAdcdTmVo>> collect1 = list.stream().collect(Collectors.groupingBy(item -> item.getTm().format(DateTimeFormatter.ISO_LOCAL_DATE)));
            List<RsvrStWarnByAdcdTmVo> collect = stcdList.stream().sorted(Comparator.comparing(RsvrStWarnByAdcdTmVo::getTm).reversed()).collect(Collectors.toList());

            RsvrWarnByAdcdTmVo riverWarnByAdcdTmVo = new RsvrWarnByAdcdTmVo();
            riverWarnByAdcdTmVo.setStcd(stcdList.get(0).getStcd());
            riverWarnByAdcdTmVo.setStnm(stcdList.get(0).getStnm());
            riverWarnByAdcdTmVo.setMaxRz(BigDecimal.valueOf(maxRz));
            riverWarnByAdcdTmVo.setMinRz(BigDecimal.valueOf(minRz));
            riverWarnByAdcdTmVo.setFsltdz(fsltdz);
            riverWarnByAdcdTmVo.setWarnWrzList(collect);
            result.add(riverWarnByAdcdTmVo);
        });
        return result;
    }

    @Override
    public List<StReglatfSqyb> getForecastLatestTime(String adcd, String bscd, String stm, String etm) {
        int adLevl = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("ad", adcd.substring(0, adLevl));
        param.put("level", adLevl);
        List<StReglatfSqyb> result = baseMapper.getForecastLatestTime(param);
        result = filterForecastLatestTimeByBscd(result, bscd);
        return result;
    }

    @Override
    public List<ReservoirRain> getContainRainList(String resCode) {
        return baseMapper.getContainRainList(resCode);
    }

    @Override
    public List<RsvrAvgDrnaVo> getRsvrAvgDrnaByStcd(String stcd, String stm, String etm) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("stcd", stcd);
        List<RsvrAvgDrnaVo> list = baseMapper.getRsvrAvgDrnaByStcd(param);
        Map<LocalDateTime, List<RsvrAvgDrnaVo>> tmMap = list.stream().collect(Collectors.groupingBy(RsvrAvgDrnaVo::getTm));
        List<RsvrAvgDrnaVo> retList = new ArrayList<>();
        tmMap.forEach((k, v) -> {
            BigDecimal sum_w = BigDecimal.valueOf(v.stream().mapToDouble(i -> i.getW().doubleValue()).sum());
            BigDecimal avg = BigDecimal.ZERO;
            for (RsvrAvgDrnaVo item : v) {
                avg = avg.add(item.getDrp().multiply(item.getW().divide(sum_w, 2, RoundingMode.HALF_UP)));
            }
            RsvrAvgDrnaVo rsvrAvgDrnaVo = new RsvrAvgDrnaVo();
            rsvrAvgDrnaVo.setStcd(stcd);
            rsvrAvgDrnaVo.setTm(k);
            rsvrAvgDrnaVo.setDrp(avg);
            retList.add(rsvrAvgDrnaVo);
        });
        return retList.stream().sorted(Comparator.comparing(RsvrAvgDrnaVo::getTm))
                .collect(Collectors.toList());
    }

    @Override
    public List<RsvrRainList> getStRsvrRainList(String basCode, String stm, String etm, String stTypes) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("basCode", basCode);
        param.put("tp", "1");
        param.put("stTypes", stTypes);

//        List<RsvrRainList> list = baseMapper.getStRsvrRainList(param);
//        List<RsvrRainChildList> clist = baseMapper.getStRsvrRainChildList(param);
//        param.put("tp", "2");
//        List<RsvrRainList> listf = baseMapper.getStRsvrRainList(param);
//        Map<String, RsvrRainList> collectf = listf.stream().collect(Collectors.toMap(RsvrRainList::getResCode, Function.identity()));
//        List<RsvrRainChildList> clistf = baseMapper.getStRsvrRainChildList(param);
//        Map<String, RsvrRainChildList> ccollectf = clistf.stream().collect(Collectors.toMap(i -> i.getResCode() + "-" + i.getStcd(), Function.identity()));
//        param.put("tp", "3");
//        List<RsvrRainList> listl = baseMapper.getStRsvrRainList(param);
//        Map<String, RsvrRainList> collectl = listl.stream().collect(Collectors.toMap(RsvrRainList::getResCode, Function.identity()));
//        List<RsvrRainChildList> clistl = baseMapper.getStRsvrRainChildList(param);
//        Map<String, RsvrRainChildList> ccollectl = clistl.stream().collect(Collectors.toMap(i -> i.getResCode() + "-" + i.getStcd(), Function.identity()));
//        list.forEach(i -> {
//            if (collectf.containsKey(i.getResCode())) {
//                i.setAvgFdrps(collectf.get(i.getResCode()).getAvgDrps());
//            }
//            if (collectl.containsKey(i.getResCode())) {
//                i.setAvgLdrps(collectl.get(i.getResCode()).getAvgDrps());
//            }
//        });
//
//        clist.forEach(i -> {
//            if (ccollectf.containsKey(i.getResCode() + "-" + i.getStcd())) {
//                i.setFdrps(ccollectf.get(i.getResCode() + "-" + i.getStcd()).getFdrps());
//            }
//            if (ccollectl.containsKey(i.getResCode() + "-" + i.getStcd())) {
//                i.setLdrps(ccollectl.get(i.getResCode() + "-" + i.getStcd()).getLdrps());
//            }
//        });
//        Map<String, List<RsvrRainChildList>> childMap = clist.stream().collect(Collectors.groupingBy(RsvrRainChildList::getResCode));
//        list.forEach(i -> {
//            if (childMap.containsKey(i.getResCode())) {
//                i.setChildren(childMap.get(i.getResCode()));
//            }
//        });

        // TRK@20240710: 水库降雨查询优化
        List<RsvrRainList> list = new LinkedList<>();

        // 测站权重
        List<RsvrRainList> rsvrList = baseMapper.getStRsvrRainBaseList(param);
        Map<String, List<RsvrRainList>> rsvrMap = new HashMap<>();
        Map<String, String> tempMap = new HashMap<>();
        rsvrList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getTempCode())) {
                if (tempMap.containsKey(item.getTempCode()) && !tempMap.get(item.getTempCode()).equals(item.getResCode())) {
                    return;
                }
                tempMap.put(item.getTempCode(), item.getResCode());
            }

            List<RsvrRainList> stList = rsvrMap.get(item.getResCode());
            if (CollectionUtils.isEmpty(stList)) {
                RsvrRainList rsvrRain = new RsvrRainList();
                BeanUtils.copyProperties(item, rsvrRain);
                // 清空来水量&权重系数
                rsvrRain.setW(null);
                list.add(rsvrRain);

                stList = new LinkedList<>();
                rsvrMap.put(item.getResCode(), stList);
            }
            stList.add(item);
        });

        List<Rain> rainList = rainService.getRainByConditionAll(stm, etm, Collections.emptyList(), "220000000000000", "", null, "0-99999", "0", null, null, null, null);
        Map<String, Rain> rainMap = rainList.stream().collect(Collectors.toMap(Rain::getStcd, Function.identity(), (o, n) -> n));

        // 雨量数据
        list.forEach(item -> {
            List<RsvrRainList> stList = rsvrMap.get(item.getResCode());
            if (CollectionUtils.isEmpty(stList)) {
                item.setAvgDrps(BigDecimal.valueOf(0));
                item.setAvgLdrps(BigDecimal.valueOf(0));
                item.setAvgF3drps(BigDecimal.valueOf(0));
                item.setAvgF24drps(BigDecimal.valueOf(0));
                item.setChildren(Collections.emptyList());
                return;
            }

            double w = 0;
            double maxDrps = 0;
            double avgDrps = 0;
            double avgLdrps = 0;
            double avgFdrps = 0;
            double avgF24drps = 0;
            // @jiangjy 20240710: 按照张雪要求未来3小时和未来24小时计算方式和中小河流一致
            double avgFdrps_w = 0;

            List<Rain> children = new LinkedList<>();
            for (int i = 0; i < stList.size(); i++) {
                RsvrRainList rsvrRain = stList.get(i);
                if (rsvrRain.getW() == null || rsvrRain.getW() <= 0) {
                    continue;
                }
                w = w + rsvrRain.getW();

                Rain rain = rainMap.get(rsvrRain.getStcd());
                if (rain == null) {
                    continue;
                }

                // drps
                try {
                    double drps = Double.parseDouble(rain.getDrps());
                    if (maxDrps < drps) {
                        maxDrps = drps;
                    }
                    avgDrps = avgDrps + drps * rsvrRain.getW();
                } catch (Exception ex) {
                    log.trace(ex.getMessage());
                }
                // ldrps
                avgLdrps = avgLdrps + rain.getRainb24() * rsvrRain.getW();

                if (Double.valueOf(rain.getDrps()) > 0) {
                    avgFdrps += rain.getRain3(); // f3drps
                    avgF24drps += rain.getRain24(); // F24drps
                    avgFdrps_w += 1;
                }

                // 测站降雨
                children.add(rain);
            }

            item.setMaxDrps(BigDecimal.valueOf(maxDrps));
            if (w > 0) {
                item.setAvgDrps(BigDecimal.valueOf(avgDrps / w));
                item.setAvgLdrps(BigDecimal.valueOf(avgLdrps / w));
            } else {
                item.setAvgDrps(BigDecimal.valueOf(0));
                item.setAvgLdrps(BigDecimal.valueOf(0));
            }
            item.setAvgF3drps(avgFdrps_w > 0 ? BigDecimal.valueOf(avgFdrps / avgFdrps_w) : BigDecimal.valueOf(0));
            item.setAvgF24drps(avgFdrps_w > 0 ? BigDecimal.valueOf(avgF24drps / avgFdrps_w) : BigDecimal.valueOf(0));

            // 测站降雨
            try {
                Collections.sort(children, (o1, o2) -> {
                    int sort = 0;
                    if (o1.getDrps() != null && o2.getDrps() != null) {
                        try {
                            Double drp1 = Double.parseDouble(o1.getDrps());
                            Double drp2 = Double.parseDouble(o2.getDrps());
                            sort = drp2.compareTo(drp1);
                        } catch (Exception ex) {

                        }
                    } else if (o1.getDrps() != null) {
                        return -1;
                    } else if (o2.getDrps() != null) {
                        return 1;
                    }

                    if (sort == 0) {
                        if (o1.getAdcd() != null && o2.getAdcd() != null) {
                            sort = o2.getAdcd().compareTo(o1.getAdcd());
                        } else if (o1.getAdcd() != null) {
                            return -1;
                        } else if (o2.getAdcd() != null) {
                            return 1;
                        }
                    }
                    if (sort == 0) {
                        return o1.getStcd().compareTo(o2.getStcd());
                    }
                    return sort;
                });
                item.setChildren(children);
            } catch (Exception ex) {
                item.setChildren(Collections.emptyList());
            }
        });

        // 排序
        Collections.sort(list, (o1, o2) -> {
            int sort = 0;
            if (o1.getAvgDrps() != null && o2.getAvgDrps() != null) {
                sort = o2.getAvgDrps().compareTo(o1.getAvgDrps());
            } else if (o1.getAvgDrps() != null) {
                return -1;
            } else if (o2.getAvgDrps() != null) {
                return 1;
            }

            if (sort == 0) {
                sort = o1.getEngScal().compareTo(o2.getEngScal());
            }
            if (sort == 0) {
                sort = o1.getResCode().compareTo(o2.getResCode());
            }
            return sort;
        });

        // 径流系数
        List<RsvrRainList> rcList = baseMapper.getStRsvrRcList(param);
        if (CollectionUtils.isNotEmpty(rcList)) {
            Map<String, RsvrRainList> rcMap = rcList.stream().collect(Collectors.toMap(RsvrRainList::getResCode, Function.identity()));
            list.forEach(item -> {
                RsvrRainList itemRc = rcMap.get(item.getResCode());
                if (itemRc == null || itemRc.getRc() <= 0) {
                    return;
                }
                item.setRc(itemRc.getRc());

                if (item.getAvgDrps() == null || item.getWatShedArea() == null) {
                    return;
                }
                Double avgDrps = Double.parseDouble(item.getAvgDrps().toString());
                // 百万方
                item.setW(avgDrps * item.getWatShedArea() * itemRc.getRc() / 1000);
            });
        }
        return list;
    }

    @Override
    public List<RsvrRainChildList> getRsvrRainChildList(String resCode, String stm, String etm, String stTypes) {
        Map<String, Object> param = new HashMap<>();
        param.put("stm", stm);
        param.put("etm", etm);
        param.put("resCode", resCode);
        param.put("tp", "1");
        param.put("stTypes", stTypes);
        List<RsvrRainChildList> clist = baseMapper.getStRsvrRainChildList(param);
        param.put("tp", "2");
        List<RsvrRainChildList> clistf = baseMapper.getStRsvrRainChildList(param);
        Map<String, RsvrRainChildList> ccollectf = clistf.stream().collect(Collectors.toMap(i -> i.getResCode() + "-" + i.getStcd(), Function.identity()));
        param.put("tp", "3");
        List<RsvrRainChildList> clistl = baseMapper.getStRsvrRainChildList(param);
        Map<String, RsvrRainChildList> ccollectl = clistl.stream().collect(Collectors.toMap(i -> i.getResCode() + "-" + i.getStcd(), Function.identity()));
        clist.forEach(i -> {
            if (ccollectf.containsKey(i.getResCode() + "-" + i.getStcd())) {
                i.setFdrps(ccollectf.get(i.getResCode() + "-" + i.getStcd()).getFdrps());
            }
            if (ccollectl.containsKey(i.getResCode() + "-" + i.getStcd())) {
                i.setLdrps(ccollectl.get(i.getResCode() + "-" + i.getStcd()).getLdrps());
            }
        });
        return clist;
    }

    private List<StReglatfSqyb> filterForecastLatestTimeByBscd(List<StReglatfSqyb> stList, String bscd) {
        if (bscd != null && !bscd.equals("")) {
            // 根据流域名称查询所有的流域对应的测站
            List<BsnBasStBTo> bsList = rainDao.getRiverTree(bscd);
            List<String> stcdList = bsList.stream().map(BsnBasStBTo::getStcd).collect(Collectors.toList());
            List<StReglatfSqyb> intersection = stList.stream().filter(item -> stcdList.contains(item.getStcd())).collect(Collectors.toList());
            return intersection;
        } else {
            return stList;
        }
    }

    @Override
    public void exportStRsvrRainList(String adcd, String stm, String etm, List<String> stTypes) {
        List<RsvrRainList> list = this.getStRsvrRainList(adcd, stm, etm, String.join(",", stTypes));
        list.forEach(i -> {
            i.setAvgDrps(i.getAvgDrps() == null ? BigDecimal.ZERO : i.getAvgDrps().setScale(1, RoundingMode.HALF_UP));
            i.setAvgF3drps(i.getAvgF3drps() == null ? BigDecimal.ZERO : i.getAvgF3drps().setScale(1, RoundingMode.HALF_UP));
            i.setAvgLdrps(i.getAvgLdrps() == null ? BigDecimal.ZERO : i.getAvgLdrps().setScale(1, RoundingMode.HALF_UP));
        });
        LocalDateTime etmB24 = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");
        DateTimeFormatter formatterWithoutSeconds = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00");
        HashMap<String, String> hiddenCollumMap = new HashMap();
        if (etm != null) {
            try {
                etmB24 = LocalDateTime.parse(etm, formatter);
            } catch (Exception e) {
                // 如果解析失败，尝试使用不包含秒的格式解析
                etmB24 = LocalDateTime.parse(etm, formatterWithoutSeconds);
            }
            long hoursDiff = Duration.between(etmB24, LocalDateTime.now()).toHours();
            if (!(Math.abs(hoursDiff) < 1.1)) {
                hiddenCollumMap.put("未来3h（mm）", "avgF3drps");
            }
        }
        ExcelExportUtil.executeWithHiddenColumn(list, "综合监视雨情水库降雨", hiddenCollumMap);
    }

    @Override
    public List<RsvrNearWarnVo> selectNearWarnTemp(RsvrNearWarnQuery query) {
        return baseMapper.selectNearWarnTemp(query.toQueryParam());
    }

    @Override
    public IPage<RsvrZuoZhanTuVo> getRsvrZuoZhanTuList(RsvrZuoZhanTuQuery query) {
        return baseMapper.getRsvrZuoZhanTuList(query.toPage(), query.toQueryParam());
    }

    @Override
    public RsvrZuoZhanTuVo getRsvrZuoZhanTu(RsvrZuoZhanTuQuery query) {
        return baseMapper.getRsvrZuoZhanTu(query.toQueryParam());
    }

}
