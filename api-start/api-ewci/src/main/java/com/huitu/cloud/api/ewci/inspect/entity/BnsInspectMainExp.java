package com.huitu.cloud.api.ewci.inspect.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运行维护经费
 */
@Data
@ApiModel(value = "运行维护经费")
public class BnsInspectMainExp extends BnsInspectReportForm {

    @ApiModelProperty(value = "运行维护经费是否足额落实(0否 1是)")
    private String iorp;

    @ApiModelProperty(value = "检查类型名称")
    private String inspectName;
}
