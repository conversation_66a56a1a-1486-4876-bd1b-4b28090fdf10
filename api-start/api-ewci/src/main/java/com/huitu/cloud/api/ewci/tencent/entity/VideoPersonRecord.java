package com.huitu.cloud.api.ewci.tencent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会议人员记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@TableName("BSN_VIDEO_PERSON_RECORD")
@ApiModel("会议人员记录表")
public class VideoPersonRecord extends Model<VideoPersonRecord> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "记录id")
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    @ApiModelProperty(value = "会议房间号")
    private String roomId;

    @ApiModelProperty(value = "参会人员id")
    private String memberId;

    @ApiModelProperty(value = "参会人员姓名")
    private String memberName;

    @ApiModelProperty(value = "进入房间时间")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "离开房间时间")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "是否在房间标识 0：在房间  1：离开房间")
    private Integer sign;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "部门编码")
    private String deptid;

    @ApiModelProperty(value = "部门名称")
    private String deptnm;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public LocalDateTime getEnterTime() {
        return enterTime;
    }

    public void setEnterTime(LocalDateTime enterTime) {
        this.enterTime = enterTime;
    }

    public LocalDateTime getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(LocalDateTime leaveTime) {
        this.leaveTime = leaveTime;
    }

    public Integer getSign() {
        return sign;
    }

    public void setSign(Integer sign) {
        this.sign = sign;
    }
    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }


    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }

    public String getDeptnm() {
        return deptnm;
    }

    public void setDeptnm(String deptnm) {
        this.deptnm = deptnm;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    @Override
    public String toString() {
        return "VideoPersonRecord{" +
                "id='" + id + '\'' +
                ", roomId='" + roomId + '\'' +
                ", memberId='" + memberId + '\'' +
                ", memberName='" + memberName + '\'' +
                ", enterTime=" + enterTime +
                ", leaveTime=" + leaveTime +
                ", sign=" + sign +
                ", adcd='" + adcd + '\'' +
                ", adnm='" + adnm + '\'' +
                ", deptid='" + deptid + '\'' +
                ", deptnm='" + deptnm + '\'' +
                '}';
    }
}
