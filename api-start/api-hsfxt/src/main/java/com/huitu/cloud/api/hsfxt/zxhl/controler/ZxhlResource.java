package com.huitu.cloud.api.hsfxt.zxhl.controler;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.hsfxt.zxhl.entity.ZxhlShehuijingji;
import com.huitu.cloud.api.hsfxt.zxhl.entity.ZxhlTree;
import com.huitu.cloud.api.hsfxt.zxhl.entity.ZxhlTunXiangqing;
import com.huitu.cloud.api.hsfxt.zxhl.entity.ZxhlXiangQing;
import com.huitu.cloud.api.hsfxt.zxhl.service.ZxhlService;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 风险图-中小河流 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */

@RestController
@Api(tags = "风险图-中小河流接口")
@Validated
@RequestMapping("/api/hsfxt/zxhl")
public class ZxhlResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "6240fd49-3343-4ccf-879c-4a8c433baa35";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    ZxhlService zxhlService;

    @ApiOperation(value = "中小河流列表查询", notes = "中小河流列表查询")
    @GetMapping(value = "get-zxhl-tree-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合（不要洪水量级）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "最小风险政区单位", dataType = "String"),
            @ApiImplicitParam(name = "hl", value = "洪水量级", dataType = "String"),
            @ApiImplicitParam(name = "bzqid", value = "编制区编码", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ZxhlTree>>> getZxhlTreeInfo(@SqlInjection @RequestParam String key, @RequestParam String name, @RequestParam String hl, @RequestParam String bzqid) throws Exception {
        List<ZxhlTree> list = zxhlService.getZxhlTreeInfo(key, name, hl, bzqid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "中小河流社会经济统计查询", notes = "中小河流社会经济统计查询")
    @GetMapping(value = "get-zxhl-shjj-tree-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ZxhlShehuijingji>>> getZxhlShjjTreeInfo(@SqlInjection @RequestParam String key) throws Exception {
        List<ZxhlShehuijingji> list = zxhlService.getZxhlShjjTreeInfo(key);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "中小河流列表详情查询", notes = "中小河流列表详情查询")
    @GetMapping(value = "get-zxhl-shjj-detail-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bzqid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<ZxhlXiangQing>> getZxhlShjjDetailInfo(@RequestParam String key, @RequestParam String adcd, @RequestParam String bzqid) throws Exception {
        ZxhlXiangQing result = zxhlService.getZxhlShjjDetailInfo(key,adcd,bzqid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "中小河流列表（屯）详情查询", notes = "中小河流列表（屯）详情查询")
    @GetMapping(value = "get-zxhl-tun-shjj-detail-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合（不要洪水量级）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "hl", value = "洪水量级", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bzqid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<ZxhlTunXiangqing>> getZxhlTunShjjDetailInfo(@RequestParam String key, @RequestParam String hl, @RequestParam String adcd, @RequestParam String bzqid) throws Exception {
        ZxhlTunXiangqing result = zxhlService.getZxhlTunShjjDetailInfo(key,hl,adcd,bzqid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

}
