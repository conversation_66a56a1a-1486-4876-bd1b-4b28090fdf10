package com.huitu.cloud.api.hyst.survey.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 小水电概况VO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-30
 */
public class SurveyVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水电站统计代码")
    @TableId(value = "HYST_CODE")
    private String hystCode;

    @ApiModelProperty(value = "水电站名称")
    @TableField("HYST_NAME")
    private String hystName;

    @ApiModelProperty(value = "政区编码")
    @TableId(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    @TableField("ADNM")
    private String adnm;

    @ApiModelProperty(value = "水电站类型，1为小型水电站、2为中型水电站、3为大型水电站")
    @TableField("HYST_TP")
    private String hystTp;

    @ApiModelProperty(value = "在线状态")
    @TableField("ECO_ONLINE")
    private String ecoOnline;

    @ApiModelProperty(value = "达标状态")
    @TableField("ECO_FLOW")
    private String ecoFlow;

    @ApiModelProperty(value = "是否考核")
    @TableField("ASS")
    private String ass;

    @ApiModelProperty(value = "考核周期，按天（0）、月（1）、年（2），三站考核周期")
    @TableField("ASS_PER")
    private String assPer;

    @ApiModelProperty(value = "瞬时总流量(m³/s)")
    @TableField("ECOFLOWNUM")
    private BigDecimal ecoFlowNum;

    @ApiModelProperty(value = "核准生态流量，单位：m³/s")
    @TableField("APP_ECO_FLOW")
    private BigDecimal appEcoFlow;

    @ApiModelProperty(value = "记录时间")
    @TableField("REC_TIME")
    private LocalDateTime recTime;

    @ApiModelProperty(value = "采集时间")
    @TableField("ACQTIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acqTime;

    public String getHystCode() {
        return hystCode;
    }

    public void setHystCode(String hystCode) {
        this.hystCode = hystCode;
    }

    public String getHystName() {
        return hystName;
    }

    public void setHystName(String hystName) {
        this.hystName = hystName;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getEcoOnline() {
        return ecoOnline;
    }

    public void setEcoOnline(String ecoOnline) {
        this.ecoOnline = ecoOnline;
    }

    public String getEcoFlow() {
        return ecoFlow;
    }

    public void setEcoFlow(String ecoFlow) {
        this.ecoFlow = ecoFlow;
    }

    public String getAss() {
        return ass;
    }

    public void setAss(String ass) {
        this.ass = ass;
    }

    public BigDecimal getEcoFlowNum() {
        return ecoFlowNum;
    }

    public void setEcoFlowNum(BigDecimal ecoFlowNum) {
        this.ecoFlowNum = ecoFlowNum;
    }

    public BigDecimal getAppEcoFlow() {
        return appEcoFlow;
    }

    public void setAppEcoFlow(BigDecimal appEcoFlow) {
        this.appEcoFlow = appEcoFlow;
    }

    public LocalDateTime getRecTime() {
        return recTime;
    }

    public void setRecTime(LocalDateTime recTime) {
        this.recTime = recTime;
    }

    public LocalDateTime getAcqTime() {
        return acqTime;
    }

    public void setAcqTime(LocalDateTime acqTime) {
        this.acqTime = acqTime;
    }

    public String getHystTp() {
        return hystTp;
    }

    public void setHystTp(String hystTp) {
        this.hystTp = hystTp;
    }

    public String getAssPer() {
        return assPer;
    }

    public void setAssPer(String assPer) {
        this.assPer = assPer;
    }
}
