package com.huitu.cloud.api.syq.rsvr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-05-14
 */
@ApiModel(value="作战图水库信息")
public class RsvrZuoZhanTuVo extends Model<RsvrZuoZhanTuVo> {

    @ApiModelProperty(value = "测站编码")
    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    @TableField("STNM")
    private String stnm;

    @ApiModelProperty(value = "地址")
    @TableField("XADNM")
    private String xadnm;

    @ApiModelProperty(value = "政区编码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "水库类型")
    @TableField("RSVRTP")
    private String rsvrtp;

    @ApiModelProperty(value = "时间")
    @TableId(value = "TM", type = IdType.NONE)
    private LocalDateTime tm;

    @ApiModelProperty(value = "库上水位")
    @TableField("RZ")
    private String rz;

    @ApiModelProperty(value = "库水水势")
    @TableField("RWPTN")
    private String rwptn;

    @ApiModelProperty(value = "设计洪水位")
    @TableField("DSFLZ")
    private BigDecimal dsflz;

    @ApiModelProperty(value = "汛限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @ApiModelProperty(value = "超讯限水位")
    @TableField("RZFSLTDZ")
    private BigDecimal rzfsltdz;

    @ApiModelProperty(value = "正常高水位")
    @TableField("NORMZ")
    private BigDecimal normz;

    @ApiModelProperty(value = "超正常高水位")
    @TableField("RZNORMZ")
    private BigDecimal rznormz;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getRsvrtp() {
        return rsvrtp;
    }

    public void setRsvrtp(String rsvrtp) {
        this.rsvrtp = rsvrtp;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public String getRz() {
        return rz;
    }

    public void setRz(String rz) {
        this.rz = rz;
    }

    public String getRwptn() {
        return rwptn;
    }

    public void setRwptn(String rwptn) {
        this.rwptn = rwptn;
    }

    public BigDecimal getDsflz() {
        return dsflz;
    }

    public void setDsflz(BigDecimal dsflz) {
        this.dsflz = dsflz;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public BigDecimal getNormz() {
        return normz;
    }

    public void setNormz(BigDecimal normz) {
        this.normz = normz;
    }

    public BigDecimal getRznormz() {
        return rznormz;
    }

    public void setRznormz(BigDecimal rznormz) {
        this.rznormz = rznormz;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }
}
