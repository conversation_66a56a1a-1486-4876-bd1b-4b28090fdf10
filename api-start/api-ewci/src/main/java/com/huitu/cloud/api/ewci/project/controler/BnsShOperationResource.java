package com.huitu.cloud.api.ewci.project.controler;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.project.entity.BnsShOperation;
import com.huitu.cloud.api.ewci.project.entity.BnsShProject;
import com.huitu.cloud.api.ewci.project.service.BnsShOperationService;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 山洪灾害项目运行效益情况和建设进度情况统计 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-14
 */

@RestController
@Api(tags = "山洪灾害运行效益情况和建设进度情况统计")
@Validated
@RequestMapping("/api/ewci/project")
public class BnsShOperationResource extends AbstractApiResource implements ApiResource {

    @Override
    public String getUuid() {
        return "00000000-0000-0000-0000-000000000000";
    }
    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    private BnsShOperationService baseService;

    @ApiOperation(value = "添加或修改运行效益情况",notes="添加或修改运行效益情况")
    @PostMapping(value = "yxjsindex-benefit-addorupdate")
    public ResponseEntity<SuccessResponse<String>> updateBsnOperationB(@RequestBody BnsShOperation bnsShOperation) throws Exception {
        Boolean flag = baseService.addorupdate(bnsShOperation);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "运行效益情况汇总",notes="根据主键修改信息")
    @PostMapping(value = "yxjsindex-benefit-summary")
    public ResponseEntity<SuccessResponse<String>> summaryBsnOperationB() throws Exception {
        boolean flag = baseService.summary();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "查询山洪灾害项目运行效益情况列表", notes = "查询山洪灾害项目运行效益情况列表")
    @GetMapping(value = "yxjsindex-benefit-data")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bno", value = "批次", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "查询条件", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "year", value = "年度", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<BnsShOperation>>> getBenefitData (@RequestParam String adcd, @SqlInjection @RequestParam String stm, @SqlInjection @RequestParam String etm, @RequestParam String bno, @RequestParam Integer type, @RequestParam String year) {
        List<BnsShOperation> list = baseService.getBenefitData(adcd, stm, etm,bno, type,year);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询山洪灾害项目运行效益距离当前时间最近的一条记录", notes = "查询山洪灾害项目运行效益距离当前时间最近的一条记录")
    @GetMapping(value = "yxjsindex-benefit-currentTimeData")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<BnsShOperation>>> getCurrentTimeData (@RequestParam String adcd) {
        List<BnsShOperation> list = baseService.getCurrentTimeData(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询山洪灾害项目运行效益批次列表", notes = "查询批次列表")
    @GetMapping(value = "yxjsindex-benefit-bnolist")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<BnsShOperation>>> getBnoList (@RequestParam String adcd) {
        List<BnsShOperation> list = baseService.getBnoList(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询建设（运行维护）进度情况列表", notes = "查询山洪灾害项目运行效益情况列表")
    @GetMapping(value = "yxjsindex-roject-data")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bno", value = "批次", required = true, dataType = "String"),
            @ApiImplicitParam(name = "stm", value = "开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "etm", value = "结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "year", value = "年度", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "查询条件", required = true, dataType = "Integer")
    })
    public ResponseEntity<SuccessResponse<List<BnsShProject>>> getrojectData (@RequestParam String adcd,@SqlInjection @RequestParam String stm, @SqlInjection @RequestParam String etm,  @RequestParam String bno,@RequestParam String year,@RequestParam Integer type) {
        List<BnsShProject> list = baseService.getrojectData(adcd, stm, etm,bno,year,type);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "查询建设（运行维护）进度情况批次列表", notes = "查询批次列表")
    @GetMapping(value = "yxjsindex-roject-rojectlist")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<BnsShProject>>> getrojectList (@RequestParam String adcd) {
        List<BnsShProject> list = baseService.getrojectList(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "添加或修改建设（运行维护）进度情况情况",notes="添加或建设（运行维护）进度情况情况")
    @PostMapping(value = "yxjsindex-roject-addrojectdate")
    public ResponseEntity<SuccessResponse<String>> updateRojectB(@RequestBody BnsShProject bnsShProject) throws Exception {
        Boolean flag = baseService.updateRojectBRoject(bnsShProject);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", flag));
    }

    @ApiOperation(value = "运行建设（运行维护）进度情况汇总",notes="建设（运行维护）进度情况")
    @PostMapping(value = "yxjsindex-roject-summary")
    public ResponseEntity<SuccessResponse<String>> summaryRojectB() throws Exception {
        Boolean flag = baseService.summaryRojectB();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", "OK"));
    }

    @ApiOperation(value = "查询运行建设（运行维护）进度情况当前时间最近的一条记录", notes = "查询运行建设（运行维护）进度情况当前时间最近的一条记录")
    @GetMapping(value = "yxjsindex-roject-currentOneData")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
    })
    public ResponseEntity<SuccessResponse<List<BnsShProject>>> getCurrentOneData (@RequestParam String adcd) {
        List<BnsShProject> list = baseService.getCurrentOneData(adcd);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }


}























