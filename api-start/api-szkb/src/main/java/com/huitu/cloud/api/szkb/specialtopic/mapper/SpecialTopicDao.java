package com.huitu.cloud.api.szkb.specialtopic.mapper;

import com.huitu.cloud.api.szkb.specialtopic.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 专题信息管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-22
 */
public interface SpecialTopicDao {
    /**
     * 查询专题类别信息
     *
     * @param
     * @return
     */
    List<BsnSpecialtopicTypeB> getSpecialTopicType();

    /**
     * 查询微应用业务分类信息
     *
     * @param
     * @return
     */
    List<BsnApptypeB> getAppTypeInfo();

    /**
     * 查询微应用业务分类信息
     *
     * @param typeId 专题类别编码
     * @param bsnCd  业务分类编码
     * @return
     */
    List<BsnTopicmcappB> getMcappinfoInfo(@Param("typeId") String typeId, @Param("bsnCd") String bsnCd);

    /**
     * 添加专题信息
     *
     * @param query 专题信息实体类
     * @return
     */
    void addSpecialTopicInfo(BsnSpecialtopicB query);

    /**
     * 更新专题信息
     *
     * @param query 专题信息实体类
     * @return
     */
    void updateSpecialTopicInfo(QueryBsnSpecialtopicB query);

    /**
     * 批量增加专题微应用关系
     *
     * @param list 专题微应用关系类
     * @return
     */
    void batchInsertTopicMcappInfo(@Param("list") List<QuerySpecialtopicMcappRel> list);

    /**
     * 删除专题对应的微应用数据
     *
     * @param topicid 专题编码
     * @return
     */
    void delTopicMcapp(@Param("topicid") String topicid);

    /**
     * 查询专题信息
     *
     * @param typeId 专题类别编码
     * @param isDefaultView 是否在门户入口默认展示该专题
     * @return
     */
    List<BsnSpecialtopicB> getSpecialTopicInfo(@Param("typeId") String typeId, @Param("isDefaultView") String isDefaultView);

    /**
     * 按id查询专题信息
     *
     * @param topicid 专题编码
     * @return
     */
    SpecialtopicVo getSpecialTopicById(@Param("topicid") String topicid);

    /**
     * 按专题id查询专题配置信息
     *
     * @param topicid 专题编码
     * @return
     */
    List<BsnSpecialtopicMcappRel> getSpecialtopicMcappRel(@Param("topicid") String topicid);

    /**
     * 专题删除
     *
     * @param topicid 专题编码
     * @return
     */
    void delSpecialTopicById(@Param("topicid") String topicid);
}
