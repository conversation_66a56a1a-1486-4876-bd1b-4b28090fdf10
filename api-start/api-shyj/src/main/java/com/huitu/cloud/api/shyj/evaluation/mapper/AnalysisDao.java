package com.huitu.cloud.api.shyj.evaluation.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 分析评价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
public interface AnalysisDao {
    /**
     * 分页查设计暴雨信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaDesntb> getDesntbList(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询设计暴雨流域总数
     *
     * @param param 查询参数
     * @return
     */
    int getDesntbSum(@Param("map") Map<String, Object> param);

    /**
     * 分页查小流域汇流时间设计暴雨时程分配表信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaHlsjtb> getHlsjtb(Page page, @Param("map") Map<String, Object> param);
    /**
     * 查询小流域汇流时间设计暴雨时程信息流域统计
     *
     * @param param 查询参数
     * @return
     */
    int getHlsjtbSum(@Param("map") Map<String, Object> param);
    /**
     * 分页查控制断面设计洪水信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaSdtdtb> getSdtdtb(Page page, @Param("map") Map<String, Object> param);
    /**
     * 控制断面设计洪水防治区统计
     *
     * @param param 查询参数
     * @return
     */
    int getSdtdtbSum(@Param("map") Map<String, Object> param);

    /**
     * 控制断面设计洪水小流域统计
     *
     * @param param 查询参数
     * @return
     */
    int getSdtdtbbasinSum(@Param("map") Map<String, Object> param);
    /**
     * 分页查控制断面水位-流量-人口关系表信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaSwllrktb> getSwllrktb(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询控制断面水位-流量-人口关系表统计政区
     *
     * @param param 查询参数
     * @return
     */
    int getDistrictSum( @Param("map") Map<String, Object> param);
    /**
     * 查询控制断面水位-流量-人口关系表统计小流域
     *
     * @param param 查询参数
     * @return
     */
    int getBasinSum( @Param("map") Map<String, Object> param);
    /**
     * 分页查防洪现状评价信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaNowfhtb> getNowfhtb(Page page, @Param("map") Map<String, Object> param);
    /**
     * 统计防洪现状评价表-政区
     *
     * @param param 查询参数
     * @return
     */
    int getNowfhtbDistrictSum(@Param("map") Map<String, Object> param);
    /**
     * 统计防洪现状评价表-小流域
     *
     * @param param 查询参数
     * @return
     */
    int getNowfhtbBasinSum( @Param("map") Map<String, Object> param);

    /**
     * 分页查临界雨量经验估计法成果表信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaJygstb> getJygstb(Page page, @Param("map") Map<String, Object> param);
    /**
     * 分页查临界雨量降雨分析法成果表信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaYjcjtb> getYjcjtb(Page page, @Param("map") Map<String, Object> param);
    /**
     * 分页查临界雨量模型分析法信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaFxcgtb> getFxcgtb(Page page, @Param("map") Map<String, Object> param);
    /**
     * 查询临界雨量模型分析法信息防治区统计
     *
     * @param param 查询参数
     * @return
     */
    int getFxcgtbDistrictSum(@Param("map") Map<String, Object> param);

    /**
     * 分页查预警指标时段雨量信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaDfwrule> getDfwrule(Page page, @Param("map") Map<String, Object> param);

    /**
     * 统计预警指标时段雨量表防治区
     *
     * @param param 查询参数
     * @return
     */
    int getDfwruleDistrictSum(@Param("map") Map<String, Object> param);

    /**
     * 分页查预警指标综合雨量信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaYjicr> getYjicr(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询预警指标综合雨量信息防治区统计
     *
     * @param param 查询参数
     * @return
     */
    int getYjicrSum(@Param("map") Map<String, Object> param);

    /**
     * 分页查预警指标水位信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<IaWlwrule> getWlwrule(Page page, @Param("map") Map<String, Object> param);
    /**
     * 查询预警指标水位信息防治区统计
     *
     * @param param 查询参数
     * @return
     */
    int getWlwruleSum(@Param("map") Map<String, Object> param);

    /**
     * 分页查计算单元(防灾对象)信息
     * @param page 查询参数
     * @param param 分页参数
     * @return
     */
    IPage<BnsIaUnitdp> getUnitdp(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇计算单元（小流域）信息
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<BnsIaUnitws> getUnitws(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价汇总
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzSummary> getCjzSummary(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-水库水情列表
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzReservoitSs> getCjzReservoit(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-水库水情列表
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzSluiceSs> getCjzSluice(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-堤防信息列表
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzDfDikeSs> getDfDike(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-路涵水情列表
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzLhRCSs> getLhZdjz(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-桥梁水情列表
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzQlBridgeSs> getBridgeQl(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-塘坝信息列表
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzTbPondSs> getPondTb(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-自动检测站
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzStationSs> getZdjczCz(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-无线广播预警
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzRadioGbSs> getRadioGb(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-简易雨量站
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzSimpleJyylSs> getSimpleJyyl(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价涉水工程-简易水位站
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzWLJysw> getWLJysw(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价-河道纵断面
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzHdzdm> getHdzdm(Page page, @Param("map") Map<String, Object> param);

    /**
     * 查询重点城(集)镇调查评价-河道横断面
     *
     * @param param 查询参数
     * @param page  分页参数
     * @return
     */
    IPage<DcjzHdhdm> getCjzHdhdm(Page page, @Param("map") Map<String, Object> param);

    List<StbprpInfoVo> getStcdInfo(@Param("stcd") String stcd);

    List<IaDfwruleVo> getDfwruleByAdcd(@Param("adcd") String adcd);
}
