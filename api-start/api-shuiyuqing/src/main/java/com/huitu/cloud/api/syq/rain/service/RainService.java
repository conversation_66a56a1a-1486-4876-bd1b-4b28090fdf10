package com.huitu.cloud.api.syq.rain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.syq.rain.entity.*;
import com.huitu.cloud.api.syq.rsvr.entity.RsvrRainList;
import com.huitu.cloud.api.syq.warn.entity.HeavyRainfallAlarm;

import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RainService {
    /**
     * 分页查询累计雨量信息
     *
     * @param stm          开始时间
     * @param etm          结束时间
     * @param stType       测站归属类型
     * @param adcd         政区编码
     * @param bscd         流域编码
     * @param stnm         测站名称
     * @param threshold    阈值范围
     * @param rainShowType 显示类型
     * @param pageNum      页码
     * @param pageSize     每页个数
     * @param isOut        辖区内与辖区外
     * @param isVideo      仅有视频
     * @return
     */
    IPage<Rain> getRainByCondition(String stm, String etm, List<String> stType, String adcd, String bscd, String stnm, String threshold, String rainShowType, int pageNum, int pageSize, List<String> isOut, List<String> isFollow, List<Integer> forecastHour, String isVideo);

    /**
     * 列表查询累计雨量信息
     *
     * @param stm          开始时间
     * @param etm          结束时间
     * @param stType       测站归属类型
     * @param adcd         政区编码
     * @param bscd         流域编码
     * @return
     */
    List<Rain> listRainDiff(String stm, String etm, List<String> stType, String adcd, String bscd);

    /**
     * 树状查询政区累计雨量信息
     *
     * @param stm          开始时间
     * @param etm          结束时间
     * @param stType       测站归属类型
     * @param adcd         政区编码
     * @return
     */
    List<AdAvgRain> listAdRainDiff(String stm, String etm, List<String> stType, String adcd);

    /**
     * 获取雨情摘要信息（全年+汛期）
     * @param tm
     * @param adcd
     * @return
     */
    RainSummary getRainSummary(String tm, String adcd);

    /**
     * 切换流域 中小河流测站列表查询
     *
     * @param baseDao
     * @return
     */
    List<RainByBas> getRainByBasCondition(QueryRain baseDao);

    /**
     * 切换流域 主要江河测站列表查询
     *
     * @param baseDao
     * @return
     */
    List<RainByBas> getRainByBasMainCondition(QueryRain baseDao);

    /**
     * 切换流域 主要江河测站列表查询（移动端）
     *
     * @param baseDao
     * @return
     */
    List<RainByBas> getRainByBasMainConditionMobile(QueryRain baseDao);

    /**
     * 分页查询累计雨量信息
     *
     * @param stm      开始时间
     * @param etm      结束时间
     * @param stType   测站归属类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<Rain> getRainByConditionEntity(String stm, String etm, List<String> stType, int pageNum, int pageSize);

    /**
     * 查询全部累计雨量信息
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @return
     */
    List<Rain> getRainByConditionAll(String stm, String etm, List<String> stType, String adcd, String bscd, String stnm, String threshold, String rainShowType, List<String> isOut, List<String> isFollow, List<Integer> forecastHour, String isVideo);

    List<Rain> getRainByConditionAllByBas(String stm, String etm, List<String> stType, String adcd, String bscd, String stnm, String threshold, String rainShowType, List<String> isOut, List<String> isFollow, List<Integer> forecastHour);

    /**
     * 单站时段降雨过程列表
     *
     * @param stm  开始时间
     * @param etm  结束时间
     * @param stcd 测站编码 必录
     * @return
     */
    List<StPptnR> getRainListByTm(String stm, String etm, String stcd, String type);

    /**
     * 查询一段时间内政区面平均雨量树
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @param adcd   政区编码
     * @return
     */
    AdAvgRain getAvgRainTreeByTm(String stm, String etm, String adcd, List<String> stType, String showLevel);

    /**
     * 查询一段时间内政区面平均雨量树
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @param adcd   政区编码
     * @return
     */
    List<AdAvgRain> getAvgRainTreeByTmNew(String stm, String etm, List<String> stType, String adcd, String isself);


    /**
     * 查询一段时间内雨量的最大值
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @param adcd   政区编码
     * @return
     */
    List<Rain> getMaxRainByTm(String stm, String etm, String adcd, List<String> stType, List<String> isOut);

    /**
     * 查询一段时间内各个雨量级别的测站数
     *
     * @param stType    测站归属类型
     * @param stm       开始时间
     * @param etm       结束时间
     * @param adcd      政区编码
     * @param rainRule0 降雨级别0-10
     * @param rainRule1 降雨级别10-25
     * @param rainRule2 降雨级别25-50
     * @param rainRule3 降雨级别50-100
     * @param rainRule4 降雨级别100-250
     * @param rainRule5 降雨级别大于250
     * @return
     */
    List<RainLevelStNum> getRainLevelStNumByTm(List<String> stType, String adcd, String etm, String stm, int rainRule0, int rainRule1, int rainRule2, int rainRule3, int rainRule4, int rainRule5);

    /**
     * 查询一段时间内的降雨强度
     *
     * @param stm      开始时间
     * @param etm      结束时间
     * @param stType   测站归属类型
     * @param intv     统计时段
     * @param adcd     政区编码
     * @param bscd     流域编码
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<RainStrength> getRainStrength(String stm, String etm, List<String> stType, int intv, String adcd, String bscd, String stnm, int pageNum, int pageSize);

    /**
     * 查询一段时间内降雨测站归属类型统计
     *
     * @param stm   开始时间
     * @param etm   结束时间
     * @param adcd  政区编码
     * @param isOut 辖区内与辖区外
     * @return
     */
    AscriptionTypeCount getAscriptionTypeCount(String adcd, String etm, String stm, List<String> stType, List<String> isOut);

    /**
     * 查询一段时间内降雨测站归属类型统计
     *
     * @param adcd  政区编码
     * @param bscd  流域编码
     * @param stType 测站类型
     * @return
     */
    AscriptionTypeCount getAscriptionTypeCountNew(String adcd, String bscd, String stType);

    /**
     * 降雨告警列表
     *
     * @param adcd     政区编码
     * @param bscd     流域编码
     * @param stnm     测站名称
     * @param stType   测站归属类型 1、山洪 2、水文 3、气象
     * @param stdt     预警指标
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */

    IPage<BsnRainAlarm> getRainWarn(String adcd, String bscd, String stnm, String warnType, List<String> stType, String stdt, int pageNum, int pageSize);

    /**
     * 降雨告警指标设置
     *
     * @return
     */

    BsnRainalarmB getBsnRainalarmb();

    /**
     * 降雨告警指标设置
     *
     * @param id         编号
     * @param movedrp1h  超危1小时
     * @param movedrp3h  超危3小时
     * @param movedrp6h  超危6小时
     * @param movedrp12h 超危12小时
     * @param movedrp24h 超危24小时
     * @param warndrp1h  超警1小时
     * @param warndrp3h  超警3小时
     * @param warndrp6h  超警6小时
     * @param warndrp12h 超警12小时
     * @param warndrp24h 超警24小时
     * @return
     */
    Boolean updateBsnRainalarmb(String id, Double movedrp1h, Double movedrp3h, Double movedrp6h, Double movedrp12h, Double movedrp24h,
                                Double warndrp1h, Double warndrp3h, Double warndrp6h, Double warndrp12h, Double warndrp24h);

    /**
     * 分页查询历史雨量信息
     *
     * @param stm      开始时间
     * @param etm      结束时间
     * @param stType   测站归属类型
     * @param adcd     政区编码
     * @param stnm     测站名称
     * @param tmType   时间类型
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<HisRain> getRainHisListByPage(String adcd, String stm, String etm, List<String> stType, String tmType, String stnm, String threshold, int pageNum, int pageSize, List<String> isOut);

    /**
     * 流域降雨统计
     *
     * @param stTypes 多个值以逗号隔开
     * @param stm     开始时间
     * @param etm     结束时间
     * @return
     */
    BsnBasStBTo getRainfallStatistics(String stTypes, String stm, String etm);

    /**
     * 值班报告降雨情况
     *
     * @param adcd 政区编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    RainByDutyRecort getRainInfoByDuty(String adcd, String stm, String etm);

    /**
     * 业务门户降雨统计
     *
     * @param adcd 政区编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    RainByYwmh getRainInfoByYwmh(String adcd, String stm, String etm);

    /**
     * 导出累计雨量信息
     *
     * @param stm          开始时间
     * @param etm          结束时间
     * @param stType       测站归属类型
     * @param adcd         政区编码
     * @param bscd         流域编码
     * @param stnm         测站名称
     * @param threshold    阈值范围
     * @param rainShowType 显示类型
     * @param type         导出模板号
     * @return
     */
    void exportRainByCondition(int type, String stm, String etm, List<String> stType, String adcd, String bscd, String stnm, String threshold, String rainShowType, List<String> isOut);

    /**
     * 导出单站时段降雨信息
     *
     * @param stm  开始时间
     * @param etm  结束时间
     * @param stcd 测站编码 必录
     * @return
     */
    void exportRainTmList(String stm, String etm, String stcd, String type);

    /**
     * 查询一段时间内的降雨强度
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @param intv   统计时段
     * @param adcd   政区编码
     * @param bscd   流域编码
     * @return
     */
    void exportRainStrength(String stm, String etm, List<String> stType, int intv, String adcd, String bscd, String stnm);

    /**
     * 历史雨量信息导出
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @param adcd   政区编码
     * @param stnm   测站名称
     * @param tmType 时间类型
     * @return
     */
    void exportRainHisList(String adcd, String stm, String etm, List<String> stType, String tmType, String stnm, String threshold, List<String> isOut);

    /**
     * 流域降雨统计导出
     *
     * @param adcd 政区编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    void exportRainfallStatistics(String adcd, String stm, String etm);

    /**
     * 大型水库实时降雨统计
     *
     * @param stTypes 多个值以逗号隔开
     * @param stm     开始时间
     * @param etm     结束时间
     * @return
     */
    List<RsvrRainStatisticsVo> getRsvrRainStatistics(String stTypes, String stm, String etm);

    /**
     * 根据时间查询政区降雨场次
     *
     * @param adcd 政区编码 15位
     * @param stm  开始时间 yyyy-MM-dd
     * @param etm  结束时间 yyyy-MM-dd
     * @return
     */
    List<BsnTimesR> getRainTimes(String stm, String etm, String adcd);

    /**
     * 分页查询场次内累计雨量信息
     *
     * @param tmsid     场次降雨编码
     * @param tmsad     场次降雨的政区
     * @param stType    测站归属类型
     * @param adcd      政区编码
     * @param bsnm      流域名称
     * @param stnm      测站名称
     * @param threshold 阈值范围
     * @param pageNum   页码
     * @param pageSize  每页个数
     * @return
     */
    IPage<TmsRainVo> getTmsRainByCondition(String tmsid, String tmsad, List<String> stType, String adcd, String bsnm, String stnm, String threshold, int pageNum, int pageSize);

    /**
     * 导出场次内累计雨量信息
     *
     * @param tmsid     场次降雨编码
     * @param tmsad     场次降雨的政区
     * @param stType    测站归属类型
     * @param adcd      政区编码
     * @param bsnm      流域名称
     * @param stnm      测站名称
     * @param threshold 阈值范围
     * @param pageNum   页码
     * @param pageSize  每页个数
     * @return
     */
    void exportRainCountByRainTms(String tmsid, String tmsad, List<String> stType, String adcd, String bsnm, String stnm, String threshold, int pageNum, int pageSize);

    /**
     * 大型水库实时降雨导出
     *
     * @param stTypes 多个值以逗号隔开
     * @param stm     开始时间
     * @param etm     结束时间
     * @return
     */
    void exportRsvrStatistics(String stTypes, String stm, String etm);

    /**
     * 查询短信描述信息
     *
     * @param adcd 政区编码
     * @param stm  开始时间
     * @param etm  结束时间
     * @return
     */
    SmsDesInfo getSmsInfo(String stm, String etm, String adcd);

    /**
     * 吉林墒情分析简易版雨量统计 查询
     *
     * @param stm      开始时间
     * @param etm      结束时间
     * @param adcd     政区
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<Rain> getRainByConditionForSqfx(String stm, String etm, String adcd, int pageNum, int pageSize);

    /**
     * 林墒情分析简易版雨量统计导出
     *
     * @param stm
     * @param etm
     * @param adcd
     */
    void exportRainByConditionForSqfx(String stm, String etm, String adcd);

    /**
     * 查询测站某一天的24小时降雨合并统计信息
     *
     * @param date     日期
     * @param adcd     政区 15位
     * @param stType   数据来源 1：水文 2：山洪 3 ：气象  多个值以逗号隔开
     * @param stnm     测站编码
     * @param admauth  管理单位
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<OneDayRain> getRainListTmMerge(String date, String adcd, String stType, String stnm, String admauth, int pageNum, int pageSize) throws Exception;

    /**
     * 查询测站某一天的24小时降雨合并统计信息
     *
     * @param date     日期
     * @param adcd     政区 15位
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<OneDayRain> getRainListTmMergeForSqfx(String date, String adcd, int pageNum, int pageSize) throws Exception;

    /**
     * 查询测站某一天的24小时降雨合并统计信息
     *
     * @param date    日期
     * @param adcd    政区 15位
     * @param stType  数据来源 1：水文 2：山洪 3 ：气象  多个值以逗号隔开
     * @param stnm    测站编码
     * @param admauth 管理单位
     * @return
     */
    void exportRainListTmMerge(String date, String adcd, String stType, String stnm, String admauth);

    /**
     * 查询测站某一天的24小时降雨合并统计信息
     *
     * @param date 日期
     * @param adcd 政区 15位
     * @return
     */
    void exportRainListTmMergeForSqfx(String date, String adcd);

    /**
     * 强降雨告警列表
     *
     * @param adcd     政区编码
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<HeavyRainfallAlarm> getHeavyRainfallAlarm(String adcd, int pageNum, int pageSize);

    /**
     * 最近24小时累计降雨统计
     *
     * @param tm       截止时间
     * @param adcd     政区 15位
     * @param stType   数据来源 1：水文 2：山洪 3 ：气象
     * @param stnm     测站编码
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<RainTimeIntervalInfo> getTimeInterval(String adcd, List<String> stType, String tm, String stnm, String sort, int pageNum, int pageSize);

    /**
     * 导出 最近24小时累计降雨统计
     *
     * @param query
     * @param output
     */
    void exportTimeInterval(QueryRainTimeInterval query, OutputStream output);

    /**
     * 最近24小时累计降雨统计
     *
     * @param tm       截止时间
     * @param adcd     政区 15位
     * @param stType   数据来源 1：水文 2：山洪 3 ：气象
     * @param stnm     测站编码
     * @return
     */
    List<RainTimeIntervalInfo> getTimeIntervalList(String adcd, List<String> stType, String tm, String stnm, String sort);

    /**
     * 政区面平均雨量列表导出
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @param adcd   政区编码
     * @return
     */
    void exportAvgRainTmTreeNew(String stm, String etm, List<String> stType, String adcd);

    /**
     * 墒情气象监测查询
     *
     * @param stm      开始时间
     * @param etm      结束时间
     * @param query    政区编码 / 政区名称
     * @param pageNum  页码
     * @param pageSize 每页个数
     * @return
     */
    IPage<MoistureMeteorologyVo> getMoistureForSqfx(String stm, String etm, String query, int pageNum, int pageSize);

    /**
     * 墒情气象监测查询
     *
     * @return
     */
    void exportMoistureForSqfx(String stm, String etm, String query, int pageNum, int pageSize);

    /**
     * 获取流域降雨汇总列表
     *
     * @param basCode 流域代码
     * @param stTypes 测站类型
     * @param stm     开始时间
     * @param etm     结束时间
     * @param isOut   辖区内外
     * @return 汇总列表
     **/
    List<BasRainSummary> getBasRainSummaryList(String basCode, String stTypes, String stm, String etm, String isOut);

    /**
     * 测站雨量数据对比
     *
     * @param stcd 测站编码
     * @param tp   颗粒度
     * @param tms  基准时间和对比时间的字符串
     * @return
     */
    List<Map<String, String>> getStcdContrastBytm(String stcd, String tp, String tms);

    /**
     * 政区雨量数据对比
     *
     * @param adcd 政区编码
     * @param tp   颗粒度
     * @param tms  基准时间和对比时间的字符串
     * @return
     */
    List<Map<String, String>> getAdcdContrastBytm(String adcd, String tp, String tms);

    /**
     * 流域雨量数据对比
     *
     * @param bscd 流域编码
     * @param tp   颗粒度
     * @param tms  基准时间和对比时间的字符串
     * @return
     */
    List<Map<String, String>> getBscdContrastBytm(String bscd, String tp, String tms);

    /**
     * 查询测站未来1、3、6、12、24小时降雨量
     *
     * @param stcd
     * @param stm
     * @return
     */
    List<RainForecastHour> getForecastHourByStcd(String stcd, String stm);

    /**
     * 所有预报降雨测站累计雨量
     *
     * @param adcd
     * @param stm
     * @param etm
     * @return
     */
    List<Rain> getForecastByStcd(String adcd, String stm, String etm);

    /**
     * 查询未来降雨量
     *
     * @param stm    开始时间
     * @param etm    结束时间
     * @param stType 测站归属类型
     * @param adcd   政区编码
     * @return
     */
    List<Rain> getForecastList(String stm, String etm, List<String> stType, String adcd);

    /**
     * 下级政区实际降雨和预报降雨平均雨量列表
     *
     * @param stm  开始时间
     * @param etm  结束时间
     * @param adcd 政区编码
     * @return
     */
    List<AdAvgRainForecast> getAvgRainForecastListByAdcdTm(String adcd, String stm, String etm);

    /**
     * 未来3小时预报降雨
     *
     * @param stcd
     * @param stm
     * @param etm
     * @return
     */
    List<StPptnF> getForecast3Hours(String stcd, String stm, String etm);

    /**
     * 未来2小时预报降雨-彩云
     *
     * @param stcd
     * @param stm
     * @param etm
     * @return
     */
    List<StPptnF> getForecast24Hours(String stcd, String stm, String etm);

    /**
     * 未来3小时或72小时逐小时预报降雨
     *
     * @param stcd
     * @param stm
     * @param etm
     * @return
     */
    List<StPptnFVo> getForecast3Or72List(String stcd, String stm, String etm, String tp);

    List<RainPointerAD> getRainPointerADList(String stcd);

    List<RainPointerST> getRainPointerSTList(String adcd);

    /**
     * 雨情笼罩面降雨（政区）
     * @param query
     * @return
     */
    List<RainArea> getRainAreaAd(QueryRain query);

    /**
     * 雨情笼罩面降雨（流域）
     * @param query
     * @return
     */
    List<RainArea> getRainAreaBas(QueryRain query);

    /**
     * 雨情笼罩面降雨导出
     * @param baseDao
     */
    void exportRainArea(QueryRain baseDao);

    /**
     * 中小河流导出
     * @param baseDao
     */
    void exportRainByBasCondition(QueryRain baseDao);

    /**
     * 主要江河导出
     * @param baseDao
     */
    void exportRainByBasMainCondition(QueryRain baseDao);


    /**
     * 雨情信息水库降雨列表
     * @param stm    开始时间
     * @param etm    结束时间
     * @param basCode 流域编码
     * @param adcd   政区编码
     * @param resName    水库名称
     * @param engScal   水库规模
     */
    List<RsvrRainList> getRainReservoirList(String basCode, String adcd,String resName,String engScal, String stm, String etm );
}
