package com.huitu.cloud.api.syq.video.entity;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2019-09-26
 */
public class QueryVideoInfo{
    @ApiModelProperty(value = "类型集合[11,12....]（11：大型水库、12：中型水库、13：小型水库、2：主要江河、3：山洪沟、4：防洪城市、6：仓库）")
    private List<String> types;
    @ApiModelProperty(value = "政区编码")
    private String adcd;
    @ApiModelProperty(value = "流域编码")
    private String bscd;
    @ApiModelProperty(value = "视频站名称")
    private String name;

    public List<String> getTypes() {
        return types;
    }

    public void setTypes(List<String> types) {
        this.types = types;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
