<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.weChat.mapper.SituationRemarkDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getAccpByTmAll" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatRain">
        select STCD,SUM(DRP) DRPS from ST_PPTN_R
        WHERE 1=1
        <if test="stm != null and stm !=''">
            and tm> CONVERT(datetime,'${stm}')
        </if>
        <if test="etm != null and etm !=''">
            and TM &lt;= CONVERT(datetime,'${etm}')
        </if>
        AND INTV='1' and drp>=0
        GROUP BY STCD
    </select>
    <select id="getRainStInfo" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatRain">
        select
        b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM
        ,b.STADTPNM,b.stadtp,PLGTD,PLTTD
        <if test="map.forecastHour != null">
            <foreach collection="map.forecastHour" index="index" item="item">
                ,round(isnull(RAIN${item}, 0), 1) RAIN${item}
            </foreach>
        </if>
        from BSN_STBPRP_V b
        left join ST_STSMTASK_B tk on b.STCD=tk.STCD
        <if test="map.forecastHour != null">
            <foreach collection="map.forecastHour" index="index" item="item">
                left join (SELECT STCD STCD${item},SUM(RAIN) RAIN${item} FROM BSN_FORECASTRAIN_F WHERE RAIN_TIME BETWEEN
                '${map.start_time}' AND
                '${map.endTimeList[index]}'
                GROUP BY STCD) k${item} on b.STCD=STCD${item}
            </foreach>
        </if>
        <if test="map.isFollow != null and map.isFollow != ''">
            left join BSN_USER_FOLLOWST follow on follow.stcd=b.stcd
        </if>
        where 1=1
        AND tk.PFL='1'
        <if test="map.isOut == null and map.ad != null and map.ad != ''">
            and left(b.ADCD,#{map.level})=#{map.ad}
            <if test="map.level !=null and map.level =='4'.toString()">
                and left(b.ADCD,6) != '220581'
            </if>
        </if>
        <if test="map.stnm != null and  map.stnm != ''">
            AND CHARINDEX(#{map.stnm},stnm) >0
        </if>
        <if test="map.bsnm !=null and  map.bsnm !=''">
            AND ltrim(rtrim(b.BSNM))=#{map.bsnm}
        </if>
        <if test="map.stTypes != null and  map.stTypes !='' ">
            and CHARINDEX(b.STADTP,#{map.stTypes})>0
        </if>
        <if test="map.isOut != null and map.isOut == '1'.toString() ">
            and left(b.ADCD,#{map.level})=#{map.ad}
            <if test="map.level !=null and map.level =='4'.toString()">
                and left(b.ADCD,6) != '220581'
            </if>
        </if>
        <if test="map.isOut != null and map.isOut == '2'.toString() ">
            and left(b.ADCD,2) != '22'
        </if>
        <if test="map.isOut != null and map.isOut == '3'.toString() ">
            and ( left(b.ADCD,2) != '22' or ( left(b.ADCD,#{map.level})=#{map.ad}
            <if test="map.level !=null and map.level =='4'.toString()">
                and left(b.ADCD,6) != '220581'
            </if>
            ))
        </if>
        <if test="map.isFollow != null and map.isFollow == '1'.toString() ">
            and follow.userid=#{map.userId}
        </if>
        <if test="map.isFollow != null and map.isFollow == '2'.toString() ">
            and isnull(follow.userid,0)!=#{map.userId}
        </if>
        GROUP BY
        b.STCD,B.STNM,b.LGTD,b.LTTD,b.BSNM,b.HNNM,b.RVNM,b.STLC,FRGRD,STTP,b.ADCD,b.ADNM,b.XADNM,b.XZADCD,b.XZADNM,b.STADTPNM,b.stadtp,PLGTD,PLTTD
        <if test="map.forecastHour != null">
            <foreach collection="map.forecastHour" index="index" item="item">
                ,RAIN${item}
            </foreach>
        </if>
        order by b.STCD
    </select>
    <select id="getRiverTree" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatBsnBasStBTo">
        WITH SUBQRY(BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE) AS (
        　　SELECT BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE FROM BSN_BAS_B
        <if test="bsnm != null and bsnm !=''">
            WHERE BAS_NAME = #{bsnm}
        </if>
        <if test="bsnm == null or bsnm ==''">
            WHERE PBAS_CODE = ''
        </if>
        　　UNION ALL
        　　SELECT BSN_BAS_B.BAS_CODE,BSN_BAS_B.BAS_NAME,BSN_BAS_B.BAS_LEVEL,BSN_BAS_B.PBAS_CODE FROM BSN_BAS_B,SUBQRY
        WHERE BSN_BAS_B.PBAS_CODE = SUBQRY.BAS_CODE
        )
        SELECT A.BAS_CODE,A.BAS_NAME,A.BAS_LEVEL,A.PBAS_CODE, B.STCD FROM SUBQRY A INNER JOIN BSN_BAS_ST B
        ON A.BAS_CODE = B.BAS_CODE
    </select>
    <select id="selectByAdLevel" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatAdcdB">
        select ADCD, ADNM, PADCD, LGTD, LTTD, ADLVL FROM BSN_ADCD_B WHERE 1=1
        <if test="level !=null and level !='' and ad !=null and ad !=''">
            and left(ADCD,#{level})=#{ad}
        </if>
        <if test="adLvl !=null and adLvl !=''">
            and ADLVL &lt;= #{adLvl}
        </if>
        order by adcd
    </select>
    <select id="getOneHourRain" resultType="java.util.Map">
        select STCD, Convert(decimal (18, 1), RAIN) RAIN
        from BSN_ONEHOURRAIN_R
        where rain >= 0
    </select>
    <select id="getRainAdAvg" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatAdAvgRain">
        select A.adcd,maxRain,avgRain,b.adnm,B.padcd,B.adlvl,B.LGTD,B.LTTD
        from (
        SELECT T.ADCD,MAX(DRPS) maxRain,Convert(decimal(18,1),AVG(DRPS)) avgRain
        FROM
        (select A.DRPS,left(b.ADCD,#{map.level2})+'${map.zero}' ADCD,b.ADNM,b.STCD,b.STNM
        from
        (select STCD,SUM(DRP) DRPS from ST_PPTN_R WITH (NOLOCK)
        WHERE 1=1
        <if test="map.stm != null and map.stm !=''">
            and tm>CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and TM &lt;= CONVERT(datetime,#{map.etm})
        </if>
        and INTV='1'
        GROUP BY STCD) A
        LEFT JOIN BSN_STBPRP_V B ON A.STCD =B.STCD
        left join ST_STSMTASK_B tk on a.STCD=tk.STCD
        WHERE 1=1 AND PFL='1' and a.drps>=0
        <if test="map.ad != null and map.ad !=''">
            and left(b.ADCD,#{map.level})=#{map.ad}
        </if>
        <if test="map.exceptFlag != null and map.exceptFlag ==true ">
            and b.ADDVCD != '220581'
        </if>
        <if test="map.stType !=null and  map.stType.size() >0 ">
            and b.stadtp in
            <foreach collection="map.stType" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ) T
        GROUP BY T.ADCD)a
        inner join BSN_ADCD_B b ON A.ADCD=B.ADCD
        order by A.adcd
    </select>
    <select id="getRainWarn" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatBsnRainAlarm">
        SELECT
        a.STCD,
        a.STDT,
        a.ALARMGRADEID,
        a.ALARMSTM,
        a.ALARMETM,
        a.ALARMDESC,
        a.DRP,
        a.ALERMDRP,
        a.REAMRK,
        b.STNM,
        b.BSNM,
        b.STLC,
        b.ADDVCD,
        b.STTP,
        b.LGTD,
        b.LTTD,
        b.stadtp,
        b.ADNM,
        b.STADTPNM,
        b.XADNM,PLGTD,PLTTD
        FROM
        BSN_RAIN_ALARM a
        LEFT JOIN BSN_STBPRP_V b ON a.STCD = b.STCD
        WHERE 1=1
        <if test="map.ad != null and map.ad !=''">
            and left(b.ADDVCD,#{map.adcd})=#{map.ad}
        </if>
        <!--<if test="map.bsnm !=null and  map.bsnm !=''">
            and CHARINDEX(#{map.bsnm},b.bsnm) >0
        </if>-->
        <if test="map.stnm !=null and  map.stnm !=''">
            and CHARINDEX(#{map.stnm},b.stnm) >0
        </if>
        <if test="map.warnType !=null and  map.warnType !=''">
            and CHARINDEX(ALARMGRADEID,#{map.warnType}) >0
        </if>
        <if test="map.stType !=null and  map.stType.size() >0 ">
            and b.stadtp in
            <foreach collection="map.stType" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by a.STCD desc
    </select>
    <select id="getRsvrLatestData" statementType="CALLABLE" parameterType="string"
            resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatStRsvrVo">
        {call proc_get_rsvrdata_all_new(
                #{stm, mode=IN}, #{etm, mode=IN}, #{whereSql, mode=IN}
            )}
    </select>
    <select id="getRiverByCondition" resultType="com.huitu.cloud.api.ewci.bia.entity.StRiverVos">
        select STB.STCD,
        STB.STNM,
        A.TM,
        Convert(decimal(18,2),A.Z) Z,
        Q,
        XSA,
        XSAVV,
        XSMXV,
        FLWCHRCD,
        WPTN,
        MSQMT,
        MSAMT,
        MSVMT,
        STB.LTTD,
        STB.LGTD,
        STB.ADNM,
        STB.ADCD,
        STB.XADCD,
        STB.XADNM,
        STB.XZADCD,
        STB.XZADNM,
        STB.STLC,
        STB.FRGRD,
        STB.RVNM,
        STB.BSNM,
        STB.HNNM,
        STB.STTP,
        STB.STADTP,
        STB.STADTPNM,
        fh.LDKEL,
        fh.RDKEL,
        WRZ,
        WRQ,
        GRZ,
        GRQ,
        Z - WRZ ZWRZ,
        Z - GRZ ZGRZ,
        Q - WRQ QWRQ,
        Q - GRQ QGRQ
        ,PLGTD,PLTTD
        from (select stcd, max(tm) tm
        from ST_RIVER_R
        WHERE 1 = 1
        <if test="map.stm != null and map.stm !=''">
            and tm>=CONVERT(datetime,#{map.stm})
        </if>
        <if test="map.etm != null and map.etm !=''">
            and TM &lt;= CONVERT(datetime,#{map.etm})
        </if>
        group by stcd) t left join BSN_STBPRP_V STB
        ON t.STCD = STB.STCD
        left join ST_RIVER_R A
        on A.STCD = t.STCD
        and A.TM = t.tm
        left join ST_RVFCCH_B fh
        on a.STCD = fh.STCD
        <if test="map.isFollow!= null and map.isFollow != ''">
            left join BSN_USER_FOLLOWST follow on follow.stcd=t.stcd
        </if>
        where 1=1
        and CHARINDEX(STTP,'ZZ,ZQ')>0
        <if test="map.isOut == null and map.ad != null and map.ad !=''">
            AND STB.ADCD like '${map.ad}%'
            <if test="map.adLevl !=null and map.adLevl =='4'.toString()">
                and left(STB.ADCD,6) != '220581'
            </if>
        </if>
        <if test="map.stnm !=null and  map.stnm !=''">
            AND CHARINDEX(#{map.stnm},stnm) >0
        </if>
        <!--        <if test="map.bsnm !=null and  map.bsnm !=''">-->
        <!--            AND ltrim(rtrim(bsnm))=#{map.bsnm}-->
        <!--        </if>-->
        <if test="map.stType !=null and  map.stType.size() >0">
            AND STB.STADTP IN
            <foreach collection="map.stType" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="map.warnFlag !=null and  map.warnFlag==true">
            and Z >=WRZ
        </if>
        <if test="map.isOut!= null and map.isOut == '1'.toString() ">
            and STB.ADCD like '${map.ad}%'
            <if test="map.adLevl !=null and map.adLevl =='4'.toString()">
                and left(STB.ADCD,6) != '220581'
            </if>
        </if>
        <if test="map.isOut!= null and map.isOut == '2'.toString() ">
            and left(STB.ADCD,2) != '22'
        </if>
        <if test="map.isOut!= null and map.isOut == '3'.toString() ">
            and (left(STB.ADCD,2) != '22' or (STB.ADCD like '${map.ad}%'
            <if test="map.adLevl !=null and map.adLevl =='4'.toString()">
                and left(STB.ADCD,6) != '220581'
            </if>
            ))
        </if>
        <if test="map.isFollow!= null and map.isFollow == '1'.toString() ">
            and follow.userid=#{map.userId}
        </if>
        <if test="map.isFollow!= null and map.isFollow == '2'.toString() ">
            and isnull(follow.userid,0)!=#{map.userId}
        </if>

        ORDER BY ZWRZ desc ,stcd

    </select>
    <select id="getLastestBnsFileurlsr"
            resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatBnsFilesAndUrls">
        select top 1 a.filecd, a.filetitle, a.fileno, a.filetype, a.fileflg, a.ctm, a.cer, a.cher, a.nt, a.adcd, a.filedesc, a.ser, a.senddept, b.filenewnm, b.fileurl
        from BNS_FILES_R a inner join BNS_FILEURLS_R b on a.filecd = b.filecd
        <where>
            <if test="filetitle !=null and filetitle !=''">
                and (charindex(#{filetitle}, filetitle) > 0 or charindex(#{filetitle}, filenewnm) > 0)
            </if>
        </where>
        order by ctm desc
    </select>

    <select id="getWeatherForecast" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatWeatherVo">
        select TOP(1) ID, TM, TITLE, WEATHER
        from BNS_WEATHER_B
        order by TM desc
    </select>

    <select id="getWxytList" resultType="com.huitu.cloud.api.ewci.weChat.entity.WeChatQxpicgetRVo">
        select picid, tm, pictp, picurl, moditime from BNS_QXPICGET_R
        where tm >= DATEADD(HOUR,#{tms},getdate())  and pictp=#{picType}  AND TM &lt;=getdate()
        order by tm desc
    </select>
</mapper>
