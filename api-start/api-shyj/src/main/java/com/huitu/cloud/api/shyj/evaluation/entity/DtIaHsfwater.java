package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 历史山洪灾害情况统计
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
@ApiModel(value="DtIaCHsfwater对象", description="历史山洪灾害情况统计")
public class DtIaHsfwater extends DtExtendInfo{

    @ApiModelProperty(value = "行政区划代码")
    private String adcd;

    @ApiModelProperty(value = "灾害次数")
    private Double hsfct;

    @ApiModelProperty(value = "死亡人数（人）")
    private Double dpcount;

    @ApiModelProperty(value = "失踪人数（人）")
    private Double mpcount;

    @ApiModelProperty(value = "损毁房屋（间）")
    private Double chcount;

    @ApiModelProperty(value = "转移人数（人）")
    private Double spcount;

    @ApiModelProperty(value = "直接经济损失（万元）")
    private Double elose;

    @ApiModelProperty(value = "下级统计")
    private List<DtIaHsfwater> children;

    public List<DtIaHsfwater> getChildren() {
        return children;
    }

    public void setChildren(List<DtIaHsfwater> children) {
        this.children = children;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Double getHsfct() {
        return hsfct;
    }

    public void setHsfct(Double hsfct) {
        this.hsfct = hsfct;
    }

    public Double getDpcount() {
        return dpcount;
    }

    public void setDpcount(Double dpcount) {
        this.dpcount = dpcount;
    }

    public Double getMpcount() {
        return mpcount;
    }

    public void setMpcount(Double mpcount) {
        this.mpcount = mpcount;
    }

    public Double getChcount() {
        return chcount;
    }

    public void setChcount(Double chcount) {
        this.chcount = chcount;
    }

    public Double getSpcount() {
        return spcount;
    }

    public void setSpcount(Double spcount) {
        this.spcount = spcount;
    }

    public Double getElose() {
        return elose;
    }

    public void setElose(Double elose) {
        this.elose = elose;
    }
}
