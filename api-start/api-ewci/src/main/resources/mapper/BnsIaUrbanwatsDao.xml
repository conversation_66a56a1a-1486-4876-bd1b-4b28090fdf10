<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.ia.mapper.BnsIaUrbanwatsDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.ewci.ia.entity.BnsIaUrbanwats">
        <id column="ADCD" property="adcd" />
        <result column="YR" property="yr" />
        <result column="RESENG" property="reseng" />
        <result column="DCENG" property="dceng" />
        <result column="DSENG" property="dseng" />
        <result column="SZG" property="szg" />
        <result column="DZG" property="dzg" />
        <result column="OTHRR" property="othrr" />
        <result column="MWS" property="mws" />
        <result column="DWS" property="dws" />
        <result column="SWS" property="sws" />
        <result column="LOWS" property="lows" />
        <result column="SWC" property="swc" />
        <result column="GWOD" property="gwod" />
        <result column="UQWQ" property="uqwq" />
        <result column="EGWS" property="egws" />
        <result column="EGWSC" property="egwsc" />
        <result column="TS" property="ts" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ADCD, YR, RESENG, DCENG, DSENG, SZG, DZG, OTHRR, MWS, DWS, SWS, LOWS, SWC, GWOD, UQWQ, EGWS, EGWSC, TS
    </sql>
    <select id="getPageList" useCache="false" resultType="com.huitu.cloud.api.ewci.ia.entity.BnsIaUrbanwatsVo">
        select D.ADCD, D.YR, D.RESENG, D.DCENG, D.DSENG, D.SZG, D.DZG, D.OTHRR, D.MWS, D.DWS, D.SWS, D.LOWS, D.SWC, D.GWOD, D.UQWQ, D.EGWS, D.EGWSC, D.TS, A.adnm
        from BNS_IA_URBANWATS D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE LEFT(D.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        <if test="map.level == '4'.toString()">
            AND LEFT(D.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.yr !=null and map.yr !=''">
            AND D.yr = #{map.yr}
        </if>
        order by D.adcd asc
    </select>
    <select id="getStatListByAdcd" resultType="com.huitu.cloud.api.ewci.ia.entity.BnsIaUrbanwatsVo">
        select D.ADCD, D.YR, D.RESENG, D.DCENG, D.DSENG, D.SZG, D.DZG, D.OTHRR, D.MWS, D.DWS, D.SWS, D.LOWS, D.SWC, D.GWOD, D.UQWQ, D.EGWS, D.EGWSC, D.TS, A.adnm
        from BNS_IA_URBANWATS D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE A.PADCD = #{map.adcd}
        <if test="map.yr !=null and map.yr !=''">
            AND D.yr = #{map.yr}
        </if>
        order by D.adcd asc
    </select>
    <select id="getStatOwnListByAdcd" resultType="com.huitu.cloud.api.ewci.ia.entity.BnsIaUrbanwatsVo">
        select D.ADCD, D.YR, D.RESENG, D.DCENG, D.DSENG, D.SZG, D.DZG, D.OTHRR, D.MWS, D.DWS, D.SWS, D.LOWS, D.SWC, D.GWOD, D.UQWQ, D.EGWS, D.EGWSC, D.TS, A.adnm
        from BNS_IA_URBANWATS D LEFT JOIN BSN_ADCD_B A ON A.ADCD = D.ADCD
        WHERE D.ADCD = #{map.adcd}
        <if test="map.yr !=null and map.yr !=''">
            AND D.yr = #{map.yr}
        </if>
        order by D.adcd asc
    </select>

</mapper>
