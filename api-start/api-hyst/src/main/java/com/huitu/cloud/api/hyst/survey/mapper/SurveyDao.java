package com.huitu.cloud.api.hyst.survey.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.hyst.survey.entity.HystStats;
import com.huitu.cloud.api.hyst.survey.entity.SurveyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 小水电概况 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface SurveyDao {

    /**
     * 小水电概况分页查询
     *
     * @param page
     * @param param
     * @return
     */
    IPage<SurveyVo> getSurveyPage(Page page, @Param("map") Map<String, Object> param);

    /**
     * 点击查看详情列表
     *
     * @param param
     * @return
     */
    List<SurveyVo> getDetailsList(@Param("map") Map<String, Object> param);

    /**
     * 根据编码和记录时间查询时间粒度
     *
     * @param hystCode 编码
     * @param recTime  记录时间
     * @return
     */
    String getAssPer(@Param("hystCode") String hystCode, @Param("recTime") LocalDateTime recTime);

    /**
     * 小水电概况统计图表
     *
     * @param param
     * @return
     */
    HystStats getHystStats(@Param("map") Map<String, Object> param);
}
