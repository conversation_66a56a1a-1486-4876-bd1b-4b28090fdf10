package com.huitu.cloud.api.shyj.evaluation.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 需防洪治理山洪沟统计
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
@ApiModel(value="DtIaGully对象", description="需防洪治理山洪沟统计")
public class DtIaGully extends DtExtendInfo{

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "山洪沟个数")
    private Double gullyCt;

    @ApiModelProperty(value = "沟道长度（km）")
    private Double chlength;

    @ApiModelProperty(value = "已有堤防防护工程长度（km）")
    private Double dikelen;

    @ApiModelProperty(value = "已有护岸防护工程长度（km）")
    private Double rtlen;

    @ApiModelProperty(value = "建国以来山洪发生次数（次）")
    private Double fcount;

    @ApiModelProperty(value = "建国以来死亡（失踪）人数（人）")
    private Double dcount;

    @ApiModelProperty(value = "下级统计")
    private List<DtIaGully> children;

    @ApiModelProperty(value = "山洪沟级统计")
    private List<IaGully> childrenGully;

    @ApiModelProperty(value = "是否有下级")
    private Boolean islazychild;

    public Boolean getIslazychild() {
        return islazychild;
    }

    public void setIslazychild(Boolean islazychild) {
        this.islazychild = islazychild;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public List<DtIaGully> getChildren() {
        return children;
    }

    public void setChildren(List<DtIaGully> children) {
        this.children = children;
    }

    public Double getGullyCt() {
        return gullyCt;
    }

    public void setGullyCt(Double gullyCt) {
        this.gullyCt = gullyCt;
    }

    public Double getChlength() {
        return chlength;
    }

    public void setChlength(Double chlength) {
        this.chlength = chlength;
    }

    public Double getDikelen() {
        return dikelen;
    }

    public void setDikelen(Double dikelen) {
        this.dikelen = dikelen;
    }

    public Double getRtlen() {
        return rtlen;
    }

    public void setRtlen(Double rtlen) {
        this.rtlen = rtlen;
    }

    public Double getFcount() {
        return fcount;
    }

    public void setFcount(Double fcount) {
        this.fcount = fcount;
    }

    public Double getDcount() {
        return dcount;
    }

    public void setDcount(Double dcount) {
        this.dcount = dcount;
    }

    public List<IaGully> getChildrenGully() {
        return childrenGully;
    }

    public void setChildrenGully(List<IaGully> childrenGully) {
        this.childrenGully = childrenGully;
    }
}
