<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.person.mapper.PersonStatDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <select id="getPersonInfoList" resultType="com.huitu.cloud.api.ewci.person.entity.PersonInfoStat">
        SELECT A.ADCD, A.PERTP, A.CHARGETP, A.REALNM, A.MOBILE, B.PADCD
        FROM (
        <if test="map.level &lt;= 4">
            SELECT LEFT (LEFT (ADCD, #{map.lowLevel})+'00000000000', 15) ADCD, REALNM REALNM, MOBILE MOBILE,
            '水库责任人' CHARGETP,
            CASE WHEN RESPERTP='1' THEN '安全度汛行政责任人'
            WHEN RESPERTP='2' THEN '抢险技术责任人'
            WHEN RESPERTP='3' THEN '主管部门责任人'
            WHEN RESPERTP='4' THEN '管理单位责任人'
            WHEN RESPERTP='5' THEN '巡查值守责任人' END PERTP
            FROM BNS_RSVRPERSON_B WHERE LEFT(ADCD, 6) NOT IN ('220581')
            UNION ALL
            SELECT LEFT (LEFT (ADCD, #{map.lowLevel})+'00000000000', 15) ADCD,REALNM REALNM, MOBILE MOBILE,
            '山洪责任人' CHARGETP,
            CASE WHEN RYTP='1' THEN '县级责任人'
            WHEN RYTP='2' THEN '乡镇责任人'
            WHEN RYTP='3' THEN '行政村责任人'
            WHEN RYTP='4' THEN '自然村责任人'
            WHEN RYTP='5' THEN '监测责任人'
            WHEN RYTP='6' THEN '预警责任人'
            WHEN RYTP='7' THEN '转移责任人'
            END PERTP
            FROM BSN_SH_Person_B WHERE LEFT(ADCD, 6) NOT IN ('220581') AND RYTP != '8'
            UNION ALL
            SELECT LEFT (LEFT (ADCD, #{map.lowLevel})+'00000000000', 15) ADCD, REALNM REALNM, MOBILE_PHONE MOBILE,
            '江河责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '行政责任人'
            WHEN PERTP='2' THEN '技术责任人' END PERTP
            FROM BNS_RIVERPERSON_B WHERE LEFT(ADCD, 6) NOT IN ('220581')
            UNION ALL
            SELECT LEFT (LEFT (ADCD, #{map.lowLevel})+'00000000000', 15) ADCD,REALNM REALNM, MOBILE MOBILE,
            '堤防责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '行政责任人'
            WHEN PERTP='2' THEN '技术责任人'
            WHEN PERTP='3' THEN '村级责任人' END PERTP
            FROM BNS_DIKEPERSON_B WHERE LEFT(ADCD, 6) NOT IN ('220581')
            UNION ALL
            SELECT LEFT (LEFT (ADCD, #{map.lowLevel})+'00000000000', 15) ADCD,REALNM REALNM, MOBILE MOBILE,
            '险工险段责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '行政责任人'
            WHEN PERTP='2' THEN '技术责任人' END PERTP
            FROM BNS_DPDSPERSON_B WHERE LEFT(ADCD, 6) NOT IN ('220581')
            UNION ALL
            SELECT LEFT (LEFT (ADCD, #{map.lowLevel})+'00000000000', 15) ADCD,REALNM REALNM, MOBILE MOBILE,
            '蓄滞洪区责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '市级防汛行政责任人'
            WHEN PERTP='2' THEN '县级防汛行政责任人'
            WHEN PERTP='3' THEN '乡级防汛行政责任人' END PERTP
            FROM BNS_FSDAPERSON_B WHERE LEFT(ADCD, 6) NOT IN ('220581')
            UNION ALL
            SELECT LEFT (LEFT (ADCD, #{map.lowLevel})+'00000000000', 15) ADCD,REALNM REALNM, MOBILE MOBILE,
            '水库淹没范围责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '乡镇防汛责任人'
            WHEN PERTP='2' THEN '村屯防汛责任人' END PERTP
            FROM BNS_VILLAGEPERSON_B WHERE LEFT(ADCD, 6) NOT IN ('220581')
            UNION ALL
            SELECT LEFT (LEFT (ADCD, #{map.lowLevel})+'00000000000', 15) ADCD,REALNM REALNM, MOBILE_PHONE MOBILE,
            '中小河流责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '县级行政责任人'
            WHEN PERTP='2' THEN '乡镇行政责任人' END PERTP
            FROM BNS_BASPERSON_B WHERE LEFT(ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.level == '2'.toString()">
            UNION ALL
            SELECT '220581000000000' ADCD, REALNM REALNM, MOBILE MOBILE,
            '水库责任人' CHARGETP,
            CASE WHEN RESPERTP='1' THEN '安全度汛行政责任人'
            WHEN RESPERTP='2' THEN '抢险技术责任人'
            WHEN RESPERTP='3' THEN '主管部门责任人'
            WHEN RESPERTP='4' THEN '管理单位责任人'
            WHEN RESPERTP='5' THEN '巡查值守责任人' END PERTP
            FROM BNS_RSVRPERSON_B WHERE LEFT(ADCD, 6) = '220581'
            UNION ALL
            SELECT '220581000000000' ADCD, REALNM REALNM, MOBILE MOBILE,
            '山洪责任人' CHARGETP,
            CASE WHEN RYTP='1' THEN '县级责任人'
            WHEN RYTP='2' THEN '乡镇责任人'
            WHEN RYTP='3' THEN '行政村责任人'
            WHEN RYTP='4' THEN '自然村责任人'
            WHEN RYTP='5' THEN '监测责任人'
            WHEN RYTP='6' THEN '预警责任人'
            WHEN RYTP='7' THEN '转移责任人'
            END PERTP
            FROM BSN_SH_Person_B WHERE LEFT(ADCD, 6) = '220581' AND RYTP != '8'
            UNION ALL
            SELECT '220581000000000' ADCD, REALNM REALNM, MOBILE_PHONE MOBILE,
            '江河责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '行政责任人'
            WHEN PERTP='2' THEN '技术责任人' END PERTP
            FROM BNS_RIVERPERSON_B WHERE LEFT(ADCD, 6) = '220581'
            UNION ALL
            SELECT '220581000000000' ADCD, REALNM REALNM, MOBILE MOBILE,
            '堤防责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '行政责任人'
            WHEN PERTP='2' THEN '技术责任人'
            WHEN PERTP='3' THEN '村级责任人' END PERTP
            FROM BNS_DIKEPERSON_B WHERE LEFT(ADCD, 6) = '220581'
            UNION ALL
            SELECT '220581000000000' ADCD, REALNM REALNM, MOBILE MOBILE,
            '险工险段责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '行政责任人'
            WHEN PERTP='2' THEN '技术责任人' END PERTP
            FROM BNS_DPDSPERSON_B WHERE LEFT(ADCD, 6) = '220581'
            UNION ALL
            SELECT '220581000000000' ADCD, REALNM REALNM, MOBILE MOBILE,
            '蓄滞洪区责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '行政责任人'
            WHEN PERTP='2' THEN '县级防汛行政责任人'
            WHEN PERTP='3' THEN '乡级防汛行政责任人' END PERTP
            FROM BNS_FSDAPERSON_B WHERE LEFT(ADCD, 6) = '220581'
            UNION ALL
            SELECT '220581000000000' ADCD,REALNM REALNM, MOBILE MOBILE,
            '水库淹没范围责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '乡镇防汛责任人'
            WHEN PERTP='2' THEN '村屯防汛责任人' END PERTP
            FROM BNS_VILLAGEPERSON_B WHERE LEFT(ADCD, 6) = '220581'
            UNION ALL
            SELECT '220581000000000' ADCD,REALNM REALNM, MOBILE_PHONE MOBILE,
            '中小河流责任人' CHARGETP,
            CASE WHEN PERTP='1' THEN '县级行政责任人'
            WHEN PERTP='2' THEN '乡镇行政责任人' END PERTP
            FROM BNS_BASPERSON_B WHERE LEFT(ADCD, 6) = '220581'
        </if>
        ) A
        LEFT JOIN MDT_ADCDINFO_B B ON A.ADCD = B.ADCD
        WHERE LEFT (A.ADCD, #{map.level}) = LEFT (#{map.adcd}, #{map.level})
    </select>
    <select id="getAdcdInfoList" resultType="com.huitu.cloud.api.ewci.person.entity.PersonStatAdcdInfo">
        SELECT ADCD, ADNM
        FROM MDT_ADCDINFO_B
        WHERE PADCD = #{padcd}
        UNION ALL
        SELECT ADCD, ADNM
        FROM MDT_ADCDINFO_B
        WHERE ADCD = #{padcd}
        ORDER BY ADCD
    </select>
    <select id="getPersonAlterStatList" resultType="com.huitu.cloud.api.ewci.person.entity.PersonAlterStatVo">
        SELECT A.ADCD    ADCD,
               A.ADNM    ADNM,
               A.PADCD   PADCD,
               B.TM      SKTM,
               B.whether SKWHETHER,
               C.TM      SHTM,
               C.whether SHWHETHER,
               D.TM      JHTM,
               D.whether JHWHETHER,
               E.TM      DFTM,
               E.whether DFWHETHER,
               F.TM      XGXDTM,
               F.whether XGXDWHETHER,
               G.TM      XZHQTM,
               G.whether XZHQWHETHER,
               H.TM      YMFWTM,
               H.whether YMFWWHETHER,
               I.TM      ZXHLTM,
               I.whether ZXHLWHETHER
        from MDT_ADCDINFO_B A
        LEFT JOIN (select LEFT (ADCD, 6)+'000000000' ADCD, MAX (UDTM) TM,
                        CASE WHEN YEAR (MAX (UDTM)) = YEAR (GETDATE()) THEN '是' ELSE '否' END WHETHER
                   from BNS_RSVRPERSON_B
                   GROUP BY LEFT (ADCD, 6)
                    ) B ON LEFT (A.ADCD, 6) = LEFT (B.ADCD, 6)
        LEFT JOIN (select LEFT (ADCD, 6)+'000000000' ADCD, MAX (systm) TM,
                        CASE WHEN YEAR (MAX (systm)) = YEAR (GETDATE()) THEN '是' ELSE '否' END WHETHER
                    from BSN_SH_Person_B GROUP BY LEFT (ADCD, 6)
                    ) C  ON LEFT (A.ADCD, 6) = LEFT (C.ADCD,6)
        LEFT JOIN (select LEFT (ADCD, 6)+'000000000' ADCD, MAX (TS) TM,
                        CASE WHEN YEAR (MAX (TS)) = YEAR (GETDATE()) THEN '是' ELSE '否' END WHETHER
                    from BNS_RIVERPERSON_B GROUP BY LEFT (ADCD, 6)
                    ) D ON LEFT (A.ADCD, 6) = LEFT (D.ADCD,6)
        LEFT JOIN (select LEFT (ADCD, 6)+'000000000' ADCD, MAX (TS) TM,
                        CASE WHEN YEAR (MAX (TS)) = YEAR (GETDATE()) THEN '是' ELSE '否' END WHETHER
                    from BNS_DIKEPERSON_B GROUP BY LEFT (ADCD, 6)
                    ) E ON LEFT (A.ADCD, 6) = LEFT (E.ADCD,6)
        LEFT JOIN (select LEFT (ADCD, 6)+'000000000' ADCD, MAX (TS) TM,
                        CASE WHEN YEAR (MAX (TS)) = YEAR (GETDATE()) THEN '是' ELSE '否' END WHETHER
                    from BNS_DPDSPERSON_B GROUP BY LEFT (ADCD, 6)
                    ) F ON LEFT (A.ADCD, 6) = LEFT (F.ADCD,6)
        LEFT JOIN (select LEFT (ADCD, 6)+'000000000' ADCD, MAX (TS) TM,
                        CASE WHEN YEAR (MAX (TS)) = YEAR (GETDATE()) THEN '是' ELSE '否' END WHETHER
                    from BNS_FSDAPERSON_B GROUP BY LEFT (ADCD, 6)
                    ) G ON LEFT (A.ADCD, 6) = LEFT (G.ADCD,6)
        LEFT JOIN (select LEFT (ADCD, 6)+'000000000' ADCD, MAX (TS) TM,
                        CASE WHEN YEAR (MAX (TS)) = YEAR (GETDATE()) THEN '是' ELSE '否' END WHETHER
                    from BNS_VILLAGEPERSON_B GROUP BY LEFT (ADCD, 6)
                    ) H ON LEFT (A.ADCD, 6) = LEFT (H.ADCD,6)
        LEFT JOIN (select LEFT (ADCD, 6)+'000000000' ADCD, MAX (TS) TM,
                        CASE WHEN YEAR (MAX (TS)) = YEAR (GETDATE()) THEN '是' ELSE '否' END WHETHER
                    from BNS_BASPERSON_B GROUP BY LEFT (ADCD, 6)
                    ) I ON LEFT (A.ADCD, 6) = LEFT (I.ADCD,6)
        where A.ADLVL in (2 , 3)
          AND LEFT (A.ADCD , #{level}) = LEFT (#{adcd} , #{level})
        ORDER BY A.ADCD
    </select>


</mapper>
