0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:48 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:48 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:48 +0800] "GET /doc.html HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:48 +0800] "GET /login HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /login?code=iIqAuF&state=d3mWxm HTTP/1.1" 302 -
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /doc.html HTTP/1.1" 200 71645
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/ext/i18n.js HTTP/1.1" 200 15672
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/highlight/styles/docco.css HTTP/1.1" 200 1141
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/ace/bootstrap.min.js HTTP/1.1" 200 27731
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 121209
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/jquery/jquery-1.12.4.min.js HTTP/1.1" 200 97163
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/highlight/styles/style.css HTTP/1.1" 200 22161
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.css HTTP/1.1" 200 4582
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/highlight/styles/default.css HTTP/1.1" 200 1159
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/iconfont/iconfont.css HTTP/1.1" 200 10083
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/layui/css/layui.css HTTP/1.1" 200 63194
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.css?v=1.9.6 HTTP/1.1" 200 13413
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui-md.css?v=1.9.6 HTTP/1.1" 200 123
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/ace/ace.min.js HTTP/1.1" 200 8260
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/css/bootstrap-tabs-x.min.css HTTP/1.1" 200 5805
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/layer3.0.3/layer.js HTTP/1.1" 200 21616
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/ext/editormd.min.css HTTP/1.1" 200 61790
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/jquery/clipboard/clipboard.min.js HTTP/1.1" 200 10662
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/jquery/axios.min.js HTTP/1.1" 200 12941
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/jquery/marked.js HTTP/1.1" 200 38414
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/layui/layui.js HTTP/1.1" 200 6664
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/jquery/template-web.js HTTP/1.1" 200 16504
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/highlight/highlight.pack.js HTTP/1.1" 200 60371
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/bootstrap-tabx/js/bootstrap-tabs-x.min.js HTTP/1.1" 200 3796
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/jquery/json5.js HTTP/1.1" 200 26167
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/jquery/md5.min.js HTTP/1.1" 200 9202
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/jquery/async.min.js HTTP/1.1" 200 19132
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/jquery/showdown.min.js HTTP/1.1" 200 80968
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/ace-editor/ace.js HTTP/1.1" 200 495111
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:57 +0800] "GET /webjars/bycdao-ui/cdao/swaggerbootstrapui.js?v=1.9.6 HTTP/1.1" 200 334496
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:58 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/layer.css?v=3.0.3303 HTTP/1.1" 200 14499
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:58 +0800] "GET /webjars/bycdao-ui/ext/sbuadmin.js HTTP/1.1" 200 2347
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:58 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/layer.js HTTP/1.1" 200 22041
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:59 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/element.js HTTP/1.1" 200 15617
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:59 +0800] "GET /webjars/bycdao-ui/layui/css/modules/layer/default/layer.css?v=3.1.1 HTTP/1.1" 200 5260
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:59 +0800] "GET /webjars/bycdao-ui/ext/treetable.js HTTP/1.1" 200 7678
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:59 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/table.js HTTP/1.1" 200 30342
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:59 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laytpl.js HTTP/1.1" 200 1836
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:59 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/laypage.js HTTP/1.1" 200 4472
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:59 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/form.js HTTP/1.1" 200 9146
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:59 +0800] "GET /webjars/bycdao-ui/layui/lay/modules/util.js HTTP/1.1" 200 3458
0:0:0:0:0:0:0:1 - - [19/May/2025:09:20:59 +0800] "GET /webjars/bycdao-ui/ext/treetable-lay/treetable.css HTTP/1.1" 200 294
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:00 +0800] "GET /swagger-resources HTTP/1.1" 200 101
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:00 +0800] "GET /swagger-resources/configuration/ui HTTP/1.1" 200 462
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:00 +0800] "GET /v2/api-docs HTTP/1.1" 200 2670309
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:09 +0800] "GET /webjars/bycdao-ui/ace-editor/theme-eclipse.js HTTP/1.1" 200 2714
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:09 +0800] "GET /webjars/bycdao-ui/ace-editor/mode-json.js HTTP/1.1" 200 10489
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:09 +0800] "GET /webjars/bycdao-ui/layui/font/iconfont.woff?v=240 HTTP/1.1" 200 26744
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:09 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:09 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:11 +0800] "GET /webjars/bycdao-ui/layer3.0.3/skin/default/loading-1.gif HTTP/1.1" 200 701
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:26 +0800] "POST /api/call/inspect/add-inspect-task HTTP/1.1" 200 152
0:0:0:0:0:0:0:1 - - [19/May/2025:09:21:26 +0800] "GET /webjars/bycdao-ui/ace-editor/worker-json.js HTTP/1.1" 200 72386
************* - - [19/May/2025:09:59:51 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************* - - [19/May/2025:09:59:51 +0800] "GET /login HTTP/1.1" 302 -
************* - - [19/May/2025:10:00:50 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************* - - [19/May/2025:10:00:50 +0800] "GET /login HTTP/1.1" 302 -
************* - - [19/May/2025:10:01:14 +0800] "GET /login?code=Cnpygb&state=rZGleN HTTP/1.1" 302 -
************* - - [19/May/2025:10:01:14 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************* - - [19/May/2025:10:01:16 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1747620077695 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1747620077695 HTTP/1.1" 200 508
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747620079754 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747620079754 HTTP/1.1" 200 59820
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747620079835 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747620079835 HTTP/1.1" 200 10388
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620079857 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620079857 HTTP/1.1" 200 2021
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620079980 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620079980 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:01:19&etm=&_timer304=1747620079980 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620079980 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620079980 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:18 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:10:01:19 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620079980 HTTP/1.1" 200 13016
************* - - [19/May/2025:10:01:20 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:01:19&etm=&_timer304=1747620079980 HTTP/1.1" 200 156
************* - - [19/May/2025:10:01:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:01:20 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620079980 HTTP/1.1" 200 164
************* - - [19/May/2025:10:01:20 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620079980 HTTP/1.1" 200 166
************* - - [19/May/2025:10:01:20 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:10:01:20 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:01:20 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620079980 HTTP/1.1" 200 169
************* - - [19/May/2025:10:01:20 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309402
************* - - [19/May/2025:10:01:20 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1747620081970 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:20 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1747620081970 HTTP/1.1" 200 1560
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-19&_timer304=1747620082303 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/base/saas/token?_timer304=1747620082303 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620082312 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/duty/event/select-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/duty/event/listEventType HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309402
************* - - [19/May/2025:10:01:21 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:21 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286484
************* - - [19/May/2025:10:01:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************* - - [19/May/2025:10:01:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************* - - [19/May/2025:10:01:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [19/May/2025:10:01:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************* - - [19/May/2025:10:01:22 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************* - - [19/May/2025:10:01:22 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 320
************* - - [19/May/2025:10:01:22 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************* - - [19/May/2025:10:01:22 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
************* - - [19/May/2025:10:01:22 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620082312 HTTP/1.1" 200 2021
************* - - [19/May/2025:10:01:22 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************* - - [19/May/2025:10:01:22 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************* - - [19/May/2025:10:01:22 +0800] "POST /api/duty/event/select-list HTTP/1.1" 200 111
************* - - [19/May/2025:10:01:22 +0800] "POST /api/duty/event/listEventType HTTP/1.1" 200 231
************* - - [19/May/2025:10:01:22 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************* - - [19/May/2025:10:01:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
************* - - [19/May/2025:10:01:23 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
************* - - [19/May/2025:10:01:23 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-19&_timer304=1747620082303 HTTP/1.1" 200 347
************* - - [19/May/2025:10:01:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747620087655 HTTP/1.1" 200 -
************* - - [19/May/2025:10:01:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747620087655 HTTP/1.1" 200 160
************* - - [19/May/2025:10:01:29 +0800] "GET /api/base/saas/token?_timer304=1747620082303 HTTP/1.1" 200 411
************* - - [19/May/2025:10:02:17 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:02:19&etm=&_timer304=1747620139165 HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:17 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620139165 HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620139165 HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620139165 HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:17 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:10:02:17 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:10:02:17 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620139165 HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:17 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:02:17 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:02:17 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:17 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:17 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620139165 HTTP/1.1" 200 166
************* - - [19/May/2025:10:02:17 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:02:19&etm=&_timer304=1747620139165 HTTP/1.1" 200 156
************* - - [19/May/2025:10:02:17 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620139165 HTTP/1.1" 200 164
************* - - [19/May/2025:10:02:18 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620139165 HTTP/1.1" 200 169
************* - - [19/May/2025:10:02:18 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:10:02:18 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620139165 HTTP/1.1" 200 13016
************* - - [19/May/2025:10:02:18 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:02:19 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:19 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:10:02:21 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747620142411 HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:21 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747620142411 HTTP/1.1" 200 3321
************* - - [19/May/2025:10:02:33 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747620154297 HTTP/1.1" 200 -
************* - - [19/May/2025:10:02:33 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747620154297 HTTP/1.1" 200 3321
************* - - [19/May/2025:10:06:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747620378173 HTTP/1.1" 200 -
************* - - [19/May/2025:10:06:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747620378173 HTTP/1.1" 200 160
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:10:15&etm=&_timer304=1747620615027 HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620615027 HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620615027 HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620615027 HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620615027 HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:13 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:14 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:10:10:14 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:10:15 +0800] "GET /client/oauth2/token?redirect_url=http://*************:9528/ HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:10:10:15 +0800] "GET /login HTTP/1.1" 302 -
************* - - [19/May/2025:10:10:15 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:10:15 +0800] "GET /login?code=tDInzW&state=jxe92S HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:10:10:15 +0800] "GET /client/oauth2/token?redirect_url=http://*************:9528/ HTTP/1.1" 302 -
************* - - [19/May/2025:10:10:16 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:16 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1747620616662 HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:17 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:10:17 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:10:15&etm=&_timer304=1747620615027 HTTP/1.1" 200 156
************* - - [19/May/2025:10:10:17 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620615027 HTTP/1.1" 200 166
************* - - [19/May/2025:10:10:17 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620615027 HTTP/1.1" 200 164
************* - - [19/May/2025:10:10:17 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:10:17 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620615027 HTTP/1.1" 200 169
************* - - [19/May/2025:10:10:17 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:10:10:18 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620615027 HTTP/1.1" 200 13016
************* - - [19/May/2025:10:10:18 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
192.168.1.33 - - [19/May/2025:10:10:18 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1747620616662 HTTP/1.1" 200 508
192.168.1.33 - - [19/May/2025:10:10:18 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747620618879 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:18 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747620618879 HTTP/1.1" 200 59820
192.168.1.33 - - [19/May/2025:10:10:18 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747620618911 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:18 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747620618911 HTTP/1.1" 200 10388
192.168.1.33 - - [19/May/2025:10:10:18 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620618928 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:18 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620618928 HTTP/1.1" 200 2021
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:10:19&etm=&_timer304=1747620619083 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620619084 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620619084 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620619084 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620619084 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:10:19&etm=&_timer304=1747620619083 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620619084 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620619084 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620619084 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620619084 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1747620619313 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1747620619313 HTTP/1.1" 200 1560
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/base/saas/token?_timer304=1747620619705 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-19&_timer304=1747620619705 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620619719 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/duty/event/listEventType HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/duty/event/select-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:19 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:20 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286484
************* - - [19/May/2025:10:10:20 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747620621908 HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:20 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747620621908 HTTP/1.1" 200 3321
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 320
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620619719 HTTP/1.1" 200 2021
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/duty/event/select-list HTTP/1.1" 200 111
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/duty/event/listEventType HTTP/1.1" 200 231
192.168.1.33 - - [19/May/2025:10:10:21 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
192.168.1.33 - - [19/May/2025:10:10:22 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
192.168.1.33 - - [19/May/2025:10:10:22 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
192.168.1.33 - - [19/May/2025:10:10:22 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-19&_timer304=1747620619705 HTTP/1.1" 200 347
************* - - [19/May/2025:10:10:23 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747620624869 HTTP/1.1" 200 -
************* - - [19/May/2025:10:10:23 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747620624869 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:10:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747620626389 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747620626389 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:10:26 +0800] "GET /api/base/saas/token?_timer304=1747620619705 HTTP/1.1" 200 411
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "OPTIONS /api/ewci/base/mal/write/939?_timer304=1747620634102 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:10:34&etm=&_timer304=1747620634225 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620634225 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620634225 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620634225 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620634225 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:10:34&etm=&_timer304=1747620634225 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620634225 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620634225 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620634225 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620634225 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:10:10:34 +0800] "GET /api/ewci/base/mal/write/939?_timer304=1747620634102 HTTP/1.1" 200 146
192.168.1.33 - - [19/May/2025:10:10:40 +0800] "OPTIONS /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:40 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1747620640384 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:40 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1747620640384 HTTP/1.1" 200 12285
192.168.1.33 - - [19/May/2025:10:10:40 +0800] "POST /api/call/inspect/select-rsvr-person-list HTTP/1.1" 200 479624
192.168.1.33 - - [19/May/2025:10:10:44 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:10:44 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
192.168.1.33 - - [19/May/2025:10:10:48 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
192.168.1.33 - - [19/May/2025:10:10:49 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
192.168.1.33 - - [19/May/2025:10:11:19 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
192.168.1.33 - - [19/May/2025:10:11:42 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
192.168.1.33 - - [19/May/2025:10:11:44 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
************* - - [19/May/2025:10:12:14 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747620735761 HTTP/1.1" 200 -
************* - - [19/May/2025:10:12:14 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747620735761 HTTP/1.1" 200 3321
192.168.1.33 - - [19/May/2025:10:12:27 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:27 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:27 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:12:27 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:12:36&etm=&_timer304=1747620756136 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620756136 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620756136 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620756136 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620756136 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:12:36&etm=&_timer304=1747620756136 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620756136 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620756136 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620756136 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:10:12:36 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620756136 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:10:12:45 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747620765915 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:12:45 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747620765915 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:12:48 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 920
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:13:05&etm=&_timer304=1747620785590 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620785590 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620785590 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620785590 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620785590 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:13:05&etm=&_timer304=1747620785590 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620785590 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620785590 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620785590 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:10:13:05 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620785590 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:10:13:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747620795416 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747620795416 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:13:20 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:13:20 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 920
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:14:02&etm=&_timer304=1747620842652 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620842652 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620842652 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620842652 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620842652 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:14:02&etm=&_timer304=1747620842652 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620842652 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620842652 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620842652 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:10:14:02 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620842652 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:10:14:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747620852468 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747620852468 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:14:22 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:14:22 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 920
************* - - [19/May/2025:10:15:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747620914871 HTTP/1.1" 200 -
************* - - [19/May/2025:10:15:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747620914871 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:15:30 +0800] "GET /client/oauth2/token?redirect_url=http://*************:9528/?redirect=%252FXcc%252FxccSetting%253Ftmpparams%253D%257B%2522title%2522%253A%2522%25E8%25AF%25AD%25E9%259F%25B3%25E5%25A4%2596%25E5%2591%25BC%25E8%25AE%25BE%25E7%25BD%25AE%2522%257D HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:10:15:30 +0800] "GET /login HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:10:15:36 +0800] "GET /login?code=AdtivD&state=AI2w5e HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:10:15:36 +0800] "GET /client/oauth2/token?redirect_url=http://*************:9528/?redirect=%252FXcc%252FxccSetting%253Ftmpparams%253D%257B%2522title%2522%253A%2522%25E8%25AF%25AD%25E9%259F%25B3%25E5%25A4%2596%25E5%2591%25BC%25E8%25AE%25BE%25E7%25BD%25AE%2522%257D HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:10:15:41 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1747620941218 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:41 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1747620941218 HTTP/1.1" 200 508
192.168.1.33 - - [19/May/2025:10:15:42 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747620942036 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:42 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747620942036 HTTP/1.1" 200 59820
192.168.1.33 - - [19/May/2025:10:15:42 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747620942903 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:42 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747620942903 HTTP/1.1" 200 10388
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620943442 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747620943442 HTTP/1.1" 200 2021
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:15:43&etm=&_timer304=1747620943928 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620943928 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620943928 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620943928 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620943928 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:15:43&etm=&_timer304=1747620943928 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747620943928 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747620943928 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:15:43 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:15:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747620943928 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:10:15:44 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747620943928 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:10:15:44 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 920
192.168.1.33 - - [19/May/2025:10:15:51 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747620951032 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:51 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747620951032 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:15:58 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:15:58 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 920
192.168.1.33 - - [19/May/2025:10:16:07 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 920
************* - - [19/May/2025:10:16:39 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621001048 HTTP/1.1" 200 -
************* - - [19/May/2025:10:16:39 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621001048 HTTP/1.1" 200 3321
************* - - [19/May/2025:10:17:25 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621046535 HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:25 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621046535 HTTP/1.1" 200 3321
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621066511 HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:17:46&etm=&_timer304=1747621066511 HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621066511 HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621066511 HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621066511 HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:45 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:10:17:45 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:10:17:45 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:10:17:45 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621066511 HTTP/1.1" 200 166
************* - - [19/May/2025:10:17:45 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:17:45 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:17:46&etm=&_timer304=1747621066511 HTTP/1.1" 200 156
************* - - [19/May/2025:10:17:45 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:17:45 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:17:45 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621066511 HTTP/1.1" 200 164
************* - - [19/May/2025:10:17:45 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621066511 HTTP/1.1" 200 169
************* - - [19/May/2025:10:17:45 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621066511 HTTP/1.1" 200 13016
************* - - [19/May/2025:10:17:49 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:49 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:10:17:51 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621072822 HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:51 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621072822 HTTP/1.1" 200 3321
************* - - [19/May/2025:10:17:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621076372 HTTP/1.1" 200 -
************* - - [19/May/2025:10:17:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621076372 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:18:38 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1652
192.168.1.33 - - [19/May/2025:10:18:43 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1329
192.168.1.33 - - [19/May/2025:10:18:51 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
192.168.1.33 - - [19/May/2025:10:19:03 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621143078 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:19:03 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621143078 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:19:06 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:10:19:32 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621173821 HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:32 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621173821 HTTP/1.1" 200 3321
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:20:00&etm=&_timer304=1747621200849 HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621200849 HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621200849 HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621200849 HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621200849 HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:19:59 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:10:19:59 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:20:00&etm=&_timer304=1747621200849 HTTP/1.1" 200 156
************* - - [19/May/2025:10:19:59 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621200849 HTTP/1.1" 200 166
************* - - [19/May/2025:10:19:59 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:10:19:59 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:10:19:59 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:19:59 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621200849 HTTP/1.1" 200 164
************* - - [19/May/2025:10:19:59 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:19:59 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:19:59 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621200849 HTTP/1.1" 200 169
************* - - [19/May/2025:10:19:59 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621200849 HTTP/1.1" 200 13016
************* - - [19/May/2025:10:20:00 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:20:00 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:10:20:02 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621203371 HTTP/1.1" 200 -
************* - - [19/May/2025:10:20:02 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621203371 HTTP/1.1" 200 3321
************* - - [19/May/2025:10:20:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621210706 HTTP/1.1" 200 -
************* - - [19/May/2025:10:20:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621210706 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:20:16 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
192.168.1.33 - - [19/May/2025:10:20:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621241979 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:20:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621241979 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:20:47 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:10:21:17 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621278999 HTTP/1.1" 200 -
************* - - [19/May/2025:10:21:17 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747621278999 HTTP/1.1" 200 3321
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:21:25&etm=&_timer304=1747621285855 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621285856 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621285856 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621285856 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621285856 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:21:25&etm=&_timer304=1747621285855 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621285856 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621285856 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621285856 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:10:21:25 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621285856 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:21:24&etm=&_timer304=1747621284800 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621284800 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621284800 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621284800 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621284800 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:21:24&etm=&_timer304=1747621284800 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621284800 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621284800 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621284800 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:10:21:26 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621284800 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:10:21:32 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
192.168.1.33 - - [19/May/2025:10:21:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621294411 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621294411 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:21:36 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621296001 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:21:36 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621296001 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:21:58 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:21:58 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:23:02 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:23:02 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:10:23:21 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:23:21 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
192.168.1.33 - - [19/May/2025:10:24:00 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:10:25:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621501502 HTTP/1.1" 200 -
************* - - [19/May/2025:10:25:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621501502 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:25:14 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:25:14 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:25:14 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:25:14 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:25:14 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:10:25:14 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:25:14 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:25:14 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:26:13 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
192.168.1.33 - - [19/May/2025:10:26:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621584978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:26:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621584978 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:26:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621585574 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:26:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621585574 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:26:25 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1652
************* - - [19/May/2025:10:26:29 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:26:29 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:10:27:23 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:27:23 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:10:27:26 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:27:26 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1329
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:28:22&etm=&_timer304=1747621702305 HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621702305 HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621702305 HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621702305 HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621702305 HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:21 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:10:28:21 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:10:28:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:10:28:21 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:28:22&etm=&_timer304=1747621702305 HTTP/1.1" 200 156
************* - - [19/May/2025:10:28:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747621702305 HTTP/1.1" 200 166
************* - - [19/May/2025:10:28:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:28:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:28:21 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:28:21 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747621702305 HTTP/1.1" 200 164
************* - - [19/May/2025:10:28:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747621702305 HTTP/1.1" 200 169
************* - - [19/May/2025:10:28:21 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747621702305 HTTP/1.1" 200 13016
************* - - [19/May/2025:10:28:22 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:22 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:10:28:30 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621712159 HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:30 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621712159 HTTP/1.1" 200 160
************* - - [19/May/2025:10:28:32 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:32 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:10:28:59 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:10:28:59 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1652
192.168.1.33 - - [19/May/2025:10:31:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621884968 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:31:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621884968 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:31:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747621885966 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:31:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747621885966 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:32:17 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:32:17 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:32:17 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:32:17 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:32:17 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:32:17 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:10:32:17 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:10:32:17 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:33:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622003415 HTTP/1.1" 200 -
************* - - [19/May/2025:10:33:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622003415 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:34:33 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:34:33 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:34:33 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
192.168.1.33 - - [19/May/2025:10:34:33 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:34:33 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:10:34:33 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:34:33 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:10:34:33 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
192.168.1.33 - - [19/May/2025:10:36:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622184966 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:36:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622184966 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:36:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622185978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:36:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622185978 HTTP/1.1" 200 160
************* - - [19/May/2025:10:38:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622302163 HTTP/1.1" 200 -
************* - - [19/May/2025:10:38:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622302163 HTTP/1.1" 200 160
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:41:22&etm=&_timer304=1747622482795 HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747622482795 HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747622482795 HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747622482795 HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747622482795 HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:21 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:10:41:21 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+10:41:22&etm=&_timer304=1747622482795 HTTP/1.1" 200 156
************* - - [19/May/2025:10:41:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747622482795 HTTP/1.1" 200 166
************* - - [19/May/2025:10:41:21 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:10:41:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:10:41:21 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:10:41:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:41:21 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+11:00&filterCnt=6&_timer304=1747622482795 HTTP/1.1" 200 164
************* - - [19/May/2025:10:41:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:10:41:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747622482795 HTTP/1.1" 200 169
************* - - [19/May/2025:10:41:21 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747622482795 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:10:41:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622484977 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:41:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622484977 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:41:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622485976 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:41:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622485976 HTTP/1.1" 200 160
************* - - [19/May/2025:10:41:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622493170 HTTP/1.1" 200 -
************* - - [19/May/2025:10:41:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622493170 HTTP/1.1" 200 160
************* - - [19/May/2025:10:44:16 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1747622657698 HTTP/1.1" 200 -
************* - - [19/May/2025:10:44:16 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:44:16 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1747622657698 HTTP/1.1" 200 12285
************* - - [19/May/2025:10:44:16 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:10:46:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622783171 HTTP/1.1" 200 -
************* - - [19/May/2025:10:46:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622783171 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:46:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622784975 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:46:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622784975 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:46:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747622785971 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:46:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747622785971 HTTP/1.1" 200 160
************* - - [19/May/2025:10:47:03 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:47:04 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:10:50:56 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:10:50:57 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:10:51:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623082659 HTTP/1.1" 200 -
************* - - [19/May/2025:10:51:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623082659 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:52:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623129968 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:52:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623129967 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:52:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623129968 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:52:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623129967 HTTP/1.1" 200 160
************* - - [19/May/2025:10:56:22 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623383579 HTTP/1.1" 200 -
************* - - [19/May/2025:10:56:22 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623383579 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:58:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623489969 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:58:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623489969 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:10:58:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623489969 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:10:58:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623489969 HTTP/1.1" 200 160
************* - - [19/May/2025:11:02:23 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623745105 HTTP/1.1" 200 -
************* - - [19/May/2025:11:02:23 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623745105 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:03:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623789977 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:03:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623789978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:03:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623789977 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:03:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623789978 HTTP/1.1" 200 160
************* - - [19/May/2025:11:05:17 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:18 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:11:05:20 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:20 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 37487
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+11:05:39&etm=&_timer304=1747623939140 HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747623939140 HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+12:00&filterCnt=6&_timer304=1747623939140 HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747623939140 HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747623939140 HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:11:05:37 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:11:05:37 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:11:05:37 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:11:05:37 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+11:05:39&etm=&_timer304=1747623939140 HTTP/1.1" 200 156
************* - - [19/May/2025:11:05:37 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+12:00&filterCnt=6&_timer304=1747623939140 HTTP/1.1" 200 164
************* - - [19/May/2025:11:05:37 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747623939140 HTTP/1.1" 200 166
************* - - [19/May/2025:11:05:37 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:11:05:37 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:11:05:37 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:11:05:37 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747623939140 HTTP/1.1" 200 169
************* - - [19/May/2025:11:05:37 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747623939140 HTTP/1.1" 200 13016
************* - - [19/May/2025:11:06:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747623972574 HTTP/1.1" 200 -
************* - - [19/May/2025:11:06:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747623972574 HTTP/1.1" 200 160
************* - - [19/May/2025:11:07:00 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:11:07:00 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
192.168.1.33 - - [19/May/2025:11:08:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624089978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:08:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624089975 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:08:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624089978 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:08:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624089975 HTTP/1.1" 200 160
************* - - [19/May/2025:11:08:22 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:11:08:22 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:11:09:31 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:11:09:32 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:11:09:35 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:11:09:35 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 32257
************* - - [19/May/2025:11:10:49 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624250267 HTTP/1.1" 200 -
************* - - [19/May/2025:11:10:49 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624250267 HTTP/1.1" 200 160
************* - - [19/May/2025:11:12:29 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:11:12:29 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
192.168.1.33 - - [19/May/2025:11:13:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624389972 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:13:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624389971 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:13:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624389972 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:13:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624389971 HTTP/1.1" 200 160
************* - - [19/May/2025:11:15:37 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624539165 HTTP/1.1" 200 -
************* - - [19/May/2025:11:15:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624539165 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:18:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624689968 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:18:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624689969 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:18:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624689968 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:18:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624689969 HTTP/1.1" 200 160
************* - - [19/May/2025:11:20:51 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624852923 HTTP/1.1" 200 -
************* - - [19/May/2025:11:20:51 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624852923 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:23:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624989978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:23:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747624989978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:23:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624989978 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:23:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747624989978 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:26:26 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747625186685 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:26:26 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747625186685 HTTP/1.1" 200 160
************* - - [19/May/2025:11:27:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747625240726 HTTP/1.1" 200 -
************* - - [19/May/2025:11:27:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747625240726 HTTP/1.1" 200 160
************* - - [19/May/2025:11:27:20 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:11:27:21 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 178969
192.168.1.33 - - [19/May/2025:11:28:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747625289974 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:28:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747625289974 HTTP/1.1" 200 160
************* - - [19/May/2025:11:32:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747625541272 HTTP/1.1" 200 -
************* - - [19/May/2025:11:32:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747625541272 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:33:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747625589976 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:33:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747625589976 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:38:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747625889975 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:38:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747625889975 HTTP/1.1" 200 160
************* - - [19/May/2025:11:38:17 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747625898467 HTTP/1.1" 200 -
************* - - [19/May/2025:11:38:17 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747625898467 HTTP/1.1" 200 160
************* - - [19/May/2025:11:43:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747626190836 HTTP/1.1" 200 -
************* - - [19/May/2025:11:43:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747626190836 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:43:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747626189978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:43:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747626189978 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:48:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747626489970 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:48:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747626489970 HTTP/1.1" 200 160
************* - - [19/May/2025:11:48:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747626501552 HTTP/1.1" 200 -
************* - - [19/May/2025:11:48:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747626501552 HTTP/1.1" 200 160
************* - - [19/May/2025:11:52:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747626774173 HTTP/1.1" 200 -
************* - - [19/May/2025:11:52:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747626774173 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:53:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747626789971 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:53:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747626789971 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:11:58:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747627089965 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:11:58:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747627089965 HTTP/1.1" 200 160
************* - - [19/May/2025:11:58:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747627107158 HTTP/1.1" 200 -
************* - - [19/May/2025:11:58:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747627107158 HTTP/1.1" 200 160
************* - - [19/May/2025:12:02:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747627352242 HTTP/1.1" 200 -
************* - - [19/May/2025:12:02:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747627352242 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:12:03:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747627390001 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:12:03:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747627390001 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:12:08:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747627689976 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:12:08:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747627689976 HTTP/1.1" 200 160
************* - - [19/May/2025:12:08:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747627691338 HTTP/1.1" 200 -
************* - - [19/May/2025:12:08:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747627691338 HTTP/1.1" 200 160
************* - - [19/May/2025:12:13:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747627981959 HTTP/1.1" 200 -
************* - - [19/May/2025:12:13:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747627981959 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:12:13:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747627989969 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:12:13:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747627989969 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:12:18:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747628289978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:12:18:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747628289978 HTTP/1.1" 200 160
************* - - [19/May/2025:12:19:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747628342914 HTTP/1.1" 200 -
************* - - [19/May/2025:12:19:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747628342914 HTTP/1.1" 200 160
************* - - [19/May/2025:12:23:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747628589671 HTTP/1.1" 200 -
************* - - [19/May/2025:12:23:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747628589671 HTTP/1.1" 200 160
************* - - [19/May/2025:12:26:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747628781956 HTTP/1.1" 200 -
************* - - [19/May/2025:12:26:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747628781956 HTTP/1.1" 200 160
************* - - [19/May/2025:12:32:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747629146132 HTTP/1.1" 200 -
************* - - [19/May/2025:12:32:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747629146132 HTTP/1.1" 200 160
************* - - [19/May/2025:12:37:27 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747629448605 HTTP/1.1" 200 -
************* - - [19/May/2025:12:37:27 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747629448605 HTTP/1.1" 200 160
************* - - [19/May/2025:12:42:41 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747629762922 HTTP/1.1" 200 -
************* - - [19/May/2025:12:42:41 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747629762922 HTTP/1.1" 200 160
************* - - [19/May/2025:12:49:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747630179952 HTTP/1.1" 200 -
************* - - [19/May/2025:12:49:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747630179952 HTTP/1.1" 200 160
************* - - [19/May/2025:12:52:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747630342544 HTTP/1.1" 200 -
************* - - [19/May/2025:12:52:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747630342544 HTTP/1.1" 200 160
************* - - [19/May/2025:12:57:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747630675971 HTTP/1.1" 200 -
************* - - [19/May/2025:12:57:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747630675971 HTTP/1.1" 200 160
************* - - [19/May/2025:13:02:59 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747630980691 HTTP/1.1" 200 -
************* - - [19/May/2025:13:02:59 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747630980691 HTTP/1.1" 200 160
************* - - [19/May/2025:13:07:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747631256481 HTTP/1.1" 200 -
************* - - [19/May/2025:13:07:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747631256481 HTTP/1.1" 200 160
************* - - [19/May/2025:13:13:08 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747631589798 HTTP/1.1" 200 -
************* - - [19/May/2025:13:13:08 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747631589798 HTTP/1.1" 200 160
************* - - [19/May/2025:13:17:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747631843044 HTTP/1.1" 200 -
************* - - [19/May/2025:13:17:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747631843044 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:13:19:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747631944302 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:13:19:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747631944302 HTTP/1.1" 200 160
************* - - [19/May/2025:13:21:33 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747632095084 HTTP/1.1" 200 -
************* - - [19/May/2025:13:21:33 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747632095084 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:13:23:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747632189972 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:13:23:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747632189972 HTTP/1.1" 200 160
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+13:23:30&etm=&_timer304=1747632210667 HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747632210667 HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+14:00&filterCnt=6&_timer304=1747632210667 HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747632210667 HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747632210667 HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:29 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:13:23:29 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:13:23:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747632210667 HTTP/1.1" 200 166
************* - - [19/May/2025:13:23:29 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:13:23:29 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+13:23:30&etm=&_timer304=1747632210667 HTTP/1.1" 200 156
************* - - [19/May/2025:13:23:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:13:23:29 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+14:00&filterCnt=6&_timer304=1747632210667 HTTP/1.1" 200 164
************* - - [19/May/2025:13:23:29 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:13:23:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:13:23:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747632210667 HTTP/1.1" 200 169
************* - - [19/May/2025:13:23:29 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747632210667 HTTP/1.1" 200 13016
************* - - [19/May/2025:13:23:34 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:35 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:13:23:35 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:36 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 178969
************* - - [19/May/2025:13:23:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747632220462 HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747632220462 HTTP/1.1" 200 160
************* - - [19/May/2025:13:23:44 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:44 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 3531
************* - - [19/May/2025:13:23:45 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:45 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 178969
************* - - [19/May/2025:13:23:52 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:53 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:13:23:53 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:23:54 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 166516
************* - - [19/May/2025:13:24:01 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:24:02 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:13:24:03 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:24:04 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 178969
************* - - [19/May/2025:13:24:05 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:24:05 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 166516
************* - - [19/May/2025:13:24:27 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:24:28 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:13:24:28 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:24:29 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 470719
************* - - [19/May/2025:13:24:30 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:24:30 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
************* - - [19/May/2025:13:27:11 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:27:11 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:13:27:22 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:27:22 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 470719
************* - - [19/May/2025:13:27:24 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:27:24 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 2263
192.168.1.33 - - [19/May/2025:13:28:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747632489969 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:13:28:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747632489969 HTTP/1.1" 200 160
************* - - [19/May/2025:13:28:19 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:28:19 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 1417532
************* - - [19/May/2025:13:28:30 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747632511947 HTTP/1.1" 200 -
************* - - [19/May/2025:13:28:30 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747632511947 HTTP/1.1" 200 160
************* - - [19/May/2025:13:28:32 +0800] "OPTIONS /api/call/inspect/select-person-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:28:32 +0800] "POST /api/call/inspect/select-person-list HTTP/1.1" 200 31773
************* - - [19/May/2025:13:28:48 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:28:48 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 2431
************* - - [19/May/2025:13:28:52 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:28:52 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:13:28:57 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:28:57 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1329
************* - - [19/May/2025:13:28:59 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:28:59 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 920
************* - - [19/May/2025:13:29:09 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:29:09 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:13:29:10 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747632551848 HTTP/1.1" 200 -
************* - - [19/May/2025:13:29:10 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747632551848 HTTP/1.1" 200 157
************* - - [19/May/2025:13:29:22 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747632563257 HTTP/1.1" 200 -
************* - - [19/May/2025:13:29:22 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747632563257 HTTP/1.1" 200 157
************* - - [19/May/2025:13:29:46 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747632587998 HTTP/1.1" 200 -
************* - - [19/May/2025:13:29:46 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747632587998 HTTP/1.1" 200 157
************* - - [19/May/2025:13:30:03 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747632605048 HTTP/1.1" 200 -
************* - - [19/May/2025:13:30:03 +0800] "GET /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747632605048 HTTP/1.1" 200 586
192.168.1.33 - - [19/May/2025:13:33:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747632789964 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:13:33:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747632789964 HTTP/1.1" 200 160
************* - - [19/May/2025:13:33:29 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747632811168 HTTP/1.1" 200 -
************* - - [19/May/2025:13:33:29 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747632811168 HTTP/1.1" 200 160
************* - - [19/May/2025:13:33:50 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747632832111 HTTP/1.1" 200 -
************* - - [19/May/2025:13:33:50 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747632832111 HTTP/1.1" 200 157
************* - - [19/May/2025:13:36:58 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747633019226 HTTP/1.1" 200 -
************* - - [19/May/2025:13:36:58 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747633019226 HTTP/1.1" 200 157
192.168.1.33 - - [19/May/2025:13:38:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747633089980 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:13:38:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747633089980 HTTP/1.1" 200 160
************* - - [19/May/2025:13:39:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747633153156 HTTP/1.1" 200 -
************* - - [19/May/2025:13:39:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747633153156 HTTP/1.1" 200 160
************* - - [19/May/2025:13:39:13 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747633154751 HTTP/1.1" 200 -
************* - - [19/May/2025:13:39:13 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747633154751 HTTP/1.1" 200 157
************* - - [19/May/2025:13:39:16 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747633158125 HTTP/1.1" 200 -
************* - - [19/May/2025:13:39:16 +0800] "GET /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747633158125 HTTP/1.1" 200 586
************* - - [19/May/2025:13:40:02 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747633204153 HTTP/1.1" 200 -
************* - - [19/May/2025:13:40:02 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747633204153 HTTP/1.1" 200 157
************* - - [19/May/2025:13:40:06 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747633207609 HTTP/1.1" 200 -
************* - - [19/May/2025:13:40:06 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747633207609 HTTP/1.1" 200 157
192.168.1.33 - - [19/May/2025:13:43:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747633389978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:13:43:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747633389978 HTTP/1.1" 200 160
************* - - [19/May/2025:13:43:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747633413596 HTTP/1.1" 200 -
************* - - [19/May/2025:13:43:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747633413596 HTTP/1.1" 200 160
************* - - [19/May/2025:13:43:33 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747633415178 HTTP/1.1" 200 -
************* - - [19/May/2025:13:43:33 +0800] "GET /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747633415178 HTTP/1.1" 200 586
************* - - [19/May/2025:13:43:40 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747633421436 HTTP/1.1" 200 -
************* - - [19/May/2025:13:43:40 +0800] "GET /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747633421436 HTTP/1.1" 200 586
192.168.1.33 - - [19/May/2025:13:48:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747633689975 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:13:48:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747633689975 HTTP/1.1" 200 160
************* - - [19/May/2025:13:48:27 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:48:27 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:13:48:28 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747633710210 HTTP/1.1" 200 -
************* - - [19/May/2025:13:48:29 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747633710210 HTTP/1.1" 200 3321
************* - - [19/May/2025:13:48:29 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747633710465 HTTP/1.1" 200 -
************* - - [19/May/2025:13:48:29 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747633710465 HTTP/1.1" 200 160
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+13:51:49&etm=&_timer304=1747633909253 HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747633909253 HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747633909253 HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+14:00&filterCnt=6&_timer304=1747633909253 HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747633909253 HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:48 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:13:51:48 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:13:51:48 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+13:51:49&etm=&_timer304=1747633909253 HTTP/1.1" 200 156
************* - - [19/May/2025:13:51:48 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:13:51:48 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:13:51:48 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:13:51:48 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747633909253 HTTP/1.1" 200 166
************* - - [19/May/2025:13:51:48 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:13:51:48 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+14:00&filterCnt=6&_timer304=1747633909253 HTTP/1.1" 200 164
************* - - [19/May/2025:13:51:48 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747633909253 HTTP/1.1" 200 169
************* - - [19/May/2025:13:51:48 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747633909253 HTTP/1.1" 200 13016
************* - - [19/May/2025:13:51:49 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:49 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:13:51:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747633919104 HTTP/1.1" 200 -
************* - - [19/May/2025:13:51:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747633919104 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:13:53:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747633990063 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:13:53:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747633990063 HTTP/1.1" 200 160
************* - - [19/May/2025:13:53:27 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:53:27 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:13:54:02 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747634044018 HTTP/1.1" 200 -
************* - - [19/May/2025:13:54:02 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747634044018 HTTP/1.1" 200 3321
************* - - [19/May/2025:13:55:24 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:55:24 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:13:55:29 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747634131200 HTTP/1.1" 200 -
************* - - [19/May/2025:13:55:30 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747634131200 HTTP/1.1" 200 3321
************* - - [19/May/2025:13:56:22 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:56:22 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:13:56:47 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747634209164 HTTP/1.1" 200 -
************* - - [19/May/2025:13:56:47 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747634209164 HTTP/1.1" 200 160
************* - - [19/May/2025:13:57:34 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747634255312 HTTP/1.1" 200 -
************* - - [19/May/2025:13:57:34 +0800] "GET /api/call/common/message/call-result?extSendId=bb30e631-2829-4d1b-9eac-f0ab24e592bc&_timer304=1747634255312 HTTP/1.1" 200 3321
************* - - [19/May/2025:13:57:38 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:57:38 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:13:57:39 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747634260308 HTTP/1.1" 200 -
************* - - [19/May/2025:13:57:39 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747634260308 HTTP/1.1" 200 157
************* - - [19/May/2025:13:57:39 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747634260741 HTTP/1.1" 200 -
************* - - [19/May/2025:13:57:39 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747634260741 HTTP/1.1" 200 157
192.168.1.33 - - [19/May/2025:13:58:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747634289965 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:13:58:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747634289965 HTTP/1.1" 200 160
************* - - [19/May/2025:13:58:42 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:58:42 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:13:58:54 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747634336009 HTTP/1.1" 200 -
************* - - [19/May/2025:13:58:54 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747634336009 HTTP/1.1" 200 157
************* - - [19/May/2025:13:58:55 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747634336750 HTTP/1.1" 200 -
************* - - [19/May/2025:13:58:55 +0800] "GET /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747634336750 HTTP/1.1" 200 586
************* - - [19/May/2025:13:59:01 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747634342577 HTTP/1.1" 200 -
************* - - [19/May/2025:13:59:01 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747634342577 HTTP/1.1" 200 157
************* - - [19/May/2025:13:59:11 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:59:11 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1329
************* - - [19/May/2025:13:59:22 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:59:22 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 9730
************* - - [19/May/2025:13:59:25 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:59:25 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 4325
************* - - [19/May/2025:13:59:29 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:59:29 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1329
************* - - [19/May/2025:13:59:33 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:59:33 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
************* - - [19/May/2025:13:59:36 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:13:59:36 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1329
************* - - [19/May/2025:14:01:47 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747634509172 HTTP/1.1" 200 -
************* - - [19/May/2025:14:01:47 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747634509172 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:14:03:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747634589970 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:14:03:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747634589970 HTTP/1.1" 200 160
************* - - [19/May/2025:14:07:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747634832428 HTTP/1.1" 200 -
************* - - [19/May/2025:14:07:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747634832428 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:14:08:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747634889975 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:14:08:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747634889975 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:14:13:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747635189974 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:14:13:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747635189974 HTTP/1.1" 200 160
************* - - [19/May/2025:14:14:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747635256558 HTTP/1.1" 200 -
************* - - [19/May/2025:14:14:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747635256558 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:14:18:09 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747635489975 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:14:18:09 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747635489975 HTTP/1.1" 200 160
************* - - [19/May/2025:14:18:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747635535499 HTTP/1.1" 200 -
************* - - [19/May/2025:14:18:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747635535499 HTTP/1.1" 200 160
************* - - [19/May/2025:14:23:46 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747635827912 HTTP/1.1" 200 -
************* - - [19/May/2025:14:23:46 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747635827912 HTTP/1.1" 200 160
************* - - [19/May/2025:14:29:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747636141272 HTTP/1.1" 200 -
************* - - [19/May/2025:14:29:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747636141272 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:14:30:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747636234930 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:14:30:34 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747636234930 HTTP/1.1" 200 160
************* - - [19/May/2025:14:31:47 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747636309115 HTTP/1.1" 200 -
************* - - [19/May/2025:14:31:47 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747636309115 HTTP/1.1" 200 160
************* - - [19/May/2025:14:38:56 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747636737798 HTTP/1.1" 200 -
************* - - [19/May/2025:14:38:56 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747636737798 HTTP/1.1" 200 160
************* - - [19/May/2025:14:44:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747637048962 HTTP/1.1" 200 -
************* - - [19/May/2025:14:44:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747637048962 HTTP/1.1" 200 160
************* - - [19/May/2025:14:48:40 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747637321456 HTTP/1.1" 200 -
************* - - [19/May/2025:14:48:40 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747637321456 HTTP/1.1" 200 160
************* - - [19/May/2025:14:53:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747637592760 HTTP/1.1" 200 -
************* - - [19/May/2025:14:53:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747637592760 HTTP/1.1" 200 160
************* - - [19/May/2025:14:56:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747637818747 HTTP/1.1" 200 -
************* - - [19/May/2025:14:56:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747637818747 HTTP/1.1" 200 160
************* - - [19/May/2025:15:02:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747638136414 HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747638136414 HTTP/1.1" 200 160
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:02:29&etm=&_timer304=1747638149586 HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747638149586 HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747638149586 HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747638149586 HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747638149586 HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:15:02:28 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:15:02:28 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:15:02:28 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:02:29&etm=&_timer304=1747638149586 HTTP/1.1" 200 156
************* - - [19/May/2025:15:02:28 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:15:02:28 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747638149586 HTTP/1.1" 200 166
************* - - [19/May/2025:15:02:28 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:15:02:28 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747638149586 HTTP/1.1" 200 164
************* - - [19/May/2025:15:02:28 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:15:02:28 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:15:02:28 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747638149586 HTTP/1.1" 200 169
************* - - [19/May/2025:15:02:28 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747638149586 HTTP/1.1" 200 13016
************* - - [19/May/2025:15:07:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747638460531 HTTP/1.1" 200 -
************* - - [19/May/2025:15:07:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747638460572 HTTP/1.1" 200 -
************* - - [19/May/2025:15:07:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747638460531 HTTP/1.1" 200 160
************* - - [19/May/2025:15:07:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747638460572 HTTP/1.1" 200 160
************* - - [19/May/2025:15:13:12 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747638796255 HTTP/1.1" 200 -
************* - - [19/May/2025:15:13:12 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747638796255 HTTP/1.1" 200 160
************* - - [19/May/2025:15:18:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747639100650 HTTP/1.1" 200 -
************* - - [19/May/2025:15:18:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747639100650 HTTP/1.1" 200 160
************* - - [19/May/2025:15:24:10 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747639454481 HTTP/1.1" 200 -
************* - - [19/May/2025:15:24:10 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747639454481 HTTP/1.1" 200 160
************* - - [19/May/2025:15:25:17 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:15:25:17 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
************* - - [19/May/2025:15:25:20 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:15:25:20 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
************* - - [19/May/2025:15:25:29 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:15:25:29 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1681
************* - - [19/May/2025:15:25:31 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747639535301 HTTP/1.1" 200 -
************* - - [19/May/2025:15:25:31 +0800] "GET /api/call/common/message/call-result?extSendId=adf30e14-1c70-45b1-972c-83298a24b679&_timer304=1747639535301 HTTP/1.1" 200 157
************* - - [19/May/2025:15:25:32 +0800] "OPTIONS /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747639536851 HTTP/1.1" 200 -
************* - - [19/May/2025:15:25:32 +0800] "GET /api/call/common/message/call-result?extSendId=c970a627-2f4f-4a1d-a871-2c1d28938f2e&_timer304=1747639536851 HTTP/1.1" 200 586
192.168.1.33 - - [19/May/2025:15:25:32 +0800] "GET /client/oauth2/token?redirect_url=http://*************:9528/ HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:15:25:32 +0800] "GET /login HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:15:25:39 +0800] "GET /login?code=8EeRaq&state=v4zIwt HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:15:25:39 +0800] "GET /client/oauth2/token?redirect_url=http://*************:9528/ HTTP/1.1" 302 -
192.168.1.33 - - [19/May/2025:15:25:42 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1747639542708 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:42 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1747639542708 HTTP/1.1" 200 508
192.168.1.33 - - [19/May/2025:15:25:43 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747639543293 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:43 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1747639543293 HTTP/1.1" 200 59820
192.168.1.33 - - [19/May/2025:15:25:43 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747639543421 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:43 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1747639543421 HTTP/1.1" 200 10388
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747639544029 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747639544029 HTTP/1.1" 200 2021
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:25:44&etm=&_timer304=1747639544298 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747639544299 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747639544299 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747639544299 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747639544299 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:25:44&etm=&_timer304=1747639544298 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747639544299 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747639544299 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747639544299 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747639544299 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1747639544448 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1747639544448 HTTP/1.1" 200 1560
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/base/saas/token?_timer304=1747639544867 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-19&_timer304=1747639544867 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1747639544880 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/duty/event/select-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:44 +0800] "OPTIONS /api/duty/event/listEventType HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286484
************* - - [19/May/2025:15:25:45 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:15:25:45 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 3874
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "GET /api/base/saas/token?_timer304=1747639544867 HTTP/1.1" 200 411
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/ewci/soil/select-isojobs-list HTTP/1.1" 200 309387
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 1330
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 320
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1747639544880 HTTP/1.1" 200 2021
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/duty/event/listEventType HTTP/1.1" 200 231
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/duty/event/select-list HTTP/1.1" 200 111
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
192.168.1.33 - - [19/May/2025:15:25:45 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
192.168.1.33 - - [19/May/2025:15:25:46 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 232
192.168.1.33 - - [19/May/2025:15:25:46 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 598
192.168.1.33 - - [19/May/2025:15:25:47 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-05-19&_timer304=1747639544867 HTTP/1.1" 200 347
192.168.1.33 - - [19/May/2025:15:25:52 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747639552571 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:52 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747639552571 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/ewci/base/mal/write/937?_timer304=1747639554032 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:25:54&etm=&_timer304=1747639554165 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747639554165 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747639554165 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747639554165 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747639554165 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/call/stats/select-stats-count-all HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/call/stats/select-stats-success HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/call/stats/select-stats-duration HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "OPTIONS /api/call/stats/select-stats-fail HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "GET /api/ewci/base/mal/write/937?_timer304=1747639554032 HTTP/1.1" 200 146
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:25:54&etm=&_timer304=1747639554165 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747639554165 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747639554165 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 251
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 424
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 419
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747639554165 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
192.168.1.33 - - [19/May/2025:15:25:54 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747639554165 HTTP/1.1" 200 13016
************* - - [19/May/2025:15:29:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747639786421 HTTP/1.1" 200 -
************* - - [19/May/2025:15:29:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747639786421 HTTP/1.1" 200 160
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:30:39&etm=&_timer304=1747639839125 HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/call/inspect/select-page-list HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/call/inspect/select-total-data HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747639839125 HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747639839125 HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747639839125 HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747639839125 HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:34 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************* - - [19/May/2025:15:30:34 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:30:39&etm=&_timer304=1747639839125 HTTP/1.1" 200 156
************* - - [19/May/2025:15:30:34 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747639839125 HTTP/1.1" 200 166
************* - - [19/May/2025:15:30:34 +0800] "POST /api/call/inspect/select-total-data HTTP/1.1" 200 388
************* - - [19/May/2025:15:30:34 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************* - - [19/May/2025:15:30:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:15:30:34 +0800] "POST /api/call/inspect/select-page-list HTTP/1.1" 200 3828
************* - - [19/May/2025:15:30:34 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************* - - [19/May/2025:15:30:34 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747639839125 HTTP/1.1" 200 164
************* - - [19/May/2025:15:30:35 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747639839125 HTTP/1.1" 200 169
************* - - [19/May/2025:15:30:35 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747639839125 HTTP/1.1" 200 13016
************* - - [19/May/2025:15:30:38 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:38 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 1806
************* - - [19/May/2025:15:30:42 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:42 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 2431
192.168.1.33 - - [19/May/2025:15:30:43 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747639843089 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:30:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747639843089 HTTP/1.1" 200 160
************* - - [19/May/2025:15:30:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747639848958 HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747639848958 HTTP/1.1" 200 160
************* - - [19/May/2025:15:30:51 +0800] "OPTIONS /api/call/inspect/inspect-detail HTTP/1.1" 200 -
************* - - [19/May/2025:15:30:51 +0800] "POST /api/call/inspect/inspect-detail HTTP/1.1" 200 920
192.168.1.33 - - [19/May/2025:15:35:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747640142991 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:35:43 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747640142991 HTTP/1.1" 200 160
************* - - [19/May/2025:15:36:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747640184645 HTTP/1.1" 200 -
************* - - [19/May/2025:15:36:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747640184645 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:15:40:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747640442988 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:40:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747640442988 HTTP/1.1" 200 160
************* - - [19/May/2025:15:41:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747640465196 HTTP/1.1" 200 -
************* - - [19/May/2025:15:41:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747640465196 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:15:45:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747640742986 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:45:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747640742986 HTTP/1.1" 200 160
************* - - [19/May/2025:15:46:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747640803840 HTTP/1.1" 200 -
************* - - [19/May/2025:15:46:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747640803840 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:15:50:42 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747641042981 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:50:42 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747641042981 HTTP/1.1" 200 160
************* - - [19/May/2025:15:51:56 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747641120153 HTTP/1.1" 200 -
************* - - [19/May/2025:15:51:56 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747641120153 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:15:56:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747641366984 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:56:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747641366984 HTTP/1.1" 200 160
************* - - [19/May/2025:15:58:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747641497863 HTTP/1.1" 200 -
************* - - [19/May/2025:15:58:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747641497863 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:58:24&etm=&_timer304=1747641504680 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "POST /api/call/stats/select-stats-count-all HTTP/1.1" 200 251
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747641504680 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747641504681 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "POST /api/call/stats/select-stats-duration HTTP/1.1" 200 274
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "POST /api/call/stats/select-stats-count-by-day HTTP/1.1" 200 424
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747641504681 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747641504681 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "POST /api/call/stats/select-stats-success HTTP/1.1" 200 419
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "POST /api/call/stats/select-stats-fail HTTP/1.1" 200 511
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-05-16+15:58:24&etm=&_timer304=1747641504680 HTTP/1.1" 200 156
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1747641504680 HTTP/1.1" 200 166
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-05-19+08:00&etm=2025-05-19+16:00&filterCnt=6&_timer304=1747641504681 HTTP/1.1" 200 164
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1747641504681 HTTP/1.1" 200 169
192.168.1.33 - - [19/May/2025:15:58:24 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1747641504681 HTTP/1.1" 200 13016
192.168.1.33 - - [19/May/2025:15:58:35 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747641515169 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:15:58:35 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747641515169 HTTP/1.1" 200 160
************* - - [19/May/2025:16:03:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747641797662 HTTP/1.1" 200 -
************* - - [19/May/2025:16:03:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747641797662 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:03:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747641805091 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:03:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747641805091 HTTP/1.1" 200 160
************* - - [19/May/2025:16:07:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747642074164 HTTP/1.1" 200 -
************* - - [19/May/2025:16:07:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747642074164 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:08:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747642104991 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:08:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747642104991 HTTP/1.1" 200 160
************* - - [19/May/2025:16:13:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747642385093 HTTP/1.1" 200 -
************* - - [19/May/2025:16:13:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747642385093 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:13:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747642404982 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:13:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747642404982 HTTP/1.1" 200 160
************* - - [19/May/2025:16:16:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747642565780 HTTP/1.1" 200 -
************* - - [19/May/2025:16:16:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747642565780 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:18:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747642704979 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:18:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747642704979 HTTP/1.1" 200 160
************* - - [19/May/2025:16:22:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747642942931 HTTP/1.1" 200 -
************* - - [19/May/2025:16:22:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747642942931 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:23:24 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747643004978 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:23:24 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747643004978 HTTP/1.1" 200 160
************* - - [19/May/2025:16:27:14 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747643239069 HTTP/1.1" 200 -
************* - - [19/May/2025:16:27:14 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747643239069 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:29:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747643346990 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:29:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747643346990 HTTP/1.1" 200 160
************* - - [19/May/2025:16:32:51 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747643575695 HTTP/1.1" 200 -
************* - - [19/May/2025:16:32:51 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747643575695 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:33:48 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747643628418 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:33:48 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747643628418 HTTP/1.1" 200 160
************* - - [19/May/2025:16:38:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747643885507 HTTP/1.1" 200 -
************* - - [19/May/2025:16:38:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747643885507 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:40:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747644006981 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:40:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747644006981 HTTP/1.1" 200 160
************* - - [19/May/2025:16:42:38 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747644162844 HTTP/1.1" 200 -
************* - - [19/May/2025:16:42:38 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747644162844 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:45:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747644306979 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:45:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747644306979 HTTP/1.1" 200 160
************* - - [19/May/2025:16:45:54 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747644358609 HTTP/1.1" 200 -
************* - - [19/May/2025:16:45:54 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747644358609 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:50:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747644606983 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:50:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747644606983 HTTP/1.1" 200 160
************* - - [19/May/2025:16:52:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747644740291 HTTP/1.1" 200 -
************* - - [19/May/2025:16:52:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747644740291 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:16:55:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747644906988 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:16:55:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747644906988 HTTP/1.1" 200 160
************* - - [19/May/2025:16:58:13 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747645097195 HTTP/1.1" 200 -
************* - - [19/May/2025:16:58:13 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747645097195 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:17:00:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747645206979 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:17:00:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747645206979 HTTP/1.1" 200 160
************* - - [19/May/2025:17:02:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747645381709 HTTP/1.1" 200 -
************* - - [19/May/2025:17:02:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747645381709 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:17:05:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747645506988 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:17:05:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747645506988 HTTP/1.1" 200 160
************* - - [19/May/2025:17:06:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747645621735 HTTP/1.1" 200 -
************* - - [19/May/2025:17:06:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747645621735 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:17:10:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747645806980 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:17:10:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747645806980 HTTP/1.1" 200 160
************* - - [19/May/2025:17:13:02 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747645986952 HTTP/1.1" 200 -
************* - - [19/May/2025:17:13:02 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747645986952 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:17:15:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747646106982 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:17:15:06 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747646106982 HTTP/1.1" 200 160
************* - - [19/May/2025:17:17:39 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747646263805 HTTP/1.1" 200 -
************* - - [19/May/2025:17:17:39 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747646263805 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:17:20:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747646406992 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:17:20:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747646406992 HTTP/1.1" 200 160
************* - - [19/May/2025:17:22:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747646540327 HTTP/1.1" 200 -
************* - - [19/May/2025:17:22:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747646540327 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:17:25:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747646706989 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:17:25:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747646706989 HTTP/1.1" 200 160
************* - - [19/May/2025:17:27:02 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747646826695 HTTP/1.1" 200 -
************* - - [19/May/2025:17:27:02 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747646826695 HTTP/1.1" 200 160
192.168.1.33 - - [19/May/2025:17:30:06 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747647006991 HTTP/1.1" 200 -
192.168.1.33 - - [19/May/2025:17:30:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747647006991 HTTP/1.1" 200 160
************* - - [19/May/2025:17:33:04 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1747647184128 HTTP/1.1" 200 -
************* - - [19/May/2025:17:33:04 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1747647184128 HTTP/1.1" 200 160
