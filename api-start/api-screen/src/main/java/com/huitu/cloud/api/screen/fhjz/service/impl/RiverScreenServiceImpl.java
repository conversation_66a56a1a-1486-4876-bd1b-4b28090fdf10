package com.huitu.cloud.api.screen.fhjz.service.impl;

import com.huitu.cloud.api.screen.fhjz.entity.DikeVo;
import com.huitu.cloud.api.screen.fhjz.entity.RiverDetailVo;
import com.huitu.cloud.api.screen.fhjz.entity.RiverVo;
import com.huitu.cloud.api.screen.fhjz.mapper.DikeScreenDao;
import com.huitu.cloud.api.screen.fhjz.mapper.RiverScreenDao;
import com.huitu.cloud.api.screen.fhjz.service.DikeScreenService;
import com.huitu.cloud.api.screen.fhjz.service.RiverScreenService;
import com.huitu.cloud.api.screen.fhjz.utils.DateTimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 河流获取 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Service
public class RiverScreenServiceImpl implements RiverScreenService {

    private static final Logger logger = LoggerFactory.getLogger(RiverScreenServiceImpl.class);

    @Autowired
    private RiverScreenDao riverScreenDao;

    @Override
    public List<RiverVo> queryRiverList(String rvName) {
        return riverScreenDao.queryRiverList(rvName);
    }

    @Override
    public List<RiverDetailVo> riverDetailList(String rvName) {
        return riverScreenDao.queryRiverDetailList(rvName);
    }

    @Override
    public List<Map> riverStationList(String rvCode,String stm,String etm) {
        return riverScreenDao.riverStationList(stm,etm,rvCode);
    }

    @Override
    public Map<String, Object> queryRiverStationInfo(String stcd) {
        return riverScreenDao.queryRiverStationInfo(stcd);
    }

    @Override
    public Map<String, Object> queryRiverInfo(String rvCode) {
        return riverScreenDao.queryRiverInfo(rvCode);
    }

    @Override
    public String queryProjectTotal(String adcd) {
        int rsvrCount = riverScreenDao.queryRsvrTotal(adcd);
        int dikeCount = riverScreenDao.queryDikeTotal(adcd);
        int riverCount = riverScreenDao.queryRiverTotal(adcd);
        return String.format("水库共%d座 堤防共%d处 河流共%d条",rsvrCount,dikeCount,riverCount);
    }

    @Override
    public String statisticRiverDetail() {
        Map<String,Object>  detailMap = riverScreenDao.statisticRiverDetail();
        return String.format("河流共%s条，其中一级河流%s条，二级河流%s条，三级河流%s条，四级河流%s条，五级河流%s条，六级河流%s条，七级河流%s条。",String.valueOf(detailMap.get("TOTAL")),String.valueOf(detailMap.get("LEVEL1")),String.valueOf(detailMap.get("LEVEL2")),String.valueOf(detailMap.get("LEVEL3")),String.valueOf(detailMap.get("LEVEL4")),String.valueOf(detailMap.get("LEVEL5")),String.valueOf(detailMap.get("LEVEL6")),String.valueOf(detailMap.get("LEVEL7")));
    }
}
