package com.huitu.cloud.api.ewci.hydrology.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huitu.cloud.validation.constraints.Option;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 洪水预警指标
 *
 * <AUTHOR>
 */
@ApiModel(value = "洪水预警指标")
public class HyWarnIndex implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测站编码")
    @NotBlank(message = "参数[测站编码]不能为空")
    @Size(max = 8, message = "参数[测站编码]的长度不能超过8个字符")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "指标类型（Z=水位、Q=流量）")
    @NotBlank(message = "参数[指标类型]不能为空")
    @Option(value = {"Z", "Q"}, message = "参数[指标类型]的值无效")
    @TableField(value = "WIDX_TYPE")
    private String widxType;

    @ApiModelProperty(value = "蓝色预警")
    @Digits(integer = 6, fraction = 3, message = "参数[蓝色预警]的格式错误，有效格式：######.###")
    @TableField(value = "WIDX_BLUE")
    private BigDecimal widxBlue;

    @ApiModelProperty(value = "黄色预警")
    @Digits(integer = 6, fraction = 3, message = "参数[黄色预警]的格式错误，有效格式：######.###")
    @TableField(value = "WIDX_YELLOW")
    private BigDecimal widxYellow;

    @ApiModelProperty(value = "橙色预警")
    @Digits(integer = 6, fraction = 3, message = "参数[橙色预警]的格式错误，有效格式：######.###")
    @TableField(value = "WIDX_ORANGE")
    private BigDecimal widxOrange;

    @ApiModelProperty(value = "红色预警")
    @Digits(integer = 6, fraction = 3, message = "参数[红色预警]的格式错误，有效格式：######.###")
    @TableField(value = "WIDX_RED")
    private BigDecimal widxRed;

    @ApiModelProperty(value = "发布单位")
    @NotBlank(message = "参数[发布单位]不能为空")
    @Size(max = 4, message = "参数[发布单位]的长度不能超过8个字符")
    @TableField(value = "RELEASE_UNIT")
    private String releaseUnit;

    @ApiModelProperty(value = "启用标识（0=禁用、1=启用）")
    @NotBlank(message = "参数[启用标识]不能为空")
    @Option(value = {"0", "1"}, message = "参数[启用标识]的值无效")
    @TableField(value = "USFL")
    private String usfl;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getWidxType() {
        return widxType;
    }

    public void setWidxType(String widxType) {
        this.widxType = widxType;
    }

    public BigDecimal getWidxBlue() {
        return widxBlue;
    }

    public void setWidxBlue(BigDecimal widxBlue) {
        this.widxBlue = widxBlue;
    }

    public BigDecimal getWidxYellow() {
        return widxYellow;
    }

    public void setWidxYellow(BigDecimal widxYellow) {
        this.widxYellow = widxYellow;
    }

    public BigDecimal getWidxOrange() {
        return widxOrange;
    }

    public void setWidxOrange(BigDecimal widxOrange) {
        this.widxOrange = widxOrange;
    }

    public BigDecimal getWidxRed() {
        return widxRed;
    }

    public void setWidxRed(BigDecimal widxRed) {
        this.widxRed = widxRed;
    }

    public String getReleaseUnit() {
        return releaseUnit;
    }

    public void setReleaseUnit(String releaseUnit) {
        this.releaseUnit = releaseUnit;
    }

    public String getUsfl() {
        return usfl;
    }

    public void setUsfl(String usfl) {
        this.usfl = usfl;
    }
}
