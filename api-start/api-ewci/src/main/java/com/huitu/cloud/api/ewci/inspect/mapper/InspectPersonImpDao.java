package com.huitu.cloud.api.ewci.inspect.mapper;

import com.huitu.cloud.api.ewci.inspect.entity.BnsInspectPersonImp;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 责任人落实
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectPersonImpDao {

    /**
     * 判断当前政区当年是否添加过责任人落实
     *
     * @param adcd
     * @param year
     * @return
     */
    List<BnsInspectPersonImp> getInspectPersonImpList(@Param("adcd") String adcd, @Param("year") String year);

    /**
     * 添加责任人落实
     *
     * @param entity
     * @return
     */
    int insertInspectPersonImp(BnsInspectPersonImp entity);

    /**
     * 修改 责任人落实
     *
     * @param entity
     * @return
     */
    int updateInspectPersonImp(BnsInspectPersonImp entity);

    /**
     * 删除 责任人落实
     *
     * @return
     */
    int deleteInspectPersonImp(@Param("inspectId") String inspectId);

    Date selectYearByInspectId(@Param("inspectId") String inspectId);
}

