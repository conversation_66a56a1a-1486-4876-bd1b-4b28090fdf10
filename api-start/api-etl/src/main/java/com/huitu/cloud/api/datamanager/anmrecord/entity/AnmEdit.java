package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 *
 * <AUTHOR>
 * @since 2019-12-07
 */
@ApiModel(value="AnmEdit", description="异常数据-处理参数表")
public class AnmEdit {
    @ApiModelProperty(value = "记录编码 主键")
    private String  reid;
    @ApiModelProperty(value = "备注")
    private String nt;
   @ApiModelProperty(value = "修改值")
    private String editValue;
    @ApiModelProperty(value = "操作人")
    private String doer;
    @ApiModelProperty(value = "数据状态 0:终审审批通过且入库；2终审审批未通过； 3,初审通过；4，初审未通过； 5，已修改")
    @TableField("STAFLG")
    private String staflg;

    public String  getReid() {
        return reid;
    }

    public void setReid(String reid) {
        this.reid = reid;
    }

    public String getNt() {
        return nt;
    }

    public void setNt(String nt) {
        this.nt = nt;
    }

    public String getEditValue() {
        return editValue;
    }

    public void setEditValue(String editValue) {
        this.editValue = editValue;
    }

    public String getDoer() {
        return doer;
    }

    public void setDoer(String doer) {
        this.doer = doer;
    }

    public String getStaflg() {
        return staflg;
    }

    public void setStaflg(String staflg) {
        this.staflg = staflg;
    }
}
