package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 水库
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@ApiModel(value = "水库列表")
public class ReservoirData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水库索引")
    @TableField(value = "RSCD")
    private String rscd;

    @ApiModelProperty(value = "水库编码")
    @TableField(value = "RS_CODE")
    private String rsCode;

    @ApiModelProperty(value = "水库名称")
    @TableField(value = "RS_NAME")
    private String rsName;

    @ApiModelProperty(value = "行政区划代码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "行政区划名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "县级行政区划名称")
    @TableField(value = "XADNM")
    private String xadnm;

    @ApiModelProperty(value = "乡镇行政区划名称")
    @TableField(value = "XZADNM")
    private String xzadnm;

    @ApiModelProperty(value = "村行政区划名称")
    @TableField(value = "CADNM")
    private String cadnm;

    @ApiModelProperty(value = "小流域代码")
    @TableField(value = "wscd")
    private String wscd;

    @ApiModelProperty(value = "小流域名称")
    @TableField(value = "WSNM")
    private String wsnm;

    @ApiModelProperty(value = "河流（湖泊）编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "水库类型")
    @TableField(value = "RS_TYPE")
    private String rsType;

    @ApiModelProperty(value = "主要挡水建筑物类型")
    @TableField(value = "MAIN_WR_TYPE")
    private String mainWrType;

    @ApiModelProperty(value = "挡水主坝类型")
    @TableField(value = "DAM_TYPE")
    private String damType;

    @ApiModelProperty(value = "主要泄洪建筑物型式")
    @TableField(value = "MAIN_FL_TYPE")
    private String mainFlType;

    @ApiModelProperty(value = "坝址多年平均径流量(万m3)")
    @TableField(value = "MUL_AVER_RUN")
    private BigDecimal mulAverRun;

    @ApiModelProperty(value = "工程类别")
    @TableField(value = "ENG_GRAD")
    private String engGrad;

    @ApiModelProperty(value = "主坝坝高(m)")
    @TableField(value = "DAM_SIZE_HIG")
    private BigDecimal damSizeHig;

    @ApiModelProperty(value = "主坝坝长(m)")
    @TableField(value = "DAM_SIZE_LEN")
    private BigDecimal damSizeLen;

    @ApiModelProperty(value = "最大泄洪流量(m3/s)")
    @TableField(value = "MAX_DIS_FLOW")
    private BigDecimal maxDisFlow;

    @ApiModelProperty(value = "设计洪水位(m)")
    @TableField(value = "DES_FL_STAG")
    private BigDecimal desFlStag;

    @ApiModelProperty(value = "总库容(万m3)")
    @TableField(value = "TOT_CAP")
    private BigDecimal totCap;

    @ApiModelProperty(value = "水面面积(km2)")
    @TableField(value = "COR_SUR_AREA")
    private BigDecimal corSurArea;

    @ApiModelProperty(value = "经度（°）")
    @TableField(value = "LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度（°）")
    @TableField(value = "LTTD")
    private BigDecimal lttd;

    public String getRscd() {
        return rscd;
    }

    public void setRscd(String rscd) {
        this.rscd = rscd;
    }

    public String getRsCode() {
        return rsCode;
    }

    public void setRsCode(String rsCode) {
        this.rsCode = rsCode;
    }

    public String getRsName() {
        return rsName;
    }

    public void setRsName(String rsName) {
        this.rsName = rsName;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }

    public String getXzadnm() {
        return xzadnm;
    }

    public void setXzadnm(String xzadnm) {
        this.xzadnm = xzadnm;
    }

    public String getCadnm() {
        return cadnm;
    }

    public void setCadnm(String cadnm) {
        this.cadnm = cadnm;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getWsnm() {
        return wsnm;
    }

    public void setWsnm(String wsnm) {
        this.wsnm = wsnm;
    }

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRsType() {
        return rsType;
    }

    public void setRsType(String rsType) {
        this.rsType = rsType;
    }

    public String getMainWrType() {
        return mainWrType;
    }

    public void setMainWrType(String mainWrType) {
        this.mainWrType = mainWrType;
    }

    public String getDamType() {
        return damType;
    }

    public void setDamType(String damType) {
        this.damType = damType;
    }

    public String getMainFlType() {
        return mainFlType;
    }

    public void setMainFlType(String mainFlType) {
        this.mainFlType = mainFlType;
    }

    public BigDecimal getMulAverRun() {
        return mulAverRun;
    }

    public void setMulAverRun(BigDecimal mulAverRun) {
        this.mulAverRun = mulAverRun;
    }

    public String getEngGrad() {
        return engGrad;
    }

    public void setEngGrad(String engGrad) {
        this.engGrad = engGrad;
    }

    public BigDecimal getDamSizeHig() {
        return damSizeHig;
    }

    public void setDamSizeHig(BigDecimal damSizeHig) {
        this.damSizeHig = damSizeHig;
    }

    public BigDecimal getDamSizeLen() {
        return damSizeLen;
    }

    public void setDamSizeLen(BigDecimal damSizeLen) {
        this.damSizeLen = damSizeLen;
    }

    public BigDecimal getMaxDisFlow() {
        return maxDisFlow;
    }

    public void setMaxDisFlow(BigDecimal maxDisFlow) {
        this.maxDisFlow = maxDisFlow;
    }

    public BigDecimal getDesFlStag() {
        return desFlStag;
    }

    public void setDesFlStag(BigDecimal desFlStag) {
        this.desFlStag = desFlStag;
    }

    public BigDecimal getTotCap() {
        return totCap;
    }

    public void setTotCap(BigDecimal totCap) {
        this.totCap = totCap;
    }

    public BigDecimal getCorSurArea() {
        return corSurArea;
    }

    public void setCorSurArea(BigDecimal corSurArea) {
        this.corSurArea = corSurArea;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }
}
