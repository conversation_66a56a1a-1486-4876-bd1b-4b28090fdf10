package com.huitu.cloud.api.datamanager.manualreport.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 人工报讯配置返回类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-24
 */
@ApiModel(value="ManualReportVo对象", description="人工报讯配置返回类")
public class ManualReportVo extends BsnManualReport{
    @ApiModelProperty(value = "测站名称")
    private String stnm;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "县名称")
    private String xadnm;

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getXadnm() {
        return xadnm;
    }

    public void setXadnm(String xadnm) {
        this.xadnm = xadnm;
    }
}
