package com.huitu.cloud.api.datamanager.manualreport.service;

import com.huitu.cloud.api.datamanager.manualreport.entity.BsnManualReport;
import com.huitu.cloud.api.datamanager.manualreport.entity.ManualReportVo;
import com.huitu.cloud.api.datamanager.manualreport.entity.QueryManualReport;
import com.huitu.cloud.api.datamanager.ststop.entity.StStopQo;

import java.util.List;

/**
 * <p>
 * 人工报讯 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-23
 */
public interface ManualReportService {
    /**
     * 人工报讯站点记录查询
     *
     * @param adcd 政区编码
     * @return
     */
    List<ManualReportVo> getManualReportStInfo(String adcd);
    /**
     * 人工报讯站点记录添加
     *
     * @param entity 屏蔽类
     * @return
     */
    String addManualReportSt(StStopQo entity);
    /**
     * 结束人工报讯站点配置
     *
     * @param stcd 测站编码
     * @return
     */
    String closeManualReportSt(String stcd,String tbnm);
    /**
     * 人工报讯
     *
     * @param entity 报讯参数
     * @return
     */
    String manualReport(QueryManualReport entity);
}
