package com.huitu.cloud.api.ewci.inspect.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.inspect.entity.*;
import com.huitu.cloud.api.usif.ad.entity.BsnAdcdB;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 检查上报
 * </p>
 *
 * <AUTHOR>
 */
public interface InspectReportDao {

    /**
     * 获取检查类型清单
     *
     * @return
     */
    List<BnsInspectTypeInfo> getBnsInspectTypeInfoList(@Param("state") String state);

    /**
     * 检查重点清单
     *
     * @param inspectCode
     * @return
     */
    List<BnsInspectPointInfo> getBnsInspectPointInfoList(@Param("inspectCode") String inspectCode);

    /**
     * 判断当前政区当年是否生成报告
     *
     * @param adcd
     * @param year
     * @return
     */
    int getBnsInspectReportCount(@Param("adcd") String adcd, @Param("year") String year);

    /**
     * 检查文件添加
     *
     * @param list
     * @return
     */
    int addFile(@Param("list") List<BnsInspectFile> list);

    /**
     * 根据业务KEY删除文件
     *
     * @param businessKey 业务KEY
     * @return 受影响的行数
     */
    int deleteFile(@Param("businessKey") String businessKey);

    /**
     * 根据业务KEY删除文件
     *
     * @param businessKey 业务KEY
     * @param fileType    文件类型
     * @return 受影响的行数
     */
    int deleteFileByType(@Param("businessKey") String businessKey, @Param("fileType") String fileType);

    /**
     * 检查记录统计列表
     *
     * @return
     */
    List<BnsInspectRecords> getBnsInspectRecordsList(@Param("map") Map<String, Object> param);

    /**
     * 检查记录列表
     *
     * @param param
     * @return
     */
    IPage<BnsInspectRecordsChild> getBnsInspectRecordsChildList(IPage<BnsInspectRecordsChild> pageParam, @Param("map") Map<String, Object> param);

    /**
     * 水库(水电站)运行水库下拉
     *
     * @param param
     * @return
     */
    List<BnsInspectRsvr> getRsvrRunList(@Param("map") Map<String, Object> param);

    /**
     * 病险水库下拉
     *
     * @param param
     * @return
     */
    List<BnsInspectRsvr> getIllRsvrList(@Param("map") Map<String, Object> param);

    /**
     * 在建工程下拉
     *
     * @param param
     * @return
     */
    List<BnsEngB> getConstructEngList(@Param("map") Map<String, Object> param);

    /**
     * 水毁工程修复建设下拉
     *
     * @param param
     * @return
     */
    List<BnsEngB> getShgcConstructList(@Param("map") Map<String, Object> param);

    /**
     * 安全度汛工程建设下拉
     *
     * @param param
     * @return
     */
    List<BnsEngB> getAqdxConstructList(@Param("map") Map<String, Object> param);

    /**
     * 自动监测站下拉
     *
     * @param param
     * @return
     */
    List<BnsAutoMonitorB> getAutoMonitorList(@Param("map") Map<String, Object> param);

    /**
     * 根据 adcd 下探两级获取 tree
     *
     * @param param
     * @return
     */
    List<BsnAdcdB> selectByAdLevel(Map<String, Object> param);

    /**
     * 根据 adcd递归获取 adnm
     *
     * @param adcd
     * @return
     */
    String getAdnmByAdcd(@Param("adcd") String adcd);

    /**
     * 根据 adcd递归获取 adnm
     *
     * @param adcd
     * @return
     */
    String getTadnmByAdcd(@Param("adcd") String adcd);

    /**
     * 根据 adcd获取当前adnm
     *
     * @param adcd
     * @return
     */
    String getCurrentAdnmByAdcd(@Param("adcd") String adcd);

    /**
     * 获取责任人落实详情
     *
     * @param inspectId
     * @return
     */
    BnsInspectPersonImp getPersonImpInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    List<BnsInspectFile> getFile(@Param("businessKey") String businessKey);

    BnsInspectPlanRevise getPlanReviseInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectEmgCommDev getEmgCommDevInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectRsvrRun getRsvrRunInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectIllRsvr getInspectIllRsvrInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectRiverDike getInspectRiverDikeInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectConEn getInspectConEnInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectWagaRun getInspectWagaRunInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectAutoMonitor getInspectAutoMonitorInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectSimpleRain getInspectSimpleRainInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectWarnBroad getInspectWarnBroadInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectCountyPlatform getInspectCountyPlatformInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectMainExp getInspectMainExpInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectShgcCons getInspectShgcConsInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    BnsInspectAqdxCons getInspectAqdxConsInfo(@Param("inspectId") String inspectId, @Param("inspectCode") String inspectCode);

    Integer selectReportCount(Map<String, Object> param);

    Integer insertReport(Map<String, Object> param);

    IPage<BnsInspectReportsChild> getBnsInspectReportsChildList(IPage<BnsInspectRecordsChild> pageParam, @Param("map") Map<String, Object> query);

    Integer getReportConfirmResult(String reportId);

    Integer updateReportConfirm(@Param("reportId") String reportId);

    Integer deleteInspectReport(@Param("reportId") String reportId);

    /**
     * 更新检查报告状态
     *
     * @param reportId 检查报告ID
     * @param status   报告生成状态(1未生成、2生成中、3已生成)
     * @return int 修改条数
     */
    Integer updateReportStatus(@Param("reportId") String reportId, @Param("status") String status);

    BnsInspectReportsChild getInspectReportById(@Param("reportId") String reportId);
}

