************ - - [25/Jun/2025:08:56:31 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [25/Jun/2025:08:56:31 +0800] "GET /login HTTP/1.1" 302 -
************ - - [25/Jun/2025:08:56:45 +0800] "GET /login?code=smiqz9&state=dKJoaG HTTP/1.1" 302 -
************ - - [25/Jun/2025:08:56:45 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/ HTTP/1.1" 302 -
************ - - [25/Jun/2025:08:56:47 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750813007706 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:50 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750813007706 HTTP/1.1" 200 552
************ - - [25/Jun/2025:08:56:50 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750813010980 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750813010980 HTTP/1.1" 200 61649
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750813011050 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750813011050 HTTP/1.1" 200 10388
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750813011065 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750813011065 HTTP/1.1" 200 2009
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750813011222 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+09:00&filterCnt=6&_timer304=1750813011224 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+08:56:51&etm=&_timer304=1750813011224 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813011224 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813011224 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813011224 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750813011222 HTTP/1.1" 200 1482
************ - - [25/Jun/2025:08:56:51 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+09:00&filterCnt=6&_timer304=1750813011224 HTTP/1.1" 200 164
************ - - [25/Jun/2025:08:56:51 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813011224 HTTP/1.1" 200 166
************ - - [25/Jun/2025:08:56:51 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:08:56:51 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+08:56:51&etm=&_timer304=1750813011224 HTTP/1.1" 200 156
************ - - [25/Jun/2025:08:56:51 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:08:56:51 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:08:56:51 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:08:56:51 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813011224 HTTP/1.1" 200 169
************ - - [25/Jun/2025:08:56:51 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813011224 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-25&_timer304=1750813011725 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/base/saas/token?_timer304=1750813011725 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750813011737 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:51 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:52 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [25/Jun/2025:08:56:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:08:56:53 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 823
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 1187
************ - - [25/Jun/2025:08:56:54 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750813011737 HTTP/1.1" 200 2009
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [25/Jun/2025:08:56:54 +0800] "GET /api/base/saas/token?_timer304=1750813011725 HTTP/1.1" 200 411
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [25/Jun/2025:08:56:54 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750813015396 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+08:56:55&etm=&_timer304=1750813015660 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813015660 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813015660 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813015661 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+09:00&filterCnt=6&_timer304=1750813015660 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813015660 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750813015664 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:08:56:55 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:08:56:55 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+08:56:55&etm=&_timer304=1750813015660 HTTP/1.1" 200 156
************ - - [25/Jun/2025:08:56:55 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:08:56:55 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:08:56:55 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813015660 HTTP/1.1" 200 166
************ - - [25/Jun/2025:08:56:55 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+09:00&filterCnt=6&_timer304=1750813015660 HTTP/1.1" 200 164
************ - - [25/Jun/2025:08:56:55 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813015661 HTTP/1.1" 200 161
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813015792 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813015660 HTTP/1.1" 200 169
************ - - [25/Jun/2025:08:56:55 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813015813 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:55 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813015660 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:08:56:55 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750813015664 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:08:56:56 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [25/Jun/2025:08:56:56 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [25/Jun/2025:08:56:56 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [25/Jun/2025:08:56:56 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750813015396 HTTP/1.1" 200 144
************ - - [25/Jun/2025:08:56:56 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813015813 HTTP/1.1" 200 155
************ - - [25/Jun/2025:08:56:56 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [25/Jun/2025:08:56:56 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [25/Jun/2025:08:56:56 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************ - - [25/Jun/2025:08:56:57 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750813017082 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813017089 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813017089 HTTP/1.1" 200 159
************ - - [25/Jun/2025:08:56:57 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750813017082 HTTP/1.1" 200 160
************ - - [25/Jun/2025:08:56:57 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 743
************ - - [25/Jun/2025:08:56:57 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813017216 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:57 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813017216 HTTP/1.1" 200 155
************ - - [25/Jun/2025:08:56:57 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813015792 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:08:56:57 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:57 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 152
************ - - [25/Jun/2025:08:56:57 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 179
************ - - [25/Jun/2025:08:56:58 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 825
************ - - [25/Jun/2025:08:56:58 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-25&_timer304=1750813011725 HTTP/1.1" 200 451
************ - - [25/Jun/2025:08:56:59 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 825
************ - - [25/Jun/2025:08:56:59 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:59 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:59 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750813019164 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:56:59 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:08:56:59 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:08:56:59 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750813019164 HTTP/1.1" 200 1513
************ - - [25/Jun/2025:08:59:20 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750813160427 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:59:20 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750813160427 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:59:20 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750813160427 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:59:20 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-25+08:00&etm=2025-06-25+08:56&_timer304=1750813160442 HTTP/1.1" 200 -
************ - - [25/Jun/2025:08:59:20 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750813160427 HTTP/1.1" 200 831
************ - - [25/Jun/2025:08:59:20 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-25+08:00&etm=2025-06-25+08:56&_timer304=1750813160442 HTTP/1.1" 200 160
************ - - [25/Jun/2025:08:59:20 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750813160427 HTTP/1.1" 200 520
************ - - [25/Jun/2025:08:59:20 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750813160427 HTTP/1.1" 200 519
************ - - [25/Jun/2025:09:00:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813252013 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:52 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750813252016 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:52 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813252013 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:00:52 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750813252016 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:09:00:52 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+09:00:52&etm=&_timer304=1750813252092 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813252092 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+09:00&filterCnt=6&_timer304=1750813252092 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:52 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813252092 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:52 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813252092 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:52 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813252171 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:52 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:09:00:52 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:09:00:52 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [25/Jun/2025:09:00:52 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:09:00:52 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:09:00:52 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [25/Jun/2025:09:00:52 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+09:00:52&etm=&_timer304=1750813252092 HTTP/1.1" 200 156
************ - - [25/Jun/2025:09:00:52 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813252092 HTTP/1.1" 200 166
************ - - [25/Jun/2025:09:00:52 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+09:00&filterCnt=6&_timer304=1750813252092 HTTP/1.1" 200 164
************ - - [25/Jun/2025:09:00:52 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813252203 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:52 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813252203 HTTP/1.1" 200 155
************ - - [25/Jun/2025:09:00:52 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813252092 HTTP/1.1" 200 169
************ - - [25/Jun/2025:09:00:52 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813252092 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:09:00:52 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [25/Jun/2025:09:00:52 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [25/Jun/2025:09:00:52 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [25/Jun/2025:09:00:53 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 741
************ - - [25/Jun/2025:09:00:53 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+09:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813252171 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:00:53 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813253359 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:53 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813253359 HTTP/1.1" 200 155
************ - - [25/Jun/2025:09:00:54 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 823
************ - - [25/Jun/2025:09:00:55 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 823
************ - - [25/Jun/2025:09:00:55 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750813255730 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:00:55 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750813255730 HTTP/1.1" 200 1513
************ - - [25/Jun/2025:09:00:55 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:00:58 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:00:58 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:01:01 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750813261789 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:01 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750813261789 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:01:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813261828 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813261828 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:01:08 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750813268738 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813268737 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:08 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813268737 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:01:08 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750813268738 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:09:01:08 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+09:01:08&etm=&_timer304=1750813268793 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813268793 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+10:00&filterCnt=6&_timer304=1750813268793 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:08 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813268793 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:08 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813268793 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:08 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813268859 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:08 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:09:01:08 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:09:01:08 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [25/Jun/2025:09:01:08 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:09:01:08 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:09:01:08 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [25/Jun/2025:09:01:08 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+09:01:08&etm=&_timer304=1750813268793 HTTP/1.1" 200 156
************ - - [25/Jun/2025:09:01:08 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+10:00&filterCnt=6&_timer304=1750813268793 HTTP/1.1" 200 164
************ - - [25/Jun/2025:09:01:08 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813268793 HTTP/1.1" 200 166
************ - - [25/Jun/2025:09:01:08 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813268883 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:08 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813268883 HTTP/1.1" 200 155
************ - - [25/Jun/2025:09:01:08 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813268793 HTTP/1.1" 200 169
************ - - [25/Jun/2025:09:01:08 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813268793 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:09:01:09 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [25/Jun/2025:09:01:09 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [25/Jun/2025:09:01:09 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [25/Jun/2025:09:01:09 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813268859 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:01:09 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 743
************ - - [25/Jun/2025:09:01:10 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813270318 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:10 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813270318 HTTP/1.1" 200 155
************ - - [25/Jun/2025:09:01:11 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 825
************ - - [25/Jun/2025:09:01:11 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:01:11 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 825
************ - - [25/Jun/2025:09:01:12 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750813272154 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:12 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750813272154 HTTP/1.1" 200 1513
************ - - [25/Jun/2025:09:01:14 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:01:14 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:01:18 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750813278556 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:18 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750813278556 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:01:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813278584 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813278584 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:01:25 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:25 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:01:54 +0800] "GET /client/oauth2/token?redirect_url=http://localhost:9528/?redirect=%252Fmonitor%253Ftmpparams%253D%257B%2522title%2522%253A%2522%25E7%25BB%25BC%25E5%2590%2588%25E7%259B%2591%25E8%25A7%2586%2522%257D HTTP/1.1" 302 -
************ - - [25/Jun/2025:09:01:56 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750813316168 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:56 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750813316168 HTTP/1.1" 200 552
************ - - [25/Jun/2025:09:01:56 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750813316632 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:56 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750813316632 HTTP/1.1" 200 61649
************ - - [25/Jun/2025:09:01:56 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750813316729 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:56 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750813316729 HTTP/1.1" 200 10388
************ - - [25/Jun/2025:09:01:57 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750813317211 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:57 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750813317211 HTTP/1.1" 200 2009
************ - - [25/Jun/2025:09:01:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813317953 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:58 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750813317962 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:58 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813317953 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:01:58 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750813317962 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:09:01:58 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+09:01:58&etm=&_timer304=1750813318085 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813318085 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+10:00&filterCnt=6&_timer304=1750813318085 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:58 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813318085 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:58 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813318085 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:58 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:09:01:58 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:09:01:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813318282 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:58 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:09:01:58 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:09:01:58 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+09:01:58&etm=&_timer304=1750813318085 HTTP/1.1" 200 156
************ - - [25/Jun/2025:09:01:58 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750813318085 HTTP/1.1" 200 166
************ - - [25/Jun/2025:09:01:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [25/Jun/2025:09:01:58 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [25/Jun/2025:09:01:58 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+10:00&filterCnt=6&_timer304=1750813318085 HTTP/1.1" 200 164
************ - - [25/Jun/2025:09:01:58 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750813318085 HTTP/1.1" 200 169
************ - - [25/Jun/2025:09:01:58 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813318333 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:58 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813318333 HTTP/1.1" 200 155
************ - - [25/Jun/2025:09:01:58 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [25/Jun/2025:09:01:58 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750813318085 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:09:01:58 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [25/Jun/2025:09:01:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [25/Jun/2025:09:01:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813318282 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:01:59 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 743
************ - - [25/Jun/2025:09:01:59 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813319665 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:01:59 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750813319665 HTTP/1.1" 200 155
************ - - [25/Jun/2025:09:02:00 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 825
************ - - [25/Jun/2025:09:02:02 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 825
************ - - [25/Jun/2025:09:02:03 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750813323469 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:02:03 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750813323469 HTTP/1.1" 200 1513
************ - - [25/Jun/2025:09:02:05 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750813325677 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:02:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813325685 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:02:05 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750813325677 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:02:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813325685 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:02:07 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:02:08 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:02:09 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:02:26 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:02:26 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:06:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750813615801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:06:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750813615801 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:06:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813617799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:06:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813617799 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:06:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:06:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:06:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813618806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:06:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:06:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:06:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:06:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813618806 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:06:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:07:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813625798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:07:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813625798 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:11:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750813915803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:11:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750813915803 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:11:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813917812 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:11:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750813917812 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:11:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:11:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:11:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813918802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:11:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:11:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:11:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:11:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750813918802 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:11:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:12:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813926806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:12:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750813926806 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:14:18 +0800] "OPTIONS /api/ewci/warn/rain/risk/listRiverRainRiskWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:14:18 +0800] "POST /api/ewci/warn/rain/risk/listRiverRainRiskWarn HTTP/1.1" 200 165
************ - - [25/Jun/2025:09:14:19 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:14:19 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:14:19 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:14:19 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:09:16:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750814215804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:16:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750814215804 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:16:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750814217811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:16:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750814217811 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:16:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:16:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:16:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750814218802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:16:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:16:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:16:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:16:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750814218802 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:16:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:17:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750814227809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:17:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750814227809 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:21:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750814515811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:21:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750814515811 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:21:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750814517805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:21:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750814517805 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:21:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:21:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:21:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:21:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750814518798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:21:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:21:59 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:21:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750814518798 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:21:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:22:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750814528802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:22:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750814528802 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:26:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750814815675 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:26:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750814815675 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:26:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750814817585 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:26:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750814817585 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:26:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:26:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750814818105 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:26:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:26:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:26:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:26:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:26:59 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750814818105 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:26:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:27:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750814828835 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:27:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750814828835 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:31:56 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750815116804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:31:56 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750815116804 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:31:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750815117800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:31:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750815117800 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:31:59 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:31:59 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:31:59 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750815119801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:31:59 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:31:59 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:32:00 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:32:01 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750815119801 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:32:01 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:32:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750815129801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:32:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750815129801 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:37:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750815430802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:37:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750815430802 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:37:11 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750815430982 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:37:11 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:37:11 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750815430988 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:37:11 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:37:11 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:37:11 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750815430991 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:37:11 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750815430982 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:37:11 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:37:11 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750815430988 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:37:11 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:37:11 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750815430991 HTTP/1.1" 200 440467
************ - - [25/Jun/2025:09:37:12 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:41:56 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750815716811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:41:56 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750815716811 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:41:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750815717811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:41:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750815717811 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:41:59 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:41:59 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:41:59 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750815719807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:41:59 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:41:59 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:42:00 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:42:00 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750815719807 HTTP/1.1" 200 441712
************ - - [25/Jun/2025:09:42:00 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:42:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750815731799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:42:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750815731799 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:46:02 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750815962615 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:02 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750815962615 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:02 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750815962615 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:02 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750815962615 HTTP/1.1" 200 831
************ - - [25/Jun/2025:09:46:02 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-25+08:00&etm=2025-06-25+09:01&_timer304=1750815962631 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:02 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750815962615 HTTP/1.1" 200 519
************ - - [25/Jun/2025:09:46:02 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750815962615 HTTP/1.1" 200 520
************ - - [25/Jun/2025:09:46:02 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-25+08:00&etm=2025-06-25+09:01&_timer304=1750815962631 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:46:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750816015690 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750816015690 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:46:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750816017585 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750816017585 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:46:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750816018109 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:46:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:46:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:46:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750816018109 HTTP/1.1" 200 441712
************ - - [25/Jun/2025:09:46:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:47:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750816031847 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:47:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750816031847 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:51:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750816315684 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:51:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750816315684 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:51:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750816317596 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:51:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750816317596 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:51:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:51:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:51:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750816318107 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:51:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:51:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:51:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:51:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750816318107 HTTP/1.1" 200 441712
************ - - [25/Jun/2025:09:51:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:52:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750816331887 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:52:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750816331887 HTTP/1.1" 200 159
************ - - [25/Jun/2025:09:56:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750816615692 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:56:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750816615692 HTTP/1.1" 200 160
************ - - [25/Jun/2025:09:56:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750816617597 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:56:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750816617597 HTTP/1.1" 200 161
************ - - [25/Jun/2025:09:56:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:56:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:56:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750816618109 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:56:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:56:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:09:56:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:09:56:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+10:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750816618109 HTTP/1.1" 200 441712
************ - - [25/Jun/2025:09:56:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:09:57:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750816631933 HTTP/1.1" 200 -
************ - - [25/Jun/2025:09:57:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750816631933 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:01:55 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750816915682 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:01:55 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750816915682 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:01:57 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750816917585 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:01:57 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750816917585 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:01:58 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:01:58 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:01:58 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750816918108 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:01:58 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:01:58 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:01:58 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:01:58 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750816918108 HTTP/1.1" 200 441712
************ - - [25/Jun/2025:10:01:59 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:02:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750816931981 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:02:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750816931981 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:07:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750817232900 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:07:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750817232900 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:07:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750817251913 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:07:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750817251913 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:08:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750817311809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:08:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:08:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:08:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750817311811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:08:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:08:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:08:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750817311809 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:08:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:08:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750817311811 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:08:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:12:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750817533797 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:12:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750817533797 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:12:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750817551802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:12:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750817551802 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:13:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750817611899 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:13:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:13:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:13:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750817611901 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:13:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:13:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:13:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750817611899 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:13:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:13:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750817611901 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:13:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:17:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750817834800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:17:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750817834800 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:17:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750817851808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:17:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750817851808 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:18:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750817911804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:18:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:18:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:18:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750817911806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:18:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:18:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:18:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750817911804 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:18:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:18:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750817911806 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:18:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:22:00 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818120819 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:00 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:00 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750818120825 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:00 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818120826 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:00 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:00 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:00 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818120819 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:22:00 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:22:00 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750818120825 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:22:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:22:01 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818120826 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:22:01 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:22:10 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:22:10 +0800] "GET /login HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:22:10 +0800] "GET /login?code=Cls9zL&state=CejgU7 HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:22:10 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:22:12 +0800] "OPTIONS /api/usif/user/select-by-username?username=huitu0518&_timer304=1750818132571 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:12 +0800] "GET /api/usif/user/select-by-username?username=huitu0518&_timer304=1750818132571 HTTP/1.1" 200 552
************ - - [25/Jun/2025:10:22:13 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750818133947 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:13 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=1&_timer304=1750818133947 HTTP/1.1" 200 61649
************ - - [25/Jun/2025:10:22:14 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750818134120 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:14 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=1d8b4b1d-a235-4642-8888-f2e6f465acdf&systemCode=5&_timer304=1750818134120 HTTP/1.1" 200 10388
************ - - [25/Jun/2025:10:22:14 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750818134662 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:14 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750818134662 HTTP/1.1" 200 2009
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750818135212 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:22:15&etm=&_timer304=1750818135213 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818135213 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818135213 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818135213 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818135213 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:15 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:22:15&etm=&_timer304=1750818135213 HTTP/1.1" 200 156
************ - - [25/Jun/2025:10:22:15 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:10:22:15 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818135213 HTTP/1.1" 200 164
************ - - [25/Jun/2025:10:22:15 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818135213 HTTP/1.1" 200 166
************ - - [25/Jun/2025:10:22:15 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:10:22:15 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750818135212 HTTP/1.1" 200 1482
************ - - [25/Jun/2025:10:22:15 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:22:15 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:22:15 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818135213 HTTP/1.1" 200 169
************ - - [25/Jun/2025:10:22:15 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818135213 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750818136958 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/base/saas/token?_timer304=1750818136958 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "GET /api/base/saas/token?_timer304=1750818136958 HTTP/1.1" 200 411
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:16 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:17 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:17 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:17 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=5440&_timer304=1750818136977 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:17 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:17 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [25/Jun/2025:10:22:17 +0800] "GET /api/usif/menu/select-user-menu?userId=5440&_timer304=1750818136977 HTTP/1.1" 200 2009
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 9928
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1978
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 2342
************ - - [25/Jun/2025:10:22:17 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 304
************ - - [25/Jun/2025:10:22:18 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750818136958 HTTP/1.1" 200 442
************ - - [25/Jun/2025:10:22:23 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750818143018 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818143019 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:23 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750818143018 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:22:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818143019 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:22:26 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750818146729 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:26 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750818146729 HTTP/1.1" 200 144
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:22:26&etm=&_timer304=1750818147067 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818147067 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818147067 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818147067 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818147067 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818147068 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750818147073 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:22:26&etm=&_timer304=1750818147067 HTTP/1.1" 200 156
************ - - [25/Jun/2025:10:22:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818147067 HTTP/1.1" 200 166
************ - - [25/Jun/2025:10:22:27 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:10:22:27 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818147067 HTTP/1.1" 200 164
************ - - [25/Jun/2025:10:22:27 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:10:22:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:22:27 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:22:27 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818147068 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:22:27 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818147067 HTTP/1.1" 200 169
************ - - [25/Jun/2025:10:22:27 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750818147073 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:10:22:27 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818147067 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818147229 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [25/Jun/2025:10:22:27 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818147260 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818147260 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:22:27 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:27 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [25/Jun/2025:10:22:27 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [25/Jun/2025:10:22:28 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 733
************ - - [25/Jun/2025:10:22:28 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818147229 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:22:28 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818148482 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:28 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818148482 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:22:28 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:28 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:28 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:22:29 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:22:29 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:30 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:30 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:30 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:22:30 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818150102 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:30 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:22:30 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:22:30 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818150102 HTTP/1.1" 200 1513
************ - - [25/Jun/2025:10:22:34 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750818154337 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:34 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750818154337 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:34 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750818154337 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:34 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902442&_timer304=1750818154337 HTTP/1.1" 200 831
************ - - [25/Jun/2025:10:22:34 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902442&_timer304=1750818154337 HTTP/1.1" 200 519
************ - - [25/Jun/2025:10:22:34 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-25+08:00&etm=2025-06-25+10:22&_timer304=1750818154354 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:22:34 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200006&_timer304=1750818154337 HTTP/1.1" 200 520
************ - - [25/Jun/2025:10:22:34 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902442&stm=2025-06-25+08:00&etm=2025-06-25+10:22&_timer304=1750818154354 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:24:25&etm=&_timer304=1750818265416 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750818265341 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818265334 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818265416 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818265416 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818265416 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818265416 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818265560 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818265524 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:26 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:29 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:10:24:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818265334 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:24:29 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750818265341 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:10:24:29 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:10:24:29 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:24:25&etm=&_timer304=1750818265416 HTTP/1.1" 200 156
************ - - [25/Jun/2025:10:24:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818265416 HTTP/1.1" 200 166
************ - - [25/Jun/2025:10:24:29 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818265416 HTTP/1.1" 200 164
************ - - [25/Jun/2025:10:24:29 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:24:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:24:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818265416 HTTP/1.1" 200 169
************ - - [25/Jun/2025:10:24:29 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [25/Jun/2025:10:24:29 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [25/Jun/2025:10:24:29 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818265560 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:24:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [25/Jun/2025:10:24:30 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:30 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [25/Jun/2025:10:24:30 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818265416 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:10:24:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [25/Jun/2025:10:24:31 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 733
************ - - [25/Jun/2025:10:24:31 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818271578 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:31 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818265524 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:24:31 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818271578 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:24:31 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:32 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:24:32 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:33 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:24:33 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818273471 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:33 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:33 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818273471 HTTP/1.1" 200 232
************ - - [25/Jun/2025:10:24:34 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 733
************ - - [25/Jun/2025:10:24:34 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818274377 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:34 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818274377 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:24:34 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:34 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750818274938 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818274978 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:35 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:24:35 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:35 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:24:35 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818275869 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:24:36 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818275869 HTTP/1.1" 200 232
************ - - [25/Jun/2025:10:24:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818274978 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:24:37 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750818274938 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:25:10 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:25:10 +0800] "GET /login HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:25:15 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750818315921 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818315916 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818315916 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:25:16 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750818315921 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:25:15&etm=&_timer304=1750818315991 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818315991 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818315991 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818315991 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818315991 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818316091 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:25:15&etm=&_timer304=1750818315991 HTTP/1.1" 200 156
************ - - [25/Jun/2025:10:25:16 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:10:25:16 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:10:25:16 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818315991 HTTP/1.1" 200 166
************ - - [25/Jun/2025:10:25:16 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818315991 HTTP/1.1" 200 164
************ - - [25/Jun/2025:10:25:16 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:16 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818316122 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [25/Jun/2025:10:25:16 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [25/Jun/2025:10:25:16 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818316122 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:25:16 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818315991 HTTP/1.1" 200 169
************ - - [25/Jun/2025:10:25:16 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818315991 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:10:25:16 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:16 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [25/Jun/2025:10:25:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [25/Jun/2025:10:25:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818316091 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:25:17 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 733
************ - - [25/Jun/2025:10:25:17 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818317501 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:17 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818317501 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:25:17 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:18 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:25:18 +0800] "GET /login HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:25:18 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:25:18 +0800] "GET /login?code=nnQIiA&state=SMXelI HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:25:18 +0800] "GET /client/oauth2/token?redirect_url=http://************:9528/ HTTP/1.1" 302 -
************ - - [25/Jun/2025:10:25:19 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/usif/user/select-by-username?username=220000&_timer304=1750818320991 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "GET /api/usif/user/select-by-username?username=220000&_timer304=1750818320991 HTTP/1.1" 200 524
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=1&_timer304=1750818321077 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=1&_timer304=1750818321077 HTTP/1.1" 200 41606
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=5&_timer304=1750818321179 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818320577 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=5&_timer304=1750818321179 HTTP/1.1" 200 8569
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1750818321236 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1750818321236 HTTP/1.1" 200 5130
************ - - [25/Jun/2025:10:25:20 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818320577 HTTP/1.1" 200 232
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750818321572 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:25:21&etm=&_timer304=1750818321573 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818321573 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818321573 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818321573 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818321573 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:20 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750818321572 HTTP/1.1" 200 1524
************ - - [25/Jun/2025:10:25:21 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818321573 HTTP/1.1" 200 164
************ - - [25/Jun/2025:10:25:21 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:25:21&etm=&_timer304=1750818321573 HTTP/1.1" 200 156
************ - - [25/Jun/2025:10:25:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818321573 HTTP/1.1" 200 166
************ - - [25/Jun/2025:10:25:21 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:10:25:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:10:25:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:21 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:21 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818321573 HTTP/1.1" 200 169
************ - - [25/Jun/2025:10:25:21 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818321573 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750818322381 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/syq/rain/select-map-by-work HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/base/saas/token?_timer304=1750818322381 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/ew/statistics/select-warn-summary HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1750818322406 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:21 +0800] "OPTIONS /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:22 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [25/Jun/2025:10:25:22 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:25:22 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1978
************ - - [25/Jun/2025:10:25:23 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 2342
************ - - [25/Jun/2025:10:25:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:25:23 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [25/Jun/2025:10:25:24 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [25/Jun/2025:10:25:24 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [25/Jun/2025:10:25:24 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 34365
************ - - [25/Jun/2025:10:25:24 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [25/Jun/2025:10:25:24 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [25/Jun/2025:10:25:24 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [25/Jun/2025:10:25:24 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [25/Jun/2025:10:25:24 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1750818322406 HTTP/1.1" 200 5130
************ - - [25/Jun/2025:10:25:25 +0800] "GET /api/base/saas/token?_timer304=1750818322381 HTTP/1.1" 200 411
************ - - [25/Jun/2025:10:25:25 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750818325550 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:25 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750818325550 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:25:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818325587 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818325587 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:25:26 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750818322381 HTTP/1.1" 200 442
************ - - [25/Jun/2025:10:25:26 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************ - - [25/Jun/2025:10:25:28 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1750818329180 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:28 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1750818329180 HTTP/1.1" 200 146
************ - - [25/Jun/2025:10:25:29 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750818330388 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818330401 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:29 +0800] "OPTIONS /api/ewci/base/mal/write/884?_timer304=1750818330410 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:29 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750818330388 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:25:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818330401 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:25:29 +0800] "GET /api/ewci/base/mal/write/884?_timer304=1750818330410 HTTP/1.1" 200 146
************ - - [25/Jun/2025:10:25:31 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750818332381 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:31 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750818332381 HTTP/1.1" 200 144
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:25:32&etm=&_timer304=1750818332600 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818332600 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818332600 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818332600 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750818332610 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818332600 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818332600 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+10:25:32&etm=&_timer304=1750818332600 HTTP/1.1" 200 156
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:10:25:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818332600 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:25:32 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750818332600 HTTP/1.1" 200 166
************ - - [25/Jun/2025:10:25:32 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750818332600 HTTP/1.1" 200 164
************ - - [25/Jun/2025:10:25:32 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750818332600 HTTP/1.1" 200 169
************ - - [25/Jun/2025:10:25:32 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750818332610 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:10:25:32 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750818332600 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818332863 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [25/Jun/2025:10:25:32 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818332900 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:32 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818332900 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [25/Jun/2025:10:25:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [25/Jun/2025:10:25:33 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 733
************ - - [25/Jun/2025:10:25:33 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818333880 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818332863 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:25:33 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818333880 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:25:34 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:25:34 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:25:34 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818335524 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:35 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818335524 HTTP/1.1" 200 232
************ - - [25/Jun/2025:10:25:36 +0800] "OPTIONS /api/syq/rain/select-time-interval HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:36 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750818336023 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:36 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=3&_timer304=1750818336023 HTTP/1.1" 200 12285
************ - - [25/Jun/2025:10:25:36 +0800] "POST /api/syq/rain/select-time-interval HTTP/1.1" 200 8262
************ - - [25/Jun/2025:10:25:36 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:36 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:25:42 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:43 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 733
************ - - [25/Jun/2025:10:25:43 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818343448 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:43 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750818343448 HTTP/1.1" 200 155
************ - - [25/Jun/2025:10:25:43 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:25:44 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:44 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:10:25:44 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818344976 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:25:45 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750818344976 HTTP/1.1" 200 232
************ - - [25/Jun/2025:10:26:02 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:26:08 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:26:09 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:26:09 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:26:09 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:26:09 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:26:10 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:26:14 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:10 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:10 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:27:16 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:16 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:27:18 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:18 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:18 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:27:18 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:27:19 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:19 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:27:20 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750818440658 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:20 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750818440658 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:20 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750818440658 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:20 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+10:30&_timer304=1750818440676 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:20 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750818440658 HTTP/1.1" 200 838
************ - - [25/Jun/2025:10:27:20 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750818440658 HTTP/1.1" 200 520
************ - - [25/Jun/2025:10:27:20 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750818440658 HTTP/1.1" 200 519
************ - - [25/Jun/2025:10:27:20 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+10:30&_timer304=1750818440676 HTTP/1.1" 200 1146
************ - - [25/Jun/2025:10:27:23 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:23 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:30 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:30 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:31 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:31 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:31 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:32 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:36 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:36 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:27:39 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:39 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:27:40 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:40 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:27:43 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:43 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:45 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:45 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:45 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:46 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:46 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:46 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:46 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:46 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:46 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:46 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:46 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:47 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:27:53 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750818473803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:53 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750818473804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:53 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750818473804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:53 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750818473803 HTTP/1.1" 200 838
************ - - [25/Jun/2025:10:27:53 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750818473804 HTTP/1.1" 200 519
************ - - [25/Jun/2025:10:27:53 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+10:30&_timer304=1750818473820 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:27:53 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750818473804 HTTP/1.1" 200 520
************ - - [25/Jun/2025:10:27:53 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+10:30&_timer304=1750818473820 HTTP/1.1" 200 1146
************ - - [25/Jun/2025:10:28:00 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:28:00 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:28:12 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:28:12 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:28:13 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:28:13 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:28:16 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:28:16 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:28:20 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:28:20 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:29:22 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:22 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:29:34 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:34 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:10:29:38 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:38 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:10:29:42 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750818582193 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:42 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750818582193 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:42 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+10:30&_timer304=1750818582193 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:42 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750818582193 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:42 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750818582193 HTTP/1.1" 200 838
************ - - [25/Jun/2025:10:29:42 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750818582193 HTTP/1.1" 200 519
************ - - [25/Jun/2025:10:29:42 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750818582193 HTTP/1.1" 200 520
************ - - [25/Jun/2025:10:29:42 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+10:30&_timer304=1750818582193 HTTP/1.1" 200 1146
************ - - [25/Jun/2025:10:29:46 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750818586886 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:46 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750818586886 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:46 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750818586886 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:46 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+10:30&_timer304=1750818586886 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:29:46 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750818586886 HTTP/1.1" 200 838
************ - - [25/Jun/2025:10:29:46 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750818586886 HTTP/1.1" 200 519
************ - - [25/Jun/2025:10:29:46 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750818586886 HTTP/1.1" 200 520
************ - - [25/Jun/2025:10:29:46 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+10:30&_timer304=1750818586886 HTTP/1.1" 200 1146
************ - - [25/Jun/2025:10:30:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750818615550 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750818615550 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:30:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818615642 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818615642 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:30:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818616030 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:30:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:30:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818616030 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:30:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:30:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750818621185 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750818621185 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:30:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818625635 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818625635 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:30:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818631208 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818631208 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:30:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818633059 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818633062 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:30:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:30:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818633059 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:30:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:30:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818633062 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:30:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:35:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818915801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750818915802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818915801 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:35:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750818915802 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:35:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818916805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:35:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:35:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818916805 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:35:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:35:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750818921054 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750818921054 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:35:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818925810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818925810 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:35:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818932055 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750818932055 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:35:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818933047 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818933050 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:35:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750818933047 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:35:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:35:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:35:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750818933050 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:35:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:40:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819215812 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750819215813 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819215812 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:40:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750819215813 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:40:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819216800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:40:17 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:40:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819216800 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:40:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:40:20 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750819221049 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:20 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750819221049 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:40:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819226806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819226806 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:40:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819233051 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:32 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819233051 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819233053 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:40:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:40:32 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819233051 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:40:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819233051 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:40:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:40:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819233053 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:40:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:43:39 +0800] "OPTIONS /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:43:39 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [25/Jun/2025:10:43:40 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [25/Jun/2025:10:43:44 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [25/Jun/2025:10:45:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [25/Jun/2025:10:45:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750819515546 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750819515546 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:45:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819515653 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819515653 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:45:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819516016 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:45:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:45:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819516016 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:45:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:45:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750819520508 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750819520508 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:45:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819526848 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819526848 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:45:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819532531 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819532531 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:45:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819532769 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:45:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819533106 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:45:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819533106 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:45:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:45:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819532769 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:45:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:50:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [25/Jun/2025:10:50:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750819815543 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750819815543 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:50:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819815648 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819815648 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:50:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819816021 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:50:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:50:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819816021 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:50:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:50:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750819820530 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750819820530 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:50:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819826882 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819826882 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:50:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819832544 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750819832544 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:50:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819832780 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:50:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819833145 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:50:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750819833145 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:50:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:50:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750819832780 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:50:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:55:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [25/Jun/2025:10:55:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750820115558 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750820115558 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:55:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750820115642 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750820115642 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:55:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750820116034 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:55:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:55:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750820116034 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:55:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:10:55:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750820120392 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750820120392 HTTP/1.1" 200 160
************ - - [25/Jun/2025:10:55:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820126913 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820126913 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:55:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750820132416 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750820132416 HTTP/1.1" 200 161
************ - - [25/Jun/2025:10:55:32 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750820132778 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:32 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:10:55:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820133177 HTTP/1.1" 200 -
************ - - [25/Jun/2025:10:55:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820133177 HTTP/1.1" 200 159
************ - - [25/Jun/2025:10:55:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:10:55:33 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+11:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750820132778 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:10:55:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:00:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/usif/user/select-by-username?username=220000&_timer304=1750820409811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/usif/user/select-by-username?username=220000&_timer304=1750820409811 HTTP/1.1" 200 524
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=1&_timer304=1750820409849 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=1&_timer304=1750820409849 HTTP/1.1" 200 41606
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=5&_timer304=1750820409874 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/usif/menu/select-menu-list-by-role?roleId=5d7896bd-847f-46d6-933b-b63b03b159d4&systemCode=5&_timer304=1750820409874 HTTP/1.1" 200 8569
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1750820409893 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1750820409893 HTTP/1.1" 200 5130
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+11:00:09&etm=&_timer304=1750820409981 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750820409981 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750820409981 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750820409981 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750820409981 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/ewci/custom/info/select-custom-list?_timer304=1750820409989 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+11:00:09&etm=&_timer304=1750820409981 HTTP/1.1" 200 156
************ - - [25/Jun/2025:11:00:09 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:11:00:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:11:00:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:11:00:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:11:00:09 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+11:00&filterCnt=6&_timer304=1750820409981 HTTP/1.1" 200 164
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750820409981 HTTP/1.1" 200 166
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/ewci/custom/info/select-custom-list?_timer304=1750820409989 HTTP/1.1" 200 1524
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750820409981 HTTP/1.1" 200 169
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750820409981 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/base/saas/token?_timer304=1750820410308 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750820410308 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "OPTIONS /api/usif/menu/select-user-menu?userId=16&_timer304=1750820410308 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:09 +0800] "POST /api/ewci/rsvr/stat-compute/get-rsvr-cap-summary HTTP/1.1" 200 296
************ - - [25/Jun/2025:11:00:09 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/base/saas/token?_timer304=1750820410308 HTTP/1.1" 200 411
************ - - [25/Jun/2025:11:00:09 +0800] "GET /api/syq/rain/get-rain-summary?adcd=220000000000000&tm=2025-06-24&_timer304=1750820410308 HTTP/1.1" 200 442
************ - - [25/Jun/2025:11:00:09 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 286443
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 298
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/ew/statistics/select-warn-summary HTTP/1.1" 200 306
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 248
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/xxjh/model/temp/select-temp-list HTTP/1.1" 200 1104
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/ewci/feedback/info/select-info-page HTTP/1.1" 200 244
************ - - [25/Jun/2025:11:00:10 +0800] "GET /api/usif/menu/select-user-menu?userId=16&_timer304=1750820410308 HTTP/1.1" 200 5130
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/syq/rsvr/select-StReglat-by-info HTTP/1.1" 200 241
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 34365
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1978
************ - - [25/Jun/2025:11:00:10 +0800] "POST /api/syq/rain/select-map-by-work HTTP/1.1" 200 2342
************ - - [25/Jun/2025:11:00:19 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750820420512 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:19 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750820420512 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:00:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820427805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820427805 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:00:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750820431809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750820431809 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:00:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820433212 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:00:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820433212 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:01:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:01:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750820491809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:01:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:01:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750820491811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:01:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:01:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:01:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750820491809 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:01:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:01:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750820491811 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:01:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:05:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820728808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:05:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750820728808 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:05:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750820731808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:05:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750820731808 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:06:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750820791803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:06:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:06:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:06:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750820791805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:06:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:06:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:06:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750820791803 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:06:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:06:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750820791805 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:06:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:10:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821029805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:10:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821029805 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:10:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750821031798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:10:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750821031798 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:11:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750821091797 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:11:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:11:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:11:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:11:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750821091799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:11:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:11:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750821091797 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:11:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:11:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750821091799 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:11:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:15:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821330807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:15:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821330807 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:15:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750821331809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:15:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750821331809 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:16:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750821391897 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:16:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:16:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:16:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750821391898 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:16:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:16:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:16:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750821391897 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:16:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:16:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750821391898 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:16:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:20:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750821631805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:20:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821631805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:20:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750821631805 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:20:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821631805 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:21:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750821691800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:21:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:21:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:21:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750821691802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:21:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:21:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:21:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750821691800 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:21:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:21:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750821691802 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:21:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:23:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821820047 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:23:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821820047 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:23:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750821825213 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:23:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750821825213 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:25:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750821931807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:25:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750821931807 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:25:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821932810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:25:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750821932810 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:26:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750821991811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:26:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:26:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:26:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750821991813 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:26:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:26:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:26:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750821991811 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:26:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:26:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750821991813 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:26:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:26:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750822005050 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:26:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750822005050 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:28:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822121058 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:28:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822121058 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:30:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822231912 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:30:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822231912 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:30:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822233804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:30:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822233804 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:31:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750822291914 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:31:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:31:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:31:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822291916 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:31:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:31:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:31:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750822291914 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:31:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:31:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822291916 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:31:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:31:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750822305050 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:31:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750822305050 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:33:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822422053 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:33:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822422053 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:35:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822531802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:35:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822531802 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:35:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822534810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:35:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822534810 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:36:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750822582530 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750822582530 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:36:23 +0800] "OPTIONS /api/ewci/base/mal/write/1?_timer304=1750822584182 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:23 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+11:36:23&etm=&_timer304=1750822584182 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750822584182 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:23 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750822584182 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+12:00&filterCnt=6&_timer304=1750822584182 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:23 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:11:36:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750822584182 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:23 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:11:36:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822584188 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:23 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750822584188 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:23 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+11:36:23&etm=&_timer304=1750822584182 HTTP/1.1" 200 156
************ - - [25/Jun/2025:11:36:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:11:36:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:11:36:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:11:36:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822584188 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:23 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750822584182 HTTP/1.1" 200 166
************ - - [25/Jun/2025:11:36:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:11:36:23 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+12:00&filterCnt=6&_timer304=1750822584182 HTTP/1.1" 200 164
************ - - [25/Jun/2025:11:36:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822584188 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:36:23 +0800] "GET /api/ewci/base/mal/write/1?_timer304=1750822584182 HTTP/1.1" 200 144
************ - - [25/Jun/2025:11:36:23 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750822584182 HTTP/1.1" 200 169
************ - - [25/Jun/2025:11:36:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:36:23 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750822584188 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:11:36:23 +0800] "POST /api/xxjh/ifmprecord/select-ifmprecordr-by-page HTTP/1.1" 200 22319
************ - - [25/Jun/2025:11:36:23 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750822584182 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:11:36:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:36:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 733
************ - - [25/Jun/2025:11:36:24 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750822585165 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:24 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750822585165 HTTP/1.1" 200 155
************ - - [25/Jun/2025:11:36:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822584188 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:36:24 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:36:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [25/Jun/2025:11:36:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:11:36:25 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [25/Jun/2025:11:36:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 1984
************ - - [25/Jun/2025:11:36:25 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750822586647 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:26 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750822586647 HTTP/1.1" 200 232
************ - - [25/Jun/2025:11:36:26 +0800] "OPTIONS /api/ewci/soil/select-soil-disposi-by-etm HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:26 +0800] "POST /api/ewci/soil/select-soil-disposi-by-etm HTTP/1.1" 200 207
************ - - [25/Jun/2025:11:36:26 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [25/Jun/2025:11:36:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750822591803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822591805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:36:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:36:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750822591803 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:36:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:36:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822591805 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:36:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:36:34 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [25/Jun/2025:11:36:35 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [25/Jun/2025:11:36:35 +0800] "POST /api/ewci/soil/select-soil-real-time-list HTTP/1.1" 200 1169
************ - - [25/Jun/2025:11:37:55 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:37:56 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:11:37:56 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:11:38:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822723187 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:38:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822723187 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:40:01 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 1354941
************ - - [25/Jun/2025:11:40:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822831807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:40:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822831807 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:40:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822835800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:40:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750822835800 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:41:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822884210 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:41:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750822884210 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:41:24 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822885054 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:41:24 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:41:24 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:41:25 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822885054 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:41:25 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:41:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:41:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750822891802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:41:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822891804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:41:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:41:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:41:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:41:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750822891802 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:41:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:41:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750822891804 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:41:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:41:44 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750822905228 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:41:44 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750822905228 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:43:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750823024054 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:43:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750823024054 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:45:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750823131916 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:45:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750823131916 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:45:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750823136811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:45:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750823136811 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:46:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750823191809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:46:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:46:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:46:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750823191811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:46:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:46:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:46:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750823191809 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:46:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:46:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750823191811 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:46:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:50:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750823431807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:50:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750823431807 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:50:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750823437811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:50:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750823437811 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:51:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750823491908 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:51:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:51:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:51:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750823491909 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:51:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:51:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:51:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750823491908 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:51:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:51:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750823491909 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:51:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:11:55:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750823731798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:55:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750823731798 HTTP/1.1" 200 161
************ - - [25/Jun/2025:11:55:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750823738798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:55:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750823738798 HTTP/1.1" 200 159
************ - - [25/Jun/2025:11:56:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750823791808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:56:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:56:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:56:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750823791811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:56:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:11:56:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:11:56:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750823791808 HTTP/1.1" 200 160
************ - - [25/Jun/2025:11:56:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:11:56:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+12:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750823791811 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:11:56:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:00:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750824031812 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:00:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750824031812 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:00:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750824039801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:00:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750824039801 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:01:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750824091807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:01:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:01:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:01:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750824091809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:01:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:01:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:01:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750824091807 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:01:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:01:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750824091809 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:01:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:05:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750824331909 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:05:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750824331909 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:05:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750824340799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:05:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750824340799 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:06:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750824391921 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:06:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:06:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:06:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750824391923 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:06:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:06:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:06:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750824391921 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:06:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:06:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750824391923 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:06:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:10:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750824631810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:10:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750824631810 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:10:41 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750824641807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:10:41 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750824641807 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:11:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750824691800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:11:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:11:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:11:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750824691802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:11:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:11:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750824691800 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:11:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:11:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:11:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750824691802 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:11:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:15:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750824931809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:15:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750824931809 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:15:42 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750824942799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:15:42 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750824942799 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:16:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750824991806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:16:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:16:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:16:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750824991808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:16:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:16:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:16:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750824991806 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:16:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:16:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750824991808 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:16:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:20:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750825231907 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:20:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750825231907 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:20:43 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750825243797 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:20:43 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750825243797 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:21:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750825291905 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:21:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:21:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:21:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750825291907 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:21:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:21:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:21:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750825291905 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:21:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:21:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750825291907 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:21:33 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:25:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750825531799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:25:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750825531799 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:25:44 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750825544804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:25:44 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750825544804 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:26:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750825591900 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:26:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:26:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:26:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750825591902 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:26:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:26:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:26:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750825591900 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:26:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:26:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750825591902 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:26:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:30:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750825831800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:30:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750825831800 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:30:45 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750825845796 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:30:45 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750825845796 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:31:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750825891808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:31:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:31:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:31:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750825891810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:31:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:31:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750825891808 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:31:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:31:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:31:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750825891810 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:31:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:35:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750826131911 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:35:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750826131911 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:35:46 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750826146802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:35:46 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750826146802 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:36:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750826191807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:36:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:36:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:36:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:36:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750826191809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:36:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:36:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750826191807 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:36:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:36:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750826191809 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:36:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:40:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750826431931 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:40:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750826431931 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:40:47 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750826447805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:40:47 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750826447805 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:41:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750826491934 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:41:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:41:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:41:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750826491936 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:41:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:41:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:41:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750826491934 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:41:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:41:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750826491936 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:41:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:45:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750826731812 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:45:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750826731812 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:45:48 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750826748800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:45:48 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750826748800 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:46:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750826791799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:46:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:46:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:46:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750826791800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:46:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:46:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:46:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750826791799 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:46:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:46:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750826791800 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:46:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:50:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750827031916 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:50:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750827031916 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:50:49 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750827049801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:50:49 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750827049801 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:51:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750827091798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:51:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:51:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:51:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750827091801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:51:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:51:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:51:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750827091798 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:51:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:51:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750827091801 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:51:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:12:55:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750827331799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:55:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750827331799 HTTP/1.1" 200 161
************ - - [25/Jun/2025:12:55:50 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750827350808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:55:50 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750827350808 HTTP/1.1" 200 159
************ - - [25/Jun/2025:12:56:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750827391798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:56:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:56:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:56:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750827391801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:56:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:12:56:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:12:56:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750827391798 HTTP/1.1" 200 160
************ - - [25/Jun/2025:12:56:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:12:56:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+13:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750827391801 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:12:56:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:00:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750827631811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:00:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750827631811 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:00:51 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750827651797 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:00:51 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750827651797 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:01:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750827691902 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:01:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:01:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:01:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:01:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750827691904 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:01:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:01:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750827691902 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:01:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:01:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750827691904 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:01:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:05:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750827931812 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:05:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750827931812 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:05:52 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750827952809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:05:52 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750827952809 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:06:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750827991804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:06:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:06:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:06:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750827991807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:06:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:06:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:06:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750827991804 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:06:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:06:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750827991807 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:06:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:10:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750828231799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:10:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750828231799 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:10:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750828253900 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:10:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750828253900 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:11:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750828291808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:11:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:11:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:11:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750828291810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:11:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:11:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:11:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750828291808 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:11:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:11:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750828291810 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:11:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:15:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750828515554 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:15:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750828515554 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:15:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750828515643 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:15:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750828515643 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:15:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:15:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750828516020 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:15:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:15:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:15:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:15:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:15:17 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750828516020 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:15:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:15:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750828553942 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:15:53 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750828553942 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:20:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750828815555 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:20:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750828815555 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:20:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750828815646 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:20:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750828815646 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:20:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:20:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:20:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750828816025 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:20:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:20:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:20:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:20:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750828816025 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:20:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:20:53 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750828853976 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:20:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750828853976 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:25:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750829115552 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:25:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750829115552 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:25:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750829115655 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:25:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750829115655 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:25:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:25:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:25:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:25:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750829116030 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:25:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:25:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:25:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750829116030 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:25:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:25:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750829154024 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:25:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750829154024 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:30:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750829415811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:30:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750829415811 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:30:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750829416797 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:30:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750829416797 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:30:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:30:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:30:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750829417803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:30:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:30:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:30:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:30:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750829417803 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:30:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:30:54 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750829454807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:30:54 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750829454807 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:35:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750829731798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:35:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750829731798 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:35:55 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750829755809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:35:55 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750829755809 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:36:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750829791803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:36:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:36:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:36:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750829791805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:36:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:36:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:36:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750829791803 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:36:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:36:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750829791805 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:36:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:40:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750830031812 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:40:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750830031812 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:40:56 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750830056898 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:40:56 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750830056898 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:41:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750830091801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:41:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:41:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:41:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750830091803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:41:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:41:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:41:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750830091801 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:41:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:41:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750830091803 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:41:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:45:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750830331928 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:45:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750830331928 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:45:57 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750830357798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:45:57 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750830357798 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:46:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750830391919 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:46:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:46:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:46:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750830391921 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:46:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:46:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:46:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750830391919 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:46:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:46:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750830391921 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:46:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:50:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750830631801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:50:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750830631801 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:50:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750830658802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:50:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750830658802 HTTP/1.1" 200 159
************ - - [25/Jun/2025:13:51:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750830691798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:51:31 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:51:31 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:51:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750830691801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:51:31 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:51:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:51:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750830691798 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:51:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:51:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750830691801 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:51:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:55:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750830915550 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:55:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750830915550 HTTP/1.1" 200 160
************ - - [25/Jun/2025:13:55:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750830915654 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:55:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750830915654 HTTP/1.1" 200 161
************ - - [25/Jun/2025:13:55:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:55:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:55:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750830916019 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:55:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:55:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:13:55:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:13:55:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750830916019 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:13:55:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:13:55:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750830958840 HTTP/1.1" 200 -
************ - - [25/Jun/2025:13:55:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750830958840 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:00:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750831215552 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:00:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750831215552 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:00:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750831215656 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:00:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750831215656 HTTP/1.1" 200 161
************ - - [25/Jun/2025:14:00:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:00:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:00:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:00:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750831216031 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:00:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:14:00:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:14:00:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+14:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750831216031 HTTP/1.1" 200 442796
************ - - [25/Jun/2025:14:00:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:14:00:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750831258872 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:00:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750831258872 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:05:15 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750831515549 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:05:15 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750831515549 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:05:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750831515641 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:05:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750831515641 HTTP/1.1" 200 161
************ - - [25/Jun/2025:14:05:16 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:05:16 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:05:16 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:05:16 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750831516030 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:05:16 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:14:05:16 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:14:05:16 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750831516030 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:14:05:17 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:14:05:58 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750831558912 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:05:58 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750831558912 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:10:15 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750831815802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:10:15 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750831815802 HTTP/1.1" 200 161
************ - - [25/Jun/2025:14:10:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750831816806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:10:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750831816806 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:10:17 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:10:17 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:10:17 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750831817810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:10:17 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:10:17 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:14:10:18 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:14:10:18 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750831817810 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:14:10:18 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:14:10:59 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750831859802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:10:59 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750831859802 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:15:31 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750832131803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:15:31 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750832131803 HTTP/1.1" 200 161
************ - - [25/Jun/2025:14:16:00 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750832160907 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:16:00 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750832160907 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:16:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750832191811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:16:31 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750832191813 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:16:31 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:14:16:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750832191811 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:16:32 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:14:16:32 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+15:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750832191813 HTTP/1.1" 200 442804
************ - - [25/Jun/2025:14:16:32 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 51718
************ - - [25/Jun/2025:14:17:09 +0800] "OPTIONS /api/ewci/base/mal/write/111?_timer304=1750832229929 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "GET /api/ewci/base/mal/write/111?_timer304=1750832229929 HTTP/1.1" 200 146
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+14:17:09&etm=&_timer304=1750832230058 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750832230058 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+15:00&filterCnt=6&_timer304=1750832230058 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750832230058 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750832230058 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/usif/dept/send-receive?_timer304=1750832230058 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:10 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:14:17:10 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+15:00&filterCnt=6&_timer304=1750832230058 HTTP/1.1" 200 164
************ - - [25/Jun/2025:14:17:10 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+14:17:09&etm=&_timer304=1750832230058 HTTP/1.1" 200 156
************ - - [25/Jun/2025:14:17:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750832230058 HTTP/1.1" 200 166
************ - - [25/Jun/2025:14:17:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:14:17:10 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:17:10 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:14:17:10 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750832230058 HTTP/1.1" 200 169
************ - - [25/Jun/2025:14:17:10 +0800] "GET /api/usif/dept/send-receive?_timer304=1750832230058 HTTP/1.1" 200 12352
************ - - [25/Jun/2025:14:17:10 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750832230058 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:14:17:13 +0800] "OPTIONS /api/ewci/base/mal/write/891?_timer304=1750832233074 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:13 +0800] "GET /api/ewci/base/mal/write/891?_timer304=1750832233074 HTTP/1.1" 200 146
************ - - [25/Jun/2025:14:17:13 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+14:17:13&etm=&_timer304=1750832233196 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750832233196 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+15:00&filterCnt=6&_timer304=1750832233196 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:13 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750832233196 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:13 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750832233196 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:13 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:14:17:13 +0800] "OPTIONS /api/usif/dept/send-receive?_timer304=1750832233199 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:13 +0800] "OPTIONS /api/xxjh/insidefile/select-risk-filesr-dispatch HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:13 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+14:17:13&etm=&_timer304=1750832233196 HTTP/1.1" 200 156
************ - - [25/Jun/2025:14:17:13 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:14:17:13 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:14:17:13 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:17:13 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750832233196 HTTP/1.1" 200 166
************ - - [25/Jun/2025:14:17:13 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+15:00&filterCnt=6&_timer304=1750832233196 HTTP/1.1" 200 164
************ - - [25/Jun/2025:14:17:13 +0800] "GET /api/usif/dept/send-receive?_timer304=1750832233199 HTTP/1.1" 200 12352
************ - - [25/Jun/2025:14:17:13 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750832233196 HTTP/1.1" 200 169
************ - - [25/Jun/2025:14:17:13 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750832233196 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:14:17:13 +0800] "POST /api/xxjh/insidefile/select-risk-filesr-dispatch HTTP/1.1" 200 1037
************ - - [25/Jun/2025:14:17:17 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-sgin?filecd=A876C71A-B76A-43B2-ADE6-B9F971167C69&tm=2025-06-19+17:31:06&_timer304=1750832237794 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:17 +0800] "GET /api/xxjh/insidefile/select-filesr-sgin?filecd=A876C71A-B76A-43B2-ADE6-B9F971167C69&tm=2025-06-19+17:31:06&_timer304=1750832237794 HTTP/1.1" 200 354
************ - - [25/Jun/2025:14:17:20 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-sgin?filecd=1374CCC8-2B8C-4918-8423-D89D0A29FB07&tm=2025-06-19+17:30:16&_timer304=1750832240202 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:20 +0800] "GET /api/xxjh/insidefile/select-filesr-sgin?filecd=1374CCC8-2B8C-4918-8423-D89D0A29FB07&tm=2025-06-19+17:30:16&_timer304=1750832240202 HTTP/1.1" 200 354
************ - - [25/Jun/2025:14:17:23 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-by-filecd?adcd=220000000000000&filecd=A876C71A-B76A-43B2-ADE6-B9F971167C69&_timer304=1750832243105 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:23 +0800] "GET /api/xxjh/insidefile/select-filesr-by-filecd?adcd=220000000000000&filecd=A876C71A-B76A-43B2-ADE6-B9F971167C69&_timer304=1750832243105 HTTP/1.1" 200 706
************ - - [25/Jun/2025:14:17:36 +0800] "OPTIONS /api/ewci/base/mal/write/112?_timer304=1750832256896 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:36 +0800] "GET /api/ewci/base/mal/write/112?_timer304=1750832256896 HTTP/1.1" 200 146
************ - - [25/Jun/2025:14:17:37 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+14:17:36&etm=&_timer304=1750832257002 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750832257002 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+15:00&filterCnt=6&_timer304=1750832257002 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:37 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750832257002 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:37 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:14:17:37 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750832257002 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:37 +0800] "OPTIONS /api/usif/dept/send-receive?_timer304=1750832257004 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:37 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:14:17:37 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-dispatch HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:37 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:14:17:37 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:17:37 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750832257002 HTTP/1.1" 200 166
************ - - [25/Jun/2025:14:17:37 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+14:17:36&etm=&_timer304=1750832257002 HTTP/1.1" 200 156
************ - - [25/Jun/2025:14:17:37 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+15:00&filterCnt=6&_timer304=1750832257002 HTTP/1.1" 200 164
************ - - [25/Jun/2025:14:17:37 +0800] "GET /api/usif/dept/send-receive?_timer304=1750832257004 HTTP/1.1" 200 12352
************ - - [25/Jun/2025:14:17:37 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750832257002 HTTP/1.1" 200 169
************ - - [25/Jun/2025:14:17:37 +0800] "POST /api/xxjh/insidefile/select-filesr-dispatch HTTP/1.1" 200 916
************ - - [25/Jun/2025:14:17:37 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750832257002 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:14:17:54 +0800] "OPTIONS /api/ewci/base/mal/write/113?_timer304=1750832274208 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:54 +0800] "GET /api/ewci/base/mal/write/113?_timer304=1750832274208 HTTP/1.1" 200 146
************ - - [25/Jun/2025:14:17:54 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+14:17:54&etm=&_timer304=1750832274321 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750832274321 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+15:00&filterCnt=6&_timer304=1750832274321 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:54 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750832274321 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:54 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750832274321 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:14:17:54 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:14:17:54 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:14:17:54 +0800] "OPTIONS /api/usif/dept/send-receive?_timer304=1750832274323 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:17:54 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+14:17:54&etm=&_timer304=1750832274321 HTTP/1.1" 200 156
************ - - [25/Jun/2025:14:17:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:17:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750832274321 HTTP/1.1" 200 166
************ - - [25/Jun/2025:14:17:54 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+15:00&filterCnt=6&_timer304=1750832274321 HTTP/1.1" 200 164
************ - - [25/Jun/2025:14:17:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:17:54 +0800] "GET /api/usif/dept/send-receive?_timer304=1750832274323 HTTP/1.1" 200 12352
************ - - [25/Jun/2025:14:17:54 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750832274321 HTTP/1.1" 200 169
************ - - [25/Jun/2025:14:17:54 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750832274321 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:14:18:04 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:18:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:19:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:20:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:21:01 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750832461800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:21:01 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750832461800 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:21:07 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750832467138 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:21:07 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750832467138 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:21:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:22:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:23:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:24:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:25:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750832716807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:25:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750832716807 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:26:02 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750832762797 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:26:02 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750832762797 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:26:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:27:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:28:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:29:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:30:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:31:03 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750833063806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:31:03 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750833063806 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:31:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750833091799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:31:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750833091799 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:31:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:32:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:33:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:34:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:35:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:36:04 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750833364798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:36:04 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750833364798 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:36:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750833391811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:36:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750833391811 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:36:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:37:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:38:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:39:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:40:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:41:05 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750833665798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:41:05 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750833665798 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:41:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750833691805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:41:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750833691805 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:41:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:42:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:43:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:44:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:45:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:46:06 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750833966801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:46:06 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750833966801 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:46:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750833991811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:46:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750833991811 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:46:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:47:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:48:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:49:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:50:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:51:07 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750834267807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:51:07 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750834267807 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:51:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750834291805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:51:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750834291805 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:51:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:52:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:53:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:54:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:55:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:56:08 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750834568810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:56:08 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750834568810 HTTP/1.1" 200 159
************ - - [25/Jun/2025:14:56:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750834591796 HTTP/1.1" 200 -
************ - - [25/Jun/2025:14:56:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750834591796 HTTP/1.1" 200 160
************ - - [25/Jun/2025:14:56:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:57:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:58:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:14:59:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:00:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:01:09 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750834869810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:01:09 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750834869810 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:01:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750834891800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:01:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750834891800 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:01:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:02:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:03:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:04:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:05:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:06:10 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750835170807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:06:10 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750835170807 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:06:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750835191798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:06:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750835191798 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:06:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:07:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:08:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:09:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:10:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:11:11 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750835471805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:11:11 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750835471805 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:11:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750835491803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:11:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750835491803 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:11:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:12:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:13:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:14:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:15:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:16:12 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750835772802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:16:12 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750835772802 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:16:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750835791799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:16:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750835791799 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:16:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:17:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:18:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:19:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:20:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:21:13 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750836073810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:21:13 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750836073810 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:21:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750836091805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:21:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750836091805 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:21:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:21:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:22:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:23:56 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:24:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:26:14 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750836374797 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:26:14 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750836374797 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:26:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750836391812 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:26:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750836391812 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:26:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:27:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:28:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:29:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:30:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:31:15 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750836675807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:31:15 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750836675807 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:31:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750836691806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:31:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750836691806 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:31:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:32:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:33:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:34:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:35:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:36:16 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750836976800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:36:16 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750836976800 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:36:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750836991802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:36:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750836991802 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:36:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:37:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:38:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:39:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:40:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:41:17 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750837277796 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:41:17 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750837277796 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:41:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750837291800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:41:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750837291800 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:41:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:41:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:43:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:44:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:45:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:45:51 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750837551091 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:45:51 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750837551091 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:45:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:46:18 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750837578801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:46:18 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750837578801 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:46:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:48:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:49:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:50:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:51:19 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750837879802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:51:19 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750837879802 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:51:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750837891806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:51:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750837891806 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:51:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:52:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:53:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:54:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:55:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:56:20 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750838180805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:56:20 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750838180805 HTTP/1.1" 200 159
************ - - [25/Jun/2025:15:56:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750838191798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:15:56:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750838191798 HTTP/1.1" 200 160
************ - - [25/Jun/2025:15:56:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:57:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:58:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:15:59:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:00:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:01:21 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750838481811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:01:21 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750838481811 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:01:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750838491798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:01:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750838491798 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:01:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:02:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:03:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:04:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:05:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:06:22 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750838782801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:06:22 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750838782801 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:06:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750838791809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:06:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750838791809 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:06:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:07:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:08:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:09:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:10:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:11:23 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750839083799 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:11:23 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750839083799 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:11:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750839091809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:11:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750839091809 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:11:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:12:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:13:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:14:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:15:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:16:24 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750839384810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:16:24 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750839384810 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:16:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750839391807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:16:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750839391807 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:16:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:17:31 +0800] "OPTIONS /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:17:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:18:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:19:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:20:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:21:25 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750839685798 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:21:25 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750839685798 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:21:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750839691807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:21:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750839691807 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:21:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:22:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:23:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:24:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:25:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:26:26 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750839986805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:26:26 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750839986805 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:26:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750839991809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:26:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750839991809 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:26:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:27:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:28:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:29:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:30:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:31:27 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750840287804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:31:27 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750840287804 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:31:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750840291812 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:31:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750840291812 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:31:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:32:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:33:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:34:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:35:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:36:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750840588810 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:36:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750840588810 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:36:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750840591803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:36:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750840591803 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:36:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:37:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:38:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:39:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:40:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:40:50 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750840850879 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:40:50 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750840850879 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:40:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:41:28 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750840888858 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:41:28 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750840888858 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:41:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:42:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:43:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:44:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:45:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750841116802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:45:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750841116802 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:45:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:46:29 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750841189802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:46:29 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750841189802 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:47:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:48:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:49:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:50:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:51:30 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750841490797 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:51:30 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750841490797 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:51:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750841491803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:51:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750841491803 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:51:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:52:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:53:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:54:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:55:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:56:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750841791804 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:56:31 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750841791820 HTTP/1.1" 200 -
************ - - [25/Jun/2025:16:56:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750841791804 HTTP/1.1" 200 160
************ - - [25/Jun/2025:16:56:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:56:31 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750841791820 HTTP/1.1" 200 159
************ - - [25/Jun/2025:16:56:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:58:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:16:59:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:00:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:01:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750842091809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:01:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750842091809 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:01:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:01:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750842092807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:01:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750842092807 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:02:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:03:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:04:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:05:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:06:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750842391808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:06:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750842391808 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:06:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:06:33 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750842393811 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:06:33 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750842393811 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:07:22 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:07:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:09:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:10:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:11:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750842691806 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:11:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750842691806 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:11:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:11:34 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750842694809 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:11:34 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750842694809 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:12:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:13:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:14:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:15:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:16:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750842991801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:16:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750842991801 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:16:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:16:35 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750842995808 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:16:35 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750842995808 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:17:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:18:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:19:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:20:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:21:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750843291802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:21:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750843291802 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:21:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:21:36 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750843296803 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:21:36 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750843296803 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:22:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:23:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:24:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:25:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:26:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750843591807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:26:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750843591807 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:26:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:26:37 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750843597801 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:26:37 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750843597801 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:27:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:28:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:29:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:30:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:31:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750843891805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:31:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750843891805 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:31:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:31:38 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750843898796 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:31:38 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750843898796 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:31:55 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:32:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:34:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:35:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:36:21 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750844181247 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:36:21 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:36:21 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750844181247 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:36:39 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750844199802 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:36:39 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750844199802 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:36:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:38:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:39:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:40:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:41:31 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750844491800 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:41:31 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750844491800 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:41:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:41:40 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750844500807 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:41:40 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750844500807 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:42:31 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:42:54 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:44:19 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+17:44:28&etm=&_timer304=1750844669375 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/duty/task/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+18:00&filterCnt=6&_timer304=1750844669375 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750844669375 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750844669375 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750844669375 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/common/msg/get-common-message HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750844669377 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750844669379 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750844669421 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/syq/river/select-latest-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/syq/rsvr/select-latest-info HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:44:29 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+17:44:28&etm=&_timer304=1750844669375 HTTP/1.1" 200 156
************ - - [25/Jun/2025:17:44:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:17:44:29 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:17:44:29 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:17:44:29 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+18:00&filterCnt=6&_timer304=1750844669375 HTTP/1.1" 200 164
************ - - [25/Jun/2025:17:44:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750844669375 HTTP/1.1" 200 166
************ - - [25/Jun/2025:17:44:29 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750844669375 HTTP/1.1" 200 169
************ - - [25/Jun/2025:17:44:29 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750844669377 HTTP/1.1" 200 161
************ - - [25/Jun/2025:17:44:29 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750844669375 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:17:44:29 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 240
************ - - [25/Jun/2025:17:44:29 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750844669379 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:17:44:29 +0800] "OPTIONS /api/ew/warning/latest-page-list HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:29 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:17:44:29 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:17:44:30 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 236
************ - - [25/Jun/2025:17:44:30 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 733
************ - - [25/Jun/2025:17:44:30 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750844670605 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:30 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750844670605 HTTP/1.1" 200 155
************ - - [25/Jun/2025:17:44:30 +0800] "OPTIONS /api/syq/rain/select-by-page HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:30 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?adcd=220000000000000&basCode=&stm=2025-06-25+08:00&etm=2025-06-25+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750844669421 HTTP/1.1" 200 445738
************ - - [25/Jun/2025:17:44:30 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 53468
************ - - [25/Jun/2025:17:44:31 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 4835
************ - - [25/Jun/2025:17:44:32 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 4835
************ - - [25/Jun/2025:17:44:32 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750844672243 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:44:32 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750844672243 HTTP/1.1" 200 232
************ - - [25/Jun/2025:17:45:16 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750844716805 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:45:16 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750844716805 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:46:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750844783128 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:23 +0800] "OPTIONS /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750844783130 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:23 +0800] "GET /api/datamanger/anmrecord/select-data-count?adcd=220000000000000&staflgs=1,2,3,4,5,6&_timer304=1750844783128 HTTP/1.1" 200 161
************ - - [25/Jun/2025:17:46:23 +0800] "GET /api/usif/ad/get-ad-tree?adcd=220000000000000&adLvl=4&_timer304=1750844783130 HTTP/1.1" 200 159491
************ - - [25/Jun/2025:17:46:23 +0800] "OPTIONS /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+17:46:23&etm=&_timer304=1750844783188 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750844783188 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+18:00&filterCnt=6&_timer304=1750844783188 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:23 +0800] "OPTIONS /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750844783188 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:23 +0800] "OPTIONS /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750844783188 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:23 +0800] "POST /api/duty/task/select-by-page HTTP/1.1" 200 795
************ - - [25/Jun/2025:17:46:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:17:46:23 +0800] "POST /api/common/msg/get-common-message HTTP/1.1" 200 153
************ - - [25/Jun/2025:17:46:23 +0800] "OPTIONS /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750844783259 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:23 +0800] "GET /api/xxjh/ctfile/select-no-read-cfile?stm=2025-06-22+17:46:23&etm=&_timer304=1750844783188 HTTP/1.1" 200 156
************ - - [25/Jun/2025:17:46:23 +0800] "POST /api/xxjh/insidefile/select-filesr-Receiver HTTP/1.1" 200 247
************ - - [25/Jun/2025:17:46:23 +0800] "GET /api/datamanger/anmrecord/select-rain-exception?adcd=220000000000000&staflgs=1,3,6&stm=&etm=&_timer304=1750844783188 HTTP/1.1" 200 166
************ - - [25/Jun/2025:17:46:23 +0800] "GET /api/datamanger/anmrecord/select-zr-exception?adcd=220000000000000&staflgs=1,3,6&stm=2025-06-25+08:00&etm=2025-06-25+18:00&filterCnt=6&_timer304=1750844783188 HTTP/1.1" 200 164
************ - - [25/Jun/2025:17:46:23 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750844783284 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:23 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 105
************ - - [25/Jun/2025:17:46:23 +0800] "POST /api/syq/river/select-latest-by-page HTTP/1.1" 200 109
************ - - [25/Jun/2025:17:46:23 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750844783284 HTTP/1.1" 200 155
************ - - [25/Jun/2025:17:46:23 +0800] "GET /api/datamanger/anmrecord/select-rain-exception-st?adcd=220000000000000&_timer304=1750844783188 HTTP/1.1" 200 169
************ - - [25/Jun/2025:17:46:23 +0800] "GET /api/datamanger/ststop/select-record?adcd=220000000000000&isAll=0&_timer304=1750844783188 HTTP/1.1" 200 13016
************ - - [25/Jun/2025:17:46:23 +0800] "POST /api/syq/rain/select-by-page-by-bas HTTP/1.1" 200 108
************ - - [25/Jun/2025:17:46:23 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 101
************ - - [25/Jun/2025:17:46:23 +0800] "POST /api/syq/rsvr/select-latest-info HTTP/1.1" 200 105
************ - - [25/Jun/2025:17:46:24 +0800] "POST /api/syq/rain/get-max-rain-by-tm HTTP/1.1" 200 733
************ - - [25/Jun/2025:17:46:24 +0800] "GET /api/syq/rsvr/select-rsvr-rain-list?basCode=&stm=2025-06-25+08:00&etm=2025-06-25+18:00&pageNum=1&pageSize=-1&stTypes=1,2,3,6&_timer304=1750844783259 HTTP/1.1" 200 445738
************ - - [25/Jun/2025:17:46:24 +0800] "OPTIONS /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750844784712 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:24 +0800] "GET /api/syq/rain/select-forecast-2hours?stcd=&stm=&etm=&_timer304=1750844784712 HTTP/1.1" 200 155
************ - - [25/Jun/2025:17:46:25 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 4835
************ - - [25/Jun/2025:17:46:26 +0800] "POST /api/ew/warning/latest-page-list HTTP/1.1" 200 236
************ - - [25/Jun/2025:17:46:26 +0800] "POST /api/syq/rain/select-by-page HTTP/1.1" 200 4835
************ - - [25/Jun/2025:17:46:26 +0800] "OPTIONS /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750844786893 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:27 +0800] "GET /api/syq/rain/select-ascription-type-count-new?adcd=220000000000000&bscd=&stType=1,2,3,6&_timer304=1750844786893 HTTP/1.1" 200 232
************ - - [25/Jun/2025:17:46:29 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:29 +0800] "OPTIONS /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:29 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:17:46:29 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:17:46:31 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:17:46:32 +0800] "OPTIONS /api/usif/user/update-web-lastly-online?_timer304=1750844792941 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:32 +0800] "GET /api/usif/user/update-web-lastly-online?_timer304=1750844792941 HTTP/1.1" 200 160
************ - - [25/Jun/2025:17:46:32 +0800] "OPTIONS /api/river/near/warn/unread-message-list?hours=24&_timer304=1750844792969 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:46:32 +0800] "GET /api/river/near/warn/unread-message-list?hours=24&_timer304=1750844792969 HTTP/1.1" 200 159
************ - - [25/Jun/2025:17:46:34 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:17:46:37 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:17:46:39 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94506
************ - - [25/Jun/2025:17:46:42 +0800] "POST /api/shyj/locwarn/listLocWarn HTTP/1.1" 200 94869
************ - - [25/Jun/2025:17:47:04 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750844824433 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:04 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750844824433 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:04 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750844824433 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:04 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750844824433 HTTP/1.1" 200 838
************ - - [25/Jun/2025:17:47:04 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+17:50&_timer304=1750844824447 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:04 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750844824433 HTTP/1.1" 200 520
************ - - [25/Jun/2025:17:47:04 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750844824433 HTTP/1.1" 200 519
************ - - [25/Jun/2025:17:47:04 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+17:50&_timer304=1750844824447 HTTP/1.1" 200 1146
************ - - [25/Jun/2025:17:47:10 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750844830082 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:10 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750844830082 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:10 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750844830082 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:10 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+17:50&_timer304=1750844830082 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:10 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750844830082 HTTP/1.1" 200 838
************ - - [25/Jun/2025:17:47:10 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750844830082 HTTP/1.1" 200 519
************ - - [25/Jun/2025:17:47:10 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750844830082 HTTP/1.1" 200 520
************ - - [25/Jun/2025:17:47:10 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+17:50&_timer304=1750844830082 HTTP/1.1" 200 1146
************ - - [25/Jun/2025:17:47:19 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902451&_timer304=1750844839852 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:19 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100201103&_timer304=1750844839852 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:19 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902451&_timer304=1750844839852 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:19 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902451&stm=2025-06-24+08:00&etm=2025-06-25+17:50&_timer304=1750844839852 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:19 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902451&_timer304=1750844839852 HTTP/1.1" 200 845
************ - - [25/Jun/2025:17:47:19 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902451&stm=2025-06-24+08:00&etm=2025-06-25+17:50&_timer304=1750844839852 HTTP/1.1" 200 887
************ - - [25/Jun/2025:17:47:19 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902451&_timer304=1750844839852 HTTP/1.1" 200 520
************ - - [25/Jun/2025:17:47:19 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100201103&_timer304=1750844839852 HTTP/1.1" 200 521
************ - - [25/Jun/2025:17:47:25 +0800] "OPTIONS /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750844845218 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:25 +0800] "OPTIONS /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750844845218 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:25 +0800] "OPTIONS /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750844845218 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:25 +0800] "OPTIONS /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+17:50&_timer304=1750844845218 HTTP/1.1" 200 -
************ - - [25/Jun/2025:17:47:25 +0800] "GET /api/xxjh/plan/get-plan_by_rainCode?rainCode=10902448&_timer304=1750844845218 HTTP/1.1" 200 519
************ - - [25/Jun/2025:17:47:25 +0800] "GET /api/syq/ststbprpb/select-by-stcd?stcd=10902448&_timer304=1750844845218 HTTP/1.1" 200 838
************ - - [25/Jun/2025:17:47:25 +0800] "GET /api/shyj/locwarn/listLocWarnDetailByStcd?stcd=10902448&stm=2025-06-24+08:00&etm=2025-06-25+17:50&_timer304=1750844845218 HTTP/1.1" 200 1146
************ - - [25/Jun/2025:17:47:25 +0800] "GET /api/xxjh/plan/get-plan-list-by-adcd?adcd=220221100200109&_timer304=1750844845218 HTTP/1.1" 200 520
