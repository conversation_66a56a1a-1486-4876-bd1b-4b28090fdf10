package com.huitu.cloud.api.syq.river.entity.export;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
/**
 * <p>
 * 河道日旬月均值导出
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-02
 */
@ApiModel(value="ExportQueryRiverDayAvg对象", description="河道日旬月均值导出")
public class ExportQueryRiverDayAvg {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "时间类型(1:一日，4：一旬，5：一月)",required = true)
    private String tmType;
    @ApiModelProperty(value = "测站编码",required = true)
    private List<String> stList;

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getTmType() {
        return tmType;
    }

    public void setTmType(String tmType) {
        this.tmType = tmType;
    }

    public List<String> getStList() {
        return stList;
    }

    public void setStList(List<String> stList) {
        this.stList = stList;
    }
}
