package com.huitu.cloud.api.hsfxt.xzhq.controler;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.hsfxt.river.entity.RiverTree;
import com.huitu.cloud.api.hsfxt.river.entity.TunXiangQing;
import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhShehuijingji;
import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqTree;
import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqTunXiangqing;
import com.huitu.cloud.api.hsfxt.xzhq.entity.XzhqXiangQing;
import com.huitu.cloud.api.hsfxt.xzhq.service.XzhqService;
import com.huitu.cloud.validation.constraints.SqlInjection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 风险图-蓄滞洪区 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */

@RestController
@Api(tags = "风险图-蓄滞洪区接口")
@Validated
@RequestMapping("/api/hsfxt/xzhq")
public class XzhqResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "7c9bcd1d-780f-4773-bdf8-a936f7ebe12b";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Autowired
    XzhqService xzhqService;

    @ApiOperation(value = "蓄滞洪区列表查询", notes = "蓄滞洪区列表查询")
    @GetMapping(value = "get-xzhq-tree-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合（不要洪水量级）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "最小风险政区单位", dataType = "String"),
            @ApiImplicitParam(name = "hl", value = "洪水量级", dataType = "String"),
            @ApiImplicitParam(name = "bzqid", value = "编制区编码", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<XzhqTree>>> getXzhqTreeInfo(@SqlInjection @RequestParam String key, @RequestParam String name, @RequestParam String hl, @RequestParam String bzqid) throws Exception {
        List<XzhqTree> list = xzhqService.getXzhqTreeInfo(key, name, hl, bzqid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "蓄滞洪区社会经济统计查询", notes = "蓄滞洪区社会经济统计查询")
    @GetMapping(value = "get-xzhq-shjj-tree-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<XzhShehuijingji>>> getXzhqShjjTreeInfo(@RequestParam String key) throws Exception {
        List<XzhShehuijingji> list = xzhqService.getXzhqShjjTreeInfo(key);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "蓄滞洪区列表详情查询", notes = "蓄滞洪区列表详情查询")
    @GetMapping(value = "get-xzhq-shjj-detail-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bzqid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<XzhqXiangQing>> getXzhqShjjDetailInfo(@RequestParam String key, @RequestParam String adcd, @RequestParam String bzqid) throws Exception {
        XzhqXiangQing result = xzhqService.getXzhqShjjDetailInfo(key,adcd,bzqid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }

    @ApiOperation(value = "蓄滞洪区列表（屯）详情查询", notes = "蓄滞洪区列表（屯）详情查询")
    @GetMapping(value = "get-xzhq-tun-shjj-detail-info")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "查询参数组合（不要洪水量级）", required = true, dataType = "String"),
            @ApiImplicitParam(name = "hl", value = "洪水量级", required = true, dataType = "String"),
            @ApiImplicitParam(name = "adcd", value = "政区编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "bzqid", value = "编制区编码", required = true, dataType = "String")
    })
    public ResponseEntity<SuccessResponse<XzhqTunXiangqing>> getXzhqTunShjjDetailInfo(@RequestParam String key, @RequestParam String hl, @RequestParam String adcd, @RequestParam String bzqid) throws Exception {
        XzhqTunXiangqing result = xzhqService.getXzhqTunShjjDetailInfo(key,hl,adcd,bzqid);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", result));
    }
}
