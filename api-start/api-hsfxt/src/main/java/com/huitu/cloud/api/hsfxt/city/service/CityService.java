package com.huitu.cloud.api.hsfxt.city.service;

import com.huitu.cloud.api.hsfxt.city.entity.*;

import java.util.List;

/**
 * <p>
 * 风险图-城市  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-09
 */
public interface CityService {
    /**
     * 编制区域信息查询
     *
     * @param fxtType 风险图类型
     * @return
     */
    List<DicBianzhiquyu> getBzqyInfo(String fxtType);

    /**
     * 洪水来源信息查询
     *
     * @param sortIds 排序号
     * @return
     */
    List<DicHongShuiLaiYuan> getHslyInfo(String sortIds);

    /**
     * 分区信息查询
     *
     * @param bianzhiquid 编制区编码
     * @return
     */
    List<DicJisuanfenqu> getFqInfo(String bianzhiquid);

    /**
     * 溃口信息查询
     *
     * @param jisuanfenquid 计算分区编码
     * @return
     */
    List<DicKuikou> getKkInfo(String jisuanfenquid);

    /**
     * 洪水量级信息查询
     *
     * @param bianzhiquid 编制区编码
     * @return
     */
    List<DicHongshuiliangji> getHsljInfo(String bianzhiquid);

    /**
     * 降雨历时查询
     *
     * @param bianzhiquid 编制区编码
     * @return
     */
    List<DicJiangyulishi> getJylsInfo(String bianzhiquid);

    /**
     * 简介信息查询
     *
     * @param fengxiantuid 风险图类型编码
     * @param bianzhiquid  编制区编码
     * @return
     */
    List<DicJianjie> getJjInfo(String fengxiantuid, String bianzhiquid);

    /**
     * 溃口名称属性信息查询
     *
     * @param kuikousort 风险图类型编码
     * @return
     */
    List<DicShuxing> getKkmcInfo(String kuikousort);

    /**
     * 社会经济统计查询
     *
     * @param key 风险图类型编码
     * @return
     */
    List<CsShehuijingji> getShjjTreeInfo(String key);

    /**
     * 直接经济损失列表查询
     *
     * @param key  风险图类型编码
     * @param name 最小风险政区单位
     * @return
     */
    List<CsZhijiejingjisunshi> getSjjjssTreeInfo(String key, String name);
    /**
     * 直接经济损失单个详情查询
     *
     * @param key  风险图类型编码
     * @param adcd 政区编码
     * @return
     */
    CsZhijiejingjisunshi getOneSjjjssInfo(String key, String adcd);
}
