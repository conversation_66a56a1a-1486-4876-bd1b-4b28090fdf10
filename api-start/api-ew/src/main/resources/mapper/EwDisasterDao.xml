<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwDisasterDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPageList" resultType="com.huitu.cloud.api.ew.entity.response.EwDisasterRecordResponse" flushCache="true">
        SELECT DISASTER_ID, A.ADCD, D.ADNM, BEGIN_TIME, END_TIME, DANGEROUS_DESC, DISASTER_DESC, TRANSFER_COUNT, TRAPPED_COUNT,
        MISSING_COUNT, DEATH_COUNT, COLLAPSE_HOUSE_COUNT, FLOODED_FARMLAND_COUNT, CREATE_BY, B.LOGINNM CREATE_BY_ALIAS, CREATE_TIME,
        LATEST_BY, C.LOGINNM LATEST_BY_ALIAS, LATEST_TIME
        FROM EW_DISASTER_RECORD A
        LEFT JOIN BNS_USERLOGUSER_B B ON B.USERID = A.CREATE_BY
        LEFT JOIN BNS_USERLOGUSER_B C ON C.USERID = A.LATEST_BY
        LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = A.ADCD
        <where>
            <choose>
                <when test="map.subItems">
                    LEFT(A.ADCD, #{map.level}) = LEFT(#{map.adcd}, #{map.level})
                    <if test="map.level == '4'.toString()">AND LEFT(A.ADCD, 6) NOT IN ('220581')</if>
                </when>
                <otherwise>
                    A.ADCD = #{map.adcd}
                </otherwise>
            </choose>
            AND ((BEGIN_TIME BETWEEN #{map.stm} AND #{map.etm}) OR (END_TIME BETWEEN #{map.stm} AND #{map.etm}))
        </where>
        ORDER BY A.LATEST_TIME DESC
    </select>
    <select id="getRecord" resultType="com.huitu.cloud.api.ew.entity.response.EwDisasterRecordResponse">
        SELECT DISASTER_ID,
               A.ADCD,
               D.ADNM,
               BEGIN_TIME,
               END_TIME,
               DANGEROUS_DESC,
               DISASTER_DESC,
               TRANSFER_COUNT,
               TRAPPED_COUNT,
               MISSING_COUNT,
               DEATH_COUNT,
               COLLAPSE_HOUSE_COUNT,
               FLOODED_FARMLAND_COUNT,
               CREATE_BY,
               B.LOGINNM CREATE_BY_ALIAS,
               CREATE_TIME,
               LATEST_BY,
               C.LOGINNM LATEST_BY_ALIAS,
               LATEST_TIME
        FROM EW_DISASTER_RECORD A
                 LEFT JOIN BNS_USERLOGUSER_B B ON B.USERID = A.CREATE_BY
                 LEFT JOIN BNS_USERLOGUSER_B C ON C.USERID = A.LATEST_BY
                 LEFT JOIN MDT_ADCDINFO_B D ON D.ADCD = A.ADCD
        WHERE DISASTER_ID = #{disasterId}
    </select>
    <select id="getFileList" resultType="com.huitu.cloud.api.ew.entity.base.EwDisasterFile">
        SELECT [FILE_ID], DISASTER_ID, FILE_TYPE, FILE_SNO, [FILE_NAME], FILE_PATH
        FROM EW_DISASTER_FILE
        <where>
            <foreach collection="disasterIds" item="disasterId" open="DISASTER_ID IN (" separator="," close=")">
                #{disasterId}
            </foreach>
        </where>
        ORDER BY FILE_SNO, LATEST_TIME ASC
    </select>
    <select id="exists" resultType="java.lang.Boolean">
        SELECT CAST((CASE WHEN COUNT(DISASTER_ID) > 0 THEN 1 ELSE 0 END) AS BIT) FROM EW_DISASTER_RECORD
        WHERE ADCD = #{adcd} AND BEGIN_TIME = #{beginTime} AND END_TIME = #{endTime}
        <if test="disasterId != null and disasterId != ''">AND DISASTER_ID != #{disasterId}</if>
    </select>
    <insert id="insertRecord">
        INSERT INTO EW_DISASTER_RECORD(DISASTER_ID, ADCD, BEGIN_TIME, END_TIME, DANGEROUS_DESC, DISASTER_DESC, TRANSFER_COUNT,
                                       TRAPPED_COUNT, MISSING_COUNT, DEATH_COUNT, COLLAPSE_HOUSE_COUNT, FLOODED_FARMLAND_COUNT,
                                       CREATE_BY, CREATE_TIME, LATEST_BY, LATEST_TIME)
        VALUES (#{disasterId}, #{adcd}, #{beginTime}, #{endTime}, #{dangerousDesc}, #{disasterDesc}, #{transferCount},
                #{trappedCount}, #{missingCount}, #{deathCount}, #{collapseHouseCount}, #{floodedFarmlandCount},
                #{createBy}, GETDATE(), #{createBy}, GETDATE())
    </insert>
    <update id="updateRecord">
        UPDATE EW_DISASTER_RECORD
        SET ADCD                   = #{adcd},
            BEGIN_TIME             = #{beginTime},
            END_TIME               = #{endTime},
            DANGEROUS_DESC         = #{dangerousDesc},
            DISASTER_DESC          = #{disasterDesc},
            TRANSFER_COUNT         = #{transferCount},
            TRAPPED_COUNT          = #{trappedCount},
            MISSING_COUNT          = #{missingCount},
            DEATH_COUNT            = #{deathCount},
            COLLAPSE_HOUSE_COUNT   = #{collapseHouseCount},
            FLOODED_FARMLAND_COUNT = #{floodedFarmlandCount},
            LATEST_BY              = #{latestBy},
            LATEST_TIME            = GETDATE()
        WHERE DISASTER_ID = #{disasterId}
    </update>
    <delete id="deleteRecord">
        DELETE
        FROM EW_DISASTER_RECORD
        WHERE DISASTER_ID = #{disasterId}
    </delete>
    <insert id="saveFileList">
        INSERT INTO EW_DISASTER_FILE([FILE_ID], DISASTER_ID, FILE_TYPE, FILE_SNO, [FILE_NAME], FILE_PATH, LATEST_TIME)
        SELECT [FILE_ID], DISASTER_ID, FILE_TYPE, FILE_SNO, [FILE_NAME], FILE_PATH, LATEST_TIME FROM (VALUES
        <foreach collection="fileList" item="file" index="index" separator=",">
            (NEWID(), #{disasterId}, #{fileType}, #{index}, #{file.filename}, #{file.filepath}, GETDATE())
        </foreach>) AS T([FILE_ID], DISASTER_ID, FILE_TYPE, FILE_SNO, [FILE_NAME], FILE_PATH, LATEST_TIME)
    </insert>
    <delete id="deleteFileByDisasterId">
        DELETE
        FROM EW_DISASTER_FILE
        WHERE DISASTER_ID = #{disasterId}
    </delete>
</mapper>
