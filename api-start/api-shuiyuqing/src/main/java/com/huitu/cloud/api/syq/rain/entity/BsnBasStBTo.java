package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-09
 */
@ApiModel(value="BsnBasB对象", description="")
public class BsnBasStBTo extends Model<BsnBasStBTo> {

    private static final long serialVersionUID=1L;

    @TableField("BAS_NAME")
    private String basName;

    @TableField("PBAS_CODE")
    private String pbasCode;

    @TableField("BAS_CODE")
    private String basCode;

    @TableField("BAS_LEVEL")
    private Integer basLevel;

    @TableField("STCD")
    private String stcd;

    @ApiModelProperty(value = "平均降水量(mm)")
    private String avgDrp;
    @ApiModelProperty(value = "最大点雨量（mm）")
    private String maxDrp;
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "是否所有测站的最大降雨站")
    private boolean maxFlag;
    @ApiModelProperty(value = "下级流域")
    private List<BsnBasStBTo> children;

    public List<BsnBasStBTo> getChildren() {
        return children;
    }

    public void setChildren(List<BsnBasStBTo> children) {
        this.children = children;
    }

    public String getAvgDrp() {
        return avgDrp;
    }

    public void setAvgDrp(String avgDrp) {
        this.avgDrp = avgDrp;
    }

    public String getMaxDrp() {
        return maxDrp;
    }

    public void setMaxDrp(String maxDrp) {
        this.maxDrp = maxDrp;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public boolean isMaxFlag() {
        return maxFlag;
    }

    public void setMaxFlag(boolean maxFlag) {
        this.maxFlag = maxFlag;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getBasName() {
        return basName;
    }

    public void setBasName(String basName) {
        this.basName = basName;
    }

    public String getPbasCode() {
        return pbasCode;
    }

    public void setPbasCode(String pbasCode) {
        this.pbasCode = pbasCode;
    }

    public String getBasCode() {
        return basCode;
    }

    public void setBasCode(String basCode) {
        this.basCode = basCode;
    }

    public Integer getBasLevel() {
        return basLevel;
    }

    public void setBasLevel(Integer basLevel) {
        this.basLevel = basLevel;
    }

    @Override
    protected Serializable pkVal() {
        return null;
    }

    @Override
    public String toString() {
        return "BsnBasB{" +
        "basName=" + basName +
        ", pbasCode=" + pbasCode +
        ", basCode=" + basCode +
        ", basLevel=" + basLevel +
        "}";
    }
}
