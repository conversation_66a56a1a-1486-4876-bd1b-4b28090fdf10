package com.huitu.cloud.api.datamanager.anmrecord.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 异常站数统计
 *
 * <AUTHOR>
 */
@ApiModel(value = "异常站数统计")
public class AnmStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "异常站数")
    private Integer abnormal;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public Integer getAbnormal() {
        return abnormal;
    }

    public void setAbnormal(Integer abnormal) {
        this.abnormal = abnormal;
    }
}
