package com.huitu.cloud.api.hyst.survey.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.hyst.survey.entity.HystStats;
import com.huitu.cloud.api.hyst.survey.entity.SurveyQuery;
import com.huitu.cloud.api.hyst.survey.entity.SurveyVo;

import java.util.List;

/**
 * 小水电概况
 *
 * <AUTHOR>
 */
public interface SurveyService {

    /**
     * 小水电概况分页查询
     *
     * @param query
     * @return
     */
    IPage<SurveyVo> getSurveyPage(SurveyQuery query) throws Exception;

    /**
     * 小水电概况详情列表
     *
     * @param query
     * @return
     */
    List<SurveyVo> getDetailsList(SurveyQuery query) throws Exception;

    /**
     * 小水电概况统计图表
     *
     * @param query
     * @return
     */
    HystStats getHystStats(SurveyQuery query) throws Exception;

}
