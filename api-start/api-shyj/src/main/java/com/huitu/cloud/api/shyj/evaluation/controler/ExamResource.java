package com.huitu.cloud.api.shyj.evaluation.controler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import com.huitu.cloud.api.shyj.evaluation.service.ExamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 考核评价  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@RestController
@Api(tags = "考核评分接口")
@RequestMapping("/api/shyj/exam")
public class ExamResource extends AbstractApiResource implements ApiResource {
    @Override
    public String getUuid() {
        return "a066cf9e-6271-4fc8-8c8c-a699ca55e76e";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }
    @Autowired
    private ExamService baseService;
    @ApiOperation(value = "获取考核指标", notes = "获取考核指标")
    @GetMapping(value = "select-examindex-list")
    public ResponseEntity<SuccessResponse<List<ExamIndex>>> getExamIndexList() throws Exception {
        List<ExamIndex> list = baseService.getExamIndexList();
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "获取考核指标（分页）", notes = "获取考核指标（分页）")
    @GetMapping(value = "select-examindex-list-page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "考核类型",  dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页码",  dataType = "int",example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量",  dataType = "int",example = "10")
    })
    public ResponseEntity<SuccessResponse<Page<ExamIndex>>> getExamIndexListPage(@RequestParam String type, @RequestParam int pageNum, @RequestParam int pageSize) throws Exception {
        IPage<ExamIndex> list = baseService.getExamIndexListPage(type, pageNum, pageSize);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }

    @ApiOperation(value = "添加考核指标", notes = "添加考核指标")
    @PostMapping(value = "add-examindex")
    public ResponseEntity<SuccessResponse<Boolean>> save(@RequestBody ExamIndex entity) throws Exception {
        List<ExamIndex> list = baseService.getExamIndexById(entity);
        if (list.size() != 0) {
            return ResponseEntity.ok(new SuccessResponse(this, "EXISTS", false));
        }
        boolean flag = baseService.saveExamIndex(entity);
        String message = flag ? "OK" : "ERR";
        return ResponseEntity.ok(
                new SuccessResponse(this, message, flag));
    }

    @ApiOperation(value = "更新考核指标", notes = "更新考核指标")
    @PostMapping(value = "upd-examindex")
    public ResponseEntity<SuccessResponse<Boolean>> update(@RequestBody ExamIndex entity) throws Exception {
        boolean flag = baseService.updateExamIndex(entity);
        return ResponseEntity.ok(
                new SuccessResponse(this, flag ? "OK" : "ERR", flag));
    }

    @ApiOperation(value = "删除考核指标", notes = "删除考核指标")
    @GetMapping(value = "del-examindex")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "examType", value = "考核类型", dataType = "String"),
            @ApiImplicitParam(name = "examOption", value = "考核项", dataType = "String")
    })
    public ResponseEntity<SuccessResponse<Boolean>> delete(@RequestParam String examType, @RequestParam String examOption) throws Exception {
        boolean flag = baseService.deleteExamIndex(examType, examOption);
        return ResponseEntity.ok(
                new SuccessResponse(this, flag ? "OK" : "ERR", flag));
    }

    @ApiOperation(value = "获取与省级部门配合得分", notes = "根据政区编码、年月获取与省级部门配合得分")
    @GetMapping(value = "select-exam-province-score-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adcd", value = "政区编码",  dataType = "String"),
            @ApiImplicitParam(name = "month", value = "年月",  dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ExamProvinceScore>>> getExamProvinceScoreListByAdcd(@RequestParam String adcd, @RequestParam String month) throws Exception {
        List<ExamProvinceScore> list = baseService.getExamProvinceScoreListByAdcd(adcd,month);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "添加或修改与省级部门配合得分", notes = "添加或修改与省级部门配合得分")
    @PostMapping(value = "addorupdate-exam-province-score")
    public ResponseEntity<SuccessResponse<Integer>> addOrUpdateExamProvinceScoreByAdcdAndMonth(@RequestBody ExamProvinceScore examProvinceScore) throws Exception {
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", baseService.addOrUpdateExamProvinceScoreByAdcdAndMonth(examProvinceScore)));
    }
    @ApiOperation(value = "获取市县满意度得分", notes = "根据政区编码、年月获取市县满意度得分")
    @GetMapping(value = "select-exam-city-score-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "month", value = "年月",  dataType = "String")
    })
    public ResponseEntity<SuccessResponse<List<ExamCityScore>>> getExamCityScoreListByAdcd(@RequestParam String month) throws Exception {
        List<ExamCityScore> list = baseService.getExamCityScoreListByYm(month);
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", list));
    }
    @ApiOperation(value = "添加或修改市县满意度得分", notes = "添加或修改市县满意度得分")
    @PostMapping(value = "addorupdate-exam-city-score")
    public ResponseEntity<SuccessResponse<Integer>> addOrUpdateExamCityScoreByAdcdAndMonth(@RequestBody ExamCityScore examCityScore) throws Exception {
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", baseService.addOrUpdateExamCityScoreByAdcdAndMonth(examCityScore)));
    }

}
