package com.huitu.cloud.api.hsfxt.xzhq.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 蓄滞洪区树形列表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
@ApiModel(value = "XzhqTree对象", description = " 蓄滞洪区树形列表")
public class XzhqTree implements Serializable {
    @ApiModelProperty(value = "行政区编码")
    private String adcd;
    @ApiModelProperty(value = "行政区名称")
    private String adnm;
    @ApiModelProperty(value = "上级编码")
    private String padcd;
    @ApiModelProperty(value = "下级节点集合")
    private List<XzhqTree> children;
    @ApiModelProperty(value = "影响屯总数")
    private int childrenCount;
    @ApiModelProperty(value = "查询详情参数组合")
    private String keyindex;
    @ApiModelProperty(value = "转移方案")
    private List<XzhqZhuanYiFangAn> zyfa;

    public List<XzhqZhuanYiFangAn> getZyfa() {
        return zyfa;
    }

    public void setZyfa(List<XzhqZhuanYiFangAn> zyfa) {
        this.zyfa = zyfa;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public List<XzhqTree> getChildren() {
        return children;
    }

    public void setChildren(List<XzhqTree> children) {
        this.children = children;
    }

    public int getChildrenCount() {
        return childrenCount;
    }

    public void setChildrenCount(int childrenCount) {
        this.childrenCount = childrenCount;
    }

    public String getKeyindex() {
        return keyindex;
    }

    public void setKeyindex(String keyindex) {
        this.keyindex = keyindex;
    }

}
