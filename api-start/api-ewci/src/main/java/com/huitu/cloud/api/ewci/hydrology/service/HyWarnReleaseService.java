package com.huitu.cloud.api.ewci.hydrology.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ewci.hydrology.entity.query.HyWarnRecordQuery;
import com.huitu.cloud.api.ewci.hydrology.entity.request.HyWarnFileRequest;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnDocumentResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnFileResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnRecordResponse;
import com.huitu.cloud.api.ewci.hydrology.entity.response.HyWarnUserResponse;
import com.huitu.cloud.api.usif.user.entity.UserInfos;

import java.util.List;

/**
 * 洪水预警发布服务
 *
 * <AUTHOR>
 */
public interface HyWarnReleaseService {

    /**
     * 分页获取预警记录列表
     *
     * @param query 查询条件
     * @return 指标列表
     */
    IPage<HyWarnRecordResponse> getPageList(HyWarnRecordQuery query);

    /**
     * 获取预警记录信息
     *
     * @param warnId 预警ID
     * @return 记录信息
     */
    HyWarnRecordResponse getRecordInfo(String warnId);

    /**
     * 获取预警文件信息
     *
     * @param warnId 预警ID
     * @return 文件信息
     */
    HyWarnFileResponse getFileInfo(String warnId);

    /**
     * 获取最新的发文期数
     *
     * @param yr 年份
     * @return 期数
     */
    int getNewFileNo(String yr);

    /**
     * 获取预警人员列表
     *
     * @param deptid 组织ID
     * @return 人员列表
     */
    List<HyWarnUserResponse> getUserList(String deptid);

    /**
     * 获取洪水预警文档
     *
     * @param warnId 预警ID
     * @return 预警文档
     */
    HyWarnDocumentResponse getDocument(String warnId);

    /**
     * 保存洪水预警文件
     *
     * @param request 请求对象
     * @return 受影响的行数
     */
    int saveFile(HyWarnFileRequest request);

    /**
     * 忽略预警
     *
     * @param warnId 预警ID
     * @return 受影响的行数
     */
    int ignore(String warnId);

    /**
     * 发布预警
     *
     * @param warnId 预警ID
     * @param user   用户信息
     * @return 预警文件
     */
    HyWarnFileResponse release(String warnId, UserInfos user);
}
