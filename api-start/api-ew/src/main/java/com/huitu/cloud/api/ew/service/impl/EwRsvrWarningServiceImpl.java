package com.huitu.cloud.api.ew.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huitu.cloud.api.ew.entity.ext.EwRsvrWarningSending;
import com.huitu.cloud.api.ew.entity.ext.EwSendingBase;
import com.huitu.cloud.api.ew.entity.request.EwRsvrWarningCorrectRequest;
import com.huitu.cloud.api.ew.entity.request.EwRsvrWarningRecordRequest;
import com.huitu.cloud.api.ew.entity.request.EwRsvrWarningSendingRequest;
import com.huitu.cloud.api.ew.entity.response.EwRsvrWarningRecordResponse;
import com.huitu.cloud.api.ew.entity.response.EwWarningGradeSummaryResponse;
import com.huitu.cloud.api.ew.entity.sms.SmsMessage;
import com.huitu.cloud.api.ew.entity.sms.SmsReceiver;
import com.huitu.cloud.api.ew.exception.WarningException;
import com.huitu.cloud.api.ew.mapper.EwRsvrWarningMessageDao;
import com.huitu.cloud.api.ew.mapper.EwRsvrWarningRecordDao;
import com.huitu.cloud.api.ew.mapper.EwRsvrWarningSendingDao;
import com.huitu.cloud.api.ew.mapper.SmsSendDao;
import com.huitu.cloud.api.ew.service.EwRsvrWarningService;
import com.huitu.cloud.api.usif.user.entity.UserInfos;
import com.huitu.cloud.config.properties.ApplicationProperties;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 水库预警服务 实现类
 */
@Service
@EnableConfigurationProperties(EwRsvrWarningServiceImpl.Properties.class)
public class EwRsvrWarningServiceImpl implements EwRsvrWarningService {

    private ApplicationProperties applicationProperties;
    private Properties warningProperties;
    private EwRsvrWarningRecordDao recordDao;
    private EwRsvrWarningMessageDao messageDao;
    private EwRsvrWarningSendingDao sendingDao;
    private SmsSendDao sendDao;

    @Autowired
    public void setApplicationProperties(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @Autowired
    public void setWarningProperties(Properties warningProperties) {
        this.warningProperties = warningProperties;
    }

    @Autowired
    public void setRecordDao(EwRsvrWarningRecordDao recordDao) {
        this.recordDao = recordDao;
    }

    @Autowired
    public void setMessageDao(EwRsvrWarningMessageDao messageDao) {
        this.messageDao = messageDao;
    }

    @Autowired
    public void setSendingDao(EwRsvrWarningSendingDao sendingDao) {
        this.sendingDao = sendingDao;
    }

    @Autowired
    public void setSendDao(SmsSendDao sendDao) {
        this.sendDao = sendDao;
    }

    @Override
    public IPage<EwRsvrWarningRecordResponse> getPageList(EwRsvrWarningRecordRequest request) {
        return recordDao.getPageList(request.toPage(), request.toQuery());
    }

    @Override
    public EwRsvrWarningRecordResponse getRecord(String warnId) {
        return recordDao.getRecord(warnId);
    }

    @Override
    public IPage<EwRsvrWarningSending> getSendingPageList(EwRsvrWarningSendingRequest<EwRsvrWarningSending> request) {
        IPage<EwRsvrWarningSending> page = sendingDao.getSendingPageList(request.toPage(), request.toQuery());
        if (null != page) {
            EwSendingBase.handleXccData(page.getRecords());
        }
        return page;
    }

    @Override
    public String getWarningFlag(String msgId) {
        return messageDao.getWarningFlag(msgId);
    }

    @Override
    public List<EwWarningGradeSummaryResponse> getRsvrWarningGradeSummary(EwRsvrWarningRecordRequest request) {
        return recordDao.getRsvrWarningGradeSummary(request.toQuery());
    }

    @Override
    public BigDecimal getLatestRz(String stcd) {
        return recordDao.getLatestRz(stcd);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int correct(EwRsvrWarningCorrectRequest request, UserInfos user) {
        int result = recordDao.correct(request.getWarnId());
        if (result <= 0) {
            throw new WarningException("当前预警不存在或已忽略，无法更正");
        }

        List<SmsReceiver> receivers = recordDao.getCorrectReceivers(request.getWarnId());
        if (!CollectionUtils.isEmpty(receivers)) {
            SmsMessage message = recordDao.getCorrectMessage(request.toQuery());
            message.setSignName(sendDao.getSignName(applicationProperties.getPlatformId(), applicationProperties.getSmsSignName()));
            message.setTemplateCode(warningProperties.getCorrectSmsTemplate());
            message.setSender(StringUtils.isNotBlank(user.getLoginnm()) ? user.getLoginnm() : user.getUsername());
            message.setUnitCode(user.getAdcd());
            message.setUnitName(user.getDeptnm());
            message.setAccessKeyId(user.getUsername());
            message.setPlatformId(applicationProperties.getPlatformId());
            if (sendDao.saveMessage(message) > 0) {
                sendDao.saveReceivers(message.getMsgId(), receivers, message.getPlatformId());
            }
        }
        return result;
    }

    @ConfigurationProperties(prefix = Properties.PREFIX, ignoreInvalidFields = true)
    public static class Properties {
        public static final String PREFIX = "huitu.warning.rsvr";

        /**
         * 预警更正短信模版CODE
         */
        private String correctSmsTemplate;

        public String getCorrectSmsTemplate() {
            return correctSmsTemplate;
        }

        public void setCorrectSmsTemplate(String correctSmsTemplate) {
            this.correctSmsTemplate = correctSmsTemplate;
        }
    }
}
