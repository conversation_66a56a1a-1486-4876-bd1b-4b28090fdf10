<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.monitor.mapper.IaCPrevadDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getPrevadList" resultType="com.huitu.cloud.api.ewci.monitor.entity.IaCPrevad">
        SELECT P.ADCD, ISNULL(A.ADNM, B.ADNM) ADNM, P.PTCOUNT, P.LDAREA, P.PLAREA, P.ETCOUNT, P.ECOUNT1, P.ECOUNT2,
        P.ECOUNT3, P.ECOUNT4, P.HTCOUNT, P.HCOUNT1, P.HCOUNT2, P.HCOUNT3, P.HCOUNT4, B.LGTD, B.LTTD
        FROM IA_C_PREVAD P
        LEFT JOIN IA_C_ADINFO A ON A.ADCD = P.ADCD
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = P.ADCD
        <where>
            <choose>
                <when test="map.type != null and map.type != ''">
                    <choose>
                        <when test='"ws".equalsIgnoreCase(map.type)'>
                            AND P.ADCD IN (SELECT DISTINCT A.ADCD FROM IA_C_WSADCD A INNER JOIN IA_C_WSADCD B ON B.WSCD
                            = A.WSCD WHERE A.ADCD != B.ADCD AND B.ADCD = #{map.adcd})
                        </when>
                        <otherwise>
                            AND LEFT(P.ADCD, 12) = LEFT(#{map.adcd}, 12) AND P.ADCD != #{map.adcd}
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    AND P.ADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY P.ADCD ASC
    </select>
</mapper>
