package com.huitu.cloud.api.fusion.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huitu.cloud.api.fusion.entity.response.FusionWarningRecordResp;
import com.huitu.cloud.entity.EntityRequest;
import com.huitu.cloud.entity.PageBean;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.validation.constraints.Option;
import com.huitu.cloud.validation.constraints.StringLength;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 山洪预警记录请求对象
 */
@ApiModel(value = "山洪预警记录请求对象")
public class FusionWarningRecordReq extends PageBean<FusionWarningRecordResp> implements EntityRequest {

    @ApiModelProperty(value = "行政区划代码", example = "220000000000000", required = true)
    @NotBlank(message = "参数[行政区划代码]不能为空")
    @StringLength(min = 15, max = 15, message = "参数[行政区划代码]的长度应为15个字符")
    private String adcd;
    @ApiModelProperty(value = "流域代码")
    private String bscd;
    @ApiModelProperty(value = "预警等级，注：1=一级预警、2=二级预警、3=三级预警（新）、4=立即转移、5=准备转移")
    @Option(value = {"1", "2", "3", "4", "5"}, message = "参数[预警等级]的值无效")
    private List<Integer> warnGradeList;
    @ApiModelProperty(value = "预警状态，注：0=新产生、1=预警升级、10=内部预警、20=外部预警、21=人工通知、30=关闭预警、31=预警更正")
    @Option(value = {"0", "1", "10", "20", "21", "30", "31"}, message = "参数[预警状态]的值无效")
    private List<Integer> warnStatusList;
    @ApiModelProperty(value = "预警方式，注：0=自动、1=人工")
    @Option(value = {"0", "1"}, message = "参数[预警方式]的值无效")
    private List<String> warnModeList;
    @ApiModelProperty(value = "开始时间，注：必填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-03 08:00:00", required = true)
    @NotNull(message = "参数[开始时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stm;
    @ApiModelProperty(value = "结束时间，注：选填，格式：yyyy-MM-dd HH:mm:ss", example = "2023-08-04 08:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etm;
    @ApiModelProperty(value = "预警名称，注：模糊匹配")
    private String warnName;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getBscd() {
        return bscd;
    }

    public void setBscd(String bscd) {
        this.bscd = bscd;
    }

    public List<Integer> getWarnGradeList() {
        return warnGradeList;
    }

    public void setWarnGradeList(List<Integer> warnGradeList) {
        this.warnGradeList = warnGradeList;
    }

    public List<Integer> getWarnStatusList() {
        return warnStatusList;
    }

    public void setWarnStatusList(List<Integer> warnStatusList) {
        this.warnStatusList = warnStatusList;
    }

    public List<String> getWarnModeList() {
        return warnModeList;
    }

    public void setWarnModeList(List<String> warnModeList) {
        this.warnModeList = warnModeList;
    }

    public Date getStm() {
        return stm;
    }

    public void setStm(Date stm) {
        this.stm = stm;
    }

    public Date getEtm() {
        return etm;
    }

    public void setEtm(Date etm) {
        this.etm = etm;
    }

    public String getWarnName() {
        return warnName;
    }

    public void setWarnName(String warnName) {
        this.warnName = warnName;
    }

    @Override
    public Map<String, Object> toQuery() {
        Map<String, Object> params = new HashMap<>();
        params.put("adcd", getAdcd());
        params.put("level", AdcdUtil.getAdLevel(getAdcd()));
        params.put("bscd", getBscd());
        params.put("warnGradeList", getWarnGradeList());
        params.put("warnStatusList", getWarnStatusList());
        params.put("warnModeList", getWarnModeList());
        params.put("stm", getStm());
        if (null != getEtm()) {
            Date btm = getStm(), etm = getEtm();
            if (btm.getTime() >= etm.getTime()) {
                throw new IllegalArgumentException("参数[结束时间]应大于参数[开始时间]");
            }
            params.put("etm", etm);
        }
        params.put("warnName", getWarnName());
        return params;
    }
}
