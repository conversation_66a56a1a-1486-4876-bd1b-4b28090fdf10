package com.huitu.cloud.api.syq.skdt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@ApiModel(value="SkInfo", description="水库调度单个水库综合信息")
public class SkInfo  {

    @ApiModelProperty(value = "测站编码")
    @TableId(value = "STCD")
    private String stcd;
    @ApiModelProperty(value = "水库工程编码")
    private String resCode;
    @ApiModelProperty(value = "水库名称")
    private String resName;
    @ApiModelProperty(value = "时间")
    private Date tm;
    @ApiModelProperty(value = "库上水位")
    @TableField("RZ")
    private String rz;
    @ApiModelProperty(value = "入库流量")
    @TableField("INQ")
    private BigDecimal inq;
    @ApiModelProperty(value = "出库流量")
    @TableField("OTQ")
    private BigDecimal otq;
    @ApiModelProperty(value = "蓄水量")
    @TableField("W")
    private BigDecimal w;

    @ApiModelProperty(value = "超讯限水位")
    @TableField("RZFSLTDZ")
    private BigDecimal rzfsltdz;
    @ApiModelProperty(value = "汛限水位")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @ApiModelProperty(value = "防洪库容")
    @TableField("FLDCP")
    private BigDecimal fldcp;

    @ApiModelProperty(value = "集水面积")
    @TableField("DRNA")
    private Integer drna;

    @ApiModelProperty(value = "可（调）用防洪库容")
    @TableField("USABLEW")
    private BigDecimal usablew;

    @ApiModelProperty(value = "纳雨能力")
    private BigDecimal nyDrp;
    @ApiModelProperty(value = "洪峰时间")
    private LocalDateTime hftm;
    @ApiModelProperty(value = "洪峰流量")
    private BigDecimal hfinq;

    @ApiModelProperty(value = "库区面雨量")
    private String avgRain;
    @ApiModelProperty(value = "产水量")
    private BigDecimal waterYield;


    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public Date getTm() {
        return tm;
    }

    public void setTm(Date tm) {
        this.tm = tm;
    }

    public String getRz() {
        return rz;
    }

    public void setRz(String rz) {
        this.rz = rz;
    }

    public BigDecimal getInq() {
        return inq;
    }

    public void setInq(BigDecimal inq) {
        this.inq = inq;
    }

    public BigDecimal getOtq() {
        return otq;
    }

    public void setOtq(BigDecimal otq) {
        this.otq = otq;
    }

    public BigDecimal getW() {
        return w;
    }

    public void setW(BigDecimal w) {
        this.w = w;
    }

    public BigDecimal getRzfsltdz() {
        return rzfsltdz;
    }

    public void setRzfsltdz(BigDecimal rzfsltdz) {
        this.rzfsltdz = rzfsltdz;
    }

    public BigDecimal getFsltdz() {
        return fsltdz;
    }

    public void setFsltdz(BigDecimal fsltdz) {
        this.fsltdz = fsltdz;
    }
    public BigDecimal getFldcp() {
        return fldcp;
    }

    public void setFldcp(BigDecimal fldcp) {
        this.fldcp = fldcp;
    }

    public Integer getDrna() {
        return drna;
    }

    public void setDrna(Integer drna) {
        this.drna = drna;
    }

    public BigDecimal getUsablew() {
        return usablew;
    }

    public void setUsablew(BigDecimal usablew) {
        this.usablew = usablew;
    }

    public BigDecimal getNyDrp() {
        return nyDrp;
    }

    public void setNyDrp(BigDecimal nyDrp) {
        this.nyDrp = nyDrp;
    }

    public LocalDateTime getHftm() {
        return hftm;
    }

    public void setHftm(LocalDateTime hftm) {
        this.hftm = hftm;
    }

    public BigDecimal getHfinq() {
        return hfinq;
    }

    public void setHfinq(BigDecimal hfinq) {
        this.hfinq = hfinq;
    }

    public String getAvgRain() {
        return avgRain;
    }

    public void setAvgRain(String avgRain) {
        this.avgRain = avgRain;
    }

    public BigDecimal getWaterYield() {
        return waterYield;
    }

    public void setWaterYield(BigDecimal waterYield) {
        this.waterYield = waterYield;
    }

}
