package com.huitu.cloud.api.shyj.evaluation.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.evaluation.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 考核评价接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
public interface ExamDao {
    /**
     * 获取所有考核指标
     * @return
     */
    List<ExamIndex> getExamIndexList();
    /**
     * 根据政区编码、年月获取与省级部门配合得分
     * @return
     */
    List<ExamProvinceScore> getExamProvinceScoreListByAdcd(Map<String, Object> param);
    /**
     * 添加年月省级部门得分
     *
     * @return
     */
    Integer addExamProvinceScoreByAdcd(ExamProvinceScore examProvinceScore);
    /**
     * 修改年月省级部门得分
     *
     * @return
     */
    Integer updateExamProvinceScoreByAdcd(ExamProvinceScore examProvinceScore);
    /**
     * 根据政区编码、年月获取市县得分
     * @return
     */
    List<ExamCityScore> getExamCityScoreListByYm(String month);
    /**
     * 添加年月市县得分
     *
     * @return
     */
    Integer addExamCityScoreByAdcd(ExamCityScore examCityScore);
    /**
     * 修改年月市县得分
     *
     * @return
     */
    Integer updateExamCityScoreByAdcd(ExamCityScore examCityScore);


    boolean saveExamIndex(ExamIndex entity);

    List<ExamIndex> getExamIndexById(ExamIndex entity);

    boolean updateExamIndex(ExamIndex entity);

    boolean deleteExamIndex(@Param("examType") String examType, @Param("examOption") String examOption);

    IPage<ExamIndex> getExamIndexListPage(Page page, @Param("map")Map<String, Object> param);

}
