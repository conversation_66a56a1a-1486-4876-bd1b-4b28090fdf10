<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.hydfcst.mapper.HydfcstDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <!-- 获取河道预报数据 -->
    <select id="getHydfcstStForecastData"
            resultType="com.huitu.cloud.api.ewci.hydfcst.entity.StForecast">
        SELECT
            DISTINCT  f.stcd, f.z, f.q, f.ymdh, bsv.stnm, bsv.rvnm, bsv.hnnm,
                    f.ymdh AS tm_ymdh,
                    bsv.bsnm,
                    bsv.stlc,
                    bsv.sttp,
                    bsv.frgrd,
                    bsv.stazt,
                    bsv.xadcd,
                    bsv.xadnm,
                    srb.ldkel,
                    srb.rdkel,
                      bsv.adcd, bsv.adnm,srb.wrz, srb.wrq, srb.grz, srb.grq,bsv.LGTD, bsv.LTTD, bsv.PLGTD, bsv.PLTTD
        FROM
            st_forecast_f f
                INNER JOIN (
                SELECT MAX ( fymdh ) fymdh FROM st_forecast_f
                        WHERE 1=1
                        <if test="stm != null and stm != ''">
                            AND fymdh &gt;= #{stm}
                        </if>
                        <if test="etm != null and etm != ''">
                            AND fymdh &lt;= #{etm}
                        </if>
                ) ff ON f.fymdh= ff.fymdh
        LEFT JOIN BSN_STBPRP_V bsv ON bsv.stcd = f.stcd
        LEFT JOIN ST_RVFCCH_B srb ON srb.stcd = f.stcd
        <if test="bscd !=null and bscd != ''">
            LEFT JOIN BSN_BAS_ST bbs ON bbs.stcd = f.stcd
            WHERE bbs.bas_code = #{bscd}
        </if>
        ORDER BY
            f.stcd,
            f.ymdh
    </select>

    <!-- 获取水库预报时间过程数据 -->
    <select id="getHydfcstStReglatData"
            resultType="com.huitu.cloud.api.ewci.hydfcst.entity.StReglat">
        SELECT DISTINCT
        f.stcd, f.z as rz, f.w, f.otq, f.ymdh, bsv.stnm, bsv.rvnm, bsv.hnnm, bsv.bsnm, bsv.adcd, bsv.adnm,
        r.fsltdw, srb.normz,srb.actz, srb.dsflz, srb.ckflz, srb.damel, srb.ttcp, r.bgmd,
        r.edmd,

        r.fsltdw,
        f.ymdh AS tm_ymdh,
        sff.q AS inq,
        f.z - srb.normz as rzfsltdz,
        f.z - r.fsltdz as rznormz,
        f.w - r.fsltdw as wfsltdw,

        bsv.stlc,
        bsv.sttp,
        bsv.frgrd,
        bsv.xadcd,
        bsv.xadnm,

        srb.rsvrtp,
        srb.fldcp,
        srb.actcp,

        RS.res_code, RS.res_name,
        r.fsltdz,bsv.LGTD, bsv.LTTD, bsv.PLGTD, bsv.PLTTD
        FROM
        st_reglat_f f
        INNER JOIN (
        SELECT MAX ( fymdh ) fymdh FROM st_forecast_f
        WHERE 1=1
        <if test="stm != null and stm != ''">
            AND fymdh &gt;= #{stm}
        </if>
        <if test="etm != null and etm != ''">
            AND fymdh &lt;= #{etm}
        </if>
        ) ff ON f.fymdh= ff.fymdh
        LEFT JOIN BSN_STBPRP_V bsv ON bsv.stcd = f.stcd
        LEFT JOIN ST_RSVRFCCH_B srb ON srb.stcd = f.stcd
        LEFT JOIN ST_RSVRFSR_B r ON f.stcd = r.stcd
        LEFT JOIN ST_FORECAST_F sff ON sff.stcd=f.stcd AND sff.fymdh=f.fymdh AND sff.ymdh=f.ymdh
        LEFT JOIN BSN_OBJONLY_B O1 ON f.STCD=O1.OBJCD
        LEFT JOIN BSN_OBJONLY_B O2 ON O1.objid=O2.OBJID AND O2.OBJTP=6
        LEFT JOIN ATT_RES_BASE RS ON O2.OBJCD=RS.RES_CODE
        AND RIGHT(CONVERT(VARCHAR(8), f.ymdh, 112), 4) &gt;= r.bgmd
        AND RIGHT(CONVERT(VARCHAR(8), f.ymdh, 112), 4) &lt;= r.edmd
        <if test="bscd !=null and bscd != ''">
            LEFT JOIN BSN_BAS_ST bbs ON bbs.stcd = f.stcd
            WHERE bbs.bas_code = #{bscd}
        </if>
        ORDER BY f.stcd, f.ymdh
    </select>


    <!-- 获取水文预报时间批次  -->
    <select id="listHydfcstBatch" resultType="java.lang.String">
        SELECT DISTINCT
            fymdh
        FROM
            st_forecast_f
        WHERE 1=1
        <if test="stm != null and stm != ''">
            AND fymdh &gt;= #{stm}
        </if>
        <if test="etm != null and etm != ''">
            AND fymdh &lt;= #{etm}
        </if>
        ORDER BY
        fymdh DESC
    </select>

    <!-- 河道预报统计结果  -->
    <select id="listHydFcstRvStat" resultType="com.huitu.cloud.api.ewci.hydfcst.entity.HydFcstRvStat">
        WITH forecast_data AS (
            SELECT
                stcd,
                z,
                q,
        ymdh AS tm_ymdh,
                ymdh,
                ROW_NUMBER() OVER (PARTITION BY stcd ORDER BY q DESC, ymdh) AS row_num
            FROM
                st_forecast_f sff
            WHERE
                fymdh = #{fymdh}
        ),
             first_forecast AS (
                 SELECT stcd, z AS max_z,z, ymdh,q,tm_ymdh
                 FROM forecast_data
                 WHERE row_num = 1
             ),
             river_data AS (
                 SELECT DISTINCT
                     sff.stcd,
                     srr.Z AS init_z
                 FROM
                     ST_FORECAST_F sff
                         LEFT JOIN ST_RIVER_R srr
                                   ON sff.stcd = srr.stcd AND sff.fymdh = srr.tm
                 WHERE
                     sff.fymdh = #{fymdh}
             )
        SELECT
            ff.stcd,
            rd.init_z,
            ff.z,
            ff.q,
        ff.tm_ymdh,
            ff.max_z,
            ff.ymdh,
            bsv.stnm,
            bsv.rvnm,
            bsv.bsnm,
            bsv.hnnm,
            bsv.adcd,
            bsv.adnm,
            bsv.stlc,
            bsv.sttp,
            bsv.frgrd,
            bsv.stazt,
            bsv.xadcd,
            bsv.xadnm,
            srb.wrz,
            srb.wrq,
            srb.grz,
            srb.grq,
            srb.ldkel,
            srb.rdkel,
        bsv.LGTD, bsv.LTTD, bsv.PLGTD, bsv.PLTTD
        FROM
            first_forecast ff
        LEFT JOIN river_data rd ON ff.stcd = rd.stcd
        LEFT JOIN BSN_STBPRP_V bsv ON bsv.stcd = ff.stcd
        LEFT JOIN ST_RVFCCH_B srb ON srb.stcd = ff.stcd
        <if test="bscd !=null and bscd != ''">
            LEFT JOIN BSN_BAS_ST bbs ON bbs.stcd = ff.stcd
            WHERE bbs.bas_code = #{bscd}
        </if>

    </select>

    <!-- 水库预报统计结果  -->
    <select id="listHydFcstRsStat" resultType="com.huitu.cloud.api.ewci.hydfcst.entity.HydFcstRsStat">
        WITH reglat_data AS (
        SELECT
        srf.stcd AS stcd,
        srf.z AS z,
        srf.ymdh AS ymdh,
        srf.w AS w,
        sff.q AS inq,
        srf.otq,
        ROW_NUMBER ( ) OVER ( PARTITION BY srf.stcd ORDER BY srf.z DESC, srf.ymdh ) AS row_num
        FROM
        st_reglat_f srf
        LEFT JOIN ST_FORECAST_F sff ON sff.stcd=srf.stcd AND sff.fymdh=srf.fymdh AND sff.ymdh=srf.ymdh

        WHERE
        srf.fymdh = #{fymdh}
        ),
        first_reglat AS ( SELECT stcd, z AS max_z, ymdh, z AS rz, w AS w , inq,otq FROM reglat_data WHERE row_num = 1 ),

        rsvr_data AS (
        SELECT DISTINCT
        srf.stcd,
        srr.rz AS init_z, srr.tm
        FROM
        st_reglat_f srf
        LEFT JOIN st_rsvr_r srr
        ON srf.stcd = srr.stcd AND srf.fymdh = srr.tm
        WHERE
        srf.fymdh = #{fymdh}
        )
        SELECT
        fr.stcd,
        rd.init_z,
        fr.max_z,
        fr.ymdh,
        bsv.stnm,
        bsv.rvnm,
        bsv.hnnm,
        bsv.bsnm,
        bsv.adcd,
        bsv.adnm,
        bsv.stlc,
        bsv.sttp,
        bsv.frgrd,
        bsv.stazt,
        bsv.xadcd,
        bsv.xadnm,
        r.fsltdz,
        r.fsltdw,
        r.bgmd,
        r.edmd,

        bsv.stlc,
        bsv.sttp,
        bsv.frgrd,
        bsv.stazt,
        bsv.xadcd,
        bsv.xadnm,

        srb.rsvrtp,
        srb.fldcp,
        srb.actcp,

        fr.ymdh AS tm_ymdh,
        fr.rz,
        fr.w,
        fr.inq,
        fr.otq,
        fr.rz - srb.normz AS rzfsltdz,
        fr.rz - r.fsltdz AS rznormz,
        fr.w - r.fsltdw AS wfsltdw,
        RS.res_code, RS.res_name,
        bsv.LGTD, bsv.LTTD, bsv.PLGTD, bsv.PLTTD
        FROM
        first_reglat fr
        LEFT JOIN rsvr_data rd ON fr.stcd = rd.stcd
        LEFT JOIN BSN_STBPRP_V bsv ON bsv.stcd = fr.stcd
        LEFT JOIN ST_RSVRFSR_B r ON fr.stcd = r.stcd
        AND RIGHT(CONVERT(VARCHAR(8), fr.ymdh, 112), 4) &gt;= r.bgmd
        AND RIGHT(CONVERT(VARCHAR(8), fr.ymdh, 112), 4) &lt;= r.edmd
        LEFT JOIN ST_RSVRFCCH_B srb ON srb.stcd = fr.stcd
        LEFT JOIN BSN_OBJONLY_B O1 ON r.STCD=O1.OBJCD
        LEFT JOIN BSN_OBJONLY_B O2 ON O1.objid=O2.OBJID AND O2.OBJTP=6
        LEFT JOIN ATT_RES_BASE RS ON O2.OBJCD=RS.RES_CODE
        <if test="bscd !=null and bscd != ''">
            LEFT JOIN BSN_BAS_ST bbs ON bbs.stcd = fr.stcd
            WHERE bbs.bas_code = #{bscd}
        </if>
    </select>

</mapper>
