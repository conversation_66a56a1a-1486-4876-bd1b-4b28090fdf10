package com.huitu.cloud.api.shyj.stwarnrule.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.shyj.stwarnrule.entity.RainWarnRule;
import com.huitu.cloud.api.shyj.stwarnrule.entity.SwWarnRule;
import com.huitu.cloud.api.shyj.stwarnrule.mapper.StWarnRuleDao;
import com.huitu.cloud.api.shyj.stwarnrule.service.StWarnRuleService;
import com.huitu.cloud.util.AdcdUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 测站预警规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-27
 */
@Service
public class StWarnRuleServiceImpl implements StWarnRuleService {
    @Autowired
    private StWarnRuleDao stWarnRuleDao;


    @Override
    public IPage<RainWarnRule> getRainWarnrule(String adcd, String warnGradeId, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        if(StringUtils.isNotBlank(adcd)){
            param.put("ad", adcd.substring(0, level));
            param.put("adLevel", level);
        }
        param.put("warnGradeId", warnGradeId);
        IPage<RainWarnRule> iPage=stWarnRuleDao.getRainWarnrule(page,param);
        return iPage;
    }

    @Override
    public IPage<SwWarnRule> getSwWarnrule(String adcd, String warnGradeId, int pageNum, int pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        if(StringUtils.isNotBlank(adcd)){
            param.put("ad", adcd.substring(0, level));
            param.put("adLevel", level);
        }
        param.put("warnGradeId", warnGradeId);
        IPage<SwWarnRule> iPage=stWarnRuleDao.getSwWarnrule(page,param);
        return iPage;
    }
}
