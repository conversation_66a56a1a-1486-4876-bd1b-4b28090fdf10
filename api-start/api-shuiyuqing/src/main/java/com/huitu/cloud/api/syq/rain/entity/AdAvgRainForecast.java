package com.huitu.cloud.api.syq.rain.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
@ApiModel(value = "政区面平均雨量信息")
public class AdAvgRainForecast extends Model<AdAvgRainForecast> {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "政区编码")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    private String adnm;

    @ApiModelProperty(value = "上级政区编码")
    private String padcd;

    @ApiModelProperty(value = "政区级别")
    private String adlvl;

    @ApiModelProperty(value = "平均降雨")
    private BigDecimal avgRain;

    @ApiModelProperty(value = "预报平均降雨")
    private BigDecimal avgForecast;

    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getPadcd() {
        return padcd;
    }

    public void setPadcd(String padcd) {
        this.padcd = padcd;
    }

    public String getAdlvl() {
        return adlvl;
    }

    public void setAdlvl(String adlvl) {
        this.adlvl = adlvl;
    }

    public BigDecimal getAvgRain() {
        return avgRain;
    }

    public void setAvgRain(BigDecimal avgRain) {
        this.avgRain = avgRain;
    }

    public BigDecimal getAvgForecast() {
        return avgForecast;
    }

    public void setAvgForecast(BigDecimal avgForecast) {
        this.avgForecast = avgForecast;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }
}
