package com.huitu.cloud.api.shyj.person.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 防汛人员信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
@TableName("PERSON_B")
@ApiModel(value="PersonB对象", description="防汛人员信息表")
public class Person extends Model<Person> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = " 用户ID  ")
    @TableId(value = "PERSONCD", type = IdType.NONE)
    private String personcd;

    @ApiModelProperty(value = "用户姓名 ")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "部门编号 ")
    @TableField("DEPTCD")
    private String deptcd;

    @ApiModelProperty(value = " 部门负责人 ")
    @TableField("ISHEAD")
    private Integer ishead;

    @ApiModelProperty(value = "部门中的职责")
    @TableField("DUTY")
    private String duty;

    @ApiModelProperty(value = " 性别")
    @TableField("SEX")
    private String sex;

    @ApiModelProperty(value = "  所在公司 ")
    @TableField("COMPANY")
    private String company;

    @ApiModelProperty(value = " 担任职务 ")
    @TableField("POSITION")
    private String position;

    @ApiModelProperty(value = "地址 ")
    @TableField("ADRESS")
    private String adress;

    @ApiModelProperty(value = " 手机 ")
    @TableField("MOBILE")
    private String mobile;

    @ApiModelProperty(value = "  固定电话")
    @TableField("OFFICETEL")
    private String officetel;

    @ApiModelProperty(value = " 邮箱 ")
    @TableField("EMAIL")
    private String email;

    @ApiModelProperty(value = "   备注 ")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "  排序 ")
    @TableField("SORTNO")
    private BigDecimal sortno;

    @ApiModelProperty(value = "  值班人员（1：值班人员，0：非值班人员）  ")
    @TableField("TYPE")
    private String type;


    public String getPersoncd() {
        return personcd;
    }

    public void setPersoncd(String personcd) {
        this.personcd = personcd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDeptcd() {
        return deptcd;
    }

    public void setDeptcd(String deptcd) {
        this.deptcd = deptcd;
    }

    public Integer getIshead() {
        return ishead;
    }

    public void setIshead(Integer ishead) {
        this.ishead = ishead;
    }

    public String getDuty() {
        return duty;
    }

    public void setDuty(String duty) {
        this.duty = duty;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getAdress() {
        return adress;
    }

    public void setAdress(String adress) {
        this.adress = adress;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOfficetel() {
        return officetel;
    }

    public void setOfficetel(String officetel) {
        this.officetel = officetel;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getSortno() {
        return sortno;
    }

    public void setSortno(BigDecimal sortno) {
        this.sortno = sortno;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    protected Serializable pkVal() {
        return this.personcd;
    }

    @Override
    public String toString() {
        return "Person{" +
        "personcd=" + personcd +
        ", name=" + name +
        ", deptcd=" + deptcd +
        ", ishead=" + ishead +
        ", duty=" + duty +
        ", sex=" + sex +
        ", company=" + company +
        ", position=" + position +
        ", adress=" + adress +
        ", mobile=" + mobile +
        ", officetel=" + officetel +
        ", email=" + email +
        ", remark=" + remark +
        ", sortno=" + sortno +
        ", type=" + type +
        "}";
    }
}
