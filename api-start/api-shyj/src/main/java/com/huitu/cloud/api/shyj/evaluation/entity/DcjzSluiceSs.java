package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 重点城(集)镇调查评价涉水工程-水闸详情列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@ApiModel(value="DcjzSluiceSs对象", description="重点城(集)镇调查评价涉水工程-水闸详情列表")
public class DcjzSluiceSs extends Model<DcjzSluiceSs> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城集镇名称")
    @TableId(value = "ADNM", type = IdType.NONE)
    private String adnm;

    @ApiModelProperty(value = "水闸名称")
    @TableId(value = "GATE_NAME", type = IdType.NONE)
    private String gateName;

    @ApiModelProperty(value = "水闸类型")
    @TableId(value = "GATE_TYPE", type = IdType.NONE)
        private String gateType;

    @ApiModelProperty(value = "工程规模")
    @TableId(value = "ENG_GCGM", type = IdType.NONE)
    private String engGcgm;

    @ApiModelProperty(value = "闸孔数量")
    @TableId(value = "HOLE_NUM", type = IdType.NONE)
        private String holeNum;

    @ApiModelProperty(value = "闸孔总净宽")
    @TableId(value = "HOLE_WID", type = IdType.NONE)
    private String holeWid;

    @ApiModelProperty(value = "过闸流量")
    @TableId(value = "FL_GATE_FLOW", type = IdType.NONE)
    private String flGateFlow;

    @ApiModelProperty(value = "橡胶坝坝高")
    @TableId(value = "RUB_DAM_HIG", type = IdType.NONE)
    private String rubDamHig;

    @ApiModelProperty(value = "橡胶坝坝长")
    @TableId(value = "RUB_DAM_LEN", type = IdType.NONE)
    private String rubDamLen;

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getGateName() {
        return gateName;
    }

    public void setGateName(String gateName) {
        this.gateName = gateName;
    }

    public String getGateType() {
        return gateType;
    }

    public void setGateType(String gateType) {
        this.gateType = gateType;
    }

    public String getEngGcgm() {
        return engGcgm;
    }

    public void setEngGcgm(String engGcgm) {
        this.engGcgm = engGcgm;
    }

    public String getHoleNum() {
        return holeNum;
    }

    public void setHoleNum(String holeNum) {
        this.holeNum = holeNum;
    }

    public String getHoleWid() {
        return holeWid;
    }

    public void setHoleWid(String holeWid) {
        this.holeWid = holeWid;
    }

    public String getFlGateFlow() {
        return flGateFlow;
    }

    public void setFlGateFlow(String flGateFlow) {
        this.flGateFlow = flGateFlow;
    }

    public String getRubDamHig() {
        return rubDamHig;
    }

    public void setRubDamHig(String rubDamHig) {
        this.rubDamHig = rubDamHig;
    }

    public String getRubDamLen() {
        return rubDamLen;
    }

    public void setRubDamLen(String rubDamLen) {
        this.rubDamLen = rubDamLen;
    }

    @Override
    public String toString() {
        return "DcjzSluiceSs{" +
                "adnm='" + adnm + '\'' +
                ", gateName='" + gateName + '\'' +
                ", gateType='" + gateType + '\'' +
                ", holeNum='" + holeNum + '\'' +
                ", holeWid='" + holeWid + '\'' +
                ", flGateFlow='" + flGateFlow + '\'' +
                ", rubDamHig='" + rubDamHig + '\'' +
                ", rubDamLen='" + rubDamLen + '\'' +
                '}';
    }
}
