package com.huitu.cloud.api.hyst.survey.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 小水电基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-30
 */
@TableName("HYST_ATT_BASE")
@ApiModel(value = "HystAttBase对象", description = "小水电基础信息表")
public class HystAttBase extends Model<HystAttBase> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "水电站统计代码")
    @TableId(value = "HYST_CODE")
    private String hystCode;

    @ApiModelProperty(value = "水电站名称")
    @TableField("HYST_NAME")
    private String hystName;

    @ApiModelProperty(value = "水电站类型，1为小型水电站、2为中型水电站、3为大型水电站")
    @TableField("HYST_TP")
    private String hystTp;

    @ApiModelProperty(value = "政区编码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private BigDecimal lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private BigDecimal lttd;

    @ApiModelProperty(value = "核准生态流量，单位：m³/s")
    @TableField("APP_ECO_FLOW")
    private BigDecimal appEcoFlow;

    public String getHystCode() {
        return hystCode;
    }

    public void setHystCode(String hystCode) {
        this.hystCode = hystCode;
    }

    public String getHystName() {
        return hystName;
    }

    public void setHystName(String hystName) {
        this.hystName = hystName;
    }

    public String getHystTp() {
        return hystTp;
    }

    public void setHystTp(String hystTp) {
        this.hystTp = hystTp;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public BigDecimal getLgtd() {
        return lgtd;
    }

    public void setLgtd(BigDecimal lgtd) {
        this.lgtd = lgtd;
    }

    public BigDecimal getLttd() {
        return lttd;
    }

    public void setLttd(BigDecimal lttd) {
        this.lttd = lttd;
    }

    public BigDecimal getAppEcoFlow() {
        return appEcoFlow;
    }

    public void setAppEcoFlow(BigDecimal appEcoFlow) {
        this.appEcoFlow = appEcoFlow;
    }
}