<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rsvr.mapper.RsvrWaterReportDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>


    <select id="getPage" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrWaterReport">
        SELECT REPORT_ID, REPORT_NAME, REPORT_TM, PUB_UNIT, Y_REPORT_NO, TOTAL_NO, SUB_UNIT, QF_USERID, SH_USERID,
        BJ_USERID, FILE_PATH, FILE_NAME, TS, REPORT_YEAR, STATUS,SEND_TIME
        FROM BNS_RSVRWATER_REPORT
        WHERE REPORT_YEAR = #{map.year}
        <if test="map.reportName !=null and map.reportName !=''">
            AND CHARINDEX(#{map.reportName},REPORT_NAME) >0
        </if>
        ORDER BY Y_REPORT_NO DESC
    </select>

    <select id="getInfoById" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrWaterReport">
        SELECT REPORT_ID, REPORT_NAME, REPORT_TM, PUB_UNIT, Y_REPORT_NO, TOTAL_NO, SUB_UNIT, QF_USERID, SH_USERID,
               BJ_USERID, FILE_PATH, FILE_NAME, TS, REPORT_YEAR, STATUS,SEND_TIME
        FROM BNS_RSVRWATER_REPORT WHERE REPORT_ID = #{reportId}
    </select>

    <select id="getMaxReportNo" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrWaterReport">
        SELECT ISNULL(MAX(Y_REPORT_NO), 0) Y_REPORT_NO, ISNULL(MAX(TOTAL_NO), 0) TOTAL_NO
        FROM BNS_RSVRWATER_REPORT
        WHERE REPORT_YEAR = YEAR (GETDATE())
    </select>

    <select id="getFileById" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrWaterReportFile">
        SELECT REPORT_ID, REPORT_NAME, REPORT_TM, PUB_UNIT, Y_REPORT_NO, TOTAL_NO, SUB_UNIT, QF_USERID, SH_USERID,
               BJ_USERID, FILE_PATH, FILE_NAME, TS, REPORT_YEAR, STATUS,SEND_TIME,
            B.REALNM QF_NAME,
               C.REALNM SH_NAME,
               D.REALNM BJ_NAME
        FROM BNS_RSVRWATER_REPORT A
                 LEFT JOIN BNS_USERINFO_B B ON B.USERID = A.QF_USERID
                 LEFT JOIN BNS_USERINFO_B C ON C.USERID = A.SH_USERID
                 LEFT JOIN BNS_USERINFO_B D ON D.USERID = A.BJ_USERID
        WHERE REPORT_ID = #{reportId}
    </select>

    <select id="getDocument" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrWaterReportDocument">
        SELECT REPORT_ID, FILE_NAME, FILE_PATH
        FROM BNS_RSVRWATER_REPORT
        WHERE REPORT_ID = #{reportId}
    </select>
    <select id="isRepeatFileNo" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(A.REPORT_ID) > 0 THEN 1 ELSE 0 END
        FROM BNS_RSVRWATER_REPORT A
        WHERE A.[STATUS] = '1' AND A.REPORT_ID != #{reportId}
          AND EXISTS(SELECT 8 FROM BNS_RSVRWATER_REPORT WHERE REPORT_ID = #{reportId} AND Y_REPORT_NO = A.Y_REPORT_NO AND TOTAL_NO = A.TOTAL_NO)
    </select>

    <insert id="insert">
        INSERT INTO BNS_RSVRWATER_REPORT (REPORT_ID, REPORT_NAME, REPORT_TM, PUB_UNIT, Y_REPORT_NO, TOTAL_NO, SUB_UNIT,
                                          QF_USERID,
                                          SH_USERID, BJ_USERID, REPORT_YEAR, STATUS)
        VALUES (#{reportId}, #{reportName}, #{reportTm}, #{pubUnit}, #{yReportNo}, #{totalNo}, #{subUnit}, #{qfUserid},
                #{shUserid}, #{bjUserid}, #{reportYear}, #{status})
    </insert>

    <update id="update">
        UPDATE BNS_RSVRWATER_REPORT
        <set>
            <if test="reportName != null and reportName !=''">
                REPORT_NAME = #{reportName},
            </if>
            <if test="pubUnit != null and pubUnit !=''">
                PUB_UNIT = #{pubUnit},
            </if>
            <if test="yReportNo != null and yReportNo !=''">
                Y_REPORT_NO = #{yReportNo},
            </if>
            <if test="totalNo != null and totalNo !=''">
                TOTAL_NO = #{totalNo},
            </if>
            <if test="subUnit != null and subUnit !=''">
                SUB_UNIT = #{subUnit},
            </if>
            <if test="qfUserid != null and qfUserid !=''">
                QF_USERID = #{qfUserid},
            </if>
            <if test="shUserid != null and shUserid !=''">
                SH_USERID = #{shUserid},
            </if>
            <if test="bjUserid != null and bjUserid !=''">
                BJ_USERID = #{bjUserid},
            </if>
        </set>
        WHERE REPORT_ID = #{reportId} AND STATUS = '0'
    </update>

    <update id="updateReportUrl">
        UPDATE BNS_RSVRWATER_REPORT
        SET FILE_PATH = #{filePath},
            FILE_NAME = #{fileName}
        WHERE REPORT_ID = #{reportId}
          AND STATUS = '0'
    </update>

    <update id="updateStatus">
        UPDATE BNS_RSVRWATER_REPORT
        SET STATUS = '1',SEND_TIME = getdate()
        WHERE REPORT_ID = #{reportId}
          AND STATUS = '0'
    </update>
</mapper>
