<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ew.mapper.EwReceiverDao">
    <select id="getSmsReceiverList" resultType="com.huitu.cloud.api.ew.entity.ext.EwSmsReceiver">
        SELECT A.RECEIVER_ID,
               A.MSG_ID,
               A.PHONE_NO,
               A.RECEIVER,
               A.POSITION,
               A.TAG,
               A.REMARK,
               B.SEND_ID,
               B.[STATUS] SEND_STATUS,
               B.SEND_TIME,
               B.ERROR_CODE,
               B.[ERROR_MESSAGE],
               C.[STATUS] READ_STATUS,
               C.CONFIRM_RESULT,
               C.CONFIRM_TIME
        FROM EW_RECEIVER A
                 LEFT JOIN SMS_SENDING B ON B.PHONE_NO = A.PHONE_NO AND
                                            EXISTS(SELECT 8 FROM SMS_MESSAGE WHERE MSG_ID = B.MSG_ID AND BUSINESS_KEY = A.MSG_ID)
                 LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.SMS_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO
        WHERE EXISTS(SELECT 8
                     FROM EW_MESSAGE B
                              INNER JOIN EW_REL_WARNING_MESSAGE C ON B.MSG_ID = C.MSG_ID
                     WHERE B.PUSH_MODE = '1'
                       AND C.WARN_ID = #{warnId}
                       AND B.MSG_ID = A.MSG_ID)
        ORDER BY A.LATEST_TIME ASC
    </select>
    <select id="getXccReceiverList" resultType="com.huitu.cloud.api.ew.entity.ext.EwXccReceiver">
        SELECT A.RECEIVER_ID,
               A.MSG_ID,
               A.PHONE_NO,
               A.RECEIVER,
               A.POSITION,
               A.TAG,
               A.REMARK,
               B.SEND_ID,
               B.EXT_SEND_ID,
               B.[STATUS] SEND_STATUS,
               B.SEND_TIME,
               B.RECEIPT_TIME,
               B.CALL_RESULT,
               B.CALL_RESULT_TEXT,
               B.FAIL_CAUSE
        FROM EW_RECEIVER A
                 LEFT JOIN XCC_SENDING B ON B.EXT_SEND_ID = A.RECEIVER_ID
                 LEFT JOIN EW_CALL_FEEDBACK C ON C.CALL_TYPE = '1' AND C.XCC_WARN_MSG_ID = A.MSG_ID AND C.PHONE_NO = A.PHONE_NO
        WHERE EXISTS(SELECT 8
                     FROM EW_MESSAGE C
                              INNER JOIN EW_REL_WARNING_MESSAGE D ON C.MSG_ID = D.MSG_ID
                     WHERE C.PUSH_MODE = '2'
                       AND D.WARN_ID = #{warnId}
                       AND C.MSG_ID = A.MSG_ID)
          AND (C.[STATUS] = '0' OR (C.[STATUS] = '1' AND C.CONFIRM_RESULT NOT IN ('1', '2')))
        ORDER BY A.LATEST_TIME ASC
    </select>
    <insert id="batchInsert" parameterType="list">
        INSERT INTO EW_RECEIVER (RECEIVER_ID, MSG_ID, PHONE_NO, RECEIVER, POSITION, TAG, REMARK, LATEST_TIME)
        SELECT RECEIVER_ID, MSG_ID, PHONE_NO, RECEIVER, POSITION, TAG, REMARK, LATEST_TIME FROM (VALUES
        <foreach collection="list" item="item" separator=", ">
            (NEWID(), #{item.msgId}, #{item.phoneNo}, #{item.receiver}, #{item.position}, #{item.tag}, #{item.remark}, GETDATE())
        </foreach>) AS S(RECEIVER_ID, MSG_ID, PHONE_NO, RECEIVER, POSITION, TAG, REMARK, LATEST_TIME)
    </insert>
</mapper>