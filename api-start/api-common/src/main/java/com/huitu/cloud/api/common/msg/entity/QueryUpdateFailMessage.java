package com.huitu.cloud.api.common.msg.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <p>
 * 重新发送失败短信参数
 * </p>
 *
 * <AUTHOR>
 * @since 2020-9-7
 */
@ApiModel(value="QueryUpdateFailMessage类", description="重新发送失败短信参数")
public class QueryUpdateFailMessage {
    @ApiModelProperty(value = "失败短信集合 ")
    private List<FailMessage> messages;

    public List<FailMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<FailMessage> messages) {
        this.messages = messages;
    }
}
