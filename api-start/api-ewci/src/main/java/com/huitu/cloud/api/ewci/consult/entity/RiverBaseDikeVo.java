package com.huitu.cloud.api.ewci.consult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 提防统计
 */
@ApiModel(value = "提防统计")
public class RiverBaseDikeVo implements Serializable {

    @ApiModelProperty(value = "政区编码")
    @TableField(value = "ADCD")
    private String adcd;

    @ApiModelProperty(value = "政区名称")
    @TableField(value = "ADNM")
    private String adnm;

    @ApiModelProperty(value = "总长度")
    @TableField(value = "TOTAL_LEN")
    private Double totalLen;

    @ApiModelProperty(value = "已治理沙基")
    @TableField(value = "B_UP_LEN")
    private Double bUpLen;

    @ApiModelProperty(value = "未治理沙基")
    @TableField(value = "B_NOT_LEN")
    private Double bNotLen;

    @ApiModelProperty(value = "已治理沙基沙堤")
    @TableField(value = "BD_UP_LEN")
    private Double bdUpLen;

    @ApiModelProperty(value = "未治理沙基沙堤")
    @TableField(value = "BD_NOT_LEN")
    private Double bdNotLen;

    @ApiModelProperty(value = "河流编码")
    @TableField(value = "RV_CODE")
    private String rvCode;

    @ApiModelProperty(value = "河流名称")
    @TableField(value = "RV_NAME")
    private String rvName;


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public Double getTotalLen() {
        return totalLen;
    }

    public void setTotalLen(Double totalLen) {
        this.totalLen = totalLen;
    }

    public Double getbUpLen() {
        return bUpLen;
    }

    public void setbUpLen(Double bUpLen) {
        this.bUpLen = bUpLen;
    }

    public Double getbNotLen() {
        return bNotLen;
    }

    public void setbNotLen(Double bNotLen) {
        this.bNotLen = bNotLen;
    }

    public Double getBdUpLen() {
        return bdUpLen;
    }

    public void setBdUpLen(Double bdUpLen) {
        this.bdUpLen = bdUpLen;
    }

    public Double getBdNotLen() {
        return bdNotLen;
    }

    public void setBdNotLen(Double bdNotLen) {
        this.bdNotLen = bdNotLen;
    }

    public String getRvCode() {
        return rvCode;
    }

    public void setRvCode(String rvCode) {
        this.rvCode = rvCode;
    }

    public String getRvName() {
        return rvName;
    }

    public void setRvName(String rvName) {
        this.rvName = rvName;
    }
}
