package com.huitu.cloud.api.syq.video.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huitu.cloud.api.syq.constants.SyqConstants;
import com.huitu.cloud.api.syq.video.entity.BsnCcdinfo;
import com.huitu.cloud.api.syq.video.entity.BsnVdstinfo;
import com.huitu.cloud.api.syq.video.entity.VideoCount;
import com.huitu.cloud.api.syq.video.mapper.VideoDao;
import com.huitu.cloud.api.syq.video.service.VideoService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ExcelExportUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("ALL")
@Service
public class VideoServiceImpl implements VideoService {
    @Autowired
    private VideoDao videoDao;

    @Override
    public List<BsnVdstinfo> getVideoInfoByPage(String adcd, String bscd, String name, List<String> types) {
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        Map<String,BsnVdstinfo> vdstinfoMap=new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("bscd", bscd);
        param.put("name", name);
        param.put("types", types);
        List<BsnVdstinfo> resultList=videoDao.getVideoInfo(param);
        List<BsnCcdinfo> equipments=videoDao.getEquipmentInfos();
        for (BsnVdstinfo item:resultList){
            vdstinfoMap.put(item.getVdstCd(),item);
        }
        for (BsnCcdinfo item:equipments){
            if(vdstinfoMap.containsKey(item.getVdstCd())){
                BsnVdstinfo vdstinfo=vdstinfoMap.get(item.getVdstCd());
                List<BsnCcdinfo> list1 = vdstinfo.getEquipments();
                List<BsnCcdinfo> allList=new ArrayList<>();
                if (list1!=null && list1.size()>0){
                    allList.addAll(list1);
                }
                allList.add(item);
                vdstinfo.setEquipments(allList);
            }
        }
        List<BsnVdstinfo> list2=new ArrayList<>();
        for(Map.Entry<String, BsnVdstinfo> entry : vdstinfoMap.entrySet()){
            BsnVdstinfo value = entry.getValue();
            list2.add(value);
        }
        list2.forEach(item->{
            if (item.getVdstTp().equals(SyqConstants.VideoConstants.VIDEOTYPETWELVE)){
                item.setSort(1);
            }else if(item.getVdstTp().equals(SyqConstants.VideoConstants.VIDEOTYPEELEVEN)){
                item.setSort(2);
            }else if(item.getVdstTp().equals(SyqConstants.VideoConstants.VIDEOTYPETHIRTEEN)){
                item.setSort(3);
            }else if(item.getVdstTp().equals(SyqConstants.VideoConstants.VIDEOTYPETWO)){
                item.setSort(4);
            }else if(item.getVdstTp().equals(SyqConstants.VideoConstants.VIDEOTYPETHREE)){
                item.setSort(5);
            }else if(item.getVdstTp().equals(SyqConstants.VideoConstants.VIDEOTYPEFOUR)){
                item.setSort(6);
            }else if(item.getVdstTp().equals(SyqConstants.VideoConstants.VIDEOTYPESIX)){
                item.setSort(7);
            }
        });
        list2.sort(Comparator.comparingInt(BsnVdstinfo::getSort).thenComparing(BsnVdstinfo::getAdcd));
        list2.forEach(item->{
            if (item.getEquipments() != null && item.getEquipments().size() > 0) {
                item.getEquipments().sort(Comparator.comparing(BsnCcdinfo::getCcdNm));
            }
        });
        return list2;
    }

    @Override
    public BsnVdstinfo getVideoInfoById(String id) {
        BsnVdstinfo vdstinfo=videoDao.getVideoInfoById(id);
        List<BsnCcdinfo> equipments=videoDao.getEquipmentInfos();
        for (BsnCcdinfo item:equipments){
            if (vdstinfo.getVdstCd().equals(item.getVdstCd())){
                List<BsnCcdinfo> list1 = vdstinfo.getEquipments();
                List<BsnCcdinfo> allList=new ArrayList<>();
                if (list1!=null && list1.size()>0){
                    allList.addAll(list1);
                }
                allList.add(item);
                vdstinfo.setEquipments(allList);
            }
        }
        return vdstinfo;
    }

    @Override
    public List<BsnVdstinfo> getVideoInfoByCode(String code) {
        // 测站关联的视频站列表
        List<BsnVdstinfo> vdstinfoList=videoDao.getVideoInfoByCode(code);
        if (vdstinfoList != null && vdstinfoList.size() > 0) {
            // 视频站主键列表
            List<String> vdstCdList = vdstinfoList.stream().map(BsnVdstinfo::getVdstCd).collect(Collectors.toList());
            List<BsnCcdinfo> equipments=videoDao.getEquipmentInfosByVdstCds(vdstCdList);
            vdstinfoList.forEach(item->{
                List<BsnCcdinfo> bsnCcdinfos = equipments.stream().filter(i -> i.getVdstCd().equals(item.getVdstCd())).collect(Collectors.toList());
                item.setEquipments(bsnCcdinfos);
            });
        }
        return vdstinfoList;
    }

    @Override
    public VideoCount getVideoCountInfo(String adcd, String bscd, String name, List<String> types) {
        VideoCount count=new VideoCount();
        int level = AdcdUtil.getAdLevel(adcd);
        Map<String, Object> param = new HashMap<>();
        param.put("ad", adcd.substring(0, level));
        param.put("level", level);
        param.put("bscd", bscd);
        param.put("name", name);
        param.put("types", types);
        List<BsnVdstinfo> list=videoDao.getVideoInfo(param);
        count.setCount(list.size());
        List<BsnCcdinfo> equipments=videoDao.getEquipmentInfos();
        List<String> VdstCdList = list.stream().map(BsnVdstinfo::getVdstCd).collect(Collectors.toList());
        long equipmentsCount = equipments.stream().filter(i -> VdstCdList.contains(i.getVdstCd())).count();
        count.setCountItem((int) equipmentsCount);
        Map<String,Long> map = list.stream()
                .collect(Collectors.groupingBy(BsnVdstinfo::getVdstTp,Collectors.counting()));
        if(map.containsKey(SyqConstants.VideoConstants.VIDEOTYPETWELVE)){
            count.setCountTwelve(Integer.parseInt(map.get(SyqConstants.VideoConstants.VIDEOTYPETWELVE).toString()));
        }if(map.containsKey(SyqConstants.VideoConstants.VIDEOTYPEELEVEN)){
            count.setCountEleven(Integer.parseInt(map.get(SyqConstants.VideoConstants.VIDEOTYPEELEVEN).toString()));
        }if(map.containsKey(SyqConstants.VideoConstants.VIDEOTYPETHIRTEEN)){
            count.setCountThirteen(Integer.parseInt(map.get(SyqConstants.VideoConstants.VIDEOTYPETHIRTEEN).toString()));
        }if(map.containsKey(SyqConstants.VideoConstants.VIDEOTYPETWO)){
            count.setCountTwo(Integer.parseInt(map.get(SyqConstants.VideoConstants.VIDEOTYPETWO).toString()));
        }if(map.containsKey(SyqConstants.VideoConstants.VIDEOTYPETHREE)){
            count.setCountThree(Integer.parseInt(map.get(SyqConstants.VideoConstants.VIDEOTYPETHREE).toString()));
        }if(map.containsKey(SyqConstants.VideoConstants.VIDEOTYPEFOUR)){
            count.setCountFour(Integer.parseInt(map.get(SyqConstants.VideoConstants.VIDEOTYPEFOUR).toString()));
        }if(map.containsKey(SyqConstants.VideoConstants.VIDEOTYPEFIVE)){
            count.setCountFive(Integer.parseInt(map.get(SyqConstants.VideoConstants.VIDEOTYPEFIVE).toString()));
        }if(map.containsKey(SyqConstants.VideoConstants.VIDEOTYPESIX)){
            count.setCountSix(Integer.parseInt(map.get(SyqConstants.VideoConstants.VIDEOTYPESIX).toString()));
        }
        return count;
    }

    @Override
    public BsnVdstinfo getVideoInfoByEnnmcd(String ennmcd) {
        BsnVdstinfo vdstinfo=videoDao.getVideoInfoByEnnmcd(ennmcd);
        List<BsnCcdinfo> equipments=videoDao.getEquipmentInfos();
        if(vdstinfo !=null){
            for (BsnCcdinfo item:equipments){
                if (vdstinfo.getVdstCd().equals(item.getVdstCd())){
                    List<BsnCcdinfo> list1 = vdstinfo.getEquipments();
                    List<BsnCcdinfo> allList=new ArrayList<>();
                    if (list1!=null && list1.size()>0){
                        allList.addAll(list1);
                    }
                    allList.add(item);
                    vdstinfo.setEquipments(allList);
                }
            }
        }
        return vdstinfo;
    }

    @Override
    public void exportVideoInfo(String adcd, String bscd, String name, List<String> types) {
        List<BsnVdstinfo> list = getVideoInfoByPage(adcd, bscd, name, types);
        ExcelExportUtil.execute(list,"视频站信息");
    }

    @Override
    public List<BsnCcdinfo> getVideoDetailInfoByStcd(String stcd) {
        return videoDao.getVideoDetailInfoByStcd(stcd);
    }
}
