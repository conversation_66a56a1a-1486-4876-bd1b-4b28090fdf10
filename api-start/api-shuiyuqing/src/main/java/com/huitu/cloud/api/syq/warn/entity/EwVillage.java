package com.huitu.cloud.api.syq.warn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@TableName("EW_VILLAGE")
@ApiModel(value="EwVillage对象", description="")
public class EwVillage extends Model<EwVillage> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "村庄编码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "预报时间")
    @TableField("TM")
    private LocalDateTime tm;

    @ApiModelProperty(value = "预警级别")
    @TableField("LEVELH")
    private Double levelh;

    @ApiModelProperty(value = "预警时间")
    @TableField("WARN_TM")
    private LocalDateTime warnTm;

    @ApiModelProperty(value = "预警值")
    @TableField("V")
    private Double v;

    @ApiModelProperty(value = "预警指标")
    @TableField("WARNV")
    private Double warnv;

    @ApiModelProperty(value = "预警来源")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = "主键编码")
    @TableId(value = "ID", type = IdType.NONE)
    private String id;

    @TableField("STDT")
    private Double stdt;


    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public LocalDateTime getTm() {
        return tm;
    }

    public void setTm(LocalDateTime tm) {
        this.tm = tm;
    }

    public Double getLevelh() {
        return levelh;
    }

    public void setLevelh(Double levelh) {
        this.levelh = levelh;
    }

    public LocalDateTime getWarnTm() {
        return warnTm;
    }

    public void setWarnTm(LocalDateTime warnTm) {
        this.warnTm = warnTm;
    }

    public Double getV() {
        return v;
    }

    public void setV(Double v) {
        this.v = v;
    }

    public Double getWarnv() {
        return warnv;
    }

    public void setWarnv(Double warnv) {
        this.warnv = warnv;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Double getStdt() {
        return stdt;
    }

    public void setStdt(Double stdt) {
        this.stdt = stdt;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "EwVillage{" +
        "adcd=" + adcd +
        ", tm=" + tm +
        ", levelh=" + levelh +
        ", warnTm=" + warnTm +
        ", v=" + v +
        ", warnv=" + warnv +
        ", type=" + type +
        ", id=" + id +
        ", stdt=" + stdt +
        "}";
    }
}
