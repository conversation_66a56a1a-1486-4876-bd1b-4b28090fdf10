package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 历史山洪灾害情况汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
@TableName("IA_C_HSFWATER")
@ApiModel(value="IaCHsfwater对象", description="历史山洪灾害情况汇总表")
public class IaHsfwater extends  RelevantInfo {


    @ApiModelProperty(value = "山洪灾害编码")
    @TableId(value = "MTCD", type = IdType.NONE)
    private String mtcd;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "灾害发生时间")
    @TableField("OTIME")
    private String otime;

    @ApiModelProperty(value = "灾害发生地点")
    @TableField("ADDRESS")
    private String address;

    @ApiModelProperty(value = "经度")
    @TableField("LGTD")
    private Double lgtd;

    @ApiModelProperty(value = "纬度")
    @TableField("LTTD")
    private Double lttd;

    @ApiModelProperty(value = "过程降雨量（mm）")
    @TableField("PFRAIN")
    private Double pfrain;

    @ApiModelProperty(value = "死亡人数（人）")
    @TableField("DPCOUNT")
    private Double dpcount;

    @ApiModelProperty(value = "失踪人数（人）")
    @TableField("MPCOUNT")
    private Double mpcount;

    @ApiModelProperty(value = "损毁房屋（间）")
    @TableField("CHCOUNT")
    private Double chcount;

    @ApiModelProperty(value = "转移人数（人）")
    @TableField("SPCOUNT")
    private Double spcount;

    @ApiModelProperty(value = "直接经济损失（万元）")
    @TableField("ELOSE")
    private Double elose;

    @ApiModelProperty(value = "灾害描述")
    @TableField("DDSCRIB")
    private String ddscrib;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;


    public String getMtcd() {
        return mtcd;
    }

    public void setMtcd(String mtcd) {
        this.mtcd = mtcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getOtime() {
        return otime;
    }

    public void setOtime(String otime) {
        this.otime = otime;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getLgtd() {
        return lgtd;
    }

    public void setLgtd(Double lgtd) {
        this.lgtd = lgtd;
    }

    public Double getLttd() {
        return lttd;
    }

    public void setLttd(Double lttd) {
        this.lttd = lttd;
    }

    public Double getPfrain() {
        return pfrain;
    }

    public void setPfrain(Double pfrain) {
        this.pfrain = pfrain;
    }

    public Double getDpcount() {
        return dpcount;
    }

    public void setDpcount(Double dpcount) {
        this.dpcount = dpcount;
    }

    public Double getMpcount() {
        return mpcount;
    }

    public void setMpcount(Double mpcount) {
        this.mpcount = mpcount;
    }

    public Double getChcount() {
        return chcount;
    }

    public void setChcount(Double chcount) {
        this.chcount = chcount;
    }

    public Double getSpcount() {
        return spcount;
    }

    public void setSpcount(Double spcount) {
        this.spcount = spcount;
    }

    public Double getElose() {
        return elose;
    }

    public void setElose(Double elose) {
        this.elose = elose;
    }

    public String getDdscrib() {
        return ddscrib;
    }

    public void setDdscrib(String ddscrib) {
        this.ddscrib = ddscrib;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }


}
