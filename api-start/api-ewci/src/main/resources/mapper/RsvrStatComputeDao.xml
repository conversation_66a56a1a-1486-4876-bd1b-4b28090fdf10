<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.rsvr.mapper.RsvrStatComputeDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getRsvrStatComputeList" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrStatComputeVo">
        SELECT BO.OBJID, A.STCD, A.STNM, B.ADNM, A.RSVRTP, D.TM,
        CONVERT(DECIMAL(13,2),ISNULL(D.tyAvw,0)) tyAvw,
        CONVERT(DECIMAL(13,2),ISNULL(E.lyAvw,0)) lyAvw,
        CONVERT(DECIMAL(13,2),
        case
        when D.tyAvw IS NULL AND E.lyAvw IS NOT NULL THEN 0 - E.lyAvw
        when D.tyAvw IS NOT NULL AND E.lyAvw IS NULL THEN D.tyAvw - 0
        when D.tyAvw IS NULL AND E.lyAvw IS NULL THEN 0
        else D.tyAvw-E.lyAvw END) lywD,
        CONVERT(DECIMAL(13,2),
        case
        when D.tyAvw IS NULL AND E.lyAvw != 0 THEN (0 - E.lyAvw)/E.lyAvw * 100
        when E.lyAvw = 0 THEN 0
        else (D.tyAvw-E.lyAvw)/E.lyAvw * 100 END) lywR,
        CONVERT(DECIMAL(13,2),ISNULL(F.oyAvw,0)) oyAvw,
        CONVERT(DECIMAL(13,2),
        case
        when D.tyAvw IS NULL AND F.oyAvw IS NOT NULL THEN 0 - E.lyAvw
        when D.tyAvw IS NOT NULL AND F.oyAvw IS NULL THEN D.tyAvw - 0
        when D.tyAvw IS NULL AND F.oyAvw IS NULL THEN 0
        else D.tyAvw-F.oyAvw END) oywD,
        CONVERT(DECIMAL(13,2),
        case
        when D.tyAvw IS NULL AND F.oyAvw != 0 THEN (0 - F.oyAvw)/F.oyAvw * 100
        when F.oyAvw = 0 THEN 0
        else (D.tyAvw-F.oyAvw)/F.oyAvw * 100 END) oywR
        , A.TTCP, A.DDCP, A.ACTCP, A.DDCP+A.ACTCP AS XLTTCP
        , A.ACTZ, A.DDZ, A.NORMZ, A.DSFLZ, A.CKFLZ, A.HHRZ, A.HLRZ
        FROM BSN_HYRSST_B A
        LEFT JOIN BSN_OBJONLY_B BO ON A.STCD = BO.OBJCD
        LEFT JOIN MDT_ADCDINFO_B B ON B.ADCD = LEFT(A.ADCD,6)+'000000000'
        <choose>
            <when test="map.prdtp == '1'.toString()">
                LEFT JOIN (
                SELECT STCD, W AS tyAvw, TM, ROW_NUMBER() OVER (PARTITION BY STCD ORDER BY TM DESC) AS RN FROM ST_RSVR_R S
                WHERE DATEPART(HH,TM)=8 AND TM >= DATEADD(DD,-2,'${map.stm}') AND TM &lt; '${map.etm}'
                AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD)
                ) D ON A.STCD = D.STCD AND D.RN = 1
            </when>
            <otherwise>
                LEFT JOIN (
                SELECT STCD, AVG(W) AS tyAvw, CONVERT(DATETIME,#{map.etm}) AS TM FROM ST_RSVR_R S
                WHERE DATEPART(HH,TM)=8 AND TM >= '${map.stm}' AND TM &lt; '${map.etm}'
                AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD)
                GROUP BY STCD
                ) D ON A.STCD = D.STCD
            </otherwise>
        </choose>
        LEFT JOIN (
        SELECT STCD, AVG(W) lyAvw FROM ST_RSVR_R S
        WHERE DATEPART(HH,TM)=8 AND TM >= DATEADD(YY,-1,'${map.stm}') AND TM &lt; DATEADD(YY,-1,'${map.etm}')
        AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD)
        GROUP BY STCD
        ) E ON A.STCD = E.STCD
        LEFT JOIN (
        SELECT STCD, MYDAVW oyAvw FROM BNS_RSVRMYAV_S S
        WHERE YRTP = '0000' AND PRDTP = '${map.prdtp}' AND MNTH = ${map.mnth}
        <if test="map.prdtp == '1'.toString()">
            AND DAY = DAY('${map.stm}')
        </if>
        <if test="map.prdtp == '2'.toString() or map.prdtp == '5'.toString()">
            AND DAY = '1'
        </if>
        <if test="map.prdtp == '3'.toString()">
            AND DAY = '11'
        </if>
        <if test="map.prdtp == '4'.toString()">
            AND DAY = '21'
        </if>
        AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD)
        ) F ON A.STCD = F.STCD
        WHERE LEFT(A.ADCD, ${map.level}) = LEFT('${map.adcd}', ${map.level})
        AND CHARINDEX(A.RSVRTP, '${map.engScals}') > 0
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.stnm !=null and map.stnm !=''">
            AND CHARINDEX('${map.stnm}',A.STNM) >0
        </if>
        ORDER BY A.RSVRTP DESC, A.ADCD, A.STCD ASC
    </select>

    <select id="getRsvrStatComputeListForSummary" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrStatComputeVo">
        SELECT BO.OBJID, A.STCD, A.STNM, A.RSVRTP, D.TM,
        CONVERT(DECIMAL(13,3),ISNULL(D.tyAvw,0)) tyAvw,
        CONVERT(DECIMAL(13,2),ISNULL(E.lyAvw,0)) lyAvw,
        CONVERT(DECIMAL(13,2),
        case
        when D.tyAvw IS NULL AND E.lyAvw IS NOT NULL THEN 0 - E.lyAvw
        when D.tyAvw IS NOT NULL AND E.lyAvw IS NULL THEN D.tyAvw - 0
        when D.tyAvw IS NULL AND E.lyAvw IS NULL THEN 0
        else D.tyAvw-E.lyAvw END) lywD,
        CONVERT(DECIMAL(13,2),
        case
        when D.tyAvw IS NULL AND E.lyAvw != 0 THEN (0 - E.lyAvw)/E.lyAvw * 100
        when E.lyAvw = 0 THEN 0
        else (D.tyAvw-E.lyAvw)/E.lyAvw * 100 END) lywR,
        CONVERT(DECIMAL(13,2),ISNULL(F.oyAvw,0)) oyAvw,
        CONVERT(DECIMAL(13,2),
        case
        when D.tyAvw IS NULL AND F.oyAvw IS NOT NULL THEN 0 - E.lyAvw
        when D.tyAvw IS NOT NULL AND F.oyAvw IS NULL THEN D.tyAvw - 0
        when D.tyAvw IS NULL AND F.oyAvw IS NULL THEN 0
        else D.tyAvw-F.oyAvw END) oywD,
        CONVERT(DECIMAL(13,2),
        case
        when D.tyAvw IS NULL AND F.oyAvw != 0 THEN (0 - F.oyAvw)/F.oyAvw * 100
        when F.oyAvw = 0 THEN 0
        else (D.tyAvw-F.oyAvw)/F.oyAvw * 100 END) oywR
        , A.TTCP, A.DDCP, A.ACTCP, A.DDCP+A.ACTCP AS XLTTCP
        , A.ACTZ, A.DDZ, A.NORMZ, A.DSFLZ, A.CKFLZ, A.HHRZ, A.HLRZ
        FROM BSN_HYRSST_B A
        LEFT JOIN BSN_OBJONLY_B BO ON A.STCD = BO.OBJCD
        <choose>
            <when test="map.prdtp == '1'.toString()">
                LEFT JOIN (
                SELECT STCD, W AS tyAvw, TM, ROW_NUMBER() OVER (PARTITION BY STCD ORDER BY TM DESC) AS RN FROM ST_RSVR_R S
                WHERE DATEPART(HH,TM)=8 AND TM >= DATEADD(DD,-2,'${map.stm}') AND TM &lt; '${map.etm}'
                AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD)
                ) D ON A.STCD = D.STCD AND D.RN = 1
            </when>
            <otherwise>
                LEFT JOIN (
                SELECT STCD, AVG(W) AS tyAvw, CONVERT(DATETIME,#{map.etm}) AS TM FROM ST_RSVR_R S
                WHERE DATEPART(HH,TM)=8 AND TM >= '${map.stm}' AND TM &lt; '${map.etm}'
                AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD)
                GROUP BY STCD
                ) D ON A.STCD = D.STCD
            </otherwise>
        </choose>
        LEFT JOIN (
        SELECT STCD, AVG(W) lyAvw FROM ST_RSVR_R S
        WHERE DATEPART(HH,TM)=8 AND TM >= DATEADD(YY,-1,'${map.stm}') AND TM &lt; DATEADD(YY,-1,'${map.etm}')
        AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD)
        GROUP BY STCD
        ) E ON A.STCD = E.STCD
        LEFT JOIN (
        SELECT STCD, MYDAVW oyAvw FROM BNS_RSVRMYAV_S S
        WHERE YRTP = '0000' AND PRDTP = '${map.prdtp}' AND MNTH = ${map.mnth}
        <if test="map.prdtp == '1'.toString()">
            AND DAY = ${map.day}
        </if>
        <if test="map.prdtp == '2'.toString() or map.prdtp == '5'.toString()">
            AND DAY = '1'
        </if>
        <if test="map.prdtp == '3'.toString()">
            AND DAY = '11'
        </if>
        <if test="map.prdtp == '4'.toString()">
            AND DAY = '21'
        </if>
        AND EXISTS(SELECT 8 FROM BSN_HYRSST_B WHERE STCD = S.STCD)
        ) F ON A.STCD = F.STCD
        WHERE LEFT(A.ADCD, ${map.level}) = LEFT('${map.adcd}', ${map.level})
        AND CHARINDEX(A.RSVRTP, '${map.engScals}') > 0
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADCD, 6) NOT IN ('220581')
        </if>
        <if test="map.stnm !=null and map.stnm !=''">
            AND CHARINDEX('${map.stnm}',A.STNM) >0
        </if>
    </select>

    <select id="getBigStatComputeByReport" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrStatComputeVo">
        SELECT A.STCD,
               A.STNM,
               ISNULL(CONVERT(DECIMAL(13, 2), B.RZ), 0)    JRZ,
               ISNULL(CONVERT(DECIMAL(13, 2), C.RZ), 0)    QRZ,
               ISNULL(CONVERT(DECIMAL(13, 2), D.tyAvw), 0) tyAvw,
               ISNULL(CONVERT(DECIMAL(13, 2), E.lyAvw), 0) lyAvw,
               ISNULL(CONVERT(
                          DECIMAL(13, 2),
                              CASE
                                  WHEN D.tyAvw IS NULL
                                      AND E.lyAvw IS NOT NULL THEN
                                      0 - E.lyAvw
                                  WHEN D.tyAvw IS NOT NULL
                                      AND E.lyAvw IS NULL THEN
                                      D.tyAvw - 0
                                  WHEN D.tyAvw IS NULL
                                      AND E.lyAvw IS NULL THEN
                                      0
                                  ELSE D.tyAvw - E.lyAvw
                                  END
                          ), 0)                            lywD,
               ISNULL(CONVERT(
                          DECIMAL(13, 2),
                              CASE
                                  WHEN D.tyAvw IS NULL
                                      AND E.lyAvw != 0 THEN (0 - E.lyAvw) / E.lyAvw * 100
                                  WHEN E.lyAvw = 0 THEN
                                      0
                                  ELSE (D.tyAvw - E.lyAvw) / E.lyAvw * 100
                                  END
                          ), 0)                            lywR,
               ISNULL(CONVERT(DECIMAL(13, 2), F.oyAvw), 0) oyAvw,
               ISNULL(CONVERT(
                          DECIMAL(13, 2),
                              CASE
                                  WHEN D.tyAvw IS NULL
                                      AND F.oyAvw IS NOT NULL THEN
                                      0 - E.lyAvw
                                  WHEN D.tyAvw IS NOT NULL
                                      AND F.oyAvw IS NULL THEN
                                      D.tyAvw - 0
                                  WHEN D.tyAvw IS NULL
                                      AND F.oyAvw IS NULL THEN
                                      0
                                  ELSE D.tyAvw - F.oyAvw
                                  END
                          ), 0)                            oywD,
               ISNULL(CONVERT(
                          DECIMAL(13, 2),
                              CASE
                                  WHEN D.tyAvw IS NULL
                                      AND F.oyAvw != 0 THEN (0 - F.oyAvw) / F.oyAvw * 100
                                  WHEN F.oyAvw = 0 THEN
                                      0
                                  ELSE (D.tyAvw - F.oyAvw) / F.oyAvw * 100
                                  END
                          ), 0)                            oywR
        FROM ST_STBPRP_B A
                 LEFT JOIN (SELECT STCD,
                                   SUM(W) / COUNT(STCD) tyAvw
                            FROM ST_RSVR_R
                            WHERE TM >= FORMAT(GETDATE(), 'yyyy-MM-dd 08:00:00')
                              AND TM &lt; FORMAT(DATEADD(dd, 1, GETDATE()), 'yyyy-MM-dd 08:00:00')
                              AND W IS NOT NULL
                            GROUP BY STCD) D ON A.STCD = D.STCD
                 LEFT JOIN (SELECT STCD,
                                   SUM(RZ) / COUNT(STCD) RZ
                            FROM ST_RSVR_R
                            WHERE TM >= FORMAT(GETDATE(), 'yyyy-MM-dd 08:00:00')
                              AND TM &lt; FORMAT(DATEADD(dd, 1, GETDATE()), 'yyyy-MM-dd 08:00:00')
                              AND RZ IS NOT NULL
                            GROUP BY STCD) B ON A.STCD = B.STCD
                 LEFT JOIN (SELECT STCD,
                                   SUM(RZ) / COUNT(STCD) RZ
                            FROM ST_RSVR_R
                            WHERE TM >= FORMAT(DATEADD(yy, - 1, GETDATE()), 'yyyy-MM-dd 08:00:00')
                              AND TM &lt; FORMAT(DATEADD(dd, 1, DATEADD(yy, - 1, GETDATE())), 'yyyy-MM-dd 08:00:00')
                              AND RZ IS NOT NULL
                            GROUP BY STCD) C ON A.STCD = C.STCD
                 LEFT JOIN (SELECT STCD,
                                   MYDAVW lyAvw
                            FROM BNS_RSVRMYAV_S
                            WHERE YRTP = YEAR(DATEADD(YY, - 1, GETDATE()))
                              AND MNTH = MONTH(DATEADD(MONTH, - 1, GETDATE()))
                              AND PRDTP = '1'
                              AND DAY = DAY(GETDATE())) E
                           ON A.STCD = E.STCD
                 LEFT JOIN (SELECT STCD,
                                   MYDAVW oyAvw
                            FROM BNS_RSVRMYAV_S
                            WHERE YRTP = '0000'
                              AND MNTH = MONTH(GETDATE())
                              AND PRDTP = '1'
                              AND DAY = DAY(GETDATE())) F ON A.STCD = F.STCD
        WHERE A.STCD IN (
                         '10800701', '10800800', '10801300', '11601301', '10911000', '10914200', '10912404', '10915800', '10810201',
                         '11002210', '20501500', '11408050', '11405810', '11516700', '10800400', '10805180', '10805100', '10805150',
                         '10901810'
            )
    </select>
    <select id="getMiddleStatComputeByReport"
            resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrStatComputeVo">
        SELECT A.STCD,
               A.STNM,
               CONVERT(DECIMAL(13, 2), ISNULL(D.tyAvw, 0))               tyAvw,
               CONVERT(DECIMAL(13, 2), ISNULL(E.lyAvw, 0))               lyAvw,
               CONVERT(DECIMAL(13, 2),
                       case
                           when D.tyAvw IS NULL AND E.lyAvw IS NOT NULL THEN 0 - E.lyAvw
                           when D.tyAvw IS NOT NULL AND E.lyAvw IS NULL THEN D.tyAvw - 0
                           when D.tyAvw IS NULL AND E.lyAvw IS NULL THEN 0
                           else D.tyAvw - E.lyAvw END)                   lywD,
               CONVERT(DECIMAL(13, 2),
                       case
                           when D.tyAvw IS NULL AND E.lyAvw != 0 THEN (0 - E.lyAvw) / E.lyAvw * 100
                           when E.lyAvw = 0 THEN 0
                           else (D.tyAvw - E.lyAvw) / E.lyAvw * 100 END) lywR,
               CONVERT(DECIMAL(13, 2), ISNULL(F.oyAvw, 0))               oyAvw,
               CONVERT(DECIMAL(13, 2),
                       case
                           when D.tyAvw IS NULL AND F.oyAvw IS NOT NULL THEN 0 - E.lyAvw
                           when D.tyAvw IS NOT NULL AND F.oyAvw IS NULL THEN D.tyAvw - 0
                           when D.tyAvw IS NULL AND F.oyAvw IS NULL THEN 0
                           else D.tyAvw - F.oyAvw END)                   oywD,
               CONVERT(DECIMAL(13, 2),
                       case
                           when D.tyAvw IS NULL AND F.oyAvw != 0 THEN (0 - F.oyAvw) / F.oyAvw * 100
                           when F.oyAvw = 0 THEN 0
                           else (D.tyAvw - F.oyAvw) / F.oyAvw * 100 END) oywR
        FROM ST_STBPRP_B A
                 LEFT JOIN ST_RSVRFCCH_B C ON A.STCD = C.STCD
                 LEFT JOIN (SELECT STCD, SUM(W) / COUNT(STCD) tyAvw
                            FROM ST_RSVR_R
                            WHERE TM >= FORMAT(GETDATE(), 'yyyy-MM-dd 08:00:00')
                              AND TM &lt; FORMAT(DATEADD(dd, 1, GETDATE()), 'yyyy-MM-dd 08:00:00')
                              AND W IS NOT NULL
                            GROUP BY STCD) D ON A.STCD = D.STCD
                 LEFT JOIN (SELECT STCD, MYDAVW lyAvw
                            FROM BNS_RSVRMYAV_S
                            WHERE YRTP = YEAR(DATEADD(YY, -1, GETDATE()))
                              AND MNTH = MONTH(
                                    DATEADD(MONTH, -1, GETDATE()))
                              AND PRDTP = '1'
                              AND DAY = DAY(GETDATE())) E
                           ON A.STCD = E.STCD
                 LEFT JOIN (SELECT STCD, MYDAVW oyAvw
                            FROM BNS_RSVRMYAV_S
                            WHERE YRTP = '0000'
                              AND MNTH = MONTH(
                                    DATEADD(MONTH, -1, GETDATE()))
                              AND PRDTP = '1'
                              AND DAY = DAY(GETDATE())) F ON A.STCD = F.STCD
        WHERE A.STTP IN ('RR', 'RQ')
          AND A.USFL = '1'
          AND FRGRD IN ('1', '2', '3', '9')
          AND CHARINDEX(C.RSVRTP, '3') > 0
          AND LEFT(A.ADDVCD + '000000000', 2) = '22'
    </select>


    <select id="getRsvrInOutStatList" resultType="com.huitu.cloud.api.ewci.rsvr.entity.RsvrInOutStatVo">
        SELECT A.STCD, A.STNM, A.ADDVCD AS ADCD, B.ADNM, C.RSVRTP
        , CONVERT(DECIMAL (13, 2), ISNULL(RR.INW, 0)) AS INW, CONVERT(DECIMAL (13, 2), ISNULL(RR.OTW, 0)) AS OTW
        , RB.IDTM AS BG_IDTM, RB.AVRZ AS BG_AVRZ, RB.AVW AS RB_AVW
        , RE.IDTM AS ED_IDTM, RE.AVRZ AS ED_AVRZ, RE.AVW AS ED_AVW
        , C.TTCP, C.DDCP, C.ACTCP, C.DDCP+C.ACTCP AS XLTTCP
        , C.ACTZ, C.DDZ, C.NORMZ, C.DSFLZ, C.CKFLZ, C.HHRZ, C.HLRZ
        FROM ST_STBPRP_B A
        LEFT JOIN MDT_ADCDINFO_B B ON LEFT(A.ADDVCD+'000000000', 15) = B.ADCD
        LEFT JOIN ST_RSVRFCCH_B C ON A.STCD = C.STCD
        INNER JOIN (SELECT STCD, SUM(AVINQ)*24*3600/1000000 AS INW, SUM(AVOTQ)*24*3600/1000000 AS OTW FROM ST_RSVRAV_R
        WHERE STTDRCD=1 AND IDTM>=#{map.stm} AND IDTM &lt;=#{map.etm} GROUP BY STCD) RR
        ON A.STCD=RR.STCD
        INNER JOIN (SELECT STCD, IDTM, STTDRCD, AVRZ, AVINQ, AVOTQ, AVW
        , ROW_NUMBER() OVER (PARTITION BY STCD ORDER BY IDTM ASC) RN FROM ST_RSVRAV_R
        WHERE STTDRCD=1 AND IDTM>=#{map.stm} AND IDTM &lt;=#{map.etm}) RB
        ON RR.STCD=RB.STCD AND RB.RN=1
        INNER JOIN (SELECT STCD, IDTM, STTDRCD, AVRZ, AVINQ, AVOTQ, AVW
        , ROW_NUMBER() OVER (PARTITION BY STCD ORDER BY IDTM DESC) RN FROM ST_RSVRAV_R
        WHERE STTDRCD=1 AND IDTM>=#{map.stm} AND IDTM &lt;=#{map.etm}) RE
        ON RR.STCD=RE.STCD AND RE.RN=1
        WHERE A.STTP IN ('RR','RQ') AND A.USFL = '1' AND FRGRD IN ('1','2','3','9')
        AND LEFT(A.ADDVCD+'000000000', #{map.level}) = LEFT(#{map.adcd}, #{map.level})
        AND CHARINDEX(C.RSVRTP, #{map.engScals}) > 0
        <if test="map.level == '4'.toString()">
            AND LEFT(A.ADDVCD+'000000000', 6) NOT IN ('220581')
        </if>
        <if test="map.stnm !=null and map.stnm !=''">
            AND CHARINDEX(#{map.stnm},A.STNM) >0
        </if>
        ORDER BY C.RSVRTP DESC, A.ADDVCD, A.STCD
    </select>
    <select id="getStcdList" resultType="java.lang.String">
        WITH SUBQRY(BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE) AS (
        　　SELECT BAS_CODE,BAS_NAME,BAS_LEVEL,PBAS_CODE FROM BSN_BAS_B
        <if test="bscd != null and bscd !=''">
            WHERE BAS_CODE = #{bscd}
        </if>
        <if test="bscd == null or bscd ==''">
            WHERE PBAS_CODE = ''
        </if>
        　　UNION ALL
        　　SELECT BSN_BAS_B.BAS_CODE,BSN_BAS_B.BAS_NAME,BSN_BAS_B.BAS_LEVEL,BSN_BAS_B.PBAS_CODE FROM BSN_BAS_B,SUBQRY
        WHERE BSN_BAS_B.PBAS_CODE = SUBQRY.BAS_CODE
        )
        SELECT DISTINCT B.STCD FROM SUBQRY A INNER JOIN BSN_BAS_ST B
        ON A.BAS_CODE = B.BAS_CODE
    </select>

</mapper>
