package com.huitu.cloud.api.ewci.tencent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huitu.cloud.api.ewci.tencent.entity.PageParams;
import com.huitu.cloud.api.ewci.tencent.entity.VideoConference;
import com.huitu.cloud.api.ewci.tencent.entity.VideoConferenceVo;
import com.huitu.cloud.api.ewci.tencent.entity.VideoPersonRecord;
import com.huitu.cloud.api.ewci.tencent.mapper.VideoConferenceDao;
import com.huitu.cloud.api.ewci.tencent.service.VideoConferenceService;
import com.huitu.cloud.api.ewci.tencent.service.VideoPersonRecordService;
import com.huitu.cloud.util.UUIDFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.IntStream;

/**
 * <p>
 * 音视频会议表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
public class VideoConferenceServiceImpl extends ServiceImpl<VideoConferenceDao, VideoConference> implements VideoConferenceService {

    private VideoPersonRecordService videoPersonRecordService;

    public VideoConferenceServiceImpl(VideoPersonRecordService videoPersonRecordService) {
        this.videoPersonRecordService = videoPersonRecordService;
    }

    @Override
    public void updateVideoConference(VideoConference entity) {
        baseMapper.updateVideoConference(entity);
    }

    @Override
    public Boolean createConference(VideoConference videoConference) {
        System.out.println(videoConference);
        // 保存会议列表BSN_VIDEO_CONFERENCE
        videoConference.setStartTime(LocalDateTime.now());
        videoConference.setStatus(1);
        save(videoConference);
        String[] members = videoConference.getMembers().split(",");
        IntStream.range(0, members.length)
                .forEach(index -> {
                    String userId = members[index];
                    VideoPersonRecord videoPersonRecord = new VideoPersonRecord();
                    videoPersonRecord.setId(UUIDFactory.createUUID());
                    videoPersonRecord.setRoomId(videoConference.getRoomId());
                    videoPersonRecord.setMemberId(userId);
                    VideoPersonRecord extUserInfo = videoPersonRecordService.selectExtInfo(userId);
                    videoPersonRecord.setDeptid(extUserInfo.getDeptid());
                    videoPersonRecord.setDeptnm(extUserInfo.getDeptnm());
                    videoPersonRecord.setAdcd(extUserInfo.getAdcd());
                    videoPersonRecord.setAdnm(extUserInfo.getAdnm());
                    videoPersonRecord.setMemberName(videoConference.getMemberNames().split(",")[index]);
                    videoPersonRecordService.save(videoPersonRecord);
                });

        return true;
    }

    /**
     * 查询会议中的列表
     *
     * @param params - status
     *               - adcd
     *               - pageNum
     *               - pageSize
     *               - userId
     * @return IPage<VideoConferenceVo>
     */
    @Override
    public IPage<VideoConferenceVo> getMeetingList(PageParams params) {
        Page<VideoConference> page = new Page<>(params.getPageNum(), params.getPageSize());
        //  1.上级要能看下级；2.成员自身也要能看到
        IPage<VideoConferenceVo> result = baseMapper.getMeetingList(page, params);
        LocalDateTime now = LocalDateTime.now();
        result.getRecords().stream().forEach(x -> {
            // 计算时长(秒)
            if (x.getStatus() == 0) {
                x.setDuration(0L);
            } else {
                if (x.getEndTime() == null) {
                    x.setDuration(Duration.between(x.getStartTime(), now).getSeconds());
                } else {
                    x.setDuration(Duration.between(x.getStartTime(), x.getEndTime()).getSeconds());
                }
            }
        });
        return result;
    }

    /**
     * 查看会议详情
     *
     * @param roomId 房间号
     * @return VideoConference
     */
    @Override
    public VideoConference getMeetingDetail(String roomId) {
        VideoConference videoConference = baseMapper.selectOne(
                new QueryWrapper<VideoConference>().eq("room_id", roomId)
        );
        List<VideoPersonRecord> personRecords = videoPersonRecordService.selectByRoomId(roomId);
        videoConference.setVideoPersonRecord(personRecords);
        LocalDateTime now = LocalDateTime.now();
        // 计算时长(秒)
        if (videoConference.getStatus() == 0) {
            videoConference.setDuration(0L);
        } else {
            if (videoConference.getEndTime() == null) {
                videoConference.setDuration(Duration.between(videoConference.getStartTime(), now).getSeconds());
            } else {
                videoConference.setDuration(Duration.between(videoConference.getStartTime(), videoConference.getEndTime()).getSeconds());
            }
        }
        return videoConference;
    }
}
