package com.huitu.cloud.api.hsfxt.city.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 简介
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@TableName("Dic_JianJie")
@ApiModel(value="DicJianjie对象", description="简介")
public class DicJianjie extends Model<DicJianjie> {

    @ApiModelProperty(value = "简介编码")
    private Integer jianjieid;

    @ApiModelProperty(value = "风险图类型编码")
    private String fengxiantuid;

    @ApiModelProperty(value = "编制区编码")
    private String bianzhiquid;

    @ApiModelProperty(value = "防洪工程体系")
    private String projectinfo;

    @ApiModelProperty(value = "社会经济指标")
    private String socioeconomic;

    @ApiModelProperty(value = "历史大洪水")
    private String historyflood;

    @ApiModelProperty(value = "洪水分析方案")
    private String floodscheme;

    public Integer getJianjieid() {
        return jianjieid;
    }

    public void setJianjieid(Integer jianjieid) {
        this.jianjieid = jianjieid;
    }

    public String getFengxiantuid() {
        return fengxiantuid;
    }

    public void setFengxiantuid(String fengxiantuid) {
        this.fengxiantuid = fengxiantuid;
    }

    public String getBianzhiquid() {
        return bianzhiquid;
    }

    public void setBianzhiquid(String bianzhiquid) {
        this.bianzhiquid = bianzhiquid;
    }

    public String getProjectinfo() {
        return projectinfo;
    }

    public void setProjectinfo(String projectinfo) {
        this.projectinfo = projectinfo;
    }

    public String getSocioeconomic() {
        return socioeconomic;
    }

    public void setSocioeconomic(String socioeconomic) {
        this.socioeconomic = socioeconomic;
    }

    public String getHistoryflood() {
        return historyflood;
    }

    public void setHistoryflood(String historyflood) {
        this.historyflood = historyflood;
    }

    public String getFloodscheme() {
        return floodscheme;
    }

    public void setFloodscheme(String floodscheme) {
        this.floodscheme = floodscheme;
    }
}
