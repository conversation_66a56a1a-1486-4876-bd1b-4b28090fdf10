package com.huitu.cloud.api.shyj.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 需防洪治理山洪沟基本情况成果表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-17
 */
@TableName("IA_C_GULLY")
@ApiModel(value="IaCGully对象", description="需防洪治理山洪沟基本情况成果表")
public class IaGully  extends  RelevantInfo{

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "山洪沟编码")
    @TableId(value = "GULLYCD", type = IdType.NONE)
    private String gullycd;

    @ApiModelProperty(value = "山洪沟名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "行政区划代码")
    @TableField("ADCD")
    private String adcd;

    @ApiModelProperty(value = "小流域代码")
    @TableField("WSCD")
    private String wscd;

    @ApiModelProperty(value = "集水面积(km2)")
    @TableField("CAREA")
    private Double carea;

    @ApiModelProperty(value = "沟道长度（km）")
    @TableField("CHLENGTH")
    private Double chlength;

    @ApiModelProperty(value = "沟道比降（‰）")
    @TableField("CHPERCENT")
    private Double chpercent;

    @ApiModelProperty(value = "有无设防")
    @TableField("FCATION")
    private String fcation;

    @ApiModelProperty(value = "现状防洪标准")
    @TableField("FSTAND")
    private String fstand;

    @ApiModelProperty(value = "已有堤防防护工程长度（km）")
    @TableField("DIKELEN")
    private Double dikelen;

    @ApiModelProperty(value = "已有护岸防护工程长度（km）")
    @TableField("RTLEN")
    private Double rtlen;

    @ApiModelProperty(value = "受影响乡镇（个）")
    @TableField("TOWNS")
    private Double towns;

    @ApiModelProperty(value = "受影响行政村（个）")
    @TableField("XZC")
    private Double xzc;

    @ApiModelProperty(value = "受影响自然村（个）")
    @TableField("ZRC")
    private Double zrc;

    @ApiModelProperty(value = "影响人口（人）")
    @TableField("PCOUNT")
    private Double pcount;

    @ApiModelProperty(value = "影响耕地（亩）")
    @TableField("LAND")
    private Double land;

    @ApiModelProperty(value = "影响重要公共基础设施（座）")
    @TableField("PFCOUNT")
    private Double pfcount;

    @ApiModelProperty(value = "建国以来山洪发生次数（次）")
    @TableField("FCOUNT")
    private Double fcount;

    @ApiModelProperty(value = "建国以来死亡（失踪）人数（人）")
    @TableField("DCOUNT")
    private Double dcount;

    @ApiModelProperty(value = "治理措施")
    @TableField("CPROGRAM")
    private String cprogram;

    @ApiModelProperty(value = "填写人姓名")
    @TableField("SIGNER")
    private String signer;

    @ApiModelProperty(value = "审核批次号")
    @TableField("AUDID")
    private String audid;

    @ApiModelProperty(value = "审核状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "备注")
    @TableField("COMMENTS")
    private String comments;

    @ApiModelProperty(value = "时间戳")
    @TableField("MODITIME")
    private LocalDateTime moditime;

    @ApiModelProperty(value = "所属一级流域")
    @TableField("BTLVLWS_1")
    private String btlvlws1;

    @ApiModelProperty(value = "所属二级流域")
    @TableField("BTLVLWS_2")
    private String btlvlws2;

    @ApiModelProperty(value = "所属三级流域")
    @TableField("BTLVLWS_3")
    private String btlvlws3;

    @ApiModelProperty(value = "影响户数（户）")
    @TableField("IMPACTHTCOUNT")
    private String impacthtcount;

    @ApiModelProperty(value = "影响房屋（座）")
    @TableField("IMPACTHCOUNT")
    private String impacthcount;

    @ApiModelProperty(value = "防汛责任人")
    @TableField("PICOFC")
    private String picofc;

    @ApiModelProperty(value = "责任人电话")
    @TableField("PICPH")
    private String picph;

    @ApiModelProperty(value = "百年淹没线以下人口（人）")
    @TableField("PIOHTCOUNT")
    private String piohtcount;

    @ApiModelProperty(value = "百年淹没线以下户数（户）")
    @TableField("PIOHCOUNT")
    private String piohcount;

    @ApiModelProperty(value = "百年淹没线以下房屋数（座）")
    @TableField("PIOHCCOUNT")
    private String piohccount;

    @ApiModelProperty(value = "百年淹没线上返2m以下人口（危险区）（人）")
    @TableField("PIOHTCOUNT_2M")
    private String piohtcount2m;

    @ApiModelProperty(value = "百年淹没线上返2m以下户数（危险区）（户）")
    @TableField("PIOHCOUNT_2M")
    private String piohcount2m;

    @ApiModelProperty(value = "百年淹没线上返2m以下房屋数（危险区）（座）")
    @TableField("PIOHCCOUNT_2M")
    private String piohccount2m;

    public String getBtlvlws1() {
        return btlvlws1;
    }

    public void setBtlvlws1(String btlvlws1) {
        this.btlvlws1 = btlvlws1;
    }

    public String getBtlvlws2() {
        return btlvlws2;
    }

    public void setBtlvlws2(String btlvlws2) {
        this.btlvlws2 = btlvlws2;
    }

    public String getBtlvlws3() {
        return btlvlws3;
    }

    public void setBtlvlws3(String btlvlws3) {
        this.btlvlws3 = btlvlws3;
    }

    public String getImpacthtcount() {
        return impacthtcount;
    }

    public void setImpacthtcount(String impacthtcount) {
        this.impacthtcount = impacthtcount;
    }

    public String getImpacthcount() {
        return impacthcount;
    }

    public void setImpacthcount(String impacthcount) {
        this.impacthcount = impacthcount;
    }

    public String getPicofc() {
        return picofc;
    }

    public void setPicofc(String picofc) {
        this.picofc = picofc;
    }

    public String getPicph() {
        return picph;
    }

    public void setPicph(String picph) {
        this.picph = picph;
    }

    public String getPiohtcount() {
        return piohtcount;
    }

    public void setPiohtcount(String piohtcount) {
        this.piohtcount = piohtcount;
    }

    public String getPiohcount() {
        return piohcount;
    }

    public void setPiohcount(String piohcount) {
        this.piohcount = piohcount;
    }

    public String getPiohccount() {
        return piohccount;
    }

    public void setPiohccount(String piohccount) {
        this.piohccount = piohccount;
    }

    public String getPiohtcount2m() {
        return piohtcount2m;
    }

    public void setPiohtcount2m(String piohtcount2m) {
        this.piohtcount2m = piohtcount2m;
    }

    public String getPiohcount2m() {
        return piohcount2m;
    }

    public void setPiohcount2m(String piohcount2m) {
        this.piohcount2m = piohcount2m;
    }

    public String getPiohccount2m() {
        return piohccount2m;
    }

    public void setPiohccount2m(String piohccount2m) {
        this.piohccount2m = piohccount2m;
    }

    public String getGullycd() {
        return gullycd;
    }

    public void setGullycd(String gullycd) {
        this.gullycd = gullycd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public String getWscd() {
        return wscd;
    }

    public void setWscd(String wscd) {
        this.wscd = wscd;
    }

    public Double getCarea() {
        return carea;
    }

    public void setCarea(Double carea) {
        this.carea = carea;
    }

    public Double getChlength() {
        return chlength;
    }

    public void setChlength(Double chlength) {
        this.chlength = chlength;
    }

    public Double getChpercent() {
        return chpercent;
    }

    public void setChpercent(Double chpercent) {
        this.chpercent = chpercent;
    }

    public String getFcation() {
        return fcation;
    }

    public void setFcation(String fcation) {
        this.fcation = fcation;
    }

    public String getFstand() {
        return fstand;
    }

    public void setFstand(String fstand) {
        this.fstand = fstand;
    }

    public Double getDikelen() {
        return dikelen;
    }

    public void setDikelen(Double dikelen) {
        this.dikelen = dikelen;
    }

    public Double getRtlen() {
        return rtlen;
    }

    public void setRtlen(Double rtlen) {
        this.rtlen = rtlen;
    }

    public Double getTowns() {
        return towns;
    }

    public void setTowns(Double towns) {
        this.towns = towns;
    }

    public Double getXzc() {
        return xzc;
    }

    public void setXzc(Double xzc) {
        this.xzc = xzc;
    }

    public Double getZrc() {
        return zrc;
    }

    public void setZrc(Double zrc) {
        this.zrc = zrc;
    }

    public Double getPcount() {
        return pcount;
    }

    public void setPcount(Double pcount) {
        this.pcount = pcount;
    }

    public Double getLand() {
        return land;
    }

    public void setLand(Double land) {
        this.land = land;
    }

    public Double getPfcount() {
        return pfcount;
    }

    public void setPfcount(Double pfcount) {
        this.pfcount = pfcount;
    }

    public Double getFcount() {
        return fcount;
    }

    public void setFcount(Double fcount) {
        this.fcount = fcount;
    }

    public Double getDcount() {
        return dcount;
    }

    public void setDcount(Double dcount) {
        this.dcount = dcount;
    }

    public String getCprogram() {
        return cprogram;
    }

    public void setCprogram(String cprogram) {
        this.cprogram = cprogram;
    }

    public String getSigner() {
        return signer;
    }

    public void setSigner(String signer) {
        this.signer = signer;
    }

    public String getAudid() {
        return audid;
    }

    public void setAudid(String audid) {
        this.audid = audid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getModitime() {
        return moditime;
    }

    public void setModitime(LocalDateTime moditime) {
        this.moditime = moditime;
    }


}
