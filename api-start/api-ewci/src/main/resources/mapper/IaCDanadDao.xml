<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.monitor.mapper.IaCDanadDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <select id="getDanadList" resultType="com.huitu.cloud.api.ewci.monitor.entity.IaCDanad">
        SELECT P.DAND, P.NAME, P.WSCD, STUFF((SELECT ',' + WSNM FROM IA_C_WATA WHERE WSCD IN (SELECT FIELD FROM
        dbo.fnSplitString(P.WSCD,',')) FOR XML PATH('')), 1, 1, '') WSNM, P.ADCD, ISNULL(B.<PERSON>NM, C.ADNM) ADNM,
        <PERSON><PERSON>UN<PERSON>, P.ETCOUNT, P.ECOUNT1, <PERSON><PERSON>T2, <PERSON><PERSON>ECOUNT3, <PERSON>.ECOUNT4, <PERSON><PERSON>, P.HCOUNT1, P.HCOUNT2, P.HCOUNT3,
        P.HCOUNT4,P.STATUS
        FROM IA_C_DANAD P
        LEFT JOIN IA_C_ADINFO B ON B.ADCD = P.ADCD
        LEFT JOIN MDT_ADCDINFO_B C ON C.ADCD = P.ADCD
        <where>
            <choose>
                <when test="map.type != null and map.type != ''">
                    <choose>
                        <when test='"ws".equalsIgnoreCase(map.type)'>
                            AND P.ADCD IN (SELECT DISTINCT A.ADCD FROM IA_C_WSADCD A INNER JOIN IA_C_WSADCD B ON B.WSCD
                            = A.WSCD WHERE A.ADCD != B.ADCD AND B.ADCD = #{map.adcd})
                        </when>
                        <otherwise>
                            AND LEFT(P.ADCD, 12) = LEFT(#{map.adcd}, 12) AND P.ADCD != #{map.adcd}
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    AND P.ADCD = #{map.adcd}
                </otherwise>
            </choose>
        </where>
        ORDER BY P.ADCD ASC
    </select>
</mapper>
