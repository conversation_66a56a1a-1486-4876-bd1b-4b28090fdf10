package com.huitu.cloud.api.screen.fhjz.service;

import com.huitu.cloud.api.screen.fhjz.entity.DikeVo;
import com.huitu.cloud.api.screen.fhjz.entity.RiverDetailVo;
import com.huitu.cloud.api.screen.fhjz.entity.RiverVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 河流 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-9-15
 */
public interface RiverScreenService {

    /**
     * 查询河流列表
     * @return
     */
    List<RiverVo> queryRiverList(String rvName);

    /**
     * 河流防洪工程详情信息查询
     *
     * @param rvName
     * @return
     */
    List<RiverDetailVo> riverDetailList(String rvName);
    /**
     * 查询河流测站列表
     * @param rvCode
     * @param stm
     * @param etm
     * @return
     */
    List<Map> riverStationList(String rvCode,String stm,String etm);

    /**
     * 根据河道站测站编码查询测站信息
     *
     * @param stcd
     * @return
     */
    Map<String, Object> queryRiverStationInfo(String stcd);

    /**
     * 根据河流编码查询河流信息
     *
     * @param rvCode
     * @return
     */
    Map<String, Object> queryRiverInfo(String rvCode);


    /**
     * 查询工程数量统计
     *
     * @param adcd
     * @return
     */
    String queryProjectTotal(String adcd);

    /**
     * 统计河流数量详情
     *
     * @return
     */
    String statisticRiverDetail();
}
