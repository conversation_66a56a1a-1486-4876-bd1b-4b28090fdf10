<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.datamanager.ststop.mapper.StstopDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huitu.cloud.api.datamanager.ststop.entity.BsnStstopr">
        <id column="STCD" property="stcd" />
        <result column="TM" property="tm" />
        <result column="STSTA" property="ststa" />
        <result column="USERNM" property="usernm" />
        <result column="STSTTP" property="ststtp" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        STCD, TM, STSTA, USERNM, STSTTP
    </sql>
    <update id="colseStStop">
        update BSN_STSTOP_R set STSTA='0' where stcd=#{stcd}  AND STSTA='1' and tbnm in(select tbnm from BSN_STSTOPTAB_B where type=#{tbnm})
    </update>
    <update id="colseALLStStop">
        update BSN_STSTOP_R set STSTA='0' where stcd=#{stcd} and TM &lt;=GETDATE() AND STSTA='1'
    </update>
    <delete id="deleteFactor">
        DELETE BSN_STSTOPEX_R WHERE STCD=#{stcd}
    </delete>
    <delete id="deleteStcdFactor">
        DELETE BSN_STSTOPEX_R WHERE STCD=#{stcd} AND FACTOR=#{tbnm}
    </delete>
    <update id="updateEtm">
         update BSN_STSTOP_R set TM=GETDATE()   where stcd=#{stcd} and CONVERT(datetime,tm,120)=CONVERT(datetime,#{etm},120)
    </update>
    <update id="updateEtmByTbnm">
        update BSN_STSTOP_R set TM=GETDATE()   where stcd=#{stcd} and CONVERT(datetime,tm,120)=CONVERT(datetime,#{etm},120) and tbnm=#{tbnm}
    </update>
    <update id="updateStType">
        update BSN_STADTP_B set STADTP=#{type}, UPDATETM= GETDATE() where stcd=#{stcd}
    </update>
    <select id="getInfoByParam" resultType="com.huitu.cloud.api.datamanager.ststop.entity.StstopVo" useCache="false">
        select distinct STCD,STNM,ADNM,STADTPNM,ADNM XADNM,OFFICER,MPHONE,STM, STSTA, USERNM, STSTTP,TBNM,ETM, IS_SHOW, REASON FROM (
        SELECT A.STCD,A.STNM,C.ADNM,D.STADTPNM,B.ADNM XADNM,e.OFFICER,e.MPHONE,TM STM, STSTA, USERNM, STSTTP,tb.type tbnm, aap.IS_SHOW, aap.reason,
        ( SELECT top 1 TM from BSN_STSTOP_R BB WHERE BB.stcd=aa.stcd and bb.tm>aa.tm and aa.STSTTP='1' and aa.tbnm=bb.tbnm  order by bb.tm)
        ETM
        FROM BSN_STSTOP_R aa inner join st_stbprp_b a on a.stcd=aa.stcd
        inner join BSN_STSTOPEX_R aap on (aap.stcd = a.STCD AND aap.factor = aa.TBNM)

        inner join bsn_stadtp_b st on st.stcd=a.stcd
        LEFT JOIN bsn_adcd_b b on a.addvcd+'000000000'=b.adcd
        LEFT JOIN bsn_adcd_b C ON ST.ADCD=C.ADCD
        LEFT JOIN bsn_adtpinfo_b D ON ST.STADTP=D.STADTP
        left join ST_STSMTASK_B e on a.STCD=e.STCD
        left join BSN_STSTOPTAB_B tb on aa.tbnm=tb.tbnm
        where 1=1 AND STSTA='1'
        <if test="isShow!= null and isShow != ''">
            AND aap.IS_SHOW = #{isShow}
        </if>
         <if test="isCurrent">
             AND TM &lt;=GETDATE()
         </if>
        <if test="ad!= null and ad != ''">
            and left(a.addvcd,#{level})=#{ad}
        </if>
        <if test="level!= null and level == '4'.toString()">
            and (a.addvcd !='220581' and a.addvcd !='220381')
        </if>
        ) T WHERE ISNULL(ETM,GETDATE()+1)>GETDATE()
        order by STM desc
    </select>
    <select id="getOneStstop" resultType="com.huitu.cloud.api.datamanager.ststop.entity.BsnStstoprEx">
        select STCD, TM, STSTA, USERNM, STSTTP,tbnm,ETM FROM (
        select STCD, TM, STSTA, USERNM, STSTTP,aa.tbnm,
        ( SELECT top 1 TM from BSN_STSTOP_R BB WHERE BB.stcd=aa.stcd and bb.tm>aa.tm and aa.STSTTP='1' and aa.tbnm=bb.tbnm )
        ETM
        from BSN_STSTOP_R aa
        left join BSN_STSTOPTAB_B tb on tb.tbnm=aa.TBNM
        where TM &lt;=GETDATE() AND STSTA='1' and stcd=#{stcd}
        <if test="tbnm !=null and tbnm !=''">
         and tb.type=#{tbnm}
        </if>
        )T WHERE  ISNULL(ETM,GETDATE()+1)>GETDATE()
    </select>
    <select id="getCountByStcdAndTbnm" resultType="java.lang.Integer">
         select count(*) FROM (
        SELECT Aa.STCD,TM STM, STSTA, USERNM, STSTTP,aa.TBNM,
        ( SELECT top 1 TM from BSN_STSTOP_R BB WHERE BB.stcd=aa.stcd
		  and bb.tm>aa.tm and aa.STSTTP='1' and aa.tbnm=bb.tbnm  order by bb.tm
		  ) ETM
        FROM BSN_STSTOP_R aa
        left join BSN_STSTOPTAB_B tb on tb.tbnm=aa.tbnm
        where 1=1  AND STSTA='1'
		 and stcd=#{stcd} and tb.type=#{tbnm}

        ) T WHERE ISNULL(ETM,GETDATE()+1)>GETDATE()
    </select>
    <select id="getTabByType" resultType="com.huitu.cloud.api.datamanager.ststop.entity.BsnStStopTab">
        SELECT tbnm
      ,tbznm
      ,type
          FROM BSN_STSTOPTAB_B where CHARINDEX(type,#{type})>0
    </select>
    <select id="getStType" resultType="java.lang.String">
        select STADTP from BSN_STBPRP_V where stcd=#{stcd}
    </select>
    <insert id="insertBsnStstopexr">
        INSERT INTO DBETL.dbo.BSN_STSTOPEX_R
            (STCD, IS_SHOW, REASON,FACTOR)
        VALUES(#{stcd}, #{isShow}, #{reason} ,#{factor});
    </insert>
</mapper>
