package com.huitu.cloud.api.shyj.warn.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@ApiModel(value="村(测站)预警关联信息对象", description="村(测站)预警关联信息对象")
public class StwarnRelatedInfo extends StwarnrecordR{
    @ApiModelProperty(value = "测站名称")
    private String stnm;
    @ApiModelProperty(value = "政区名称")
    private String adnm;
    @ApiModelProperty(value = "测站经度")
    private String stLgtd;
    @ApiModelProperty(value = "测站纬度")
    private String stLttd;
    @ApiModelProperty(value = "政区经度")
    private String adLgtd;
    @ApiModelProperty(value = "政区纬度")
    private String adLttd;
    @ApiModelProperty(value = "测站类型")
    private String sttp;

    public String getSttp() {
        return sttp;
    }
    public void setSttp(String sttp) {
        this.sttp = sttp;
    }
    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getAdnm() {
        return adnm;
    }

    public void setAdnm(String adnm) {
        this.adnm = adnm;
    }

    public String getStLgtd() {
        return stLgtd;
    }

    public void setStLgtd(String stLgtd) {
        this.stLgtd = stLgtd;
    }

    public String getStLttd() {
        return stLttd;
    }

    public void setStLttd(String stLttd) {
        this.stLttd = stLttd;
    }

    public String getAdLgtd() {
        return adLgtd;
    }

    public void setAdLgtd(String adLgtd) {
        this.adLgtd = adLgtd;
    }

    public String getAdLttd() {
        return adLttd;
    }

    public void setAdLttd(String adLttd) {
        this.adLttd = adLttd;
    }
}
