package com.huitu.cloud.api.syq.river.entity.export;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * <p>
 * 单站水位流量过程信息导出
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-02
 */
@ApiModel(value="ExportQueryOneRiver对象", description="单站水位流量过程信息导出")
public class ExportQueryOneRiver {
    @ApiModelProperty(value = "开始时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String stm;
    @ApiModelProperty(value = "结束时间 格式yyyy-MM-dd HH:mm:ss",required = true)
    private String etm;
    @ApiModelProperty(value = "测站编码",required = true)
    private String stcd;

    public String getStm() {
        return stm;
    }

    public void setStm(String stm) {
        this.stm = stm;
    }

    public String getEtm() {
        return etm;
    }

    public void setEtm(String etm) {
        this.etm = etm;
    }

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }
}
