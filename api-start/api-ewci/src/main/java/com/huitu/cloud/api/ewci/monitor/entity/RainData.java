package com.huitu.cloud.api.ewci.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 降雨数据
 *
 * <AUTHOR>
 */
@ApiModel(value = "降雨数据")
public class RainData implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测站编码")
    @TableField(value = "STCD")
    private String stcd;

    @ApiModelProperty(value = "测站名称")
    @TableField(value = "STNM")
    private String stnm;

    @ApiModelProperty(value = "测站地址")
    @TableField(value = "STLC")
    private String stlc;

    @ApiModelProperty(value = "降雨量")
    @TableField(value = "ACCP")
    private Double accp;

    @ApiModelProperty(value = "未来1小时降雨")
    @TableField(value = "RAINFALL1")
    private Double rainfall1;

    @ApiModelProperty(value = "未来3小时降雨")
    @TableField(value = "RAINFALL3")
    private Double rainfall3;

    @ApiModelProperty(value = "未来6小时降雨")
    @TableField(value = "RAINFALL6")
    private Double rainfall6;

    public String getStcd() {
        return stcd;
    }

    public void setStcd(String stcd) {
        this.stcd = stcd;
    }

    public String getStnm() {
        return stnm;
    }

    public void setStnm(String stnm) {
        this.stnm = stnm;
    }

    public String getStlc() {
        return stlc;
    }

    public void setStlc(String stlc) {
        this.stlc = stlc;
    }

    public Double getAccp() {
        return accp;
    }

    public void setAccp(Double accp) {
        this.accp = accp;
    }

    public Double getRainfall1() {
        return rainfall1;
    }

    public void setRainfall1(Double rainfall1) {
        this.rainfall1 = rainfall1;
    }

    public Double getRainfall3() {
        return rainfall3;
    }

    public void setRainfall3(Double rainfall3) {
        this.rainfall3 = rainfall3;
    }

    public Double getRainfall6() {
        return rainfall6;
    }

    public void setRainfall6(Double rainfall6) {
        this.rainfall6 = rainfall6;
    }
}
