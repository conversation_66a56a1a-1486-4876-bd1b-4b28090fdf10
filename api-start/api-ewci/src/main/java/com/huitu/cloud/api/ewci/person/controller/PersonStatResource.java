package com.huitu.cloud.api.ewci.person.controller;

import com.huitu.cloud.api.AbstractApiResource;
import com.huitu.cloud.api.ApiResource;
import com.huitu.cloud.api.SuccessResponse;
import com.huitu.cloud.api.ewci.person.entity.PersonAlterStatVo;
import com.huitu.cloud.api.ewci.person.entity.PersonStatAdcdInfo;
import com.huitu.cloud.api.ewci.person.entity.PersonStatVo;
import com.huitu.cloud.api.ewci.person.service.PersonStatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 责任人统计
 *
 * <AUTHOR>
 */
@Api(tags = "责任人统计")
@RestController
@RequestMapping("/api/ewci/person/stat")
public class PersonStatResource extends AbstractApiResource implements ApiResource {

    private final PersonStatService baseService;

    public PersonStatResource(PersonStatService baseService) {
        this.baseService = baseService;
    }

    @Override
    public String getUuid() {
        return "cc9aebfa-bf2f-824f-4347-7f6121057396";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @ApiOperation(value = "责任人统计数据", notes = "赵英捷")
    @GetMapping(value = "get-per-stat-list/{adcd}")

    public ResponseEntity<SuccessResponse<List<PersonStatVo>>> getPersonStatList(@PathVariable("adcd") String adcd) throws Exception {
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", baseService.getPersonStatList(adcd)));
    }

    @ApiOperation(value = "责任人统计政区表头", notes = "赵英捷")
    @GetMapping(value = "get-adcd-info-list/{adcd}")

    public ResponseEntity<SuccessResponse<List<PersonStatAdcdInfo>>> getAdcdInfoList(@PathVariable("adcd") String adcd) throws Exception {
        return ResponseEntity.ok(
                new SuccessResponse(this, "OK", baseService.getAdcdInfoList(adcd)));
    }

    @ApiOperation(value = "责任人变更统计", notes = "赵英捷")
    @GetMapping(value = "get-alter-stat-list/{adcd}")

    public ResponseEntity<SuccessResponse<List<PersonAlterStatVo>>> getPersonAlterStatList(@PathVariable("adcd") String adcd) throws Exception {
        return ResponseEntity.ok(new SuccessResponse(this, "OK", baseService.getPersonAlterStatList(adcd)));
    }
}
