<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huitu.cloud.api.ewci.base.mapper.BnsDfKnowledgeDao">
    <cache type="com.huitu.cloud.config.RedisCache"/>

    <insert id="insert" parameterType="com.huitu.cloud.api.ewci.base.entity.BnsDfKnowledge">
        INSERT INTO BNS_DF_KNOWLEDGE (TITLE, CONTENT, STATUS)
        VALUES (#{title}, #{content}, #{status})
    </insert>
    <update id="update">
        UPDATE BNS_DF_KNOWLEDGE
        <set>
            <if test="title != null and title !=''">
                TITLE = #{title},
            </if>
            <if test="content != null and content !=''">
                CONTENT = #{content},
            </if>
            <if test="status != null and status !=''">
                STATUS = #{status},
            </if>
        </set>
        WHERE ID = #{id}
    </update>
    <delete id="delete">
        delete
        from BNS_DF_KNOWLEDGE
        where ID = #{id}
    </delete>
    <select id="getList" resultType="com.huitu.cloud.api.ewci.base.entity.BnsDfKnowledge">
        SELECT ID,TITLE,CONTENT,TS,STATUS FROM BNS_DF_KNOWLEDGE
        <where>
            <if test="map.stm != null and map.stm !=''">
                and TS>CONVERT(datetime,#{map.stm})
            </if>
            <if test="map.etm != null and map.etm !=''">
                and TS &lt;= CONVERT(datetime,#{map.etm})
            </if>
            <if test="map.title !=null and map.title !=''">
                AND CHARINDEX(#{map.title},TITLE) >0
            </if>
            <if test="map.id !=null and map.id !=''">
                AND ID = #{map.id}
            </if>
        </where>
        ORDER BY TS DESC
    </select>

    <select id="getKnowledgeList" resultType="com.huitu.cloud.api.ewci.base.entity.BnsDfKnowledge">
        SELECT
            ID,
            TITLE,
            CONTENT,
            TS,
            STATUS
        FROM
            BNS_DF_KNOWLEDGE
        WHERE STATUS = 1
        ORDER BY TS DESC
    </select>

</mapper>