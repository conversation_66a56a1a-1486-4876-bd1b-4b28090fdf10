package com.huitu.cloud.api.ewci.monitor.service.impl;

import com.huitu.cloud.api.ewci.feign.OssFeign;
import com.huitu.cloud.api.ewci.monitor.entity.*;
import com.huitu.cloud.api.ewci.monitor.mapper.LiveDao;
import com.huitu.cloud.api.ewci.monitor.service.LiveService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ListUtils;
import feign.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblBorders;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 实况信息实现类
 *
 * <AUTHOR>
 */
@Service
public class LiveServiceImpl implements LiveService {

    private LiveDao baseDao;

    @Autowired
    private OssFeign ossFeign;

    @Autowired
    public void setBaseDao(LiveDao baseDao) {
        this.baseDao = baseDao;
    }

    @Override
    public List<LiveRain> getRainList(LiveQuery query) {
        Map<String, Object> params = query.toQueryParam();
        params.put("fstm", query.getEtm());
        params.put("fetm3", DateUtils.addHours(query.getEtm(), 3));
        params.put("fetm24", DateUtils.addHours(query.getEtm(), 24));
        return baseDao.getRainList(params);
    }

    @Override
    public List<IaCPrevad> getPrevadList(List<String> list) {
        if (list.size() > 1000) {
            List<List<String>> lists = ListUtils.splitList(list, 600);
            List<IaCPrevad> retList = new ArrayList<>();
            lists.forEach(itemList -> retList.addAll(baseDao.getPrevadList(itemList)));
            return retList;
        } else {
            return baseDao.getPrevadList(list);
        }
    }

    @Override
    public List<IaCDanad> getDanadList(List<String> list) {
        if (list.size() > 1000) {
            List<List<String>> lists = ListUtils.splitList(list, 600);
            List<IaCDanad> retList = new ArrayList<>();
            lists.forEach(itemList -> retList.addAll(baseDao.getDanadList(itemList)));
            return retList;
        } else {
            return baseDao.getDanadList(list);
        }
    }

    @Override
    public List<FloodPerson> getFloodPersonList(List<String> list) {
        List<FloodPerson> ret;
        if (list.size() > 1000) {
            ret = new ArrayList<>();
            List<List<String>> lists = ListUtils.splitList(list, 600);
            lists.forEach(itemList -> ret.addAll(baseDao.getFloodPersonList(itemList)));
        } else {
            ret = baseDao.getFloodPersonList(list);
        }
        //获取自然村的第一行数据
        Map<String, FloodPerson> perMap = new HashMap<>();
        for (FloodPerson x : ret) {
            String key = x.getAdcd() + x.getZrc();
            if (perMap.containsKey(key)) {
                FloodPerson firstPer = perMap.get(key);
                if (x.getXsRealnm() == null) {
                    x.setXsRealnm(firstPer.getXsRealnm());
                }
                if (x.getXsMobile() == null) {
                    x.setXsMobile(firstPer.getXsMobile());
                }
                if (x.getXsDuty() == null) {
                    x.setXsDuty(firstPer.getXsDuty());
                }
                if (x.getXzRealnm() == null) {
                    x.setXzRealnm(firstPer.getXzRealnm());
                }
                if (x.getXzMobile() == null) {
                    x.setXzMobile(firstPer.getXzMobile());
                }
                if (x.getXzDuty() == null) {
                    x.setXzDuty(firstPer.getXzDuty());
                }
                if (x.getXzcRealnm() == null) {
                    x.setXzcRealnm(firstPer.getXzcRealnm());
                }
                if (x.getXzcMobile() == null) {
                    x.setXzcMobile(firstPer.getXzcMobile());
                }
                if (x.getXzcDuty() == null) {
                    x.setXzcDuty(firstPer.getXzcDuty());
                }
                if (x.getZrcRealnm() == null) {
                    x.setZrcRealnm(firstPer.getZrcRealnm());
                }
                if (x.getZrcMobile() == null) {
                    x.setZrcMobile(firstPer.getZrcMobile());
                }
                if (x.getZrcDuty() == null) {
                    x.setZrcDuty(firstPer.getZrcDuty());
                }
                if (x.getJcRealnm() == null) {
                    x.setJcRealnm(firstPer.getJcRealnm());
                }
                if (x.getJcMobile() == null) {
                    x.setJcMobile(firstPer.getJcMobile());
                }
                if (x.getYjRealnm() == null) {
                    x.setYjRealnm(firstPer.getYjRealnm());
                }
                if (x.getYjMobile() == null) {
                    x.setYjMobile(firstPer.getYjMobile());
                }
                if (x.getZxRealnm() == null) {
                    x.setZxRealnm(firstPer.getZxRealnm());
                }
                if (x.getZxMobile() == null) {
                    x.setZxMobile(firstPer.getZxMobile());
                }
                if (x.getGldwRealnm() == null) {
                    x.setGldwRealnm(firstPer.getGldwRealnm());
                }
                if (x.getGldwMobile() == null) {
                    x.setGldwMobile(firstPer.getGldwMobile());
                }
                if (x.getGldwDuty() == null) {
                    x.setGldwDuty(firstPer.getGldwDuty());
                }
            } else {
                perMap.put(key, x);
            }
        }
        return ret;
    }

    @Override
    public List<ReservoirData> getReservoirList(List<String> list) {
        if (list.size() > 1000) {
            List<List<String>> lists = ListUtils.splitList(list, 600);
            List<ReservoirData> retList = new ArrayList<>();
            lists.forEach(itemList -> retList.addAll(baseDao.getReservoirList(itemList)));
            return retList;
        } else {
            return baseDao.getReservoirList(list);
        }
    }

    @Override
    public List<SluiceData> getSluiceList(List<String> list) {
        if (list.size() > 1000) {
            List<List<String>> lists = ListUtils.splitList(list, 600);
            List<SluiceData> retList = new ArrayList<>();
            lists.forEach(itemList -> retList.addAll(baseDao.getSluiceList(itemList)));
            return retList;
        } else {
            return baseDao.getSluiceList(list);
        }
    }

    @Override
    public List<DikeData> getDikeList(List<String> list) {
        if (list.size() > 1000) {
            List<List<String>> lists = ListUtils.splitList(list, 600);
            List<DikeData> retList = new ArrayList<>();
            lists.forEach(itemList -> retList.addAll(baseDao.getDikeList(itemList)));
            return retList;
        } else {
            return baseDao.getDikeList(list);
        }
    }

    @Override
    public List<CulvertData> getCulvertList(List<String> list) {
        if (list.size() > 1000) {
            List<List<String>> lists = ListUtils.splitList(list, 600);
            List<CulvertData> retList = new ArrayList<>();
            lists.forEach(itemList -> retList.addAll(baseDao.getCulvertList(itemList)));
            return retList;
        } else {
            return baseDao.getCulvertList(list);
        }
    }

    @Override
    public List<BridgeData> getBridgeList(List<String> list) {
        if (list.size() > 1000) {
            List<List<String>> lists = ListUtils.splitList(list, 600);
            List<BridgeData> retList = new ArrayList<>();
            lists.forEach(itemList -> retList.addAll(baseDao.getBridgeList(itemList)));
            return retList;
        } else {
            return baseDao.getBridgeList(list);
        }
    }

    @Override
    public List<DaminfoData> getDaminfoList(List<String> list) {
        if (list.size() > 1000) {
            List<List<String>> lists = ListUtils.splitList(list, 600);
            List<DaminfoData> retList = new ArrayList<>();
            lists.forEach(itemList -> retList.addAll(baseDao.getDaminfoList(itemList)));
            return retList;
        } else {
            return baseDao.getDaminfoList(list);
        }
    }

    @Override
    public void generateWarnFile(LiveWarnFileQuery query) throws Exception {
        Map<String, String> textMap = new HashMap<>();
        Map<String, String> titleMap = new HashMap<>();

        String stm = DateFormatUtils.format(query.getStm(), "MM月dd日HH时");
        String etm = DateFormatUtils.format(query.getEtm(), "MM月dd日HH时");
        StringBuffer digest = new StringBuffer("");
        digest.append(stm + "至" + etm + "，" + query.getAdnm() + "");
        if (query.getMaxDrpLevel() != null) {
            digest.append("部分地区降雨量达到" + query.getMaxDrpLevel() + "毫米以上，");
        } else {
            if (query.getMaxDrps() == null || query.getMaxDrps() == 0) digest.append("无降雨，");
            else digest.append("部分地区降雨量最大" + query.getMaxDrps() + "毫米，");
        }
        if (query.getPrevadList().size() > 0 || query.getReservoirList().size() > 0 || query.getDikeList().size() > 0) {
            digest.append("区域内山洪灾害防治区和防洪工程存在风险，其中");
            if (query.getPrevadList().size() > 0) {
                // 重点防治区
                int tp2 = (int) query.getPrevadList().stream().filter(item -> item.getPrevtp().equals("2")).count();
                // 一般防治区
                int tp1 = (int) query.getPrevadList().stream().filter(item -> !item.getPrevtp().equals("2")).count();
                digest.append("山洪灾害防治区" + query.getPrevadList().size() + "个(重点防治区" + tp2 + "个、一般防治区" + tp1 + "个）、");
            }
            if (query.getReservoirList().size() > 0) {
                digest.append("水库" + query.getReservoirList().size() + "座、");
                if (query.getRiskInfoPointsList().size() > 0) {
                    digest.deleteCharAt(digest.length() - 1);
                    int bx = (int) query.getRiskInfoPointsList().stream().filter(item -> Arrays.asList("1-1", "1-2", "1-4").contains(item.getIfmptp())).count();
                    digest.append("（含病险水库" + bx + "座）、");
                }
            }
            if (query.getDikeList().size() > 0) {
                digest.append("堤防" + query.getDikeList().size() + "处、");
                if (query.getRiskInfoPointsList().size() > 0) {
                    digest.deleteCharAt(digest.length() - 1);
                    int bx = (int) query.getRiskInfoPointsList().stream().filter(item -> item.getIfmptp().equals("2")).count();
                    digest.append("（含险工险段" + bx + "处）。");
                }
            }
        }
        digest.deleteCharAt(digest.length() - 1);
        textMap.put("digest", digest.toString() + "。请你地高度重视，切实加强监测和巡查防守，及时预警并提醒政府及有关部门，适时转移受威胁群众，确保人民生命安全。");

        Response feignResponse = ossFeign.getFile(query.getOrgFileUrl(), query.getOrgFileName());
        Response.Body body = feignResponse.body();
        InputStream inputStream = body.asInputStream();
        XWPFDocument document = new XWPFDocument(inputStream);

        // 获取表格
        List<XWPFTable> tables = document.getTables();
        for (XWPFTable table : tables) {
            CTTblBorders borders = table.getCTTbl().getTblPr().addNewTblBorders();
            CTBorder hBorder = borders.addNewInsideH();
            hBorder.setVal(STBorder.Enum.forString("single"));  // 线条类型
            hBorder.setSz(new BigInteger("1")); // 线条大小
            hBorder.setColor("000000"); // 设置颜色

            CTBorder vBorder = borders.addNewInsideV();
            vBorder.setVal(STBorder.Enum.forString("single"));
            vBorder.setSz(new BigInteger("1"));
            vBorder.setColor("000000");

            CTBorder lBorder = borders.addNewLeft();
            lBorder.setVal(STBorder.Enum.forString("single"));
            lBorder.setSz(new BigInteger("1"));
            lBorder.setColor("000000");

            CTBorder rBorder = borders.addNewRight();
            rBorder.setVal(STBorder.Enum.forString("single"));
            rBorder.setSz(new BigInteger("1"));
            rBorder.setColor("000000");

            CTBorder tBorder = borders.addNewTop();
            tBorder.setVal(STBorder.Enum.forString("single"));
            tBorder.setSz(new BigInteger("1"));
            tBorder.setColor("000000");

            CTBorder bBorder = borders.addNewBottom();
            bBorder.setVal(STBorder.Enum.forString("single"));
            bBorder.setSz(new BigInteger("1"));
            bBorder.setColor("000000");

            List<XWPFTableRow> rows = table.getRows();
            // 第一行是表头，第二行用于判断关键字属于哪类表格
            if (rows.size() >= 2) {
                XWPFTableRow row = rows.get(1);
                List<IaCPrevad> zdPrevadList = query.getPrevadList().stream().filter(item -> item.getPrevtp().equals("2")).collect(Collectors.toList());
                List<IaCPrevad> ybPrevadList = query.getPrevadList().stream().filter(item -> item.getPrevtp().equals("1")).collect(Collectors.toList());
                if (row.getCell(0).getText().equals("${zdfzq_index}")) {
                    if (query.getPrevadList().size() == 0 || zdPrevadList == null || zdPrevadList.size() == 0) {
                        deleteTable(table);
                        titleMap.put("重点防治区信息", "");
                    } else {
                        table.removeRow(1);
                        for (int i = 0; i < zdPrevadList.size(); i++) {
                            IaCPrevad item = zdPrevadList.get(i);
                            XWPFTableRow nextRow = table.createRow();
                            nextRow.getCell(0).setText(String.valueOf(i+1));
                            nextRow.getCell(1).setText(item.getAdnm());
                            nextRow.getCell(2).setText(item.getXadnm());
                            nextRow.getCell(3).setText(item.getXzadnm());
                            nextRow.getCell(4).setText(item.getCadnm());
                            nextRow.getCell(5).setText(String.valueOf(item.getPtcount()));
                            nextRow.getCell(6).setText(String.valueOf(item.getEtcount()));
                            nextRow.getCell(7).setText(String.valueOf(item.getHtcount()));
                        }
                    }
                } else if (row.getCell(0).getText().equals("${ybfzq_index}")) {
                    if (query.getPrevadList().size() == 0 || ybPrevadList == null || ybPrevadList.size() == 0) {
                        deleteTable(table);
                        titleMap.put("一般防治区信息", "");
                    } else {
                        table.removeRow(1);
                        for (int i = 0; i < ybPrevadList.size(); i++) {
                            IaCPrevad item = ybPrevadList.get(i);
                            XWPFTableRow nextRow = table.createRow();
                            nextRow.getCell(0).setText(String.valueOf(i+1));
                            nextRow.getCell(1).setText(item.getAdnm());
                            nextRow.getCell(2).setText(item.getXadnm());
                            nextRow.getCell(3).setText(item.getXzadnm());
                            nextRow.getCell(4).setText(item.getCadnm());
                            nextRow.getCell(5).setText(String.valueOf(item.getPtcount()));
                            nextRow.getCell(6).setText(String.valueOf(item.getEtcount()));
                            nextRow.getCell(7).setText(String.valueOf(item.getHtcount()));
                        }
                    }
                } else if (row.getCell(0).getText().equals("${sk_index}")) {
                    if (query.getReservoirList().size() == 0) {
                        deleteTable(table);
                        titleMap.put("水库信息", "");
                    } else {
                        table.removeRow(1);
                        for (int i = 0; i < query.getReservoirList().size(); i++) {
                            ReservoirData item = query.getReservoirList().get(i);
                            XWPFTableRow nextRow = table.createRow();
                            nextRow.getCell(0).setText(String.valueOf(i+1));
                            nextRow.getCell(1).setText(item.getRsName());
                            nextRow.getCell(2).setText(item.getXadnm());
                            nextRow.getCell(3).setText(item.getXzadnm());
                            nextRow.getCell(4).setText(item.getCadnm());
                            String engGrad = item.getEngGrad();
                            String engGradStr = "";
                            if (engGrad == null) engGradStr = "-";
                            else if (engGrad.equals("Ⅰ")) engGradStr = "大(1)型";
                            else if (engGrad.equals("Ⅱ")) engGradStr = "大(2)型";
                            else if (engGrad.equals("Ⅲ")) engGradStr = "中型";
                            else if (engGrad.equals("Ⅳ")) engGradStr = "小(1)型";
                            else if (engGrad.equals("Ⅴ")) engGradStr = "小(2)型";
                            nextRow.getCell(5).setText(engGradStr);
                        }
                    }
                } else if (row.getCell(0).getText().equals("${df_index}")) {
                    if (query.getDikeList().size() == 0) {
                        deleteTable(table);
                        titleMap.put("堤防信息", "");
                    } else {
                        table.removeRow(1);
                        for (int i = 0; i < query.getDikeList().size(); i++) {
                            DikeData item = query.getDikeList().get(i);
                            XWPFTableRow nextRow = table.createRow();
                            nextRow.getCell(0).setText(String.valueOf(i+1));
                            nextRow.getCell(1).setText(item.getDikeName());
                            nextRow.getCell(2).setText(item.getXadnm());
                            nextRow.getCell(3).setText(item.getXzadnm());
                            nextRow.getCell(4).setText(item.getCadnm());
                            nextRow.getCell(5).setText(item.getDikeGrad());
                        }
                    }
                } else if (row.getCell(0).getText().equals("${fxyh_index}")) {
                    if (query.getRiskInfoPointsList().size() == 0) {
                        deleteTable(table);
                        titleMap.put("风险隐患信息", "");
                    } else {
                        table.removeRow(1);
                        for (int i = 0; i < query.getRiskInfoPointsList().size(); i++) {
                            RiskInfoPointData item = query.getRiskInfoPointsList().get(i);
                            XWPFTableRow nextRow = table.createRow();
                            nextRow.getCell(0).setText(String.valueOf(i+1));
                            nextRow.getCell(1).setText(item.getIfmpnm());
                            nextRow.getCell(2).setText(item.getAdnm());
                            nextRow.getCell(3).setText(item.getIfmptpnm());
                            nextRow.getCell(4).setText(item.getType());
                        }
                    }
                }
            }
        }

        //生成匹配模式的正则表达式
        String patternString = "\\$\\{(" + StringUtils.join(textMap.keySet(), "|") + ")\\}";
        Pattern pattern = Pattern.compile(patternString);

        //获取段落集合
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            String dataStr = "";

            String text = paragraph.getText();
            if (titleMap.containsKey(text) && titleMap.get(text).equals("")) {
                dataStr = "";
            } else {
                Matcher matcher = pattern.matcher(text);

                StringBuffer sb = new StringBuffer();
                while (matcher.find()) {
                    matcher.appendReplacement(sb, textMap.get(matcher.group(1)));
                }
                matcher.appendTail(sb);
                dataStr = sb.toString();
            }
            List<XWPFRun> runs = paragraph.getRuns();
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                if (i == 0) {
                    run.setText(dataStr, 0);
                } else {
                    run.setText("", 0);
                }
            }
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();//二进制OutputStream
        document.write(baos);//文档写入流
        byte[] body_data = baos.toByteArray();
        ossFeign.putFile(query.getOrgFileUrl().replace("/", "HTJLUP"), body_data);
    }

    /**
     * 删除表格
     * @param table 表格对象
     */
    public static void deleteTable(XWPFTable table){
        List<XWPFTableRow> rows = table.getRows();
        int rowLength = rows.size();
        for (int i = 0; i < rowLength; i++) {
            table.removeRow(0);
        }
    }

    @Override
    public List<MessageWarnStatusB> getLiveWarnStatusList(String level, String adcd) {
        int adLevel = AdcdUtil.getAdLevel(adcd);

        Map<String, Object> params = new HashMap<>();
        params.put("adcd", adcd);
        params.put("adLevel", adLevel);
        params.put("level", level);
        return baseDao.getLiveWarnStatusList(params);
    }

    @Override
    public List<RsvrPersonVo> getRsvrPersonList(List<String> list) {
        if (list.size() > 1000) {
            List<List<String>> lists = ListUtils.splitList(list, 600);
            List<RsvrPersonVo> retList = new ArrayList<>();
            lists.forEach(itemList -> retList.addAll(baseDao.getRsvrPersonList(itemList)));
            return retList;
        } else {
            return baseDao.getRsvrPersonList(list);
        }
    }
}
